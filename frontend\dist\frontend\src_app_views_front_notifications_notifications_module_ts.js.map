{"version": 3, "file": "src_app_views_front_notifications_notifications_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUgE;AAO1B;AASd;;;;;;;;;;ICEhBW,4DAAA,cAAoE;IAK9DA,wDAAA,mBAAAG,sEAAAC,MAAA;MAAAJ,2DAAA,CAAAM,IAAA;MAAA,MAAAC,OAAA,GAAAP,2DAAA;MAAA,OAASA,yDAAA,CAAAO,OAAA,CAAAG,eAAA,CAAAN,MAAA,CAAuB;IAAA,EAAC;IAHnCJ,0DAAA,EAIE;IACFA,uDAAA,eAA+B;IACjCA,0DAAA,EAAQ;;;;IAJJA,uDAAA,GAAuB;IAAvBA,wDAAA,YAAAe,OAAA,CAAAC,WAAA,CAAuB;;;;;;IA+B7BhB,4DAAA,iBAIC;IAFCA,wDAAA,mBAAAiB,0EAAA;MAAAjB,2DAAA,CAAAkB,IAAA;MAAA,MAAAC,OAAA,GAAAnB,2DAAA;MAAA,OAASA,yDAAA,CAAAmB,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAGzBpB,uDAAA,YAAwC;IAACA,oDAAA,8BAC3C;IAAAA,0DAAA,EAAS;;;;;;IAGTA,4DAAA,iBAKC;IAHCA,wDAAA,mBAAAsB,2EAAA;MAAAtB,2DAAA,CAAAuB,IAAA;MAAA,MAAAC,OAAA,GAAAxB,2DAAA;MAAA,OAASA,yDAAA,CAAAwB,OAAA,CAAAC,sBAAA,EAAwB;IAAA,EAAC;IAIlCzB,uDAAA,YAAqC;IAACA,oDAAA,uBACxC;IAAAA,0DAAA,EAAS;;;;;;IA9DXA,4DAAA,cAAsD;IAGlDA,wDAAA,mBAAA0B,iEAAA;MAAA1B,2DAAA,CAAA2B,IAAA;MAAA,MAAAC,OAAA,GAAA5B,2DAAA;MAAA,OAASA,yDAAA,CAAA4B,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IAI7B7B,uDAAA,YAA+B;IACjCA,0DAAA,EAAS;IAGTA,wDAAA,IAAA+B,8CAAA,kBASM;;IAGN/B,4DAAA,iBAKC;IAJCA,wDAAA,mBAAAgC,iEAAA;MAAAhC,2DAAA,CAAA2B,IAAA;MAAA,MAAAM,OAAA,GAAAjC,2DAAA;MAAA,OAASA,yDAAA,CAAAiC,OAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IAK9BlC,uDAAA,YAA6B;IAC/BA,0DAAA,EAAS;IAGTA,4DAAA,iBAKC;IAJCA,wDAAA,mBAAAmC,iEAAA;MAAAnC,2DAAA,CAAA2B,IAAA;MAAA,MAAAS,OAAA,GAAApC,2DAAA;MAAA,OAASA,yDAAA,CAAAoC,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAKvBrC,uDAAA,YAGK;IACPA,0DAAA,EAAS;IAGTA,wDAAA,IAAAsC,iDAAA,qBAMS;;IAGTtC,wDAAA,KAAAuC,kDAAA,qBAOS;;IACXvC,0DAAA,EAAM;;;;IApDEA,uDAAA,GAAgC;IAAhCA,wDAAA,SAAAA,yDAAA,OAAAyC,MAAA,CAAAC,gBAAA,IAAgC;IAepC1C,uDAAA,GAA+B;IAA/BA,yDAAA,WAAAyC,MAAA,CAAAG,cAAA,CAA+B;IAU/B5C,uDAAA,GAA8B;IAA9BA,yDAAA,YAAAyC,MAAA,CAAAI,YAAA,CAA8B;IAC9B7C,mEAAA,UAAAyC,MAAA,CAAAI,YAAA,+CAAmE;IAIjE7C,uDAAA,GAA4D;IAA5DA,wDAAA,YAAAyC,MAAA,CAAAI,YAAA,qCAA4D;IAM7D7C,uDAAA,GAAiC;IAAjCA,wDAAA,SAAAA,yDAAA,SAAAyC,MAAA,CAAAM,YAAA,OAAiC;IASjC/C,uDAAA,GAAgC;IAAhCA,wDAAA,SAAAA,yDAAA,SAAAyC,MAAA,CAAAC,gBAAA,IAAgC;;;;;;IAUrC1C,4DAAA,cAAuE;IAElEA,oDAAA,GAA+C;IAAAA,0DAAA,EACjD;IAGDA,4DAAA,iBAGC;IAFCA,wDAAA,mBAAAgD,iEAAA;MAAAhD,2DAAA,CAAAiD,IAAA;MAAA,MAAAC,OAAA,GAAAlD,2DAAA;MAAA,OAASA,yDAAA,CAAAkD,OAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IAG9BnD,uDAAA,YAAiC;IAACA,oDAAA,yBACpC;IAAAA,0DAAA,EAAS;IAGTA,4DAAA,iBAGC;IAFCA,wDAAA,mBAAAoD,iEAAA;MAAApD,2DAAA,CAAAiD,IAAA;MAAA,MAAAI,OAAA,GAAArD,2DAAA;MAAA,OAASA,yDAAA,CAAAqD,OAAA,CAAAC,2BAAA,EAA6B;IAAA,EAAC;IAGvCtD,uDAAA,YAAqC;IAACA,oDAAA,kBACxC;IAAAA,0DAAA,EAAS;IAGTA,4DAAA,iBAOC;IANCA,wDAAA,mBAAAuD,iEAAA;MAAAvD,2DAAA,CAAAiD,IAAA;MAAA,MAAAO,OAAA,GAAAxD,2DAAA;MACewD,OAAA,CAAAC,qBAAA,CAAAC,KAAA,EACd;MAAAF,OAAA,CAAAG,gBAAA,GACL,KAAK;MAAA,OAAA3D,yDAAA,CAAAwD,OAAA,CAAAxC,WAAA,GACJ,KACP;IAAA,EADW;IAGDhB,uDAAA,aAAiC;IAACA,oDAAA,iBACpC;IAAAA,0DAAA,EAAS;;;;IA7BNA,uDAAA,GAA+C;IAA/CA,gEAAA,KAAA6D,MAAA,CAAAJ,qBAAA,CAAAK,IAAA,8BAA+C;;;;;IAkCtD9D,4DAAA,cAA0D;IACxDA,uDAAA,cAA6C;IAC7CA,4DAAA,YAAmC;IAAAA,oDAAA,sCAA+B;IAAAA,0DAAA,EAAI;;;;;;IAIxEA,4DAAA,cAAoD;IAEhDA,uDAAA,YAAiE;IACjEA,4DAAA,UAAK;IACgCA,oDAAA,2BAAoB;IAAAA,0DAAA,EAAK;IAC5DA,4DAAA,YAAiC;IAAAA,oDAAA,GAAuB;IAAAA,0DAAA,EAAI;IAE9DA,4DAAA,iBAGC;IAFCA,wDAAA,mBAAA+D,kEAAA;MAAA/D,2DAAA,CAAAgE,IAAA;MAAA,MAAAC,OAAA,GAAAjE,2DAAA;MAAA,OAASA,yDAAA,CAAAiE,OAAA,CAAApC,iBAAA,EAAmB;IAAA,EAAC;IAG7B7B,oDAAA,uBACF;IAAAA,0DAAA,EAAS;;;;IAP0BA,uDAAA,GAAuB;IAAvBA,+DAAA,CAAAmE,MAAA,CAAAC,eAAA,GAAuB;;;;;;IAY9DpE,4DAAA,cAGC;IAEGA,uDAAA,YAAiC;IACnCA,0DAAA,EAAM;IACNA,4DAAA,aAAmC;IAAAA,oDAAA,0BAAmB;IAAAA,0DAAA,EAAK;IAC3DA,4DAAA,YAAiC;IAAAA,oDAAA,mCAAkB;IAAAA,0DAAA,EAAI;IACvDA,4DAAA,iBAAsE;IAA9DA,wDAAA,mBAAAqE,kEAAA;MAAArE,2DAAA,CAAAsE,IAAA;MAAA,MAAAC,OAAA,GAAAvE,2DAAA;MAAA,OAASA,yDAAA,CAAAuE,OAAA,CAAA1C,iBAAA,EAAmB;IAAA,EAAC;IACnC7B,oDAAA,kDACF;IAAAA,0DAAA,EAAS;;;;;IAkEDA,4DAAA,cAGC;IACCA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAAwE,gBAAA,CAAAC,OAAA,kBAAAD,gBAAA,CAAAC,OAAA,CAAAC,OAAA,MACF;;;;;IAGA1E,4DAAA,cAGC;IACCA,uDAAA,YAAgC;IAChCA,oDAAA,GAEF;IAAAA,0DAAA,EAAM;;;;IAFJA,uDAAA,GAEF;IAFEA,gEAAA,MAAAwE,gBAAA,CAAAC,OAAA,kBAAAD,gBAAA,CAAAC,OAAA,CAAAE,WAAA,kBAAAH,gBAAA,CAAAC,OAAA,CAAAE,WAAA,CAAAC,MAAA,8BAEF;;;;;IAIF5E,uDAAA,cAAiE;;;;;;IAMjEA,4DAAA,iBAQC;IANCA,wDAAA,mBAAA6E,2FAAAzE,MAAA;MAAAJ,2DAAA,CAAA8E,IAAA;MAAA,MAAAN,gBAAA,GAAAxE,2DAAA,GAAA+E,SAAA;MAAA,MAAAC,OAAA,GAAAhF,2DAAA;MACmBgF,OAAA,CAAAC,0BAAA,CAAAT,gBAAA,CAAAU,EAAA,CAClB;MAAA,OAAkBlF,yDAAA,CAAAI,MAAA,CAAA+E,eAAA,EAEjC;IAAA,EADe;IAIDnF,uDAAA,YAAgC;IAClCA,0DAAA,EAAS;;;;;IAcPA,uDAAA,YAAgD;;;;;IAChDA,uDAAA,YAAsD;;;;;;IAZxDA,4DAAA,iBAUC;IAJCA,wDAAA,mBAAAoF,2FAAAhF,MAAA;MAAAJ,2DAAA,CAAAqF,IAAA;MAAA,MAAAb,gBAAA,GAAAxE,2DAAA,GAAA+E,SAAA;MAAA,MAAAO,OAAA,GAAAtF,2DAAA;MAASsF,OAAA,CAAAC,gBAAA,CAAAf,gBAAA,CAA8B;MAAA,OAAExE,yDAAA,CAAAI,MAAA,CAAA+E,eAAA,EAAwB;IAAA,EAAC;IAKlEnF,wDAAA,IAAAwF,sEAAA,gBAAgD;IAChDxF,wDAAA,IAAAyF,sEAAA,gBAAsD;IACxDzF,0DAAA,EAAS;;;;IAJPA,wDAAA,aAAA0F,OAAA,CAAAC,OAAA,CAAoB;IAEQ3F,uDAAA,GAAc;IAAdA,wDAAA,UAAA0F,OAAA,CAAAC,OAAA,CAAc;IACP3F,uDAAA,GAAa;IAAbA,wDAAA,SAAA0F,OAAA,CAAAC,OAAA,CAAa;;;;;;IAelD3F,4DAAA,iBAKC;IAHCA,wDAAA,mBAAA4F,2FAAAxF,MAAA;MAAAJ,2DAAA,CAAA6F,IAAA;MAAA,MAAArB,gBAAA,GAAAxE,2DAAA,GAAA+E,SAAA;MAAA,MAAAe,OAAA,GAAA9F,2DAAA;MAAS8F,OAAA,CAAAC,UAAA,CAAAvB,gBAAA,CAAAU,EAAA,CAA2B;MAAA,OAAElF,yDAAA,CAAAI,MAAA,CAAA+E,eAAA,EAAwB;IAAA,EAAC;IAI/DnF,uDAAA,aAA4B;IAC9BA,0DAAA,EAAS;;;;;;IAhIfA,qEAAA,GAA0E;IACxEA,4DAAA,cAKC;IAOOA,wDAAA,mBAAAiG,gFAAA7F,MAAA;MAAA,MAAA8F,WAAA,GAAAlG,2DAAA,CAAAmG,IAAA;MAAA,MAAA3B,gBAAA,GAAA0B,WAAA,CAAAnB,SAAA;MAAA,MAAAqB,OAAA,GAAApG,2DAAA;MAAA,OAASA,yDAAA,CAAAoG,OAAA,CAAAC,eAAA,CAAA7B,gBAAA,CAAAU,EAAA,EAAA9E,MAAA,CAAwC;IAAA,EAAC;IAHpDJ,0DAAA,EAIE;IACFA,uDAAA,eAA+B;IACjCA,0DAAA,EAAQ;IAIVA,4DAAA,cAAiC;IAC/BA,uDAAA,cAOE;IACJA,0DAAA,EAAM;IAGNA,4DAAA,cAAuC;IAKGA,oDAAA,IAEhC;IAAAA,0DAAA,EAAO;IAGTA,4DAAA,eAA+B;IAC7BA,oDAAA,IACF;;IAAAA,0DAAA,EAAM;IAKVA,4DAAA,eAAyC;IACPA,oDAAA,IAE9B;IAAAA,0DAAA,EAAO;IAIXA,wDAAA,KAAAsG,+DAAA,kBAKM;IAGNtG,wDAAA,KAAAuG,+DAAA,kBAOM;IACRvG,0DAAA,EAAM;IAGNA,wDAAA,KAAAwG,+DAAA,kBAAiE;IACnExG,0DAAA,EAAM;IAGNA,4DAAA,eAAkC;IAEhCA,wDAAA,KAAAyG,kEAAA,qBAUS;IAGTzG,wDAAA,KAAA0G,kEAAA,qBAaS;IAGT1G,4DAAA,kBAMC;IALCA,wDAAA,mBAAA2G,kFAAAvG,MAAA;MAAA,MAAA8F,WAAA,GAAAlG,2DAAA,CAAAmG,IAAA;MAAA,MAAA3B,gBAAA,GAAA0B,WAAA,CAAAnB,SAAA;MAAA,MAAA6B,OAAA,GAAA5G,2DAAA;MACmB4G,OAAA,CAAAC,uBAAA,CAAArC,gBAAA,CAAqC;MAAA,OAAExE,yDAAA,CAAAI,MAAA,CAAA+E,eAAA,EAExE;IAAA,EADe;IAIDnF,uDAAA,aAAkC;IACpCA,0DAAA,EAAS;IAGTA,wDAAA,KAAA8G,kEAAA,qBAOS;IAGT9G,4DAAA,kBAMC;IALCA,wDAAA,mBAAA+G,kFAAA3G,MAAA;MAAA,MAAA8F,WAAA,GAAAlG,2DAAA,CAAAmG,IAAA;MAAA,MAAA3B,gBAAA,GAAA0B,WAAA,CAAAnB,SAAA;MAAA,MAAAiC,OAAA,GAAAhH,2DAAA;MACmBgH,OAAA,CAAAC,kBAAA,CAAAzC,gBAAA,CAAAU,EAAA,CAAmC;MAAA,OAAElF,yDAAA,CAAAI,MAAA,CAAA+E,eAAA,EAEtE;IAAA,EADe;IAIDnF,uDAAA,aAAgC;IAClCA,0DAAA,EAAS;IAGfA,mEAAA,EAAe;;;;;IA5IXA,uDAAA,GAA6D;IAA7DA,yDAAA,oCAAAwE,gBAAA,CAAA2C,MAAA,CAA6D,iCAAA3C,gBAAA,CAAA2C,MAAA,sCAAAC,OAAA,CAAAC,UAAA,CAAA7C,gBAAA,CAAAU,EAAA;IAUvDlF,uDAAA,GAAuC;IAAvCA,wDAAA,YAAAoH,OAAA,CAAAC,UAAA,CAAA7C,gBAAA,CAAAU,EAAA,EAAuC;IAUzClF,uDAAA,GAGC;IAHDA,wDAAA,SAAAwE,gBAAA,CAAA8C,QAAA,kBAAA9C,gBAAA,CAAA8C,QAAA,CAAAC,KAAA,yCAAAvH,2DAAA,CAGC;IAYqCA,uDAAA,GAEhC;IAFgCA,+DAAA,EAAAwE,gBAAA,CAAA8C,QAAA,kBAAA9C,gBAAA,CAAA8C,QAAA,CAAAG,QAAA,oBAEhC;IAIAzH,uDAAA,GACF;IADEA,gEAAA,MAAAA,yDAAA,SAAAwE,gBAAA,CAAAmD,SAAA,oBACF;IAM8B3H,uDAAA,GAE9B;IAF8BA,+DAAA,CAAAwE,gBAAA,CAAAE,OAAA,CAE9B;IAKD1E,uDAAA,GAAmC;IAAnCA,wDAAA,SAAAwE,gBAAA,CAAAC,OAAA,kBAAAD,gBAAA,CAAAC,OAAA,CAAAC,OAAA,CAAmC;IAQnC1E,uDAAA,GAA+C;IAA/CA,wDAAA,SAAAwE,gBAAA,CAAAC,OAAA,kBAAAD,gBAAA,CAAAC,OAAA,CAAAE,WAAA,kBAAAH,gBAAA,CAAAC,OAAA,CAAAE,WAAA,CAAAC,MAAA,CAA+C;IAU9C5E,uDAAA,GAA0B;IAA1BA,wDAAA,UAAAwE,gBAAA,CAAA2C,MAAA,CAA0B;IAO7BnH,uDAAA,GAA+C;IAA/CA,wDAAA,SAAAwE,gBAAA,CAAAC,OAAA,kBAAAD,gBAAA,CAAAC,OAAA,CAAAE,WAAA,kBAAAH,gBAAA,CAAAC,OAAA,CAAAE,WAAA,CAAAC,MAAA,CAA+C;IAa/C5E,uDAAA,GAKf;IALeA,wDAAA,SAAAwE,gBAAA,CAAAoD,IAAA,sBAAApD,gBAAA,CAAAoD,IAAA,uBAAApD,gBAAA,CAAAoD,IAAA,wBAKf;IAsBe5H,uDAAA,GAA0B;IAA1BA,wDAAA,UAAAwE,gBAAA,CAAA2C,MAAA,CAA0B;;;;;IAuBnCnH,4DAAA,eAAyD;IACvDA,uDAAA,eAAmD;IACnDA,4DAAA,aAAyC;IACvCA,oDAAA,uDACF;IAAAA,0DAAA,EAAI;;;;;;IA3JRA,4DAAA,kBAKC;IADCA,wDAAA,oBAAA6H,gEAAA;MAAA7H,2DAAA,CAAA8H,IAAA;MAAA,MAAAC,IAAA,GAAA/H,yDAAA;MAAA,MAAAiI,OAAA,GAAAjI,2DAAA;MAAA,OAAUA,yDAAA,CAAAiI,OAAA,CAAAC,QAAA,CAAAH,IAAA,CAA+B;IAAA,EAAC;IAE1C/H,wDAAA,IAAAmI,wDAAA,6BA8Ie;;IAGfnI,wDAAA,IAAAoI,+CAAA,kBAKM;IACRpI,0DAAA,EAAM;;;;IAvJmCA,uDAAA,GAAiC;IAAjCA,wDAAA,YAAAA,yDAAA,OAAAqI,MAAA,CAAAC,sBAAA,EAAiC;IAiJlEtI,uDAAA,GAAiB;IAAjBA,wDAAA,SAAAqI,MAAA,CAAAE,WAAA,CAAiB;;;;;IA2BvBvI,4DAAA,cAAqE;IACnEA,uDAAA,cAA6C;IAC7CA,4DAAA,YAAmC;IAAAA,oDAAA,4CAAgC;IAAAA,0DAAA,EAAI;;;;;IAGzEA,4DAAA,cAGC;IAEGA,uDAAA,aAA+B;IACjCA,0DAAA,EAAM;IACNA,4DAAA,aAAmC;IAAAA,oDAAA,+BAAmB;IAAAA,0DAAA,EAAK;IAC3DA,4DAAA,YAAiC;IAC/BA,oDAAA,yFACF;IAAAA,0DAAA,EAAI;;;;;;IAYFA,4DAAA,eAGC;IAIGA,wDAAA,mBAAAwI,2EAAA;MAAAxI,2DAAA,CAAAyI,IAAA;MAAA,MAAAC,cAAA,GAAA1I,2DAAA,GAAA+E,SAAA;MAAA,MAAA4D,OAAA,GAAA3I,2DAAA;MAAA,OAASA,yDAAA,CAAA2I,OAAA,CAAAC,cAAA,CAAAF,cAAA,CAAAG,GAAA,CAA8B;IAAA,EAAC;IAH1C7I,0DAAA,EAIE;;;;IAHAA,uDAAA,GAAsB;IAAtBA,wDAAA,QAAA0I,cAAA,CAAAG,GAAA,EAAA7I,2DAAA,CAAsB;;;;;IAO1BA,4DAAA,eAGC;IACCA,uDAAA,QAA8C;IAChDA,0DAAA,EAAM;;;;;IADDA,uDAAA,GAAsC;IAAtCA,wDAAA,CAAA+I,OAAA,CAAAC,WAAA,CAAAN,cAAA,CAAAd,IAAA,EAAsC;;;;;IAWvC5H,4DAAA,gBAGG;IAAAA,oDAAA,GAAqC;IAAAA,0DAAA,EACvC;;;;;IADEA,uDAAA,GAAqC;IAArCA,+DAAA,CAAAiJ,OAAA,CAAAC,cAAA,CAAAR,cAAA,CAAA5E,IAAA,EAAqC;;;;;;IAnC9C9D,4DAAA,eAGC;IAECA,wDAAA,IAAAmJ,qDAAA,mBASM;IAGNnJ,wDAAA,IAAAoJ,qDAAA,mBAKM;IAENpJ,4DAAA,eAAwC;IAEpCA,oDAAA,GACF;IAAAA,0DAAA,EAAM;IACNA,4DAAA,eAAwC;IACGA,oDAAA,GAEvC;IAAAA,0DAAA,EAAO;IACTA,wDAAA,IAAAqJ,sDAAA,oBAIC;IACHrJ,0DAAA,EAAM;IAGRA,4DAAA,gBAA2C;IAGvCA,wDAAA,mBAAAsJ,yEAAA;MAAA,MAAApD,WAAA,GAAAlG,2DAAA,CAAAuJ,IAAA;MAAA,MAAAb,cAAA,GAAAxC,WAAA,CAAAnB,SAAA;MAAA,MAAAyE,OAAA,GAAAxJ,2DAAA;MAAA,OAASA,yDAAA,CAAAwJ,OAAA,CAAAZ,cAAA,CAAAF,cAAA,CAAAG,GAAA,CAA8B;IAAA,EAAC;IAGxC7I,uDAAA,cAAwC;IAC1CA,0DAAA,EAAS;IACTA,4DAAA,mBAIC;IAFCA,wDAAA,mBAAAyJ,yEAAA;MAAA,MAAAvD,WAAA,GAAAlG,2DAAA,CAAAuJ,IAAA;MAAA,MAAAb,cAAA,GAAAxC,WAAA,CAAAnB,SAAA;MAAA,MAAA2E,OAAA,GAAA1J,2DAAA;MAAA,OAASA,yDAAA,CAAA0J,OAAA,CAAAC,kBAAA,CAAAjB,cAAA,CAA8B;IAAA,EAAC;IAGxC1I,uDAAA,cAA+B;IACjCA,0DAAA,EAAS;;;;;IAhDRA,uDAAA,GAA8B;IAA9BA,wDAAA,SAAA4J,OAAA,CAAAC,OAAA,CAAAnB,cAAA,CAAAd,IAAA,EAA8B;IAY9B5H,uDAAA,GAA+B;IAA/BA,wDAAA,UAAA4J,OAAA,CAAAC,OAAA,CAAAnB,cAAA,CAAAd,IAAA,EAA+B;IAQ9B5H,uDAAA,GACF;IADEA,gEAAA,MAAA0I,cAAA,CAAAoB,IAAA,6BACF;IAE2C9J,uDAAA,GAEvC;IAFuCA,+DAAA,CAAA4J,OAAA,CAAAG,gBAAA,CAAArB,cAAA,CAAAd,IAAA,EAEvC;IAEC5H,uDAAA,GAAqB;IAArBA,wDAAA,SAAA0I,cAAA,CAAA5E,IAAA,CAAqB;;;;;IArChC9D,4DAAA,eAGC;IACCA,wDAAA,IAAAgK,+CAAA,oBAwDM;IACRhK,0DAAA,EAAM;;;;IAxDqBA,uDAAA,GAAqB;IAArBA,wDAAA,YAAAiK,MAAA,CAAAC,kBAAA,CAAqB;;;;;IAqH9ClK,4DAAA,eAGC;IACSA,oDAAA,yBAAkB;IAAAA,0DAAA,EAAS;IACnCA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAAmK,OAAA,CAAAC,mBAAA,CAAA3F,OAAA,kBAAA0F,OAAA,CAAAC,mBAAA,CAAA3F,OAAA,CAAAC,OAAA,MACF;;;;;IA0BE1E,4DAAA,eAGC;IACuCA,oDAAA,cAAO;IAAAA,0DAAA,EAAO;IACpDA,4DAAA,gBAAsC;IAAAA,oDAAA,GAEpC;;IAAAA,0DAAA,EAAO;;;;IAF6BA,uDAAA,GAEpC;IAFoCA,+DAAA,CAAAA,yDAAA,OAAAqK,OAAA,CAAAD,mBAAA,CAAAE,MAAA,YAEpC;;;;;IAEJtK,4DAAA,eAOC;IAEGA,uDAAA,aAAuC;IACvCA,oDAAA,eACF;IAAAA,0DAAA,EAAO;IACPA,4DAAA,gBAGC;IACCA,oDAAA,uEACF;IAAAA,0DAAA,EAAO;;;;;;IAoBPA,4DAAA,eAGC;IAIGA,wDAAA,mBAAAuK,kFAAA;MAAAvK,2DAAA,CAAAwK,IAAA;MAAA,MAAAC,cAAA,GAAAzK,2DAAA,GAAA+E,SAAA;MAAA,MAAA2F,OAAA,GAAA1K,2DAAA;MAAA,OAASA,yDAAA,CAAA0K,OAAA,CAAA9B,cAAA,CAAA6B,cAAA,CAAA5B,GAAA,CAA8B;IAAA,EAAC;IAH1C7I,0DAAA,EAIE;;;;IAHAA,uDAAA,GAAsB;IAAtBA,wDAAA,QAAAyK,cAAA,CAAA5B,GAAA,EAAA7I,2DAAA,CAAsB;;;;;IAO1BA,4DAAA,eAGC;IACCA,uDAAA,QAA8C;IAChDA,0DAAA,EAAM;;;;;IADDA,uDAAA,GAAsC;IAAtCA,wDAAA,CAAA2K,OAAA,CAAA3B,WAAA,CAAAyB,cAAA,CAAA7C,IAAA,EAAsC;;;;;IAWvC5H,4DAAA,gBAGG;IAAAA,oDAAA,GAAqC;IAAAA,0DAAA,EACvC;;;;;IADEA,uDAAA,GAAqC;IAArCA,+DAAA,CAAA4K,OAAA,CAAA1B,cAAA,CAAAuB,cAAA,CAAA3G,IAAA,EAAqC;;;;;;IAnC9C9D,4DAAA,eAGC;IAECA,wDAAA,IAAA6K,4DAAA,mBASM;IAGN7K,wDAAA,IAAA8K,4DAAA,mBAKM;IAEN9K,4DAAA,eAA0C;IAEtCA,oDAAA,GACF;IAAAA,0DAAA,EAAM;IACNA,4DAAA,eAA0C;IACGA,oDAAA,GAEzC;IAAAA,0DAAA,EAAO;IACTA,wDAAA,IAAA+K,6DAAA,oBAIC;IACH/K,0DAAA,EAAM;IAGRA,4DAAA,gBAA6C;IAGzCA,wDAAA,mBAAAgL,gFAAA;MAAA,MAAA9E,WAAA,GAAAlG,2DAAA,CAAAiL,IAAA;MAAA,MAAAR,cAAA,GAAAvE,WAAA,CAAAnB,SAAA;MAAA,MAAAmG,OAAA,GAAAlL,2DAAA;MAAA,OAASA,yDAAA,CAAAkL,OAAA,CAAAtC,cAAA,CAAA6B,cAAA,CAAA5B,GAAA,CAA8B;IAAA,EAAC;IAGxC7I,uDAAA,cAAwC;IAC1CA,0DAAA,EAAS;IACTA,4DAAA,mBAIC;IAFCA,wDAAA,mBAAAmL,gFAAA;MAAA,MAAAjF,WAAA,GAAAlG,2DAAA,CAAAiL,IAAA;MAAA,MAAAR,cAAA,GAAAvE,WAAA,CAAAnB,SAAA;MAAA,MAAAqG,OAAA,GAAApL,2DAAA;MAAA,OAASA,yDAAA,CAAAoL,OAAA,CAAAzB,kBAAA,CAAAc,cAAA,CAA8B;IAAA,EAAC;IAGxCzK,uDAAA,cAA+B;IACjCA,0DAAA,EAAS;;;;;IAhDRA,uDAAA,GAA8B;IAA9BA,wDAAA,SAAAqL,OAAA,CAAAxB,OAAA,CAAAY,cAAA,CAAA7C,IAAA,EAA8B;IAY9B5H,uDAAA,GAA+B;IAA/BA,wDAAA,UAAAqL,OAAA,CAAAxB,OAAA,CAAAY,cAAA,CAAA7C,IAAA,EAA+B;IAQ9B5H,uDAAA,GACF;IADEA,gEAAA,MAAAyK,cAAA,CAAAX,IAAA,6BACF;IAE6C9J,uDAAA,GAEzC;IAFyCA,+DAAA,CAAAqL,OAAA,CAAAtB,gBAAA,CAAAU,cAAA,CAAA7C,IAAA,EAEzC;IAEC5H,uDAAA,GAAqB;IAArBA,wDAAA,SAAAyK,cAAA,CAAA3G,IAAA,CAAqB;;;;;IA1ClC9D,4DAAA,eAGC;IAEGA,uDAAA,YAAqC;IACrCA,oDAAA,GACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,eAA2C;IACzCA,wDAAA,IAAAsL,sDAAA,oBAwDM;IACRtL,0DAAA,EAAM;;;;IA5DJA,uDAAA,GACF;IADEA,gEAAA,2BAAAuL,OAAA,CAAArB,kBAAA,CAAAtF,MAAA,OACF;IAG2B5E,uDAAA,GAAqB;IAArBA,wDAAA,YAAAuL,OAAA,CAAArB,kBAAA,CAAqB;;;;;IA0E9ClK,uDAAA,aAAqD;;;;;IACrDA,uDAAA,aAA2D;;;;;;IAd7DA,4DAAA,kBAYC;IANCA,wDAAA,mBAAAwL,4EAAA;MAAAxL,2DAAA,CAAAyL,IAAA;MAAA,MAAAC,OAAA,GAAA1L,2DAAA;MACe0L,OAAA,CAAAnG,gBAAA,CAAAmG,OAAA,CAAAtB,mBAAA,CACd;MAAA,OAAcpK,yDAAA,CAAA0L,OAAA,CAAAC,6BAAA,EAEzB;IAAA,EADW;IAID3L,wDAAA,IAAA4L,uDAAA,iBAAqD;IACrD5L,wDAAA,IAAA6L,uDAAA,iBAA2D;IAC3D7L,oDAAA,kCACF;IAAAA,0DAAA,EAAS;;;;IALPA,wDAAA,aAAA8L,OAAA,CAAAnG,OAAA,CAAoB;IAEa3F,uDAAA,GAAc;IAAdA,wDAAA,UAAA8L,OAAA,CAAAnG,OAAA,CAAc;IACP3F,uDAAA,GAAa;IAAbA,wDAAA,SAAA8L,OAAA,CAAAnG,OAAA,CAAa;;;;;;IAIvD3F,4DAAA,kBAIC;IAFCA,wDAAA,mBAAA+L,4EAAA;MAAA/L,2DAAA,CAAAgM,IAAA;MAAA,MAAAC,OAAA,GAAAjM,2DAAA;MAAA,OAASA,yDAAA,CAAAiM,OAAA,CAAAlG,UAAA,CAAAkG,OAAA,CAAA7B,mBAAA,CAAAlF,EAAA,CAAkC;IAAA,EAAC;IAG5ClF,uDAAA,aAAiC;IACjCA,oDAAA,yBACF;IAAAA,0DAAA,EAAS;;;;;;IArMbA,4DAAA,cAA+D;IAIzDA,uDAAA,aAAgC;IAChCA,oDAAA,wBACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,eAAsC;IACpCA,uDAAA,eAQE;IACFA,4DAAA,eAAyC;IAErCA,oDAAA,GACF;IAAAA,0DAAA,EAAO;IACPA,4DAAA,iBAAqC;IACnCA,oDAAA,IACF;;IAAAA,0DAAA,EAAO;IAMbA,4DAAA,gBAAyC;IAErCA,uDAAA,cAAmC;IACnCA,oDAAA,iBACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,gBAAyC;IACvCA,oDAAA,IACF;IAAAA,0DAAA,EAAM;IACNA,wDAAA,KAAAkM,gDAAA,mBAMM;IACRlM,0DAAA,EAAM;IAGNA,4DAAA,gBAAyC;IAErCA,uDAAA,cAA+B;IAC/BA,oDAAA,sBACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,gBAAoC;IAEMA,oDAAA,cAAM;IAAAA,0DAAA,EAAO;IACnDA,4DAAA,iBAAsC;IAAAA,oDAAA,IAEpC;IAAAA,0DAAA,EAAO;IAEXA,4DAAA,gBAAoC;IACIA,oDAAA,gBAAQ;IAAAA,0DAAA,EAAO;IACrDA,4DAAA,iBAIC;IACCA,oDAAA,IACF;IAAAA,0DAAA,EAAO;IAETA,wDAAA,KAAAmM,gDAAA,mBAQM;IACNnM,wDAAA,KAAAoM,gDAAA,mBAkBM;IACRpM,0DAAA,EAAM;IAIRA,wDAAA,KAAAqM,gDAAA,mBAmEM;IAGNrM,4DAAA,gBAAyC;IACvCA,wDAAA,KAAAsM,mDAAA,sBAgBS;IAETtM,wDAAA,KAAAuM,mDAAA,sBAOS;IAETvM,4DAAA,kBAMC;IALCA,wDAAA,mBAAAwM,mEAAA;MAAAxM,2DAAA,CAAAyM,KAAA;MAAA,MAAAC,QAAA,GAAA1M,2DAAA;MACe0M,QAAA,CAAAzF,kBAAA,CAAAyF,QAAA,CAAAtC,mBAAA,CAAAlF,EAAA,CACd;MAAA,OAAclF,yDAAA,CAAA0M,QAAA,CAAAf,6BAAA,EAEzB;IAAA,EADW;IAGD3L,uDAAA,cAAqC;IACrCA,oDAAA,mBACF;IAAAA,0DAAA,EAAS;;;;IAvMLA,uDAAA,GAGC;IAHDA,wDAAA,SAAA2M,MAAA,CAAAvC,mBAAA,CAAA9C,QAAA,kBAAAqF,MAAA,CAAAvC,mBAAA,CAAA9C,QAAA,CAAAC,KAAA,yCAAAvH,2DAAA,CAGC;IAOCA,uDAAA,GACF;IADEA,gEAAA,OAAA2M,MAAA,CAAAvC,mBAAA,CAAA9C,QAAA,kBAAAqF,MAAA,CAAAvC,mBAAA,CAAA9C,QAAA,CAAAG,QAAA,yBACF;IAEEzH,uDAAA,GACF;IADEA,gEAAA,MAAAA,yDAAA,SAAA2M,MAAA,CAAAvC,mBAAA,CAAAzC,SAAA,iBACF;IAYF3H,uDAAA,GACF;IADEA,gEAAA,MAAA2M,MAAA,CAAAvC,mBAAA,CAAA1F,OAAA,MACF;IAEG1E,uDAAA,GAA0C;IAA1CA,wDAAA,SAAA2M,MAAA,CAAAvC,mBAAA,CAAA3F,OAAA,kBAAAkI,MAAA,CAAAvC,mBAAA,CAAA3F,OAAA,CAAAC,OAAA,CAA0C;IAiBH1E,uDAAA,IAEpC;IAFoCA,+DAAA,CAAA2M,MAAA,CAAAvC,mBAAA,CAAAxC,IAAA,CAEpC;IAMA5H,uDAAA,GAAmD;IAAnDA,yDAAA,mBAAA2M,MAAA,CAAAvC,mBAAA,CAAAjD,MAAA,CAAmD,qBAAAwF,MAAA,CAAAvC,mBAAA,CAAAjD,MAAA;IAGnDnH,uDAAA,GACF;IADEA,gEAAA,MAAA2M,MAAA,CAAAvC,mBAAA,CAAAjD,MAAA,wBACF;IAICnH,uDAAA,GAAgC;IAAhCA,wDAAA,SAAA2M,MAAA,CAAAvC,mBAAA,CAAAE,MAAA,CAAgC;IAShCtK,uDAAA,GAAiC;IAAjCA,wDAAA,UAAA2M,MAAA,CAAAvC,mBAAA,CAAAjD,MAAA,CAAiC;IAuBrCnH,uDAAA,GAAmC;IAAnCA,wDAAA,SAAA2M,MAAA,CAAAzC,kBAAA,CAAAtF,MAAA,KAAmC;IAsEjC5E,uDAAA,GAKX;IALWA,wDAAA,SAAA2M,MAAA,CAAAvC,mBAAA,CAAAxC,IAAA,sBAAA+E,MAAA,CAAAvC,mBAAA,CAAAxC,IAAA,uBAAA+E,MAAA,CAAAvC,mBAAA,CAAAxC,IAAA,wBAKX;IAaW5H,uDAAA,GAAiC;IAAjCA,wDAAA,UAAA2M,MAAA,CAAAvC,mBAAA,CAAAjD,MAAA,CAAiC;;;ADplBtC,MAAOyF,yBAAyB;EAgCpCC,YACUC,cAA8B,EAC9BC,YAA0B,EAC1BC,MAAc;IAFd,KAAAF,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,MAAM,GAANA,MAAM;IA3BhB,KAAArH,OAAO,GAAG,IAAI;IACd,KAAA4C,WAAW,GAAG,KAAK;IACnB,KAAA0E,oBAAoB,GAAG,IAAI;IAC3B,KAAAC,KAAK,GAAiB,IAAI;IAC1B,KAAAtK,cAAc,GAAG,KAAK;IACtB,KAAAC,YAAY,GAAG,KAAK;IAEpB;IACA,KAAAY,qBAAqB,GAAgB,IAAI0J,GAAG,EAAU;IACtD,KAAAnM,WAAW,GAAG,KAAK;IACnB,KAAA2C,gBAAgB,GAAG,KAAK;IAExB;IACA,KAAAyJ,oBAAoB,GAAG,KAAK;IAC5B,KAAAC,kBAAkB,GAAG,KAAK;IAC1B,KAAAnD,kBAAkB,GAAiB,EAAE;IAErC;IACA,KAAAoD,4BAA4B,GAAG,KAAK;IACpC,KAAAlD,mBAAmB,GAAwB,IAAI;IAEvC,KAAAmD,QAAQ,GAAG,IAAIlO,yCAAO,EAAQ;IAC9B,KAAAmO,eAAe,GAAG,IAAIjO,iDAAe,CAAS,CAAC,CAAC;IAOtD,IAAI,CAACkO,cAAc,GAAG,IAAI,CAACX,cAAc,CAACW,cAAc;IACxD,IAAI,CAACnF,sBAAsB,GAAG,IAAI,CAACmF,cAAc,CAAC,CAAC;IACnD,IAAI,CAAC1K,YAAY,GAAG,IAAI,CAAC+J,cAAc,CAACY,kBAAkB;IAC1D,IAAI,CAACC,WAAW,GAAG,IAAI,CAACZ,YAAY,CAACa,aAAa,CAACC,IAAI,CACrDnO,mDAAG,CAAEoO,KAAK,IAAKA,KAAK,CAAChE,IAAI,KAAK,MAAM,CAAC,CACtC;IAED;IACA,IAAI,CAACjH,YAAY,GAAG,IAAI,CAACiK,cAAc,CAACiB,OAAO,EAAE;EACnD;EAEA;;;;EAIAxI,gBAAgBA,CAACyI,YAA0B;IACzC;IACA,IAAI,CAACjI,UAAU,CAACiI,YAAY,CAAC9I,EAAE,CAAC;IAEhC;IACA,MAAM+I,cAAc,GAClBD,YAAY,CAACC,cAAc,IAC1BD,YAAY,CAACE,QAAQ,IAAIF,YAAY,CAACE,QAAQ,CAAC,gBAAgB,CAAE,KACjEF,YAAY,CAACG,aAAa,IAC3BH,YAAY,CAACG,aAAa,CAACC,QAAQ,CAAC,cAAc,CAAC,GAC/CJ,YAAY,CAACG,aAAa,GAC1B,IAAI,CAAC;IAEX,MAAME,OAAO,GACXL,YAAY,CAACK,OAAO,IACnBL,YAAY,CAACE,QAAQ,IAAIF,YAAY,CAACE,QAAQ,CAAC,SAAS,CAAE,KAC1DF,YAAY,CAACG,aAAa,IAC3BH,YAAY,CAACG,aAAa,CAACC,QAAQ,CAAC,OAAO,CAAC,GACxCJ,YAAY,CAACG,aAAa,GAC1B,IAAI,CAAC;IAEX;IACA,IAAIF,cAAc,EAAE;MAClB,IAAI,CAACjB,MAAM,CAACsB,QAAQ,CAAC,CAAC,8BAA8B,EAAEL,cAAc,CAAC,CAAC;KACvE,MAAM,IAAII,OAAO,EAAE;MAClB,IAAI,CAACrB,MAAM,CAACsB,QAAQ,CAAC,CAAC,iBAAiB,EAAED,OAAO,CAAC,CAAC;KACnD,MAAM,IAAIL,YAAY,CAAC1G,QAAQ,IAAI0G,YAAY,CAAC1G,QAAQ,CAACpC,EAAE,EAAE;MAC5D,IAAI,CAACS,OAAO,GAAG,IAAI;MAEnB,IAAI,CAACmH,cAAc,CAChByB,uBAAuB,CAACP,YAAY,CAAC1G,QAAQ,CAACpC,EAAE,CAAC,CACjDsJ,SAAS,CAAC;QACTC,IAAI,EAAGC,YAAY,IAAI;UACrB,IAAI,CAAC/I,OAAO,GAAG,KAAK;UACpB,IAAI+I,YAAY,IAAIA,YAAY,CAACxJ,EAAE,EAAE;YACnC,IAAI,CAAC8H,MAAM,CAACsB,QAAQ,CAAC,CACnB,8BAA8B,EAC9BI,YAAY,CAACxJ,EAAE,CAChB,CAAC;WACH,MAAM;YACL,IAAI,CAAC8H,MAAM,CAACsB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;;QAEvC,CAAC;QACDpB,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACvH,OAAO,GAAG,KAAK;UACpB,IAAI,CAACuH,KAAK,GAAGA,KAAK;UAClB,IAAI,CAACF,MAAM,CAACsB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;QACrC;OACD,CAAC;KACL,MAAM;MACL,IAAI,CAACtB,MAAM,CAACsB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;;EAEvC;EAGApG,QAAQA,CAACyG,MAAmB;IAC1B,IAAI,CAACA,MAAM,EAAE;IAEb,MAAMC,cAAc,GAAGD,MAAM,CAACE,SAAS;IACvC,MAAMC,YAAY,GAAGH,MAAM,CAACG,YAAY;IACxC,MAAMC,YAAY,GAAGJ,MAAM,CAACI,YAAY;IAExC;IACA,IAAID,YAAY,GAAGF,cAAc,GAAGG,YAAY,GAAG,GAAG,EAAE;MACtD,IAAI,CAACvB,eAAe,CAACiB,IAAI,CAACG,cAAc,CAAC;;EAE7C;EACAI,QAAQA,CAAA;IACN;IACA,MAAMC,mBAAmB,GAAGC,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC;IAC1E,IAAIF,mBAAmB,KAAK,IAAI,EAAE;MAChC,IAAI,CAACpM,YAAY,GAAGoM,mBAAmB,KAAK,MAAM;MAClD,IAAI,CAACnC,cAAc,CAACsC,QAAQ,CAAC,IAAI,CAACvM,YAAY,CAAC;;IAGjD,IAAI,CAAChB,iBAAiB,EAAE;IACxB,IAAI,CAACwN,kBAAkB,EAAE;IACzB,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,0BAA0B,EAAE;EACnC;EAEA;;;EAGQA,0BAA0BA,CAAA;IAChC,MAAMC,sBAAsB,GAAG,IAAI,CAACC,yBAAyB,EAAE;IAE/D,IAAID,sBAAsB,CAAC1L,IAAI,GAAG,CAAC,EAAE;MACnC,IAAI,CAAC2J,cAAc,CAACI,IAAI,CAACjO,oDAAI,CAAC,CAAC,CAAC,CAAC,CAAC4O,SAAS,CAAEkB,aAAa,IAAI;QAC5D,MAAMC,qBAAqB,GAAGD,aAAa,CAAC3P,MAAM,CAC/CiO,YAAY,IAAK,CAACwB,sBAAsB,CAACI,GAAG,CAAC5B,YAAY,CAAC9I,EAAE,CAAC,CAC/D;QAEA,IAAI,CAAC4H,cAAsB,CAAC4C,aAAa,CAACjB,IAAI,CAACkB,qBAAqB,CAAC;QACtE,MAAME,WAAW,GAAGF,qBAAqB,CAAC5P,MAAM,CAC7C+P,CAAC,IAAK,CAACA,CAAC,CAAC3I,MAAM,CACjB,CAACvC,MAAM;QACP,IAAI,CAACkI,cAAsB,CAACiD,iBAAiB,CAACtB,IAAI,CAACoB,WAAW,CAAC;QAChE,IAAI,CAACG,uBAAuB,CAACL,qBAAqB,CAAC;MACrD,CAAC,CAAC;;EAEN;EAEAL,mBAAmBA,CAAA;IACjB;IACA,IAAI,CAAC9B,eAAe,CACjBK,IAAI,CACHlO,yDAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,EACxB1N,4DAAY,CAAC,GAAG,CAAC;IAAE;IACnBC,qEAAoB,EAAE;IAAE;IACxBC,uDAAM,CAAC,MAAM,CAAC,IAAI,CAACwI,WAAW,IAAI,IAAI,CAAC0E,oBAAoB,CAAC,CAAC;KAC9D,CACAuB,SAAS,CAAC,MAAK;MACd,IAAI,CAACyB,qBAAqB,EAAE;IAC9B,CAAC,CAAC;EACN;EACApO,iBAAiBA,CAAA;IACf,IAAI,CAAC8D,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC4C,WAAW,GAAG,KAAK;IACxB,IAAI,CAAC2E,KAAK,GAAG,IAAI;IACjB,IAAI,CAACD,oBAAoB,GAAG,IAAI;IAEhC,MAAMuC,sBAAsB,GAAG,IAAI,CAACC,yBAAyB,EAAE;IAE/D,IAAI,CAAC3C,cAAc,CAChBoD,gBAAgB,CAAC,IAAI,CAAC,CACtBrC,IAAI,CACHlO,yDAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,EACxB7N,mDAAG,CAAEgQ,aAAa,IAAI;MACpB,IAAIF,sBAAsB,CAAC1L,IAAI,GAAG,CAAC,EAAE;QACnC,OAAO4L,aAAa,CAAC3P,MAAM,CACxBiO,YAAY,IAAK,CAACwB,sBAAsB,CAACI,GAAG,CAAC5B,YAAY,CAAC9I,EAAE,CAAC,CAC/D;;MAEH,OAAOwK,aAAa;IACtB,CAAC,CAAC,CACH,CACAlB,SAAS,CAAC;MACTC,IAAI,EAAGiB,aAAa,IAAI;QACrB,IAAI,CAAC5C,cAAsB,CAAC4C,aAAa,CAACjB,IAAI,CAACiB,aAAa,CAAC;QAC9D,MAAMG,WAAW,GAAGH,aAAa,CAAC3P,MAAM,CAAE+P,CAAC,IAAK,CAACA,CAAC,CAAC3I,MAAM,CAAC,CAACvC,MAAM;QAChE,IAAI,CAACkI,cAAsB,CAACiD,iBAAiB,CAACtB,IAAI,CAACoB,WAAW,CAAC;QAChE,IAAI,CAAClK,OAAO,GAAG,KAAK;QACpB,IAAI,CAACsH,oBAAoB,GACvB,IAAI,CAACH,cAAc,CAACG,oBAAoB,EAAE;MAC9C,CAAC;MACDC,KAAK,EAAGiD,GAAU,IAAI;QACpB,IAAI,CAACjD,KAAK,GAAGiD,GAAG;QAChB,IAAI,CAACxK,OAAO,GAAG,KAAK;QACpB,IAAI,CAACsH,oBAAoB,GAAG,KAAK;MACnC;KACD,CAAC;EACN;EAEAgD,qBAAqBA,CAAA;IACnB,IAAI,IAAI,CAAC1H,WAAW,IAAI,CAAC,IAAI,CAAC0E,oBAAoB,EAAE;IAEpD,IAAI,CAAC1E,WAAW,GAAG,IAAI;IACvB,MAAMiH,sBAAsB,GAAG,IAAI,CAACC,yBAAyB,EAAE;IAE/D,IAAI,CAAC3C,cAAc,CAChBmD,qBAAqB,EAAE,CACvBpC,IAAI,CACHlO,yDAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,EACxB7N,mDAAG,CAAEgQ,aAAa,IAAI;MACpB,IAAIF,sBAAsB,CAAC1L,IAAI,GAAG,CAAC,EAAE;QACnC,OAAO4L,aAAa,CAAC3P,MAAM,CACxBiO,YAAY,IAAK,CAACwB,sBAAsB,CAACI,GAAG,CAAC5B,YAAY,CAAC9I,EAAE,CAAC,CAC/D;;MAEH,OAAOwK,aAAa;IACtB,CAAC,CAAC,CACH,CACAlB,SAAS,CAAC;MACTC,IAAI,EAAGiB,aAAa,IAAI;QACtB,IAAI,CAACjC,cAAc,CAChBI,IAAI,CAACjO,oDAAI,CAAC,CAAC,CAAC,CAAC,CACb4O,SAAS,CAAE4B,qBAAqB,IAAI;UACnC,MAAMC,gBAAgB,GAAG,CACvB,GAAGD,qBAAqB,EACxB,GAAGV,aAAa,CACjB;UACA,IAAI,CAAC5C,cAAsB,CAAC4C,aAAa,CAACjB,IAAI,CAAC4B,gBAAgB,CAAC;UACjE,MAAMR,WAAW,GAAGQ,gBAAgB,CAACtQ,MAAM,CACxC+P,CAAC,IAAK,CAACA,CAAC,CAAC3I,MAAM,CACjB,CAACvC,MAAM;UACP,IAAI,CAACkI,cAAsB,CAACiD,iBAAiB,CAACtB,IAAI,CAACoB,WAAW,CAAC;UAChE,IAAI,CAACG,uBAAuB,CAACK,gBAAgB,CAAC;QAChD,CAAC,CAAC;QAEJ,IAAI,CAAC9H,WAAW,GAAG,KAAK;QACxB,IAAI,CAAC0E,oBAAoB,GACvB,IAAI,CAACH,cAAc,CAACG,oBAAoB,EAAE;MAC9C,CAAC;MACDC,KAAK,EAAGiD,GAAU,IAAI;QACpB,IAAI,CAAC5H,WAAW,GAAG,KAAK;QACxB,IAAI,CAAC0E,oBAAoB,GAAG,KAAK;MACnC;KACD,CAAC;EACN;EACAoC,kBAAkBA,CAAA;IAChB,IAAI,CAACvC,cAAc,CAChBwD,2BAA2B,EAAE,CAC7BzC,IAAI,CACHlO,yDAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,EACxB9N,2DAAU,CAAEyN,KAAK,IAAI;MACnBqD,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEtD,KAAK,CAAC;MAChD,OAAO5N,yCAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH,CACAkP,SAAS,EAAE;IACd,IAAI,CAAC1B,cAAc,CAChB2D,4BAA4B,EAAE,CAC9B5C,IAAI,CACHlO,yDAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,EACxB9N,2DAAU,CAAEyN,KAAK,IAAI;MACnBqD,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEtD,KAAK,CAAC;MACtD,OAAO5N,yCAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH,CACAkP,SAAS,EAAE;EAChB;EACAzI,UAAUA,CAAC2K,cAAsB;IAC/B,IAAI,CAACA,cAAc,EAAE;MACnB,IAAI,CAACxD,KAAK,GAAG,IAAIyD,KAAK,CAAC,6BAA6B,CAAC;MACrD;;IAGF,IAAI,CAAClD,cAAc,CAACI,IAAI,CAACjO,oDAAI,CAAC,CAAC,CAAC,CAAC,CAAC4O,SAAS,CAAEkB,aAAa,IAAI;MAC5D,MAAM1B,YAAY,GAAG0B,aAAa,CAACkB,IAAI,CAAEd,CAAC,IAAKA,CAAC,CAAC5K,EAAE,KAAKwL,cAAc,CAAC;MACvE,IAAI1C,YAAY,EAAE;QAChB,IAAIA,YAAY,CAAC7G,MAAM,EAAE;QAEzB,MAAM0J,oBAAoB,GAAGnB,aAAa,CAAChQ,GAAG,CAAEoQ,CAAC,IAC/CA,CAAC,CAAC5K,EAAE,KAAKwL,cAAc,GACnB;UAAE,GAAGZ,CAAC;UAAE3I,MAAM,EAAE,IAAI;UAAEmD,MAAM,EAAE,IAAIwG,IAAI,EAAE,CAACC,WAAW;QAAE,CAAE,GACxDjB,CAAC,CACN;QAED,IAAI,CAACkB,yBAAyB,CAACH,oBAAoB,CAAC;QAEpD,IAAI,CAAC/D,cAAc,CAChB/G,UAAU,CAAC,CAAC2K,cAAc,CAAC,CAAC,CAC5B7C,IAAI,CAAClO,yDAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,CAAC,CAC9BiB,SAAS,CAAC;UACTC,IAAI,EAAGwC,MAAM,IAAI;YACf,IAAIA,MAAM,IAAIA,MAAM,CAACC,OAAO,EAAE;cAC5B,IAAI,IAAI,CAAChE,KAAK,IAAI,IAAI,CAACA,KAAK,CAACzI,OAAO,CAAC2J,QAAQ,CAAC,MAAM,CAAC,EAAE;gBACrD,IAAI,CAAClB,KAAK,GAAG,IAAI;;;UAGvB,CAAC;UACDA,KAAK,EAAGiD,GAAG,IAAI;YACb,MAAMgB,qBAAqB,GAAGzB,aAAa,CAAChQ,GAAG,CAAEoQ,CAAC,IAChDA,CAAC,CAAC5K,EAAE,KAAKwL,cAAc,GACnB;cAAE,GAAGZ,CAAC;cAAE3I,MAAM,EAAE,KAAK;cAAEmD,MAAM,EAAE8G;YAAS,CAAE,GAC1CtB,CAAC,CACN;YACA,IAAI,CAAChD,cAAsB,CAAC4C,aAAa,CAACjB,IAAI,CAC7C0C,qBAAqB,CACtB;YAED,MAAME,mBAAmB,GAAGF,qBAAqB,CAACpR,MAAM,CACrD+P,CAAC,IAAK,CAACA,CAAC,CAAC3I,MAAM,CACjB,CAACvC,MAAM;YACP,IAAI,CAACkI,cAAsB,CAACiD,iBAAiB,CAACtB,IAAI,CACjD4C,mBAAmB,CACpB;UACH;SACD,CAAC;OACL,MAAM;QACL,IAAI,CAACxP,iBAAiB,EAAE;;IAE5B,CAAC,CAAC;EACJ;EAEA;;;;EAIQmP,yBAAyBA,CAACtB,aAAoB;IACpD;IACC,IAAI,CAAC5C,cAAsB,CAAC4C,aAAa,CAACjB,IAAI,CAACiB,aAAa,CAAC;IAE9D;IACA,MAAMG,WAAW,GAAGH,aAAa,CAAC3P,MAAM,CAAE+P,CAAC,IAAK,CAACA,CAAC,CAAC3I,MAAM,CAAC,CAACvC,MAAM;IAChE,IAAI,CAACkI,cAAsB,CAACiD,iBAAiB,CAACtB,IAAI,CAACoB,WAAW,CAAC;IAEhE;IACA,IAAI,CAACG,uBAAuB,CAACN,aAAa,CAAC;EAC7C;EAEA;;;;EAIQM,uBAAuBA,CAACN,aAAoB;IAClDA,aAAa,CAAC4B,OAAO,CAAEtD,YAAY,IAAI;MACpC,IAAI,CAAClB,cAAsB,CAACkD,uBAAuB,GAAGhC,YAAY,CAAC;IACtE,CAAC,CAAC;EACJ;EAEA;;;EAGQuD,cAAcA,CAAA;IACpB,IAAI,CAAC9N,qBAAqB,CAACC,KAAK,EAAE;IAClC,IAAI,CAAC1C,WAAW,GAAG,KAAK;IACxB,IAAI,CAAC2C,gBAAgB,GAAG,KAAK;EAC/B;EAEAvC,aAAaA,CAAA;IACX,IAAI,CAACqM,cAAc,CAACI,IAAI,CAACjO,oDAAI,CAAC,CAAC,CAAC,CAAC,CAAC4O,SAAS,CAAEkB,aAAa,IAAI;MAC5D,MAAM8B,SAAS,GAAG9B,aAAa,CAAC3P,MAAM,CAAE+P,CAAC,IAAK,CAACA,CAAC,CAAC3I,MAAM,CAAC,CAACzH,GAAG,CAAEoQ,CAAC,IAAKA,CAAC,CAAC5K,EAAE,CAAC;MAEzE,IAAIsM,SAAS,CAAC5M,MAAM,KAAK,CAAC,EAAE;MAE5B,MAAM6M,QAAQ,GAAGD,SAAS,CAACzR,MAAM,CAC9BmF,EAAE,IAAKA,EAAE,IAAI,OAAOA,EAAE,KAAK,QAAQ,IAAIA,EAAE,CAACwM,IAAI,EAAE,KAAK,EAAE,CACzD;MAED,IAAID,QAAQ,CAAC7M,MAAM,KAAK4M,SAAS,CAAC5M,MAAM,EAAE;QACxC,IAAI,CAACsI,KAAK,GAAG,IAAIyD,KAAK,CAAC,0BAA0B,CAAC;QAClD;;MAGF,MAAME,oBAAoB,GAAGnB,aAAa,CAAChQ,GAAG,CAAEoQ,CAAC,IAC/C2B,QAAQ,CAACrD,QAAQ,CAAC0B,CAAC,CAAC5K,EAAE,CAAC,GACnB;QAAE,GAAG4K,CAAC;QAAE3I,MAAM,EAAE,IAAI;QAAEmD,MAAM,EAAE,IAAIwG,IAAI,EAAE,CAACC,WAAW;MAAE,CAAE,GACxDjB,CAAC,CACN;MAED,IAAI,CAACkB,yBAAyB,CAACH,oBAAoB,CAAC;MAEpD,IAAI,CAAC/D,cAAc,CAChB/G,UAAU,CAAC0L,QAAQ,CAAC,CACpB5D,IAAI,CAAClO,yDAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,CAAC,CAC9BiB,SAAS,CAAC;QACTC,IAAI,EAAGwC,MAAM,IAAI;UACf,IAAIA,MAAM,IAAIA,MAAM,CAACC,OAAO,EAAE;YAC5B,IAAI,IAAI,CAAChE,KAAK,IAAI,IAAI,CAACA,KAAK,CAACzI,OAAO,CAAC2J,QAAQ,CAAC,MAAM,CAAC,EAAE;cACrD,IAAI,CAAClB,KAAK,GAAG,IAAI;;;QAGvB,CAAC;QACDA,KAAK,EAAGiD,GAAG,IAAI;UACb;QAAA;OAEH,CAAC;IACN,CAAC,CAAC;EACJ;EAEAzN,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAC+K,cAAc,CAACI,IAAI,CAC7BnO,mDAAG,CAAEgQ,aAAa,IAAKA,aAAa,EAAE9K,MAAM,GAAG,CAAC,CAAC,CAClD;EACH;EACA+M,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAAC5O,YAAY,CAAC8K,IAAI,CAACnO,mDAAG,CAAEkS,KAAK,IAAKA,KAAK,GAAG,CAAC,CAAC,CAAC;EAC1D;EAEA;;;EAGA1P,kBAAkBA,CAAA;IAChB,IAAI,CAACU,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAE1C,IAAI,IAAI,CAACA,cAAc,EAAE;MACvB,IAAI,CAAC0F,sBAAsB,GACzB,IAAI,CAACwE,cAAc,CAAC+E,sBAAsB,EAAE;KAC/C,MAAM;MACL,IAAI,CAACvJ,sBAAsB,GAAG,IAAI,CAACmF,cAAc;;EAErD;EAEA;;;EAGApL,WAAWA,CAAA;IACT,IAAI,CAACQ,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IACtC,IAAI,CAACiK,cAAc,CAACsC,QAAQ,CAAC,IAAI,CAACvM,YAAY,CAAC;IAE/C,IAAI,CAAC,IAAI,CAACA,YAAY,EAAE;MACtBiP,UAAU,CAAC,MAAK;QACd,IAAI,CAAChF,cAAc,CAACiF,qBAAqB,EAAE;QAC3CD,UAAU,CAAC,MAAK;UACd,IAAI,CAAChF,cAAc,CAACiF,qBAAqB,EAAE;QAC7C,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,EAAE,GAAG,CAAC;;IAGT7C,YAAY,CAAC8C,OAAO,CAClB,wBAAwB,EACxB,IAAI,CAACnP,YAAY,CAACoP,QAAQ,EAAE,CAC7B;EACH;EAEA;;;;EAIAhN,0BAA0BA,CAACyL,cAAsB;IAC/C,IAAI,CAACA,cAAc,EAAE;IAErB,IAAI,CAACxG,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACmD,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACD,oBAAoB,GAAG,IAAI;IAEhC,IAAIY,YAAsC;IAE1C,IAAI,CAACP,cAAc,CAACI,IAAI,CAACjO,oDAAI,CAAC,CAAC,CAAC,CAAC,CAAC4O,SAAS,CAAEkB,aAAa,IAAI;MAC5D1B,YAAY,GAAG0B,aAAa,CAACkB,IAAI,CAC9Bd,CAAe,IAAKA,CAAC,CAAC5K,EAAE,KAAKwL,cAAc,CAC7C;IACH,CAAC,CAAC;IAEF,IACE1C,YAAY,IACZA,YAAY,CAACvJ,OAAO,IACpBuJ,YAAY,CAACvJ,OAAO,CAACE,WAAW,IAChCqJ,YAAY,CAACvJ,OAAO,CAACE,WAAW,CAACC,MAAM,GAAG,CAAC,EAC3C;MACA,IAAI,CAACyI,kBAAkB,GAAG,KAAK;MAC/B,IAAI,CAACnD,kBAAkB,GAAG8D,YAAY,CAACvJ,OAAO,CAACE,WAAW,CAACjF,GAAG,CAC3DwS,UAAkC,KAChC;QACChN,EAAE,EAAE,EAAE;QACN2D,GAAG,EAAEqJ,UAAU,CAACrJ,GAAG,IAAI,EAAE;QACzBjB,IAAI,EAAE,IAAI,CAACuK,kCAAkC,CAACD,UAAU,CAACtK,IAAI,CAAC;QAC9DkC,IAAI,EAAEoI,UAAU,CAACpI,IAAI,IAAI,EAAE;QAC3BhG,IAAI,EAAEoO,UAAU,CAACpO,IAAI,IAAI,CAAC;QAC1BsO,QAAQ,EAAE;OACI,EACnB;MACD;;IAGF,IAAI,CAACtF,cAAc,CAChB7H,0BAA0B,CAACyL,cAAc,CAAC,CAC1C7C,IAAI,CAAClO,yDAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,CAAC,CAC9BiB,SAAS,CAAC;MACTC,IAAI,EAAG9J,WAAW,IAAI;QACpB,IAAI,CAAC0I,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAACnD,kBAAkB,GAAGvF,WAAW;MACvC,CAAC;MACDuI,KAAK,EAAGiD,GAAG,IAAI;QACb,IAAI,CAAC9C,kBAAkB,GAAG,KAAK;MACjC;KACD,CAAC;EACN;EAEA;;;EAGAgF,qBAAqBA,CAAA;IACnB,IAAI,CAACjF,oBAAoB,GAAG,KAAK;EACnC;EAEA;;;;EAIAvG,uBAAuBA,CAACmH,YAA0B;IAChD,IAAI,CAAC5D,mBAAmB,GAAG4D,YAAY;IACvC,IAAI,CAACV,4BAA4B,GAAG,IAAI;IAExC,IAAIU,YAAY,CAACvJ,OAAO,EAAEE,WAAW,EAAEC,MAAM,EAAE;MAC7C,IAAI,CAAC0N,kCAAkC,CAACtE,YAAY,CAAC9I,EAAE,CAAC;;EAE5D;EAEA;;;EAGAyG,6BAA6BA,CAAA;IAC3B,IAAI,CAAC2B,4BAA4B,GAAG,KAAK;IACzC,IAAI,CAAClD,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACF,kBAAkB,GAAG,EAAE;EAC9B;EAEA;;;EAGQoI,kCAAkCA,CAAC5B,cAAsB;IAC/D,IAAI,CAACxG,kBAAkB,GAAG,EAAE;IAE5B,IAAI,IAAI,CAACE,mBAAmB,EAAE3F,OAAO,EAAEE,WAAW,EAAEC,MAAM,EAAE;MAC1D,IAAI,CAACsF,kBAAkB,GACrB,IAAI,CAACE,mBAAmB,CAAC3F,OAAO,CAACE,WAAW,CAACjF,GAAG,CAC7CwS,UAAkC,KAChC;QACChN,EAAE,EAAE,EAAE;QACN2D,GAAG,EAAEqJ,UAAU,CAACrJ,GAAG,IAAI,EAAE;QACzBjB,IAAI,EAAE,IAAI,CAACuK,kCAAkC,CAACD,UAAU,CAACtK,IAAI,CAAC;QAC9DkC,IAAI,EAAEoI,UAAU,CAACpI,IAAI,IAAI,EAAE;QAC3BhG,IAAI,EAAEoO,UAAU,CAACpO,IAAI,IAAI,CAAC;QAC1BsO,QAAQ,EAAE;OACI,EACnB;;EAEP;EAEA;;;EAGQD,kCAAkCA,CACxCvK,IAAoB;IAEpB,QAAQA,IAAI;MACV,KAAK,OAAO;QACV,OAAOpI,qEAAW,CAAC+S,KAAK;MAC1B,KAAK,OAAO;QACV,OAAO/S,qEAAW,CAACgT,KAAK;MAC1B,KAAK,OAAO;QACV,OAAOhT,qEAAW,CAACiT,KAAK;MAC1B,KAAK,MAAM;QACT,OAAOjT,qEAAW,CAACkT,IAAI;MACzB;QACE,OAAOlT,qEAAW,CAACkT,IAAI;;EAE7B;EAEA;;;EAGA7I,OAAOA,CAACjC,IAAY;IAClB,OAAOA,IAAI,EAAE+K,UAAU,CAAC,QAAQ,CAAC,IAAI,KAAK;EAC5C;EAEA;;;;;EAKA3J,WAAWA,CAACpB,IAAY;IACtB,IAAI,CAACA,IAAI,EAAE,OAAO,aAAa;IAE/B,IAAIA,IAAI,CAAC+K,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACzD,IAAI/K,IAAI,CAAC+K,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACzD,IAAI/K,IAAI,CAAC+K,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,mBAAmB;IACzD,IAAI/K,IAAI,CAAC+K,UAAU,CAAC,OAAO,CAAC,EAAE,OAAO,iBAAiB;IACtD,IAAI/K,IAAI,CAACwG,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,iBAAiB;IAClD,IAAIxG,IAAI,CAACwG,QAAQ,CAAC,MAAM,CAAC,IAAIxG,IAAI,CAACwG,QAAQ,CAAC,UAAU,CAAC,EACpD,OAAO,kBAAkB;IAC3B,IAAIxG,IAAI,CAACwG,QAAQ,CAAC,OAAO,CAAC,IAAIxG,IAAI,CAACwG,QAAQ,CAAC,OAAO,CAAC,EAClD,OAAO,mBAAmB;IAC5B,IAAIxG,IAAI,CAACwG,QAAQ,CAAC,YAAY,CAAC,IAAIxG,IAAI,CAACwG,QAAQ,CAAC,cAAc,CAAC,EAC9D,OAAO,wBAAwB;IACjC,IAAIxG,IAAI,CAACwG,QAAQ,CAAC,KAAK,CAAC,IAAIxG,IAAI,CAACwG,QAAQ,CAAC,YAAY,CAAC,EACrD,OAAO,qBAAqB;IAE9B,OAAO,aAAa;EACtB;EAEA;;;;;EAKArE,gBAAgBA,CAACnC,IAAY;IAC3B,IAAI,CAACA,IAAI,EAAE,OAAO,SAAS;IAE3B,IAAIA,IAAI,CAAC+K,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;IAC7C,IAAI/K,IAAI,CAAC+K,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;IAC7C,IAAI/K,IAAI,CAAC+K,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;IAC7C,IAAI/K,IAAI,CAAC+K,UAAU,CAAC,OAAO,CAAC,EAAE,OAAO,OAAO;IAC5C,IAAI/K,IAAI,CAACwG,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;IACtC,IAAIxG,IAAI,CAACwG,QAAQ,CAAC,MAAM,CAAC,IAAIxG,IAAI,CAACwG,QAAQ,CAAC,UAAU,CAAC,EAAE,OAAO,UAAU;IACzE,IAAIxG,IAAI,CAACwG,QAAQ,CAAC,OAAO,CAAC,IAAIxG,IAAI,CAACwG,QAAQ,CAAC,OAAO,CAAC,EAClD,OAAO,mBAAmB;IAC5B,IAAIxG,IAAI,CAACwG,QAAQ,CAAC,YAAY,CAAC,IAAIxG,IAAI,CAACwG,QAAQ,CAAC,cAAc,CAAC,EAC9D,OAAO,cAAc;IACvB,IAAIxG,IAAI,CAACwG,QAAQ,CAAC,KAAK,CAAC,IAAIxG,IAAI,CAACwG,QAAQ,CAAC,YAAY,CAAC,EAAE,OAAO,SAAS;IAEzE,OAAO,SAAS;EAClB;EAEA;;;;;EAKAlF,cAAcA,CAACpF,IAAY;IACzB,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IAEpB,MAAM8O,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3C,IAAIC,CAAC,GAAG,CAAC;IACT,IAAIC,aAAa,GAAGhP,IAAI;IAExB,OAAOgP,aAAa,IAAI,IAAI,IAAID,CAAC,GAAGD,KAAK,CAAChO,MAAM,GAAG,CAAC,EAAE;MACpDkO,aAAa,IAAI,IAAI;MACrBD,CAAC,EAAE;;IAGL,OAAO,GAAGC,aAAa,CAACC,OAAO,CAAC,CAAC,CAAC,IAAIH,KAAK,CAACC,CAAC,CAAC,EAAE;EAClD;EAEA;;;;EAIAjK,cAAcA,CAACC,GAAW;IACxB,IAAI,CAACA,GAAG,EAAE;IACVmK,MAAM,CAACC,IAAI,CAACpK,GAAG,EAAE,QAAQ,CAAC;EAC5B;EAEA;;;;EAIAc,kBAAkBA,CAACuI,UAAsB;IACvC,IAAI,CAACA,UAAU,EAAErJ,GAAG,EAAE;IAEtB,MAAMqK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGnB,UAAU,CAACrJ,GAAG;IAC1BqK,IAAI,CAACI,QAAQ,GAAGpB,UAAU,CAACpI,IAAI,IAAI,YAAY;IAC/CoJ,IAAI,CAACvE,MAAM,GAAG,QAAQ;IACtBwE,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;IAC/BA,IAAI,CAACO,KAAK,EAAE;IACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;EACjC;EAEAS,mBAAmBA,CAAC3F,YAA0B;IAC5C,IAAI,CAACjI,UAAU,CAACiI,YAAY,CAAC9I,EAAE,CAAC;EAClC;EAEA;;;;EAIA+B,kBAAkBA,CAACyJ,cAAsB;IACvC,IAAI,CAACA,cAAc,EAAE;MACnB,IAAI,CAACxD,KAAK,GAAG,IAAIyD,KAAK,CAAC,6BAA6B,CAAC;MACrD;;IAGF,MAAMnB,sBAAsB,GAAG,IAAI,CAACC,yBAAyB,EAAE;IAC/DD,sBAAsB,CAACoE,GAAG,CAAClD,cAAc,CAAC;IAC1C,IAAI,CAACmD,0BAA0B,CAACrE,sBAAsB,CAAC;IAEvD,IAAI,CAAC1C,cAAc,CAChB7F,kBAAkB,CAACyJ,cAAc,CAAC,CAClC7C,IAAI,CAAClO,yDAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,CAAC,CAC9BiB,SAAS,CAAC;MACTC,IAAI,EAAGwC,MAAM,IAAI;QACf,IAAIA,MAAM,IAAIA,MAAM,CAACC,OAAO,EAAE;UAC5B,IAAI,IAAI,CAAChE,KAAK,IAAI,IAAI,CAACA,KAAK,CAACzI,OAAO,CAAC2J,QAAQ,CAAC,aAAa,CAAC,EAAE;YAC5D,IAAI,CAAClB,KAAK,GAAG,IAAI;;;MAGvB,CAAC;MACDA,KAAK,EAAGiD,GAAG,IAAI;QACb,IAAI,CAACjD,KAAK,GAAGiD,GAAG;MAClB;KACD,CAAC;EACN;EAEA;;;EAGA1O,sBAAsBA,CAAA;IACpB,IAAI,CAACgM,cAAc,CAACI,IAAI,CAACjO,oDAAI,CAAC,CAAC,CAAC,CAAC,CAAC4O,SAAS,CAAEkB,aAAa,IAAI;MAC5D,MAAMF,sBAAsB,GAAG,IAAI,CAACC,yBAAyB,EAAE;MAE/DC,aAAa,CAAC4B,OAAO,CAAEtD,YAAY,IAAI;QACrCwB,sBAAsB,CAACoE,GAAG,CAAC5F,YAAY,CAAC9I,EAAE,CAAC;MAC7C,CAAC,CAAC;MAEF,IAAI,CAAC2O,0BAA0B,CAACrE,sBAAsB,CAAC;MAEvD,IAAI,CAAC1C,cAAc,CAChBrL,sBAAsB,EAAE,CACxBoM,IAAI,CAAClO,yDAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,CAAC,CAC9BiB,SAAS,CAAC;QACTC,IAAI,EAAGwC,MAAM,IAAI;UACf,IAAIA,MAAM,IAAIA,MAAM,CAACC,OAAO,EAAE;YAC5B,IAAI,IAAI,CAAChE,KAAK,IAAI,IAAI,CAACA,KAAK,CAACzI,OAAO,CAAC2J,QAAQ,CAAC,aAAa,CAAC,EAAE;cAC5D,IAAI,CAAClB,KAAK,GAAG,IAAI;;;QAGvB,CAAC;QACDA,KAAK,EAAGiD,GAAG,IAAI;UACb,IAAI,CAACjD,KAAK,GAAGiD,GAAG;QAClB;OACD,CAAC;IACN,CAAC,CAAC;EACJ;EAEA/L,eAAeA,CAAA;IACb,OAAO,IAAI,CAAC8I,KAAK,EAAEzI,OAAO,IAAI,wBAAwB;EACxD;EAEA;;;;EAIQgL,yBAAyBA,CAAA;IAC/B,IAAI;MACF,MAAMqE,cAAc,GAAG5E,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC;MACrE,IAAI2E,cAAc,EAAE;QAClB,OAAO,IAAI3G,GAAG,CAAS4G,IAAI,CAACC,KAAK,CAACF,cAAc,CAAC,CAAC;;MAEpD,OAAO,IAAI3G,GAAG,EAAU;KACzB,CAAC,OAAOD,KAAK,EAAE;MACd,OAAO,IAAIC,GAAG,EAAU;;EAE5B;EAEA;;;;EAIQ0G,0BAA0BA,CAACI,UAAuB;IACxD,IAAI;MACF/E,YAAY,CAAC8C,OAAO,CAClB,wBAAwB,EACxB+B,IAAI,CAACG,SAAS,CAACC,KAAK,CAACC,IAAI,CAACH,UAAU,CAAC,CAAC,CACvC;KACF,CAAC,OAAO/G,KAAK,EAAE;MACd;IAAA;EAEJ;EAEAmH,WAAWA,CAAA;IACT,IAAI,CAAC9G,QAAQ,CAACkB,IAAI,EAAE;IACpB,IAAI,CAAClB,QAAQ,CAAC+G,QAAQ,EAAE;EAC1B;EAEA;;;;;EAKAjO,eAAeA,CAACqK,cAAsB,EAAE6D,KAAY;IAClDA,KAAK,CAACpP,eAAe,EAAE,CAAC,CAAC;IAEzB,IAAI,IAAI,CAAC1B,qBAAqB,CAACmM,GAAG,CAACc,cAAc,CAAC,EAAE;MAClD,IAAI,CAACjN,qBAAqB,CAAC+Q,MAAM,CAAC9D,cAAc,CAAC;KAClD,MAAM;MACL,IAAI,CAACjN,qBAAqB,CAACmQ,GAAG,CAAClD,cAAc,CAAC;;IAGhD;IACA,IAAI,CAAC+D,oBAAoB,EAAE;IAE3B;IACA,IAAI,CAAC9Q,gBAAgB,GAAG,IAAI,CAACF,qBAAqB,CAACK,IAAI,GAAG,CAAC;EAC7D;EAEA;;;;EAIApD,eAAeA,CAAC6T,KAAY;IAC1BA,KAAK,CAACpP,eAAe,EAAE,CAAC,CAAC;IAEzB,IAAI,CAACnE,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;IAEpC,IAAI,CAACsH,sBAAsB,CAACuF,IAAI,CAACjO,oDAAI,CAAC,CAAC,CAAC,CAAC,CAAC4O,SAAS,CAAEkB,aAAa,IAAI;MACpE,IAAI,IAAI,CAAC1O,WAAW,EAAE;QACpB;QACA0O,aAAa,CAAC4B,OAAO,CAAEtD,YAAY,IAAI;UACrC,IAAI,CAACvK,qBAAqB,CAACmQ,GAAG,CAAC5F,YAAY,CAAC9I,EAAE,CAAC;QACjD,CAAC,CAAC;OACH,MAAM;QACL;QACA,IAAI,CAACzB,qBAAqB,CAACC,KAAK,EAAE;;MAGpC;MACA,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACF,qBAAqB,CAACK,IAAI,GAAG,CAAC;IAC7D,CAAC,CAAC;EACJ;EAEA;;;EAGQ2Q,oBAAoBA,CAAA;IAC1B,IAAI,CAACnM,sBAAsB,CAACuF,IAAI,CAACjO,oDAAI,CAAC,CAAC,CAAC,CAAC,CAAC4O,SAAS,CAAEkB,aAAa,IAAI;MACpE,IAAI,CAAC1O,WAAW,GACd0O,aAAa,CAAC9K,MAAM,GAAG,CAAC,IACxB,IAAI,CAACnB,qBAAqB,CAACK,IAAI,KAAK4L,aAAa,CAAC9K,MAAM;IAC5D,CAAC,CAAC;EACJ;EAEA;;;EAGAtB,2BAA2BA,CAAA;IACzB,IAAI,IAAI,CAACG,qBAAqB,CAACK,IAAI,KAAK,CAAC,EAAE;IAE3C,MAAM4Q,WAAW,GAAGP,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC3Q,qBAAqB,CAAC;IAE1D,IAAI,CAACgK,cAAc,CAACI,IAAI,CAACjO,oDAAI,CAAC,CAAC,CAAC,CAAC,CAAC4O,SAAS,CAAEkB,aAAa,IAAI;MAC5D,MAAMmB,oBAAoB,GAAGnB,aAAa,CAAC3P,MAAM,CAC9CiO,YAAY,IAAK,CAAC,IAAI,CAACvK,qBAAqB,CAACmM,GAAG,CAAC5B,YAAY,CAAC9I,EAAE,CAAC,CACnE;MAED,IAAI,CAAC8L,yBAAyB,CAACH,oBAAoB,CAAC;MACpD,IAAI,CAACU,cAAc,EAAE;IACvB,CAAC,CAAC;IAEF,IAAI,CAACzE,cAAc,CAChB6H,2BAA2B,CAACD,WAAW,CAAC,CACxC7G,IAAI,CAAClO,yDAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,CAAC,CAC9BiB,SAAS,CAAC;MACTC,IAAI,EAAGwC,MAAM,IAAI;QACf;MAAA,CACD;MACD/D,KAAK,EAAGiD,GAAG,IAAI;QACb;MAAA;KAEH,CAAC;EACN;EAEA;;;EAGAhN,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACM,qBAAqB,CAACK,IAAI,KAAK,CAAC,EAAE;IAE3C,MAAM4Q,WAAW,GAAGP,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC3Q,qBAAqB,CAAC;IAE1D,IAAI,CAACgK,cAAc,CAACI,IAAI,CAACjO,oDAAI,CAAC,CAAC,CAAC,CAAC,CAAC4O,SAAS,CAAEkB,aAAa,IAAI;MAC5D,MAAMmB,oBAAoB,GAAGnB,aAAa,CAAChQ,GAAG,CAAEsO,YAAY,IAC1D,IAAI,CAACvK,qBAAqB,CAACmM,GAAG,CAAC5B,YAAY,CAAC9I,EAAE,CAAC,GAC3C;QAAE,GAAG8I,YAAY;QAAE7G,MAAM,EAAE,IAAI;QAAEmD,MAAM,EAAE,IAAIwG,IAAI,EAAE,CAACC,WAAW;MAAE,CAAE,GACnE/C,YAAY,CACjB;MAED,IAAI,CAACgD,yBAAyB,CAACH,oBAAoB,CAAC;MACpD,IAAI,CAACU,cAAc,EAAE;IACvB,CAAC,CAAC;IAEF,IAAI,CAACzE,cAAc,CAChB/G,UAAU,CAAC2O,WAAW,CAAC,CACvB7G,IAAI,CAAClO,yDAAS,CAAC,IAAI,CAAC4N,QAAQ,CAAC,CAAC,CAC9BiB,SAAS,CAAC;MACTC,IAAI,EAAGwC,MAAM,IAAI;QACf;MAAA,CACD;MACD/D,KAAK,EAAGiD,GAAG,IAAI;QACb;MAAA;KAEH,CAAC;EACN;EAEA;;;;;EAKA9I,UAAUA,CAACqJ,cAAsB;IAC/B,OAAO,IAAI,CAACjN,qBAAqB,CAACmM,GAAG,CAACc,cAAc,CAAC;EACvD;;;uBA14BW9D,yBAAyB,EAAA5M,+DAAA,CAAA6U,4EAAA,GAAA7U,+DAAA,CAAA+U,qEAAA,GAAA/U,+DAAA,CAAAiV,oDAAA;IAAA;EAAA;;;YAAzBrI,yBAAyB;MAAAuI,SAAA;MAAAC,SAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;mBAAzBC,GAAA,CAAArN,QAAA,CAAA9H,MAAA,CAAAuO,MAAA,CAAuB;UAAA;;;;;;;;UCjCpC3O,4DAAA,aAGC;;UAECA,uDAAA,aAEM;UAENA,4DAAA,aAAsE;UAGhEA,uDAAA,WAAgC;UAChCA,oDAAA,sBACF;UAAAA,0DAAA,EAAK;UAGLA,wDAAA,IAAAwV,wCAAA,mBA+DM;UAGNxV,wDAAA,IAAAyV,wCAAA,kBAgCM;UACRzV,0DAAA,EAAM;UAGNA,wDAAA,KAAA0V,yCAAA,iBAGM;UAGN1V,wDAAA,KAAA2V,yCAAA,kBAcM;UAGN3V,wDAAA,KAAA4V,yCAAA,kBAYM;;UAGN5V,wDAAA,KAAA6V,yCAAA,kBA6JM;;UACR7V,0DAAA,EAAM;UAIRA,4DAAA,eAIC;UADCA,wDAAA,mBAAA8V,yDAAA;YAAA,OAASP,GAAA,CAAAlD,qBAAA,EAAuB;UAAA,EAAC;UAEjCrS,4DAAA,eAA2E;UAAnCA,wDAAA,mBAAA+V,yDAAA3V,MAAA;YAAA,OAASA,MAAA,CAAA+E,eAAA,EAAwB;UAAA,EAAC;UACxEnF,4DAAA,eAAqC;UAEjCA,uDAAA,aAAqC;UACrCA,oDAAA,6BACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,kBAAyE;UAAlCA,wDAAA,mBAAAgW,4DAAA;YAAA,OAAST,GAAA,CAAAlD,qBAAA,EAAuB;UAAA,EAAC;UACtErS,uDAAA,aAA4B;UAC9BA,0DAAA,EAAS;UAEXA,4DAAA,eAAmC;UACjCA,wDAAA,KAAAiW,yCAAA,iBAGM;UAENjW,wDAAA,KAAAkW,yCAAA,kBAWM;UAENlW,wDAAA,KAAAmW,yCAAA,kBA6DM;UACRnW,0DAAA,EAAM;UAKVA,4DAAA,eAIC;UADCA,wDAAA,mBAAAoW,yDAAA;YAAA,OAASb,GAAA,CAAA5J,6BAAA,EAA+B;UAAA,EAAC;UAEzC3L,4DAAA,eAA2E;UAAnCA,wDAAA,mBAAAqW,yDAAAjW,MAAA;YAAA,OAASA,MAAA,CAAA+E,eAAA,EAAwB;UAAA,EAAC;UACxEnF,4DAAA,eAAqC;UAEjCA,uDAAA,aAAuC;UACvCA,oDAAA,yCACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,kBAGC;UADCA,wDAAA,mBAAAsW,4DAAA;YAAA,OAASf,GAAA,CAAA5J,6BAAA,EAA+B;UAAA,EAAC;UAEzC3L,uDAAA,aAA4B;UAC9BA,0DAAA,EAAS;UAEXA,wDAAA,KAAAuW,yCAAA,oBAkNM;UACRvW,0DAAA,EAAM;;;UAvoBNA,yDAAA,SAAAA,yDAAA,QAAAuV,GAAA,CAAA5H,WAAA,EAAkC;UAeD3N,uDAAA,GAAuB;UAAvBA,wDAAA,UAAAuV,GAAA,CAAA5R,gBAAA,CAAuB;UAkEL3D,uDAAA,GAAsB;UAAtBA,wDAAA,SAAAuV,GAAA,CAAA5R,gBAAA,CAAsB;UAoCjE3D,uDAAA,GAAa;UAAbA,wDAAA,SAAAuV,GAAA,CAAA5P,OAAA,CAAa;UAMb3F,uDAAA,GAAW;UAAXA,wDAAA,SAAAuV,GAAA,CAAArI,KAAA,CAAW;UAkBdlN,uDAAA,GAA+C;UAA/CA,wDAAA,UAAAuV,GAAA,CAAA5P,OAAA,KAAA3F,yDAAA,SAAAuV,GAAA,CAAA7S,gBAAA,IAA+C;UAe/C1C,uDAAA,GAA8C;UAA9CA,wDAAA,UAAAuV,GAAA,CAAA5P,OAAA,IAAA3F,yDAAA,SAAAuV,GAAA,CAAA7S,gBAAA,IAA8C;UAmKnD1C,uDAAA,GAAwD;UAAxDA,yDAAA,YAAAuV,GAAA,CAAAnI,oBAAA,mBAAwD;UAc9CpN,uDAAA,GAAwB;UAAxBA,wDAAA,SAAAuV,GAAA,CAAAlI,kBAAA,CAAwB;UAM3BrN,uDAAA,GAA4D;UAA5DA,wDAAA,UAAAuV,GAAA,CAAAlI,kBAAA,IAAAkI,GAAA,CAAArL,kBAAA,CAAAtF,MAAA,OAA4D;UAa5D5E,uDAAA,GAA0D;UAA1DA,wDAAA,UAAAuV,GAAA,CAAAlI,kBAAA,IAAAkI,GAAA,CAAArL,kBAAA,CAAAtF,MAAA,KAA0D;UAoEjE5E,uDAAA,GAAgE;UAAhEA,yDAAA,YAAAuV,GAAA,CAAAjI,4BAAA,mBAAgE;UAgB1BtN,uDAAA,GAAyB;UAAzBA,wDAAA,SAAAuV,GAAA,CAAAnL,mBAAA,CAAyB;;;;;;;;;;;;;;;;;;;;;;;;;ACrbV;AACqC;AACC;;;AAE7F,MAAMuM,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH,qGAAsB;EACjCI,IAAI,EAAE;IAAEC,OAAO,EAAE;EAAe,CAAE;EAClCC,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,EAAE;IACRC,SAAS,EAAEjK,qGAAyB;IACpCkK,IAAI,EAAE;MAAEG,KAAK,EAAE;IAAe;GAC/B;CAEJ,CACF;AAKK,MAAOC,0BAA0B;;;uBAA1BA,0BAA0B;IAAA;EAAA;;;YAA1BA;IAA0B;EAAA;;;gBAH3BT,yDAAY,CAACU,QAAQ,CAACR,MAAM,CAAC,EAC7BF,yDAAY;IAAA;EAAA;;;sHAEXS,0BAA0B;IAAAE,OAAA,GAAAvC,yDAAA;IAAAwC,OAAA,GAF3BZ,yDAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;ACpBuB;AAC6B;AAC7B;AAC6C;AAC1B;;AAO5D,MAAOc,mBAAmB;;;uBAAnBA,mBAAmB;IAAA;EAAA;;;YAAnBA;IAAmB;EAAA;;;iBAFnB,CAACzC,4EAAc,CAAC;MAAAsC,OAAA,GADjBE,yDAAY,EAAEJ,qFAA0B,EAAET,yDAAY;IAAA;EAAA;;;sHAGrDc,mBAAmB;IAAAC,YAAA,GAJf5K,qGAAyB;IAAAwK,OAAA,GAC9BE,yDAAY,EAAEJ,qFAA0B,EAAET,yDAAY;EAAA;AAAA", "sources": ["./src/app/views/front/notifications/notification-list/notification-list.component.ts", "./src/app/views/front/notifications/notification-list/notification-list.component.html", "./src/app/views/front/notifications/notifications-routing.module.ts", "./src/app/views/front/notifications/notifications.module.ts"], "sourcesContent": ["import {\n  Component,\n  OnInit,\n  <PERSON><PERSON><PERSON><PERSON>,\n  HostL<PERSON>ener,\n  ElementRef,\n  ViewChild,\n} from '@angular/core';\nimport { Router } from '@angular/router';\nimport { MessageService } from 'src/app/services/message.service';\nimport { Observable, Subject, of, BehaviorSubject } from 'rxjs';\nimport {\n  Notification,\n  Attachment,\n  NotificationAttachment,\n  AttachmentType,\n  MessageType,\n} from 'src/app/models/message.model';\nimport {\n  catchError,\n  map,\n  takeUntil,\n  take,\n  debounceTime,\n  distinctUntilChanged,\n  filter,\n} from 'rxjs/operators';\nimport { ThemeService } from '@app/services/theme.service';\n@Component({\n  selector: 'app-notification-list',\n  templateUrl: './notification-list.component.html',\n  styleUrls: ['./notification-list.component.css'],\n})\nexport class NotificationListComponent implements OnInit, OnDestroy {\n  @ViewChild('notificationContainer', { static: false })\n  notificationContainer!: ElementRef;\n\n  notifications$: Observable<Notification[]>;\n  filteredNotifications$: Observable<Notification[]>;\n  unreadCount$: Observable<number>;\n  isDarkMode$: Observable<boolean>;\n  loading = true;\n  loadingMore = false;\n  hasMoreNotifications = true;\n  error: Error | null = null;\n  showOnlyUnread = false;\n  isSoundMuted = false;\n\n  // Propriétés pour la sélection multiple\n  selectedNotifications: Set<string> = new Set<string>();\n  allSelected = false;\n  showSelectionBar = false;\n\n  // Propriétés pour le modal des pièces jointes\n  showAttachmentsModal = false;\n  loadingAttachments = false;\n  currentAttachments: Attachment[] = [];\n\n  // Propriétés pour le modal des détails de notification\n  showNotificationDetailsModal = false;\n  currentNotification: Notification | null = null;\n\n  private destroy$ = new Subject<void>();\n  private scrollPosition$ = new BehaviorSubject<number>(0);\n\n  constructor(\n    private messageService: MessageService,\n    private themeService: ThemeService,\n    private router: Router\n  ) {\n    this.notifications$ = this.messageService.notifications$;\n    this.filteredNotifications$ = this.notifications$; // Par défaut, afficher toutes les notifications\n    this.unreadCount$ = this.messageService.notificationCount$;\n    this.isDarkMode$ = this.themeService.currentTheme$.pipe(\n      map((theme) => theme.name === 'dark')\n    );\n\n    // Vérifier l'état du son\n    this.isSoundMuted = this.messageService.isMuted();\n  }\n\n  /**\n   * Rejoint une conversation ou un groupe à partir d'une notification\n   * @param notification Notification contenant les informations de la conversation ou du groupe\n   */\n  joinConversation(notification: Notification): void {\n    // Marquer la notification comme lue d'abord\n    this.markAsRead(notification.id);\n\n    // Extraire les informations pertinentes de la notification\n    const conversationId =\n      notification.conversationId ||\n      (notification.metadata && notification.metadata['conversationId']) ||\n      (notification.relatedEntity &&\n      notification.relatedEntity.includes('conversation')\n        ? notification.relatedEntity\n        : null);\n\n    const groupId =\n      notification.groupId ||\n      (notification.metadata && notification.metadata['groupId']) ||\n      (notification.relatedEntity &&\n      notification.relatedEntity.includes('group')\n        ? notification.relatedEntity\n        : null);\n\n    // Déterminer où rediriger l'utilisateur\n    if (conversationId) {\n      this.router.navigate(['/messages/conversations/chat', conversationId]);\n    } else if (groupId) {\n      this.router.navigate(['/messages/group', groupId]);\n    } else if (notification.senderId && notification.senderId.id) {\n      this.loading = true;\n\n      this.messageService\n        .getOrCreateConversation(notification.senderId.id)\n        .subscribe({\n          next: (conversation) => {\n            this.loading = false;\n            if (conversation && conversation.id) {\n              this.router.navigate([\n                '/messages/conversations/chat',\n                conversation.id,\n              ]);\n            } else {\n              this.router.navigate(['/messages']);\n            }\n          },\n          error: (error) => {\n            this.loading = false;\n            this.error = error;\n            this.router.navigate(['/messages']);\n          },\n        });\n    } else {\n      this.router.navigate(['/messages']);\n    }\n  }\n\n  @HostListener('scroll', ['$event.target'])\n  onScroll(target: HTMLElement): void {\n    if (!target) return;\n\n    const scrollPosition = target.scrollTop;\n    const scrollHeight = target.scrollHeight;\n    const clientHeight = target.clientHeight;\n\n    // Si on est proche du bas (à 200px du bas)\n    if (scrollHeight - scrollPosition - clientHeight < 200) {\n      this.scrollPosition$.next(scrollPosition);\n    }\n  }\n  ngOnInit(): void {\n    // Charger la préférence de son depuis le localStorage\n    const savedMutePreference = localStorage.getItem('notificationSoundMuted');\n    if (savedMutePreference !== null) {\n      this.isSoundMuted = savedMutePreference === 'true';\n      this.messageService.setMuted(this.isSoundMuted);\n    }\n\n    this.loadNotifications();\n    this.setupSubscriptions();\n    this.setupInfiniteScroll();\n    this.filterDeletedNotifications();\n  }\n\n  /**\n   * Filtre les notifications supprimées lors du chargement initial\n   */\n  private filterDeletedNotifications(): void {\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n\n    if (deletedNotificationIds.size > 0) {\n      this.notifications$.pipe(take(1)).subscribe((notifications) => {\n        const filteredNotifications = notifications.filter(\n          (notification) => !deletedNotificationIds.has(notification.id)\n        );\n\n        (this.messageService as any).notifications.next(filteredNotifications);\n        const unreadCount = filteredNotifications.filter(\n          (n) => !n.isRead\n        ).length;\n        (this.messageService as any).notificationCount.next(unreadCount);\n        this.updateNotificationCache(filteredNotifications);\n      });\n    }\n  }\n\n  setupInfiniteScroll(): void {\n    // Configurer le chargement des anciennes notifications lors du défilement\n    this.scrollPosition$\n      .pipe(\n        takeUntil(this.destroy$),\n        debounceTime(200), // Attendre 200ms après le dernier événement de défilement\n        distinctUntilChanged(), // Ne déclencher que si la position de défilement a changé\n        filter(() => !this.loadingMore && this.hasMoreNotifications) // Ne charger que s'il y a plus de notifications et qu'on n'est pas déjà en train de charger\n      )\n      .subscribe(() => {\n        this.loadMoreNotifications();\n      });\n  }\n  loadNotifications(): void {\n    this.loading = true;\n    this.loadingMore = false;\n    this.error = null;\n    this.hasMoreNotifications = true;\n\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n\n    this.messageService\n      .getNotifications(true)\n      .pipe(\n        takeUntil(this.destroy$),\n        map((notifications) => {\n          if (deletedNotificationIds.size > 0) {\n            return notifications.filter(\n              (notification) => !deletedNotificationIds.has(notification.id)\n            );\n          }\n          return notifications;\n        })\n      )\n      .subscribe({\n        next: (notifications) => {\n          (this.messageService as any).notifications.next(notifications);\n          const unreadCount = notifications.filter((n) => !n.isRead).length;\n          (this.messageService as any).notificationCount.next(unreadCount);\n          this.loading = false;\n          this.hasMoreNotifications =\n            this.messageService.hasMoreNotifications();\n        },\n        error: (err: Error) => {\n          this.error = err;\n          this.loading = false;\n          this.hasMoreNotifications = false;\n        },\n      });\n  }\n\n  loadMoreNotifications(): void {\n    if (this.loadingMore || !this.hasMoreNotifications) return;\n\n    this.loadingMore = true;\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n\n    this.messageService\n      .loadMoreNotifications()\n      .pipe(\n        takeUntil(this.destroy$),\n        map((notifications) => {\n          if (deletedNotificationIds.size > 0) {\n            return notifications.filter(\n              (notification) => !deletedNotificationIds.has(notification.id)\n            );\n          }\n          return notifications;\n        })\n      )\n      .subscribe({\n        next: (notifications) => {\n          this.notifications$\n            .pipe(take(1))\n            .subscribe((existingNotifications) => {\n              const allNotifications = [\n                ...existingNotifications,\n                ...notifications,\n              ];\n              (this.messageService as any).notifications.next(allNotifications);\n              const unreadCount = allNotifications.filter(\n                (n) => !n.isRead\n              ).length;\n              (this.messageService as any).notificationCount.next(unreadCount);\n              this.updateNotificationCache(allNotifications);\n            });\n\n          this.loadingMore = false;\n          this.hasMoreNotifications =\n            this.messageService.hasMoreNotifications();\n        },\n        error: (err: Error) => {\n          this.loadingMore = false;\n          this.hasMoreNotifications = false;\n        },\n      });\n  }\n  setupSubscriptions(): void {\n    this.messageService\n      .subscribeToNewNotifications()\n      .pipe(\n        takeUntil(this.destroy$),\n        catchError((error) => {\n          console.log('Notification stream error:', error);\n          return of(null);\n        })\n      )\n      .subscribe();\n    this.messageService\n      .subscribeToNotificationsRead()\n      .pipe(\n        takeUntil(this.destroy$),\n        catchError((error) => {\n          console.log('Notifications read stream error:', error);\n          return of(null);\n        })\n      )\n      .subscribe();\n  }\n  markAsRead(notificationId: string): void {\n    if (!notificationId) {\n      this.error = new Error('ID de notification invalide');\n      return;\n    }\n\n    this.notifications$.pipe(take(1)).subscribe((notifications) => {\n      const notification = notifications.find((n) => n.id === notificationId);\n      if (notification) {\n        if (notification.isRead) return;\n\n        const updatedNotifications = notifications.map((n) =>\n          n.id === notificationId\n            ? { ...n, isRead: true, readAt: new Date().toISOString() }\n            : n\n        );\n\n        this.updateUIWithNotifications(updatedNotifications);\n\n        this.messageService\n          .markAsRead([notificationId])\n          .pipe(takeUntil(this.destroy$))\n          .subscribe({\n            next: (result) => {\n              if (result && result.success) {\n                if (this.error && this.error.message.includes('mark')) {\n                  this.error = null;\n                }\n              }\n            },\n            error: (err) => {\n              const revertedNotifications = notifications.map((n) =>\n                n.id === notificationId\n                  ? { ...n, isRead: false, readAt: undefined }\n                  : n\n              );\n              (this.messageService as any).notifications.next(\n                revertedNotifications\n              );\n\n              const revertedUnreadCount = revertedNotifications.filter(\n                (n) => !n.isRead\n              ).length;\n              (this.messageService as any).notificationCount.next(\n                revertedUnreadCount\n              );\n            },\n          });\n      } else {\n        this.loadNotifications();\n      }\n    });\n  }\n\n  /**\n   * Met à jour l'interface utilisateur avec les nouvelles notifications\n   * @param notifications Notifications à afficher\n   */\n  private updateUIWithNotifications(notifications: any[]): void {\n    // Mettre à jour l'interface utilisateur immédiatement\n    (this.messageService as any).notifications.next(notifications);\n\n    // Mettre à jour le compteur de notifications non lues\n    const unreadCount = notifications.filter((n) => !n.isRead).length;\n    (this.messageService as any).notificationCount.next(unreadCount);\n\n    // Mettre à jour le cache de notifications dans le service\n    this.updateNotificationCache(notifications);\n  }\n\n  /**\n   * Met à jour le cache de notifications dans le service\n   * @param notifications Notifications à mettre à jour\n   */\n  private updateNotificationCache(notifications: any[]): void {\n    notifications.forEach((notification) => {\n      (this.messageService as any).updateNotificationCache?.(notification);\n    });\n  }\n\n  /**\n   * Réinitialise la sélection des notifications\n   */\n  private resetSelection(): void {\n    this.selectedNotifications.clear();\n    this.allSelected = false;\n    this.showSelectionBar = false;\n  }\n\n  markAllAsRead(): void {\n    this.notifications$.pipe(take(1)).subscribe((notifications) => {\n      const unreadIds = notifications.filter((n) => !n.isRead).map((n) => n.id);\n\n      if (unreadIds.length === 0) return;\n\n      const validIds = unreadIds.filter(\n        (id) => id && typeof id === 'string' && id.trim() !== ''\n      );\n\n      if (validIds.length !== unreadIds.length) {\n        this.error = new Error('Invalid notification IDs');\n        return;\n      }\n\n      const updatedNotifications = notifications.map((n) =>\n        validIds.includes(n.id)\n          ? { ...n, isRead: true, readAt: new Date().toISOString() }\n          : n\n      );\n\n      this.updateUIWithNotifications(updatedNotifications);\n\n      this.messageService\n        .markAsRead(validIds)\n        .pipe(takeUntil(this.destroy$))\n        .subscribe({\n          next: (result) => {\n            if (result && result.success) {\n              if (this.error && this.error.message.includes('mark')) {\n                this.error = null;\n              }\n            }\n          },\n          error: (err) => {\n            // Ne pas définir d'erreur pour éviter de perturber l'interface utilisateur\n          },\n        });\n    });\n  }\n\n  hasNotifications(): Observable<boolean> {\n    return this.notifications$.pipe(\n      map((notifications) => notifications?.length > 0)\n    );\n  }\n  hasUnreadNotifications(): Observable<boolean> {\n    return this.unreadCount$.pipe(map((count) => count > 0));\n  }\n\n  /**\n   * Active/désactive le filtre pour n'afficher que les notifications non lues\n   */\n  toggleUnreadFilter(): void {\n    this.showOnlyUnread = !this.showOnlyUnread;\n\n    if (this.showOnlyUnread) {\n      this.filteredNotifications$ =\n        this.messageService.getUnreadNotifications();\n    } else {\n      this.filteredNotifications$ = this.notifications$;\n    }\n  }\n\n  /**\n   * Active/désactive le son des notifications\n   */\n  toggleSound(): void {\n    this.isSoundMuted = !this.isSoundMuted;\n    this.messageService.setMuted(this.isSoundMuted);\n\n    if (!this.isSoundMuted) {\n      setTimeout(() => {\n        this.messageService.playNotificationSound();\n        setTimeout(() => {\n          this.messageService.playNotificationSound();\n        }, 1000);\n      }, 100);\n    }\n\n    localStorage.setItem(\n      'notificationSoundMuted',\n      this.isSoundMuted.toString()\n    );\n  }\n\n  /**\n   * Récupère les pièces jointes d'une notification et ouvre le modal\n   * @param notificationId ID de la notification\n   */\n  getNotificationAttachments(notificationId: string): void {\n    if (!notificationId) return;\n\n    this.currentAttachments = [];\n    this.loadingAttachments = true;\n    this.showAttachmentsModal = true;\n\n    let notification: Notification | undefined;\n\n    this.notifications$.pipe(take(1)).subscribe((notifications) => {\n      notification = notifications.find(\n        (n: Notification) => n.id === notificationId\n      );\n    });\n\n    if (\n      notification &&\n      notification.message &&\n      notification.message.attachments &&\n      notification.message.attachments.length > 0\n    ) {\n      this.loadingAttachments = false;\n      this.currentAttachments = notification.message.attachments.map(\n        (attachment: NotificationAttachment) =>\n          ({\n            id: '',\n            url: attachment.url || '',\n            type: this.convertAttachmentTypeToMessageType(attachment.type),\n            name: attachment.name || '',\n            size: attachment.size || 0,\n            duration: 0,\n          } as Attachment)\n      );\n      return;\n    }\n\n    this.messageService\n      .getNotificationAttachments(notificationId)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (attachments) => {\n          this.loadingAttachments = false;\n          this.currentAttachments = attachments;\n        },\n        error: (err) => {\n          this.loadingAttachments = false;\n        },\n      });\n  }\n\n  /**\n   * Ferme le modal des pièces jointes\n   */\n  closeAttachmentsModal(): void {\n    this.showAttachmentsModal = false;\n  }\n\n  /**\n   * Ouvre le modal des détails de notification\n   * @param notification Notification à afficher\n   */\n  openNotificationDetails(notification: Notification): void {\n    this.currentNotification = notification;\n    this.showNotificationDetailsModal = true;\n\n    if (notification.message?.attachments?.length) {\n      this.getNotificationAttachmentsForModal(notification.id);\n    }\n  }\n\n  /**\n   * Ferme le modal des détails de notification\n   */\n  closeNotificationDetailsModal(): void {\n    this.showNotificationDetailsModal = false;\n    this.currentNotification = null;\n    this.currentAttachments = [];\n  }\n\n  /**\n   * Récupère les pièces jointes d'une notification pour le modal de détails\n   */\n  private getNotificationAttachmentsForModal(notificationId: string): void {\n    this.currentAttachments = [];\n\n    if (this.currentNotification?.message?.attachments?.length) {\n      this.currentAttachments =\n        this.currentNotification.message.attachments.map(\n          (attachment: NotificationAttachment) =>\n            ({\n              id: '',\n              url: attachment.url || '',\n              type: this.convertAttachmentTypeToMessageType(attachment.type),\n              name: attachment.name || '',\n              size: attachment.size || 0,\n              duration: 0,\n            } as Attachment)\n        );\n    }\n  }\n\n  /**\n   * Convertit AttachmentType en MessageType\n   */\n  private convertAttachmentTypeToMessageType(\n    type: AttachmentType\n  ): MessageType {\n    switch (type) {\n      case 'IMAGE':\n        return MessageType.IMAGE;\n      case 'VIDEO':\n        return MessageType.VIDEO;\n      case 'AUDIO':\n        return MessageType.AUDIO;\n      case 'FILE':\n        return MessageType.FILE;\n      default:\n        return MessageType.FILE;\n    }\n  }\n\n  /**\n   * Vérifie si un type de fichier est une image\n   */\n  isImage(type: string): boolean {\n    return type?.startsWith('image/') || false;\n  }\n\n  /**\n   * Obtient l'icône FontAwesome correspondant au type de fichier\n   * @param type Type MIME du fichier\n   * @returns Classe CSS de l'icône\n   */\n  getFileIcon(type: string): string {\n    if (!type) return 'fas fa-file';\n\n    if (type.startsWith('image/')) return 'fas fa-file-image';\n    if (type.startsWith('video/')) return 'fas fa-file-video';\n    if (type.startsWith('audio/')) return 'fas fa-file-audio';\n    if (type.startsWith('text/')) return 'fas fa-file-alt';\n    if (type.includes('pdf')) return 'fas fa-file-pdf';\n    if (type.includes('word') || type.includes('document'))\n      return 'fas fa-file-word';\n    if (type.includes('excel') || type.includes('sheet'))\n      return 'fas fa-file-excel';\n    if (type.includes('powerpoint') || type.includes('presentation'))\n      return 'fas fa-file-powerpoint';\n    if (type.includes('zip') || type.includes('compressed'))\n      return 'fas fa-file-archive';\n\n    return 'fas fa-file';\n  }\n\n  /**\n   * Obtient le libellé du type de fichier\n   * @param type Type MIME du fichier\n   * @returns Libellé du type de fichier\n   */\n  getFileTypeLabel(type: string): string {\n    if (!type) return 'Fichier';\n\n    if (type.startsWith('image/')) return 'Image';\n    if (type.startsWith('video/')) return 'Vidéo';\n    if (type.startsWith('audio/')) return 'Audio';\n    if (type.startsWith('text/')) return 'Texte';\n    if (type.includes('pdf')) return 'PDF';\n    if (type.includes('word') || type.includes('document')) return 'Document';\n    if (type.includes('excel') || type.includes('sheet'))\n      return 'Feuille de calcul';\n    if (type.includes('powerpoint') || type.includes('presentation'))\n      return 'Présentation';\n    if (type.includes('zip') || type.includes('compressed')) return 'Archive';\n\n    return 'Fichier';\n  }\n\n  /**\n   * Formate la taille du fichier en unités lisibles\n   * @param size Taille en octets\n   * @returns Taille formatée (ex: \"1.5 MB\")\n   */\n  formatFileSize(size: number): string {\n    if (!size) return '';\n\n    const units = ['B', 'KB', 'MB', 'GB', 'TB'];\n    let i = 0;\n    let formattedSize = size;\n\n    while (formattedSize >= 1024 && i < units.length - 1) {\n      formattedSize /= 1024;\n      i++;\n    }\n\n    return `${formattedSize.toFixed(1)} ${units[i]}`;\n  }\n\n  /**\n   * Ouvre une pièce jointe dans un nouvel onglet\n   * @param url URL de la pièce jointe\n   */\n  openAttachment(url: string): void {\n    if (!url) return;\n    window.open(url, '_blank');\n  }\n\n  /**\n   * Télécharge une pièce jointe\n   * @param attachment Pièce jointe à télécharger\n   */\n  downloadAttachment(attachment: Attachment): void {\n    if (!attachment?.url) return;\n\n    const link = document.createElement('a');\n    link.href = attachment.url;\n    link.download = attachment.name || 'attachment';\n    link.target = '_blank';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  }\n\n  acceptFriendRequest(notification: Notification): void {\n    this.markAsRead(notification.id);\n  }\n\n  /**\n   * Supprime une notification et la stocke dans le localStorage\n   * @param notificationId ID de la notification à supprimer\n   */\n  deleteNotification(notificationId: string): void {\n    if (!notificationId) {\n      this.error = new Error('ID de notification invalide');\n      return;\n    }\n\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n    deletedNotificationIds.add(notificationId);\n    this.saveDeletedNotificationIds(deletedNotificationIds);\n\n    this.messageService\n      .deleteNotification(notificationId)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (result) => {\n          if (result && result.success) {\n            if (this.error && this.error.message.includes('suppression')) {\n              this.error = null;\n            }\n          }\n        },\n        error: (err) => {\n          this.error = err;\n        },\n      });\n  }\n\n  /**\n   * Supprime toutes les notifications et les stocke dans le localStorage\n   */\n  deleteAllNotifications(): void {\n    this.notifications$.pipe(take(1)).subscribe((notifications) => {\n      const deletedNotificationIds = this.getDeletedNotificationIds();\n\n      notifications.forEach((notification) => {\n        deletedNotificationIds.add(notification.id);\n      });\n\n      this.saveDeletedNotificationIds(deletedNotificationIds);\n\n      this.messageService\n        .deleteAllNotifications()\n        .pipe(takeUntil(this.destroy$))\n        .subscribe({\n          next: (result) => {\n            if (result && result.success) {\n              if (this.error && this.error.message.includes('suppression')) {\n                this.error = null;\n              }\n            }\n          },\n          error: (err) => {\n            this.error = err;\n          },\n        });\n    });\n  }\n\n  getErrorMessage(): string {\n    return this.error?.message || 'Unknown error occurred';\n  }\n\n  /**\n   * Récupère les IDs des notifications supprimées du localStorage\n   * @returns Set contenant les IDs des notifications supprimées\n   */\n  private getDeletedNotificationIds(): Set<string> {\n    try {\n      const deletedIdsJson = localStorage.getItem('deletedNotificationIds');\n      if (deletedIdsJson) {\n        return new Set<string>(JSON.parse(deletedIdsJson));\n      }\n      return new Set<string>();\n    } catch (error) {\n      return new Set<string>();\n    }\n  }\n\n  /**\n   * Sauvegarde les IDs des notifications supprimées dans le localStorage\n   * @param deletedIds Set contenant les IDs des notifications supprimées\n   */\n  private saveDeletedNotificationIds(deletedIds: Set<string>): void {\n    try {\n      localStorage.setItem(\n        'deletedNotificationIds',\n        JSON.stringify(Array.from(deletedIds))\n      );\n    } catch (error) {\n      // Ignore silently\n    }\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  /**\n   * Sélectionne ou désélectionne une notification\n   * @param notificationId ID de la notification\n   * @param event Événement de la case à cocher\n   */\n  toggleSelection(notificationId: string, event: Event): void {\n    event.stopPropagation(); // Empêcher la propagation de l'événement\n\n    if (this.selectedNotifications.has(notificationId)) {\n      this.selectedNotifications.delete(notificationId);\n    } else {\n      this.selectedNotifications.add(notificationId);\n    }\n\n    // Mettre à jour l'état de sélection globale\n    this.updateSelectionState();\n\n    // Afficher ou masquer la barre de sélection\n    this.showSelectionBar = this.selectedNotifications.size > 0;\n  }\n\n  /**\n   * Sélectionne ou désélectionne toutes les notifications\n   * @param event Événement de la case à cocher\n   */\n  toggleSelectAll(event: Event): void {\n    event.stopPropagation(); // Empêcher la propagation de l'événement\n\n    this.allSelected = !this.allSelected;\n\n    this.filteredNotifications$.pipe(take(1)).subscribe((notifications) => {\n      if (this.allSelected) {\n        // Sélectionner toutes les notifications\n        notifications.forEach((notification) => {\n          this.selectedNotifications.add(notification.id);\n        });\n      } else {\n        // Désélectionner toutes les notifications\n        this.selectedNotifications.clear();\n      }\n\n      // Afficher ou masquer la barre de sélection\n      this.showSelectionBar = this.selectedNotifications.size > 0;\n    });\n  }\n\n  /**\n   * Met à jour l'état de sélection globale\n   */\n  private updateSelectionState(): void {\n    this.filteredNotifications$.pipe(take(1)).subscribe((notifications) => {\n      this.allSelected =\n        notifications.length > 0 &&\n        this.selectedNotifications.size === notifications.length;\n    });\n  }\n\n  /**\n   * Supprime les notifications sélectionnées\n   */\n  deleteSelectedNotifications(): void {\n    if (this.selectedNotifications.size === 0) return;\n\n    const selectedIds = Array.from(this.selectedNotifications);\n\n    this.notifications$.pipe(take(1)).subscribe((notifications) => {\n      const updatedNotifications = notifications.filter(\n        (notification) => !this.selectedNotifications.has(notification.id)\n      );\n\n      this.updateUIWithNotifications(updatedNotifications);\n      this.resetSelection();\n    });\n\n    this.messageService\n      .deleteMultipleNotifications(selectedIds)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (result) => {\n          // Success handled silently\n        },\n        error: (err) => {\n          // Error handled silently\n        },\n      });\n  }\n\n  /**\n   * Marque les notifications sélectionnées comme lues\n   */\n  markSelectedAsRead(): void {\n    if (this.selectedNotifications.size === 0) return;\n\n    const selectedIds = Array.from(this.selectedNotifications);\n\n    this.notifications$.pipe(take(1)).subscribe((notifications) => {\n      const updatedNotifications = notifications.map((notification) =>\n        this.selectedNotifications.has(notification.id)\n          ? { ...notification, isRead: true, readAt: new Date().toISOString() }\n          : notification\n      );\n\n      this.updateUIWithNotifications(updatedNotifications);\n      this.resetSelection();\n    });\n\n    this.messageService\n      .markAsRead(selectedIds)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (result) => {\n          // Success handled silently\n        },\n        error: (err) => {\n          // Error handled silently\n        },\n      });\n  }\n\n  /**\n   * Vérifie si une notification est sélectionnée\n   * @param notificationId ID de la notification\n   * @returns true si la notification est sélectionnée, false sinon\n   */\n  isSelected(notificationId: string): boolean {\n    return this.selectedNotifications.has(notificationId);\n  }\n}\n", "<div\n  class=\"futuristic-notifications-container main-grid-container\"\n  [class.dark]=\"isDarkMode$ | async\"\n>\n  <!-- Background decorative elements -->\n  <div class=\"background-elements background-grid\">\n    <!-- Grid pattern and scan line will be added via CSS -->\n  </div>\n\n  <div class=\"futuristic-notifications-card content-card relative z-10\">\n    <div class=\"futuristic-notifications-header\">\n      <h2 class=\"futuristic-title\">\n        <i class=\"fas fa-bell mr-2\"></i>\n        Notifications\n      </h2>\n\n      <!-- Barre d'actions normale -->\n      <div class=\"flex space-x-2\" *ngIf=\"!showSelectionBar\">\n        <!-- Bouton de rafraîchissement -->\n        <button\n          (click)=\"loadNotifications()\"\n          class=\"futuristic-action-button\"\n          title=\"Rafraîchir\"\n        >\n          <i class=\"fas fa-sync-alt\"></i>\n        </button>\n\n        <!-- Case à cocher \"Tout sélectionner\" (déplacée après le bouton de rafraîchissement) -->\n        <div *ngIf=\"hasNotifications() | async\" class=\"select-all-checkbox\">\n          <label class=\"futuristic-checkbox\">\n            <input\n              type=\"checkbox\"\n              [checked]=\"allSelected\"\n              (click)=\"toggleSelectAll($event)\"\n            />\n            <span class=\"checkmark\"></span>\n          </label>\n        </div>\n\n        <!-- Bouton de filtrage des notifications non lues -->\n        <button\n          (click)=\"toggleUnreadFilter()\"\n          class=\"futuristic-action-button\"\n          [class.active]=\"showOnlyUnread\"\n          title=\"Filtrer les non lues\"\n        >\n          <i class=\"fas fa-filter\"></i>\n        </button>\n\n        <!-- Bouton pour activer/désactiver le son -->\n        <button\n          (click)=\"toggleSound()\"\n          class=\"futuristic-action-button\"\n          [class.active]=\"!isSoundMuted\"\n          title=\"{{ isSoundMuted ? 'Activer le son' : 'Désactiver le son' }}\"\n        >\n          <i\n            class=\"fas\"\n            [ngClass]=\"isSoundMuted ? 'fa-volume-mute' : 'fa-volume-up'\"\n          ></i>\n        </button>\n\n        <!-- Bouton pour marquer toutes les notifications comme lues -->\n        <button\n          *ngIf=\"(unreadCount$ | async) || 0\"\n          (click)=\"markAllAsRead()\"\n          class=\"futuristic-primary-button\"\n        >\n          <i class=\"fas fa-check-double mr-1\"></i> Tout marquer comme lu\n        </button>\n\n        <!-- Bouton pour supprimer toutes les notifications -->\n        <button\n          *ngIf=\"hasNotifications() | async\"\n          (click)=\"deleteAllNotifications()\"\n          class=\"futuristic-danger-button\"\n          title=\"Supprimer toutes les notifications\"\n        >\n          <i class=\"fas fa-trash-alt mr-1\"></i> Tout supprimer\n        </button>\n      </div>\n\n      <!-- Barre d'actions pour les notifications sélectionnées -->\n      <div class=\"flex space-x-2 selection-actions\" *ngIf=\"showSelectionBar\">\n        <span class=\"selection-count\"\n          >{{ selectedNotifications.size }} sélectionné(s)</span\n        >\n\n        <!-- Bouton pour marquer les notifications sélectionnées comme lues -->\n        <button\n          (click)=\"markSelectedAsRead()\"\n          class=\"futuristic-primary-button\"\n        >\n          <i class=\"fas fa-check mr-1\"></i> Marquer comme lu\n        </button>\n\n        <!-- Bouton pour supprimer les notifications sélectionnées -->\n        <button\n          (click)=\"deleteSelectedNotifications()\"\n          class=\"futuristic-danger-button\"\n        >\n          <i class=\"fas fa-trash-alt mr-1\"></i> Supprimer\n        </button>\n\n        <!-- Bouton pour annuler la sélection -->\n        <button\n          (click)=\"\n            selectedNotifications.clear();\n            showSelectionBar = false;\n            allSelected = false\n          \"\n          class=\"futuristic-cancel-button\"\n        >\n          <i class=\"fas fa-times mr-1\"></i> Annuler\n        </button>\n      </div>\n    </div>\n\n    <!-- État de chargement futuriste -->\n    <div *ngIf=\"loading\" class=\"futuristic-loading-container\">\n      <div class=\"futuristic-loading-circle\"></div>\n      <p class=\"futuristic-loading-text\">Chargement des notifications...</p>\n    </div>\n\n    <!-- État d'erreur futuriste -->\n    <div *ngIf=\"error\" class=\"futuristic-error-message\">\n      <div class=\"flex items-center\">\n        <i class=\"fas fa-exclamation-triangle futuristic-error-icon\"></i>\n        <div>\n          <h3 class=\"futuristic-error-title\">Erreur de chargement</h3>\n          <p class=\"futuristic-error-text\">{{ getErrorMessage() }}</p>\n        </div>\n        <button\n          (click)=\"loadNotifications()\"\n          class=\"futuristic-retry-button ml-auto\"\n        >\n          Réessayer\n        </button>\n      </div>\n    </div>\n\n    <!-- État vide futuriste -->\n    <div\n      *ngIf=\"!loading && !(hasNotifications() | async)\"\n      class=\"futuristic-empty-state\"\n    >\n      <div class=\"futuristic-empty-icon\">\n        <i class=\"fas fa-bell-slash\"></i>\n      </div>\n      <h3 class=\"futuristic-empty-title\">Aucune notification</h3>\n      <p class=\"futuristic-empty-text\">Vous êtes à jour !</p>\n      <button (click)=\"loadNotifications()\" class=\"futuristic-check-button\">\n        Vérifier les nouvelles notifications\n      </button>\n    </div>\n\n    <!-- Liste des notifications futuriste -->\n    <div\n      *ngIf=\"!loading && (hasNotifications() | async)\"\n      class=\"futuristic-notifications-list\"\n      #notificationContainer\n      (scroll)=\"onScroll(notificationContainer)\"\n    >\n      <ng-container *ngFor=\"let notification of filteredNotifications$ | async\">\n        <div\n          [class.futuristic-notification-unread]=\"!notification.isRead\"\n          [class.futuristic-notification-read]=\"notification.isRead\"\n          [class.futuristic-notification-selected]=\"isSelected(notification.id)\"\n          class=\"futuristic-notification-card\"\n        >\n          <!-- Case à cocher pour la sélection (déplacée en haut à gauche) -->\n          <div class=\"notification-checkbox\">\n            <label class=\"futuristic-checkbox\">\n              <input\n                type=\"checkbox\"\n                [checked]=\"isSelected(notification.id)\"\n                (click)=\"toggleSelection(notification.id, $event)\"\n              />\n              <span class=\"checkmark\"></span>\n            </label>\n          </div>\n\n          <!-- Avatar de l'expéditeur simplifié -->\n          <div class=\"notification-avatar\">\n            <img\n              [src]=\"\n                notification.senderId?.image ||\n                'assets/images/default-avatar.png'\n              \"\n              alt=\"Avatar\"\n              onerror=\"this.src='assets/images/default-avatar.png'\"\n            />\n          </div>\n\n          <!-- Contenu principal de la notification -->\n          <div class=\"notification-main-content\">\n            <!-- Contenu de notification simplifié -->\n            <div class=\"notification-content\">\n              <div class=\"notification-header\">\n                <div class=\"notification-header-top\">\n                  <span class=\"notification-sender\">{{\n                    notification.senderId?.username || \"Système\"\n                  }}</span>\n\n                  <!-- Heure de la notification (placée à droite du nom d'utilisateur) -->\n                  <div class=\"notification-time\">\n                    {{ notification.timestamp | date : \"shortTime\" }}\n                  </div>\n                </div>\n              </div>\n\n              <!-- Contenu du message (déplacé après l'en-tête) -->\n              <div class=\"notification-text-container\">\n                <span class=\"notification-text\">{{\n                  notification.content\n                }}</span>\n              </div>\n\n              <!-- Aperçu du message simplifié -->\n              <div\n                *ngIf=\"notification.message?.content\"\n                class=\"notification-message-preview\"\n              >\n                {{ notification.message?.content }}\n              </div>\n\n              <!-- Indicateur de pièces jointes -->\n              <div\n                *ngIf=\"notification.message?.attachments?.length\"\n                class=\"notification-attachments-indicator\"\n              >\n                <i class=\"fas fa-paperclip\"></i>\n                {{ notification.message?.attachments?.length }} pièce(s)\n                jointe(s)\n              </div>\n            </div>\n\n            <!-- Indicateur de non-lu (petit point bleu) -->\n            <div *ngIf=\"!notification.isRead\" class=\"unread-indicator\"></div>\n          </div>\n\n          <!-- Actions de notification -->\n          <div class=\"notification-actions\">\n            <!-- Bouton pour afficher les pièces jointes -->\n            <button\n              *ngIf=\"notification.message?.attachments?.length\"\n              (click)=\"\n                getNotificationAttachments(notification.id);\n                $event.stopPropagation()\n              \"\n              class=\"notification-action-button notification-attachment-button\"\n              title=\"Voir les pièces jointes\"\n            >\n              <i class=\"fas fa-paperclip\"></i>\n            </button>\n\n            <!-- Bouton pour rejoindre la conversation -->\n            <button\n              *ngIf=\"\n                notification.type === 'NEW_MESSAGE' ||\n                notification.type === 'GROUP_INVITE' ||\n                notification.type === 'MESSAGE_REACTION'\n              \"\n              (click)=\"joinConversation(notification); $event.stopPropagation()\"\n              class=\"notification-action-button notification-join-button\"\n              title=\"Rejoindre la conversation\"\n              [disabled]=\"loading\"\n            >\n              <i class=\"fas fa-comments\" *ngIf=\"!loading\"></i>\n              <i class=\"fas fa-spinner fa-spin\" *ngIf=\"loading\"></i>\n            </button>\n\n            <!-- Bouton pour voir les détails de la notification -->\n            <button\n              (click)=\"\n                openNotificationDetails(notification); $event.stopPropagation()\n              \"\n              class=\"notification-action-button notification-details-button\"\n              title=\"Voir les détails (ne marque PAS comme lu automatiquement)\"\n            >\n              <i class=\"fas fa-info-circle\"></i>\n            </button>\n\n            <!-- Bouton marquer comme lu -->\n            <button\n              *ngIf=\"!notification.isRead\"\n              (click)=\"markAsRead(notification.id); $event.stopPropagation()\"\n              class=\"notification-action-button notification-read-button\"\n              title=\"Marquer cette notification comme lue\"\n            >\n              <i class=\"fas fa-check\"></i>\n            </button>\n\n            <!-- Bouton pour supprimer la notification -->\n            <button\n              (click)=\"\n                deleteNotification(notification.id); $event.stopPropagation()\n              \"\n              class=\"notification-action-button notification-delete-button\"\n              title=\"Supprimer cette notification\"\n            >\n              <i class=\"fas fa-trash-alt\"></i>\n            </button>\n          </div>\n        </div>\n      </ng-container>\n\n      <!-- Indicateur de chargement des anciennes notifications -->\n      <div *ngIf=\"loadingMore\" class=\"futuristic-loading-more\">\n        <div class=\"futuristic-loading-circle-small\"></div>\n        <p class=\"futuristic-loading-text-small\">\n          Chargement des notifications plus anciennes...\n        </p>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Modal pour afficher les pièces jointes -->\n<div\n  class=\"futuristic-modal-overlay\"\n  [style.display]=\"showAttachmentsModal ? 'flex' : 'none'\"\n  (click)=\"closeAttachmentsModal()\"\n>\n  <div class=\"futuristic-modal-container\" (click)=\"$event.stopPropagation()\">\n    <div class=\"futuristic-modal-header\">\n      <h3 class=\"futuristic-modal-title\">\n        <i class=\"fas fa-paperclip mr-2\"></i>\n        Pièces jointes\n      </h3>\n      <button class=\"futuristic-modal-close\" (click)=\"closeAttachmentsModal()\">\n        <i class=\"fas fa-times\"></i>\n      </button>\n    </div>\n    <div class=\"futuristic-modal-body\">\n      <div *ngIf=\"loadingAttachments\" class=\"futuristic-loading-container\">\n        <div class=\"futuristic-loading-circle\"></div>\n        <p class=\"futuristic-loading-text\">Chargement des pièces jointes...</p>\n      </div>\n\n      <div\n        *ngIf=\"!loadingAttachments && currentAttachments.length === 0\"\n        class=\"futuristic-empty-state\"\n      >\n        <div class=\"futuristic-empty-icon\">\n          <i class=\"fas fa-file-alt\"></i>\n        </div>\n        <h3 class=\"futuristic-empty-title\">Aucune pièce jointe</h3>\n        <p class=\"futuristic-empty-text\">\n          Aucune pièce jointe n'a été trouvée pour cette notification.\n        </p>\n      </div>\n\n      <div\n        *ngIf=\"!loadingAttachments && currentAttachments.length > 0\"\n        class=\"futuristic-attachments-list\"\n      >\n        <div\n          *ngFor=\"let attachment of currentAttachments\"\n          class=\"futuristic-attachment-item\"\n        >\n          <!-- Image -->\n          <div\n            *ngIf=\"isImage(attachment.type)\"\n            class=\"futuristic-attachment-preview\"\n          >\n            <img\n              [src]=\"attachment.url\"\n              alt=\"Image\"\n              (click)=\"openAttachment(attachment.url)\"\n            />\n          </div>\n\n          <!-- Document -->\n          <div\n            *ngIf=\"!isImage(attachment.type)\"\n            class=\"futuristic-attachment-icon\"\n          >\n            <i [class]=\"getFileIcon(attachment.type)\"></i>\n          </div>\n\n          <div class=\"futuristic-attachment-info\">\n            <div class=\"futuristic-attachment-name\">\n              {{ attachment.name || \"Pièce jointe\" }}\n            </div>\n            <div class=\"futuristic-attachment-meta\">\n              <span class=\"futuristic-attachment-type\">{{\n                getFileTypeLabel(attachment.type)\n              }}</span>\n              <span\n                *ngIf=\"attachment.size\"\n                class=\"futuristic-attachment-size\"\n                >{{ formatFileSize(attachment.size) }}</span\n              >\n            </div>\n          </div>\n\n          <div class=\"futuristic-attachment-actions\">\n            <button\n              class=\"futuristic-attachment-button\"\n              (click)=\"openAttachment(attachment.url)\"\n              title=\"Ouvrir\"\n            >\n              <i class=\"fas fa-external-link-alt\"></i>\n            </button>\n            <button\n              class=\"futuristic-attachment-button\"\n              (click)=\"downloadAttachment(attachment)\"\n              title=\"Télécharger\"\n            >\n              <i class=\"fas fa-download\"></i>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Modal pour afficher les détails de notification -->\n<div\n  class=\"futuristic-modal-overlay\"\n  [style.display]=\"showNotificationDetailsModal ? 'flex' : 'none'\"\n  (click)=\"closeNotificationDetailsModal()\"\n>\n  <div class=\"futuristic-modal-container\" (click)=\"$event.stopPropagation()\">\n    <div class=\"futuristic-modal-header\">\n      <h3 class=\"futuristic-modal-title\">\n        <i class=\"fas fa-info-circle mr-2\"></i>\n        Détails de la notification\n      </h3>\n      <button\n        class=\"futuristic-modal-close\"\n        (click)=\"closeNotificationDetailsModal()\"\n      >\n        <i class=\"fas fa-times\"></i>\n      </button>\n    </div>\n    <div class=\"futuristic-modal-body\" *ngIf=\"currentNotification\">\n      <!-- Informations de l'expéditeur -->\n      <div class=\"notification-detail-section\">\n        <h4 class=\"notification-detail-title\">\n          <i class=\"fas fa-user mr-2\"></i>\n          Expéditeur\n        </h4>\n        <div class=\"notification-sender-info\">\n          <img\n            [src]=\"\n              currentNotification.senderId?.image ||\n              'assets/images/default-avatar.png'\n            \"\n            alt=\"Avatar\"\n            class=\"notification-sender-avatar\"\n            onerror=\"this.src='assets/images/default-avatar.png'\"\n          />\n          <div class=\"notification-sender-details\">\n            <span class=\"notification-sender-name\">\n              {{ currentNotification.senderId?.username || \"Système\" }}\n            </span>\n            <span class=\"notification-timestamp\">\n              {{ currentNotification.timestamp | date : \"medium\" }}\n            </span>\n          </div>\n        </div>\n      </div>\n\n      <!-- Contenu de la notification -->\n      <div class=\"notification-detail-section\">\n        <h4 class=\"notification-detail-title\">\n          <i class=\"fas fa-message mr-2\"></i>\n          Message\n        </h4>\n        <div class=\"notification-content-detail\">\n          {{ currentNotification.content }}\n        </div>\n        <div\n          *ngIf=\"currentNotification.message?.content\"\n          class=\"notification-message-detail\"\n        >\n          <strong>Message original :</strong>\n          {{ currentNotification.message?.content }}\n        </div>\n      </div>\n\n      <!-- Type et statut -->\n      <div class=\"notification-detail-section\">\n        <h4 class=\"notification-detail-title\">\n          <i class=\"fas fa-tag mr-2\"></i>\n          Informations\n        </h4>\n        <div class=\"notification-info-grid\">\n          <div class=\"notification-info-item\">\n            <span class=\"notification-info-label\">Type :</span>\n            <span class=\"notification-info-value\">{{\n              currentNotification.type\n            }}</span>\n          </div>\n          <div class=\"notification-info-item\">\n            <span class=\"notification-info-label\">Statut :</span>\n            <span\n              class=\"notification-info-value\"\n              [class.text-green-500]=\"currentNotification.isRead\"\n              [class.text-orange-500]=\"!currentNotification.isRead\"\n            >\n              {{ currentNotification.isRead ? \"Lu\" : \"Non lu\" }}\n            </span>\n          </div>\n          <div\n            class=\"notification-info-item\"\n            *ngIf=\"currentNotification.readAt\"\n          >\n            <span class=\"notification-info-label\">Lu le :</span>\n            <span class=\"notification-info-value\">{{\n              currentNotification.readAt | date : \"medium\"\n            }}</span>\n          </div>\n          <div\n            class=\"notification-info-item\"\n            *ngIf=\"!currentNotification.isRead\"\n            style=\"\n              background: rgba(255, 140, 0, 0.1);\n              border: 1px solid rgba(255, 140, 0, 0.3);\n            \"\n          >\n            <span class=\"notification-info-label\">\n              <i class=\"fas fa-info-circle mr-1\"></i>\n              Note :\n            </span>\n            <span\n              class=\"notification-info-value\"\n              style=\"color: #ff8c00; font-style: italic\"\n            >\n              Ouvrir les détails ne marque pas automatiquement comme lu\n            </span>\n          </div>\n        </div>\n      </div>\n\n      <!-- Pièces jointes -->\n      <div\n        class=\"notification-detail-section\"\n        *ngIf=\"currentAttachments.length > 0\"\n      >\n        <h4 class=\"notification-detail-title\">\n          <i class=\"fas fa-paperclip mr-2\"></i>\n          Pièces jointes ({{ currentAttachments.length }})\n        </h4>\n        <div class=\"notification-attachments-grid\">\n          <div\n            *ngFor=\"let attachment of currentAttachments\"\n            class=\"notification-attachment-item\"\n          >\n            <!-- Image -->\n            <div\n              *ngIf=\"isImage(attachment.type)\"\n              class=\"notification-attachment-preview\"\n            >\n              <img\n                [src]=\"attachment.url\"\n                alt=\"Image\"\n                (click)=\"openAttachment(attachment.url)\"\n              />\n            </div>\n\n            <!-- Document -->\n            <div\n              *ngIf=\"!isImage(attachment.type)\"\n              class=\"notification-attachment-icon\"\n            >\n              <i [class]=\"getFileIcon(attachment.type)\"></i>\n            </div>\n\n            <div class=\"notification-attachment-info\">\n              <div class=\"notification-attachment-name\">\n                {{ attachment.name || \"Pièce jointe\" }}\n              </div>\n              <div class=\"notification-attachment-meta\">\n                <span class=\"notification-attachment-type\">{{\n                  getFileTypeLabel(attachment.type)\n                }}</span>\n                <span\n                  *ngIf=\"attachment.size\"\n                  class=\"notification-attachment-size\"\n                  >{{ formatFileSize(attachment.size) }}</span\n                >\n              </div>\n            </div>\n\n            <div class=\"notification-attachment-actions\">\n              <button\n                class=\"notification-attachment-button\"\n                (click)=\"openAttachment(attachment.url)\"\n                title=\"Ouvrir\"\n              >\n                <i class=\"fas fa-external-link-alt\"></i>\n              </button>\n              <button\n                class=\"notification-attachment-button\"\n                (click)=\"downloadAttachment(attachment)\"\n                title=\"Télécharger\"\n              >\n                <i class=\"fas fa-download\"></i>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Actions -->\n      <div class=\"notification-detail-actions\">\n        <button\n          *ngIf=\"\n            currentNotification.type === 'NEW_MESSAGE' ||\n            currentNotification.type === 'GROUP_INVITE' ||\n            currentNotification.type === 'MESSAGE_REACTION'\n          \"\n          (click)=\"\n            joinConversation(currentNotification);\n            closeNotificationDetailsModal()\n          \"\n          class=\"futuristic-primary-button\"\n          [disabled]=\"loading\"\n        >\n          <i class=\"fas fa-comments mr-2\" *ngIf=\"!loading\"></i>\n          <i class=\"fas fa-spinner fa-spin mr-2\" *ngIf=\"loading\"></i>\n          Rejoindre la conversation\n        </button>\n\n        <button\n          *ngIf=\"!currentNotification.isRead\"\n          (click)=\"markAsRead(currentNotification.id)\"\n          class=\"futuristic-secondary-button\"\n        >\n          <i class=\"fas fa-check mr-2\"></i>\n          Marquer comme lu\n        </button>\n\n        <button\n          (click)=\"\n            deleteNotification(currentNotification.id);\n            closeNotificationDetailsModal()\n          \"\n          class=\"futuristic-danger-button\"\n        >\n          <i class=\"fas fa-trash-alt mr-2\"></i>\n          Supprimer\n        </button>\n      </div>\n    </div>\n  </div>\n</div>\n", "import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { NotificationListComponent } from './notification-list/notification-list.component';\nimport { MessageLayoutComponent } from '../messages/message-layout/message-layout.component';\n\nconst routes: Routes = [\n  {\n    path: '',\n    component: MessageLayoutComponent,\n    data: { context: 'notifications' },\n    children: [\n      {\n        path: '',\n        component: NotificationListComponent,\n        data: { title: 'Notifications' },\n      },\n    ],\n  },\n];\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule]\n})\nexport class NotificationsRoutingModule { }\n", "import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { NotificationsRoutingModule } from './notifications-routing.module';\nimport { RouterModule } from '@angular/router';\nimport { NotificationListComponent } from './notification-list/notification-list.component';\nimport { MessageService } from 'src/app/services/message.service';\n\n@NgModule({\n  declarations: [NotificationListComponent],\n  imports: [CommonModule, NotificationsRoutingModule, RouterModule],\n  providers: [MessageService],\n})\nexport class NotificationsModule {}\n"], "names": ["Subject", "of", "BehaviorSubject", "MessageType", "catchError", "map", "takeUntil", "take", "debounceTime", "distinctUntilChanged", "filter", "i0", "ɵɵelementStart", "ɵɵlistener", "NotificationListComponent_div_8_div_3_Template_input_click_2_listener", "$event", "ɵɵrestoreView", "_r14", "ctx_r13", "ɵɵnextContext", "ɵɵresetView", "toggleSelectAll", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "ctx_r10", "allSelected", "NotificationListComponent_div_8_button_9_Template_button_click_0_listener", "_r16", "ctx_r15", "markAllAsRead", "ɵɵtext", "NotificationListComponent_div_8_button_11_Template_button_click_0_listener", "_r18", "ctx_r17", "deleteAllNotifications", "NotificationListComponent_div_8_Template_button_click_1_listener", "_r20", "ctx_r19", "loadNotifications", "ɵɵtemplate", "NotificationListComponent_div_8_div_3_Template", "NotificationListComponent_div_8_Template_button_click_5_listener", "ctx_r21", "toggleUn<PERSON><PERSON><PERSON>er", "NotificationListComponent_div_8_Template_button_click_7_listener", "ctx_r22", "toggleSound", "NotificationListComponent_div_8_button_9_Template", "NotificationListComponent_div_8_button_11_Template", "ɵɵpipeBind1", "ctx_r0", "hasNotifications", "ɵɵclassProp", "showOnlyUnread", "isSoundMuted", "ɵɵpropertyInterpolate", "unreadCount$", "NotificationListComponent_div_9_Template_button_click_3_listener", "_r24", "ctx_r23", "markSelectedAsRead", "NotificationListComponent_div_9_Template_button_click_6_listener", "ctx_r25", "deleteSelectedNotifications", "NotificationListComponent_div_9_Template_button_click_9_listener", "ctx_r26", "selectedNotifications", "clear", "showSelectionBar", "ɵɵtextInterpolate1", "ctx_r1", "size", "NotificationListComponent_div_11_Template_button_click_8_listener", "_r28", "ctx_r27", "ɵɵtextInterpolate", "ctx_r3", "getErrorMessage", "NotificationListComponent_div_12_Template_button_click_7_listener", "_r30", "ctx_r29", "notification_r34", "message", "content", "attachments", "length", "NotificationListComponent_div_14_ng_container_2_button_24_Template_button_click_0_listener", "_r45", "$implicit", "ctx_r43", "getNotificationAttachments", "id", "stopPropagation", "NotificationListComponent_div_14_ng_container_2_button_25_Template_button_click_0_listener", "_r50", "ctx_r48", "joinConversation", "NotificationListComponent_div_14_ng_container_2_button_25_i_1_Template", "NotificationListComponent_div_14_ng_container_2_button_25_i_2_Template", "ctx_r39", "loading", "NotificationListComponent_div_14_ng_container_2_button_28_Template_button_click_0_listener", "_r53", "ctx_r51", "mark<PERSON><PERSON><PERSON>", "ɵɵelementContainerStart", "NotificationListComponent_div_14_ng_container_2_Template_input_click_4_listener", "restoredCtx", "_r55", "ctx_r54", "toggleSelection", "NotificationListComponent_div_14_ng_container_2_div_20_Template", "NotificationListComponent_div_14_ng_container_2_div_21_Template", "NotificationListComponent_div_14_ng_container_2_div_22_Template", "NotificationListComponent_div_14_ng_container_2_button_24_Template", "NotificationListComponent_div_14_ng_container_2_button_25_Template", "NotificationListComponent_div_14_ng_container_2_Template_button_click_26_listener", "ctx_r56", "openNotificationDetails", "NotificationListComponent_div_14_ng_container_2_button_28_Template", "NotificationListComponent_div_14_ng_container_2_Template_button_click_29_listener", "ctx_r57", "deleteNotification", "ɵɵelementContainerEnd", "isRead", "ctx_r32", "isSelected", "senderId", "image", "ɵɵsanitizeUrl", "username", "ɵɵpipeBind2", "timestamp", "type", "NotificationListComponent_div_14_Template_div_scroll_0_listener", "_r59", "_r31", "ɵɵreference", "ctx_r58", "onScroll", "NotificationListComponent_div_14_ng_container_2_Template", "NotificationListComponent_div_14_div_4_Template", "ctx_r5", "filteredNotifications$", "loadingMore", "NotificationListComponent_div_27_div_1_div_1_Template_img_click_1_listener", "_r67", "attachment_r61", "ctx_r65", "openAttachment", "url", "ɵɵclassMap", "ctx_r63", "getFileIcon", "ctx_r64", "formatFileSize", "NotificationListComponent_div_27_div_1_div_1_Template", "NotificationListComponent_div_27_div_1_div_2_Template", "NotificationListComponent_div_27_div_1_span_9_Template", "NotificationListComponent_div_27_div_1_Template_button_click_11_listener", "_r72", "ctx_r71", "NotificationListComponent_div_27_div_1_Template_button_click_13_listener", "ctx_r73", "downloadAttachment", "ctx_r60", "isImage", "name", "getFileTypeLabel", "NotificationListComponent_div_27_div_1_Template", "ctx_r8", "currentAttachments", "ctx_r74", "currentNotification", "ctx_r75", "readAt", "NotificationListComponent_div_36_div_37_div_5_div_1_Template_img_click_1_listener", "_r87", "attachment_r81", "ctx_r85", "ctx_r83", "ctx_r84", "NotificationListComponent_div_36_div_37_div_5_div_1_Template", "NotificationListComponent_div_36_div_37_div_5_div_2_Template", "NotificationListComponent_div_36_div_37_div_5_span_9_Template", "NotificationListComponent_div_36_div_37_div_5_Template_button_click_11_listener", "_r92", "ctx_r91", "NotificationListComponent_div_36_div_37_div_5_Template_button_click_13_listener", "ctx_r93", "ctx_r80", "NotificationListComponent_div_36_div_37_div_5_Template", "ctx_r77", "NotificationListComponent_div_36_button_39_Template_button_click_0_listener", "_r97", "ctx_r96", "closeNotificationDetailsModal", "NotificationListComponent_div_36_button_39_i_1_Template", "NotificationListComponent_div_36_button_39_i_2_Template", "ctx_r78", "NotificationListComponent_div_36_button_40_Template_button_click_0_listener", "_r99", "ctx_r98", "NotificationListComponent_div_36_div_19_Template", "NotificationListComponent_div_36_div_35_Template", "NotificationListComponent_div_36_div_36_Template", "NotificationListComponent_div_36_div_37_Template", "NotificationListComponent_div_36_button_39_Template", "NotificationListComponent_div_36_button_40_Template", "NotificationListComponent_div_36_Template_button_click_41_listener", "_r101", "ctx_r100", "ctx_r9", "NotificationListComponent", "constructor", "messageService", "themeService", "router", "hasMoreNotifications", "error", "Set", "showAttachmentsModal", "loadingAttachments", "showNotificationDetailsModal", "destroy$", "scrollPosition$", "notifications$", "notificationCount$", "isDarkMode$", "currentTheme$", "pipe", "theme", "isMuted", "notification", "conversationId", "metadata", "relatedEntity", "includes", "groupId", "navigate", "getOrCreateConversation", "subscribe", "next", "conversation", "target", "scrollPosition", "scrollTop", "scrollHeight", "clientHeight", "ngOnInit", "savedMutePreference", "localStorage", "getItem", "setMuted", "setupSubscriptions", "setupInfiniteScroll", "filterDeletedNotifications", "deletedNotificationIds", "getDeletedNotificationIds", "notifications", "filteredNotifications", "has", "unreadCount", "n", "notificationCount", "updateNotificationCache", "loadMoreNotifications", "getNotifications", "err", "existingNotifications", "allNotifications", "subscribeToNewNotifications", "console", "log", "subscribeToNotificationsRead", "notificationId", "Error", "find", "updatedNotifications", "Date", "toISOString", "updateUIWithNotifications", "result", "success", "revertedNotifications", "undefined", "revertedUnreadCount", "for<PERSON>ach", "resetSelection", "unreadIds", "validIds", "trim", "hasUnreadNotifications", "count", "getUnreadNotifications", "setTimeout", "playNotificationSound", "setItem", "toString", "attachment", "convertAttachmentTypeToMessageType", "duration", "closeAttachmentsModal", "getNotificationAttachmentsForModal", "IMAGE", "VIDEO", "AUDIO", "FILE", "startsWith", "units", "i", "formattedSize", "toFixed", "window", "open", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "acceptFriendRequest", "add", "saveDeletedNotificationIds", "deletedIdsJson", "JSON", "parse", "deletedIds", "stringify", "Array", "from", "ngOnDestroy", "complete", "event", "delete", "updateSelectionState", "selectedIds", "deleteMultipleNotifications", "ɵɵdirectiveInject", "i1", "MessageService", "i2", "ThemeService", "i3", "Router", "selectors", "viewQuery", "NotificationListComponent_Query", "rf", "ctx", "NotificationListComponent_div_8_Template", "NotificationListComponent_div_9_Template", "NotificationListComponent_div_10_Template", "NotificationListComponent_div_11_Template", "NotificationListComponent_div_12_Template", "NotificationListComponent_div_14_Template", "NotificationListComponent_Template_div_click_16_listener", "NotificationListComponent_Template_div_click_17_listener", "NotificationListComponent_Template_button_click_22_listener", "NotificationListComponent_div_25_Template", "NotificationListComponent_div_26_Template", "NotificationListComponent_div_27_Template", "NotificationListComponent_Template_div_click_28_listener", "NotificationListComponent_Template_div_click_29_listener", "NotificationListComponent_Template_button_click_34_listener", "NotificationListComponent_div_36_Template", "ɵɵstyleProp", "RouterModule", "MessageLayoutComponent", "routes", "path", "component", "data", "context", "children", "title", "NotificationsRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "exports", "CommonModule", "NotificationsModule", "declarations"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}