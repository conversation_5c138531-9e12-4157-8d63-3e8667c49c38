import {
  <PERSON>mponent,
  <PERSON><PERSON>nit,
  <PERSON><PERSON><PERSON>roy,
  ChangeDetectorRef,
  ViewChild,
  ElementRef,
} from '@angular/core';
import { Subscription, BehaviorSubject } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { MessageService } from '../../../../services/message.service';
import { AuthService } from '../../../../services/auth.service';
import { ToastService } from '../../../../services/toast.service';
import { ThemeService, Theme } from '../../../../services/theme.service';
import { MockDataService } from '../../../../services/mock-data.service';
import {
  User,
  Conversation,
  Message,
  Notification,
} from '../../../../models/message.model';

@Component({
  selector: 'app-message-layout',
  templateUrl: './message-layout.component.html',
  styleUrls: ['./message-layout.component.css'],
})
export class MessageLayoutComponent implements OnInit, OnDestroy {
  @ViewChild('searchInput') searchInput!: ElementRef;

  // État du composant
  currentUser: User | null = null;
  conversations: Conversation[] = [];
  users: User[] = [];
  notifications: Notification[] = [];

  // Navigation et UI
  activeTab: 'conversations' | 'users' | 'notifications' = 'conversations';
  selectedConversationId: string | null = null;
  isMobileMenuOpen = false;
  isSearching = false;

  // Thème
  currentTheme: Theme | null = null;
  availableThemes: Theme[] = [];
  showThemeSelector = false;

  // Recherche
  searchQuery = '';
  searchResults: (Conversation | User)[] = [];

  // États de chargement
  isLoadingConversations = false;
  isLoadingUsers = false;
  isLoadingNotifications = false;

  // Pagination
  conversationsPage = 1;
  usersPage = 1;
  hasMoreConversations = true;
  hasMoreUsers = true;

  // Subscriptions
  private subscriptions: Subscription[] = [];

  // Observables
  private searchQuery$ = new BehaviorSubject<string>('');

  constructor(
    private messageService: MessageService,
    private authService: AuthService,
    private toastService: ToastService,
    private themeService: ThemeService,
    private mockDataService: MockDataService,
    private route: ActivatedRoute,
    private router: Router,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.initializeComponent();
    this.setupSubscriptions();
    this.loadInitialData();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }

  // ============================================================================
  // MÉTHODES D'INITIALISATION
  // ============================================================================

  private initializeComponent(): void {
    // Récupérer l'utilisateur actuel
    this.currentUser = this.authService.getCurrentUser();

    if (!this.currentUser) {
      this.router.navigate(['/login']);
      return;
    }

    // Initialiser les thèmes
    this.currentTheme = this.themeService.getCurrentTheme();
    this.availableThemes = this.themeService.getAvailableThemes();

    // Écouter les changements de route
    this.route.params.subscribe((params) => {
      const conversationId = params['conversationId'];
      if (conversationId) {
        this.selectedConversationId = conversationId;
        this.markConversationAsSelected(conversationId);
      }
    });
  }

  private setupSubscriptions(): void {
    // Subscription pour les nouveaux messages
    const messagesSub = this.messageService
      .subscribeToMessages()
      .subscribe((message) => {
        if (message) {
          this.handleNewMessage(message);
        }
      });

    // Subscription pour les notifications
    const notificationsSub = this.messageService
      .subscribeToNotifications()
      .subscribe((notification) => {
        if (notification) {
          this.handleNewNotification(notification);
        }
      });

    // Subscription pour la recherche
    const searchSub = this.searchQuery$.subscribe((query) => {
      this.performSearch(query);
    });

    // Subscription pour les changements de thème
    const themeSub = this.themeService.currentTheme$.subscribe((theme) => {
      this.currentTheme = theme;
      this.cdr.detectChanges();
    });

    this.subscriptions.push(messagesSub, notificationsSub, searchSub, themeSub);
  }

  private loadInitialData(): void {
    this.loadConversations();
    this.loadUsers();
    this.loadNotifications();

    // Charger l'utilisateur actuel depuis les données de test
    if (!this.currentUser) {
      this.currentUser = this.mockDataService.getCurrentUser();
    }
  }

  // ============================================================================
  // MÉTHODES DE CHARGEMENT DES DONNÉES
  // ============================================================================

  loadConversations(page: number = 1): void {
    if (this.isLoadingConversations) return;

    this.isLoadingConversations = true;

    this.messageService.getConversations(25, page).subscribe({
      next: (conversations) => {
        if (page === 1) {
          this.conversations = conversations;
        } else {
          this.conversations.push(...conversations);
        }

        this.conversationsPage = page;
        this.hasMoreConversations = conversations.length === 25;
        this.isLoadingConversations = false;
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.warn(
          'Service principal indisponible, utilisation des données de test:',
          error
        );
        // Fallback sur les données de test
        this.mockDataService.getConversations().subscribe({
          next: (conversations) => {
            if (page === 1) {
              this.conversations = conversations;
            } else {
              this.conversations.push(...conversations);
            }
            this.conversationsPage = page;
            this.hasMoreConversations = false; // Pas de pagination pour les données de test
            this.isLoadingConversations = false;
            this.cdr.detectChanges();
            if (page === 1) {
              this.toastService.showInfo(
                'Mode démo - Données de test chargées'
              );
            }
          },
          error: (mockError) => {
            console.error(
              'Erreur lors du chargement des données de test:',
              mockError
            );
            this.isLoadingConversations = false;
            this.toastService.showError(
              'Erreur lors du chargement des conversations'
            );
          },
        });
      },
    });
  }

  loadUsers(page: number = 1): void {
    if (this.isLoadingUsers) return;

    this.isLoadingUsers = true;

    this.messageService.getAllUsers(false, '', page, 25).subscribe({
      next: (users) => {
        if (page === 1) {
          this.users = users;
        } else {
          this.users.push(...users);
        }

        this.usersPage = page;
        this.hasMoreUsers = users.length === 25;
        this.isLoadingUsers = false;
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.warn(
          'Service principal indisponible, utilisation des données de test:',
          error
        );
        // Fallback sur les données de test
        this.mockDataService.getUsers().subscribe({
          next: (users) => {
            if (page === 1) {
              this.users = users;
            } else {
              this.users.push(...users);
            }
            this.usersPage = page;
            this.hasMoreUsers = false; // Pas de pagination pour les données de test
            this.isLoadingUsers = false;
            this.cdr.detectChanges();
          },
          error: (mockError) => {
            console.error(
              'Erreur lors du chargement des données de test:',
              mockError
            );
            this.isLoadingUsers = false;
            this.toastService.showError(
              'Erreur lors du chargement des utilisateurs'
            );
          },
        });
      },
    });
  }

  loadNotifications(): void {
    if (this.isLoadingNotifications) return;

    this.isLoadingNotifications = true;

    this.messageService.getNotifications().subscribe({
      next: (notifications) => {
        this.notifications = notifications;
        this.isLoadingNotifications = false;
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.warn(
          'Service principal indisponible, utilisation des données de test:',
          error
        );
        // Fallback sur les données de test
        this.mockDataService.getNotifications().subscribe({
          next: (notifications) => {
            this.notifications = notifications;
            this.isLoadingNotifications = false;
            this.cdr.detectChanges();
          },
          error: (mockError) => {
            console.error(
              'Erreur lors du chargement des données de test:',
              mockError
            );
            this.isLoadingNotifications = false;
            this.toastService.showError(
              'Erreur lors du chargement des notifications'
            );
          },
        });
      },
    });
  }

  // ============================================================================
  // MÉTHODES DE GESTION DES ÉVÉNEMENTS
  // ============================================================================

  private handleNewMessage(message: Message): void {
    // Mettre à jour la conversation correspondante
    const conversationIndex = this.conversations.findIndex(
      (conv) => conv.id === message.conversationId
    );

    if (conversationIndex !== -1) {
      // Mettre à jour le dernier message
      this.conversations[conversationIndex].lastMessage = message;

      // Déplacer la conversation en haut de la liste
      const conversation = this.conversations.splice(conversationIndex, 1)[0];
      this.conversations.unshift(conversation);

      this.cdr.detectChanges();
    }
  }

  private handleNewNotification(notification: Notification): void {
    // Ajouter la nouvelle notification en haut de la liste
    this.notifications.unshift(notification);
    this.cdr.detectChanges();

    // Afficher une notification toast si ce n'est pas l'onglet actif
    if (this.activeTab !== 'notifications') {
      this.toastService.showInfo('Nouvelle notification reçue');
    }
  }

  private markConversationAsSelected(conversationId: string): void {
    // Marquer la conversation comme sélectionnée visuellement
    this.selectedConversationId = conversationId;
    this.cdr.detectChanges();
  }

  // ============================================================================
  // MÉTHODES DE NAVIGATION ET UI
  // ============================================================================

  switchTab(tab: 'conversations' | 'users' | 'notifications'): void {
    this.activeTab = tab;
    this.searchQuery = '';
    this.searchResults = [];
    this.isSearching = false;

    // Charger les données si nécessaire
    switch (tab) {
      case 'conversations':
        if (this.conversations.length === 0) {
          this.loadConversations();
        }
        break;
      case 'users':
        if (this.users.length === 0) {
          this.loadUsers();
        }
        break;
      case 'notifications':
        if (this.notifications.length === 0) {
          this.loadNotifications();
        }
        break;
    }
  }

  selectConversation(conversation: Conversation): void {
    if (!conversation.id) return;

    this.selectedConversationId = conversation.id;
    this.router.navigate(['/messages', conversation.id]);

    // Fermer le menu mobile si ouvert
    this.isMobileMenuOpen = false;
  }

  startConversationWithUser(user: User): void {
    if (!user.id && !user._id) return;

    const userId = user.id || user._id!;

    // Créer ou récupérer la conversation avec cet utilisateur
    this.messageService.createOrGetConversation(userId).subscribe({
      next: (conversation) => {
        this.selectConversation(conversation);
      },
      error: (error) => {
        console.warn(
          'Service principal indisponible, utilisation des données de test:',
          error
        );
        // Fallback sur les données de test
        const currentUserId = this.currentUser?.id || '1';
        this.mockDataService
          .createConversation(userId, currentUserId)
          .subscribe({
            next: (conversation) => {
              this.conversations.unshift(conversation);
              this.selectConversation(conversation);
              this.toastService.showSuccess('Conversation créée (mode démo)');
            },
            error: (mockError) => {
              console.error(
                'Erreur lors de la création de la conversation:',
                mockError
              );
              this.toastService.showError(
                'Erreur lors de la création de la conversation'
              );
            },
          });
      },
    });
  }

  toggleMobileMenu(): void {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
  }

  // ============================================================================
  // MÉTHODES DE RECHERCHE
  // ============================================================================

  onSearchInput(event: any): void {
    const query = event.target.value.trim();
    this.searchQuery = query;
    this.searchQuery$.next(query);
  }

  private performSearch(query: string): void {
    if (!query) {
      this.searchResults = [];
      this.isSearching = false;
      return;
    }

    this.isSearching = true;

    if (this.activeTab === 'conversations') {
      this.searchResults = this.conversations.filter((conv) =>
        conv.isGroup
          ? conv.groupName?.toLowerCase().includes(query.toLowerCase())
          : conv.participants?.some((p) =>
              p.username?.toLowerCase().includes(query.toLowerCase())
            )
      );
    } else if (this.activeTab === 'users') {
      this.searchResults = this.users.filter(
        (user) =>
          user.username?.toLowerCase().includes(query.toLowerCase()) ||
          user.email?.toLowerCase().includes(query.toLowerCase())
      );
    }

    this.cdr.detectChanges();
  }

  clearSearch(): void {
    this.searchQuery = '';
    this.searchResults = [];
    this.isSearching = false;
    this.searchQuery$.next('');
  }

  // ============================================================================
  // MÉTHODES DE PAGINATION
  // ============================================================================

  loadMoreConversations(): void {
    if (this.hasMoreConversations && !this.isLoadingConversations) {
      this.loadConversations(this.conversationsPage + 1);
    }
  }

  loadMoreUsers(): void {
    if (this.hasMoreUsers && !this.isLoadingUsers) {
      this.loadUsers(this.usersPage + 1);
    }
  }

  // ============================================================================
  // MÉTHODES UTILITAIRES POUR LE TEMPLATE
  // ============================================================================

  getConversationName(conversation: Conversation): string {
    if (conversation.isGroup) {
      return conversation.groupName || 'Groupe sans nom';
    }

    if (!this.currentUser) return 'Conversation';

    const currentUserId = this.currentUser.id || this.currentUser._id;
    const otherParticipant = conversation.participants?.find(
      (p) => (p.id || p._id) !== currentUserId
    );

    return otherParticipant?.username || 'Utilisateur inconnu';
  }

  getConversationAvatar(conversation: Conversation): string {
    if (conversation.isGroup) {
      return conversation.groupPhoto || '/assets/images/default-group.png';
    }

    if (!this.currentUser) return '/assets/images/default-avatar.png';

    const currentUserId = this.currentUser.id || this.currentUser._id;
    const otherParticipant = conversation.participants?.find(
      (p) => (p.id || p._id) !== currentUserId
    );

    return otherParticipant?.image || '/assets/images/default-avatar.png';
  }

  getLastMessagePreview(conversation: Conversation): string {
    if (!conversation.lastMessage) return 'Aucun message';

    const message = conversation.lastMessage;

    if (message.type === 'TEXT') {
      return message.content || '';
    } else if (message.type === 'IMAGE') {
      return '📷 Image';
    } else if (message.type === 'FILE') {
      return '📎 Fichier';
    } else if (message.type === 'VOICE_MESSAGE') {
      return '🎤 Message vocal';
    } else if (message.type === 'VIDEO') {
      return '🎥 Vidéo';
    }

    return 'Message';
  }

  formatLastMessageTime(timestamp: Date | string | undefined): string {
    if (!timestamp) return '';

    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return "À l'instant";
    } else if (diffInHours < 24) {
      return date.toLocaleTimeString('fr-FR', {
        hour: '2-digit',
        minute: '2-digit',
      });
    } else if (diffInHours < 168) {
      // 7 jours
      return date.toLocaleDateString('fr-FR', { weekday: 'short' });
    } else {
      return date.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
      });
    }
  }

  getUnreadCount(conversation: Conversation): number {
    return conversation.unreadCount || 0;
  }

  isUserOnline(user: User): boolean {
    return user.isOnline || false;
  }

  trackByConversationId(index: number, conversation: Conversation): string {
    return conversation.id || conversation._id || index.toString();
  }

  trackByUserId(index: number, user: User): string {
    return user.id || user._id || index.toString();
  }

  trackByNotificationId(index: number, notification: Notification): string {
    return notification.id || notification._id || index.toString();
  }

  markNotificationAsRead(notification: Notification): void {
    if (!notification.id || notification.isRead) return;

    this.messageService.markNotificationAsRead(notification.id).subscribe({
      next: () => {
        notification.isRead = true;
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error(
          'Erreur lors du marquage de la notification comme lue:',
          error
        );
        this.toastService.showError(
          'Erreur lors du marquage de la notification'
        );
      },
    });
  }

  // Type guards pour différencier User et Conversation dans les résultats de recherche
  isUser(item: User | Conversation): item is User {
    return 'username' in item && 'email' in item;
  }

  isConversation(item: User | Conversation): item is Conversation {
    return 'participants' in item || 'isGroup' in item;
  }

  getNotificationTitle(notification: Notification): string {
    switch (notification.type) {
      case 'NEW_MESSAGE':
        return 'Nouveau message';
      case 'FRIEND_REQUEST':
        return "Demande d'ami";
      case 'GROUP_INVITE':
        return 'Invitation de groupe';
      case 'MESSAGE_REACTION':
        return 'Réaction à un message';
      case 'SYSTEM_ALERT':
        return 'Alerte système';
      default:
        return 'Notification';
    }
  }

  // ============================================================================
  // MÉTHODES DE GESTION DES THÈMES
  // ============================================================================

  selectTheme(themeName: string): void {
    this.themeService.setTheme(themeName);
    this.showThemeSelector = false;
    this.toastService.showSuccess(
      `Thème "${this.themeService.getCurrentTheme().displayName}" appliqué`
    );
  }
}
