"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([["src_app_views_front_verify-email_verify-email_module_ts"],{

/***/ 2839:
/*!*************************************************************************!*\
  !*** ./src/app/views/front/verify-email/verify-email-routing.module.ts ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   VerifyEmailRoutingModule: () => (/* binding */ VerifyEmailRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _verify_email_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./verify-email.component */ 8935);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);




const routes = [{
  path: '',
  component: _verify_email_component__WEBPACK_IMPORTED_MODULE_0__.VerifyEmailComponent
}];
class VerifyEmailRoutingModule {
  static {
    this.ɵfac = function VerifyEmailRoutingModule_Factory(t) {
      return new (t || VerifyEmailRoutingModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineNgModule"]({
      type: VerifyEmailRoutingModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjector"]({
      imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵsetNgModuleScope"](VerifyEmailRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
  });
})();

/***/ }),

/***/ 8935:
/*!********************************************************************!*\
  !*** ./src/app/views/front/verify-email/verify-email.component.ts ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   VerifyEmailComponent: () => (/* binding */ VerifyEmailComponent)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _services_auth_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../services/auth.service */ 4796);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 316);






function VerifyEmailComponent_div_36_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 38)(1, "div", 39)(2, "div", 40);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](3, "i", 41)(4, "div", 42);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](5, "div", 43)(6, "p", 44);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"](" ", ctx_r0.error, " ");
  }
}
function VerifyEmailComponent_div_37_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 45)(1, "div", 39)(2, "div", 46);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](3, "i", 47)(4, "div", 48);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](5, "div", 43)(6, "p", 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"](" ", ctx_r1.message, " ");
  }
}
function VerifyEmailComponent_i_42_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](0, "i", 50);
  }
}
function VerifyEmailComponent_i_43_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](0, "i", 51);
  }
}
function VerifyEmailComponent_span_52_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "span", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"]("(", ctx_r4.timer, "s)");
  }
}
class VerifyEmailComponent {
  constructor(route, router, authService, fb) {
    this.route = route;
    this.router = router;
    this.authService = authService;
    this.fb = fb;
    this.loading = false;
    this.error = null;
    this.message = null;
    this.email = '';
    this.timer = 60;
    this.canResend = false;
    this.verifyForm = this.fb.group({
      code: ['', [_angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.minLength(6), _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.maxLength(6)]]
    });
  }
  ngOnInit() {
    // Get email from URL query params
    this.email = this.route.snapshot.queryParamMap.get('email') || '';
    // Check if we have an email to verify
    if (!this.email) {
      this.error = 'No email address provided for verification.';
      return;
    }
    // Start the countdown for resend
    this.startCountdown();
  }
  startCountdown() {
    this.canResend = false;
    this.timer = 60;
    this.intervalId = setInterval(() => {
      this.timer--;
      if (this.timer === 0) {
        this.canResend = true;
        clearInterval(this.intervalId);
      }
    }, 1000);
  }
  onVerifySubmit() {
    if (this.verifyForm.invalid) return;
    this.loading = true;
    this.error = null;
    this.message = null;
    const verifyData = {
      email: this.email,
      code: this.verifyForm.value.code
    };
    this.authService.verifyEmail(verifyData).subscribe({
      next: res => {
        this.loading = false;
        this.message = res.message + ' Redirection vers la page de connexion...';
        // Reset form
        this.verifyForm.reset();
        // Redirect after 1.5 seconds
        setTimeout(() => {
          this.router.navigate(['/login'], {
            queryParams: {
              message: 'Votre compte a été vérifié avec succès. Vous pouvez maintenant vous connecter.'
            }
          });
        }, 1500);
      },
      error: err => {
        this.loading = false;
        this.error = err.error?.message || 'Échec de la vérification';
      }
    });
  }
  resendCode() {
    if (!this.canResend) return;
    this.loading = true;
    this.error = null;
    this.message = null;
    this.authService.resendCode(this.email).subscribe({
      next: res => {
        this.loading = false;
        this.message = res.message || 'Code renvoyé avec succès.';
        this.startCountdown();
      },
      error: err => {
        this.loading = false;
        this.error = err.error?.message || "Erreur lors de l'envoi du code.";
      }
    });
  }
  static {
    this.ɵfac = function VerifyEmailComponent_Factory(t) {
      return new (t || VerifyEmailComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_3__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_3__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_services_auth_service__WEBPACK_IMPORTED_MODULE_0__.AuthService), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormBuilder));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
      type: VerifyEmailComponent,
      selectors: [["app-verify-email"]],
      decls: 56,
      vars: 16,
      consts: [[1, "min-h-screen", "bg-[#edf1f4]", "dark:bg-[#121212]", "flex", "items-center", "justify-center", "p-4", "relative", "futuristic-layout"], [1, "absolute", "inset-0", "overflow-hidden", "pointer-events-none"], [1, "absolute", "top-[15%]", "left-[10%]", "w-64", "h-64", "rounded-full", "bg-gradient-to-br", "from-[#4f5fad]/5", "to-transparent", "dark:from-[#6d78c9]/3", "dark:to-transparent", "blur-3xl"], [1, "absolute", "bottom-[20%]", "right-[10%]", "w-80", "h-80", "rounded-full", "bg-gradient-to-tl", "from-[#4f5fad]/5", "to-transparent", "dark:from-[#6d78c9]/3", "dark:to-transparent", "blur-3xl"], [1, "absolute", "inset-0", "opacity-5", "dark:opacity-[0.03]"], [1, "h-full", "grid", "grid-cols-12"], [1, "border-r", "border-[#4f5fad]", "dark:border-[#6d78c9]"], [1, "w-full", "max-w-md", "relative", "z-10"], [1, "bg-white", "dark:bg-[#1e1e1e]", "rounded-xl", "shadow-lg", "dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]", "overflow-hidden", "backdrop-blur-sm", "border", "border-[#edf1f4]/50", "dark:border-[#2a2a2a]", "relative"], [1, "absolute", "top-0", "left-0", "right-0", "h-1", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]"], [1, "absolute", "top-0", "left-0", "right-0", "h-1", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "blur-md"], [1, "p-6", "text-center"], [1, "text-2xl", "font-bold", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "bg-clip-text", "text-transparent"], [1, "text-sm", "text-[#6d6870]", "dark:text-[#a0a0a0]", "mt-2"], [1, "p-6"], [1, "space-y-5", 3, "formGroup", "ngSubmit"], [1, "group"], [1, "flex", "items-center", "text-sm", "font-medium", "text-[#4f5fad]", "dark:text-[#6d78c9]", "mb-2"], [1, "fas", "fa-key", "mr-1.5", "text-xs"], [1, "relative"], ["formControlName", "code", "placeholder", "123456", 1, "w-full", "px-4", "py-2.5", "text-sm", "rounded-lg", "border", "border-[#bdc6cc]", "dark:border-[#2a2a2a]", "bg-white", "dark:bg-[#1e1e1e]", "text-[#6d6870]", "dark:text-[#e0e0e0]", "focus:outline-none", "focus:border-[#4f5fad]", "dark:focus:border-[#6d78c9]", "focus:ring-2", "focus:ring-[#4f5fad]/20", "dark:focus:ring-[#6d78c9]/20", "transition-all"], [1, "absolute", "inset-y-0", "left-0", "pl-3", "flex", "items-center", "pointer-events-none", "opacity-0", "group-focus-within:opacity-100", "transition-opacity"], [1, "w-0.5", "h-4", "bg-gradient-to-b", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "rounded-full"], ["class", "bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-3 backdrop-blur-sm", 4, "ngIf"], ["class", "bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-3 backdrop-blur-sm", 4, "ngIf"], ["type", "submit", 1, "w-full", "relative", "overflow-hidden", "group", "mt-6", 3, "disabled"], [1, "absolute", "inset-0", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#3d4a85]", "dark:to-[#6d78c9]", "rounded-lg", "transition-transform", "duration-300", "group-hover:scale-105"], [1, "absolute", "inset-0", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#3d4a85]", "dark:to-[#6d78c9]", "rounded-lg", "opacity-0", "group-hover:opacity-100", "blur-xl", "transition-opacity", "duration-300"], [1, "relative", "flex", "items-center", "justify-center", "text-white", "font-medium", "py-2.5", "px-4", "rounded-lg", "transition-all", "z-10"], ["class", "fas fa-spinner fa-spin mr-2", 4, "ngIf"], ["class", "fas fa-check mr-2", 4, "ngIf"], [1, "text-center", "text-sm", "text-[#6d6870]", "dark:text-[#a0a0a0]", "space-y-2", "pt-4"], [1, "flex", "items-center", "justify-center"], ["type", "button", 1, "ml-1.5", "text-[#4f5fad]", "dark:text-[#6d78c9]", "hover:text-[#3d4a85]", "dark:hover:text-[#4f5fad]", "transition-colors", "font-medium", "disabled:opacity-50", "disabled:cursor-not-allowed", "flex", "items-center", 3, "disabled", "click"], [1, "fas", "fa-sync-alt", "mr-1", "text-xs"], ["class", "ml-1", 4, "ngIf"], [1, "text-center", "mt-4"], ["routerLink", "/login", 1, "text-[#4f5fad]", "dark:text-[#6d78c9]", "hover:text-[#3d4a85]", "dark:hover:text-[#4f5fad]", "transition-colors", "text-sm"], [1, "bg-[#ff6b69]/10", "dark:bg-[#ff6b69]/5", "border", "border-[#ff6b69]", "dark:border-[#ff6b69]/30", "rounded-lg", "p-3", "backdrop-blur-sm"], [1, "flex", "items-start"], [1, "text-[#ff6b69]", "dark:text-[#ff8785]", "mr-2", "text-base", "relative"], [1, "fas", "fa-exclamation-triangle"], [1, "absolute", "inset-0", "bg-[#ff6b69]/20", "dark:bg-[#ff8785]/20", "blur-xl", "rounded-full", "transform", "scale-150", "-z-10"], [1, "flex-1"], [1, "text-xs", "text-[#ff6b69]", "dark:text-[#ff8785]"], [1, "bg-[#4f5fad]/10", "dark:bg-[#6d78c9]/5", "border", "border-[#4f5fad]", "dark:border-[#6d78c9]/30", "rounded-lg", "p-3", "backdrop-blur-sm"], [1, "text-[#4f5fad]", "dark:text-[#6d78c9]", "mr-2", "text-base", "relative"], [1, "fas", "fa-check-circle"], [1, "absolute", "inset-0", "bg-[#4f5fad]/20", "dark:bg-[#6d78c9]/20", "blur-xl", "rounded-full", "transform", "scale-150", "-z-10"], [1, "text-xs", "text-[#4f5fad]", "dark:text-[#6d78c9]"], [1, "fas", "fa-spinner", "fa-spin", "mr-2"], [1, "fas", "fa-check", "mr-2"], [1, "ml-1"]],
      template: function VerifyEmailComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 0)(1, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](2, "div", 2)(3, "div", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](4, "div", 4)(5, "div", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](6, "div", 6)(7, "div", 6)(8, "div", 6)(9, "div", 6)(10, "div", 6)(11, "div", 6)(12, "div", 6)(13, "div", 6)(14, "div", 6)(15, "div", 6)(16, "div", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](17, "div", 7)(18, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](19, "div", 9)(20, "div", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](21, "div", 11)(22, "h1", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](23, " Email Verification ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](24, "p", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](25);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](26, "div", 14)(27, "form", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("ngSubmit", function VerifyEmailComponent_Template_form_ngSubmit_27_listener() {
            return ctx.onVerifySubmit();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](28, "div", 16)(29, "label", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](30, "i", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](31, " Verification Code ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](32, "div", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](33, "input", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](34, "div", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](35, "div", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](36, VerifyEmailComponent_div_36_Template, 8, 1, "div", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](37, VerifyEmailComponent_div_37_Template, 8, 1, "div", 24);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](38, "button", 25);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](39, "div", 26)(40, "div", 27);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](41, "span", 28);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](42, VerifyEmailComponent_i_42_Template, 1, 0, "i", 29);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](43, VerifyEmailComponent_i_43_Template, 1, 0, "i", 30);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](44);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](45, "div", 31)(46, "div", 32)(47, "span");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](48, "Didn't receive a code?");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](49, "button", 33);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("click", function VerifyEmailComponent_Template_button_click_49_listener() {
            return ctx.resendCode();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](50, "i", 34);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](51, " Resend ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](52, VerifyEmailComponent_span_52_Template, 2, 1, "span", 35);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](53, "div", 36)(54, "a", 37);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](55, " Back to Login ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()()()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](25);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"](" A verification code has been sent to ", ctx.email, " ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("formGroup", ctx.verifyForm);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](9);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.error);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.message);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("disabled", ctx.loading || ctx.verifyForm.invalid);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵclassProp"]("opacity-70", ctx.loading || ctx.verifyForm.invalid);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵclassProp"]("opacity-0", ctx.loading || ctx.verifyForm.invalid);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.loading);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", !ctx.loading);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"](" ", ctx.loading ? "Verifying..." : "Verify", " ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("disabled", !ctx.canResend || ctx.loading);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵclassProp"]("animate-spin", !ctx.canResend);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", !ctx.canResend);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_2__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_2__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormControlName, _angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterLink],
      styles: ["/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJ2ZXJpZnktZW1haWwuY29tcG9uZW50LmNzcyJ9 */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvdmVyaWZ5LWVtYWlsL3ZlcmlmeS1lbWFpbC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7O0FBRUEsd0tBQXdLIiwic291cmNlUm9vdCI6IiJ9 */"]
    });
  }
}

/***/ }),

/***/ 7942:
/*!*****************************************************************!*\
  !*** ./src/app/views/front/verify-email/verify-email.module.ts ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   VerifyEmailModule: () => (/* binding */ VerifyEmailModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _verify_email_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./verify-email-routing.module */ 2839);
/* harmony import */ var _verify_email_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./verify-email.component */ 8935);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 7580);





class VerifyEmailModule {
  static {
    this.ɵfac = function VerifyEmailModule_Factory(t) {
      return new (t || VerifyEmailModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineNgModule"]({
      type: VerifyEmailModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineInjector"]({
      imports: [_angular_common__WEBPACK_IMPORTED_MODULE_3__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.ReactiveFormsModule, _verify_email_routing_module__WEBPACK_IMPORTED_MODULE_0__.VerifyEmailRoutingModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵsetNgModuleScope"](VerifyEmailModule, {
    declarations: [_verify_email_component__WEBPACK_IMPORTED_MODULE_1__.VerifyEmailComponent],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_3__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.ReactiveFormsModule, _verify_email_routing_module__WEBPACK_IMPORTED_MODULE_0__.VerifyEmailRoutingModule]
  });
})();

/***/ })

}]);
//# sourceMappingURL=src_app_views_front_verify-email_verify-email_module_ts.js.map