/* ============================================================================
   STYLES POUR LE LAYOUT DE MESSAGERIE - THÈME SOMBRE FUTURISTE
   ============================================================================ */

.message-layout {
  @apply h-screen bg-gray-900 text-white;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
}

/* ============================================================================
   SIDEBAR
   ============================================================================ */

.sidebar {
  @apply w-80 bg-gray-800 border-r border-gray-700 flex flex-col;
  background: linear-gradient(180deg, #1f2937 0%, #111827 100%);
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
}

.sidebar-header {
  @apply p-4 border-b border-gray-700 bg-gray-800;
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
}

/* ============================================================================
   ONGLETS DE NAVIGATION
   ============================================================================ */

.tabs {
  @apply flex border-b border-gray-700;
  background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
}

.tab {
  @apply flex-1 py-3 px-4 text-center transition-all duration-200 cursor-pointer;
  position: relative;
}

.tab:hover {
  @apply bg-gray-700;
}

.tab.active {
  @apply text-blue-400 border-b-2 border-blue-500;
  background: linear-gradient(135deg, #1e3a8a 0%, #1d4ed8 100%);
  box-shadow: 0 2px 10px rgba(59, 130, 246, 0.3);
}

.tab i {
  @apply block text-lg mb-1;
}

/* ============================================================================
   CONTENU DE LA SIDEBAR
   ============================================================================ */

.sidebar-content {
  @apply flex-1 overflow-y-auto;
  scrollbar-width: thin;
  scrollbar-color: #374151 #1f2937;
}

.sidebar-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: #1f2937;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: #374151;
  border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
  background: #4b5563;
}

/* ============================================================================
   ÉLÉMENTS DE LISTE (CONVERSATIONS, UTILISATEURS, NOTIFICATIONS)
   ============================================================================ */

.conversation-item,
.user-item,
.notification-item {
  @apply p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-all duration-200;
  position: relative;
}

.conversation-item:hover,
.user-item:hover,
.notification-item:hover {
  background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
  transform: translateX(2px);
}

.conversation-item.bg-gray-700 {
  @apply border-l-4 border-blue-500;
  background: linear-gradient(135deg, #1e3a8a 0%, #1d4ed8 100%);
  box-shadow: 0 2px 10px rgba(59, 130, 246, 0.2);
}

/* ============================================================================
   AVATARS ET INDICATEURS
   ============================================================================ */

.user-avatar,
.conversation-avatar {
  @apply w-12 h-12 rounded-full border-2 border-gray-600;
  transition: all 0.2s ease;
}

.user-avatar:hover,
.conversation-avatar:hover {
  @apply border-blue-500;
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.4);
}

.online-indicator {
  @apply absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-gray-800;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* ============================================================================
   BADGES ET COMPTEURS
   ============================================================================ */

.unread-badge {
  @apply bg-blue-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
  animation: badgePulse 2s infinite;
}

@keyframes badgePulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.notification-badge {
  @apply absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.4);
  animation: notificationPulse 1s infinite;
}

@keyframes notificationPulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

/* ============================================================================
   ZONE PRINCIPALE
   ============================================================================ */

.main-content {
  @apply flex-1 flex flex-col;
  background: linear-gradient(180deg, #0f172a 0%, #111827 100%);
}

/* ============================================================================
   RESPONSIVE DESIGN
   ============================================================================ */

@media (max-width: 768px) {
  .sidebar {
    @apply fixed inset-y-0 left-0 z-50 w-80;
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
  }

  .sidebar.show {
    transform: translateX(0);
  }

  .main-content {
    @apply w-full;
  }
}
