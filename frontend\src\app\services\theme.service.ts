import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export interface Theme {
  name: string;
  displayName: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
    success: string;
    warning: string;
    error: string;
  };
  gradients: {
    primary: string;
    secondary: string;
    accent: string;
  };
}

export const THEMES: { [key: string]: Theme } = {
  dark: {
    name: 'dark',
    displayName: 'Sombre',
    colors: {
      primary: '#3b82f6',
      secondary: '#6366f1',
      accent: '#8b5cf6',
      background: '#111827',
      surface: '#1f2937',
      text: '#ffffff',
      textSecondary: '#9ca3af',
      border: '#374151',
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
    },
    gradients: {
      primary: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
      secondary: 'linear-gradient(135deg, #6366f1 0%, #4f46e5 100%)',
      accent: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
    },
  },
  neon: {
    name: 'neon',
    displayName: 'Néon',
    colors: {
      primary: '#00ffff',
      secondary: '#ff00ff',
      accent: '#ffff00',
      background: '#0a0a0a',
      surface: '#1a1a1a',
      text: '#ffffff',
      textSecondary: '#cccccc',
      border: '#333333',
      success: '#00ff00',
      warning: '#ff8800',
      error: '#ff0040',
    },
    gradients: {
      primary: 'linear-gradient(135deg, #00ffff 0%, #0080ff 100%)',
      secondary: 'linear-gradient(135deg, #ff00ff 0%, #8000ff 100%)',
      accent: 'linear-gradient(135deg, #ffff00 0%, #ff8000 100%)',
    },
  },
  purple: {
    name: 'purple',
    displayName: 'Violet',
    colors: {
      primary: '#8b5cf6',
      secondary: '#a855f7',
      accent: '#c084fc',
      background: '#1e1b4b',
      surface: '#312e81',
      text: '#ffffff',
      textSecondary: '#c7d2fe',
      border: '#4c1d95',
      success: '#22c55e',
      warning: '#eab308',
      error: '#ef4444',
    },
    gradients: {
      primary: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
      secondary: 'linear-gradient(135deg, #a855f7 0%, #9333ea 100%)',
      accent: 'linear-gradient(135deg, #c084fc 0%, #a855f7 100%)',
    },
  },
  ocean: {
    name: 'ocean',
    displayName: 'Océan',
    colors: {
      primary: '#0ea5e9',
      secondary: '#06b6d4',
      accent: '#22d3ee',
      background: '#0c4a6e',
      surface: '#075985',
      text: '#ffffff',
      textSecondary: '#bae6fd',
      border: '#0369a1',
      success: '#059669',
      warning: '#d97706',
      error: '#dc2626',
    },
    gradients: {
      primary: 'linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%)',
      secondary: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',
      accent: 'linear-gradient(135deg, #22d3ee 0%, #06b6d4 100%)',
    },
  },
};

@Injectable({
  providedIn: 'root',
})
export class ThemeService {
  private currentTheme = new BehaviorSubject<Theme>(THEMES['dark']);
  public currentTheme$ = this.currentTheme.asObservable();

  constructor() {
    this.loadThemeFromStorage();
    this.applyTheme(this.currentTheme.value);
  }

  setTheme(themeName: string): void {
    const theme = THEMES[themeName];
    if (theme) {
      this.currentTheme.next(theme);
      this.applyTheme(theme);
      this.saveThemeToStorage(themeName);
    }
  }

  getCurrentTheme(): Theme {
    return this.currentTheme.value;
  }

  getAvailableThemes(): Theme[] {
    return Object.values(THEMES);
  }

  private applyTheme(theme: Theme): void {
    const root = document.documentElement;

    // Appliquer les couleurs CSS custom properties
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value);
    });

    // Appliquer les gradients
    Object.entries(theme.gradients).forEach(([key, value]) => {
      root.style.setProperty(`--gradient-${key}`, value);
    });

    // Ajouter la classe de thème au body
    document.body.className = document.body.className.replace(/theme-\w+/g, '');
    document.body.classList.add(`theme-${theme.name}`);
  }

  private saveThemeToStorage(themeName: string): void {
    try {
      localStorage.setItem('selectedTheme', themeName);
    } catch (error) {
      console.warn('Could not save theme to localStorage:', error);
    }
  }

  private loadThemeFromStorage(): void {
    try {
      const savedTheme = localStorage.getItem('selectedTheme');
      if (savedTheme && THEMES[savedTheme]) {
        this.currentTheme.next(THEMES[savedTheme]);
      }
    } catch (error) {
      console.warn('Could not load theme from localStorage:', error);
    }
  }

  // Méthodes utilitaires pour les composants
  getPrimaryColor(): string {
    return this.currentTheme.value.colors.primary;
  }

  getSecondaryColor(): string {
    return this.currentTheme.value.colors.secondary;
  }

  getAccentColor(): string {
    return this.currentTheme.value.colors.accent;
  }

  getPrimaryGradient(): string {
    return this.currentTheme.value.gradients.primary;
  }

  isTheme(themeName: string): boolean {
    return this.currentTheme.value.name === themeName;
  }
}
