{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { LayoutsModule } from './layouts/layouts.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\nimport { JwtModule, JWT_OPTIONS } from '@auth0/angular-jwt';\nimport { environment } from 'src/environments/environment';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { GraphQLModule } from './graphql.module';\nimport { ApolloModule } from 'apollo-angular';\n// Modules temporairement commentés - à créer si nécessaire\n// import { CallModule } from './components/call/call.module';\n// import { ConnectionStatusModule } from './components/connection-status/connection-status.module';\n// import { GraphqlStatusModule } from './components/graphql-status/graphql-status.module';\n// import { VoiceMessageModule } from './components/voice-message/voice-message.module';\nimport { SharedModule } from './shared/shared.module';\n// Factory simplifiée sans injection de JwtHelperService\nexport function jwtOptionsFactory() {\n  return {\n    tokenGetter: () => {\n      // Supprimer les logs pour améliorer les performances\n      // if (!environment.production) {\n      //   console.debug('JWT token retrieved from storage');\n      // }\n      return localStorage.getItem('token');\n    },\n    allowedDomains: [new URL(environment.urlBackend).hostname],\n    disallowedRoutes: [`${new URL(environment.urlBackend).origin}/users/login`]\n  };\n}\nexport let AppModule = class AppModule {};\nAppModule = __decorate([NgModule({\n  declarations: [AppComponent],\n  imports: [BrowserModule, AppRoutingModule, LayoutsModule, FormsModule, ReactiveFormsModule, HttpClientModule, BrowserAnimationsModule, JwtModule.forRoot({\n    jwtOptionsProvider: {\n      provide: JWT_OPTIONS,\n      useFactory: jwtOptionsFactory\n    }\n  }), GraphQLModule, ApolloModule, CallModule, ConnectionStatusModule, GraphqlStatusModule, VoiceMessageModule, SharedModule],\n  providers: [],\n  bootstrap: [AppComponent]\n})], AppModule);", "map": {"version": 3, "names": ["NgModule", "BrowserModule", "AppRoutingModule", "AppComponent", "LayoutsModule", "FormsModule", "ReactiveFormsModule", "HttpClientModule", "JwtModule", "JWT_OPTIONS", "environment", "BrowserAnimationsModule", "GraphQLModule", "ApolloModule", "SharedModule", "jwtOptionsFactory", "tokenGetter", "localStorage", "getItem", "allowedDomains", "URL", "urlBackend", "hostname", "disallowedRoutes", "origin", "AppModule", "__decorate", "declarations", "imports", "forRoot", "jwtOptionsProvider", "provide", "useFactory", "CallModule", "ConnectionStatusModule", "GraphqlStatusModule", "VoiceMessageModule", "providers", "bootstrap"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { LayoutsModule } from './layouts/layouts.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\nimport { JwtModule, JWT_OPTIONS } from '@auth0/angular-jwt';\nimport { environment } from 'src/environments/environment';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { GraphQLModule } from './graphql.module';\nimport { ApolloModule } from 'apollo-angular';\n// Modules temporairement commentés - à créer si nécessaire\n// import { CallModule } from './components/call/call.module';\n// import { ConnectionStatusModule } from './components/connection-status/connection-status.module';\n// import { GraphqlStatusModule } from './components/graphql-status/graphql-status.module';\n// import { VoiceMessageModule } from './components/voice-message/voice-message.module';\nimport { SharedModule } from './shared/shared.module';\n// Factory simplifiée sans injection de JwtHelperService\nexport function jwtOptionsFactory() {\n  return {\n    tokenGetter: () => {\n      // Supprimer les logs pour améliorer les performances\n      // if (!environment.production) {\n      //   console.debug('JWT token retrieved from storage');\n      // }\n      return localStorage.getItem('token');\n    },\n    allowedDomains: [new URL(environment.urlBackend).hostname],\n    disallowedRoutes: [`${new URL(environment.urlBackend).origin}/users/login`],\n  };\n}\n\n@NgModule({\n  declarations: [AppComponent],\n  imports: [\n    BrowserModule,\n    AppRoutingModule,\n    LayoutsModule,\n    FormsModule,\n    ReactiveFormsModule,\n    HttpClientModule,\n    BrowserAnimationsModule,\n    JwtModule.forRoot({\n      jwtOptionsProvider: {\n        provide: JWT_OPTIONS,\n        useFactory: jwtOptionsFactory,\n      },\n    }),\n    GraphQLModule,\n    ApolloModule,\n    CallModule,\n    ConnectionStatusModule,\n    GraphqlStatusModule,\n    VoiceMessageModule,\n    SharedModule,\n  ],\n  providers: [],\n  bootstrap: [AppComponent],\n})\nexport class AppModule {}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,0BAA0B;AACxD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,SAAS,EAAEC,WAAW,QAAQ,oBAAoB;AAC3D,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C;AACA;AACA;AACA;AACA;AACA,SAASC,YAAY,QAAQ,wBAAwB;AACrD;AACA,OAAM,SAAUC,iBAAiBA,CAAA;EAC/B,OAAO;IACLC,WAAW,EAAEA,CAAA,KAAK;MAChB;MACA;MACA;MACA;MACA,OAAOC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IACtC,CAAC;IACDC,cAAc,EAAE,CAAC,IAAIC,GAAG,CAACV,WAAW,CAACW,UAAU,CAAC,CAACC,QAAQ,CAAC;IAC1DC,gBAAgB,EAAE,CAAC,GAAG,IAAIH,GAAG,CAACV,WAAW,CAACW,UAAU,CAAC,CAACG,MAAM,cAAc;GAC3E;AACH;AA6BO,WAAMC,SAAS,GAAf,MAAMA,SAAS,GAAG;AAAZA,SAAS,GAAAC,UAAA,EA3BrB1B,QAAQ,CAAC;EACR2B,YAAY,EAAE,CAACxB,YAAY,CAAC;EAC5ByB,OAAO,EAAE,CACP3B,aAAa,EACbC,gBAAgB,EAChBE,aAAa,EACbC,WAAW,EACXC,mBAAmB,EACnBC,gBAAgB,EAChBI,uBAAuB,EACvBH,SAAS,CAACqB,OAAO,CAAC;IAChBC,kBAAkB,EAAE;MAClBC,OAAO,EAAEtB,WAAW;MACpBuB,UAAU,EAAEjB;;GAEf,CAAC,EACFH,aAAa,EACbC,YAAY,EACZoB,UAAU,EACVC,sBAAsB,EACtBC,mBAAmB,EACnBC,kBAAkB,EAClBtB,YAAY,CACb;EACDuB,SAAS,EAAE,EAAE;EACbC,SAAS,EAAE,CAACnC,YAAY;CACzB,CAAC,C,EACWsB,SAAS,CAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}