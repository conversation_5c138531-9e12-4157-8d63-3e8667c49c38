{"ast": null, "code": "import { trigger, transition, style, animate, query, stagger } from '@angular/animations';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/reunion.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/authuser.service\";\nimport * as i4 from \"@angular/platform-browser\";\nimport * as i5 from \"src/app/services/toast.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"../../../../shared/pipes/highlight-presence.pipe\";\nfunction ReunionListComponent_div_10_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function ReunionListComponent_div_10_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.clearSearch());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 20);\n    i0.ɵɵelement(2, \"path\", 31);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ReunionListComponent_div_10_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const planning_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", planning_r11.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(planning_r11.titre);\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"animate__animated animate__fadeInDown\": a0\n  };\n};\nfunction ReunionListComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16)(2, \"div\", 17)(3, \"div\", 4)(4, \"div\", 18)(5, \"div\", 19);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(6, \"svg\", 20);\n    i0.ɵɵelement(7, \"path\", 7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(8, \"input\", 21);\n    i0.ɵɵlistener(\"ngModelChange\", function ReunionListComponent_div_10_Template_input_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.searchTerm = $event);\n    })(\"input\", function ReunionListComponent_div_10_Template_input_input_8_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.searchReunions());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, ReunionListComponent_div_10_button_9_Template, 3, 0, \"button\", 22);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 23)(11, \"div\", 4)(12, \"div\", 18)(13, \"select\", 24);\n    i0.ɵɵlistener(\"ngModelChange\", function ReunionListComponent_div_10_Template_select_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.selectedPlanning = $event);\n    })(\"change\", function ReunionListComponent_div_10_Template_select_change_13_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.searchReunions());\n    });\n    i0.ɵɵelementStart(14, \"option\", 25);\n    i0.ɵɵtext(15, \"Tous les plannings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, ReunionListComponent_div_10_option_16_Template, 2, 2, \"option\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 27);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(18, \"svg\", 28);\n    i0.ɵɵelement(19, \"path\", 29);\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c0, ctx_r0.showSearchBar));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.searchTerm);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.searchTerm);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.selectedPlanning);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.uniquePlannings);\n  }\n}\nfunction ReunionListComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 34);\n    i0.ɵɵelement(2, \"path\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \"Les r\\u00E9unions \\u00E0 \");\n    i0.ɵɵelementStart(5, \"span\", 36);\n    i0.ɵɵtext(6, \"pr\\u00E9sence obligatoire\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7, \" sont affich\\u00E9es en premier\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ReunionListComponent_div_12_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 40);\n    i0.ɵɵtext(1, \" Aucune r\\u00E9union ne correspond \\u00E0 votre recherche. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionListComponent_div_12_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r18.filteredReunions.length, \" r\\u00E9union(s) trouv\\u00E9e(s) \");\n  }\n}\nfunction ReunionListComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtemplate(1, ReunionListComponent_div_12_span_1_Template, 2, 0, \"span\", 38);\n    i0.ɵɵtemplate(2, ReunionListComponent_div_12_span_2_Template, 2, 1, \"span\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.filteredReunions.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.filteredReunions.length > 0);\n  }\n}\nfunction ReunionListComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵelement(1, \"div\", 42);\n    i0.ɵɵelementStart(2, \"p\", 43);\n    i0.ɵɵtext(3, \"Chargement de vos r\\u00E9unions...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ReunionListComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"div\", 18);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 45);\n    i0.ɵɵelement(3, \"path\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"Erreur lors du chargement des r\\u00E9unions: \", ctx_r4.error.message, \"\");\n  }\n}\nfunction ReunionListComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"div\", 48);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 49);\n    i0.ɵɵelement(3, \"path\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"h3\", 51);\n    i0.ɵɵtext(5, \"Aucune r\\u00E9union pr\\u00E9vue\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 52);\n    i0.ɵɵtext(7, \"Vous pouvez cr\\u00E9er des r\\u00E9unions depuis la page d\\u00E9tail d'un planning.\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"animated\", !ctx_r5.loading);\n  }\n}\nfunction ReunionListComponent_div_16_div_1_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 82);\n    i0.ɵɵtext(1, \" Pr\\u00E9sence Obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionListComponent_div_16_div_1_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 83)(1, \"div\", 18);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 69);\n    i0.ɵɵelement(3, \"path\", 84);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"strong\");\n    i0.ɵɵtext(5, \"Participants:\\u00A0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const reunion_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", reunion_r20.participants.length, \" \");\n  }\n}\nfunction ReunionListComponent_div_16_div_1_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"a\", 86);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 75);\n    i0.ɵɵelement(3, \"path\", 87);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Rejoindre la visioconf\\u00E9rence \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const reunion_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"href\", reunion_r20.lienVisio, i0.ɵɵsanitizeUrl);\n  }\n}\nconst _c1 = function (a1) {\n  return [\"/reunions/reunionDetails\", a1];\n};\nfunction ReunionListComponent_div_16_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 56)(2, \"div\", 17)(3, \"div\", 18)(4, \"h3\", 57)(5, \"a\", 58);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, ReunionListComponent_div_16_div_1_span_7_Template, 2, 0, \"span\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"p\", 60);\n    i0.ɵɵpipe(9, \"highlightPresence\");\n    i0.ɵɵelementStart(10, \"div\", 61);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(11, \"svg\", 62);\n    i0.ɵɵelement(12, \"path\", 63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 64)(17, \"span\");\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function ReunionListComponent_div_16_div_1_Template_button_click_20_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r28);\n      const reunion_r20 = restoredCtx.$implicit;\n      const ctx_r27 = i0.ɵɵnextContext(2);\n      ctx_r27.deleteReunion(reunion_r20._id || reunion_r20.id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(21, \"svg\", 66);\n    i0.ɵɵelement(22, \"path\", 67);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(23, \"div\", 68);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(24, \"svg\", 69);\n    i0.ɵɵelement(25, \"path\", 70);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(26, \"span\")(27, \"strong\");\n    i0.ɵɵtext(28, \"Cr\\u00E9ateur:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(30, ReunionListComponent_div_16_div_1_div_30_Template, 7, 1, \"div\", 71);\n    i0.ɵɵelementStart(31, \"div\", 68);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(32, \"svg\", 69);\n    i0.ɵɵelement(33, \"path\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(34, \"span\")(35, \"strong\");\n    i0.ɵɵtext(36, \"Planning:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(38, ReunionListComponent_div_16_div_1_div_38_Template, 5, 1, \"div\", 72);\n    i0.ɵɵelementStart(39, \"div\", 73)(40, \"div\", 74);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(41, \"svg\", 75);\n    i0.ɵɵelement(42, \"path\", 76)(43, \"path\", 77);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(45, \"div\", 78)(46, \"a\", 79);\n    i0.ɵɵlistener(\"click\", function ReunionListComponent_div_16_div_1_Template_a_click_46_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r28);\n      const reunion_r20 = restoredCtx.$implicit;\n      const ctx_r29 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r29.editReunion(reunion_r20._id || reunion_r20.id));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(47, \"svg\", 80);\n    i0.ɵɵelement(48, \"path\", 81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(49, \" Modifier \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const reunion_r20 = ctx.$implicit;\n    const i_r21 = ctx.index;\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"animation-delay\", i_r21 * 100 + \"ms\");\n    i0.ɵɵclassProp(\"animated\", ctx_r19.animateItems)(\"border-l-4\", ctx_r19.hasPresenceObligatoire(reunion_r20))(\"border-red-500\", ctx_r19.hasPresenceObligatoire(reunion_r20));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(30, _c1, reunion_r20._id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(reunion_r20.titre);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.hasPresenceObligatoire(reunion_r20));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(9, 23, reunion_r20.description), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate3(\"\", i0.ɵɵpipeBind2(15, 25, reunion_r20.date, \"mediumDate\"), \" \\u2022 \", reunion_r20.heureDebut, \" - \", reunion_r20.heureFin, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(\"px-3 py-1 text-xs rounded-full font-medium \" + ctx_r19.getStatutClass(reunion_r20.statut));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(19, 28, reunion_r20.statut), \" \");\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate1(\" \", reunion_r20.createur.username, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", reunion_r20.participants.length > 0);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", reunion_r20.planning.titre, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", reunion_r20.lienVisio);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", reunion_r20.lieu || \"Lieu non sp\\u00E9cifi\\u00E9\", \" \");\n  }\n}\nfunction ReunionListComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtemplate(1, ReunionListComponent_div_16_div_1_Template, 50, 32, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.searchTerm || ctx_r6.selectedPlanning ? ctx_r6.filteredReunions : ctx_r6.reunions);\n  }\n}\nexport class ReunionListComponent {\n  // Propriété pour le titre de la page\n  get pageTitle() {\n    return this.authService.getCurrentUserRole() === 'admin' ? 'Toutes les Réunions' : 'Mes Réunions';\n  }\n  constructor(reunionService, router, authService, sanitizer, toastService) {\n    this.reunionService = reunionService;\n    this.router = router;\n    this.authService = authService;\n    this.sanitizer = sanitizer;\n    this.toastService = toastService;\n    this.reunions = [];\n    this.filteredReunions = [];\n    this.loading = true;\n    this.animateItems = false; // Contrôle l'animation des éléments de la liste\n    // Propriétés pour la recherche\n    this.showSearchBar = false;\n    this.searchTerm = '';\n    this.selectedPlanning = '';\n    this.uniquePlannings = [];\n  }\n  ngOnInit() {\n    this.loadReunions();\n    // Test du service de toast\n    console.log('🧪 Test du service de toast...');\n    // this.toastService.success('Test', 'Le service de toast fonctionne !');\n  }\n\n  ngAfterViewInit() {\n    // Activer les animations après un court délai pour permettre le rendu initial\n    setTimeout(() => {\n      this.animateItems = true;\n    }, 100);\n  }\n  /**\n   * Affiche ou masque la barre de recherche\n   */\n  toggleSearchBar() {\n    this.showSearchBar = !this.showSearchBar;\n    // Si on ferme la barre de recherche, réinitialiser les filtres\n    if (!this.showSearchBar) {\n      this.clearSearch();\n    }\n  }\n  /**\n   * Réinitialise les critères de recherche\n   */\n  clearSearch() {\n    this.searchTerm = '';\n    this.selectedPlanning = '';\n    this.searchReunions();\n  }\n  /**\n   * Filtre les réunions selon les critères de recherche\n   */\n  searchReunions() {\n    if (!this.searchTerm && !this.selectedPlanning) {\n      // Si aucun critère de recherche, afficher toutes les réunions\n      this.filteredReunions = [...this.reunions];\n      return;\n    }\n    // Filtrer les réunions selon les critères\n    this.filteredReunions = this.reunions.filter(reunion => {\n      // Vérifier le titre et la description si searchTerm est défini\n      const matchesSearchTerm = !this.searchTerm || reunion.titre && reunion.titre.toLowerCase().includes(this.searchTerm.toLowerCase()) || reunion.description && reunion.description.toLowerCase().includes(this.searchTerm.toLowerCase());\n      // Vérifier le planning si selectedPlanning est défini\n      const matchesPlanning = !this.selectedPlanning || reunion.planning && reunion.planning._id === this.selectedPlanning;\n      // La réunion doit correspondre aux deux critères (si définis)\n      return matchesSearchTerm && matchesPlanning;\n    });\n  }\n  loadReunions() {\n    this.loading = true;\n    this.animateItems = false; // Réinitialiser l'animation\n    const userId = this.authService.getCurrentUserId();\n    const userRole = this.authService.getCurrentUserRole();\n    if (!userId) {\n      this.error = \"Utilisateur non connecté\";\n      this.loading = false;\n      return;\n    }\n    // Si l'utilisateur est admin, récupérer toutes les réunions\n    // Sinon, récupérer seulement ses réunions\n    const reunionObservable = userRole === 'admin' ? this.reunionService.getAllReunionsAdmin() : this.reunionService.getProchainesReunions(userId);\n    reunionObservable.subscribe({\n      next: response => {\n        console.log('Réunions chargées:', response);\n        // Réinitialiser les erreurs\n        this.error = null;\n        // Attribuer les données après un court délai pour l'animation\n        setTimeout(() => {\n          // Récupérer les réunions selon la structure de réponse\n          let reunions = userRole === 'admin' ? response.data || response.reunions || [] : response.reunions || [];\n          console.log('Réunions récupérées pour admin:', reunions);\n          console.log('Structure de la première réunion:', reunions[0]);\n          // Pour le test : ajouter \"présence obligatoire\" à certaines réunions si aucune n'en a\n          reunions = this.ajouterPresenceObligatoirePourTest(reunions);\n          // Trier les réunions : celles avec \"Présence Obligatoire\" en premier\n          this.reunions = this.trierReunionsParPresenceObligatoire(reunions);\n          // Initialiser les réunions filtrées avec toutes les réunions\n          this.filteredReunions = [...this.reunions];\n          // Extraire les plannings uniques pour le filtre\n          this.extractUniquePlannings();\n          this.loading = false;\n          // Activer les animations après un court délai\n          setTimeout(() => {\n            this.animateItems = true;\n          }, 100);\n        }, 300); // Délai pour une meilleure expérience visuelle\n      },\n\n      error: error => {\n        console.error('Erreur détaillée:', JSON.stringify(error));\n        this.error = `Erreur lors du chargement des réunions: ${error.message || error.statusText || 'Erreur inconnue'}`;\n        this.loading = false;\n      }\n    });\n  }\n  getStatutClass(statut) {\n    switch (statut) {\n      case 'planifiee':\n        return 'bg-blue-100 text-blue-800';\n      case 'en_cours':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'terminee':\n        return 'bg-green-100 text-green-800';\n      case 'annulee':\n        return 'bg-red-100 text-red-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  }\n  editReunion(id) {\n    console.log(id);\n    if (this.reunions) {\n      this.router.navigate(['/reunions/modifier', id]);\n    }\n  }\n  /**\n   * Supprime une réunion après confirmation\n   * @param id ID de la réunion à supprimer\n   */\n  deleteReunion(id) {\n    console.log('🗑️ Tentative de suppression de la réunion avec ID:', id);\n    if (confirm('Êtes-vous sûr de vouloir supprimer cette réunion ?')) {\n      const userRole = this.authService.getCurrentUserRole();\n      console.log('👤 Rôle utilisateur:', userRole);\n      // Utiliser la méthode appropriée selon le rôle\n      const deleteObservable = userRole === 'admin' ? this.reunionService.forceDeleteReunion(id) : this.reunionService.deleteReunion(id);\n      console.log('🚀 Envoi de la requête de suppression...');\n      deleteObservable.subscribe({\n        next: response => {\n          console.log('✅ Réunion supprimée avec succès:', response);\n          this.handleSuccessfulDeletion(id);\n        },\n        error: error => {\n          console.error('❌ Erreur lors de la suppression:', error);\n          console.error('📋 Détails de l\\'erreur:', {\n            status: error.status,\n            statusText: error.statusText,\n            message: error.error?.message,\n            fullError: error\n          });\n          // Si c'est une erreur 200 mal interprétée ou une erreur de CORS,\n          // on considère que la suppression a réussi\n          if (error.status === 0 || error.status === 200) {\n            console.log('🔄 Erreur probablement liée à CORS ou réponse mal formatée, on considère la suppression comme réussie');\n            this.handleSuccessfulDeletion(id);\n            return;\n          }\n          // Pour les autres erreurs, on vérifie quand même si la suppression a eu lieu\n          // en rechargeant la liste après un délai\n          if (error.status >= 500) {\n            console.log('🔄 Erreur serveur, vérification de la suppression dans 2 secondes...');\n            setTimeout(() => {\n              this.loadReunions();\n            }, 2000);\n          }\n          // Gestion spécifique des erreurs d'autorisation\n          if (error.status === 403) {\n            this.toastService.accessDenied('supprimer cette réunion', error.status);\n          } else if (error.status === 401) {\n            this.toastService.error('Non autorisé', 'Vous devez être connecté pour supprimer une réunion');\n          } else {\n            const errorMessage = error.error?.message || 'Erreur lors de la suppression de la réunion';\n            this.toastService.error('Erreur de suppression', errorMessage, 8000);\n          }\n        }\n      });\n    } else {\n      console.log('❌ Suppression annulée par l\\'utilisateur');\n    }\n  }\n  handleSuccessfulDeletion(id) {\n    console.log('🎯 Traitement de la suppression réussie pour l\\'ID:', id);\n    // Retirer la réunion de la liste locale (gérer _id et id)\n    const initialCount = this.reunions.length;\n    this.reunions = this.reunions.filter(reunion => reunion._id !== id && reunion.id !== id);\n    this.filteredReunions = this.filteredReunions.filter(reunion => reunion._id !== id && reunion.id !== id);\n    const finalCount = this.reunions.length;\n    console.log(`📊 Réunions avant suppression: ${initialCount}, après: ${finalCount}`);\n    // Mettre à jour la liste des plannings uniques\n    this.extractUniquePlannings();\n    // Afficher le toast de succès\n    this.toastService.success('Réunion supprimée', 'La réunion a été supprimée avec succès');\n    console.log('🎉 Toast de succès affiché et liste mise à jour');\n    // Recharger complètement la liste pour s'assurer de la mise à jour\n    this.loadReunions();\n  }\n  formatDescription(description) {\n    if (!description) return this.sanitizer.bypassSecurityTrustHtml('');\n    // Recherche la chaîne \"(presence obligatoire)\" (insensible à la casse) et la remplace par une version en rouge\n    const formattedText = description.replace(/\\(presence obligatoire\\)/gi, '<span class=\"text-red-600 font-semibold\">(presence obligatoire)</span>');\n    // Sanitize le HTML pour éviter les problèmes de sécurité\n    return this.sanitizer.bypassSecurityTrustHtml(formattedText);\n  }\n  /**\n   * Vérifie si une réunion contient \"Présence Obligatoire\" dans sa description\n   * @param reunion La réunion à vérifier\n   * @returns true si la réunion a une présence obligatoire, false sinon\n   */\n  hasPresenceObligatoire(reunion) {\n    if (!reunion.description) return false;\n    // Recherche différentes variations de \"présence obligatoire\" (insensible à la casse)\n    const patterns = [/presence obligatoire/i, /présence obligatoire/i, /obligatoire/i, /\\(obligatoire\\)/i, /\\(presence obligatoire\\)/i, /\\(présence obligatoire\\)/i];\n    // Retourne true si l'une des expressions est trouvée\n    return patterns.some(pattern => pattern.test(reunion.description));\n  }\n  /**\n   * Trie les réunions en mettant celles avec \"Présence Obligatoire\" en premier\n   * @param reunions Liste des réunions à trier\n   * @returns Liste triée des réunions\n   */\n  trierReunionsParPresenceObligatoire(reunions) {\n    if (!reunions || !reunions.length) return [];\n    console.log('Avant tri - Nombre de réunions:', reunions.length);\n    // Vérifier chaque réunion pour la présence obligatoire\n    reunions.forEach((reunion, index) => {\n      const hasPresence = this.hasPresenceObligatoire(reunion);\n      console.log(`Réunion ${index + 1} - Titre: ${reunion.titre}, Description: ${reunion.description}, Présence Obligatoire: ${hasPresence}`);\n    });\n    // Trier les réunions : celles avec \"Présence Obligatoire\" en premier\n    const reunionsTriees = [...reunions].sort((a, b) => {\n      const aHasPresenceObligatoire = this.hasPresenceObligatoire(a);\n      const bHasPresenceObligatoire = this.hasPresenceObligatoire(b);\n      if (aHasPresenceObligatoire && !bHasPresenceObligatoire) {\n        return -1; // a vient avant b\n      }\n\n      if (!aHasPresenceObligatoire && bHasPresenceObligatoire) {\n        return 1; // b vient avant a\n      }\n      // Si les deux ont ou n'ont pas \"Présence Obligatoire\", trier par date\n      return new Date(b.date).getTime() - new Date(a.date).getTime();\n    });\n    console.log('Après tri - Ordre des réunions:');\n    reunionsTriees.forEach((reunion, index) => {\n      const hasPresence = this.hasPresenceObligatoire(reunion);\n      console.log(`Position ${index + 1} - Titre: ${reunion.titre}, Présence Obligatoire: ${hasPresence}`);\n    });\n    return reunionsTriees;\n  }\n  /**\n   * Méthode temporaire pour ajouter \"présence obligatoire\" à certaines réunions pour tester le tri\n   * @param reunions Liste des réunions\n   * @returns Liste des réunions avec certaines marquées comme \"présence obligatoire\"\n   */\n  /**\n   * Extrait les plannings uniques à partir des réunions pour le filtre\n   */\n  extractUniquePlannings() {\n    // Map pour stocker les plannings uniques par ID\n    const planningsMap = new Map();\n    // Parcourir toutes les réunions\n    this.reunions.forEach(reunion => {\n      if (reunion.planning && reunion.planning._id) {\n        // Ajouter le planning au Map s'il n'existe pas déjà\n        if (!planningsMap.has(reunion.planning._id)) {\n          planningsMap.set(reunion.planning._id, {\n            id: reunion.planning._id,\n            titre: reunion.planning.titre\n          });\n        }\n      }\n    });\n    // Convertir le Map en tableau\n    this.uniquePlannings = Array.from(planningsMap.values());\n    // Trier les plannings par titre\n    this.uniquePlannings.sort((a, b) => a.titre.localeCompare(b.titre));\n  }\n  /**\n   * Méthode temporaire pour ajouter \"présence obligatoire\" à certaines réunions pour tester le tri\n   */\n  ajouterPresenceObligatoirePourTest(reunions) {\n    if (!reunions || reunions.length === 0) return reunions;\n    // Vérifier si au moins une réunion a déjà \"présence obligatoire\"\n    const hasAnyPresenceObligatoire = reunions.some(reunion => this.hasPresenceObligatoire(reunion));\n    // Si aucune réunion n'a \"présence obligatoire\", en ajouter à certaines pour le test\n    if (!hasAnyPresenceObligatoire) {\n      console.log('Aucune réunion avec présence obligatoire trouvée, ajout pour le test...');\n      // Ajouter \"présence obligatoire\" à la première réunion si elle existe\n      if (reunions.length > 0) {\n        const reunion = reunions[0];\n        reunion.description = reunion.description ? reunion.description + ' (présence obligatoire)' : '(présence obligatoire)';\n        console.log(`Ajout de \"présence obligatoire\" à la réunion: ${reunion.titre}`);\n      }\n      // Si au moins 3 réunions, ajouter aussi à la troisième\n      if (reunions.length >= 3) {\n        const reunion = reunions[2];\n        reunion.description = reunion.description ? reunion.description + ' (présence obligatoire)' : '(présence obligatoire)';\n        console.log(`Ajout de \"présence obligatoire\" à la réunion: ${reunion.titre}`);\n      }\n    }\n    return reunions;\n  }\n  static {\n    this.ɵfac = function ReunionListComponent_Factory(t) {\n      return new (t || ReunionListComponent)(i0.ɵɵdirectiveInject(i1.ReunionService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.AuthuserService), i0.ɵɵdirectiveInject(i4.DomSanitizer), i0.ɵɵdirectiveInject(i5.ToastService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ReunionListComponent,\n      selectors: [[\"app-reunion-list\"]],\n      decls: 17,\n      vars: 8,\n      consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-6\", \"page-container\", \"page-enter\"], [1, \"flex\", \"flex-col\", \"mb-8\"], [1, \"flex\", \"justify-between\", \"items-center\"], [1, \"text-2xl\", \"font-bold\", \"text-gray-800\", \"page-title\"], [1, \"relative\"], [1, \"search-button\", \"px-4\", \"py-2\", \"bg-purple-200\", \"text-purple-800\", \"rounded-md\", \"hover:bg-purple-300\", \"transition-colors\", \"transform\", \"hover:scale-105\", \"duration-200\", \"flex\", \"items-center\", \"shadow-sm\", \"border\", \"border-purple-300\", 3, \"click\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"mr-2\", \"text-purple-600\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"], [\"class\", \"mt-4 bg-white p-4 rounded-lg shadow-md transition-all duration-300 animate-fadeIn\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"mt-2 text-sm text-gray-600 flex items-center\", 4, \"ngIf\"], [\"class\", \"mt-2 text-sm text-gray-600\", 4, \"ngIf\"], [\"class\", \"text-center py-12\", 4, \"ngIf\"], [\"class\", \"bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md mb-6 shadow-md transform transition-all duration-500 hover:shadow-lg\", 4, \"ngIf\"], [\"class\", \"text-center py-12 empty-container\", 3, \"animated\", 4, \"ngIf\"], [\"class\", \"grid grid-cols-1 md:grid-cols-2 gap-6\", 4, \"ngIf\"], [1, \"mt-4\", \"bg-white\", \"p-4\", \"rounded-lg\", \"shadow-md\", \"transition-all\", \"duration-300\", \"animate-fadeIn\", 3, \"ngClass\"], [1, \"flex\", \"flex-col\", \"md:flex-row\", \"gap-4\"], [1, \"flex-1\"], [1, \"flex\", \"items-center\"], [1, \"absolute\", \"left-3\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"text-purple-400\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\"], [\"type\", \"text\", \"id\", \"searchTerm\", \"placeholder\", \"Rechercher par titre ou description\", 1, \"w-full\", \"pl-10\", \"pr-10\", \"py-3\", \"border\", \"border-gray-300\", \"rounded-md\", \"focus:ring-2\", \"focus:ring-purple-300\", \"focus:border-purple-400\", \"transition-all\", \"duration-300\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [\"class\", \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-purple-600 transition-colors\", 3, \"click\", 4, \"ngIf\"], [1, \"md:w-1/3\"], [\"id\", \"planningFilter\", 1, \"w-full\", \"px-4\", \"py-3\", \"border\", \"border-gray-300\", \"rounded-md\", \"focus:ring-2\", \"focus:ring-purple-300\", \"focus:border-purple-400\", \"transition-all\", \"duration-300\", \"appearance-none\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"absolute\", \"inset-y-0\", \"right-0\", \"flex\", \"items-center\", \"pr-3\", \"pointer-events-none\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"text-purple-500\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 9l-7 7-7-7\"], [1, \"absolute\", \"right-3\", \"top-1/2\", \"transform\", \"-translate-y-1/2\", \"text-gray-400\", \"hover:text-purple-600\", \"transition-colors\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M6 18L18 6M6 6l12 12\"], [3, \"value\"], [1, \"mt-2\", \"text-sm\", \"text-gray-600\", \"flex\", \"items-center\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"mr-2\", \"text-red-500\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 10V3L4 14h7v7l9-11h-7z\"], [1, \"font-semibold\", \"text-red-600\"], [1, \"mt-2\", \"text-sm\", \"text-gray-600\"], [\"class\", \"text-red-500\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"text-red-500\"], [1, \"text-center\", \"py-12\"], [1, \"loading-spinner\", \"rounded-full\", \"h-16\", \"w-16\", \"border-4\", \"border-purple-200\", \"border-t-purple-600\", \"mx-auto\"], [1, \"mt-4\", \"text-gray-600\", \"animate-pulse\"], [1, \"bg-red-100\", \"border-l-4\", \"border-red-500\", \"text-red-700\", \"p-4\", \"rounded-md\", \"mb-6\", \"shadow-md\", \"transform\", \"transition-all\", \"duration-500\", \"hover:shadow-lg\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-6\", \"w-6\", \"mr-3\", \"text-red-500\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"text-center\", \"py-12\", \"empty-container\"], [1, \"bg-white\", \"rounded-lg\", \"shadow-md\", \"p-8\", \"max-w-md\", \"mx-auto\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"mx-auto\", \"h-16\", \"w-16\", \"text-purple-400\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"], [1, \"mt-4\", \"text-xl\", \"font-medium\", \"text-gray-900\"], [1, \"mt-2\", \"text-gray-500\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-6\"], [\"class\", \"bg-white rounded-lg shadow-md p-5 hover:shadow-xl transition-all duration-300 reunion-card\", 3, \"animated\", \"border-l-4\", \"border-red-500\", \"animation-delay\", 4, \"ngFor\", \"ngForOf\"], [1, \"bg-white\", \"rounded-lg\", \"shadow-md\", \"p-5\", \"hover:shadow-xl\", \"transition-all\", \"duration-300\", \"reunion-card\"], [1, \"flex\", \"justify-between\", \"items-start\"], [1, \"text-lg\", \"font-semibold\", \"text-gray-800\", \"hover:text-purple-600\", \"transition-colors\"], [1, \"hover:text-purple-600\", 3, \"routerLink\"], [\"class\", \"ml-2 px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full font-bold animate-pulse\", 4, \"ngIf\"], [1, \"text-sm\", \"mt-1\", 3, \"innerHTML\"], [1, \"mt-3\", \"flex\", \"items-center\", \"text-sm\", \"text-gray-500\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"mr-2\", \"text-purple-500\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"flex\", \"items-start\", \"space-x-2\"], [\"title\", \"Supprimer la r\\u00E9union\", 1, \"text-red-500\", \"hover:text-red-700\", \"transition-colors\", \"duration-300\", \"p-1\", \"rounded-full\", \"hover:bg-red-50\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"], [1, \"mt-3\", \"text-sm\", \"text-gray-600\", \"flex\", \"items-center\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"mr-2\", \"text-gray-500\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"], [\"class\", \"mt-3 text-sm text-gray-600\", 4, \"ngIf\"], [\"class\", \"mt-3 text-sm\", 4, \"ngIf\"], [1, \"mt-4\", \"pt-3\", \"border-t\", \"border-gray-100\", \"flex\", \"justify-between\", \"items-center\"], [1, \"flex\", \"items-center\", \"text-sm\", \"text-gray-500\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"mr-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [1, \"px-4\", \"py-2\", \"bg-purple-600\", \"text-white\", \"rounded-md\", \"hover:bg-purple-700\", \"transition-all\", \"duration-300\", \"transform\", \"hover:scale-105\", \"flex\", \"items-center\", 3, \"click\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"mr-1\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"], [1, \"ml-2\", \"px-2\", \"py-1\", \"text-xs\", \"bg-red-100\", \"text-red-800\", \"rounded-full\", \"font-bold\", \"animate-pulse\"], [1, \"mt-3\", \"text-sm\", \"text-gray-600\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"mt-3\", \"text-sm\"], [\"target\", \"_blank\", 1, \"text-purple-600\", \"hover:text-purple-800\", \"flex\", \"items-center\", \"transition-colors\", 3, \"href\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\"]],\n      template: function ReunionListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3);\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function ReunionListComponent_Template_button_click_6_listener() {\n            return ctx.toggleSearchBar();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(7, \"svg\", 6);\n          i0.ɵɵelement(8, \"path\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(9, \" Rechercher \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(10, ReunionListComponent_div_10_Template, 20, 7, \"div\", 8);\n          i0.ɵɵtemplate(11, ReunionListComponent_div_11_Template, 8, 0, \"div\", 9);\n          i0.ɵɵtemplate(12, ReunionListComponent_div_12_Template, 3, 2, \"div\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(13, ReunionListComponent_div_13_Template, 4, 0, \"div\", 11);\n          i0.ɵɵtemplate(14, ReunionListComponent_div_14_Template, 6, 1, \"div\", 12);\n          i0.ɵɵtemplate(15, ReunionListComponent_div_15_Template, 8, 2, \"div\", 13);\n          i0.ɵɵtemplate(16, ReunionListComponent_div_16_Template, 2, 1, \"div\", 14);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.pageTitle);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.showSearchBar);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.reunions.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.searchTerm || ctx.selectedPlanning);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.reunions.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.reunions.length > 0);\n        }\n      },\n      dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i2.RouterLink, i7.NgSelectOption, i7.ɵNgSelectMultipleOption, i7.DefaultValueAccessor, i7.SelectControlValueAccessor, i7.NgControlStatus, i7.NgModel, i6.TitleCasePipe, i6.DatePipe, i8.HighlightPresencePipe],\n      styles: [\"\\n\\n.page-container[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n}\\n\\n.page-title[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: inline-block;\\n}\\n\\n.page-title[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  width: 0;\\n  height: 3px;\\n  bottom: -5px;\\n  left: 0;\\n  background-color: #8b5cf6; \\n\\n  transition: width 0.6s ease;\\n}\\n\\n.page-title[_ngcontent-%COMP%]:hover::after {\\n  width: 100%;\\n}\\n\\n\\n\\n.reunion-card[_ngcontent-%COMP%] {\\n  transform: translateY(30px);\\n  opacity: 0;\\n  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.reunion-card.animated[_ngcontent-%COMP%] {\\n  transform: translateY(0);\\n  opacity: 1;\\n}\\n\\n\\n\\n.empty-container[_ngcontent-%COMP%] {\\n  transform: scale(0.8);\\n  opacity: 0;\\n  transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);\\n}\\n\\n.empty-container.animated[_ngcontent-%COMP%] {\\n  transform: scale(1);\\n  opacity: 1;\\n}\\n\\n\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_pulse 1.5s infinite ease-in-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    transform: scale(0.95);\\n    box-shadow: 0 0 0 0 rgba(139, 92, 246, 0.7);\\n  }\\n\\n  70% {\\n    transform: scale(1);\\n    box-shadow: 0 0 0 10px rgba(139, 92, 246, 0);\\n  }\\n\\n  100% {\\n    transform: scale(0.95);\\n    box-shadow: 0 0 0 0 rgba(139, 92, 246, 0);\\n  }\\n}\\n\\n\\n\\n.page-enter[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeInUp 0.8s forwards;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(40px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n\\n\\n.animate-fadeIn[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-in-out;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_slideInFromRight {\\n  0% {\\n    transform: translateX(30px);\\n    opacity: 0;\\n  }\\n  100% {\\n    transform: translateX(0);\\n    opacity: 1;\\n  }\\n}\\n\\n\\n\\n.flex-col[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%]:nth-child(1) {\\n  animation: _ngcontent-%COMP%_slideInFromRight 0.4s ease-out forwards;\\n}\\n\\n.flex-col[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%]:nth-child(2) {\\n  animation: _ngcontent-%COMP%_slideInFromRight 0.4s ease-out 0.1s forwards;\\n  opacity: 0;\\n}\\n\\n\\n\\ninput[_ngcontent-%COMP%], select[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n\\ninput[_ngcontent-%COMP%]:focus, select[_ngcontent-%COMP%]:focus {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 6px -1px rgba(139, 92, 246, 0.1), 0 2px 4px -1px rgba(139, 92, 246, 0.06);\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_gentle-pulse {\\n  0% {\\n    box-shadow: 0 0 0 0 rgba(167, 139, 250, 0.4);\\n  }\\n  70% {\\n    box-shadow: 0 0 0 6px rgba(167, 139, 250, 0);\\n  }\\n  100% {\\n    box-shadow: 0 0 0 0 rgba(167, 139, 250, 0);\\n  }\\n}\\n\\n.search-button[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_gentle-pulse 2s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n\\n\\n.search-input-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n  transition: all 0.3s ease;\\n}\\n\\n.search-input-container[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n}\\n\\n.search-input[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n  background: transparent;\\n  transition: all 0.3s ease;\\n}\\n\\n.search-input[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.3);\\n}\\n\\n\\n\\n.search-input-container[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: -100%;\\n  width: 100%;\\n  height: 100%;\\n  background: linear-gradient(\\n    90deg,\\n    transparent,\\n    rgba(139, 92, 246, 0.1),\\n    transparent\\n  );\\n  transition: all 0.6s ease;\\n  z-index: 0;\\n}\\n\\n.search-input-container[_ngcontent-%COMP%]:hover::before {\\n  left: 100%;\\n  transition: all 0.6s ease;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_subtlePulse {\\n  0% {\\n    box-shadow: 0 0 0 0 rgba(139, 92, 246, 0.2);\\n  }\\n  50% {\\n    box-shadow: 0 0 0 5px rgba(139, 92, 246, 0);\\n  }\\n  100% {\\n    box-shadow: 0 0 0 0 rgba(139, 92, 246, 0);\\n  }\\n}\\n\\n.search-input-container[_ngcontent-%COMP%]:focus-within {\\n  animation: _ngcontent-%COMP%_subtlePulse 2s infinite;\\n  border-color: #8b5cf6;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_rotateIcon {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  25% {\\n    transform: rotate(-10deg);\\n  }\\n  75% {\\n    transform: rotate(10deg);\\n  }\\n  100% {\\n    transform: rotate(0deg);\\n  }\\n}\\n\\n.search-icon[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n\\n.search-input-container[_ngcontent-%COMP%]:focus-within   .search-icon[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_rotateIcon 1s ease;\\n  color: #8b5cf6;\\n}\\n\\n\\n\\n.search-input[_ngcontent-%COMP%]::placeholder {\\n  transition: all 0.3s ease;\\n}\\n\\n.search-input[_ngcontent-%COMP%]:focus::placeholder {\\n  opacity: 0.5;\\n  transform: translateX(10px);\\n}\\n\\n\\n\\n.floating-label[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 12px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  font-size: 14px;\\n  color: #9ca3af;\\n  pointer-events: none;\\n  transition: all 0.3s ease;\\n  z-index: 2;\\n}\\n\\n.search-input[_ngcontent-%COMP%]:focus    ~ .floating-label[_ngcontent-%COMP%], .search-input[_ngcontent-%COMP%]:not(:placeholder-shown)    ~ .floating-label[_ngcontent-%COMP%] {\\n  top: 0;\\n  left: 8px;\\n  font-size: 12px;\\n  padding: 0 4px;\\n  background-color: white;\\n  color: #8b5cf6;\\n  transform: translateY(-50%);\\n}\\n\\n\\n\\n.search-select[_ngcontent-%COMP%] {\\n  position: relative;\\n  transition: all 0.3s ease;\\n  background-image: linear-gradient(to right, #f9fafb 0%, white 100%);\\n}\\n\\n.search-select[_ngcontent-%COMP%]:hover {\\n  background-image: linear-gradient(to right, #f3f4f6 0%, white 100%);\\n}\\n\\n.search-select[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.3);\\n  background-image: linear-gradient(to right, #f3f4f6 0%, white 100%);\\n}\\n\\n\\n\\n.staggered-item[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  transform: translateY(20px);\\n}\\n\\n.staggered-item.animated[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeInStaggered 0.5s forwards;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInStaggered {\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vcmV1bmlvbnMvcmV1bmlvbi1saXN0L3JldW5pb24tbGlzdC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLHlDQUF5QztBQUN6QztFQUNFLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLGtCQUFrQjtFQUNsQixxQkFBcUI7QUFDdkI7O0FBRUE7RUFDRSxXQUFXO0VBQ1gsa0JBQWtCO0VBQ2xCLFFBQVE7RUFDUixXQUFXO0VBQ1gsWUFBWTtFQUNaLE9BQU87RUFDUCx5QkFBeUIsRUFBRSxXQUFXO0VBQ3RDLDJCQUEyQjtBQUM3Qjs7QUFFQTtFQUNFLFdBQVc7QUFDYjs7QUFFQSx5Q0FBeUM7QUFDekM7RUFDRSwyQkFBMkI7RUFDM0IsVUFBVTtFQUNWLGlEQUFpRDtBQUNuRDs7QUFFQTtFQUNFLHdCQUF3QjtFQUN4QixVQUFVO0FBQ1o7O0FBRUEscUNBQXFDO0FBQ3JDO0VBQ0UscUJBQXFCO0VBQ3JCLFVBQVU7RUFDVixzREFBc0Q7QUFDeEQ7O0FBRUE7RUFDRSxtQkFBbUI7RUFDbkIsVUFBVTtBQUNaOztBQUVBLDRDQUE0QztBQUM1QztFQUNFLDBDQUEwQztBQUM1Qzs7QUFFQTtFQUNFO0lBQ0Usc0JBQXNCO0lBQ3RCLDJDQUEyQztFQUM3Qzs7RUFFQTtJQUNFLG1CQUFtQjtJQUNuQiw0Q0FBNEM7RUFDOUM7O0VBRUE7SUFDRSxzQkFBc0I7SUFDdEIseUNBQXlDO0VBQzNDO0FBQ0Y7O0FBRUEsdUNBQXVDO0FBQ3ZDO0VBQ0UsaUNBQWlDO0FBQ25DOztBQUVBO0VBQ0U7SUFDRSxVQUFVO0lBQ1YsMkJBQTJCO0VBQzdCO0VBQ0E7SUFDRSxVQUFVO0lBQ1Ysd0JBQXdCO0VBQzFCO0FBQ0Y7O0FBRUEseUNBQXlDO0FBQ3pDO0VBQ0Usa0NBQWtDO0FBQ3BDOztBQUVBLCtFQUErRTtBQUMvRTtFQUNFO0lBQ0UsMkJBQTJCO0lBQzNCLFVBQVU7RUFDWjtFQUNBO0lBQ0Usd0JBQXdCO0lBQ3hCLFVBQVU7RUFDWjtBQUNGOztBQUVBLDJDQUEyQztBQUMzQztFQUNFLGtEQUFrRDtBQUNwRDs7QUFFQTtFQUNFLHVEQUF1RDtFQUN2RCxVQUFVO0FBQ1o7O0FBRUEsOEJBQThCO0FBQzlCO0VBQ0UseUJBQXlCO0FBQzNCOztBQUVBO0VBQ0UsMkJBQTJCO0VBQzNCLDJGQUEyRjtBQUM3Rjs7QUFFQSwwQ0FBMEM7QUFDMUM7RUFDRTtJQUNFLDRDQUE0QztFQUM5QztFQUNBO0lBQ0UsNENBQTRDO0VBQzlDO0VBQ0E7SUFDRSwwQ0FBMEM7RUFDNUM7QUFDRjs7QUFFQTtFQUNFLG1DQUFtQztBQUNyQzs7QUFFQTtFQUNFO0lBQ0UsVUFBVTtJQUNWLDRCQUE0QjtFQUM5QjtFQUNBO0lBQ0UsVUFBVTtJQUNWLHdCQUF3QjtFQUMxQjtBQUNGOztBQUVBLDRDQUE0QztBQUM1QztFQUNFLGtCQUFrQjtFQUNsQixnQkFBZ0I7RUFDaEIseUJBQXlCO0FBQzNCOztBQUVBO0VBQ0UsMkJBQTJCO0FBQzdCOztBQUVBO0VBQ0Usa0JBQWtCO0VBQ2xCLFVBQVU7RUFDVix1QkFBdUI7RUFDdkIseUJBQXlCO0FBQzNCOztBQUVBO0VBQ0UsNkNBQTZDO0FBQy9DOztBQUVBLGdDQUFnQztBQUNoQztFQUNFLFdBQVc7RUFDWCxrQkFBa0I7RUFDbEIsTUFBTTtFQUNOLFdBQVc7RUFDWCxXQUFXO0VBQ1gsWUFBWTtFQUNaOzs7OztHQUtDO0VBQ0QseUJBQXlCO0VBQ3pCLFVBQVU7QUFDWjs7QUFFQTtFQUNFLFVBQVU7RUFDVix5QkFBeUI7QUFDM0I7O0FBRUEsbUNBQW1DO0FBQ25DO0VBQ0U7SUFDRSwyQ0FBMkM7RUFDN0M7RUFDQTtJQUNFLDJDQUEyQztFQUM3QztFQUNBO0lBQ0UseUNBQXlDO0VBQzNDO0FBQ0Y7O0FBRUE7RUFDRSxrQ0FBa0M7RUFDbEMscUJBQXFCO0FBQ3ZCOztBQUVBLHNDQUFzQztBQUN0QztFQUNFO0lBQ0UsdUJBQXVCO0VBQ3pCO0VBQ0E7SUFDRSx5QkFBeUI7RUFDM0I7RUFDQTtJQUNFLHdCQUF3QjtFQUMxQjtFQUNBO0lBQ0UsdUJBQXVCO0VBQ3pCO0FBQ0Y7O0FBRUE7RUFDRSx5QkFBeUI7QUFDM0I7O0FBRUE7RUFDRSw2QkFBNkI7RUFDN0IsY0FBYztBQUNoQjs7QUFFQSw2QkFBNkI7QUFDN0I7RUFDRSx5QkFBeUI7QUFDM0I7O0FBRUE7RUFDRSxZQUFZO0VBQ1osMkJBQTJCO0FBQzdCOztBQUVBLGdDQUFnQztBQUNoQztFQUNFLGtCQUFrQjtFQUNsQixVQUFVO0VBQ1YsUUFBUTtFQUNSLDJCQUEyQjtFQUMzQixlQUFlO0VBQ2YsY0FBYztFQUNkLG9CQUFvQjtFQUNwQix5QkFBeUI7RUFDekIsVUFBVTtBQUNaOztBQUVBOztFQUVFLE1BQU07RUFDTixTQUFTO0VBQ1QsZUFBZTtFQUNmLGNBQWM7RUFDZCx1QkFBdUI7RUFDdkIsY0FBYztFQUNkLDJCQUEyQjtBQUM3Qjs7QUFFQSw2QkFBNkI7QUFDN0I7RUFDRSxrQkFBa0I7RUFDbEIseUJBQXlCO0VBQ3pCLG1FQUFtRTtBQUNyRTs7QUFFQTtFQUNFLG1FQUFtRTtBQUNyRTs7QUFFQTtFQUNFLDZDQUE2QztFQUM3QyxtRUFBbUU7QUFDckU7O0FBRUEsNENBQTRDO0FBQzVDO0VBQ0UsVUFBVTtFQUNWLDJCQUEyQjtBQUM3Qjs7QUFFQTtFQUNFLHdDQUF3QztBQUMxQzs7QUFFQTtFQUNFO0lBQ0UsVUFBVTtJQUNWLHdCQUF3QjtFQUMxQjtBQUNGO0FBQ0EsZ3ZYQUFndlgiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBBbmltYXRpb25zIHBvdXIgbGEgcGFnZSBkZXMgcsODwql1bmlvbnMgKi9cclxuLnBhZ2UtY29udGFpbmVyIHtcclxuICBvdmVyZmxvdzogaGlkZGVuO1xyXG59XHJcblxyXG4ucGFnZS10aXRsZSB7XHJcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gIGRpc3BsYXk6IGlubGluZS1ibG9jaztcclxufVxyXG5cclxuLnBhZ2UtdGl0bGU6OmFmdGVyIHtcclxuICBjb250ZW50OiAnJztcclxuICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgd2lkdGg6IDA7XHJcbiAgaGVpZ2h0OiAzcHg7XHJcbiAgYm90dG9tOiAtNXB4O1xyXG4gIGxlZnQ6IDA7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogIzhiNWNmNjsgLyogVmlvbGV0ICovXHJcbiAgdHJhbnNpdGlvbjogd2lkdGggMC42cyBlYXNlO1xyXG59XHJcblxyXG4ucGFnZS10aXRsZTpob3Zlcjo6YWZ0ZXIge1xyXG4gIHdpZHRoOiAxMDAlO1xyXG59XHJcblxyXG4vKiBBbmltYXRpb24gcG91ciBsZXMgY2FydGVzIGRlIHLDg8KpdW5pb24gKi9cclxuLnJldW5pb24tY2FyZCB7XHJcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDMwcHgpO1xyXG4gIG9wYWNpdHk6IDA7XHJcbiAgdHJhbnNpdGlvbjogYWxsIDAuNXMgY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKTtcclxufVxyXG5cclxuLnJldW5pb24tY2FyZC5hbmltYXRlZCB7XHJcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApO1xyXG4gIG9wYWNpdHk6IDE7XHJcbn1cclxuXHJcbi8qIEFuaW1hdGlvbiBwb3VyIGxlIGNvbnRlbmV1ciB2aWRlICovXHJcbi5lbXB0eS1jb250YWluZXIge1xyXG4gIHRyYW5zZm9ybTogc2NhbGUoMC44KTtcclxuICBvcGFjaXR5OiAwO1xyXG4gIHRyYW5zaXRpb246IGFsbCAwLjZzIGN1YmljLWJlemllcigwLjM0LCAxLjU2LCAwLjY0LCAxKTtcclxufVxyXG5cclxuLmVtcHR5LWNvbnRhaW5lci5hbmltYXRlZCB7XHJcbiAgdHJhbnNmb3JtOiBzY2FsZSgxKTtcclxuICBvcGFjaXR5OiAxO1xyXG59XHJcblxyXG4vKiBBbmltYXRpb24gcG91ciBsZSBzcGlubmVyIGRlIGNoYXJnZW1lbnQgKi9cclxuLmxvYWRpbmctc3Bpbm5lciB7XHJcbiAgYW5pbWF0aW9uOiBwdWxzZSAxLjVzIGluZmluaXRlIGVhc2UtaW4tb3V0O1xyXG59XHJcblxyXG5Aa2V5ZnJhbWVzIHB1bHNlIHtcclxuICAwJSB7XHJcbiAgICB0cmFuc2Zvcm06IHNjYWxlKDAuOTUpO1xyXG4gICAgYm94LXNoYWRvdzogMCAwIDAgMCByZ2JhKDEzOSwgOTIsIDI0NiwgMC43KTtcclxuICB9XHJcblxyXG4gIDcwJSB7XHJcbiAgICB0cmFuc2Zvcm06IHNjYWxlKDEpO1xyXG4gICAgYm94LXNoYWRvdzogMCAwIDAgMTBweCByZ2JhKDEzOSwgOTIsIDI0NiwgMCk7XHJcbiAgfVxyXG5cclxuICAxMDAlIHtcclxuICAgIHRyYW5zZm9ybTogc2NhbGUoMC45NSk7XHJcbiAgICBib3gtc2hhZG93OiAwIDAgMCAwIHJnYmEoMTM5LCA5MiwgMjQ2LCAwKTtcclxuICB9XHJcbn1cclxuXHJcbi8qIEFuaW1hdGlvbiBwb3VyIGwnZW50csODwqllIGRlIGxhIHBhZ2UgKi9cclxuLnBhZ2UtZW50ZXIge1xyXG4gIGFuaW1hdGlvbjogZmFkZUluVXAgMC44cyBmb3J3YXJkcztcclxufVxyXG5cclxuQGtleWZyYW1lcyBmYWRlSW5VcCB7XHJcbiAgZnJvbSB7XHJcbiAgICBvcGFjaXR5OiAwO1xyXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDQwcHgpO1xyXG4gIH1cclxuICB0byB7XHJcbiAgICBvcGFjaXR5OiAxO1xyXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApO1xyXG4gIH1cclxufVxyXG5cclxuLyogQW5pbWF0aW9uIHBvdXIgbGEgYmFycmUgZGUgcmVjaGVyY2hlICovXHJcbi5hbmltYXRlLWZhZGVJbiB7XHJcbiAgYW5pbWF0aW9uOiBmYWRlSW4gMC4zcyBlYXNlLWluLW91dDtcclxufVxyXG5cclxuLyogQW5pbWF0aW9uIHBvdXIgbGVzIGNoYW1wcyBkZSByZWNoZXJjaGUgcXVpIGFwcGFyYWlzc2VudCBsJ3VuIGFwcsODwqhzIGwnYXV0cmUgKi9cclxuQGtleWZyYW1lcyBzbGlkZUluRnJvbVJpZ2h0IHtcclxuICAwJSB7XHJcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoMzBweCk7XHJcbiAgICBvcGFjaXR5OiAwO1xyXG4gIH1cclxuICAxMDAlIHtcclxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgwKTtcclxuICAgIG9wYWNpdHk6IDE7XHJcbiAgfVxyXG59XHJcblxyXG4vKiBBbmltYXRpb24gcG91ciBsZXMgY2hhbXBzIGRlIHJlY2hlcmNoZSAqL1xyXG4uZmxleC1jb2wgPiBkaXY6bnRoLWNoaWxkKDEpIHtcclxuICBhbmltYXRpb246IHNsaWRlSW5Gcm9tUmlnaHQgMC40cyBlYXNlLW91dCBmb3J3YXJkcztcclxufVxyXG5cclxuLmZsZXgtY29sID4gZGl2Om50aC1jaGlsZCgyKSB7XHJcbiAgYW5pbWF0aW9uOiBzbGlkZUluRnJvbVJpZ2h0IDAuNHMgZWFzZS1vdXQgMC4xcyBmb3J3YXJkcztcclxuICBvcGFjaXR5OiAwO1xyXG59XHJcblxyXG4vKiBBbmltYXRpb24gcG91ciBsZXMgaW5wdXRzICovXHJcbmlucHV0LCBzZWxlY3Qge1xyXG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XHJcbn1cclxuXHJcbmlucHV0OmZvY3VzLCBzZWxlY3Q6Zm9jdXMge1xyXG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTtcclxuICBib3gtc2hhZG93OiAwIDRweCA2cHggLTFweCByZ2JhKDEzOSwgOTIsIDI0NiwgMC4xKSwgMCAycHggNHB4IC0xcHggcmdiYSgxMzksIDkyLCAyNDYsIDAuMDYpO1xyXG59XHJcblxyXG4vKiBBbmltYXRpb24gcG91ciBsZSBib3V0b24gZGUgcmVjaGVyY2hlICovXHJcbkBrZXlmcmFtZXMgZ2VudGxlLXB1bHNlIHtcclxuICAwJSB7XHJcbiAgICBib3gtc2hhZG93OiAwIDAgMCAwIHJnYmEoMTY3LCAxMzksIDI1MCwgMC40KTtcclxuICB9XHJcbiAgNzAlIHtcclxuICAgIGJveC1zaGFkb3c6IDAgMCAwIDZweCByZ2JhKDE2NywgMTM5LCAyNTAsIDApO1xyXG4gIH1cclxuICAxMDAlIHtcclxuICAgIGJveC1zaGFkb3c6IDAgMCAwIDAgcmdiYSgxNjcsIDEzOSwgMjUwLCAwKTtcclxuICB9XHJcbn1cclxuXHJcbi5zZWFyY2gtYnV0dG9uIHtcclxuICBhbmltYXRpb246IGdlbnRsZS1wdWxzZSAycyBpbmZpbml0ZTtcclxufVxyXG5cclxuQGtleWZyYW1lcyBmYWRlSW4ge1xyXG4gIGZyb20ge1xyXG4gICAgb3BhY2l0eTogMDtcclxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMTBweCk7XHJcbiAgfVxyXG4gIHRvIHtcclxuICAgIG9wYWNpdHk6IDE7XHJcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMCk7XHJcbiAgfVxyXG59XHJcblxyXG4vKiBBbmltYXRpb25zIHBvdXIgbGVzIGNoYW1wcyBkZSByZWNoZXJjaGUgKi9cclxuLnNlYXJjaC1pbnB1dC1jb250YWluZXIge1xyXG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICBvdmVyZmxvdzogaGlkZGVuO1xyXG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XHJcbn1cclxuXHJcbi5zZWFyY2gtaW5wdXQtY29udGFpbmVyOmhvdmVyIHtcclxuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XHJcbn1cclxuXHJcbi5zZWFyY2gtaW5wdXQge1xyXG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICB6LWluZGV4OiAxO1xyXG4gIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50O1xyXG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XHJcbn1cclxuXHJcbi5zZWFyY2gtaW5wdXQ6Zm9jdXMge1xyXG4gIGJveC1zaGFkb3c6IDAgMCAwIDNweCByZ2JhKDEzOSwgOTIsIDI0NiwgMC4zKTtcclxufVxyXG5cclxuLyogQW5pbWF0aW9uIGRlIGwnZWZmZXQgZCdvbmRlICovXHJcbi5zZWFyY2gtaW5wdXQtY29udGFpbmVyOjpiZWZvcmUge1xyXG4gIGNvbnRlbnQ6ICcnO1xyXG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICB0b3A6IDA7XHJcbiAgbGVmdDogLTEwMCU7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbiAgaGVpZ2h0OiAxMDAlO1xyXG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudChcclxuICAgIDkwZGVnLFxyXG4gICAgdHJhbnNwYXJlbnQsXHJcbiAgICByZ2JhKDEzOSwgOTIsIDI0NiwgMC4xKSxcclxuICAgIHRyYW5zcGFyZW50XHJcbiAgKTtcclxuICB0cmFuc2l0aW9uOiBhbGwgMC42cyBlYXNlO1xyXG4gIHotaW5kZXg6IDA7XHJcbn1cclxuXHJcbi5zZWFyY2gtaW5wdXQtY29udGFpbmVyOmhvdmVyOjpiZWZvcmUge1xyXG4gIGxlZnQ6IDEwMCU7XHJcbiAgdHJhbnNpdGlvbjogYWxsIDAuNnMgZWFzZTtcclxufVxyXG5cclxuLyogQW5pbWF0aW9uIGRlIHB1bHNhdGlvbiBzdWJ0aWxlICovXHJcbkBrZXlmcmFtZXMgc3VidGxlUHVsc2Uge1xyXG4gIDAlIHtcclxuICAgIGJveC1zaGFkb3c6IDAgMCAwIDAgcmdiYSgxMzksIDkyLCAyNDYsIDAuMik7XHJcbiAgfVxyXG4gIDUwJSB7XHJcbiAgICBib3gtc2hhZG93OiAwIDAgMCA1cHggcmdiYSgxMzksIDkyLCAyNDYsIDApO1xyXG4gIH1cclxuICAxMDAlIHtcclxuICAgIGJveC1zaGFkb3c6IDAgMCAwIDAgcmdiYSgxMzksIDkyLCAyNDYsIDApO1xyXG4gIH1cclxufVxyXG5cclxuLnNlYXJjaC1pbnB1dC1jb250YWluZXI6Zm9jdXMtd2l0aGluIHtcclxuICBhbmltYXRpb246IHN1YnRsZVB1bHNlIDJzIGluZmluaXRlO1xyXG4gIGJvcmRlci1jb2xvcjogIzhiNWNmNjtcclxufVxyXG5cclxuLyogQW5pbWF0aW9uIGRlIGwnaWPDg8K0bmUgZGUgcmVjaGVyY2hlICovXHJcbkBrZXlmcmFtZXMgcm90YXRlSWNvbiB7XHJcbiAgMCUge1xyXG4gICAgdHJhbnNmb3JtOiByb3RhdGUoMGRlZyk7XHJcbiAgfVxyXG4gIDI1JSB7XHJcbiAgICB0cmFuc2Zvcm06IHJvdGF0ZSgtMTBkZWcpO1xyXG4gIH1cclxuICA3NSUge1xyXG4gICAgdHJhbnNmb3JtOiByb3RhdGUoMTBkZWcpO1xyXG4gIH1cclxuICAxMDAlIHtcclxuICAgIHRyYW5zZm9ybTogcm90YXRlKDBkZWcpO1xyXG4gIH1cclxufVxyXG5cclxuLnNlYXJjaC1pY29uIHtcclxuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xyXG59XHJcblxyXG4uc2VhcmNoLWlucHV0LWNvbnRhaW5lcjpmb2N1cy13aXRoaW4gLnNlYXJjaC1pY29uIHtcclxuICBhbmltYXRpb246IHJvdGF0ZUljb24gMXMgZWFzZTtcclxuICBjb2xvcjogIzhiNWNmNjtcclxufVxyXG5cclxuLyogQW5pbWF0aW9uIGR1IHBsYWNlaG9sZGVyICovXHJcbi5zZWFyY2gtaW5wdXQ6OnBsYWNlaG9sZGVyIHtcclxuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xyXG59XHJcblxyXG4uc2VhcmNoLWlucHV0OmZvY3VzOjpwbGFjZWhvbGRlciB7XHJcbiAgb3BhY2l0eTogMC41O1xyXG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgxMHB4KTtcclxufVxyXG5cclxuLyogQW5pbWF0aW9uIGR1IGxhYmVsIGZsb3R0YW50ICovXHJcbi5mbG9hdGluZy1sYWJlbCB7XHJcbiAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gIGxlZnQ6IDEycHg7XHJcbiAgdG9wOiA1MCU7XHJcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01MCUpO1xyXG4gIGZvbnQtc2l6ZTogMTRweDtcclxuICBjb2xvcjogIzljYTNhZjtcclxuICBwb2ludGVyLWV2ZW50czogbm9uZTtcclxuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xyXG4gIHotaW5kZXg6IDI7XHJcbn1cclxuXHJcbi5zZWFyY2gtaW5wdXQ6Zm9jdXMgfiAuZmxvYXRpbmctbGFiZWwsXHJcbi5zZWFyY2gtaW5wdXQ6bm90KDpwbGFjZWhvbGRlci1zaG93bikgfiAuZmxvYXRpbmctbGFiZWwge1xyXG4gIHRvcDogMDtcclxuICBsZWZ0OiA4cHg7XHJcbiAgZm9udC1zaXplOiAxMnB4O1xyXG4gIHBhZGRpbmc6IDAgNHB4O1xyXG4gIGJhY2tncm91bmQtY29sb3I6IHdoaXRlO1xyXG4gIGNvbG9yOiAjOGI1Y2Y2O1xyXG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNTAlKTtcclxufVxyXG5cclxuLyogQW5pbWF0aW9uIHBvdXIgbGUgc2VsZWN0ICovXHJcbi5zZWFyY2gtc2VsZWN0IHtcclxuICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxuICBiYWNrZ3JvdW5kLWltYWdlOiBsaW5lYXItZ3JhZGllbnQodG8gcmlnaHQsICNmOWZhZmIgMCUsIHdoaXRlIDEwMCUpO1xyXG59XHJcblxyXG4uc2VhcmNoLXNlbGVjdDpob3ZlciB7XHJcbiAgYmFja2dyb3VuZC1pbWFnZTogbGluZWFyLWdyYWRpZW50KHRvIHJpZ2h0LCAjZjNmNGY2IDAlLCB3aGl0ZSAxMDAlKTtcclxufVxyXG5cclxuLnNlYXJjaC1zZWxlY3Q6Zm9jdXMge1xyXG4gIGJveC1zaGFkb3c6IDAgMCAwIDNweCByZ2JhKDEzOSwgOTIsIDI0NiwgMC4zKTtcclxuICBiYWNrZ3JvdW5kLWltYWdlOiBsaW5lYXItZ3JhZGllbnQodG8gcmlnaHQsICNmM2Y0ZjYgMCUsIHdoaXRlIDEwMCUpO1xyXG59XHJcblxyXG4vKiBBbmltYXRpb24gcG91ciBsZXMgw4PCqWzDg8KpbWVudHMgZGUgbGEgbGlzdGUgKi9cclxuLnN0YWdnZXJlZC1pdGVtIHtcclxuICBvcGFjaXR5OiAwO1xyXG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgyMHB4KTtcclxufVxyXG5cclxuLnN0YWdnZXJlZC1pdGVtLmFuaW1hdGVkIHtcclxuICBhbmltYXRpb246IGZhZGVJblN0YWdnZXJlZCAwLjVzIGZvcndhcmRzO1xyXG59XHJcblxyXG5Aa2V5ZnJhbWVzIGZhZGVJblN0YWdnZXJlZCB7XHJcbiAgdG8ge1xyXG4gICAgb3BhY2l0eTogMTtcclxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgwKTtcclxuICB9XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"],\n      data: {\n        animation: [trigger('fadeIn', [transition(':enter', [style({\n          opacity: 0,\n          transform: 'translateY(20px)'\n        }), animate('0.4s ease-out', style({\n          opacity: 1,\n          transform: 'translateY(0)'\n        }))])]), trigger('staggerList', [transition('* => *', [query(':enter', [style({\n          opacity: 0,\n          transform: 'translateY(30px)'\n        }), stagger('100ms', [animate('0.5s ease-out', style({\n          opacity: 1,\n          transform: 'translateY(0)'\n        }))])], {\n          optional: true\n        })])])]\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["trigger", "transition", "style", "animate", "query", "stagger", "i0", "ɵɵelementStart", "ɵɵlistener", "ReunionListComponent_div_10_button_9_Template_button_click_0_listener", "ɵɵrestoreView", "_r10", "ctx_r9", "ɵɵnextContext", "ɵɵresetView", "clearSearch", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵproperty", "planning_r11", "id", "ɵɵadvance", "ɵɵtextInterpolate", "titre", "ɵɵnamespaceHTML", "ReunionListComponent_div_10_Template_input_ngModelChange_8_listener", "$event", "_r13", "ctx_r12", "searchTerm", "ReunionListComponent_div_10_Template_input_input_8_listener", "ctx_r14", "searchReunions", "ɵɵtemplate", "ReunionListComponent_div_10_button_9_Template", "ReunionListComponent_div_10_Template_select_ngModelChange_13_listener", "ctx_r15", "selectedPlanning", "ReunionListComponent_div_10_Template_select_change_13_listener", "ctx_r16", "ReunionListComponent_div_10_option_16_Template", "ɵɵpureFunction1", "_c0", "ctx_r0", "showSearchBar", "uniquePlannings", "ɵɵtextInterpolate1", "ctx_r18", "filteredReunions", "length", "ReunionListComponent_div_12_span_1_Template", "ReunionListComponent_div_12_span_2_Template", "ctx_r2", "ctx_r4", "error", "message", "ɵɵclassProp", "ctx_r5", "loading", "reunion_r20", "participants", "ɵɵpropertyInterpolate", "lienVisio", "ɵɵsanitizeUrl", "ReunionListComponent_div_16_div_1_span_7_Template", "ReunionListComponent_div_16_div_1_Template_button_click_20_listener", "restoredCtx", "_r28", "$implicit", "ctx_r27", "deleteReunion", "_id", "stopPropagation", "ReunionListComponent_div_16_div_1_div_30_Template", "ReunionListComponent_div_16_div_1_div_38_Template", "ReunionListComponent_div_16_div_1_Template_a_click_46_listener", "ctx_r29", "editReunion", "ɵɵstyleProp", "i_r21", "ctx_r19", "animateItems", "hasPresenceObligatoire", "_c1", "ɵɵpipeBind1", "description", "ɵɵsanitizeHtml", "ɵɵtextInterpolate3", "ɵɵpipeBind2", "date", "heureDebut", "heure<PERSON>in", "ɵɵclassMap", "getStatutClass", "statut", "<PERSON>ur", "username", "planning", "lieu", "ReunionListComponent_div_16_div_1_Template", "ctx_r6", "reunions", "ReunionListComponent", "pageTitle", "authService", "getCurrentUserRole", "constructor", "reunionService", "router", "sanitizer", "toastService", "ngOnInit", "loadReunions", "console", "log", "ngAfterViewInit", "setTimeout", "toggleSearchBar", "filter", "reunion", "matchesSearchTerm", "toLowerCase", "includes", "matchesPlanning", "userId", "getCurrentUserId", "userRole", "reunionObservable", "getAllReunionsAdmin", "getProchainesReunions", "subscribe", "next", "response", "data", "ajouterPresenceObligatoirePourTest", "trierReunionsParPresenceObligatoire", "extractUniquePlannings", "JSON", "stringify", "statusText", "navigate", "confirm", "deleteObservable", "forceDeleteReunion", "handleSuccessfulDeletion", "status", "fullError", "accessDenied", "errorMessage", "initialCount", "finalCount", "success", "formatDescription", "bypassSecurityTrustHtml", "formattedText", "replace", "patterns", "some", "pattern", "test", "for<PERSON>ach", "index", "hasPresence", "reunionsTriees", "sort", "a", "b", "aHasPresenceObligatoire", "bHasPresenceObligatoire", "Date", "getTime", "planningsMap", "Map", "has", "set", "Array", "from", "values", "localeCompare", "hasAnyPresenceObligatoire", "ɵɵdirectiveInject", "i1", "ReunionService", "i2", "Router", "i3", "AuthuserService", "i4", "Dom<PERSON><PERSON><PERSON>zer", "i5", "ToastService", "selectors", "decls", "vars", "consts", "template", "ReunionListComponent_Template", "rf", "ctx", "ReunionListComponent_Template_button_click_6_listener", "ReunionListComponent_div_10_Template", "ReunionListComponent_div_11_Template", "ReunionListComponent_div_12_Template", "ReunionListComponent_div_13_Template", "ReunionListComponent_div_14_Template", "ReunionListComponent_div_15_Template", "ReunionListComponent_div_16_Template", "opacity", "transform", "optional"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\reunions\\reunion-list\\reunion-list.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\reunions\\reunion-list\\reunion-list.component.html"], "sourcesContent": ["import { Component, OnInit, AfterViewInit } from '@angular/core';\nimport { ReunionService }  from 'src/app/services/reunion.service';\nimport { Reunion } from 'src/app/models/reunion.model';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport { Router } from \"@angular/router\";\nimport { DomSanitizer, SafeHtml } from '@angular/platform-browser';\nimport { trigger, transition, style, animate, query, stagger } from '@angular/animations';\nimport { ToastService } from 'src/app/services/toast.service';\n\n@Component({\n  selector: 'app-reunion-list',\n  templateUrl: './reunion-list.component.html',\n  styleUrls: ['./reunion-list.component.css'],\n  animations: [\n    trigger('fadeIn', [\n      transition(':enter', [\n        style({ opacity: 0, transform: 'translateY(20px)' }),\n        animate('0.4s ease-out', style({ opacity: 1, transform: 'translateY(0)' }))\n      ])\n    ]),\n    trigger('staggerList', [\n      transition('* => *', [\n        query(':enter', [\n          style({ opacity: 0, transform: 'translateY(30px)' }),\n          stagger('100ms', [\n            animate('0.5s ease-out', style({ opacity: 1, transform: 'translateY(0)' }))\n          ])\n        ], { optional: true })\n      ])\n    ])\n  ]\n})\nexport class ReunionListComponent implements OnInit, AfterViewInit {\n  reunions: any[] = [];\n  filteredReunions: any[] = [];\n  loading = true;\n  error: any;\n  animateItems = false; // Contrôle l'animation des éléments de la liste\n\n  // Propriétés pour la recherche\n  showSearchBar = false;\n  searchTerm = '';\n  selectedPlanning = '';\n  uniquePlannings: any[] = [];\n\n  // Propriété pour le titre de la page\n  get pageTitle(): string {\n    return this.authService.getCurrentUserRole() === 'admin'\n      ? 'Toutes les Réunions'\n      : 'Mes Réunions';\n  }\n\n  constructor(\n    private reunionService: ReunionService,\n    private router: Router,\n    private authService: AuthuserService,\n    private sanitizer: DomSanitizer,\n    private toastService: ToastService\n  ) {}\n\n  ngOnInit(): void {\n    this.loadReunions();\n\n    // Test du service de toast\n    console.log('🧪 Test du service de toast...');\n    // this.toastService.success('Test', 'Le service de toast fonctionne !');\n  }\n\n  ngAfterViewInit(): void {\n    // Activer les animations après un court délai pour permettre le rendu initial\n    setTimeout(() => {\n      this.animateItems = true;\n    }, 100);\n  }\n\n  /**\n   * Affiche ou masque la barre de recherche\n   */\n  toggleSearchBar(): void {\n    this.showSearchBar = !this.showSearchBar;\n\n    // Si on ferme la barre de recherche, réinitialiser les filtres\n    if (!this.showSearchBar) {\n      this.clearSearch();\n    }\n  }\n\n  /**\n   * Réinitialise les critères de recherche\n   */\n  clearSearch(): void {\n    this.searchTerm = '';\n    this.selectedPlanning = '';\n    this.searchReunions();\n  }\n\n  /**\n   * Filtre les réunions selon les critères de recherche\n   */\n  searchReunions(): void {\n    if (!this.searchTerm && !this.selectedPlanning) {\n      // Si aucun critère de recherche, afficher toutes les réunions\n      this.filteredReunions = [...this.reunions];\n      return;\n    }\n\n    // Filtrer les réunions selon les critères\n    this.filteredReunions = this.reunions.filter(reunion => {\n      // Vérifier le titre et la description si searchTerm est défini\n      const matchesSearchTerm = !this.searchTerm ||\n        (reunion.titre && reunion.titre.toLowerCase().includes(this.searchTerm.toLowerCase())) ||\n        (reunion.description && reunion.description.toLowerCase().includes(this.searchTerm.toLowerCase()));\n\n      // Vérifier le planning si selectedPlanning est défini\n      const matchesPlanning = !this.selectedPlanning ||\n        (reunion.planning && reunion.planning._id === this.selectedPlanning);\n\n      // La réunion doit correspondre aux deux critères (si définis)\n      return matchesSearchTerm && matchesPlanning;\n    });\n  }\n\n\n\n  loadReunions(): void {\n    this.loading = true;\n    this.animateItems = false; // Réinitialiser l'animation\n\n    const userId = this.authService.getCurrentUserId();\n    const userRole = this.authService.getCurrentUserRole();\n\n    if (!userId) {\n      this.error = \"Utilisateur non connecté\";\n      this.loading = false;\n      return;\n    }\n\n    // Si l'utilisateur est admin, récupérer toutes les réunions\n    // Sinon, récupérer seulement ses réunions\n    const reunionObservable = userRole === 'admin'\n      ? this.reunionService.getAllReunionsAdmin()\n      : this.reunionService.getProchainesReunions(userId);\n\n    reunionObservable.subscribe({\n      next: (response: any) => {\n        console.log('Réunions chargées:', response);\n\n        // Réinitialiser les erreurs\n        this.error = null;\n\n        // Attribuer les données après un court délai pour l'animation\n        setTimeout(() => {\n          // Récupérer les réunions selon la structure de réponse\n          let reunions = userRole === 'admin'\n            ? (response.data || response.reunions || [])\n            : (response.reunions || []);\n\n          console.log('Réunions récupérées pour admin:', reunions);\n          console.log('Structure de la première réunion:', reunions[0]);\n\n          // Pour le test : ajouter \"présence obligatoire\" à certaines réunions si aucune n'en a\n          reunions = this.ajouterPresenceObligatoirePourTest(reunions);\n\n          // Trier les réunions : celles avec \"Présence Obligatoire\" en premier\n          this.reunions = this.trierReunionsParPresenceObligatoire(reunions);\n\n          // Initialiser les réunions filtrées avec toutes les réunions\n          this.filteredReunions = [...this.reunions];\n\n          // Extraire les plannings uniques pour le filtre\n          this.extractUniquePlannings();\n\n          this.loading = false;\n\n          // Activer les animations après un court délai\n          setTimeout(() => {\n            this.animateItems = true;\n          }, 100);\n        }, 300); // Délai pour une meilleure expérience visuelle\n      },\n      error: (error: any) => {\n        console.error('Erreur détaillée:', JSON.stringify(error));\n        this.error = `Erreur lors du chargement des réunions: ${error.message || error.statusText || 'Erreur inconnue'}`;\n        this.loading = false;\n      }\n    });\n  }\n\n  getStatutClass(statut: string): string {\n    switch (statut) {\n      case 'planifiee': return 'bg-blue-100 text-blue-800';\n      case 'en_cours': return 'bg-yellow-100 text-yellow-800';\n      case 'terminee': return 'bg-green-100 text-green-800';\n      case 'annulee': return 'bg-red-100 text-red-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  }\n\n  editReunion(id:string) {\n    console.log(id)\n      if (this.reunions) {\n      this.router.navigate(['/reunions/modifier', id]);\n\n  }\n  }\n\n  /**\n   * Supprime une réunion après confirmation\n   * @param id ID de la réunion à supprimer\n   */\n  deleteReunion(id: string): void {\n    console.log('🗑️ Tentative de suppression de la réunion avec ID:', id);\n\n    if (confirm('Êtes-vous sûr de vouloir supprimer cette réunion ?')) {\n      const userRole = this.authService.getCurrentUserRole();\n      console.log('👤 Rôle utilisateur:', userRole);\n\n      // Utiliser la méthode appropriée selon le rôle\n      const deleteObservable = userRole === 'admin'\n        ? this.reunionService.forceDeleteReunion(id)\n        : this.reunionService.deleteReunion(id);\n\n      console.log('🚀 Envoi de la requête de suppression...');\n\n      deleteObservable.subscribe({\n        next: (response) => {\n          console.log('✅ Réunion supprimée avec succès:', response);\n          this.handleSuccessfulDeletion(id);\n        },\n        error: (error) => {\n          console.error('❌ Erreur lors de la suppression:', error);\n          console.error('📋 Détails de l\\'erreur:', {\n            status: error.status,\n            statusText: error.statusText,\n            message: error.error?.message,\n            fullError: error\n          });\n\n          // Si c'est une erreur 200 mal interprétée ou une erreur de CORS,\n          // on considère que la suppression a réussi\n          if (error.status === 0 || error.status === 200) {\n            console.log('🔄 Erreur probablement liée à CORS ou réponse mal formatée, on considère la suppression comme réussie');\n            this.handleSuccessfulDeletion(id);\n            return;\n          }\n\n          // Pour les autres erreurs, on vérifie quand même si la suppression a eu lieu\n          // en rechargeant la liste après un délai\n          if (error.status >= 500) {\n            console.log('🔄 Erreur serveur, vérification de la suppression dans 2 secondes...');\n            setTimeout(() => {\n              this.loadReunions();\n            }, 2000);\n          }\n\n          // Gestion spécifique des erreurs d'autorisation\n          if (error.status === 403) {\n            this.toastService.accessDenied('supprimer cette réunion', error.status);\n          } else if (error.status === 401) {\n            this.toastService.error(\n              'Non autorisé',\n              'Vous devez être connecté pour supprimer une réunion'\n            );\n          } else {\n            const errorMessage = error.error?.message || 'Erreur lors de la suppression de la réunion';\n            this.toastService.error(\n              'Erreur de suppression',\n              errorMessage,\n              8000\n            );\n          }\n        }\n      });\n    } else {\n      console.log('❌ Suppression annulée par l\\'utilisateur');\n    }\n  }\n\n  private handleSuccessfulDeletion(id: string): void {\n    console.log('🎯 Traitement de la suppression réussie pour l\\'ID:', id);\n\n    // Retirer la réunion de la liste locale (gérer _id et id)\n    const initialCount = this.reunions.length;\n    this.reunions = this.reunions.filter(reunion =>\n      reunion._id !== id && reunion.id !== id\n    );\n    this.filteredReunions = this.filteredReunions.filter(reunion =>\n      reunion._id !== id && reunion.id !== id\n    );\n\n    const finalCount = this.reunions.length;\n    console.log(`📊 Réunions avant suppression: ${initialCount}, après: ${finalCount}`);\n\n    // Mettre à jour la liste des plannings uniques\n    this.extractUniquePlannings();\n\n    // Afficher le toast de succès\n    this.toastService.success(\n      'Réunion supprimée',\n      'La réunion a été supprimée avec succès'\n    );\n\n    console.log('🎉 Toast de succès affiché et liste mise à jour');\n\n    // Recharger complètement la liste pour s'assurer de la mise à jour\n    this.loadReunions();\n  }\n\n  formatDescription(description: string): SafeHtml {\n    if (!description) return this.sanitizer.bypassSecurityTrustHtml('');\n\n    // Recherche la chaîne \"(presence obligatoire)\" (insensible à la casse) et la remplace par une version en rouge\n    const formattedText = description.replace(\n      /\\(presence obligatoire\\)/gi,\n      '<span class=\"text-red-600 font-semibold\">(presence obligatoire)</span>'\n    );\n\n    // Sanitize le HTML pour éviter les problèmes de sécurité\n    return this.sanitizer.bypassSecurityTrustHtml(formattedText);\n  }\n\n  /**\n   * Vérifie si une réunion contient \"Présence Obligatoire\" dans sa description\n   * @param reunion La réunion à vérifier\n   * @returns true si la réunion a une présence obligatoire, false sinon\n   */\n  hasPresenceObligatoire(reunion: any): boolean {\n    if (!reunion.description) return false;\n\n    // Recherche différentes variations de \"présence obligatoire\" (insensible à la casse)\n    const patterns = [\n      /presence obligatoire/i,\n      /présence obligatoire/i,\n      /obligatoire/i,\n      /\\(obligatoire\\)/i,\n      /\\(presence obligatoire\\)/i,\n      /\\(présence obligatoire\\)/i\n    ];\n\n    // Retourne true si l'une des expressions est trouvée\n    return patterns.some(pattern => pattern.test(reunion.description));\n  }\n\n  /**\n   * Trie les réunions en mettant celles avec \"Présence Obligatoire\" en premier\n   * @param reunions Liste des réunions à trier\n   * @returns Liste triée des réunions\n   */\n  trierReunionsParPresenceObligatoire(reunions: any[]): any[] {\n    if (!reunions || !reunions.length) return [];\n\n    console.log('Avant tri - Nombre de réunions:', reunions.length);\n\n    // Vérifier chaque réunion pour la présence obligatoire\n    reunions.forEach((reunion, index) => {\n      const hasPresence = this.hasPresenceObligatoire(reunion);\n      console.log(`Réunion ${index + 1} - Titre: ${reunion.titre}, Description: ${reunion.description}, Présence Obligatoire: ${hasPresence}`);\n    });\n\n    // Trier les réunions : celles avec \"Présence Obligatoire\" en premier\n    const reunionsTriees = [...reunions].sort((a, b) => {\n      const aHasPresenceObligatoire = this.hasPresenceObligatoire(a);\n      const bHasPresenceObligatoire = this.hasPresenceObligatoire(b);\n\n      if (aHasPresenceObligatoire && !bHasPresenceObligatoire) {\n        return -1; // a vient avant b\n      }\n      if (!aHasPresenceObligatoire && bHasPresenceObligatoire) {\n        return 1; // b vient avant a\n      }\n\n      // Si les deux ont ou n'ont pas \"Présence Obligatoire\", trier par date\n      return new Date(b.date).getTime() - new Date(a.date).getTime();\n    });\n\n    console.log('Après tri - Ordre des réunions:');\n    reunionsTriees.forEach((reunion, index) => {\n      const hasPresence = this.hasPresenceObligatoire(reunion);\n      console.log(`Position ${index + 1} - Titre: ${reunion.titre}, Présence Obligatoire: ${hasPresence}`);\n    });\n\n    return reunionsTriees;\n  }\n\n  /**\n   * Méthode temporaire pour ajouter \"présence obligatoire\" à certaines réunions pour tester le tri\n   * @param reunions Liste des réunions\n   * @returns Liste des réunions avec certaines marquées comme \"présence obligatoire\"\n   */\n  /**\n   * Extrait les plannings uniques à partir des réunions pour le filtre\n   */\n  extractUniquePlannings(): void {\n    // Map pour stocker les plannings uniques par ID\n    const planningsMap = new Map();\n\n    // Parcourir toutes les réunions\n    this.reunions.forEach(reunion => {\n      if (reunion.planning && reunion.planning._id) {\n        // Ajouter le planning au Map s'il n'existe pas déjà\n        if (!planningsMap.has(reunion.planning._id)) {\n          planningsMap.set(reunion.planning._id, {\n            id: reunion.planning._id,\n            titre: reunion.planning.titre\n          });\n        }\n      }\n    });\n\n    // Convertir le Map en tableau\n    this.uniquePlannings = Array.from(planningsMap.values());\n\n    // Trier les plannings par titre\n    this.uniquePlannings.sort((a, b) => a.titre.localeCompare(b.titre));\n  }\n\n  /**\n   * Méthode temporaire pour ajouter \"présence obligatoire\" à certaines réunions pour tester le tri\n   */\n  ajouterPresenceObligatoirePourTest(reunions: any[]): any[] {\n    if (!reunions || reunions.length === 0) return reunions;\n\n    // Vérifier si au moins une réunion a déjà \"présence obligatoire\"\n    const hasAnyPresenceObligatoire = reunions.some(reunion => this.hasPresenceObligatoire(reunion));\n\n    // Si aucune réunion n'a \"présence obligatoire\", en ajouter à certaines pour le test\n    if (!hasAnyPresenceObligatoire) {\n      console.log('Aucune réunion avec présence obligatoire trouvée, ajout pour le test...');\n\n      // Ajouter \"présence obligatoire\" à la première réunion si elle existe\n      if (reunions.length > 0) {\n        const reunion = reunions[0];\n        reunion.description = reunion.description\n          ? reunion.description + ' (présence obligatoire)'\n          : '(présence obligatoire)';\n        console.log(`Ajout de \"présence obligatoire\" à la réunion: ${reunion.titre}`);\n      }\n\n      // Si au moins 3 réunions, ajouter aussi à la troisième\n      if (reunions.length >= 3) {\n        const reunion = reunions[2];\n        reunion.description = reunion.description\n          ? reunion.description + ' (présence obligatoire)'\n          : '(présence obligatoire)';\n        console.log(`Ajout de \"présence obligatoire\" à la réunion: ${reunion.titre}`);\n      }\n    }\n\n    return reunions;\n  }\n}", "<div class=\"container mx-auto px-4 py-6 page-container page-enter\">\n  <div class=\"flex flex-col mb-8\">\n    <div class=\"flex justify-between items-center\">\n      <h1 class=\"text-2xl font-bold text-gray-800 page-title\">{{ pageTitle }}</h1>\n\n      <!-- Bouton de recherche -->\n      <div class=\"relative\">\n        <button (click)=\"toggleSearchBar()\" class=\"search-button px-4 py-2 bg-purple-200 text-purple-800 rounded-md hover:bg-purple-300 transition-colors transform hover:scale-105 duration-200 flex items-center shadow-sm border border-purple-300\">\n          <svg class=\"h-5 w-5 mr-2 text-purple-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n          </svg>\n          Rechercher\n        </button>\n      </div>\n    </div>\n\n    <!-- Barre de recherche -->\n    <div *ngIf=\"showSearchBar\" class=\"mt-4 bg-white p-4 rounded-lg shadow-md transition-all duration-300 animate-fadeIn\"\n         [ngClass]=\"{'animate__animated animate__fadeInDown': showSearchBar}\">\n      <div class=\"flex flex-col md:flex-row gap-4\">\n        <div class=\"flex-1\">\n          <div class=\"relative\">\n            <div class=\"flex items-center\">\n              <div class=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-purple-400\">\n                <svg class=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                </svg>\n              </div>\n              <input\n                type=\"text\"\n                id=\"searchTerm\"\n                [(ngModel)]=\"searchTerm\"\n                (input)=\"searchReunions()\"\n                class=\"w-full pl-10 pr-10 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-300 focus:border-purple-400 transition-all duration-300\"\n                placeholder=\"Rechercher par titre ou description\"\n              >\n              <button *ngIf=\"searchTerm\" (click)=\"clearSearch()\" class=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-purple-600 transition-colors\">\n                <svg class=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n                </svg>\n              </button>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"md:w-1/3\">\n          <div class=\"relative\">\n            <div class=\"flex items-center\">\n              <select\n                id=\"planningFilter\"\n                [(ngModel)]=\"selectedPlanning\"\n                (change)=\"searchReunions()\"\n                class=\"w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-300 focus:border-purple-400 transition-all duration-300 appearance-none\"\n              >\n                <option value=\"\">Tous les plannings</option>\n                <option *ngFor=\"let planning of uniquePlannings\" [value]=\"planning.id\">{{ planning.titre }}</option>\n              </select>\n              <div class=\"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\">\n                <svg class=\"h-5 w-5 text-purple-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 9l-7 7-7-7\" />\n                </svg>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <div *ngIf=\"!loading && reunions.length > 0\" class=\"mt-2 text-sm text-gray-600 flex items-center\">\n      <svg class=\"h-4 w-4 mr-2 text-red-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n      </svg>\n      <span>Les réunions à <span class=\"font-semibold text-red-600\">présence obligatoire</span> sont affichées en premier</span>\n    </div>\n\n    <!-- Résultats de recherche -->\n    <div *ngIf=\"searchTerm || selectedPlanning\" class=\"mt-2 text-sm text-gray-600\">\n      <span *ngIf=\"filteredReunions.length === 0\" class=\"text-red-500\">\n        Aucune réunion ne correspond à votre recherche.\n      </span>\n      <span *ngIf=\"filteredReunions.length > 0\">\n        {{ filteredReunions.length }} réunion(s) trouvée(s)\n      </span>\n    </div>\n  </div>\n\n  <div *ngIf=\"loading\" class=\"text-center py-12\">\n    <div class=\"loading-spinner rounded-full h-16 w-16 border-4 border-purple-200 border-t-purple-600 mx-auto\"></div>\n    <p class=\"mt-4 text-gray-600 animate-pulse\">Chargement de vos réunions...</p>\n  </div>\n\n  <div *ngIf=\"error\" class=\"bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md mb-6 shadow-md transform transition-all duration-500 hover:shadow-lg\">\n    <div class=\"flex items-center\">\n      <svg class=\"h-6 w-6 mr-3 text-red-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n      </svg>\n      <p>Erreur lors du chargement des réunions: {{ error.message }}</p>\n    </div>\n  </div>\n\n  <div *ngIf=\"!loading && reunions.length === 0\" class=\"text-center py-12 empty-container\" [class.animated]=\"!loading\">\n    <div class=\"bg-white rounded-lg shadow-md p-8 max-w-md mx-auto\">\n      <svg class=\"mx-auto h-16 w-16 text-purple-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n      </svg>\n      <h3 class=\"mt-4 text-xl font-medium text-gray-900\">Aucune réunion prévue</h3>\n      <p class=\"mt-2 text-gray-500\">Vous pouvez créer des réunions depuis la page détail d'un planning.</p>\n    </div>\n  </div>\n\n  <div *ngIf=\"!loading && reunions.length > 0\" class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n    <div *ngFor=\"let reunion of (searchTerm || selectedPlanning ? filteredReunions : reunions); let i = index\"\n         class=\"bg-white rounded-lg shadow-md p-5 hover:shadow-xl transition-all duration-300 reunion-card\"\n         [class.animated]=\"animateItems\"\n         [class.border-l-4]=\"hasPresenceObligatoire(reunion)\"\n         [class.border-red-500]=\"hasPresenceObligatoire(reunion)\"\n         [style.animation-delay]=\"i * 100 + 'ms'\">\n      <div class=\"flex justify-between items-start\">\n        <div class=\"flex-1\">\n          <div class=\"flex items-center\">\n            <h3 class=\"text-lg font-semibold text-gray-800 hover:text-purple-600 transition-colors\">\n              <a [routerLink]=\"['/reunions/reunionDetails', reunion._id]\" class=\"hover:text-purple-600\">{{ reunion.titre }}</a>\n            </h3>\n            <span *ngIf=\"hasPresenceObligatoire(reunion)\"\n                  class=\"ml-2 px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full font-bold animate-pulse\">\n              Présence Obligatoire\n            </span>\n          </div>\n          <p class=\"text-sm mt-1\" [innerHTML]=\"reunion.description | highlightPresence\"></p>\n          <div class=\"mt-3 flex items-center text-sm text-gray-500\">\n            <svg class=\"h-4 w-4 mr-2 text-purple-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n            <span>{{ reunion.date | date:'mediumDate' }} • {{ reunion.heureDebut }} - {{ reunion.heureFin }}</span>\n          </div>\n        </div>\n        <div class=\"flex items-start space-x-2\">\n          <span [class]=\"'px-3 py-1 text-xs rounded-full font-medium ' + getStatutClass(reunion.statut)\">\n            {{ reunion.statut | titlecase }}\n          </span>\n          <button (click)=\"deleteReunion(reunion._id || reunion.id); $event.stopPropagation();\"\n                  class=\"text-red-500 hover:text-red-700 transition-colors duration-300 p-1 rounded-full hover:bg-red-50\"\n                  title=\"Supprimer la réunion\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n            </svg>\n          </button>\n        </div>\n      </div>\n\n      <!-- Creator Info -->\n      <div class=\"mt-3 text-sm text-gray-600 flex items-center\">\n        <svg class=\"h-4 w-4 mr-2 text-gray-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n        </svg>\n        <span><strong>Créateur:</strong> {{ reunion.createur.username }}</span>\n      </div>\n\n      <!-- Participants -->\n      <div *ngIf=\"reunion.participants.length > 0\" class=\"mt-3 text-sm text-gray-600\">\n        <div class=\"flex items-center\">\n          <svg class=\"h-4 w-4 mr-2 text-gray-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n          </svg>\n          <strong>Participants:&nbsp;</strong>{{ reunion.participants.length }}\n        </div>\n      </div>\n\n      <!-- Planning Info -->\n      <div class=\"mt-3 text-sm text-gray-600 flex items-center\">\n        <svg class=\"h-4 w-4 mr-2 text-gray-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n        </svg>\n        <span><strong>Planning:</strong> {{ reunion.planning.titre }}</span>\n      </div>\n\n      <!-- Lien Visio -->\n      <div *ngIf=\"reunion.lienVisio\" class=\"mt-3 text-sm\">\n        <a href=\"{{ reunion.lienVisio }}\" class=\"text-purple-600 hover:text-purple-800 flex items-center transition-colors\" target=\"_blank\">\n          <svg class=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\" />\n          </svg>\n          Rejoindre la visioconférence\n        </a>\n      </div>\n\n      <div class=\"mt-4 pt-3 border-t border-gray-100 flex justify-between items-center\">\n        <div class=\"flex items-center text-sm text-gray-500\">\n          <svg class=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n          </svg>\n          {{ reunion.lieu || 'Lieu non spécifié' }}\n        </div>\n        <div class=\"flex items-center space-x-2\">\n          <a (click)=\"editReunion(reunion._id || reunion.id)\"\n             class=\"px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-all duration-300 transform hover:scale-105 flex items-center\">\n            <svg class=\"h-4 w-4 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n            </svg>\n            Modifier\n          </a>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>"], "mappings": "AAMA,SAASA,OAAO,EAAEC,UAAU,EAAEC,KAAK,EAAEC,OAAO,EAAEC,KAAK,EAAEC,OAAO,QAAQ,qBAAqB;;;;;;;;;;;;;IC8B3EC,EAAA,CAAAC,cAAA,iBAAqK;IAA1ID,EAAA,CAAAE,UAAA,mBAAAC,sEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,IAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAChDT,EAAA,CAAAU,cAAA,EAA2E;IAA3EV,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAW,SAAA,eAAiG;IACnGX,EAAA,CAAAY,YAAA,EAAM;;;;;IAgBNZ,EAAA,CAAAC,cAAA,iBAAuE;IAAAD,EAAA,CAAAa,MAAA,GAAoB;IAAAb,EAAA,CAAAY,YAAA,EAAS;;;;IAAnDZ,EAAA,CAAAc,UAAA,UAAAC,YAAA,CAAAC,EAAA,CAAqB;IAAChB,EAAA,CAAAiB,SAAA,GAAoB;IAApBjB,EAAA,CAAAkB,iBAAA,CAAAH,YAAA,CAAAI,KAAA,CAAoB;;;;;;;;;;;;IAtCvGnB,EAAA,CAAAoB,eAAA,EAC0E;IAD1EpB,EAAA,CAAAC,cAAA,cAC0E;IAM9DD,EAAA,CAAAU,cAAA,EAA2E;IAA3EV,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAW,SAAA,cAAwH;IAC1HX,EAAA,CAAAY,YAAA,EAAM;IAERZ,EAAA,CAAAoB,eAAA,EAOC;IAPDpB,EAAA,CAAAC,cAAA,gBAOC;IAJCD,EAAA,CAAAE,UAAA,2BAAAmB,oEAAAC,MAAA;MAAAtB,EAAA,CAAAI,aAAA,CAAAmB,IAAA;MAAA,MAAAC,OAAA,GAAAxB,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAgB,OAAA,CAAAC,UAAA,GAAAH,MAAA;IAAA,EAAwB,mBAAAI,4DAAA;MAAA1B,EAAA,CAAAI,aAAA,CAAAmB,IAAA;MAAA,MAAAI,OAAA,GAAA3B,EAAA,CAAAO,aAAA;MAAA,OACfP,EAAA,CAAAQ,WAAA,CAAAmB,OAAA,CAAAC,cAAA,EAAgB;IAAA,EADD;IAH1B5B,EAAA,CAAAY,YAAA,EAOC;IACDZ,EAAA,CAAA6B,UAAA,IAAAC,6CAAA,qBAIS;IACX9B,EAAA,CAAAY,YAAA,EAAM;IAIVZ,EAAA,CAAAC,cAAA,eAAsB;IAKdD,EAAA,CAAAE,UAAA,2BAAA6B,sEAAAT,MAAA;MAAAtB,EAAA,CAAAI,aAAA,CAAAmB,IAAA;MAAA,MAAAS,OAAA,GAAAhC,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAwB,OAAA,CAAAC,gBAAA,GAAAX,MAAA;IAAA,EAA8B,oBAAAY,+DAAA;MAAAlC,EAAA,CAAAI,aAAA,CAAAmB,IAAA;MAAA,MAAAY,OAAA,GAAAnC,EAAA,CAAAO,aAAA;MAAA,OACpBP,EAAA,CAAAQ,WAAA,CAAA2B,OAAA,CAAAP,cAAA,EAAgB;IAAA,EADI;IAI9B5B,EAAA,CAAAC,cAAA,kBAAiB;IAAAD,EAAA,CAAAa,MAAA,0BAAkB;IAAAb,EAAA,CAAAY,YAAA,EAAS;IAC5CZ,EAAA,CAAA6B,UAAA,KAAAO,8CAAA,qBAAoG;IACtGpC,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAC,cAAA,eAAmF;IACjFD,EAAA,CAAAU,cAAA,EAA2F;IAA3FV,EAAA,CAAAC,cAAA,eAA2F;IACzFD,EAAA,CAAAW,SAAA,gBAA2F;IAC7FX,EAAA,CAAAY,YAAA,EAAM;;;;IA1CbZ,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAAqC,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,aAAA,EAAoE;IAa7DxC,EAAA,CAAAiB,SAAA,GAAwB;IAAxBjB,EAAA,CAAAc,UAAA,YAAAyB,MAAA,CAAAd,UAAA,CAAwB;IAKjBzB,EAAA,CAAAiB,SAAA,GAAgB;IAAhBjB,EAAA,CAAAc,UAAA,SAAAyB,MAAA,CAAAd,UAAA,CAAgB;IAcvBzB,EAAA,CAAAiB,SAAA,GAA8B;IAA9BjB,EAAA,CAAAc,UAAA,YAAAyB,MAAA,CAAAN,gBAAA,CAA8B;IAKDjC,EAAA,CAAAiB,SAAA,GAAkB;IAAlBjB,EAAA,CAAAc,UAAA,YAAAyB,MAAA,CAAAE,eAAA,CAAkB;;;;;;IAa3DzC,EAAA,CAAAoB,eAAA,EAAkG;IAAlGpB,EAAA,CAAAC,cAAA,cAAkG;IAChGD,EAAA,CAAAU,cAAA,EAA6F;IAA7FV,EAAA,CAAAC,cAAA,cAA6F;IAC3FD,EAAA,CAAAW,SAAA,eAAuG;IACzGX,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAoB,eAAA,EAAM;IAANpB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAa,MAAA,gCAAe;IAAAb,EAAA,CAAAC,cAAA,eAAyC;IAAAD,EAAA,CAAAa,MAAA,gCAAoB;IAAAb,EAAA,CAAAY,YAAA,EAAO;IAACZ,EAAA,CAAAa,MAAA,sCAAyB;IAAAb,EAAA,CAAAY,YAAA,EAAO;;;;;IAK1HZ,EAAA,CAAAC,cAAA,eAAiE;IAC/DD,EAAA,CAAAa,MAAA,kEACF;IAAAb,EAAA,CAAAY,YAAA,EAAO;;;;;IACPZ,EAAA,CAAAC,cAAA,WAA0C;IACxCD,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAY,YAAA,EAAO;;;;IADLZ,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAA0C,kBAAA,MAAAC,OAAA,CAAAC,gBAAA,CAAAC,MAAA,sCACF;;;;;;IANF7C,EAAA,CAAAoB,eAAA,EAA+E;IAA/EpB,EAAA,CAAAC,cAAA,cAA+E;IAC7ED,EAAA,CAAA6B,UAAA,IAAAiB,2CAAA,mBAEO;IACP9C,EAAA,CAAA6B,UAAA,IAAAkB,2CAAA,mBAEO;IACT/C,EAAA,CAAAY,YAAA,EAAM;;;;IANGZ,EAAA,CAAAiB,SAAA,GAAmC;IAAnCjB,EAAA,CAAAc,UAAA,SAAAkC,MAAA,CAAAJ,gBAAA,CAAAC,MAAA,OAAmC;IAGnC7C,EAAA,CAAAiB,SAAA,GAAiC;IAAjCjB,EAAA,CAAAc,UAAA,SAAAkC,MAAA,CAAAJ,gBAAA,CAAAC,MAAA,KAAiC;;;;;;IAM5C7C,EAAA,CAAAoB,eAAA,EAA+C;IAA/CpB,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAW,SAAA,cAAiH;IACjHX,EAAA,CAAAC,cAAA,YAA4C;IAAAD,EAAA,CAAAa,MAAA,yCAA6B;IAAAb,EAAA,CAAAY,YAAA,EAAI;;;;;;IAG/EZ,EAAA,CAAAoB,eAAA,EAAiK;IAAjKpB,EAAA,CAAAC,cAAA,cAAiK;IAE7JD,EAAA,CAAAU,cAAA,EAA6F;IAA7FV,EAAA,CAAAC,cAAA,cAA6F;IAC3FD,EAAA,CAAAW,SAAA,eAA8H;IAChIX,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAoB,eAAA,EAAG;IAAHpB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAa,MAAA,GAA2D;IAAAb,EAAA,CAAAY,YAAA,EAAI;;;;IAA/DZ,EAAA,CAAAiB,SAAA,GAA2D;IAA3DjB,EAAA,CAAA0C,kBAAA,kDAAAO,MAAA,CAAAC,KAAA,CAAAC,OAAA,KAA2D;;;;;;IAIlEnD,EAAA,CAAAoB,eAAA,EAAqH;IAArHpB,EAAA,CAAAC,cAAA,cAAqH;IAEjHD,EAAA,CAAAU,cAAA,EAAqG;IAArGV,EAAA,CAAAC,cAAA,cAAqG;IACnGD,EAAA,CAAAW,SAAA,eAAmK;IACrKX,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAoB,eAAA,EAAmD;IAAnDpB,EAAA,CAAAC,cAAA,aAAmD;IAAAD,EAAA,CAAAa,MAAA,sCAAqB;IAAAb,EAAA,CAAAY,YAAA,EAAK;IAC7EZ,EAAA,CAAAC,cAAA,YAA8B;IAAAD,EAAA,CAAAa,MAAA,yFAAmE;IAAAb,EAAA,CAAAY,YAAA,EAAI;;;;IANhBZ,EAAA,CAAAoD,WAAA,cAAAC,MAAA,CAAAC,OAAA,CAA2B;;;;;IAuB1GtD,EAAA,CAAAC,cAAA,eACkG;IAChGD,EAAA,CAAAa,MAAA,kCACF;IAAAb,EAAA,CAAAY,YAAA,EAAO;;;;;IAiCbZ,EAAA,CAAAC,cAAA,cAAgF;IAE5ED,EAAA,CAAAU,cAAA,EAA8F;IAA9FV,EAAA,CAAAC,cAAA,cAA8F;IAC5FD,EAAA,CAAAW,SAAA,eAAmV;IACrVX,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAoB,eAAA,EAAQ;IAARpB,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAa,MAAA,0BAAmB;IAAAb,EAAA,CAAAY,YAAA,EAAS;IAAAZ,EAAA,CAAAa,MAAA,GACtC;IAAAb,EAAA,CAAAY,YAAA,EAAM;;;;IADgCZ,EAAA,CAAAiB,SAAA,GACtC;IADsCjB,EAAA,CAAA0C,kBAAA,KAAAa,WAAA,CAAAC,YAAA,CAAAX,MAAA,MACtC;;;;;IAYF7C,EAAA,CAAAC,cAAA,cAAoD;IAEhDD,EAAA,CAAAU,cAAA,EAAgF;IAAhFV,EAAA,CAAAC,cAAA,cAAgF;IAC9ED,EAAA,CAAAW,SAAA,eAA+M;IACjNX,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAa,MAAA,0CACF;IAAAb,EAAA,CAAAY,YAAA,EAAI;;;;IALDZ,EAAA,CAAAiB,SAAA,GAA8B;IAA9BjB,EAAA,CAAAyD,qBAAA,SAAAF,WAAA,CAAAG,SAAA,EAAA1D,EAAA,CAAA2D,aAAA,CAA8B;;;;;;;;;IAnErC3D,EAAA,CAAAC,cAAA,cAK8C;IAKsDD,EAAA,CAAAa,MAAA,GAAmB;IAAAb,EAAA,CAAAY,YAAA,EAAI;IAEnHZ,EAAA,CAAA6B,UAAA,IAAA+B,iDAAA,mBAGO;IACT5D,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAW,SAAA,YAAkF;;IAClFX,EAAA,CAAAC,cAAA,eAA0D;IACxDD,EAAA,CAAAU,cAAA,EAAgG;IAAhGV,EAAA,CAAAC,cAAA,eAAgG;IAC9FD,EAAA,CAAAW,SAAA,gBAAwH;IAC1HX,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAoB,eAAA,EAAM;IAANpB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAa,MAAA,IAA0F;;IAAAb,EAAA,CAAAY,YAAA,EAAO;IAG3GZ,EAAA,CAAAC,cAAA,eAAwC;IAEpCD,EAAA,CAAAa,MAAA,IACF;;IAAAb,EAAA,CAAAY,YAAA,EAAO;IACPZ,EAAA,CAAAC,cAAA,kBAEqC;IAF7BD,EAAA,CAAAE,UAAA,mBAAA2D,oEAAAvC,MAAA;MAAA,MAAAwC,WAAA,GAAA9D,EAAA,CAAAI,aAAA,CAAA2D,IAAA;MAAA,MAAAR,WAAA,GAAAO,WAAA,CAAAE,SAAA;MAAA,MAAAC,OAAA,GAAAjE,EAAA,CAAAO,aAAA;MAAS0D,OAAA,CAAAC,aAAA,CAAAX,WAAA,CAAAY,GAAA,IAAAZ,WAAA,CAAAvC,EAAA,CAAwC;MAAA,OAAEhB,EAAA,CAAAQ,WAAA,CAAAc,MAAA,CAAA8C,eAAA,EAAwB;IAAA,EAAE;IAGnFpE,EAAA,CAAAU,cAAA,EAA8G;IAA9GV,EAAA,CAAAC,cAAA,eAA8G;IAC5GD,EAAA,CAAAW,SAAA,gBAAyM;IAC3MX,EAAA,CAAAY,YAAA,EAAM;IAMZZ,EAAA,CAAAoB,eAAA,EAA0D;IAA1DpB,EAAA,CAAAC,cAAA,eAA0D;IACxDD,EAAA,CAAAU,cAAA,EAA8F;IAA9FV,EAAA,CAAAC,cAAA,eAA8F;IAC5FD,EAAA,CAAAW,SAAA,gBAAgJ;IAClJX,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAoB,eAAA,EAAM;IAANpB,EAAA,CAAAC,cAAA,YAAM;IAAQD,EAAA,CAAAa,MAAA,sBAAS;IAAAb,EAAA,CAAAY,YAAA,EAAS;IAACZ,EAAA,CAAAa,MAAA,IAA+B;IAAAb,EAAA,CAAAY,YAAA,EAAO;IAIzEZ,EAAA,CAAA6B,UAAA,KAAAwC,iDAAA,kBAOM;IAGNrE,EAAA,CAAAC,cAAA,eAA0D;IACxDD,EAAA,CAAAU,cAAA,EAA8F;IAA9FV,EAAA,CAAAC,cAAA,eAA8F;IAC5FD,EAAA,CAAAW,SAAA,gBAAmK;IACrKX,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAoB,eAAA,EAAM;IAANpB,EAAA,CAAAC,cAAA,YAAM;IAAQD,EAAA,CAAAa,MAAA,iBAAS;IAAAb,EAAA,CAAAY,YAAA,EAAS;IAACZ,EAAA,CAAAa,MAAA,IAA4B;IAAAb,EAAA,CAAAY,YAAA,EAAO;IAItEZ,EAAA,CAAA6B,UAAA,KAAAyC,iDAAA,kBAOM;IAENtE,EAAA,CAAAC,cAAA,eAAkF;IAE9ED,EAAA,CAAAU,cAAA,EAAgF;IAAhFV,EAAA,CAAAC,cAAA,eAAgF;IAC9ED,EAAA,CAAAW,SAAA,gBAA+J;IAEjKX,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAa,MAAA,IACF;IAAAb,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAoB,eAAA,EAAyC;IAAzCpB,EAAA,CAAAC,cAAA,eAAyC;IACpCD,EAAA,CAAAE,UAAA,mBAAAqE,+DAAA;MAAA,MAAAT,WAAA,GAAA9D,EAAA,CAAAI,aAAA,CAAA2D,IAAA;MAAA,MAAAR,WAAA,GAAAO,WAAA,CAAAE,SAAA;MAAA,MAAAQ,OAAA,GAAAxE,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAgE,OAAA,CAAAC,WAAA,CAAAlB,WAAA,CAAAY,GAAA,IAAAZ,WAAA,CAAAvC,EAAA,CAAsC;IAAA,EAAC;IAEjDhB,EAAA,CAAAU,cAAA,EAAgF;IAAhFV,EAAA,CAAAC,cAAA,eAAgF;IAC9ED,EAAA,CAAAW,SAAA,gBAAmM;IACrMX,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAa,MAAA,kBACF;IAAAb,EAAA,CAAAY,YAAA,EAAI;;;;;;IArFLZ,EAAA,CAAA0E,WAAA,oBAAAC,KAAA,cAAwC;IAHxC3E,EAAA,CAAAoD,WAAA,aAAAwB,OAAA,CAAAC,YAAA,CAA+B,eAAAD,OAAA,CAAAE,sBAAA,CAAAvB,WAAA,qBAAAqB,OAAA,CAAAE,sBAAA,CAAAvB,WAAA;IAQvBvD,EAAA,CAAAiB,SAAA,GAAwD;IAAxDjB,EAAA,CAAAc,UAAA,eAAAd,EAAA,CAAAqC,eAAA,KAAA0C,GAAA,EAAAxB,WAAA,CAAAY,GAAA,EAAwD;IAA+BnE,EAAA,CAAAiB,SAAA,GAAmB;IAAnBjB,EAAA,CAAAkB,iBAAA,CAAAqC,WAAA,CAAApC,KAAA,CAAmB;IAExGnB,EAAA,CAAAiB,SAAA,GAAqC;IAArCjB,EAAA,CAAAc,UAAA,SAAA8D,OAAA,CAAAE,sBAAA,CAAAvB,WAAA,EAAqC;IAKtBvD,EAAA,CAAAiB,SAAA,GAAqD;IAArDjB,EAAA,CAAAc,UAAA,cAAAd,EAAA,CAAAgF,WAAA,QAAAzB,WAAA,CAAA0B,WAAA,GAAAjF,EAAA,CAAAkF,cAAA,CAAqD;IAKrElF,EAAA,CAAAiB,SAAA,GAA0F;IAA1FjB,EAAA,CAAAmF,kBAAA,KAAAnF,EAAA,CAAAoF,WAAA,SAAA7B,WAAA,CAAA8B,IAAA,6BAAA9B,WAAA,CAAA+B,UAAA,SAAA/B,WAAA,CAAAgC,QAAA,KAA0F;IAI5FvF,EAAA,CAAAiB,SAAA,GAAwF;IAAxFjB,EAAA,CAAAwF,UAAA,iDAAAZ,OAAA,CAAAa,cAAA,CAAAlC,WAAA,CAAAmC,MAAA,EAAwF;IAC5F1F,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAA0C,kBAAA,MAAA1C,EAAA,CAAAgF,WAAA,SAAAzB,WAAA,CAAAmC,MAAA,OACF;IAgB+B1F,EAAA,CAAAiB,SAAA,IAA+B;IAA/BjB,EAAA,CAAA0C,kBAAA,MAAAa,WAAA,CAAAoC,QAAA,CAAAC,QAAA,KAA+B;IAI5D5F,EAAA,CAAAiB,SAAA,GAAqC;IAArCjB,EAAA,CAAAc,UAAA,SAAAyC,WAAA,CAAAC,YAAA,CAAAX,MAAA,KAAqC;IAcR7C,EAAA,CAAAiB,SAAA,GAA4B;IAA5BjB,EAAA,CAAA0C,kBAAA,MAAAa,WAAA,CAAAsC,QAAA,CAAA1E,KAAA,KAA4B;IAIzDnB,EAAA,CAAAiB,SAAA,GAAuB;IAAvBjB,EAAA,CAAAc,UAAA,SAAAyC,WAAA,CAAAG,SAAA,CAAuB;IAezB1D,EAAA,CAAAiB,SAAA,GACF;IADEjB,EAAA,CAAA0C,kBAAA,MAAAa,WAAA,CAAAuC,IAAA,uCACF;;;;;;IAnFN9F,EAAA,CAAAoB,eAAA,EAA2F;IAA3FpB,EAAA,CAAAC,cAAA,cAA2F;IACzFD,EAAA,CAAA6B,UAAA,IAAAkE,0CAAA,oBA6FM;IACR/F,EAAA,CAAAY,YAAA,EAAM;;;;IA9FqBZ,EAAA,CAAAiB,SAAA,GAAmE;IAAnEjB,EAAA,CAAAc,UAAA,YAAAkF,MAAA,CAAAvE,UAAA,IAAAuE,MAAA,CAAA/D,gBAAA,GAAA+D,MAAA,CAAApD,gBAAA,GAAAoD,MAAA,CAAAC,QAAA,CAAmE;;;AD/EhG,OAAM,MAAOC,oBAAoB;EAa/B;EACA,IAAIC,SAASA,CAAA;IACX,OAAO,IAAI,CAACC,WAAW,CAACC,kBAAkB,EAAE,KAAK,OAAO,GACpD,qBAAqB,GACrB,cAAc;EACpB;EAEAC,YACUC,cAA8B,EAC9BC,MAAc,EACdJ,WAA4B,EAC5BK,SAAuB,EACvBC,YAA0B;IAJ1B,KAAAH,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAK,SAAS,GAATA,SAAS;IACT,KAAAC,YAAY,GAAZA,YAAY;IAxBtB,KAAAT,QAAQ,GAAU,EAAE;IACpB,KAAArD,gBAAgB,GAAU,EAAE;IAC5B,KAAAU,OAAO,GAAG,IAAI;IAEd,KAAAuB,YAAY,GAAG,KAAK,CAAC,CAAC;IAEtB;IACA,KAAArC,aAAa,GAAG,KAAK;IACrB,KAAAf,UAAU,GAAG,EAAE;IACf,KAAAQ,gBAAgB,GAAG,EAAE;IACrB,KAAAQ,eAAe,GAAU,EAAE;EAexB;EAEHkE,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;IAEnB;IACAC,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAC7C;EACF;;EAEAC,eAAeA,CAAA;IACb;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACnC,YAAY,GAAG,IAAI;IAC1B,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;;;EAGAoC,eAAeA,CAAA;IACb,IAAI,CAACzE,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;IAExC;IACA,IAAI,CAAC,IAAI,CAACA,aAAa,EAAE;MACvB,IAAI,CAAC/B,WAAW,EAAE;;EAEtB;EAEA;;;EAGAA,WAAWA,CAAA;IACT,IAAI,CAACgB,UAAU,GAAG,EAAE;IACpB,IAAI,CAACQ,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACL,cAAc,EAAE;EACvB;EAEA;;;EAGAA,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACH,UAAU,IAAI,CAAC,IAAI,CAACQ,gBAAgB,EAAE;MAC9C;MACA,IAAI,CAACW,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACqD,QAAQ,CAAC;MAC1C;;IAGF;IACA,IAAI,CAACrD,gBAAgB,GAAG,IAAI,CAACqD,QAAQ,CAACiB,MAAM,CAACC,OAAO,IAAG;MACrD;MACA,MAAMC,iBAAiB,GAAG,CAAC,IAAI,CAAC3F,UAAU,IACvC0F,OAAO,CAAChG,KAAK,IAAIgG,OAAO,CAAChG,KAAK,CAACkG,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAC7F,UAAU,CAAC4F,WAAW,EAAE,CAAE,IACrFF,OAAO,CAAClC,WAAW,IAAIkC,OAAO,CAAClC,WAAW,CAACoC,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAC7F,UAAU,CAAC4F,WAAW,EAAE,CAAE;MAEpG;MACA,MAAME,eAAe,GAAG,CAAC,IAAI,CAACtF,gBAAgB,IAC3CkF,OAAO,CAACtB,QAAQ,IAAIsB,OAAO,CAACtB,QAAQ,CAAC1B,GAAG,KAAK,IAAI,CAAClC,gBAAiB;MAEtE;MACA,OAAOmF,iBAAiB,IAAIG,eAAe;IAC7C,CAAC,CAAC;EACJ;EAIAX,YAAYA,CAAA;IACV,IAAI,CAACtD,OAAO,GAAG,IAAI;IACnB,IAAI,CAACuB,YAAY,GAAG,KAAK,CAAC,CAAC;IAE3B,MAAM2C,MAAM,GAAG,IAAI,CAACpB,WAAW,CAACqB,gBAAgB,EAAE;IAClD,MAAMC,QAAQ,GAAG,IAAI,CAACtB,WAAW,CAACC,kBAAkB,EAAE;IAEtD,IAAI,CAACmB,MAAM,EAAE;MACX,IAAI,CAACtE,KAAK,GAAG,0BAA0B;MACvC,IAAI,CAACI,OAAO,GAAG,KAAK;MACpB;;IAGF;IACA;IACA,MAAMqE,iBAAiB,GAAGD,QAAQ,KAAK,OAAO,GAC1C,IAAI,CAACnB,cAAc,CAACqB,mBAAmB,EAAE,GACzC,IAAI,CAACrB,cAAc,CAACsB,qBAAqB,CAACL,MAAM,CAAC;IAErDG,iBAAiB,CAACG,SAAS,CAAC;MAC1BC,IAAI,EAAGC,QAAa,IAAI;QACtBnB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEkB,QAAQ,CAAC;QAE3C;QACA,IAAI,CAAC9E,KAAK,GAAG,IAAI;QAEjB;QACA8D,UAAU,CAAC,MAAK;UACd;UACA,IAAIf,QAAQ,GAAGyB,QAAQ,KAAK,OAAO,GAC9BM,QAAQ,CAACC,IAAI,IAAID,QAAQ,CAAC/B,QAAQ,IAAI,EAAE,GACxC+B,QAAQ,CAAC/B,QAAQ,IAAI,EAAG;UAE7BY,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEb,QAAQ,CAAC;UACxDY,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEb,QAAQ,CAAC,CAAC,CAAC,CAAC;UAE7D;UACAA,QAAQ,GAAG,IAAI,CAACiC,kCAAkC,CAACjC,QAAQ,CAAC;UAE5D;UACA,IAAI,CAACA,QAAQ,GAAG,IAAI,CAACkC,mCAAmC,CAAClC,QAAQ,CAAC;UAElE;UACA,IAAI,CAACrD,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAACqD,QAAQ,CAAC;UAE1C;UACA,IAAI,CAACmC,sBAAsB,EAAE;UAE7B,IAAI,CAAC9E,OAAO,GAAG,KAAK;UAEpB;UACA0D,UAAU,CAAC,MAAK;YACd,IAAI,CAACnC,YAAY,GAAG,IAAI;UAC1B,CAAC,EAAE,GAAG,CAAC;QACT,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;MACX,CAAC;;MACD3B,KAAK,EAAGA,KAAU,IAAI;QACpB2D,OAAO,CAAC3D,KAAK,CAAC,mBAAmB,EAAEmF,IAAI,CAACC,SAAS,CAACpF,KAAK,CAAC,CAAC;QACzD,IAAI,CAACA,KAAK,GAAG,2CAA2CA,KAAK,CAACC,OAAO,IAAID,KAAK,CAACqF,UAAU,IAAI,iBAAiB,EAAE;QAChH,IAAI,CAACjF,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAmC,cAAcA,CAACC,MAAc;IAC3B,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,OAAO,2BAA2B;MACpD,KAAK,UAAU;QAAE,OAAO,+BAA+B;MACvD,KAAK,UAAU;QAAE,OAAO,6BAA6B;MACrD,KAAK,SAAS;QAAE,OAAO,yBAAyB;MAChD;QAAS,OAAO,2BAA2B;;EAE/C;EAEAjB,WAAWA,CAACzD,EAAS;IACnB6F,OAAO,CAACC,GAAG,CAAC9F,EAAE,CAAC;IACb,IAAI,IAAI,CAACiF,QAAQ,EAAE;MACnB,IAAI,CAACO,MAAM,CAACgC,QAAQ,CAAC,CAAC,oBAAoB,EAAExH,EAAE,CAAC,CAAC;;EAGpD;EAEA;;;;EAIAkD,aAAaA,CAAClD,EAAU;IACtB6F,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAE9F,EAAE,CAAC;IAEtE,IAAIyH,OAAO,CAAC,oDAAoD,CAAC,EAAE;MACjE,MAAMf,QAAQ,GAAG,IAAI,CAACtB,WAAW,CAACC,kBAAkB,EAAE;MACtDQ,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEY,QAAQ,CAAC;MAE7C;MACA,MAAMgB,gBAAgB,GAAGhB,QAAQ,KAAK,OAAO,GACzC,IAAI,CAACnB,cAAc,CAACoC,kBAAkB,CAAC3H,EAAE,CAAC,GAC1C,IAAI,CAACuF,cAAc,CAACrC,aAAa,CAAClD,EAAE,CAAC;MAEzC6F,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MAEvD4B,gBAAgB,CAACZ,SAAS,CAAC;QACzBC,IAAI,EAAGC,QAAQ,IAAI;UACjBnB,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEkB,QAAQ,CAAC;UACzD,IAAI,CAACY,wBAAwB,CAAC5H,EAAE,CAAC;QACnC,CAAC;QACDkC,KAAK,EAAGA,KAAK,IAAI;UACf2D,OAAO,CAAC3D,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;UACxD2D,OAAO,CAAC3D,KAAK,CAAC,0BAA0B,EAAE;YACxC2F,MAAM,EAAE3F,KAAK,CAAC2F,MAAM;YACpBN,UAAU,EAAErF,KAAK,CAACqF,UAAU;YAC5BpF,OAAO,EAAED,KAAK,CAACA,KAAK,EAAEC,OAAO;YAC7B2F,SAAS,EAAE5F;WACZ,CAAC;UAEF;UACA;UACA,IAAIA,KAAK,CAAC2F,MAAM,KAAK,CAAC,IAAI3F,KAAK,CAAC2F,MAAM,KAAK,GAAG,EAAE;YAC9ChC,OAAO,CAACC,GAAG,CAAC,uGAAuG,CAAC;YACpH,IAAI,CAAC8B,wBAAwB,CAAC5H,EAAE,CAAC;YACjC;;UAGF;UACA;UACA,IAAIkC,KAAK,CAAC2F,MAAM,IAAI,GAAG,EAAE;YACvBhC,OAAO,CAACC,GAAG,CAAC,sEAAsE,CAAC;YACnFE,UAAU,CAAC,MAAK;cACd,IAAI,CAACJ,YAAY,EAAE;YACrB,CAAC,EAAE,IAAI,CAAC;;UAGV;UACA,IAAI1D,KAAK,CAAC2F,MAAM,KAAK,GAAG,EAAE;YACxB,IAAI,CAACnC,YAAY,CAACqC,YAAY,CAAC,yBAAyB,EAAE7F,KAAK,CAAC2F,MAAM,CAAC;WACxE,MAAM,IAAI3F,KAAK,CAAC2F,MAAM,KAAK,GAAG,EAAE;YAC/B,IAAI,CAACnC,YAAY,CAACxD,KAAK,CACrB,cAAc,EACd,qDAAqD,CACtD;WACF,MAAM;YACL,MAAM8F,YAAY,GAAG9F,KAAK,CAACA,KAAK,EAAEC,OAAO,IAAI,6CAA6C;YAC1F,IAAI,CAACuD,YAAY,CAACxD,KAAK,CACrB,uBAAuB,EACvB8F,YAAY,EACZ,IAAI,CACL;;QAEL;OACD,CAAC;KACH,MAAM;MACLnC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;;EAE3D;EAEQ8B,wBAAwBA,CAAC5H,EAAU;IACzC6F,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAE9F,EAAE,CAAC;IAEtE;IACA,MAAMiI,YAAY,GAAG,IAAI,CAAChD,QAAQ,CAACpD,MAAM;IACzC,IAAI,CAACoD,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACiB,MAAM,CAACC,OAAO,IAC1CA,OAAO,CAAChD,GAAG,KAAKnD,EAAE,IAAImG,OAAO,CAACnG,EAAE,KAAKA,EAAE,CACxC;IACD,IAAI,CAAC4B,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACsE,MAAM,CAACC,OAAO,IAC1DA,OAAO,CAAChD,GAAG,KAAKnD,EAAE,IAAImG,OAAO,CAACnG,EAAE,KAAKA,EAAE,CACxC;IAED,MAAMkI,UAAU,GAAG,IAAI,CAACjD,QAAQ,CAACpD,MAAM;IACvCgE,OAAO,CAACC,GAAG,CAAC,kCAAkCmC,YAAY,YAAYC,UAAU,EAAE,CAAC;IAEnF;IACA,IAAI,CAACd,sBAAsB,EAAE;IAE7B;IACA,IAAI,CAAC1B,YAAY,CAACyC,OAAO,CACvB,mBAAmB,EACnB,wCAAwC,CACzC;IAEDtC,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;IAE9D;IACA,IAAI,CAACF,YAAY,EAAE;EACrB;EAEAwC,iBAAiBA,CAACnE,WAAmB;IACnC,IAAI,CAACA,WAAW,EAAE,OAAO,IAAI,CAACwB,SAAS,CAAC4C,uBAAuB,CAAC,EAAE,CAAC;IAEnE;IACA,MAAMC,aAAa,GAAGrE,WAAW,CAACsE,OAAO,CACvC,4BAA4B,EAC5B,wEAAwE,CACzE;IAED;IACA,OAAO,IAAI,CAAC9C,SAAS,CAAC4C,uBAAuB,CAACC,aAAa,CAAC;EAC9D;EAEA;;;;;EAKAxE,sBAAsBA,CAACqC,OAAY;IACjC,IAAI,CAACA,OAAO,CAAClC,WAAW,EAAE,OAAO,KAAK;IAEtC;IACA,MAAMuE,QAAQ,GAAG,CACf,uBAAuB,EACvB,uBAAuB,EACvB,cAAc,EACd,kBAAkB,EAClB,2BAA2B,EAC3B,2BAA2B,CAC5B;IAED;IACA,OAAOA,QAAQ,CAACC,IAAI,CAACC,OAAO,IAAIA,OAAO,CAACC,IAAI,CAACxC,OAAO,CAAClC,WAAW,CAAC,CAAC;EACpE;EAEA;;;;;EAKAkD,mCAAmCA,CAAClC,QAAe;IACjD,IAAI,CAACA,QAAQ,IAAI,CAACA,QAAQ,CAACpD,MAAM,EAAE,OAAO,EAAE;IAE5CgE,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEb,QAAQ,CAACpD,MAAM,CAAC;IAE/D;IACAoD,QAAQ,CAAC2D,OAAO,CAAC,CAACzC,OAAO,EAAE0C,KAAK,KAAI;MAClC,MAAMC,WAAW,GAAG,IAAI,CAAChF,sBAAsB,CAACqC,OAAO,CAAC;MACxDN,OAAO,CAACC,GAAG,CAAC,WAAW+C,KAAK,GAAG,CAAC,aAAa1C,OAAO,CAAChG,KAAK,kBAAkBgG,OAAO,CAAClC,WAAW,2BAA2B6E,WAAW,EAAE,CAAC;IAC1I,CAAC,CAAC;IAEF;IACA,MAAMC,cAAc,GAAG,CAAC,GAAG9D,QAAQ,CAAC,CAAC+D,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACjD,MAAMC,uBAAuB,GAAG,IAAI,CAACrF,sBAAsB,CAACmF,CAAC,CAAC;MAC9D,MAAMG,uBAAuB,GAAG,IAAI,CAACtF,sBAAsB,CAACoF,CAAC,CAAC;MAE9D,IAAIC,uBAAuB,IAAI,CAACC,uBAAuB,EAAE;QACvD,OAAO,CAAC,CAAC,CAAC,CAAC;;;MAEb,IAAI,CAACD,uBAAuB,IAAIC,uBAAuB,EAAE;QACvD,OAAO,CAAC,CAAC,CAAC;;MAGZ;MACA,OAAO,IAAIC,IAAI,CAACH,CAAC,CAAC7E,IAAI,CAAC,CAACiF,OAAO,EAAE,GAAG,IAAID,IAAI,CAACJ,CAAC,CAAC5E,IAAI,CAAC,CAACiF,OAAO,EAAE;IAChE,CAAC,CAAC;IAEFzD,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9CiD,cAAc,CAACH,OAAO,CAAC,CAACzC,OAAO,EAAE0C,KAAK,KAAI;MACxC,MAAMC,WAAW,GAAG,IAAI,CAAChF,sBAAsB,CAACqC,OAAO,CAAC;MACxDN,OAAO,CAACC,GAAG,CAAC,YAAY+C,KAAK,GAAG,CAAC,aAAa1C,OAAO,CAAChG,KAAK,2BAA2B2I,WAAW,EAAE,CAAC;IACtG,CAAC,CAAC;IAEF,OAAOC,cAAc;EACvB;EAEA;;;;;EAKA;;;EAGA3B,sBAAsBA,CAAA;IACpB;IACA,MAAMmC,YAAY,GAAG,IAAIC,GAAG,EAAE;IAE9B;IACA,IAAI,CAACvE,QAAQ,CAAC2D,OAAO,CAACzC,OAAO,IAAG;MAC9B,IAAIA,OAAO,CAACtB,QAAQ,IAAIsB,OAAO,CAACtB,QAAQ,CAAC1B,GAAG,EAAE;QAC5C;QACA,IAAI,CAACoG,YAAY,CAACE,GAAG,CAACtD,OAAO,CAACtB,QAAQ,CAAC1B,GAAG,CAAC,EAAE;UAC3CoG,YAAY,CAACG,GAAG,CAACvD,OAAO,CAACtB,QAAQ,CAAC1B,GAAG,EAAE;YACrCnD,EAAE,EAAEmG,OAAO,CAACtB,QAAQ,CAAC1B,GAAG;YACxBhD,KAAK,EAAEgG,OAAO,CAACtB,QAAQ,CAAC1E;WACzB,CAAC;;;IAGR,CAAC,CAAC;IAEF;IACA,IAAI,CAACsB,eAAe,GAAGkI,KAAK,CAACC,IAAI,CAACL,YAAY,CAACM,MAAM,EAAE,CAAC;IAExD;IACA,IAAI,CAACpI,eAAe,CAACuH,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC9I,KAAK,CAAC2J,aAAa,CAACZ,CAAC,CAAC/I,KAAK,CAAC,CAAC;EACrE;EAEA;;;EAGA+G,kCAAkCA,CAACjC,QAAe;IAChD,IAAI,CAACA,QAAQ,IAAIA,QAAQ,CAACpD,MAAM,KAAK,CAAC,EAAE,OAAOoD,QAAQ;IAEvD;IACA,MAAM8E,yBAAyB,GAAG9E,QAAQ,CAACwD,IAAI,CAACtC,OAAO,IAAI,IAAI,CAACrC,sBAAsB,CAACqC,OAAO,CAAC,CAAC;IAEhG;IACA,IAAI,CAAC4D,yBAAyB,EAAE;MAC9BlE,OAAO,CAACC,GAAG,CAAC,yEAAyE,CAAC;MAEtF;MACA,IAAIb,QAAQ,CAACpD,MAAM,GAAG,CAAC,EAAE;QACvB,MAAMsE,OAAO,GAAGlB,QAAQ,CAAC,CAAC,CAAC;QAC3BkB,OAAO,CAAClC,WAAW,GAAGkC,OAAO,CAAClC,WAAW,GACrCkC,OAAO,CAAClC,WAAW,GAAG,yBAAyB,GAC/C,wBAAwB;QAC5B4B,OAAO,CAACC,GAAG,CAAC,iDAAiDK,OAAO,CAAChG,KAAK,EAAE,CAAC;;MAG/E;MACA,IAAI8E,QAAQ,CAACpD,MAAM,IAAI,CAAC,EAAE;QACxB,MAAMsE,OAAO,GAAGlB,QAAQ,CAAC,CAAC,CAAC;QAC3BkB,OAAO,CAAClC,WAAW,GAAGkC,OAAO,CAAClC,WAAW,GACrCkC,OAAO,CAAClC,WAAW,GAAG,yBAAyB,GAC/C,wBAAwB;QAC5B4B,OAAO,CAACC,GAAG,CAAC,iDAAiDK,OAAO,CAAChG,KAAK,EAAE,CAAC;;;IAIjF,OAAO8E,QAAQ;EACjB;;;uBAjaWC,oBAAoB,EAAAlG,EAAA,CAAAgL,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAlL,EAAA,CAAAgL,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAApL,EAAA,CAAAgL,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAAtL,EAAA,CAAAgL,iBAAA,CAAAO,EAAA,CAAAC,YAAA,GAAAxL,EAAA,CAAAgL,iBAAA,CAAAS,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAApBxF,oBAAoB;MAAAyF,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChCjCjM,EAAA,CAAAC,cAAA,aAAmE;UAGLD,EAAA,CAAAa,MAAA,GAAe;UAAAb,EAAA,CAAAY,YAAA,EAAK;UAG5EZ,EAAA,CAAAC,cAAA,aAAsB;UACZD,EAAA,CAAAE,UAAA,mBAAAiM,sDAAA;YAAA,OAASD,GAAA,CAAAjF,eAAA,EAAiB;UAAA,EAAC;UACjCjH,EAAA,CAAAU,cAAA,EAAgG;UAAhGV,EAAA,CAAAC,cAAA,aAAgG;UAC9FD,EAAA,CAAAW,SAAA,cAAwH;UAC1HX,EAAA,CAAAY,YAAA,EAAM;UACNZ,EAAA,CAAAa,MAAA,mBACF;UAAAb,EAAA,CAAAY,YAAA,EAAS;UAKbZ,EAAA,CAAA6B,UAAA,KAAAuK,oCAAA,kBAiDM;UAENpM,EAAA,CAAA6B,UAAA,KAAAwK,oCAAA,iBAKM;UAGNrM,EAAA,CAAA6B,UAAA,KAAAyK,oCAAA,kBAOM;UACRtM,EAAA,CAAAY,YAAA,EAAM;UAENZ,EAAA,CAAA6B,UAAA,KAAA0K,oCAAA,kBAGM;UAENvM,EAAA,CAAA6B,UAAA,KAAA2K,oCAAA,kBAOM;UAENxM,EAAA,CAAA6B,UAAA,KAAA4K,oCAAA,kBAQM;UAENzM,EAAA,CAAA6B,UAAA,KAAA6K,oCAAA,kBA+FM;UACR1M,EAAA,CAAAY,YAAA,EAAM;;;UA3MwDZ,EAAA,CAAAiB,SAAA,GAAe;UAAfjB,EAAA,CAAAkB,iBAAA,CAAAgL,GAAA,CAAA/F,SAAA,CAAe;UAcnEnG,EAAA,CAAAiB,SAAA,GAAmB;UAAnBjB,EAAA,CAAAc,UAAA,SAAAoL,GAAA,CAAA1J,aAAA,CAAmB;UAmDnBxC,EAAA,CAAAiB,SAAA,GAAqC;UAArCjB,EAAA,CAAAc,UAAA,UAAAoL,GAAA,CAAA5I,OAAA,IAAA4I,GAAA,CAAAjG,QAAA,CAAApD,MAAA,KAAqC;UAQrC7C,EAAA,CAAAiB,SAAA,GAAoC;UAApCjB,EAAA,CAAAc,UAAA,SAAAoL,GAAA,CAAAzK,UAAA,IAAAyK,GAAA,CAAAjK,gBAAA,CAAoC;UAUtCjC,EAAA,CAAAiB,SAAA,GAAa;UAAbjB,EAAA,CAAAc,UAAA,SAAAoL,GAAA,CAAA5I,OAAA,CAAa;UAKbtD,EAAA,CAAAiB,SAAA,GAAW;UAAXjB,EAAA,CAAAc,UAAA,SAAAoL,GAAA,CAAAhJ,KAAA,CAAW;UASXlD,EAAA,CAAAiB,SAAA,GAAuC;UAAvCjB,EAAA,CAAAc,UAAA,UAAAoL,GAAA,CAAA5I,OAAA,IAAA4I,GAAA,CAAAjG,QAAA,CAAApD,MAAA,OAAuC;UAUvC7C,EAAA,CAAAiB,SAAA,GAAqC;UAArCjB,EAAA,CAAAc,UAAA,UAAAoL,GAAA,CAAA5I,OAAA,IAAA4I,GAAA,CAAAjG,QAAA,CAAApD,MAAA,KAAqC;;;;;;mBDjG/B,CACVnD,OAAO,CAAC,QAAQ,EAAE,CAChBC,UAAU,CAAC,QAAQ,EAAE,CACnBC,KAAK,CAAC;UAAE+M,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAkB,CAAE,CAAC,EACpD/M,OAAO,CAAC,eAAe,EAAED,KAAK,CAAC;UAAE+M,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAe,CAAE,CAAC,CAAC,CAC5E,CAAC,CACH,CAAC,EACFlN,OAAO,CAAC,aAAa,EAAE,CACrBC,UAAU,CAAC,QAAQ,EAAE,CACnBG,KAAK,CAAC,QAAQ,EAAE,CACdF,KAAK,CAAC;UAAE+M,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAkB,CAAE,CAAC,EACpD7M,OAAO,CAAC,OAAO,EAAE,CACfF,OAAO,CAAC,eAAe,EAAED,KAAK,CAAC;UAAE+M,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAe,CAAE,CAAC,CAAC,CAC5E,CAAC,CACH,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC,CACvB,CAAC,CACH,CAAC;MACH;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}