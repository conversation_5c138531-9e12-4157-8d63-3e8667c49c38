import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'highlightPresence'
})
export class HighlightPresencePipe implements PipeTransform {

  transform(value: string, searchTerm?: string): string {
    if (!value || !searchTerm) {
      return value;
    }

    const regex = new RegExp(`(${searchTerm})`, 'gi');
    return value.replace(regex, '<mark class="bg-yellow-200 text-yellow-800">$1</mark>');
  }

}
