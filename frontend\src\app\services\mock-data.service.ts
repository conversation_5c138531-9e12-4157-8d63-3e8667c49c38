import { Injectable } from '@angular/core';
import { Observable, of, BehaviorSubject } from 'rxjs';
import { delay } from 'rxjs/operators';
import { 
  User, 
  Conversation, 
  Message, 
  Notification, 
  MessageType,
  NotificationType 
} from '../models/message.model';

@Injectable({
  providedIn: 'root'
})
export class MockDataService {
  
  // Utilisateurs de test
  private mockUsers: User[] = [
    {
      id: '1',
      username: '<PERSON>',
      email: '<EMAIL>',
      image: '/assets/images/avatars/alice.jpg',
      isOnline: true,
      role: 'developer'
    },
    {
      id: '2', 
      username: '<PERSON>',
      email: '<EMAIL>',
      image: '/assets/images/avatars/bob.jpg',
      isOnline: false,
      role: 'designer'
    },
    {
      id: '3',
      username: '<PERSON>',
      email: '<EMAIL>', 
      image: '/assets/images/avatars/claire.jpg',
      isOnline: true,
      role: 'manager'
    },
    {
      id: '4',
      username: '<PERSON>',
      email: '<EMAIL>',
      image: '/assets/images/avatars/david.jpg',
      isOnline: true,
      role: 'developer'
    },
    {
      id: '5',
      username: '<PERSON> <PERSON>',
      email: '<EMAIL>',
      image: '/assets/images/avatars/emma.jpg',
      isOnline: false,
      role: 'tester'
    }
  ];

  // Messages de test
  private mockMessages: Message[] = [
    {
      id: '1',
      content: 'Salut ! Comment ça va ?',
      type: MessageType.TEXT,
      timestamp: new Date(Date.now() - 3600000), // 1h ago
      sender: this.mockUsers[1],
      isRead: true,
      conversationId: 'conv1'
    },
    {
      id: '2', 
      content: 'Ça va bien merci ! Et toi ?',
      type: MessageType.TEXT,
      timestamp: new Date(Date.now() - 3500000), // 58min ago
      sender: this.mockUsers[0], // Current user
      isRead: true,
      conversationId: 'conv1'
    },
    {
      id: '3',
      content: 'Super ! Tu as vu le nouveau design ?',
      type: MessageType.TEXT,
      timestamp: new Date(Date.now() - 1800000), // 30min ago
      sender: this.mockUsers[1],
      isRead: false,
      conversationId: 'conv1'
    }
  ];

  // Conversations de test
  private mockConversations: Conversation[] = [
    {
      id: 'conv1',
      participants: [this.mockUsers[0], this.mockUsers[1]],
      lastMessage: this.mockMessages[2],
      unreadCount: 1,
      isGroup: false,
      createdAt: new Date(Date.now() - 86400000) // 1 day ago
    },
    {
      id: 'conv2',
      participants: [this.mockUsers[0], this.mockUsers[2]],
      lastMessage: {
        id: '4',
        content: 'Réunion à 14h ?',
        type: MessageType.TEXT,
        timestamp: new Date(Date.now() - 7200000), // 2h ago
        sender: this.mockUsers[2],
        isRead: true,
        conversationId: 'conv2'
      },
      unreadCount: 0,
      isGroup: false,
      createdAt: new Date(Date.now() - 172800000) // 2 days ago
    },
    {
      id: 'conv3',
      participants: [this.mockUsers[0], this.mockUsers[1], this.mockUsers[2], this.mockUsers[3]],
      lastMessage: {
        id: '5',
        content: 'Nouveau projet lancé ! 🚀',
        type: MessageType.TEXT,
        timestamp: new Date(Date.now() - 10800000), // 3h ago
        sender: this.mockUsers[3],
        isRead: false,
        conversationId: 'conv3'
      },
      unreadCount: 3,
      isGroup: true,
      groupName: 'Équipe DevBridge',
      groupPhoto: '/assets/images/groups/team.jpg',
      createdAt: new Date(Date.now() - 259200000) // 3 days ago
    }
  ];

  // Notifications de test
  private mockNotifications: Notification[] = [
    {
      id: 'notif1',
      type: NotificationType.NEW_MESSAGE,
      content: 'Nouveau message de Bob Dupont',
      timestamp: new Date(Date.now() - 1800000), // 30min ago
      isRead: false,
      userId: '1'
    },
    {
      id: 'notif2',
      type: NotificationType.MESSAGE_REACTION,
      content: 'Alice a réagi à votre message avec ❤️',
      timestamp: new Date(Date.now() - 3600000), // 1h ago
      isRead: true,
      userId: '1'
    },
    {
      id: 'notif3',
      type: NotificationType.GROUP_INVITE,
      content: 'Vous avez été ajouté au groupe "Équipe DevBridge"',
      timestamp: new Date(Date.now() - 7200000), // 2h ago
      isRead: false,
      userId: '1'
    }
  ];

  constructor() {}

  // ============================================================================
  // MÉTHODES PUBLIQUES POUR LES TESTS
  // ============================================================================

  /**
   * Récupère tous les utilisateurs
   */
  getUsers(): Observable<User[]> {
    return of(this.mockUsers).pipe(delay(500)); // Simule la latence réseau
  }

  /**
   * Récupère toutes les conversations
   */
  getConversations(): Observable<Conversation[]> {
    return of(this.mockConversations).pipe(delay(300));
  }

  /**
   * Récupère une conversation par ID
   */
  getConversation(id: string): Observable<Conversation | null> {
    const conversation = this.mockConversations.find(c => c.id === id);
    return of(conversation || null).pipe(delay(200));
  }

  /**
   * Récupère les messages d'une conversation
   */
  getMessages(conversationId: string): Observable<Message[]> {
    const messages = this.mockMessages.filter(m => m.conversationId === conversationId);
    return of(messages).pipe(delay(300));
  }

  /**
   * Récupère toutes les notifications
   */
  getNotifications(): Observable<Notification[]> {
    return of(this.mockNotifications).pipe(delay(200));
  }

  /**
   * Simule l'envoi d'un message
   */
  sendMessage(content: string, conversationId: string, senderId: string): Observable<Message> {
    const newMessage: Message = {
      id: `msg_${Date.now()}`,
      content,
      type: MessageType.TEXT,
      timestamp: new Date(),
      sender: this.mockUsers.find(u => u.id === senderId) || this.mockUsers[0],
      isRead: false,
      conversationId
    };

    // Ajouter le message à la liste
    this.mockMessages.push(newMessage);

    // Mettre à jour la conversation
    const conversation = this.mockConversations.find(c => c.id === conversationId);
    if (conversation) {
      conversation.lastMessage = newMessage;
    }

    return of(newMessage).pipe(delay(100));
  }

  /**
   * Simule la création d'une conversation
   */
  createConversation(userId: string, currentUserId: string): Observable<Conversation> {
    const otherUser = this.mockUsers.find(u => u.id === userId);
    const currentUser = this.mockUsers.find(u => u.id === currentUserId);
    
    if (!otherUser || !currentUser) {
      throw new Error('Utilisateur non trouvé');
    }

    const newConversation: Conversation = {
      id: `conv_${Date.now()}`,
      participants: [currentUser, otherUser],
      unreadCount: 0,
      isGroup: false,
      createdAt: new Date()
    };

    this.mockConversations.unshift(newConversation);
    return of(newConversation).pipe(delay(200));
  }

  /**
   * Récupère l'utilisateur actuel (pour les tests)
   */
  getCurrentUser(): User {
    return this.mockUsers[0]; // Alice comme utilisateur actuel
  }

  /**
   * Simule la recherche d'utilisateurs
   */
  searchUsers(query: string): Observable<User[]> {
    const results = this.mockUsers.filter(user => 
      user.username.toLowerCase().includes(query.toLowerCase()) ||
      user.email.toLowerCase().includes(query.toLowerCase())
    );
    return of(results).pipe(delay(300));
  }

  /**
   * Simule la recherche de conversations
   */
  searchConversations(query: string): Observable<Conversation[]> {
    const results = this.mockConversations.filter(conv => {
      if (conv.isGroup) {
        return conv.groupName?.toLowerCase().includes(query.toLowerCase());
      } else {
        return conv.participants?.some(p => 
          p.username.toLowerCase().includes(query.toLowerCase())
        );
      }
    });
    return of(results).pipe(delay(300));
  }
}
