{"ast": null, "code": "import { BehaviorSubject, Observable, of, throwError, retry, EMPTY } from 'rxjs';\nimport { map, catchError, tap, filter, debounceTime, distinctUntilChanged, shareReplay } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport { MessageType, CallType, CallStatus } from '../models/message.model';\nimport { GET_CONVERSATIONS_QUERY, GET_NOTIFICATIONS_QUERY, NOTIFICATION_SUBSCRIPTION, GET_CONVERSATION_QUERY, SEND_MESSAGE_MUTATION, MARK_AS_READ_MUTATION, MESSAGE_SENT_SUBSCRIPTION, USER_STATUS_SUBSCRIPTION, GET_USER_QUERY, GET_ALL_USER_QUERY, CONVERSATION_UPDATED_SUBSCRIPTION, SEARCH_MESSAGES_QUERY, GET_UNREAD_MESSAGES_QUERY, SET_USER_ONLINE_MUTATION, SET_USER_OFFLINE_MUTATION, START_TYPING_MUTATION, STOP_TYPING_MUTATION, TYPING_INDICATOR_SUBSCRIPTION, GET_CURRENT_USER_QUERY, REACT_TO_MESSAGE_MUTATION, FORWARD_MESSAGE_MUTATION, PIN_MESSAGE_MUTATION, CREATE_GROUP_MUTATION, UPDATE_GROUP_MUTATION, DELETE_GROUP_MUTATION, LEAVE_GROUP_MUTATION, GET_GROUP_QUERY, GET_USER_GROUPS_QUERY, EDIT_MESSAGE_MUTATION, DELETE_MESSAGE_MUTATION, GET_MESSAGES_QUERY, GET_NOTIFICATIONS_ATTACHAMENTS, MARK_NOTIFICATION_READ_MUTATION, NOTIFICATIONS_READ_SUBSCRIPTION, CREATE_CONVERSATION_MUTATION, DELETE_NOTIFICATION_MUTATION, DELETE_MULTIPLE_NOTIFICATIONS_MUTATION, DELETE_ALL_NOTIFICATIONS_MUTATION,\n// Requêtes et mutations pour les appels\nCALL_HISTORY_QUERY, CALL_DETAILS_QUERY, CALL_STATS_QUERY, INITIATE_CALL_MUTATION, SEND_CALL_SIGNAL_MUTATION, CALL_SIGNAL_SUBSCRIPTION, INCOMING_CALL_SUBSCRIPTION, GET_VOICE_MESSAGES_QUERY } from '../graphql/message.graphql';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"apollo-angular\";\nimport * as i2 from \"./logger.service\";\nexport class MessageService {\n  constructor(apollo, logger, zone) {\n    this.apollo = apollo;\n    this.logger = logger;\n    this.zone = zone;\n    // État partagé\n    this.activeConversation = new BehaviorSubject(null);\n    this.notifications = new BehaviorSubject([]);\n    this.notificationCache = new Map();\n    this.notificationCount = new BehaviorSubject(0);\n    this.onlineUsers = new Map();\n    this.subscriptions = [];\n    this.CACHE_DURATION = 300000;\n    this.lastFetchTime = 0;\n    // Propriétés pour les appels\n    this.activeCall = new BehaviorSubject(null);\n    this.incomingCall = new BehaviorSubject(null);\n    this.callSignals = new BehaviorSubject(null);\n    this.localStream = null;\n    this.remoteStream = null;\n    this.peerConnection = null;\n    // Observables publics pour les appels\n    this.activeCall$ = this.activeCall.asObservable();\n    this.incomingCall$ = this.incomingCall.asObservable();\n    this.callSignals$ = this.callSignals.asObservable();\n    this.localStream$ = new BehaviorSubject(null);\n    this.remoteStream$ = new BehaviorSubject(null);\n    // Configuration WebRTC\n    this.rtcConfig = {\n      iceServers: [{\n        urls: 'stun:stun.l.google.com:19302'\n      }, {\n        urls: 'stun:stun1.l.google.com:19302'\n      }]\n    };\n    this.usersCache = [];\n    // Pagination metadata for user list\n    this.currentUserPagination = {\n      totalCount: 0,\n      totalPages: 0,\n      currentPage: 1,\n      hasNextPage: false,\n      hasPreviousPage: false\n    };\n    // Observables publics\n    this.activeConversation$ = this.activeConversation.asObservable();\n    this.notifications$ = this.notifications.asObservable();\n    this.notificationCount$ = this.notificationCount.asObservable();\n    // Propriétés pour la gestion des sons\n    this.sounds = {};\n    this.isPlaying = {};\n    this.muted = false;\n    // --------------------------------------------------------------------------\n    // Section 2: Méthodes pour les Notifications\n    // --------------------------------------------------------------------------\n    // Propriétés pour la pagination des notifications\n    this.notificationPagination = {\n      currentPage: 1,\n      limit: 10,\n      hasMoreNotifications: true\n    };\n    // --------------------------------------------------------------------------\n    // Section 4: Subscriptions et Gestion Temps Réel\n    // --------------------------------------------------------------------------\n    // ✅ Optimized subscription with connection pooling and caching\n    this.subscriptionCache = new Map();\n    this.subscriptionRefCount = new Map();\n    this.toSafeISOString = date => {\n      if (!date) return undefined;\n      return typeof date === 'string' ? date : date.toISOString();\n    };\n    this.loadNotificationsFromLocalStorage();\n    this.initSubscriptions();\n    this.startCleanupInterval();\n    this.preloadSounds();\n  }\n  /**\n   * Charge les notifications depuis le localStorage\n   * @private\n   */\n  loadNotificationsFromLocalStorage() {\n    try {\n      const savedNotifications = localStorage.getItem('notifications');\n      if (savedNotifications) {\n        const notifications = JSON.parse(savedNotifications);\n        this.notificationCache.clear();\n        notifications.forEach(notification => {\n          if (notification && notification.id) {\n            this.notificationCache.set(notification.id, notification);\n          }\n        });\n        this.notifications.next(Array.from(this.notificationCache.values()));\n        this.updateUnreadCount();\n      }\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n  initSubscriptions() {\n    this.zone.runOutsideAngular(() => {\n      this.subscribeToNewNotifications().subscribe();\n      this.subscribeToNotificationsRead().subscribe();\n      this.subscribeToIncomingCalls().subscribe();\n      // 🔥 AJOUT: Subscription générale pour l'utilisateur\n    });\n\n    this.subscribeToUserStatus();\n  }\n  /**\n   * S'abonne aux appels entrants\n   */\n  subscribeToIncomingCalls() {\n    return this.apollo.subscribe({\n      query: INCOMING_CALL_SUBSCRIPTION\n    }).pipe(map(({\n      data\n    }) => {\n      if (!data?.incomingCall) {\n        return null;\n      }\n      // Gérer l'appel entrant\n      this.handleIncomingCall(data.incomingCall);\n      return data.incomingCall;\n    }), catchError(error => {\n      this.logger.error('Error in incoming call subscription', error);\n      return of(null);\n    }));\n  }\n  /**\n   * Gère un appel entrant\n   */\n  handleIncomingCall(call) {\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n  }\n  // --------------------------------------------------------------------------\n  // Section: Subscriptions aux événements temps réel\n  // --------------------------------------------------------------------------\n  /**\n   * S'abonne aux nouveaux messages\n   */\n  subscribeToMessages() {\n    return this.apollo.subscribe({\n      query: MESSAGE_SENT_SUBSCRIPTION\n    }).pipe(map(({\n      data\n    }) => {\n      if (!data?.messageSent) {\n        return null;\n      }\n      return this.normalizeMessage(data.messageSent);\n    }), catchError(error => {\n      this.logger.error('Error in message subscription', error);\n      return of(null);\n    }));\n  }\n  /**\n   * S'abonne aux indicateurs de frappe\n   */\n  subscribeToTypingIndicators() {\n    return this.apollo.subscribe({\n      query: TYPING_INDICATOR_SUBSCRIPTION\n    }).pipe(map(({\n      data\n    }) => {\n      if (!data?.typingIndicator) {\n        return null;\n      }\n      return data.typingIndicator;\n    }), catchError(error => {\n      this.logger.error('Error in typing indicator subscription', error);\n      return of(null);\n    }));\n  }\n  /**\n   * Démarre l'indicateur de frappe\n   */\n  startTyping(conversationId) {\n    return this.apollo.mutate({\n      mutation: START_TYPING_MUTATION,\n      variables: {\n        conversationId\n      }\n    }).pipe(map(result => result.data?.startTyping || false), catchError(error => {\n      this.logger.error('Error starting typing indicator', error);\n      return of(false);\n    }));\n  }\n  /**\n   * Arrête l'indicateur de frappe\n   */\n  stopTyping(conversationId) {\n    return this.apollo.mutate({\n      mutation: STOP_TYPING_MUTATION,\n      variables: {\n        conversationId\n      }\n    }).pipe(map(result => result.data?.stopTyping || false), catchError(error => {\n      this.logger.error('Error stopping typing indicator', error);\n      return of(false);\n    }));\n  }\n  /**\n   * Initie un appel\n   */\n  initiateCall(recipientId, callType) {\n    return this.apollo.mutate({\n      mutation: INITIATE_CALL_MUTATION,\n      variables: {\n        recipientId,\n        callType\n      }\n    }).pipe(map(result => {\n      if (!result.data?.initiateCall) {\n        throw new Error('Failed to initiate call');\n      }\n      return result.data.initiateCall;\n    }), catchError(error => {\n      this.logger.error('Error initiating call', error);\n      return throwError(() => new Error('Failed to initiate call'));\n    }));\n  }\n  /**\n   * Envoie un message avec fichier\n   */\n  sendMessageWithFile(senderId, receiverId, content, file) {\n    return this.apollo.mutate({\n      mutation: SEND_MESSAGE_MUTATION,\n      variables: {\n        senderId,\n        receiverId,\n        content,\n        file\n      }\n    }).pipe(map(result => {\n      if (!result.data?.sendMessage) {\n        throw new Error('Failed to send message');\n      }\n      return this.normalizeMessage(result.data.sendMessage);\n    }), catchError(error => {\n      this.logger.error('Error sending message with file', error);\n      return throwError(() => new Error('Failed to send message'));\n    }));\n  }\n  /**\n   * Envoie un message simple\n   */\n  sendMessage(senderId, receiverId, content) {\n    return this.apollo.mutate({\n      mutation: SEND_MESSAGE_MUTATION,\n      variables: {\n        senderId,\n        receiverId,\n        content\n      }\n    }).pipe(map(result => {\n      if (!result.data?.sendMessage) {\n        throw new Error('Failed to send message');\n      }\n      return this.normalizeMessage(result.data.sendMessage);\n    }), catchError(error => {\n      this.logger.error('Error sending message', error);\n      return throwError(() => new Error('Failed to send message'));\n    }));\n  }\n  /**\n   * S'abonne aux notifications\n   */\n  subscribeToNotifications() {\n    return this.apollo.subscribe({\n      query: NOTIFICATION_SUBSCRIPTION\n    }).pipe(map(({\n      data\n    }) => {\n      if (!data?.notificationReceived) {\n        return null;\n      }\n      return data.notificationReceived;\n    }), catchError(error => {\n      this.logger.error('Error in notification subscription', error);\n      return of(null);\n    }));\n  }\n  /**\n   * Marque une notification comme lue\n   */\n  markNotificationAsRead(notificationId) {\n    return this.apollo.mutate({\n      mutation: MARK_NOTIFICATION_READ_MUTATION,\n      variables: {\n        notificationId\n      }\n    }).pipe(map(result => result.data?.markNotificationAsRead || false), catchError(error => {\n      this.logger.error('Error marking notification as read', error);\n      return of(false);\n    }));\n  }\n  /**\n   * Crée ou récupère une conversation avec un utilisateur\n   */\n  createOrGetConversation(userId) {\n    return this.apollo.mutate({\n      mutation: CREATE_CONVERSATION_MUTATION,\n      variables: {\n        userId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.createConversation) {\n        throw new Error('Failed to create or get conversation');\n      }\n      return this.normalizeConversation(result.data.createConversation);\n    }), catchError(error => {\n      this.logger.error('Error creating or getting conversation', error);\n      return throwError(() => new Error('Failed to create or get conversation'));\n    }));\n  }\n  // --------------------------------------------------------------------------\n  // Section: Gestion des sons (intégré depuis SoundService)\n  // --------------------------------------------------------------------------\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  preloadSounds() {\n    this.loadSound('ringtone', 'assets/sounds/ringtone.mp3');\n    this.loadSound('call-end', 'assets/sounds/call-end.mp3');\n    this.loadSound('call-connected', 'assets/sounds/call-connected.mp3');\n    this.loadSound('notification', 'assets/sounds/notification.mp3');\n  }\n  /**\n   * Charge un fichier audio\n   * @param name Nom du son\n   * @param path Chemin du fichier\n   */\n  loadSound(name, path) {\n    try {\n      const audio = new Audio(path);\n      audio.load();\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n      });\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n  /**\n   * Joue un son\n   * @param name Nom du son\n   * @param loop Lecture en boucle\n   */\n  play(name, loop = false) {\n    if (this.muted) {\n      return;\n    }\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        return;\n      }\n      sound.loop = loop;\n      if (!this.isPlaying[name]) {\n        sound.currentTime = 0;\n        sound.play().catch(error => {\n          // Handle error silently\n        });\n        this.isPlaying[name] = true;\n      }\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n  /**\n   * Arrête un son\n   * @param name Nom du son\n   */\n  stop(name) {\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        return;\n      }\n      if (this.isPlaying[name]) {\n        sound.pause();\n        sound.currentTime = 0;\n        this.isPlaying[name] = false;\n      }\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n  /**\n   * Arrête tous les sons\n   */\n  stopAllSounds() {\n    Object.keys(this.sounds).forEach(name => {\n      this.stop(name);\n    });\n  }\n  /**\n   * Active ou désactive le son\n   * @param muted True pour désactiver le son, false pour l'activer\n   */\n  setMuted(muted) {\n    this.muted = muted;\n    if (muted) {\n      this.stopAllSounds();\n    }\n  }\n  /**\n   * Vérifie si le son est désactivé\n   * @returns True si le son est désactivé, false sinon\n   */\n  isMuted() {\n    return this.muted;\n  }\n  /**\n   * Joue le son de notification\n   */\n  playNotificationSound() {\n    console.log('MessageService: Tentative de lecture du son de notification');\n    if (this.muted) {\n      console.log('MessageService: Son désactivé, notification ignorée');\n      return;\n    }\n    // Créer une mélodie agréable avec l'API Web Audio\n    try {\n      // Créer un contexte audio\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      // 🎵 TESTEZ DIFFÉRENTS SONS - Décommentez celui que vous voulez tester !\n      // SON 1: Mélodie douce (WhatsApp style) - ACTUEL\n      this.playNotificationMelody1(audioContext);\n      // SON 2: Mélodie montante (iPhone style) - Décommentez pour tester\n      // this.playNotificationMelody2(audioContext);\n      // SON 3: Mélodie descendante (Messenger style) - Décommentez pour tester\n      // this.playNotificationMelody3(audioContext);\n      // SON 4: Triple note (Discord style) - Décommentez pour tester\n      // this.playNotificationMelody4(audioContext);\n      // SON 5: Cloche douce (Slack style) - Décommentez pour tester\n      // this.playNotificationMelody5(audioContext);\n      console.log('MessageService: Son de notification mélodieux généré avec succès');\n    } catch (error) {\n      console.error('MessageService: Erreur lors de la génération du son:', error);\n      // Fallback à la méthode originale en cas d'erreur\n      try {\n        const audio = new Audio('assets/sounds/notification.mp3');\n        audio.volume = 0.7; // Volume plus doux\n        audio.play().catch(err => {\n          console.error('MessageService: Erreur lors de la lecture du fichier son:', err);\n        });\n      } catch (audioError) {\n        console.error('MessageService: Exception lors de la lecture du fichier son:', audioError);\n      }\n    }\n  }\n  // 🎵 SON 1: Mélodie douce (WhatsApp style)\n  playNotificationMelody1(audioContext) {\n    this.playNotificationTone(audioContext, 0, 659.25, 0.15); // E5\n    this.playNotificationTone(audioContext, 0.15, 523.25, 0.15); // C5\n  }\n  // 🎵 SON 2: Mélodie montante (iPhone style)\n  playNotificationMelody2(audioContext) {\n    this.playNotificationTone(audioContext, 0, 523.25, 0.12); // C5\n    this.playNotificationTone(audioContext, 0.12, 659.25, 0.12); // E5\n    this.playNotificationTone(audioContext, 0.24, 783.99, 0.16); // G5\n  }\n  // 🎵 SON 3: Mélodie descendante (Messenger style)\n  playNotificationMelody3(audioContext) {\n    this.playNotificationTone(audioContext, 0, 880, 0.1); // A5\n    this.playNotificationTone(audioContext, 0.1, 659.25, 0.1); // E5\n    this.playNotificationTone(audioContext, 0.2, 523.25, 0.15); // C5\n  }\n  // 🎵 SON 4: Triple note (Discord style)\n  playNotificationMelody4(audioContext) {\n    this.playNotificationTone(audioContext, 0, 698.46, 0.08); // F5\n    this.playNotificationTone(audioContext, 0.08, 698.46, 0.08); // F5\n    this.playNotificationTone(audioContext, 0.16, 880, 0.12); // A5\n  }\n  // 🎵 SON 5: Cloche douce (Slack style)\n  playNotificationMelody5(audioContext) {\n    this.playBellTone(audioContext, 0, 1046.5, 0.4); // C6 - son de cloche\n  }\n  /**\n   * Joue une note individuelle pour la mélodie de notification\n   */\n  playNotificationTone(audioContext, startTime, frequency, duration) {\n    const oscillator = audioContext.createOscillator();\n    const gainNode = audioContext.createGain();\n    // Configurer l'oscillateur pour un son plus doux\n    oscillator.type = 'sine';\n    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime + startTime);\n    // Configurer le volume avec une enveloppe douce\n    gainNode.gain.setValueAtTime(0, audioContext.currentTime + startTime);\n    gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + startTime + 0.02);\n    gainNode.gain.linearRampToValueAtTime(0.2, audioContext.currentTime + startTime + duration * 0.7);\n    gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + startTime + duration);\n    // Connecter les nœuds\n    oscillator.connect(gainNode);\n    gainNode.connect(audioContext.destination);\n    // Démarrer et arrêter l'oscillateur\n    oscillator.start(audioContext.currentTime + startTime);\n    oscillator.stop(audioContext.currentTime + startTime + duration);\n  }\n  /**\n   * Joue un son de cloche pour les notifications\n   */\n  playBellTone(audioContext, startTime, frequency, duration) {\n    const oscillator = audioContext.createOscillator();\n    const gainNode = audioContext.createGain();\n    // Configurer l'oscillateur pour un son de cloche\n    oscillator.type = 'triangle'; // Son plus doux que sine\n    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime + startTime);\n    // Enveloppe de cloche (attaque rapide, déclin lent)\n    gainNode.gain.setValueAtTime(0, audioContext.currentTime + startTime);\n    gainNode.gain.linearRampToValueAtTime(0.4, audioContext.currentTime + startTime + 0.01);\n    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + startTime + duration);\n    // Connecter les nœuds\n    oscillator.connect(gainNode);\n    gainNode.connect(audioContext.destination);\n    // Démarrer et arrêter l'oscillateur\n    oscillator.start(audioContext.currentTime + startTime);\n    oscillator.stop(audioContext.currentTime + startTime + duration);\n  }\n  // --------------------------------------------------------------------------\n  // Section 1: Méthodes pour les Messages\n  // --------------------------------------------------------------------------\n  /**\n   * Joue un fichier audio\n   * @param audioUrl URL du fichier audio à jouer\n   * @returns Promise qui se résout lorsque la lecture est terminée\n   */\n  playAudio(audioUrl) {\n    return new Promise((resolve, reject) => {\n      const audio = new Audio(audioUrl);\n      audio.onended = () => {\n        resolve();\n      };\n      audio.onerror = error => {\n        this.logger.error(`[MessageService] Error playing audio:`, error);\n        reject(error);\n      };\n      audio.play().catch(error => {\n        this.logger.error(`[MessageService] Error playing audio:`, error);\n        reject(error);\n      });\n    });\n  }\n  /**\n   * Récupère tous les messages vocaux de l'utilisateur\n   * @returns Observable avec la liste des messages vocaux\n   */\n  getVoiceMessages() {\n    this.logger.debug('[MessageService] Getting voice messages');\n    return this.apollo.watchQuery({\n      query: GET_VOICE_MESSAGES_QUERY,\n      fetchPolicy: 'network-only' // Ne pas utiliser le cache pour cette requête\n    }).valueChanges.pipe(map(result => {\n      const voiceMessages = result.data?.getVoiceMessages || [];\n      this.logger.debug(`[MessageService] Retrieved ${voiceMessages.length} voice messages`);\n      return voiceMessages;\n    }), catchError(error => {\n      this.logger.error('[MessageService] Error fetching voice messages:', error);\n      return throwError(() => new Error('Failed to fetch voice messages'));\n    }));\n  }\n  // Message methods\n  getMessages(senderId, receiverId, conversationId, page = 1, limit = 25 // ✅ Increased batch size for better performance\n  ) {\n    return this.apollo.watchQuery({\n      query: GET_MESSAGES_QUERY,\n      variables: {\n        senderId,\n        receiverId,\n        conversationId,\n        limit,\n        page\n      },\n      fetchPolicy: 'cache-first',\n      errorPolicy: 'all' // ✅ Handle partial errors gracefully\n    }).valueChanges.pipe(map(result => {\n      const messages = result.data?.getMessages || [];\n      // ✅ Batch normalize messages for better performance\n      return this.batchNormalizeMessages(messages);\n    }), catchError(error => {\n      console.error('Error fetching messages:', error);\n      return throwError(() => new Error('Failed to fetch messages'));\n    }));\n  }\n  editMessage(messageId, newContent) {\n    return this.apollo.mutate({\n      mutation: EDIT_MESSAGE_MUTATION,\n      variables: {\n        messageId,\n        newContent\n      }\n    }).pipe(map(result => {\n      if (!result.data?.editMessage) {\n        throw new Error('Failed to edit message');\n      }\n      return this.normalizeMessage(result.data.editMessage);\n    }), catchError(error => {\n      this.logger.error('Error editing message:', error);\n      return throwError(() => new Error('Failed to edit message'));\n    }));\n  }\n  deleteMessage(messageId) {\n    return this.apollo.mutate({\n      mutation: DELETE_MESSAGE_MUTATION,\n      variables: {\n        messageId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.deleteMessage) {\n        throw new Error('Failed to delete message');\n      }\n      return this.normalizeMessage(result.data.deleteMessage);\n    }), catchError(error => {\n      this.logger.error('Error deleting message:', error);\n      return throwError(() => new Error('Failed to delete message'));\n    }));\n  }\n  markMessageAsRead(messageId) {\n    return this.apollo.mutate({\n      mutation: MARK_AS_READ_MUTATION,\n      variables: {\n        messageId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.markMessageAsRead) throw new Error('Failed to mark message as read');\n      return {\n        ...result.data.markMessageAsRead,\n        readAt: new Date()\n      };\n    }), catchError(error => {\n      console.error('Error marking message as read:', error);\n      return throwError(() => new Error('Failed to mark message as read'));\n    }));\n  }\n  reactToMessage(messageId, emoji) {\n    return this.apollo.mutate({\n      mutation: REACT_TO_MESSAGE_MUTATION,\n      variables: {\n        messageId,\n        emoji\n      }\n    }).pipe(map(result => {\n      if (!result.data?.reactToMessage) throw new Error('Failed to react to message');\n      return result.data.reactToMessage;\n    }), catchError(error => {\n      console.error('Error reacting to message:', error);\n      return throwError(() => new Error('Failed to react to message'));\n    }));\n  }\n  forwardMessage(messageId, conversationIds) {\n    return this.apollo.mutate({\n      mutation: FORWARD_MESSAGE_MUTATION,\n      variables: {\n        messageId,\n        conversationIds\n      }\n    }).pipe(map(result => {\n      if (!result.data?.forwardMessage) throw new Error('Failed to forward message');\n      return result.data.forwardMessage.map(msg => ({\n        ...msg,\n        timestamp: msg.timestamp ? this.normalizeDate(msg.timestamp) : new Date()\n      }));\n    }), catchError(error => {\n      console.error('Error forwarding message:', error);\n      return throwError(() => new Error('Failed to forward message'));\n    }));\n  }\n  pinMessage(messageId, conversationId) {\n    return this.apollo.mutate({\n      mutation: PIN_MESSAGE_MUTATION,\n      variables: {\n        messageId,\n        conversationId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.pinMessage) throw new Error('Failed to pin message');\n      return {\n        ...result.data.pinMessage,\n        pinnedAt: new Date()\n      };\n    }), catchError(error => {\n      console.error('Error pinning message:', error);\n      return throwError(() => new Error('Failed to pin message'));\n    }));\n  }\n  searchMessages(query, conversationId, filters = {}) {\n    return this.apollo.watchQuery({\n      query: SEARCH_MESSAGES_QUERY,\n      variables: {\n        query,\n        conversationId,\n        ...filters,\n        dateFrom: this.toSafeISOString(filters.dateFrom),\n        dateTo: this.toSafeISOString(filters.dateTo)\n      },\n      fetchPolicy: 'cache-first',\n      errorPolicy: 'all'\n    }).valueChanges.pipe(map(result => result.data?.searchMessages?.map(msg => ({\n      ...msg,\n      timestamp: this.safeDate(msg.timestamp),\n      sender: this.normalizeUser(msg.sender)\n    })) || []), catchError(error => {\n      console.error('Error searching messages:', error);\n      return throwError(() => new Error('Failed to search messages'));\n    }));\n  }\n  // ✅ Batch normalization for better performance\n  batchNormalizeMessages(messages) {\n    if (!messages || messages.length === 0) return [];\n    return messages.map(msg => {\n      try {\n        return this.normalizeMessage(msg);\n      } catch (error) {\n        console.error('Error normalizing message:', error);\n        // Return minimal valid message on error\n        return {\n          id: msg.id || msg._id || `temp-${Date.now()}`,\n          content: msg.content || '',\n          type: msg.type || MessageType.TEXT,\n          timestamp: this.safeDate(msg.timestamp),\n          isRead: false,\n          sender: msg.sender ? this.normalizeUser(msg.sender) : {\n            id: this.getCurrentUserId(),\n            username: 'Unknown'\n          }\n        };\n      }\n    });\n  }\n  getUnreadMessages(userId) {\n    return this.apollo.watchQuery({\n      query: GET_UNREAD_MESSAGES_QUERY,\n      variables: {\n        userId\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => result.data?.getUnreadMessages?.map(msg => ({\n      ...msg,\n      timestamp: this.safeDate(msg.timestamp),\n      sender: this.normalizeUser(msg.sender)\n    })) || []), catchError(error => {\n      console.error('Error fetching unread messages:', error);\n      return throwError(() => new Error('Failed to fetch unread messages'));\n    }));\n  }\n  setActiveConversation(conversationId) {\n    this.activeConversation.next(conversationId);\n  }\n  getConversations() {\n    return this.apollo.watchQuery({\n      query: GET_CONVERSATIONS_QUERY,\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      const conversations = result.data?.getConversations || [];\n      return conversations.map(conv => this.normalizeConversation(conv));\n    }), catchError(error => {\n      console.error('Error fetching conversations:', error);\n      return throwError(() => new Error('Failed to load conversations'));\n    }));\n  }\n  getConversation(conversationId, limit, page) {\n    this.logger.info(`[MessageService] Getting conversation: ${conversationId}, limit: ${limit}, page: ${page}`);\n    const variables = {\n      conversationId\n    };\n    // Ajouter les paramètres de pagination s'ils sont fournis\n    if (limit !== undefined) {\n      variables.limit = limit;\n    } else {\n      variables.limit = 10; // Valeur par défaut\n    }\n    // Calculer l'offset à partir de la page si elle est fournie\n    if (page !== undefined) {\n      // La requête GraphQL utilise offset, donc nous devons convertir la page en offset\n      const offset = (page - 1) * variables.limit;\n      variables.offset = offset;\n      this.logger.debug(`[MessageService] Calculated offset: ${offset} from page: ${page} and limit: ${variables.limit}`);\n    } else {\n      variables.offset = 0; // Valeur par défaut\n    }\n\n    this.logger.debug(`[MessageService] Final pagination parameters: limit=${variables.limit}, offset=${variables.offset}`);\n    return this.apollo.watchQuery({\n      query: GET_CONVERSATION_QUERY,\n      variables: variables,\n      fetchPolicy: 'network-only',\n      errorPolicy: 'all'\n    }).valueChanges.pipe(retry(2),\n    // Réessayer 2 fois en cas d'erreur\n    map(result => {\n      this.logger.debug(`[MessageService] Conversation response received:`, result);\n      const conv = result.data?.getConversation;\n      if (!conv) {\n        this.logger.error(`[MessageService] Conversation not found: ${conversationId}`);\n        throw new Error('Conversation not found');\n      }\n      this.logger.debug(`[MessageService] Normalizing conversation: ${conversationId}`);\n      const normalizedConversation = this.normalizeConversation(conv);\n      this.logger.info(`[MessageService] Conversation loaded successfully: ${conversationId}, participants: ${normalizedConversation.participants?.length || 0}, messages: ${normalizedConversation.messages?.length || 0}`);\n      return normalizedConversation;\n    }), catchError(error => {\n      this.logger.error(`[MessageService] Error fetching conversation:`, error);\n      return throwError(() => new Error('Failed to load conversation'));\n    }));\n  }\n  createConversation(userId) {\n    this.logger.info(`[MessageService] Creating conversation with user: ${userId}`);\n    if (!userId) {\n      this.logger.error(`[MessageService] Cannot create conversation: userId is undefined`);\n      return throwError(() => new Error('User ID is required to create a conversation'));\n    }\n    return this.apollo.mutate({\n      mutation: CREATE_CONVERSATION_MUTATION,\n      variables: {\n        userId\n      }\n    }).pipe(map(result => {\n      this.logger.debug(`[MessageService] Conversation creation response:`, result);\n      const conversation = result.data?.createConversation;\n      if (!conversation) {\n        this.logger.error(`[MessageService] Failed to create conversation with user: ${userId}`);\n        throw new Error('Failed to create conversation');\n      }\n      try {\n        const normalizedConversation = this.normalizeConversation(conversation);\n        this.logger.info(`[MessageService] Conversation created successfully: ${normalizedConversation.id}`);\n        return normalizedConversation;\n      } catch (error) {\n        this.logger.error(`[MessageService] Error normalizing created conversation:`, error);\n        throw new Error('Error processing created conversation');\n      }\n    }), catchError(error => {\n      this.logger.error(`[MessageService] Error creating conversation with user ${userId}:`, error);\n      return throwError(() => new Error(`Failed to create conversation: ${error.message}`));\n    }));\n  }\n  /**\n   * Récupère une conversation existante ou en crée une nouvelle si elle n'existe pas\n   * @param userId ID de l'utilisateur avec qui créer/récupérer une conversation\n   * @returns Observable avec la conversation\n   */\n  getOrCreateConversation(userId) {\n    this.logger.info(`[MessageService] Getting or creating conversation with user: ${userId}`);\n    if (!userId) {\n      this.logger.error(`[MessageService] Cannot get/create conversation: userId is undefined`);\n      return throwError(() => new Error('User ID is required to get/create a conversation'));\n    }\n    // D'abord, essayons de trouver une conversation existante entre les deux utilisateurs\n    return this.getConversations().pipe(map(conversations => {\n      // Récupérer l'ID de l'utilisateur actuel\n      const currentUserId = this.getCurrentUserId();\n      // Chercher une conversation directe (non groupe) entre les deux utilisateurs\n      const existingConversation = conversations.find(conv => {\n        if (conv.isGroup) return false;\n        // Vérifier si la conversation contient les deux utilisateurs\n        const participantIds = conv.participants?.map(p => p.id || p._id) || [];\n        return participantIds.includes(userId) && participantIds.includes(currentUserId);\n      });\n      if (existingConversation) {\n        this.logger.info(`[MessageService] Found existing conversation: ${existingConversation.id}`);\n        return existingConversation;\n      }\n      // Si aucune conversation n'est trouvée, en créer une nouvelle\n      throw new Error('No existing conversation found');\n    }), catchError(error => {\n      this.logger.info(`[MessageService] No existing conversation found, creating new one: ${error.message}`);\n      return this.createConversation(userId);\n    }));\n  }\n  getNotifications(refresh = false, page = 1, limit = 10) {\n    this.logger.info('MessageService', `Fetching notifications, refresh: ${refresh}, page: ${page}, limit: ${limit}`);\n    this.logger.debug('MessageService', 'Using query', {\n      query: GET_NOTIFICATIONS_QUERY\n    });\n    // Si refresh est true, réinitialiser la pagination mais ne pas vider le cache\n    // pour conserver les suppressions locales\n    if (refresh) {\n      this.logger.debug('MessageService', 'Resetting pagination due to refresh');\n      this.notificationPagination.currentPage = 1;\n      this.notificationPagination.hasMoreNotifications = true;\n    }\n    // Mettre à jour les paramètres de pagination\n    this.notificationPagination.currentPage = page;\n    this.notificationPagination.limit = limit;\n    // Récupérer les IDs des notifications supprimées du localStorage\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n    this.logger.debug('MessageService', `Found ${deletedNotificationIds.size} deleted notification IDs in localStorage`);\n    return this.apollo.watchQuery({\n      query: GET_NOTIFICATIONS_QUERY,\n      variables: {\n        page: page,\n        limit: limit\n      },\n      fetchPolicy: refresh ? 'network-only' : 'cache-first'\n    }).valueChanges.pipe(map(result => {\n      this.logger.debug('MessageService', 'Notifications response received');\n      if (result.errors) {\n        this.logger.error('MessageService', 'GraphQL errors:', result.errors);\n        throw new Error(result.errors.map(e => e.message).join(', '));\n      }\n      const notifications = result.data?.getUserNotifications || [];\n      this.logger.debug('MessageService', `Received ${notifications.length} notifications from server for page ${page}`);\n      // Vérifier s'il y a plus de notifications à charger\n      this.notificationPagination.hasMoreNotifications = notifications.length >= limit;\n      if (notifications.length === 0) {\n        this.logger.info('MessageService', 'No notifications received from server');\n        this.notificationPagination.hasMoreNotifications = false;\n      }\n      // Filtrer les notifications supprimées\n      const filteredNotifications = notifications.filter(notif => !deletedNotificationIds.has(notif.id));\n      this.logger.debug('MessageService', `Filtered out ${notifications.length - filteredNotifications.length} deleted notifications`);\n      // Afficher les notifications reçues pour le débogage\n      filteredNotifications.forEach((notif, index) => {\n        console.log(`Notification ${index + 1} (page ${page}):`, {\n          id: notif.id || notif._id,\n          type: notif.type,\n          content: notif.content,\n          isRead: notif.isRead\n        });\n      });\n      // Vérifier si les notifications existent déjà dans le cache avant de les ajouter\n      // Mettre à jour le cache avec les nouvelles notifications\n      this.updateCache(filteredNotifications);\n      // Récupérer toutes les notifications du cache et les TRIER\n      const cachedNotifications = Array.from(this.notificationCache.values());\n      // 🚀 TRI OPTIMISÉ: Les notifications les plus récentes en premier\n      const sortedNotifications = this.sortNotificationsByDate(cachedNotifications);\n      console.log(`📊 SORTED: ${sortedNotifications.length} notifications triées (plus récentes en premier)`);\n      // Mettre à jour le BehaviorSubject avec les notifications TRIÉES\n      this.notifications.next(sortedNotifications);\n      // Mettre à jour le compteur de notifications non lues\n      this.updateUnreadCount();\n      // Sauvegarder les notifications dans le localStorage\n      this.saveNotificationsToLocalStorage();\n      return cachedNotifications;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error loading notifications:', error);\n      if (error.graphQLErrors) {\n        this.logger.error('MessageService', 'GraphQL errors:', error.graphQLErrors);\n      }\n      if (error.networkError) {\n        this.logger.error('MessageService', 'Network error:', error.networkError);\n      }\n      return throwError(() => new Error('Failed to load notifications'));\n    }));\n  }\n  /**\n   * Récupère les IDs des notifications supprimées du localStorage\n   * @private\n   * @returns Set contenant les IDs des notifications supprimées\n   */\n  getDeletedNotificationIds() {\n    try {\n      const deletedIds = new Set();\n      const savedNotifications = localStorage.getItem('notifications');\n      // Si aucune notification n'est sauvegardée, retourner un ensemble vide\n      if (!savedNotifications) {\n        return deletedIds;\n      }\n      // Récupérer les IDs des notifications sauvegardées\n      const savedNotificationIds = new Set(JSON.parse(savedNotifications).map(n => n.id));\n      // Récupérer les notifications du serveur (si disponibles dans le cache Apollo)\n      const serverNotifications = this.apollo.client.readQuery({\n        query: GET_NOTIFICATIONS_QUERY\n      })?.getUserNotifications || [];\n      // Pour chaque notification du serveur, vérifier si elle est dans les notifications sauvegardées\n      serverNotifications.forEach(notification => {\n        if (!savedNotificationIds.has(notification.id)) {\n          deletedIds.add(notification.id);\n        }\n      });\n      return deletedIds;\n    } catch (error) {\n      this.logger.error('MessageService', 'Erreur lors de la récupération des IDs de notifications supprimées:', error);\n      return new Set();\n    }\n  }\n  // Méthode pour vérifier s'il y a plus de notifications à charger\n  hasMoreNotifications() {\n    return this.notificationPagination.hasMoreNotifications;\n  }\n  // Méthode pour charger la page suivante de notifications\n  loadMoreNotifications() {\n    const nextPage = this.notificationPagination.currentPage + 1;\n    return this.getNotifications(false, nextPage, this.notificationPagination.limit);\n  }\n  getNotificationById(id) {\n    return this.notifications$.pipe(map(notifications => notifications.find(n => n.id === id)), catchError(error => {\n      this.logger.error('Error finding notification:', error);\n      return throwError(() => new Error('Failed to find notification'));\n    }));\n  }\n  getNotificationCount() {\n    return this.notifications.value?.length || 0;\n  }\n  getNotificationAttachments(notificationId) {\n    return this.apollo.query({\n      query: GET_NOTIFICATIONS_ATTACHAMENTS,\n      variables: {\n        id: notificationId\n      },\n      fetchPolicy: 'network-only'\n    }).pipe(map(result => result.data?.getNotificationAttachments || []), catchError(error => {\n      this.logger.error('Error fetching notification attachments:', error);\n      return throwError(() => new Error('Failed to fetch attachments'));\n    }));\n  }\n  getUnreadNotifications() {\n    return this.notifications$.pipe(map(notifications => notifications.filter(n => !n.isRead)));\n  }\n  /**\n   * Supprime une notification\n   * @param notificationId ID de la notification à supprimer\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteNotification(notificationId) {\n    this.logger.debug('MessageService', `Suppression de la notification ${notificationId}`);\n    if (!notificationId) {\n      this.logger.warn('MessageService', 'ID de notification invalide');\n      return throwError(() => new Error('ID de notification invalide'));\n    }\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const removedCount = this.removeNotificationsFromCache([notificationId]);\n    // Appeler le backend pour supprimer la notification\n    return this.apollo.mutate({\n      mutation: DELETE_NOTIFICATION_MUTATION,\n      variables: {\n        notificationId\n      }\n    }).pipe(map(result => {\n      const response = result.data?.deleteNotification;\n      if (!response) {\n        throw new Error('Réponse de suppression invalide');\n      }\n      this.logger.debug('MessageService', 'Résultat de la suppression:', response);\n      return response;\n    }), catchError(error => this.handleDeletionError(error, 'la suppression de la notification', {\n      success: true,\n      message: 'Notification supprimée localement (erreur serveur)'\n    })));\n  }\n  /**\n   * Sauvegarde les notifications dans le localStorage\n   * @private\n   */\n  saveNotificationsToLocalStorage() {\n    try {\n      const notifications = Array.from(this.notificationCache.values());\n      localStorage.setItem('notifications', JSON.stringify(notifications));\n      this.logger.debug('MessageService', 'Notifications sauvegardées localement');\n    } catch (error) {\n      this.logger.error('MessageService', 'Erreur lors de la sauvegarde des notifications:', error);\n    }\n  }\n  /**\n   * Supprime toutes les notifications de l'utilisateur\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteAllNotifications() {\n    this.logger.debug('MessageService', 'Suppression de toutes les notifications');\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const count = this.notificationCache.size;\n    const allNotificationIds = Array.from(this.notificationCache.keys());\n    this.removeNotificationsFromCache(allNotificationIds);\n    // Appeler le backend pour supprimer toutes les notifications\n    return this.apollo.mutate({\n      mutation: DELETE_ALL_NOTIFICATIONS_MUTATION\n    }).pipe(map(result => {\n      const response = result.data?.deleteAllNotifications;\n      if (!response) {\n        throw new Error('Réponse de suppression invalide');\n      }\n      this.logger.debug('MessageService', 'Résultat de la suppression de toutes les notifications:', response);\n      return response;\n    }), catchError(error => this.handleDeletionError(error, 'la suppression de toutes les notifications', {\n      success: true,\n      count,\n      message: `${count} notifications supprimées localement (erreur serveur)`\n    })));\n  }\n  /**\n   * Supprime plusieurs notifications\n   * @param notificationIds IDs des notifications à supprimer\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteMultipleNotifications(notificationIds) {\n    this.logger.debug('MessageService', `Suppression de ${notificationIds.length} notifications`);\n    if (!notificationIds || notificationIds.length === 0) {\n      this.logger.warn('MessageService', 'Aucun ID de notification fourni');\n      return throwError(() => new Error('Aucun ID de notification fourni'));\n    }\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const count = this.removeNotificationsFromCache(notificationIds);\n    // Appeler le backend pour supprimer les notifications\n    return this.apollo.mutate({\n      mutation: DELETE_MULTIPLE_NOTIFICATIONS_MUTATION,\n      variables: {\n        notificationIds\n      }\n    }).pipe(map(result => {\n      const response = result.data?.deleteMultipleNotifications;\n      if (!response) {\n        throw new Error('Réponse de suppression invalide');\n      }\n      this.logger.debug('MessageService', 'Résultat de la suppression multiple:', response);\n      return response;\n    }), catchError(error => this.handleDeletionError(error, 'la suppression multiple de notifications', {\n      success: count > 0,\n      count,\n      message: `${count} notifications supprimées localement (erreur serveur)`\n    })));\n  }\n  groupNotificationsByType() {\n    return this.notifications$.pipe(map(notifications => {\n      const groups = new Map();\n      notifications.forEach(notif => {\n        if (!groups.has(notif.type)) {\n          groups.set(notif.type, []);\n        }\n        groups.get(notif.type)?.push(notif);\n      });\n      return groups;\n    }));\n  }\n  markAsRead(notificationIds) {\n    this.logger.debug('MessageService', `Marking notifications as read: ${notificationIds?.join(', ') || 'none'}`);\n    if (!notificationIds || notificationIds.length === 0) {\n      this.logger.warn('MessageService', 'No notification IDs provided');\n      return of({\n        success: false,\n        readCount: 0,\n        remainingCount: this.notificationCount.value\n      });\n    }\n    // Vérifier que tous les IDs sont valides\n    const validIds = notificationIds.filter(id => id && typeof id === 'string' && id.trim() !== '');\n    if (validIds.length !== notificationIds.length) {\n      this.logger.error('MessageService', 'Some notification IDs are invalid', {\n        provided: notificationIds,\n        valid: validIds\n      });\n      return throwError(() => new Error('Some notification IDs are invalid'));\n    }\n    this.logger.debug('MessageService', 'Sending mutation to mark notifications as read', validIds);\n    // Mettre à jour localement d'abord pour une meilleure expérience utilisateur\n    this.updateNotificationStatus(validIds, true);\n    // Créer une réponse optimiste\n    const optimisticResponse = {\n      markNotificationsAsRead: {\n        success: true,\n        readCount: validIds.length,\n        remainingCount: Math.max(0, this.notificationCount.value - validIds.length)\n      }\n    };\n    // Afficher des informations de débogage supplémentaires\n    console.log('Sending markNotificationsAsRead mutation with variables:', {\n      notificationIds: validIds\n    });\n    console.log('Using mutation:', MARK_NOTIFICATION_READ_MUTATION);\n    return this.apollo.mutate({\n      mutation: MARK_NOTIFICATION_READ_MUTATION,\n      variables: {\n        notificationIds: validIds\n      },\n      optimisticResponse: optimisticResponse,\n      errorPolicy: 'all',\n      fetchPolicy: 'no-cache' // Ne pas utiliser le cache pour cette mutation\n    }).pipe(map(result => {\n      this.logger.debug('MessageService', 'Mutation result', result);\n      console.log('Mutation result:', result);\n      // Si nous avons des erreurs GraphQL, les logger mais continuer\n      if (result.errors) {\n        this.logger.error('MessageService', 'GraphQL errors:', result.errors);\n        console.error('GraphQL errors:', result.errors);\n      }\n      // Utiliser la réponse du serveur ou notre réponse optimiste\n      const response = result.data?.markNotificationsAsRead ?? optimisticResponse.markNotificationsAsRead;\n      return response;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error marking notifications as read:', error);\n      console.error('Error in markAsRead:', error);\n      // En cas d'erreur, retourner quand même un succès simulé\n      // puisque nous avons déjà mis à jour l'interface utilisateur\n      return of({\n        success: true,\n        readCount: validIds.length,\n        remainingCount: Math.max(0, this.notificationCount.value - validIds.length)\n      });\n    }));\n  }\n  // --------------------------------------------------------------------------\n  // Section 3: Méthodes pour les Appels (SUPPRIMÉES - VOIR SECTION À LA FIN)\n  // --------------------------------------------------------------------------\n  /**\n   * S'abonne aux signaux d'appel\n   * @param callId ID de l'appel\n   * @returns Observable avec les signaux d'appel\n   */\n  subscribeToCallSignals(callId) {\n    return this.apollo.subscribe({\n      query: CALL_SIGNAL_SUBSCRIPTION,\n      variables: {\n        callId\n      }\n    }).pipe(map(({\n      data\n    }) => {\n      if (!data?.callSignal) {\n        throw new Error('No call signal received');\n      }\n      return data.callSignal;\n    }), tap(signal => {\n      this.callSignals.next(signal);\n      this.handleCallSignal(signal);\n    }), catchError(error => {\n      this.logger.error('Error in call signal subscription', error);\n      return throwError(() => new Error('Call signal subscription failed'));\n    }));\n  }\n  /**\n   * Envoie un signal d'appel\n   * @param callId ID de l'appel\n   * @param signalType Type de signal\n   * @param signalData Données du signal\n   * @returns Observable avec le résultat de l'opération\n   */\n  sendCallSignal(callId, signalType, signalData) {\n    return this.apollo.mutate({\n      mutation: SEND_CALL_SIGNAL_MUTATION,\n      variables: {\n        callId,\n        signalType,\n        signalData\n      }\n    }).pipe(map(result => {\n      const success = result.data?.sendCallSignal;\n      if (!success) {\n        throw new Error('Failed to send call signal');\n      }\n      return success;\n    }), catchError(error => {\n      this.logger.error('Error sending call signal', error);\n      return throwError(() => new Error('Failed to send call signal'));\n    }));\n  }\n  /**\n   * Récupère l'historique des appels avec filtres\n   * @param limit Nombre d'appels à récupérer\n   * @param offset Décalage pour la pagination\n   * @param status Filtres de statut\n   * @param type Filtres de type\n   * @param startDate Date de début\n   * @param endDate Date de fin\n   * @returns Observable avec l'historique des appels\n   */\n  getCallHistory(limit = 20, offset = 0, status, type, startDate, endDate) {\n    return this.apollo.watchQuery({\n      query: CALL_HISTORY_QUERY,\n      variables: {\n        limit,\n        offset,\n        status,\n        type,\n        startDate,\n        endDate\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      const history = result.data?.callHistory || [];\n      this.logger.debug(`Retrieved ${history.length} call history items`);\n      return history;\n    }), catchError(error => {\n      this.logger.error('Error fetching call history:', error);\n      return throwError(() => new Error('Failed to fetch call history'));\n    }));\n  }\n  /**\n   * Récupère les détails d'un appel spécifique\n   * @param callId ID de l'appel\n   * @returns Observable avec les détails de l'appel\n   */\n  getCallDetails(callId) {\n    return this.apollo.watchQuery({\n      query: CALL_DETAILS_QUERY,\n      variables: {\n        callId\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      const details = result.data?.callDetails;\n      if (!details) {\n        throw new Error('Call details not found');\n      }\n      this.logger.debug(`Retrieved call details for: ${callId}`);\n      return details;\n    }), catchError(error => {\n      this.logger.error('Error fetching call details:', error);\n      return throwError(() => new Error('Failed to fetch call details'));\n    }));\n  }\n  /**\n   * Récupère les statistiques d'appels\n   * @returns Observable avec les statistiques d'appels\n   */\n  getCallStats() {\n    return this.apollo.watchQuery({\n      query: CALL_STATS_QUERY,\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      const stats = result.data?.callStats;\n      if (!stats) {\n        throw new Error('Call stats not found');\n      }\n      this.logger.debug('Retrieved call stats:', stats);\n      return stats;\n    }), catchError(error => {\n      this.logger.error('Error fetching call stats:', error);\n      return throwError(() => new Error('Failed to fetch call stats'));\n    }));\n  }\n  /**\n   * Gère un signal d'appel reçu\n   * @param signal Signal d'appel\n   */\n  handleCallSignal(signal) {\n    switch (signal.type) {\n      case 'ice-candidate':\n        this.handleIceCandidate(signal);\n        break;\n      case 'answer':\n        this.handleAnswer(signal);\n        break;\n      case 'end-call':\n        this.handleEndCall(signal);\n        break;\n      case 'reject':\n        this.handleRejectCall(signal);\n        break;\n      default:\n        this.logger.debug(`Unhandled signal type: ${signal.type}`, signal);\n    }\n  }\n  /**\n   * Gère un candidat ICE reçu\n   * @param signal Signal d'appel contenant un candidat ICE\n   */\n  handleIceCandidate(signal) {\n    if (!this.peerConnection) {\n      this.logger.error('No peer connection available for ICE candidate');\n      return;\n    }\n    try {\n      const candidate = JSON.parse(signal.data);\n      this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate)).catch(error => {\n        this.logger.error('Error adding ICE candidate', error);\n      });\n    } catch (error) {\n      this.logger.error('Error parsing ICE candidate', error);\n    }\n  }\n  /**\n   * Gère une réponse SDP reçue\n   * @param signal Signal d'appel contenant une réponse SDP\n   */\n  handleAnswer(signal) {\n    if (!this.peerConnection) {\n      this.logger.error('No peer connection available for answer');\n      return;\n    }\n    try {\n      const answer = JSON.parse(signal.data);\n      this.peerConnection.setRemoteDescription(new RTCSessionDescription(answer)).catch(error => {\n        this.logger.error('Error setting remote description', error);\n      });\n    } catch (error) {\n      this.logger.error('Error parsing answer', error);\n    }\n  }\n  /**\n   * Gère la fin d'un appel\n   * @param signal Signal d'appel indiquant la fin de l'appel\n   */\n  handleEndCall(signal) {\n    this.stop('ringtone');\n    this.cleanupCall();\n    // Mettre à jour l'état de l'appel actif\n    const currentCall = this.activeCall.value;\n    if (currentCall && currentCall.id === signal.callId) {\n      this.activeCall.next({\n        ...currentCall,\n        status: CallStatus.ENDED,\n        endTime: new Date().toISOString()\n      });\n    }\n  }\n  /**\n   * Gère le rejet d'un appel\n   * @param signal Signal d'appel indiquant le rejet de l'appel\n   */\n  handleRejectCall(signal) {\n    this.stop('ringtone');\n    this.cleanupCall();\n    // Mettre à jour l'état de l'appel actif\n    const currentCall = this.activeCall.value;\n    if (currentCall && currentCall.id === signal.callId) {\n      this.activeCall.next({\n        ...currentCall,\n        status: CallStatus.REJECTED,\n        endTime: new Date().toISOString()\n      });\n    }\n  }\n  /**\n   * Nettoie les ressources d'appel\n   */\n  cleanupCall() {\n    if (this.localStream) {\n      this.localStream.getTracks().forEach(track => track.stop());\n      this.localStream = null;\n      this.localStream$.next(null);\n    }\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n    this.remoteStream = null;\n    this.remoteStream$.next(null);\n  }\n  /**\n   * Configure les périphériques média pour un appel\n   * @param callType Type d'appel (audio, vidéo)\n   * @returns Observable avec le flux média\n   */\n  setupMediaDevices(callType) {\n    const constraints = {\n      audio: true,\n      video: callType !== CallType.AUDIO ? {\n        width: {\n          ideal: 1280\n        },\n        height: {\n          ideal: 720\n        }\n      } : false\n    };\n    return new Observable(observer => {\n      navigator.mediaDevices.getUserMedia(constraints).then(stream => {\n        observer.next(stream);\n        observer.complete();\n      }).catch(error => {\n        this.logger.error('Error accessing media devices', error);\n        observer.error(new Error('Failed to access media devices'));\n      });\n    });\n  }\n  /**\n   * Génère un ID d'appel unique\n   * @returns ID d'appel unique\n   */\n  generateCallId() {\n    return Date.now().toString() + Math.random().toString(36).substring(2, 9);\n  }\n  // --------------------------------------------------------------------------\n  // Section 4: Méthodes pour les Utilisateurs/Groupes\n  // --------------------------------------------------------------------------\n  // User methods\n  getAllUsers(forceRefresh = false, search, page = 1, limit = 10, sortBy = 'username', sortOrder = 'asc', isOnline) {\n    this.logger.info('MessageService', `Getting users with params: forceRefresh=${forceRefresh}, search=${search || '(empty)'}, page=${page}, limit=${limit}, sortBy=${sortBy}, sortOrder=${sortOrder}, isOnline=${isOnline}`);\n    const now = Date.now();\n    const cacheValid = !forceRefresh && this.usersCache.length > 0 && now - this.lastFetchTime <= this.CACHE_DURATION && !search && page === 1 && limit >= this.usersCache.length;\n    // Use cache only for first page with no filters\n    if (cacheValid) {\n      this.logger.debug('MessageService', `Using cached users (${this.usersCache.length} users)`);\n      return of([...this.usersCache]);\n    }\n    this.logger.debug('MessageService', `Fetching users from server with pagination, fetchPolicy=${forceRefresh ? 'network-only' : 'cache-first'}`);\n    return this.apollo.watchQuery({\n      query: GET_ALL_USER_QUERY,\n      variables: {\n        search,\n        page,\n        limit,\n        sortBy,\n        sortOrder,\n        isOnline: isOnline !== undefined ? isOnline : null\n      },\n      fetchPolicy: forceRefresh ? 'network-only' : 'cache-first'\n    }).valueChanges.pipe(map(result => {\n      this.logger.debug('MessageService', 'Users response received', result);\n      if (result.errors) {\n        this.logger.error('MessageService', 'GraphQL errors in getAllUsers:', result.errors);\n        throw new Error(result.errors.map(e => e.message).join(', '));\n      }\n      if (!result.data?.getAllUsers) {\n        this.logger.warn('MessageService', 'No users data received from server');\n        return [];\n      }\n      const paginatedResponse = result.data.getAllUsers;\n      // Log pagination metadata\n      this.logger.debug('MessageService', 'Pagination metadata:', {\n        totalCount: paginatedResponse.totalCount,\n        totalPages: paginatedResponse.totalPages,\n        currentPage: paginatedResponse.currentPage,\n        hasNextPage: paginatedResponse.hasNextPage,\n        hasPreviousPage: paginatedResponse.hasPreviousPage\n      });\n      // Normalize users with error handling\n      const users = [];\n      for (const user of paginatedResponse.users) {\n        try {\n          if (user) {\n            users.push(this.normalizeUser(user));\n          }\n        } catch (error) {\n          this.logger.warn('MessageService', `Error normalizing user, skipping:`, error);\n        }\n      }\n      this.logger.info('MessageService', `Received ${users.length} users from server (page ${paginatedResponse.currentPage} of ${paginatedResponse.totalPages})`);\n      // Update cache only for first page with no filters\n      if (!search && page === 1 && !isOnline) {\n        this.usersCache = [...users];\n        this.lastFetchTime = Date.now();\n        this.logger.debug('MessageService', `User cache updated with ${users.length} users`);\n      }\n      // Store pagination metadata in a property for component access\n      this.currentUserPagination = {\n        totalCount: paginatedResponse.totalCount,\n        totalPages: paginatedResponse.totalPages,\n        currentPage: paginatedResponse.currentPage,\n        hasNextPage: paginatedResponse.hasNextPage,\n        hasPreviousPage: paginatedResponse.hasPreviousPage\n      };\n      return users;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error fetching users:', error);\n      if (error.graphQLErrors) {\n        this.logger.error('MessageService', 'GraphQL errors:', error.graphQLErrors);\n      }\n      if (error.networkError) {\n        this.logger.error('MessageService', 'Network error:', error.networkError);\n      }\n      // Return cache if available (only for first page)\n      if (this.usersCache.length > 0 && page === 1 && !search && !isOnline) {\n        this.logger.warn('MessageService', `Returning ${this.usersCache.length} cached users due to fetch error`);\n        return of([...this.usersCache]);\n      }\n      return throwError(() => new Error(`Failed to fetch users: ${error.message || 'Unknown error'}`));\n    }));\n  }\n  getOneUser(userId) {\n    return this.apollo.watchQuery({\n      query: GET_USER_QUERY,\n      variables: {\n        id: userId\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => this.normalizeUser(result.data?.getOneUser)), catchError(error => {\n      this.logger.error('MessageService', 'Error fetching user:', error);\n      return throwError(() => new Error('Failed to fetch user'));\n    }));\n  }\n  getCurrentUser() {\n    return this.apollo.watchQuery({\n      query: GET_CURRENT_USER_QUERY,\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => this.normalizeUser(result.data?.getCurrentUser)), catchError(error => {\n      this.logger.error('MessageService', 'Error fetching current user:', error);\n      return throwError(() => new Error('Failed to fetch current user'));\n    }));\n  }\n  setUserOnline(userId) {\n    return this.apollo.mutate({\n      mutation: SET_USER_ONLINE_MUTATION,\n      variables: {\n        userId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.setUserOnline) throw new Error('Failed to set user online');\n      return this.normalizeUser(result.data.setUserOnline);\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error setting user online:', error);\n      return throwError(() => new Error('Failed to set user online'));\n    }));\n  }\n  setUserOffline(userId) {\n    return this.apollo.mutate({\n      mutation: SET_USER_OFFLINE_MUTATION,\n      variables: {\n        userId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.setUserOffline) throw new Error('Failed to set user offline');\n      return this.normalizeUser(result.data.setUserOffline);\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error setting user offline:', error);\n      return throwError(() => new Error('Failed to set user offline'));\n    }));\n  }\n  // --------------------------------------------------------------------------\n  // Section: Gestion des Groupes\n  // --------------------------------------------------------------------------\n  /**\n   * Crée un nouveau groupe\n   */\n  createGroup(name, participantIds, photo, description) {\n    this.logger.debug('MessageService', `Creating group: ${name} with ${participantIds.length} participants`);\n    if (!name || !participantIds || participantIds.length === 0) {\n      return throwError(() => new Error('Nom du groupe et participants requis'));\n    }\n    return this.apollo.mutate({\n      mutation: CREATE_GROUP_MUTATION,\n      variables: {\n        name,\n        participantIds,\n        photo,\n        description\n      }\n    }).pipe(map(result => {\n      const group = result.data?.createGroup;\n      if (!group) {\n        throw new Error('Échec de la création du groupe');\n      }\n      this.logger.info('MessageService', `Group created successfully: ${group.id}`);\n      return group;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error creating group:', error);\n      return throwError(() => new Error('Échec de la création du groupe'));\n    }));\n  }\n  /**\n   * Met à jour un groupe existant\n   */\n  updateGroup(groupId, input) {\n    this.logger.debug('MessageService', `Updating group: ${groupId}`);\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n    return this.apollo.mutate({\n      mutation: UPDATE_GROUP_MUTATION,\n      variables: {\n        id: groupId,\n        input\n      }\n    }).pipe(map(result => {\n      const group = result.data?.updateGroup;\n      if (!group) {\n        throw new Error('Échec de la mise à jour du groupe');\n      }\n      this.logger.info('MessageService', `Group updated successfully: ${group.id}`);\n      return group;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error updating group:', error);\n      return throwError(() => new Error('Échec de la mise à jour du groupe'));\n    }));\n  }\n  /**\n   * Supprime un groupe\n   */\n  deleteGroup(groupId) {\n    this.logger.debug('MessageService', `Deleting group: ${groupId}`);\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n    return this.apollo.mutate({\n      mutation: DELETE_GROUP_MUTATION,\n      variables: {\n        id: groupId\n      }\n    }).pipe(map(result => {\n      const response = result.data?.deleteGroup;\n      if (!response) {\n        throw new Error('Échec de la suppression du groupe');\n      }\n      this.logger.info('MessageService', `Group deleted successfully: ${groupId}`);\n      return response;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error deleting group:', error);\n      return throwError(() => new Error('Échec de la suppression du groupe'));\n    }));\n  }\n  /**\n   * Quitte un groupe\n   */\n  leaveGroup(groupId) {\n    this.logger.debug('MessageService', `Leaving group: ${groupId}`);\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n    return this.apollo.mutate({\n      mutation: LEAVE_GROUP_MUTATION,\n      variables: {\n        groupId\n      }\n    }).pipe(map(result => {\n      const response = result.data?.leaveGroup;\n      if (!response) {\n        throw new Error('Échec de la sortie du groupe');\n      }\n      this.logger.info('MessageService', `Left group successfully: ${groupId}`);\n      return response;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error leaving group:', error);\n      return throwError(() => new Error('Échec de la sortie du groupe'));\n    }));\n  }\n  /**\n   * Récupère les informations d'un groupe\n   */\n  getGroup(groupId) {\n    this.logger.debug('MessageService', `Getting group: ${groupId}`);\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n    return this.apollo.query({\n      query: GET_GROUP_QUERY,\n      variables: {\n        id: groupId\n      },\n      fetchPolicy: 'network-only'\n    }).pipe(map(result => {\n      const group = result.data?.getGroup;\n      if (!group) {\n        throw new Error('Groupe non trouvé');\n      }\n      this.logger.info('MessageService', `Group retrieved successfully: ${groupId}`);\n      return group;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error getting group:', error);\n      return throwError(() => new Error('Échec de la récupération du groupe'));\n    }));\n  }\n  /**\n   * Récupère les groupes d'un utilisateur\n   */\n  getUserGroups(userId) {\n    this.logger.debug('MessageService', `Getting groups for user: ${userId}`);\n    if (!userId) {\n      return throwError(() => new Error(\"ID de l'utilisateur requis\"));\n    }\n    return this.apollo.query({\n      query: GET_USER_GROUPS_QUERY,\n      variables: {\n        userId\n      },\n      fetchPolicy: 'network-only'\n    }).pipe(map(result => {\n      const groups = result.data?.getUserGroups || [];\n      this.logger.info('MessageService', `Retrieved ${groups.length} groups for user: ${userId}`);\n      return groups;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error getting user groups:', error);\n      return throwError(() => new Error('Échec de la récupération des groupes'));\n    }));\n  }\n  subscribeToNewMessages(conversationId) {\n    if (!conversationId) {\n      return throwError(() => new Error('Conversation ID is required'));\n    }\n    // ✅ Use cached subscription if available\n    const cacheKey = `messages_${conversationId}`;\n    if (this.subscriptionCache.has(cacheKey)) {\n      const refCount = this.subscriptionRefCount.get(cacheKey) || 0;\n      this.subscriptionRefCount.set(cacheKey, refCount + 1);\n      return this.subscriptionCache.get(cacheKey);\n    }\n    // ✅ Quick token validation without verbose logging\n    if (!this.isTokenValid()) {\n      return EMPTY;\n    }\n    // ✅ Reduced logging for better performance\n    if (!environment.production) {\n      console.log(`🚀 Setting up subscription: ${conversationId}`);\n    }\n    // ✅ Create optimized subscription with caching and shareReplay\n    const sub$ = this.apollo.subscribe({\n      query: MESSAGE_SENT_SUBSCRIPTION,\n      variables: {\n        conversationId\n      },\n      errorPolicy: 'all' // Handle partial errors gracefully\n    }).pipe(\n    // ✅ Debounce rapid messages to prevent UI flooding\n    debounceTime(10), map(result => {\n      const msg = result.data?.messageSent;\n      if (!msg) {\n        throw new Error('No message payload received');\n      }\n      // ✅ Reduced logging for better performance\n      if (!environment.production) {\n        console.log('⚡ New message via WebSocket:', msg.id);\n      }\n      // Vérifier que l'ID est présent\n      if (!msg.id && !msg._id) {\n        this.logger.warn('⚠️ Message without ID received, generating temp ID');\n        msg.id = `temp-${Date.now()}`;\n      }\n      try {\n        // NORMALISATION RAPIDE du message\n        const normalizedMessage = this.normalizeMessage(msg);\n        this.logger.debug('✅ INSTANT: Message normalized successfully', normalizedMessage);\n        // TRAITEMENT INSTANTANÉ selon le type\n        if (normalizedMessage.type === MessageType.AUDIO || normalizedMessage.type === MessageType.VOICE_MESSAGE || normalizedMessage.attachments && normalizedMessage.attachments.some(att => att.type === 'AUDIO')) {\n          this.logger.debug('🎤 INSTANT: Voice message received in real-time');\n        }\n        // MISE À JOUR IMMÉDIATE de l'UI\n        this.zone.run(() => {\n          this.logger.debug('📡 INSTANT: Updating conversation UI immediately');\n          this.updateConversationWithNewMessage(conversationId, normalizedMessage);\n        });\n        return normalizedMessage;\n      } catch (err) {\n        this.logger.error('❌ Error normalizing message:', err);\n        // Créer un message minimal mais valide pour éviter les erreurs\n        const minimalMessage = {\n          id: msg.id || msg._id || `temp-${Date.now()}`,\n          content: msg.content || '',\n          type: msg.type || MessageType.TEXT,\n          timestamp: this.safeDate(msg.timestamp),\n          isRead: false,\n          sender: msg.sender ? this.normalizeUser(msg.sender) : {\n            id: this.getCurrentUserId(),\n            username: 'Unknown'\n          }\n        };\n        this.logger.debug('🔧 FALLBACK: Created minimal message', minimalMessage);\n        return minimalMessage;\n      }\n    }), catchError(error => {\n      console.error('Message subscription error:', error);\n      return EMPTY;\n    }),\n    // ✅ Filter null values and deduplicate messages\n    filter(message => !!message), distinctUntilChanged((prev, curr) => prev?.id === curr?.id),\n    // ✅ Cache subscription with shareReplay for performance\n    shareReplay({\n      bufferSize: 1,\n      refCount: true\n    }),\n    // ✅ Retry with exponential backoff\n    retry(3));\n    // ✅ Cache the subscription for reuse\n    this.subscriptionCache.set(cacheKey, sub$);\n    this.subscriptionRefCount.set(cacheKey, 1);\n    // ✅ Optimized subscription observer with minimal logging\n    const sub = sub$.subscribe({\n      next: message => {\n        if (!environment.production) {\n          console.log(`✅ Message received:`, message.id);\n        }\n        // ✅ Update conversation immediately\n        this.updateConversationWithNewMessage(conversationId, message);\n      },\n      error: err => {\n        console.error('Subscription error:', err);\n        // ✅ Clean up cache on error\n        this.subscriptionCache.delete(cacheKey);\n        this.subscriptionRefCount.delete(cacheKey);\n      },\n      complete: () => {\n        // ✅ Clean up cache on completion\n        this.subscriptionCache.delete(cacheKey);\n        this.subscriptionRefCount.delete(cacheKey);\n      }\n    });\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  /**\n   * Met à jour une conversation avec un nouveau message INSTANTANÉMENT\n   * @param conversationId ID de la conversation\n   * @param message Nouveau message\n   */\n  updateConversationWithNewMessage(conversationId, message) {\n    this.logger.debug(`⚡ INSTANT: Updating conversation ${conversationId} with new message ${message.id}`);\n    // MISE À JOUR IMMÉDIATE sans attendre la requête\n    this.zone.run(() => {\n      // Émettre IMMÉDIATEMENT l'événement de conversation active\n      this.activeConversation.next(conversationId);\n      this.logger.debug('📡 INSTANT: Conversation event emitted immediately');\n    });\n    // Mise à jour en arrière-plan (non-bloquante)\n    setTimeout(() => {\n      this.getConversation(conversationId).subscribe({\n        next: conversation => {\n          this.logger.debug(`✅ BACKGROUND: Conversation ${conversationId} refreshed with ${conversation?.messages?.length || 0} messages`);\n        },\n        error: error => {\n          this.logger.error(`⚠️ BACKGROUND: Error refreshing conversation ${conversationId}:`, error);\n        }\n      });\n    }, 0); // Exécution asynchrone immédiate\n  }\n  /**\n   * Rafraîchit les notifications du sender après envoi d'un message\n   */\n  refreshSenderNotifications() {\n    console.log('🔄 SENDER: Refreshing notifications after message sent');\n    // Recharger les notifications en arrière-plan\n    this.getNotifications(true).subscribe({\n      next: notifications => {\n        console.log('🔄 SENDER: Notifications refreshed successfully', notifications.length);\n      },\n      error: error => {\n        console.error('🔄 SENDER: Error refreshing notifications:', error);\n      }\n    });\n  }\n  subscribeToUserStatus() {\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    if (!this.isTokenValid()) {\n      this.logger.warn(\"Tentative d'abonnement au statut utilisateur avec un token invalide ou expiré\");\n      return throwError(() => new Error('Invalid or expired token'));\n    }\n    this.logger.debug(\"Démarrage de l'abonnement au statut utilisateur\");\n    const sub$ = this.apollo.subscribe({\n      query: USER_STATUS_SUBSCRIPTION\n    }).pipe(tap(result => this.logger.debug(\"Données reçues de l'abonnement au statut utilisateur:\", result)), map(result => {\n      const user = result.data?.userStatusChanged;\n      if (!user) {\n        this.logger.error('No status payload received');\n        throw new Error('No status payload received');\n      }\n      return this.normalizeUser(user);\n    }), catchError(error => {\n      this.logger.error('Status subscription error:', error);\n      return throwError(() => new Error('Status subscription failed'));\n    }), retry(3) // Réessayer 3 fois en cas d'erreur\n    );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToConversationUpdates(conversationId) {\n    const sub$ = this.apollo.subscribe({\n      query: CONVERSATION_UPDATED_SUBSCRIPTION,\n      variables: {\n        conversationId\n      }\n    }).pipe(map(result => {\n      const conv = result.data?.conversationUpdated;\n      if (!conv) throw new Error('No conversation payload received');\n      const normalizedConversation = {\n        ...conv,\n        participants: conv.participants?.map(p => this.normalizeUser(p)) || [],\n        lastMessage: conv.lastMessage ? {\n          ...conv.lastMessage,\n          sender: this.normalizeUser(conv.lastMessage.sender),\n          timestamp: this.safeDate(conv.lastMessage.timestamp),\n          readAt: conv.lastMessage.readAt ? this.safeDate(conv.lastMessage.readAt) : undefined,\n          // Conservez toutes les autres propriétés du message\n          id: conv.lastMessage.id,\n          content: conv.lastMessage.content,\n          type: conv.lastMessage.type,\n          isRead: conv.lastMessage.isRead\n          // ... autres propriétés nécessaires\n        } : null // On conserve null comme dans votre version originale\n      };\n\n      return normalizedConversation; // Assertion de type si nécessaire\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Conversation subscription error:', error);\n      return throwError(() => new Error('Conversation subscription failed'));\n    }));\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToTypingIndicator(conversationId) {\n    const sub$ = this.apollo.subscribe({\n      query: TYPING_INDICATOR_SUBSCRIPTION,\n      variables: {\n        conversationId\n      }\n    }).pipe(map(result => result.data?.typingIndicator), filter(Boolean), catchError(error => {\n      this.logger.error('MessageService', 'Typing indicator subscription error:', error);\n      return throwError(() => new Error('Typing indicator subscription failed'));\n    }));\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  isTokenValid() {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      this.logger.warn('Aucun token trouvé');\n      return false;\n    }\n    try {\n      // Décoder le token JWT (format: header.payload.signature)\n      const parts = token.split('.');\n      if (parts.length !== 3) {\n        this.logger.warn('Format de token invalide');\n        return false;\n      }\n      // Décoder le payload (deuxième partie du token)\n      const payload = JSON.parse(atob(parts[1]));\n      // Vérifier l'expiration\n      if (!payload.exp) {\n        this.logger.warn(\"Token sans date d'expiration\");\n        return false;\n      }\n      const expirationDate = new Date(payload.exp * 1000);\n      const now = new Date();\n      if (expirationDate < now) {\n        this.logger.warn('Token expiré', {\n          expiration: expirationDate.toISOString(),\n          now: now.toISOString()\n        });\n        return false;\n      }\n      return true;\n    } catch (error) {\n      this.logger.error('Erreur lors de la vérification du token:', error);\n      return false;\n    }\n  }\n  subscribeToNotificationsRead() {\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    if (!this.isTokenValid()) {\n      this.logger.warn(\"Tentative d'abonnement aux notifications avec un token invalide ou expiré\");\n      return of([]);\n    }\n    this.logger.debug(\"Démarrage de l'abonnement aux notifications lues\");\n    const sub$ = this.apollo.subscribe({\n      query: NOTIFICATIONS_READ_SUBSCRIPTION\n    }).pipe(tap(result => this.logger.debug(\"Données reçues de l'abonnement aux notifications lues:\", result)), map(result => {\n      const notificationIds = result.data?.notificationsRead || [];\n      this.logger.debug('Notifications marquées comme lues:', notificationIds);\n      this.updateNotificationStatus(notificationIds, true);\n      return notificationIds;\n    }), catchError(err => {\n      this.logger.error('Notifications read subscription error:', err);\n      // Retourner un tableau vide au lieu de propager l'erreur\n      return of([]);\n    }),\n    // Réessayer après un délai en cas d'erreur\n    retry(3) // Réessayer 3 fois en cas d'erreur\n    );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToNewNotifications() {\n    // Vérifier si l'utilisateur est connecté\n    const token = localStorage.getItem('token');\n    if (!token) {\n      this.logger.warn(\"Tentative d'abonnement aux notifications sans être connecté\");\n      return EMPTY;\n    }\n    this.logger.debug('🚀 INSTANT NOTIFICATION: Setting up real-time subscription');\n    const source$ = this.apollo.subscribe({\n      query: NOTIFICATION_SUBSCRIPTION\n    });\n    const processed$ = source$.pipe(map(result => {\n      const notification = result.data?.notificationReceived;\n      if (!notification) {\n        throw new Error('No notification payload received');\n      }\n      this.logger.debug('⚡ INSTANT: New notification received', notification);\n      const normalized = this.normalizeNotification(notification);\n      // Vérification rapide du cache\n      if (this.notificationCache.has(normalized.id)) {\n        this.logger.debug(`🔄 Notification ${normalized.id} already in cache, skipping`);\n        throw new Error('Notification already exists in cache');\n      }\n      // TRAITEMENT INSTANTANÉ\n      this.logger.debug('📡 INSTANT: Processing notification immediately');\n      // Vérifier si la notification existe déjà pour éviter les doublons\n      const currentNotifications = this.notifications.value;\n      const existingNotification = currentNotifications.find(n => n.id === normalized.id);\n      if (existingNotification) {\n        this.logger.debug('🔄 DUPLICATE: Notification already exists, skipping:', normalized.id);\n        return normalized;\n      }\n      // Son de notification IMMÉDIAT\n      this.playNotificationSound();\n      // Mise à jour INSTANTANÉE du cache\n      this.updateNotificationCache(normalized);\n      // Émettre IMMÉDIATEMENT la notification EN PREMIER\n      this.zone.run(() => {\n        // 🚀 INSERTION EN PREMIER: Nouvelle notification en tête de liste\n        const updatedNotifications = [normalized, ...currentNotifications];\n        this.logger.debug(`⚡ INSTANT: Nouvelle notification ajoutée en PREMIER (${updatedNotifications.length} total)`);\n        this.notifications.next(updatedNotifications);\n        this.notificationCount.next(this.notificationCount.value + 1);\n      });\n      this.logger.debug('✅ INSTANT: Notification processed and emitted', normalized);\n      return normalized;\n    }),\n    // Gestion d'erreurs optimisée\n    catchError(err => {\n      if (err instanceof Error && err.message === 'Notification already exists in cache') {\n        return EMPTY;\n      }\n      this.logger.error('❌ Notification subscription error:', err);\n      return EMPTY;\n    }),\n    // Optimisation: traitement en temps réel\n    tap(notification => {\n      this.logger.debug('⚡ INSTANT: Notification ready for UI update', notification);\n    }));\n    const sub = processed$.subscribe({\n      next: notification => {\n        this.logger.debug('✅ INSTANT: Notification delivered to UI', notification);\n      },\n      error: error => {\n        this.logger.error('❌ CRITICAL: Notification subscription error', error);\n      }\n    });\n    this.subscriptions.push(sub);\n    this.logger.debug('🔗 INSTANT: Notification subscription established');\n    return processed$;\n  }\n  // --------------------------------------------------------------------------\n  // Helpers et Utilitaires\n  // --------------------------------------------------------------------------\n  startCleanupInterval() {\n    this.cleanupInterval = setInterval(() => {\n      this.cleanupExpiredNotifications();\n    }, 3600000);\n  }\n  cleanupExpiredNotifications() {\n    const now = new Date();\n    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n    let expiredCount = 0;\n    this.notificationCache.forEach((notification, id) => {\n      const notificationDate = new Date(notification.timestamp);\n      if (notificationDate < thirtyDaysAgo) {\n        this.notificationCache.delete(id);\n        expiredCount++;\n      }\n    });\n    if (expiredCount > 0) {\n      this.logger.debug(`Cleaned up ${expiredCount} expired notifications`);\n      // 🚀 TRI OPTIMISÉ: Maintenir l'ordre après nettoyage\n      const remainingNotifications = Array.from(this.notificationCache.values());\n      const sortedNotifications = this.sortNotificationsByDate(remainingNotifications);\n      this.notifications.next(sortedNotifications);\n      this.updateUnreadCount();\n    }\n  }\n  /**\n   * Trie les notifications par date (plus récentes en premier)\n   * @param notifications Array de notifications à trier\n   * @returns Array de notifications triées\n   */\n  sortNotificationsByDate(notifications) {\n    return notifications.sort((a, b) => {\n      // Utiliser timestamp ou une date par défaut si manquant\n      const dateA = new Date(a.timestamp || 0);\n      const dateB = new Date(b.timestamp || 0);\n      return dateB.getTime() - dateA.getTime(); // Ordre décroissant (plus récent en premier)\n    });\n  }\n\n  getCurrentUserId() {\n    return localStorage.getItem('userId') || '';\n  }\n  normalizeMessage(message) {\n    if (!message) {\n      this.logger.error('[MessageService] Cannot normalize null or undefined message');\n      throw new Error('Message object is required');\n    }\n    try {\n      // Vérification des champs obligatoires\n      if (!message.id && !message._id) {\n        this.logger.error('[MessageService] Message ID is missing', undefined, message);\n        throw new Error('Message ID is required');\n      }\n      // Normaliser le sender avec gestion d'erreur\n      let normalizedSender;\n      try {\n        normalizedSender = message.sender ? this.normalizeUser(message.sender) : undefined;\n      } catch (error) {\n        this.logger.warn('[MessageService] Error normalizing message sender, using default values', error);\n        normalizedSender = {\n          _id: message.senderId || 'unknown',\n          id: message.senderId || 'unknown',\n          username: 'Unknown User',\n          email: '<EMAIL>',\n          role: 'user',\n          isActive: true\n        };\n      }\n      // Normaliser le receiver si présent\n      let normalizedReceiver;\n      if (message.receiver) {\n        try {\n          normalizedReceiver = this.normalizeUser(message.receiver);\n        } catch (error) {\n          this.logger.warn('[MessageService] Error normalizing message receiver, using default values', error);\n          normalizedReceiver = {\n            _id: message.receiverId || 'unknown',\n            id: message.receiverId || 'unknown',\n            username: 'Unknown User',\n            email: '<EMAIL>',\n            role: 'user',\n            isActive: true\n          };\n        }\n      }\n      // Normaliser les pièces jointes si présentes\n      const normalizedAttachments = message.attachments?.map(att => ({\n        id: att.id || att._id || `attachment-${Date.now()}`,\n        url: att.url || '',\n        type: att.type || 'unknown',\n        name: att.name || 'attachment',\n        size: att.size || 0,\n        duration: att.duration || 0\n      })) || [];\n      // Construire le message normalisé\n      const normalizedMessage = {\n        ...message,\n        _id: message.id || message._id,\n        id: message.id || message._id,\n        content: message.content || '',\n        sender: normalizedSender,\n        timestamp: this.normalizeDate(message.timestamp),\n        readAt: message.readAt ? this.normalizeDate(message.readAt) : undefined,\n        attachments: normalizedAttachments,\n        metadata: message.metadata || null\n      };\n      // Ajouter le receiver seulement s'il existe\n      if (normalizedReceiver) {\n        normalizedMessage.receiver = normalizedReceiver;\n      }\n      this.logger.debug('[MessageService] Message normalized successfully', {\n        messageId: normalizedMessage.id,\n        senderId: normalizedMessage.sender?.id\n      });\n      return normalizedMessage;\n    } catch (error) {\n      this.logger.error('[MessageService] Error normalizing message:', error instanceof Error ? error : new Error(String(error)), message);\n      throw new Error(`Failed to normalize message: ${error instanceof Error ? error.message : String(error)}`);\n    }\n  }\n  normalizeUser(user) {\n    if (!user) {\n      throw new Error('User object is required');\n    }\n    // Vérification des champs obligatoires avec valeurs par défaut\n    const userId = user.id || user._id;\n    if (!userId) {\n      throw new Error('User ID is required');\n    }\n    // Utiliser des valeurs par défaut pour les champs manquants\n    const username = user.username || 'Unknown User';\n    const email = user.email || `user-${userId}@example.com`;\n    const isActive = user.isActive !== undefined && user.isActive !== null ? user.isActive : true;\n    const role = user.role || 'user';\n    // Construire l'objet utilisateur normalisé\n    return {\n      _id: userId,\n      id: userId,\n      username: username,\n      email: email,\n      role: role,\n      isActive: isActive,\n      // Champs optionnels\n      image: user.image ?? null,\n      bio: user.bio,\n      isOnline: user.isOnline || false,\n      lastActive: user.lastActive ? new Date(user.lastActive) : undefined,\n      createdAt: user.createdAt ? new Date(user.createdAt) : undefined,\n      updatedAt: user.updatedAt ? new Date(user.updatedAt) : undefined,\n      followingCount: user.followingCount,\n      followersCount: user.followersCount,\n      postCount: user.postCount\n    };\n  }\n  normalizeConversation(conv) {\n    if (!conv) {\n      this.logger.error('[MessageService] Cannot normalize null or undefined conversation');\n      throw new Error('Conversation object is required');\n    }\n    try {\n      // Vérification des champs obligatoires\n      if (!conv.id && !conv._id) {\n        this.logger.error('[MessageService] Conversation ID is missing', undefined, conv);\n        throw new Error('Conversation ID is required');\n      }\n      // Normaliser les participants avec gestion d'erreur\n      const normalizedParticipants = [];\n      if (conv.participants && Array.isArray(conv.participants)) {\n        for (const participant of conv.participants) {\n          try {\n            if (participant) {\n              normalizedParticipants.push(this.normalizeUser(participant));\n            }\n          } catch (error) {\n            this.logger.warn('[MessageService] Error normalizing participant, skipping', error);\n          }\n        }\n      } else {\n        this.logger.warn('[MessageService] Conversation has no participants or invalid participants array', conv);\n      }\n      // Normaliser les messages avec gestion d'erreur\n      const normalizedMessages = [];\n      if (conv.messages && Array.isArray(conv.messages)) {\n        this.logger.debug('[MessageService] Processing conversation messages', {\n          count: conv.messages.length\n        });\n        for (const message of conv.messages) {\n          try {\n            if (message) {\n              const normalizedMessage = this.normalizeMessage(message);\n              this.logger.debug('[MessageService] Successfully normalized message', {\n                messageId: normalizedMessage.id,\n                content: normalizedMessage.content?.substring(0, 20),\n                sender: normalizedMessage.sender?.username\n              });\n              normalizedMessages.push(normalizedMessage);\n            }\n          } catch (error) {\n            this.logger.warn('[MessageService] Error normalizing message in conversation, skipping', error);\n          }\n        }\n      } else {\n        this.logger.debug('[MessageService] No messages found in conversation or invalid messages array');\n      }\n      // Normaliser le dernier message avec gestion d'erreur\n      let normalizedLastMessage = null;\n      if (conv.lastMessage) {\n        try {\n          normalizedLastMessage = this.normalizeMessage(conv.lastMessage);\n        } catch (error) {\n          this.logger.warn('[MessageService] Error normalizing last message, using null', error);\n        }\n      }\n      // Construire la conversation normalisée\n      const normalizedConversation = {\n        ...conv,\n        _id: conv.id || conv._id,\n        id: conv.id || conv._id,\n        participants: normalizedParticipants,\n        messages: normalizedMessages,\n        lastMessage: normalizedLastMessage,\n        unreadCount: conv.unreadCount || 0,\n        isGroup: !!conv.isGroup,\n        createdAt: this.normalizeDate(conv.createdAt),\n        updatedAt: this.normalizeDate(conv.updatedAt)\n      };\n      this.logger.debug('[MessageService] Conversation normalized successfully', {\n        conversationId: normalizedConversation.id,\n        participantCount: normalizedParticipants.length,\n        messageCount: normalizedMessages.length\n      });\n      return normalizedConversation;\n    } catch (error) {\n      this.logger.error('[MessageService] Error normalizing conversation:', error instanceof Error ? error : new Error(String(error)), conv);\n      throw new Error(`Failed to normalize conversation: ${error instanceof Error ? error.message : String(error)}`);\n    }\n  }\n  normalizeDate(date) {\n    if (!date) return new Date();\n    try {\n      return typeof date === 'string' ? new Date(date) : date;\n    } catch (error) {\n      this.logger.warn(`Failed to parse date: ${date}`, error);\n      return new Date();\n    }\n  }\n  // Méthode sécurisée pour créer une date à partir d'une valeur potentiellement undefined\n  safeDate(date) {\n    if (!date) return new Date();\n    try {\n      return typeof date === 'string' ? new Date(date) : date;\n    } catch (error) {\n      this.logger.warn(`Failed to create safe date: ${date}`, error);\n      return new Date();\n    }\n  }\n  normalizeNotification(notification) {\n    this.logger.debug('MessageService', 'Normalizing notification', notification);\n    if (!notification) {\n      this.logger.error('MessageService', 'Notification is null or undefined');\n      throw new Error('Notification is required');\n    }\n    // Vérifier et normaliser l'ID\n    const notificationId = notification.id || notification._id;\n    if (!notificationId) {\n      this.logger.error('MessageService', 'Notification ID is missing', notification);\n      throw new Error('Notification ID is required');\n    }\n    if (!notification.timestamp) {\n      this.logger.warn('MessageService', 'Notification timestamp is missing, using current time', notification);\n      notification.timestamp = new Date();\n    }\n    try {\n      const normalized = {\n        ...notification,\n        _id: notificationId,\n        id: notificationId,\n        timestamp: new Date(notification.timestamp),\n        ...(notification.senderId && {\n          senderId: this.normalizeSender(notification.senderId)\n        }),\n        ...(notification.message && {\n          message: this.normalizeNotMessage(notification.message)\n        })\n      };\n      this.logger.debug('MessageService', 'Normalized notification result', normalized);\n      return normalized;\n    } catch (error) {\n      this.logger.error('MessageService', 'Error in normalizeNotification', error);\n      throw error;\n    }\n  }\n  normalizeSender(sender) {\n    return {\n      id: sender.id,\n      username: sender.username,\n      ...(sender.image && {\n        image: sender.image\n      })\n    };\n  }\n  /**\n   * Normalise un message de notification\n   * @param message Message à normaliser\n   * @returns Message normalisé\n   */\n  normalizeNotMessage(message) {\n    if (!message) return null;\n    return {\n      id: message.id || message._id,\n      content: message.content || '',\n      type: message.type || 'TEXT',\n      timestamp: this.safeDate(message.timestamp),\n      attachments: message.attachments || [],\n      ...(message.sender && {\n        sender: this.normalizeSender(message.sender)\n      })\n    };\n  }\n  /**\n   * Met à jour le cache de notifications avec une ou plusieurs notifications\n   * @param notifications Notification(s) à ajouter au cache\n   * @param skipDuplicates Si true, ignore les notifications déjà présentes dans le cache\n   */\n  updateCache(notifications, skipDuplicates = true) {\n    const notificationArray = Array.isArray(notifications) ? notifications : [notifications];\n    this.logger.debug('MessageService', `Updating notification cache with ${notificationArray.length} notifications`);\n    if (notificationArray.length === 0) {\n      this.logger.warn('MessageService', 'No notifications to update in cache');\n      return;\n    }\n    // Vérifier si les notifications ont des IDs valides\n    const validNotifications = notificationArray.filter(notif => notif && (notif.id || notif._id));\n    if (validNotifications.length !== notificationArray.length) {\n      this.logger.warn('MessageService', `Found ${notificationArray.length - validNotifications.length} notifications without valid IDs`);\n    }\n    let addedCount = 0;\n    let skippedCount = 0;\n    // Traiter chaque notification\n    validNotifications.forEach((notif, index) => {\n      try {\n        // S'assurer que la notification a un ID\n        const notifId = notif.id || notif._id;\n        if (!notifId) {\n          this.logger.error('MessageService', 'Notification without ID:', notif);\n          return;\n        }\n        // Normaliser la notification\n        const normalized = this.normalizeNotification(notif);\n        // Vérifier si cette notification existe déjà dans le cache\n        if (skipDuplicates && this.notificationCache.has(normalized.id)) {\n          this.logger.debug('MessageService', `Notification ${normalized.id} already exists in cache, skipping`);\n          skippedCount++;\n          return;\n        }\n        // Ajouter au cache\n        this.notificationCache.set(normalized.id, normalized);\n        addedCount++;\n        this.logger.debug('MessageService', `Added notification ${normalized.id} to cache`);\n      } catch (error) {\n        this.logger.error('MessageService', `Error processing notification ${index + 1}:`, error);\n      }\n    });\n    this.logger.debug('MessageService', `Cache update complete: ${addedCount} added, ${skippedCount} skipped, total: ${this.notificationCache.size}`);\n    // Mettre à jour les observables et sauvegarder\n    this.refreshNotificationObservables();\n  }\n  /**\n   * Met à jour les observables de notifications et sauvegarde dans le localStorage\n   * OPTIMISÉ: Trie les notifications par date (plus récentes en premier)\n   */\n  refreshNotificationObservables() {\n    const allNotifications = Array.from(this.notificationCache.values());\n    // 🚀 TRI OPTIMISÉ: Les notifications les plus récentes en premier\n    const sortedNotifications = this.sortNotificationsByDate(allNotifications);\n    this.logger.debug(`📊 SORTED: ${sortedNotifications.length} notifications triées par date (plus récentes en premier)`);\n    this.notifications.next(sortedNotifications);\n    this.updateUnreadCount();\n    this.saveNotificationsToLocalStorage();\n  }\n  /**\n   * Met à jour le compteur de notifications non lues\n   */\n  updateUnreadCount() {\n    const allNotifications = Array.from(this.notificationCache.values());\n    const unreadNotifications = allNotifications.filter(n => !n.isRead);\n    const count = unreadNotifications.length;\n    // Forcer la mise à jour dans la zone Angular\n    this.zone.run(() => {\n      this.notificationCount.next(count);\n      // Émettre un événement global pour forcer la mise à jour du layout\n      window.dispatchEvent(new CustomEvent('notificationCountChanged', {\n        detail: {\n          count\n        }\n      }));\n    });\n  }\n  /**\n   * Met à jour le cache avec une seule notification (méthode simplifiée)\n   * @param notification Notification à ajouter\n   */\n  updateNotificationCache(notification) {\n    this.updateCache(notification, true);\n  }\n  /**\n   * Met à jour le statut de lecture des notifications\n   * @param ids IDs des notifications à mettre à jour\n   * @param isRead Nouveau statut de lecture\n   */\n  updateNotificationStatus(ids, isRead) {\n    ids.forEach(id => {\n      const notif = this.notificationCache.get(id);\n      if (notif) {\n        this.notificationCache.set(id, {\n          ...notif,\n          isRead,\n          readAt: isRead ? new Date().toISOString() : undefined\n        });\n      }\n    });\n    this.refreshNotificationObservables();\n  }\n  /**\n   * Méthode générique pour supprimer des notifications du cache local\n   * @param notificationIds IDs des notifications à supprimer\n   * @returns Nombre de notifications supprimées\n   */\n  removeNotificationsFromCache(notificationIds) {\n    console.log('🗑️ REMOVE FROM CACHE: Starting removal of', notificationIds.length, 'notifications');\n    console.log('🗑️ REMOVE FROM CACHE: Cache size before:', this.notificationCache.size);\n    let removedCount = 0;\n    notificationIds.forEach(id => {\n      if (this.notificationCache.has(id)) {\n        console.log('🗑️ REMOVE FROM CACHE: Removing notification:', id);\n        this.notificationCache.delete(id);\n        removedCount++;\n      } else {\n        console.log('🗑️ REMOVE FROM CACHE: Notification not found in cache:', id);\n      }\n    });\n    console.log('🗑️ REMOVE FROM CACHE: Removed', removedCount, 'notifications');\n    console.log('🗑️ REMOVE FROM CACHE: Cache size after:', this.notificationCache.size);\n    if (removedCount > 0) {\n      console.log('🗑️ REMOVE FROM CACHE: Refreshing observables...');\n      this.refreshNotificationObservables();\n    }\n    return removedCount;\n  }\n  /**\n   * Méthode générique pour gérer les erreurs de suppression\n   * @param error Erreur survenue\n   * @param operation Nom de l'opération\n   * @param fallbackResponse Réponse de fallback en cas d'erreur\n   */\n  handleDeletionError(error, operation, fallbackResponse) {\n    this.logger.error('MessageService', `Erreur lors de ${operation}:`, error);\n    return of(fallbackResponse);\n  }\n  // Typing indicators\n  startTyping(conversationId) {\n    const userId = this.getCurrentUserId();\n    if (!userId) {\n      this.logger.warn('MessageService', 'Cannot start typing: no user ID');\n      return of(false);\n    }\n    return this.apollo.mutate({\n      mutation: START_TYPING_MUTATION,\n      variables: {\n        input: {\n          conversationId,\n          userId\n        }\n      }\n    }).pipe(map(result => result.data?.startTyping || false), catchError(error => {\n      this.logger.error('MessageService', 'Error starting typing indicator', error);\n      return throwError(() => new Error('Failed to start typing indicator'));\n    }));\n  }\n  stopTyping(conversationId) {\n    const userId = this.getCurrentUserId();\n    if (!userId) {\n      this.logger.warn('MessageService', 'Cannot stop typing: no user ID');\n      return of(false);\n    }\n    return this.apollo.mutate({\n      mutation: STOP_TYPING_MUTATION,\n      variables: {\n        input: {\n          conversationId,\n          userId\n        }\n      }\n    }).pipe(map(result => result.data?.stopTyping || false), catchError(error => {\n      this.logger.error('MessageService', 'Error stopping typing indicator', error);\n      return throwError(() => new Error('Failed to stop typing indicator'));\n    }));\n  }\n  // ========================================\n  // MÉTHODE SENDMESSAGE MANQUANTE\n  // ========================================\n  /**\n   * Envoie un message (texte, fichier, audio, etc.)\n   * @param receiverId ID du destinataire\n   * @param content Contenu du message (texte)\n   * @param file Fichier à envoyer (optionnel)\n   * @param messageType Type de message (TEXT, AUDIO, IMAGE, etc.)\n   * @param conversationId ID de la conversation\n   * @returns Observable avec le message envoyé\n   */\n  sendMessage(receiverId, content, file, messageType = 'TEXT', conversationId) {\n    console.log('🚀 [MessageService] sendMessage called with:', {\n      receiverId,\n      content: content?.substring(0, 50),\n      hasFile: !!file,\n      fileName: file?.name,\n      fileType: file?.type,\n      fileSize: file?.size,\n      messageType,\n      conversationId\n    });\n    if (!receiverId) {\n      const error = new Error('Receiver ID is required');\n      console.error('❌ [MessageService] sendMessage error:', error);\n      return throwError(() => error);\n    }\n    // Préparer les variables pour la mutation\n    const variables = {\n      receiverId,\n      content: content || '',\n      type: messageType\n    };\n    // Ajouter l'ID de conversation si fourni\n    if (conversationId) {\n      variables.conversationId = conversationId;\n    }\n    // Si un fichier est fourni, l'ajouter aux variables\n    if (file) {\n      variables.file = file;\n      console.log('📁 [MessageService] Adding file to mutation:', {\n        name: file.name,\n        type: file.type,\n        size: file.size\n      });\n    }\n    console.log('📤 [MessageService] Sending mutation with variables:', variables);\n    return this.apollo.mutate({\n      mutation: SEND_MESSAGE_MUTATION,\n      variables,\n      context: {\n        useMultipart: !!file // Utiliser multipart si un fichier est présent\n      }\n    }).pipe(map(result => {\n      console.log('✅ [MessageService] sendMessage mutation result:', result);\n      if (!result.data?.sendMessage) {\n        throw new Error('No message data received from server');\n      }\n      const message = result.data.sendMessage;\n      console.log('📨 [MessageService] Message sent successfully:', {\n        id: message.id,\n        type: message.type,\n        content: message.content?.substring(0, 50),\n        hasAttachments: !!message.attachments?.length\n      });\n      // Normaliser le message reçu\n      const normalizedMessage = this.normalizeMessage(message);\n      console.log('🔧 [MessageService] Message normalized:', normalizedMessage);\n      return normalizedMessage;\n    }), catchError(error => {\n      console.error('❌ [MessageService] sendMessage error:', error);\n      this.logger.error('Error sending message:', error);\n      // Fournir un message d'erreur plus spécifique\n      let errorMessage = \"Erreur lors de l'envoi du message\";\n      if (error.networkError) {\n        errorMessage = 'Erreur de connexion réseau';\n      } else if (error.graphQLErrors?.length > 0) {\n        errorMessage = error.graphQLErrors[0].message || errorMessage;\n      }\n      return throwError(() => new Error(errorMessage));\n    }));\n  }\n  // ========================================\n  // MÉTHODES UTILITAIRES CONSOLIDÉES\n  // ========================================\n  /**\n   * Formate l'heure d'un message\n   */\n  formatMessageTime(timestamp) {\n    if (!timestamp) return 'Unknown time';\n    try {\n      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\n      return date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: false\n      });\n    } catch (error) {\n      return 'Invalid time';\n    }\n  }\n  /**\n   * Formate la dernière activité d'un utilisateur\n   */\n  formatLastActive(lastActive) {\n    if (!lastActive) return 'Offline';\n    const lastActiveDate = lastActive instanceof Date ? lastActive : new Date(lastActive);\n    const now = new Date();\n    const diffHours = Math.abs(now.getTime() - lastActiveDate.getTime()) / (1000 * 60 * 60);\n    if (diffHours < 24) {\n      return `Active ${lastActiveDate.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      })}`;\n    }\n    return `Active ${lastActiveDate.toLocaleDateString()}`;\n  }\n  /**\n   * Formate la date d'un message\n   */\n  formatMessageDate(timestamp) {\n    if (!timestamp) return 'Unknown date';\n    try {\n      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\n      const today = new Date();\n      if (date.toDateString() === today.toDateString()) {\n        return date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n      }\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n      if (date.toDateString() === yesterday.toDateString()) {\n        return `LUN., ${date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        })}`;\n      }\n      const day = date.toLocaleDateString('fr-FR', {\n        weekday: 'short'\n      }).toUpperCase();\n      return `${day}., ${date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      })}`;\n    } catch (error) {\n      return 'Invalid date';\n    }\n  }\n  /**\n   * Détermine si un en-tête de date doit être affiché\n   */\n  shouldShowDateHeader(messages, index) {\n    if (index === 0) return true;\n    try {\n      const currentMsg = messages[index];\n      const prevMsg = messages[index - 1];\n      if (!currentMsg?.timestamp || !prevMsg?.timestamp) return true;\n      const currentDate = this.getDateFromTimestamp(currentMsg.timestamp);\n      const prevDate = this.getDateFromTimestamp(prevMsg.timestamp);\n      return currentDate !== prevDate;\n    } catch (error) {\n      return false;\n    }\n  }\n  getDateFromTimestamp(timestamp) {\n    if (!timestamp) return 'unknown-date';\n    try {\n      return (timestamp instanceof Date ? timestamp : new Date(timestamp)).toDateString();\n    } catch (error) {\n      return 'invalid-date';\n    }\n  }\n  /**\n   * Obtient l'icône d'un fichier selon son type MIME\n   */\n  getFileIcon(mimeType) {\n    if (!mimeType) return 'fa-file';\n    if (mimeType.startsWith('image/')) return 'fa-image';\n    if (mimeType.includes('pdf')) return 'fa-file-pdf';\n    if (mimeType.includes('word') || mimeType.includes('msword')) return 'fa-file-word';\n    if (mimeType.includes('excel')) return 'fa-file-excel';\n    if (mimeType.includes('powerpoint')) return 'fa-file-powerpoint';\n    if (mimeType.includes('audio')) return 'fa-file-audio';\n    if (mimeType.includes('video')) return 'fa-file-video';\n    if (mimeType.includes('zip') || mimeType.includes('compressed')) return 'fa-file-archive';\n    return 'fa-file';\n  }\n  /**\n   * Obtient le type d'un fichier selon son type MIME\n   */\n  getFileType(mimeType) {\n    if (!mimeType) return 'File';\n    const typeMap = {\n      'image/': 'Image',\n      'application/pdf': 'PDF',\n      'application/msword': 'Word Doc',\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word Doc',\n      'application/vnd.ms-excel': 'Excel',\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel',\n      'application/vnd.ms-powerpoint': 'PowerPoint',\n      'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'PowerPoint',\n      'audio/': 'Audio',\n      'video/': 'Video',\n      'application/zip': 'ZIP Archive',\n      'application/x-rar-compressed': 'RAR Archive'\n    };\n    for (const [key, value] of Object.entries(typeMap)) {\n      if (mimeType.includes(key)) return value;\n    }\n    return 'File';\n  }\n  /**\n   * Vérifie si un message contient une image\n   */\n  hasImage(message) {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return false;\n    }\n    const attachment = message.attachments[0];\n    if (!attachment || !attachment.type) {\n      return false;\n    }\n    const type = attachment.type.toString();\n    return type === 'IMAGE' || type === 'image';\n  }\n  /**\n   * Vérifie si le message est un message vocal\n   */\n  isVoiceMessage(message) {\n    if (!message) return false;\n    // Vérifier le type du message\n    if (message.type === MessageType.VOICE_MESSAGE || message.type === MessageType.VOICE_MESSAGE) {\n      return true;\n    }\n    // Vérifier les pièces jointes\n    if (message.attachments && message.attachments.length > 0) {\n      return message.attachments.some(att => {\n        const type = att.type?.toString();\n        return type === 'VOICE_MESSAGE' || type === 'voice_message' || message.metadata?.isVoiceMessage && (type === 'AUDIO' || type === 'audio');\n      });\n    }\n    // Vérifier les métadonnées\n    return !!message.metadata?.isVoiceMessage;\n  }\n  /**\n   * Récupère l'URL du message vocal\n   */\n  getVoiceMessageUrl(message) {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return '';\n    }\n    const voiceAttachment = message.attachments.find(att => {\n      const type = att.type?.toString();\n      return type === 'VOICE_MESSAGE' || type === 'voice_message' || type === 'AUDIO' || type === 'audio';\n    });\n    return voiceAttachment?.url || '';\n  }\n  /**\n   * Récupère la durée du message vocal\n   */\n  getVoiceMessageDuration(message) {\n    if (!message) return 0;\n    // Essayer d'abord de récupérer la durée depuis les métadonnées\n    if (message.metadata?.duration) {\n      return message.metadata.duration;\n    }\n    // Sinon, essayer de récupérer depuis les pièces jointes\n    if (message.attachments && message.attachments.length > 0) {\n      const voiceAttachment = message.attachments.find(att => {\n        const type = att.type?.toString();\n        return type === 'VOICE_MESSAGE' || type === 'voice_message' || type === 'AUDIO' || type === 'audio';\n      });\n      if (voiceAttachment && voiceAttachment.duration) {\n        return voiceAttachment.duration;\n      }\n    }\n    return 0;\n  }\n  /**\n   * Génère la hauteur des barres de la forme d'onde moderne\n   */\n  getVoiceBarHeight(index) {\n    const pattern = [8, 12, 6, 15, 10, 18, 7, 14, 9, 16, 5, 13, 11, 17, 8, 12, 6, 15, 10, 18];\n    return pattern[index % pattern.length];\n  }\n  /**\n   * Formate la durée du message vocal en format MM:SS\n   */\n  formatVoiceDuration(seconds) {\n    if (!seconds || seconds === 0) {\n      return '0:00';\n    }\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n  /**\n   * Obtient l'URL de l'image en toute sécurité\n   */\n  getImageUrl(message) {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return '';\n    }\n    const attachment = message.attachments[0];\n    return attachment?.url || '';\n  }\n  /**\n   * Détermine le type d'un message\n   */\n  getMessageType(message) {\n    if (!message) return MessageType.TEXT;\n    try {\n      if (message.type) {\n        const msgType = message.type.toString();\n        if (msgType === 'text' || msgType === 'TEXT') {\n          return MessageType.TEXT;\n        } else if (msgType === 'image' || msgType === 'IMAGE') {\n          return MessageType.IMAGE;\n        } else if (msgType === 'file' || msgType === 'FILE') {\n          return MessageType.FILE;\n        } else if (msgType === 'audio' || msgType === 'AUDIO') {\n          return MessageType.AUDIO;\n        } else if (msgType === 'video' || msgType === 'VIDEO') {\n          return MessageType.VIDEO;\n        } else if (msgType === 'system' || msgType === 'SYSTEM') {\n          return MessageType.SYSTEM;\n        }\n      }\n      if (message.attachments?.length) {\n        const attachment = message.attachments[0];\n        if (attachment && attachment.type) {\n          const attachmentTypeStr = attachment.type.toString();\n          if (attachmentTypeStr === 'image' || attachmentTypeStr === 'IMAGE') {\n            return MessageType.IMAGE;\n          } else if (attachmentTypeStr === 'file' || attachmentTypeStr === 'FILE') {\n            return MessageType.FILE;\n          } else if (attachmentTypeStr === 'audio' || attachmentTypeStr === 'AUDIO') {\n            return MessageType.AUDIO;\n          } else if (attachmentTypeStr === 'video' || attachmentTypeStr === 'VIDEO') {\n            return MessageType.VIDEO;\n          }\n        }\n        return MessageType.FILE;\n      }\n      return MessageType.TEXT;\n    } catch (error) {\n      return MessageType.TEXT;\n    }\n  }\n  /**\n   * Retourne la liste des emojis communs\n   */\n  getCommonEmojis() {\n    return ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣', '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗', '🤔', '👍', '👎', '👏', '🙌', '👐', '🤲', '🤝', '🙏', '✌️', '🤞', '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '💔', '💯', '💢'];\n  }\n  /**\n   * Obtient les classes CSS pour un message\n   */\n  getMessageTypeClass(message, currentUserId) {\n    if (!message) {\n      return 'bg-gray-100 rounded-lg px-4 py-2';\n    }\n    try {\n      const isCurrentUser = message.sender?.id === currentUserId || message.sender?._id === currentUserId || message.senderId === currentUserId;\n      const baseClass = isCurrentUser ? 'bg-blue-500 text-white rounded-2xl rounded-br-sm' : 'bg-gray-200 text-gray-800 rounded-2xl rounded-bl-sm';\n      const messageType = this.getMessageType(message);\n      if (message.attachments && message.attachments.length > 0) {\n        const attachment = message.attachments[0];\n        if (attachment && attachment.type) {\n          const attachmentTypeStr = attachment.type.toString();\n          if (attachmentTypeStr === 'IMAGE' || attachmentTypeStr === 'image') {\n            return `p-1 max-w-xs`;\n          } else if (attachmentTypeStr === 'FILE' || attachmentTypeStr === 'file') {\n            return `${baseClass} p-3`;\n          }\n        }\n      }\n      // Les vérifications de type sont déjà faites avec les attachments ci-dessus\n      return `${baseClass} px-4 py-3 whitespace-normal break-words min-w-[120px]`;\n    } catch (error) {\n      return 'bg-gray-100 rounded-lg px-4 py-2 whitespace-normal break-words';\n    }\n  }\n  // ========================================\n  // APPELS WEBRTC - DÉLÉGUÉS AU CALLSERVICE\n  // ========================================\n  // Note: Les méthodes d'appel ont été déplacées vers CallService\n  // pour éviter la duplication de code et centraliser la logique\n  // destroy\n  cleanupSubscriptions() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.subscriptions = [];\n    if (this.cleanupInterval) {\n      clearInterval(this.cleanupInterval);\n    }\n    this.notificationCache.clear();\n    this.logger.debug('NotificationService destroyed');\n  }\n  ngOnDestroy() {\n    this.cleanupSubscriptions();\n  }\n  static {\n    this.ɵfac = function MessageService_Factory(t) {\n      return new (t || MessageService)(i0.ɵɵinject(i1.Apollo), i0.ɵɵinject(i2.LoggerService), i0.ɵɵinject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: MessageService,\n      factory: MessageService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "Observable", "of", "throwError", "retry", "EMPTY", "map", "catchError", "tap", "filter", "debounceTime", "distinctUntilChanged", "shareReplay", "environment", "MessageType", "CallType", "CallStatus", "GET_CONVERSATIONS_QUERY", "GET_NOTIFICATIONS_QUERY", "NOTIFICATION_SUBSCRIPTION", "GET_CONVERSATION_QUERY", "SEND_MESSAGE_MUTATION", "MARK_AS_READ_MUTATION", "MESSAGE_SENT_SUBSCRIPTION", "USER_STATUS_SUBSCRIPTION", "GET_USER_QUERY", "GET_ALL_USER_QUERY", "CONVERSATION_UPDATED_SUBSCRIPTION", "SEARCH_MESSAGES_QUERY", "GET_UNREAD_MESSAGES_QUERY", "SET_USER_ONLINE_MUTATION", "SET_USER_OFFLINE_MUTATION", "START_TYPING_MUTATION", "STOP_TYPING_MUTATION", "TYPING_INDICATOR_SUBSCRIPTION", "GET_CURRENT_USER_QUERY", "REACT_TO_MESSAGE_MUTATION", "FORWARD_MESSAGE_MUTATION", "PIN_MESSAGE_MUTATION", "CREATE_GROUP_MUTATION", "UPDATE_GROUP_MUTATION", "DELETE_GROUP_MUTATION", "LEAVE_GROUP_MUTATION", "GET_GROUP_QUERY", "GET_USER_GROUPS_QUERY", "EDIT_MESSAGE_MUTATION", "DELETE_MESSAGE_MUTATION", "GET_MESSAGES_QUERY", "GET_NOTIFICATIONS_ATTACHAMENTS", "MARK_NOTIFICATION_READ_MUTATION", "NOTIFICATIONS_READ_SUBSCRIPTION", "CREATE_CONVERSATION_MUTATION", "DELETE_NOTIFICATION_MUTATION", "DELETE_MULTIPLE_NOTIFICATIONS_MUTATION", "DELETE_ALL_NOTIFICATIONS_MUTATION", "CALL_HISTORY_QUERY", "CALL_DETAILS_QUERY", "CALL_STATS_QUERY", "INITIATE_CALL_MUTATION", "SEND_CALL_SIGNAL_MUTATION", "CALL_SIGNAL_SUBSCRIPTION", "INCOMING_CALL_SUBSCRIPTION", "GET_VOICE_MESSAGES_QUERY", "MessageService", "constructor", "apollo", "logger", "zone", "activeConversation", "notifications", "notificationCache", "Map", "notificationCount", "onlineUsers", "subscriptions", "CACHE_DURATION", "lastFetchTime", "activeCall", "incomingCall", "callSignals", "localStream", "remoteStream", "peerConnection", "activeCall$", "asObservable", "incomingCall$", "callSignals$", "localStream$", "remoteStream$", "rtcConfig", "iceServers", "urls", "usersCache", "currentUserPagination", "totalCount", "totalPages", "currentPage", "hasNextPage", "hasPreviousPage", "activeConversation$", "notifications$", "notificationCount$", "sounds", "isPlaying", "muted", "notificationPagination", "limit", "hasMoreNotifications", "subscriptionCache", "subscriptionRefCount", "toSafeISOString", "date", "undefined", "toISOString", "loadNotificationsFromLocalStorage", "initSubscriptions", "startCleanupInterval", "preloadSounds", "savedNotifications", "localStorage", "getItem", "JSON", "parse", "clear", "for<PERSON>ach", "notification", "id", "set", "next", "Array", "from", "values", "updateUnreadCount", "error", "runOutsideAngular", "subscribeToNewNotifications", "subscribe", "subscribeToNotificationsRead", "subscribeToIncomingCalls", "subscribeToUserStatus", "query", "pipe", "data", "handleIncomingCall", "call", "play", "subscribeToMessages", "messageSent", "normalizeMessage", "subscribeToTypingIndicators", "typingIndicator", "startTyping", "conversationId", "mutate", "mutation", "variables", "result", "stopTyping", "initiateCall", "recipientId", "callType", "Error", "sendMessageWithFile", "senderId", "receiverId", "content", "file", "sendMessage", "subscribeToNotifications", "notificationReceived", "markNotificationAsRead", "notificationId", "createOrGetConversation", "userId", "createConversation", "normalizeConversation", "loadSound", "name", "path", "audio", "Audio", "load", "addEventListener", "loop", "sound", "currentTime", "catch", "stop", "pause", "stopAllSounds", "Object", "keys", "setMuted", "isMuted", "playNotificationSound", "console", "log", "audioContext", "window", "AudioContext", "webkitAudioContext", "playNotificationMelody1", "volume", "err", "audioError", "playNotificationTone", "playNotificationMelody2", "playNotificationMelody3", "playNotificationMelody4", "playNotificationMelody5", "playBellTone", "startTime", "frequency", "duration", "oscillator", "createOscillator", "gainNode", "createGain", "type", "setValueAtTime", "gain", "linearRampToValueAtTime", "connect", "destination", "start", "exponentialRampToValueAtTime", "playAudio", "audioUrl", "Promise", "resolve", "reject", "onended", "onerror", "getVoiceMessages", "debug", "watch<PERSON><PERSON>y", "fetchPolicy", "valueChanges", "voiceMessages", "length", "getMessages", "page", "errorPolicy", "messages", "batchNormalizeMessages", "editMessage", "messageId", "newContent", "deleteMessage", "markMessageAsRead", "readAt", "Date", "reactToMessage", "emoji", "forwardMessage", "conversationIds", "msg", "timestamp", "normalizeDate", "pinMessage", "pinnedAt", "searchMessages", "filters", "dateFrom", "dateTo", "safeDate", "sender", "normalizeUser", "_id", "now", "TEXT", "isRead", "getCurrentUserId", "username", "getUnreadMessages", "setActiveConversation", "getConversations", "conversations", "conv", "getConversation", "info", "offset", "normalizedConversation", "participants", "conversation", "message", "getOrCreateConversation", "currentUserId", "existingConversation", "find", "isGroup", "participantIds", "p", "includes", "getNotifications", "refresh", "deletedNotificationIds", "getDeletedNotificationIds", "size", "errors", "e", "join", "getUserNotifications", "filteredNotifications", "notif", "has", "index", "updateCache", "cachedNotifications", "sortedNotifications", "sortNotificationsByDate", "saveNotificationsToLocalStorage", "graphQLErrors", "networkError", "deletedIds", "Set", "savedNotificationIds", "n", "serverNotifications", "client", "readQuery", "add", "loadMoreNotifications", "nextPage", "getNotificationById", "getNotificationCount", "value", "getNotificationAttachments", "getUnreadNotifications", "deleteNotification", "warn", "removedCount", "removeNotificationsFromCache", "response", "handleDeletionError", "success", "setItem", "stringify", "deleteAllNotifications", "count", "allNotificationIds", "deleteMultipleNotifications", "notificationIds", "groupNotificationsByType", "groups", "get", "push", "mark<PERSON><PERSON><PERSON>", "readCount", "remainingCount", "validIds", "trim", "provided", "valid", "updateNotificationStatus", "optimisticResponse", "markNotificationsAsRead", "Math", "max", "subscribeToCallSignals", "callId", "callSignal", "signal", "handleCallSignal", "sendCallSignal", "signalType", "signalData", "getCallHistory", "status", "startDate", "endDate", "history", "callHistory", "getCallDetails", "details", "callDetails", "getCallStats", "stats", "callStats", "handleIceCandidate", "handleAnswer", "handleEndCall", "handleRejectCall", "candidate", "addIceCandidate", "RTCIceCandidate", "answer", "setRemoteDescription", "RTCSessionDescription", "cleanupCall", "currentCall", "ENDED", "endTime", "REJECTED", "getTracks", "track", "close", "setupMediaDevices", "constraints", "video", "AUDIO", "width", "ideal", "height", "observer", "navigator", "mediaDevices", "getUserMedia", "then", "stream", "complete", "generateCallId", "toString", "random", "substring", "getAllUsers", "forceRefresh", "search", "sortBy", "sortOrder", "isOnline", "cacheValid", "paginatedResponse", "users", "user", "getOneUser", "getCurrentUser", "setUserOnline", "setUserOffline", "createGroup", "photo", "description", "group", "updateGroup", "groupId", "input", "deleteGroup", "leaveGroup", "getGroup", "getUserGroups", "subscribeToNewMessages", "cache<PERSON>ey", "refCount", "isTokenValid", "production", "sub$", "normalizedMessage", "VOICE_MESSAGE", "attachments", "some", "att", "run", "updateConversationWithNewMessage", "minimalMessage", "prev", "curr", "bufferSize", "sub", "delete", "setTimeout", "refreshSenderNotifications", "userStatusChanged", "subscribeToConversationUpdates", "conversationUpdated", "lastMessage", "subscribeToTypingIndicator", "Boolean", "token", "parts", "split", "payload", "atob", "exp", "expirationDate", "expiration", "notificationsRead", "source$", "processed$", "normalized", "normalizeNotification", "currentNotifications", "existingNotification", "updateNotificationCache", "updatedNotifications", "cleanupInterval", "setInterval", "cleanupExpiredNotifications", "thirtyDaysAgo", "getTime", "expiredCount", "notificationDate", "remainingNotifications", "sort", "a", "b", "dateA", "dateB", "normalizedSender", "email", "role", "isActive", "normalizedReceiver", "receiver", "normalizedAttachments", "url", "metadata", "String", "image", "bio", "lastActive", "createdAt", "updatedAt", "followingCount", "followersCount", "postCount", "normalizedParticipants", "isArray", "participant", "normalizedMessages", "normalizedLastMessage", "unreadCount", "participantCount", "messageCount", "normalizeSender", "normalizeNotMessage", "skipDuplicates", "notificationArray", "validNotifications", "addedCount", "skippedCount", "notifId", "refreshNotificationObservables", "allNotifications", "unreadNotifications", "dispatchEvent", "CustomEvent", "detail", "ids", "operation", "fallbackResponse", "messageType", "hasFile", "fileName", "fileType", "fileSize", "context", "useMultipart", "hasAttachments", "errorMessage", "formatMessageTime", "toLocaleTimeString", "hour", "minute", "hour12", "formatLastActive", "lastActiveDate", "diffHours", "abs", "toLocaleDateString", "formatMessageDate", "today", "toDateString", "yesterday", "setDate", "getDate", "day", "weekday", "toUpperCase", "shouldShowDateHeader", "currentMsg", "prevMsg", "currentDate", "getDateFromTimestamp", "prevDate", "getFileIcon", "mimeType", "startsWith", "getFileType", "typeMap", "key", "entries", "hasImage", "attachment", "isVoiceMessage", "getVoiceMessageUrl", "voiceAttachment", "getVoiceMessageDuration", "getVoiceBarHeight", "pattern", "formatVoiceDuration", "seconds", "minutes", "floor", "remainingSeconds", "padStart", "getImageUrl", "getMessageType", "msgType", "IMAGE", "FILE", "VIDEO", "SYSTEM", "attachmentTypeStr", "getCommonEmojis", "getMessageTypeClass", "isCurrentUser", "baseClass", "cleanupSubscriptions", "unsubscribe", "clearInterval", "ngOnDestroy", "i0", "ɵɵinject", "i1", "Apollo", "i2", "LoggerService", "NgZone", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\services\\message.service.ts"], "sourcesContent": ["import { Injectable, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@angular/core';\nimport { Apollo } from 'apollo-angular';\nimport {\n  BehaviorSubject,\n  Observable,\n  of,\n  Subscription,\n  throwError,\n  retry,\n  EMPTY,\n} from 'rxjs';\nimport {\n  map,\n  catchError,\n  tap,\n  filter,\n  switchMap,\n  concatMap,\n  toArray,\n  debounceTime,\n  distinctUntilChanged,\n  shareReplay,\n} from 'rxjs/operators';\nimport { from } from 'rxjs';\nimport { environment } from '../../environments/environment';\nimport {\n  MessageType,\n  Call,\n  CallType,\n  CallStatus,\n  IncomingCall,\n  CallSignal,\n  CallOptions,\n  CallFeedback,\n  CallSuccess,\n} from '../models/message.model';\nimport {\n  GET_CONVERSATIONS_QUERY,\n  GET_NOTIFICATIONS_QUERY,\n  NOTIFICATION_SUBSCRIPTION,\n  GET_CONVERSATION_QUERY,\n  SEND_MESSAGE_MUTATION,\n  MARK_AS_READ_MUTATION,\n  MESSAGE_SENT_SUBSCRIPTION,\n  USER_STATUS_SUBSCRIPTION,\n  GET_USER_QUERY,\n  GET_ALL_USER_QUERY,\n  CONVERSATION_UPDATED_SUBSCRIPTION,\n  SEARCH_MESSAGES_QUERY,\n  GET_UNREAD_MESSAGES_QUERY,\n  SET_USER_ONLINE_MUTATION,\n  SET_USER_OFFLINE_MUTATION,\n  START_TYPING_MUTATION,\n  STOP_TYPING_MUTATION,\n  TYPING_INDICATOR_SUBSCRIPTION,\n  GET_CURRENT_USER_QUERY,\n  REACT_TO_MESSAGE_MUTATION,\n  FORWARD_MESSAGE_MUTATION,\n  PIN_MESSAGE_MUTATION,\n  CREATE_GROUP_MUTATION,\n  UPDATE_GROUP_MUTATION,\n  DELETE_GROUP_MUTATION,\n  ADD_GROUP_PARTICIPANTS_MUTATION,\n  REMOVE_GROUP_PARTICIPANTS_MUTATION,\n  LEAVE_GROUP_MUTATION,\n  GET_GROUP_QUERY,\n  GET_USER_GROUPS_QUERY,\n  EDIT_MESSAGE_MUTATION,\n  DELETE_MESSAGE_MUTATION,\n  GET_MESSAGES_QUERY,\n  GET_NOTIFICATIONS_ATTACHAMENTS,\n  MARK_NOTIFICATION_READ_MUTATION,\n  NOTIFICATIONS_READ_SUBSCRIPTION,\n  CREATE_CONVERSATION_MUTATION,\n  DELETE_NOTIFICATION_MUTATION,\n  DELETE_MULTIPLE_NOTIFICATIONS_MUTATION,\n  DELETE_ALL_NOTIFICATIONS_MUTATION,\n  // Requêtes et mutations pour les appels\n  CALL_HISTORY_QUERY,\n  CALL_DETAILS_QUERY,\n  CALL_STATS_QUERY,\n  INITIATE_CALL_MUTATION,\n  SEND_CALL_SIGNAL_MUTATION,\n  ACCEPT_CALL_MUTATION,\n  REJECT_CALL_MUTATION,\n  END_CALL_MUTATION,\n  TOGGLE_CALL_MEDIA_MUTATION,\n  CALL_SIGNAL_SUBSCRIPTION,\n  INCOMING_CALL_SUBSCRIPTION,\n  CALL_STATUS_CHANGED_SUBSCRIPTION,\n  GET_VOICE_MESSAGES_QUERY,\n} from '../graphql/message.graphql';\nimport {\n  Conversation,\n  Message,\n  Notification,\n  User,\n  Attachment,\n  getNotificationAttachmentsEvent,\n  Group,\n  MessageFilter,\n  TypingIndicatorEvent,\n  GetConversationsResponse,\n  GetConversationResponse,\n  MarkAsReadResponse,\n  ReactToMessageResponse,\n  ForwardMessageResponse,\n  PinMessageResponse,\n  SearchMessagesResponse,\n  SendMessageResponse,\n  GetUnreadMessagesResponse,\n  GetAllUsersResponse,\n  GetOneUserResponse,\n  getCurrentUserResponse,\n  SetUserOnlineResponse,\n  SetUserOfflineResponse,\n  GetGroupResponse,\n  GetUserGroupsResponse,\n  CreateGroupResponse,\n  UpdateGroupResponse,\n  StartTupingResponse,\n  StopTypingResponse,\n  TypingIndicatorEvents,\n  getUserNotificationsResponse,\n  NotificationType,\n  MarkNotificationsAsReadResponse,\n  NotificationReceivedEvent,\n  NotificationsReadEvent,\n} from '../models/message.model';\nimport { LoggerService } from './logger.service';\n@Injectable({\n  providedIn: 'root',\n})\nexport class MessageService implements OnDestroy {\n  // État partagé\n  private activeConversation = new BehaviorSubject<string | null>(null);\n  private notifications = new BehaviorSubject<Notification[]>([]);\n  private notificationCache = new Map<string, Notification>();\n  private cleanupInterval: any;\n  private notificationCount = new BehaviorSubject<number>(0);\n  private onlineUsers = new Map<string, User>();\n  private subscriptions: Subscription[] = [];\n  private readonly CACHE_DURATION = 300000;\n  private lastFetchTime = 0;\n\n  // Propriétés pour les appels\n  private activeCall = new BehaviorSubject<Call | null>(null);\n  private incomingCall = new BehaviorSubject<IncomingCall | null>(null);\n  private callSignals = new BehaviorSubject<CallSignal | null>(null);\n  private localStream: MediaStream | null = null;\n  private remoteStream: MediaStream | null = null;\n  private peerConnection: RTCPeerConnection | null = null;\n\n  // Observables publics pour les appels\n  public activeCall$ = this.activeCall.asObservable();\n  public incomingCall$ = this.incomingCall.asObservable();\n  public callSignals$ = this.callSignals.asObservable();\n  public localStream$ = new BehaviorSubject<MediaStream | null>(null);\n  public remoteStream$ = new BehaviorSubject<MediaStream | null>(null);\n\n  // Configuration WebRTC\n  private readonly rtcConfig: RTCConfiguration = {\n    iceServers: [\n      { urls: 'stun:stun.l.google.com:19302' },\n      { urls: 'stun:stun1.l.google.com:19302' },\n    ],\n  };\n  private usersCache: User[] = [];\n\n  // Pagination metadata for user list\n  public currentUserPagination: {\n    totalCount: number;\n    totalPages: number;\n    currentPage: number;\n    hasNextPage: boolean;\n    hasPreviousPage: boolean;\n  } = {\n    totalCount: 0,\n    totalPages: 0,\n    currentPage: 1,\n    hasNextPage: false,\n    hasPreviousPage: false,\n  };\n\n  // Observables publics\n  public activeConversation$ = this.activeConversation.asObservable();\n  public notifications$ = this.notifications.asObservable();\n  public notificationCount$ = this.notificationCount.asObservable();\n\n  // Propriétés pour la gestion des sons\n  private sounds: { [key: string]: HTMLAudioElement } = {};\n  private isPlaying: { [key: string]: boolean } = {};\n  private muted = false;\n\n  constructor(\n    private apollo: Apollo,\n    private logger: LoggerService,\n    private zone: NgZone\n  ) {\n    this.loadNotificationsFromLocalStorage();\n    this.initSubscriptions();\n    this.startCleanupInterval();\n    this.preloadSounds();\n  }\n\n  /**\n   * Charge les notifications depuis le localStorage\n   * @private\n   */\n  private loadNotificationsFromLocalStorage(): void {\n    try {\n      const savedNotifications = localStorage.getItem('notifications');\n      if (savedNotifications) {\n        const notifications = JSON.parse(savedNotifications) as Notification[];\n\n        this.notificationCache.clear();\n\n        notifications.forEach((notification) => {\n          if (notification && notification.id) {\n            this.notificationCache.set(notification.id, notification);\n          }\n        });\n\n        this.notifications.next(Array.from(this.notificationCache.values()));\n        this.updateUnreadCount();\n      }\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n  private initSubscriptions(): void {\n    this.zone.runOutsideAngular(() => {\n      this.subscribeToNewNotifications().subscribe();\n      this.subscribeToNotificationsRead().subscribe();\n      this.subscribeToIncomingCalls().subscribe();\n      // 🔥 AJOUT: Subscription générale pour l'utilisateur\n    });\n    this.subscribeToUserStatus();\n  }\n\n  /**\n   * S'abonne aux appels entrants\n   */\n  private subscribeToIncomingCalls(): Observable<IncomingCall | null> {\n    return this.apollo\n      .subscribe<{ incomingCall: IncomingCall }>({\n        query: INCOMING_CALL_SUBSCRIPTION,\n      })\n      .pipe(\n        map(({ data }) => {\n          if (!data?.incomingCall) {\n            return null;\n          }\n\n          // Gérer l'appel entrant\n          this.handleIncomingCall(data.incomingCall);\n          return data.incomingCall;\n        }),\n        catchError((error) => {\n          this.logger.error('Error in incoming call subscription', error);\n          return of(null);\n        })\n      );\n  }\n\n  /**\n   * Gère un appel entrant\n   */\n  private handleIncomingCall(call: IncomingCall): void {\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n  }\n\n  // --------------------------------------------------------------------------\n  // Section: Subscriptions aux événements temps réel\n  // --------------------------------------------------------------------------\n\n  /**\n   * S'abonne aux nouveaux messages\n   */\n  subscribeToMessages(): Observable<Message | null> {\n    return this.apollo\n      .subscribe<MessageSentEvent>({\n        query: MESSAGE_SENT_SUBSCRIPTION,\n      })\n      .pipe(\n        map(({ data }) => {\n          if (!data?.messageSent) {\n            return null;\n          }\n          return this.normalizeMessage(data.messageSent);\n        }),\n        catchError((error) => {\n          this.logger.error('Error in message subscription', error);\n          return of(null);\n        })\n      );\n  }\n\n  /**\n   * S'abonne aux indicateurs de frappe\n   */\n  subscribeToTypingIndicators(): Observable<TypingIndicatorEvent | null> {\n    return this.apollo\n      .subscribe<TypingIndicatorEvents>({\n        query: TYPING_INDICATOR_SUBSCRIPTION,\n      })\n      .pipe(\n        map(({ data }) => {\n          if (!data?.typingIndicator) {\n            return null;\n          }\n          return data.typingIndicator;\n        }),\n        catchError((error) => {\n          this.logger.error('Error in typing indicator subscription', error);\n          return of(null);\n        })\n      );\n  }\n\n  /**\n   * Démarre l'indicateur de frappe\n   */\n  startTyping(conversationId: string): Observable<boolean> {\n    return this.apollo\n      .mutate<StartTupingResponse>({\n        mutation: START_TYPING_MUTATION,\n        variables: { conversationId },\n      })\n      .pipe(\n        map((result) => result.data?.startTyping || false),\n        catchError((error) => {\n          this.logger.error('Error starting typing indicator', error);\n          return of(false);\n        })\n      );\n  }\n\n  /**\n   * Arrête l'indicateur de frappe\n   */\n  stopTyping(conversationId: string): Observable<boolean> {\n    return this.apollo\n      .mutate<StopTypingResponse>({\n        mutation: STOP_TYPING_MUTATION,\n        variables: { conversationId },\n      })\n      .pipe(\n        map((result) => result.data?.stopTyping || false),\n        catchError((error) => {\n          this.logger.error('Error stopping typing indicator', error);\n          return of(false);\n        })\n      );\n  }\n\n  /**\n   * Initie un appel\n   */\n  initiateCall(recipientId: string, callType: CallType): Observable<Call> {\n    return this.apollo\n      .mutate<{ initiateCall: Call }>({\n        mutation: INITIATE_CALL_MUTATION,\n        variables: { recipientId, callType },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.initiateCall) {\n            throw new Error('Failed to initiate call');\n          }\n          return result.data.initiateCall;\n        }),\n        catchError((error) => {\n          this.logger.error('Error initiating call', error);\n          return throwError(() => new Error('Failed to initiate call'));\n        })\n      );\n  }\n\n  /**\n   * Envoie un message avec fichier\n   */\n  sendMessageWithFile(\n    senderId: string,\n    receiverId: string,\n    content: string,\n    file: File\n  ): Observable<Message> {\n    return this.apollo\n      .mutate<SendMessageResponse>({\n        mutation: SEND_MESSAGE_MUTATION,\n        variables: {\n          senderId,\n          receiverId,\n          content,\n          file,\n        },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.sendMessage) {\n            throw new Error('Failed to send message');\n          }\n          return this.normalizeMessage(result.data.sendMessage);\n        }),\n        catchError((error) => {\n          this.logger.error('Error sending message with file', error);\n          return throwError(() => new Error('Failed to send message'));\n        })\n      );\n  }\n\n  /**\n   * Envoie un message simple\n   */\n  sendMessage(\n    senderId: string,\n    receiverId: string,\n    content: string\n  ): Observable<Message> {\n    return this.apollo\n      .mutate<SendMessageResponse>({\n        mutation: SEND_MESSAGE_MUTATION,\n        variables: {\n          senderId,\n          receiverId,\n          content,\n        },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.sendMessage) {\n            throw new Error('Failed to send message');\n          }\n          return this.normalizeMessage(result.data.sendMessage);\n        }),\n        catchError((error) => {\n          this.logger.error('Error sending message', error);\n          return throwError(() => new Error('Failed to send message'));\n        })\n      );\n  }\n\n  /**\n   * S'abonne aux notifications\n   */\n  subscribeToNotifications(): Observable<Notification | null> {\n    return this.apollo\n      .subscribe<{ notificationReceived: Notification }>({\n        query: NOTIFICATION_SUBSCRIPTION,\n      })\n      .pipe(\n        map(({ data }) => {\n          if (!data?.notificationReceived) {\n            return null;\n          }\n          return data.notificationReceived;\n        }),\n        catchError((error) => {\n          this.logger.error('Error in notification subscription', error);\n          return of(null);\n        })\n      );\n  }\n\n  /**\n   * Marque une notification comme lue\n   */\n  markNotificationAsRead(notificationId: string): Observable<boolean> {\n    return this.apollo\n      .mutate<{ markNotificationAsRead: boolean }>({\n        mutation: MARK_NOTIFICATION_READ_MUTATION,\n        variables: { notificationId },\n      })\n      .pipe(\n        map((result) => result.data?.markNotificationAsRead || false),\n        catchError((error) => {\n          this.logger.error('Error marking notification as read', error);\n          return of(false);\n        })\n      );\n  }\n\n  /**\n   * Crée ou récupère une conversation avec un utilisateur\n   */\n  createOrGetConversation(userId: string): Observable<Conversation> {\n    return this.apollo\n      .mutate<{ createConversation: Conversation }>({\n        mutation: CREATE_CONVERSATION_MUTATION,\n        variables: { userId },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.createConversation) {\n            throw new Error('Failed to create or get conversation');\n          }\n          return this.normalizeConversation(result.data.createConversation);\n        }),\n        catchError((error) => {\n          this.logger.error('Error creating or getting conversation', error);\n          return throwError(\n            () => new Error('Failed to create or get conversation')\n          );\n        })\n      );\n  }\n\n  // --------------------------------------------------------------------------\n  // Section: Gestion des sons (intégré depuis SoundService)\n  // --------------------------------------------------------------------------\n\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  private preloadSounds(): void {\n    this.loadSound('ringtone', 'assets/sounds/ringtone.mp3');\n    this.loadSound('call-end', 'assets/sounds/call-end.mp3');\n    this.loadSound('call-connected', 'assets/sounds/call-connected.mp3');\n    this.loadSound('notification', 'assets/sounds/notification.mp3');\n  }\n\n  /**\n   * Charge un fichier audio\n   * @param name Nom du son\n   * @param path Chemin du fichier\n   */\n  private loadSound(name: string, path: string): void {\n    try {\n      const audio = new Audio(path);\n      audio.load();\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n      });\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n\n  /**\n   * Joue un son\n   * @param name Nom du son\n   * @param loop Lecture en boucle\n   */\n  play(name: string, loop: boolean = false): void {\n    if (this.muted) {\n      return;\n    }\n\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        return;\n      }\n\n      sound.loop = loop;\n\n      if (!this.isPlaying[name]) {\n        sound.currentTime = 0;\n        sound.play().catch((error) => {\n          // Handle error silently\n        });\n        this.isPlaying[name] = true;\n      }\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n\n  /**\n   * Arrête un son\n   * @param name Nom du son\n   */\n  stop(name: string): void {\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        return;\n      }\n\n      if (this.isPlaying[name]) {\n        sound.pause();\n        sound.currentTime = 0;\n        this.isPlaying[name] = false;\n      }\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n\n  /**\n   * Arrête tous les sons\n   */\n  stopAllSounds(): void {\n    Object.keys(this.sounds).forEach((name) => {\n      this.stop(name);\n    });\n  }\n\n  /**\n   * Active ou désactive le son\n   * @param muted True pour désactiver le son, false pour l'activer\n   */\n  setMuted(muted: boolean): void {\n    this.muted = muted;\n\n    if (muted) {\n      this.stopAllSounds();\n    }\n  }\n\n  /**\n   * Vérifie si le son est désactivé\n   * @returns True si le son est désactivé, false sinon\n   */\n  isMuted(): boolean {\n    return this.muted;\n  }\n\n  /**\n   * Joue le son de notification\n   */\n  playNotificationSound(): void {\n    console.log('MessageService: Tentative de lecture du son de notification');\n\n    if (this.muted) {\n      console.log('MessageService: Son désactivé, notification ignorée');\n      return;\n    }\n\n    // Créer une mélodie agréable avec l'API Web Audio\n    try {\n      // Créer un contexte audio\n      const audioContext = new (window.AudioContext ||\n        (window as any).webkitAudioContext)();\n\n      // 🎵 TESTEZ DIFFÉRENTS SONS - Décommentez celui que vous voulez tester !\n\n      // SON 1: Mélodie douce (WhatsApp style) - ACTUEL\n      this.playNotificationMelody1(audioContext);\n\n      // SON 2: Mélodie montante (iPhone style) - Décommentez pour tester\n      // this.playNotificationMelody2(audioContext);\n\n      // SON 3: Mélodie descendante (Messenger style) - Décommentez pour tester\n      // this.playNotificationMelody3(audioContext);\n\n      // SON 4: Triple note (Discord style) - Décommentez pour tester\n      // this.playNotificationMelody4(audioContext);\n\n      // SON 5: Cloche douce (Slack style) - Décommentez pour tester\n      // this.playNotificationMelody5(audioContext);\n\n      console.log(\n        'MessageService: Son de notification mélodieux généré avec succès'\n      );\n    } catch (error) {\n      console.error(\n        'MessageService: Erreur lors de la génération du son:',\n        error\n      );\n\n      // Fallback à la méthode originale en cas d'erreur\n      try {\n        const audio = new Audio('assets/sounds/notification.mp3');\n        audio.volume = 0.7; // Volume plus doux\n        audio.play().catch((err) => {\n          console.error(\n            'MessageService: Erreur lors de la lecture du fichier son:',\n            err\n          );\n        });\n      } catch (audioError) {\n        console.error(\n          'MessageService: Exception lors de la lecture du fichier son:',\n          audioError\n        );\n      }\n    }\n  }\n\n  // 🎵 SON 1: Mélodie douce (WhatsApp style)\n  private playNotificationMelody1(audioContext: AudioContext): void {\n    this.playNotificationTone(audioContext, 0, 659.25, 0.15); // E5\n    this.playNotificationTone(audioContext, 0.15, 523.25, 0.15); // C5\n  }\n\n  // 🎵 SON 2: Mélodie montante (iPhone style)\n  private playNotificationMelody2(audioContext: AudioContext): void {\n    this.playNotificationTone(audioContext, 0, 523.25, 0.12); // C5\n    this.playNotificationTone(audioContext, 0.12, 659.25, 0.12); // E5\n    this.playNotificationTone(audioContext, 0.24, 783.99, 0.16); // G5\n  }\n\n  // 🎵 SON 3: Mélodie descendante (Messenger style)\n  private playNotificationMelody3(audioContext: AudioContext): void {\n    this.playNotificationTone(audioContext, 0, 880, 0.1); // A5\n    this.playNotificationTone(audioContext, 0.1, 659.25, 0.1); // E5\n    this.playNotificationTone(audioContext, 0.2, 523.25, 0.15); // C5\n  }\n\n  // 🎵 SON 4: Triple note (Discord style)\n  private playNotificationMelody4(audioContext: AudioContext): void {\n    this.playNotificationTone(audioContext, 0, 698.46, 0.08); // F5\n    this.playNotificationTone(audioContext, 0.08, 698.46, 0.08); // F5\n    this.playNotificationTone(audioContext, 0.16, 880, 0.12); // A5\n  }\n\n  // 🎵 SON 5: Cloche douce (Slack style)\n  private playNotificationMelody5(audioContext: AudioContext): void {\n    this.playBellTone(audioContext, 0, 1046.5, 0.4); // C6 - son de cloche\n  }\n\n  /**\n   * Joue une note individuelle pour la mélodie de notification\n   */\n  private playNotificationTone(\n    audioContext: AudioContext,\n    startTime: number,\n    frequency: number,\n    duration: number\n  ): void {\n    const oscillator = audioContext.createOscillator();\n    const gainNode = audioContext.createGain();\n\n    // Configurer l'oscillateur pour un son plus doux\n    oscillator.type = 'sine';\n    oscillator.frequency.setValueAtTime(\n      frequency,\n      audioContext.currentTime + startTime\n    );\n\n    // Configurer le volume avec une enveloppe douce\n    gainNode.gain.setValueAtTime(0, audioContext.currentTime + startTime);\n    gainNode.gain.linearRampToValueAtTime(\n      0.3,\n      audioContext.currentTime + startTime + 0.02\n    );\n    gainNode.gain.linearRampToValueAtTime(\n      0.2,\n      audioContext.currentTime + startTime + duration * 0.7\n    );\n    gainNode.gain.linearRampToValueAtTime(\n      0,\n      audioContext.currentTime + startTime + duration\n    );\n\n    // Connecter les nœuds\n    oscillator.connect(gainNode);\n    gainNode.connect(audioContext.destination);\n\n    // Démarrer et arrêter l'oscillateur\n    oscillator.start(audioContext.currentTime + startTime);\n    oscillator.stop(audioContext.currentTime + startTime + duration);\n  }\n\n  /**\n   * Joue un son de cloche pour les notifications\n   */\n  private playBellTone(\n    audioContext: AudioContext,\n    startTime: number,\n    frequency: number,\n    duration: number\n  ): void {\n    const oscillator = audioContext.createOscillator();\n    const gainNode = audioContext.createGain();\n\n    // Configurer l'oscillateur pour un son de cloche\n    oscillator.type = 'triangle'; // Son plus doux que sine\n    oscillator.frequency.setValueAtTime(\n      frequency,\n      audioContext.currentTime + startTime\n    );\n\n    // Enveloppe de cloche (attaque rapide, déclin lent)\n    gainNode.gain.setValueAtTime(0, audioContext.currentTime + startTime);\n    gainNode.gain.linearRampToValueAtTime(\n      0.4,\n      audioContext.currentTime + startTime + 0.01\n    );\n    gainNode.gain.exponentialRampToValueAtTime(\n      0.01,\n      audioContext.currentTime + startTime + duration\n    );\n\n    // Connecter les nœuds\n    oscillator.connect(gainNode);\n    gainNode.connect(audioContext.destination);\n\n    // Démarrer et arrêter l'oscillateur\n    oscillator.start(audioContext.currentTime + startTime);\n    oscillator.stop(audioContext.currentTime + startTime + duration);\n  }\n  // --------------------------------------------------------------------------\n  // Section 1: Méthodes pour les Messages\n  // --------------------------------------------------------------------------\n\n  /**\n   * Joue un fichier audio\n   * @param audioUrl URL du fichier audio à jouer\n   * @returns Promise qui se résout lorsque la lecture est terminée\n   */\n  playAudio(audioUrl: string): Promise<void> {\n    return new Promise((resolve, reject) => {\n      const audio = new Audio(audioUrl);\n\n      audio.onended = () => {\n        resolve();\n      };\n\n      audio.onerror = (error) => {\n        this.logger.error(`[MessageService] Error playing audio:`, error);\n        reject(error);\n      };\n\n      audio.play().catch((error) => {\n        this.logger.error(`[MessageService] Error playing audio:`, error);\n        reject(error);\n      });\n    });\n  }\n\n  /**\n   * Récupère tous les messages vocaux de l'utilisateur\n   * @returns Observable avec la liste des messages vocaux\n   */\n  getVoiceMessages(): Observable<Call[]> {\n    this.logger.debug('[MessageService] Getting voice messages');\n\n    return this.apollo\n      .watchQuery<{ getVoiceMessages: Call[] }>({\n        query: GET_VOICE_MESSAGES_QUERY,\n        fetchPolicy: 'network-only', // Ne pas utiliser le cache pour cette requête\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const voiceMessages = result.data?.getVoiceMessages || [];\n          this.logger.debug(\n            `[MessageService] Retrieved ${voiceMessages.length} voice messages`\n          );\n          return voiceMessages;\n        }),\n        catchError((error) => {\n          this.logger.error(\n            '[MessageService] Error fetching voice messages:',\n            error\n          );\n          return throwError(() => new Error('Failed to fetch voice messages'));\n        })\n      );\n  }\n  // Message methods\n  getMessages(\n    senderId: string,\n    receiverId: string,\n    conversationId: string,\n    page: number = 1,\n    limit: number = 25 // ✅ Increased batch size for better performance\n  ): Observable<Message[]> {\n    return this.apollo\n      .watchQuery<{ getMessages: Message[] }>({\n        query: GET_MESSAGES_QUERY,\n        variables: { senderId, receiverId, conversationId, limit, page },\n        fetchPolicy: 'cache-first', // ✅ Use cache for better performance\n        errorPolicy: 'all', // ✅ Handle partial errors gracefully\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const messages = result.data?.getMessages || [];\n          // ✅ Batch normalize messages for better performance\n          return this.batchNormalizeMessages(messages);\n        }),\n        catchError((error) => {\n          console.error('Error fetching messages:', error);\n          return throwError(() => new Error('Failed to fetch messages'));\n        })\n      );\n  }\n  editMessage(messageId: string, newContent: string): Observable<Message> {\n    return this.apollo\n      .mutate<{ editMessage: Message }>({\n        mutation: EDIT_MESSAGE_MUTATION,\n        variables: { messageId, newContent },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.editMessage) {\n            throw new Error('Failed to edit message');\n          }\n          return this.normalizeMessage(result.data.editMessage);\n        }),\n        catchError((error) => {\n          this.logger.error('Error editing message:', error);\n          return throwError(() => new Error('Failed to edit message'));\n        })\n      );\n  }\n\n  deleteMessage(messageId: string): Observable<Message> {\n    return this.apollo\n      .mutate<{ deleteMessage: Message }>({\n        mutation: DELETE_MESSAGE_MUTATION,\n        variables: { messageId },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.deleteMessage) {\n            throw new Error('Failed to delete message');\n          }\n          return this.normalizeMessage(result.data.deleteMessage);\n        }),\n        catchError((error) => {\n          this.logger.error('Error deleting message:', error);\n          return throwError(() => new Error('Failed to delete message'));\n        })\n      );\n  }\n\n  markMessageAsRead(messageId: string): Observable<Message> {\n    return this.apollo\n      .mutate<MarkAsReadResponse>({\n        mutation: MARK_AS_READ_MUTATION,\n        variables: { messageId },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.markMessageAsRead)\n            throw new Error('Failed to mark message as read');\n          return {\n            ...result.data.markMessageAsRead,\n            readAt: new Date(),\n          };\n        }),\n        catchError((error) => {\n          console.error('Error marking message as read:', error);\n          return throwError(() => new Error('Failed to mark message as read'));\n        })\n      );\n  }\n\n  reactToMessage(messageId: string, emoji: string): Observable<Message> {\n    return this.apollo\n      .mutate<ReactToMessageResponse>({\n        mutation: REACT_TO_MESSAGE_MUTATION,\n        variables: { messageId, emoji },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.reactToMessage)\n            throw new Error('Failed to react to message');\n          return result.data.reactToMessage;\n        }),\n        catchError((error) => {\n          console.error('Error reacting to message:', error);\n          return throwError(() => new Error('Failed to react to message'));\n        })\n      );\n  }\n\n  forwardMessage(\n    messageId: string,\n    conversationIds: string[]\n  ): Observable<Message[]> {\n    return this.apollo\n      .mutate<ForwardMessageResponse>({\n        mutation: FORWARD_MESSAGE_MUTATION,\n        variables: { messageId, conversationIds },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.forwardMessage)\n            throw new Error('Failed to forward message');\n          return result.data.forwardMessage.map((msg) => ({\n            ...msg,\n            timestamp: msg.timestamp\n              ? this.normalizeDate(msg.timestamp)\n              : new Date(),\n          }));\n        }),\n        catchError((error) => {\n          console.error('Error forwarding message:', error);\n          return throwError(() => new Error('Failed to forward message'));\n        })\n      );\n  }\n\n  pinMessage(messageId: string, conversationId: string): Observable<Message> {\n    return this.apollo\n      .mutate<PinMessageResponse>({\n        mutation: PIN_MESSAGE_MUTATION,\n        variables: { messageId, conversationId },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.pinMessage)\n            throw new Error('Failed to pin message');\n          return {\n            ...result.data.pinMessage,\n            pinnedAt: new Date(),\n          };\n        }),\n        catchError((error) => {\n          console.error('Error pinning message:', error);\n          return throwError(() => new Error('Failed to pin message'));\n        })\n      );\n  }\n\n  searchMessages(\n    query: string,\n    conversationId?: string,\n    filters: MessageFilter = {}\n  ): Observable<Message[]> {\n    return this.apollo\n      .watchQuery<SearchMessagesResponse>({\n        query: SEARCH_MESSAGES_QUERY,\n        variables: {\n          query,\n          conversationId,\n          ...filters,\n          dateFrom: this.toSafeISOString(filters.dateFrom),\n          dateTo: this.toSafeISOString(filters.dateTo),\n        },\n        fetchPolicy: 'cache-first', // ✅ Use cache for better performance\n        errorPolicy: 'all',\n      })\n      .valueChanges.pipe(\n        map(\n          (result) =>\n            result.data?.searchMessages?.map((msg) => ({\n              ...msg,\n              timestamp: this.safeDate(msg.timestamp),\n              sender: this.normalizeUser(msg.sender),\n            })) || []\n        ),\n        catchError((error) => {\n          console.error('Error searching messages:', error);\n          return throwError(() => new Error('Failed to search messages'));\n        })\n      );\n  }\n\n  // ✅ Batch normalization for better performance\n  private batchNormalizeMessages(messages: any[]): Message[] {\n    if (!messages || messages.length === 0) return [];\n\n    return messages.map((msg) => {\n      try {\n        return this.normalizeMessage(msg);\n      } catch (error) {\n        console.error('Error normalizing message:', error);\n        // Return minimal valid message on error\n        return {\n          id: msg.id || msg._id || `temp-${Date.now()}`,\n          content: msg.content || '',\n          type: msg.type || MessageType.TEXT,\n          timestamp: this.safeDate(msg.timestamp),\n          isRead: false,\n          sender: msg.sender\n            ? this.normalizeUser(msg.sender)\n            : {\n                id: this.getCurrentUserId(),\n                username: 'Unknown',\n              },\n        } as Message;\n      }\n    });\n  }\n\n  getUnreadMessages(userId: string): Observable<Message[]> {\n    return this.apollo\n      .watchQuery<GetUnreadMessagesResponse>({\n        query: GET_UNREAD_MESSAGES_QUERY,\n        variables: { userId },\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map(\n          (result) =>\n            result.data?.getUnreadMessages?.map((msg) => ({\n              ...msg,\n              timestamp: this.safeDate(msg.timestamp),\n              sender: this.normalizeUser(msg.sender),\n            })) || []\n        ),\n        catchError((error) => {\n          console.error('Error fetching unread messages:', error);\n          return throwError(() => new Error('Failed to fetch unread messages'));\n        })\n      );\n  }\n\n  setActiveConversation(conversationId: string): void {\n    this.activeConversation.next(conversationId);\n  }\n\n  getConversations(): Observable<Conversation[]> {\n    return this.apollo\n      .watchQuery<GetConversationsResponse>({\n        query: GET_CONVERSATIONS_QUERY,\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const conversations = result.data?.getConversations || [];\n          return conversations.map((conv) => this.normalizeConversation(conv));\n        }),\n        catchError((error) => {\n          console.error('Error fetching conversations:', error);\n          return throwError(() => new Error('Failed to load conversations'));\n        })\n      );\n  }\n\n  getConversation(\n    conversationId: string,\n    limit?: number,\n    page?: number\n  ): Observable<Conversation> {\n    this.logger.info(\n      `[MessageService] Getting conversation: ${conversationId}, limit: ${limit}, page: ${page}`\n    );\n\n    const variables: any = { conversationId };\n\n    // Ajouter les paramètres de pagination s'ils sont fournis\n    if (limit !== undefined) {\n      variables.limit = limit;\n    } else {\n      variables.limit = 10; // Valeur par défaut\n    }\n\n    // Calculer l'offset à partir de la page si elle est fournie\n    if (page !== undefined) {\n      // La requête GraphQL utilise offset, donc nous devons convertir la page en offset\n      const offset = (page - 1) * variables.limit;\n      variables.offset = offset;\n      this.logger.debug(\n        `[MessageService] Calculated offset: ${offset} from page: ${page} and limit: ${variables.limit}`\n      );\n    } else {\n      variables.offset = 0; // Valeur par défaut\n    }\n\n    this.logger.debug(\n      `[MessageService] Final pagination parameters: limit=${variables.limit}, offset=${variables.offset}`\n    );\n\n    return this.apollo\n      .watchQuery<GetConversationResponse>({\n        query: GET_CONVERSATION_QUERY,\n        variables: variables,\n        fetchPolicy: 'network-only',\n        errorPolicy: 'all',\n      })\n      .valueChanges.pipe(\n        retry(2), // Réessayer 2 fois en cas d'erreur\n        map((result) => {\n          this.logger.debug(\n            `[MessageService] Conversation response received:`,\n            result\n          );\n\n          const conv = result.data?.getConversation;\n          if (!conv) {\n            this.logger.error(\n              `[MessageService] Conversation not found: ${conversationId}`\n            );\n            throw new Error('Conversation not found');\n          }\n\n          this.logger.debug(\n            `[MessageService] Normalizing conversation: ${conversationId}`\n          );\n          const normalizedConversation = this.normalizeConversation(conv);\n\n          this.logger.info(\n            `[MessageService] Conversation loaded successfully: ${conversationId}, participants: ${\n              normalizedConversation.participants?.length || 0\n            }, messages: ${normalizedConversation.messages?.length || 0}`\n          );\n          return normalizedConversation;\n        }),\n        catchError((error) => {\n          this.logger.error(\n            `[MessageService] Error fetching conversation:`,\n            error\n          );\n          return throwError(() => new Error('Failed to load conversation'));\n        })\n      );\n  }\n\n  createConversation(userId: string): Observable<Conversation> {\n    this.logger.info(\n      `[MessageService] Creating conversation with user: ${userId}`\n    );\n\n    if (!userId) {\n      this.logger.error(\n        `[MessageService] Cannot create conversation: userId is undefined`\n      );\n      return throwError(\n        () => new Error('User ID is required to create a conversation')\n      );\n    }\n\n    return this.apollo\n      .mutate<{ createConversation: Conversation }>({\n        mutation: CREATE_CONVERSATION_MUTATION,\n        variables: { userId },\n      })\n      .pipe(\n        map((result) => {\n          this.logger.debug(\n            `[MessageService] Conversation creation response:`,\n            result\n          );\n\n          const conversation = result.data?.createConversation;\n          if (!conversation) {\n            this.logger.error(\n              `[MessageService] Failed to create conversation with user: ${userId}`\n            );\n            throw new Error('Failed to create conversation');\n          }\n\n          try {\n            const normalizedConversation =\n              this.normalizeConversation(conversation);\n            this.logger.info(\n              `[MessageService] Conversation created successfully: ${normalizedConversation.id}`\n            );\n            return normalizedConversation;\n          } catch (error) {\n            this.logger.error(\n              `[MessageService] Error normalizing created conversation:`,\n              error\n            );\n            throw new Error('Error processing created conversation');\n          }\n        }),\n        catchError((error) => {\n          this.logger.error(\n            `[MessageService] Error creating conversation with user ${userId}:`,\n            error\n          );\n          return throwError(\n            () => new Error(`Failed to create conversation: ${error.message}`)\n          );\n        })\n      );\n  }\n\n  /**\n   * Récupère une conversation existante ou en crée une nouvelle si elle n'existe pas\n   * @param userId ID de l'utilisateur avec qui créer/récupérer une conversation\n   * @returns Observable avec la conversation\n   */\n  getOrCreateConversation(userId: string): Observable<Conversation> {\n    this.logger.info(\n      `[MessageService] Getting or creating conversation with user: ${userId}`\n    );\n\n    if (!userId) {\n      this.logger.error(\n        `[MessageService] Cannot get/create conversation: userId is undefined`\n      );\n      return throwError(\n        () => new Error('User ID is required to get/create a conversation')\n      );\n    }\n\n    // D'abord, essayons de trouver une conversation existante entre les deux utilisateurs\n    return this.getConversations().pipe(\n      map((conversations) => {\n        // Récupérer l'ID de l'utilisateur actuel\n        const currentUserId = this.getCurrentUserId();\n\n        // Chercher une conversation directe (non groupe) entre les deux utilisateurs\n        const existingConversation = conversations.find((conv) => {\n          if (conv.isGroup) return false;\n\n          // Vérifier si la conversation contient les deux utilisateurs\n          const participantIds =\n            conv.participants?.map((p) => p.id || p._id) || [];\n          return (\n            participantIds.includes(userId) &&\n            participantIds.includes(currentUserId)\n          );\n        });\n\n        if (existingConversation) {\n          this.logger.info(\n            `[MessageService] Found existing conversation: ${existingConversation.id}`\n          );\n          return existingConversation;\n        }\n\n        // Si aucune conversation n'est trouvée, en créer une nouvelle\n        throw new Error('No existing conversation found');\n      }),\n      catchError((error) => {\n        this.logger.info(\n          `[MessageService] No existing conversation found, creating new one: ${error.message}`\n        );\n        return this.createConversation(userId);\n      })\n    );\n  }\n\n  // --------------------------------------------------------------------------\n  // Section 2: Méthodes pour les Notifications\n  // --------------------------------------------------------------------------\n  // Propriétés pour la pagination des notifications\n  private notificationPagination = {\n    currentPage: 1,\n    limit: 10,\n    hasMoreNotifications: true,\n  };\n\n  getNotifications(\n    refresh = false,\n    page = 1,\n    limit = 10\n  ): Observable<Notification[]> {\n    this.logger.info(\n      'MessageService',\n      `Fetching notifications, refresh: ${refresh}, page: ${page}, limit: ${limit}`\n    );\n    this.logger.debug('MessageService', 'Using query', {\n      query: GET_NOTIFICATIONS_QUERY,\n    });\n\n    // Si refresh est true, réinitialiser la pagination mais ne pas vider le cache\n    // pour conserver les suppressions locales\n    if (refresh) {\n      this.logger.debug(\n        'MessageService',\n        'Resetting pagination due to refresh'\n      );\n      this.notificationPagination.currentPage = 1;\n      this.notificationPagination.hasMoreNotifications = true;\n    }\n\n    // Mettre à jour les paramètres de pagination\n    this.notificationPagination.currentPage = page;\n    this.notificationPagination.limit = limit;\n\n    // Récupérer les IDs des notifications supprimées du localStorage\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n    this.logger.debug(\n      'MessageService',\n      `Found ${deletedNotificationIds.size} deleted notification IDs in localStorage`\n    );\n\n    return this.apollo\n      .watchQuery<getUserNotificationsResponse>({\n        query: GET_NOTIFICATIONS_QUERY,\n        variables: {\n          page: page,\n          limit: limit,\n        },\n        fetchPolicy: refresh ? 'network-only' : 'cache-first',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          this.logger.debug(\n            'MessageService',\n            'Notifications response received'\n          );\n\n          if (result.errors) {\n            this.logger.error(\n              'MessageService',\n              'GraphQL errors:',\n              result.errors\n            );\n            throw new Error(result.errors.map((e) => e.message).join(', '));\n          }\n\n          const notifications = result.data?.getUserNotifications || [];\n          this.logger.debug(\n            'MessageService',\n            `Received ${notifications.length} notifications from server for page ${page}`\n          );\n\n          // Vérifier s'il y a plus de notifications à charger\n          this.notificationPagination.hasMoreNotifications =\n            notifications.length >= limit;\n\n          if (notifications.length === 0) {\n            this.logger.info(\n              'MessageService',\n              'No notifications received from server'\n            );\n            this.notificationPagination.hasMoreNotifications = false;\n          }\n\n          // Filtrer les notifications supprimées\n          const filteredNotifications = notifications.filter(\n            (notif) => !deletedNotificationIds.has(notif.id)\n          );\n\n          this.logger.debug(\n            'MessageService',\n            `Filtered out ${\n              notifications.length - filteredNotifications.length\n            } deleted notifications`\n          );\n\n          // Afficher les notifications reçues pour le débogage\n          filteredNotifications.forEach((notif, index) => {\n            console.log(`Notification ${index + 1} (page ${page}):`, {\n              id: notif.id || (notif as any)._id,\n              type: notif.type,\n              content: notif.content,\n              isRead: notif.isRead,\n            });\n          });\n\n          // Vérifier si les notifications existent déjà dans le cache avant de les ajouter\n          // Mettre à jour le cache avec les nouvelles notifications\n          this.updateCache(filteredNotifications);\n\n          // Récupérer toutes les notifications du cache et les TRIER\n          const cachedNotifications = Array.from(\n            this.notificationCache.values()\n          );\n\n          // 🚀 TRI OPTIMISÉ: Les notifications les plus récentes en premier\n          const sortedNotifications =\n            this.sortNotificationsByDate(cachedNotifications);\n\n          console.log(\n            `📊 SORTED: ${sortedNotifications.length} notifications triées (plus récentes en premier)`\n          );\n\n          // Mettre à jour le BehaviorSubject avec les notifications TRIÉES\n          this.notifications.next(sortedNotifications);\n\n          // Mettre à jour le compteur de notifications non lues\n          this.updateUnreadCount();\n\n          // Sauvegarder les notifications dans le localStorage\n          this.saveNotificationsToLocalStorage();\n\n          return cachedNotifications;\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error loading notifications:',\n            error\n          );\n\n          if (error.graphQLErrors) {\n            this.logger.error(\n              'MessageService',\n              'GraphQL errors:',\n              error.graphQLErrors\n            );\n          }\n\n          if (error.networkError) {\n            this.logger.error(\n              'MessageService',\n              'Network error:',\n              error.networkError\n            );\n          }\n\n          return throwError(() => new Error('Failed to load notifications'));\n        })\n      );\n  }\n\n  /**\n   * Récupère les IDs des notifications supprimées du localStorage\n   * @private\n   * @returns Set contenant les IDs des notifications supprimées\n   */\n  private getDeletedNotificationIds(): Set<string> {\n    try {\n      const deletedIds = new Set<string>();\n      const savedNotifications = localStorage.getItem('notifications');\n\n      // Si aucune notification n'est sauvegardée, retourner un ensemble vide\n      if (!savedNotifications) {\n        return deletedIds;\n      }\n\n      // Récupérer les IDs des notifications sauvegardées\n      const savedNotificationIds = new Set(\n        JSON.parse(savedNotifications).map((n: Notification) => n.id)\n      );\n\n      // Récupérer les notifications du serveur (si disponibles dans le cache Apollo)\n      const serverNotifications =\n        this.apollo.client.readQuery<getUserNotificationsResponse>({\n          query: GET_NOTIFICATIONS_QUERY,\n        })?.getUserNotifications || [];\n\n      // Pour chaque notification du serveur, vérifier si elle est dans les notifications sauvegardées\n      serverNotifications.forEach((notification) => {\n        if (!savedNotificationIds.has(notification.id)) {\n          deletedIds.add(notification.id);\n        }\n      });\n\n      return deletedIds;\n    } catch (error) {\n      this.logger.error(\n        'MessageService',\n        'Erreur lors de la récupération des IDs de notifications supprimées:',\n        error\n      );\n      return new Set<string>();\n    }\n  }\n\n  // Méthode pour vérifier s'il y a plus de notifications à charger\n  hasMoreNotifications(): boolean {\n    return this.notificationPagination.hasMoreNotifications;\n  }\n\n  // Méthode pour charger la page suivante de notifications\n  loadMoreNotifications(): Observable<Notification[]> {\n    const nextPage = this.notificationPagination.currentPage + 1;\n    return this.getNotifications(\n      false,\n      nextPage,\n      this.notificationPagination.limit\n    );\n  }\n  getNotificationById(id: string): Observable<Notification | undefined> {\n    return this.notifications$.pipe(\n      map((notifications) => notifications.find((n) => n.id === id)),\n      catchError((error) => {\n        this.logger.error('Error finding notification:', error);\n        return throwError(() => new Error('Failed to find notification'));\n      })\n    );\n  }\n  getNotificationCount(): number {\n    return this.notifications.value?.length || 0;\n  }\n  getNotificationAttachments(notificationId: string): Observable<Attachment[]> {\n    return this.apollo\n      .query<getNotificationAttachmentsEvent>({\n        query: GET_NOTIFICATIONS_ATTACHAMENTS,\n        variables: { id: notificationId },\n        fetchPolicy: 'network-only',\n      })\n      .pipe(\n        map((result) => result.data?.getNotificationAttachments || []),\n        catchError((error) => {\n          this.logger.error('Error fetching notification attachments:', error);\n          return throwError(() => new Error('Failed to fetch attachments'));\n        })\n      );\n  }\n  getUnreadNotifications(): Observable<Notification[]> {\n    return this.notifications$.pipe(\n      map((notifications) => notifications.filter((n) => !n.isRead))\n    );\n  }\n\n  /**\n   * Supprime une notification\n   * @param notificationId ID de la notification à supprimer\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteNotification(\n    notificationId: string\n  ): Observable<{ success: boolean; message: string }> {\n    this.logger.debug(\n      'MessageService',\n      `Suppression de la notification ${notificationId}`\n    );\n\n    if (!notificationId) {\n      this.logger.warn('MessageService', 'ID de notification invalide');\n      return throwError(() => new Error('ID de notification invalide'));\n    }\n\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const removedCount = this.removeNotificationsFromCache([notificationId]);\n\n    // Appeler le backend pour supprimer la notification\n    return this.apollo\n      .mutate<{ deleteNotification: { success: boolean; message: string } }>({\n        mutation: DELETE_NOTIFICATION_MUTATION,\n        variables: { notificationId },\n      })\n      .pipe(\n        map((result) => {\n          const response = result.data?.deleteNotification;\n          if (!response) {\n            throw new Error('Réponse de suppression invalide');\n          }\n\n          this.logger.debug(\n            'MessageService',\n            'Résultat de la suppression:',\n            response\n          );\n\n          return response;\n        }),\n        catchError((error) =>\n          this.handleDeletionError(error, 'la suppression de la notification', {\n            success: true,\n            message: 'Notification supprimée localement (erreur serveur)',\n          })\n        )\n      );\n  }\n\n  /**\n   * Sauvegarde les notifications dans le localStorage\n   * @private\n   */\n  private saveNotificationsToLocalStorage(): void {\n    try {\n      const notifications = Array.from(this.notificationCache.values());\n      localStorage.setItem('notifications', JSON.stringify(notifications));\n      this.logger.debug(\n        'MessageService',\n        'Notifications sauvegardées localement'\n      );\n    } catch (error) {\n      this.logger.error(\n        'MessageService',\n        'Erreur lors de la sauvegarde des notifications:',\n        error\n      );\n    }\n  }\n\n  /**\n   * Supprime toutes les notifications de l'utilisateur\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteAllNotifications(): Observable<{\n    success: boolean;\n    count: number;\n    message: string;\n  }> {\n    this.logger.debug(\n      'MessageService',\n      'Suppression de toutes les notifications'\n    );\n\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const count = this.notificationCache.size;\n    const allNotificationIds = Array.from(this.notificationCache.keys());\n    this.removeNotificationsFromCache(allNotificationIds);\n\n    // Appeler le backend pour supprimer toutes les notifications\n    return this.apollo\n      .mutate<{\n        deleteAllNotifications: {\n          success: boolean;\n          count: number;\n          message: string;\n        };\n      }>({\n        mutation: DELETE_ALL_NOTIFICATIONS_MUTATION,\n      })\n      .pipe(\n        map((result) => {\n          const response = result.data?.deleteAllNotifications;\n          if (!response) {\n            throw new Error('Réponse de suppression invalide');\n          }\n\n          this.logger.debug(\n            'MessageService',\n            'Résultat de la suppression de toutes les notifications:',\n            response\n          );\n\n          return response;\n        }),\n        catchError((error) =>\n          this.handleDeletionError(\n            error,\n            'la suppression de toutes les notifications',\n            {\n              success: true,\n              count,\n              message: `${count} notifications supprimées localement (erreur serveur)`,\n            }\n          )\n        )\n      );\n  }\n\n  /**\n   * Supprime plusieurs notifications\n   * @param notificationIds IDs des notifications à supprimer\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteMultipleNotifications(\n    notificationIds: string[]\n  ): Observable<{ success: boolean; count: number; message: string }> {\n    this.logger.debug(\n      'MessageService',\n      `Suppression de ${notificationIds.length} notifications`\n    );\n\n    if (!notificationIds || notificationIds.length === 0) {\n      this.logger.warn('MessageService', 'Aucun ID de notification fourni');\n      return throwError(() => new Error('Aucun ID de notification fourni'));\n    }\n\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const count = this.removeNotificationsFromCache(notificationIds);\n\n    // Appeler le backend pour supprimer les notifications\n    return this.apollo\n      .mutate<{\n        deleteMultipleNotifications: {\n          success: boolean;\n          count: number;\n          message: string;\n        };\n      }>({\n        mutation: DELETE_MULTIPLE_NOTIFICATIONS_MUTATION,\n        variables: { notificationIds },\n      })\n      .pipe(\n        map((result) => {\n          const response = result.data?.deleteMultipleNotifications;\n          if (!response) {\n            throw new Error('Réponse de suppression invalide');\n          }\n\n          this.logger.debug(\n            'MessageService',\n            'Résultat de la suppression multiple:',\n            response\n          );\n\n          return response;\n        }),\n        catchError((error) =>\n          this.handleDeletionError(\n            error,\n            'la suppression multiple de notifications',\n            {\n              success: count > 0,\n              count,\n              message: `${count} notifications supprimées localement (erreur serveur)`,\n            }\n          )\n        )\n      );\n  }\n  groupNotificationsByType(): Observable<\n    Map<NotificationType, Notification[]>\n  > {\n    return this.notifications$.pipe(\n      map((notifications) => {\n        const groups = new Map<NotificationType, Notification[]>();\n        notifications.forEach((notif) => {\n          if (!groups.has(notif.type)) {\n            groups.set(notif.type, []);\n          }\n          groups.get(notif.type)?.push(notif);\n        });\n        return groups;\n      })\n    );\n  }\n  markAsRead(notificationIds: string[]): Observable<{\n    success: boolean;\n    readCount: number;\n    remainingCount: number;\n  }> {\n    this.logger.debug(\n      'MessageService',\n      `Marking notifications as read: ${notificationIds?.join(', ') || 'none'}`\n    );\n\n    if (!notificationIds || notificationIds.length === 0) {\n      this.logger.warn('MessageService', 'No notification IDs provided');\n      return of({\n        success: false,\n        readCount: 0,\n        remainingCount: this.notificationCount.value,\n      });\n    }\n\n    // Vérifier que tous les IDs sont valides\n    const validIds = notificationIds.filter(\n      (id) => id && typeof id === 'string' && id.trim() !== ''\n    );\n\n    if (validIds.length !== notificationIds.length) {\n      this.logger.error('MessageService', 'Some notification IDs are invalid', {\n        provided: notificationIds,\n        valid: validIds,\n      });\n      return throwError(() => new Error('Some notification IDs are invalid'));\n    }\n\n    this.logger.debug(\n      'MessageService',\n      'Sending mutation to mark notifications as read',\n      validIds\n    );\n\n    // Mettre à jour localement d'abord pour une meilleure expérience utilisateur\n    this.updateNotificationStatus(validIds, true);\n\n    // Créer une réponse optimiste\n    const optimisticResponse = {\n      markNotificationsAsRead: {\n        success: true,\n        readCount: validIds.length,\n        remainingCount: Math.max(\n          0,\n          this.notificationCount.value - validIds.length\n        ),\n      },\n    };\n\n    // Afficher des informations de débogage supplémentaires\n    console.log('Sending markNotificationsAsRead mutation with variables:', {\n      notificationIds: validIds,\n    });\n    console.log('Using mutation:', MARK_NOTIFICATION_READ_MUTATION);\n\n    return this.apollo\n      .mutate<MarkNotificationsAsReadResponse>({\n        mutation: MARK_NOTIFICATION_READ_MUTATION,\n        variables: { notificationIds: validIds },\n        optimisticResponse: optimisticResponse,\n        errorPolicy: 'all', // Continuer même en cas d'erreur\n        fetchPolicy: 'no-cache', // Ne pas utiliser le cache pour cette mutation\n      })\n      .pipe(\n        map((result) => {\n          this.logger.debug('MessageService', 'Mutation result', result);\n          console.log('Mutation result:', result);\n\n          // Si nous avons des erreurs GraphQL, les logger mais continuer\n          if (result.errors) {\n            this.logger.error(\n              'MessageService',\n              'GraphQL errors:',\n              result.errors\n            );\n            console.error('GraphQL errors:', result.errors);\n          }\n\n          // Utiliser la réponse du serveur ou notre réponse optimiste\n          const response =\n            result.data?.markNotificationsAsRead ??\n            optimisticResponse.markNotificationsAsRead;\n\n          return response;\n        }),\n        catchError((error: Error) => {\n          this.logger.error(\n            'MessageService',\n            'Error marking notifications as read:',\n            error\n          );\n          console.error('Error in markAsRead:', error);\n\n          // En cas d'erreur, retourner quand même un succès simulé\n          // puisque nous avons déjà mis à jour l'interface utilisateur\n          return of({\n            success: true,\n            readCount: validIds.length,\n            remainingCount: Math.max(\n              0,\n              this.notificationCount.value - validIds.length\n            ),\n          });\n        })\n      );\n  }\n  // --------------------------------------------------------------------------\n  // Section 3: Méthodes pour les Appels (SUPPRIMÉES - VOIR SECTION À LA FIN)\n  // --------------------------------------------------------------------------\n\n  /**\n   * S'abonne aux signaux d'appel\n   * @param callId ID de l'appel\n   * @returns Observable avec les signaux d'appel\n   */\n  subscribeToCallSignals(callId: string): Observable<CallSignal> {\n    return this.apollo\n      .subscribe<{ callSignal: CallSignal }>({\n        query: CALL_SIGNAL_SUBSCRIPTION,\n        variables: { callId },\n      })\n      .pipe(\n        map(({ data }) => {\n          if (!data?.callSignal) {\n            throw new Error('No call signal received');\n          }\n          return data.callSignal;\n        }),\n        tap((signal) => {\n          this.callSignals.next(signal);\n          this.handleCallSignal(signal);\n        }),\n        catchError((error) => {\n          this.logger.error('Error in call signal subscription', error);\n          return throwError(() => new Error('Call signal subscription failed'));\n        })\n      );\n  }\n\n  /**\n   * Envoie un signal d'appel\n   * @param callId ID de l'appel\n   * @param signalType Type de signal\n   * @param signalData Données du signal\n   * @returns Observable avec le résultat de l'opération\n   */\n  sendCallSignal(\n    callId: string,\n    signalType: string,\n    signalData: string\n  ): Observable<CallSuccess> {\n    return this.apollo\n      .mutate<{ sendCallSignal: CallSuccess }>({\n        mutation: SEND_CALL_SIGNAL_MUTATION,\n        variables: {\n          callId,\n          signalType,\n          signalData,\n        },\n      })\n      .pipe(\n        map((result) => {\n          const success = result.data?.sendCallSignal;\n          if (!success) {\n            throw new Error('Failed to send call signal');\n          }\n          return success;\n        }),\n        catchError((error) => {\n          this.logger.error('Error sending call signal', error);\n          return throwError(() => new Error('Failed to send call signal'));\n        })\n      );\n  }\n\n  /**\n   * Récupère l'historique des appels avec filtres\n   * @param limit Nombre d'appels à récupérer\n   * @param offset Décalage pour la pagination\n   * @param status Filtres de statut\n   * @param type Filtres de type\n   * @param startDate Date de début\n   * @param endDate Date de fin\n   * @returns Observable avec l'historique des appels\n   */\n  getCallHistory(\n    limit: number = 20,\n    offset: number = 0,\n    status?: string[],\n    type?: string[],\n    startDate?: string | null,\n    endDate?: string | null\n  ): Observable<Call[]> {\n    return this.apollo\n      .watchQuery<{ callHistory: Call[] }>({\n        query: CALL_HISTORY_QUERY,\n        variables: {\n          limit,\n          offset,\n          status,\n          type,\n          startDate,\n          endDate,\n        },\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const history = result.data?.callHistory || [];\n          this.logger.debug(`Retrieved ${history.length} call history items`);\n          return history;\n        }),\n        catchError((error) => {\n          this.logger.error('Error fetching call history:', error);\n          return throwError(() => new Error('Failed to fetch call history'));\n        })\n      );\n  }\n\n  /**\n   * Récupère les détails d'un appel spécifique\n   * @param callId ID de l'appel\n   * @returns Observable avec les détails de l'appel\n   */\n  getCallDetails(callId: string): Observable<Call> {\n    return this.apollo\n      .watchQuery<{ callDetails: Call }>({\n        query: CALL_DETAILS_QUERY,\n        variables: { callId },\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const details = result.data?.callDetails;\n          if (!details) {\n            throw new Error('Call details not found');\n          }\n          this.logger.debug(`Retrieved call details for: ${callId}`);\n          return details;\n        }),\n        catchError((error) => {\n          this.logger.error('Error fetching call details:', error);\n          return throwError(() => new Error('Failed to fetch call details'));\n        })\n      );\n  }\n\n  /**\n   * Récupère les statistiques d'appels\n   * @returns Observable avec les statistiques d'appels\n   */\n  getCallStats(): Observable<any> {\n    return this.apollo\n      .watchQuery<{ callStats: any }>({\n        query: CALL_STATS_QUERY,\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const stats = result.data?.callStats;\n          if (!stats) {\n            throw new Error('Call stats not found');\n          }\n          this.logger.debug('Retrieved call stats:', stats);\n          return stats;\n        }),\n        catchError((error) => {\n          this.logger.error('Error fetching call stats:', error);\n          return throwError(() => new Error('Failed to fetch call stats'));\n        })\n      );\n  }\n\n  /**\n   * Gère un signal d'appel reçu\n   * @param signal Signal d'appel\n   */\n  private handleCallSignal(signal: CallSignal): void {\n    switch (signal.type) {\n      case 'ice-candidate':\n        this.handleIceCandidate(signal);\n        break;\n      case 'answer':\n        this.handleAnswer(signal);\n        break;\n      case 'end-call':\n        this.handleEndCall(signal);\n        break;\n      case 'reject':\n        this.handleRejectCall(signal);\n        break;\n      default:\n        this.logger.debug(`Unhandled signal type: ${signal.type}`, signal);\n    }\n  }\n\n  /**\n   * Gère un candidat ICE reçu\n   * @param signal Signal d'appel contenant un candidat ICE\n   */\n  private handleIceCandidate(signal: CallSignal): void {\n    if (!this.peerConnection) {\n      this.logger.error('No peer connection available for ICE candidate');\n      return;\n    }\n\n    try {\n      const candidate = JSON.parse(signal.data);\n      this.peerConnection\n        .addIceCandidate(new RTCIceCandidate(candidate))\n        .catch((error) => {\n          this.logger.error('Error adding ICE candidate', error as Error);\n        });\n    } catch (error) {\n      this.logger.error('Error parsing ICE candidate', error as Error);\n    }\n  }\n\n  /**\n   * Gère une réponse SDP reçue\n   * @param signal Signal d'appel contenant une réponse SDP\n   */\n  private handleAnswer(signal: CallSignal): void {\n    if (!this.peerConnection) {\n      this.logger.error('No peer connection available for answer');\n      return;\n    }\n\n    try {\n      const answer = JSON.parse(signal.data);\n      this.peerConnection\n        .setRemoteDescription(new RTCSessionDescription(answer))\n        .catch((error) => {\n          this.logger.error('Error setting remote description', error as Error);\n        });\n    } catch (error) {\n      this.logger.error('Error parsing answer', error as Error);\n    }\n  }\n\n  /**\n   * Gère la fin d'un appel\n   * @param signal Signal d'appel indiquant la fin de l'appel\n   */\n  private handleEndCall(signal: CallSignal): void {\n    this.stop('ringtone');\n    this.cleanupCall();\n\n    // Mettre à jour l'état de l'appel actif\n    const currentCall = this.activeCall.value;\n    if (currentCall && currentCall.id === signal.callId) {\n      this.activeCall.next({\n        ...currentCall,\n        status: CallStatus.ENDED,\n        endTime: new Date().toISOString(),\n      });\n    }\n  }\n\n  /**\n   * Gère le rejet d'un appel\n   * @param signal Signal d'appel indiquant le rejet de l'appel\n   */\n  private handleRejectCall(signal: CallSignal): void {\n    this.stop('ringtone');\n    this.cleanupCall();\n\n    // Mettre à jour l'état de l'appel actif\n    const currentCall = this.activeCall.value;\n    if (currentCall && currentCall.id === signal.callId) {\n      this.activeCall.next({\n        ...currentCall,\n        status: CallStatus.REJECTED,\n        endTime: new Date().toISOString(),\n      });\n    }\n  }\n\n  /**\n   * Nettoie les ressources d'appel\n   */\n  private cleanupCall(): void {\n    if (this.localStream) {\n      this.localStream.getTracks().forEach((track) => track.stop());\n      this.localStream = null;\n      this.localStream$.next(null);\n    }\n\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n\n    this.remoteStream = null;\n    this.remoteStream$.next(null);\n  }\n\n  /**\n   * Configure les périphériques média pour un appel\n   * @param callType Type d'appel (audio, vidéo)\n   * @returns Observable avec le flux média\n   */\n  private setupMediaDevices(callType: CallType): Observable<MediaStream> {\n    const constraints: MediaStreamConstraints = {\n      audio: true,\n      video:\n        callType !== CallType.AUDIO\n          ? {\n              width: { ideal: 1280 },\n              height: { ideal: 720 },\n            }\n          : false,\n    };\n\n    return new Observable<MediaStream>((observer) => {\n      navigator.mediaDevices\n        .getUserMedia(constraints)\n        .then((stream) => {\n          observer.next(stream);\n          observer.complete();\n        })\n        .catch((error) => {\n          this.logger.error('Error accessing media devices', error);\n          observer.error(new Error('Failed to access media devices'));\n        });\n    });\n  }\n\n  /**\n   * Génère un ID d'appel unique\n   * @returns ID d'appel unique\n   */\n  private generateCallId(): string {\n    return Date.now().toString() + Math.random().toString(36).substring(2, 9);\n  }\n\n  // --------------------------------------------------------------------------\n  // Section 4: Méthodes pour les Utilisateurs/Groupes\n  // --------------------------------------------------------------------------\n  // User methods\n  getAllUsers(\n    forceRefresh = false,\n    search?: string,\n    page: number = 1,\n    limit: number = 10,\n    sortBy: string = 'username',\n    sortOrder: string = 'asc',\n    isOnline?: boolean\n  ): Observable<User[]> {\n    this.logger.info(\n      'MessageService',\n      `Getting users with params: forceRefresh=${forceRefresh}, search=${\n        search || '(empty)'\n      }, page=${page}, limit=${limit}, sortBy=${sortBy}, sortOrder=${sortOrder}, isOnline=${isOnline}`\n    );\n\n    const now = Date.now();\n    const cacheValid =\n      !forceRefresh &&\n      this.usersCache.length > 0 &&\n      now - this.lastFetchTime <= this.CACHE_DURATION &&\n      !search &&\n      page === 1 &&\n      limit >= this.usersCache.length;\n\n    // Use cache only for first page with no filters\n    if (cacheValid) {\n      this.logger.debug(\n        'MessageService',\n        `Using cached users (${this.usersCache.length} users)`\n      );\n      return of([...this.usersCache]);\n    }\n\n    this.logger.debug(\n      'MessageService',\n      `Fetching users from server with pagination, fetchPolicy=${\n        forceRefresh ? 'network-only' : 'cache-first'\n      }`\n    );\n\n    return this.apollo\n      .watchQuery<any>({\n        query: GET_ALL_USER_QUERY,\n        variables: {\n          search,\n          page,\n          limit,\n          sortBy,\n          sortOrder,\n          isOnline: isOnline !== undefined ? isOnline : null,\n        },\n        fetchPolicy: forceRefresh ? 'network-only' : 'cache-first',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          this.logger.debug(\n            'MessageService',\n            'Users response received',\n            result\n          );\n\n          if (result.errors) {\n            this.logger.error(\n              'MessageService',\n              'GraphQL errors in getAllUsers:',\n              result.errors\n            );\n            throw new Error(result.errors.map((e) => e.message).join(', '));\n          }\n\n          if (!result.data?.getAllUsers) {\n            this.logger.warn(\n              'MessageService',\n              'No users data received from server'\n            );\n            return [];\n          }\n\n          const paginatedResponse = result.data.getAllUsers;\n\n          // Log pagination metadata\n          this.logger.debug('MessageService', 'Pagination metadata:', {\n            totalCount: paginatedResponse.totalCount,\n            totalPages: paginatedResponse.totalPages,\n            currentPage: paginatedResponse.currentPage,\n            hasNextPage: paginatedResponse.hasNextPage,\n            hasPreviousPage: paginatedResponse.hasPreviousPage,\n          });\n\n          // Normalize users with error handling\n          const users: User[] = [];\n          for (const user of paginatedResponse.users) {\n            try {\n              if (user) {\n                users.push(this.normalizeUser(user));\n              }\n            } catch (error) {\n              this.logger.warn(\n                'MessageService',\n                `Error normalizing user, skipping:`,\n                error\n              );\n            }\n          }\n\n          this.logger.info(\n            'MessageService',\n            `Received ${users.length} users from server (page ${paginatedResponse.currentPage} of ${paginatedResponse.totalPages})`\n          );\n\n          // Update cache only for first page with no filters\n          if (!search && page === 1 && !isOnline) {\n            this.usersCache = [...users];\n            this.lastFetchTime = Date.now();\n            this.logger.debug(\n              'MessageService',\n              `User cache updated with ${users.length} users`\n            );\n          }\n\n          // Store pagination metadata in a property for component access\n          this.currentUserPagination = {\n            totalCount: paginatedResponse.totalCount,\n            totalPages: paginatedResponse.totalPages,\n            currentPage: paginatedResponse.currentPage,\n            hasNextPage: paginatedResponse.hasNextPage,\n            hasPreviousPage: paginatedResponse.hasPreviousPage,\n          };\n\n          return users;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error fetching users:', error);\n\n          if (error.graphQLErrors) {\n            this.logger.error(\n              'MessageService',\n              'GraphQL errors:',\n              error.graphQLErrors\n            );\n          }\n\n          if (error.networkError) {\n            this.logger.error(\n              'MessageService',\n              'Network error:',\n              error.networkError\n            );\n          }\n\n          // Return cache if available (only for first page)\n          if (\n            this.usersCache.length > 0 &&\n            page === 1 &&\n            !search &&\n            !isOnline\n          ) {\n            this.logger.warn(\n              'MessageService',\n              `Returning ${this.usersCache.length} cached users due to fetch error`\n            );\n            return of([...this.usersCache]);\n          }\n\n          return throwError(\n            () =>\n              new Error(\n                `Failed to fetch users: ${error.message || 'Unknown error'}`\n              )\n          );\n        })\n      );\n  }\n  getOneUser(userId: string): Observable<User> {\n    return this.apollo\n      .watchQuery<GetOneUserResponse>({\n        query: GET_USER_QUERY,\n        variables: { id: userId },\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => this.normalizeUser(result.data?.getOneUser)),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error fetching user:', error);\n          return throwError(() => new Error('Failed to fetch user'));\n        })\n      );\n  }\n  getCurrentUser(): Observable<User> {\n    return this.apollo\n      .watchQuery<getCurrentUserResponse>({\n        query: GET_CURRENT_USER_QUERY,\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => this.normalizeUser(result.data?.getCurrentUser)),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error fetching current user:',\n            error\n          );\n          return throwError(() => new Error('Failed to fetch current user'));\n        })\n      );\n  }\n  setUserOnline(userId: string): Observable<User> {\n    return this.apollo\n      .mutate<SetUserOnlineResponse>({\n        mutation: SET_USER_ONLINE_MUTATION,\n        variables: { userId },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.setUserOnline)\n            throw new Error('Failed to set user online');\n          return this.normalizeUser(result.data.setUserOnline);\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error setting user online:',\n            error\n          );\n          return throwError(() => new Error('Failed to set user online'));\n        })\n      );\n  }\n  setUserOffline(userId: string): Observable<User> {\n    return this.apollo\n      .mutate<SetUserOfflineResponse>({\n        mutation: SET_USER_OFFLINE_MUTATION,\n        variables: { userId },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.setUserOffline)\n            throw new Error('Failed to set user offline');\n          return this.normalizeUser(result.data.setUserOffline);\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error setting user offline:',\n            error\n          );\n          return throwError(() => new Error('Failed to set user offline'));\n        })\n      );\n  }\n\n  // --------------------------------------------------------------------------\n  // Section: Gestion des Groupes\n  // --------------------------------------------------------------------------\n\n  /**\n   * Crée un nouveau groupe\n   */\n  createGroup(\n    name: string,\n    participantIds: string[],\n    photo?: File,\n    description?: string\n  ): Observable<any> {\n    this.logger.debug(\n      'MessageService',\n      `Creating group: ${name} with ${participantIds.length} participants`\n    );\n\n    if (!name || !participantIds || participantIds.length === 0) {\n      return throwError(\n        () => new Error('Nom du groupe et participants requis')\n      );\n    }\n\n    return this.apollo\n      .mutate({\n        mutation: CREATE_GROUP_MUTATION,\n        variables: { name, participantIds, photo, description },\n      })\n      .pipe(\n        map((result: any) => {\n          const group = result.data?.createGroup;\n          if (!group) {\n            throw new Error('Échec de la création du groupe');\n          }\n          this.logger.info(\n            'MessageService',\n            `Group created successfully: ${group.id}`\n          );\n          return group;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error creating group:', error);\n          return throwError(() => new Error('Échec de la création du groupe'));\n        })\n      );\n  }\n\n  /**\n   * Met à jour un groupe existant\n   */\n  updateGroup(groupId: string, input: any): Observable<any> {\n    this.logger.debug('MessageService', `Updating group: ${groupId}`);\n\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n\n    return this.apollo\n      .mutate({\n        mutation: UPDATE_GROUP_MUTATION,\n        variables: { id: groupId, input },\n      })\n      .pipe(\n        map((result: any) => {\n          const group = result.data?.updateGroup;\n          if (!group) {\n            throw new Error('Échec de la mise à jour du groupe');\n          }\n          this.logger.info(\n            'MessageService',\n            `Group updated successfully: ${group.id}`\n          );\n          return group;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error updating group:', error);\n          return throwError(\n            () => new Error('Échec de la mise à jour du groupe')\n          );\n        })\n      );\n  }\n\n  /**\n   * Supprime un groupe\n   */\n  deleteGroup(\n    groupId: string\n  ): Observable<{ success: boolean; message: string }> {\n    this.logger.debug('MessageService', `Deleting group: ${groupId}`);\n\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n\n    return this.apollo\n      .mutate({\n        mutation: DELETE_GROUP_MUTATION,\n        variables: { id: groupId },\n      })\n      .pipe(\n        map((result: any) => {\n          const response = result.data?.deleteGroup;\n          if (!response) {\n            throw new Error('Échec de la suppression du groupe');\n          }\n          this.logger.info(\n            'MessageService',\n            `Group deleted successfully: ${groupId}`\n          );\n          return response;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error deleting group:', error);\n          return throwError(\n            () => new Error('Échec de la suppression du groupe')\n          );\n        })\n      );\n  }\n\n  /**\n   * Quitte un groupe\n   */\n  leaveGroup(\n    groupId: string\n  ): Observable<{ success: boolean; message: string }> {\n    this.logger.debug('MessageService', `Leaving group: ${groupId}`);\n\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n\n    return this.apollo\n      .mutate({\n        mutation: LEAVE_GROUP_MUTATION,\n        variables: { groupId },\n      })\n      .pipe(\n        map((result: any) => {\n          const response = result.data?.leaveGroup;\n          if (!response) {\n            throw new Error('Échec de la sortie du groupe');\n          }\n          this.logger.info(\n            'MessageService',\n            `Left group successfully: ${groupId}`\n          );\n          return response;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error leaving group:', error);\n          return throwError(() => new Error('Échec de la sortie du groupe'));\n        })\n      );\n  }\n\n  /**\n   * Récupère les informations d'un groupe\n   */\n  getGroup(groupId: string): Observable<any> {\n    this.logger.debug('MessageService', `Getting group: ${groupId}`);\n\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n\n    return this.apollo\n      .query({\n        query: GET_GROUP_QUERY,\n        variables: { id: groupId },\n        fetchPolicy: 'network-only',\n      })\n      .pipe(\n        map((result: any) => {\n          const group = result.data?.getGroup;\n          if (!group) {\n            throw new Error('Groupe non trouvé');\n          }\n          this.logger.info(\n            'MessageService',\n            `Group retrieved successfully: ${groupId}`\n          );\n          return group;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error getting group:', error);\n          return throwError(\n            () => new Error('Échec de la récupération du groupe')\n          );\n        })\n      );\n  }\n\n  /**\n   * Récupère les groupes d'un utilisateur\n   */\n  getUserGroups(userId: string): Observable<any[]> {\n    this.logger.debug('MessageService', `Getting groups for user: ${userId}`);\n\n    if (!userId) {\n      return throwError(() => new Error(\"ID de l'utilisateur requis\"));\n    }\n\n    return this.apollo\n      .query({\n        query: GET_USER_GROUPS_QUERY,\n        variables: { userId },\n        fetchPolicy: 'network-only',\n      })\n      .pipe(\n        map((result: any) => {\n          const groups = result.data?.getUserGroups || [];\n          this.logger.info(\n            'MessageService',\n            `Retrieved ${groups.length} groups for user: ${userId}`\n          );\n          return groups;\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error getting user groups:',\n            error\n          );\n          return throwError(\n            () => new Error('Échec de la récupération des groupes')\n          );\n        })\n      );\n  }\n\n  // --------------------------------------------------------------------------\n  // Section 4: Subscriptions et Gestion Temps Réel\n  // --------------------------------------------------------------------------\n  // ✅ Optimized subscription with connection pooling and caching\n  private subscriptionCache = new Map<string, Observable<Message>>();\n  private subscriptionRefCount = new Map<string, number>();\n\n  subscribeToNewMessages(conversationId: string): Observable<Message> {\n    if (!conversationId) {\n      return throwError(() => new Error('Conversation ID is required'));\n    }\n\n    // ✅ Use cached subscription if available\n    const cacheKey = `messages_${conversationId}`;\n    if (this.subscriptionCache.has(cacheKey)) {\n      const refCount = this.subscriptionRefCount.get(cacheKey) || 0;\n      this.subscriptionRefCount.set(cacheKey, refCount + 1);\n      return this.subscriptionCache.get(cacheKey)!;\n    }\n\n    // ✅ Quick token validation without verbose logging\n    if (!this.isTokenValid()) {\n      return EMPTY;\n    }\n\n    // ✅ Reduced logging for better performance\n    if (!environment.production) {\n      console.log(`🚀 Setting up subscription: ${conversationId}`);\n    }\n    // ✅ Create optimized subscription with caching and shareReplay\n    const sub$ = this.apollo\n      .subscribe<{ messageSent: Message }>({\n        query: MESSAGE_SENT_SUBSCRIPTION,\n        variables: { conversationId },\n        errorPolicy: 'all', // Handle partial errors gracefully\n      })\n      .pipe(\n        // ✅ Debounce rapid messages to prevent UI flooding\n        debounceTime(10),\n        map((result) => {\n          const msg = result.data?.messageSent;\n          if (!msg) {\n            throw new Error('No message payload received');\n          }\n\n          // ✅ Reduced logging for better performance\n          if (!environment.production) {\n            console.log('⚡ New message via WebSocket:', msg.id);\n          }\n\n          // Vérifier que l'ID est présent\n          if (!msg.id && !msg._id) {\n            this.logger.warn(\n              '⚠️ Message without ID received, generating temp ID'\n            );\n            msg.id = `temp-${Date.now()}`;\n          }\n\n          try {\n            // NORMALISATION RAPIDE du message\n            const normalizedMessage = this.normalizeMessage(msg);\n\n            this.logger.debug(\n              '✅ INSTANT: Message normalized successfully',\n              normalizedMessage\n            );\n\n            // TRAITEMENT INSTANTANÉ selon le type\n            if (\n              normalizedMessage.type === MessageType.AUDIO ||\n              normalizedMessage.type === MessageType.VOICE_MESSAGE ||\n              (normalizedMessage.attachments &&\n                normalizedMessage.attachments.some(\n                  (att) => att.type === 'AUDIO'\n                ))\n            ) {\n              this.logger.debug(\n                '🎤 INSTANT: Voice message received in real-time'\n              );\n            }\n\n            // MISE À JOUR IMMÉDIATE de l'UI\n            this.zone.run(() => {\n              this.logger.debug(\n                '📡 INSTANT: Updating conversation UI immediately'\n              );\n              this.updateConversationWithNewMessage(\n                conversationId,\n                normalizedMessage\n              );\n            });\n\n            return normalizedMessage;\n          } catch (err) {\n            this.logger.error('❌ Error normalizing message:', err);\n\n            // Créer un message minimal mais valide pour éviter les erreurs\n            const minimalMessage: Message = {\n              id: msg.id || msg._id || `temp-${Date.now()}`,\n              content: msg.content || '',\n              type: msg.type || MessageType.TEXT,\n              timestamp: this.safeDate(msg.timestamp),\n              isRead: false,\n              sender: msg.sender\n                ? this.normalizeUser(msg.sender)\n                : {\n                    id: this.getCurrentUserId(),\n                    username: 'Unknown',\n                  },\n            };\n\n            this.logger.debug(\n              '🔧 FALLBACK: Created minimal message',\n              minimalMessage\n            );\n            return minimalMessage;\n          }\n        }),\n        catchError((error) => {\n          console.error('Message subscription error:', error);\n          return EMPTY;\n        }),\n        // ✅ Filter null values and deduplicate messages\n        filter((message) => !!message),\n        distinctUntilChanged((prev, curr) => prev?.id === curr?.id),\n        // ✅ Cache subscription with shareReplay for performance\n        shareReplay({ bufferSize: 1, refCount: true }),\n        // ✅ Retry with exponential backoff\n        retry(3)\n      );\n\n    // ✅ Cache the subscription for reuse\n    this.subscriptionCache.set(cacheKey, sub$);\n    this.subscriptionRefCount.set(cacheKey, 1);\n\n    // ✅ Optimized subscription observer with minimal logging\n    const sub = sub$.subscribe({\n      next: (message) => {\n        if (!environment.production) {\n          console.log(`✅ Message received:`, message.id);\n        }\n        // ✅ Update conversation immediately\n        this.updateConversationWithNewMessage(conversationId, message);\n      },\n      error: (err) => {\n        console.error('Subscription error:', err);\n        // ✅ Clean up cache on error\n        this.subscriptionCache.delete(cacheKey);\n        this.subscriptionRefCount.delete(cacheKey);\n      },\n      complete: () => {\n        // ✅ Clean up cache on completion\n        this.subscriptionCache.delete(cacheKey);\n        this.subscriptionRefCount.delete(cacheKey);\n      },\n    });\n\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n\n  /**\n   * Met à jour une conversation avec un nouveau message INSTANTANÉMENT\n   * @param conversationId ID de la conversation\n   * @param message Nouveau message\n   */\n  private updateConversationWithNewMessage(\n    conversationId: string,\n    message: Message\n  ): void {\n    this.logger.debug(\n      `⚡ INSTANT: Updating conversation ${conversationId} with new message ${message.id}`\n    );\n\n    // MISE À JOUR IMMÉDIATE sans attendre la requête\n    this.zone.run(() => {\n      // Émettre IMMÉDIATEMENT l'événement de conversation active\n      this.activeConversation.next(conversationId);\n\n      this.logger.debug('📡 INSTANT: Conversation event emitted immediately');\n    });\n\n    // Mise à jour en arrière-plan (non-bloquante)\n    setTimeout(() => {\n      this.getConversation(conversationId).subscribe({\n        next: (conversation) => {\n          this.logger.debug(\n            `✅ BACKGROUND: Conversation ${conversationId} refreshed with ${\n              conversation?.messages?.length || 0\n            } messages`\n          );\n        },\n        error: (error) => {\n          this.logger.error(\n            `⚠️ BACKGROUND: Error refreshing conversation ${conversationId}:`,\n            error\n          );\n        },\n      });\n    }, 0); // Exécution asynchrone immédiate\n  }\n\n  /**\n   * Rafraîchit les notifications du sender après envoi d'un message\n   */\n  private refreshSenderNotifications(): void {\n    console.log('🔄 SENDER: Refreshing notifications after message sent');\n\n    // Recharger les notifications en arrière-plan\n    this.getNotifications(true).subscribe({\n      next: (notifications) => {\n        console.log(\n          '🔄 SENDER: Notifications refreshed successfully',\n          notifications.length\n        );\n      },\n      error: (error) => {\n        console.error('🔄 SENDER: Error refreshing notifications:', error);\n      },\n    });\n  }\n\n  subscribeToUserStatus(): Observable<User> {\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    if (!this.isTokenValid()) {\n      this.logger.warn(\n        \"Tentative d'abonnement au statut utilisateur avec un token invalide ou expiré\"\n      );\n      return throwError(() => new Error('Invalid or expired token'));\n    }\n\n    this.logger.debug(\"Démarrage de l'abonnement au statut utilisateur\");\n\n    const sub$ = this.apollo\n      .subscribe<{ userStatusChanged: User }>({\n        query: USER_STATUS_SUBSCRIPTION,\n      })\n      .pipe(\n        tap((result) =>\n          this.logger.debug(\n            \"Données reçues de l'abonnement au statut utilisateur:\",\n            result\n          )\n        ),\n        map((result) => {\n          const user = result.data?.userStatusChanged;\n          if (!user) {\n            this.logger.error('No status payload received');\n            throw new Error('No status payload received');\n          }\n          return this.normalizeUser(user);\n        }),\n        catchError((error) => {\n          this.logger.error('Status subscription error:', error as Error);\n          return throwError(() => new Error('Status subscription failed'));\n        }),\n        retry(3) // Réessayer 3 fois en cas d'erreur\n      );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToConversationUpdates(\n    conversationId: string\n  ): Observable<Conversation> {\n    const sub$ = this.apollo\n      .subscribe<{ conversationUpdated: Conversation }>({\n        query: CONVERSATION_UPDATED_SUBSCRIPTION,\n        variables: { conversationId },\n      })\n      .pipe(\n        map((result) => {\n          const conv = result.data?.conversationUpdated;\n          if (!conv) throw new Error('No conversation payload received');\n\n          const normalizedConversation: Conversation = {\n            ...conv,\n            participants:\n              conv.participants?.map((p) => this.normalizeUser(p)) || [],\n            lastMessage: conv.lastMessage\n              ? {\n                  ...conv.lastMessage,\n                  sender: this.normalizeUser(conv.lastMessage.sender),\n                  timestamp: this.safeDate(conv.lastMessage.timestamp),\n                  readAt: conv.lastMessage.readAt\n                    ? this.safeDate(conv.lastMessage.readAt)\n                    : undefined,\n                  // Conservez toutes les autres propriétés du message\n                  id: conv.lastMessage.id,\n                  content: conv.lastMessage.content,\n                  type: conv.lastMessage.type,\n                  isRead: conv.lastMessage.isRead,\n                  // ... autres propriétés nécessaires\n                }\n              : null, // On conserve null comme dans votre version originale\n          };\n\n          return normalizedConversation as Conversation; // Assertion de type si nécessaire\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Conversation subscription error:',\n            error\n          );\n          return throwError(\n            () => new Error('Conversation subscription failed')\n          );\n        })\n      );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToTypingIndicator(\n    conversationId: string\n  ): Observable<TypingIndicatorEvent> {\n    const sub$ = this.apollo\n      .subscribe<TypingIndicatorEvents>({\n        query: TYPING_INDICATOR_SUBSCRIPTION,\n        variables: { conversationId },\n      })\n      .pipe(\n        map((result) => result.data?.typingIndicator),\n        filter(Boolean),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Typing indicator subscription error:',\n            error\n          );\n          return throwError(\n            () => new Error('Typing indicator subscription failed')\n          );\n        })\n      );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  private isTokenValid(): boolean {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      this.logger.warn('Aucun token trouvé');\n      return false;\n    }\n\n    try {\n      // Décoder le token JWT (format: header.payload.signature)\n      const parts = token.split('.');\n      if (parts.length !== 3) {\n        this.logger.warn('Format de token invalide');\n        return false;\n      }\n\n      // Décoder le payload (deuxième partie du token)\n      const payload = JSON.parse(atob(parts[1]));\n\n      // Vérifier l'expiration\n      if (!payload.exp) {\n        this.logger.warn(\"Token sans date d'expiration\");\n        return false;\n      }\n\n      const expirationDate = new Date(payload.exp * 1000);\n      const now = new Date();\n\n      if (expirationDate < now) {\n        this.logger.warn('Token expiré', {\n          expiration: expirationDate.toISOString(),\n          now: now.toISOString(),\n        });\n        return false;\n      }\n\n      return true;\n    } catch (error) {\n      this.logger.error(\n        'Erreur lors de la vérification du token:',\n        error as Error\n      );\n      return false;\n    }\n  }\n\n  subscribeToNotificationsRead(): Observable<string[]> {\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    if (!this.isTokenValid()) {\n      this.logger.warn(\n        \"Tentative d'abonnement aux notifications avec un token invalide ou expiré\"\n      );\n      return of([]);\n    }\n\n    this.logger.debug(\"Démarrage de l'abonnement aux notifications lues\");\n\n    const sub$ = this.apollo\n      .subscribe<NotificationsReadEvent>({\n        query: NOTIFICATIONS_READ_SUBSCRIPTION,\n      })\n      .pipe(\n        tap((result) =>\n          this.logger.debug(\n            \"Données reçues de l'abonnement aux notifications lues:\",\n            result\n          )\n        ),\n        map((result) => {\n          const notificationIds = result.data?.notificationsRead || [];\n          this.logger.debug(\n            'Notifications marquées comme lues:',\n            notificationIds\n          );\n          this.updateNotificationStatus(notificationIds, true);\n          return notificationIds;\n        }),\n        catchError((err) => {\n          this.logger.error(\n            'Notifications read subscription error:',\n            err as Error\n          );\n          // Retourner un tableau vide au lieu de propager l'erreur\n          return of([]);\n        }),\n        // Réessayer après un délai en cas d'erreur\n        retry(3) // Réessayer 3 fois en cas d'erreur\n      );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToNewNotifications(): Observable<Notification> {\n    // Vérifier si l'utilisateur est connecté\n    const token = localStorage.getItem('token');\n    if (!token) {\n      this.logger.warn(\n        \"Tentative d'abonnement aux notifications sans être connecté\"\n      );\n      return EMPTY;\n    }\n\n    this.logger.debug(\n      '🚀 INSTANT NOTIFICATION: Setting up real-time subscription'\n    );\n\n    const source$ = this.apollo.subscribe<NotificationReceivedEvent>({\n      query: NOTIFICATION_SUBSCRIPTION,\n    });\n\n    const processed$ = source$.pipe(\n      map((result) => {\n        const notification = result.data?.notificationReceived;\n        if (!notification) {\n          throw new Error('No notification payload received');\n        }\n\n        this.logger.debug(\n          '⚡ INSTANT: New notification received',\n          notification\n        );\n\n        const normalized = this.normalizeNotification(notification);\n\n        // Vérification rapide du cache\n        if (this.notificationCache.has(normalized.id)) {\n          this.logger.debug(\n            `🔄 Notification ${normalized.id} already in cache, skipping`\n          );\n          throw new Error('Notification already exists in cache');\n        }\n\n        // TRAITEMENT INSTANTANÉ\n        this.logger.debug('📡 INSTANT: Processing notification immediately');\n\n        // Vérifier si la notification existe déjà pour éviter les doublons\n        const currentNotifications = this.notifications.value;\n        const existingNotification = currentNotifications.find(\n          (n) => n.id === normalized.id\n        );\n\n        if (existingNotification) {\n          this.logger.debug(\n            '🔄 DUPLICATE: Notification already exists, skipping:',\n            normalized.id\n          );\n          return normalized;\n        }\n\n        // Son de notification IMMÉDIAT\n        this.playNotificationSound();\n\n        // Mise à jour INSTANTANÉE du cache\n        this.updateNotificationCache(normalized);\n\n        // Émettre IMMÉDIATEMENT la notification EN PREMIER\n        this.zone.run(() => {\n          // 🚀 INSERTION EN PREMIER: Nouvelle notification en tête de liste\n          const updatedNotifications = [normalized, ...currentNotifications];\n\n          this.logger.debug(\n            `⚡ INSTANT: Nouvelle notification ajoutée en PREMIER (${updatedNotifications.length} total)`\n          );\n\n          this.notifications.next(updatedNotifications);\n          this.notificationCount.next(this.notificationCount.value + 1);\n        });\n\n        this.logger.debug(\n          '✅ INSTANT: Notification processed and emitted',\n          normalized\n        );\n\n        return normalized;\n      }),\n      // Gestion d'erreurs optimisée\n      catchError((err) => {\n        if (\n          err instanceof Error &&\n          err.message === 'Notification already exists in cache'\n        ) {\n          return EMPTY;\n        }\n\n        this.logger.error('❌ Notification subscription error:', err as Error);\n        return EMPTY;\n      }),\n      // Optimisation: traitement en temps réel\n      tap((notification) => {\n        this.logger.debug(\n          '⚡ INSTANT: Notification ready for UI update',\n          notification\n        );\n      })\n    );\n\n    const sub = processed$.subscribe({\n      next: (notification) => {\n        this.logger.debug(\n          '✅ INSTANT: Notification delivered to UI',\n          notification\n        );\n      },\n      error: (error) => {\n        this.logger.error(\n          '❌ CRITICAL: Notification subscription error',\n          error\n        );\n      },\n    });\n\n    this.subscriptions.push(sub);\n    this.logger.debug('🔗 INSTANT: Notification subscription established');\n    return processed$;\n  }\n  // --------------------------------------------------------------------------\n  // Helpers et Utilitaires\n  // --------------------------------------------------------------------------\n\n  private startCleanupInterval(): void {\n    this.cleanupInterval = setInterval(() => {\n      this.cleanupExpiredNotifications();\n    }, 3600000);\n  }\n  private cleanupExpiredNotifications(): void {\n    const now = new Date();\n    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n\n    let expiredCount = 0;\n\n    this.notificationCache.forEach((notification, id) => {\n      const notificationDate = new Date(notification.timestamp);\n      if (notificationDate < thirtyDaysAgo) {\n        this.notificationCache.delete(id);\n        expiredCount++;\n      }\n    });\n\n    if (expiredCount > 0) {\n      this.logger.debug(`Cleaned up ${expiredCount} expired notifications`);\n\n      // 🚀 TRI OPTIMISÉ: Maintenir l'ordre après nettoyage\n      const remainingNotifications = Array.from(\n        this.notificationCache.values()\n      );\n      const sortedNotifications = this.sortNotificationsByDate(\n        remainingNotifications\n      );\n\n      this.notifications.next(sortedNotifications);\n      this.updateUnreadCount();\n    }\n  }\n  /**\n   * Trie les notifications par date (plus récentes en premier)\n   * @param notifications Array de notifications à trier\n   * @returns Array de notifications triées\n   */\n  private sortNotificationsByDate(\n    notifications: Notification[]\n  ): Notification[] {\n    return notifications.sort((a, b) => {\n      // Utiliser timestamp ou une date par défaut si manquant\n      const dateA = new Date(a.timestamp || 0);\n      const dateB = new Date(b.timestamp || 0);\n      return dateB.getTime() - dateA.getTime(); // Ordre décroissant (plus récent en premier)\n    });\n  }\n\n  private getCurrentUserId(): string {\n    return localStorage.getItem('userId') || '';\n  }\n  private normalizeMessage(message: Message): Message {\n    if (!message) {\n      this.logger.error(\n        '[MessageService] Cannot normalize null or undefined message'\n      );\n      throw new Error('Message object is required');\n    }\n\n    try {\n      // Vérification des champs obligatoires\n      if (!message.id && !message._id) {\n        this.logger.error(\n          '[MessageService] Message ID is missing',\n          undefined,\n          message\n        );\n        throw new Error('Message ID is required');\n      }\n\n      // Normaliser le sender avec gestion d'erreur\n      let normalizedSender;\n      try {\n        normalizedSender = message.sender\n          ? this.normalizeUser(message.sender)\n          : undefined;\n      } catch (error) {\n        this.logger.warn(\n          '[MessageService] Error normalizing message sender, using default values',\n          error\n        );\n        normalizedSender = {\n          _id: message.senderId || 'unknown',\n          id: message.senderId || 'unknown',\n          username: 'Unknown User',\n          email: '<EMAIL>',\n          role: 'user',\n          isActive: true,\n        };\n      }\n\n      // Normaliser le receiver si présent\n      let normalizedReceiver;\n      if (message.receiver) {\n        try {\n          normalizedReceiver = this.normalizeUser(message.receiver);\n        } catch (error) {\n          this.logger.warn(\n            '[MessageService] Error normalizing message receiver, using default values',\n            error\n          );\n          normalizedReceiver = {\n            _id: message.receiverId || 'unknown',\n            id: message.receiverId || 'unknown',\n            username: 'Unknown User',\n            email: '<EMAIL>',\n            role: 'user',\n            isActive: true,\n          };\n        }\n      }\n\n      // Normaliser les pièces jointes si présentes\n      const normalizedAttachments =\n        message.attachments?.map((att) => ({\n          id: att.id || att._id || `attachment-${Date.now()}`,\n          url: att.url || '',\n          type: att.type || 'unknown',\n          name: att.name || 'attachment',\n          size: att.size || 0,\n          duration: att.duration || 0,\n        })) || [];\n\n      // Construire le message normalisé\n      const normalizedMessage = {\n        ...message,\n        _id: message.id || message._id,\n        id: message.id || message._id,\n        content: message.content || '',\n        sender: normalizedSender,\n        timestamp: this.normalizeDate(message.timestamp),\n        readAt: message.readAt ? this.normalizeDate(message.readAt) : undefined,\n        attachments: normalizedAttachments,\n        metadata: message.metadata || null,\n      };\n\n      // Ajouter le receiver seulement s'il existe\n      if (normalizedReceiver) {\n        normalizedMessage.receiver = normalizedReceiver;\n      }\n\n      this.logger.debug('[MessageService] Message normalized successfully', {\n        messageId: normalizedMessage.id,\n        senderId: normalizedMessage.sender?.id,\n      });\n\n      return normalizedMessage;\n    } catch (error) {\n      this.logger.error(\n        '[MessageService] Error normalizing message:',\n        error instanceof Error ? error : new Error(String(error)),\n        message\n      );\n      throw new Error(\n        `Failed to normalize message: ${\n          error instanceof Error ? error.message : String(error)\n        }`\n      );\n    }\n  }\n\n  public normalizeUser(user: any): User {\n    if (!user) {\n      throw new Error('User object is required');\n    }\n\n    // Vérification des champs obligatoires avec valeurs par défaut\n    const userId = user.id || user._id;\n    if (!userId) {\n      throw new Error('User ID is required');\n    }\n\n    // Utiliser des valeurs par défaut pour les champs manquants\n    const username = user.username || 'Unknown User';\n    const email = user.email || `user-${userId}@example.com`;\n    const isActive =\n      user.isActive !== undefined && user.isActive !== null\n        ? user.isActive\n        : true;\n    const role = user.role || 'user';\n\n    // Construire l'objet utilisateur normalisé\n    return {\n      _id: userId,\n      id: userId,\n      username: username,\n      email: email,\n      role: role,\n      isActive: isActive,\n      // Champs optionnels\n      image: user.image ?? null,\n      bio: user.bio,\n      isOnline: user.isOnline || false,\n      lastActive: user.lastActive ? new Date(user.lastActive) : undefined,\n      createdAt: user.createdAt ? new Date(user.createdAt) : undefined,\n      updatedAt: user.updatedAt ? new Date(user.updatedAt) : undefined,\n      followingCount: user.followingCount,\n      followersCount: user.followersCount,\n      postCount: user.postCount,\n    };\n  }\n  private normalizeConversation(conv: Conversation): Conversation {\n    if (!conv) {\n      this.logger.error(\n        '[MessageService] Cannot normalize null or undefined conversation'\n      );\n      throw new Error('Conversation object is required');\n    }\n\n    try {\n      // Vérification des champs obligatoires\n      if (!conv.id && !conv._id) {\n        this.logger.error(\n          '[MessageService] Conversation ID is missing',\n          undefined,\n          conv\n        );\n        throw new Error('Conversation ID is required');\n      }\n\n      // Normaliser les participants avec gestion d'erreur\n      const normalizedParticipants = [];\n      if (conv.participants && Array.isArray(conv.participants)) {\n        for (const participant of conv.participants) {\n          try {\n            if (participant) {\n              normalizedParticipants.push(this.normalizeUser(participant));\n            }\n          } catch (error) {\n            this.logger.warn(\n              '[MessageService] Error normalizing participant, skipping',\n              error\n            );\n          }\n        }\n      } else {\n        this.logger.warn(\n          '[MessageService] Conversation has no participants or invalid participants array',\n          conv\n        );\n      }\n\n      // Normaliser les messages avec gestion d'erreur\n      const normalizedMessages = [];\n      if (conv.messages && Array.isArray(conv.messages)) {\n        this.logger.debug('[MessageService] Processing conversation messages', {\n          count: conv.messages.length,\n        });\n\n        for (const message of conv.messages) {\n          try {\n            if (message) {\n              const normalizedMessage = this.normalizeMessage(message);\n              this.logger.debug(\n                '[MessageService] Successfully normalized message',\n                {\n                  messageId: normalizedMessage.id,\n                  content: normalizedMessage.content?.substring(0, 20),\n                  sender: normalizedMessage.sender?.username,\n                }\n              );\n              normalizedMessages.push(normalizedMessage);\n            }\n          } catch (error) {\n            this.logger.warn(\n              '[MessageService] Error normalizing message in conversation, skipping',\n              error\n            );\n          }\n        }\n      } else {\n        this.logger.debug(\n          '[MessageService] No messages found in conversation or invalid messages array'\n        );\n      }\n\n      // Normaliser le dernier message avec gestion d'erreur\n      let normalizedLastMessage = null;\n      if (conv.lastMessage) {\n        try {\n          normalizedLastMessage = this.normalizeMessage(conv.lastMessage);\n        } catch (error) {\n          this.logger.warn(\n            '[MessageService] Error normalizing last message, using null',\n            error\n          );\n        }\n      }\n\n      // Construire la conversation normalisée\n      const normalizedConversation = {\n        ...conv,\n        _id: conv.id || conv._id,\n        id: conv.id || conv._id,\n        participants: normalizedParticipants,\n        messages: normalizedMessages,\n        lastMessage: normalizedLastMessage,\n        unreadCount: conv.unreadCount || 0,\n        isGroup: !!conv.isGroup,\n        createdAt: this.normalizeDate(conv.createdAt),\n        updatedAt: this.normalizeDate(conv.updatedAt),\n      };\n\n      this.logger.debug(\n        '[MessageService] Conversation normalized successfully',\n        {\n          conversationId: normalizedConversation.id,\n          participantCount: normalizedParticipants.length,\n          messageCount: normalizedMessages.length,\n        }\n      );\n\n      return normalizedConversation;\n    } catch (error) {\n      this.logger.error(\n        '[MessageService] Error normalizing conversation:',\n        error instanceof Error ? error : new Error(String(error)),\n        conv\n      );\n      throw new Error(\n        `Failed to normalize conversation: ${\n          error instanceof Error ? error.message : String(error)\n        }`\n      );\n    }\n  }\n  private normalizeDate(date: string | Date | undefined): Date {\n    if (!date) return new Date();\n    try {\n      return typeof date === 'string' ? new Date(date) : date;\n    } catch (error) {\n      this.logger.warn(`Failed to parse date: ${date}`, error);\n      return new Date();\n    }\n  }\n\n  // Méthode sécurisée pour créer une date à partir d'une valeur potentiellement undefined\n  private safeDate(date: string | Date | undefined): Date {\n    if (!date) return new Date();\n    try {\n      return typeof date === 'string' ? new Date(date) : date;\n    } catch (error) {\n      this.logger.warn(`Failed to create safe date: ${date}`, error);\n      return new Date();\n    }\n  }\n  private toSafeISOString = (\n    date: Date | string | undefined\n  ): string | undefined => {\n    if (!date) return undefined;\n    return typeof date === 'string' ? date : date.toISOString();\n  };\n  private normalizeNotification(notification: Notification): Notification {\n    this.logger.debug(\n      'MessageService',\n      'Normalizing notification',\n      notification\n    );\n\n    if (!notification) {\n      this.logger.error('MessageService', 'Notification is null or undefined');\n      throw new Error('Notification is required');\n    }\n\n    // Vérifier et normaliser l'ID\n    const notificationId = notification.id || (notification as any)._id;\n    if (!notificationId) {\n      this.logger.error(\n        'MessageService',\n        'Notification ID is missing',\n        notification\n      );\n      throw new Error('Notification ID is required');\n    }\n\n    if (!notification.timestamp) {\n      this.logger.warn(\n        'MessageService',\n        'Notification timestamp is missing, using current time',\n        notification\n      );\n      notification.timestamp = new Date();\n    }\n\n    try {\n      const normalized = {\n        ...notification,\n        _id: notificationId, // Conserver l'ID MongoDB original\n        id: notificationId, // Utiliser le même ID pour les deux propriétés\n        timestamp: new Date(notification.timestamp),\n        ...(notification.senderId && {\n          senderId: this.normalizeSender(notification.senderId),\n        }),\n        ...(notification.message && {\n          message: this.normalizeNotMessage(notification.message),\n        }),\n      };\n\n      this.logger.debug(\n        'MessageService',\n        'Normalized notification result',\n        normalized\n      );\n      return normalized;\n    } catch (error) {\n      this.logger.error(\n        'MessageService',\n        'Error in normalizeNotification',\n        error\n      );\n      throw error;\n    }\n  }\n  private normalizeSender(sender: any) {\n    return {\n      id: sender.id,\n      username: sender.username,\n      ...(sender.image && { image: sender.image }),\n    };\n  }\n\n  /**\n   * Normalise un message de notification\n   * @param message Message à normaliser\n   * @returns Message normalisé\n   */\n  private normalizeNotMessage(message: any) {\n    if (!message) return null;\n\n    return {\n      id: message.id || message._id,\n      content: message.content || '',\n      type: message.type || 'TEXT',\n      timestamp: this.safeDate(message.timestamp),\n      attachments: message.attachments || [],\n      ...(message.sender && { sender: this.normalizeSender(message.sender) }),\n    };\n  }\n  /**\n   * Met à jour le cache de notifications avec une ou plusieurs notifications\n   * @param notifications Notification(s) à ajouter au cache\n   * @param skipDuplicates Si true, ignore les notifications déjà présentes dans le cache\n   */\n  private updateCache(\n    notifications: Notification | Notification[],\n    skipDuplicates: boolean = true\n  ) {\n    const notificationArray = Array.isArray(notifications)\n      ? notifications\n      : [notifications];\n\n    this.logger.debug(\n      'MessageService',\n      `Updating notification cache with ${notificationArray.length} notifications`\n    );\n\n    if (notificationArray.length === 0) {\n      this.logger.warn('MessageService', 'No notifications to update in cache');\n      return;\n    }\n\n    // Vérifier si les notifications ont des IDs valides\n    const validNotifications = notificationArray.filter(\n      (notif) => notif && (notif.id || (notif as any)._id)\n    );\n\n    if (validNotifications.length !== notificationArray.length) {\n      this.logger.warn(\n        'MessageService',\n        `Found ${\n          notificationArray.length - validNotifications.length\n        } notifications without valid IDs`\n      );\n    }\n\n    let addedCount = 0;\n    let skippedCount = 0;\n\n    // Traiter chaque notification\n    validNotifications.forEach((notif, index) => {\n      try {\n        // S'assurer que la notification a un ID\n        const notifId = notif.id || (notif as any)._id;\n        if (!notifId) {\n          this.logger.error(\n            'MessageService',\n            'Notification without ID:',\n            notif\n          );\n          return;\n        }\n\n        // Normaliser la notification\n        const normalized = this.normalizeNotification(notif);\n\n        // Vérifier si cette notification existe déjà dans le cache\n        if (skipDuplicates && this.notificationCache.has(normalized.id)) {\n          this.logger.debug(\n            'MessageService',\n            `Notification ${normalized.id} already exists in cache, skipping`\n          );\n          skippedCount++;\n          return;\n        }\n\n        // Ajouter au cache\n        this.notificationCache.set(normalized.id, normalized);\n        addedCount++;\n\n        this.logger.debug(\n          'MessageService',\n          `Added notification ${normalized.id} to cache`\n        );\n      } catch (error) {\n        this.logger.error(\n          'MessageService',\n          `Error processing notification ${index + 1}:`,\n          error\n        );\n      }\n    });\n\n    this.logger.debug(\n      'MessageService',\n      `Cache update complete: ${addedCount} added, ${skippedCount} skipped, total: ${this.notificationCache.size}`\n    );\n\n    // Mettre à jour les observables et sauvegarder\n    this.refreshNotificationObservables();\n  }\n  /**\n   * Met à jour les observables de notifications et sauvegarde dans le localStorage\n   * OPTIMISÉ: Trie les notifications par date (plus récentes en premier)\n   */\n  private refreshNotificationObservables(): void {\n    const allNotifications = Array.from(this.notificationCache.values());\n\n    // 🚀 TRI OPTIMISÉ: Les notifications les plus récentes en premier\n    const sortedNotifications = this.sortNotificationsByDate(allNotifications);\n\n    this.logger.debug(\n      `📊 SORTED: ${sortedNotifications.length} notifications triées par date (plus récentes en premier)`\n    );\n\n    this.notifications.next(sortedNotifications);\n    this.updateUnreadCount();\n    this.saveNotificationsToLocalStorage();\n  }\n\n  /**\n   * Met à jour le compteur de notifications non lues\n   */\n  private updateUnreadCount(): void {\n    const allNotifications = Array.from(this.notificationCache.values());\n    const unreadNotifications = allNotifications.filter((n) => !n.isRead);\n    const count = unreadNotifications.length;\n\n    // Forcer la mise à jour dans la zone Angular\n    this.zone.run(() => {\n      this.notificationCount.next(count);\n\n      // Émettre un événement global pour forcer la mise à jour du layout\n      window.dispatchEvent(\n        new CustomEvent('notificationCountChanged', {\n          detail: { count },\n        })\n      );\n    });\n  }\n\n  /**\n   * Met à jour le cache avec une seule notification (méthode simplifiée)\n   * @param notification Notification à ajouter\n   */\n  private updateNotificationCache(notification: Notification): void {\n    this.updateCache(notification, true);\n  }\n  /**\n   * Met à jour le statut de lecture des notifications\n   * @param ids IDs des notifications à mettre à jour\n   * @param isRead Nouveau statut de lecture\n   */\n  private updateNotificationStatus(ids: string[], isRead: boolean): void {\n    ids.forEach((id) => {\n      const notif = this.notificationCache.get(id);\n      if (notif) {\n        this.notificationCache.set(id, {\n          ...notif,\n          isRead,\n          readAt: isRead ? new Date().toISOString() : undefined,\n        });\n      }\n    });\n    this.refreshNotificationObservables();\n  }\n\n  /**\n   * Méthode générique pour supprimer des notifications du cache local\n   * @param notificationIds IDs des notifications à supprimer\n   * @returns Nombre de notifications supprimées\n   */\n  private removeNotificationsFromCache(notificationIds: string[]): number {\n    console.log(\n      '🗑️ REMOVE FROM CACHE: Starting removal of',\n      notificationIds.length,\n      'notifications'\n    );\n    console.log(\n      '🗑️ REMOVE FROM CACHE: Cache size before:',\n      this.notificationCache.size\n    );\n\n    let removedCount = 0;\n    notificationIds.forEach((id) => {\n      if (this.notificationCache.has(id)) {\n        console.log('🗑️ REMOVE FROM CACHE: Removing notification:', id);\n        this.notificationCache.delete(id);\n        removedCount++;\n      } else {\n        console.log(\n          '🗑️ REMOVE FROM CACHE: Notification not found in cache:',\n          id\n        );\n      }\n    });\n\n    console.log('🗑️ REMOVE FROM CACHE: Removed', removedCount, 'notifications');\n    console.log(\n      '🗑️ REMOVE FROM CACHE: Cache size after:',\n      this.notificationCache.size\n    );\n\n    if (removedCount > 0) {\n      console.log('🗑️ REMOVE FROM CACHE: Refreshing observables...');\n      this.refreshNotificationObservables();\n    }\n\n    return removedCount;\n  }\n\n  /**\n   * Méthode générique pour gérer les erreurs de suppression\n   * @param error Erreur survenue\n   * @param operation Nom de l'opération\n   * @param fallbackResponse Réponse de fallback en cas d'erreur\n   */\n  private handleDeletionError(\n    error: any,\n    operation: string,\n    fallbackResponse: any\n  ) {\n    this.logger.error('MessageService', `Erreur lors de ${operation}:`, error);\n    return of(fallbackResponse);\n  }\n  // Typing indicators\n  startTyping(conversationId: string): Observable<boolean> {\n    const userId = this.getCurrentUserId();\n    if (!userId) {\n      this.logger.warn('MessageService', 'Cannot start typing: no user ID');\n      return of(false);\n    }\n\n    return this.apollo\n      .mutate<StartTupingResponse>({\n        mutation: START_TYPING_MUTATION,\n        variables: {\n          input: {\n            conversationId,\n            userId,\n          },\n        },\n      })\n      .pipe(\n        map((result) => result.data?.startTyping || false),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error starting typing indicator',\n            error\n          );\n          return throwError(\n            () => new Error('Failed to start typing indicator')\n          );\n        })\n      );\n  }\n\n  stopTyping(conversationId: string): Observable<boolean> {\n    const userId = this.getCurrentUserId();\n    if (!userId) {\n      this.logger.warn('MessageService', 'Cannot stop typing: no user ID');\n      return of(false);\n    }\n\n    return this.apollo\n      .mutate<StopTypingResponse>({\n        mutation: STOP_TYPING_MUTATION,\n        variables: {\n          input: {\n            conversationId,\n            userId,\n          },\n        },\n      })\n      .pipe(\n        map((result) => result.data?.stopTyping || false),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error stopping typing indicator',\n            error\n          );\n          return throwError(() => new Error('Failed to stop typing indicator'));\n        })\n      );\n  }\n\n  // ========================================\n  // MÉTHODE SENDMESSAGE MANQUANTE\n  // ========================================\n\n  /**\n   * Envoie un message (texte, fichier, audio, etc.)\n   * @param receiverId ID du destinataire\n   * @param content Contenu du message (texte)\n   * @param file Fichier à envoyer (optionnel)\n   * @param messageType Type de message (TEXT, AUDIO, IMAGE, etc.)\n   * @param conversationId ID de la conversation\n   * @returns Observable avec le message envoyé\n   */\n  sendMessage(\n    receiverId: string,\n    content: string,\n    file?: File,\n    messageType: any = 'TEXT',\n    conversationId?: string\n  ): Observable<Message> {\n    console.log('🚀 [MessageService] sendMessage called with:', {\n      receiverId,\n      content: content?.substring(0, 50),\n      hasFile: !!file,\n      fileName: file?.name,\n      fileType: file?.type,\n      fileSize: file?.size,\n      messageType,\n      conversationId,\n    });\n\n    if (!receiverId) {\n      const error = new Error('Receiver ID is required');\n      console.error('❌ [MessageService] sendMessage error:', error);\n      return throwError(() => error);\n    }\n\n    // Préparer les variables pour la mutation\n    const variables: any = {\n      receiverId,\n      content: content || '',\n      type: messageType,\n    };\n\n    // Ajouter l'ID de conversation si fourni\n    if (conversationId) {\n      variables.conversationId = conversationId;\n    }\n\n    // Si un fichier est fourni, l'ajouter aux variables\n    if (file) {\n      variables.file = file;\n      console.log('📁 [MessageService] Adding file to mutation:', {\n        name: file.name,\n        type: file.type,\n        size: file.size,\n      });\n    }\n\n    console.log(\n      '📤 [MessageService] Sending mutation with variables:',\n      variables\n    );\n\n    return this.apollo\n      .mutate<SendMessageResponse>({\n        mutation: SEND_MESSAGE_MUTATION,\n        variables,\n        context: {\n          useMultipart: !!file, // Utiliser multipart si un fichier est présent\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log(\n            '✅ [MessageService] sendMessage mutation result:',\n            result\n          );\n\n          if (!result.data?.sendMessage) {\n            throw new Error('No message data received from server');\n          }\n\n          const message = result.data.sendMessage;\n          console.log('📨 [MessageService] Message sent successfully:', {\n            id: message.id,\n            type: message.type,\n            content: message.content?.substring(0, 50),\n            hasAttachments: !!message.attachments?.length,\n          });\n\n          // Normaliser le message reçu\n          const normalizedMessage = this.normalizeMessage(message);\n          console.log(\n            '🔧 [MessageService] Message normalized:',\n            normalizedMessage\n          );\n\n          return normalizedMessage;\n        }),\n        catchError((error) => {\n          console.error('❌ [MessageService] sendMessage error:', error);\n          this.logger.error('Error sending message:', error);\n\n          // Fournir un message d'erreur plus spécifique\n          let errorMessage = \"Erreur lors de l'envoi du message\";\n          if (error.networkError) {\n            errorMessage = 'Erreur de connexion réseau';\n          } else if (error.graphQLErrors?.length > 0) {\n            errorMessage = error.graphQLErrors[0].message || errorMessage;\n          }\n\n          return throwError(() => new Error(errorMessage));\n        })\n      );\n  }\n\n  // ========================================\n  // MÉTHODES UTILITAIRES CONSOLIDÉES\n  // ========================================\n\n  /**\n   * Formate l'heure d'un message\n   */\n  formatMessageTime(timestamp: string | Date | undefined): string {\n    if (!timestamp) return 'Unknown time';\n    try {\n      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\n      return date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: false,\n      });\n    } catch (error) {\n      return 'Invalid time';\n    }\n  }\n\n  /**\n   * Formate la dernière activité d'un utilisateur\n   */\n  formatLastActive(lastActive: string | Date | undefined): string {\n    if (!lastActive) return 'Offline';\n    const lastActiveDate =\n      lastActive instanceof Date ? lastActive : new Date(lastActive);\n    const now = new Date();\n    const diffHours =\n      Math.abs(now.getTime() - lastActiveDate.getTime()) / (1000 * 60 * 60);\n\n    if (diffHours < 24) {\n      return `Active ${lastActiveDate.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit',\n      })}`;\n    }\n    return `Active ${lastActiveDate.toLocaleDateString()}`;\n  }\n\n  /**\n   * Formate la date d'un message\n   */\n  formatMessageDate(timestamp: string | Date | undefined): string {\n    if (!timestamp) return 'Unknown date';\n\n    try {\n      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\n      const today = new Date();\n\n      if (date.toDateString() === today.toDateString()) {\n        return date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit',\n        });\n      }\n\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n\n      if (date.toDateString() === yesterday.toDateString()) {\n        return `LUN., ${date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit',\n        })}`;\n      }\n\n      const day = date\n        .toLocaleDateString('fr-FR', { weekday: 'short' })\n        .toUpperCase();\n      return `${day}., ${date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit',\n      })}`;\n    } catch (error) {\n      return 'Invalid date';\n    }\n  }\n\n  /**\n   * Détermine si un en-tête de date doit être affiché\n   */\n  shouldShowDateHeader(messages: any[], index: number): boolean {\n    if (index === 0) return true;\n\n    try {\n      const currentMsg = messages[index];\n      const prevMsg = messages[index - 1];\n\n      if (!currentMsg?.timestamp || !prevMsg?.timestamp) return true;\n\n      const currentDate = this.getDateFromTimestamp(currentMsg.timestamp);\n      const prevDate = this.getDateFromTimestamp(prevMsg.timestamp);\n\n      return currentDate !== prevDate;\n    } catch (error) {\n      return false;\n    }\n  }\n\n  private getDateFromTimestamp(timestamp: string | Date | undefined): string {\n    if (!timestamp) return 'unknown-date';\n    try {\n      return (\n        timestamp instanceof Date ? timestamp : new Date(timestamp)\n      ).toDateString();\n    } catch (error) {\n      return 'invalid-date';\n    }\n  }\n\n  /**\n   * Obtient l'icône d'un fichier selon son type MIME\n   */\n  getFileIcon(mimeType?: string): string {\n    if (!mimeType) return 'fa-file';\n    if (mimeType.startsWith('image/')) return 'fa-image';\n    if (mimeType.includes('pdf')) return 'fa-file-pdf';\n    if (mimeType.includes('word') || mimeType.includes('msword'))\n      return 'fa-file-word';\n    if (mimeType.includes('excel')) return 'fa-file-excel';\n    if (mimeType.includes('powerpoint')) return 'fa-file-powerpoint';\n    if (mimeType.includes('audio')) return 'fa-file-audio';\n    if (mimeType.includes('video')) return 'fa-file-video';\n    if (mimeType.includes('zip') || mimeType.includes('compressed'))\n      return 'fa-file-archive';\n    return 'fa-file';\n  }\n\n  /**\n   * Obtient le type d'un fichier selon son type MIME\n   */\n  getFileType(mimeType?: string): string {\n    if (!mimeType) return 'File';\n\n    const typeMap: Record<string, string> = {\n      'image/': 'Image',\n      'application/pdf': 'PDF',\n      'application/msword': 'Word Doc',\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document':\n        'Word Doc',\n      'application/vnd.ms-excel': 'Excel',\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':\n        'Excel',\n      'application/vnd.ms-powerpoint': 'PowerPoint',\n      'application/vnd.openxmlformats-officedocument.presentationml.presentation':\n        'PowerPoint',\n      'audio/': 'Audio',\n      'video/': 'Video',\n      'application/zip': 'ZIP Archive',\n      'application/x-rar-compressed': 'RAR Archive',\n    };\n\n    for (const [key, value] of Object.entries(typeMap)) {\n      if (mimeType.includes(key)) return value;\n    }\n    return 'File';\n  }\n\n  /**\n   * Vérifie si un message contient une image\n   */\n  hasImage(message: any): boolean {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return false;\n    }\n\n    const attachment = message.attachments[0];\n    if (!attachment || !attachment.type) {\n      return false;\n    }\n\n    const type = attachment.type.toString();\n    return type === 'IMAGE' || type === 'image';\n  }\n\n  /**\n   * Vérifie si le message est un message vocal\n   */\n  isVoiceMessage(message: any): boolean {\n    if (!message) return false;\n\n    // Vérifier le type du message\n    if (\n      message.type === MessageType.VOICE_MESSAGE ||\n      message.type === MessageType.VOICE_MESSAGE\n    ) {\n      return true;\n    }\n\n    // Vérifier les pièces jointes\n    if (message.attachments && message.attachments.length > 0) {\n      return message.attachments.some((att: any) => {\n        const type = att.type?.toString();\n        return (\n          type === 'VOICE_MESSAGE' ||\n          type === 'voice_message' ||\n          (message.metadata?.isVoiceMessage &&\n            (type === 'AUDIO' || type === 'audio'))\n        );\n      });\n    }\n\n    // Vérifier les métadonnées\n    return !!message.metadata?.isVoiceMessage;\n  }\n\n  /**\n   * Récupère l'URL du message vocal\n   */\n  getVoiceMessageUrl(message: any): string {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return '';\n    }\n\n    const voiceAttachment = message.attachments.find((att: any) => {\n      const type = att.type?.toString();\n      return (\n        type === 'VOICE_MESSAGE' ||\n        type === 'voice_message' ||\n        type === 'AUDIO' ||\n        type === 'audio'\n      );\n    });\n\n    return voiceAttachment?.url || '';\n  }\n\n  /**\n   * Récupère la durée du message vocal\n   */\n  getVoiceMessageDuration(message: any): number {\n    if (!message) return 0;\n\n    // Essayer d'abord de récupérer la durée depuis les métadonnées\n    if (message.metadata?.duration) {\n      return message.metadata.duration;\n    }\n\n    // Sinon, essayer de récupérer depuis les pièces jointes\n    if (message.attachments && message.attachments.length > 0) {\n      const voiceAttachment = message.attachments.find((att: any) => {\n        const type = att.type?.toString();\n        return (\n          type === 'VOICE_MESSAGE' ||\n          type === 'voice_message' ||\n          type === 'AUDIO' ||\n          type === 'audio'\n        );\n      });\n\n      if (voiceAttachment && voiceAttachment.duration) {\n        return voiceAttachment.duration;\n      }\n    }\n\n    return 0;\n  }\n\n  /**\n   * Génère la hauteur des barres de la forme d'onde moderne\n   */\n  getVoiceBarHeight(index: number): number {\n    const pattern = [\n      8, 12, 6, 15, 10, 18, 7, 14, 9, 16, 5, 13, 11, 17, 8, 12, 6, 15, 10, 18,\n    ];\n    return pattern[index % pattern.length];\n  }\n\n  /**\n   * Formate la durée du message vocal en format MM:SS\n   */\n  formatVoiceDuration(seconds: number): string {\n    if (!seconds || seconds === 0) {\n      return '0:00';\n    }\n\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n\n  /**\n   * Obtient l'URL de l'image en toute sécurité\n   */\n  getImageUrl(message: any): string {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return '';\n    }\n\n    const attachment = message.attachments[0];\n    return attachment?.url || '';\n  }\n\n  /**\n   * Détermine le type d'un message\n   */\n  getMessageType(message: any): MessageType {\n    if (!message) return MessageType.TEXT;\n\n    try {\n      if (message.type) {\n        const msgType = message.type.toString();\n        if (msgType === 'text' || msgType === 'TEXT') {\n          return MessageType.TEXT;\n        } else if (msgType === 'image' || msgType === 'IMAGE') {\n          return MessageType.IMAGE;\n        } else if (msgType === 'file' || msgType === 'FILE') {\n          return MessageType.FILE;\n        } else if (msgType === 'audio' || msgType === 'AUDIO') {\n          return MessageType.AUDIO;\n        } else if (msgType === 'video' || msgType === 'VIDEO') {\n          return MessageType.VIDEO;\n        } else if (msgType === 'system' || msgType === 'SYSTEM') {\n          return MessageType.SYSTEM;\n        }\n      }\n\n      if (message.attachments?.length) {\n        const attachment = message.attachments[0];\n        if (attachment && attachment.type) {\n          const attachmentTypeStr = attachment.type.toString();\n\n          if (attachmentTypeStr === 'image' || attachmentTypeStr === 'IMAGE') {\n            return MessageType.IMAGE;\n          } else if (\n            attachmentTypeStr === 'file' ||\n            attachmentTypeStr === 'FILE'\n          ) {\n            return MessageType.FILE;\n          } else if (\n            attachmentTypeStr === 'audio' ||\n            attachmentTypeStr === 'AUDIO'\n          ) {\n            return MessageType.AUDIO;\n          } else if (\n            attachmentTypeStr === 'video' ||\n            attachmentTypeStr === 'VIDEO'\n          ) {\n            return MessageType.VIDEO;\n          }\n        }\n\n        return MessageType.FILE;\n      }\n\n      return MessageType.TEXT;\n    } catch (error) {\n      return MessageType.TEXT;\n    }\n  }\n\n  /**\n   * Retourne la liste des emojis communs\n   */\n  getCommonEmojis(): string[] {\n    return [\n      '😀',\n      '😃',\n      '😄',\n      '😁',\n      '😆',\n      '😅',\n      '😂',\n      '🤣',\n      '😊',\n      '😇',\n      '🙂',\n      '🙃',\n      '😉',\n      '😌',\n      '😍',\n      '🥰',\n      '😘',\n      '😗',\n      '😙',\n      '😚',\n      '😋',\n      '😛',\n      '😝',\n      '😜',\n      '🤪',\n      '🤨',\n      '🧐',\n      '🤓',\n      '😎',\n      '🤩',\n      '😏',\n      '😒',\n      '😞',\n      '😔',\n      '😟',\n      '😕',\n      '🙁',\n      '☹️',\n      '😣',\n      '😖',\n      '😫',\n      '😩',\n      '🥺',\n      '😢',\n      '😭',\n      '😤',\n      '😠',\n      '😡',\n      '🤬',\n      '🤯',\n      '😳',\n      '🥵',\n      '🥶',\n      '😱',\n      '😨',\n      '😰',\n      '😥',\n      '😓',\n      '🤗',\n      '🤔',\n      '👍',\n      '👎',\n      '👏',\n      '🙌',\n      '👐',\n      '🤲',\n      '🤝',\n      '🙏',\n      '✌️',\n      '🤞',\n      '❤️',\n      '🧡',\n      '💛',\n      '💚',\n      '💙',\n      '💜',\n      '🖤',\n      '💔',\n      '💯',\n      '💢',\n    ];\n  }\n\n  /**\n   * Obtient les classes CSS pour un message\n   */\n  getMessageTypeClass(message: any, currentUserId: string | null): string {\n    if (!message) {\n      return 'bg-gray-100 rounded-lg px-4 py-2';\n    }\n\n    try {\n      const isCurrentUser =\n        message.sender?.id === currentUserId ||\n        message.sender?._id === currentUserId ||\n        message.senderId === currentUserId;\n\n      const baseClass = isCurrentUser\n        ? 'bg-blue-500 text-white rounded-2xl rounded-br-sm'\n        : 'bg-gray-200 text-gray-800 rounded-2xl rounded-bl-sm';\n\n      const messageType = this.getMessageType(message);\n\n      if (message.attachments && message.attachments.length > 0) {\n        const attachment = message.attachments[0];\n        if (attachment && attachment.type) {\n          const attachmentTypeStr = attachment.type.toString();\n          if (attachmentTypeStr === 'IMAGE' || attachmentTypeStr === 'image') {\n            return `p-1 max-w-xs`;\n          } else if (\n            attachmentTypeStr === 'FILE' ||\n            attachmentTypeStr === 'file'\n          ) {\n            return `${baseClass} p-3`;\n          }\n        }\n      }\n\n      // Les vérifications de type sont déjà faites avec les attachments ci-dessus\n\n      return `${baseClass} px-4 py-3 whitespace-normal break-words min-w-[120px]`;\n    } catch (error) {\n      return 'bg-gray-100 rounded-lg px-4 py-2 whitespace-normal break-words';\n    }\n  }\n\n  // ========================================\n  // APPELS WEBRTC - DÉLÉGUÉS AU CALLSERVICE\n  // ========================================\n  // Note: Les méthodes d'appel ont été déplacées vers CallService\n  // pour éviter la duplication de code et centraliser la logique\n\n  // destroy\n  cleanupSubscriptions(): void {\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\n    this.subscriptions = [];\n    if (this.cleanupInterval) {\n      clearInterval(this.cleanupInterval);\n    }\n    this.notificationCache.clear();\n    this.logger.debug('NotificationService destroyed');\n  }\n\n  ngOnDestroy() {\n    this.cleanupSubscriptions();\n  }\n}\n"], "mappings": "AAEA,SACEA,eAAe,EACfC,UAAU,EACVC,EAAE,EAEFC,UAAU,EACVC,KAAK,EACLC,KAAK,QACA,MAAM;AACb,SACEC,GAAG,EACHC,UAAU,EACVC,GAAG,EACHC,MAAM,EAINC,YAAY,EACZC,oBAAoB,EACpBC,WAAW,QACN,gBAAgB;AAEvB,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,SACEC,WAAW,EAEXC,QAAQ,EACRC,UAAU,QAML,yBAAyB;AAChC,SACEC,uBAAuB,EACvBC,uBAAuB,EACvBC,yBAAyB,EACzBC,sBAAsB,EACtBC,qBAAqB,EACrBC,qBAAqB,EACrBC,yBAAyB,EACzBC,wBAAwB,EACxBC,cAAc,EACdC,kBAAkB,EAClBC,iCAAiC,EACjCC,qBAAqB,EACrBC,yBAAyB,EACzBC,wBAAwB,EACxBC,yBAAyB,EACzBC,qBAAqB,EACrBC,oBAAoB,EACpBC,6BAA6B,EAC7BC,sBAAsB,EACtBC,yBAAyB,EACzBC,wBAAwB,EACxBC,oBAAoB,EACpBC,qBAAqB,EACrBC,qBAAqB,EACrBC,qBAAqB,EAGrBC,oBAAoB,EACpBC,eAAe,EACfC,qBAAqB,EACrBC,qBAAqB,EACrBC,uBAAuB,EACvBC,kBAAkB,EAClBC,8BAA8B,EAC9BC,+BAA+B,EAC/BC,+BAA+B,EAC/BC,4BAA4B,EAC5BC,4BAA4B,EAC5BC,sCAAsC,EACtCC,iCAAiC;AACjC;AACAC,kBAAkB,EAClBC,kBAAkB,EAClBC,gBAAgB,EAChBC,sBAAsB,EACtBC,yBAAyB,EAKzBC,wBAAwB,EACxBC,0BAA0B,EAE1BC,wBAAwB,QACnB,4BAA4B;;;;AA0CnC,OAAM,MAAOC,cAAc;EA6DzBC,YACUC,MAAc,EACdC,MAAqB,EACrBC,IAAY;IAFZ,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IA/Dd;IACQ,KAAAC,kBAAkB,GAAG,IAAIpE,eAAe,CAAgB,IAAI,CAAC;IAC7D,KAAAqE,aAAa,GAAG,IAAIrE,eAAe,CAAiB,EAAE,CAAC;IACvD,KAAAsE,iBAAiB,GAAG,IAAIC,GAAG,EAAwB;IAEnD,KAAAC,iBAAiB,GAAG,IAAIxE,eAAe,CAAS,CAAC,CAAC;IAClD,KAAAyE,WAAW,GAAG,IAAIF,GAAG,EAAgB;IACrC,KAAAG,aAAa,GAAmB,EAAE;IACzB,KAAAC,cAAc,GAAG,MAAM;IAChC,KAAAC,aAAa,GAAG,CAAC;IAEzB;IACQ,KAAAC,UAAU,GAAG,IAAI7E,eAAe,CAAc,IAAI,CAAC;IACnD,KAAA8E,YAAY,GAAG,IAAI9E,eAAe,CAAsB,IAAI,CAAC;IAC7D,KAAA+E,WAAW,GAAG,IAAI/E,eAAe,CAAoB,IAAI,CAAC;IAC1D,KAAAgF,WAAW,GAAuB,IAAI;IACtC,KAAAC,YAAY,GAAuB,IAAI;IACvC,KAAAC,cAAc,GAA6B,IAAI;IAEvD;IACO,KAAAC,WAAW,GAAG,IAAI,CAACN,UAAU,CAACO,YAAY,EAAE;IAC5C,KAAAC,aAAa,GAAG,IAAI,CAACP,YAAY,CAACM,YAAY,EAAE;IAChD,KAAAE,YAAY,GAAG,IAAI,CAACP,WAAW,CAACK,YAAY,EAAE;IAC9C,KAAAG,YAAY,GAAG,IAAIvF,eAAe,CAAqB,IAAI,CAAC;IAC5D,KAAAwF,aAAa,GAAG,IAAIxF,eAAe,CAAqB,IAAI,CAAC;IAEpE;IACiB,KAAAyF,SAAS,GAAqB;MAC7CC,UAAU,EAAE,CACV;QAAEC,IAAI,EAAE;MAA8B,CAAE,EACxC;QAAEA,IAAI,EAAE;MAA+B,CAAE;KAE5C;IACO,KAAAC,UAAU,GAAW,EAAE;IAE/B;IACO,KAAAC,qBAAqB,GAMxB;MACFC,UAAU,EAAE,CAAC;MACbC,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC;MACdC,WAAW,EAAE,KAAK;MAClBC,eAAe,EAAE;KAClB;IAED;IACO,KAAAC,mBAAmB,GAAG,IAAI,CAAC/B,kBAAkB,CAACgB,YAAY,EAAE;IAC5D,KAAAgB,cAAc,GAAG,IAAI,CAAC/B,aAAa,CAACe,YAAY,EAAE;IAClD,KAAAiB,kBAAkB,GAAG,IAAI,CAAC7B,iBAAiB,CAACY,YAAY,EAAE;IAEjE;IACQ,KAAAkB,MAAM,GAAwC,EAAE;IAChD,KAAAC,SAAS,GAA+B,EAAE;IAC1C,KAAAC,KAAK,GAAG,KAAK;IAomCrB;IACA;IACA;IACA;IACQ,KAAAC,sBAAsB,GAAG;MAC/BT,WAAW,EAAE,CAAC;MACdU,KAAK,EAAE,EAAE;MACTC,oBAAoB,EAAE;KACvB;IA02CD;IACA;IACA;IACA;IACQ,KAAAC,iBAAiB,GAAG,IAAIrC,GAAG,EAA+B;IAC1D,KAAAsC,oBAAoB,GAAG,IAAItC,GAAG,EAAkB;IAy4BhD,KAAAuC,eAAe,GACrBC,IAA+B,IACT;MACtB,IAAI,CAACA,IAAI,EAAE,OAAOC,SAAS;MAC3B,OAAO,OAAOD,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAGA,IAAI,CAACE,WAAW,EAAE;IAC7D,CAAC;IAl2GC,IAAI,CAACC,iCAAiC,EAAE;IACxC,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,aAAa,EAAE;EACtB;EAEA;;;;EAIQH,iCAAiCA,CAAA;IACvC,IAAI;MACF,MAAMI,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;MAChE,IAAIF,kBAAkB,EAAE;QACtB,MAAMjD,aAAa,GAAGoD,IAAI,CAACC,KAAK,CAACJ,kBAAkB,CAAmB;QAEtE,IAAI,CAAChD,iBAAiB,CAACqD,KAAK,EAAE;QAE9BtD,aAAa,CAACuD,OAAO,CAAEC,YAAY,IAAI;UACrC,IAAIA,YAAY,IAAIA,YAAY,CAACC,EAAE,EAAE;YACnC,IAAI,CAACxD,iBAAiB,CAACyD,GAAG,CAACF,YAAY,CAACC,EAAE,EAAED,YAAY,CAAC;;QAE7D,CAAC,CAAC;QAEF,IAAI,CAACxD,aAAa,CAAC2D,IAAI,CAACC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC5D,iBAAiB,CAAC6D,MAAM,EAAE,CAAC,CAAC;QACpE,IAAI,CAACC,iBAAiB,EAAE;;KAE3B,CAAC,OAAOC,KAAK,EAAE;MACd;IAAA;EAEJ;EACQlB,iBAAiBA,CAAA;IACvB,IAAI,CAAChD,IAAI,CAACmE,iBAAiB,CAAC,MAAK;MAC/B,IAAI,CAACC,2BAA2B,EAAE,CAACC,SAAS,EAAE;MAC9C,IAAI,CAACC,4BAA4B,EAAE,CAACD,SAAS,EAAE;MAC/C,IAAI,CAACE,wBAAwB,EAAE,CAACF,SAAS,EAAE;MAC3C;IACF,CAAC,CAAC;;IACF,IAAI,CAACG,qBAAqB,EAAE;EAC9B;EAEA;;;EAGQD,wBAAwBA,CAAA;IAC9B,OAAO,IAAI,CAACzE,MAAM,CACfuE,SAAS,CAAiC;MACzCI,KAAK,EAAE/E;KACR,CAAC,CACDgF,IAAI,CACHvI,GAAG,CAAC,CAAC;MAAEwI;IAAI,CAAE,KAAI;MACf,IAAI,CAACA,IAAI,EAAEhE,YAAY,EAAE;QACvB,OAAO,IAAI;;MAGb;MACA,IAAI,CAACiE,kBAAkB,CAACD,IAAI,CAAChE,YAAY,CAAC;MAC1C,OAAOgE,IAAI,CAAChE,YAAY;IAC1B,CAAC,CAAC,EACFvE,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC/D,OAAOnI,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH;EACL;EAEA;;;EAGQ6I,kBAAkBA,CAACC,IAAkB;IAC3C,IAAI,CAAClE,YAAY,CAACkD,IAAI,CAACgB,IAAI,CAAC;IAC5B,IAAI,CAACC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;EAC7B;EAEA;EACA;EACA;EAEA;;;EAGAC,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAACjF,MAAM,CACfuE,SAAS,CAAmB;MAC3BI,KAAK,EAAErH;KACR,CAAC,CACDsH,IAAI,CACHvI,GAAG,CAAC,CAAC;MAAEwI;IAAI,CAAE,KAAI;MACf,IAAI,CAACA,IAAI,EAAEK,WAAW,EAAE;QACtB,OAAO,IAAI;;MAEb,OAAO,IAAI,CAACC,gBAAgB,CAACN,IAAI,CAACK,WAAW,CAAC;IAChD,CAAC,CAAC,EACF5I,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACzD,OAAOnI,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAmJ,2BAA2BA,CAAA;IACzB,OAAO,IAAI,CAACpF,MAAM,CACfuE,SAAS,CAAwB;MAChCI,KAAK,EAAE1G;KACR,CAAC,CACD2G,IAAI,CACHvI,GAAG,CAAC,CAAC;MAAEwI;IAAI,CAAE,KAAI;MACf,IAAI,CAACA,IAAI,EAAEQ,eAAe,EAAE;QAC1B,OAAO,IAAI;;MAEb,OAAOR,IAAI,CAACQ,eAAe;IAC7B,CAAC,CAAC,EACF/I,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAClE,OAAOnI,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAqJ,WAAWA,CAACC,cAAsB;IAChC,OAAO,IAAI,CAACvF,MAAM,CACfwF,MAAM,CAAsB;MAC3BC,QAAQ,EAAE1H,qBAAqB;MAC/B2H,SAAS,EAAE;QAAEH;MAAc;KAC5B,CAAC,CACDX,IAAI,CACHvI,GAAG,CAAEsJ,MAAM,IAAKA,MAAM,CAACd,IAAI,EAAES,WAAW,IAAI,KAAK,CAAC,EAClDhJ,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MAC3D,OAAOnI,EAAE,CAAC,KAAK,CAAC;IAClB,CAAC,CAAC,CACH;EACL;EAEA;;;EAGA2J,UAAUA,CAACL,cAAsB;IAC/B,OAAO,IAAI,CAACvF,MAAM,CACfwF,MAAM,CAAqB;MAC1BC,QAAQ,EAAEzH,oBAAoB;MAC9B0H,SAAS,EAAE;QAAEH;MAAc;KAC5B,CAAC,CACDX,IAAI,CACHvI,GAAG,CAAEsJ,MAAM,IAAKA,MAAM,CAACd,IAAI,EAAEe,UAAU,IAAI,KAAK,CAAC,EACjDtJ,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MAC3D,OAAOnI,EAAE,CAAC,KAAK,CAAC;IAClB,CAAC,CAAC,CACH;EACL;EAEA;;;EAGA4J,YAAYA,CAACC,WAAmB,EAAEC,QAAkB;IAClD,OAAO,IAAI,CAAC/F,MAAM,CACfwF,MAAM,CAAyB;MAC9BC,QAAQ,EAAEhG,sBAAsB;MAChCiG,SAAS,EAAE;QAAEI,WAAW;QAAEC;MAAQ;KACnC,CAAC,CACDnB,IAAI,CACHvI,GAAG,CAAEsJ,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACd,IAAI,EAAEgB,YAAY,EAAE;QAC9B,MAAM,IAAIG,KAAK,CAAC,yBAAyB,CAAC;;MAE5C,OAAOL,MAAM,CAACd,IAAI,CAACgB,YAAY;IACjC,CAAC,CAAC,EACFvJ,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,OAAOlI,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC/D,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAC,mBAAmBA,CACjBC,QAAgB,EAChBC,UAAkB,EAClBC,OAAe,EACfC,IAAU;IAEV,OAAO,IAAI,CAACrG,MAAM,CACfwF,MAAM,CAAsB;MAC3BC,QAAQ,EAAErI,qBAAqB;MAC/BsI,SAAS,EAAE;QACTQ,QAAQ;QACRC,UAAU;QACVC,OAAO;QACPC;;KAEH,CAAC,CACDzB,IAAI,CACHvI,GAAG,CAAEsJ,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACd,IAAI,EAAEyB,WAAW,EAAE;QAC7B,MAAM,IAAIN,KAAK,CAAC,wBAAwB,CAAC;;MAE3C,OAAO,IAAI,CAACb,gBAAgB,CAACQ,MAAM,CAACd,IAAI,CAACyB,WAAW,CAAC;IACvD,CAAC,CAAC,EACFhK,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MAC3D,OAAOlI,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,wBAAwB,CAAC,CAAC;IAC9D,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAM,WAAWA,CACTJ,QAAgB,EAChBC,UAAkB,EAClBC,OAAe;IAEf,OAAO,IAAI,CAACpG,MAAM,CACfwF,MAAM,CAAsB;MAC3BC,QAAQ,EAAErI,qBAAqB;MAC/BsI,SAAS,EAAE;QACTQ,QAAQ;QACRC,UAAU;QACVC;;KAEH,CAAC,CACDxB,IAAI,CACHvI,GAAG,CAAEsJ,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACd,IAAI,EAAEyB,WAAW,EAAE;QAC7B,MAAM,IAAIN,KAAK,CAAC,wBAAwB,CAAC;;MAE3C,OAAO,IAAI,CAACb,gBAAgB,CAACQ,MAAM,CAACd,IAAI,CAACyB,WAAW,CAAC;IACvD,CAAC,CAAC,EACFhK,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACjD,OAAOlI,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,wBAAwB,CAAC,CAAC;IAC9D,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAO,wBAAwBA,CAAA;IACtB,OAAO,IAAI,CAACvG,MAAM,CACfuE,SAAS,CAAyC;MACjDI,KAAK,EAAEzH;KACR,CAAC,CACD0H,IAAI,CACHvI,GAAG,CAAC,CAAC;MAAEwI;IAAI,CAAE,KAAI;MACf,IAAI,CAACA,IAAI,EAAE2B,oBAAoB,EAAE;QAC/B,OAAO,IAAI;;MAEb,OAAO3B,IAAI,CAAC2B,oBAAoB;IAClC,CAAC,CAAC,EACFlK,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC9D,OAAOnI,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAwK,sBAAsBA,CAACC,cAAsB;IAC3C,OAAO,IAAI,CAAC1G,MAAM,CACfwF,MAAM,CAAsC;MAC3CC,QAAQ,EAAEzG,+BAA+B;MACzC0G,SAAS,EAAE;QAAEgB;MAAc;KAC5B,CAAC,CACD9B,IAAI,CACHvI,GAAG,CAAEsJ,MAAM,IAAKA,MAAM,CAACd,IAAI,EAAE4B,sBAAsB,IAAI,KAAK,CAAC,EAC7DnK,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC9D,OAAOnI,EAAE,CAAC,KAAK,CAAC;IAClB,CAAC,CAAC,CACH;EACL;EAEA;;;EAGA0K,uBAAuBA,CAACC,MAAc;IACpC,OAAO,IAAI,CAAC5G,MAAM,CACfwF,MAAM,CAAuC;MAC5CC,QAAQ,EAAEvG,4BAA4B;MACtCwG,SAAS,EAAE;QAAEkB;MAAM;KACpB,CAAC,CACDhC,IAAI,CACHvI,GAAG,CAAEsJ,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACd,IAAI,EAAEgC,kBAAkB,EAAE;QACpC,MAAM,IAAIb,KAAK,CAAC,sCAAsC,CAAC;;MAEzD,OAAO,IAAI,CAACc,qBAAqB,CAACnB,MAAM,CAACd,IAAI,CAACgC,kBAAkB,CAAC;IACnE,CAAC,CAAC,EACFvK,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAClE,OAAOlI,UAAU,CACf,MAAM,IAAI8J,KAAK,CAAC,sCAAsC,CAAC,CACxD;IACH,CAAC,CAAC,CACH;EACL;EAEA;EACA;EACA;EAEA;;;EAGQ5C,aAAaA,CAAA;IACnB,IAAI,CAAC2D,SAAS,CAAC,UAAU,EAAE,4BAA4B,CAAC;IACxD,IAAI,CAACA,SAAS,CAAC,UAAU,EAAE,4BAA4B,CAAC;IACxD,IAAI,CAACA,SAAS,CAAC,gBAAgB,EAAE,kCAAkC,CAAC;IACpE,IAAI,CAACA,SAAS,CAAC,cAAc,EAAE,gCAAgC,CAAC;EAClE;EAEA;;;;;EAKQA,SAASA,CAACC,IAAY,EAAEC,IAAY;IAC1C,IAAI;MACF,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC;MAC7BC,KAAK,CAACE,IAAI,EAAE;MACZ,IAAI,CAAC/E,MAAM,CAAC2E,IAAI,CAAC,GAAGE,KAAK;MACzB,IAAI,CAAC5E,SAAS,CAAC0E,IAAI,CAAC,GAAG,KAAK;MAE5BE,KAAK,CAACG,gBAAgB,CAAC,OAAO,EAAE,MAAK;QACnC,IAAI,CAAC/E,SAAS,CAAC0E,IAAI,CAAC,GAAG,KAAK;MAC9B,CAAC,CAAC;KACH,CAAC,OAAO5C,KAAK,EAAE;MACd;IAAA;EAEJ;EAEA;;;;;EAKAY,IAAIA,CAACgC,IAAY,EAAEM,IAAA,GAAgB,KAAK;IACtC,IAAI,IAAI,CAAC/E,KAAK,EAAE;MACd;;IAGF,IAAI;MACF,MAAMgF,KAAK,GAAG,IAAI,CAAClF,MAAM,CAAC2E,IAAI,CAAC;MAC/B,IAAI,CAACO,KAAK,EAAE;QACV;;MAGFA,KAAK,CAACD,IAAI,GAAGA,IAAI;MAEjB,IAAI,CAAC,IAAI,CAAChF,SAAS,CAAC0E,IAAI,CAAC,EAAE;QACzBO,KAAK,CAACC,WAAW,GAAG,CAAC;QACrBD,KAAK,CAACvC,IAAI,EAAE,CAACyC,KAAK,CAAErD,KAAK,IAAI;UAC3B;QAAA,CACD,CAAC;QACF,IAAI,CAAC9B,SAAS,CAAC0E,IAAI,CAAC,GAAG,IAAI;;KAE9B,CAAC,OAAO5C,KAAK,EAAE;MACd;IAAA;EAEJ;EAEA;;;;EAIAsD,IAAIA,CAACV,IAAY;IACf,IAAI;MACF,MAAMO,KAAK,GAAG,IAAI,CAAClF,MAAM,CAAC2E,IAAI,CAAC;MAC/B,IAAI,CAACO,KAAK,EAAE;QACV;;MAGF,IAAI,IAAI,CAACjF,SAAS,CAAC0E,IAAI,CAAC,EAAE;QACxBO,KAAK,CAACI,KAAK,EAAE;QACbJ,KAAK,CAACC,WAAW,GAAG,CAAC;QACrB,IAAI,CAAClF,SAAS,CAAC0E,IAAI,CAAC,GAAG,KAAK;;KAE/B,CAAC,OAAO5C,KAAK,EAAE;MACd;IAAA;EAEJ;EAEA;;;EAGAwD,aAAaA,CAAA;IACXC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACzF,MAAM,CAAC,CAACsB,OAAO,CAAEqD,IAAI,IAAI;MACxC,IAAI,CAACU,IAAI,CAACV,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ;EAEA;;;;EAIAe,QAAQA,CAACxF,KAAc;IACrB,IAAI,CAACA,KAAK,GAAGA,KAAK;IAElB,IAAIA,KAAK,EAAE;MACT,IAAI,CAACqF,aAAa,EAAE;;EAExB;EAEA;;;;EAIAI,OAAOA,CAAA;IACL,OAAO,IAAI,CAACzF,KAAK;EACnB;EAEA;;;EAGA0F,qBAAqBA,CAAA;IACnBC,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;IAE1E,IAAI,IAAI,CAAC5F,KAAK,EAAE;MACd2F,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;MAClE;;IAGF;IACA,IAAI;MACF;MACA,MAAMC,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAC1CD,MAAc,CAACE,kBAAkB,EAAC,CAAE;MAEvC;MAEA;MACA,IAAI,CAACC,uBAAuB,CAACJ,YAAY,CAAC;MAE1C;MACA;MAEA;MACA;MAEA;MACA;MAEA;MACA;MAEAF,OAAO,CAACC,GAAG,CACT,kEAAkE,CACnE;KACF,CAAC,OAAO/D,KAAK,EAAE;MACd8D,OAAO,CAAC9D,KAAK,CACX,sDAAsD,EACtDA,KAAK,CACN;MAED;MACA,IAAI;QACF,MAAM8C,KAAK,GAAG,IAAIC,KAAK,CAAC,gCAAgC,CAAC;QACzDD,KAAK,CAACuB,MAAM,GAAG,GAAG,CAAC,CAAC;QACpBvB,KAAK,CAAClC,IAAI,EAAE,CAACyC,KAAK,CAAEiB,GAAG,IAAI;UACzBR,OAAO,CAAC9D,KAAK,CACX,2DAA2D,EAC3DsE,GAAG,CACJ;QACH,CAAC,CAAC;OACH,CAAC,OAAOC,UAAU,EAAE;QACnBT,OAAO,CAAC9D,KAAK,CACX,8DAA8D,EAC9DuE,UAAU,CACX;;;EAGP;EAEA;EACQH,uBAAuBA,CAACJ,YAA0B;IACxD,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;IAC1D,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;EAC/D;EAEA;EACQS,uBAAuBA,CAACT,YAA0B;IACxD,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;IAC1D,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;IAC7D,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;EAC/D;EAEA;EACQU,uBAAuBA,CAACV,YAA0B;IACxD,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IACtD,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;IAC3D,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;EAC9D;EAEA;EACQW,uBAAuBA,CAACX,YAA0B;IACxD,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;IAC1D,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;IAC7D,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;EAC5D;EAEA;EACQY,uBAAuBA,CAACZ,YAA0B;IACxD,IAAI,CAACa,YAAY,CAACb,YAAY,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;EACnD;EAEA;;;EAGQQ,oBAAoBA,CAC1BR,YAA0B,EAC1Bc,SAAiB,EACjBC,SAAiB,EACjBC,QAAgB;IAEhB,MAAMC,UAAU,GAAGjB,YAAY,CAACkB,gBAAgB,EAAE;IAClD,MAAMC,QAAQ,GAAGnB,YAAY,CAACoB,UAAU,EAAE;IAE1C;IACAH,UAAU,CAACI,IAAI,GAAG,MAAM;IACxBJ,UAAU,CAACF,SAAS,CAACO,cAAc,CACjCP,SAAS,EACTf,YAAY,CAACZ,WAAW,GAAG0B,SAAS,CACrC;IAED;IACAK,QAAQ,CAACI,IAAI,CAACD,cAAc,CAAC,CAAC,EAAEtB,YAAY,CAACZ,WAAW,GAAG0B,SAAS,CAAC;IACrEK,QAAQ,CAACI,IAAI,CAACC,uBAAuB,CACnC,GAAG,EACHxB,YAAY,CAACZ,WAAW,GAAG0B,SAAS,GAAG,IAAI,CAC5C;IACDK,QAAQ,CAACI,IAAI,CAACC,uBAAuB,CACnC,GAAG,EACHxB,YAAY,CAACZ,WAAW,GAAG0B,SAAS,GAAGE,QAAQ,GAAG,GAAG,CACtD;IACDG,QAAQ,CAACI,IAAI,CAACC,uBAAuB,CACnC,CAAC,EACDxB,YAAY,CAACZ,WAAW,GAAG0B,SAAS,GAAGE,QAAQ,CAChD;IAED;IACAC,UAAU,CAACQ,OAAO,CAACN,QAAQ,CAAC;IAC5BA,QAAQ,CAACM,OAAO,CAACzB,YAAY,CAAC0B,WAAW,CAAC;IAE1C;IACAT,UAAU,CAACU,KAAK,CAAC3B,YAAY,CAACZ,WAAW,GAAG0B,SAAS,CAAC;IACtDG,UAAU,CAAC3B,IAAI,CAACU,YAAY,CAACZ,WAAW,GAAG0B,SAAS,GAAGE,QAAQ,CAAC;EAClE;EAEA;;;EAGQH,YAAYA,CAClBb,YAA0B,EAC1Bc,SAAiB,EACjBC,SAAiB,EACjBC,QAAgB;IAEhB,MAAMC,UAAU,GAAGjB,YAAY,CAACkB,gBAAgB,EAAE;IAClD,MAAMC,QAAQ,GAAGnB,YAAY,CAACoB,UAAU,EAAE;IAE1C;IACAH,UAAU,CAACI,IAAI,GAAG,UAAU,CAAC,CAAC;IAC9BJ,UAAU,CAACF,SAAS,CAACO,cAAc,CACjCP,SAAS,EACTf,YAAY,CAACZ,WAAW,GAAG0B,SAAS,CACrC;IAED;IACAK,QAAQ,CAACI,IAAI,CAACD,cAAc,CAAC,CAAC,EAAEtB,YAAY,CAACZ,WAAW,GAAG0B,SAAS,CAAC;IACrEK,QAAQ,CAACI,IAAI,CAACC,uBAAuB,CACnC,GAAG,EACHxB,YAAY,CAACZ,WAAW,GAAG0B,SAAS,GAAG,IAAI,CAC5C;IACDK,QAAQ,CAACI,IAAI,CAACK,4BAA4B,CACxC,IAAI,EACJ5B,YAAY,CAACZ,WAAW,GAAG0B,SAAS,GAAGE,QAAQ,CAChD;IAED;IACAC,UAAU,CAACQ,OAAO,CAACN,QAAQ,CAAC;IAC5BA,QAAQ,CAACM,OAAO,CAACzB,YAAY,CAAC0B,WAAW,CAAC;IAE1C;IACAT,UAAU,CAACU,KAAK,CAAC3B,YAAY,CAACZ,WAAW,GAAG0B,SAAS,CAAC;IACtDG,UAAU,CAAC3B,IAAI,CAACU,YAAY,CAACZ,WAAW,GAAG0B,SAAS,GAAGE,QAAQ,CAAC;EAClE;EACA;EACA;EACA;EAEA;;;;;EAKAa,SAASA,CAACC,QAAgB;IACxB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAMnD,KAAK,GAAG,IAAIC,KAAK,CAAC+C,QAAQ,CAAC;MAEjChD,KAAK,CAACoD,OAAO,GAAG,MAAK;QACnBF,OAAO,EAAE;MACX,CAAC;MAEDlD,KAAK,CAACqD,OAAO,GAAInG,KAAK,IAAI;QACxB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QACjEiG,MAAM,CAACjG,KAAK,CAAC;MACf,CAAC;MAED8C,KAAK,CAAClC,IAAI,EAAE,CAACyC,KAAK,CAAErD,KAAK,IAAI;QAC3B,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QACjEiG,MAAM,CAACjG,KAAK,CAAC;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA;;;;EAIAoG,gBAAgBA,CAAA;IACd,IAAI,CAACvK,MAAM,CAACwK,KAAK,CAAC,yCAAyC,CAAC;IAE5D,OAAO,IAAI,CAACzK,MAAM,CACf0K,UAAU,CAA+B;MACxC/F,KAAK,EAAE9E,wBAAwB;MAC/B8K,WAAW,EAAE,cAAc,CAAE;KAC9B,CAAC,CACDC,YAAY,CAAChG,IAAI,CAChBvI,GAAG,CAAEsJ,MAAM,IAAI;MACb,MAAMkF,aAAa,GAAGlF,MAAM,CAACd,IAAI,EAAE2F,gBAAgB,IAAI,EAAE;MACzD,IAAI,CAACvK,MAAM,CAACwK,KAAK,CACf,8BAA8BI,aAAa,CAACC,MAAM,iBAAiB,CACpE;MACD,OAAOD,aAAa;IACtB,CAAC,CAAC,EACFvO,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,iDAAiD,EACjDA,KAAK,CACN;MACD,OAAOlI,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACtE,CAAC,CAAC,CACH;EACL;EACA;EACA+E,WAAWA,CACT7E,QAAgB,EAChBC,UAAkB,EAClBZ,cAAsB,EACtByF,IAAA,GAAe,CAAC,EAChBvI,KAAA,GAAgB,EAAE,CAAC;EAAA,E;IAEnB,OAAO,IAAI,CAACzC,MAAM,CACf0K,UAAU,CAA6B;MACtC/F,KAAK,EAAE7F,kBAAkB;MACzB4G,SAAS,EAAE;QAAEQ,QAAQ;QAAEC,UAAU;QAAEZ,cAAc;QAAE9C,KAAK;QAAEuI;MAAI,CAAE;MAChEL,WAAW,EAAE,aAAa;MAC1BM,WAAW,EAAE,KAAK,CAAE;KACrB,CAAC,CACDL,YAAY,CAAChG,IAAI,CAChBvI,GAAG,CAAEsJ,MAAM,IAAI;MACb,MAAMuF,QAAQ,GAAGvF,MAAM,CAACd,IAAI,EAAEkG,WAAW,IAAI,EAAE;MAC/C;MACA,OAAO,IAAI,CAACI,sBAAsB,CAACD,QAAQ,CAAC;IAC9C,CAAC,CAAC,EACF5O,UAAU,CAAE8H,KAAK,IAAI;MACnB8D,OAAO,CAAC9D,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAOlI,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAChE,CAAC,CAAC,CACH;EACL;EACAoF,WAAWA,CAACC,SAAiB,EAAEC,UAAkB;IAC/C,OAAO,IAAI,CAACtL,MAAM,CACfwF,MAAM,CAA2B;MAChCC,QAAQ,EAAE7G,qBAAqB;MAC/B8G,SAAS,EAAE;QAAE2F,SAAS;QAAEC;MAAU;KACnC,CAAC,CACD1G,IAAI,CACHvI,GAAG,CAAEsJ,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACd,IAAI,EAAEuG,WAAW,EAAE;QAC7B,MAAM,IAAIpF,KAAK,CAAC,wBAAwB,CAAC;;MAE3C,OAAO,IAAI,CAACb,gBAAgB,CAACQ,MAAM,CAACd,IAAI,CAACuG,WAAW,CAAC;IACvD,CAAC,CAAC,EACF9O,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAClD,OAAOlI,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,wBAAwB,CAAC,CAAC;IAC9D,CAAC,CAAC,CACH;EACL;EAEAuF,aAAaA,CAACF,SAAiB;IAC7B,OAAO,IAAI,CAACrL,MAAM,CACfwF,MAAM,CAA6B;MAClCC,QAAQ,EAAE5G,uBAAuB;MACjC6G,SAAS,EAAE;QAAE2F;MAAS;KACvB,CAAC,CACDzG,IAAI,CACHvI,GAAG,CAAEsJ,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACd,IAAI,EAAE0G,aAAa,EAAE;QAC/B,MAAM,IAAIvF,KAAK,CAAC,0BAA0B,CAAC;;MAE7C,OAAO,IAAI,CAACb,gBAAgB,CAACQ,MAAM,CAACd,IAAI,CAAC0G,aAAa,CAAC;IACzD,CAAC,CAAC,EACFjP,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACnD,OAAOlI,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAChE,CAAC,CAAC,CACH;EACL;EAEAwF,iBAAiBA,CAACH,SAAiB;IACjC,OAAO,IAAI,CAACrL,MAAM,CACfwF,MAAM,CAAqB;MAC1BC,QAAQ,EAAEpI,qBAAqB;MAC/BqI,SAAS,EAAE;QAAE2F;MAAS;KACvB,CAAC,CACDzG,IAAI,CACHvI,GAAG,CAAEsJ,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACd,IAAI,EAAE2G,iBAAiB,EACjC,MAAM,IAAIxF,KAAK,CAAC,gCAAgC,CAAC;MACnD,OAAO;QACL,GAAGL,MAAM,CAACd,IAAI,CAAC2G,iBAAiB;QAChCC,MAAM,EAAE,IAAIC,IAAI;OACjB;IACH,CAAC,CAAC,EACFpP,UAAU,CAAE8H,KAAK,IAAI;MACnB8D,OAAO,CAAC9D,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,OAAOlI,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACtE,CAAC,CAAC,CACH;EACL;EAEA2F,cAAcA,CAACN,SAAiB,EAAEO,KAAa;IAC7C,OAAO,IAAI,CAAC5L,MAAM,CACfwF,MAAM,CAAyB;MAC9BC,QAAQ,EAAEtH,yBAAyB;MACnCuH,SAAS,EAAE;QAAE2F,SAAS;QAAEO;MAAK;KAC9B,CAAC,CACDhH,IAAI,CACHvI,GAAG,CAAEsJ,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACd,IAAI,EAAE8G,cAAc,EAC9B,MAAM,IAAI3F,KAAK,CAAC,4BAA4B,CAAC;MAC/C,OAAOL,MAAM,CAACd,IAAI,CAAC8G,cAAc;IACnC,CAAC,CAAC,EACFrP,UAAU,CAAE8H,KAAK,IAAI;MACnB8D,OAAO,CAAC9D,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,OAAOlI,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,CACH;EACL;EAEA6F,cAAcA,CACZR,SAAiB,EACjBS,eAAyB;IAEzB,OAAO,IAAI,CAAC9L,MAAM,CACfwF,MAAM,CAAyB;MAC9BC,QAAQ,EAAErH,wBAAwB;MAClCsH,SAAS,EAAE;QAAE2F,SAAS;QAAES;MAAe;KACxC,CAAC,CACDlH,IAAI,CACHvI,GAAG,CAAEsJ,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACd,IAAI,EAAEgH,cAAc,EAC9B,MAAM,IAAI7F,KAAK,CAAC,2BAA2B,CAAC;MAC9C,OAAOL,MAAM,CAACd,IAAI,CAACgH,cAAc,CAACxP,GAAG,CAAE0P,GAAG,KAAM;QAC9C,GAAGA,GAAG;QACNC,SAAS,EAAED,GAAG,CAACC,SAAS,GACpB,IAAI,CAACC,aAAa,CAACF,GAAG,CAACC,SAAS,CAAC,GACjC,IAAIN,IAAI;OACb,CAAC,CAAC;IACL,CAAC,CAAC,EACFpP,UAAU,CAAE8H,KAAK,IAAI;MACnB8D,OAAO,CAAC9D,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAOlI,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,2BAA2B,CAAC,CAAC;IACjE,CAAC,CAAC,CACH;EACL;EAEAkG,UAAUA,CAACb,SAAiB,EAAE9F,cAAsB;IAClD,OAAO,IAAI,CAACvF,MAAM,CACfwF,MAAM,CAAqB;MAC1BC,QAAQ,EAAEpH,oBAAoB;MAC9BqH,SAAS,EAAE;QAAE2F,SAAS;QAAE9F;MAAc;KACvC,CAAC,CACDX,IAAI,CACHvI,GAAG,CAAEsJ,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACd,IAAI,EAAEqH,UAAU,EAC1B,MAAM,IAAIlG,KAAK,CAAC,uBAAuB,CAAC;MAC1C,OAAO;QACL,GAAGL,MAAM,CAACd,IAAI,CAACqH,UAAU;QACzBC,QAAQ,EAAE,IAAIT,IAAI;OACnB;IACH,CAAC,CAAC,EACFpP,UAAU,CAAE8H,KAAK,IAAI;MACnB8D,OAAO,CAAC9D,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAOlI,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,uBAAuB,CAAC,CAAC;IAC7D,CAAC,CAAC,CACH;EACL;EAEAoG,cAAcA,CACZzH,KAAa,EACbY,cAAuB,EACvB8G,OAAA,GAAyB,EAAE;IAE3B,OAAO,IAAI,CAACrM,MAAM,CACf0K,UAAU,CAAyB;MAClC/F,KAAK,EAAEhH,qBAAqB;MAC5B+H,SAAS,EAAE;QACTf,KAAK;QACLY,cAAc;QACd,GAAG8G,OAAO;QACVC,QAAQ,EAAE,IAAI,CAACzJ,eAAe,CAACwJ,OAAO,CAACC,QAAQ,CAAC;QAChDC,MAAM,EAAE,IAAI,CAAC1J,eAAe,CAACwJ,OAAO,CAACE,MAAM;OAC5C;MACD5B,WAAW,EAAE,aAAa;MAC1BM,WAAW,EAAE;KACd,CAAC,CACDL,YAAY,CAAChG,IAAI,CAChBvI,GAAG,CACAsJ,MAAM,IACLA,MAAM,CAACd,IAAI,EAAEuH,cAAc,EAAE/P,GAAG,CAAE0P,GAAG,KAAM;MACzC,GAAGA,GAAG;MACNC,SAAS,EAAE,IAAI,CAACQ,QAAQ,CAACT,GAAG,CAACC,SAAS,CAAC;MACvCS,MAAM,EAAE,IAAI,CAACC,aAAa,CAACX,GAAG,CAACU,MAAM;KACtC,CAAC,CAAC,IAAI,EAAE,CACZ,EACDnQ,UAAU,CAAE8H,KAAK,IAAI;MACnB8D,OAAO,CAAC9D,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAOlI,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,2BAA2B,CAAC,CAAC;IACjE,CAAC,CAAC,CACH;EACL;EAEA;EACQmF,sBAAsBA,CAACD,QAAe;IAC5C,IAAI,CAACA,QAAQ,IAAIA,QAAQ,CAACJ,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAEjD,OAAOI,QAAQ,CAAC7O,GAAG,CAAE0P,GAAG,IAAI;MAC1B,IAAI;QACF,OAAO,IAAI,CAAC5G,gBAAgB,CAAC4G,GAAG,CAAC;OAClC,CAAC,OAAO3H,KAAK,EAAE;QACd8D,OAAO,CAAC9D,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD;QACA,OAAO;UACLP,EAAE,EAAEkI,GAAG,CAAClI,EAAE,IAAIkI,GAAG,CAACY,GAAG,IAAI,QAAQjB,IAAI,CAACkB,GAAG,EAAE,EAAE;UAC7CxG,OAAO,EAAE2F,GAAG,CAAC3F,OAAO,IAAI,EAAE;UAC1BqD,IAAI,EAAEsC,GAAG,CAACtC,IAAI,IAAI5M,WAAW,CAACgQ,IAAI;UAClCb,SAAS,EAAE,IAAI,CAACQ,QAAQ,CAACT,GAAG,CAACC,SAAS,CAAC;UACvCc,MAAM,EAAE,KAAK;UACbL,MAAM,EAAEV,GAAG,CAACU,MAAM,GACd,IAAI,CAACC,aAAa,CAACX,GAAG,CAACU,MAAM,CAAC,GAC9B;YACE5I,EAAE,EAAE,IAAI,CAACkJ,gBAAgB,EAAE;YAC3BC,QAAQ,EAAE;;SAEN;;IAEhB,CAAC,CAAC;EACJ;EAEAC,iBAAiBA,CAACrG,MAAc;IAC9B,OAAO,IAAI,CAAC5G,MAAM,CACf0K,UAAU,CAA4B;MACrC/F,KAAK,EAAE/G,yBAAyB;MAChC8H,SAAS,EAAE;QAAEkB;MAAM,CAAE;MACrB+D,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAChG,IAAI,CAChBvI,GAAG,CACAsJ,MAAM,IACLA,MAAM,CAACd,IAAI,EAAEoI,iBAAiB,EAAE5Q,GAAG,CAAE0P,GAAG,KAAM;MAC5C,GAAGA,GAAG;MACNC,SAAS,EAAE,IAAI,CAACQ,QAAQ,CAACT,GAAG,CAACC,SAAS,CAAC;MACvCS,MAAM,EAAE,IAAI,CAACC,aAAa,CAACX,GAAG,CAACU,MAAM;KACtC,CAAC,CAAC,IAAI,EAAE,CACZ,EACDnQ,UAAU,CAAE8H,KAAK,IAAI;MACnB8D,OAAO,CAAC9D,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,OAAOlI,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEAkH,qBAAqBA,CAAC3H,cAAsB;IAC1C,IAAI,CAACpF,kBAAkB,CAAC4D,IAAI,CAACwB,cAAc,CAAC;EAC9C;EAEA4H,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACnN,MAAM,CACf0K,UAAU,CAA2B;MACpC/F,KAAK,EAAE3H,uBAAuB;MAC9B2N,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAChG,IAAI,CAChBvI,GAAG,CAAEsJ,MAAM,IAAI;MACb,MAAMyH,aAAa,GAAGzH,MAAM,CAACd,IAAI,EAAEsI,gBAAgB,IAAI,EAAE;MACzD,OAAOC,aAAa,CAAC/Q,GAAG,CAAEgR,IAAI,IAAK,IAAI,CAACvG,qBAAqB,CAACuG,IAAI,CAAC,CAAC;IACtE,CAAC,CAAC,EACF/Q,UAAU,CAAE8H,KAAK,IAAI;MACnB8D,OAAO,CAAC9D,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAOlI,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EAEAsH,eAAeA,CACb/H,cAAsB,EACtB9C,KAAc,EACduI,IAAa;IAEb,IAAI,CAAC/K,MAAM,CAACsN,IAAI,CACd,0CAA0ChI,cAAc,YAAY9C,KAAK,WAAWuI,IAAI,EAAE,CAC3F;IAED,MAAMtF,SAAS,GAAQ;MAAEH;IAAc,CAAE;IAEzC;IACA,IAAI9C,KAAK,KAAKM,SAAS,EAAE;MACvB2C,SAAS,CAACjD,KAAK,GAAGA,KAAK;KACxB,MAAM;MACLiD,SAAS,CAACjD,KAAK,GAAG,EAAE,CAAC,CAAC;;IAGxB;IACA,IAAIuI,IAAI,KAAKjI,SAAS,EAAE;MACtB;MACA,MAAMyK,MAAM,GAAG,CAACxC,IAAI,GAAG,CAAC,IAAItF,SAAS,CAACjD,KAAK;MAC3CiD,SAAS,CAAC8H,MAAM,GAAGA,MAAM;MACzB,IAAI,CAACvN,MAAM,CAACwK,KAAK,CACf,uCAAuC+C,MAAM,eAAexC,IAAI,eAAetF,SAAS,CAACjD,KAAK,EAAE,CACjG;KACF,MAAM;MACLiD,SAAS,CAAC8H,MAAM,GAAG,CAAC,CAAC,CAAC;;;IAGxB,IAAI,CAACvN,MAAM,CAACwK,KAAK,CACf,uDAAuD/E,SAAS,CAACjD,KAAK,YAAYiD,SAAS,CAAC8H,MAAM,EAAE,CACrG;IAED,OAAO,IAAI,CAACxN,MAAM,CACf0K,UAAU,CAA0B;MACnC/F,KAAK,EAAExH,sBAAsB;MAC7BuI,SAAS,EAAEA,SAAS;MACpBiF,WAAW,EAAE,cAAc;MAC3BM,WAAW,EAAE;KACd,CAAC,CACDL,YAAY,CAAChG,IAAI,CAChBzI,KAAK,CAAC,CAAC,CAAC;IAAE;IACVE,GAAG,CAAEsJ,MAAM,IAAI;MACb,IAAI,CAAC1F,MAAM,CAACwK,KAAK,CACf,kDAAkD,EAClD9E,MAAM,CACP;MAED,MAAM0H,IAAI,GAAG1H,MAAM,CAACd,IAAI,EAAEyI,eAAe;MACzC,IAAI,CAACD,IAAI,EAAE;QACT,IAAI,CAACpN,MAAM,CAACmE,KAAK,CACf,4CAA4CmB,cAAc,EAAE,CAC7D;QACD,MAAM,IAAIS,KAAK,CAAC,wBAAwB,CAAC;;MAG3C,IAAI,CAAC/F,MAAM,CAACwK,KAAK,CACf,8CAA8ClF,cAAc,EAAE,CAC/D;MACD,MAAMkI,sBAAsB,GAAG,IAAI,CAAC3G,qBAAqB,CAACuG,IAAI,CAAC;MAE/D,IAAI,CAACpN,MAAM,CAACsN,IAAI,CACd,sDAAsDhI,cAAc,mBAClEkI,sBAAsB,CAACC,YAAY,EAAE5C,MAAM,IAAI,CACjD,eAAe2C,sBAAsB,CAACvC,QAAQ,EAAEJ,MAAM,IAAI,CAAC,EAAE,CAC9D;MACD,OAAO2C,sBAAsB;IAC/B,CAAC,CAAC,EACFnR,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,+CAA+C,EAC/CA,KAAK,CACN;MACD,OAAOlI,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,6BAA6B,CAAC,CAAC;IACnE,CAAC,CAAC,CACH;EACL;EAEAa,kBAAkBA,CAACD,MAAc;IAC/B,IAAI,CAAC3G,MAAM,CAACsN,IAAI,CACd,qDAAqD3G,MAAM,EAAE,CAC9D;IAED,IAAI,CAACA,MAAM,EAAE;MACX,IAAI,CAAC3G,MAAM,CAACmE,KAAK,CACf,kEAAkE,CACnE;MACD,OAAOlI,UAAU,CACf,MAAM,IAAI8J,KAAK,CAAC,8CAA8C,CAAC,CAChE;;IAGH,OAAO,IAAI,CAAChG,MAAM,CACfwF,MAAM,CAAuC;MAC5CC,QAAQ,EAAEvG,4BAA4B;MACtCwG,SAAS,EAAE;QAAEkB;MAAM;KACpB,CAAC,CACDhC,IAAI,CACHvI,GAAG,CAAEsJ,MAAM,IAAI;MACb,IAAI,CAAC1F,MAAM,CAACwK,KAAK,CACf,kDAAkD,EAClD9E,MAAM,CACP;MAED,MAAMgI,YAAY,GAAGhI,MAAM,CAACd,IAAI,EAAEgC,kBAAkB;MACpD,IAAI,CAAC8G,YAAY,EAAE;QACjB,IAAI,CAAC1N,MAAM,CAACmE,KAAK,CACf,6DAA6DwC,MAAM,EAAE,CACtE;QACD,MAAM,IAAIZ,KAAK,CAAC,+BAA+B,CAAC;;MAGlD,IAAI;QACF,MAAMyH,sBAAsB,GAC1B,IAAI,CAAC3G,qBAAqB,CAAC6G,YAAY,CAAC;QAC1C,IAAI,CAAC1N,MAAM,CAACsN,IAAI,CACd,uDAAuDE,sBAAsB,CAAC5J,EAAE,EAAE,CACnF;QACD,OAAO4J,sBAAsB;OAC9B,CAAC,OAAOrJ,KAAK,EAAE;QACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,0DAA0D,EAC1DA,KAAK,CACN;QACD,MAAM,IAAI4B,KAAK,CAAC,uCAAuC,CAAC;;IAE5D,CAAC,CAAC,EACF1J,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,0DAA0DwC,MAAM,GAAG,EACnExC,KAAK,CACN;MACD,OAAOlI,UAAU,CACf,MAAM,IAAI8J,KAAK,CAAC,kCAAkC5B,KAAK,CAACwJ,OAAO,EAAE,CAAC,CACnE;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKAC,uBAAuBA,CAACjH,MAAc;IACpC,IAAI,CAAC3G,MAAM,CAACsN,IAAI,CACd,gEAAgE3G,MAAM,EAAE,CACzE;IAED,IAAI,CAACA,MAAM,EAAE;MACX,IAAI,CAAC3G,MAAM,CAACmE,KAAK,CACf,sEAAsE,CACvE;MACD,OAAOlI,UAAU,CACf,MAAM,IAAI8J,KAAK,CAAC,kDAAkD,CAAC,CACpE;;IAGH;IACA,OAAO,IAAI,CAACmH,gBAAgB,EAAE,CAACvI,IAAI,CACjCvI,GAAG,CAAE+Q,aAAa,IAAI;MACpB;MACA,MAAMU,aAAa,GAAG,IAAI,CAACf,gBAAgB,EAAE;MAE7C;MACA,MAAMgB,oBAAoB,GAAGX,aAAa,CAACY,IAAI,CAAEX,IAAI,IAAI;QACvD,IAAIA,IAAI,CAACY,OAAO,EAAE,OAAO,KAAK;QAE9B;QACA,MAAMC,cAAc,GAClBb,IAAI,CAACK,YAAY,EAAErR,GAAG,CAAE8R,CAAC,IAAKA,CAAC,CAACtK,EAAE,IAAIsK,CAAC,CAACxB,GAAG,CAAC,IAAI,EAAE;QACpD,OACEuB,cAAc,CAACE,QAAQ,CAACxH,MAAM,CAAC,IAC/BsH,cAAc,CAACE,QAAQ,CAACN,aAAa,CAAC;MAE1C,CAAC,CAAC;MAEF,IAAIC,oBAAoB,EAAE;QACxB,IAAI,CAAC9N,MAAM,CAACsN,IAAI,CACd,iDAAiDQ,oBAAoB,CAAClK,EAAE,EAAE,CAC3E;QACD,OAAOkK,oBAAoB;;MAG7B;MACA,MAAM,IAAI/H,KAAK,CAAC,gCAAgC,CAAC;IACnD,CAAC,CAAC,EACF1J,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACsN,IAAI,CACd,sEAAsEnJ,KAAK,CAACwJ,OAAO,EAAE,CACtF;MACD,OAAO,IAAI,CAAC/G,kBAAkB,CAACD,MAAM,CAAC;IACxC,CAAC,CAAC,CACH;EACH;EAYAyH,gBAAgBA,CACdC,OAAO,GAAG,KAAK,EACftD,IAAI,GAAG,CAAC,EACRvI,KAAK,GAAG,EAAE;IAEV,IAAI,CAACxC,MAAM,CAACsN,IAAI,CACd,gBAAgB,EAChB,oCAAoCe,OAAO,WAAWtD,IAAI,YAAYvI,KAAK,EAAE,CAC9E;IACD,IAAI,CAACxC,MAAM,CAACwK,KAAK,CAAC,gBAAgB,EAAE,aAAa,EAAE;MACjD9F,KAAK,EAAE1H;KACR,CAAC;IAEF;IACA;IACA,IAAIqR,OAAO,EAAE;MACX,IAAI,CAACrO,MAAM,CAACwK,KAAK,CACf,gBAAgB,EAChB,qCAAqC,CACtC;MACD,IAAI,CAACjI,sBAAsB,CAACT,WAAW,GAAG,CAAC;MAC3C,IAAI,CAACS,sBAAsB,CAACE,oBAAoB,GAAG,IAAI;;IAGzD;IACA,IAAI,CAACF,sBAAsB,CAACT,WAAW,GAAGiJ,IAAI;IAC9C,IAAI,CAACxI,sBAAsB,CAACC,KAAK,GAAGA,KAAK;IAEzC;IACA,MAAM8L,sBAAsB,GAAG,IAAI,CAACC,yBAAyB,EAAE;IAC/D,IAAI,CAACvO,MAAM,CAACwK,KAAK,CACf,gBAAgB,EAChB,SAAS8D,sBAAsB,CAACE,IAAI,2CAA2C,CAChF;IAED,OAAO,IAAI,CAACzO,MAAM,CACf0K,UAAU,CAA+B;MACxC/F,KAAK,EAAE1H,uBAAuB;MAC9ByI,SAAS,EAAE;QACTsF,IAAI,EAAEA,IAAI;QACVvI,KAAK,EAAEA;OACR;MACDkI,WAAW,EAAE2D,OAAO,GAAG,cAAc,GAAG;KACzC,CAAC,CACD1D,YAAY,CAAChG,IAAI,CAChBvI,GAAG,CAAEsJ,MAAM,IAAI;MACb,IAAI,CAAC1F,MAAM,CAACwK,KAAK,CACf,gBAAgB,EAChB,iCAAiC,CAClC;MAED,IAAI9E,MAAM,CAAC+I,MAAM,EAAE;QACjB,IAAI,CAACzO,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iBAAiB,EACjBuB,MAAM,CAAC+I,MAAM,CACd;QACD,MAAM,IAAI1I,KAAK,CAACL,MAAM,CAAC+I,MAAM,CAACrS,GAAG,CAAEsS,CAAC,IAAKA,CAAC,CAACf,OAAO,CAAC,CAACgB,IAAI,CAAC,IAAI,CAAC,CAAC;;MAGjE,MAAMxO,aAAa,GAAGuF,MAAM,CAACd,IAAI,EAAEgK,oBAAoB,IAAI,EAAE;MAC7D,IAAI,CAAC5O,MAAM,CAACwK,KAAK,CACf,gBAAgB,EAChB,YAAYrK,aAAa,CAAC0K,MAAM,uCAAuCE,IAAI,EAAE,CAC9E;MAED;MACA,IAAI,CAACxI,sBAAsB,CAACE,oBAAoB,GAC9CtC,aAAa,CAAC0K,MAAM,IAAIrI,KAAK;MAE/B,IAAIrC,aAAa,CAAC0K,MAAM,KAAK,CAAC,EAAE;QAC9B,IAAI,CAAC7K,MAAM,CAACsN,IAAI,CACd,gBAAgB,EAChB,uCAAuC,CACxC;QACD,IAAI,CAAC/K,sBAAsB,CAACE,oBAAoB,GAAG,KAAK;;MAG1D;MACA,MAAMoM,qBAAqB,GAAG1O,aAAa,CAAC5D,MAAM,CAC/CuS,KAAK,IAAK,CAACR,sBAAsB,CAACS,GAAG,CAACD,KAAK,CAAClL,EAAE,CAAC,CACjD;MAED,IAAI,CAAC5D,MAAM,CAACwK,KAAK,CACf,gBAAgB,EAChB,gBACErK,aAAa,CAAC0K,MAAM,GAAGgE,qBAAqB,CAAChE,MAC/C,wBAAwB,CACzB;MAED;MACAgE,qBAAqB,CAACnL,OAAO,CAAC,CAACoL,KAAK,EAAEE,KAAK,KAAI;QAC7C/G,OAAO,CAACC,GAAG,CAAC,gBAAgB8G,KAAK,GAAG,CAAC,UAAUjE,IAAI,IAAI,EAAE;UACvDnH,EAAE,EAAEkL,KAAK,CAAClL,EAAE,IAAKkL,KAAa,CAACpC,GAAG;UAClClD,IAAI,EAAEsF,KAAK,CAACtF,IAAI;UAChBrD,OAAO,EAAE2I,KAAK,CAAC3I,OAAO;UACtB0G,MAAM,EAAEiC,KAAK,CAACjC;SACf,CAAC;MACJ,CAAC,CAAC;MAEF;MACA;MACA,IAAI,CAACoC,WAAW,CAACJ,qBAAqB,CAAC;MAEvC;MACA,MAAMK,mBAAmB,GAAGnL,KAAK,CAACC,IAAI,CACpC,IAAI,CAAC5D,iBAAiB,CAAC6D,MAAM,EAAE,CAChC;MAED;MACA,MAAMkL,mBAAmB,GACvB,IAAI,CAACC,uBAAuB,CAACF,mBAAmB,CAAC;MAEnDjH,OAAO,CAACC,GAAG,CACT,cAAciH,mBAAmB,CAACtE,MAAM,kDAAkD,CAC3F;MAED;MACA,IAAI,CAAC1K,aAAa,CAAC2D,IAAI,CAACqL,mBAAmB,CAAC;MAE5C;MACA,IAAI,CAACjL,iBAAiB,EAAE;MAExB;MACA,IAAI,CAACmL,+BAA+B,EAAE;MAEtC,OAAOH,mBAAmB;IAC5B,CAAC,CAAC,EACF7S,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,8BAA8B,EAC9BA,KAAK,CACN;MAED,IAAIA,KAAK,CAACmL,aAAa,EAAE;QACvB,IAAI,CAACtP,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iBAAiB,EACjBA,KAAK,CAACmL,aAAa,CACpB;;MAGH,IAAInL,KAAK,CAACoL,YAAY,EAAE;QACtB,IAAI,CAACvP,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,gBAAgB,EAChBA,KAAK,CAACoL,YAAY,CACnB;;MAGH,OAAOtT,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKQwI,yBAAyBA,CAAA;IAC/B,IAAI;MACF,MAAMiB,UAAU,GAAG,IAAIC,GAAG,EAAU;MACpC,MAAMrM,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;MAEhE;MACA,IAAI,CAACF,kBAAkB,EAAE;QACvB,OAAOoM,UAAU;;MAGnB;MACA,MAAME,oBAAoB,GAAG,IAAID,GAAG,CAClClM,IAAI,CAACC,KAAK,CAACJ,kBAAkB,CAAC,CAAChH,GAAG,CAAEuT,CAAe,IAAKA,CAAC,CAAC/L,EAAE,CAAC,CAC9D;MAED;MACA,MAAMgM,mBAAmB,GACvB,IAAI,CAAC7P,MAAM,CAAC8P,MAAM,CAACC,SAAS,CAA+B;QACzDpL,KAAK,EAAE1H;OACR,CAAC,EAAE4R,oBAAoB,IAAI,EAAE;MAEhC;MACAgB,mBAAmB,CAAClM,OAAO,CAAEC,YAAY,IAAI;QAC3C,IAAI,CAAC+L,oBAAoB,CAACX,GAAG,CAACpL,YAAY,CAACC,EAAE,CAAC,EAAE;UAC9C4L,UAAU,CAACO,GAAG,CAACpM,YAAY,CAACC,EAAE,CAAC;;MAEnC,CAAC,CAAC;MAEF,OAAO4L,UAAU;KAClB,CAAC,OAAOrL,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,qEAAqE,EACrEA,KAAK,CACN;MACD,OAAO,IAAIsL,GAAG,EAAU;;EAE5B;EAEA;EACAhN,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACF,sBAAsB,CAACE,oBAAoB;EACzD;EAEA;EACAuN,qBAAqBA,CAAA;IACnB,MAAMC,QAAQ,GAAG,IAAI,CAAC1N,sBAAsB,CAACT,WAAW,GAAG,CAAC;IAC5D,OAAO,IAAI,CAACsM,gBAAgB,CAC1B,KAAK,EACL6B,QAAQ,EACR,IAAI,CAAC1N,sBAAsB,CAACC,KAAK,CAClC;EACH;EACA0N,mBAAmBA,CAACtM,EAAU;IAC5B,OAAO,IAAI,CAAC1B,cAAc,CAACyC,IAAI,CAC7BvI,GAAG,CAAE+D,aAAa,IAAKA,aAAa,CAAC4N,IAAI,CAAE4B,CAAC,IAAKA,CAAC,CAAC/L,EAAE,KAAKA,EAAE,CAAC,CAAC,EAC9DvH,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACvD,OAAOlI,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,6BAA6B,CAAC,CAAC;IACnE,CAAC,CAAC,CACH;EACH;EACAoK,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAChQ,aAAa,CAACiQ,KAAK,EAAEvF,MAAM,IAAI,CAAC;EAC9C;EACAwF,0BAA0BA,CAAC5J,cAAsB;IAC/C,OAAO,IAAI,CAAC1G,MAAM,CACf2E,KAAK,CAAkC;MACtCA,KAAK,EAAE5F,8BAA8B;MACrC2G,SAAS,EAAE;QAAE7B,EAAE,EAAE6C;MAAc,CAAE;MACjCiE,WAAW,EAAE;KACd,CAAC,CACD/F,IAAI,CACHvI,GAAG,CAAEsJ,MAAM,IAAKA,MAAM,CAACd,IAAI,EAAEyL,0BAA0B,IAAI,EAAE,CAAC,EAC9DhU,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MACpE,OAAOlI,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,6BAA6B,CAAC,CAAC;IACnE,CAAC,CAAC,CACH;EACL;EACAuK,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAACpO,cAAc,CAACyC,IAAI,CAC7BvI,GAAG,CAAE+D,aAAa,IAAKA,aAAa,CAAC5D,MAAM,CAAEoT,CAAC,IAAK,CAACA,CAAC,CAAC9C,MAAM,CAAC,CAAC,CAC/D;EACH;EAEA;;;;;EAKA0D,kBAAkBA,CAChB9J,cAAsB;IAEtB,IAAI,CAACzG,MAAM,CAACwK,KAAK,CACf,gBAAgB,EAChB,kCAAkC/D,cAAc,EAAE,CACnD;IAED,IAAI,CAACA,cAAc,EAAE;MACnB,IAAI,CAACzG,MAAM,CAACwQ,IAAI,CAAC,gBAAgB,EAAE,6BAA6B,CAAC;MACjE,OAAOvU,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,6BAA6B,CAAC,CAAC;;IAGnE;IACA,MAAM0K,YAAY,GAAG,IAAI,CAACC,4BAA4B,CAAC,CAACjK,cAAc,CAAC,CAAC;IAExE;IACA,OAAO,IAAI,CAAC1G,MAAM,CACfwF,MAAM,CAAgE;MACrEC,QAAQ,EAAEtG,4BAA4B;MACtCuG,SAAS,EAAE;QAAEgB;MAAc;KAC5B,CAAC,CACD9B,IAAI,CACHvI,GAAG,CAAEsJ,MAAM,IAAI;MACb,MAAMiL,QAAQ,GAAGjL,MAAM,CAACd,IAAI,EAAE2L,kBAAkB;MAChD,IAAI,CAACI,QAAQ,EAAE;QACb,MAAM,IAAI5K,KAAK,CAAC,iCAAiC,CAAC;;MAGpD,IAAI,CAAC/F,MAAM,CAACwK,KAAK,CACf,gBAAgB,EAChB,6BAA6B,EAC7BmG,QAAQ,CACT;MAED,OAAOA,QAAQ;IACjB,CAAC,CAAC,EACFtU,UAAU,CAAE8H,KAAK,IACf,IAAI,CAACyM,mBAAmB,CAACzM,KAAK,EAAE,mCAAmC,EAAE;MACnE0M,OAAO,EAAE,IAAI;MACblD,OAAO,EAAE;KACV,CAAC,CACH,CACF;EACL;EAEA;;;;EAIQ0B,+BAA+BA,CAAA;IACrC,IAAI;MACF,MAAMlP,aAAa,GAAG4D,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC5D,iBAAiB,CAAC6D,MAAM,EAAE,CAAC;MACjEZ,YAAY,CAACyN,OAAO,CAAC,eAAe,EAAEvN,IAAI,CAACwN,SAAS,CAAC5Q,aAAa,CAAC,CAAC;MACpE,IAAI,CAACH,MAAM,CAACwK,KAAK,CACf,gBAAgB,EAChB,uCAAuC,CACxC;KACF,CAAC,OAAOrG,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iDAAiD,EACjDA,KAAK,CACN;;EAEL;EAEA;;;;EAIA6M,sBAAsBA,CAAA;IAKpB,IAAI,CAAChR,MAAM,CAACwK,KAAK,CACf,gBAAgB,EAChB,yCAAyC,CAC1C;IAED;IACA,MAAMyG,KAAK,GAAG,IAAI,CAAC7Q,iBAAiB,CAACoO,IAAI;IACzC,MAAM0C,kBAAkB,GAAGnN,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC5D,iBAAiB,CAACyH,IAAI,EAAE,CAAC;IACpE,IAAI,CAAC6I,4BAA4B,CAACQ,kBAAkB,CAAC;IAErD;IACA,OAAO,IAAI,CAACnR,MAAM,CACfwF,MAAM,CAMJ;MACDC,QAAQ,EAAEpG;KACX,CAAC,CACDuF,IAAI,CACHvI,GAAG,CAAEsJ,MAAM,IAAI;MACb,MAAMiL,QAAQ,GAAGjL,MAAM,CAACd,IAAI,EAAEoM,sBAAsB;MACpD,IAAI,CAACL,QAAQ,EAAE;QACb,MAAM,IAAI5K,KAAK,CAAC,iCAAiC,CAAC;;MAGpD,IAAI,CAAC/F,MAAM,CAACwK,KAAK,CACf,gBAAgB,EAChB,yDAAyD,EACzDmG,QAAQ,CACT;MAED,OAAOA,QAAQ;IACjB,CAAC,CAAC,EACFtU,UAAU,CAAE8H,KAAK,IACf,IAAI,CAACyM,mBAAmB,CACtBzM,KAAK,EACL,4CAA4C,EAC5C;MACE0M,OAAO,EAAE,IAAI;MACbI,KAAK;MACLtD,OAAO,EAAE,GAAGsD,KAAK;KAClB,CACF,CACF,CACF;EACL;EAEA;;;;;EAKAE,2BAA2BA,CACzBC,eAAyB;IAEzB,IAAI,CAACpR,MAAM,CAACwK,KAAK,CACf,gBAAgB,EAChB,kBAAkB4G,eAAe,CAACvG,MAAM,gBAAgB,CACzD;IAED,IAAI,CAACuG,eAAe,IAAIA,eAAe,CAACvG,MAAM,KAAK,CAAC,EAAE;MACpD,IAAI,CAAC7K,MAAM,CAACwQ,IAAI,CAAC,gBAAgB,EAAE,iCAAiC,CAAC;MACrE,OAAOvU,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,iCAAiC,CAAC,CAAC;;IAGvE;IACA,MAAMkL,KAAK,GAAG,IAAI,CAACP,4BAA4B,CAACU,eAAe,CAAC;IAEhE;IACA,OAAO,IAAI,CAACrR,MAAM,CACfwF,MAAM,CAMJ;MACDC,QAAQ,EAAErG,sCAAsC;MAChDsG,SAAS,EAAE;QAAE2L;MAAe;KAC7B,CAAC,CACDzM,IAAI,CACHvI,GAAG,CAAEsJ,MAAM,IAAI;MACb,MAAMiL,QAAQ,GAAGjL,MAAM,CAACd,IAAI,EAAEuM,2BAA2B;MACzD,IAAI,CAACR,QAAQ,EAAE;QACb,MAAM,IAAI5K,KAAK,CAAC,iCAAiC,CAAC;;MAGpD,IAAI,CAAC/F,MAAM,CAACwK,KAAK,CACf,gBAAgB,EAChB,sCAAsC,EACtCmG,QAAQ,CACT;MAED,OAAOA,QAAQ;IACjB,CAAC,CAAC,EACFtU,UAAU,CAAE8H,KAAK,IACf,IAAI,CAACyM,mBAAmB,CACtBzM,KAAK,EACL,0CAA0C,EAC1C;MACE0M,OAAO,EAAEI,KAAK,GAAG,CAAC;MAClBA,KAAK;MACLtD,OAAO,EAAE,GAAGsD,KAAK;KAClB,CACF,CACF,CACF;EACL;EACAI,wBAAwBA,CAAA;IAGtB,OAAO,IAAI,CAACnP,cAAc,CAACyC,IAAI,CAC7BvI,GAAG,CAAE+D,aAAa,IAAI;MACpB,MAAMmR,MAAM,GAAG,IAAIjR,GAAG,EAAoC;MAC1DF,aAAa,CAACuD,OAAO,CAAEoL,KAAK,IAAI;QAC9B,IAAI,CAACwC,MAAM,CAACvC,GAAG,CAACD,KAAK,CAACtF,IAAI,CAAC,EAAE;UAC3B8H,MAAM,CAACzN,GAAG,CAACiL,KAAK,CAACtF,IAAI,EAAE,EAAE,CAAC;;QAE5B8H,MAAM,CAACC,GAAG,CAACzC,KAAK,CAACtF,IAAI,CAAC,EAAEgI,IAAI,CAAC1C,KAAK,CAAC;MACrC,CAAC,CAAC;MACF,OAAOwC,MAAM;IACf,CAAC,CAAC,CACH;EACH;EACAG,UAAUA,CAACL,eAAyB;IAKlC,IAAI,CAACpR,MAAM,CAACwK,KAAK,CACf,gBAAgB,EAChB,kCAAkC4G,eAAe,EAAEzC,IAAI,CAAC,IAAI,CAAC,IAAI,MAAM,EAAE,CAC1E;IAED,IAAI,CAACyC,eAAe,IAAIA,eAAe,CAACvG,MAAM,KAAK,CAAC,EAAE;MACpD,IAAI,CAAC7K,MAAM,CAACwQ,IAAI,CAAC,gBAAgB,EAAE,8BAA8B,CAAC;MAClE,OAAOxU,EAAE,CAAC;QACR6U,OAAO,EAAE,KAAK;QACda,SAAS,EAAE,CAAC;QACZC,cAAc,EAAE,IAAI,CAACrR,iBAAiB,CAAC8P;OACxC,CAAC;;IAGJ;IACA,MAAMwB,QAAQ,GAAGR,eAAe,CAAC7U,MAAM,CACpCqH,EAAE,IAAKA,EAAE,IAAI,OAAOA,EAAE,KAAK,QAAQ,IAAIA,EAAE,CAACiO,IAAI,EAAE,KAAK,EAAE,CACzD;IAED,IAAID,QAAQ,CAAC/G,MAAM,KAAKuG,eAAe,CAACvG,MAAM,EAAE;MAC9C,IAAI,CAAC7K,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,mCAAmC,EAAE;QACvE2N,QAAQ,EAAEV,eAAe;QACzBW,KAAK,EAAEH;OACR,CAAC;MACF,OAAO3V,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,mCAAmC,CAAC,CAAC;;IAGzE,IAAI,CAAC/F,MAAM,CAACwK,KAAK,CACf,gBAAgB,EAChB,gDAAgD,EAChDoH,QAAQ,CACT;IAED;IACA,IAAI,CAACI,wBAAwB,CAACJ,QAAQ,EAAE,IAAI,CAAC;IAE7C;IACA,MAAMK,kBAAkB,GAAG;MACzBC,uBAAuB,EAAE;QACvBrB,OAAO,EAAE,IAAI;QACba,SAAS,EAAEE,QAAQ,CAAC/G,MAAM;QAC1B8G,cAAc,EAAEQ,IAAI,CAACC,GAAG,CACtB,CAAC,EACD,IAAI,CAAC9R,iBAAiB,CAAC8P,KAAK,GAAGwB,QAAQ,CAAC/G,MAAM;;KAGnD;IAED;IACA5C,OAAO,CAACC,GAAG,CAAC,0DAA0D,EAAE;MACtEkJ,eAAe,EAAEQ;KAClB,CAAC;IACF3J,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEnJ,+BAA+B,CAAC;IAE/D,OAAO,IAAI,CAACgB,MAAM,CACfwF,MAAM,CAAkC;MACvCC,QAAQ,EAAEzG,+BAA+B;MACzC0G,SAAS,EAAE;QAAE2L,eAAe,EAAEQ;MAAQ,CAAE;MACxCK,kBAAkB,EAAEA,kBAAkB;MACtCjH,WAAW,EAAE,KAAK;MAClBN,WAAW,EAAE,UAAU,CAAE;KAC1B,CAAC,CACD/F,IAAI,CACHvI,GAAG,CAAEsJ,MAAM,IAAI;MACb,IAAI,CAAC1F,MAAM,CAACwK,KAAK,CAAC,gBAAgB,EAAE,iBAAiB,EAAE9E,MAAM,CAAC;MAC9DuC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAExC,MAAM,CAAC;MAEvC;MACA,IAAIA,MAAM,CAAC+I,MAAM,EAAE;QACjB,IAAI,CAACzO,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iBAAiB,EACjBuB,MAAM,CAAC+I,MAAM,CACd;QACDxG,OAAO,CAAC9D,KAAK,CAAC,iBAAiB,EAAEuB,MAAM,CAAC+I,MAAM,CAAC;;MAGjD;MACA,MAAMkC,QAAQ,GACZjL,MAAM,CAACd,IAAI,EAAEsN,uBAAuB,IACpCD,kBAAkB,CAACC,uBAAuB;MAE5C,OAAOvB,QAAQ;IACjB,CAAC,CAAC,EACFtU,UAAU,CAAE8H,KAAY,IAAI;MAC1B,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,sCAAsC,EACtCA,KAAK,CACN;MACD8D,OAAO,CAAC9D,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAE5C;MACA;MACA,OAAOnI,EAAE,CAAC;QACR6U,OAAO,EAAE,IAAI;QACba,SAAS,EAAEE,QAAQ,CAAC/G,MAAM;QAC1B8G,cAAc,EAAEQ,IAAI,CAACC,GAAG,CACtB,CAAC,EACD,IAAI,CAAC9R,iBAAiB,CAAC8P,KAAK,GAAGwB,QAAQ,CAAC/G,MAAM;OAEjD,CAAC;IACJ,CAAC,CAAC,CACH;EACL;EACA;EACA;EACA;EAEA;;;;;EAKAwH,sBAAsBA,CAACC,MAAc;IACnC,OAAO,IAAI,CAACvS,MAAM,CACfuE,SAAS,CAA6B;MACrCI,KAAK,EAAEhF,wBAAwB;MAC/B+F,SAAS,EAAE;QAAE6M;MAAM;KACpB,CAAC,CACD3N,IAAI,CACHvI,GAAG,CAAC,CAAC;MAAEwI;IAAI,CAAE,KAAI;MACf,IAAI,CAACA,IAAI,EAAE2N,UAAU,EAAE;QACrB,MAAM,IAAIxM,KAAK,CAAC,yBAAyB,CAAC;;MAE5C,OAAOnB,IAAI,CAAC2N,UAAU;IACxB,CAAC,CAAC,EACFjW,GAAG,CAAEkW,MAAM,IAAI;MACb,IAAI,CAAC3R,WAAW,CAACiD,IAAI,CAAC0O,MAAM,CAAC;MAC7B,IAAI,CAACC,gBAAgB,CAACD,MAAM,CAAC;IAC/B,CAAC,CAAC,EACFnW,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MAC7D,OAAOlI,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEA;;;;;;;EAOA2M,cAAcA,CACZJ,MAAc,EACdK,UAAkB,EAClBC,UAAkB;IAElB,OAAO,IAAI,CAAC7S,MAAM,CACfwF,MAAM,CAAkC;MACvCC,QAAQ,EAAE/F,yBAAyB;MACnCgG,SAAS,EAAE;QACT6M,MAAM;QACNK,UAAU;QACVC;;KAEH,CAAC,CACDjO,IAAI,CACHvI,GAAG,CAAEsJ,MAAM,IAAI;MACb,MAAMmL,OAAO,GAAGnL,MAAM,CAACd,IAAI,EAAE8N,cAAc;MAC3C,IAAI,CAAC7B,OAAO,EAAE;QACZ,MAAM,IAAI9K,KAAK,CAAC,4BAA4B,CAAC;;MAE/C,OAAO8K,OAAO;IAChB,CAAC,CAAC,EACFxU,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACrD,OAAOlI,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,CACH;EACL;EAEA;;;;;;;;;;EAUA8M,cAAcA,CACZrQ,KAAA,GAAgB,EAAE,EAClB+K,MAAA,GAAiB,CAAC,EAClBuF,MAAiB,EACjBtJ,IAAe,EACfuJ,SAAyB,EACzBC,OAAuB;IAEvB,OAAO,IAAI,CAACjT,MAAM,CACf0K,UAAU,CAA0B;MACnC/F,KAAK,EAAErF,kBAAkB;MACzBoG,SAAS,EAAE;QACTjD,KAAK;QACL+K,MAAM;QACNuF,MAAM;QACNtJ,IAAI;QACJuJ,SAAS;QACTC;OACD;MACDtI,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAChG,IAAI,CAChBvI,GAAG,CAAEsJ,MAAM,IAAI;MACb,MAAMuN,OAAO,GAAGvN,MAAM,CAACd,IAAI,EAAEsO,WAAW,IAAI,EAAE;MAC9C,IAAI,CAAClT,MAAM,CAACwK,KAAK,CAAC,aAAayI,OAAO,CAACpI,MAAM,qBAAqB,CAAC;MACnE,OAAOoI,OAAO;IAChB,CAAC,CAAC,EACF5W,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACxD,OAAOlI,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKAoN,cAAcA,CAACb,MAAc;IAC3B,OAAO,IAAI,CAACvS,MAAM,CACf0K,UAAU,CAAwB;MACjC/F,KAAK,EAAEpF,kBAAkB;MACzBmG,SAAS,EAAE;QAAE6M;MAAM,CAAE;MACrB5H,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAChG,IAAI,CAChBvI,GAAG,CAAEsJ,MAAM,IAAI;MACb,MAAM0N,OAAO,GAAG1N,MAAM,CAACd,IAAI,EAAEyO,WAAW;MACxC,IAAI,CAACD,OAAO,EAAE;QACZ,MAAM,IAAIrN,KAAK,CAAC,wBAAwB,CAAC;;MAE3C,IAAI,CAAC/F,MAAM,CAACwK,KAAK,CAAC,+BAA+B8H,MAAM,EAAE,CAAC;MAC1D,OAAOc,OAAO;IAChB,CAAC,CAAC,EACF/W,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACxD,OAAOlI,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EAEA;;;;EAIAuN,YAAYA,CAAA;IACV,OAAO,IAAI,CAACvT,MAAM,CACf0K,UAAU,CAAqB;MAC9B/F,KAAK,EAAEnF,gBAAgB;MACvBmL,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAChG,IAAI,CAChBvI,GAAG,CAAEsJ,MAAM,IAAI;MACb,MAAM6N,KAAK,GAAG7N,MAAM,CAACd,IAAI,EAAE4O,SAAS;MACpC,IAAI,CAACD,KAAK,EAAE;QACV,MAAM,IAAIxN,KAAK,CAAC,sBAAsB,CAAC;;MAEzC,IAAI,CAAC/F,MAAM,CAACwK,KAAK,CAAC,uBAAuB,EAAE+I,KAAK,CAAC;MACjD,OAAOA,KAAK;IACd,CAAC,CAAC,EACFlX,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MACtD,OAAOlI,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,CACH;EACL;EAEA;;;;EAIQ0M,gBAAgBA,CAACD,MAAkB;IACzC,QAAQA,MAAM,CAAChJ,IAAI;MACjB,KAAK,eAAe;QAClB,IAAI,CAACiK,kBAAkB,CAACjB,MAAM,CAAC;QAC/B;MACF,KAAK,QAAQ;QACX,IAAI,CAACkB,YAAY,CAAClB,MAAM,CAAC;QACzB;MACF,KAAK,UAAU;QACb,IAAI,CAACmB,aAAa,CAACnB,MAAM,CAAC;QAC1B;MACF,KAAK,QAAQ;QACX,IAAI,CAACoB,gBAAgB,CAACpB,MAAM,CAAC;QAC7B;MACF;QACE,IAAI,CAACxS,MAAM,CAACwK,KAAK,CAAC,0BAA0BgI,MAAM,CAAChJ,IAAI,EAAE,EAAEgJ,MAAM,CAAC;;EAExE;EAEA;;;;EAIQiB,kBAAkBA,CAACjB,MAAkB;IAC3C,IAAI,CAAC,IAAI,CAACxR,cAAc,EAAE;MACxB,IAAI,CAAChB,MAAM,CAACmE,KAAK,CAAC,gDAAgD,CAAC;MACnE;;IAGF,IAAI;MACF,MAAM0P,SAAS,GAAGtQ,IAAI,CAACC,KAAK,CAACgP,MAAM,CAAC5N,IAAI,CAAC;MACzC,IAAI,CAAC5D,cAAc,CAChB8S,eAAe,CAAC,IAAIC,eAAe,CAACF,SAAS,CAAC,CAAC,CAC/CrM,KAAK,CAAErD,KAAK,IAAI;QACf,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,4BAA4B,EAAEA,KAAc,CAAC;MACjE,CAAC,CAAC;KACL,CAAC,OAAOA,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,6BAA6B,EAAEA,KAAc,CAAC;;EAEpE;EAEA;;;;EAIQuP,YAAYA,CAAClB,MAAkB;IACrC,IAAI,CAAC,IAAI,CAACxR,cAAc,EAAE;MACxB,IAAI,CAAChB,MAAM,CAACmE,KAAK,CAAC,yCAAyC,CAAC;MAC5D;;IAGF,IAAI;MACF,MAAM6P,MAAM,GAAGzQ,IAAI,CAACC,KAAK,CAACgP,MAAM,CAAC5N,IAAI,CAAC;MACtC,IAAI,CAAC5D,cAAc,CAChBiT,oBAAoB,CAAC,IAAIC,qBAAqB,CAACF,MAAM,CAAC,CAAC,CACvDxM,KAAK,CAAErD,KAAK,IAAI;QACf,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,kCAAkC,EAAEA,KAAc,CAAC;MACvE,CAAC,CAAC;KACL,CAAC,OAAOA,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,sBAAsB,EAAEA,KAAc,CAAC;;EAE7D;EAEA;;;;EAIQwP,aAAaA,CAACnB,MAAkB;IACtC,IAAI,CAAC/K,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAAC0M,WAAW,EAAE;IAElB;IACA,MAAMC,WAAW,GAAG,IAAI,CAACzT,UAAU,CAACyP,KAAK;IACzC,IAAIgE,WAAW,IAAIA,WAAW,CAACxQ,EAAE,KAAK4O,MAAM,CAACF,MAAM,EAAE;MACnD,IAAI,CAAC3R,UAAU,CAACmD,IAAI,CAAC;QACnB,GAAGsQ,WAAW;QACdtB,MAAM,EAAEhW,UAAU,CAACuX,KAAK;QACxBC,OAAO,EAAE,IAAI7I,IAAI,EAAE,CAAC1I,WAAW;OAChC,CAAC;;EAEN;EAEA;;;;EAIQ6Q,gBAAgBA,CAACpB,MAAkB;IACzC,IAAI,CAAC/K,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAAC0M,WAAW,EAAE;IAElB;IACA,MAAMC,WAAW,GAAG,IAAI,CAACzT,UAAU,CAACyP,KAAK;IACzC,IAAIgE,WAAW,IAAIA,WAAW,CAACxQ,EAAE,KAAK4O,MAAM,CAACF,MAAM,EAAE;MACnD,IAAI,CAAC3R,UAAU,CAACmD,IAAI,CAAC;QACnB,GAAGsQ,WAAW;QACdtB,MAAM,EAAEhW,UAAU,CAACyX,QAAQ;QAC3BD,OAAO,EAAE,IAAI7I,IAAI,EAAE,CAAC1I,WAAW;OAChC,CAAC;;EAEN;EAEA;;;EAGQoR,WAAWA,CAAA;IACjB,IAAI,IAAI,CAACrT,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAAC0T,SAAS,EAAE,CAAC9Q,OAAO,CAAE+Q,KAAK,IAAKA,KAAK,CAAChN,IAAI,EAAE,CAAC;MAC7D,IAAI,CAAC3G,WAAW,GAAG,IAAI;MACvB,IAAI,CAACO,YAAY,CAACyC,IAAI,CAAC,IAAI,CAAC;;IAG9B,IAAI,IAAI,CAAC9C,cAAc,EAAE;MACvB,IAAI,CAACA,cAAc,CAAC0T,KAAK,EAAE;MAC3B,IAAI,CAAC1T,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACD,YAAY,GAAG,IAAI;IACxB,IAAI,CAACO,aAAa,CAACwC,IAAI,CAAC,IAAI,CAAC;EAC/B;EAEA;;;;;EAKQ6Q,iBAAiBA,CAAC7O,QAAkB;IAC1C,MAAM8O,WAAW,GAA2B;MAC1C3N,KAAK,EAAE,IAAI;MACX4N,KAAK,EACH/O,QAAQ,KAAKjJ,QAAQ,CAACiY,KAAK,GACvB;QACEC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QACtBC,MAAM,EAAE;UAAED,KAAK,EAAE;QAAG;OACrB,GACD;KACP;IAED,OAAO,IAAIjZ,UAAU,CAAemZ,QAAQ,IAAI;MAC9CC,SAAS,CAACC,YAAY,CACnBC,YAAY,CAACT,WAAW,CAAC,CACzBU,IAAI,CAAEC,MAAM,IAAI;QACfL,QAAQ,CAACpR,IAAI,CAACyR,MAAM,CAAC;QACrBL,QAAQ,CAACM,QAAQ,EAAE;MACrB,CAAC,CAAC,CACDhO,KAAK,CAAErD,KAAK,IAAI;QACf,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACzD+Q,QAAQ,CAAC/Q,KAAK,CAAC,IAAI4B,KAAK,CAAC,gCAAgC,CAAC,CAAC;MAC7D,CAAC,CAAC;IACN,CAAC,CAAC;EACJ;EAEA;;;;EAIQ0P,cAAcA,CAAA;IACpB,OAAOhK,IAAI,CAACkB,GAAG,EAAE,CAAC+I,QAAQ,EAAE,GAAGvD,IAAI,CAACwD,MAAM,EAAE,CAACD,QAAQ,CAAC,EAAE,CAAC,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;EAC3E;EAEA;EACA;EACA;EACA;EACAC,WAAWA,CACTC,YAAY,GAAG,KAAK,EACpBC,MAAe,EACfhL,IAAA,GAAe,CAAC,EAChBvI,KAAA,GAAgB,EAAE,EAClBwT,MAAA,GAAiB,UAAU,EAC3BC,SAAA,GAAoB,KAAK,EACzBC,QAAkB;IAElB,IAAI,CAAClW,MAAM,CAACsN,IAAI,CACd,gBAAgB,EAChB,2CAA2CwI,YAAY,YACrDC,MAAM,IAAI,SACZ,UAAUhL,IAAI,WAAWvI,KAAK,YAAYwT,MAAM,eAAeC,SAAS,cAAcC,QAAQ,EAAE,CACjG;IAED,MAAMvJ,GAAG,GAAGlB,IAAI,CAACkB,GAAG,EAAE;IACtB,MAAMwJ,UAAU,GACd,CAACL,YAAY,IACb,IAAI,CAACpU,UAAU,CAACmJ,MAAM,GAAG,CAAC,IAC1B8B,GAAG,GAAG,IAAI,CAACjM,aAAa,IAAI,IAAI,CAACD,cAAc,IAC/C,CAACsV,MAAM,IACPhL,IAAI,KAAK,CAAC,IACVvI,KAAK,IAAI,IAAI,CAACd,UAAU,CAACmJ,MAAM;IAEjC;IACA,IAAIsL,UAAU,EAAE;MACd,IAAI,CAACnW,MAAM,CAACwK,KAAK,CACf,gBAAgB,EAChB,uBAAuB,IAAI,CAAC9I,UAAU,CAACmJ,MAAM,SAAS,CACvD;MACD,OAAO7O,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC0F,UAAU,CAAC,CAAC;;IAGjC,IAAI,CAAC1B,MAAM,CAACwK,KAAK,CACf,gBAAgB,EAChB,2DACEsL,YAAY,GAAG,cAAc,GAAG,aAClC,EAAE,CACH;IAED,OAAO,IAAI,CAAC/V,MAAM,CACf0K,UAAU,CAAM;MACf/F,KAAK,EAAElH,kBAAkB;MACzBiI,SAAS,EAAE;QACTsQ,MAAM;QACNhL,IAAI;QACJvI,KAAK;QACLwT,MAAM;QACNC,SAAS;QACTC,QAAQ,EAAEA,QAAQ,KAAKpT,SAAS,GAAGoT,QAAQ,GAAG;OAC/C;MACDxL,WAAW,EAAEoL,YAAY,GAAG,cAAc,GAAG;KAC9C,CAAC,CACDnL,YAAY,CAAChG,IAAI,CAChBvI,GAAG,CAAEsJ,MAAM,IAAI;MACb,IAAI,CAAC1F,MAAM,CAACwK,KAAK,CACf,gBAAgB,EAChB,yBAAyB,EACzB9E,MAAM,CACP;MAED,IAAIA,MAAM,CAAC+I,MAAM,EAAE;QACjB,IAAI,CAACzO,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,gCAAgC,EAChCuB,MAAM,CAAC+I,MAAM,CACd;QACD,MAAM,IAAI1I,KAAK,CAACL,MAAM,CAAC+I,MAAM,CAACrS,GAAG,CAAEsS,CAAC,IAAKA,CAAC,CAACf,OAAO,CAAC,CAACgB,IAAI,CAAC,IAAI,CAAC,CAAC;;MAGjE,IAAI,CAACjJ,MAAM,CAACd,IAAI,EAAEiR,WAAW,EAAE;QAC7B,IAAI,CAAC7V,MAAM,CAACwQ,IAAI,CACd,gBAAgB,EAChB,oCAAoC,CACrC;QACD,OAAO,EAAE;;MAGX,MAAM4F,iBAAiB,GAAG1Q,MAAM,CAACd,IAAI,CAACiR,WAAW;MAEjD;MACA,IAAI,CAAC7V,MAAM,CAACwK,KAAK,CAAC,gBAAgB,EAAE,sBAAsB,EAAE;QAC1D5I,UAAU,EAAEwU,iBAAiB,CAACxU,UAAU;QACxCC,UAAU,EAAEuU,iBAAiB,CAACvU,UAAU;QACxCC,WAAW,EAAEsU,iBAAiB,CAACtU,WAAW;QAC1CC,WAAW,EAAEqU,iBAAiB,CAACrU,WAAW;QAC1CC,eAAe,EAAEoU,iBAAiB,CAACpU;OACpC,CAAC;MAEF;MACA,MAAMqU,KAAK,GAAW,EAAE;MACxB,KAAK,MAAMC,IAAI,IAAIF,iBAAiB,CAACC,KAAK,EAAE;QAC1C,IAAI;UACF,IAAIC,IAAI,EAAE;YACRD,KAAK,CAAC7E,IAAI,CAAC,IAAI,CAAC/E,aAAa,CAAC6J,IAAI,CAAC,CAAC;;SAEvC,CAAC,OAAOnS,KAAK,EAAE;UACd,IAAI,CAACnE,MAAM,CAACwQ,IAAI,CACd,gBAAgB,EAChB,mCAAmC,EACnCrM,KAAK,CACN;;;MAIL,IAAI,CAACnE,MAAM,CAACsN,IAAI,CACd,gBAAgB,EAChB,YAAY+I,KAAK,CAACxL,MAAM,4BAA4BuL,iBAAiB,CAACtU,WAAW,OAAOsU,iBAAiB,CAACvU,UAAU,GAAG,CACxH;MAED;MACA,IAAI,CAACkU,MAAM,IAAIhL,IAAI,KAAK,CAAC,IAAI,CAACmL,QAAQ,EAAE;QACtC,IAAI,CAACxU,UAAU,GAAG,CAAC,GAAG2U,KAAK,CAAC;QAC5B,IAAI,CAAC3V,aAAa,GAAG+K,IAAI,CAACkB,GAAG,EAAE;QAC/B,IAAI,CAAC3M,MAAM,CAACwK,KAAK,CACf,gBAAgB,EAChB,2BAA2B6L,KAAK,CAACxL,MAAM,QAAQ,CAChD;;MAGH;MACA,IAAI,CAAClJ,qBAAqB,GAAG;QAC3BC,UAAU,EAAEwU,iBAAiB,CAACxU,UAAU;QACxCC,UAAU,EAAEuU,iBAAiB,CAACvU,UAAU;QACxCC,WAAW,EAAEsU,iBAAiB,CAACtU,WAAW;QAC1CC,WAAW,EAAEqU,iBAAiB,CAACrU,WAAW;QAC1CC,eAAe,EAAEoU,iBAAiB,CAACpU;OACpC;MAED,OAAOqU,KAAK;IACd,CAAC,CAAC,EACFha,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MAEnE,IAAIA,KAAK,CAACmL,aAAa,EAAE;QACvB,IAAI,CAACtP,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iBAAiB,EACjBA,KAAK,CAACmL,aAAa,CACpB;;MAGH,IAAInL,KAAK,CAACoL,YAAY,EAAE;QACtB,IAAI,CAACvP,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,gBAAgB,EAChBA,KAAK,CAACoL,YAAY,CACnB;;MAGH;MACA,IACE,IAAI,CAAC7N,UAAU,CAACmJ,MAAM,GAAG,CAAC,IAC1BE,IAAI,KAAK,CAAC,IACV,CAACgL,MAAM,IACP,CAACG,QAAQ,EACT;QACA,IAAI,CAAClW,MAAM,CAACwQ,IAAI,CACd,gBAAgB,EAChB,aAAa,IAAI,CAAC9O,UAAU,CAACmJ,MAAM,kCAAkC,CACtE;QACD,OAAO7O,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC0F,UAAU,CAAC,CAAC;;MAGjC,OAAOzF,UAAU,CACf,MACE,IAAI8J,KAAK,CACP,0BAA0B5B,KAAK,CAACwJ,OAAO,IAAI,eAAe,EAAE,CAC7D,CACJ;IACH,CAAC,CAAC,CACH;EACL;EACA4I,UAAUA,CAAC5P,MAAc;IACvB,OAAO,IAAI,CAAC5G,MAAM,CACf0K,UAAU,CAAqB;MAC9B/F,KAAK,EAAEnH,cAAc;MACrBkI,SAAS,EAAE;QAAE7B,EAAE,EAAE+C;MAAM,CAAE;MACzB+D,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAChG,IAAI,CAChBvI,GAAG,CAAEsJ,MAAM,IAAK,IAAI,CAAC+G,aAAa,CAAC/G,MAAM,CAACd,IAAI,EAAE2R,UAAU,CAAC,CAAC,EAC5Dla,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,sBAAsB,EAAEA,KAAK,CAAC;MAClE,OAAOlI,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,sBAAsB,CAAC,CAAC;IAC5D,CAAC,CAAC,CACH;EACL;EACAyQ,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACzW,MAAM,CACf0K,UAAU,CAAyB;MAClC/F,KAAK,EAAEzG,sBAAsB;MAC7ByM,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAChG,IAAI,CAChBvI,GAAG,CAAEsJ,MAAM,IAAK,IAAI,CAAC+G,aAAa,CAAC/G,MAAM,CAACd,IAAI,EAAE4R,cAAc,CAAC,CAAC,EAChEna,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,8BAA8B,EAC9BA,KAAK,CACN;MACD,OAAOlI,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EACA0Q,aAAaA,CAAC9P,MAAc;IAC1B,OAAO,IAAI,CAAC5G,MAAM,CACfwF,MAAM,CAAwB;MAC7BC,QAAQ,EAAE5H,wBAAwB;MAClC6H,SAAS,EAAE;QAAEkB;MAAM;KACpB,CAAC,CACDhC,IAAI,CACHvI,GAAG,CAAEsJ,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACd,IAAI,EAAE6R,aAAa,EAC7B,MAAM,IAAI1Q,KAAK,CAAC,2BAA2B,CAAC;MAC9C,OAAO,IAAI,CAAC0G,aAAa,CAAC/G,MAAM,CAACd,IAAI,CAAC6R,aAAa,CAAC;IACtD,CAAC,CAAC,EACFpa,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,4BAA4B,EAC5BA,KAAK,CACN;MACD,OAAOlI,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,2BAA2B,CAAC,CAAC;IACjE,CAAC,CAAC,CACH;EACL;EACA2Q,cAAcA,CAAC/P,MAAc;IAC3B,OAAO,IAAI,CAAC5G,MAAM,CACfwF,MAAM,CAAyB;MAC9BC,QAAQ,EAAE3H,yBAAyB;MACnC4H,SAAS,EAAE;QAAEkB;MAAM;KACpB,CAAC,CACDhC,IAAI,CACHvI,GAAG,CAAEsJ,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAACd,IAAI,EAAE8R,cAAc,EAC9B,MAAM,IAAI3Q,KAAK,CAAC,4BAA4B,CAAC;MAC/C,OAAO,IAAI,CAAC0G,aAAa,CAAC/G,MAAM,CAACd,IAAI,CAAC8R,cAAc,CAAC;IACvD,CAAC,CAAC,EACFra,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,6BAA6B,EAC7BA,KAAK,CACN;MACD,OAAOlI,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,CACH;EACL;EAEA;EACA;EACA;EAEA;;;EAGA4Q,WAAWA,CACT5P,IAAY,EACZkH,cAAwB,EACxB2I,KAAY,EACZC,WAAoB;IAEpB,IAAI,CAAC7W,MAAM,CAACwK,KAAK,CACf,gBAAgB,EAChB,mBAAmBzD,IAAI,SAASkH,cAAc,CAACpD,MAAM,eAAe,CACrE;IAED,IAAI,CAAC9D,IAAI,IAAI,CAACkH,cAAc,IAAIA,cAAc,CAACpD,MAAM,KAAK,CAAC,EAAE;MAC3D,OAAO5O,UAAU,CACf,MAAM,IAAI8J,KAAK,CAAC,sCAAsC,CAAC,CACxD;;IAGH,OAAO,IAAI,CAAChG,MAAM,CACfwF,MAAM,CAAC;MACNC,QAAQ,EAAEnH,qBAAqB;MAC/BoH,SAAS,EAAE;QAAEsB,IAAI;QAAEkH,cAAc;QAAE2I,KAAK;QAAEC;MAAW;KACtD,CAAC,CACDlS,IAAI,CACHvI,GAAG,CAAEsJ,MAAW,IAAI;MAClB,MAAMoR,KAAK,GAAGpR,MAAM,CAACd,IAAI,EAAE+R,WAAW;MACtC,IAAI,CAACG,KAAK,EAAE;QACV,MAAM,IAAI/Q,KAAK,CAAC,gCAAgC,CAAC;;MAEnD,IAAI,CAAC/F,MAAM,CAACsN,IAAI,CACd,gBAAgB,EAChB,+BAA+BwJ,KAAK,CAAClT,EAAE,EAAE,CAC1C;MACD,OAAOkT,KAAK;IACd,CAAC,CAAC,EACFza,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MACnE,OAAOlI,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACtE,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAgR,WAAWA,CAACC,OAAe,EAAEC,KAAU;IACrC,IAAI,CAACjX,MAAM,CAACwK,KAAK,CAAC,gBAAgB,EAAE,mBAAmBwM,OAAO,EAAE,CAAC;IAEjE,IAAI,CAACA,OAAO,EAAE;MACZ,OAAO/a,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,qBAAqB,CAAC,CAAC;;IAG3D,OAAO,IAAI,CAAChG,MAAM,CACfwF,MAAM,CAAC;MACNC,QAAQ,EAAElH,qBAAqB;MAC/BmH,SAAS,EAAE;QAAE7B,EAAE,EAAEoT,OAAO;QAAEC;MAAK;KAChC,CAAC,CACDtS,IAAI,CACHvI,GAAG,CAAEsJ,MAAW,IAAI;MAClB,MAAMoR,KAAK,GAAGpR,MAAM,CAACd,IAAI,EAAEmS,WAAW;MACtC,IAAI,CAACD,KAAK,EAAE;QACV,MAAM,IAAI/Q,KAAK,CAAC,mCAAmC,CAAC;;MAEtD,IAAI,CAAC/F,MAAM,CAACsN,IAAI,CACd,gBAAgB,EAChB,+BAA+BwJ,KAAK,CAAClT,EAAE,EAAE,CAC1C;MACD,OAAOkT,KAAK;IACd,CAAC,CAAC,EACFza,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MACnE,OAAOlI,UAAU,CACf,MAAM,IAAI8J,KAAK,CAAC,mCAAmC,CAAC,CACrD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAmR,WAAWA,CACTF,OAAe;IAEf,IAAI,CAAChX,MAAM,CAACwK,KAAK,CAAC,gBAAgB,EAAE,mBAAmBwM,OAAO,EAAE,CAAC;IAEjE,IAAI,CAACA,OAAO,EAAE;MACZ,OAAO/a,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,qBAAqB,CAAC,CAAC;;IAG3D,OAAO,IAAI,CAAChG,MAAM,CACfwF,MAAM,CAAC;MACNC,QAAQ,EAAEjH,qBAAqB;MAC/BkH,SAAS,EAAE;QAAE7B,EAAE,EAAEoT;MAAO;KACzB,CAAC,CACDrS,IAAI,CACHvI,GAAG,CAAEsJ,MAAW,IAAI;MAClB,MAAMiL,QAAQ,GAAGjL,MAAM,CAACd,IAAI,EAAEsS,WAAW;MACzC,IAAI,CAACvG,QAAQ,EAAE;QACb,MAAM,IAAI5K,KAAK,CAAC,mCAAmC,CAAC;;MAEtD,IAAI,CAAC/F,MAAM,CAACsN,IAAI,CACd,gBAAgB,EAChB,+BAA+B0J,OAAO,EAAE,CACzC;MACD,OAAOrG,QAAQ;IACjB,CAAC,CAAC,EACFtU,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MACnE,OAAOlI,UAAU,CACf,MAAM,IAAI8J,KAAK,CAAC,mCAAmC,CAAC,CACrD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAoR,UAAUA,CACRH,OAAe;IAEf,IAAI,CAAChX,MAAM,CAACwK,KAAK,CAAC,gBAAgB,EAAE,kBAAkBwM,OAAO,EAAE,CAAC;IAEhE,IAAI,CAACA,OAAO,EAAE;MACZ,OAAO/a,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,qBAAqB,CAAC,CAAC;;IAG3D,OAAO,IAAI,CAAChG,MAAM,CACfwF,MAAM,CAAC;MACNC,QAAQ,EAAEhH,oBAAoB;MAC9BiH,SAAS,EAAE;QAAEuR;MAAO;KACrB,CAAC,CACDrS,IAAI,CACHvI,GAAG,CAAEsJ,MAAW,IAAI;MAClB,MAAMiL,QAAQ,GAAGjL,MAAM,CAACd,IAAI,EAAEuS,UAAU;MACxC,IAAI,CAACxG,QAAQ,EAAE;QACb,MAAM,IAAI5K,KAAK,CAAC,8BAA8B,CAAC;;MAEjD,IAAI,CAAC/F,MAAM,CAACsN,IAAI,CACd,gBAAgB,EAChB,4BAA4B0J,OAAO,EAAE,CACtC;MACD,OAAOrG,QAAQ;IACjB,CAAC,CAAC,EACFtU,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,sBAAsB,EAAEA,KAAK,CAAC;MAClE,OAAOlI,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAqR,QAAQA,CAACJ,OAAe;IACtB,IAAI,CAAChX,MAAM,CAACwK,KAAK,CAAC,gBAAgB,EAAE,kBAAkBwM,OAAO,EAAE,CAAC;IAEhE,IAAI,CAACA,OAAO,EAAE;MACZ,OAAO/a,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,qBAAqB,CAAC,CAAC;;IAG3D,OAAO,IAAI,CAAChG,MAAM,CACf2E,KAAK,CAAC;MACLA,KAAK,EAAEjG,eAAe;MACtBgH,SAAS,EAAE;QAAE7B,EAAE,EAAEoT;MAAO,CAAE;MAC1BtM,WAAW,EAAE;KACd,CAAC,CACD/F,IAAI,CACHvI,GAAG,CAAEsJ,MAAW,IAAI;MAClB,MAAMoR,KAAK,GAAGpR,MAAM,CAACd,IAAI,EAAEwS,QAAQ;MACnC,IAAI,CAACN,KAAK,EAAE;QACV,MAAM,IAAI/Q,KAAK,CAAC,mBAAmB,CAAC;;MAEtC,IAAI,CAAC/F,MAAM,CAACsN,IAAI,CACd,gBAAgB,EAChB,iCAAiC0J,OAAO,EAAE,CAC3C;MACD,OAAOF,KAAK;IACd,CAAC,CAAC,EACFza,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,sBAAsB,EAAEA,KAAK,CAAC;MAClE,OAAOlI,UAAU,CACf,MAAM,IAAI8J,KAAK,CAAC,oCAAoC,CAAC,CACtD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAsR,aAAaA,CAAC1Q,MAAc;IAC1B,IAAI,CAAC3G,MAAM,CAACwK,KAAK,CAAC,gBAAgB,EAAE,4BAA4B7D,MAAM,EAAE,CAAC;IAEzE,IAAI,CAACA,MAAM,EAAE;MACX,OAAO1K,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,4BAA4B,CAAC,CAAC;;IAGlE,OAAO,IAAI,CAAChG,MAAM,CACf2E,KAAK,CAAC;MACLA,KAAK,EAAEhG,qBAAqB;MAC5B+G,SAAS,EAAE;QAAEkB;MAAM,CAAE;MACrB+D,WAAW,EAAE;KACd,CAAC,CACD/F,IAAI,CACHvI,GAAG,CAAEsJ,MAAW,IAAI;MAClB,MAAM4L,MAAM,GAAG5L,MAAM,CAACd,IAAI,EAAEyS,aAAa,IAAI,EAAE;MAC/C,IAAI,CAACrX,MAAM,CAACsN,IAAI,CACd,gBAAgB,EAChB,aAAagE,MAAM,CAACzG,MAAM,qBAAqBlE,MAAM,EAAE,CACxD;MACD,OAAO2K,MAAM;IACf,CAAC,CAAC,EACFjV,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,4BAA4B,EAC5BA,KAAK,CACN;MACD,OAAOlI,UAAU,CACf,MAAM,IAAI8J,KAAK,CAAC,sCAAsC,CAAC,CACxD;IACH,CAAC,CAAC,CACH;EACL;EASAuR,sBAAsBA,CAAChS,cAAsB;IAC3C,IAAI,CAACA,cAAc,EAAE;MACnB,OAAOrJ,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,6BAA6B,CAAC,CAAC;;IAGnE;IACA,MAAMwR,QAAQ,GAAG,YAAYjS,cAAc,EAAE;IAC7C,IAAI,IAAI,CAAC5C,iBAAiB,CAACqM,GAAG,CAACwI,QAAQ,CAAC,EAAE;MACxC,MAAMC,QAAQ,GAAG,IAAI,CAAC7U,oBAAoB,CAAC4O,GAAG,CAACgG,QAAQ,CAAC,IAAI,CAAC;MAC7D,IAAI,CAAC5U,oBAAoB,CAACkB,GAAG,CAAC0T,QAAQ,EAAEC,QAAQ,GAAG,CAAC,CAAC;MACrD,OAAO,IAAI,CAAC9U,iBAAiB,CAAC6O,GAAG,CAACgG,QAAQ,CAAE;;IAG9C;IACA,IAAI,CAAC,IAAI,CAACE,YAAY,EAAE,EAAE;MACxB,OAAOtb,KAAK;;IAGd;IACA,IAAI,CAACQ,WAAW,CAAC+a,UAAU,EAAE;MAC3BzP,OAAO,CAACC,GAAG,CAAC,+BAA+B5C,cAAc,EAAE,CAAC;;IAE9D;IACA,MAAMqS,IAAI,GAAG,IAAI,CAAC5X,MAAM,CACrBuE,SAAS,CAA2B;MACnCI,KAAK,EAAErH,yBAAyB;MAChCoI,SAAS,EAAE;QAAEH;MAAc,CAAE;MAC7B0F,WAAW,EAAE,KAAK,CAAE;KACrB,CAAC,CACDrG,IAAI;IACH;IACAnI,YAAY,CAAC,EAAE,CAAC,EAChBJ,GAAG,CAAEsJ,MAAM,IAAI;MACb,MAAMoG,GAAG,GAAGpG,MAAM,CAACd,IAAI,EAAEK,WAAW;MACpC,IAAI,CAAC6G,GAAG,EAAE;QACR,MAAM,IAAI/F,KAAK,CAAC,6BAA6B,CAAC;;MAGhD;MACA,IAAI,CAACpJ,WAAW,CAAC+a,UAAU,EAAE;QAC3BzP,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE4D,GAAG,CAAClI,EAAE,CAAC;;MAGrD;MACA,IAAI,CAACkI,GAAG,CAAClI,EAAE,IAAI,CAACkI,GAAG,CAACY,GAAG,EAAE;QACvB,IAAI,CAAC1M,MAAM,CAACwQ,IAAI,CACd,oDAAoD,CACrD;QACD1E,GAAG,CAAClI,EAAE,GAAG,QAAQ6H,IAAI,CAACkB,GAAG,EAAE,EAAE;;MAG/B,IAAI;QACF;QACA,MAAMiL,iBAAiB,GAAG,IAAI,CAAC1S,gBAAgB,CAAC4G,GAAG,CAAC;QAEpD,IAAI,CAAC9L,MAAM,CAACwK,KAAK,CACf,4CAA4C,EAC5CoN,iBAAiB,CAClB;QAED;QACA,IACEA,iBAAiB,CAACpO,IAAI,KAAK5M,WAAW,CAACkY,KAAK,IAC5C8C,iBAAiB,CAACpO,IAAI,KAAK5M,WAAW,CAACib,aAAa,IACnDD,iBAAiB,CAACE,WAAW,IAC5BF,iBAAiB,CAACE,WAAW,CAACC,IAAI,CAC/BC,GAAG,IAAKA,GAAG,CAACxO,IAAI,KAAK,OAAO,CAC7B,EACJ;UACA,IAAI,CAACxJ,MAAM,CAACwK,KAAK,CACf,iDAAiD,CAClD;;QAGH;QACA,IAAI,CAACvK,IAAI,CAACgY,GAAG,CAAC,MAAK;UACjB,IAAI,CAACjY,MAAM,CAACwK,KAAK,CACf,kDAAkD,CACnD;UACD,IAAI,CAAC0N,gCAAgC,CACnC5S,cAAc,EACdsS,iBAAiB,CAClB;QACH,CAAC,CAAC;QAEF,OAAOA,iBAAiB;OACzB,CAAC,OAAOnP,GAAG,EAAE;QACZ,IAAI,CAACzI,MAAM,CAACmE,KAAK,CAAC,8BAA8B,EAAEsE,GAAG,CAAC;QAEtD;QACA,MAAM0P,cAAc,GAAY;UAC9BvU,EAAE,EAAEkI,GAAG,CAAClI,EAAE,IAAIkI,GAAG,CAACY,GAAG,IAAI,QAAQjB,IAAI,CAACkB,GAAG,EAAE,EAAE;UAC7CxG,OAAO,EAAE2F,GAAG,CAAC3F,OAAO,IAAI,EAAE;UAC1BqD,IAAI,EAAEsC,GAAG,CAACtC,IAAI,IAAI5M,WAAW,CAACgQ,IAAI;UAClCb,SAAS,EAAE,IAAI,CAACQ,QAAQ,CAACT,GAAG,CAACC,SAAS,CAAC;UACvCc,MAAM,EAAE,KAAK;UACbL,MAAM,EAAEV,GAAG,CAACU,MAAM,GACd,IAAI,CAACC,aAAa,CAACX,GAAG,CAACU,MAAM,CAAC,GAC9B;YACE5I,EAAE,EAAE,IAAI,CAACkJ,gBAAgB,EAAE;YAC3BC,QAAQ,EAAE;;SAEjB;QAED,IAAI,CAAC/M,MAAM,CAACwK,KAAK,CACf,sCAAsC,EACtC2N,cAAc,CACf;QACD,OAAOA,cAAc;;IAEzB,CAAC,CAAC,EACF9b,UAAU,CAAE8H,KAAK,IAAI;MACnB8D,OAAO,CAAC9D,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,OAAOhI,KAAK;IACd,CAAC,CAAC;IACF;IACAI,MAAM,CAAEoR,OAAO,IAAK,CAAC,CAACA,OAAO,CAAC,EAC9BlR,oBAAoB,CAAC,CAAC2b,IAAI,EAAEC,IAAI,KAAKD,IAAI,EAAExU,EAAE,KAAKyU,IAAI,EAAEzU,EAAE,CAAC;IAC3D;IACAlH,WAAW,CAAC;MAAE4b,UAAU,EAAE,CAAC;MAAEd,QAAQ,EAAE;IAAI,CAAE,CAAC;IAC9C;IACAtb,KAAK,CAAC,CAAC,CAAC,CACT;IAEH;IACA,IAAI,CAACwG,iBAAiB,CAACmB,GAAG,CAAC0T,QAAQ,EAAEI,IAAI,CAAC;IAC1C,IAAI,CAAChV,oBAAoB,CAACkB,GAAG,CAAC0T,QAAQ,EAAE,CAAC,CAAC;IAE1C;IACA,MAAMgB,GAAG,GAAGZ,IAAI,CAACrT,SAAS,CAAC;MACzBR,IAAI,EAAG6J,OAAO,IAAI;QAChB,IAAI,CAAChR,WAAW,CAAC+a,UAAU,EAAE;UAC3BzP,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEyF,OAAO,CAAC/J,EAAE,CAAC;;QAEhD;QACA,IAAI,CAACsU,gCAAgC,CAAC5S,cAAc,EAAEqI,OAAO,CAAC;MAChE,CAAC;MACDxJ,KAAK,EAAGsE,GAAG,IAAI;QACbR,OAAO,CAAC9D,KAAK,CAAC,qBAAqB,EAAEsE,GAAG,CAAC;QACzC;QACA,IAAI,CAAC/F,iBAAiB,CAAC8V,MAAM,CAACjB,QAAQ,CAAC;QACvC,IAAI,CAAC5U,oBAAoB,CAAC6V,MAAM,CAACjB,QAAQ,CAAC;MAC5C,CAAC;MACD/B,QAAQ,EAAEA,CAAA,KAAK;QACb;QACA,IAAI,CAAC9S,iBAAiB,CAAC8V,MAAM,CAACjB,QAAQ,CAAC;QACvC,IAAI,CAAC5U,oBAAoB,CAAC6V,MAAM,CAACjB,QAAQ,CAAC;MAC5C;KACD,CAAC;IAEF,IAAI,CAAC/W,aAAa,CAACgR,IAAI,CAAC+G,GAAG,CAAC;IAC5B,OAAOZ,IAAI;EACb;EAEA;;;;;EAKQO,gCAAgCA,CACtC5S,cAAsB,EACtBqI,OAAgB;IAEhB,IAAI,CAAC3N,MAAM,CAACwK,KAAK,CACf,oCAAoClF,cAAc,qBAAqBqI,OAAO,CAAC/J,EAAE,EAAE,CACpF;IAED;IACA,IAAI,CAAC3D,IAAI,CAACgY,GAAG,CAAC,MAAK;MACjB;MACA,IAAI,CAAC/X,kBAAkB,CAAC4D,IAAI,CAACwB,cAAc,CAAC;MAE5C,IAAI,CAACtF,MAAM,CAACwK,KAAK,CAAC,oDAAoD,CAAC;IACzE,CAAC,CAAC;IAEF;IACAiO,UAAU,CAAC,MAAK;MACd,IAAI,CAACpL,eAAe,CAAC/H,cAAc,CAAC,CAAChB,SAAS,CAAC;QAC7CR,IAAI,EAAG4J,YAAY,IAAI;UACrB,IAAI,CAAC1N,MAAM,CAACwK,KAAK,CACf,8BAA8BlF,cAAc,mBAC1CoI,YAAY,EAAEzC,QAAQ,EAAEJ,MAAM,IAAI,CACpC,WAAW,CACZ;QACH,CAAC;QACD1G,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gDAAgDmB,cAAc,GAAG,EACjEnB,KAAK,CACN;QACH;OACD,CAAC;IACJ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACT;EAEA;;;EAGQuU,0BAA0BA,CAAA;IAChCzQ,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;IAErE;IACA,IAAI,CAACkG,gBAAgB,CAAC,IAAI,CAAC,CAAC9J,SAAS,CAAC;MACpCR,IAAI,EAAG3D,aAAa,IAAI;QACtB8H,OAAO,CAACC,GAAG,CACT,iDAAiD,EACjD/H,aAAa,CAAC0K,MAAM,CACrB;MACH,CAAC;MACD1G,KAAK,EAAGA,KAAK,IAAI;QACf8D,OAAO,CAAC9D,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MACpE;KACD,CAAC;EACJ;EAEAM,qBAAqBA,CAAA;IACnB;IACA,IAAI,CAAC,IAAI,CAACgT,YAAY,EAAE,EAAE;MACxB,IAAI,CAACzX,MAAM,CAACwQ,IAAI,CACd,+EAA+E,CAChF;MACD,OAAOvU,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,0BAA0B,CAAC,CAAC;;IAGhE,IAAI,CAAC/F,MAAM,CAACwK,KAAK,CAAC,iDAAiD,CAAC;IAEpE,MAAMmN,IAAI,GAAG,IAAI,CAAC5X,MAAM,CACrBuE,SAAS,CAA8B;MACtCI,KAAK,EAAEpH;KACR,CAAC,CACDqH,IAAI,CACHrI,GAAG,CAAEoJ,MAAM,IACT,IAAI,CAAC1F,MAAM,CAACwK,KAAK,CACf,uDAAuD,EACvD9E,MAAM,CACP,CACF,EACDtJ,GAAG,CAAEsJ,MAAM,IAAI;MACb,MAAM4Q,IAAI,GAAG5Q,MAAM,CAACd,IAAI,EAAE+T,iBAAiB;MAC3C,IAAI,CAACrC,IAAI,EAAE;QACT,IAAI,CAACtW,MAAM,CAACmE,KAAK,CAAC,4BAA4B,CAAC;QAC/C,MAAM,IAAI4B,KAAK,CAAC,4BAA4B,CAAC;;MAE/C,OAAO,IAAI,CAAC0G,aAAa,CAAC6J,IAAI,CAAC;IACjC,CAAC,CAAC,EACFja,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,4BAA4B,EAAEA,KAAc,CAAC;MAC/D,OAAOlI,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,EACF7J,KAAK,CAAC,CAAC,CAAC,CAAC;KACV;;IAEH,MAAMqc,GAAG,GAAGZ,IAAI,CAACrT,SAAS,EAAE;IAC5B,IAAI,CAAC9D,aAAa,CAACgR,IAAI,CAAC+G,GAAG,CAAC;IAC5B,OAAOZ,IAAI;EACb;EACAiB,8BAA8BA,CAC5BtT,cAAsB;IAEtB,MAAMqS,IAAI,GAAG,IAAI,CAAC5X,MAAM,CACrBuE,SAAS,CAAwC;MAChDI,KAAK,EAAEjH,iCAAiC;MACxCgI,SAAS,EAAE;QAAEH;MAAc;KAC5B,CAAC,CACDX,IAAI,CACHvI,GAAG,CAAEsJ,MAAM,IAAI;MACb,MAAM0H,IAAI,GAAG1H,MAAM,CAACd,IAAI,EAAEiU,mBAAmB;MAC7C,IAAI,CAACzL,IAAI,EAAE,MAAM,IAAIrH,KAAK,CAAC,kCAAkC,CAAC;MAE9D,MAAMyH,sBAAsB,GAAiB;QAC3C,GAAGJ,IAAI;QACPK,YAAY,EACVL,IAAI,CAACK,YAAY,EAAErR,GAAG,CAAE8R,CAAC,IAAK,IAAI,CAACzB,aAAa,CAACyB,CAAC,CAAC,CAAC,IAAI,EAAE;QAC5D4K,WAAW,EAAE1L,IAAI,CAAC0L,WAAW,GACzB;UACE,GAAG1L,IAAI,CAAC0L,WAAW;UACnBtM,MAAM,EAAE,IAAI,CAACC,aAAa,CAACW,IAAI,CAAC0L,WAAW,CAACtM,MAAM,CAAC;UACnDT,SAAS,EAAE,IAAI,CAACQ,QAAQ,CAACa,IAAI,CAAC0L,WAAW,CAAC/M,SAAS,CAAC;UACpDP,MAAM,EAAE4B,IAAI,CAAC0L,WAAW,CAACtN,MAAM,GAC3B,IAAI,CAACe,QAAQ,CAACa,IAAI,CAAC0L,WAAW,CAACtN,MAAM,CAAC,GACtC1I,SAAS;UACb;UACAc,EAAE,EAAEwJ,IAAI,CAAC0L,WAAW,CAAClV,EAAE;UACvBuC,OAAO,EAAEiH,IAAI,CAAC0L,WAAW,CAAC3S,OAAO;UACjCqD,IAAI,EAAE4D,IAAI,CAAC0L,WAAW,CAACtP,IAAI;UAC3BqD,MAAM,EAAEO,IAAI,CAAC0L,WAAW,CAACjM;UACzB;SACD,GACD,IAAI,CAAE;OACX;;MAED,OAAOW,sBAAsC,CAAC,CAAC;IACjD,CAAC,CAAC,EACFnR,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,kCAAkC,EAClCA,KAAK,CACN;MACD,OAAOlI,UAAU,CACf,MAAM,IAAI8J,KAAK,CAAC,kCAAkC,CAAC,CACpD;IACH,CAAC,CAAC,CACH;IAEH,MAAMwS,GAAG,GAAGZ,IAAI,CAACrT,SAAS,EAAE;IAC5B,IAAI,CAAC9D,aAAa,CAACgR,IAAI,CAAC+G,GAAG,CAAC;IAC5B,OAAOZ,IAAI;EACb;EACAoB,0BAA0BA,CACxBzT,cAAsB;IAEtB,MAAMqS,IAAI,GAAG,IAAI,CAAC5X,MAAM,CACrBuE,SAAS,CAAwB;MAChCI,KAAK,EAAE1G,6BAA6B;MACpCyH,SAAS,EAAE;QAAEH;MAAc;KAC5B,CAAC,CACDX,IAAI,CACHvI,GAAG,CAAEsJ,MAAM,IAAKA,MAAM,CAACd,IAAI,EAAEQ,eAAe,CAAC,EAC7C7I,MAAM,CAACyc,OAAO,CAAC,EACf3c,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,sCAAsC,EACtCA,KAAK,CACN;MACD,OAAOlI,UAAU,CACf,MAAM,IAAI8J,KAAK,CAAC,sCAAsC,CAAC,CACxD;IACH,CAAC,CAAC,CACH;IAEH,MAAMwS,GAAG,GAAGZ,IAAI,CAACrT,SAAS,EAAE;IAC5B,IAAI,CAAC9D,aAAa,CAACgR,IAAI,CAAC+G,GAAG,CAAC;IAC5B,OAAOZ,IAAI;EACb;EACQF,YAAYA,CAAA;IAClB,MAAMwB,KAAK,GAAG5V,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAAC2V,KAAK,EAAE;MACV,IAAI,CAACjZ,MAAM,CAACwQ,IAAI,CAAC,oBAAoB,CAAC;MACtC,OAAO,KAAK;;IAGd,IAAI;MACF;MACA,MAAM0I,KAAK,GAAGD,KAAK,CAACE,KAAK,CAAC,GAAG,CAAC;MAC9B,IAAID,KAAK,CAACrO,MAAM,KAAK,CAAC,EAAE;QACtB,IAAI,CAAC7K,MAAM,CAACwQ,IAAI,CAAC,0BAA0B,CAAC;QAC5C,OAAO,KAAK;;MAGd;MACA,MAAM4I,OAAO,GAAG7V,IAAI,CAACC,KAAK,CAAC6V,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MAE1C;MACA,IAAI,CAACE,OAAO,CAACE,GAAG,EAAE;QAChB,IAAI,CAACtZ,MAAM,CAACwQ,IAAI,CAAC,8BAA8B,CAAC;QAChD,OAAO,KAAK;;MAGd,MAAM+I,cAAc,GAAG,IAAI9N,IAAI,CAAC2N,OAAO,CAACE,GAAG,GAAG,IAAI,CAAC;MACnD,MAAM3M,GAAG,GAAG,IAAIlB,IAAI,EAAE;MAEtB,IAAI8N,cAAc,GAAG5M,GAAG,EAAE;QACxB,IAAI,CAAC3M,MAAM,CAACwQ,IAAI,CAAC,cAAc,EAAE;UAC/BgJ,UAAU,EAAED,cAAc,CAACxW,WAAW,EAAE;UACxC4J,GAAG,EAAEA,GAAG,CAAC5J,WAAW;SACrB,CAAC;QACF,OAAO,KAAK;;MAGd,OAAO,IAAI;KACZ,CAAC,OAAOoB,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,0CAA0C,EAC1CA,KAAc,CACf;MACD,OAAO,KAAK;;EAEhB;EAEAI,4BAA4BA,CAAA;IAC1B;IACA,IAAI,CAAC,IAAI,CAACkT,YAAY,EAAE,EAAE;MACxB,IAAI,CAACzX,MAAM,CAACwQ,IAAI,CACd,2EAA2E,CAC5E;MACD,OAAOxU,EAAE,CAAC,EAAE,CAAC;;IAGf,IAAI,CAACgE,MAAM,CAACwK,KAAK,CAAC,kDAAkD,CAAC;IAErE,MAAMmN,IAAI,GAAG,IAAI,CAAC5X,MAAM,CACrBuE,SAAS,CAAyB;MACjCI,KAAK,EAAE1F;KACR,CAAC,CACD2F,IAAI,CACHrI,GAAG,CAAEoJ,MAAM,IACT,IAAI,CAAC1F,MAAM,CAACwK,KAAK,CACf,wDAAwD,EACxD9E,MAAM,CACP,CACF,EACDtJ,GAAG,CAAEsJ,MAAM,IAAI;MACb,MAAM0L,eAAe,GAAG1L,MAAM,CAACd,IAAI,EAAE6U,iBAAiB,IAAI,EAAE;MAC5D,IAAI,CAACzZ,MAAM,CAACwK,KAAK,CACf,oCAAoC,EACpC4G,eAAe,CAChB;MACD,IAAI,CAACY,wBAAwB,CAACZ,eAAe,EAAE,IAAI,CAAC;MACpD,OAAOA,eAAe;IACxB,CAAC,CAAC,EACF/U,UAAU,CAAEoM,GAAG,IAAI;MACjB,IAAI,CAACzI,MAAM,CAACmE,KAAK,CACf,wCAAwC,EACxCsE,GAAY,CACb;MACD;MACA,OAAOzM,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC;IACF;IACAE,KAAK,CAAC,CAAC,CAAC,CAAC;KACV;;IAEH,MAAMqc,GAAG,GAAGZ,IAAI,CAACrT,SAAS,EAAE;IAC5B,IAAI,CAAC9D,aAAa,CAACgR,IAAI,CAAC+G,GAAG,CAAC;IAC5B,OAAOZ,IAAI;EACb;EACAtT,2BAA2BA,CAAA;IACzB;IACA,MAAM4U,KAAK,GAAG5V,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAAC2V,KAAK,EAAE;MACV,IAAI,CAACjZ,MAAM,CAACwQ,IAAI,CACd,6DAA6D,CAC9D;MACD,OAAOrU,KAAK;;IAGd,IAAI,CAAC6D,MAAM,CAACwK,KAAK,CACf,4DAA4D,CAC7D;IAED,MAAMkP,OAAO,GAAG,IAAI,CAAC3Z,MAAM,CAACuE,SAAS,CAA4B;MAC/DI,KAAK,EAAEzH;KACR,CAAC;IAEF,MAAM0c,UAAU,GAAGD,OAAO,CAAC/U,IAAI,CAC7BvI,GAAG,CAAEsJ,MAAM,IAAI;MACb,MAAM/B,YAAY,GAAG+B,MAAM,CAACd,IAAI,EAAE2B,oBAAoB;MACtD,IAAI,CAAC5C,YAAY,EAAE;QACjB,MAAM,IAAIoC,KAAK,CAAC,kCAAkC,CAAC;;MAGrD,IAAI,CAAC/F,MAAM,CAACwK,KAAK,CACf,sCAAsC,EACtC7G,YAAY,CACb;MAED,MAAMiW,UAAU,GAAG,IAAI,CAACC,qBAAqB,CAAClW,YAAY,CAAC;MAE3D;MACA,IAAI,IAAI,CAACvD,iBAAiB,CAAC2O,GAAG,CAAC6K,UAAU,CAAChW,EAAE,CAAC,EAAE;QAC7C,IAAI,CAAC5D,MAAM,CAACwK,KAAK,CACf,mBAAmBoP,UAAU,CAAChW,EAAE,6BAA6B,CAC9D;QACD,MAAM,IAAImC,KAAK,CAAC,sCAAsC,CAAC;;MAGzD;MACA,IAAI,CAAC/F,MAAM,CAACwK,KAAK,CAAC,iDAAiD,CAAC;MAEpE;MACA,MAAMsP,oBAAoB,GAAG,IAAI,CAAC3Z,aAAa,CAACiQ,KAAK;MACrD,MAAM2J,oBAAoB,GAAGD,oBAAoB,CAAC/L,IAAI,CACnD4B,CAAC,IAAKA,CAAC,CAAC/L,EAAE,KAAKgW,UAAU,CAAChW,EAAE,CAC9B;MAED,IAAImW,oBAAoB,EAAE;QACxB,IAAI,CAAC/Z,MAAM,CAACwK,KAAK,CACf,sDAAsD,EACtDoP,UAAU,CAAChW,EAAE,CACd;QACD,OAAOgW,UAAU;;MAGnB;MACA,IAAI,CAAC5R,qBAAqB,EAAE;MAE5B;MACA,IAAI,CAACgS,uBAAuB,CAACJ,UAAU,CAAC;MAExC;MACA,IAAI,CAAC3Z,IAAI,CAACgY,GAAG,CAAC,MAAK;QACjB;QACA,MAAMgC,oBAAoB,GAAG,CAACL,UAAU,EAAE,GAAGE,oBAAoB,CAAC;QAElE,IAAI,CAAC9Z,MAAM,CAACwK,KAAK,CACf,wDAAwDyP,oBAAoB,CAACpP,MAAM,SAAS,CAC7F;QAED,IAAI,CAAC1K,aAAa,CAAC2D,IAAI,CAACmW,oBAAoB,CAAC;QAC7C,IAAI,CAAC3Z,iBAAiB,CAACwD,IAAI,CAAC,IAAI,CAACxD,iBAAiB,CAAC8P,KAAK,GAAG,CAAC,CAAC;MAC/D,CAAC,CAAC;MAEF,IAAI,CAACpQ,MAAM,CAACwK,KAAK,CACf,+CAA+C,EAC/CoP,UAAU,CACX;MAED,OAAOA,UAAU;IACnB,CAAC,CAAC;IACF;IACAvd,UAAU,CAAEoM,GAAG,IAAI;MACjB,IACEA,GAAG,YAAY1C,KAAK,IACpB0C,GAAG,CAACkF,OAAO,KAAK,sCAAsC,EACtD;QACA,OAAOxR,KAAK;;MAGd,IAAI,CAAC6D,MAAM,CAACmE,KAAK,CAAC,oCAAoC,EAAEsE,GAAY,CAAC;MACrE,OAAOtM,KAAK;IACd,CAAC,CAAC;IACF;IACAG,GAAG,CAAEqH,YAAY,IAAI;MACnB,IAAI,CAAC3D,MAAM,CAACwK,KAAK,CACf,6CAA6C,EAC7C7G,YAAY,CACb;IACH,CAAC,CAAC,CACH;IAED,MAAM4U,GAAG,GAAGoB,UAAU,CAACrV,SAAS,CAAC;MAC/BR,IAAI,EAAGH,YAAY,IAAI;QACrB,IAAI,CAAC3D,MAAM,CAACwK,KAAK,CACf,yCAAyC,EACzC7G,YAAY,CACb;MACH,CAAC;MACDQ,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,6CAA6C,EAC7CA,KAAK,CACN;MACH;KACD,CAAC;IAEF,IAAI,CAAC3D,aAAa,CAACgR,IAAI,CAAC+G,GAAG,CAAC;IAC5B,IAAI,CAACvY,MAAM,CAACwK,KAAK,CAAC,mDAAmD,CAAC;IACtE,OAAOmP,UAAU;EACnB;EACA;EACA;EACA;EAEQzW,oBAAoBA,CAAA;IAC1B,IAAI,CAACgX,eAAe,GAAGC,WAAW,CAAC,MAAK;MACtC,IAAI,CAACC,2BAA2B,EAAE;IACpC,CAAC,EAAE,OAAO,CAAC;EACb;EACQA,2BAA2BA,CAAA;IACjC,MAAMzN,GAAG,GAAG,IAAIlB,IAAI,EAAE;IACtB,MAAM4O,aAAa,GAAG,IAAI5O,IAAI,CAACkB,GAAG,CAAC2N,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAExE,IAAIC,YAAY,GAAG,CAAC;IAEpB,IAAI,CAACna,iBAAiB,CAACsD,OAAO,CAAC,CAACC,YAAY,EAAEC,EAAE,KAAI;MAClD,MAAM4W,gBAAgB,GAAG,IAAI/O,IAAI,CAAC9H,YAAY,CAACoI,SAAS,CAAC;MACzD,IAAIyO,gBAAgB,GAAGH,aAAa,EAAE;QACpC,IAAI,CAACja,iBAAiB,CAACoY,MAAM,CAAC5U,EAAE,CAAC;QACjC2W,YAAY,EAAE;;IAElB,CAAC,CAAC;IAEF,IAAIA,YAAY,GAAG,CAAC,EAAE;MACpB,IAAI,CAACva,MAAM,CAACwK,KAAK,CAAC,cAAc+P,YAAY,wBAAwB,CAAC;MAErE;MACA,MAAME,sBAAsB,GAAG1W,KAAK,CAACC,IAAI,CACvC,IAAI,CAAC5D,iBAAiB,CAAC6D,MAAM,EAAE,CAChC;MACD,MAAMkL,mBAAmB,GAAG,IAAI,CAACC,uBAAuB,CACtDqL,sBAAsB,CACvB;MAED,IAAI,CAACta,aAAa,CAAC2D,IAAI,CAACqL,mBAAmB,CAAC;MAC5C,IAAI,CAACjL,iBAAiB,EAAE;;EAE5B;EACA;;;;;EAKQkL,uBAAuBA,CAC7BjP,aAA6B;IAE7B,OAAOA,aAAa,CAACua,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACjC;MACA,MAAMC,KAAK,GAAG,IAAIpP,IAAI,CAACkP,CAAC,CAAC5O,SAAS,IAAI,CAAC,CAAC;MACxC,MAAM+O,KAAK,GAAG,IAAIrP,IAAI,CAACmP,CAAC,CAAC7O,SAAS,IAAI,CAAC,CAAC;MACxC,OAAO+O,KAAK,CAACR,OAAO,EAAE,GAAGO,KAAK,CAACP,OAAO,EAAE,CAAC,CAAC;IAC5C,CAAC,CAAC;EACJ;;EAEQxN,gBAAgBA,CAAA;IACtB,OAAOzJ,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE;EAC7C;EACQ4B,gBAAgBA,CAACyI,OAAgB;IACvC,IAAI,CAACA,OAAO,EAAE;MACZ,IAAI,CAAC3N,MAAM,CAACmE,KAAK,CACf,6DAA6D,CAC9D;MACD,MAAM,IAAI4B,KAAK,CAAC,4BAA4B,CAAC;;IAG/C,IAAI;MACF;MACA,IAAI,CAAC4H,OAAO,CAAC/J,EAAE,IAAI,CAAC+J,OAAO,CAACjB,GAAG,EAAE;QAC/B,IAAI,CAAC1M,MAAM,CAACmE,KAAK,CACf,wCAAwC,EACxCrB,SAAS,EACT6K,OAAO,CACR;QACD,MAAM,IAAI5H,KAAK,CAAC,wBAAwB,CAAC;;MAG3C;MACA,IAAIgV,gBAAgB;MACpB,IAAI;QACFA,gBAAgB,GAAGpN,OAAO,CAACnB,MAAM,GAC7B,IAAI,CAACC,aAAa,CAACkB,OAAO,CAACnB,MAAM,CAAC,GAClC1J,SAAS;OACd,CAAC,OAAOqB,KAAK,EAAE;QACd,IAAI,CAACnE,MAAM,CAACwQ,IAAI,CACd,yEAAyE,EACzErM,KAAK,CACN;QACD4W,gBAAgB,GAAG;UACjBrO,GAAG,EAAEiB,OAAO,CAAC1H,QAAQ,IAAI,SAAS;UAClCrC,EAAE,EAAE+J,OAAO,CAAC1H,QAAQ,IAAI,SAAS;UACjC8G,QAAQ,EAAE,cAAc;UACxBiO,KAAK,EAAE,qBAAqB;UAC5BC,IAAI,EAAE,MAAM;UACZC,QAAQ,EAAE;SACX;;MAGH;MACA,IAAIC,kBAAkB;MACtB,IAAIxN,OAAO,CAACyN,QAAQ,EAAE;QACpB,IAAI;UACFD,kBAAkB,GAAG,IAAI,CAAC1O,aAAa,CAACkB,OAAO,CAACyN,QAAQ,CAAC;SAC1D,CAAC,OAAOjX,KAAK,EAAE;UACd,IAAI,CAACnE,MAAM,CAACwQ,IAAI,CACd,2EAA2E,EAC3ErM,KAAK,CACN;UACDgX,kBAAkB,GAAG;YACnBzO,GAAG,EAAEiB,OAAO,CAACzH,UAAU,IAAI,SAAS;YACpCtC,EAAE,EAAE+J,OAAO,CAACzH,UAAU,IAAI,SAAS;YACnC6G,QAAQ,EAAE,cAAc;YACxBiO,KAAK,EAAE,qBAAqB;YAC5BC,IAAI,EAAE,MAAM;YACZC,QAAQ,EAAE;WACX;;;MAIL;MACA,MAAMG,qBAAqB,GACzB1N,OAAO,CAACmK,WAAW,EAAE1b,GAAG,CAAE4b,GAAG,KAAM;QACjCpU,EAAE,EAAEoU,GAAG,CAACpU,EAAE,IAAIoU,GAAG,CAACtL,GAAG,IAAI,cAAcjB,IAAI,CAACkB,GAAG,EAAE,EAAE;QACnD2O,GAAG,EAAEtD,GAAG,CAACsD,GAAG,IAAI,EAAE;QAClB9R,IAAI,EAAEwO,GAAG,CAACxO,IAAI,IAAI,SAAS;QAC3BzC,IAAI,EAAEiR,GAAG,CAACjR,IAAI,IAAI,YAAY;QAC9ByH,IAAI,EAAEwJ,GAAG,CAACxJ,IAAI,IAAI,CAAC;QACnBrF,QAAQ,EAAE6O,GAAG,CAAC7O,QAAQ,IAAI;OAC3B,CAAC,CAAC,IAAI,EAAE;MAEX;MACA,MAAMyO,iBAAiB,GAAG;QACxB,GAAGjK,OAAO;QACVjB,GAAG,EAAEiB,OAAO,CAAC/J,EAAE,IAAI+J,OAAO,CAACjB,GAAG;QAC9B9I,EAAE,EAAE+J,OAAO,CAAC/J,EAAE,IAAI+J,OAAO,CAACjB,GAAG;QAC7BvG,OAAO,EAAEwH,OAAO,CAACxH,OAAO,IAAI,EAAE;QAC9BqG,MAAM,EAAEuO,gBAAgB;QACxBhP,SAAS,EAAE,IAAI,CAACC,aAAa,CAAC2B,OAAO,CAAC5B,SAAS,CAAC;QAChDP,MAAM,EAAEmC,OAAO,CAACnC,MAAM,GAAG,IAAI,CAACQ,aAAa,CAAC2B,OAAO,CAACnC,MAAM,CAAC,GAAG1I,SAAS;QACvEgV,WAAW,EAAEuD,qBAAqB;QAClCE,QAAQ,EAAE5N,OAAO,CAAC4N,QAAQ,IAAI;OAC/B;MAED;MACA,IAAIJ,kBAAkB,EAAE;QACtBvD,iBAAiB,CAACwD,QAAQ,GAAGD,kBAAkB;;MAGjD,IAAI,CAACnb,MAAM,CAACwK,KAAK,CAAC,kDAAkD,EAAE;QACpEY,SAAS,EAAEwM,iBAAiB,CAAChU,EAAE;QAC/BqC,QAAQ,EAAE2R,iBAAiB,CAACpL,MAAM,EAAE5I;OACrC,CAAC;MAEF,OAAOgU,iBAAiB;KACzB,CAAC,OAAOzT,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,6CAA6C,EAC7CA,KAAK,YAAY4B,KAAK,GAAG5B,KAAK,GAAG,IAAI4B,KAAK,CAACyV,MAAM,CAACrX,KAAK,CAAC,CAAC,EACzDwJ,OAAO,CACR;MACD,MAAM,IAAI5H,KAAK,CACb,gCACE5B,KAAK,YAAY4B,KAAK,GAAG5B,KAAK,CAACwJ,OAAO,GAAG6N,MAAM,CAACrX,KAAK,CACvD,EAAE,CACH;;EAEL;EAEOsI,aAAaA,CAAC6J,IAAS;IAC5B,IAAI,CAACA,IAAI,EAAE;MACT,MAAM,IAAIvQ,KAAK,CAAC,yBAAyB,CAAC;;IAG5C;IACA,MAAMY,MAAM,GAAG2P,IAAI,CAAC1S,EAAE,IAAI0S,IAAI,CAAC5J,GAAG;IAClC,IAAI,CAAC/F,MAAM,EAAE;MACX,MAAM,IAAIZ,KAAK,CAAC,qBAAqB,CAAC;;IAGxC;IACA,MAAMgH,QAAQ,GAAGuJ,IAAI,CAACvJ,QAAQ,IAAI,cAAc;IAChD,MAAMiO,KAAK,GAAG1E,IAAI,CAAC0E,KAAK,IAAI,QAAQrU,MAAM,cAAc;IACxD,MAAMuU,QAAQ,GACZ5E,IAAI,CAAC4E,QAAQ,KAAKpY,SAAS,IAAIwT,IAAI,CAAC4E,QAAQ,KAAK,IAAI,GACjD5E,IAAI,CAAC4E,QAAQ,GACb,IAAI;IACV,MAAMD,IAAI,GAAG3E,IAAI,CAAC2E,IAAI,IAAI,MAAM;IAEhC;IACA,OAAO;MACLvO,GAAG,EAAE/F,MAAM;MACX/C,EAAE,EAAE+C,MAAM;MACVoG,QAAQ,EAAEA,QAAQ;MAClBiO,KAAK,EAAEA,KAAK;MACZC,IAAI,EAAEA,IAAI;MACVC,QAAQ,EAAEA,QAAQ;MAClB;MACAO,KAAK,EAAEnF,IAAI,CAACmF,KAAK,IAAI,IAAI;MACzBC,GAAG,EAAEpF,IAAI,CAACoF,GAAG;MACbxF,QAAQ,EAAEI,IAAI,CAACJ,QAAQ,IAAI,KAAK;MAChCyF,UAAU,EAAErF,IAAI,CAACqF,UAAU,GAAG,IAAIlQ,IAAI,CAAC6K,IAAI,CAACqF,UAAU,CAAC,GAAG7Y,SAAS;MACnE8Y,SAAS,EAAEtF,IAAI,CAACsF,SAAS,GAAG,IAAInQ,IAAI,CAAC6K,IAAI,CAACsF,SAAS,CAAC,GAAG9Y,SAAS;MAChE+Y,SAAS,EAAEvF,IAAI,CAACuF,SAAS,GAAG,IAAIpQ,IAAI,CAAC6K,IAAI,CAACuF,SAAS,CAAC,GAAG/Y,SAAS;MAChEgZ,cAAc,EAAExF,IAAI,CAACwF,cAAc;MACnCC,cAAc,EAAEzF,IAAI,CAACyF,cAAc;MACnCC,SAAS,EAAE1F,IAAI,CAAC0F;KACjB;EACH;EACQnV,qBAAqBA,CAACuG,IAAkB;IAC9C,IAAI,CAACA,IAAI,EAAE;MACT,IAAI,CAACpN,MAAM,CAACmE,KAAK,CACf,kEAAkE,CACnE;MACD,MAAM,IAAI4B,KAAK,CAAC,iCAAiC,CAAC;;IAGpD,IAAI;MACF;MACA,IAAI,CAACqH,IAAI,CAACxJ,EAAE,IAAI,CAACwJ,IAAI,CAACV,GAAG,EAAE;QACzB,IAAI,CAAC1M,MAAM,CAACmE,KAAK,CACf,6CAA6C,EAC7CrB,SAAS,EACTsK,IAAI,CACL;QACD,MAAM,IAAIrH,KAAK,CAAC,6BAA6B,CAAC;;MAGhD;MACA,MAAMkW,sBAAsB,GAAG,EAAE;MACjC,IAAI7O,IAAI,CAACK,YAAY,IAAI1J,KAAK,CAACmY,OAAO,CAAC9O,IAAI,CAACK,YAAY,CAAC,EAAE;QACzD,KAAK,MAAM0O,WAAW,IAAI/O,IAAI,CAACK,YAAY,EAAE;UAC3C,IAAI;YACF,IAAI0O,WAAW,EAAE;cACfF,sBAAsB,CAACzK,IAAI,CAAC,IAAI,CAAC/E,aAAa,CAAC0P,WAAW,CAAC,CAAC;;WAE/D,CAAC,OAAOhY,KAAK,EAAE;YACd,IAAI,CAACnE,MAAM,CAACwQ,IAAI,CACd,0DAA0D,EAC1DrM,KAAK,CACN;;;OAGN,MAAM;QACL,IAAI,CAACnE,MAAM,CAACwQ,IAAI,CACd,iFAAiF,EACjFpD,IAAI,CACL;;MAGH;MACA,MAAMgP,kBAAkB,GAAG,EAAE;MAC7B,IAAIhP,IAAI,CAACnC,QAAQ,IAAIlH,KAAK,CAACmY,OAAO,CAAC9O,IAAI,CAACnC,QAAQ,CAAC,EAAE;QACjD,IAAI,CAACjL,MAAM,CAACwK,KAAK,CAAC,mDAAmD,EAAE;UACrEyG,KAAK,EAAE7D,IAAI,CAACnC,QAAQ,CAACJ;SACtB,CAAC;QAEF,KAAK,MAAM8C,OAAO,IAAIP,IAAI,CAACnC,QAAQ,EAAE;UACnC,IAAI;YACF,IAAI0C,OAAO,EAAE;cACX,MAAMiK,iBAAiB,GAAG,IAAI,CAAC1S,gBAAgB,CAACyI,OAAO,CAAC;cACxD,IAAI,CAAC3N,MAAM,CAACwK,KAAK,CACf,kDAAkD,EAClD;gBACEY,SAAS,EAAEwM,iBAAiB,CAAChU,EAAE;gBAC/BuC,OAAO,EAAEyR,iBAAiB,CAACzR,OAAO,EAAEyP,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;gBACpDpJ,MAAM,EAAEoL,iBAAiB,CAACpL,MAAM,EAAEO;eACnC,CACF;cACDqP,kBAAkB,CAAC5K,IAAI,CAACoG,iBAAiB,CAAC;;WAE7C,CAAC,OAAOzT,KAAK,EAAE;YACd,IAAI,CAACnE,MAAM,CAACwQ,IAAI,CACd,sEAAsE,EACtErM,KAAK,CACN;;;OAGN,MAAM;QACL,IAAI,CAACnE,MAAM,CAACwK,KAAK,CACf,8EAA8E,CAC/E;;MAGH;MACA,IAAI6R,qBAAqB,GAAG,IAAI;MAChC,IAAIjP,IAAI,CAAC0L,WAAW,EAAE;QACpB,IAAI;UACFuD,qBAAqB,GAAG,IAAI,CAACnX,gBAAgB,CAACkI,IAAI,CAAC0L,WAAW,CAAC;SAChE,CAAC,OAAO3U,KAAK,EAAE;UACd,IAAI,CAACnE,MAAM,CAACwQ,IAAI,CACd,6DAA6D,EAC7DrM,KAAK,CACN;;;MAIL;MACA,MAAMqJ,sBAAsB,GAAG;QAC7B,GAAGJ,IAAI;QACPV,GAAG,EAAEU,IAAI,CAACxJ,EAAE,IAAIwJ,IAAI,CAACV,GAAG;QACxB9I,EAAE,EAAEwJ,IAAI,CAACxJ,EAAE,IAAIwJ,IAAI,CAACV,GAAG;QACvBe,YAAY,EAAEwO,sBAAsB;QACpChR,QAAQ,EAAEmR,kBAAkB;QAC5BtD,WAAW,EAAEuD,qBAAqB;QAClCC,WAAW,EAAElP,IAAI,CAACkP,WAAW,IAAI,CAAC;QAClCtO,OAAO,EAAE,CAAC,CAACZ,IAAI,CAACY,OAAO;QACvB4N,SAAS,EAAE,IAAI,CAAC5P,aAAa,CAACoB,IAAI,CAACwO,SAAS,CAAC;QAC7CC,SAAS,EAAE,IAAI,CAAC7P,aAAa,CAACoB,IAAI,CAACyO,SAAS;OAC7C;MAED,IAAI,CAAC7b,MAAM,CAACwK,KAAK,CACf,uDAAuD,EACvD;QACElF,cAAc,EAAEkI,sBAAsB,CAAC5J,EAAE;QACzC2Y,gBAAgB,EAAEN,sBAAsB,CAACpR,MAAM;QAC/C2R,YAAY,EAAEJ,kBAAkB,CAACvR;OAClC,CACF;MAED,OAAO2C,sBAAsB;KAC9B,CAAC,OAAOrJ,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,kDAAkD,EAClDA,KAAK,YAAY4B,KAAK,GAAG5B,KAAK,GAAG,IAAI4B,KAAK,CAACyV,MAAM,CAACrX,KAAK,CAAC,CAAC,EACzDiJ,IAAI,CACL;MACD,MAAM,IAAIrH,KAAK,CACb,qCACE5B,KAAK,YAAY4B,KAAK,GAAG5B,KAAK,CAACwJ,OAAO,GAAG6N,MAAM,CAACrX,KAAK,CACvD,EAAE,CACH;;EAEL;EACQ6H,aAAaA,CAACnJ,IAA+B;IACnD,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI4I,IAAI,EAAE;IAC5B,IAAI;MACF,OAAO,OAAO5I,IAAI,KAAK,QAAQ,GAAG,IAAI4I,IAAI,CAAC5I,IAAI,CAAC,GAAGA,IAAI;KACxD,CAAC,OAAOsB,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACwQ,IAAI,CAAC,yBAAyB3N,IAAI,EAAE,EAAEsB,KAAK,CAAC;MACxD,OAAO,IAAIsH,IAAI,EAAE;;EAErB;EAEA;EACQc,QAAQA,CAAC1J,IAA+B;IAC9C,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI4I,IAAI,EAAE;IAC5B,IAAI;MACF,OAAO,OAAO5I,IAAI,KAAK,QAAQ,GAAG,IAAI4I,IAAI,CAAC5I,IAAI,CAAC,GAAGA,IAAI;KACxD,CAAC,OAAOsB,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACwQ,IAAI,CAAC,+BAA+B3N,IAAI,EAAE,EAAEsB,KAAK,CAAC;MAC9D,OAAO,IAAIsH,IAAI,EAAE;;EAErB;EAOQoO,qBAAqBA,CAAClW,YAA0B;IACtD,IAAI,CAAC3D,MAAM,CAACwK,KAAK,CACf,gBAAgB,EAChB,0BAA0B,EAC1B7G,YAAY,CACb;IAED,IAAI,CAACA,YAAY,EAAE;MACjB,IAAI,CAAC3D,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,mCAAmC,CAAC;MACxE,MAAM,IAAI4B,KAAK,CAAC,0BAA0B,CAAC;;IAG7C;IACA,MAAMU,cAAc,GAAG9C,YAAY,CAACC,EAAE,IAAKD,YAAoB,CAAC+I,GAAG;IACnE,IAAI,CAACjG,cAAc,EAAE;MACnB,IAAI,CAACzG,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,4BAA4B,EAC5BR,YAAY,CACb;MACD,MAAM,IAAIoC,KAAK,CAAC,6BAA6B,CAAC;;IAGhD,IAAI,CAACpC,YAAY,CAACoI,SAAS,EAAE;MAC3B,IAAI,CAAC/L,MAAM,CAACwQ,IAAI,CACd,gBAAgB,EAChB,uDAAuD,EACvD7M,YAAY,CACb;MACDA,YAAY,CAACoI,SAAS,GAAG,IAAIN,IAAI,EAAE;;IAGrC,IAAI;MACF,MAAMmO,UAAU,GAAG;QACjB,GAAGjW,YAAY;QACf+I,GAAG,EAAEjG,cAAc;QACnB7C,EAAE,EAAE6C,cAAc;QAClBsF,SAAS,EAAE,IAAIN,IAAI,CAAC9H,YAAY,CAACoI,SAAS,CAAC;QAC3C,IAAIpI,YAAY,CAACsC,QAAQ,IAAI;UAC3BA,QAAQ,EAAE,IAAI,CAACwW,eAAe,CAAC9Y,YAAY,CAACsC,QAAQ;SACrD,CAAC;QACF,IAAItC,YAAY,CAACgK,OAAO,IAAI;UAC1BA,OAAO,EAAE,IAAI,CAAC+O,mBAAmB,CAAC/Y,YAAY,CAACgK,OAAO;SACvD;OACF;MAED,IAAI,CAAC3N,MAAM,CAACwK,KAAK,CACf,gBAAgB,EAChB,gCAAgC,EAChCoP,UAAU,CACX;MACD,OAAOA,UAAU;KAClB,CAAC,OAAOzV,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,gCAAgC,EAChCA,KAAK,CACN;MACD,MAAMA,KAAK;;EAEf;EACQsY,eAAeA,CAACjQ,MAAW;IACjC,OAAO;MACL5I,EAAE,EAAE4I,MAAM,CAAC5I,EAAE;MACbmJ,QAAQ,EAAEP,MAAM,CAACO,QAAQ;MACzB,IAAIP,MAAM,CAACiP,KAAK,IAAI;QAAEA,KAAK,EAAEjP,MAAM,CAACiP;MAAK,CAAE;KAC5C;EACH;EAEA;;;;;EAKQiB,mBAAmBA,CAAC/O,OAAY;IACtC,IAAI,CAACA,OAAO,EAAE,OAAO,IAAI;IAEzB,OAAO;MACL/J,EAAE,EAAE+J,OAAO,CAAC/J,EAAE,IAAI+J,OAAO,CAACjB,GAAG;MAC7BvG,OAAO,EAAEwH,OAAO,CAACxH,OAAO,IAAI,EAAE;MAC9BqD,IAAI,EAAEmE,OAAO,CAACnE,IAAI,IAAI,MAAM;MAC5BuC,SAAS,EAAE,IAAI,CAACQ,QAAQ,CAACoB,OAAO,CAAC5B,SAAS,CAAC;MAC3C+L,WAAW,EAAEnK,OAAO,CAACmK,WAAW,IAAI,EAAE;MACtC,IAAInK,OAAO,CAACnB,MAAM,IAAI;QAAEA,MAAM,EAAE,IAAI,CAACiQ,eAAe,CAAC9O,OAAO,CAACnB,MAAM;MAAC,CAAE;KACvE;EACH;EACA;;;;;EAKQyC,WAAWA,CACjB9O,aAA4C,EAC5Cwc,cAAA,GAA0B,IAAI;IAE9B,MAAMC,iBAAiB,GAAG7Y,KAAK,CAACmY,OAAO,CAAC/b,aAAa,CAAC,GAClDA,aAAa,GACb,CAACA,aAAa,CAAC;IAEnB,IAAI,CAACH,MAAM,CAACwK,KAAK,CACf,gBAAgB,EAChB,oCAAoCoS,iBAAiB,CAAC/R,MAAM,gBAAgB,CAC7E;IAED,IAAI+R,iBAAiB,CAAC/R,MAAM,KAAK,CAAC,EAAE;MAClC,IAAI,CAAC7K,MAAM,CAACwQ,IAAI,CAAC,gBAAgB,EAAE,qCAAqC,CAAC;MACzE;;IAGF;IACA,MAAMqM,kBAAkB,GAAGD,iBAAiB,CAACrgB,MAAM,CAChDuS,KAAK,IAAKA,KAAK,KAAKA,KAAK,CAAClL,EAAE,IAAKkL,KAAa,CAACpC,GAAG,CAAC,CACrD;IAED,IAAImQ,kBAAkB,CAAChS,MAAM,KAAK+R,iBAAiB,CAAC/R,MAAM,EAAE;MAC1D,IAAI,CAAC7K,MAAM,CAACwQ,IAAI,CACd,gBAAgB,EAChB,SACEoM,iBAAiB,CAAC/R,MAAM,GAAGgS,kBAAkB,CAAChS,MAChD,kCAAkC,CACnC;;IAGH,IAAIiS,UAAU,GAAG,CAAC;IAClB,IAAIC,YAAY,GAAG,CAAC;IAEpB;IACAF,kBAAkB,CAACnZ,OAAO,CAAC,CAACoL,KAAK,EAAEE,KAAK,KAAI;MAC1C,IAAI;QACF;QACA,MAAMgO,OAAO,GAAGlO,KAAK,CAAClL,EAAE,IAAKkL,KAAa,CAACpC,GAAG;QAC9C,IAAI,CAACsQ,OAAO,EAAE;UACZ,IAAI,CAAChd,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,0BAA0B,EAC1B2K,KAAK,CACN;UACD;;QAGF;QACA,MAAM8K,UAAU,GAAG,IAAI,CAACC,qBAAqB,CAAC/K,KAAK,CAAC;QAEpD;QACA,IAAI6N,cAAc,IAAI,IAAI,CAACvc,iBAAiB,CAAC2O,GAAG,CAAC6K,UAAU,CAAChW,EAAE,CAAC,EAAE;UAC/D,IAAI,CAAC5D,MAAM,CAACwK,KAAK,CACf,gBAAgB,EAChB,gBAAgBoP,UAAU,CAAChW,EAAE,oCAAoC,CAClE;UACDmZ,YAAY,EAAE;UACd;;QAGF;QACA,IAAI,CAAC3c,iBAAiB,CAACyD,GAAG,CAAC+V,UAAU,CAAChW,EAAE,EAAEgW,UAAU,CAAC;QACrDkD,UAAU,EAAE;QAEZ,IAAI,CAAC9c,MAAM,CAACwK,KAAK,CACf,gBAAgB,EAChB,sBAAsBoP,UAAU,CAAChW,EAAE,WAAW,CAC/C;OACF,CAAC,OAAOO,KAAK,EAAE;QACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iCAAiC6K,KAAK,GAAG,CAAC,GAAG,EAC7C7K,KAAK,CACN;;IAEL,CAAC,CAAC;IAEF,IAAI,CAACnE,MAAM,CAACwK,KAAK,CACf,gBAAgB,EAChB,0BAA0BsS,UAAU,WAAWC,YAAY,oBAAoB,IAAI,CAAC3c,iBAAiB,CAACoO,IAAI,EAAE,CAC7G;IAED;IACA,IAAI,CAACyO,8BAA8B,EAAE;EACvC;EACA;;;;EAIQA,8BAA8BA,CAAA;IACpC,MAAMC,gBAAgB,GAAGnZ,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC5D,iBAAiB,CAAC6D,MAAM,EAAE,CAAC;IAEpE;IACA,MAAMkL,mBAAmB,GAAG,IAAI,CAACC,uBAAuB,CAAC8N,gBAAgB,CAAC;IAE1E,IAAI,CAACld,MAAM,CAACwK,KAAK,CACf,cAAc2E,mBAAmB,CAACtE,MAAM,2DAA2D,CACpG;IAED,IAAI,CAAC1K,aAAa,CAAC2D,IAAI,CAACqL,mBAAmB,CAAC;IAC5C,IAAI,CAACjL,iBAAiB,EAAE;IACxB,IAAI,CAACmL,+BAA+B,EAAE;EACxC;EAEA;;;EAGQnL,iBAAiBA,CAAA;IACvB,MAAMgZ,gBAAgB,GAAGnZ,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC5D,iBAAiB,CAAC6D,MAAM,EAAE,CAAC;IACpE,MAAMkZ,mBAAmB,GAAGD,gBAAgB,CAAC3gB,MAAM,CAAEoT,CAAC,IAAK,CAACA,CAAC,CAAC9C,MAAM,CAAC;IACrE,MAAMoE,KAAK,GAAGkM,mBAAmB,CAACtS,MAAM;IAExC;IACA,IAAI,CAAC5K,IAAI,CAACgY,GAAG,CAAC,MAAK;MACjB,IAAI,CAAC3X,iBAAiB,CAACwD,IAAI,CAACmN,KAAK,CAAC;MAElC;MACA7I,MAAM,CAACgV,aAAa,CAClB,IAAIC,WAAW,CAAC,0BAA0B,EAAE;QAC1CC,MAAM,EAAE;UAAErM;QAAK;OAChB,CAAC,CACH;IACH,CAAC,CAAC;EACJ;EAEA;;;;EAIQ+I,uBAAuBA,CAACrW,YAA0B;IACxD,IAAI,CAACsL,WAAW,CAACtL,YAAY,EAAE,IAAI,CAAC;EACtC;EACA;;;;;EAKQqO,wBAAwBA,CAACuL,GAAa,EAAE1Q,MAAe;IAC7D0Q,GAAG,CAAC7Z,OAAO,CAAEE,EAAE,IAAI;MACjB,MAAMkL,KAAK,GAAG,IAAI,CAAC1O,iBAAiB,CAACmR,GAAG,CAAC3N,EAAE,CAAC;MAC5C,IAAIkL,KAAK,EAAE;QACT,IAAI,CAAC1O,iBAAiB,CAACyD,GAAG,CAACD,EAAE,EAAE;UAC7B,GAAGkL,KAAK;UACRjC,MAAM;UACNrB,MAAM,EAAEqB,MAAM,GAAG,IAAIpB,IAAI,EAAE,CAAC1I,WAAW,EAAE,GAAGD;SAC7C,CAAC;;IAEN,CAAC,CAAC;IACF,IAAI,CAACma,8BAA8B,EAAE;EACvC;EAEA;;;;;EAKQvM,4BAA4BA,CAACU,eAAyB;IAC5DnJ,OAAO,CAACC,GAAG,CACT,4CAA4C,EAC5CkJ,eAAe,CAACvG,MAAM,EACtB,eAAe,CAChB;IACD5C,OAAO,CAACC,GAAG,CACT,2CAA2C,EAC3C,IAAI,CAAC9H,iBAAiB,CAACoO,IAAI,CAC5B;IAED,IAAIiC,YAAY,GAAG,CAAC;IACpBW,eAAe,CAAC1N,OAAO,CAAEE,EAAE,IAAI;MAC7B,IAAI,IAAI,CAACxD,iBAAiB,CAAC2O,GAAG,CAACnL,EAAE,CAAC,EAAE;QAClCqE,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAEtE,EAAE,CAAC;QAChE,IAAI,CAACxD,iBAAiB,CAACoY,MAAM,CAAC5U,EAAE,CAAC;QACjC6M,YAAY,EAAE;OACf,MAAM;QACLxI,OAAO,CAACC,GAAG,CACT,yDAAyD,EACzDtE,EAAE,CACH;;IAEL,CAAC,CAAC;IAEFqE,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEuI,YAAY,EAAE,eAAe,CAAC;IAC5ExI,OAAO,CAACC,GAAG,CACT,0CAA0C,EAC1C,IAAI,CAAC9H,iBAAiB,CAACoO,IAAI,CAC5B;IAED,IAAIiC,YAAY,GAAG,CAAC,EAAE;MACpBxI,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;MAC/D,IAAI,CAAC+U,8BAA8B,EAAE;;IAGvC,OAAOxM,YAAY;EACrB;EAEA;;;;;;EAMQG,mBAAmBA,CACzBzM,KAAU,EACVqZ,SAAiB,EACjBC,gBAAqB;IAErB,IAAI,CAACzd,MAAM,CAACmE,KAAK,CAAC,gBAAgB,EAAE,kBAAkBqZ,SAAS,GAAG,EAAErZ,KAAK,CAAC;IAC1E,OAAOnI,EAAE,CAACyhB,gBAAgB,CAAC;EAC7B;EACA;EACApY,WAAWA,CAACC,cAAsB;IAChC,MAAMqB,MAAM,GAAG,IAAI,CAACmG,gBAAgB,EAAE;IACtC,IAAI,CAACnG,MAAM,EAAE;MACX,IAAI,CAAC3G,MAAM,CAACwQ,IAAI,CAAC,gBAAgB,EAAE,iCAAiC,CAAC;MACrE,OAAOxU,EAAE,CAAC,KAAK,CAAC;;IAGlB,OAAO,IAAI,CAAC+D,MAAM,CACfwF,MAAM,CAAsB;MAC3BC,QAAQ,EAAE1H,qBAAqB;MAC/B2H,SAAS,EAAE;QACTwR,KAAK,EAAE;UACL3R,cAAc;UACdqB;;;KAGL,CAAC,CACDhC,IAAI,CACHvI,GAAG,CAAEsJ,MAAM,IAAKA,MAAM,CAACd,IAAI,EAAES,WAAW,IAAI,KAAK,CAAC,EAClDhJ,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iCAAiC,EACjCA,KAAK,CACN;MACD,OAAOlI,UAAU,CACf,MAAM,IAAI8J,KAAK,CAAC,kCAAkC,CAAC,CACpD;IACH,CAAC,CAAC,CACH;EACL;EAEAJ,UAAUA,CAACL,cAAsB;IAC/B,MAAMqB,MAAM,GAAG,IAAI,CAACmG,gBAAgB,EAAE;IACtC,IAAI,CAACnG,MAAM,EAAE;MACX,IAAI,CAAC3G,MAAM,CAACwQ,IAAI,CAAC,gBAAgB,EAAE,gCAAgC,CAAC;MACpE,OAAOxU,EAAE,CAAC,KAAK,CAAC;;IAGlB,OAAO,IAAI,CAAC+D,MAAM,CACfwF,MAAM,CAAqB;MAC1BC,QAAQ,EAAEzH,oBAAoB;MAC9B0H,SAAS,EAAE;QACTwR,KAAK,EAAE;UACL3R,cAAc;UACdqB;;;KAGL,CAAC,CACDhC,IAAI,CACHvI,GAAG,CAAEsJ,MAAM,IAAKA,MAAM,CAACd,IAAI,EAAEe,UAAU,IAAI,KAAK,CAAC,EACjDtJ,UAAU,CAAE8H,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,gBAAgB,EAChB,iCAAiC,EACjCA,KAAK,CACN;MACD,OAAOlI,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEA;EACA;EACA;EAEA;;;;;;;;;EASAM,WAAWA,CACTH,UAAkB,EAClBC,OAAe,EACfC,IAAW,EACXsX,WAAA,GAAmB,MAAM,EACzBpY,cAAuB;IAEvB2C,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE;MAC1DhC,UAAU;MACVC,OAAO,EAAEA,OAAO,EAAEyP,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;MAClC+H,OAAO,EAAE,CAAC,CAACvX,IAAI;MACfwX,QAAQ,EAAExX,IAAI,EAAEW,IAAI;MACpB8W,QAAQ,EAAEzX,IAAI,EAAEoD,IAAI;MACpBsU,QAAQ,EAAE1X,IAAI,EAAEoI,IAAI;MACpBkP,WAAW;MACXpY;KACD,CAAC;IAEF,IAAI,CAACY,UAAU,EAAE;MACf,MAAM/B,KAAK,GAAG,IAAI4B,KAAK,CAAC,yBAAyB,CAAC;MAClDkC,OAAO,CAAC9D,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,OAAOlI,UAAU,CAAC,MAAMkI,KAAK,CAAC;;IAGhC;IACA,MAAMsB,SAAS,GAAQ;MACrBS,UAAU;MACVC,OAAO,EAAEA,OAAO,IAAI,EAAE;MACtBqD,IAAI,EAAEkU;KACP;IAED;IACA,IAAIpY,cAAc,EAAE;MAClBG,SAAS,CAACH,cAAc,GAAGA,cAAc;;IAG3C;IACA,IAAIc,IAAI,EAAE;MACRX,SAAS,CAACW,IAAI,GAAGA,IAAI;MACrB6B,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE;QAC1DnB,IAAI,EAAEX,IAAI,CAACW,IAAI;QACfyC,IAAI,EAAEpD,IAAI,CAACoD,IAAI;QACfgF,IAAI,EAAEpI,IAAI,CAACoI;OACZ,CAAC;;IAGJvG,OAAO,CAACC,GAAG,CACT,sDAAsD,EACtDzC,SAAS,CACV;IAED,OAAO,IAAI,CAAC1F,MAAM,CACfwF,MAAM,CAAsB;MAC3BC,QAAQ,EAAErI,qBAAqB;MAC/BsI,SAAS;MACTsY,OAAO,EAAE;QACPC,YAAY,EAAE,CAAC,CAAC5X,IAAI,CAAE;;KAEzB,CAAC,CACDzB,IAAI,CACHvI,GAAG,CAAEsJ,MAAM,IAAI;MACbuC,OAAO,CAACC,GAAG,CACT,iDAAiD,EACjDxC,MAAM,CACP;MAED,IAAI,CAACA,MAAM,CAACd,IAAI,EAAEyB,WAAW,EAAE;QAC7B,MAAM,IAAIN,KAAK,CAAC,sCAAsC,CAAC;;MAGzD,MAAM4H,OAAO,GAAGjI,MAAM,CAACd,IAAI,CAACyB,WAAW;MACvC4B,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAE;QAC5DtE,EAAE,EAAE+J,OAAO,CAAC/J,EAAE;QACd4F,IAAI,EAAEmE,OAAO,CAACnE,IAAI;QAClBrD,OAAO,EAAEwH,OAAO,CAACxH,OAAO,EAAEyP,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;QAC1CqI,cAAc,EAAE,CAAC,CAACtQ,OAAO,CAACmK,WAAW,EAAEjN;OACxC,CAAC;MAEF;MACA,MAAM+M,iBAAiB,GAAG,IAAI,CAAC1S,gBAAgB,CAACyI,OAAO,CAAC;MACxD1F,OAAO,CAACC,GAAG,CACT,yCAAyC,EACzC0P,iBAAiB,CAClB;MAED,OAAOA,iBAAiB;IAC1B,CAAC,CAAC,EACFvb,UAAU,CAAE8H,KAAK,IAAI;MACnB8D,OAAO,CAAC9D,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAElD;MACA,IAAI+Z,YAAY,GAAG,mCAAmC;MACtD,IAAI/Z,KAAK,CAACoL,YAAY,EAAE;QACtB2O,YAAY,GAAG,4BAA4B;OAC5C,MAAM,IAAI/Z,KAAK,CAACmL,aAAa,EAAEzE,MAAM,GAAG,CAAC,EAAE;QAC1CqT,YAAY,GAAG/Z,KAAK,CAACmL,aAAa,CAAC,CAAC,CAAC,CAAC3B,OAAO,IAAIuQ,YAAY;;MAG/D,OAAOjiB,UAAU,CAAC,MAAM,IAAI8J,KAAK,CAACmY,YAAY,CAAC,CAAC;IAClD,CAAC,CAAC,CACH;EACL;EAEA;EACA;EACA;EAEA;;;EAGAC,iBAAiBA,CAACpS,SAAoC;IACpD,IAAI,CAACA,SAAS,EAAE,OAAO,cAAc;IACrC,IAAI;MACF,MAAMlJ,IAAI,GAAGkJ,SAAS,YAAYN,IAAI,GAAGM,SAAS,GAAG,IAAIN,IAAI,CAACM,SAAS,CAAC;MACxE,OAAOlJ,IAAI,CAACub,kBAAkB,CAAC,EAAE,EAAE;QACjCC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE;OACT,CAAC;KACH,CAAC,OAAOpa,KAAK,EAAE;MACd,OAAO,cAAc;;EAEzB;EAEA;;;EAGAqa,gBAAgBA,CAAC7C,UAAqC;IACpD,IAAI,CAACA,UAAU,EAAE,OAAO,SAAS;IACjC,MAAM8C,cAAc,GAClB9C,UAAU,YAAYlQ,IAAI,GAAGkQ,UAAU,GAAG,IAAIlQ,IAAI,CAACkQ,UAAU,CAAC;IAChE,MAAMhP,GAAG,GAAG,IAAIlB,IAAI,EAAE;IACtB,MAAMiT,SAAS,GACbvM,IAAI,CAACwM,GAAG,CAAChS,GAAG,CAAC2N,OAAO,EAAE,GAAGmE,cAAc,CAACnE,OAAO,EAAE,CAAC,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAEvE,IAAIoE,SAAS,GAAG,EAAE,EAAE;MAClB,OAAO,UAAUD,cAAc,CAACL,kBAAkB,CAAC,EAAE,EAAE;QACrDC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;OACT,CAAC,EAAE;;IAEN,OAAO,UAAUG,cAAc,CAACG,kBAAkB,EAAE,EAAE;EACxD;EAEA;;;EAGAC,iBAAiBA,CAAC9S,SAAoC;IACpD,IAAI,CAACA,SAAS,EAAE,OAAO,cAAc;IAErC,IAAI;MACF,MAAMlJ,IAAI,GAAGkJ,SAAS,YAAYN,IAAI,GAAGM,SAAS,GAAG,IAAIN,IAAI,CAACM,SAAS,CAAC;MACxE,MAAM+S,KAAK,GAAG,IAAIrT,IAAI,EAAE;MAExB,IAAI5I,IAAI,CAACkc,YAAY,EAAE,KAAKD,KAAK,CAACC,YAAY,EAAE,EAAE;QAChD,OAAOlc,IAAI,CAACub,kBAAkB,CAAC,EAAE,EAAE;UACjCC,IAAI,EAAE,SAAS;UACfC,MAAM,EAAE;SACT,CAAC;;MAGJ,MAAMU,SAAS,GAAG,IAAIvT,IAAI,CAACqT,KAAK,CAAC;MACjCE,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;MAE1C,IAAIrc,IAAI,CAACkc,YAAY,EAAE,KAAKC,SAAS,CAACD,YAAY,EAAE,EAAE;QACpD,OAAO,SAASlc,IAAI,CAACub,kBAAkB,CAAC,EAAE,EAAE;UAC1CC,IAAI,EAAE,SAAS;UACfC,MAAM,EAAE;SACT,CAAC,EAAE;;MAGN,MAAMa,GAAG,GAAGtc,IAAI,CACb+b,kBAAkB,CAAC,OAAO,EAAE;QAAEQ,OAAO,EAAE;MAAO,CAAE,CAAC,CACjDC,WAAW,EAAE;MAChB,OAAO,GAAGF,GAAG,MAAMtc,IAAI,CAACub,kBAAkB,CAAC,EAAE,EAAE;QAC7CC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;OACT,CAAC,EAAE;KACL,CAAC,OAAOna,KAAK,EAAE;MACd,OAAO,cAAc;;EAEzB;EAEA;;;EAGAmb,oBAAoBA,CAACrU,QAAe,EAAE+D,KAAa;IACjD,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;IAE5B,IAAI;MACF,MAAMuQ,UAAU,GAAGtU,QAAQ,CAAC+D,KAAK,CAAC;MAClC,MAAMwQ,OAAO,GAAGvU,QAAQ,CAAC+D,KAAK,GAAG,CAAC,CAAC;MAEnC,IAAI,CAACuQ,UAAU,EAAExT,SAAS,IAAI,CAACyT,OAAO,EAAEzT,SAAS,EAAE,OAAO,IAAI;MAE9D,MAAM0T,WAAW,GAAG,IAAI,CAACC,oBAAoB,CAACH,UAAU,CAACxT,SAAS,CAAC;MACnE,MAAM4T,QAAQ,GAAG,IAAI,CAACD,oBAAoB,CAACF,OAAO,CAACzT,SAAS,CAAC;MAE7D,OAAO0T,WAAW,KAAKE,QAAQ;KAChC,CAAC,OAAOxb,KAAK,EAAE;MACd,OAAO,KAAK;;EAEhB;EAEQub,oBAAoBA,CAAC3T,SAAoC;IAC/D,IAAI,CAACA,SAAS,EAAE,OAAO,cAAc;IACrC,IAAI;MACF,OAAO,CACLA,SAAS,YAAYN,IAAI,GAAGM,SAAS,GAAG,IAAIN,IAAI,CAACM,SAAS,CAAC,EAC3DgT,YAAY,EAAE;KACjB,CAAC,OAAO5a,KAAK,EAAE;MACd,OAAO,cAAc;;EAEzB;EAEA;;;EAGAyb,WAAWA,CAACC,QAAiB;IAC3B,IAAI,CAACA,QAAQ,EAAE,OAAO,SAAS;IAC/B,IAAIA,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,UAAU;IACpD,IAAID,QAAQ,CAAC1R,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,aAAa;IAClD,IAAI0R,QAAQ,CAAC1R,QAAQ,CAAC,MAAM,CAAC,IAAI0R,QAAQ,CAAC1R,QAAQ,CAAC,QAAQ,CAAC,EAC1D,OAAO,cAAc;IACvB,IAAI0R,QAAQ,CAAC1R,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,eAAe;IACtD,IAAI0R,QAAQ,CAAC1R,QAAQ,CAAC,YAAY,CAAC,EAAE,OAAO,oBAAoB;IAChE,IAAI0R,QAAQ,CAAC1R,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,eAAe;IACtD,IAAI0R,QAAQ,CAAC1R,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,eAAe;IACtD,IAAI0R,QAAQ,CAAC1R,QAAQ,CAAC,KAAK,CAAC,IAAI0R,QAAQ,CAAC1R,QAAQ,CAAC,YAAY,CAAC,EAC7D,OAAO,iBAAiB;IAC1B,OAAO,SAAS;EAClB;EAEA;;;EAGA4R,WAAWA,CAACF,QAAiB;IAC3B,IAAI,CAACA,QAAQ,EAAE,OAAO,MAAM;IAE5B,MAAMG,OAAO,GAA2B;MACtC,QAAQ,EAAE,OAAO;MACjB,iBAAiB,EAAE,KAAK;MACxB,oBAAoB,EAAE,UAAU;MAChC,yEAAyE,EACvE,UAAU;MACZ,0BAA0B,EAAE,OAAO;MACnC,mEAAmE,EACjE,OAAO;MACT,+BAA+B,EAAE,YAAY;MAC7C,2EAA2E,EACzE,YAAY;MACd,QAAQ,EAAE,OAAO;MACjB,QAAQ,EAAE,OAAO;MACjB,iBAAiB,EAAE,aAAa;MAChC,8BAA8B,EAAE;KACjC;IAED,KAAK,MAAM,CAACC,GAAG,EAAE7P,KAAK,CAAC,IAAIxI,MAAM,CAACsY,OAAO,CAACF,OAAO,CAAC,EAAE;MAClD,IAAIH,QAAQ,CAAC1R,QAAQ,CAAC8R,GAAG,CAAC,EAAE,OAAO7P,KAAK;;IAE1C,OAAO,MAAM;EACf;EAEA;;;EAGA+P,QAAQA,CAACxS,OAAY;IACnB,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACmK,WAAW,IAAInK,OAAO,CAACmK,WAAW,CAACjN,MAAM,KAAK,CAAC,EAAE;MACxE,OAAO,KAAK;;IAGd,MAAMuV,UAAU,GAAGzS,OAAO,CAACmK,WAAW,CAAC,CAAC,CAAC;IACzC,IAAI,CAACsI,UAAU,IAAI,CAACA,UAAU,CAAC5W,IAAI,EAAE;MACnC,OAAO,KAAK;;IAGd,MAAMA,IAAI,GAAG4W,UAAU,CAAC5W,IAAI,CAACkM,QAAQ,EAAE;IACvC,OAAOlM,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,OAAO;EAC7C;EAEA;;;EAGA6W,cAAcA,CAAC1S,OAAY;IACzB,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;IAE1B;IACA,IACEA,OAAO,CAACnE,IAAI,KAAK5M,WAAW,CAACib,aAAa,IAC1ClK,OAAO,CAACnE,IAAI,KAAK5M,WAAW,CAACib,aAAa,EAC1C;MACA,OAAO,IAAI;;IAGb;IACA,IAAIlK,OAAO,CAACmK,WAAW,IAAInK,OAAO,CAACmK,WAAW,CAACjN,MAAM,GAAG,CAAC,EAAE;MACzD,OAAO8C,OAAO,CAACmK,WAAW,CAACC,IAAI,CAAEC,GAAQ,IAAI;QAC3C,MAAMxO,IAAI,GAAGwO,GAAG,CAACxO,IAAI,EAAEkM,QAAQ,EAAE;QACjC,OACElM,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,eAAe,IACvBmE,OAAO,CAAC4N,QAAQ,EAAE8E,cAAc,KAC9B7W,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,OAAO,CAAE;MAE7C,CAAC,CAAC;;IAGJ;IACA,OAAO,CAAC,CAACmE,OAAO,CAAC4N,QAAQ,EAAE8E,cAAc;EAC3C;EAEA;;;EAGAC,kBAAkBA,CAAC3S,OAAY;IAC7B,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACmK,WAAW,IAAInK,OAAO,CAACmK,WAAW,CAACjN,MAAM,KAAK,CAAC,EAAE;MACxE,OAAO,EAAE;;IAGX,MAAM0V,eAAe,GAAG5S,OAAO,CAACmK,WAAW,CAAC/J,IAAI,CAAEiK,GAAQ,IAAI;MAC5D,MAAMxO,IAAI,GAAGwO,GAAG,CAACxO,IAAI,EAAEkM,QAAQ,EAAE;MACjC,OACElM,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,OAAO,IAChBA,IAAI,KAAK,OAAO;IAEpB,CAAC,CAAC;IAEF,OAAO+W,eAAe,EAAEjF,GAAG,IAAI,EAAE;EACnC;EAEA;;;EAGAkF,uBAAuBA,CAAC7S,OAAY;IAClC,IAAI,CAACA,OAAO,EAAE,OAAO,CAAC;IAEtB;IACA,IAAIA,OAAO,CAAC4N,QAAQ,EAAEpS,QAAQ,EAAE;MAC9B,OAAOwE,OAAO,CAAC4N,QAAQ,CAACpS,QAAQ;;IAGlC;IACA,IAAIwE,OAAO,CAACmK,WAAW,IAAInK,OAAO,CAACmK,WAAW,CAACjN,MAAM,GAAG,CAAC,EAAE;MACzD,MAAM0V,eAAe,GAAG5S,OAAO,CAACmK,WAAW,CAAC/J,IAAI,CAAEiK,GAAQ,IAAI;QAC5D,MAAMxO,IAAI,GAAGwO,GAAG,CAACxO,IAAI,EAAEkM,QAAQ,EAAE;QACjC,OACElM,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,OAAO,IAChBA,IAAI,KAAK,OAAO;MAEpB,CAAC,CAAC;MAEF,IAAI+W,eAAe,IAAIA,eAAe,CAACpX,QAAQ,EAAE;QAC/C,OAAOoX,eAAe,CAACpX,QAAQ;;;IAInC,OAAO,CAAC;EACV;EAEA;;;EAGAsX,iBAAiBA,CAACzR,KAAa;IAC7B,MAAM0R,OAAO,GAAG,CACd,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CACxE;IACD,OAAOA,OAAO,CAAC1R,KAAK,GAAG0R,OAAO,CAAC7V,MAAM,CAAC;EACxC;EAEA;;;EAGA8V,mBAAmBA,CAACC,OAAe;IACjC,IAAI,CAACA,OAAO,IAAIA,OAAO,KAAK,CAAC,EAAE;MAC7B,OAAO,MAAM;;IAGf,MAAMC,OAAO,GAAG1O,IAAI,CAAC2O,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMG,gBAAgB,GAAG5O,IAAI,CAAC2O,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;IACjD,OAAO,GAAGC,OAAO,IAAIE,gBAAgB,CAACrL,QAAQ,EAAE,CAACsL,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACrE;EAEA;;;EAGAC,WAAWA,CAACtT,OAAY;IACtB,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACmK,WAAW,IAAInK,OAAO,CAACmK,WAAW,CAACjN,MAAM,KAAK,CAAC,EAAE;MACxE,OAAO,EAAE;;IAGX,MAAMuV,UAAU,GAAGzS,OAAO,CAACmK,WAAW,CAAC,CAAC,CAAC;IACzC,OAAOsI,UAAU,EAAE9E,GAAG,IAAI,EAAE;EAC9B;EAEA;;;EAGA4F,cAAcA,CAACvT,OAAY;IACzB,IAAI,CAACA,OAAO,EAAE,OAAO/Q,WAAW,CAACgQ,IAAI;IAErC,IAAI;MACF,IAAIe,OAAO,CAACnE,IAAI,EAAE;QAChB,MAAM2X,OAAO,GAAGxT,OAAO,CAACnE,IAAI,CAACkM,QAAQ,EAAE;QACvC,IAAIyL,OAAO,KAAK,MAAM,IAAIA,OAAO,KAAK,MAAM,EAAE;UAC5C,OAAOvkB,WAAW,CAACgQ,IAAI;SACxB,MAAM,IAAIuU,OAAO,KAAK,OAAO,IAAIA,OAAO,KAAK,OAAO,EAAE;UACrD,OAAOvkB,WAAW,CAACwkB,KAAK;SACzB,MAAM,IAAID,OAAO,KAAK,MAAM,IAAIA,OAAO,KAAK,MAAM,EAAE;UACnD,OAAOvkB,WAAW,CAACykB,IAAI;SACxB,MAAM,IAAIF,OAAO,KAAK,OAAO,IAAIA,OAAO,KAAK,OAAO,EAAE;UACrD,OAAOvkB,WAAW,CAACkY,KAAK;SACzB,MAAM,IAAIqM,OAAO,KAAK,OAAO,IAAIA,OAAO,KAAK,OAAO,EAAE;UACrD,OAAOvkB,WAAW,CAAC0kB,KAAK;SACzB,MAAM,IAAIH,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,QAAQ,EAAE;UACvD,OAAOvkB,WAAW,CAAC2kB,MAAM;;;MAI7B,IAAI5T,OAAO,CAACmK,WAAW,EAAEjN,MAAM,EAAE;QAC/B,MAAMuV,UAAU,GAAGzS,OAAO,CAACmK,WAAW,CAAC,CAAC,CAAC;QACzC,IAAIsI,UAAU,IAAIA,UAAU,CAAC5W,IAAI,EAAE;UACjC,MAAMgY,iBAAiB,GAAGpB,UAAU,CAAC5W,IAAI,CAACkM,QAAQ,EAAE;UAEpD,IAAI8L,iBAAiB,KAAK,OAAO,IAAIA,iBAAiB,KAAK,OAAO,EAAE;YAClE,OAAO5kB,WAAW,CAACwkB,KAAK;WACzB,MAAM,IACLI,iBAAiB,KAAK,MAAM,IAC5BA,iBAAiB,KAAK,MAAM,EAC5B;YACA,OAAO5kB,WAAW,CAACykB,IAAI;WACxB,MAAM,IACLG,iBAAiB,KAAK,OAAO,IAC7BA,iBAAiB,KAAK,OAAO,EAC7B;YACA,OAAO5kB,WAAW,CAACkY,KAAK;WACzB,MAAM,IACL0M,iBAAiB,KAAK,OAAO,IAC7BA,iBAAiB,KAAK,OAAO,EAC7B;YACA,OAAO5kB,WAAW,CAAC0kB,KAAK;;;QAI5B,OAAO1kB,WAAW,CAACykB,IAAI;;MAGzB,OAAOzkB,WAAW,CAACgQ,IAAI;KACxB,CAAC,OAAOzI,KAAK,EAAE;MACd,OAAOvH,WAAW,CAACgQ,IAAI;;EAE3B;EAEA;;;EAGA6U,eAAeA,CAAA;IACb,OAAO,CACL,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,CACL;EACH;EAEA;;;EAGAC,mBAAmBA,CAAC/T,OAAY,EAAEE,aAA4B;IAC5D,IAAI,CAACF,OAAO,EAAE;MACZ,OAAO,kCAAkC;;IAG3C,IAAI;MACF,MAAMgU,aAAa,GACjBhU,OAAO,CAACnB,MAAM,EAAE5I,EAAE,KAAKiK,aAAa,IACpCF,OAAO,CAACnB,MAAM,EAAEE,GAAG,KAAKmB,aAAa,IACrCF,OAAO,CAAC1H,QAAQ,KAAK4H,aAAa;MAEpC,MAAM+T,SAAS,GAAGD,aAAa,GAC3B,kDAAkD,GAClD,qDAAqD;MAEzD,MAAMjE,WAAW,GAAG,IAAI,CAACwD,cAAc,CAACvT,OAAO,CAAC;MAEhD,IAAIA,OAAO,CAACmK,WAAW,IAAInK,OAAO,CAACmK,WAAW,CAACjN,MAAM,GAAG,CAAC,EAAE;QACzD,MAAMuV,UAAU,GAAGzS,OAAO,CAACmK,WAAW,CAAC,CAAC,CAAC;QACzC,IAAIsI,UAAU,IAAIA,UAAU,CAAC5W,IAAI,EAAE;UACjC,MAAMgY,iBAAiB,GAAGpB,UAAU,CAAC5W,IAAI,CAACkM,QAAQ,EAAE;UACpD,IAAI8L,iBAAiB,KAAK,OAAO,IAAIA,iBAAiB,KAAK,OAAO,EAAE;YAClE,OAAO,cAAc;WACtB,MAAM,IACLA,iBAAiB,KAAK,MAAM,IAC5BA,iBAAiB,KAAK,MAAM,EAC5B;YACA,OAAO,GAAGI,SAAS,MAAM;;;;MAK/B;MAEA,OAAO,GAAGA,SAAS,wDAAwD;KAC5E,CAAC,OAAOzd,KAAK,EAAE;MACd,OAAO,gEAAgE;;EAE3E;EAEA;EACA;EACA;EACA;EACA;EAEA;EACA0d,oBAAoBA,CAAA;IAClB,IAAI,CAACrhB,aAAa,CAACkD,OAAO,CAAE6U,GAAG,IAAKA,GAAG,CAACuJ,WAAW,EAAE,CAAC;IACtD,IAAI,CAACthB,aAAa,GAAG,EAAE;IACvB,IAAI,IAAI,CAAC0Z,eAAe,EAAE;MACxB6H,aAAa,CAAC,IAAI,CAAC7H,eAAe,CAAC;;IAErC,IAAI,CAAC9Z,iBAAiB,CAACqD,KAAK,EAAE;IAC9B,IAAI,CAACzD,MAAM,CAACwK,KAAK,CAAC,+BAA+B,CAAC;EACpD;EAEAwX,WAAWA,CAAA;IACT,IAAI,CAACH,oBAAoB,EAAE;EAC7B;;;uBA93IWhiB,cAAc,EAAAoiB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAD,EAAA,CAAAM,MAAA;IAAA;EAAA;;;aAAd1iB,cAAc;MAAA2iB,OAAA,EAAd3iB,cAAc,CAAA4iB,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}