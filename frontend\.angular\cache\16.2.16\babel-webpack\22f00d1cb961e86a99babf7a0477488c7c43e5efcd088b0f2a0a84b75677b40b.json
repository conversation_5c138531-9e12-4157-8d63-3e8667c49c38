{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { interval } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/message.service\";\nimport * as i2 from \"../../services/mock-data.service\";\nimport * as i3 from \"../../services/theme.service\";\nimport * as i4 from \"../../services/toast.service\";\nimport * as i5 from \"@angular/common\";\nexport let SystemStatusComponent = /*#__PURE__*/(() => {\n  class SystemStatusComponent {\n    constructor(messageService, mockDataService, themeService, toastService) {\n      this.messageService = messageService;\n      this.mockDataService = mockDataService;\n      this.themeService = themeService;\n      this.toastService = toastService;\n      this.status = {\n        backend: 'checking',\n        frontend: 'online',\n        database: 'checking',\n        websocket: 'checking',\n        mockData: 'checking',\n        theme: 'Chargement...',\n        lastCheck: new Date()\n      };\n      this.isChecking = false;\n    }\n    ngOnInit() {\n      this.checkSystemStatus();\n      // Auto-refresh every 30 seconds\n      this.subscription = interval(30000).subscribe(() => {\n        this.checkSystemStatus();\n      });\n      // Listen to theme changes\n      this.themeService.currentTheme$.subscribe(theme => {\n        this.status.theme = theme.displayName;\n      });\n    }\n    ngOnDestroy() {\n      this.subscription?.unsubscribe();\n    }\n    checkSystemStatus() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        _this.isChecking = true;\n        _this.status.lastCheck = new Date();\n        // Check mock data availability\n        try {\n          yield _this.mockDataService.getUsers().toPromise();\n          _this.status.mockData = 'available';\n        } catch {\n          _this.status.mockData = 'unavailable';\n        }\n        // Check backend connectivity\n        try {\n          yield _this.messageService.getConversations().toPromise();\n          _this.status.backend = 'online';\n          _this.status.database = 'online';\n          _this.status.websocket = 'online';\n        } catch {\n          _this.status.backend = 'offline';\n          _this.status.database = 'offline';\n          _this.status.websocket = 'offline';\n        }\n        _this.isChecking = false;\n      })();\n    }\n    getStatusText(status) {\n      switch (status) {\n        case 'online':\n          return 'En ligne';\n        case 'offline':\n          return 'Hors ligne';\n        case 'checking':\n          return 'Vérification...';\n        default:\n          return 'Inconnu';\n      }\n    }\n    testMockData() {\n      this.mockDataService.getUsers().subscribe({\n        next: users => {\n          this.toastService.showSuccess(`${users.length} utilisateurs de test chargés`);\n        },\n        error: () => {\n          this.toastService.showError('Erreur lors du chargement des données de test');\n        }\n      });\n    }\n    testThemes() {\n      const themes = this.themeService.getAvailableThemes();\n      this.toastService.showInfo(`${themes.length} thèmes disponibles`);\n    }\n    testNotifications() {\n      this.toastService.showSuccess('Test de notification réussi !');\n      setTimeout(() => {\n        this.toastService.showInfo('Notification d\\'information');\n      }, 1000);\n      setTimeout(() => {\n        this.toastService.showWarning('Notification d\\'avertissement');\n      }, 2000);\n    }\n    static {\n      this.ɵfac = function SystemStatusComponent_Factory(t) {\n        return new (t || SystemStatusComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.MockDataService), i0.ɵɵdirectiveInject(i3.ThemeService), i0.ɵɵdirectiveInject(i4.ToastService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SystemStatusComponent,\n        selectors: [[\"app-system-status\"]],\n        decls: 68,\n        vars: 79,\n        consts: [[1, \"system-status-panel\", \"bg-gray-800\", \"rounded-lg\", \"p-6\", \"border\", \"border-gray-700\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-4\"], [1, \"text-lg\", \"font-semibold\", \"text-white\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-heartbeat\", \"text-blue-400\", \"mr-2\"], [1, \"px-3\", \"py-1\", \"bg-blue-600\", \"hover:bg-blue-700\", \"text-white\", \"rounded\", \"text-sm\", \"transition-colors\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-sync-alt\", \"mr-1\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-4\"], [1, \"status-item\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"text-gray-300\"], [1, \"flex\", \"items-center\"], [1, \"w-3\", \"h-3\", \"rounded-full\", \"mr-2\"], [1, \"text-sm\"], [1, \"mt-4\", \"p-3\", \"bg-gray-700\", \"rounded\", \"border-l-4\"], [1, \"text-xs\", \"text-gray-400\", \"mt-1\"], [1, \"mt-4\", \"p-3\", \"bg-gray-700\", \"rounded\"], [1, \"text-sm\", \"text-blue-400\"], [1, \"mt-4\", \"text-xs\", \"text-gray-500\", \"text-center\"], [1, \"mt-6\", \"grid\", \"grid-cols-1\", \"md:grid-cols-3\", \"gap-2\"], [1, \"px-3\", \"py-2\", \"bg-green-600\", \"hover:bg-green-700\", \"text-white\", \"rounded\", \"text-sm\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-database\", \"mr-1\"], [1, \"px-3\", \"py-2\", \"bg-purple-600\", \"hover:bg-purple-700\", \"text-white\", \"rounded\", \"text-sm\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-palette\", \"mr-1\"], [1, \"px-3\", \"py-2\", \"bg-orange-600\", \"hover:bg-orange-700\", \"text-white\", \"rounded\", \"text-sm\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-bell\", \"mr-1\"]],\n        template: function SystemStatusComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h3\", 2);\n            i0.ɵɵelement(3, \"i\", 3);\n            i0.ɵɵtext(4, \" \\u00C9tat du syst\\u00E8me \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"button\", 4);\n            i0.ɵɵlistener(\"click\", function SystemStatusComponent_Template_button_click_5_listener() {\n              return ctx.checkSystemStatus();\n            });\n            i0.ɵɵelement(6, \"i\", 5);\n            i0.ɵɵtext(7);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(8, \"div\", 6)(9, \"div\", 7)(10, \"div\", 8)(11, \"span\", 9);\n            i0.ɵɵtext(12, \"Backend\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(13, \"div\", 10);\n            i0.ɵɵelement(14, \"div\", 11);\n            i0.ɵɵelementStart(15, \"span\", 12);\n            i0.ɵɵtext(16);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8)(19, \"span\", 9);\n            i0.ɵɵtext(20, \"Frontend\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"div\", 10);\n            i0.ɵɵelement(22, \"div\", 11);\n            i0.ɵɵelementStart(23, \"span\", 12);\n            i0.ɵɵtext(24);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(25, \"div\", 7)(26, \"div\", 8)(27, \"span\", 9);\n            i0.ɵɵtext(28, \"Base de donn\\u00E9es\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"div\", 10);\n            i0.ɵɵelement(30, \"div\", 11);\n            i0.ɵɵelementStart(31, \"span\", 12);\n            i0.ɵɵtext(32);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(33, \"div\", 7)(34, \"div\", 8)(35, \"span\", 9);\n            i0.ɵɵtext(36, \"WebSocket\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(37, \"div\", 10);\n            i0.ɵɵelement(38, \"div\", 11);\n            i0.ɵɵelementStart(39, \"span\", 12);\n            i0.ɵɵtext(40);\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(41, \"div\", 13)(42, \"div\", 8)(43, \"span\", 9);\n            i0.ɵɵtext(44, \"Donn\\u00E9es de test\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(45, \"span\", 12);\n            i0.ɵɵtext(46);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(47, \"p\", 14);\n            i0.ɵɵtext(48);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(49, \"div\", 15)(50, \"div\", 8)(51, \"span\", 9);\n            i0.ɵɵtext(52, \"Th\\u00E8me actuel\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(53, \"span\", 16);\n            i0.ɵɵtext(54);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(55, \"div\", 17);\n            i0.ɵɵtext(56);\n            i0.ɵɵpipe(57, \"date\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(58, \"div\", 18)(59, \"button\", 19);\n            i0.ɵɵlistener(\"click\", function SystemStatusComponent_Template_button_click_59_listener() {\n              return ctx.testMockData();\n            });\n            i0.ɵɵelement(60, \"i\", 20);\n            i0.ɵɵtext(61, \" Test donn\\u00E9es \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(62, \"button\", 21);\n            i0.ɵɵlistener(\"click\", function SystemStatusComponent_Template_button_click_62_listener() {\n              return ctx.testThemes();\n            });\n            i0.ɵɵelement(63, \"i\", 22);\n            i0.ɵɵtext(64, \" Test th\\u00E8mes \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(65, \"button\", 23);\n            i0.ɵɵlistener(\"click\", function SystemStatusComponent_Template_button_click_65_listener() {\n              return ctx.testNotifications();\n            });\n            i0.ɵɵelement(66, \"i\", 24);\n            i0.ɵɵtext(67, \" Test notifs \");\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"disabled\", ctx.isChecking);\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassProp(\"fa-spin\", ctx.isChecking);\n            i0.ɵɵadvance(1);\n            i0.ɵɵtextInterpolate1(\" \", ctx.isChecking ? \"V\\u00E9rification...\" : \"Actualiser\", \" \");\n            i0.ɵɵadvance(7);\n            i0.ɵɵclassProp(\"bg-green-500\", ctx.status.backend === \"online\")(\"bg-red-500\", ctx.status.backend === \"offline\")(\"bg-yellow-500\", ctx.status.backend === \"checking\")(\"animate-pulse\", ctx.status.backend === \"checking\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassProp(\"text-green-400\", ctx.status.backend === \"online\")(\"text-red-400\", ctx.status.backend === \"offline\")(\"text-yellow-400\", ctx.status.backend === \"checking\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵtextInterpolate1(\" \", ctx.getStatusText(ctx.status.backend), \" \");\n            i0.ɵɵadvance(6);\n            i0.ɵɵclassProp(\"bg-green-500\", ctx.status.frontend === \"online\")(\"bg-red-500\", ctx.status.frontend === \"offline\")(\"bg-yellow-500\", ctx.status.frontend === \"checking\")(\"animate-pulse\", ctx.status.frontend === \"checking\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassProp(\"text-green-400\", ctx.status.frontend === \"online\")(\"text-red-400\", ctx.status.frontend === \"offline\")(\"text-yellow-400\", ctx.status.frontend === \"checking\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵtextInterpolate1(\" \", ctx.getStatusText(ctx.status.frontend), \" \");\n            i0.ɵɵadvance(6);\n            i0.ɵɵclassProp(\"bg-green-500\", ctx.status.database === \"online\")(\"bg-red-500\", ctx.status.database === \"offline\")(\"bg-yellow-500\", ctx.status.database === \"checking\")(\"animate-pulse\", ctx.status.database === \"checking\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassProp(\"text-green-400\", ctx.status.database === \"online\")(\"text-red-400\", ctx.status.database === \"offline\")(\"text-yellow-400\", ctx.status.database === \"checking\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵtextInterpolate1(\" \", ctx.getStatusText(ctx.status.database), \" \");\n            i0.ɵɵadvance(6);\n            i0.ɵɵclassProp(\"bg-green-500\", ctx.status.websocket === \"online\")(\"bg-red-500\", ctx.status.websocket === \"offline\")(\"bg-yellow-500\", ctx.status.websocket === \"checking\")(\"animate-pulse\", ctx.status.websocket === \"checking\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassProp(\"text-green-400\", ctx.status.websocket === \"online\")(\"text-red-400\", ctx.status.websocket === \"offline\")(\"text-yellow-400\", ctx.status.websocket === \"checking\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵtextInterpolate1(\" \", ctx.getStatusText(ctx.status.websocket), \" \");\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassProp(\"border-green-500\", ctx.status.mockData === \"available\")(\"border-red-500\", ctx.status.mockData === \"unavailable\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵclassProp(\"text-green-400\", ctx.status.mockData === \"available\")(\"text-red-400\", ctx.status.mockData === \"unavailable\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵtextInterpolate1(\" \", ctx.status.mockData === \"available\" ? \"Disponibles\" : \"Indisponibles\", \" \");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\" \", ctx.status.mockData === \"available\" ? \"Le mode d\\u00E9mo est actif avec des donn\\u00E9es de test\" : \"Aucune donn\\u00E9e de test disponible\", \" \");\n            i0.ɵɵadvance(6);\n            i0.ɵɵtextInterpolate(ctx.status.theme);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\" Derni\\u00E8re v\\u00E9rification : \", i0.ɵɵpipeBind2(57, 76, ctx.status.lastCheck, \"medium\"), \" \");\n          }\n        },\n        dependencies: [i5.DatePipe],\n        styles: [\".status-item[_ngcontent-%COMP%]{border-radius:.25rem;--tw-bg-opacity: 1;background-color:rgb(55 65 81 / var(--tw-bg-opacity, 1));padding:.75rem}.status-item[_ngcontent-%COMP%]:hover{--tw-bg-opacity: 1;background-color:rgb(75 85 99 / var(--tw-bg-opacity, 1))}\"]\n      });\n    }\n  }\n  return SystemStatusComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}