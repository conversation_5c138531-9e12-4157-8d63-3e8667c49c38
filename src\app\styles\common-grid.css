/* ============================================================================
   GRILLE COMMUNE - STYLES POUR LES LAYOUTS GRID
   ============================================================================ */

.grid-container {
  display: grid;
  gap: 1rem;
  width: 100%;
  height: 100%;
}

.grid-2-cols {
  grid-template-columns: 1fr 1fr;
}

.grid-3-cols {
  grid-template-columns: 1fr 1fr 1fr;
}

.grid-4-cols {
  grid-template-columns: repeat(4, 1fr);
}

.grid-auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-auto-fill {
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}

/* Responsive breakpoints */
@media (max-width: 768px) {
  .grid-2-cols,
  .grid-3-cols,
  .grid-4-cols {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .grid-3-cols,
  .grid-4-cols {
    grid-template-columns: 1fr 1fr;
  }
}

/* Grid items */
.grid-item {
  padding: 1rem;
  border-radius: 0.5rem;
  background: var(--color-surface, #1f2937);
  border: 1px solid var(--color-border, #374151);
}

.grid-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
}

/* Grid utilities */
.col-span-2 {
  grid-column: span 2;
}

.col-span-3 {
  grid-column: span 3;
}

.col-span-4 {
  grid-column: span 4;
}

.row-span-2 {
  grid-row: span 2;
}

.row-span-3 {
  grid-row: span 3;
}
