{"version": 3, "file": "src_app_views_admin_equipes_equipes_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;;;;;AAM0C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICAlCC,4DAAA,cAE8G;IAMxGA,uDAAA,YAAsF;IACxFA,0DAAA,EAAM;IAGNA,4DAAA,cAC4G;IAC1GA,uDAAA,YAAkD;IAGlDA,4DAAA,cAAyD;IACvDA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;;IAjBPA,wDAAA,YAAAA,6DAAA,IAAAO,GAAA,EAAAC,UAAA,CAAAC,IAAA,aAAAD,UAAA,CAAAC,IAAA,kBAAwG;IAEvFT,uDAAA,GAAyD;IAAzDA,wDAAA,YAAAA,6DAAA,KAAAY,GAAA,EAAAJ,UAAA,CAAAC,IAAA,aAAyD;IAGtET,uDAAA,GAA+F;IAA/FA,wDAAA,YAAAA,6DAAA,KAAAa,GAAA,EAAAL,UAAA,CAAAC,IAAA,aAAAD,UAAA,CAAAC,IAAA,kBAA+F;IACpFT,uDAAA,GAAmE;IAAnEA,wDAAA,YAAAQ,UAAA,CAAAC,IAAA,4CAAmE;IAK9ET,uDAAA,GAAsG;IAAtGA,wDAAA,YAAAA,6DAAA,KAAAc,GAAA,EAAAN,UAAA,CAAAC,IAAA,aAAAD,UAAA,CAAAC,IAAA,kBAAsG;IACzFT,uDAAA,GAA6B;IAA7BA,wDAAA,cAAAQ,UAAA,CAAAO,OAAA,EAAAf,4DAAA,CAA6B;IAI3CA,uDAAA,GACF;IADEA,gEAAA,MAAAQ,UAAA,CAAAC,IAAA,mDAAAS,MAAA,CAAAC,cAAA,QACF;;;;;IAMNnB,4DAAA,cAAqF;IAI/EA,uDAAA,YAA2B;IAC7BA,0DAAA,EAAM;IAGNA,4DAAA,cAAqE;IAEjEA,uDAAA,WAAa;IAGfA,0DAAA,EAAM;;;;;;;;;;;;;;;;;;;;;;IAoDJA,4DAAA,cAEoE;IAGhEA,uDAAA,YAAiC;IACnCA,0DAAA,EAAM;IACNA,4DAAA,UAAK;IAC2BA,oDAAA,kBAAW;IAAAA,0DAAA,EAAM;IAC/CA,4DAAA,cAAgE;IAAAA,oDAAA,GAAuB;IAAAA,0DAAA,EAAM;;;;;;;IAR5FA,wDAAA,YAAAA,6DAAA,IAAAoB,GAAA,iBAAAC,OAAA,CAAAC,gBAAA,CAAAC,KAAA,GAC8D;IAE5DvB,uDAAA,GAAkD;IAAlDA,wDAAA,YAAAA,6DAAA,IAAAwB,GAAA,EAAAH,OAAA,CAAAI,mBAAA,CAAAF,KAAA,GAAkD;IAKhCvB,uDAAA,GAA0C;IAA1CA,wDAAA,YAAAA,6DAAA,IAAA0B,GAAA,EAAAL,OAAA,CAAAC,gBAAA,CAAAC,KAAA,GAA0C;IAACvB,uDAAA,GAAuB;IAAvBA,+DAAA,CAAA4B,UAAA,CAAAC,UAAA,CAAuB;;;;;;;;;;;;;;;;;;;IAiBzF7B,4DAAA,cAI0D;IAG1BA,oDAAA,GAAgB;IAAAA,0DAAA,EAAK;IACjDA,4DAAA,eAIG;IACDA,oDAAA,GAEF;IAAAA,0DAAA,EAAO;IAITA,4DAAA,cAA+C;IAC7CA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IAnBHA,wDAAA,YAAAA,6DAAA,IAAA+B,GAAA,EAAAC,QAAA,CAAAC,QAAA,aAAAD,QAAA,CAAAC,QAAA,eAAAD,QAAA,CAAAC,QAAA,YAEoD;IAGzBjC,uDAAA,GAAgB;IAAhBA,+DAAA,CAAAgC,QAAA,CAAAE,KAAA,CAAgB;IACXlC,uDAAA,GAI/B;IAJ+BA,wDAAA,YAAAA,6DAAA,IAAAmC,GAAA,EAAAH,QAAA,CAAAC,QAAA,aAAAD,QAAA,CAAAC,QAAA,eAAAD,QAAA,CAAAC,QAAA,YAI/B;IACAjC,uDAAA,GAEF;IAFEA,gEAAA,MAAAgC,QAAA,CAAAC,QAAA,wBAAAD,QAAA,CAAAC,QAAA,yCAEF;IAKAjC,uDAAA,GACF;IADEA,gEAAA,MAAAgC,QAAA,CAAAK,WAAA,MACF;;;;;IArEVrC,4DAAA,cAA6G;IAIjGA,oDAAA,GAAkB;IAAAA,0DAAA,EAAO;IAIjCA,4DAAA,cAA8G;IAE1GA,uDAAA,YAAuG;IACzGA,0DAAA,EAAM;IACNA,4DAAA,aAAkC;IAAAA,oDAAA,GAAiB;IAAAA,0DAAA,EAAK;IAG1DA,4DAAA,eAAuB;IAGHA,oDAAA,IAAwB;IAAAA,0DAAA,EAAI;IAI9CA,wDAAA,KAAAuC,4CAAA,mBAWM;IAGNvC,4DAAA,eAAmG;IAE/FA,uDAAA,aAAgF;IAChFA,oDAAA,0CACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,gBAAoF;IAClFA,oDAAA,IACF;IAAAA,0DAAA,EAAO;IAITA,4DAAA,eAAuB;IACrBA,wDAAA,KAAAwC,4CAAA,mBAsBM;IACRxC,0DAAA,EAAM;;;;;;IApEmBA,uDAAA,GAAkD;IAAlDA,wDAAA,YAAAA,6DAAA,KAAAwB,GAAA,EAAAiB,MAAA,CAAAhB,mBAAA,CAAAF,KAAA,GAAkD;IACrEvB,uDAAA,GAAkB;IAAlBA,gEAAA,YAAAuB,KAAA,SAAkB;IAIiCvB,uDAAA,GAAkD;IAAlDA,wDAAA,YAAAA,6DAAA,KAAAwB,GAAA,EAAAiB,MAAA,CAAAhB,mBAAA,CAAAF,KAAA,GAAkD;IAE3FvB,uDAAA,GAAyC;IAAzCA,wDAAA,YAAAyC,MAAA,CAAAC,gBAAA,CAAAd,UAAA,CAAAe,IAAA,EAAyC,YAAA3C,6DAAA,KAAA0B,GAAA,EAAAe,MAAA,CAAAnB,gBAAA,CAAAC,KAAA;IAEvBvB,uDAAA,GAAiB;IAAjBA,+DAAA,CAAA4B,UAAA,CAAAe,IAAA,CAAiB;IAMjC3C,uDAAA,GAAwB;IAAxBA,+DAAA,CAAA4B,UAAA,CAAAS,WAAA,CAAwB;IAIpCrC,uDAAA,GAAuB;IAAvBA,wDAAA,SAAA4B,UAAA,CAAAC,UAAA,CAAuB;IAgBQ7B,uDAAA,GAA0C;IAA1CA,wDAAA,YAAAA,6DAAA,KAAA0B,GAAA,EAAAe,MAAA,CAAAnB,gBAAA,CAAAC,KAAA,GAA0C;IAG5CvB,uDAAA,GAAkD;IAAlDA,wDAAA,YAAAA,6DAAA,KAAAwB,GAAA,EAAAiB,MAAA,CAAAhB,mBAAA,CAAAF,KAAA,GAAkD;IACjFvB,uDAAA,GACF;IADEA,gEAAA,MAAA4B,UAAA,CAAAgB,KAAA,CAAAC,MAAA,kBACF;IAKsB7C,uDAAA,GAAiB;IAAjBA,wDAAA,YAAA4B,UAAA,CAAAgB,KAAA,CAAiB;;;;;IA4CvC5C,4DAAA,cAAsF;IAEjEA,oDAAA,GAAgB;IAAAA,0DAAA,EAAK;IACtCA,4DAAA,eAIG;IACDA,oDAAA,GAEF;IAAAA,0DAAA,EAAO;IAETA,4DAAA,aAAsB;IAAAA,oDAAA,GAAsB;IAAAA,0DAAA,EAAI;;;;IAV7BA,uDAAA,GAAgB;IAAhBA,+DAAA,CAAA8C,QAAA,CAAAZ,KAAA,CAAgB;IACblC,uDAAA,GAIlB;IAJkBA,wDAAA,YAAAA,6DAAA,IAAAmC,GAAA,EAAAW,QAAA,CAAAb,QAAA,aAAAa,QAAA,CAAAb,QAAA,eAAAa,QAAA,CAAAb,QAAA,YAIlB;IACAjC,uDAAA,GAEF;IAFEA,gEAAA,MAAA8C,QAAA,CAAAb,QAAA,wBAAAa,QAAA,CAAAb,QAAA,yCAEF;IAEoBjC,uDAAA,GAAsB;IAAtBA,+DAAA,CAAA8C,QAAA,CAAAT,WAAA,CAAsB;;;;;IAzBtDrC,4DAAA,cAA0G;IAI5FA,oDAAA,GAAiB;IAAAA,0DAAA,EAAS;IAClCA,4DAAA,eAAiD;IAAAA,oDAAA,GAAgC;IAAAA,0DAAA,EAAO;IAG5FA,4DAAA,cAAgJ;IAEjHA,oDAAA,IAAwB;IAAAA,0DAAA,EAAI;IAEvDA,4DAAA,eAAwB;IACtBA,wDAAA,KAAA+C,4CAAA,kBAaM;IACR/C,0DAAA,EAAM;;;;;IA1BmBA,uDAAA,GAAoB;IAApBA,wDAAA,mBAAAgD,KAAA,CAAoB;IAEvChD,uDAAA,GAAuC;IAAvCA,yDAAA,iCAAAgD,KAAA,CAAuC,kBAAAA,KAAA,sCAAAA,KAAA;IACrChD,uDAAA,GAAiB;IAAjBA,+DAAA,CAAAkD,UAAA,CAAAP,IAAA,CAAiB;IACwB3C,uDAAA,GAAgC;IAAhCA,gEAAA,KAAAkD,UAAA,CAAAN,KAAA,CAAAC,MAAA,iBAAgC;IAGhF7C,uDAAA,GAAqB;IAArBA,wDAAA,oBAAAgD,KAAA,CAAqB;IAAqChD,yDAAA,gCAAAgD,KAAA,CAAsC;IAEtEhD,uDAAA,GAAwB;IAAxBA,+DAAA,CAAAkD,UAAA,CAAAb,WAAA,CAAwB;IAG3BrC,uDAAA,GAAe;IAAfA,wDAAA,YAAAkD,UAAA,CAAAN,KAAA,CAAe;;;;;;IAnHjD5C,4DAAA,cAAuE;IAM7DA,uDAAA,YAAyC;IACzCA,oDAAA,GACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,YAA2B;IACzBA,uDAAA,YAAsC;IACtCA,oDAAA,GACF;IAAAA,0DAAA,EAAI;IAENA,4DAAA,gBAAsD;IACpDA,uDAAA,aAAsC;IACtCA,oDAAA,IACF;IAAAA,0DAAA,EAAO;IAKXA,4DAAA,eAAsB;IACpBA,wDAAA,KAAAmD,qCAAA,oBA0EM;IACRnD,0DAAA,EAAM;IAGNA,4DAAA,eAA2D;IACzDA,wDAAA,KAAAoD,qCAAA,oBA8BM;IACRpD,0DAAA,EAAM;IAGNA,4DAAA,eAAkB;IAMNA,uDAAA,aAA4C;IAC5CA,oDAAA,wEACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,eAAyB;IAC6BA,oDAAA,SAAC;IAAAA,0DAAA,EAAM;IAC3DA,4DAAA,WAAK;IACcA,oDAAA,qCAAmB;IAAAA,0DAAA,EAAK;IACzCA,4DAAA,aAAiC;IAAAA,oDAAA,IAAuE;IAAAA,0DAAA,EAAI;IAGhHA,4DAAA,eAAyB;IAC6BA,oDAAA,SAAC;IAAAA,0DAAA,EAAM;IAC3DA,4DAAA,WAAK;IACcA,oDAAA,+BAAuB;IAAAA,0DAAA,EAAK;IAC7CA,4DAAA,aAAiC;IAAAA,oDAAA,IAAwG;IAAAA,0DAAA,EAAI;IAGjJA,4DAAA,eAAoB;IAC+BA,oDAAA,SAAC;IAAAA,0DAAA,EAAM;IACxDA,4DAAA,WAAK;IACcA,oDAAA,uBAAe;IAAAA,0DAAA,EAAK;IACrCA,4DAAA,aAAiC;IAAAA,oDAAA,gFAAmE;IAAAA,0DAAA,EAAI;IAI9GA,4DAAA,eAA+C;IACsCA,wDAAA,mBAAAsD,wDAAA;MAAAtD,2DAAA,CAAAwD,IAAA;MAAA,MAAAC,OAAA,GAAAzD,2DAAA;MAAA,OAASA,yDAAA,CAAAyD,OAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IACxG5D,uDAAA,aAA2C;IAACA,oDAAA,oCAC9C;IAAAA,0DAAA,EAAS;IACTA,4DAAA,eAAmC;IACjCA,uDAAA,aAAsC;IACtCA,oDAAA,4CACF;IAAAA,0DAAA,EAAM;;;;IAvKRA,uDAAA,GACF;IADEA,gEAAA,uBAAA6D,MAAA,CAAAC,gBAAA,CAAAC,YAAA,QACF;IAGE/D,uDAAA,GACF;IADEA,gEAAA,MAAA6D,MAAA,CAAAC,gBAAA,CAAAE,QAAA,CAAAnB,MAAA,2CAAAgB,MAAA,CAAAI,UAAA,CAAAJ,MAAA,CAAAC,gBAAA,4BACF;IAIA9D,uDAAA,GACF;IADEA,gEAAA,MAAA6D,MAAA,CAAAK,IAAA,IAAAL,MAAA,CAAAK,IAAA,CAAAC,OAAA,GAAAN,MAAA,CAAAK,IAAA,CAAAC,OAAA,CAAAtB,MAAA,kBACF;IAMsB7C,uDAAA,GAA8B;IAA9BA,wDAAA,YAAA6D,MAAA,CAAAC,gBAAA,CAAAE,QAAA,CAA8B;IA+E9BhE,uDAAA,GAA8B;IAA9BA,wDAAA,YAAA6D,MAAA,CAAAC,gBAAA,CAAAE,QAAA,CAA8B;IA+CThE,uDAAA,IAAuE;IAAvEA,gEAAA,KAAA6D,MAAA,CAAAI,UAAA,CAAAJ,MAAA,CAAAC,gBAAA,+DAAuE;IAOvE9D,uDAAA,GAAwG;IAAxGA,gEAAA,+CAAA6D,MAAA,CAAAK,IAAA,IAAAL,MAAA,CAAAK,IAAA,CAAAC,OAAA,GAAAN,MAAA,CAAAK,IAAA,CAAAC,OAAA,CAAAtB,MAAA,kCAAwG;;;;;IA2BzJ7C,4DAAA,eAAkD;IAChDA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAAoE,MAAA,CAAAC,KAAA,MACF;;;;;;IAIErE,4DAAA,eAA4C;IAIpCA,uDAAA,aAA6C;IAC7CA,oDAAA,oDACF;IAAAA,0DAAA,EAAK;IAELA,4DAAA,eAAkB;IAC8CA,oDAAA,sCAA+B;IAAAA,0DAAA,EAAQ;IACrGA,4DAAA,cAAyB;IAErBA,uDAAA,cAA4C;IAC9CA,0DAAA,EAAO;IACPA,4DAAA,kBAE4D;IAArDA,wDAAA,2BAAAsE,gEAAAC,MAAA;MAAAvE,2DAAA,CAAAwE,IAAA;MAAA,MAAAC,OAAA,GAAAzE,2DAAA;MAAA,OAAAA,yDAAA,CAAAyE,OAAA,CAAAV,YAAA,GAAAQ,MAAA;IAAA,EAA0B;IAFjCvE,0DAAA,EAE4D;IAE9DA,4DAAA,kBAAuC;IACrCA,uDAAA,aAAsC;IACtCA,oDAAA,IACF;IAAAA,0DAAA,EAAQ;IAGVA,4DAAA,gBAAoB;IAEVA,wDAAA,mBAAA0E,yDAAA;MAAA1E,2DAAA,CAAAwE,IAAA;MAAA,MAAAG,OAAA,GAAA3E,2DAAA;MAAA,OAASA,yDAAA,CAAA2E,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAE/B5E,uDAAA,aAAoF;IACpFA,oDAAA,IACF;IAAAA,0DAAA,EAAS;;;;IAdAA,uDAAA,IAA0B;IAA1BA,wDAAA,YAAA6E,MAAA,CAAAd,YAAA,CAA0B,aAAAc,MAAA,CAAAC,YAAA;IAIjC9E,uDAAA,GACF;IADEA,gEAAA,8BAAA6E,MAAA,CAAAX,IAAA,IAAAW,MAAA,CAAAX,IAAA,CAAAC,OAAA,GAAAU,MAAA,CAAAX,IAAA,CAAAC,OAAA,CAAAtB,MAAA,2DACF;IAMQ7C,uDAAA,GAAiD;IAAjDA,wDAAA,aAAA6E,MAAA,CAAAC,YAAA,KAAAD,MAAA,CAAAd,YAAA,CAAAgB,IAAA,GAAiD;IACzC/E,uDAAA,GAAiE;IAAjEA,wDAAA,YAAA6E,MAAA,CAAAC,YAAA,0CAAiE;IAC/E9E,uDAAA,GACF;IADEA,gEAAA,MAAA6E,MAAA,CAAAC,YAAA,iFACF;;;ADlQV,MAAOE,eAAe;EAa1BC,YACUC,SAAoB,EACpBC,WAAwB,EACxBC,mBAAwC;IAFxC,KAAAF,SAAS,GAATA,SAAS;IACT,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAb7B,KAAArB,YAAY,GAAW,EAAE;IACzB,KAAAe,YAAY,GAAY,KAAK;IAC7B,KAAAhB,gBAAgB,GAAQ,IAAI;IAC5B,KAAAO,KAAK,GAAkB,IAAI;IAE3B;IACA,KAAAgB,QAAQ,GAAsD,EAAE;IAChE,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,gBAAgB,GAAY,KAAK;EAM9B;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACH,QAAQ,CAACI,IAAI,CAAC;MACjBhF,IAAI,EAAE,WAAW;MACjBM,OAAO,EACL;KACH,CAAC;EACJ;EAEA6D,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAACb,YAAY,CAACgB,IAAI,EAAE,EAAE;MAC7B,IAAI,CAACK,mBAAmB,CAACM,SAAS,CAAC,oCAAoC,CAAC;MACxE;;IAGF;IACA,IAAIC,WAAW,GACb,IAAI,CAACzB,IAAI,IAAI,IAAI,CAACA,IAAI,CAACC,OAAO,GAAG,IAAI,CAACD,IAAI,CAACC,OAAO,CAACtB,MAAM,GAAG,CAAC;IAE/D;IACA,MAAM+C,oBAAoB,GAAGC,IAAI,CAACC,GAAG,CAACH,WAAW,EAAE,CAAC,CAAC;IAErD,IAAIA,WAAW,KAAK,CAAC,EAAE;MACrB,IAAI,CAACP,mBAAmB,CAACW,WAAW,CAClC,mEAAmE,CACpE;;IAGH,IAAI,CAACjB,YAAY,GAAG,IAAI;IACxB,IAAI,CAACT,KAAK,GAAG,IAAI;IAEjB2B,OAAO,CAACC,GAAG,CACT,6BAA6BL,oBAAoB,uBAAuBD,WAAW,WAAW,CAC/F;IAED;IACA,IAAI,CAACN,QAAQ,CAACI,IAAI,CAAC;MACjBhF,IAAI,EAAE,MAAM;MACZM,OAAO,EAAE,qCAAqC,IAAI,CAACgD,YAAY,qBAAqB6B,oBAAoB;KACzG,CAAC;IAEF;IACA,MAAMM,mBAAmB,GAAG,IAAI,CAACb,QAAQ,CAACxC,MAAM;IAChD,IAAI,CAACwC,QAAQ,CAACI,IAAI,CAAC;MACjBhF,IAAI,EAAE,WAAW;MACjBM,OAAO,EAAE;KACV,CAAC;IAEF;IACA,IAAIoF,WAAW,GAAU,EAAE;IAC3B,IAAI,IAAI,CAACjC,IAAI,IAAI,IAAI,CAACA,IAAI,CAACC,OAAO,EAAE;MAClC;MACAgC,WAAW,GAAG,IAAI,CAACjC,IAAI,CAACC,OAAO,CAACiC,GAAG,CAAC,CAACC,QAAgB,EAAEC,KAAa,KAAI;QACtE,OAAO;UAAEC,EAAE,EAAEF,QAAQ;UAAE1D,IAAI,EAAE,UAAU2D,KAAK,GAAG,CAAC,EAAE;UAAE7F,IAAI,EAAE;QAAQ,CAAE;MACtE,CAAC,CAAC;MAEFuF,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEE,WAAW,CAAC;;IAG1E,IAAI,CAACjB,SAAS,CACXsB,oBAAoB,CAAC,IAAI,CAACzC,YAAY,EAAE4B,WAAW,EAAEQ,WAAW,CAAC,CACjEM,IAAI,CAAC1G,wDAAQ,CAAC,MAAO,IAAI,CAAC+E,YAAY,GAAG,KAAM,CAAC,CAAC,CACjD4B,SAAS,CAAC;MACTC,IAAI,EAAGC,MAAW,IAAI;QACpB,IAAI,CAACA,MAAM,IAAI,CAACA,MAAM,CAAC5C,QAAQ,IAAI4C,MAAM,CAAC5C,QAAQ,CAACnB,MAAM,KAAK,CAAC,EAAE;UAC/DmD,OAAO,CAAC3B,KAAK,CAAC,kCAAkC,EAAEuC,MAAM,CAAC;UACzD,IAAI,CAACC,qBAAqB,CACxBX,mBAAmB,EACnB,4BAA4B,CAC7B;UACD;;QAGF,IAAI,CAACpC,gBAAgB,GAAG8C,MAAM;QAE9B;QACA,IAAI,CAACvB,QAAQ,CAACa,mBAAmB,CAAC,GAAG;UACnCzF,IAAI,EAAE,WAAW;UACjBM,OAAO,EAAE,eACP6F,MAAM,CAAC5C,QAAQ,CAACnB,MAClB,+BACE+D,MAAM,CAAC7C,YACT,sBAAsB,IAAI,CAACE,UAAU,CAAC2C,MAAM,CAAC;SAC9C;QAED,IAAI,CAACxB,mBAAmB,CAAC0B,WAAW,CAAC,6BAA6B,CAAC;MACrE,CAAC;MACDzC,KAAK,EAAGA,KAAU,IAAI;QACpB2B,OAAO,CAAC3B,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;QAChE,IAAI,CAACwC,qBAAqB,CACxBX,mBAAmB,EACnB7B,KAAK,CAAC0C,OAAO,IAAI,iBAAiB,CACnC;MACH;KACD,CAAC;EACN;EAEA;EACQF,qBAAqBA,CAC3BG,YAAoB,EACpBC,YAAoB;IAEpB,IAAI,CAAC5C,KAAK,GAAG,uDAAuD;IAEpE;IACA,IAAI,CAACgB,QAAQ,CAAC2B,YAAY,CAAC,GAAG;MAC5BvG,IAAI,EAAE,WAAW;MACjBM,OAAO,EACL;KACH;IAED,IAAI,CAACqE,mBAAmB,CAACM,SAAS,CAChC,2CAA2C,GAAGuB,YAAY,CAC3D;EACH;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAAC5B,YAAY,CAACP,IAAI,EAAE,EAAE;MAC7B;;IAGF,MAAMoC,QAAQ,GAAG,IAAI,CAAC7B,YAAY,CAACP,IAAI,EAAE;IACzC,IAAI,CAACO,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAE5B;IACA,IAAI,CAACF,QAAQ,CAACI,IAAI,CAAC;MACjBhF,IAAI,EAAE,MAAM;MACZM,OAAO,EAAEoG;KACV,CAAC;IAEF,MAAMC,cAAc,GAAG;MACrBlF,KAAK,EACH,IAAI,CAAC6B,YAAY,KAChB,IAAI,CAACD,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACC,YAAY,GAAG,EAAE,CAAC;MACnE1B,WAAW,EACT,2BAA2B,IAAI,IAAI,CAAC6B,IAAI,GAAG,IAAI,CAACA,IAAI,CAACvB,IAAI,GAAG,EAAE;KACjE;IAED,IAAI,CAACuC,SAAS,CACXmC,kBAAkB,CAACF,QAAQ,EAAEC,cAAc,CAAC,CAC5CX,IAAI,CAAC1G,wDAAQ,CAAC,MAAO,IAAI,CAACwF,gBAAgB,GAAG,KAAM,CAAC,CAAC,CACrDmB,SAAS,CAAC;MACTC,IAAI,EAAGW,QAAgB,IAAI;QACzB;QACA,IAAI,CAACjC,QAAQ,CAACI,IAAI,CAAC;UACjBhF,IAAI,EAAE,WAAW;UACjBM,OAAO,EAAEuG;SACV,CAAC;MACJ,CAAC;MACDjD,KAAK,EAAGA,KAAU,IAAI;QACpB2B,OAAO,CAAC3B,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QAEzD;QACA,IAAI,CAACgB,QAAQ,CAACI,IAAI,CAAC;UACjBhF,IAAI,EAAE,WAAW;UACjBM,OAAO,EACL;SACH,CAAC;QAEF,IAAI,CAACqE,mBAAmB,CAACM,SAAS,CAChC,2CAA2C,CAC5C;MACH;KACD,CAAC;EACN;EAEA9B,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACE,gBAAgB,IAAI,CAAC,IAAI,CAACI,IAAI,IAAI,CAAC,IAAI,CAACA,IAAI,CAACqD,GAAG,EAAE;MAC1D,IAAI,CAACnC,mBAAmB,CAACM,SAAS,CAChC,yCAAyC,CAC1C;MACD;;IAGF,IAAI8B,YAAY,GAAG,CAAC;IACpB,MAAMC,UAAU,GAAG,IAAI,CAACxD,UAAU,CAAC,IAAI,CAACH,gBAAgB,CAAC;IAEzD;IACA,IAAI,CAAC,IAAI,CAACI,IAAI,CAACC,OAAO,IAAI,IAAI,CAACD,IAAI,CAACC,OAAO,CAACtB,MAAM,KAAK,CAAC,EAAE;MACxD,IAAI,CAACuC,mBAAmB,CAACM,SAAS,CAChC,sDAAsD,CACvD;MACD;;IAGF;IACA,MAAMS,WAAW,GAAG,IAAI,CAACjC,IAAI,CAACC,OAAO,CAACiC,GAAG,CAAEsB,MAAM,IAAI;MACnD,OAAO,OAAOA,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAIA,MAAc,CAACC,MAAM;IACrE,CAAC,CAAC;IAEF;IACA,MAAMC,iBAAiB,GAA8B,EAAE;IACvDzB,WAAW,CAAC0B,OAAO,CAAC,CAACxB,QAAQ,EAAEC,KAAK,KAAI;MACtCsB,iBAAiB,CAAC,UAAUtB,KAAK,GAAG,CAAC,EAAE,CAAC,GAAGD,QAAQ;IACrD,CAAC,CAAC;IAEFL,OAAO,CAACC,GAAG,CACT,qDAAqD,EACrDE,WAAW,CACZ;IACDH,OAAO,CAACC,GAAG,CACT,6CAA6C,EAC7C2B,iBAAiB,CAClB;IAED;IACA,IAAI,CAAC9D,gBAAgB,CAACE,QAAQ,CAAC6D,OAAO,CAAEC,MAAW,IAAI;MACrD;MACA,IAAIC,gBAAoC;MAExC;MACA,IAAID,MAAM,CAACjG,UAAU,EAAE;QACrB;QACA,MAAMmG,UAAU,GAAGF,MAAM,CAACjG,UAAU;QACpC,IAAI+F,iBAAiB,CAACI,UAAU,CAAC,EAAE;UACjCD,gBAAgB,GAAGH,iBAAiB,CAACI,UAAU,CAAC;UAChDhC,OAAO,CAACC,GAAG,CACT,0CAA0C6B,MAAM,CAACnF,IAAI,iBAAiBqF,UAAU,UAAUD,gBAAgB,GAAG,CAC9G;SACF,MAAM;UACL;UACA,MAAME,iBAAiB,GAAGpC,IAAI,CAACqC,KAAK,CAClCrC,IAAI,CAACsC,MAAM,EAAE,GAAGhC,WAAW,CAACtD,MAAM,CACnC;UACDkF,gBAAgB,GAAG5B,WAAW,CAAC8B,iBAAiB,CAAC;UACjDjC,OAAO,CAACC,GAAG,CACT,kBAAkB+B,UAAU,iDAAiDC,iBAAiB,EAAE,CACjG;;OAEJ,MAAM;QACL;QACA,MAAMA,iBAAiB,GAAGpC,IAAI,CAACqC,KAAK,CAClCrC,IAAI,CAACsC,MAAM,EAAE,GAAGhC,WAAW,CAACtD,MAAM,CACnC;QACDkF,gBAAgB,GAAG5B,WAAW,CAAC8B,iBAAiB,CAAC;QACjDjC,OAAO,CAACC,GAAG,CACT,+DAA+DgC,iBAAiB,EAAE,CACnF;;MAGH;MACAH,MAAM,CAAClF,KAAK,CAACiF,OAAO,CAAEO,QAAa,IAAI;QACrC,MAAMC,IAAI,GAAS;UACjBnG,KAAK,EAAEkG,QAAQ,CAAClG,KAAK;UACrBG,WAAW,EAAE,IAAIyF,MAAM,CAACnF,IAAI,KAAKyF,QAAQ,CAAC/F,WAAW,EAAE;UACvDiG,MAAM,EAAEF,QAAQ,CAACE,MAAM,IAAI,MAAM;UACjCrG,QAAQ,EAAEmG,QAAQ,CAACnG,QAAQ,IAAI,QAAQ;UACvCsG,MAAM,EAAE,IAAI,CAACrE,IAAI,CAACqD,GAAG,IAAI,EAAE;UAC3B;UACA1F,UAAU,EAAEkG;SACb;QAED,IAAI,CAAC5C,WAAW,CAACqD,UAAU,CAACH,IAAI,CAAC,CAAC3B,SAAS,CAAC;UAC1CC,IAAI,EAAEA,CAAA,KAAK;YACTa,YAAY,EAAE;YACd,IAAIA,YAAY,KAAKC,UAAU,EAAE;cAC/B,IAAI,CAACrC,mBAAmB,CAAC0B,WAAW,CAClC,GAAGU,YAAY,iEAAiE,CACjF;cACD;cACA,IAAI,CAAC1D,gBAAgB,GAAG,IAAI;cAC5B,IAAI,CAACC,YAAY,GAAG,EAAE;;UAE1B,CAAC;UACDM,KAAK,EAAGA,KAAK,IAAI;YACf2B,OAAO,CAAC3B,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;YAC/D,IAAI,CAACe,mBAAmB,CAACM,SAAS,CAChC,uCAAuC,CACxC;UACH;SACD,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAzB,UAAUA,CAAClD,OAAY;IACrB,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACiD,QAAQ,EAAE,OAAO,CAAC;IAE3C,OAAOjD,OAAO,CAACiD,QAAQ,CAACyE,MAAM,CAAC,CAACC,KAAa,EAAEZ,MAAW,KAAI;MAC5D,OAAOY,KAAK,IAAIZ,MAAM,CAAClF,KAAK,GAAGkF,MAAM,CAAClF,KAAK,CAACC,MAAM,GAAG,CAAC,CAAC;IACzD,CAAC,EAAE,CAAC,CAAC;EACP;EAEA;EACApB,mBAAmBA,CAAC6E,KAAa;IAC/B;IACA,MAAMqC,SAAS,GAAG,CAChB,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C,EAC1C,0CAA0C,EAC1C,mDAAmD,CAAE;IAAA,CACtD;IAED;IACA,OAAOA,SAAS,CAACrC,KAAK,GAAGqC,SAAS,CAAC9F,MAAM,CAAC;EAC5C;EAEA;EACAvB,gBAAgBA,CAACgF,KAAa;IAC5B;IACA,MAAMsC,MAAM,GAAG,CACb,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CAAE;IAAA,CACZ;IAED;IACA,OAAOA,MAAM,CAACtC,KAAK,GAAGsC,MAAM,CAAC/F,MAAM,CAAC;EACtC;EAEA;EACAH,gBAAgBA,CAACmG,UAAkB;IACjC;IACA,MAAMlG,IAAI,GAAGkG,UAAU,CAACC,WAAW,EAAE;IAErC;IACA,IACEnG,IAAI,CAACoG,QAAQ,CAAC,MAAM,CAAC,IACrBpG,IAAI,CAACoG,QAAQ,CAAC,KAAK,CAAC,IACpBpG,IAAI,CAACoG,QAAQ,CAAC,SAAS,CAAC,IACxBpG,IAAI,CAACoG,QAAQ,CAAC,MAAM,CAAC,EACrB;MACA,OAAO,kBAAkB;KAC1B,MAAM,IACLpG,IAAI,CAACoG,QAAQ,CAAC,WAAW,CAAC,IAC1BpG,IAAI,CAACoG,QAAQ,CAAC,IAAI,CAAC,IACnBpG,IAAI,CAACoG,QAAQ,CAAC,OAAO,CAAC,IACtBpG,IAAI,CAACoG,QAAQ,CAAC,aAAa,CAAC,EAC5B;MACA,OAAO,WAAW;KACnB,MAAM,IACLpG,IAAI,CAACoG,QAAQ,CAAC,aAAa,CAAC,IAC5BpG,IAAI,CAACoG,QAAQ,CAAC,SAAS,CAAC,IACxBpG,IAAI,CAACoG,QAAQ,CAAC,OAAO,CAAC,EACtB;MACA,OAAO,wBAAwB;KAChC,MAAM,IACLpG,IAAI,CAACoG,QAAQ,CAAC,MAAM,CAAC,IACrBpG,IAAI,CAACoG,QAAQ,CAAC,SAAS,CAAC,IACxBpG,IAAI,CAACoG,QAAQ,CAAC,IAAI,CAAC,EACnB;MACA,OAAO,aAAa;KACrB,MAAM,IAAIpG,IAAI,CAACoG,QAAQ,CAAC,UAAU,CAAC,IAAIpG,IAAI,CAACoG,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC7D,OAAO,qBAAqB;KAC7B,MAAM,IAAIpG,IAAI,CAACoG,QAAQ,CAAC,UAAU,CAAC,IAAIpG,IAAI,CAACoG,QAAQ,CAAC,aAAa,CAAC,EAAE;MACpE,OAAO,qBAAqB;KAC7B,MAAM,IACLpG,IAAI,CAACoG,QAAQ,CAAC,aAAa,CAAC,IAC5BpG,IAAI,CAACoG,QAAQ,CAAC,MAAM,CAAC,IACrBpG,IAAI,CAACoG,QAAQ,CAAC,QAAQ,CAAC,EACvB;MACA,OAAO,gBAAgB;KACxB,MAAM,IAAIpG,IAAI,CAACoG,QAAQ,CAAC,KAAK,CAAC,IAAIpG,IAAI,CAACoG,QAAQ,CAAC,eAAe,CAAC,EAAE;MACjE,OAAO,mBAAmB;KAC3B,MAAM,IAAIpG,IAAI,CAACoG,QAAQ,CAAC,QAAQ,CAAC,IAAIpG,IAAI,CAACoG,QAAQ,CAAC,KAAK,CAAC,EAAE;MAC1D,OAAO,eAAe;KACvB,MAAM,IAAIpG,IAAI,CAACoG,QAAQ,CAAC,SAAS,CAAC,IAAIpG,IAAI,CAACoG,QAAQ,CAAC,SAAS,CAAC,EAAE;MAC/D,OAAO,WAAW;KACnB,MAAM,IACLpG,IAAI,CAACoG,QAAQ,CAAC,WAAW,CAAC,IAC1BpG,IAAI,CAACoG,QAAQ,CAAC,aAAa,CAAC,IAC5BpG,IAAI,CAACoG,QAAQ,CAAC,KAAK,CAAC,EACpB;MACA,OAAO,aAAa;;IAGtB;IACA,OAAO,gBAAgB;EACzB;EAEA;EACA5H,cAAcA,CAAA;IACZ,MAAM6H,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,KAAK,GAAGF,GAAG,CAACG,QAAQ,EAAE,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACxD,MAAMC,OAAO,GAAGN,GAAG,CAACO,UAAU,EAAE,CAACH,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC5D,OAAO,GAAGH,KAAK,IAAII,OAAO,EAAE;EAC9B;;;uBAvZWtE,eAAe,EAAAhF,+DAAA,CAAAyJ,kEAAA,GAAAzJ,+DAAA,CAAA2J,sEAAA,GAAA3J,+DAAA,CAAA6J,sFAAA;IAAA;EAAA;;;YAAf7E,eAAe;MAAA+E,SAAA;MAAAC,MAAA;QAAA9F,IAAA;MAAA;MAAA+F,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZ5BtK,4DAAA,aAAqC;UAM7BA,wDAAA,IAAAwK,8BAAA,kBAsBM;UAGNxK,wDAAA,IAAAyK,8BAAA,iBAgBM;UACRzK,0DAAA,EAAM;UAGNA,wDAAA,IAAA0K,8BAAA,kBAoLM;UAGN1K,wDAAA,IAAA2K,8BAAA,iBAEM;UAGN3K,4DAAA,aAAuC;UACrCA,wDAAA,KAAA4K,+BAAA,mBAkCM;UAEN5K,4DAAA,eAAwD;UAIhDA,uDAAA,aAA4C;UAC9CA,0DAAA,EAAO;UACPA,4DAAA,iBAGqC;UAD9BA,wDAAA,2BAAA6K,yDAAAtG,MAAA;YAAA,OAAAgG,GAAA,CAAAjF,YAAA,GAAAf,MAAA;UAAA,EAA0B,yBAAAuG,uDAAA;YAAA,OAAgBP,GAAA,CAAArD,WAAA,EAAa;UAAA,EAA7B;UAFjClH,0DAAA,EAGqC;UACrCA,4DAAA,kBACsF;UAA9EA,wDAAA,mBAAA+K,kDAAA;YAAA,OAASR,GAAA,CAAArD,WAAA,EAAa;UAAA,EAAC;UAC7BlH,uDAAA,aAA4F;UAC9FA,0DAAA,EAAS;;;UA3RUA,uDAAA,GAAa;UAAbA,wDAAA,YAAAuK,GAAA,CAAAlF,QAAA,CAAa;UAyBhCrF,uDAAA,GAAsC;UAAtCA,wDAAA,SAAAuK,GAAA,CAAAzF,YAAA,IAAAyF,GAAA,CAAAhF,gBAAA,CAAsC;UAoBxCvF,uDAAA,GAAsB;UAAtBA,wDAAA,SAAAuK,GAAA,CAAAzG,gBAAA,CAAsB;UAuLtB9D,uDAAA,GAAW;UAAXA,wDAAA,SAAAuK,GAAA,CAAAlG,KAAA,CAAW;UAMTrE,uDAAA,GAAuB;UAAvBA,wDAAA,UAAAuK,GAAA,CAAAzG,gBAAA,CAAuB;UA4ChB9D,uDAAA,GAA0B;UAA1BA,wDAAA,YAAAuK,GAAA,CAAAjF,YAAA,CAA0B,aAAAiF,GAAA,CAAAhF,gBAAA;UAGDvF,uDAAA,GAAqD;UAArDA,wDAAA,aAAAuK,GAAA,CAAAhF,gBAAA,KAAAgF,GAAA,CAAAjF,YAAA,CAAAP,IAAA,GAAqD;UACrE/E,uDAAA,GAAyE;UAAzEA,wDAAA,YAAAuK,GAAA,CAAAhF,gBAAA,8CAAyE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IEvBnFvF,4DAAA,cAGC;IAgBOA,uDAAA,YAYK;IACPA,0DAAA,EAAM;IACNA,4DAAA,UAAK;IAEDA,oDAAA,GACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,cAA4C;IAUxCA,uDAAA,aAOK;IACLA,oDAAA,IAKF;IAAAA,0DAAA,EAAO;IACPA,4DAAA,iBAA0B;IAAAA,oDAAA,IAOxB;IAAAA,0DAAA,EAAQ;IAMhBA,4DAAA,kBAIC;IADCA,wDAAA,mBAAAgL,4EAAA;MAAA,MAAAC,WAAA,GAAAjL,2DAAA,CAAAkL,IAAA;MAAA,MAAAC,SAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,MAAA,GAAArL,2DAAA;MAAA,OAASA,yDAAA,CAAAqL,MAAA,CAAAC,sBAAA,CAAAH,SAAA,CAAA5D,GAAA,CAAkC;IAAA,EAAC;IAE5CvH,uDAAA,aAA2B;IAC7BA,0DAAA,EAAS;;;;;IArELA,uDAAA,GAME;IANFA,wDAAA,YAAAA,6DAAA,IAAAO,GAAA,EAAAsE,MAAA,CAAA0G,iBAAA,CAAAJ,SAAA,CAAAK,IAAA,kBAAA3G,MAAA,CAAA0G,iBAAA,CAAAJ,SAAA,CAAAK,IAAA,qBAAA3G,MAAA,CAAA0G,iBAAA,CAAAJ,SAAA,CAAAK,IAAA,GAME;IAIAxL,uDAAA,GASE;IATFA,wDAAA,YAAAA,6DAAA,KAAAY,GAAA,EAAAiE,MAAA,CAAA0G,iBAAA,CAAAJ,SAAA,CAAAK,IAAA,kBAAA3G,MAAA,CAAA0G,iBAAA,CAAAJ,SAAA,CAAAK,IAAA,qBAAA3G,MAAA,CAAA0G,iBAAA,CAAAJ,SAAA,CAAAK,IAAA,GASE;IAKFxL,uDAAA,GACF;IADEA,gEAAA,MAAA6E,MAAA,CAAA4G,WAAA,CAAAN,SAAA,CAAAK,IAAA,OACF;IAIIxL,uDAAA,GAKE;IALFA,wDAAA,YAAAA,6DAAA,KAAAa,GAAA,EAAAsK,SAAA,CAAA1K,IAAA,cAAA0K,SAAA,CAAA1K,IAAA,eAKE;IAIAT,uDAAA,GAIE;IAJFA,wDAAA,YAAAA,6DAAA,KAAAc,GAAA,EAAAqK,SAAA,CAAA1K,IAAA,cAAA0K,SAAA,CAAA1K,IAAA,eAIE;IAEJT,uDAAA,GAKF;IALEA,gEAAA,MAAAmL,SAAA,CAAA1K,IAAA,gDAKF;IAC0BT,uDAAA,GAOxB;IAPwBA,+DAAA,CAAA6E,MAAA,CAAA0G,iBAAA,CAAAJ,SAAA,CAAAK,IAAA,qCAAA3G,MAAA,CAAA0G,iBAAA,CAAAJ,SAAA,CAAAK,IAAA,kDAOxB;;;;;IA0BdxL,4DAAA,cAGC;IACCA,uDAAA,YAEK;IACLA,4DAAA,UAAK;IACHA,oDAAA,oFAEF;IAAAA,0DAAA,EAAM;;;;;IAoBFA,4DAAA,kBAGC;IACCA,oDAAA,GAYF;IAAAA,0DAAA,EAAS;;;;IAdPA,wDAAA,UAAA0L,QAAA,CAAAnE,GAAA,IAAAmE,QAAA,CAAAnF,EAAA,CAA6B;IAE7BvG,uDAAA,GAYF;IAZEA,gEAAA,MAAA0L,QAAA,CAAAE,SAAA,aAAAF,QAAA,CAAAG,QAAA,IAAAH,QAAA,CAAA/I,IAAA,IAAA+I,QAAA,CAAAnF,EAAA,OAAAmF,QAAA,CAAAI,KAAA,UAAAJ,QAAA,CAAAI,KAAA,YAAAJ,QAAA,CAAAK,UAAA,UAAAL,QAAA,CAAAK,UAAA,kEAYF;;;;;;IAhCN/L,4DAAA,cAGC;IAGMA,oDAAA,kBAAW;IAAAA,0DAAA,EACb;IACDA,4DAAA,uBAIC;IAEGA,oDAAA,yCACF;IAAAA,0DAAA,EAAS;IACTA,wDAAA,IAAAgM,4DAAA,sBAgBS;IACXhM,0DAAA,EAAS;IAGXA,4DAAA,eAAkB;IAEbA,oDAAA,oCAAkB;IAAAA,0DAAA,EACpB;IACDA,4DAAA,gBAA0B;IAEtBA,uDAAA,uBAQE;IACFA,4DAAA,kBAGC;IACCA,uDAAA,cAA8C;IAC9CA,oDAAA,gBACF;IAAAA,0DAAA,EAAQ;IAEVA,4DAAA,gBAAoC;IAClCA,uDAAA,uBAOE;IACFA,4DAAA,kBAGC;IACCA,uDAAA,cAEK;IACLA,oDAAA,eACF;IAAAA,0DAAA,EAAQ;IAKdA,4DAAA,gBAAoB;IAKhBA,wDAAA,mBAAAiM,6EAAA;MAAAjM,2DAAA,CAAAkM,IAAA;MAAA,MAAAC,IAAA,GAAAnM,yDAAA;MAAA,MAAAqM,IAAA,GAAArM,yDAAA;MAAA,MAAAsM,OAAA,GAAAtM,2DAAA;MAERsM,OAAA,CAAAC,SAAA,CAAAJ,IAAA,CAAAK,KAAA,EAAAH,IAAA,CAAAI,OAAA,GAER,QAAQ,GAAG,OAAO,CACD;MAAA,OACpBzM,yDAAA,CAAAmM,IAAA,CAAAK,KAAA,GAAmB,EACxB;IAAA,EADyB;IAEDxM,uDAAA,cAAsC;IAACA,oDAAA,sCAEzC;IAAAA,0DAAA,EAAS;;;;;IA/EYA,uDAAA,GAAiB;IAAjBA,wDAAA,YAAA0M,MAAA,CAAAC,cAAA,CAAiB;IAoEpC3M,uDAAA,IAA8B;IAA9BA,wDAAA,cAAAmM,IAAA,CAAAK,KAAA,CAA8B;;;;;IAxM1CxM,4DAAA,cAGC;IAIOA,wDAAA,IAAA4M,kDAAA,oBAkFM;IACR5M,0DAAA,EAAM;IAIRA,4DAAA,cAAmC;IAE/BA,uDAAA,YAA2C;IAC3CA,oDAAA,0BACF;IAAAA,0DAAA,EAAK;IAGLA,wDAAA,IAAA6M,kDAAA,kBAWM;IAGN7M,wDAAA,KAAA8M,mDAAA,mBAkGM;IACR9M,0DAAA,EAAM;;;;IA9MmBA,uDAAA,GAAc;IAAdA,wDAAA,YAAA+M,MAAA,CAAA5G,WAAA,CAAc;IA8FlCnG,uDAAA,GAAiC;IAAjCA,wDAAA,SAAA+M,MAAA,CAAAJ,cAAA,CAAA9J,MAAA,OAAiC;IAcjC7C,uDAAA,GAA+B;IAA/BA,wDAAA,SAAA+M,MAAA,CAAAJ,cAAA,CAAA9J,MAAA,KAA+B;;;;;IA6HlC7C,4DAAA,cAGC;IACCA,uDAAA,YAEK;IACLA,4DAAA,UAAK;IACHA,oDAAA,oFAEF;IAAAA,0DAAA,EAAM;;;;;IAoBFA,4DAAA,kBAGC;IACCA,oDAAA,GAYF;IAAAA,0DAAA,EAAS;;;;IAdPA,wDAAA,UAAAgN,QAAA,CAAAzF,GAAA,IAAAyF,QAAA,CAAAzG,EAAA,CAA6B;IAE7BvG,uDAAA,GAYF;IAZEA,gEAAA,MAAAgN,QAAA,CAAApB,SAAA,aAAAoB,QAAA,CAAAnB,QAAA,IAAAmB,QAAA,CAAArK,IAAA,IAAAqK,QAAA,CAAAzG,EAAA,OAAAyG,QAAA,CAAAlB,KAAA,UAAAkB,QAAA,CAAAlB,KAAA,YAAAkB,QAAA,CAAAjB,UAAA,UAAAiB,QAAA,CAAAjB,UAAA,kEAYF;;;;;;IAhCN/L,4DAAA,cAGC;IAGMA,oDAAA,kBAAW;IAAAA,0DAAA,EACb;IACDA,4DAAA,uBAIC;IAEGA,oDAAA,yCACF;IAAAA,0DAAA,EAAS;IACTA,wDAAA,IAAAiN,oEAAA,sBAgBS;IACXjN,0DAAA,EAAS;IAGXA,4DAAA,eAAkB;IAEbA,oDAAA,oCAAkB;IAAAA,0DAAA,EACpB;IACDA,4DAAA,gBAA0B;IAEtBA,uDAAA,uBAQE;IACFA,4DAAA,kBAGC;IACCA,uDAAA,cAA8C;IAC9CA,oDAAA,gBACF;IAAAA,0DAAA,EAAQ;IAEVA,4DAAA,gBAAoC;IAClCA,uDAAA,uBAOE;IACFA,4DAAA,kBAGC;IACCA,uDAAA,cAEK;IACLA,oDAAA,eACF;IAAAA,0DAAA,EAAQ;IAKdA,4DAAA,gBAAoB;IAKhBA,wDAAA,mBAAAkN,qFAAA;MAAAlN,2DAAA,CAAAmN,IAAA;MAAA,MAAAC,IAAA,GAAApN,yDAAA;MAAA,MAAAwD,IAAA,GAAAxD,yDAAA;MAAA,MAAA2E,OAAA,GAAA3E,2DAAA;MAER2E,OAAA,CAAA4H,SAAA,CAAAa,IAAA,CAAAZ,KAAA,EAAAhJ,IAAA,CAAAiJ,OAAA,GAER,QAAQ,GAAG,OAAO,CACD;MAAA,OACrBzM,yDAAA,CAAAoN,IAAA,CAAAZ,KAAA,GAAoB,EACxB;IAAA,EADyB;IAEDxM,uDAAA,cAAsC;IAACA,oDAAA,sCAEzC;IAAAA,0DAAA,EAAS;;;;;IA/EYA,uDAAA,GAAiB;IAAjBA,wDAAA,YAAAqN,OAAA,CAAAV,cAAA,CAAiB;IAoEpC3M,uDAAA,IAA+B;IAA/BA,wDAAA,cAAAoN,IAAA,CAAAZ,KAAA,CAA+B;;;;;IAzHzCxM,4DAAA,cAAqB;IAIbA,uDAAA,aAA4C;IAC9CA,0DAAA,EAAM;IACNA,4DAAA,aAAuB;IAAAA,oDAAA,0CAA8B;IAAAA,0DAAA,EAAK;IAC1DA,4DAAA,YAAsB;IACpBA,oDAAA,uFAEF;IAAAA,0DAAA,EAAI;IAKRA,4DAAA,cAAmC;IAE/BA,uDAAA,aAA2C;IAC3CA,oDAAA,2BACF;IAAAA,0DAAA,EAAK;IAGLA,wDAAA,KAAAsN,2DAAA,kBAWM;IAGNtN,wDAAA,KAAAuN,2DAAA,mBAkGM;IACRvN,0DAAA,EAAM;;;;IAhHDA,uDAAA,IAAiC;IAAjCA,wDAAA,SAAAoE,MAAA,CAAAuI,cAAA,CAAA9J,MAAA,OAAiC;IAcjC7C,uDAAA,GAA+B;IAA/BA,wDAAA,SAAAoE,MAAA,CAAAuI,cAAA,CAAA9J,MAAA,KAA+B;;;;;;IAlgBpD7C,4DAAA,aAGC;IAGGA,uDAAA,aAEO;IAMPA,4DAAA,aAA4D;IAExDA,uDAAA,aAAmE;IAWrEA,0DAAA,EAAM;IAIVA,4DAAA,cAAiD;IAG7CA,uDAAA,eAEO;IAKPA,4DAAA,eAEC;IAOOA,oDAAA,IACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,aAAsC;IACpCA,oDAAA,iEACF;IAAAA,0DAAA,EAAI;IAGJA,4DAAA,eAAoC;IAIhCA,uDAAA,aAAgD;IAChDA,4DAAA,gBAAsC;IAAAA,oDAAA,IAEpC;IAAAA,0DAAA,EAAO;IACTA,4DAAA,iBAA6B;IAAAA,oDAAA,eAAO;IAAAA,0DAAA,EAAQ;IAE9CA,4DAAA,eAEC;IACCA,uDAAA,aAAgD;IAChDA,4DAAA,gBAAsC;IAAAA,oDAAA,SAAC;IAAAA,0DAAA,EAAO;IAC9CA,4DAAA,iBAA6B;IAAAA,oDAAA,mBAAM;IAAAA,0DAAA,EAAQ;IAE7CA,4DAAA,eAEC;IACCA,uDAAA,aAAyD;IACzDA,4DAAA,gBAAsC;IAAAA,oDAAA,IAEpC;IAAAA,0DAAA,EAAO;IACTA,4DAAA,iBAA6B;IAAAA,oDAAA,0BAAQ;IAAAA,0DAAA,EAAQ;IAMnDA,4DAAA,eAEC;IAIGA,uDAAA,aAAgC;IAAAA,oDAAA,wBAClC;IAAAA,0DAAA,EAAK;IACLA,4DAAA,eAAuB;IAEnBA,wDAAA,mBAAAwN,8DAAA;MAAAxN,2DAAA,CAAAyN,IAAA;MAAA,MAAAC,OAAA,GAAA1N,2DAAA;MAAA,OAASA,yDAAA,CAAA0N,OAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;IAG3B3N,uDAAA,aAAiC;IAACA,oDAAA,oCACpC;IAAAA,0DAAA,EAAS;IACTA,4DAAA,kBAGC;IAFCA,wDAAA,mBAAA4N,8DAAA;MAAA5N,2DAAA,CAAAyN,IAAA;MAAA,MAAAI,OAAA,GAAA7N,2DAAA;MAAA,OAASA,yDAAA,CAAA6N,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAGhC9N,uDAAA,aAAgC;IAACA,oDAAA,gCACnC;IAAAA,0DAAA,EAAS;IACTA,4DAAA,eAAoC;IAEhCA,wDAAA,mBAAA+N,8DAAA;MAAA/N,2DAAA,CAAAyN,IAAA;MAAA,MAAAO,OAAA,GAAAhO,2DAAA;MAAA,OAASA,yDAAA,CAAAgO,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAGhCjO,uDAAA,aAAsC;IAACA,oDAAA,gBACzC;IAAAA,0DAAA,EAAS;IACTA,4DAAA,kBAGC;IAFCA,wDAAA,mBAAAkO,8DAAA;MAAAlO,2DAAA,CAAAyN,IAAA;MAAA,MAAAU,OAAA,GAAAnO,2DAAA;MAAA,OAASA,yDAAA,CAAAmO,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAGxBpO,uDAAA,aAAiC;IAACA,oDAAA,mBACpC;IAAAA,0DAAA,EAAS;IASrBA,4DAAA,eAAsB;IAYRA,uDAAA,aAA2C;IAC7CA,0DAAA,EAAM;IACNA,4DAAA,cAAiB;IAAAA,oDAAA,qBAAQ;IAAAA,0DAAA,EAAK;IAC9BA,4DAAA,aAA8B;IAC5BA,oDAAA,wDACF;IAAAA,0DAAA,EAAI;IAINA,4DAAA,eAA0B;IAIQA,oDAAA,mBAAW;IAAAA,0DAAA,EAAK;IAC9CA,4DAAA,gBAEC;IACCA,uDAAA,aAA2C;IAC3CA,oDAAA,IAMF;IAAAA,0DAAA,EAAO;IAGTA,4DAAA,eAAyD;IAErDA,oDAAA,IAIF;IAAAA,0DAAA,EAAI;IAINA,4DAAA,eAAyC;IAIrCA,uDAAA,aAAsC;IACtCA,oDAAA,IACF;IAAAA,0DAAA,EAAO;IACPA,4DAAA,gBAEC;IACCA,uDAAA,aAAyC;IACzCA,oDAAA,IACF;IAAAA,0DAAA,EAAO;IACPA,4DAAA,gBAEC;IACCA,uDAAA,aAAiC;IACjCA,oDAAA,2BACF;IAAAA,0DAAA,EAAO;IAUrBA,4DAAA,eAAsB;IAYRA,uDAAA,cAA2B;IAC7BA,0DAAA,EAAM;IACNA,oDAAA,8BACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,iBAAiE;IAC/DA,uDAAA,cAAgC;IAChCA,oDAAA,2DACF;IAAAA,0DAAA,EAAO;IAGXA,4DAAA,gBAA2B;IACzBA,uDAAA,wBAA2C;IAC7CA,0DAAA,EAAM;IAMZA,4DAAA,gBAAsB;IAWVA,uDAAA,cAAiC;IACnCA,0DAAA,EAAM;IACNA,oDAAA,mCACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,iBAAiE;IAC/DA,oDAAA,KACF;IAAAA,0DAAA,EAAO;IAGTA,4DAAA,gBAA2B;IAEzBA,wDAAA,MAAAqO,4CAAA,mBAwNM;IAENrO,wDAAA,MAAAsO,oDAAA,kCAAAtO,oEAAA,CA0Ic;IAChBA,0DAAA,EAAM;;;;;IArjBFA,uDAAA,IACF;IADEA,gEAAA,MAAAwO,MAAA,CAAAC,MAAA,CAAA9L,IAAA,MACF;IAW0C3C,uDAAA,GAEpC;IAFoCA,+DAAA,EAAAwO,MAAA,CAAAC,MAAA,CAAAtK,OAAA,kBAAAqK,MAAA,CAAAC,MAAA,CAAAtK,OAAA,CAAAtB,MAAA,OAEpC;IAcoC7C,uDAAA,IAEpC;IAFoCA,+DAAA,CAAAwO,MAAA,CAAAE,UAAA,CAAAF,MAAA,CAAAC,MAAA,CAAAE,SAAA,EAEpC;IA+EE3O,uDAAA,IAMF;IANEA,gEAAA,aAAAwO,MAAA,CAAAC,MAAA,CAAAG,KAAA,GAAAJ,MAAA,CAAA/C,WAAA,CAAA+C,MAAA,CAAAC,MAAA,CAAAG,KAAA,KAAAJ,MAAA,CAAAC,MAAA,CAAAG,KAAA,0BAMF;IAKE5O,uDAAA,GAIF;IAJEA,gEAAA,MAAAwO,MAAA,CAAAC,MAAA,CAAApM,WAAA,iEAIF;IASErC,uDAAA,GACF;IADEA,gEAAA,OAAAwO,MAAA,CAAAC,MAAA,CAAAtK,OAAA,kBAAAqK,MAAA,CAAAC,MAAA,CAAAtK,OAAA,CAAAtB,MAAA,oBACF;IAKE7C,uDAAA,GACF;IADEA,gEAAA,yBAAAwO,MAAA,CAAAE,UAAA,CAAAF,MAAA,CAAAC,MAAA,CAAAE,SAAA,OACF;IAuCO3O,uDAAA,IAAe;IAAfA,wDAAA,SAAAwO,MAAA,CAAAC,MAAA,CAAe;IAuB1BzO,uDAAA,IACF;IADEA,gEAAA,MAAAwO,MAAA,CAAArI,WAAA,CAAAtD,MAAA,mBACF;IAMG7C,uDAAA,GAA6C;IAA7CA,wDAAA,SAAAwO,MAAA,CAAArI,WAAA,IAAAqI,MAAA,CAAArI,WAAA,CAAAtD,MAAA,KAA6C,aAAAgM,GAAA;;;;;;IA4W5D7O,4DAAA,eAA2D;IAOjDA,uDAAA,aAAsE;IACtEA,4DAAA,eAAkB;IAChBA,oDAAA,kEACF;IAAAA,0DAAA,EAAM;IAERA,4DAAA,kBAGC;IADCA,wDAAA,mBAAA8O,6DAAA;MAAA9O,2DAAA,CAAA+O,IAAA;MAAA,MAAAC,OAAA,GAAAhP,2DAAA;MAAA,OAASA,yDAAA,CAAAgP,OAAA,CAAAf,oBAAA,EAAsB;IAAA,EAAC;IAEhCjO,uDAAA,aAAqC;IAACA,oDAAA,iDACxC;IAAAA,0DAAA,EAAS;;;ADpnBX,MAAOiP,qBAAqB;EAUhChK,YACUiK,aAA4B,EAC5BC,WAAwB;EAAE;EAC1BC,KAAqB,EACrBC,MAAc;IAHd,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IAbhB,KAAAZ,MAAM,GAAkB,IAAI;IAC5B,KAAAa,OAAO,GAAG,KAAK;IACf,KAAAjL,KAAK,GAAkB,IAAI;IAC3B,KAAAkL,QAAQ,GAAkB,IAAI;IAC9B,KAAAC,SAAS,GAAQ;MAAEjJ,EAAE,EAAE,EAAE;MAAE9F,IAAI,EAAE;IAAQ,CAAE;IAC3C,KAAAkM,cAAc,GAAW,EAAE;IAC3B,KAAA8C,WAAW,GAA8B,EAAE,CAAC,CAAC;IAC7C,KAAAtJ,WAAW,GAAU,EAAE,CAAC,CAAC;EAOtB;;EAEHX,QAAQA,CAAA;IACN,IAAI,CAAC+J,QAAQ,GAAG,IAAI,CAACH,KAAK,CAACM,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IAEtD;IACA,IAAI,CAACC,SAAS,EAAE;IAEhB,IAAI,IAAI,CAACN,QAAQ,EAAE;MACjB,IAAI,CAACO,UAAU,CAAC,IAAI,CAACP,QAAQ,CAAC;KAC/B,MAAM;MACL,IAAI,CAAClL,KAAK,GAAG,0BAA0B;;EAE3C;EAEA;EACAwL,SAASA,CAAA;IACP;IACA;IACA,MAAME,SAAS,GAAW,CACxB;MACExI,GAAG,EAAE,OAAO;MACZyI,QAAQ,EAAE,UAAU;MACpBlE,KAAK,EAAE,kBAAkB;MACzBrL,IAAI,EAAE,OAAO;MACbwP,QAAQ,EAAE;KACX,EACD;MACE1I,GAAG,EAAE,OAAO;MACZyI,QAAQ,EAAE,YAAY;MACtBlE,KAAK,EAAE,kBAAkB;MACzBrL,IAAI,EAAE,SAAS;MACfwP,QAAQ,EAAE;KACX,EACD;MACE1I,GAAG,EAAE,OAAO;MACZyI,QAAQ,EAAE,YAAY;MACtBlE,KAAK,EAAE,iBAAiB;MACxBrL,IAAI,EAAE,SAAS;MACfwP,QAAQ,EAAE;KACX,CACF;IAED;IACAC,UAAU,CAAC,MAAK;MACd;MACA,MAAMC,QAAQ,GAAG,CAAC,GAAGJ,SAAS,CAAC;MAC/B/J,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEkK,QAAQ,CAAC;MAE9D;MACA,IAAI,IAAI,CAAChK,WAAW,IAAI,IAAI,CAACA,WAAW,CAACtD,MAAM,GAAG,CAAC,EAAE;QACnD,MAAMuN,aAAa,GAAG,IAAI,CAACjK,WAAW,CAACC,GAAG,CAAEiK,CAAC,IAAKA,CAAC,CAAC7E,IAAI,CAAC;QACzD,IAAI,CAACmB,cAAc,GAAGoD,SAAS,CAACO,MAAM,CACnC9E,IAAI,IAAK,CAAC4E,aAAa,CAACrH,QAAQ,CAACyC,IAAI,CAACjE,GAAG,IAAIiE,IAAI,CAACjF,EAAE,IAAI,EAAE,CAAC,CAC7D;OACF,MAAM;QACL,IAAI,CAACoG,cAAc,GAAGoD,SAAS;;MAGjC/J,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAAC0G,cAAc,CAAC;MAE7D;MACA,IAAI,IAAI,CAAC8B,MAAM,IAAI,IAAI,CAACA,MAAM,CAACtK,OAAO,EAAE;QACtC,IAAI,CAACoM,iBAAiB,EAAE;;IAE5B,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACAA,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAAC9B,MAAM,IAAI,CAAC,IAAI,CAACA,MAAM,CAACtK,OAAO,EAAE;IAE1C,IAAI,CAACsK,MAAM,CAACtK,OAAO,CAAC0D,OAAO,CAAE2I,QAAQ,IAAI;MACvC,MAAMhF,IAAI,GAAG,IAAI,CAACmB,cAAc,CAAC8D,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAACnJ,GAAG,KAAKiJ,QAAQ,IAAIE,CAAC,CAACnK,EAAE,KAAKiK,QAAQ,CAC/C;MACD,IAAIhF,IAAI,IAAIA,IAAI,CAAC7I,IAAI,EAAE;QACrB,IAAI,CAAC8M,WAAW,CAACe,QAAQ,CAAC,GAAGhF,IAAI,CAAC7I,IAAI;OACvC,MAAM;QACL;QACA;QACA;QACA,IAAI,CAAC8M,WAAW,CAACe,QAAQ,CAAC,GAAGA,QAAQ;;IAEzC,CAAC,CAAC;EACJ;EAEA;EACAG,aAAaA,CAACH,QAAgB;IAC5B,OAAO,IAAI,CAACf,WAAW,CAACe,QAAQ,CAAC,IAAIA,QAAQ;EAC/C;EAEA;EACA/E,WAAWA,CAAC9D,MAA0B;IACpC,IAAI,CAACA,MAAM,EAAE;MACX,OAAO,YAAY;;IAGrB,MAAM6D,IAAI,GAAG,IAAI,CAACmB,cAAc,CAAC8D,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAACnJ,GAAG,KAAKI,MAAM,IAAI+I,CAAC,CAACnK,EAAE,KAAKoB,MAAM,CAC3C;IACD,IAAI6D,IAAI,EAAE;MACR,IAAIA,IAAI,CAACI,SAAS,IAAIJ,IAAI,CAACK,QAAQ,EAAE;QACnC,OAAO,GAAGL,IAAI,CAACI,SAAS,IAAIJ,IAAI,CAACK,QAAQ,EAAE;OAC5C,MAAM,IAAIL,IAAI,CAAC7I,IAAI,EAAE;QACpB,OAAO6I,IAAI,CAAC7I,IAAI;;;IAGpB,OAAOgF,MAAM;EACf;EAEA;EACA4D,iBAAiBA,CAAC5D,MAA0B;IAC1C,IAAI,CAACA,MAAM,EAAE;MACX,OAAO,EAAE;;IAGX,MAAM6D,IAAI,GAAG,IAAI,CAACmB,cAAc,CAAC8D,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAACnJ,GAAG,KAAKI,MAAM,IAAI+I,CAAC,CAACnK,EAAE,KAAKoB,MAAM,CAC3C;IACD,IAAI6D,IAAI,EAAE;MACR,OAAOA,IAAI,CAACO,UAAU,IAAIP,IAAI,CAAC/K,IAAI,IAAI,EAAE;;IAE3C,OAAO,EAAE;EACX;EAEAqP,UAAUA,CAACvJ,EAAU;IACnB,IAAI,CAAC+I,OAAO,GAAG,IAAI;IACnB,IAAI,CAACjL,KAAK,GAAG,IAAI;IAEjB,IAAI,CAAC6K,aAAa,CAAC0B,SAAS,CAACrK,EAAE,CAAC,CAACG,SAAS,CAAC;MACzCC,IAAI,EAAGkK,IAAI,IAAI;QACb7K,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE4K,IAAI,CAAC;QACjD,IAAI,CAACpC,MAAM,GAAGoC,IAAI;QAElB;QACA,IAAI,CAACC,eAAe,CAACvK,EAAE,CAAC;QAExB;QACA,IACE,IAAI,CAACkI,MAAM,IACX,IAAI,CAACA,MAAM,CAACtK,OAAO,IACnB,IAAI,CAACsK,MAAM,CAACtK,OAAO,CAACtB,MAAM,GAAG,CAAC,EAC9B;UACA,IAAI,CAAC0N,iBAAiB,EAAE;;QAG1B,IAAI,CAACjB,OAAO,GAAG,KAAK;MACtB,CAAC;MACDjL,KAAK,EAAGA,KAAK,IAAI;QACf2B,OAAO,CAAC3B,KAAK,CACX,oDAAoD,EACpDA,KAAK,CACN;QACD,IAAI,CAACA,KAAK,GACR,8EAA8E;QAChF,IAAI,CAACiL,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA;EACAwB,eAAeA,CAACvI,MAAc;IAC5B,IAAI,CAAC2G,aAAa,CAAC6B,cAAc,CAACxI,MAAM,CAAC,CAAC7B,SAAS,CAAC;MAClDC,IAAI,EAAGxC,OAAO,IAAI;QAChB6B,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE9B,OAAO,CAAC;QACpD,IAAI,CAACgC,WAAW,GAAGhC,OAAO;MAC5B,CAAC;MACDE,KAAK,EAAGA,KAAK,IAAI;QACf2B,OAAO,CAAC3B,KAAK,CACX,oDAAoD,EACpDA,KAAK,CACN;MACH;KACD,CAAC;EACJ;EAEAyJ,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACyB,QAAQ,EAAE;MACjB,IAAI,CAACF,MAAM,CAAC2B,QAAQ,CAAC,CAAC,mBAAmB,EAAE,IAAI,CAACzB,QAAQ,CAAC,CAAC;;EAE9D;EAEAtB,oBAAoBA,CAAA;IAClB,IAAI,CAACoB,MAAM,CAAC2B,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC1C;EAEArD,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC4B,QAAQ,EAAE;MACjB,IAAI,CAACF,MAAM,CAAC2B,QAAQ,CAAC,CAAC,gBAAgB,EAAE,IAAI,CAACzB,QAAQ,CAAC,CAAC;;EAE3D;EAEA;EACAb,UAAUA,CAACuC,IAA+B;IACxC,IAAI,CAACA,IAAI,EAAE;MACT,OAAO,KAAK;;IAGd,IAAI;MACF,IAAIC,OAAa;MAEjB,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;QAC5BC,OAAO,GAAG,IAAIjI,IAAI,CAACgI,IAAI,CAAC;OACzB,MAAM;QACLC,OAAO,GAAGD,IAAI;;MAGhB,IAAIE,KAAK,CAACD,OAAO,CAACE,OAAO,EAAE,CAAC,EAAE;QAC5B,OAAO,eAAe;;MAGxB;MACA,OAAOF,OAAO,CAACG,kBAAkB,CAAC,OAAO,EAAE;QACzCC,GAAG,EAAE,SAAS;QACdC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE;OACP,CAAC;KACH,CAAC,OAAOnN,KAAK,EAAE;MACd2B,OAAO,CAAC3B,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,OAAO,gBAAgB;;EAE3B;EAEA;EACAkI,SAASA,CAAC5E,MAAc,EAAElH,IAAY;IACpCuF,OAAO,CAACC,GAAG,CAAC,0BAA0B0B,MAAM,iBAAiBlH,IAAI,EAAE,CAAC;IAEpE,IAAI,CAAC,IAAI,CAAC8O,QAAQ,IAAI,CAAC5H,MAAM,EAAE;MAC7B3B,OAAO,CAAC3B,KAAK,CAAC,0CAA0C,CAAC;MACzD,IAAI,CAACA,KAAK,GAAG,0CAA0C;MACvD;;IAGF;IACA,MAAMoN,eAAe,GAAG,IAAI,CAACtL,WAAW,CAACuL,IAAI,CAAErB,CAAC,IAAKA,CAAC,CAAC7E,IAAI,KAAK7D,MAAM,CAAC;IACvE,IAAI8J,eAAe,EAAE;MACnB,IAAI,CAACpN,KAAK,GAAG,6CAA6C;MAC1DsN,KAAK,CAAC,6CAA6C,CAAC;MACpD;;IAGF;IACA,MAAMC,MAAM,GAAW;MACrBrL,EAAE,EAAEoB,MAAM;MACVlH,IAAI,EAAEA,IAAI,IAAI;KACf;IAED;IACA,MAAMoR,QAAQ,GAAG,IAAI,CAACpG,WAAW,CAAC9D,MAAM,CAAC;IACzC,MAAMmK,QAAQ,GAAGrR,IAAI,KAAK,OAAO,GAAG,gBAAgB,GAAG,QAAQ;IAE/D,IAAI,CAACyO,aAAa,CAAC6C,iBAAiB,CAAC,IAAI,CAACxC,QAAQ,EAAEqC,MAAM,CAAC,CAAClL,SAAS,CAAC;MACpEC,IAAI,EAAGW,QAAQ,IAAI;QACjBtB,OAAO,CAACC,GAAG,CACT,gBAAgB4L,QAAQ,kBAAkBC,QAAQ,eAAe,EACjExK,QAAQ,CACT;QAED;QACAqK,KAAK,CAAC,gBAAgBE,QAAQ,kBAAkBC,QAAQ,cAAc,CAAC;QAEvE;QACA,IAAI,CAAChB,eAAe,CAAC,IAAI,CAACvB,QAAS,CAAC;QAEpC;QACA,IAAI,CAACO,UAAU,CAAC,IAAI,CAACP,QAAS,CAAC;QAE/B;QACA,IAAI,CAACyC,oBAAoB,EAAE;MAC7B,CAAC;MACD3N,KAAK,EAAGA,KAAK,IAAI;QACf2B,OAAO,CAAC3B,KAAK,CACX,uDAAuD,EACvDA,KAAK,CACN;QACD,IAAI,CAACA,KAAK,GAAG,uCAAuCwN,QAAQ,WAAWC,QAAQ,iCAAiC;QAChHH,KAAK,CAAC,IAAI,CAACtN,KAAK,CAAC;MACnB;KACD,CAAC;EACJ;EAEA;EACA2N,oBAAoBA,CAAA;IAClB;IACA;IACA,IAAI,CAACnC,SAAS,EAAE;EAClB;EAEA;EACAkC,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACxC,QAAQ,IAAI,CAAC,IAAI,CAACC,SAAS,CAACjJ,EAAE,EAAE;MACxCP,OAAO,CAAC3B,KAAK,CAAC,sCAAsC,CAAC;MACrD;;IAGF,IAAI,CAACkI,SAAS,CAAC,IAAI,CAACiD,SAAS,CAACjJ,EAAE,EAAE,IAAI,CAACiJ,SAAS,CAAC/O,IAAI,IAAI,QAAQ,CAAC;EACpE;EAEA6K,sBAAsBA,CAACkF,QAAgB;IACrCxK,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEuK,QAAQ,CAAC;IAExE,IAAI,CAAC,IAAI,CAACjB,QAAQ,EAAE;MAClBvJ,OAAO,CAAC3B,KAAK,CAAC,sBAAsB,CAAC;MACrC,IAAI,CAACA,KAAK,GAAG,wDAAwD;MACrE;;IAGF;IACA,MAAMsD,MAAM,GAAG6I,QAAQ;IAEvB;IACA,MAAMqB,QAAQ,GAAG,IAAI,CAACpG,WAAW,CAAC9D,MAAM,CAAC;IAEzC3B,OAAO,CAACC,GAAG,CACT,yCAAyC0B,MAAM,KAAKkK,QAAQ,iBAAiB,IAAI,CAACtC,QAAQ,EAAE,CAC7F;IAED,IACE0C,OAAO,CACL,mDAAmDJ,QAAQ,gBAAgB,CAC5E,EACD;MACA7L,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAE7D,IAAI,CAACqJ,OAAO,GAAG,IAAI;MACnB,IAAI,CAACjL,KAAK,GAAG,IAAI;MAEjB,IAAI,CAAC6K,aAAa,CACf5D,sBAAsB,CAAC,IAAI,CAACiE,QAAQ,EAAE5H,MAAM,CAAC,CAC7CjB,SAAS,CAAC;QACTC,IAAI,EAAGW,QAAQ,IAAI;UACjBtB,OAAO,CAACC,GAAG,CACT,gBAAgB4L,QAAQ,mCAAmC,EAC3DvK,QAAQ,CACT;UACD,IAAI,CAACgI,OAAO,GAAG,KAAK;UAEpB;UACAqC,KAAK,CAAC,gBAAgBE,QAAQ,kCAAkC,CAAC;UAEjE;UACA,IAAI,CAACf,eAAe,CAAC,IAAI,CAACvB,QAAS,CAAC;UAEpC;UACA,IAAI,CAACO,UAAU,CAAC,IAAI,CAACP,QAAS,CAAC;UAE/B;UACA,IAAI,CAACyC,oBAAoB,EAAE;QAC7B,CAAC;QACD3N,KAAK,EAAGA,KAAK,IAAI;UACf2B,OAAO,CAAC3B,KAAK,CACX,4CAA4CwN,QAAQ,IAAI,EACxDxN,KAAK,CACN;UACD,IAAI,CAACiL,OAAO,GAAG,KAAK;UACpB,IAAI,CAACjL,KAAK,GAAG,wCAAwCwN,QAAQ,kBAC3DxN,KAAK,CAAC0C,OAAO,IAAI,iBACnB,EAAE;QACJ;OACD,CAAC;KACL,MAAM;MACLf,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;;EAExD;EAEAmI,YAAYA,CAAA;IACVpI,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAE3C,IAAI,CAAC,IAAI,CAACsJ,QAAQ,EAAE;MAClBvJ,OAAO,CAAC3B,KAAK,CAAC,sBAAsB,CAAC;MACrC,IAAI,CAACA,KAAK,GAAG,yDAAyD;MACtE;;IAGF2B,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACsJ,QAAQ,CAAC;IAEzD,IACE0C,OAAO,CACL,gDAAgD,IAAI,CAACxD,MAAM,EAAE9L,IAAI,mCAAmC,CACrG,EACD;MACAqD,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAE7D,IAAI,CAACqJ,OAAO,GAAG,IAAI;MACnB,IAAI,CAACjL,KAAK,GAAG,IAAI;MAEjB,IAAI,CAAC6K,aAAa,CAACd,YAAY,CAAC,IAAI,CAACmB,QAAQ,CAAC,CAAC7I,SAAS,CAAC;QACvDC,IAAI,EAAEA,CAAA,KAAK;UACTX,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;UAC3C,IAAI,CAACqJ,OAAO,GAAG,KAAK;UACpBqC,KAAK,CAAC,8BAA8B,CAAC;UACrC,IAAI,CAACtC,MAAM,CAAC2B,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;QAC1C,CAAC;QACD3M,KAAK,EAAGA,KAAK,IAAI;UACf2B,OAAO,CAAC3B,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;UAClE,IAAI,CAACiL,OAAO,GAAG,KAAK;UACpB,IAAI,CAACjL,KAAK,GAAG,qCACXA,KAAK,CAAC0C,OAAO,IAAI,iBACnB,EAAE;UACF4K,KAAK,CAAC,kCAAkC,IAAI,CAACtN,KAAK,EAAE,CAAC;QACvD;OACD,CAAC;KACH,MAAM;MACL2B,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;;EAExD;;;uBAraWgJ,qBAAqB,EAAAjP,+DAAA,CAAAyJ,0EAAA,GAAAzJ,+DAAA,CAAA2J,sEAAA,GAAA3J,+DAAA,CAAA6J,2DAAA,GAAA7J,+DAAA,CAAA6J,mDAAA;IAAA;EAAA;;;YAArBoF,qBAAqB;MAAAlF,SAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAkI,+BAAAhI,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZlCtK,wDAAA,IAAAuS,oCAAA,oBA4mBM;UAGNvS,wDAAA,IAAAwS,oCAAA,kBAqBM;;;UAloBHxS,wDAAA,SAAAuK,GAAA,CAAAkE,MAAA,CAAY;UA6mB6BzO,uDAAA,GAAa;UAAbA,wDAAA,UAAAuK,GAAA,CAAAkE,MAAA,CAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICzhBjDzO,4DAAA,cAGC;IAEGA,uDAAA,cAEO;IAITA,0DAAA,EAAM;IACNA,4DAAA,YAEC;IACCA,oDAAA,uCACF;IAAAA,0DAAA,EAAI;;;;;IAINA,4DAAA,cAAgC;IAMxBA,uDAAA,YAA2C;IAC7CA,0DAAA,EAAM;IACNA,4DAAA,cAAoB;IAIhBA,oDAAA,eACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,YAAsD;IACpDA,oDAAA,GACF;IAAAA,0DAAA,EAAI;;;;IADFA,uDAAA,GACF;IADEA,gEAAA,MAAAkB,MAAA,CAAAmD,KAAA,MACF;;;;;IAuEErE,4DAAA,cAGC;IACCA,uDAAA,YAAgD;IAChDA,oDAAA,kFACF;IAAAA,0DAAA,EAAM;;;;;;IA2DJA,4DAAA,iBAKC;IAFCA,wDAAA,mBAAAyS,sEAAA;MAAAzS,2DAAA,CAAAkL,IAAA;MAAA,MAAAG,MAAA,GAAArL,2DAAA;MAAA,OAASA,yDAAA,CAAAqL,MAAA,CAAA+C,YAAA,EAAc;IAAA,EAAC;IAGxBpO,uDAAA,YAAiC;IACjCA,oDAAA,kBACF;IAAAA,0DAAA,EAAS;;;;;IAeTA,uDAAA,eAGQ;;;;;;;;;;;IACRA,uDAAA,YAOK;;;;IAJHA,wDAAA,YAAAA,6DAAA,IAAAO,GAAA,EAAAkC,MAAA,CAAAiQ,UAAA,GAAAjQ,MAAA,CAAAiQ,UAAA,EAGE;;;;;;;;;;;;IAlKhB1S,4DAAA,aAA4C;IAC1CA,uDAAA,aAEO;IAKPA,4DAAA,cAEC;IAMKA,uDAAA,YAMK;IACLA,oDAAA,GAKF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,YAAiC;IAC/BA,oDAAA,6DACF;IAAAA,0DAAA,EAAI;IAINA,4DAAA,eAAiB;IACTA,wDAAA,sBAAA2S,8DAAA;MAAA3S,2DAAA,CAAA4S,IAAA;MAAA,MAAAC,OAAA,GAAA7S,2DAAA;MAAA,OAAYA,yDAAA,CAAA6S,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IAE3B9S,4DAAA,WAAK;IAIDA,oDAAA,8BACA;IAAAA,4DAAA,gBAAiD;IAAAA,oDAAA,SAAC;IAAAA,0DAAA,EAAO;IAE3DA,4DAAA,eAAsB;IAIlBA,uDAAA,aAEK;IACPA,0DAAA,EAAM;IACNA,4DAAA,qBASE;IALAA,wDAAA,mBAAA+S,4DAAA;MAAA/S,2DAAA,CAAA4S,IAAA;MAAA,MAAA/D,GAAA,GAAA7O,yDAAA;MAAA,MAAAgT,OAAA,GAAAhT,2DAAA;MAAA,OAASA,yDAAA,CAAAgT,OAAA,CAAAC,UAAA,CAAApE,GAAA,CAAArC,KAAA,CAA2B;IAAA,EAAC;IAJvCxM,0DAAA,EASE;IAEJA,wDAAA,KAAAkT,0CAAA,kBAMM;IACRlT,0DAAA,EAAM;IAGNA,4DAAA,WAAK;IAIDA,oDAAA,qBACA;IAAAA,4DAAA,gBAAiD;IAAAA,oDAAA,SAAC;IAAAA,0DAAA,EAAO;IAE3DA,4DAAA,eAAsB;IAElBA,uDAAA,aAEK;IACPA,0DAAA,EAAM;IACNA,4DAAA,wBASC;IANCA,wDAAA,mBAAAmT,+DAAA;MAAAnT,2DAAA,CAAA4S,IAAA;MAAA,MAAAQ,GAAA,GAAApT,yDAAA;MAAA,MAAAqT,OAAA,GAAArT,2DAAA;MAAA,OAASA,yDAAA,CAAAqT,OAAA,CAAAC,iBAAA,CAAAF,GAAA,CAAA5G,KAAA,CAAkC;IAAA,EAAC;IAM7CxM,0DAAA,EAAW;IAKhBA,uDAAA,iBAA8C;IAC9CA,4DAAA,eAEC;IAKKA,uDAAA,aAAkC;IACpCA,0DAAA,EAAM;IACNA,4DAAA,eAAwD;IACtDA,oDAAA,uFACF;IAAAA,0DAAA,EAAM;IAKVA,4DAAA,eAAoD;IAI9CA,wDAAA,mBAAAuT,6DAAA;MAAAvT,2DAAA,CAAA4S,IAAA;MAAA,MAAAY,OAAA,GAAAxT,2DAAA;MAAA,OAASA,yDAAA,CAAAwT,OAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IAGlBzT,uDAAA,aAAsC;IACtCA,oDAAA,gBACF;IAAAA,0DAAA,EAAS;IAETA,wDAAA,KAAA0T,6CAAA,qBAQS;IACX1T,0DAAA,EAAM;IAENA,4DAAA,kBAWC;IACCA,wDAAA,KAAA2T,2CAAA,mBAGQ;IACR3T,wDAAA,KAAA4T,wCAAA,gBAOK;IACL5T,oDAAA,IACF;IAAAA,0DAAA,EAAS;;;;IAnJTA,uDAAA,GAGE;IAHFA,wDAAA,YAAAA,6DAAA,KAAAY,GAAA,EAAAmM,MAAA,CAAA2F,UAAA,GAAA3F,MAAA,CAAA2F,UAAA,EAGE;IAEJ1S,uDAAA,GAKF;IALEA,gEAAA,MAAA+M,MAAA,CAAA2F,UAAA,oFAKF;IA4BQ1S,uDAAA,IAA2B;IAA3BA,wDAAA,UAAA+M,MAAA,CAAA0B,MAAA,CAAA9L,IAAA,OAA2B;IAS5B3C,uDAAA,GAAgB;IAAhBA,wDAAA,SAAA+M,MAAA,CAAA8G,UAAA,CAAgB;IAwBf7T,uDAAA,GAAkC;IAAlCA,wDAAA,UAAA+M,MAAA,CAAA0B,MAAA,CAAApM,WAAA,OAAkC;IAYnBrC,uDAAA,GAAsB;IAAtBA,wDAAA,UAAA+M,MAAA,CAAA0B,MAAA,CAAAG,KAAA,CAAsB;IA6BpC5O,uDAAA,IAA4B;IAA5BA,wDAAA,SAAA+M,MAAA,CAAA2F,UAAA,IAAA3F,MAAA,CAAAwC,QAAA,CAA4B;IAY/BvP,uDAAA,GAOC;IAPDA,wDAAA,aAAA+M,MAAA,CAAA+G,UAAA,KAAA/G,MAAA,CAAA0B,MAAA,CAAA9L,IAAA,KAAAoK,MAAA,CAAA0B,MAAA,CAAApM,WAAA,IAAA0K,MAAA,CAAA8G,UAAA,IAAA9G,MAAA,CAAAgH,SAAA,IAAAhH,MAAA,CAAAiH,gBAAA,CAOC;IAIEhU,uDAAA,GAAgB;IAAhBA,wDAAA,SAAA+M,MAAA,CAAA+G,UAAA,CAAgB;IAIhB9T,uDAAA,GAAiB;IAAjBA,wDAAA,UAAA+M,MAAA,CAAA+G,UAAA,CAAiB;IAOpB9T,uDAAA,GACF;IADEA,gEAAA,MAAA+M,MAAA,CAAA2F,UAAA,0DACF;;;AAWZ,MAAOuB,mBAAmB;EAoB9BhP,YACUiK,aAA4B,EAC5BgF,aAA4B,EAC5B/E,WAAwB,EACxBC,KAAqB,EACrBC,MAAc,EACdjK,mBAAwC;IALxC,KAAA8J,aAAa,GAAbA,aAAa;IACb,KAAAgF,aAAa,GAAbA,aAAa;IACb,KAAA/E,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAjK,mBAAmB,GAAnBA,mBAAmB;IAzB7B,KAAAqJ,MAAM,GAAW;MACf9L,IAAI,EAAE,EAAE;MACRN,WAAW,EAAE,EAAE;MACfuM,KAAK,EAAE,EAAE,CAAE;KACZ;;IACD,KAAA8D,UAAU,GAAG,KAAK;IAClB,KAAApD,OAAO,GAAG,KAAK;IACf,KAAAwE,UAAU,GAAG,KAAK;IAClB,KAAAzP,KAAK,GAAkB,IAAI;IAC3B,KAAAkL,QAAQ,GAAkB,IAAI;IAC9B,KAAAsE,UAAU,GAAG,KAAK;IAClB,KAAAE,SAAS,GAAG,KAAK;IACjB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAG,YAAY,GAAG,KAAK;IACpB,KAAAC,eAAe,GAAa,EAAE;IAC9B,KAAAC,gBAAgB,GAAa,EAAE,CAAC,CAAC;IACjC,KAAA1H,cAAc,GAAW,EAAE,CAAC,CAAC;IAC7B,KAAA2H,aAAa,GAAkB,IAAI,CAAC,CAAC;EASlC;;EAEH9O,QAAQA,CAAA;IACNQ,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAE9C;IACA,IAAI,CAACsO,cAAc,EAAE;IAErB;IACA,IAAI,CAACC,cAAc,EAAE;IAErB;IACA,IAAI,CAACC,cAAc,EAAE;IAErB;IACA,IAAI,CAACC,YAAY,EAAE;IAEnB,IAAI;MACF;MACA,IAAI,CAACnF,QAAQ,GAAG,IAAI,CAACH,KAAK,CAACM,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;MACtD,IAAI,CAAC8C,UAAU,GAAG,CAAC,CAAC,IAAI,CAACnD,QAAQ;MACjCvJ,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAACyM,UAAU,EAAE,KAAK,EAAE,IAAI,CAACnD,QAAQ,CAAC;MAEhE,IAAI,IAAI,CAACmD,UAAU,IAAI,IAAI,CAACnD,QAAQ,EAAE;QACpC,IAAI,CAACO,UAAU,CAAC,IAAI,CAACP,QAAQ,CAAC;QAE9B;QACAW,UAAU,CAAC,MAAK;UACdlK,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACsJ,QAAQ,CAAC;UAC1DvJ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACwI,MAAM,CAAC;QACxD,CAAC,EAAE,IAAI,CAAC;;KAEX,CAAC,OAAOpK,KAAK,EAAE;MACd2B,OAAO,CAAC3B,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,IAAI,CAACA,KAAK,GAAG,yBAAyB;;IAGxC;IACA6L,UAAU,CAAC,MAAK;MACd,MAAMyE,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,iBAAiB,CAAC;MAC5D,IAAIF,SAAS,EAAE;QACb3O,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C0O,SAAS,CAACG,gBAAgB,CAAC,OAAO,EAAE,MAAK;UACvC9O,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAChD,CAAC,CAAC;OACH,MAAM;QACLD,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;;IAEtD,CAAC,EAAE,IAAI,CAAC;EACV;EAEAsO,cAAcA,CAAA;IACZ,MAAMQ,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACT,IAAI,CAAC5F,WAAW,CAAC+F,UAAU,CAACH,KAAK,CAAC,CAACrO,SAAS,CAAC;QAC3CC,IAAI,EAAG6E,IAAS,IAAI;UAClBxF,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEuF,IAAI,CAAC;UAC1C,IAAI,CAAC8I,aAAa,GAAG9I,IAAI,CAACjE,GAAG,IAAIiE,IAAI,CAACjF,EAAE;UAExC;UACA,IAAI,CAAC,IAAI,CAACmM,UAAU,IAAI,IAAI,CAAC4B,aAAa,EAAE;YAC1C,IAAI,CAAC7F,MAAM,CAACG,KAAK,GAAG,IAAI,CAAC0F,aAAa;YACtCtO,OAAO,CAACC,GAAG,CACT,oCAAoC,EACpC,IAAI,CAACwI,MAAM,CAACG,KAAK,CAClB;;QAEL,CAAC;QACDvK,KAAK,EAAGA,KAAK,IAAI;UACf2B,OAAO,CAAC3B,KAAK,CACX,uDAAuD,EACvDA,KAAK,CACN;UACD,IAAI,CAACA,KAAK,GACR,qEAAqE;QACzE;OACD,CAAC;KACH,MAAM;MACL,IAAI,CAACA,KAAK,GACR,mEAAmE;;EAEzE;EAEAoQ,cAAcA,CAAA;IACZ,IAAI,CAACP,aAAa,CAACiB,UAAU,EAAE,CAACzO,SAAS,CAAC;MACxCC,IAAI,EAAGyO,OAAO,IAAI;QAChB,IAAI,CAACf,gBAAgB,GAAGe,OAAO;QAC/BpP,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEmP,OAAO,CAAC;MACtD,CAAC;MACD/Q,KAAK,EAAGA,KAAK,IAAI;QACf2B,OAAO,CAAC3B,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAC9D,IAAI,CAACA,KAAK,GACR,2EAA2E;MAC/E;KACD,CAAC;EACJ;EAEAqQ,YAAYA,CAAA;IACV,MAAMK,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACT,IAAI,CAAC5F,WAAW,CAACkG,WAAW,CAACN,KAAK,CAAC,CAACrO,SAAS,CAAC;QAC5CC,IAAI,EAAG2O,KAAU,IAAI;UACnB,IAAI,CAAC3I,cAAc,GAAG2I,KAAK;UAC3BtP,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEqP,KAAK,CAAC;QACzD,CAAC;QACDjR,KAAK,EAAGA,KAAK,IAAI;UACf2B,OAAO,CAAC3B,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;UACnE,IAAI,CAACA,KAAK,GACR,gFAAgF;QACpF;OACD,CAAC;;EAEN;EAEAmQ,cAAcA,CAAA;IACZ,IAAI,CAACtF,aAAa,CAACqG,UAAU,EAAE,CAAC7O,SAAS,CAAC;MACxCC,IAAI,EAAG6O,OAAO,IAAI;QAChB,IAAI,CAACpB,eAAe,GAAGoB,OAAO;QAC9BxP,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEuP,OAAO,CAAC;MACtD,CAAC;MACDnR,KAAK,EAAGA,KAAK,IAAI;QACf2B,OAAO,CAAC3B,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAChE;KACD,CAAC;EACJ;EAEAyL,UAAUA,CAACvJ,EAAU;IACnBP,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEM,EAAE,CAAC;IAC1C,IAAI,CAAC+I,OAAO,GAAG,IAAI;IACnB,IAAI,CAACjL,KAAK,GAAG,IAAI;IAEjB,IAAI,CAAC6K,aAAa,CAAC0B,SAAS,CAACrK,EAAE,CAAC,CAACG,SAAS,CAAC;MACzCC,IAAI,EAAGkK,IAAI,IAAI;QACb7K,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE4K,IAAI,CAAC;QACpC,IAAI,CAACpC,MAAM,GAAGoC,IAAI;QAElB;QACA7K,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAACwI,MAAM,CAAClH,GAAG,CAAC;QAChEvB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACsJ,QAAQ,CAAC;QAE5C;QACA,IAAI,IAAI,CAACd,MAAM,CAACtK,OAAO,IAAI,IAAI,CAACsK,MAAM,CAACtK,OAAO,CAACtB,MAAM,GAAG,CAAC,EAAE;UACzD,IAAI,CAAC4S,kBAAkB,EAAE;;QAG3B,IAAI,CAACnG,OAAO,GAAG,KAAK;MACtB,CAAC;MACDjL,KAAK,EAAGA,KAAK,IAAI;QACf2B,OAAO,CAAC3B,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAC9D,IAAI,CAACA,KAAK,GACR,8EAA8E;QAChF,IAAI,CAACiL,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA;EACAmG,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAAChH,MAAM,CAACtK,OAAO,IAAI,IAAI,CAACsK,MAAM,CAACtK,OAAO,CAACtB,MAAM,KAAK,CAAC,EAAE;MAC5D;;IAGFmD,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;IAEhE;IACA,IAAI,CAACwI,MAAM,CAACtK,OAAO,CAAC0D,OAAO,CAAE2I,QAAQ,IAAI;MACvC;MACA,MAAMhF,IAAI,GAAG,IAAI,CAACmB,cAAc,CAAC8D,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAACnJ,GAAG,KAAKiJ,QAAQ,IAAIE,CAAC,CAACnK,EAAE,KAAKiK,QAAQ,CAC/C;MACD,IAAIhF,IAAI,EAAE;QACRxF,OAAO,CAACC,GAAG,CACT,UAAUuK,QAAQ,yCAAyC,EAC3DhF,IAAI,CACL;QAED;QACA,IAAI,CAACA,IAAI,CAACM,KAAK,IAAK,CAACN,IAAI,CAACO,UAAU,IAAI,CAACP,IAAI,CAAC/K,IAAK,EAAE;UACnD;UACA,MAAMsU,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC3C,IAAIF,KAAK,EAAE;YACT,IAAI,CAAC5F,WAAW,CAACuG,WAAW,CAAClF,QAAQ,EAAEuE,KAAK,CAAC,CAACrO,SAAS,CAAC;cACtDC,IAAI,EAAGgP,QAAa,IAAI;gBACtB3P,OAAO,CAACC,GAAG,CACT,4CAA4CuK,QAAQ,aAAa,EACjEmF,QAAQ,CACT;gBAED;gBACA,MAAMrP,KAAK,GAAG,IAAI,CAACqG,cAAc,CAACiJ,SAAS,CACxClF,CAAC,IAAKA,CAAC,CAACnJ,GAAG,KAAKiJ,QAAQ,IAAIE,CAAC,CAACnK,EAAE,KAAKiK,QAAQ,CAC/C;gBACD,IAAIlK,KAAK,KAAK,CAAC,CAAC,EAAE;kBAChB,IAAI,CAACqG,cAAc,CAACrG,KAAK,CAAC,GAAG;oBAC3B,GAAG,IAAI,CAACqG,cAAc,CAACrG,KAAK,CAAC;oBAC7B,GAAGqP;mBACJ;;cAEL,CAAC;cACDtR,KAAK,EAAGA,KAAK,IAAI;gBACf2B,OAAO,CAAC3B,KAAK,CACX,+EAA+EmM,QAAQ,GAAG,EAC1FnM,KAAK,CACN;cACH;aACD,CAAC;;;OAGP,MAAM;QACL;QACA,MAAM0Q,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3C,IAAIF,KAAK,EAAE;UACT,IAAI,CAAC5F,WAAW,CAACuG,WAAW,CAAClF,QAAQ,EAAEuE,KAAK,CAAC,CAACrO,SAAS,CAAC;YACtDC,IAAI,EAAGgP,QAAa,IAAI;cACtB3P,OAAO,CAACC,GAAG,CACT,4BAA4BuK,QAAQ,aAAa,EACjDmF,QAAQ,CACT;cACD;cACA,IACE,CAAC,IAAI,CAAChJ,cAAc,CAAC+E,IAAI,CACtBhB,CAAC,IAAKA,CAAC,CAACnJ,GAAG,KAAKoO,QAAQ,CAACpO,GAAG,IAAImJ,CAAC,CAACnK,EAAE,KAAKoP,QAAQ,CAACpP,EAAE,CACtD,EACD;gBACA,IAAI,CAACoG,cAAc,CAAClH,IAAI,CAACkQ,QAAQ,CAAC;;YAEtC,CAAC;YACDtR,KAAK,EAAGA,KAAK,IAAI;cACf2B,OAAO,CAAC3B,KAAK,CACX,+DAA+DmM,QAAQ,GAAG,EAC1EnM,KAAK,CACN;YACH;WACD,CAAC;;;IAGR,CAAC,CAAC;EACJ;EAEAwR,eAAeA,CAAClT,IAAY;IAC1B;IACA,IAAI,IAAI,CAAC+P,UAAU,IAAI,IAAI,CAACnD,QAAQ,EAAE;MACpC,OAAO,IAAI,CAAC6E,eAAe,CAAC1C,IAAI,CAC7BoE,CAAC,IAAKA,CAAC,CAACnT,IAAI,KAAKA,IAAI,IAAImT,CAAC,CAACvO,GAAG,KAAK,IAAI,CAACgI,QAAQ,CAClD;;IAEH;IACA,OAAO,IAAI,CAAC6E,eAAe,CAAC1C,IAAI,CAAEoE,CAAC,IAAKA,CAAC,CAACnT,IAAI,KAAKA,IAAI,CAAC;EAC1D;EAEAsQ,UAAUA,CAACzG,KAAa;IACtBxG,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEuG,KAAK,CAAC;IACnC,IAAI,CAACiC,MAAM,CAAC9L,IAAI,GAAG6J,KAAK;IAExB;IACA,IAAI,CAACqH,UAAU,GAAG,IAAI,CAACgC,eAAe,CAACrJ,KAAK,CAAC;IAC7C,IAAI,IAAI,CAACqH,UAAU,EAAE;MACnB7N,OAAO,CAAC+P,IAAI,CAAC,6BAA6B,CAAC;;IAG7C;IACA,IAAI,CAAChC,SAAS,GAAGvH,KAAK,CAAC3J,MAAM,GAAG,CAAC,IAAI2J,KAAK,CAAC3J,MAAM,GAAG,CAAC;IACrD,IAAI,IAAI,CAACkR,SAAS,EAAE;MAClB/N,OAAO,CAAC+P,IAAI,CAAC,4CAA4C,CAAC;;EAE9D;EAEAzC,iBAAiBA,CAAC9G,KAAa;IAC7BxG,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEuG,KAAK,CAAC;IAC1C,IAAI,CAACiC,MAAM,CAACpM,WAAW,GAAGmK,KAAK;IAE/B;IACA,IAAI,CAACwH,gBAAgB,GAAGxH,KAAK,CAAC3J,MAAM,GAAG,CAAC,IAAI2J,KAAK,CAAC3J,MAAM,GAAG,EAAE;IAC7D,IAAI,IAAI,CAACmR,gBAAgB,EAAE;MACzBhO,OAAO,CAAC+P,IAAI,CAAC,qDAAqD,CAAC;;EAEvE;EAEAjD,QAAQA,CAAA;IACN9M,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACwI,MAAM,CAAC;IAEhD;IACA,IAAI,CAAC,IAAI,CAACA,MAAM,CAAC9L,IAAI,EAAE;MACrB,IAAI,CAAC0B,KAAK,GAAG,gCAAgC;MAC7C;;IAGF,IAAI,IAAI,CAACoK,MAAM,CAAC9L,IAAI,CAACE,MAAM,GAAG,CAAC,EAAE;MAC/B,IAAI,CAACkR,SAAS,GAAG,IAAI;MACrB,IAAI,CAAC1P,KAAK,GAAG,yDAAyD;MACtE;;IAGF;IACA,IAAI,CAAC,IAAI,CAACoK,MAAM,CAACpM,WAAW,EAAE;MAC5B,IAAI,CAACgC,KAAK,GAAG,yCAAyC;MACtD;;IAGF,IAAI,IAAI,CAACoK,MAAM,CAACpM,WAAW,CAACQ,MAAM,GAAG,EAAE,EAAE;MACvC,IAAI,CAACmR,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAAC3P,KAAK,GACR,kEAAkE;MACpE;;IAGF;IACA,IAAI,IAAI,CAACwR,eAAe,CAAC,IAAI,CAACpH,MAAM,CAAC9L,IAAI,CAAC,EAAE;MAC1C,IAAI,CAACkR,UAAU,GAAG,IAAI;MACtB,IAAI,CAACxP,KAAK,GACR,oEAAoE;MACtE;;IAGF,IAAI,CAACyP,UAAU,GAAG,IAAI;IACtB,IAAI,CAACzP,KAAK,GAAG,IAAI;IAEjB;IACA,IAAI,CAAC,IAAI,CAACoK,MAAM,CAACG,KAAK,IAAI,IAAI,CAAC0F,aAAa,EAAE;MAC5C,IAAI,CAAC7F,MAAM,CAACG,KAAK,GAAG,IAAI,CAAC0F,aAAa;;IAGxC,IAAI,CAAC,IAAI,CAAC7F,MAAM,CAACG,KAAK,EAAE;MACtB,IAAI,CAACvK,KAAK,GACR,mFAAmF;MACrF;;IAGF;IACA,MAAM2R,YAAY,GAAW;MAC3BrT,IAAI,EAAE,IAAI,CAAC8L,MAAM,CAAC9L,IAAI;MACtBN,WAAW,EAAE,IAAI,CAACoM,MAAM,CAACpM,WAAW,IAAI,EAAE;MAC1CuM,KAAK,EAAE,IAAI,CAACH,MAAM,CAACG;KACpB;IAED;IACA,IAAI,IAAI,CAAC8D,UAAU,IAAI,IAAI,CAACnD,QAAQ,EAAE;MACpCyG,YAAY,CAACzO,GAAG,GAAG,IAAI,CAACgI,QAAQ;;IAGlCvJ,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE+P,YAAY,CAAC;IAE/C,IAAI,IAAI,CAACtD,UAAU,IAAI,IAAI,CAACnD,QAAQ,EAAE;MACpC;MACA,IAAI,CAACL,aAAa,CAAC+G,YAAY,CAAC,IAAI,CAAC1G,QAAQ,EAAEyG,YAAY,CAAC,CAACtP,SAAS,CAAC;QACrEC,IAAI,EAAGW,QAAQ,IAAI;UACjBtB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEqB,QAAQ,CAAC;UACxD,IAAI,CAACwM,UAAU,GAAG,KAAK;UACvB,IAAI,CAAC1O,mBAAmB,CAAC0B,WAAW,CAClC,aAAaQ,QAAQ,CAAC3E,IAAI,kCAAkC,CAC7D;UACD,IAAI,CAAC0M,MAAM,CAAC2B,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;QAC1C,CAAC;QACD3M,KAAK,EAAGA,KAAK,IAAI;UACf2B,OAAO,CAAC3B,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;UAClE,IAAI,CAACA,KAAK,GAAG,yCAAyCA,KAAK,CAAC0C,OAAO,EAAE;UACrE,IAAI,CAAC+M,UAAU,GAAG,KAAK;UACvB,IAAI,CAAC1O,mBAAmB,CAACM,SAAS,CAAC,WAAWrB,KAAK,CAAC0C,OAAO,EAAE,CAAC;QAChE;OACD,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAACmI,aAAa,CAACgH,SAAS,CAACF,YAAY,CAAC,CAACtP,SAAS,CAAC;QACnDC,IAAI,EAAGW,QAAQ,IAAI;UACjBtB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEqB,QAAQ,CAAC;UACpD,IAAI,CAACwM,UAAU,GAAG,KAAK;UACvB,IAAI,CAAC1O,mBAAmB,CAAC0B,WAAW,CAClC,aAAaQ,QAAQ,CAAC3E,IAAI,4BAA4B,CACvD;UACD,IAAI,CAAC0M,MAAM,CAAC2B,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;QAC1C,CAAC;QACD3M,KAAK,EAAGA,KAAK,IAAI;UACf2B,OAAO,CAAC3B,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;UAC3D,IAAI,CAACA,KAAK,GAAG,kCAAkCA,KAAK,CAAC0C,OAAO,EAAE;UAC9D,IAAI,CAAC+M,UAAU,GAAG,KAAK;UACvB,IAAI,CAAC1O,mBAAmB,CAACM,SAAS,CAAC,WAAWrB,KAAK,CAAC0C,OAAO,EAAE,CAAC;QAChE;OACD,CAAC;;EAEN;EAEA0M,MAAMA,CAAA;IACJzN,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B,IAAI,CAACoJ,MAAM,CAAC2B,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC1C;EAEA;EACAe,iBAAiBA,CAACvB,QAAgB,EAAE/P,IAAA,GAAe,QAAQ;IACzDuF,OAAO,CAACC,GAAG,CACT,2CAA2C,EAC3CuK,QAAQ,EACR,UAAU,EACV/P,IAAI,CACL;IACDuF,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACsJ,QAAQ,CAAC;IAC1DvJ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACwI,MAAM,CAAC;IAEtD;IACA,MAAMc,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAK,IAAI,CAACd,MAAM,IAAI,IAAI,CAACA,MAAM,CAAClH,GAAI;IAElEvB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEsJ,QAAQ,CAAC;IAE1C,IAAI,CAACA,QAAQ,IAAI,CAACiB,QAAQ,EAAE;MAC1BxK,OAAO,CAAC3B,KAAK,CAAC,sCAAsC,CAAC;MACrD,IAAI,CAACA,KAAK,GAAG,sCAAsC;MACnD2B,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEsJ,QAAQ,EAAE,WAAW,EAAEiB,QAAQ,CAAC;MAEzD;MACA,IAAI,CAACpL,mBAAmB,CAACM,SAAS,CAChC,sEAAsE,CACvE;MACD;;IAGF;IACA,IAAI,IAAI,CAAC+I,MAAM,CAACtK,OAAO,IAAI,IAAI,CAACsK,MAAM,CAACtK,OAAO,CAAC4E,QAAQ,CAACyH,QAAQ,CAAC,EAAE;MACjE,IAAI,CAACpL,mBAAmB,CAACM,SAAS,CAChC,wCAAwC,CACzC;MACD;;IAGF;IACA,MAAM8F,IAAI,GAAG,IAAI,CAACmB,cAAc,CAAC8D,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAACnJ,GAAG,KAAKiJ,QAAQ,IAAIE,CAAC,CAACnK,EAAE,KAAKiK,QAAQ,CAC/C;IACD,MAAMqB,QAAQ,GAAGrG,IAAI,GACjBA,IAAI,CAACI,SAAS,IAAIJ,IAAI,CAACK,QAAQ,GAC7B,GAAGL,IAAI,CAACI,SAAS,IAAIJ,IAAI,CAACK,QAAQ,EAAE,GACpCL,IAAI,CAAC7I,IAAI,IAAI6N,QAAQ,GACvBA,QAAQ;IAEZ;IACA,MAAMoB,MAAM,GAAW;MACrBrL,EAAE,EAAEiK,QAAQ;MACZ/P,IAAI,EAAEA;KACP;IAED,IAAI,CAAC6O,OAAO,GAAG,IAAI;IAEnBtJ,OAAO,CAACC,GAAG,CACT,2BAA2B4L,QAAQ,WAAWpR,IAAI,eAAe8O,QAAQ,EAAE,CAC5E;IAED,IAAI,CAACL,aAAa,CAAC6C,iBAAiB,CAACxC,QAAQ,EAAEqC,MAAM,CAAC,CAAClL,SAAS,CAAC;MAC/DC,IAAI,EAAGW,QAAQ,IAAI;QACjBtB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEqB,QAAQ,CAAC;QACnD,IAAI,CAAClC,mBAAmB,CAAC0B,WAAW,CAClC,GAAG+K,QAAQ,uBACTpR,IAAI,KAAK,OAAO,GAAG,gBAAgB,GAAG,QACxC,aAAa,CACd;QACD;QACA,IAAI,CAACqP,UAAU,CAACP,QAAQ,CAAC;QACzB,IAAI,CAACD,OAAO,GAAG,KAAK;MACtB,CAAC;MACDjL,KAAK,EAAGA,KAAK,IAAI;QACf2B,OAAO,CAAC3B,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD,IAAI,CAACA,KAAK,GACR,+DAA+D;QACjE,IAAI,CAACe,mBAAmB,CAACM,SAAS,CAChC,oCAAoC,GAAGrB,KAAK,CAAC0C,OAAO,CACrD;QACD,IAAI,CAACuI,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA;EACAqB,aAAaA,CAACH,QAAgB;IAC5B;IACA,MAAMhF,IAAI,GAAG,IAAI,CAACmB,cAAc,CAAC8D,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAACnJ,GAAG,KAAKiJ,QAAQ,IAAIE,CAAC,CAACnK,EAAE,KAAKiK,QAAQ,CAC/C;IACD,IAAIhF,IAAI,EAAE;MACR,IAAIA,IAAI,CAACI,SAAS,IAAIJ,IAAI,CAACK,QAAQ,EAAE;QACnC,OAAO,GAAGL,IAAI,CAACI,SAAS,IAAIJ,IAAI,CAACK,QAAQ,EAAE;OAC5C,MAAM,IAAIL,IAAI,CAAC7I,IAAI,EAAE;QACpB,OAAO6I,IAAI,CAAC7I,IAAI;;;IAIpB;IACA,MAAMiP,MAAM,GAAG,IAAI,CAACyC,gBAAgB,CAAC5D,IAAI,CACtCJ,CAAC,IAAKA,CAAC,CAAC9I,GAAG,KAAKiJ,QAAQ,IAAIH,CAAC,CAAC9J,EAAE,KAAKiK,QAAQ,CAC/C;IACD,IAAIoB,MAAM,IAAIA,MAAM,CAACjP,IAAI,EAAE;MACzB,OAAOiP,MAAM,CAACjP,IAAI;;IAGpB;IACA,OAAO6N,QAAQ;EACjB;EAEA;EACA2F,cAAcA,CAAC3F,QAAgB;IAC7B,MAAMhF,IAAI,GAAG,IAAI,CAACmB,cAAc,CAAC8D,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAACnJ,GAAG,KAAKiJ,QAAQ,IAAIE,CAAC,CAACnK,EAAE,KAAKiK,QAAQ,CAC/C;IACD,IAAIhF,IAAI,IAAIA,IAAI,CAACM,KAAK,EAAE;MACtB,OAAON,IAAI,CAACM,KAAK;;IAEnB,OAAO,eAAe;EACxB;EAEA;EACAsK,mBAAmBA,CAAC5F,QAAgB;IAClC,MAAMhF,IAAI,GAAG,IAAI,CAACmB,cAAc,CAAC8D,IAAI,CAClCC,CAAC,IAAKA,CAAC,CAACnJ,GAAG,KAAKiJ,QAAQ,IAAIE,CAAC,CAACnK,EAAE,KAAKiK,QAAQ,CAC/C;IACD,IAAIhF,IAAI,EAAE;MACR,IAAIA,IAAI,CAACO,UAAU,EAAE;QACnB,OAAOP,IAAI,CAACO,UAAU,KAAK,UAAU,GAAG,UAAU,GAAG,YAAY;OAClE,MAAM,IAAIP,IAAI,CAAC/K,IAAI,EAAE;QACpB,OAAO+K,IAAI,CAAC/K,IAAI,KAAK,UAAU,GAAG,UAAU,GAAG,YAAY;;;IAG/D,OAAO,cAAc;EACvB;EAEA;EACA4V,aAAaA,CAACC,SAAiB;IAC7B;IACA;IACA,OAAO,QAAQ;EACjB;EAEAhL,sBAAsBA,CAACkF,QAAgB;IACrCxK,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEuK,QAAQ,CAAC;IACxExK,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACsJ,QAAQ,CAAC;IAC1DvJ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACwI,MAAM,CAAC;IAEtD;IACA,MAAMc,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAK,IAAI,CAACd,MAAM,IAAI,IAAI,CAACA,MAAM,CAAClH,GAAI;IAElE,IAAI,CAACgI,QAAQ,EAAE;MACbvJ,OAAO,CAAC3B,KAAK,CAAC,sBAAsB,CAAC;MACrC,IAAI,CAACA,KAAK,GAAG,wDAAwD;MACrE,IAAI,CAACe,mBAAmB,CAACM,SAAS,CAChC,wDAAwD,CACzD;MACD;;IAGF,IAAI,CAAC8K,QAAQ,EAAE;MACbxK,OAAO,CAAC3B,KAAK,CAAC,uBAAuB,CAAC;MACtC,IAAI,CAACA,KAAK,GAAG,yDAAyD;MACtE,IAAI,CAACe,mBAAmB,CAACM,SAAS,CAChC,yDAAyD,CAC1D;MACD;;IAGF;IACA,MAAM6Q,UAAU,GAAG,IAAI,CAAC5F,aAAa,CAACH,QAAQ,CAAC;IAE/CxK,OAAO,CAACC,GAAG,CACT,yCAAyCuK,QAAQ,KAAK+F,UAAU,iBAAiBhH,QAAQ,EAAE,CAC5F;IAED,IAAI;MACF,IACE0C,OAAO,CAAC,oCAAoCsE,UAAU,eAAe,CAAC,EACtE;QACAvQ,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;QAE7D,IAAI,CAACqJ,OAAO,GAAG,IAAI;QACnB,IAAI,CAACjL,KAAK,GAAG,IAAI;QAEjB;QACA6L,UAAU,CAAC,MAAK;UACd,IAAI,CAAChB,aAAa,CACf5D,sBAAsB,CAACiE,QAAQ,EAAEiB,QAAQ,CAAC,CAC1C9J,SAAS,CAAC;YACTC,IAAI,EAAGW,QAAQ,IAAI;cACjBtB,OAAO,CAACC,GAAG,CACT,gBAAgBsQ,UAAU,mCAAmC,EAC7DjP,QAAQ,CACT;cACD,IAAI,CAACgI,OAAO,GAAG,KAAK;cACpB,IAAI,CAAClK,mBAAmB,CAAC0B,WAAW,CAClC,GAAGyP,UAAU,uCAAuC,CACrD;cAED;cACA,IAAI,CAACzG,UAAU,CAACP,QAAQ,CAAC;YAC3B,CAAC;YACDlL,KAAK,EAAGA,KAAK,IAAI;cACf2B,OAAO,CAAC3B,KAAK,CACX,4CAA4CkS,UAAU,IAAI,EAC1DlS,KAAK,CACN;cACD2B,OAAO,CAAC3B,KAAK,CAAC,sBAAsB,EAAE;gBACpCiE,MAAM,EAAEjE,KAAK,CAACiE,MAAM;gBACpBvB,OAAO,EAAE1C,KAAK,CAAC0C,OAAO;gBACtB1C,KAAK,EAAEA;eACR,CAAC;cAEF,IAAI,CAACiL,OAAO,GAAG,KAAK;cACpB,IAAI,CAACjL,KAAK,GAAG,wCAAwCkS,UAAU,kBAC7DlS,KAAK,CAAC0C,OAAO,IAAI,iBACnB,EAAE;cACF,IAAI,CAAC3B,mBAAmB,CAACM,SAAS,CAChC,qCAAqC,IAAI,CAACrB,KAAK,EAAE,CAClD;YACH;WACD,CAAC;QACN,CAAC,EAAE,GAAG,CAAC;OACR,MAAM;QACL2B,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;;KAEvD,CAAC,OAAO5B,KAAU,EAAE;MACnB2B,OAAO,CAAC3B,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,IAAI,CAACA,KAAK,GAAG,cAAcA,KAAK,EAAE0C,OAAO,IAAI,iBAAiB,EAAE;MAChE,IAAI,CAAC3B,mBAAmB,CAACM,SAAS,CAAC,cAAc,IAAI,CAACrB,KAAK,EAAE,CAAC;;EAElE;EAEA;EACA+J,YAAYA,CAAA;IACVpI,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;IACzED,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACsJ,QAAQ,CAAC;IAC1DvJ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACwI,MAAM,CAAC;IAEtD;IACA,MAAMc,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAK,IAAI,CAACd,MAAM,IAAI,IAAI,CAACA,MAAM,CAAClH,GAAI;IAElE,IAAI,CAACgI,QAAQ,EAAE;MACbvJ,OAAO,CAAC3B,KAAK,CAAC,sBAAsB,CAAC;MACrC,IAAI,CAACA,KAAK,GAAG,yDAAyD;MACtE,IAAI,CAACe,mBAAmB,CAACM,SAAS,CAChC,yDAAyD,CAC1D;MACD;;IAGFM,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEsJ,QAAQ,CAAC;IAE5D,IAAI;MACF,IACE0C,OAAO,CACL,gDAAgD,IAAI,CAACxD,MAAM,CAAC9L,IAAI,mCAAmC,CACpG,EACD;QACAqD,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;QAE7D,IAAI,CAACqJ,OAAO,GAAG,IAAI;QACnB,IAAI,CAACjL,KAAK,GAAG,IAAI;QAEjB;QACA6L,UAAU,CAAC,MAAK;UACd,IAAI,CAAChB,aAAa,CAACd,YAAY,CAACmB,QAAQ,CAAC,CAAC7I,SAAS,CAAC;YAClDC,IAAI,EAAGW,QAAQ,IAAI;cACjBtB,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEqB,QAAQ,CAAC;cAC/D,IAAI,CAACgI,OAAO,GAAG,KAAK;cACpB,IAAI,CAAClK,mBAAmB,CAAC0B,WAAW,CAClC,aAAa,IAAI,CAAC2H,MAAM,CAAC9L,IAAI,gCAAgC,CAC9D;cAED;cACAuN,UAAU,CAAC,MAAK;gBACd,IAAI,CAACb,MAAM,CAAC2B,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;cAC1C,CAAC,EAAE,GAAG,CAAC;YACT,CAAC;YACD3M,KAAK,EAAGA,KAAK,IAAI;cACf2B,OAAO,CAAC3B,KAAK,CACX,4CAA4C,EAC5CA,KAAK,CACN;cACD2B,OAAO,CAAC3B,KAAK,CAAC,sBAAsB,EAAE;gBACpCiE,MAAM,EAAEjE,KAAK,CAACiE,MAAM;gBACpBvB,OAAO,EAAE1C,KAAK,CAAC0C,OAAO;gBACtB1C,KAAK,EAAEA;eACR,CAAC;cAEF,IAAI,CAACiL,OAAO,GAAG,KAAK;cACpB,IAAI,CAACjL,KAAK,GAAG,qCACXA,KAAK,CAAC0C,OAAO,IAAI,iBACnB,EAAE;cACF,IAAI,CAAC3B,mBAAmB,CAACM,SAAS,CAChC,kCAAkC,IAAI,CAACrB,KAAK,EAAE,CAC/C;YACH;WACD,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;OACR,MAAM;QACL2B,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;;KAEvD,CAAC,OAAO5B,KAAU,EAAE;MACnB2B,OAAO,CAAC3B,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,IAAI,CAACA,KAAK,GAAG,cAAcA,KAAK,EAAE0C,OAAO,IAAI,iBAAiB,EAAE;MAChE,IAAI,CAAC3B,mBAAmB,CAACM,SAAS,CAAC,cAAc,IAAI,CAACrB,KAAK,EAAE,CAAC;;EAElE;;;uBAhtBW4P,mBAAmB,EAAAjU,+DAAA,CAAAyJ,0EAAA,GAAAzJ,+DAAA,CAAA2J,0EAAA,GAAA3J,+DAAA,CAAA6J,sEAAA,GAAA7J,+DAAA,CAAAyW,2DAAA,GAAAzW,+DAAA,CAAAyW,mDAAA,GAAAzW,+DAAA,CAAA0W,sFAAA;IAAA;EAAA;;;YAAnBzC,mBAAmB;MAAAlK,SAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAuM,6BAAArM,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UApS5BtK,4DAAA,aAEC;UAGGA,uDAAA,aAEO;UAMPA,4DAAA,aAA4D;UAExDA,uDAAA,aAAmE;UAWrEA,0DAAA,EAAM;UAIVA,4DAAA,cAAiD;UAG7CA,uDAAA,cAEO;UAKPA,4DAAA,eAEC;UAQOA,oDAAA,IACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,aAAsD;UACpDA,oDAAA,IAKF;UAAAA,0DAAA,EAAI;UAGNA,4DAAA,kBAGC;UAFCA,wDAAA,mBAAA4W,sDAAA;YAAA,OAASrM,GAAA,CAAAkJ,MAAA,EAAQ;UAAA,EAAC;UAGlBzT,uDAAA,aAAsC;UACtCA,oDAAA,gCACF;UAAAA,0DAAA,EAAS;UAMfA,wDAAA,KAAA6W,mCAAA,kBAiBM;UAGN7W,wDAAA,KAAA8W,mCAAA,mBAoBM;UAGN9W,wDAAA,KAAA+W,mCAAA,oBA0KM;UACR/W,0DAAA,EAAM;;;UA7OMA,uDAAA,IACF;UADEA,gEAAA,MAAAuK,GAAA,CAAAmI,UAAA,0DACF;UAEE1S,uDAAA,GAKF;UALEA,gEAAA,MAAAuK,GAAA,CAAAmI,UAAA,sJAKF;UAgBL1S,uDAAA,GAAa;UAAbA,wDAAA,SAAAuK,GAAA,CAAA+E,OAAA,CAAa;UAmBVtP,uDAAA,GAAW;UAAXA,wDAAA,SAAAuK,GAAA,CAAAlG,KAAA,CAAW;UAuBXrE,uDAAA,GAAc;UAAdA,wDAAA,UAAAuK,GAAA,CAAA+E,OAAA,CAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;AChI4B;AACtB;AACM;;;;;AASlC,MAAO4H,qBAAqB;EAYhCjS,YACUoK,MAAc,EACd8H,QAAkB,EAClBjI,aAA4B;IAF5B,KAAAG,MAAM,GAANA,MAAM;IACN,KAAA8H,QAAQ,GAARA,QAAQ;IACR,KAAAjI,aAAa,GAAbA,aAAa;IAdvB,KAAAkI,eAAe,GAAwB,IAAIH,4CAAU,EAAW;IAEhE;IACA,KAAAI,SAAS,GAAW,qBAAqB;IACzC,KAAAC,YAAY,GAAW,0CAA0C;IAEjE;IACA,KAAAC,YAAY,GAAW,CAAC;IACxB,KAAAC,YAAY,GAAW,CAAC;IACxB,KAAAC,YAAY,GAAW,CAAC;EAMrB;EAEHjS,QAAQA,CAAA;IACN,IAAI,CAACkS,cAAc,EAAE;IACrB,IAAI,CAACC,eAAe,EAAE;IAEtB;IACA,IAAI,CAACtI,MAAM,CAACuI,MAAM,CACfnR,IAAI,CAAC6J,sDAAM,CAAEuH,KAAK,IAAKA,KAAK,YAAYb,0DAAa,CAAC,CAAC,CACvDtQ,SAAS,CAAC,MAAK;MACd,IAAI,CAACiR,eAAe,EAAE;IACxB,CAAC,CAAC;EACN;EAEAD,cAAcA,CAAA;IACZ;IACA,IAAI,CAACxI,aAAa,CAACqG,UAAU,EAAE,CAAC7O,SAAS,CAAC;MACxCC,IAAI,EAAG6O,OAAO,IAAI;QAChB,IAAI,CAAC+B,YAAY,GAAG/B,OAAO,CAAC3S,MAAM;QAElC;QACA,IAAI,CAAC2U,YAAY,GAAGhC,OAAO,CAAC/M,MAAM,CAAC,CAACC,KAAK,EAAE+F,MAAM,KAAI;UACnD,OAAO/F,KAAK,IAAI+F,MAAM,CAACtK,OAAO,GAAGsK,MAAM,CAACtK,OAAO,CAACtB,MAAM,GAAG,CAAC,CAAC;QAC7D,CAAC,EAAE,CAAC,CAAC;QAEL;QACA,IAAI,CAAC4U,YAAY,GAAG,CAAC;MACvB,CAAC;MACDpT,KAAK,EAAGA,KAAK,IAAI;QACf2B,OAAO,CAAC3B,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;MACrE;KACD,CAAC;EACJ;EAEAsT,eAAeA,CAAA;IACb,MAAMG,GAAG,GAAG,IAAI,CAACzI,MAAM,CAACyI,GAAG;IAE3B,IAAIA,GAAG,CAAC/O,QAAQ,CAAC,gBAAgB,CAAC,EAAE;MAClC,IAAI,CAACsO,SAAS,GAAG,mBAAmB;MACpC,IAAI,CAACC,YAAY,GAAG,uCAAuC;KAC5D,MAAM,IAAIQ,GAAG,CAAC/O,QAAQ,CAAC,kBAAkB,CAAC,EAAE;MAC3C,IAAI,CAACsO,SAAS,GAAG,kBAAkB;MACnC,IAAI,CAACC,YAAY,GAAG,4CAA4C;KACjE,MAAM,IAAIQ,GAAG,CAAC/O,QAAQ,CAAC,sBAAsB,CAAC,EAAE;MAC/C,IAAI,CAACsO,SAAS,GAAG,aAAa;MAC9B,IAAI,CAACC,YAAY,GAAG,iDAAiD;KACtE,MAAM,IAAIQ,GAAG,CAAC/O,QAAQ,CAAC,iBAAiB,CAAC,EAAE;MAC1C,IAAI,CAACsO,SAAS,GAAG,qBAAqB;MACtC,IAAI,CAACC,YAAY,GAAG,qCAAqC;KAC1D,MAAM,IAAIQ,GAAG,CAAC/O,QAAQ,CAAC,eAAe,CAAC,EAAE;MACxC,IAAI,CAACsO,SAAS,GAAG,mBAAmB;MACpC,IAAI,CAACC,YAAY,GAAG,2CAA2C;KAChE,MAAM;MACL,IAAI,CAACD,SAAS,GAAG,qBAAqB;MACtC,IAAI,CAACC,YAAY,GAAG,0CAA0C;;EAElE;EAEAS,MAAMA,CAAA;IACJ,IAAI,CAACZ,QAAQ,CAACa,IAAI,EAAE;EACtB;;;uBA5EWd,qBAAqB,EAAAlX,+DAAA,CAAAyJ,mDAAA,GAAAzJ,+DAAA,CAAA2J,qDAAA,GAAA3J,+DAAA,CAAA6J,0EAAA;IAAA;EAAA;;;YAArBqN,qBAAqB;MAAAnN,SAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA8N,+BAAA5N,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZlCtK,4DAAA,aAEC;UAGGA,uDAAA,aAEO;UAMPA,4DAAA,aAA4D;UAExDA,uDAAA,aAAmE;UAWrEA,0DAAA,EAAM;UAKVA,4DAAA,cAAyC;UAW/BA,uDAAA,aAA+C;UACjDA,0DAAA,EAAM;UACNA,4DAAA,WAAK;UAIDA,oDAAA,sBACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,aAAsD;UACpDA,oDAAA,+BACF;UAAAA,0DAAA,EAAI;UAMVA,4DAAA,eAAkC;UAQ5BA,uDAAA,aAEK;UAIPA,0DAAA,EAAM;UACNA,4DAAA,gBAAuB;UAAAA,oDAAA,8BAAiB;UAAAA,0DAAA,EAAO;UAIjDA,4DAAA,aAIC;UAEGA,uDAAA,aAEK;UAIPA,0DAAA,EAAM;UACNA,4DAAA,gBAAuB;UAAAA,oDAAA,kCAAgB;UAAAA,0DAAA,EAAO;UAIhDA,4DAAA,aAIC;UAEGA,uDAAA,aAEK;UAIPA,0DAAA,EAAM;UACNA,4DAAA,gBAAuB;UAAAA,oDAAA,wBAAW;UAAAA,0DAAA,EAAO;UAI3CA,uDAAA,eAEO;UAGPA,4DAAA,eAAuB;UAInBA,oDAAA,sBACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,eAAuB;UAGhBA,oDAAA,qCAAc;UAAAA,0DAAA,EAChB;UACDA,4DAAA,gBAEG;UAAAA,oDAAA,SAAC;UAAAA,0DAAA,EACH;UAEHA,4DAAA,eAA+C;UAE1CA,oDAAA,sBAAc;UAAAA,0DAAA,EAChB;UACDA,4DAAA,gBAAiD;UAAAA,oDAAA,SAAC;UAAAA,0DAAA,EAAO;UAE3DA,4DAAA,eAA+C;UAE1CA,oDAAA,wBAAgB;UAAAA,0DAAA,EAClB;UACDA,4DAAA,gBAEG;UAAAA,oDAAA,SAAC;UAAAA,0DAAA,EACH;UAOTA,4DAAA,eAAuE;UAKnEA,uDAAA,aAAsC;UACtCA,oDAAA,iCACF;UAAAA,0DAAA,EAAS;UAKbA,4DAAA,eAAkD;UAWxCA,uDAAA,aAA+C;UACjDA,0DAAA,EAAM;UACNA,4DAAA,WAAK;UAEDA,oDAAA,kCACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,aAAsD;UACpDA,oDAAA,4DACF;UAAAA,0DAAA,EAAI;UAKRA,4DAAA,eAAyC;UAGrCA,uDAAA,iBAIE;UACFA,4DAAA,eAEC;UACCA,uDAAA,aAAgE;UAClEA,0DAAA,EAAM;UAIRA,4DAAA,kBAGC;UACCA,uDAAA,aAAgC;UAChCA,oDAAA,8BACF;UAAAA,0DAAA,EAAS;UAMfA,4DAAA,gBAAuC;UACrCA,uDAAA,qBAA+B;UACjCA,0DAAA,EAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrN6B;;;;;;;;ICmEtCA,4DAAA,cAGC;IAEGA,uDAAA,cAEO;IAITA,0DAAA,EAAM;IACNA,4DAAA,YAEC;IACCA,oDAAA,uCACF;IAAAA,0DAAA,EAAI;;;;;;IAINA,4DAAA,cAAgC;IAMxBA,uDAAA,YAA2C;IAC7CA,0DAAA,EAAM;IACNA,4DAAA,cAAoB;IAEhBA,oDAAA,8CACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,YAAsD;IACpDA,oDAAA,GACF;IAAAA,0DAAA,EAAI;IACJA,4DAAA,kBAGC;IAFCA,wDAAA,mBAAAmY,6DAAA;MAAAnY,2DAAA,CAAAoT,GAAA;MAAA,MAAAhP,MAAA,GAAApE,2DAAA;MAAA,OAASA,yDAAA,CAAAoE,MAAA,CAAAgU,WAAA,EAAa;IAAA,EAAC;IAGvBpY,uDAAA,aAAsC;IAACA,oDAAA,wBACzC;IAAAA,0DAAA,EAAS;;;;IAPPA,uDAAA,GACF;IADEA,gEAAA,MAAAkB,MAAA,CAAAmD,KAAA,MACF;;;;;;IAaRrE,4DAAA,cAGC;IAIGA,uDAAA,YAAqC;IACvCA,0DAAA,EAAM;IACNA,4DAAA,aAA0E;IACxEA,oDAAA,wCACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,YAA2D;IACzDA,oDAAA,gGAEF;IAAAA,0DAAA,EAAI;IACJA,4DAAA,iBAGC;IAFCA,wDAAA,mBAAAqY,4DAAA;MAAArY,2DAAA,CAAAsY,GAAA;MAAA,MAAAC,MAAA,GAAAvY,2DAAA;MAAA,OAASA,yDAAA,CAAAuY,MAAA,CAAAC,mBAAA,EAAqB;IAAA,EAAC;IAG/BxY,uDAAA,YAAuC;IACvCA,oDAAA,mCACF;IAAAA,0DAAA,EAAS;;;;;;IAQTA,4DAAA,cAGC;IAGGA,uDAAA,aAEO;IAKPA,4DAAA,cAAiB;IAMTA,oDAAA,GACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,cAAuC;IAInCA,uDAAA,aAAiC;IACjCA,oDAAA,IACF;IAAAA,0DAAA,EAAO;IAKXA,4DAAA,eAEC;IACCA,uDAAA,aAA+C;IACjDA,0DAAA,EAAM;IAIRA,4DAAA,aAEC;IACCA,oDAAA,IAKF;;IAAAA,0DAAA,EAAI;IAKRA,4DAAA,eAAuB;IAMjBA,wDAAA,mBAAAyY,mEAAA;MAAA,MAAAxN,WAAA,GAAAjL,2DAAA,CAAAmM,IAAA;MAAA,MAAAuM,SAAA,GAAAzN,WAAA,CAAAG,SAAA;MAAA,MAAAuN,OAAA,GAAA3Y,2DAAA;MAAA,OAASA,yDAAA,CAAA0Y,SAAA,CAAAnR,GAAA,IAAcoR,OAAA,CAAAC,sBAAA,CAAAF,SAAA,CAAAnR,GAAA,CAAkC;IAAA,EAAC;IAG1DvH,uDAAA,aAEK;IACLA,oDAAA,sBACF;IAAAA,0DAAA,EAAS;IAGTA,4DAAA,eAAyC;IAErCA,wDAAA,mBAAA6Y,mEAAA;MAAA,MAAA5N,WAAA,GAAAjL,2DAAA,CAAAmM,IAAA;MAAA,MAAAuM,SAAA,GAAAzN,WAAA,CAAAG,SAAA;MAAA,MAAA/J,OAAA,GAAArB,2DAAA;MAAA,OAASA,yDAAA,CAAA0Y,SAAA,CAAAnR,GAAA,IAAclG,OAAA,CAAAyM,oBAAA,CAAA4K,SAAA,CAAAnR,GAAA,CAAgC;IAAA,EAAC;IAIxDvH,uDAAA,aAA2B;IAC7BA,0DAAA,EAAS;IAETA,4DAAA,kBAIC;IAHCA,wDAAA,mBAAA8Y,mEAAA;MAAA,MAAA7N,WAAA,GAAAjL,2DAAA,CAAAmM,IAAA;MAAA,MAAAuM,SAAA,GAAAzN,WAAA,CAAAG,SAAA;MAAA,MAAA4H,OAAA,GAAAhT,2DAAA;MAAA,OAASA,yDAAA,CAAA0Y,SAAA,CAAAnR,GAAA,IAAcyL,OAAA,CAAArF,eAAA,CAAA+K,SAAA,CAAAnR,GAAA,CAA2B;IAAA,EAAC;IAInDvH,uDAAA,aAA4B;IAC9BA,0DAAA,EAAS;IAETA,4DAAA,kBAIC;IAHCA,wDAAA,mBAAA+Y,mEAAA;MAAA,MAAA9N,WAAA,GAAAjL,2DAAA,CAAAmM,IAAA;MAAA,MAAAuM,SAAA,GAAAzN,WAAA,CAAAG,SAAA;MAAA,MAAAiI,OAAA,GAAArT,2DAAA;MAAA,OAASA,yDAAA,CAAA0Y,SAAA,CAAAnR,GAAA,IAAc8L,OAAA,CAAAjF,YAAA,CAAAsK,SAAA,CAAAnR,GAAA,CAAwB;IAAA,EAAC;IAIhDvH,uDAAA,aAA4B;IAC9BA,0DAAA,EAAS;;;;IAzELA,uDAAA,GACF;IADEA,gEAAA,MAAA0Y,SAAA,CAAA/V,IAAA,MACF;IAMI3C,uDAAA,GACF;IADEA,gEAAA,OAAA0Y,SAAA,CAAAvU,OAAA,kBAAAuU,SAAA,CAAAvU,OAAA,CAAAtB,MAAA,sBACF;IAgBJ7C,uDAAA,GAKF;IALEA,gEAAA,MAAA0Y,SAAA,CAAArW,WAAA,IAAAqW,SAAA,CAAArW,WAAA,CAAAQ,MAAA,QAAA7C,yDAAA,QAAA0Y,SAAA,CAAArW,WAAA,mBAAAqW,SAAA,CAAArW,WAAA,yCAKF;;;;;IApDRrC,4DAAA,cAGC;IACCA,wDAAA,IAAAiZ,yCAAA,mBAgGM;IACRjZ,0DAAA,EAAM;;;;IAhGiBA,uDAAA,GAAU;IAAVA,wDAAA,YAAA6D,MAAA,CAAA2R,OAAA,CAAU;;;AD1I/B,MAAO0D,mBAAmB;EAK9BjU,YACUiK,aAA4B,EAC5BG,MAAc,EACdjK,mBAAwC;IAFxC,KAAA8J,aAAa,GAAbA,aAAa;IACb,KAAAG,MAAM,GAANA,MAAM;IACN,KAAAjK,mBAAmB,GAAnBA,mBAAmB;IAP7B,KAAAoQ,OAAO,GAAa,EAAE;IACtB,KAAAlG,OAAO,GAAG,KAAK;IACf,KAAAjL,KAAK,GAAkB,IAAI;EAMxB;EAEHmB,QAAQA,CAAA;IACN,IAAI,CAAC4S,WAAW,EAAE;EACpB;EAEAA,WAAWA,CAAA;IACT,IAAI,CAAC9I,OAAO,GAAG,IAAI;IACnB,IAAI,CAACjL,KAAK,GAAG,IAAI;IAEjB,IAAI,CAAC6K,aAAa,CAACqG,UAAU,EAAE,CAAC9O,IAAI,CAClC1G,wDAAQ,CAAC,MAAM,IAAI,CAACuP,OAAO,GAAG,KAAK,CAAC,CACrC,CAAC5I,SAAS,CAAC;MACVC,IAAI,EAAGkK,IAAI,IAAI;QACb7K,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE4K,IAAI,CAAC;QACtC,IAAI,CAAC2E,OAAO,GAAG3E,IAAI;QAEnB;QACA,IAAI,CAAC2E,OAAO,CAAC2D,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;UACzB,IAAID,CAAC,CAACzW,IAAI,IAAI0W,CAAC,CAAC1W,IAAI,EAAE;YACpB,OAAOyW,CAAC,CAACzW,IAAI,CAAC2W,aAAa,CAACD,CAAC,CAAC1W,IAAI,CAAC;;UAErC,OAAO,CAAC;QACV,CAAC,CAAC;MACJ,CAAC;MACD0B,KAAK,EAAGA,KAAK,IAAI;QACf2B,OAAO,CAAC3B,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAC9D,IAAI,CAACA,KAAK,GAAG,kEAAkE;QAC/E,IAAI,CAACe,mBAAmB,CAACM,SAAS,CAAC,uCAAuC,CAAC;MAC7E;KACD,CAAC;EACJ;EAEA8S,mBAAmBA,CAAA;IACjB,IAAI,CAACnJ,MAAM,CAAC2B,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;EAC5C;EAEAlD,oBAAoBA,CAACvH,EAAU;IAC7B,IAAI,CAAC8I,MAAM,CAAC2B,QAAQ,CAAC,CAAC,mBAAmB,EAAEzK,EAAE,CAAC,CAAC;EACjD;EAEAqS,sBAAsBA,CAACrS,EAAU;IAC/B,IAAI,CAAC8I,MAAM,CAAC2B,QAAQ,CAAC,CAAC,iBAAiB,EAAEzK,EAAE,CAAC,CAAC;EAC/C;EAEA6H,YAAYA,CAAC7H,EAAU;IACrB,IAAI,CAACA,EAAE,EAAE;MACPP,OAAO,CAAC3B,KAAK,CAAC,iBAAiB,CAAC;MAChC,IAAI,CAACe,mBAAmB,CAACM,SAAS,CAAC,uBAAuB,CAAC;MAC3D;;IAGF;IACA,MAAM+I,MAAM,GAAG,IAAI,CAAC+G,OAAO,CAAC/E,IAAI,CAACqF,CAAC,IAAIA,CAAC,CAACvO,GAAG,KAAKhB,EAAE,CAAC;IACnD,MAAMgT,UAAU,GAAG9K,MAAM,EAAE9L,IAAI,IAAI,cAAc;IAEjD,IAAIsP,OAAO,CAAC,gDAAgDsH,UAAU,KAAK,CAAC,EAAE;MAC5E,IAAI,CAACjK,OAAO,GAAG,IAAI;MAEnB,IAAI,CAACJ,aAAa,CAACd,YAAY,CAAC7H,EAAE,CAAC,CAACE,IAAI,CACtC1G,wDAAQ,CAAC,MAAM,IAAI,CAACuP,OAAO,GAAG,KAAK,CAAC,CACrC,CAAC5I,SAAS,CAAC;QACVC,IAAI,EAAEA,CAAA,KAAK;UACTX,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;UAC3C,IAAI,CAACb,mBAAmB,CAAC0B,WAAW,CAAC,aAAayS,UAAU,+BAA+B,CAAC;UAC5F,IAAI,CAACnB,WAAW,EAAE;QACpB,CAAC;QACD/T,KAAK,EAAGA,KAAK,IAAI;UACf2B,OAAO,CAAC3B,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;UACnE,IAAI,CAACA,KAAK,GAAG,kEAAkE;UAC/E,IAAI,CAACe,mBAAmB,CAACM,SAAS,CAAC,8CAA8C6T,UAAU,GAAG,CAAC;QACjG;OACD,CAAC;;EAEN;EAEA5L,eAAeA,CAACpH,EAAU;IACxB,IAAI,CAACA,EAAE,EAAE;MACPP,OAAO,CAAC3B,KAAK,CAAC,iBAAiB,CAAC;MAChC,IAAI,CAACe,mBAAmB,CAACM,SAAS,CAAC,uBAAuB,CAAC;MAC3D;;IAGF,MAAM+I,MAAM,GAAG,IAAI,CAAC+G,OAAO,CAAC/E,IAAI,CAACqF,CAAC,IAAIA,CAAC,CAACvO,GAAG,KAAKhB,EAAE,CAAC;IACnD,MAAMgT,UAAU,GAAG9K,MAAM,EAAE9L,IAAI,IAAI,cAAc;IAEjD;IACA,IAAI,CAAC0M,MAAM,CAAC2B,QAAQ,CAAC,CAAC,QAAQ,EAAEzK,EAAE,CAAC,CAAC;EAAK;;;uBAhGhC2S,mBAAmB,EAAAlZ,+DAAA,CAAAyJ,0EAAA,GAAAzJ,+DAAA,CAAA2J,mDAAA,GAAA3J,+DAAA,CAAA6J,sFAAA;IAAA;EAAA;;;YAAnBqP,mBAAmB;MAAAnP,SAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAoP,6BAAAlP,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZhCtK,4DAAA,aAEC;UAGGA,uDAAA,aAEO;UAMPA,4DAAA,aAA4D;UAExDA,uDAAA,aAAmE;UAWrEA,0DAAA,EAAM;UAIVA,4DAAA,cAAiD;UAI7CA,uDAAA,cAEO;UAKPA,4DAAA,eAEC;UAQOA,oDAAA,sBACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,aAAsD;UACpDA,oDAAA,2EACF;UAAAA,0DAAA,EAAI;UAGNA,4DAAA,kBAGC;UAFCA,wDAAA,mBAAAyZ,sDAAA;YAAA,OAASlP,GAAA,CAAAiO,mBAAA,EAAqB;UAAA,EAAC;UAG/BxY,uDAAA,aAEK;UACLA,oDAAA,8BACF;UAAAA,0DAAA,EAAS;UAMfA,wDAAA,KAAA0Z,mCAAA,kBAiBM;UAGN1Z,wDAAA,KAAA2Z,mCAAA,mBAwBM;UAGN3Z,wDAAA,KAAA4Z,mCAAA,mBAuBM;UAGN5Z,wDAAA,KAAA6Z,mCAAA,kBAqGM;UACR7Z,0DAAA,EAAM;;;UA9KDA,uDAAA,IAAa;UAAbA,wDAAA,SAAAuK,GAAA,CAAA+E,OAAA,CAAa;UAmBVtP,uDAAA,GAAW;UAAXA,wDAAA,SAAAuK,GAAA,CAAAlG,KAAA,CAAW;UA4BdrE,uDAAA,GAAgD;UAAhDA,wDAAA,UAAAuK,GAAA,CAAA+E,OAAA,KAAA/E,GAAA,CAAAlG,KAAA,IAAAkG,GAAA,CAAAiL,OAAA,CAAA3S,MAAA,OAAgD;UA0BhD7C,uDAAA,GAAoC;UAApCA,wDAAA,UAAAuK,GAAA,CAAA+E,OAAA,IAAA/E,GAAA,CAAAiL,OAAA,CAAA3S,MAAA,KAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IEhJzC7C,4DAAA,cAIC;IACCA,oDAAA,GACA;IAAAA,4DAAA,iBAKC;IAFCA,wDAAA,mBAAA8Z,uDAAA;MAAA9Z,2DAAA,CAAAkL,IAAA;MAAA,MAAAG,MAAA,GAAArL,2DAAA;MAAA,OAAAA,yDAAA,CAAAqL,MAAA,CAAAhH,KAAA,GAAiB,EAAE;IAAA,EAAC;IAErBrE,0DAAA,EAAS;;;;IANVA,uDAAA,GACA;IADAA,gEAAA,MAAAwO,MAAA,CAAAnK,KAAA,MACA;;;;;IASFrE,4DAAA,cAAgE;IAE9BA,oDAAA,oBAAa;IAAAA,0DAAA,EAAO;;;;;IAclDA,4DAAA,cAAuE;IACrEA,oDAAA,gGACF;IAAAA,0DAAA,EAAM;;;;;;IAcAA,4DAAA,SAAmC;IAC7BA,oDAAA,GAAiB;IAAAA,0DAAA,EAAK;IAC1BA,4DAAA,SAAI;IAAAA,oDAAA,GAAwB;IAAAA,0DAAA,EAAK;IACjCA,4DAAA,SAAI;IAAAA,oDAAA,GAAkB;IAAAA,0DAAA,EAAK;IAC3BA,4DAAA,SAAI;IAAAA,oDAAA,GAAyC;IAAAA,0DAAA,EAAK;IAClDA,4DAAA,SAAI;IAGAA,wDAAA,mBAAA+Z,+DAAA;MAAA,MAAA9O,WAAA,GAAAjL,2DAAA,CAAAga,IAAA;MAAA,MAAAC,UAAA,GAAAhP,WAAA,CAAAG,SAAA;MAAA,MAAA4H,OAAA,GAAAhT,2DAAA;MAAA,OAASA,yDAAA,CAAAgT,OAAA,CAAAkH,UAAA,CAAAD,UAAA,CAAkB;IAAA,EAAC;IAE5Bja,oDAAA,kBACF;IAAAA,0DAAA,EAAS;IACTA,4DAAA,kBAGC;IADCA,wDAAA,mBAAAma,+DAAA;MAAA,MAAAlP,WAAA,GAAAjL,2DAAA,CAAAga,IAAA;MAAA,MAAAC,UAAA,GAAAhP,WAAA,CAAAG,SAAA;MAAA,MAAAoI,OAAA,GAAAxT,2DAAA;MAAA,OAASA,yDAAA,CAAAia,UAAA,CAAA1S,GAAA,IAAciM,OAAA,CAAApF,YAAA,CAAA6L,UAAA,CAAA1S,GAAA,CAAwB;IAAA,EAAC;IAEhDvH,oDAAA,mBACF;IAAAA,0DAAA,EAAS;IACTA,4DAAA,kBAGC;IADCA,wDAAA,mBAAAoa,+DAAA;MAAA,MAAAnP,WAAA,GAAAjL,2DAAA,CAAAga,IAAA;MAAA,MAAAC,UAAA,GAAAhP,WAAA,CAAAG,SAAA;MAAA,MAAAkB,OAAA,GAAAtM,2DAAA;MAAA,OAASA,yDAAA,CAAAsM,OAAA,CAAA+N,eAAA,CAAAJ,UAAA,CAAuB;IAAA,EAAC;IAEjCja,oDAAA,4BACF;IAAAA,0DAAA,EAAS;;;;IAtBPA,uDAAA,GAAiB;IAAjBA,+DAAA,CAAAia,UAAA,CAAAtX,IAAA,CAAiB;IACjB3C,uDAAA,GAAwB;IAAxBA,+DAAA,CAAAia,UAAA,CAAA5X,WAAA,CAAwB;IACxBrC,uDAAA,GAAkB;IAAlBA,+DAAA,CAAAia,UAAA,CAAArL,KAAA,CAAkB;IAClB5O,uDAAA,GAAyC;IAAzCA,gEAAA,MAAAia,UAAA,CAAA9V,OAAA,kBAAA8V,UAAA,CAAA9V,OAAA,CAAAtB,MAAA,mBAAyC;;;;;IAhBrD7C,4DAAA,cAAyD;IAI7CA,oDAAA,UAAG;IAAAA,0DAAA,EAAK;IACZA,4DAAA,SAAI;IAAAA,oDAAA,kBAAW;IAAAA,0DAAA,EAAK;IACpBA,4DAAA,SAAI;IAAAA,oDAAA,YAAK;IAAAA,0DAAA,EAAK;IACdA,4DAAA,UAAI;IAAAA,oDAAA,eAAO;IAAAA,0DAAA,EAAK;IAChBA,4DAAA,UAAI;IAAAA,oDAAA,eAAO;IAAAA,0DAAA,EAAK;IAGpBA,4DAAA,aAAO;IACLA,wDAAA,KAAAsa,qCAAA,kBAyBK;IACPta,0DAAA,EAAQ;;;;IA1BiBA,uDAAA,IAAU;IAAVA,wDAAA,YAAA6D,MAAA,CAAA2R,OAAA,CAAU;;;;;IAoF7BxV,uDAAA,eAKQ;;;;;;IAGVA,4DAAA,iBAKC;IADCA,wDAAA,mBAAAua,2DAAA;MAAAva,2DAAA,CAAAwa,IAAA;MAAA,MAAAC,OAAA,GAAAza,2DAAA;MAAA,OAASA,yDAAA,CAAAya,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAEtB1a,oDAAA,gBACF;IAAAA,0DAAA,EAAS;;;;;;IAmCLA,4DAAA,aAGC;IACOA,oDAAA,GAAc;IAAAA,0DAAA,EAAO;IAC3BA,4DAAA,iBAKC;IAHCA,wDAAA,mBAAA2a,mEAAA;MAAA,MAAA1P,WAAA,GAAAjL,2DAAA,CAAAmN,IAAA;MAAA,MAAAyN,YAAA,GAAA3P,WAAA,CAAAG,SAAA;MAAA,MAAAzG,OAAA,GAAA3E,2DAAA;MAAA,OAC2BA,yDAAA,CAAA2E,OAAA,CAAA2G,sBAAA,CAAA3G,OAAA,CAAAkW,cAAA,CAAAtT,GAAA,EAAAqT,YAAA,CAEjD;IAAA,EADuB;IAED5a,oDAAA,gBACF;IAAAA,0DAAA,EAAS;;;;IARHA,uDAAA,GAAc;IAAdA,+DAAA,CAAA4a,YAAA,CAAc;;;;;IAX1B5a,4DAAA,UAKC;IAEGA,wDAAA,IAAA8a,0CAAA,iBAaK;IACP9a,0DAAA,EAAK;;;;IAZoBA,uDAAA,GAAyB;IAAzBA,wDAAA,YAAAqN,OAAA,CAAAwN,cAAA,CAAA1W,OAAA,CAAyB;;;;;IAelDnE,4DAAA,YAAsB;IAAAA,oDAAA,0CAA8B;IAAAA,0DAAA,EAAI;;;;;;IA9B9DA,4DAAA,UAA4B;IACtBA,oDAAA,GAAiC;IAAAA,0DAAA,EAAK;IAG1CA,4DAAA,cAAkB;IACZA,oDAAA,uBAAgB;IAAAA,0DAAA,EAAK;IACzBA,wDAAA,IAAA+a,qCAAA,kBAsBM;IACN/a,wDAAA,IAAAgb,6CAAA,iCAAAhb,oEAAA,CAEc;IAChBA,0DAAA,EAAM;IAGNA,4DAAA,cAAkB;IACZA,oDAAA,0BAAkB;IAAAA,0DAAA,EAAK;IAC3BA,4DAAA,eAA8B;IAC5BA,uDAAA,qBAKE;IACFA,4DAAA,kBAWC;IAJCA,wDAAA,mBAAAib,yDAAA;MAAAjb,2DAAA,CAAAyN,IAAA;MAAA,MAAAjK,IAAA,GAAAxD,yDAAA;MAAA,MAAA0N,OAAA,GAAA1N,2DAAA;MACuB0N,OAAA,CAAAqE,iBAAA,CAAArE,OAAA,CAAAmN,cAAA,CAAAtT,GAAA,EAAA/D,IAAA,CAAAgJ,KAAA,CACtB;MAAA,OAAsBxM,yDAAA,CAAAwD,IAAA,CAAAgJ,KAAA,GACvB,EAClB;IAAA,EADmB;IAEDxM,oDAAA,iBACF;IAAAA,0DAAA,EAAS;IAEXA,4DAAA,iBACG;IAAAA,oDAAA,iEAA0C;IAAAA,0DAAA,EAC5C;IAIHA,4DAAA,eAAmC;IAEvBA,oDAAA,aAAK;IAAAA,0DAAA,EAAS;IAACA,oDAAA,sGAEzB;IAAAA,0DAAA,EAAI;;;;;;IApEFA,uDAAA,GAAiC;IAAjCA,gEAAA,kBAAAyC,MAAA,CAAAoY,cAAA,CAAAlY,IAAA,KAAiC;IAMhC3C,uDAAA,GAGb;IAHaA,wDAAA,SAAAyC,MAAA,CAAAoY,cAAA,CAAA1W,OAAA,IAAA1B,MAAA,CAAAoY,cAAA,CAAA1W,OAAA,CAAAtB,MAAA,KAGb,aAAAuK,IAAA;IAoCcpN,uDAAA,GAIC;IAJDA,wDAAA,cAAAyC,MAAA,CAAAoY,cAAA,KAAApY,MAAA,CAAAoY,cAAA,CAAAtT,GAAA,KAAA/D,IAAA,CAAAgJ,KAAA,CAIC;;;ADzMb,MAAO0O,eAAe;EAS1BjW,YACUiK,aAA4B,EAC5BgF,aAA4B;IAD5B,KAAAhF,aAAa,GAAbA,aAAa;IACb,KAAAgF,aAAa,GAAbA,aAAa;IAVvB,KAAAsB,OAAO,GAAa,EAAE;IACtB,KAAA2F,SAAS,GAAW;MAAExY,IAAI,EAAE,EAAE;MAAEN,WAAW,EAAE;IAAE,CAAE;IACjD,KAAAwY,cAAc,GAAkB,IAAI;IACpC,KAAAO,SAAS,GAAG,KAAK;IACjB,KAAAhG,OAAO,GAAa,EAAE;IACtB,KAAA9F,OAAO,GAAG,KAAK;IACf,KAAAjL,KAAK,GAAG,EAAE;EAKP;EAEHmB,QAAQA,CAAA;IACN,IAAI,CAAC4S,WAAW,EAAE;IAClB,IAAI,CAACiD,WAAW,EAAE;EACpB;EAEAjD,WAAWA,CAAA;IACT,IAAI,CAAC9I,OAAO,GAAG,IAAI;IACnB,IAAI,CAACJ,aAAa,CAACqG,UAAU,EAAE,CAAC7O,SAAS,CAAC;MACxCC,IAAI,EAAGkK,IAAI,IAAI;QACb7K,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE4K,IAAI,CAAC;QACpC,IAAI,CAAC2E,OAAO,GAAG3E,IAAI;QACnB,IAAI,CAACvB,OAAO,GAAG,KAAK;MACtB,CAAC;MACDjL,KAAK,EAAGA,KAAK,IAAI;QACf2B,OAAO,CAAC3B,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACA,KAAK,GAAG,yCAAyC,GAAGA,KAAK,CAAC0C,OAAO;QACtE,IAAI,CAACuI,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA+L,WAAWA,CAAA;IACT,IAAI,CAAC/L,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC4E,aAAa,CAACiB,UAAU,EAAE,CAACzO,SAAS,CAAC;MACxCC,IAAI,EAAGkK,IAAI,IAAI;QACb7K,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE4K,IAAI,CAAC;QACpC,IAAI,CAACuE,OAAO,GAAGvE,IAAI;QACnB,IAAI,CAACvB,OAAO,GAAG,KAAK;MACtB,CAAC;MACDjL,KAAK,EAAGA,KAAK,IAAI;QACf2B,OAAO,CAAC3B,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACA,KAAK,GAAG,yCAAyC,GAAGA,KAAK,CAAC0C,OAAO;QACtE,IAAI,CAACuI,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA4G,SAASA,CAAA;IACPlQ,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACkV,SAAS,CAAC;IAE7C,IAAI,CAAC,IAAI,CAACA,SAAS,CAACxY,IAAI,EAAE;MACxBqD,OAAO,CAAC3B,KAAK,CAAC,uBAAuB,CAAC;MACtC,IAAI,CAACA,KAAK,GAAG,gCAAgC;MAC7C;;IAGF,IAAI,CAACiL,OAAO,GAAG,IAAI;IACnB,IAAI,CAACjL,KAAK,GAAG,EAAE;IAEf,IAAI,CAAC6K,aAAa,CAACgH,SAAS,CAAC,IAAI,CAACiF,SAAS,CAAC,CAACzU,SAAS,CAAC;MACrDC,IAAI,EAAGW,QAAQ,IAAI;QACjBtB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEqB,QAAQ,CAAC;QACnD,IAAI,CAAC8Q,WAAW,EAAE;QAClB,IAAI,CAAC+C,SAAS,GAAG;UAAExY,IAAI,EAAE,EAAE;UAAEN,WAAW,EAAE;QAAE,CAAE,CAAC,CAAC;QAChD,IAAI,CAACiN,OAAO,GAAG,KAAK;QAEpB;QACA,MAAMgM,cAAc,GAAG,2BAA2B;QAClD,IAAI,CAACjX,KAAK,GAAG,EAAE,CAAC,CAAC;QACjBsN,KAAK,CAAC2J,cAAc,CAAC;MACvB,CAAC;MACDjX,KAAK,EAAGA,KAAK,IAAI;QACf2B,OAAO,CAAC3B,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAACA,KAAK,GAAG,2CAA2C,IAAIA,KAAK,CAACA,KAAK,EAAE0C,OAAO,IAAI1C,KAAK,CAAC0C,OAAO,IAAI,eAAe,CAAC;QACrH,IAAI,CAACuI,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA4K,UAAUA,CAACzL,MAAc;IACvB,IAAI,CAAC2M,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACD,SAAS,GAAG;MACf5T,GAAG,EAAEkH,MAAM,CAAClH,GAAG;MACf5E,IAAI,EAAE8L,MAAM,CAAC9L,IAAI,IAAI,EAAE;MACvBN,WAAW,EAAEoM,MAAM,CAACpM,WAAW,IAAI,EAAE;MACrCuM,KAAK,EAAEH,MAAM,CAACG,KAAK;MACnBzK,OAAO,EAAEsK,MAAM,CAACtK,OAAO,GAAG,CAAC,GAAGsK,MAAM,CAACtK,OAAO,CAAC,GAAG;KACjD;EACH;EAEAuW,UAAUA,CAAA;IACR,IAAI,CAACU,SAAS,GAAG,KAAK;IACtB,IAAI,CAACD,SAAS,GAAG;MAAExY,IAAI,EAAE,EAAE;MAAEN,WAAW,EAAE;IAAE,CAAE;IAC9C,IAAI,CAACgC,KAAK,GAAG,EAAE,CAAC,CAAC;EACnB;;EAEAkX,oBAAoBA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACJ,SAAS,CAACxY,IAAI,EAAE;MACxBqD,OAAO,CAAC3B,KAAK,CAAC,uBAAuB,CAAC;MACtC,IAAI,CAACA,KAAK,GAAG,gCAAgC;MAC7C;;IAGF,IAAI,IAAI,CAAC8W,SAAS,CAAC5T,GAAG,EAAE;MACtB,IAAI,CAAC+H,OAAO,GAAG,IAAI;MACnB,IAAI,CAACjL,KAAK,GAAG,EAAE;MAEf,IAAI,CAAC6K,aAAa,CAAC+G,YAAY,CAAC,IAAI,CAACkF,SAAS,CAAC5T,GAAG,EAAE,IAAI,CAAC4T,SAAS,CAAC,CAACzU,SAAS,CAAC;QAC5EC,IAAI,EAAG6U,aAAa,IAAI;UACtBxV,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEuV,aAAa,CAAC;UACxD,IAAI,CAACpD,WAAW,EAAE;UAClB,IAAI,CAACgD,SAAS,GAAG,KAAK;UACtB,IAAI,CAACD,SAAS,GAAG;YAAExY,IAAI,EAAE,EAAE;YAAEN,WAAW,EAAE;UAAE,CAAE;UAC9C,IAAI,CAACiN,OAAO,GAAG,KAAK;UAEpB;UACA,MAAMgM,cAAc,GAAG,iCAAiC;UACxD3J,KAAK,CAAC2J,cAAc,CAAC;QACvB,CAAC;QACDjX,KAAK,EAAGA,KAAK,IAAI;UACf2B,OAAO,CAAC3B,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,IAAI,CAACA,KAAK,GAAG,8CAA8C,IAAIA,KAAK,CAACA,KAAK,EAAE0C,OAAO,IAAI1C,KAAK,CAAC0C,OAAO,IAAI,eAAe,CAAC;UACxH,IAAI,CAACuI,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;KACH,MAAM;MACL,IAAI,CAACjL,KAAK,GAAG,8CAA8C;;EAE/D;EAEA+J,YAAYA,CAAC7H,EAAU;IACrB,IAAI,CAACA,EAAE,EAAE;MACPP,OAAO,CAAC3B,KAAK,CAAC,iBAAiB,CAAC;MAChC,IAAI,CAACA,KAAK,GAAG,4BAA4B;MACzC;;IAGF,IAAI4N,OAAO,CAAC,iFAAiF,CAAC,EAAE;MAC9F,IAAI,CAAC3C,OAAO,GAAG,IAAI;MACnB,IAAI,CAACjL,KAAK,GAAG,EAAE;MAEf,IAAI,CAAC6K,aAAa,CAACd,YAAY,CAAC7H,EAAE,CAAC,CAACG,SAAS,CAAC;QAC5CC,IAAI,EAAGW,QAAQ,IAAI;UACjBtB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEqB,QAAQ,CAAC;UAEnD;UACA,IAAI,IAAI,CAAC8T,SAAS,IAAI,IAAI,CAACD,SAAS,CAAC5T,GAAG,KAAKhB,EAAE,EAAE;YAC/C,IAAI,CAAC6U,SAAS,GAAG,KAAK;YACtB,IAAI,CAACD,SAAS,GAAG;cAAExY,IAAI,EAAE,EAAE;cAAEN,WAAW,EAAE;YAAE,CAAE;;UAGhD,IAAI,CAAC+V,WAAW,EAAE;UAClB,IAAI,CAAC9I,OAAO,GAAG,KAAK;UAEpB;UACAqC,KAAK,CAAC,8BAA8B,CAAC;QACvC,CAAC;QACDtN,KAAK,EAAGA,KAAK,IAAI;UACf2B,OAAO,CAAC3B,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,IAAI,CAACA,KAAK,GAAG,8CAA8C,IAAIA,KAAK,CAACA,KAAK,EAAE0C,OAAO,IAAI1C,KAAK,CAAC0C,OAAO,IAAI,eAAe,CAAC;UACxH,IAAI,CAACuI,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;;EAEN;EAEA+K,eAAeA,CAAC5L,MAAc;IAC5B,IAAI,CAACoM,cAAc,GAAGpM,MAAM;IAC5B;IACA,MAAMgN,QAAQ,GAAG7G,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC;IACvD,IAAI4G,QAAQ,EAAE;MACZ,IAAI;QACF;QACA,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,SAAS,EAAE;UACrD,MAAMC,KAAK,GAAG,IAAIF,MAAM,CAACC,SAAS,CAACE,KAAK,CAACJ,QAAQ,CAAC;UAClDG,KAAK,CAACE,IAAI,EAAE;SACb,MAAM;UACL9V,OAAO,CAAC3B,KAAK,CAAC,kCAAkC,CAAC;UACjDsN,KAAK,CAAC,kDAAkD,CAAC;;OAE5D,CAAC,OAAOtN,KAAK,EAAE;QACd2B,OAAO,CAAC3B,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;;KAE/C,MAAM;MACL2B,OAAO,CAAC3B,KAAK,CAAC,yBAAyB,CAAC;;EAE5C;EAEA0N,iBAAiBA,CAACxJ,MAA0B,EAAEiI,QAAgB;IAC5D,IAAI,CAACjI,MAAM,EAAE;MACXvC,OAAO,CAAC3B,KAAK,CAAC,sBAAsB,CAAC;MACrCsN,KAAK,CAAC,4BAA4B,CAAC;MACnC;;IAGF,IAAI,CAACnB,QAAQ,IAAIA,QAAQ,CAACzL,IAAI,EAAE,KAAK,EAAE,EAAE;MACvCiB,OAAO,CAAC3B,KAAK,CAAC,oBAAoB,CAAC;MACnCsN,KAAK,CAAC,4BAA4B,CAAC;MACnC;;IAGF,IAAI,CAACrC,OAAO,GAAG,IAAI;IAEnB;IACA,MAAMsC,MAAM,GAAW;MAAErL,EAAE,EAAEiK;IAAQ,CAAE;IAEvC,IAAI,CAACtB,aAAa,CAAC6C,iBAAiB,CAACxJ,MAAM,EAAEqJ,MAAM,CAAC,CAAClL,SAAS,CAAC;MAC7DC,IAAI,EAAGW,QAAQ,IAAI;QACjBtB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEqB,QAAQ,CAAC;QACnD,IAAI,CAAC8Q,WAAW,EAAE;QAClB,IAAI,CAAC9I,OAAO,GAAG,KAAK;QAEpB;QACAqC,KAAK,CAAC,uCAAuC,CAAC;MAChD,CAAC;MACDtN,KAAK,EAAGA,KAAK,IAAI;QACf2B,OAAO,CAAC3B,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAACA,KAAK,GAAG,qCAAqC,IAAIA,KAAK,CAACA,KAAK,EAAE0C,OAAO,IAAI1C,KAAK,CAAC0C,OAAO,IAAI,eAAe,CAAC;QAC/G4K,KAAK,CAAC,IAAI,CAACtN,KAAK,CAAC;QACjB,IAAI,CAACiL,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAhE,sBAAsBA,CAAC/C,MAA0B,EAAEiI,QAAgB;IACjE,IAAI,CAACjI,MAAM,EAAE;MACXvC,OAAO,CAAC3B,KAAK,CAAC,sBAAsB,CAAC;MACrCsN,KAAK,CAAC,4BAA4B,CAAC;MACnC;;IAGF,IAAI,CAACnB,QAAQ,EAAE;MACbxK,OAAO,CAAC3B,KAAK,CAAC,wBAAwB,CAAC;MACvCsN,KAAK,CAAC,yBAAyB,CAAC;MAChC;;IAGF,IAAIM,OAAO,CAAC,0DAA0D,CAAC,EAAE;MACvE,IAAI,CAAC3C,OAAO,GAAG,IAAI;MAEnB,IAAI,CAACJ,aAAa,CAAC5D,sBAAsB,CAAC/C,MAAM,EAAEiI,QAAQ,CAAC,CAAC9J,SAAS,CAAC;QACpEC,IAAI,EAAGW,QAAQ,IAAI;UACjBtB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEqB,QAAQ,CAAC;UACrD,IAAI,CAAC8Q,WAAW,EAAE;UAClB,IAAI,CAAC9I,OAAO,GAAG,KAAK;UAEpB;UACA,IAAI,IAAI,CAACuL,cAAc,IAAI,IAAI,CAACA,cAAc,CAACtT,GAAG,KAAKgB,MAAM,EAAE;YAC7D,MAAMiT,aAAa,GAAG,IAAI,CAAChG,OAAO,CAAC/E,IAAI,CAACqF,CAAC,IAAIA,CAAC,CAACvO,GAAG,KAAKgB,MAAM,CAAC;YAC9D,IAAIiT,aAAa,EAAE;cACjB,IAAI,CAACX,cAAc,GAAGW,aAAa;;;QAGzC,CAAC;QACDnX,KAAK,EAAGA,KAAK,IAAI;UACf2B,OAAO,CAAC3B,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;UAC9C,IAAI,CAACA,KAAK,GAAG,2CAA2C,IAAIA,KAAK,CAACA,KAAK,EAAE0C,OAAO,IAAI1C,KAAK,CAAC0C,OAAO,IAAI,eAAe,CAAC;UACrH4K,KAAK,CAAC,IAAI,CAACtN,KAAK,CAAC;UACjB,IAAI,CAACiL,OAAO,GAAG,KAAK;QACtB;OACD,CAAC;;EAEN;;;uBA3QW4L,eAAe,EAAAlb,+DAAA,CAAAyJ,0EAAA,GAAAzJ,+DAAA,CAAA2J,0EAAA;IAAA;EAAA;;;YAAfuR,eAAe;MAAAnR,SAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA2R,yBAAAzR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCnB5BtK,4DAAA,aAA4B;UAE1BA,wDAAA,IAAAgc,8BAAA,iBAYM;UAGNhc,wDAAA,IAAAic,8BAAA,iBAIM;UAGNjc,4DAAA,aAAsB;UAGZA,oDAAA,6BAAiB;UAAAA,0DAAA,EAAK;UAC1BA,4DAAA,gBAAuE;UAAxBA,wDAAA,mBAAAkc,iDAAA;YAAA,OAAS3R,GAAA,CAAA6N,WAAA,EAAa;UAAA,EAAC;UACpEpY,uDAAA,WAAqC;UAACA,oDAAA,yBACxC;UAAAA,0DAAA,EAAS;UAGXA,wDAAA,KAAAmc,+BAAA,iBAEM;UAENnc,wDAAA,KAAAoc,+BAAA,kBAwCM;UACRpc,0DAAA,EAAM;UAIRA,4DAAA,cAAsB;UAKZA,oDAAA,IACF;UAAAA,0DAAA,EAAK;UAEPA,4DAAA,eAAuB;UAIdA,oDAAA,6BAAgB;UAAAA,4DAAA,gBAA0B;UAAAA,oDAAA,SAAC;UAAAA,0DAAA,EAAO;UAErDA,4DAAA,qBAYE;UANAA,wDAAA,mBAAAqc,iDAAA;YAAArc,2DAAA,CAAAsc,IAAA;YAAA,MAAAC,GAAA,GAAAvc,yDAAA;YAAA,OAASA,yDAAA,CAAAuK,GAAA,CAAA4Q,SAAA,CAAAxY,IAAA,GAAA4Z,GAAA,CAAA/P,KAAA,CAAgC;UAAA,EAAC;UAN5CxM,0DAAA,EAYE;UACFA,4DAAA,eAA8B;UAAAA,oDAAA,0CAA6B;UAAAA,0DAAA,EAAM;UAEnEA,4DAAA,eAAkB;UAC4BA,oDAAA,mBAAW;UAAAA,0DAAA,EAAQ;UAC/DA,4DAAA,wBAQC;UAHCA,wDAAA,mBAAAwc,oDAAA;YAAAxc,2DAAA,CAAAsc,IAAA;YAAA,MAAAlJ,GAAA,GAAApT,yDAAA;YAAA,OAASA,yDAAA,CAAAuK,GAAA,CAAA4Q,SAAA,CAAA9Y,WAAA,GAAA+Q,GAAA,CAAA5G,KAAA,CAAuC;UAAA,EAAC;UAGlDxM,0DAAA,EAAW;UACZA,4DAAA,iBACG;UAAAA,oDAAA,sEAAoD;UAAAA,0DAAA,EACtD;UAEHA,4DAAA,eAAoB;UAKhBA,wDAAA,mBAAAyc,kDAAA;YAAA,OAAAlS,GAAA,CAAA6Q,SAAA,GAAqB7Q,GAAA,CAAAgR,oBAAA,EAAsB,GAAGhR,GAAA,CAAA2L,SAAA,EAAW;UAAA,EAAC;UAE1DlW,wDAAA,KAAA0c,gCAAA,mBAKQ;UACR1c,oDAAA,IACF;UAAAA,0DAAA,EAAS;UACTA,wDAAA,KAAA2c,kCAAA,qBAOS;UACX3c,0DAAA,EAAM;UAQhBA,4DAAA,eAA0E;UAI1CA,oDAAA,+CAA6B;UAAAA,0DAAA,EAAK;UAC1DA,uDAAA,kBAKU;UACZA,0DAAA,EAAM;UACNA,4DAAA,eAAwB;UACtBA,wDAAA,KAAA4c,+BAAA,mBAuEM;UACR5c,0DAAA,EAAM;UACNA,4DAAA,eAA0B;UAMtBA,oDAAA,gBACF;UAAAA,0DAAA,EAAS;;;UAvPdA,uDAAA,GAAW;UAAXA,wDAAA,SAAAuK,GAAA,CAAAlG,KAAA,CAAW;UAcRrE,uDAAA,GAAa;UAAbA,wDAAA,SAAAuK,GAAA,CAAA+E,OAAA,CAAa;UAgBTtP,uDAAA,GAAsC;UAAtCA,wDAAA,SAAAuK,GAAA,CAAAiL,OAAA,CAAA3S,MAAA,WAAA0H,GAAA,CAAA+E,OAAA,CAAsC;UAItCtP,uDAAA,GAAwB;UAAxBA,wDAAA,SAAAuK,GAAA,CAAAiL,OAAA,CAAA3S,MAAA,KAAwB;UAkDxB7C,uDAAA,GACF;UADEA,gEAAA,MAAAuK,GAAA,CAAA6Q,SAAA,kEACF;UAgBMpb,uDAAA,GAEC;UAFDA,yDAAA,gBAAAuK,GAAA,CAAA4Q,SAAA,CAAAxY,IAAA,KAAA4H,GAAA,CAAA6Q,SAAA,IAAA7Q,GAAA,CAAA4Q,SAAA,CAAAxY,IAAA,SAEC;UALD3C,wDAAA,UAAAuK,GAAA,CAAA4Q,SAAA,CAAAxY,IAAA,CAAwB;UAgBxB3C,uDAAA,GAAqC;UAArCA,wDAAA,UAAAuK,GAAA,CAAA4Q,SAAA,CAAA9Y,WAAA,OAAqC;UAarCrC,uDAAA,GAAuC;UAAvCA,wDAAA,cAAAuK,GAAA,CAAA4Q,SAAA,CAAAxY,IAAA,IAAA4H,GAAA,CAAA+E,OAAA,CAAuC;UAIpCtP,uDAAA,GAAa;UAAbA,wDAAA,SAAAuK,GAAA,CAAA+E,OAAA,CAAa;UAKhBtP,uDAAA,GACF;UADEA,gEAAA,MAAAuK,GAAA,CAAA6Q,SAAA,4CACF;UAEGpb,uDAAA,GAAe;UAAfA,wDAAA,SAAAuK,GAAA,CAAA6Q,SAAA,CAAe;UA4BhBpb,uDAAA,GAAoB;UAApBA,wDAAA,SAAAuK,GAAA,CAAAsQ,cAAA,CAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzKmB;AACmB;AACA;AACM;AACZ;AAEY;;;AAEhF,MAAMmC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEhG,yFAAqB;EAChCiG,QAAQ,EAAE;EACR;EACA;IAAEF,IAAI,EAAE,EAAE;IAAEC,SAAS,EAAEhE,mFAAmBA;EAAA,CAAE,EAC5C;IAAE+D,IAAI,EAAE,OAAO;IAAEC,SAAS,EAAEhE,mFAAmBA;EAAA,CAAE,EACjD;IAAE+D,IAAI,EAAE,aAAa;IAAEC,SAAS,EAAEhE,mFAAmBA;EAAA,CAAE;EAEvD;EACA;IAAE+D,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAEjJ,mFAAmBA;EAAA,CAAE,EACnD;IAAEgJ,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAEjJ,mFAAmBA;EAAA,CAAE;EAEnD;EACA;IAAEgJ,IAAI,EAAE,cAAc;IAAEC,SAAS,EAAEjJ,mFAAmBA;EAAA,CAAE;EAExD;EACA;IAAEgJ,IAAI,EAAE,YAAY;IAAEC,SAAS,EAAEjO,yFAAqBA;EAAA,CAAE;EAExD;EACA;IAAEgO,IAAI,EAAE,WAAW;IAAEC,SAAS,EAAEH,6EAAiBA;EAAA,CAAE;CAEtD,CACF;AAMK,MAAOK,oBAAoB;;;uBAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;gBAHrBN,yDAAY,CAACO,QAAQ,CAACL,MAAM,CAAC,EAC7BF,yDAAY;IAAA;EAAA;;;sHAEXM,oBAAoB;IAAAE,OAAA,GAAA7T,yDAAA;IAAA8T,OAAA,GAFrBT,yDAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpCuB;AAEiB;AACU;AACA;AACM;AACZ;AACN;AACF;AACkB;AACtB;AACX;AACW;AACwB;;AAsB1E,MAAOe,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA;IAAa;EAAA;;;gBARtBL,0DAAY,EACZJ,yEAAoB,EACpBO,wDAAW,EACXC,mEAAc,EACdF,mEAAgB;IAAA;EAAA;;;sHAIPG,aAAa;IAAAC,YAAA,GAlBtB5E,mFAAmB,EACnBjF,mFAAmB,EACnBhF,yFAAqB,EACrB8N,6EAAiB,EACjB/X,uEAAe,EACfkW,qEAAe,EACfuC,uFAAqB,EACrBvG,yFAAqB;IAAAoG,OAAA,GAGrBE,0DAAY,EACZJ,yEAAoB,EACpBO,wDAAW,EACXC,mEAAc,EACdF,mEAAgB;EAAA;AAAA;;;;;;;;;;;;;;;;;;AC/BgB;;;;;;;;;;;;;;;IAMhC1d,4DAAA,aAE8C;IAE1CA,uDAAA,WAMU;IACVA,4DAAA,WAAM;IAAAA,oDAAA,GAA0B;IAAAA,0DAAA,EAAO;IAEzCA,4DAAA,gBAAsE;IAA9BA,wDAAA,mBAAAge,6DAAA;MAAAhe,2DAAA,CAAAie,GAAA;MAAA,MAAA/c,MAAA,GAAAlB,2DAAA;MAAA,OAASA,yDAAA,CAAAkB,MAAA,CAAAgd,iBAAA,EAAmB;IAAA,EAAC;IAACle,0DAAA,EAAS;;;;IAX5EA,wDAAA,uBAAAwO,MAAA,CAAA2P,YAAA,CAAAC,IAAA,CAAwC;IAGtCpe,uDAAA,GAKE;IALFA,wDAAA,YAAAA,6DAAA,IAAAO,GAAA,EAAAiO,MAAA,CAAA2P,YAAA,CAAAC,IAAA,gBAAA5P,MAAA,CAAA2P,YAAA,CAAAC,IAAA,gBAAA5P,MAAA,CAAA2P,YAAA,CAAAC,IAAA,aAAA5P,MAAA,CAAA2P,YAAA,CAAAC,IAAA,cAKE;IACCpe,uDAAA,GAA0B;IAA1BA,+DAAA,CAAAwO,MAAA,CAAA2P,YAAA,CAAApX,OAAA,CAA0B;;;AA8ClC,MAAO0W,qBAAqB;EAIhCxY,YAAoBG,mBAAwC;IAAxC,KAAAA,mBAAmB,GAAnBA,mBAAmB;IAHvC,KAAA+Y,YAAY,GAAwB,IAAI;IAChC,KAAAG,YAAY,GAAiB,IAAIP,8CAAY,EAAE;EAEQ;EAE/DvY,QAAQA,CAAA;IACN,IAAI,CAAC8Y,YAAY,GAAG,IAAI,CAAClZ,mBAAmB,CAACmZ,gBAAgB,EAAE,CAAC7X,SAAS,CAACyX,YAAY,IAAG;MACvF,IAAI,CAACA,YAAY,GAAGA,YAAY;IAClC,CAAC,CAAC;EACJ;EAEAK,WAAWA,CAAA;IACT,IAAI,CAACF,YAAY,CAACG,WAAW,EAAE;EACjC;EAEAP,iBAAiBA,CAAA;IACf,IAAI,CAAC9Y,mBAAmB,CAACsZ,KAAK,EAAE;EAClC;;;uBAlBWjB,qBAAqB,EAAAzd,+DAAA,CAAAyJ,sFAAA;IAAA;EAAA;;;YAArBgU,qBAAqB;MAAA1T,SAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAuU,+BAAArU,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAzD9BtK,wDAAA,IAAA4e,oCAAA,iBAcM;;;UAdA5e,wDAAA,SAAAuK,GAAA,CAAA4T,YAAA,CAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACFI;AASU;;;;;;;;;;;;ICL9Bne,4DAAA,aAAwD;IACtDA,oDAAA,GACF;IAAAA,0DAAA,EAAK;;;;IADHA,uDAAA,GACF;IADEA,gEAAA,mBAAAwO,MAAA,CAAAtK,IAAA,CAAAvB,IAAA,MACF;;;;;IAuBR3C,4DAAA,cAA6D;IAGzBA,oDAAA,oBAAa;IAAAA,0DAAA,EAAO;IAEpDA,4DAAA,cAA4D;IAC5BA,oDAAA,oBAAa;IAAAA,0DAAA,EAAO;IAEpDA,4DAAA,cAA0D;IAC1BA,oDAAA,qBAAa;IAAAA,0DAAA,EAAO;IAEpDA,4DAAA,aAA2B;IAAAA,oDAAA,qCAAwB;IAAAA,0DAAA,EAAI;;;;;;IAK3DA,4DAAA,cAA2D;IAKrDA,uDAAA,YAAyD;IACzDA,4DAAA,cAAyB;IACvBA,oDAAA,GACF;IAAAA,0DAAA,EAAM;IACNA,4DAAA,iBAGC;IADCA,wDAAA,mBAAA+e,0DAAA;MAAA/e,2DAAA,CAAAgf,GAAA;MAAA,MAAAtS,MAAA,GAAA1M,2DAAA;MAAA,OAASA,yDAAA,CAAA0M,MAAA,CAAAnE,MAAA,IAAUmE,MAAA,CAAAuS,SAAA,CAAAvS,MAAA,CAAAnE,MAAA,CAAiB;IAAA,EAAC;IAErCvI,uDAAA,YAA0C;IAACA,oDAAA,uBAC7C;IAAAA,0DAAA,EAAS;;;;IAPPA,uDAAA,GACF;IADEA,gEAAA,MAAA+M,MAAA,CAAA1I,KAAA,MACF;;;;;IAoGUrE,4DAAA,iBAGC;IACCA,oDAAA,GACF;IAAAA,0DAAA,EAAS;;;;;IAHPA,wDAAA,UAAAkf,QAAA,CAAA3X,GAAA,IAAA2X,QAAA,CAAA3Y,EAAA,CAA6B;IAE7BvG,uDAAA,GACF;IADEA,gEAAA,MAAAqL,MAAA,CAAAI,WAAA,CAAAyT,QAAA,CAAA3X,GAAA,IAAA2X,QAAA,CAAA3Y,EAAA,aACF;;;;;;IA7FhBvG,4DAAA,aAA0D;IAKhDA,oDAAA,GACF;IAAAA,0DAAA,EAAK;IAEPA,4DAAA,cAA2B;IACnBA,wDAAA,sBAAAmf,2DAAA;MAAAnf,2DAAA,CAAA4S,IAAA;MAAA,MAAAC,OAAA,GAAA7S,2DAAA;MAAA,OAAYA,yDAAA,CAAA6S,OAAA,CAAAuM,WAAA,GAAcvM,OAAA,CAAAwM,UAAA,EAAY,GAAGxM,OAAA,CAAArK,UAAA,EAAY;IAAA,EAAC;IAC1DxI,4DAAA,cAAqB;IAEyBA,oDAAA,cAAM;IAAAA,0DAAA,EAAQ;IACxDA,4DAAA,iBAaE;IAPAA,wDAAA,2BAAAsf,kEAAA/a,MAAA;MAAAvE,2DAAA,CAAA4S,IAAA;MAAA,MAAAI,OAAA,GAAAhT,2DAAA;MAAA,OAETA,yDAAA,CAAAgT,OAAA,CAAAoM,WAAA,GAAApM,OAAA,CAAAoM,WAAA,CAAAld,KAAA,GAAAqC,MAAA,GAAAyO,OAAA,CAAAuM,OAAA,CAAArd,KAAA,GAAAqC,MAAA,CAGX;IAAA,EADqB;IAVHvE,0DAAA,EAaE;IAEJA,4DAAA,eAAsB;IACyBA,oDAAA,sBAAS;IAAAA,0DAAA,EAAQ;IAC9DA,4DAAA,kBAaC;IANCA,wDAAA,2BAAAwf,mEAAAjb,MAAA;MAAAvE,2DAAA,CAAA4S,IAAA;MAAA,MAAAS,OAAA,GAAArT,2DAAA;MAAA,OAETA,yDAAA,CAAAqT,OAAA,CAAA+L,WAAA,GAAA/L,OAAA,CAAA+L,WAAA,CAAAnd,QAAA,GAAAsC,MAAA,GAAA8O,OAAA,CAAAkM,OAAA,CAAAtd,QAAA,GAAAsC,MAAA,CAGX;IAAA,EADqB;IAGDvE,4DAAA,kBAAoB;IAAAA,oDAAA,aAAK;IAAAA,0DAAA,EAAS;IAClCA,4DAAA,kBAAuB;IAAAA,oDAAA,eAAO;IAAAA,0DAAA,EAAS;IACvCA,4DAAA,kBAAqB;IAAAA,oDAAA,aAAK;IAAAA,0DAAA,EAAS;IAGvCA,4DAAA,eAAsB;IACuBA,oDAAA,eAAO;IAAAA,0DAAA,EAAQ;IAC1DA,4DAAA,kBAaC;IANCA,wDAAA,2BAAAyf,mEAAAlb,MAAA;MAAAvE,2DAAA,CAAA4S,IAAA;MAAA,MAAAY,OAAA,GAAAxT,2DAAA;MAAA,OAETA,yDAAA,CAAAwT,OAAA,CAAA4L,WAAA,GAAA5L,OAAA,CAAA4L,WAAA,CAAA9W,MAAA,GAAA/D,MAAA,GAAAiP,OAAA,CAAA+L,OAAA,CAAAjX,MAAA,GAAA/D,MAAA,CAGX;IAAA,EADqB;IAGDvE,4DAAA,kBAAqB;IAAAA,oDAAA,oBAAO;IAAAA,0DAAA,EAAS;IACrCA,4DAAA,kBAA4B;IAAAA,oDAAA,gBAAQ;IAAAA,0DAAA,EAAS;IAC7CA,4DAAA,kBAAqB;IAAAA,oDAAA,qBAAQ;IAAAA,0DAAA,EAAS;IAG1CA,4DAAA,eAAsB;IAEjBA,oDAAA,4BAAU;IAAAA,0DAAA,EACZ;IACDA,4DAAA,kBAYC;IANCA,wDAAA,2BAAA0f,mEAAAnb,MAAA;MAAAvE,2DAAA,CAAA4S,IAAA;MAAA,MAAAtG,OAAA,GAAAtM,2DAAA;MAAA,OAETA,yDAAA,CAAAsM,OAAA,CAAA8S,WAAA,GAAA9S,OAAA,CAAA8S,WAAA,CAAAvd,UAAA,GAAA0C,MAAA,GAAA+H,OAAA,CAAAiT,OAAA,CAAA1d,UAAA,GAAA0C,MAAA,CAGX;IAAA,EADqB;IAGDvE,4DAAA,kBAAuB;IAAAA,oDAAA,yBAAY;IAAAA,0DAAA,EAAS;IAC5CA,wDAAA,KAAA2f,2CAAA,qBAKS;IACX3f,0DAAA,EAAS;IAEXA,4DAAA,eAAsB;IAEjBA,oDAAA,iCAAe;IAAAA,0DAAA,EACjB;IACDA,4DAAA,iBAaE;IANAA,wDAAA,2BAAA4f,kEAAArb,MAAA;MAAAvE,2DAAA,CAAA4S,IAAA;MAAA,MAAA6H,OAAA,GAAAza,2DAAA;MAAA,OAETA,yDAAA,CAAAya,OAAA,CAAA2E,WAAA,GAAA3E,OAAA,CAAA2E,WAAA,CAAAS,OAAA,GAAAtb,MAAA,GAAAkW,OAAA,CAAA8E,OAAA,CAAAM,OAAA,GAAAtb,MAAA,CAGX;IAAA,EADqB;IAXHvE,0DAAA,EAaE;IAEJA,4DAAA,cAAoB;IAEfA,oDAAA,mBAAW;IAAAA,0DAAA,EACb;IACDA,4DAAA,oBAgBC;IAPCA,wDAAA,2BAAA8f,qEAAAvb,MAAA;MAAAvE,2DAAA,CAAA4S,IAAA;MAAA,MAAAmN,OAAA,GAAA/f,2DAAA;MAAA,OAETA,yDAAA,CAAA+f,OAAA,CAAAX,WAAA,GAAAW,OAAA,CAAAX,WAAA,CAAA/c,WAAA,GAAAkC,MAAA,GAAAwb,OAAA,CAAAR,OAAA,CAAAld,WAAA,GAAAkC,MAAA,CAGX;IAAA,EADqB;IAGFvE,0DAAA,EAAW;IAEdA,4DAAA,eAA0D;IAItDA,wDAAA,mBAAAggB,2DAAA;MAAAhgB,2DAAA,CAAA4S,IAAA;MAAA,MAAAvF,OAAA,GAAArN,2DAAA;MAAA,OAASA,yDAAA,CAAAqN,OAAA,CAAA+R,WAAA,GAAc/R,OAAA,CAAAqN,UAAA,EAAY,GAAGrN,OAAA,CAAA4S,cAAA,EAAgB;IAAA,EAAC;IAEvDjgB,oDAAA,iBACF;IAAAA,0DAAA,EAAS;IACTA,4DAAA,kBAGC;IACCA,oDAAA,IACF;IAAAA,0DAAA,EAAS;;;;IAjJbA,uDAAA,GACF;IADEA,gEAAA,MAAA6D,MAAA,CAAAub,WAAA,yDACF;IAYQpf,uDAAA,GAA2D;IAA3DA,wDAAA,YAAA6D,MAAA,CAAAub,WAAA,GAAAvb,MAAA,CAAAub,WAAA,CAAAld,KAAA,GAAA2B,MAAA,CAAA0b,OAAA,CAAArd,KAAA,CAA2D;IAgB3DlC,uDAAA,GAEC;IAFDA,wDAAA,YAAA6D,MAAA,CAAAub,WAAA,GAAAvb,MAAA,CAAAub,WAAA,CAAAnd,QAAA,GAAA4B,MAAA,CAAA0b,OAAA,CAAAtd,QAAA,CAEC;IAmBDjC,uDAAA,IAEC;IAFDA,wDAAA,YAAA6D,MAAA,CAAAub,WAAA,GAAAvb,MAAA,CAAAub,WAAA,CAAA9W,MAAA,GAAAzE,MAAA,CAAA0b,OAAA,CAAAjX,MAAA,CAEC;IAoBDtI,uDAAA,IAEC;IAFDA,wDAAA,YAAA6D,MAAA,CAAAub,WAAA,GAAAvb,MAAA,CAAAub,WAAA,CAAAvd,UAAA,GAAAgC,MAAA,CAAA0b,OAAA,CAAA1d,UAAA,CAEC;IAQO7B,uDAAA,GAAc;IAAdA,wDAAA,eAAc;IAEHA,uDAAA,GAAQ;IAARA,wDAAA,YAAA6D,MAAA,CAAAyR,KAAA,CAAQ;IAe3BtV,uDAAA,GAEC;IAFDA,wDAAA,YAAA6D,MAAA,CAAAub,WAAA,GAAAvb,MAAA,CAAAub,WAAA,CAAAS,OAAA,GAAAhc,MAAA,CAAA0b,OAAA,CAAAM,OAAA,CAEC;IAiBD7f,uDAAA,GAIC;IAJDA,wDAAA,YAAA6D,MAAA,CAAAub,WAAA,GAAAvb,MAAA,CAAAub,WAAA,CAAA/c,WAAA,GAAAwB,MAAA,CAAA0b,OAAA,CAAAld,WAAA,CAIC;IAsBDrC,uDAAA,GACF;IADEA,gEAAA,MAAA6D,MAAA,CAAAub,WAAA,4CACF;;;;;;IAUdpf,4DAAA,cAA+C;IAQ/BA,uDAAA,YAA4B;IAC9BA,0DAAA,EAAO;IACPA,4DAAA,gBAKE;IADAA,wDAAA,2BAAAkgB,iEAAA3b,MAAA;MAAAvE,2DAAA,CAAAmgB,IAAA;MAAA,MAAAC,OAAA,GAAApgB,2DAAA;MAAA,OAAAA,yDAAA,CAAAogB,OAAA,CAAAC,UAAA,GAAA9b,MAAA;IAAA,EAAwB;IAJ1BvE,0DAAA,EAKE;IAGNA,4DAAA,eAAsB;IACQA,wDAAA,2BAAAsgB,mEAAA/b,MAAA;MAAAvE,2DAAA,CAAAmgB,IAAA;MAAA,MAAAI,OAAA,GAAAvgB,2DAAA;MAAA,OAAAA,yDAAA,CAAAugB,OAAA,CAAAC,YAAA,GAAAjc,MAAA;IAAA,EAA0B;IACpDvE,4DAAA,kBAAoB;IAAAA,oDAAA,wBAAgB;IAAAA,0DAAA,EAAS;IAC7CA,4DAAA,kBAAqB;IAAAA,oDAAA,oBAAO;IAAAA,0DAAA,EAAS;IACrCA,4DAAA,kBAA4B;IAAAA,oDAAA,gBAAQ;IAAAA,0DAAA,EAAS;IAC7CA,4DAAA,kBAAqB;IAAAA,oDAAA,sBAAS;IAAAA,0DAAA,EAAS;IAG3CA,4DAAA,eAAsB;IACQA,wDAAA,2BAAAygB,mEAAAlc,MAAA;MAAAvE,2DAAA,CAAAmgB,IAAA;MAAA,MAAA1b,OAAA,GAAAzE,2DAAA;MAAA,OAAAA,yDAAA,CAAAyE,OAAA,CAAAic,cAAA,GAAAnc,MAAA;IAAA,EAA4B;IACtDvE,4DAAA,kBAAoB;IAAAA,oDAAA,iCAAoB;IAAAA,0DAAA,EAAS;IACjDA,4DAAA,kBAAqB;IAAAA,oDAAA,aAAK;IAAAA,0DAAA,EAAS;IACnCA,4DAAA,kBAAuB;IAAAA,oDAAA,eAAO;IAAAA,0DAAA,EAAS;IACvCA,4DAAA,kBAAoB;IAAAA,oDAAA,aAAK;IAAAA,0DAAA,EAAS;;;;IAjBhCA,uDAAA,GAAwB;IAAxBA,wDAAA,YAAAoE,MAAA,CAAAic,UAAA,CAAwB;IAKArgB,uDAAA,GAA0B;IAA1BA,wDAAA,YAAAoE,MAAA,CAAAoc,YAAA,CAA0B;IAQ1BxgB,uDAAA,IAA4B;IAA5BA,wDAAA,YAAAoE,MAAA,CAAAsc,cAAA,CAA4B;;;;;;IAcpE1gB,4DAAA,cAGC;IAGKA,uDAAA,YAAqD;IACrDA,4DAAA,aAAiB;IAAAA,oDAAA,qCAAoB;IAAAA,0DAAA,EAAK;IAC1CA,4DAAA,YAA2B;IACzBA,oDAAA,iFACF;IAAAA,0DAAA,EAAI;IACJA,4DAAA,iBAGC;IADCA,wDAAA,mBAAA2gB,0DAAA;MAAA3gB,2DAAA,CAAA4gB,IAAA;MAAA,MAAAC,OAAA,GAAA7gB,2DAAA;MAAA,OAASA,yDAAA,CAAA6gB,OAAA,CAAAZ,cAAA,EAAgB;IAAA,EAAC;IAE1BjgB,uDAAA,YAAsC;IAACA,oDAAA,mCACzC;IAAAA,0DAAA,EAAS;;;;;IAuGDA,4DAAA,iBAAkD;IAChDA,oDAAA,GACF;IAAAA,0DAAA,EAAQ;;;;;IADNA,uDAAA,GACF;IADEA,gEAAA,MAAA8gB,OAAA,CAAArV,WAAA,CAAAsV,QAAA,CAAAlf,UAAA,OACF;;;;;;;;;;;;;IA5EJ7B,4DAAA,cAKC;IAIkCA,oDAAA,GAAgB;IAAAA,0DAAA,EAAK;IACpDA,4DAAA,cAAsB;IAMlBA,uDAAA,YAAyC;IAC3CA,0DAAA,EAAS;IACTA,4DAAA,cAA4C;IAEVA,wDAAA,mBAAAghB,iEAAA;MAAA,MAAA/V,WAAA,GAAAjL,2DAAA,CAAAihB,IAAA;MAAA,MAAAF,QAAA,GAAA9V,WAAA,CAAAG,SAAA;MAAA,MAAA8V,OAAA,GAAAlhB,2DAAA;MAAA,OAASA,yDAAA,CAAAkhB,OAAA,CAAAC,QAAA,CAAAJ,QAAA,CAAc;IAAA,EAAC;IACpD/gB,uDAAA,cAAiC;IAACA,oDAAA,kBACpC;IAAAA,0DAAA,EAAS;IAEXA,4DAAA,UAAI;IAGAA,wDAAA,mBAAAohB,kEAAA;MAAA,MAAAnW,WAAA,GAAAjL,2DAAA,CAAAihB,IAAA;MAAA,MAAAF,QAAA,GAAA9V,WAAA,CAAAG,SAAA;MAAA,MAAAiW,OAAA,GAAArhB,2DAAA;MAAA,OAASA,yDAAA,CAAAqhB,OAAA,CAAAC,gBAAA,CAAAP,QAAA,EAAuB,aAAa,CAAC;IAAA,EAAC;IAE/C/gB,uDAAA,cAAsC;IAACA,oDAAA,yCAEzC;IAAAA,0DAAA,EAAS;IAEXA,4DAAA,UAAI;IAGAA,wDAAA,mBAAAuhB,kEAAA;MAAA,MAAAtW,WAAA,GAAAjL,2DAAA,CAAAihB,IAAA;MAAA,MAAAF,QAAA,GAAA9V,WAAA,CAAAG,SAAA;MAAA,MAAAoW,OAAA,GAAAxhB,2DAAA;MAAA,OAASA,yDAAA,CAAAwhB,OAAA,CAAAF,gBAAA,CAAAP,QAAA,EAAuB,MAAM,CAAC;IAAA,EAAC;IAExC/gB,uDAAA,aAAqC;IAACA,oDAAA,qCAExC;IAAAA,0DAAA,EAAS;IAEXA,4DAAA,UAAI;IAAAA,uDAAA,eAA+B;IAAAA,0DAAA,EAAK;IACxCA,4DAAA,UAAI;IAGAA,wDAAA,mBAAAyhB,kEAAA;MAAA,MAAAxW,WAAA,GAAAjL,2DAAA,CAAAihB,IAAA;MAAA,MAAAF,QAAA,GAAA9V,WAAA,CAAAG,SAAA;MAAA,MAAAsW,OAAA,GAAA1hB,2DAAA;MAAA,OAASA,yDAAA,CAAA+gB,QAAA,CAAAxZ,GAAA,IAAYma,OAAA,CAAAC,UAAA,CAAAZ,QAAA,CAAAxZ,GAAA,CAAoB;IAAA,EAAC;IAE1CvH,uDAAA,cAAgC;IAACA,oDAAA,mBACnC;IAAAA,0DAAA,EAAS;IAKjBA,4DAAA,cAAkD;IAChDA,oDAAA,IACF;IAAAA,0DAAA,EAAI;IACJA,4DAAA,gBAA+D;IAS3DA,oDAAA,IAOF;IAAAA,0DAAA,EAAO;IACPA,wDAAA,KAAA4hB,iDAAA,qBAEQ;IACV5hB,0DAAA,EAAM;IAENA,4DAAA,gBAA4C;IAC1CA,uDAAA,cAAqC;IACvCA,0DAAA,EAAM;;;;IA9ENA,wDAAA,0BAAA+gB,QAAA,CAAA9e,QAAA,CAAuC;IAMNjC,uDAAA,GAAgB;IAAhBA,+DAAA,CAAA+gB,QAAA,CAAA7e,KAAA,CAAgB;IA8C/ClC,uDAAA,IACF;IADEA,gEAAA,MAAA+gB,QAAA,CAAA1e,WAAA,8BACF;IAIIrC,uDAAA,GAIE;IAJFA,wDAAA,YAAAA,6DAAA,IAAAO,GAAA,EAAAwgB,QAAA,CAAA9e,QAAA,aAAA8e,QAAA,CAAA9e,QAAA,eAAA8e,QAAA,CAAA9e,QAAA,YAIE;IAEFjC,uDAAA,GAOF;IAPEA,gEAAA,MAAA+gB,QAAA,CAAA9e,QAAA,wBAAA8e,QAAA,CAAA9e,QAAA,yCAOF;IAC2BjC,uDAAA,GAAqB;IAArBA,wDAAA,SAAA+gB,QAAA,CAAAlf,UAAA,CAAqB;;;;;IA8GhD7B,4DAAA,iBAAkD;IAChDA,oDAAA,GACF;IAAAA,0DAAA,EAAQ;;;;;IADNA,uDAAA,GACF;IADEA,gEAAA,MAAA6hB,OAAA,CAAApW,WAAA,CAAAqW,QAAA,CAAAjgB,UAAA,OACF;;;;;;IA5EJ7B,4DAAA,cAKC;IAIkCA,oDAAA,GAAgB;IAAAA,0DAAA,EAAK;IACpDA,4DAAA,cAAsB;IAMlBA,uDAAA,YAAyC;IAC3CA,0DAAA,EAAS;IACTA,4DAAA,cAA4C;IAEVA,wDAAA,mBAAA+hB,iEAAA;MAAA,MAAA9W,WAAA,GAAAjL,2DAAA,CAAAgiB,IAAA;MAAA,MAAAF,QAAA,GAAA7W,WAAA,CAAAG,SAAA;MAAA,MAAA6W,OAAA,GAAAjiB,2DAAA;MAAA,OAASA,yDAAA,CAAAiiB,OAAA,CAAAd,QAAA,CAAAW,QAAA,CAAc;IAAA,EAAC;IACpD9hB,uDAAA,cAAiC;IAACA,oDAAA,kBACpC;IAAAA,0DAAA,EAAS;IAEXA,4DAAA,UAAI;IAGAA,wDAAA,mBAAAkiB,kEAAA;MAAA,MAAAjX,WAAA,GAAAjL,2DAAA,CAAAgiB,IAAA;MAAA,MAAAF,QAAA,GAAA7W,WAAA,CAAAG,SAAA;MAAA,MAAA+W,OAAA,GAAAniB,2DAAA;MAAA,OAASA,yDAAA,CAAAmiB,OAAA,CAAAb,gBAAA,CAAAQ,QAAA,EAAuB,MAAM,CAAC;IAAA,EAAC;IAExC9hB,uDAAA,YAAqC;IAACA,oDAAA,6CAExC;IAAAA,0DAAA,EAAS;IAEXA,4DAAA,UAAI;IAGAA,wDAAA,mBAAAoiB,kEAAA;MAAA,MAAAnX,WAAA,GAAAjL,2DAAA,CAAAgiB,IAAA;MAAA,MAAAF,QAAA,GAAA7W,WAAA,CAAAG,SAAA;MAAA,MAAAiX,OAAA,GAAAriB,2DAAA;MAAA,OAASA,yDAAA,CAAAqiB,OAAA,CAAAf,gBAAA,CAAAQ,QAAA,EAAuB,MAAM,CAAC;IAAA,EAAC;IAExC9hB,uDAAA,aAAqC;IAACA,oDAAA,qCAExC;IAAAA,0DAAA,EAAS;IAEXA,4DAAA,UAAI;IAAAA,uDAAA,eAA+B;IAAAA,0DAAA,EAAK;IACxCA,4DAAA,UAAI;IAGAA,wDAAA,mBAAAsiB,kEAAA;MAAA,MAAArX,WAAA,GAAAjL,2DAAA,CAAAgiB,IAAA;MAAA,MAAAF,QAAA,GAAA7W,WAAA,CAAAG,SAAA;MAAA,MAAAmX,OAAA,GAAAviB,2DAAA;MAAA,OAASA,yDAAA,CAAA8hB,QAAA,CAAAva,GAAA,IAAYgb,OAAA,CAAAZ,UAAA,CAAAG,QAAA,CAAAva,GAAA,CAAoB;IAAA,EAAC;IAE1CvH,uDAAA,cAAgC;IAACA,oDAAA,mBACnC;IAAAA,0DAAA,EAAS;IAKjBA,4DAAA,cAAkD;IAChDA,oDAAA,IACF;IAAAA,0DAAA,EAAI;IACJA,4DAAA,gBAA+D;IAS3DA,oDAAA,IAOF;IAAAA,0DAAA,EAAO;IACPA,wDAAA,KAAAwiB,iDAAA,qBAEQ;IACVxiB,0DAAA,EAAM;IAENA,4DAAA,gBAA4C;IAC1CA,uDAAA,cAAqC;IACvCA,0DAAA,EAAM;;;;IA9ENA,wDAAA,0BAAA8hB,QAAA,CAAA7f,QAAA,CAAuC;IAMNjC,uDAAA,GAAgB;IAAhBA,+DAAA,CAAA8hB,QAAA,CAAA5f,KAAA,CAAgB;IA8C/ClC,uDAAA,IACF;IADEA,gEAAA,MAAA8hB,QAAA,CAAAzf,WAAA,8BACF;IAIIrC,uDAAA,GAIE;IAJFA,wDAAA,YAAAA,6DAAA,IAAAO,GAAA,EAAAuhB,QAAA,CAAA7f,QAAA,aAAA6f,QAAA,CAAA7f,QAAA,eAAA6f,QAAA,CAAA7f,QAAA,YAIE;IAEFjC,uDAAA,GAOF;IAPEA,gEAAA,MAAA8hB,QAAA,CAAA7f,QAAA,wBAAA6f,QAAA,CAAA7f,QAAA,yCAOF;IAC2BjC,uDAAA,GAAqB;IAArBA,wDAAA,SAAA8hB,QAAA,CAAAjgB,UAAA,CAAqB;;;;;IA8GhD7B,4DAAA,iBAAkD;IAChDA,oDAAA,GACF;IAAAA,0DAAA,EAAQ;;;;;IADNA,uDAAA,GACF;IADEA,gEAAA,MAAAyiB,OAAA,CAAAhX,WAAA,CAAAiX,QAAA,CAAA7gB,UAAA,OACF;;;;;;IA5EJ7B,4DAAA,eAKC;IAIkCA,oDAAA,GAAgB;IAAAA,0DAAA,EAAK;IACpDA,4DAAA,cAAsB;IAMlBA,uDAAA,YAAyC;IAC3CA,0DAAA,EAAS;IACTA,4DAAA,cAA4C;IAEVA,wDAAA,mBAAA2iB,iEAAA;MAAA,MAAA1X,WAAA,GAAAjL,2DAAA,CAAA4iB,IAAA;MAAA,MAAAF,QAAA,GAAAzX,WAAA,CAAAG,SAAA;MAAA,MAAAyX,OAAA,GAAA7iB,2DAAA;MAAA,OAASA,yDAAA,CAAA6iB,OAAA,CAAA1B,QAAA,CAAAuB,QAAA,CAAc;IAAA,EAAC;IACpD1iB,uDAAA,cAAiC;IAACA,oDAAA,kBACpC;IAAAA,0DAAA,EAAS;IAEXA,4DAAA,UAAI;IAGAA,wDAAA,mBAAA8iB,kEAAA;MAAA,MAAA7X,WAAA,GAAAjL,2DAAA,CAAA4iB,IAAA;MAAA,MAAAF,QAAA,GAAAzX,WAAA,CAAAG,SAAA;MAAA,MAAA2X,OAAA,GAAA/iB,2DAAA;MAAA,OAASA,yDAAA,CAAA+iB,OAAA,CAAAzB,gBAAA,CAAAoB,QAAA,EAAuB,MAAM,CAAC;IAAA,EAAC;IAExC1iB,uDAAA,YAAqC;IAACA,oDAAA,6CAExC;IAAAA,0DAAA,EAAS;IAEXA,4DAAA,UAAI;IAGAA,wDAAA,mBAAAgjB,kEAAA;MAAA,MAAA/X,WAAA,GAAAjL,2DAAA,CAAA4iB,IAAA;MAAA,MAAAF,QAAA,GAAAzX,WAAA,CAAAG,SAAA;MAAA,MAAA6X,OAAA,GAAAjjB,2DAAA;MAAA,OAASA,yDAAA,CAAAijB,OAAA,CAAA3B,gBAAA,CAAAoB,QAAA,EAAuB,aAAa,CAAC;IAAA,EAAC;IAE/C1iB,uDAAA,YAAqC;IAACA,oDAAA,yCAExC;IAAAA,0DAAA,EAAS;IAEXA,4DAAA,UAAI;IAAAA,uDAAA,eAA+B;IAAAA,0DAAA,EAAK;IACxCA,4DAAA,UAAI;IAGAA,wDAAA,mBAAAkjB,kEAAA;MAAA,MAAAjY,WAAA,GAAAjL,2DAAA,CAAA4iB,IAAA;MAAA,MAAAF,QAAA,GAAAzX,WAAA,CAAAG,SAAA;MAAA,MAAA+X,OAAA,GAAAnjB,2DAAA;MAAA,OAASA,yDAAA,CAAA0iB,QAAA,CAAAnb,GAAA,IAAY4b,OAAA,CAAAxB,UAAA,CAAAe,QAAA,CAAAnb,GAAA,CAAoB;IAAA,EAAC;IAE1CvH,uDAAA,cAAgC;IAACA,oDAAA,mBACnC;IAAAA,0DAAA,EAAS;IAKjBA,4DAAA,cAAkD;IAChDA,oDAAA,IACF;IAAAA,0DAAA,EAAI;IACJA,4DAAA,gBAA+D;IAS3DA,oDAAA,IAOF;IAAAA,0DAAA,EAAO;IACPA,wDAAA,KAAAojB,iDAAA,qBAEQ;IACVpjB,0DAAA,EAAM;IAENA,4DAAA,gBAA4C;IAC1CA,uDAAA,cAAqC;IACvCA,0DAAA,EAAM;;;;IA9ENA,wDAAA,0BAAA0iB,QAAA,CAAAzgB,QAAA,CAAuC;IAMNjC,uDAAA,GAAgB;IAAhBA,+DAAA,CAAA0iB,QAAA,CAAAxgB,KAAA,CAAgB;IA8C/ClC,uDAAA,IACF;IADEA,gEAAA,MAAA0iB,QAAA,CAAArgB,WAAA,8BACF;IAIIrC,uDAAA,GAIE;IAJFA,wDAAA,YAAAA,6DAAA,IAAAO,GAAA,EAAAmiB,QAAA,CAAAzgB,QAAA,aAAAygB,QAAA,CAAAzgB,QAAA,eAAAygB,QAAA,CAAAzgB,QAAA,YAIE;IAEFjC,uDAAA,GAOF;IAPEA,gEAAA,MAAA0iB,QAAA,CAAAzgB,QAAA,wBAAAygB,QAAA,CAAAzgB,QAAA,yCAOF;IAC2BjC,uDAAA,GAAqB;IAArBA,wDAAA,SAAA0iB,QAAA,CAAA7gB,UAAA,CAAqB;;;;;;;;;IA7T9D7B,4DAAA,cAA8C;IAMpCA,uDAAA,YAAoC;IACpCA,oDAAA,qBACA;IAAAA,4DAAA,eAA4D;IAC1DA,oDAAA,GACF;IAAAA,0DAAA,EAAO;IAGXA,4DAAA,cAA2B;IAQvBA,wDAAA,gCAAAqjB,qEAAA9e,MAAA;MAAAvE,2DAAA,CAAAsjB,IAAA;MAAA,MAAAC,OAAA,GAAAvjB,2DAAA;MAAA,OAAsBA,yDAAA,CAAAujB,OAAA,CAAAC,IAAA,CAAAjf,MAAA,CAAY;IAAA,EAAC;IAEnCvE,wDAAA,KAAAyjB,wCAAA,oBAkFM;IACRzjB,0DAAA,EAAM;IAMZA,4DAAA,eAAsB;IAIdA,uDAAA,aAA0C;IAC1CA,oDAAA,kBACA;IAAAA,4DAAA,gBAA4D;IAC1DA,oDAAA,IACF;IAAAA,0DAAA,EAAO;IAGXA,4DAAA,eAA2B;IAQvBA,wDAAA,gCAAA0jB,qEAAAnf,MAAA;MAAAvE,2DAAA,CAAAsjB,IAAA;MAAA,MAAAK,OAAA,GAAA3jB,2DAAA;MAAA,OAAsBA,yDAAA,CAAA2jB,OAAA,CAAAH,IAAA,CAAAjf,MAAA,CAAY;IAAA,EAAC;IAEnCvE,wDAAA,KAAA4jB,wCAAA,oBAkFM;IACR5jB,0DAAA,EAAM;IAMZA,4DAAA,eAAsB;IAIdA,uDAAA,aAAqC;IACrCA,oDAAA,wBACA;IAAAA,4DAAA,gBAA4D;IAC1DA,oDAAA,IACF;IAAAA,0DAAA,EAAO;IAGXA,4DAAA,eAA2B;IAQvBA,wDAAA,gCAAA6jB,qEAAAtf,MAAA;MAAAvE,2DAAA,CAAAsjB,IAAA;MAAA,MAAAQ,OAAA,GAAA9jB,2DAAA;MAAA,OAAsBA,yDAAA,CAAA8jB,OAAA,CAAAN,IAAA,CAAAjf,MAAA,CAAY;IAAA,EAAC;IAEnCvE,wDAAA,KAAA+jB,wCAAA,oBAkFM;IACR/jB,0DAAA,EAAM;;;;;;;IA7TFA,uDAAA,GACF;IADEA,gEAAA,MAAAuY,MAAA,CAAAyL,iBAAA,QACF;IAQAhkB,uDAAA,GAAkC;IAAlCA,wDAAA,oBAAAuY,MAAA,CAAA0L,YAAA,GAAkC,2BAAAjkB,6DAAA,KAAAY,GAAA,EAAA6M,IAAA,EAAAyW,IAAA;IAMflkB,uDAAA,GAAiB;IAAjBA,wDAAA,YAAAuY,MAAA,CAAA0L,YAAA,GAAiB;IA+FlCjkB,uDAAA,GACF;IADEA,gEAAA,MAAAuY,MAAA,CAAA4L,uBAAA,QACF;IAQAnkB,uDAAA,GAAwC;IAAxCA,wDAAA,oBAAAuY,MAAA,CAAA6L,kBAAA,GAAwC,2BAAApkB,6DAAA,KAAAY,GAAA,EAAAuM,IAAA,EAAA+W,IAAA;IAMrBlkB,uDAAA,GAAuB;IAAvBA,wDAAA,YAAAuY,MAAA,CAAA6L,kBAAA,GAAuB;IA+FxCpkB,uDAAA,GACF;IADEA,gEAAA,MAAAuY,MAAA,CAAA8L,iBAAA,QACF;IAQArkB,uDAAA,GAAkC;IAAlCA,wDAAA,oBAAAuY,MAAA,CAAA+L,YAAA,GAAkC,2BAAAtkB,6DAAA,KAAAY,GAAA,EAAAuM,IAAA,EAAAM,IAAA;IAMfzN,uDAAA,GAAiB;IAAjBA,wDAAA,YAAAuY,MAAA,CAAA+L,YAAA,GAAiB;;;ADpgB5C,MAAOvH,iBAAiB;EAgB5B9X,YACUE,WAAwB,EACxB+J,aAA4B,EAC5BC,WAA4B,EAC5BC,KAAqB,EACrBC,MAAc,EACdjK,mBAAwC;IALxC,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAA+J,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAjK,mBAAmB,GAAnBA,mBAAmB;IArB7B,KAAAxC,KAAK,GAAW,EAAE;IAClB,KAAA2F,MAAM,GAAkB,IAAI;IAC5B,KAAArE,IAAI,GAAkB,IAAI;IAC1B,KAAAoL,OAAO,GAAG,KAAK;IACf,KAAAjL,KAAK,GAAkB,IAAI;IAC3B,KAAAiR,KAAK,GAAW,EAAE;IAElB,KAAA8J,WAAW,GAAgB,IAAI;IAC/B,KAAAmF,YAAY,GAAG,KAAK;IAEpB;IACA,KAAA/D,YAAY,GAAW,KAAK;IAC5B,KAAAE,cAAc,GAAW,KAAK;IAC9B,KAAAL,UAAU,GAAW,EAAE;EASpB;EAEH7a,QAAQA,CAAA;IACN;IACA,IAAI,CAAC+Z,OAAO,GAAG,IAAI,CAACiF,iBAAiB,EAAE;IAEvC,IAAI,CAACpV,KAAK,CAACO,QAAQ,CAACjJ,SAAS,CAAE+d,MAAM,IAAI;MACvC,IAAI,CAAClc,MAAM,GAAGkc,MAAM,CAAC7U,GAAG,CAAC,IAAI,CAAC;MAC9B,IAAI,IAAI,CAACrH,MAAM,EAAE;QACf,IAAI,CAACmc,eAAe,CAAC,IAAI,CAACnc,MAAM,CAAC;QACjC,IAAI,CAAC0W,SAAS,CAAC,IAAI,CAAC1W,MAAM,CAAC;QAC3B,IAAI,CAACsH,SAAS,EAAE;OACjB,MAAM;QACL,IAAI,CAACxL,KAAK,GAAG,sBAAsB;QACnC,IAAI,CAACe,mBAAmB,CAACM,SAAS,CAAC,sBAAsB,CAAC;;IAE9D,CAAC,CAAC;EACJ;EAEAgf,eAAeA,CAACnc,MAAc;IAC5B,IAAI,CAAC+G,OAAO,GAAG,IAAI;IAEnB;IACA,MAAMqV,WAAW,GAAG,KAAK,CAAC,CAAC;IAE3B,IAAIA,WAAW,EAAE;MACf;MACA,MAAMC,QAAQ,GAAW;QACvBrd,GAAG,EAAEgB,MAAM;QACX5F,IAAI,EAAE,SAAS,GAAG4F,MAAM;QACxBlG,WAAW,EAAE,0BAA0B,GAAGkG,MAAM;QAChDqG,KAAK,EAAE,UAAU;QACjBzK,OAAO,EAAE;OACV;MAED+L,UAAU,CAAC,MAAK;QACd,IAAI,CAAChM,IAAI,GAAG0gB,QAAQ;QACpB,IAAI,CAACtV,OAAO,GAAG,KAAK;QACpBtJ,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE,IAAI,CAAC/B,IAAI,CAAC;MAC/D,CAAC,EAAE,GAAG,CAAC;KACR,MAAM;MACL;MACA,IAAI,CAACgL,aAAa,CACf0B,SAAS,CAACrI,MAAM,CAAC,CACjB9B,IAAI,CAAC1G,wDAAQ,CAAC,MAAO,IAAI,CAACuP,OAAO,GAAG,KAAM,CAAC,CAAC,CAC5C5I,SAAS,CAAC;QACTC,IAAI,EAAGkK,IAAI,IAAI;UACb,IAAI,CAAC3M,IAAI,GAAG2M,IAAI;UAChB7K,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE,IAAI,CAAC/B,IAAI,CAAC;QACrE,CAAC;QACDG,KAAK,EAAGA,KAAK,IAAI;UACf2B,OAAO,CAAC3B,KAAK,CACX,oDAAoD,EACpDA,KAAK,CACN;UACD,IAAI,CAACA,KAAK,GAAG,+CAA+C;UAC5D,IAAI,CAACe,mBAAmB,CAACM,SAAS,CAChC,mDAAmD,CACpD;UAED;UACA,MAAMkf,QAAQ,GAAW;YACvBrd,GAAG,EAAEgB,MAAM;YACX5F,IAAI,EAAE,SAAS,GAAG4F,MAAM,GAAG,aAAa;YACxClG,WAAW,EAAE,0BAA0B,GAAGkG,MAAM;YAChDqG,KAAK,EAAE,UAAU;YACjBzK,OAAO,EAAE;WACV;UAED,IAAI,CAACD,IAAI,GAAG0gB,QAAQ;QACtB;OACD,CAAC;;EAER;EAEA3F,SAASA,CAAC1W,MAAc;IACtB,IAAI,CAAC+G,OAAO,GAAG,IAAI;IAEnB;IACA,MAAMqV,WAAW,GAAG,KAAK,CAAC,CAAC;IAE3B,IAAIA,WAAW,EAAE;MACf;MACA,MAAME,SAAS,GAAW,CACxB;QACEtd,GAAG,EAAE,GAAG;QACRrF,KAAK,EAAE,SAAS;QAChBG,WAAW,EAAE,2BAA2B;QACxCiG,MAAM,EAAE,MAAM;QACdrG,QAAQ,EAAE,MAAM;QAChBsG,MAAM,EAAEA;OACT,EACD;QACEhB,GAAG,EAAE,GAAG;QACRrF,KAAK,EAAE,SAAS;QAChBG,WAAW,EAAE,2BAA2B;QACxCiG,MAAM,EAAE,MAAM;QACdrG,QAAQ,EAAE,QAAQ;QAClBsG,MAAM,EAAEA;OACT,EACD;QACEhB,GAAG,EAAE,GAAG;QACRrF,KAAK,EAAE,SAAS;QAChBG,WAAW,EAAE,2BAA2B;QACxCiG,MAAM,EAAE,aAAa;QACrBrG,QAAQ,EAAE,MAAM;QAChBsG,MAAM,EAAEA;OACT,EACD;QACEhB,GAAG,EAAE,GAAG;QACRrF,KAAK,EAAE,SAAS;QAChBG,WAAW,EAAE,2BAA2B;QACxCiG,MAAM,EAAE,MAAM;QACdrG,QAAQ,EAAE,KAAK;QACfsG,MAAM,EAAEA;OACT,CACF;MAED2H,UAAU,CAAC,MAAK;QACd,IAAI,CAACtN,KAAK,GAAGiiB,SAAS;QACtB,IAAI,CAACC,SAAS,EAAE;QAChB,IAAI,CAACxV,OAAO,GAAG,KAAK;QACpBtJ,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAACrD,KAAK,CAAC;MACpD,CAAC,EAAE,GAAG,CAAC;KACR,MAAM;MACL;MACA,IAAI,CAACuC,WAAW,CACb4f,cAAc,CAACxc,MAAM,CAAC,CACtB9B,IAAI,CAAC1G,wDAAQ,CAAC,MAAO,IAAI,CAACuP,OAAO,GAAG,KAAM,CAAC,CAAC,CAC5C5I,SAAS,CAAC;QACTC,IAAI,EAAGkK,IAAY,IAAI;UACrB,IAAI,CAACjO,KAAK,GAAGiO,IAAI;UACjB,IAAI,CAACiU,SAAS,EAAE;UAChB9e,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAACrD,KAAK,CAAC;QAC1D,CAAC;QACDyB,KAAK,EAAGA,KAAU,IAAI;UACpB2B,OAAO,CAAC3B,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;UAC7D,IAAI,CAACA,KAAK,GAAG,kCAAkC;UAC/C,IAAI,CAACe,mBAAmB,CAACM,SAAS,CAChC,sCAAsC,CACvC;UAED;UACA,MAAMmf,SAAS,GAAW,CACxB;YACEtd,GAAG,EAAE,GAAG;YACRrF,KAAK,EAAE,oBAAoB;YAC3BG,WAAW,EAAE,2BAA2B;YACxCiG,MAAM,EAAE,MAAM;YACdrG,QAAQ,EAAE,MAAM;YAChBsG,MAAM,EAAEA;WACT,EACD;YACEhB,GAAG,EAAE,GAAG;YACRrF,KAAK,EAAE,oBAAoB;YAC3BG,WAAW,EAAE,2BAA2B;YACxCiG,MAAM,EAAE,MAAM;YACdrG,QAAQ,EAAE,QAAQ;YAClBsG,MAAM,EAAEA;WACT,CACF;UAED,IAAI,CAAC3F,KAAK,GAAGiiB,SAAS;UACtB,IAAI,CAACC,SAAS,EAAE;UAChB9e,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAACrD,KAAK,CAAC;QACxD;OACD,CAAC;;EAER;EAEA;EACA4gB,IAAIA,CAAC3L,KAA0B;IAC7B,IAAIA,KAAK,CAACmN,iBAAiB,KAAKnN,KAAK,CAACoN,SAAS,EAAE;MAC/C;MACApG,uEAAe,CACbhH,KAAK,CAACoN,SAAS,CAACpU,IAAI,EACpBgH,KAAK,CAACqN,aAAa,EACnBrN,KAAK,CAACsN,YAAY,CACnB;KACF,MAAM;MACL;MACArG,yEAAiB,CACfjH,KAAK,CAACmN,iBAAiB,CAACnU,IAAI,EAC5BgH,KAAK,CAACoN,SAAS,CAACpU,IAAI,EACpBgH,KAAK,CAACqN,aAAa,EACnBrN,KAAK,CAACsN,YAAY,CACnB;MAED;MACA,MAAM9c,IAAI,GAAGwP,KAAK,CAACoN,SAAS,CAACpU,IAAI,CAACgH,KAAK,CAACsN,YAAY,CAAC;MACrD,IAAIC,SAA0C;MAE9C,IAAIvN,KAAK,CAACoN,SAAS,CAAC1e,EAAE,KAAK,WAAW,EAAE;QACtC6e,SAAS,GAAG,MAAM;OACnB,MAAM,IAAIvN,KAAK,CAACoN,SAAS,CAAC1e,EAAE,KAAK,kBAAkB,EAAE;QACpD6e,SAAS,GAAG,aAAa;OAC1B,MAAM;QACLA,SAAS,GAAG,MAAM;;MAGpB,IAAI/c,IAAI,CAACd,GAAG,IAAIc,IAAI,CAACC,MAAM,KAAK8c,SAAS,EAAE;QACzC/c,IAAI,CAACC,MAAM,GAAG8c,SAAS;QACvB,IAAI,CAAC9D,gBAAgB,CAACjZ,IAAI,EAAE+c,SAAS,CAAC;;;EAG5C;EAEAvV,SAASA,CAAA;IACP;IACA,MAAM8U,WAAW,GAAG,KAAK,CAAC,CAAC;IAE3B,IAAIA,WAAW,EAAE;MACf;MACA,MAAM5U,SAAS,GAAW,CACxB;QACExI,GAAG,EAAE,OAAO;QACZyI,QAAQ,EAAE,UAAU;QACpBlE,KAAK,EAAE,kBAAkB;QACzBrL,IAAI,EAAE,OAAO;QACbwP,QAAQ,EAAE;OACX,EACD;QACE1I,GAAG,EAAE,OAAO;QACZyI,QAAQ,EAAE,YAAY;QACtBlE,KAAK,EAAE,kBAAkB;QACzBrL,IAAI,EAAE,SAAS;QACfwP,QAAQ,EAAE;OACX,CACF;MAEDC,UAAU,CAAC,MAAK;QACd,IAAI,CAACoF,KAAK,GAAGvF,SAAS;QACtB/J,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACqP,KAAK,CAAC;MACzD,CAAC,EAAE,GAAG,CAAC;KACR,MAAM;MACL;MACA;MACA,MAAMvF,SAAS,GAAW,CACxB;QACExI,GAAG,EAAE,OAAO;QACZyI,QAAQ,EAAE,UAAU;QACpBlE,KAAK,EAAE,kBAAkB;QACzBrL,IAAI,EAAE,OAAO;QACbwP,QAAQ,EAAE;OACX,EACD;QACE1I,GAAG,EAAE,OAAO;QACZyI,QAAQ,EAAE,YAAY;QACtBlE,KAAK,EAAE,kBAAkB;QACzBrL,IAAI,EAAE,SAAS;QACfwP,QAAQ,EAAE;OACX,CACF;MAED,IAAI,CAACqF,KAAK,GAAGvF,SAAS;MACtB/J,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAACqP,KAAK,CAAC;;EAE/D;EAEA7J,WAAWA,CAAC9D,MAAc;IACxB,MAAM6D,IAAI,GAAG,IAAI,CAAC8J,KAAK,CAAC7E,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACnJ,GAAG,KAAKI,MAAM,IAAI+I,CAAC,CAACnK,EAAE,KAAKoB,MAAM,CAAC;IACxE,IAAI6D,IAAI,EAAE;MACR,IAAIA,IAAI,CAACI,SAAS,IAAIJ,IAAI,CAACK,QAAQ,EAAE;QACnC,OAAO,GAAGL,IAAI,CAACI,SAAS,IAAIJ,IAAI,CAACK,QAAQ,EAAE;OAC5C,MAAM,IAAIL,IAAI,CAAC7I,IAAI,EAAE;QACpB,OAAO6I,IAAI,CAAC7I,IAAI;;;IAGpB,OAAO,qBAAqB;EAC9B;EAEA6F,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACD,MAAM,EAAE;MAChB,IAAI,CAACnD,mBAAmB,CAACM,SAAS,CAAC,sBAAsB,CAAC;MAC1D;;IAGF,IAAI,CAAC6Z,OAAO,CAAChX,MAAM,GAAG,IAAI,CAACA,MAAM;IAEjC,IAAI,CAAC+G,OAAO,GAAG,IAAI;IACnB,IAAI,CAACnK,WAAW,CACbqD,UAAU,CAAC,IAAI,CAAC+W,OAAO,CAAC,CACxB9Y,IAAI,CAAC1G,wDAAQ,CAAC,MAAO,IAAI,CAACuP,OAAO,GAAG,KAAM,CAAC,CAAC,CAC5C5I,SAAS,CAAC;MACTC,IAAI,EAAGkK,IAAU,IAAI;QACnB,IAAI,CAACjO,KAAK,CAAC6C,IAAI,CAACoL,IAAI,CAAC;QACrB,IAAI,CAACiU,SAAS,EAAE;QAChB,IAAI,CAACvF,OAAO,GAAG,IAAI,CAACiF,iBAAiB,EAAE;QACvC,IAAI,CAACD,YAAY,GAAG,KAAK;QACzB,IAAI,CAACnf,mBAAmB,CAAC0B,WAAW,CAAC,yBAAyB,CAAC;MACjE,CAAC;MACDzC,KAAK,EAAGA,KAAU,IAAI;QACpB2B,OAAO,CAAC3B,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QAC/D,IAAI,CAACe,mBAAmB,CAACM,SAAS,CAChC,wCAAwC,CACzC;MACH;KACD,CAAC;EACN;EAEA2Z,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACD,WAAW,IAAI,CAAC,IAAI,CAACA,WAAW,CAAC7X,GAAG,EAAE;MAC9C,IAAI,CAACnC,mBAAmB,CAACM,SAAS,CAAC,gBAAgB,CAAC;MACpD;;IAGF,IAAI,CAAC4J,OAAO,GAAG,IAAI;IACnB,IAAI,CAACnK,WAAW,CACbka,UAAU,CAAC,IAAI,CAACD,WAAW,CAAC7X,GAAG,EAAE,IAAI,CAAC6X,WAAW,CAAC,CAClD3Y,IAAI,CAAC1G,wDAAQ,CAAC,MAAO,IAAI,CAACuP,OAAO,GAAG,KAAM,CAAC,CAAC,CAC5C5I,SAAS,CAAC;MACTC,IAAI,EAAGkK,IAAU,IAAI;QACnB,MAAMvK,KAAK,GAAG,IAAI,CAAC1D,KAAK,CAACgT,SAAS,CAAEyP,CAAC,IAAKA,CAAC,CAAC9d,GAAG,KAAKsJ,IAAI,CAACtJ,GAAG,CAAC;QAC7D,IAAIjB,KAAK,KAAK,CAAC,CAAC,EAAE;UAChB,IAAI,CAAC1D,KAAK,CAAC0D,KAAK,CAAC,GAAGuK,IAAI;;QAE1B,IAAI,CAACuO,WAAW,GAAG,IAAI;QACvB,IAAI,CAACha,mBAAmB,CAAC0B,WAAW,CAAC,+BAA+B,CAAC;MACvE,CAAC;MACDzC,KAAK,EAAGA,KAAU,IAAI;QACpB2B,OAAO,CAAC3B,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;QAClE,IAAI,CAACe,mBAAmB,CAACM,SAAS,CAChC,2CAA2C,CAC5C;MACH;KACD,CAAC;EACN;EAEAic,UAAUA,CAACpb,EAAU;IACnB,IAAI0L,OAAO,CAAC,kDAAkD,CAAC,EAAE;MAC/D,IAAI,CAAC3C,OAAO,GAAG,IAAI;MACnB,IAAI,CAACnK,WAAW,CACbwc,UAAU,CAACpb,EAAE,CAAC,CACdE,IAAI,CAAC1G,wDAAQ,CAAC,MAAO,IAAI,CAACuP,OAAO,GAAG,KAAM,CAAC,CAAC,CAC5C5I,SAAS,CAAC;QACTC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAAC/D,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC0N,MAAM,CAAE+U,CAAC,IAAKA,CAAC,CAAC9d,GAAG,KAAKhB,EAAE,CAAC;UACnD,IAAI,CAACnB,mBAAmB,CAAC0B,WAAW,CAAC,6BAA6B,CAAC;QACrE,CAAC;QACDzC,KAAK,EAAGA,KAAU,IAAI;UACpB2B,OAAO,CAAC3B,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;UAClE,IAAI,CAACe,mBAAmB,CAACM,SAAS,CAChC,2CAA2C,CAC5C;QACH;OACD,CAAC;;EAER;EAEA4b,gBAAgBA,CAACjZ,IAAU,EAAEC,MAAuC;IAClE,IAAI,CAACD,IAAI,CAACd,GAAG,EAAE;IAEf,IAAI,CAAC+H,OAAO,GAAG,IAAI;IACnB,IAAI,CAACnK,WAAW,CACbmc,gBAAgB,CAACjZ,IAAI,CAACd,GAAG,EAAEe,MAAM,CAAC,CAClC7B,IAAI,CAAC1G,wDAAQ,CAAC,MAAO,IAAI,CAACuP,OAAO,GAAG,KAAM,CAAC,CAAC,CAC5C5I,SAAS,CAAC;MACTC,IAAI,EAAGkK,IAAU,IAAI;QACnB,MAAMvK,KAAK,GAAG,IAAI,CAAC1D,KAAK,CAACgT,SAAS,CAAEyP,CAAC,IAAKA,CAAC,CAAC9d,GAAG,KAAKsJ,IAAI,CAACtJ,GAAG,CAAC;QAC7D,IAAIjB,KAAK,KAAK,CAAC,CAAC,EAAE;UAChB,IAAI,CAAC1D,KAAK,CAAC0D,KAAK,CAAC,GAAGuK,IAAI;;QAE1B,IAAI,CAACzL,mBAAmB,CAAC0B,WAAW,CAAC,+BAA+B,CAAC;MACvE,CAAC;MACDzC,KAAK,EAAGA,KAAU,IAAI;QACpB2B,OAAO,CAAC3B,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;QAChE,IAAI,CAACe,mBAAmB,CAACM,SAAS,CAChC,yCAAyC,CAC1C;MACH;KACD,CAAC;EACN;EAEAyb,QAAQA,CAAC9Y,IAAU;IACjB,IAAI,CAAC+W,WAAW,GAAG;MAAE,GAAG/W;IAAI,CAAE;EAChC;EAEAqS,UAAUA,CAAA;IACR,IAAI,CAAC0E,WAAW,GAAG,IAAI;EACzB;EAEAa,cAAcA,CAAA;IACZ,IAAI,CAACsE,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IACtC,IAAI,IAAI,CAACA,YAAY,EAAE;MACrB,IAAI,CAAChF,OAAO,GAAG,IAAI,CAACiF,iBAAiB,EAAE;;EAE3C;EAEAA,iBAAiBA,CAAA;IACf,OAAO;MACLtiB,KAAK,EAAE,EAAE;MACTG,WAAW,EAAE,EAAE;MACfiG,MAAM,EAAE,MAAM;MACdrG,QAAQ,EAAE,QAAQ;MAClBsG,MAAM,EAAE,IAAI,CAACA,MAAM,IAAI,EAAE;MACzBsX,OAAO,EAAE,IAAI5W,IAAI,CAACA,IAAI,CAACD,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAE;KAC1D;EACH;;EAEA8b,SAASA,CAAA;IACP;IACA,IAAI,CAACliB,KAAK,CAACuW,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACvB,MAAMiM,aAAa,GAA8B;QAC/CC,IAAI,EAAE,CAAC;QACPC,MAAM,EAAE,CAAC;QACTC,GAAG,EAAE;OACN;MACD,MAAMC,WAAW,GAA8B;QAC7CC,IAAI,EAAE,CAAC;QACP,aAAa,EAAE,CAAC;QAChBC,IAAI,EAAE;OACP;MAED;MACA,IAAIN,aAAa,CAAClM,CAAC,CAACnX,QAAQ,CAAC,KAAKqjB,aAAa,CAACjM,CAAC,CAACpX,QAAQ,CAAC,EAAE;QAC3D,OAAOqjB,aAAa,CAAClM,CAAC,CAACnX,QAAQ,CAAC,GAAGqjB,aAAa,CAACjM,CAAC,CAACpX,QAAQ,CAAC;;MAG9D;MACA,OAAOyjB,WAAW,CAACtM,CAAC,CAAC9Q,MAAM,CAAC,GAAGod,WAAW,CAACrM,CAAC,CAAC/Q,MAAM,CAAC;IACtD,CAAC,CAAC;EACJ;EAEA;EACAud,WAAWA,CAAA;IACT,OAAO,IAAI,CAACjjB,KAAK,CAAC0N,MAAM,CAAEjI,IAAI,IAAI;MAChC;MACA,IAAI,IAAI,CAACmY,YAAY,KAAK,KAAK,IAAInY,IAAI,CAACC,MAAM,KAAK,IAAI,CAACkY,YAAY,EAAE;QACpE,OAAO,KAAK;;MAGd;MACA,IACE,IAAI,CAACE,cAAc,KAAK,KAAK,IAC7BrY,IAAI,CAACpG,QAAQ,KAAK,IAAI,CAACye,cAAc,EACrC;QACA,OAAO,KAAK;;MAGd;MACA,IACE,IAAI,CAACL,UAAU,IACf,CAAChY,IAAI,CAACnG,KAAK,CAAC4G,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACsX,UAAU,CAACvX,WAAW,EAAE,CAAC,EACjE;QACA,OAAO,KAAK;;MAGd,OAAO,IAAI;IACb,CAAC,CAAC;EACJ;EAEA;EACAmb,YAAYA,CAAA;IACV,OAAO,IAAI,CAACrhB,KAAK,CAAC0N,MAAM,CACrBjI,IAAI,IACHA,IAAI,CAACC,MAAM,KAAK,MAAM,KACrB,IAAI,CAACoY,cAAc,KAAK,KAAK,IAC5BrY,IAAI,CAACpG,QAAQ,KAAK,IAAI,CAACye,cAAc,CAAC,KACvC,CAAC,IAAI,CAACL,UAAU,IACfhY,IAAI,CAACnG,KAAK,CAAC4G,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACsX,UAAU,CAACvX,WAAW,EAAE,CAAC,CAAC,CACtE;EACH;EAEAsb,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACxhB,KAAK,CAAC0N,MAAM,CACrBjI,IAAI,IACHA,IAAI,CAACC,MAAM,KAAK,aAAa,KAC5B,IAAI,CAACoY,cAAc,KAAK,KAAK,IAC5BrY,IAAI,CAACpG,QAAQ,KAAK,IAAI,CAACye,cAAc,CAAC,KACvC,CAAC,IAAI,CAACL,UAAU,IACfhY,IAAI,CAACnG,KAAK,CAAC4G,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACsX,UAAU,CAACvX,WAAW,EAAE,CAAC,CAAC,CACtE;EACH;EAEAwb,YAAYA,CAAA;IACV,OAAO,IAAI,CAAC1hB,KAAK,CAAC0N,MAAM,CACrBjI,IAAI,IACHA,IAAI,CAACC,MAAM,KAAK,MAAM,KACrB,IAAI,CAACoY,cAAc,KAAK,KAAK,IAC5BrY,IAAI,CAACpG,QAAQ,KAAK,IAAI,CAACye,cAAc,CAAC,KACvC,CAAC,IAAI,CAACL,UAAU,IACfhY,IAAI,CAACnG,KAAK,CAAC4G,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACsX,UAAU,CAACvX,WAAW,EAAE,CAAC,CAAC,CACtE;EACH;EAEA;EACAkb,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACphB,KAAK,CAAC0N,MAAM,CAAEjI,IAAI,IAAKA,IAAI,CAACC,MAAM,KAAK,MAAM,CAAC,CAACzF,MAAM;EACnE;EAEAshB,uBAAuBA,CAAA;IACrB,OAAO,IAAI,CAACvhB,KAAK,CAAC0N,MAAM,CAAEjI,IAAI,IAAKA,IAAI,CAACC,MAAM,KAAK,aAAa,CAAC,CAACzF,MAAM;EAC1E;EAEAwhB,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACzhB,KAAK,CAAC0N,MAAM,CAAEjI,IAAI,IAAKA,IAAI,CAACC,MAAM,KAAK,MAAM,CAAC,CAACzF,MAAM;EACnE;EAEAijB,YAAYA,CAAA;IACV,IAAI,CAACzW,MAAM,CAAC2B,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;;;uBA3gBW+L,iBAAiB,EAAA/c,+DAAA,CAAAyJ,sEAAA,GAAAzJ,+DAAA,CAAA2J,0EAAA,GAAA3J,+DAAA,CAAA6J,8EAAA,GAAA7J,+DAAA,CAAAyW,2DAAA,GAAAzW,+DAAA,CAAAyW,mDAAA,GAAAzW,+DAAA,CAAA0W,sFAAA;IAAA;EAAA;;;YAAjBqG,iBAAiB;MAAAhT,SAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA4b,2BAAA1b,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCrB9BtK,4DAAA,aAA2C;UAS/BA,wDAAA,IAAAimB,+BAAA,gBAEK;UACLjmB,4DAAA,WAA2B;UAAAA,oDAAA,sDAAgC;UAAAA,0DAAA,EAAI;UAEjEA,4DAAA,aAA0B;UAGtBA,wDAAA,mBAAAkmB,oDAAA;YAAA,OAAS3b,GAAA,CAAAub,YAAA,EAAc;UAAA,EAAC;UAExB9lB,uDAAA,YAAqC;UAACA,oDAAA,gBACxC;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAGC;UADCA,wDAAA,mBAAAmmB,oDAAA;YAAA,OAAS5b,GAAA,CAAA0V,cAAA,EAAgB;UAAA,EAAC;UAE1BjgB,uDAAA,aAAsC;UAACA,oDAAA,6BACzC;UAAAA,0DAAA,EAAS;UAGbA,uDAAA,cAAmB;UACrBA,0DAAA,EAAM;UAIRA,wDAAA,KAAAomB,iCAAA,mBAaM;UAGNpmB,wDAAA,KAAAqmB,iCAAA,kBAiBM;UAGNrmB,wDAAA,KAAAsmB,iCAAA,oBA6JM;UAGNtmB,wDAAA,KAAAumB,iCAAA,mBAsCM;UAGNvmB,wDAAA,KAAAwmB,iCAAA,mBAmBM;UAGNxmB,wDAAA,KAAAymB,iCAAA,oBA0UM;UACRzmB,0DAAA,EAAM;;;UAvmBgDA,uDAAA,GAAU;UAAVA,wDAAA,SAAAuK,GAAA,CAAArG,IAAA,CAAU;UAyBxDlE,uDAAA,IAAa;UAAbA,wDAAA,SAAAuK,GAAA,CAAA+E,OAAA,CAAa;UAgBbtP,uDAAA,GAAW;UAAXA,wDAAA,SAAAuK,GAAA,CAAAlG,KAAA,CAAW;UAoBXrE,uDAAA,GAAiC;UAAjCA,wDAAA,SAAAuK,GAAA,CAAAga,YAAA,IAAAha,GAAA,CAAA6U,WAAA,CAAiC;UAgKhBpf,uDAAA,GAAsB;UAAtBA,wDAAA,SAAAuK,GAAA,CAAA3H,KAAA,CAAAC,MAAA,KAAsB;UA0C1C7C,uDAAA,GAA8C;UAA9CA,wDAAA,UAAAuK,GAAA,CAAA+E,OAAA,KAAA/E,GAAA,CAAAlG,KAAA,IAAAkG,GAAA,CAAA3H,KAAA,CAAAC,MAAA,OAA8C;UAqB3B7C,uDAAA,GAAsB;UAAtBA,wDAAA,SAAAuK,GAAA,CAAA3H,KAAA,CAAAC,MAAA,KAAsB", "sources": ["./src/app/views/admin/equipes/ai-chat/ai-chat.component.ts", "./src/app/views/admin/equipes/ai-chat/ai-chat.component.html", "./src/app/views/admin/equipes/equipe-detail/equipe-detail.component.ts", "./src/app/views/admin/equipes/equipe-detail/equipe-detail.component.html", "./src/app/views/admin/equipes/equipe-form/equipe-form.component.ts", "./src/app/views/admin/equipes/equipe-layout/equipe-layout.component.ts", "./src/app/views/admin/equipes/equipe-layout/equipe-layout.component.html", "./src/app/views/admin/equipes/equipe-list/equipe-list.component.ts", "./src/app/views/admin/equipes/equipe-list/equipe-list.component.html", "./src/app/views/admin/equipes/equipe/equipe.component.ts", "./src/app/views/admin/equipes/equipe/equipe.component.html", "./src/app/views/admin/equipes/equipes-routing.module.ts", "./src/app/views/admin/equipes/equipes.module.ts", "./src/app/views/admin/equipes/notification/notification.component.ts", "./src/app/views/admin/equipes/task-list/task-list.component.ts", "./src/app/views/admin/equipes/task-list/task-list.component.html"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\nimport { AiService } from 'src/app/services/ai.service';\nimport { TaskService } from 'src/app/services/task.service';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { Equipe } from 'src/app/models/equipe.model';\nimport { Task } from 'src/app/models/task.model';\nimport { finalize } from 'rxjs/operators';\n@Component({\n  selector: 'app-ai-chat',\n  templateUrl: './ai-chat.component.html',\n  styleUrls: ['./ai-chat.component.css'],\n})\nexport class AiChatComponent implements OnInit {\n  @Input() team!: Equipe;\n\n  projectTitle: string = '';\n  isGenerating: boolean = false;\n  generatedContent: any = null;\n  error: string | null = null;\n\n  // Pour le chat\n  messages: { role: 'user' | 'assistant'; content: string }[] = [];\n  userQuestion: string = '';\n  isAskingQuestion: boolean = false;\n\n  constructor(\n    private aiService: AiService,\n    private taskService: TaskService,\n    private notificationService: NotificationService\n  ) {}\n\n  ngOnInit(): void {\n    // Ajouter un message de bienvenue\n    this.messages.push({\n      role: 'assistant',\n      content:\n        'Bonjour ! Je suis votre assistant IA pour la gestion de projet. Entrez le titre de votre projet pour que je puisse vous aider à le diviser en tâches, ou posez-moi une question sur la gestion de projet.',\n    });\n  }\n\n  generateTasks(): void {\n    if (!this.projectTitle.trim()) {\n      this.notificationService.showError('Veuillez entrer un titre de projet');\n      return;\n    }\n\n    // Vérifier si l'équipe a des membres, sinon utiliser un nombre par défaut\n    let memberCount =\n      this.team && this.team.members ? this.team.members.length : 3;\n\n    // S'assurer que nous avons au moins 3 entités pour un projet significatif\n    const effectiveMemberCount = Math.max(memberCount, 3);\n\n    if (memberCount === 0) {\n      this.notificationService.showWarning(\n        \"L'équipe n'a pas de membres. Des tâches génériques seront créées.\"\n      );\n    }\n\n    this.isGenerating = true;\n    this.error = null;\n\n    console.log(\n      `Génération de tâches pour ${effectiveMemberCount} entités (équipe de ${memberCount} membres)`\n    );\n\n    // Ajouter la demande aux messages\n    this.messages.push({\n      role: 'user',\n      content: `Génère des tâches pour le projet \"${this.projectTitle}\" avec exactement ${effectiveMemberCount} entités, une pour chaque membre de l'équipe. Chaque entité doit représenter un module distinct du projet.`,\n    });\n\n    // Ajouter un message de chargement\n    const loadingMessageIndex = this.messages.length;\n    this.messages.push({\n      role: 'assistant',\n      content: 'Je génère des tâches pour votre projet...',\n    });\n\n    // Récupérer les informations sur les membres de l'équipe\n    let teamMembers: any[] = [];\n    if (this.team && this.team.members) {\n      // Utiliser les IDs des membres\n      teamMembers = this.team.members.map((memberId: string, index: number) => {\n        return { id: memberId, name: `Membre ${index + 1}`, role: 'membre' };\n      });\n\n      console.log(\"Informations sur les membres passées à l'IA:\", teamMembers);\n    }\n\n    this.aiService\n      .generateProjectTasks(this.projectTitle, memberCount, teamMembers)\n      .pipe(finalize(() => (this.isGenerating = false)))\n      .subscribe({\n        next: (result: any) => {\n          if (!result || !result.entities || result.entities.length === 0) {\n            console.error(\"Résultat invalide reçu de l'API:\", result);\n            this.handleGenerationError(\n              loadingMessageIndex,\n              'Format de réponse invalide'\n            );\n            return;\n          }\n\n          this.generatedContent = result;\n\n          // Remplacer le message de chargement par la réponse\n          this.messages[loadingMessageIndex] = {\n            role: 'assistant',\n            content: `J'ai généré ${\n              result.entities.length\n            } entités pour votre projet \"${\n              result.projectTitle\n            }\" avec un total de ${this.countTasks(result)} tâches.`,\n          };\n\n          this.notificationService.showSuccess('Tâches générées avec succès');\n        },\n        error: (error: any) => {\n          console.error('Erreur lors de la génération des tâches:', error);\n          this.handleGenerationError(\n            loadingMessageIndex,\n            error.message || 'Erreur inconnue'\n          );\n        },\n      });\n  }\n\n  // Méthode pour gérer les erreurs de génération\n  private handleGenerationError(\n    messageIndex: number,\n    errorDetails: string\n  ): void {\n    this.error = 'Impossible de générer les tâches. Veuillez réessayer.';\n\n    // Remplacer le message de chargement par le message d'erreur\n    this.messages[messageIndex] = {\n      role: 'assistant',\n      content:\n        \"Désolé, je n'ai pas pu générer les tâches. Veuillez réessayer avec un titre de projet différent.\",\n    };\n\n    this.notificationService.showError(\n      'Erreur lors de la génération des tâches: ' + errorDetails\n    );\n  }\n\n  askQuestion(): void {\n    if (!this.userQuestion.trim()) {\n      return;\n    }\n\n    const question = this.userQuestion.trim();\n    this.userQuestion = '';\n    this.isAskingQuestion = true;\n\n    // Ajouter la question aux messages\n    this.messages.push({\n      role: 'user',\n      content: question,\n    });\n\n    const projectContext = {\n      title:\n        this.projectTitle ||\n        (this.generatedContent ? this.generatedContent.projectTitle : ''),\n      description:\n        \"Projet géré par l'équipe \" + (this.team ? this.team.name : ''),\n    };\n\n    this.aiService\n      .askProjectQuestion(question, projectContext)\n      .pipe(finalize(() => (this.isAskingQuestion = false)))\n      .subscribe({\n        next: (response: string) => {\n          // Ajouter la réponse aux messages\n          this.messages.push({\n            role: 'assistant',\n            content: response,\n          });\n        },\n        error: (error: any) => {\n          console.error(\"Erreur lors de la demande à l'IA:\", error);\n\n          // Ajouter l'erreur aux messages\n          this.messages.push({\n            role: 'assistant',\n            content:\n              \"Désolé, je n'ai pas pu répondre à votre question. Veuillez réessayer.\",\n          });\n\n          this.notificationService.showError(\n            \"Erreur lors de la communication avec l'IA\"\n          );\n        },\n      });\n  }\n\n  createTasks(): void {\n    if (!this.generatedContent || !this.team || !this.team._id) {\n      this.notificationService.showError(\n        'Aucune tâche générée ou équipe invalide'\n      );\n      return;\n    }\n\n    let createdCount = 0;\n    const totalTasks = this.countTasks(this.generatedContent);\n\n    // Vérifier si l'équipe a des membres\n    if (!this.team.members || this.team.members.length === 0) {\n      this.notificationService.showError(\n        \"L'équipe n'a pas de membres pour assigner les tâches\"\n      );\n      return;\n    }\n\n    // Préparer la liste des membres de l'équipe\n    const teamMembers = this.team.members.map((member) => {\n      return typeof member === 'string' ? member : (member as any).userId;\n    });\n\n    // Créer un mapping des noms de membres vers leurs IDs\n    const memberNameToIdMap: { [key: string]: string } = {};\n    teamMembers.forEach((memberId, index) => {\n      memberNameToIdMap[`Membre ${index + 1}`] = memberId;\n    });\n\n    console.log(\n      \"Membres de l'équipe disponibles pour l'assignation:\",\n      teamMembers\n    );\n    console.log(\n      'Mapping des noms de membres vers leurs IDs:',\n      memberNameToIdMap\n    );\n\n    // Pour chaque entité\n    this.generatedContent.entities.forEach((entity: any) => {\n      // Déterminer le membre assigné à cette entité\n      let assignedMemberId: string | undefined;\n\n      // Si l'IA a suggéré une assignation\n      if (entity.assignedTo) {\n        // Essayer de trouver l'ID du membre à partir du nom suggéré\n        const memberName = entity.assignedTo;\n        if (memberNameToIdMap[memberName]) {\n          assignedMemberId = memberNameToIdMap[memberName];\n          console.log(\n            `Assignation suggérée par l'IA: Entité \"${entity.name}\" assignée à \"${memberName}\" (ID: ${assignedMemberId})`\n          );\n        } else {\n          // Si le nom n'est pas trouvé, assigner aléatoirement\n          const randomMemberIndex = Math.floor(\n            Math.random() * teamMembers.length\n          );\n          assignedMemberId = teamMembers[randomMemberIndex];\n          console.log(\n            `Nom de membre \"${memberName}\" non trouvé, assignation aléatoire à l'index ${randomMemberIndex}`\n          );\n        }\n      } else {\n        // Si pas d'assignation suggérée, assigner aléatoirement\n        const randomMemberIndex = Math.floor(\n          Math.random() * teamMembers.length\n        );\n        assignedMemberId = teamMembers[randomMemberIndex];\n        console.log(\n          `Pas d'assignation suggérée, assignation aléatoire à l'index ${randomMemberIndex}`\n        );\n      }\n\n      // Pour chaque tâche dans l'entité\n      entity.tasks.forEach((taskData: any) => {\n        const task: Task = {\n          title: taskData.title,\n          description: `[${entity.name}] ${taskData.description}`,\n          status: taskData.status || 'todo',\n          priority: taskData.priority || 'medium',\n          teamId: this.team._id || '',\n          // Utiliser l'ID du membre assigné à l'entité\n          assignedTo: assignedMemberId,\n        };\n\n        this.taskService.createTask(task).subscribe({\n          next: () => {\n            createdCount++;\n            if (createdCount === totalTasks) {\n              this.notificationService.showSuccess(\n                `${createdCount} tâches créées avec succès et assignées aux membres de l'équipe`\n              );\n              // Réinitialiser après création\n              this.generatedContent = null;\n              this.projectTitle = '';\n            }\n          },\n          error: (error) => {\n            console.error('Erreur lors de la création de la tâche:', error);\n            this.notificationService.showError(\n              'Erreur lors de la création des tâches'\n            );\n          },\n        });\n      });\n    });\n  }\n\n  countTasks(content: any): number {\n    if (!content || !content.entities) return 0;\n\n    return content.entities.reduce((total: number, entity: any) => {\n      return total + (entity.tasks ? entity.tasks.length : 0);\n    }, 0);\n  }\n\n  // Méthode pour obtenir un dégradé de couleur basé sur l'index\n  getGradientForIndex(index: number): string {\n    // Liste de dégradés prédéfinis\n    const gradients = [\n      'linear-gradient(45deg, #007bff, #6610f2)', // Bleu-Violet\n      'linear-gradient(45deg, #11998e, #38ef7d)', // Vert\n      'linear-gradient(45deg, #FC5C7D, #6A82FB)', // Rose-Bleu\n      'linear-gradient(45deg, #FF8008, #FFC837)', // Orange-Jaune\n      'linear-gradient(45deg, #8E2DE2, #4A00E0)', // Violet\n      'linear-gradient(45deg, #2193b0, #6dd5ed)', // Bleu clair\n      'linear-gradient(45deg, #373B44, #4286f4)', // Gris-Bleu\n      'linear-gradient(45deg, #834d9b, #d04ed6)', // Violet-Rose\n      'linear-gradient(45deg, #0cebeb, #20e3b2, #29ffc6)', // Turquoise\n    ];\n\n    // Utiliser le modulo pour s'assurer que nous ne dépassons pas le tableau\n    return gradients[index % gradients.length];\n  }\n\n  // Méthode pour obtenir une couleur unique basée sur l'index\n  getColorForIndex(index: number): string {\n    // Liste de couleurs prédéfinies\n    const colors = [\n      '#007bff', // Bleu\n      '#11998e', // Vert\n      '#FC5C7D', // Rose\n      '#FF8008', // Orange\n      '#8E2DE2', // Violet\n      '#2193b0', // Bleu clair\n      '#373B44', // Gris foncé\n      '#834d9b', // Violet\n      '#0cebeb', // Turquoise\n    ];\n\n    // Utiliser le modulo pour s'assurer que nous ne dépassons pas le tableau\n    return colors[index % colors.length];\n  }\n\n  // Méthode pour obtenir une icône en fonction du nom du module\n  getIconForModule(moduleName: string): string {\n    // Convertir le nom du module en minuscules pour faciliter la comparaison\n    const name = moduleName.toLowerCase();\n\n    // Mapper les noms de modules courants à des icônes Bootstrap\n    if (\n      name.includes('crud') ||\n      name.includes('api') ||\n      name.includes('données') ||\n      name.includes('base')\n    ) {\n      return 'bi-database-fill';\n    } else if (\n      name.includes('interface') ||\n      name.includes('ui') ||\n      name.includes('front') ||\n      name.includes('utilisateur')\n    ) {\n      return 'bi-window';\n    } else if (\n      name.includes('déploiement') ||\n      name.includes('serveur') ||\n      name.includes('cloud')\n    ) {\n      return 'bi-cloud-arrow-up-fill';\n    } else if (\n      name.includes('test') ||\n      name.includes('qualité') ||\n      name.includes('qa')\n    ) {\n      return 'bi-bug-fill';\n    } else if (name.includes('sécurité') || name.includes('auth')) {\n      return 'bi-shield-lock-fill';\n    } else if (name.includes('paiement') || name.includes('transaction')) {\n      return 'bi-credit-card-fill';\n    } else if (\n      name.includes('utilisateur') ||\n      name.includes('user') ||\n      name.includes('profil')\n    ) {\n      return 'bi-person-fill';\n    } else if (name.includes('doc') || name.includes('documentation')) {\n      return 'bi-file-text-fill';\n    } else if (name.includes('mobile') || name.includes('app')) {\n      return 'bi-phone-fill';\n    } else if (name.includes('backend') || name.includes('serveur')) {\n      return 'bi-server';\n    } else if (\n      name.includes('analytics') ||\n      name.includes('statistique') ||\n      name.includes('seo')\n    ) {\n      return 'bi-graph-up';\n    }\n\n    // Icône par défaut si aucune correspondance n'est trouvée\n    return 'bi-code-square';\n  }\n\n  // Méthode pour obtenir l'heure actuelle au format HH:MM\n  getCurrentTime(): string {\n    const now = new Date();\n    const hours = now.getHours().toString().padStart(2, '0');\n    const minutes = now.getMinutes().toString().padStart(2, '0');\n    return `${hours}:${minutes}`;\n  }\n}\n", "<div class=\"ai-chat-container w-100\">\n  <div class=\"card border-0 shadow-sm w-100\">\n    <!-- Suppression de l'en-tête redondant car il est déjà présent dans le composant parent -->\n    <div class=\"card-body p-0\">\n      <!-- Messages du chat améliorés -->\n      <div class=\"chat-messages p-3\" #chatContainer>\n        <div *ngFor=\"let message of messages; let i = index\"\n             class=\"message mb-3\"\n             [ngClass]=\"{'user-message': message.role === 'user', 'assistant-message': message.role === 'assistant'}\">\n          <!-- Avatar et contenu du message -->\n          <div class=\"d-flex\" [ngClass]=\"{'flex-row-reverse': message.role === 'user'}\">\n            <!-- Avatar -->\n            <div class=\"message-avatar rounded-circle d-flex align-items-center justify-content-center me-2\"\n                 [ngClass]=\"{'bg-primary': message.role === 'user', 'bg-success': message.role === 'assistant'}\">\n              <i class=\"bi\" [ngClass]=\"message.role === 'user' ? 'bi-person-fill' : 'bi-robot'\"></i>\n            </div>\n\n            <!-- Contenu du message -->\n            <div class=\"message-bubble p-3 rounded-4 shadow-sm\"\n                 [ngClass]=\"{'user-bubble': message.role === 'user', 'assistant-bubble': message.role === 'assistant'}\">\n              <p class=\"mb-0\" [innerHTML]=\"message.content\"></p>\n\n              <!-- Horodatage -->\n              <div class=\"message-time small text-muted mt-1 text-end\">\n                {{ message.role === 'user' ? 'Vous' : 'Assistant IA' }} • {{ getCurrentTime() }}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Indicateur de chargement amélioré -->\n        <div *ngIf=\"isGenerating || isAskingQuestion\" class=\"message assistant-message mb-3\">\n          <div class=\"d-flex\">\n            <!-- Avatar -->\n            <div class=\"message-avatar rounded-circle d-flex align-items-center justify-content-center me-2 bg-success\">\n              <i class=\"bi bi-robot\"></i>\n            </div>\n\n            <!-- Indicateur de chargement -->\n            <div class=\"message-bubble assistant-bubble p-3 rounded-4 shadow-sm\">\n              <div class=\"typing-indicator\">\n                <span></span>\n                <span></span>\n                <span></span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Résultats générés avec en-tête amélioré -->\n      <div *ngIf=\"generatedContent\" class=\"generated-content p-4 border-top\">\n        <div class=\"generated-header mb-4 p-3 rounded-4 shadow-sm\"\n             style=\"background: linear-gradient(120deg, rgba(13, 110, 253, 0.1), rgba(102, 16, 242, 0.1))\">\n          <div class=\"d-flex justify-content-between align-items-center\">\n            <div>\n              <h5 class=\"text-primary mb-1\">\n                <i class=\"bi bi-diagram-3-fill me-2\"></i>\n                Plan du projet \"{{ generatedContent.projectTitle }}\"\n              </h5>\n              <p class=\"text-muted mb-0\">\n                <i class=\"bi bi-info-circle me-1\"></i>\n                {{ generatedContent.entities.length }} modules générés avec {{ countTasks(generatedContent) }} tâches au total\n              </p>\n            </div>\n            <span class=\"badge bg-primary rounded-pill px-3 py-2\">\n              <i class=\"bi bi-people-fill me-1\"></i>\n              {{ team && team.members ? team.members.length : 0 }} membres\n            </span>\n          </div>\n        </div>\n\n        <!-- Affichage des modules avec une présentation plus claire et adaptée à la largeur complète -->\n        <div class=\"row mb-4\">\n          <div *ngFor=\"let entity of generatedContent.entities; let i = index\" class=\"col-lg-3 col-md-4 col-sm-6 mb-4\">\n            <div class=\"module-card card h-100 border-0 shadow-sm\">\n              <!-- Ruban indiquant le numéro du module -->\n              <div class=\"module-ribbon\" [ngStyle]=\"{'background': getGradientForIndex(i)}\">\n                <span>Module {{ i + 1 }}</span>\n              </div>\n\n              <!-- En-tête avec couleur dynamique basée sur l'index -->\n              <div class=\"card-header text-white position-relative py-4\" [ngStyle]=\"{'background': getGradientForIndex(i)}\">\n                <div class=\"module-icon-large rounded-circle bg-white d-flex align-items-center justify-content-center shadow\">\n                  <i class=\"bi\" [ngClass]=\"getIconForModule(entity.name)\" [ngStyle]=\"{'color': getColorForIndex(i)}\"></i>\n                </div>\n                <h5 class=\"mt-3 mb-0 text-center\">{{ entity.name }}</h5>\n              </div>\n\n              <div class=\"card-body\">\n                <!-- Description -->\n                <div class=\"description-box p-3 rounded-3 bg-light mb-3\">\n                  <p class=\"mb-0\">{{ entity.description }}</p>\n                </div>\n\n                <!-- Assignation avec style amélioré -->\n                <div *ngIf=\"entity.assignedTo\" class=\"assignation-badge mb-3 p-3 rounded-3 d-flex align-items-center\"\n                     [ngStyle]=\"{'background': 'linear-gradient(45deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7))',\n                                'border-left': '4px solid ' + getColorForIndex(i)}\">\n                  <div class=\"member-avatar rounded-circle me-3 d-flex align-items-center justify-content-center text-white\"\n                       [ngStyle]=\"{'background': getGradientForIndex(i)}\">\n                    <i class=\"bi bi-person-fill\"></i>\n                  </div>\n                  <div>\n                    <div class=\"small text-muted\">Responsable</div>\n                    <div class=\"fw-bold\" [ngStyle]=\"{'color': getColorForIndex(i)}\">{{ entity.assignedTo }}</div>\n                  </div>\n                </div>\n\n                <!-- En-tête de la liste des tâches -->\n                <div class=\"task-header d-flex justify-content-between align-items-center mb-3 pb-2 border-bottom\">\n                  <h6 class=\"mb-0 d-flex align-items-center\">\n                    <i class=\"bi bi-list-check me-2\" [ngStyle]=\"{'color': getColorForIndex(i)}\"></i>\n                    Tâches à réaliser\n                  </h6>\n                  <span class=\"badge rounded-pill\" [ngStyle]=\"{'background': getGradientForIndex(i)}\">\n                    {{ entity.tasks.length }} tâches\n                  </span>\n                </div>\n\n                <!-- Liste des tâches avec design amélioré -->\n                <div class=\"task-list\">\n                  <div *ngFor=\"let task of entity.tasks; let j = index\"\n                       class=\"task-item mb-3 p-3 rounded-3 shadow-sm\"\n                       [ngClass]=\"{'high-priority': task.priority === 'high',\n                                  'medium-priority': task.priority === 'medium',\n                                  'low-priority': task.priority === 'low'}\">\n                    <!-- Titre et priorité -->\n                    <div class=\"d-flex justify-content-between align-items-center mb-2\">\n                      <h6 class=\"mb-0 task-title\">{{ task.title }}</h6>\n                      <span class=\"badge rounded-pill\" [ngClass]=\"{\n                        'bg-danger': task.priority === 'high',\n                        'bg-warning text-dark': task.priority === 'medium',\n                        'bg-info text-dark': task.priority === 'low'\n                      }\">\n                        {{ task.priority === 'high' ? 'Haute' :\n                           task.priority === 'medium' ? 'Moyenne' : 'Basse' }}\n                      </span>\n                    </div>\n\n                    <!-- Description de la tâche (toujours visible) -->\n                    <div class=\"task-description text-muted small\">\n                      {{ task.description }}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Affichage détaillé (accordéon) -->\n        <div class=\"accordion d-none\" id=\"generatedTasksAccordion\">\n          <div *ngFor=\"let entity of generatedContent.entities; let i = index\" class=\"accordion-item border-0 mb-2\">\n            <h2 class=\"accordion-header\" [id]=\"'heading' + i\">\n              <button class=\"accordion-button collapsed\" type=\"button\" data-bs-toggle=\"collapse\"\n                      [attr.data-bs-target]=\"'#collapse' + i\" [attr.aria-expanded]=\"i === 0\" [attr.aria-controls]=\"'collapse' + i\">\n                <strong>{{ entity.name }}</strong>\n                <span class=\"badge bg-primary rounded-pill ms-2\">{{ entity.tasks.length }} tâches</span>\n              </button>\n            </h2>\n            <div [id]=\"'collapse' + i\" class=\"accordion-collapse collapse\" [attr.aria-labelledby]=\"'heading' + i\" data-bs-parent=\"#generatedTasksAccordion\">\n              <div class=\"accordion-body\">\n                <p class=\"text-muted mb-3\">{{ entity.description }}</p>\n\n                <div class=\"list-group\">\n                  <div *ngFor=\"let task of entity.tasks\" class=\"list-group-item list-group-item-action\">\n                    <div class=\"d-flex w-100 justify-content-between\">\n                      <h6 class=\"mb-1\">{{ task.title }}</h6>\n                      <span class=\"badge\" [ngClass]=\"{\n                        'bg-danger': task.priority === 'high',\n                        'bg-warning text-dark': task.priority === 'medium',\n                        'bg-info text-dark': task.priority === 'low'\n                      }\">\n                        {{ task.priority === 'high' ? 'Haute' :\n                           task.priority === 'medium' ? 'Moyenne' : 'Basse' }}\n                      </span>\n                    </div>\n                    <p class=\"mb-1 small\">{{ task.description }}</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Bouton de création de tâches amélioré -->\n        <div class=\"mt-5\">\n          <div class=\"card border-0 rounded-4 shadow-sm create-tasks-card\">\n            <div class=\"card-body p-4\">\n              <div class=\"row align-items-center\">\n                <div class=\"col-lg-8\">\n                  <h5 class=\"mb-3 text-success\">\n                    <i class=\"bi bi-check-circle-fill me-2\"></i>\n                    Plan de projet prêt à être implémenté\n                  </h5>\n                  <div class=\"d-flex mb-3\">\n                    <div class=\"step-circle bg-success text-white me-3\">1</div>\n                    <div>\n                      <h6 class=\"mb-1\">Création des tâches</h6>\n                      <p class=\"text-muted mb-0 small\">{{ countTasks(generatedContent) }} tâches seront créées dans le système</p>\n                    </div>\n                  </div>\n                  <div class=\"d-flex mb-3\">\n                    <div class=\"step-circle bg-primary text-white me-3\">2</div>\n                    <div>\n                      <h6 class=\"mb-1\">Assignation aux membres</h6>\n                      <p class=\"text-muted mb-0 small\">Les tâches seront assignées aux {{ team && team.members ? team.members.length : 0 }} membres de l'équipe</p>\n                    </div>\n                  </div>\n                  <div class=\"d-flex\">\n                    <div class=\"step-circle bg-info text-white me-3\">3</div>\n                    <div>\n                      <h6 class=\"mb-1\">Suivi du projet</h6>\n                      <p class=\"text-muted mb-0 small\">Vous pourrez suivre l'avancement dans le tableau de bord des tâches</p>\n                    </div>\n                  </div>\n                </div>\n                <div class=\"col-lg-4 text-center mt-4 mt-lg-0\">\n                  <button class=\"btn btn-success btn-lg rounded-pill px-5 py-3 shadow create-button\" (click)=\"createTasks()\">\n                    <i class=\"bi bi-plus-circle-fill me-2\"></i> Créer les tâches\n                  </button>\n                  <div class=\"text-muted small mt-2\">\n                    <i class=\"bi bi-info-circle me-1\"></i>\n                    Cette action est irréversible\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Message d'erreur -->\n      <div *ngIf=\"error\" class=\"alert alert-danger m-3\">\n        {{ error }}\n      </div>\n\n      <!-- Entrée utilisateur améliorée -->\n      <div class=\"chat-input p-3 border-top\">\n        <div *ngIf=\"!generatedContent\" class=\"mb-4\">\n          <div class=\"card border-0 bg-light rounded-4 shadow-sm mb-3\">\n            <div class=\"card-body p-3\">\n              <h6 class=\"mb-3 d-flex align-items-center\">\n                <i class=\"bi bi-stars me-2 text-primary\"></i>\n                Générer des tâches avec l'IA\n              </h6>\n\n              <div class=\"mb-3\">\n                <label for=\"projectTitle\" class=\"form-label small text-muted\">Entrez le titre de votre projet</label>\n                <div class=\"input-group\">\n                  <span class=\"input-group-text bg-white border-0\">\n                    <i class=\"bi bi-lightbulb text-primary\"></i>\n                  </span>\n                  <input type=\"text\" id=\"projectTitle\" class=\"form-control border-0 bg-white shadow-none\"\n                         placeholder=\"Ex: Site e-commerce, Application mobile, Système de gestion...\"\n                         [(ngModel)]=\"projectTitle\" [disabled]=\"isGenerating\">\n                </div>\n                <small class=\"text-muted mt-1 d-block\">\n                  <i class=\"bi bi-info-circle me-1\"></i>\n                  L'IA générera {{ team && team.members ? team.members.length : 3 }} modules, un pour chaque membre de l'équipe.\n                </small>\n              </div>\n\n              <div class=\"d-grid\">\n                <button class=\"btn btn-primary rounded-3\"\n                        (click)=\"generateTasks()\"\n                        [disabled]=\"isGenerating || !projectTitle.trim()\">\n                  <i class=\"bi\" [ngClass]=\"isGenerating ? 'bi-hourglass-split spin' : 'bi-magic'\"></i>\n                  {{ isGenerating ? 'Génération en cours...' : 'Générer des tâches' }}\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"card border-0 bg-light rounded-4 shadow-sm\">\n          <div class=\"card-body p-2\">\n            <div class=\"input-group\">\n              <span class=\"input-group-text bg-white border-0\">\n                <i class=\"bi bi-chat-dots text-primary\"></i>\n              </span>\n              <input type=\"text\" class=\"form-control border-0 bg-white shadow-none\"\n                     placeholder=\"Posez une question sur la gestion de projet...\"\n                     [(ngModel)]=\"userQuestion\" (keyup.enter)=\"askQuestion()\"\n                     [disabled]=\"isAskingQuestion\">\n              <button class=\"btn btn-primary rounded-circle\" style=\"width: 38px; height: 38px;\"\n                      (click)=\"askQuestion()\" [disabled]=\"isAskingQuestion || !userQuestion.trim()\">\n                <i class=\"bi\" [ngClass]=\"isAskingQuestion ? 'bi-hourglass-split spin' : 'bi-send-fill'\"></i>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n", "import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { EquipeService } from 'src/app/services/equipe.service';\nimport { AuthService } from 'src/app/services/auth.service';\nimport { Equipe } from 'src/app/models/equipe.model';\nimport { Membre } from 'src/app/models/membre.model';\nimport { User } from 'src/app/models/user.model';\n@Component({\n  selector: 'app-equipe-detail',\n  templateUrl: './equipe-detail.component.html',\n  styleUrls: ['./equipe-detail.component.css'],\n})\nexport class EquipeDetailComponent implements OnInit {\n  equipe: Equipe | null = null;\n  loading = false;\n  error: string | null = null;\n  equipeId: string | null = null;\n  newMembre: any = { id: '', role: 'membre' };\n  availableUsers: User[] = [];\n  memberNames: { [key: string]: string } = {}; // Map pour stocker les noms des membres\n  teamMembers: any[] = []; // Liste des membres de l'équipe avec leurs détails\n\n  constructor(\n    private equipeService: EquipeService,\n    private userService: AuthService, // TODO: Will be used when implementing real user API calls\n    private route: ActivatedRoute,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.equipeId = this.route.snapshot.paramMap.get('id');\n\n    // Charger tous les utilisateurs disponibles\n    this.loadUsers();\n\n    if (this.equipeId) {\n      this.loadEquipe(this.equipeId);\n    } else {\n      this.error = \"ID d'équipe non spécifié\";\n    }\n  }\n\n  // Méthode pour charger tous les utilisateurs\n  loadUsers(): void {\n    // TODO: Implémenter l'API pour récupérer les utilisateurs\n    // Pour l'instant, utiliser des données mockées\n    const mockUsers: User[] = [\n      {\n        _id: 'user1',\n        username: 'john_doe',\n        email: '<EMAIL>',\n        role: 'admin',\n        isActive: true,\n      },\n      {\n        _id: 'user2',\n        username: 'jane_smith',\n        email: '<EMAIL>',\n        role: 'student',\n        isActive: true,\n      },\n      {\n        _id: 'user3',\n        username: 'bob_wilson',\n        email: '<EMAIL>',\n        role: 'teacher',\n        isActive: true,\n      },\n    ];\n\n    // Simuler un délai d'API\n    setTimeout(() => {\n      // Stocker tous les utilisateurs pour la recherche de noms\n      const allUsers = [...mockUsers];\n      console.log('Tous les utilisateurs chargés (mock):', allUsers);\n\n      // Filtrer les utilisateurs disponibles (non membres de l'équipe)\n      if (this.teamMembers && this.teamMembers.length > 0) {\n        const memberUserIds = this.teamMembers.map((m) => m.user);\n        this.availableUsers = mockUsers.filter(\n          (user) => !memberUserIds.includes(user._id || user.id || '')\n        );\n      } else {\n        this.availableUsers = mockUsers;\n      }\n\n      console.log('Utilisateurs disponibles:', this.availableUsers);\n\n      // Si l'équipe est déjà chargée, mettre à jour les noms des membres\n      if (this.equipe && this.equipe.members) {\n        this.updateMemberNames();\n      }\n    }, 500);\n  }\n\n  // Méthode pour mettre à jour les noms des membres\n  updateMemberNames(): void {\n    if (!this.equipe || !this.equipe.members) return;\n\n    this.equipe.members.forEach((membreId) => {\n      const user = this.availableUsers.find(\n        (u) => u._id === membreId || u.id === membreId\n      );\n      if (user && user.name) {\n        this.memberNames[membreId] = user.name;\n      } else {\n        // Si l'utilisateur n'est pas trouvé dans la liste, essayer de le récupérer individuellement\n        // TODO: Implémenter getUser dans AuthuserService\n        // Pour l'instant, utiliser l'ID comme nom par défaut\n        this.memberNames[membreId] = membreId;\n      }\n    });\n  }\n\n  // Méthode pour obtenir le nom d'un membre\n  getMembreName(membreId: string): string {\n    return this.memberNames[membreId] || membreId;\n  }\n\n  // Méthode pour obtenir le nom d'un utilisateur à partir de son ID\n  getUserName(userId: string | undefined): string {\n    if (!userId) {\n      return 'Non défini';\n    }\n\n    const user = this.availableUsers.find(\n      (u) => u._id === userId || u.id === userId\n    );\n    if (user) {\n      if (user.firstName && user.lastName) {\n        return `${user.firstName} ${user.lastName}`;\n      } else if (user.name) {\n        return user.name;\n      }\n    }\n    return userId;\n  }\n\n  // Méthode pour obtenir la profession d'un utilisateur à partir de son ID\n  getUserProfession(userId: string | undefined): string {\n    if (!userId) {\n      return '';\n    }\n\n    const user = this.availableUsers.find(\n      (u) => u._id === userId || u.id === userId\n    );\n    if (user) {\n      return user.profession || user.role || '';\n    }\n    return '';\n  }\n\n  loadEquipe(id: string): void {\n    this.loading = true;\n    this.error = null;\n\n    this.equipeService.getEquipe(id).subscribe({\n      next: (data) => {\n        console.log(\"Détails de l'équipe chargés:\", data);\n        this.equipe = data;\n\n        // Charger les détails des membres de l'équipe\n        this.loadTeamMembers(id);\n\n        // Mettre à jour les noms des membres\n        if (\n          this.equipe &&\n          this.equipe.members &&\n          this.equipe.members.length > 0\n        ) {\n          this.updateMemberNames();\n        }\n\n        this.loading = false;\n      },\n      error: (error) => {\n        console.error(\n          \"Erreur lors du chargement des détails de l'équipe:\",\n          error\n        );\n        this.error =\n          \"Impossible de charger les détails de l'équipe. Veuillez réessayer plus tard.\";\n        this.loading = false;\n      },\n    });\n  }\n\n  // Méthode pour charger les détails des membres de l'équipe\n  loadTeamMembers(teamId: string): void {\n    this.equipeService.getTeamMembers(teamId).subscribe({\n      next: (members) => {\n        console.log('Détails des membres chargés:', members);\n        this.teamMembers = members;\n      },\n      error: (error) => {\n        console.error(\n          'Erreur lors du chargement des détails des membres:',\n          error\n        );\n      },\n    });\n  }\n\n  navigateToEditEquipe(): void {\n    if (this.equipeId) {\n      this.router.navigate(['/equipes/modifier', this.equipeId]);\n    }\n  }\n\n  navigateToEquipeList(): void {\n    this.router.navigate(['/equipes/liste']);\n  }\n\n  navigateToTasks(): void {\n    if (this.equipeId) {\n      this.router.navigate(['/equipes/tasks', this.equipeId]);\n    }\n  }\n\n  // Méthode pour formater les dates\n  formatDate(date: Date | string | undefined): string {\n    if (!date) {\n      return 'N/A';\n    }\n\n    try {\n      let dateObj: Date;\n\n      if (typeof date === 'string') {\n        dateObj = new Date(date);\n      } else {\n        dateObj = date;\n      }\n\n      if (isNaN(dateObj.getTime())) {\n        return 'Date invalide';\n      }\n\n      // Format: JJ/MM/AAAA\n      return dateObj.toLocaleDateString('fr-FR', {\n        day: '2-digit',\n        month: '2-digit',\n        year: 'numeric',\n      });\n    } catch (error) {\n      console.error('Erreur lors du formatage de la date:', error);\n      return 'Erreur de date';\n    }\n  }\n\n  // Méthode pour ajouter un membre à l'équipe\n  addMembre(userId: string, role: string): void {\n    console.log(`Ajout de l'utilisateur ${userId} avec le rôle ${role}`);\n\n    if (!this.equipeId || !userId) {\n      console.error(\"ID d'équipe ou ID d'utilisateur manquant\");\n      this.error = \"ID d'équipe ou ID d'utilisateur manquant\";\n      return;\n    }\n\n    // Vérifier si l'utilisateur est déjà membre de l'équipe\n    const isAlreadyMember = this.teamMembers.some((m) => m.user === userId);\n    if (isAlreadyMember) {\n      this.error = \"Cet utilisateur est déjà membre de l'équipe\";\n      alert(\"Cet utilisateur est déjà membre de l'équipe\");\n      return;\n    }\n\n    // Créer l'objet membre avec le rôle spécifié\n    const membre: Membre = {\n      id: userId,\n      role: role || 'membre',\n    };\n\n    // Récupérer les informations de l'utilisateur pour afficher un message plus informatif\n    const userName = this.getUserName(userId);\n    const roleName = role === 'admin' ? 'administrateur' : 'membre';\n\n    this.equipeService.addMembreToEquipe(this.equipeId, membre).subscribe({\n      next: (response) => {\n        console.log(\n          `Utilisateur \"${userName}\" ajouté comme ${roleName} avec succès:`,\n          response\n        );\n\n        // Afficher un message de succès\n        alert(`Utilisateur \"${userName}\" ajouté comme ${roleName} avec succès`);\n\n        // Recharger les membres de l'équipe\n        this.loadTeamMembers(this.equipeId!);\n\n        // Recharger l'équipe pour mettre à jour la liste des membres\n        this.loadEquipe(this.equipeId!);\n\n        // Mettre à jour la liste des utilisateurs disponibles\n        this.updateAvailableUsers();\n      },\n      error: (error) => {\n        console.error(\n          \"Erreur lors de l'ajout de l'utilisateur comme membre:\",\n          error\n        );\n        this.error = `Impossible d'ajouter l'utilisateur \"${userName}\" comme ${roleName}. Veuillez réessayer plus tard.`;\n        alert(this.error);\n      },\n    });\n  }\n\n  // Méthode pour mettre à jour la liste des utilisateurs disponibles\n  updateAvailableUsers(): void {\n    // TODO: Implémenter l'API pour récupérer les utilisateurs\n    // Pour l'instant, utiliser les données mockées de loadUsers()\n    this.loadUsers();\n  }\n\n  // Ancienne méthode maintenue pour compatibilité\n  addMembreToEquipe(): void {\n    if (!this.equipeId || !this.newMembre.id) {\n      console.error(\"ID d'équipe ou ID de membre manquant\");\n      return;\n    }\n\n    this.addMembre(this.newMembre.id, this.newMembre.role || 'membre');\n  }\n\n  removeMembreFromEquipe(membreId: string): void {\n    console.log('Méthode removeMembreFromEquipe appelée avec ID:', membreId);\n\n    if (!this.equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de retirer le membre.\";\n      return;\n    }\n\n    // Dans notre implémentation actuelle, membre._id est l'ID de l'utilisateur\n    const userId = membreId;\n\n    // Récupérer le nom de l'utilisateur pour un message plus informatif\n    const userName = this.getUserName(userId);\n\n    console.log(\n      `Tentative de retrait de l'utilisateur ${userId} (${userName}) de l'équipe ${this.equipeId}`\n    );\n\n    if (\n      confirm(\n        `Êtes-vous sûr de vouloir retirer l'utilisateur \"${userName}\" de l'équipe?`\n      )\n    ) {\n      console.log('Confirmation acceptée, suppression en cours...');\n\n      this.loading = true;\n      this.error = null;\n\n      this.equipeService\n        .removeMembreFromEquipe(this.equipeId, userId)\n        .subscribe({\n          next: (response) => {\n            console.log(\n              `Utilisateur \"${userName}\" retiré avec succès de l'équipe:`,\n              response\n            );\n            this.loading = false;\n\n            // Afficher un message de succès\n            alert(`Utilisateur \"${userName}\" retiré avec succès de l'équipe`);\n\n            // Recharger les membres de l'équipe\n            this.loadTeamMembers(this.equipeId!);\n\n            // Recharger l'équipe pour mettre à jour la liste des membres\n            this.loadEquipe(this.equipeId!);\n\n            // Mettre à jour la liste des utilisateurs disponibles\n            this.updateAvailableUsers();\n          },\n          error: (error) => {\n            console.error(\n              `Erreur lors du retrait de l'utilisateur \"${userName}\":`,\n              error\n            );\n            this.loading = false;\n            this.error = `Impossible de retirer l'utilisateur \"${userName}\" de l'équipe: ${\n              error.message || 'Erreur inconnue'\n            }`;\n          },\n        });\n    } else {\n      console.log(\"Suppression annulée par l'utilisateur\");\n    }\n  }\n\n  deleteEquipe(): void {\n    console.log('Méthode deleteEquipe appelée');\n\n    if (!this.equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de supprimer l'équipe.\";\n      return;\n    }\n\n    console.log(\"ID de l'équipe à supprimer:\", this.equipeId);\n\n    if (\n      confirm(\n        `Êtes-vous sûr de vouloir supprimer l'équipe \"${this.equipe?.name}\"? Cette action est irréversible.`\n      )\n    ) {\n      console.log('Confirmation acceptée, suppression en cours...');\n\n      this.loading = true;\n      this.error = null;\n\n      this.equipeService.deleteEquipe(this.equipeId).subscribe({\n        next: () => {\n          console.log('Équipe supprimée avec succès');\n          this.loading = false;\n          alert('Équipe supprimée avec succès');\n          this.router.navigate(['/equipes/liste']);\n        },\n        error: (error) => {\n          console.error(\"Erreur lors de la suppression de l'équipe:\", error);\n          this.loading = false;\n          this.error = `Impossible de supprimer l'équipe: ${\n            error.message || 'Erreur inconnue'\n          }`;\n          alert(`Erreur lors de la suppression: ${this.error}`);\n        },\n      });\n    } else {\n      console.log(\"Suppression annulée par l'utilisateur\");\n    }\n  }\n}\n", "<div\n  class=\"min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] relative overflow-hidden\"\n  *ngIf=\"equipe\"\n>\n  <!-- Background decorative elements -->\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n    <div\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"\n    ></div>\n    <div\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"\n    ></div>\n\n    <!-- Grid pattern -->\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\n      <div class=\"h-full grid grid-cols-12\">\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n      </div>\n    </div>\n  </div>\n\n  <div class=\"max-w-7xl mx-auto p-6 relative z-10\">\n    <!-- Header futuriste -->\n    <div class=\"mb-8 relative\">\n      <div\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad]\"\n      ></div>\n      <div\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] blur-md\"\n      ></div>\n\n      <div\n        class=\"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden border border-[#4f5fad]/20 dark:border-[#00f7ff]/20\"\n      >\n        <div class=\"grid grid-cols-1 lg:grid-cols-3 gap-0\">\n          <!-- Bannière avec titre -->\n          <div\n            class=\"lg:col-span-2 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] p-6\"\n          >\n            <h1 class=\"text-3xl font-bold text-white mb-2 tracking-wide\">\n              {{ equipe.name }}\n            </h1>\n            <p class=\"text-white/80 text-sm mb-6\">\n              Gestion et collaboration d'équipe - Administration\n            </p>\n\n            <!-- Statistiques rapides -->\n            <div class=\"grid grid-cols-3 gap-4\">\n              <div\n                class=\"bg-white/20 backdrop-blur-sm rounded-xl p-4 text-white text-center\"\n              >\n                <i class=\"fas fa-users text-2xl mb-2 block\"></i>\n                <span class=\"text-xl font-bold block\">{{\n                  equipe.members?.length || 0\n                }}</span>\n                <small class=\"text-white/80\">Membres</small>\n              </div>\n              <div\n                class=\"bg-white/20 backdrop-blur-sm rounded-xl p-4 text-white text-center\"\n              >\n                <i class=\"fas fa-tasks text-2xl mb-2 block\"></i>\n                <span class=\"text-xl font-bold block\">0</span>\n                <small class=\"text-white/80\">Tâches</small>\n              </div>\n              <div\n                class=\"bg-white/20 backdrop-blur-sm rounded-xl p-4 text-white text-center\"\n              >\n                <i class=\"fas fa-calendar-check text-2xl mb-2 block\"></i>\n                <span class=\"text-sm font-bold block\">{{\n                  formatDate(equipe.createdAt)\n                }}</span>\n                <small class=\"text-white/80\">Créée le</small>\n              </div>\n            </div>\n          </div>\n\n          <!-- Actions rapides -->\n          <div\n            class=\"bg-white dark:bg-[#1a1a1a] p-6 flex flex-col justify-center\"\n          >\n            <h4\n              class=\"text-lg font-bold text-[#4f5fad] dark:text-[#00f7ff] mb-4 flex items-center\"\n            >\n              <i class=\"fas fa-bolt mr-2\"></i>Actions rapides\n            </h4>\n            <div class=\"space-y-3\">\n              <button\n                (click)=\"navigateToTasks()\"\n                class=\"w-full bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] text-white px-4 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 shadow-lg\"\n              >\n                <i class=\"fas fa-tasks mr-2\"></i> Gérer les tâches\n              </button>\n              <button\n                (click)=\"navigateToEditEquipe()\"\n                class=\"w-full bg-[#4f5fad]/20 dark:bg-[#00f7ff]/20 text-[#4f5fad] dark:text-[#00f7ff] px-4 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105\"\n              >\n                <i class=\"fas fa-edit mr-2\"></i> Modifier l'équipe\n              </button>\n              <div class=\"grid grid-cols-2 gap-2\">\n                <button\n                  (click)=\"navigateToEquipeList()\"\n                  class=\"bg-[#6d6870]/20 dark:bg-[#a0a0a0]/20 text-[#6d6870] dark:text-[#e0e0e0] px-4 py-2 rounded-xl font-medium transition-all duration-300 hover:scale-105\"\n                >\n                  <i class=\"fas fa-arrow-left mr-1\"></i> Retour\n                </button>\n                <button\n                  (click)=\"deleteEquipe()\"\n                  class=\"bg-[#ff6b69]/20 dark:bg-[#ff3b30]/20 text-[#ff6b69] dark:text-[#ff3b30] px-4 py-2 rounded-xl font-medium transition-all duration-300 hover:scale-105\"\n                >\n                  <i class=\"fas fa-trash mr-1\"></i> Supprimer\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Carte d'informations de l'équipe -->\n    <div class=\"row mb-5\">\n      <div class=\"col-12\">\n        <div\n          class=\"card border-0 shadow-sm rounded-4 overflow-hidden hover-card\"\n        >\n          <div class=\"card-body p-0\">\n            <div class=\"row g-0\">\n              <!-- Icône et titre -->\n              <div\n                class=\"col-md-3 bg-primary text-white p-4 d-flex flex-column justify-content-center align-items-center text-center\"\n              >\n                <div class=\"icon-circle bg-white text-primary mb-3\">\n                  <i class=\"bi bi-info-circle-fill fs-1\"></i>\n                </div>\n                <h3 class=\"mb-2\">À propos</h3>\n                <p class=\"mb-0 text-white-50\">\n                  Détails et informations sur l'équipe\n                </p>\n              </div>\n\n              <!-- Contenu -->\n              <div class=\"col-md-9 p-4\">\n                <div\n                  class=\"d-flex justify-content-between align-items-center mb-4\"\n                >\n                  <h4 class=\"text-primary mb-0\">Description</h4>\n                  <span\n                    class=\"badge bg-light text-primary rounded-pill px-3 py-2\"\n                  >\n                    <i class=\"bi bi-person-fill-gear me-1\"></i>\n                    Admin:\n                    {{\n                      equipe.admin\n                        ? getUserName(equipe.admin) || equipe.admin\n                        : \"Non défini\"\n                    }}\n                  </span>\n                </div>\n\n                <div class=\"description-box p-3 bg-light rounded-4 mb-4\">\n                  <p class=\"lead mb-0\">\n                    {{\n                      equipe.description ||\n                        \"Aucune description disponible pour cette équipe.\"\n                    }}\n                  </p>\n                </div>\n\n                <!-- Tags et informations supplémentaires -->\n                <div class=\"d-flex flex-wrap gap-2 mt-4\">\n                  <span\n                    class=\"badge bg-primary bg-opacity-10 text-primary rounded-pill px-3 py-2\"\n                  >\n                    <i class=\"bi bi-people-fill me-1\"></i>\n                    {{ equipe.members?.length || 0 }} membres\n                  </span>\n                  <span\n                    class=\"badge bg-success bg-opacity-10 text-success rounded-pill px-3 py-2\"\n                  >\n                    <i class=\"bi bi-calendar-check me-1\"></i>\n                    Créée le {{ formatDate(equipe.createdAt) }}\n                  </span>\n                  <span\n                    class=\"badge bg-info bg-opacity-10 text-info rounded-pill px-3 py-2\"\n                  >\n                    <i class=\"bi bi-kanban me-1\"></i>\n                    Gestion de projet\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Assistant IA pour la gestion de projet (pleine largeur) -->\n    <div class=\"row mb-5\">\n      <div class=\"col-12\">\n        <div\n          class=\"card border-0 shadow-sm rounded-4 overflow-hidden hover-card\"\n        >\n          <div\n            class=\"card-header border-0 py-4\"\n            style=\"background: linear-gradient(45deg, #8e2de2, #4a00e0)\"\n          >\n            <div class=\"d-flex justify-content-between align-items-center\">\n              <h3 class=\"mb-0 text-white d-flex align-items-center\">\n                <div class=\"icon-circle bg-white text-primary me-3\">\n                  <i class=\"bi bi-robot\"></i>\n                </div>\n                Assistant IA Gemini\n              </h3>\n              <span class=\"badge bg-white text-primary rounded-pill px-3 py-2\">\n                <i class=\"bi bi-magic me-1\"></i>\n                Génération de tâches intelligente\n              </span>\n            </div>\n          </div>\n          <div class=\"card-body p-0\">\n            <app-ai-chat [team]=\"equipe\"></app-ai-chat>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Section des membres de l'équipe -->\n    <div class=\"row mb-5\">\n      <div class=\"col-12\">\n        <div\n          class=\"card border-0 shadow-sm rounded-4 overflow-hidden hover-card\"\n        >\n          <div\n            class=\"card-header border-0 py-4 d-flex justify-content-between align-items-center\"\n            style=\"background: linear-gradient(45deg, #11998e, #38ef7d)\"\n          >\n            <h3 class=\"mb-0 text-white d-flex align-items-center\">\n              <div class=\"icon-circle bg-white text-success me-3\">\n                <i class=\"bi bi-people-fill\"></i>\n              </div>\n              Membres de l'équipe\n            </h3>\n            <span class=\"badge bg-white text-success rounded-pill px-3 py-2\">\n              {{ teamMembers.length || 0 }} membres\n            </span>\n          </div>\n\n          <div class=\"card-body p-0\">\n            <!-- Liste des membres -->\n            <div\n              *ngIf=\"teamMembers && teamMembers.length > 0; else noMembers\"\n              class=\"p-0\"\n            >\n              <div class=\"row g-0\">\n                <div class=\"col-md-8\">\n                  <div class=\"member-grid p-4\">\n                    <div\n                      *ngFor=\"let membre of teamMembers\"\n                      class=\"member-card mb-3 p-3 rounded-4 shadow-sm transition\"\n                    >\n                      <div\n                        class=\"d-flex justify-content-between align-items-start\"\n                      >\n                        <!-- Informations du membre -->\n                        <div class=\"d-flex align-items-center\">\n                          <div\n                            class=\"member-avatar rounded-circle text-white me-3\"\n                            [ngClass]=\"{\n                              'bg-primary':\n                                getUserProfession(membre.user) === 'etudiant',\n                              'bg-success':\n                                getUserProfession(membre.user) === 'professeur',\n                              'bg-secondary': !getUserProfession(membre.user)\n                            }\"\n                          >\n                            <i\n                              class=\"bi\"\n                              [ngClass]=\"{\n                                'bi-mortarboard-fill':\n                                  getUserProfession(membre.user) === 'etudiant',\n                                'bi-briefcase-fill':\n                                  getUserProfession(membre.user) ===\n                                  'professeur',\n                                'bi-person-fill': !getUserProfession(\n                                  membre.user\n                                )\n                              }\"\n                            ></i>\n                          </div>\n                          <div>\n                            <h6 class=\"mb-0 fw-bold\">\n                              {{ getUserName(membre.user) }}\n                            </h6>\n                            <div class=\"d-flex align-items-center mt-1\">\n                              <span\n                                class=\"badge rounded-pill me-2\"\n                                [ngClass]=\"{\n                                  'bg-success bg-opacity-10 text-success':\n                                    membre.role === 'admin',\n                                  'bg-primary bg-opacity-10 text-primary':\n                                    membre.role === 'membre'\n                                }\"\n                              >\n                                <i\n                                  class=\"bi\"\n                                  [ngClass]=\"{\n                                    'bi-person-fill-gear':\n                                      membre.role === 'admin',\n                                    'bi-person': membre.role === 'membre'\n                                  }\"\n                                ></i>\n                                {{\n                                  membre.role === \"admin\"\n                                    ? \"Administrateur\"\n                                    : \"Membre\"\n                                }}\n                              </span>\n                              <small class=\"text-muted\">{{\n                                getUserProfession(membre.user) === \"etudiant\"\n                                  ? \"Étudiant\"\n                                  : getUserProfession(membre.user) ===\n                                    \"professeur\"\n                                  ? \"Professeur\"\n                                  : \"Utilisateur\"\n                              }}</small>\n                            </div>\n                          </div>\n                        </div>\n\n                        <!-- Actions -->\n                        <button\n                          class=\"btn btn-sm btn-outline-danger rounded-circle\"\n                          title=\"Retirer de l'équipe\"\n                          (click)=\"removeMembreFromEquipe(membre._id)\"\n                        >\n                          <i class=\"bi bi-trash\"></i>\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- Formulaire d'ajout de membre -->\n                <div class=\"col-md-4 bg-light p-4\">\n                  <h5 class=\"d-flex align-items-center mb-4 text-success\">\n                    <i class=\"bi bi-person-plus-fill me-2\"></i>\n                    Ajouter un membre\n                  </h5>\n\n                  <!-- Afficher un message si aucun utilisateur n'est disponible -->\n                  <div\n                    *ngIf=\"availableUsers.length === 0\"\n                    class=\"alert alert-info border-0 rounded-4 shadow-sm d-flex align-items-center\"\n                  >\n                    <i\n                      class=\"bi bi-info-circle-fill fs-4 me-3 text-primary\"\n                    ></i>\n                    <div>\n                      Aucun utilisateur disponible. Veuillez d'abord créer des\n                      utilisateurs.\n                    </div>\n                  </div>\n\n                  <!-- Formulaire d'ajout d'utilisateur avec rôle -->\n                  <div\n                    *ngIf=\"availableUsers.length > 0\"\n                    class=\"add-member-form\"\n                  >\n                    <div class=\"mb-3\">\n                      <label for=\"userSelect\" class=\"form-label fw-medium\"\n                        >Utilisateur</label\n                      >\n                      <select\n                        #userSelect\n                        id=\"userSelect\"\n                        class=\"form-select border-0 shadow-sm rounded-4 py-2\"\n                      >\n                        <option value=\"\" selected disabled>\n                          Sélectionnez un utilisateur\n                        </option>\n                        <option\n                          *ngFor=\"let user of availableUsers\"\n                          [value]=\"user._id || user.id\"\n                        >\n                          {{ user.firstName || \"\" }}\n                          {{ user.lastName || user.name || user.id }}\n                          {{ user.email ? \"- \" + user.email : \"\" }}\n                          {{\n                            user.profession\n                              ? \"(\" +\n                                (user.profession === \"etudiant\"\n                                  ? \"Étudiant\"\n                                  : \"Professeur\") +\n                                \")\"\n                              : \"\"\n                          }}\n                        </option>\n                      </select>\n                    </div>\n\n                    <div class=\"mb-3\">\n                      <label for=\"roleSelect\" class=\"form-label fw-medium\"\n                        >Rôle dans l'équipe</label\n                      >\n                      <div class=\"d-flex gap-2\">\n                        <div class=\"form-check flex-grow-1\">\n                          <input\n                            class=\"form-check-input\"\n                            type=\"radio\"\n                            name=\"roleRadio\"\n                            id=\"roleMembre\"\n                            value=\"membre\"\n                            checked\n                            #roleMembre\n                          />\n                          <label\n                            class=\"form-check-label w-100 p-2 border rounded-4 text-center\"\n                            for=\"roleMembre\"\n                          >\n                            <i class=\"bi bi-person d-block fs-4 mb-1\"></i>\n                            Membre\n                          </label>\n                        </div>\n                        <div class=\"form-check flex-grow-1\">\n                          <input\n                            class=\"form-check-input\"\n                            type=\"radio\"\n                            name=\"roleRadio\"\n                            id=\"roleAdmin\"\n                            value=\"admin\"\n                            #roleAdmin\n                          />\n                          <label\n                            class=\"form-check-label w-100 p-2 border rounded-4 text-center\"\n                            for=\"roleAdmin\"\n                          >\n                            <i\n                              class=\"bi bi-person-fill-gear d-block fs-4 mb-1\"\n                            ></i>\n                            Admin\n                          </label>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div class=\"d-grid\">\n                      <button\n                        type=\"button\"\n                        class=\"btn btn-success rounded-4 py-2 shadow-sm\"\n                        [disabled]=\"!userSelect.value\"\n                        (click)=\"\n                          addMembre(\n                            userSelect.value,\n                            roleMembre.checked ? 'membre' : 'admin'\n                          );\n                          userSelect.value = ''\n                        \"\n                      >\n                        <i class=\"bi bi-plus-circle me-2\"></i> Ajouter à\n                        l'équipe\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <ng-template #noMembers>\n              <div class=\"row g-0\">\n                <div class=\"col-md-8\">\n                  <div class=\"text-center py-5\">\n                    <div class=\"empty-state-icon mb-4\">\n                      <i class=\"bi bi-people fs-1 text-muted\"></i>\n                    </div>\n                    <h5 class=\"text-muted\">Aucun membre dans cette équipe</h5>\n                    <p class=\"text-muted\">\n                      Ajoutez des membres à l'équipe en utilisant le formulaire\n                      ci-contre.\n                    </p>\n                  </div>\n                </div>\n\n                <!-- Formulaire d'ajout de membre (même code que ci-dessus) -->\n                <div class=\"col-md-4 bg-light p-4\">\n                  <h5 class=\"d-flex align-items-center mb-4 text-success\">\n                    <i class=\"bi bi-person-plus-fill me-2\"></i>\n                    Ajouter un membre\n                  </h5>\n\n                  <!-- Afficher un message si aucun utilisateur n'est disponible -->\n                  <div\n                    *ngIf=\"availableUsers.length === 0\"\n                    class=\"alert alert-info border-0 rounded-4 shadow-sm d-flex align-items-center\"\n                  >\n                    <i\n                      class=\"bi bi-info-circle-fill fs-4 me-3 text-primary\"\n                    ></i>\n                    <div>\n                      Aucun utilisateur disponible. Veuillez d'abord créer des\n                      utilisateurs.\n                    </div>\n                  </div>\n\n                  <!-- Formulaire d'ajout d'utilisateur avec rôle -->\n                  <div\n                    *ngIf=\"availableUsers.length > 0\"\n                    class=\"add-member-form\"\n                  >\n                    <div class=\"mb-3\">\n                      <label for=\"userSelect2\" class=\"form-label fw-medium\"\n                        >Utilisateur</label\n                      >\n                      <select\n                        #userSelect2\n                        id=\"userSelect2\"\n                        class=\"form-select border-0 shadow-sm rounded-4 py-2\"\n                      >\n                        <option value=\"\" selected disabled>\n                          Sélectionnez un utilisateur\n                        </option>\n                        <option\n                          *ngFor=\"let user of availableUsers\"\n                          [value]=\"user._id || user.id\"\n                        >\n                          {{ user.firstName || \"\" }}\n                          {{ user.lastName || user.name || user.id }}\n                          {{ user.email ? \"- \" + user.email : \"\" }}\n                          {{\n                            user.profession\n                              ? \"(\" +\n                                (user.profession === \"etudiant\"\n                                  ? \"Étudiant\"\n                                  : \"Professeur\") +\n                                \")\"\n                              : \"\"\n                          }}\n                        </option>\n                      </select>\n                    </div>\n\n                    <div class=\"mb-3\">\n                      <label for=\"roleSelect2\" class=\"form-label fw-medium\"\n                        >Rôle dans l'équipe</label\n                      >\n                      <div class=\"d-flex gap-2\">\n                        <div class=\"form-check flex-grow-1\">\n                          <input\n                            class=\"form-check-input\"\n                            type=\"radio\"\n                            name=\"roleRadio2\"\n                            id=\"roleMembre2\"\n                            value=\"membre\"\n                            checked\n                            #roleMembre2\n                          />\n                          <label\n                            class=\"form-check-label w-100 p-2 border rounded-4 text-center\"\n                            for=\"roleMembre2\"\n                          >\n                            <i class=\"bi bi-person d-block fs-4 mb-1\"></i>\n                            Membre\n                          </label>\n                        </div>\n                        <div class=\"form-check flex-grow-1\">\n                          <input\n                            class=\"form-check-input\"\n                            type=\"radio\"\n                            name=\"roleRadio2\"\n                            id=\"roleAdmin2\"\n                            value=\"admin\"\n                            #roleAdmin2\n                          />\n                          <label\n                            class=\"form-check-label w-100 p-2 border rounded-4 text-center\"\n                            for=\"roleAdmin2\"\n                          >\n                            <i\n                              class=\"bi bi-person-fill-gear d-block fs-4 mb-1\"\n                            ></i>\n                            Admin\n                          </label>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div class=\"d-grid\">\n                      <button\n                        type=\"button\"\n                        class=\"btn btn-success rounded-4 py-2 shadow-sm\"\n                        [disabled]=\"!userSelect2.value\"\n                        (click)=\"\n                          addMembre(\n                            userSelect2.value,\n                            roleMembre2.checked ? 'membre' : 'admin'\n                          );\n                          userSelect2.value = ''\n                        \"\n                      >\n                        <i class=\"bi bi-plus-circle me-2\"></i> Ajouter à\n                        l'équipe\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </ng-template>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Message de chargement ou d'erreur -->\n<div class=\"container-fluid py-5 bg-light\" *ngIf=\"!equipe\">\n  <div class=\"container\">\n    <div class=\"row justify-content-center\">\n      <div class=\"col-md-8 text-center\">\n        <div\n          class=\"alert alert-warning shadow-sm border-0 rounded-3 d-flex align-items-center p-4\"\n        >\n          <i class=\"bi bi-exclamation-triangle-fill fs-1 me-4 text-warning\"></i>\n          <div class=\"fs-5\">\n            Équipe non trouvée ou en cours de chargement...\n          </div>\n        </div>\n        <button\n          class=\"btn btn-outline-primary rounded-pill mt-4\"\n          (click)=\"navigateToEquipeList()\"\n        >\n          <i class=\"bi bi-arrow-left me-2\"></i> Retour à la liste des équipes\n        </button>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Styles spécifiques pour cette page -->\n<style>\n  /* Fond dégradé pour l'en-tête du formulaire */\n  .bg-gradient-primary {\n    background: linear-gradient(45deg, #007bff, #6610f2) !important;\n  }\n\n  .bg-gradient-light {\n    background: linear-gradient(to right, #f8f9fa, #e9ecef) !important;\n  }\n\n  /* Animation au survol des éléments */\n  .transition {\n    transition: all 0.3s ease;\n  }\n\n  /* Effet de survol pour les cartes */\n  .hover-card {\n    transition: all 0.3s ease;\n  }\n\n  .hover-card:hover {\n    transform: translateY(-5px);\n    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;\n  }\n\n  /* Styles pour les cartes de membres */\n  .member-card {\n    background-color: white;\n    transition: all 0.3s ease;\n    border-left: 4px solid transparent;\n  }\n\n  .member-card:hover {\n    transform: translateY(-3px);\n    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1) !important;\n  }\n\n  /* Avatar des membres */\n  .member-avatar {\n    width: 45px;\n    height: 45px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-size: 1.2rem;\n  }\n\n  /* Icône circulaire */\n  .icon-circle {\n    width: 40px;\n    height: 40px;\n    border-radius: 50%;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-size: 1.2rem;\n  }\n\n  /* Styles pour la boîte de description */\n  .description-box {\n    border-left: 4px solid #007bff;\n  }\n\n  /* Animation pour les boutons */\n  .btn {\n    transition: all 0.3s ease;\n  }\n\n  .btn:hover {\n    transform: translateY(-2px);\n  }\n\n  /* Styles pour les badges */\n  .badge {\n    font-weight: 500;\n    letter-spacing: 0.5px;\n  }\n\n  /* Styles pour les formulaires */\n  .form-select,\n  .form-control {\n    transition: all 0.2s ease;\n  }\n\n  .form-select:focus,\n  .form-control:focus {\n    border-color: #007bff;\n    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);\n  }\n\n  /* Styles pour les états vides */\n  .empty-state-icon {\n    width: 80px;\n    height: 80px;\n    margin: 0 auto;\n    background-color: #f8f9fa;\n    border-radius: 50%;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-size: 2rem;\n    color: #adb5bd;\n  }\n\n  /* Styles pour les sélecteurs de rôle */\n  .form-check-label {\n    cursor: pointer;\n    transition: all 0.2s ease;\n  }\n\n  .form-check-input:checked + .form-check-label {\n    background-color: rgba(13, 110, 253, 0.1);\n    border-color: #007bff;\n  }\n\n  /* Styles pour les arrondis */\n  .rounded-4 {\n    border-radius: 0.75rem !important;\n  }\n\n  /* Styles pour la grille de membres */\n  .member-grid {\n    max-height: 500px;\n    overflow-y: auto;\n  }\n</style>\n", "import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { EquipeService } from 'src/app/services/equipe.service';\nimport { MembreService } from 'src/app/services/membre.service';\nimport { AuthService } from 'src/app/services/auth.service';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { Equipe } from 'src/app/models/equipe.model';\nimport { Membre } from 'src/app/models/membre.model';\nimport { User } from 'src/app/models/user.model';\n\n@Component({\n  selector: 'app-equipe-form',\n  template: `\n    <div\n      class=\"min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] relative overflow-hidden\"\n    >\n      <!-- Background decorative elements -->\n      <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        <div\n          class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#dac4ea]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"\n        ></div>\n        <div\n          class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#dac4ea]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"\n        ></div>\n\n        <!-- Grid pattern -->\n        <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\n          <div class=\"h-full grid grid-cols-12\">\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n            <div class=\"border-r border-[#dac4ea] dark:border-[#00f7ff]\"></div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"max-w-4xl mx-auto p-6 relative z-10\">\n        <!-- Header -->\n        <div class=\"mb-8 relative\">\n          <div\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea]\"\n          ></div>\n          <div\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] blur-md\"\n          ></div>\n\n          <div\n            class=\"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] p-6 backdrop-blur-sm border border-[#dac4ea]/20 dark:border-[#00f7ff]/20\"\n          >\n            <div\n              class=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\"\n            >\n              <div class=\"mb-4 lg:mb-0\">\n                <h1\n                  class=\"text-3xl font-bold text-[#dac4ea] dark:text-[#00f7ff] mb-2 tracking-wide\"\n                >\n                  {{ isEditMode ? \"Modifier l'équipe\" : 'Nouvelle équipe' }}\n                </h1>\n                <p class=\"text-[#6d6870] dark:text-[#e0e0e0] text-sm\">\n                  {{\n                    isEditMode\n                      ? 'Modifiez les informations et les membres de votre équipe'\n                      : 'Créez une nouvelle équipe pour organiser vos projets et membres'\n                  }}\n                </p>\n              </div>\n\n              <button\n                (click)=\"cancel()\"\n                class=\"bg-[#dac4ea]/20 dark:bg-[#00f7ff]/20 text-[#dac4ea] dark:text-[#00f7ff] px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 hover:bg-[#dac4ea]/30 dark:hover:bg-[#00f7ff]/30\"\n              >\n                <i class=\"fas fa-arrow-left mr-2\"></i>\n                Retour à la liste\n              </button>\n            </div>\n          </div>\n        </div>\n\n        <!-- Loading Indicator -->\n        <div\n          *ngIf=\"loading\"\n          class=\"flex flex-col items-center justify-center py-16\"\n        >\n          <div class=\"relative\">\n            <div\n              class=\"w-12 h-12 border-3 border-[#dac4ea]/20 dark:border-[#00f7ff]/20 border-t-[#dac4ea] dark:border-t-[#00f7ff] rounded-full animate-spin\"\n            ></div>\n            <div\n              class=\"absolute inset-0 bg-[#dac4ea]/20 dark:bg-[#00f7ff]/20 blur-xl rounded-full transform scale-150 -z-10\"\n            ></div>\n          </div>\n          <p\n            class=\"mt-4 text-[#dac4ea] dark:text-[#00f7ff] text-sm font-medium tracking-wide\"\n          >\n            Chargement des données...\n          </p>\n        </div>\n\n        <!-- Error Alert -->\n        <div *ngIf=\"error\" class=\"mb-6\">\n          <div\n            class=\"bg-[#ff6b69]/10 dark:bg-[#ff3b30]/10 border-l-4 border-[#ff6b69] dark:border-[#ff3b30] rounded-lg p-4 backdrop-blur-sm\"\n          >\n            <div class=\"flex items-start\">\n              <div class=\"text-[#ff6b69] dark:text-[#ff3b30] mr-3 text-xl\">\n                <i class=\"fas fa-exclamation-triangle\"></i>\n              </div>\n              <div class=\"flex-1\">\n                <h3\n                  class=\"font-semibold text-[#ff6b69] dark:text-[#ff3b30] mb-1\"\n                >\n                  Erreur\n                </h3>\n                <p class=\"text-sm text-[#6d6870] dark:text-[#e0e0e0]\">\n                  {{ error }}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Formulaire principal -->\n        <div *ngIf=\"!loading\" class=\"mb-8 relative\">\n          <div\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea]\"\n          ></div>\n          <div\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] blur-md\"\n          ></div>\n\n          <div\n            class=\"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden border border-[#dac4ea]/20 dark:border-[#00f7ff]/20\"\n          >\n            <!-- Header -->\n            <div\n              class=\"bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] p-6\"\n            >\n              <h3 class=\"text-xl font-bold text-white mb-1 flex items-center\">\n                <i\n                  class=\"fas mr-2\"\n                  [ngClass]=\"{\n                    'fa-edit': isEditMode,\n                    'fa-plus-circle': !isEditMode\n                  }\"\n                ></i>\n                {{\n                  isEditMode\n                    ? \"Informations de l'équipe\"\n                    : 'Détails de la nouvelle équipe'\n                }}\n              </h3>\n              <p class=\"text-white/80 text-sm\">\n                Remplissez les informations de base de l'équipe\n              </p>\n            </div>\n\n            <!-- Form -->\n            <div class=\"p-6\">\n              <form (ngSubmit)=\"onSubmit()\" class=\"space-y-6\">\n                <!-- Nom de l'équipe -->\n                <div>\n                  <label\n                    class=\"block text-sm font-medium text-[#dac4ea] dark:text-[#00f7ff] mb-2\"\n                  >\n                    Nom de l'équipe\n                    <span class=\"text-[#ff6b69] dark:text-[#ff3b30]\">*</span>\n                  </label>\n                  <div class=\"relative\">\n                    <div\n                      class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\n                    >\n                      <i\n                        class=\"fas fa-users text-[#dac4ea] dark:text-[#00f7ff]\"\n                      ></i>\n                    </div>\n                    <input\n                      #nameInput\n                      type=\"text\"\n                      [value]=\"equipe.name || ''\"\n                      (input)=\"updateName(nameInput.value)\"\n                      class=\"w-full pl-10 pr-4 py-3 bg-[#f0f4f8] dark:bg-[#0a0a0a] border border-[#dac4ea]/20 dark:border-[#00f7ff]/20 rounded-lg text-[#6d6870] dark:text-[#e0e0e0] placeholder-[#6d6870]/50 dark:placeholder-[#a0a0a0] focus:outline-none focus:ring-2 focus:ring-[#dac4ea] dark:focus:ring-[#00f7ff] focus:border-transparent transition-all\"\n                      placeholder=\"Entrez le nom de l'équipe\"\n                      required\n                      minlength=\"3\"\n                    />\n                  </div>\n                  <div\n                    *ngIf=\"nameExists\"\n                    class=\"mt-1 text-sm text-[#ff6b69] dark:text-[#ff3b30] flex items-center\"\n                  >\n                    <i class=\"fas fa-exclamation-triangle mr-1\"></i>\n                    Ce nom d'équipe existe déjà. Veuillez en choisir un autre.\n                  </div>\n                </div>\n\n                <!-- Description -->\n                <div>\n                  <label\n                    class=\"block text-sm font-medium text-[#dac4ea] dark:text-[#00f7ff] mb-2\"\n                  >\n                    Description\n                    <span class=\"text-[#ff6b69] dark:text-[#ff3b30]\">*</span>\n                  </label>\n                  <div class=\"relative\">\n                    <div class=\"absolute top-3 left-3 pointer-events-none\">\n                      <i\n                        class=\"fas fa-file-alt text-[#dac4ea] dark:text-[#00f7ff]\"\n                      ></i>\n                    </div>\n                    <textarea\n                      #descInput\n                      [value]=\"equipe.description || ''\"\n                      (input)=\"updateDescription(descInput.value)\"\n                      rows=\"4\"\n                      class=\"w-full pl-10 pr-4 py-3 bg-[#f0f4f8] dark:bg-[#0a0a0a] border border-[#dac4ea]/20 dark:border-[#00f7ff]/20 rounded-lg text-[#6d6870] dark:text-[#e0e0e0] placeholder-[#6d6870]/50 dark:placeholder-[#a0a0a0] focus:outline-none focus:ring-2 focus:ring-[#dac4ea] dark:focus:ring-[#00f7ff] focus:border-transparent transition-all resize-none\"\n                      placeholder=\"Décrivez l'objectif et les activités de cette équipe\"\n                      required\n                      minlength=\"10\"\n                    ></textarea>\n                  </div>\n                </div>\n\n                <!-- Admin info -->\n                <input type=\"hidden\" [value]=\"equipe.admin\" />\n                <div\n                  class=\"bg-[#dac4ea]/10 dark:bg-[#00f7ff]/10 border-l-4 border-[#dac4ea] dark:border-[#00f7ff] rounded-lg p-4\"\n                >\n                  <div class=\"flex items-center\">\n                    <div\n                      class=\"text-[#dac4ea] dark:text-[#00f7ff] mr-3 text-lg\"\n                    >\n                      <i class=\"fas fa-info-circle\"></i>\n                    </div>\n                    <div class=\"text-sm text-[#6d6870] dark:text-[#e0e0e0]\">\n                      Un administrateur par défaut sera assigné à cette équipe.\n                    </div>\n                  </div>\n                </div>\n\n                <!-- Buttons -->\n                <div class=\"flex items-center justify-between pt-4\">\n                  <div class=\"flex items-center space-x-4\">\n                    <button\n                      type=\"button\"\n                      (click)=\"cancel()\"\n                      class=\"bg-[#6d6870]/20 dark:bg-[#a0a0a0]/20 text-[#6d6870] dark:text-[#e0e0e0] px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 hover:bg-[#6d6870]/30 dark:hover:bg-[#a0a0a0]/30\"\n                    >\n                      <i class=\"fas fa-arrow-left mr-2\"></i>\n                      Retour\n                    </button>\n\n                    <button\n                      *ngIf=\"isEditMode && equipeId\"\n                      type=\"button\"\n                      (click)=\"deleteEquipe()\"\n                      class=\"bg-[#ff6b69]/20 dark:bg-[#ff3b30]/20 text-[#ff6b69] dark:text-[#ff3b30] px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 hover:bg-[#ff6b69]/30 dark:hover:bg-[#ff3b30]/30\"\n                    >\n                      <i class=\"fas fa-trash mr-2\"></i>\n                      Supprimer\n                    </button>\n                  </div>\n\n                  <button\n                    type=\"submit\"\n                    [disabled]=\"\n                      submitting ||\n                      !equipe.name ||\n                      !equipe.description ||\n                      nameExists ||\n                      nameError ||\n                      descriptionError\n                    \"\n                    class=\"relative overflow-hidden group bg-gradient-to-r from-[#dac4ea] to-[#8b5a9f] dark:from-[#00f7ff] dark:to-[#dac4ea] text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-[0_0_25px_rgba(218,196,234,0.4)] dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)] disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100\"\n                  >\n                    <span\n                      *ngIf=\"submitting\"\n                      class=\"inline-block w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2\"\n                    ></span>\n                    <i\n                      *ngIf=\"!submitting\"\n                      class=\"fas mr-2\"\n                      [ngClass]=\"{\n                        'fa-save': isEditMode,\n                        'fa-plus-circle': !isEditMode\n                      }\"\n                    ></i>\n                    {{ isEditMode ? 'Mettre à jour' : \"Créer l'équipe\" }}\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  `,\n  styleUrls: ['./equipe-form.component.css'],\n})\nexport class EquipeFormComponent implements OnInit {\n  equipe: Equipe = {\n    name: '',\n    description: '',\n    admin: '', // Sera défini avec l'ID de l'utilisateur connecté\n  };\n  isEditMode = false;\n  loading = false;\n  submitting = false;\n  error: string | null = null;\n  equipeId: string | null = null;\n  nameExists = false;\n  nameError = false;\n  descriptionError = false;\n  checkingName = false;\n  existingEquipes: Equipe[] = [];\n  availableMembers: Membre[] = []; // Liste des membres disponibles\n  availableUsers: User[] = []; // Liste des utilisateurs disponibles\n  currentUserId: string | null = null; // ID de l'utilisateur connecté\n\n  constructor(\n    private equipeService: EquipeService,\n    private membreService: MembreService,\n    private userService: AuthService,\n    private route: ActivatedRoute,\n    private router: Router,\n    private notificationService: NotificationService\n  ) {}\n\n  ngOnInit(): void {\n    console.log('EquipeFormComponent initialized');\n\n    // Récupérer l'ID de l'utilisateur connecté\n    this.getCurrentUser();\n\n    // Charger toutes les équipes pour vérifier les noms existants\n    this.loadAllEquipes();\n\n    // Charger tous les membres disponibles\n    this.loadAllMembers();\n\n    // Charger tous les utilisateurs disponibles\n    this.loadAllUsers();\n\n    try {\n      // Vérifier si nous sommes en mode édition (si un ID est présent dans l'URL)\n      this.equipeId = this.route.snapshot.paramMap.get('id');\n      this.isEditMode = !!this.equipeId;\n      console.log('Edit mode:', this.isEditMode, 'ID:', this.equipeId);\n\n      if (this.isEditMode && this.equipeId) {\n        this.loadEquipe(this.equipeId);\n\n        // Ajouter un délai pour s'assurer que l'équipe est chargée\n        setTimeout(() => {\n          console.log('Après délai - this.equipeId:', this.equipeId);\n          console.log('Après délai - this.equipe:', this.equipe);\n        }, 1000);\n      }\n    } catch (error) {\n      console.error('Error in ngOnInit:', error);\n      this.error = \"Erreur d'initialisation\";\n    }\n\n    // Ajouter un gestionnaire d'événements pour le bouton d'ajout de membre\n    setTimeout(() => {\n      const addButton = document.getElementById('addMembreButton');\n      if (addButton) {\n        console.log(\"Bouton d'ajout de membre trouvé\");\n        addButton.addEventListener('click', () => {\n          console.log(\"Bouton d'ajout de membre cliqué\");\n        });\n      } else {\n        console.log(\"Bouton d'ajout de membre non trouvé\");\n      }\n    }, 2000);\n  }\n\n  getCurrentUser(): void {\n    const token = localStorage.getItem('token');\n    if (token) {\n      this.userService.getProfile(token).subscribe({\n        next: (user: any) => {\n          console.log('Utilisateur connecté:', user);\n          this.currentUserId = user._id || user.id;\n\n          // Définir l'admin de l'équipe avec l'ID de l'utilisateur connecté\n          if (!this.isEditMode && this.currentUserId) {\n            this.equipe.admin = this.currentUserId;\n            console.log(\n              'Admin défini pour nouvelle équipe:',\n              this.equipe.admin\n            );\n          }\n        },\n        error: (error) => {\n          console.error(\n            'Erreur lors de la récupération du profil utilisateur:',\n            error\n          );\n          this.error =\n            \"Impossible de récupérer les informations de l'utilisateur connecté.\";\n        },\n      });\n    } else {\n      this.error =\n        \"Aucun token d'authentification trouvé. Veuillez vous reconnecter.\";\n    }\n  }\n\n  loadAllMembers(): void {\n    this.membreService.getMembres().subscribe({\n      next: (membres) => {\n        this.availableMembers = membres;\n        console.log('Membres disponibles chargés:', membres);\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement des membres:', error);\n        this.error =\n          'Impossible de charger la liste des membres. Veuillez réessayer plus tard.';\n      },\n    });\n  }\n\n  loadAllUsers(): void {\n    const token = localStorage.getItem('token');\n    if (token) {\n      this.userService.getAllUsers(token).subscribe({\n        next: (users: any) => {\n          this.availableUsers = users;\n          console.log('Utilisateurs disponibles chargés:', users);\n        },\n        error: (error) => {\n          console.error('Erreur lors du chargement des utilisateurs:', error);\n          this.error =\n            'Impossible de charger la liste des utilisateurs. Veuillez réessayer plus tard.';\n        },\n      });\n    }\n  }\n\n  loadAllEquipes(): void {\n    this.equipeService.getEquipes().subscribe({\n      next: (equipes) => {\n        this.existingEquipes = equipes;\n        console.log('Équipes existantes chargées:', equipes);\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement des équipes:', error);\n      },\n    });\n  }\n\n  loadEquipe(id: string): void {\n    console.log('Loading equipe with ID:', id);\n    this.loading = true;\n    this.error = null;\n\n    this.equipeService.getEquipe(id).subscribe({\n      next: (data) => {\n        console.log('Équipe chargée:', data);\n        this.equipe = data;\n\n        // Vérifier que l'ID est correctement défini\n        console.log(\"ID de l'équipe après chargement:\", this.equipe._id);\n        console.log('this.equipeId:', this.equipeId);\n\n        // Si l'équipe a des membres, récupérer les informations de chaque membre\n        if (this.equipe.members && this.equipe.members.length > 0) {\n          this.loadMembersDetails();\n        }\n\n        this.loading = false;\n      },\n      error: (error) => {\n        console.error(\"Erreur lors du chargement de l'équipe:\", error);\n        this.error =\n          \"Impossible de charger les détails de l'équipe. Veuillez réessayer plus tard.\";\n        this.loading = false;\n      },\n    });\n  }\n\n  // Méthode pour récupérer les détails des membres de l'équipe\n  loadMembersDetails(): void {\n    if (!this.equipe.members || this.equipe.members.length === 0) {\n      return;\n    }\n\n    console.log(\"Chargement des détails des membres de l'équipe...\");\n\n    // Pour chaque membre de l'équipe, essayer de trouver ses informations dans la liste des utilisateurs\n    this.equipe.members.forEach((membreId) => {\n      // Chercher d'abord dans la liste des utilisateurs\n      const user = this.availableUsers.find(\n        (u) => u._id === membreId || u.id === membreId\n      );\n      if (user) {\n        console.log(\n          `Membre ${membreId} trouvé dans la liste des utilisateurs:`,\n          user\n        );\n\n        // Vérifier si toutes les informations nécessaires sont présentes\n        if (!user.email || (!user.profession && !user.role)) {\n          // Si des informations manquent, essayer de les récupérer depuis l'API\n          const token = localStorage.getItem('token');\n          if (token) {\n            this.userService.getUserById(membreId, token).subscribe({\n              next: (userData: any) => {\n                console.log(\n                  `Détails supplémentaires de l'utilisateur ${membreId} récupérés:`,\n                  userData\n                );\n\n                // Mettre à jour l'utilisateur dans la liste avec les nouvelles informations\n                const index = this.availableUsers.findIndex(\n                  (u) => u._id === membreId || u.id === membreId\n                );\n                if (index !== -1) {\n                  this.availableUsers[index] = {\n                    ...this.availableUsers[index],\n                    ...userData,\n                  };\n                }\n              },\n              error: (error) => {\n                console.error(\n                  `Erreur lors de la récupération des détails supplémentaires de l'utilisateur ${membreId}:`,\n                  error\n                );\n              },\n            });\n          }\n        }\n      } else {\n        // Si non trouvé, essayer de récupérer l'utilisateur depuis l'API\n        const token = localStorage.getItem('token');\n        if (token) {\n          this.userService.getUserById(membreId, token).subscribe({\n            next: (userData: any) => {\n              console.log(\n                `Détails de l'utilisateur ${membreId} récupérés:`,\n                userData\n              );\n              // Ajouter l'utilisateur à la liste des utilisateurs disponibles s'il n'y est pas déjà\n              if (\n                !this.availableUsers.some(\n                  (u) => u._id === userData._id || u.id === userData.id\n                )\n              ) {\n                this.availableUsers.push(userData);\n              }\n            },\n            error: (error) => {\n              console.error(\n                `Erreur lors de la récupération des détails de l'utilisateur ${membreId}:`,\n                error\n              );\n            },\n          });\n        }\n      }\n    });\n  }\n\n  checkNameExists(name: string): boolean {\n    // En mode édition, ignorer l'équipe actuelle\n    if (this.isEditMode && this.equipeId) {\n      return this.existingEquipes.some(\n        (e) => e.name === name && e._id !== this.equipeId\n      );\n    }\n    // En mode création, vérifier tous les noms\n    return this.existingEquipes.some((e) => e.name === name);\n  }\n\n  updateName(value: string): void {\n    console.log('Name updated:', value);\n    this.equipe.name = value;\n\n    // Vérifier si le nom existe déjà\n    this.nameExists = this.checkNameExists(value);\n    if (this.nameExists) {\n      console.warn(\"Ce nom d'équipe existe déjà\");\n    }\n\n    // Vérifier si le nom a au moins 3 caractères\n    this.nameError = value.length > 0 && value.length < 3;\n    if (this.nameError) {\n      console.warn('Le nom doit contenir au moins 3 caractères');\n    }\n  }\n\n  updateDescription(value: string): void {\n    console.log('Description updated:', value);\n    this.equipe.description = value;\n\n    // Vérifier si la description a au moins 10 caractères\n    this.descriptionError = value.length > 0 && value.length < 10;\n    if (this.descriptionError) {\n      console.warn('La description doit contenir au moins 10 caractères');\n    }\n  }\n\n  onSubmit(): void {\n    console.log('Form submitted with:', this.equipe);\n\n    // Vérifier si le nom est présent et valide\n    if (!this.equipe.name) {\n      this.error = \"Le nom de l'équipe est requis.\";\n      return;\n    }\n\n    if (this.equipe.name.length < 3) {\n      this.nameError = true;\n      this.error = \"Le nom de l'équipe doit contenir au moins 3 caractères.\";\n      return;\n    }\n\n    // Vérifier si la description est présente et valide\n    if (!this.equipe.description) {\n      this.error = \"La description de l'équipe est requise.\";\n      return;\n    }\n\n    if (this.equipe.description.length < 10) {\n      this.descriptionError = true;\n      this.error =\n        \"La description de l'équipe doit contenir au moins 10 caractères.\";\n      return;\n    }\n\n    // Vérifier si le nom existe déjà avant de soumettre\n    if (this.checkNameExists(this.equipe.name)) {\n      this.nameExists = true;\n      this.error =\n        'Une équipe avec ce nom existe déjà. Veuillez choisir un autre nom.';\n      return;\n    }\n\n    this.submitting = true;\n    this.error = null;\n\n    // S'assurer que l'admin est défini\n    if (!this.equipe.admin && this.currentUserId) {\n      this.equipe.admin = this.currentUserId;\n    }\n\n    if (!this.equipe.admin) {\n      this.error =\n        \"Impossible de déterminer l'administrateur de l'équipe. Veuillez vous reconnecter.\";\n      return;\n    }\n\n    // Créer une copie de l'objet équipe pour éviter les problèmes de référence\n    const equipeToSave: Equipe = {\n      name: this.equipe.name,\n      description: this.equipe.description || '',\n      admin: this.equipe.admin,\n    };\n\n    // Ajouter l'ID si nous sommes en mode édition\n    if (this.isEditMode && this.equipeId) {\n      equipeToSave._id = this.equipeId;\n    }\n\n    console.log('Données à envoyer:', equipeToSave);\n\n    if (this.isEditMode && this.equipeId) {\n      // Mode édition\n      this.equipeService.updateEquipe(this.equipeId, equipeToSave).subscribe({\n        next: (response) => {\n          console.log('Équipe mise à jour avec succès:', response);\n          this.submitting = false;\n          this.notificationService.showSuccess(\n            `L'équipe \"${response.name}\" a été mise à jour avec succès.`\n          );\n          this.router.navigate(['/equipes/liste']);\n        },\n        error: (error) => {\n          console.error(\"Erreur lors de la mise à jour de l'équipe:\", error);\n          this.error = `Impossible de mettre à jour l'équipe: ${error.message}`;\n          this.submitting = false;\n          this.notificationService.showError(`Erreur: ${error.message}`);\n        },\n      });\n    } else {\n      // Mode ajout\n      this.equipeService.addEquipe(equipeToSave).subscribe({\n        next: (response) => {\n          console.log('Équipe ajoutée avec succès:', response);\n          this.submitting = false;\n          this.notificationService.showSuccess(\n            `L'équipe \"${response.name}\" a été créée avec succès.`\n          );\n          this.router.navigate(['/equipes/liste']);\n        },\n        error: (error) => {\n          console.error(\"Erreur lors de l'ajout de l'équipe:\", error);\n          this.error = `Impossible d'ajouter l'équipe: ${error.message}`;\n          this.submitting = false;\n          this.notificationService.showError(`Erreur: ${error.message}`);\n        },\n      });\n    }\n  }\n\n  cancel(): void {\n    console.log('Form cancelled');\n    this.router.navigate(['/admin/equipes']);\n  }\n\n  // Méthodes pour gérer les membres\n  addMembreToEquipe(membreId: string, role: string = 'membre'): void {\n    console.log(\n      'Début de addMembreToEquipe avec membreId:',\n      membreId,\n      'et rôle:',\n      role\n    );\n    console.log('État actuel - this.equipeId:', this.equipeId);\n    console.log('État actuel - this.equipe:', this.equipe);\n\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\n    const equipeId = this.equipeId || (this.equipe && this.equipe._id);\n\n    console.log('equipeId calculé:', equipeId);\n\n    if (!equipeId || !membreId) {\n      console.error(\"ID d'équipe ou ID de membre manquant\");\n      this.error = \"ID d'équipe ou ID de membre manquant\";\n      console.log('equipeId:', equipeId, 'membreId:', membreId);\n\n      // Afficher un message à l'utilisateur\n      this.notificationService.showError(\n        \"Impossible d'ajouter le membre: ID d'équipe ou ID de membre manquant\"\n      );\n      return;\n    }\n\n    // Vérifier si le membre est déjà dans l'équipe\n    if (this.equipe.members && this.equipe.members.includes(membreId)) {\n      this.notificationService.showError(\n        \"Ce membre fait déjà partie de l'équipe\"\n      );\n      return;\n    }\n\n    // Récupérer les informations de l'utilisateur pour afficher un message plus informatif\n    const user = this.availableUsers.find(\n      (u) => u._id === membreId || u.id === membreId\n    );\n    const userName = user\n      ? user.firstName && user.lastName\n        ? `${user.firstName} ${user.lastName}`\n        : user.name || membreId\n      : membreId;\n\n    // Créer l'objet membre avec le rôle spécifié\n    const membre: Membre = {\n      id: membreId,\n      role: role,\n    };\n\n    this.loading = true;\n\n    console.log(\n      `Ajout de l'utilisateur \"${userName}\" comme ${role} à l'équipe ${equipeId}`\n    );\n\n    this.equipeService.addMembreToEquipe(equipeId, membre).subscribe({\n      next: (response) => {\n        console.log('Membre ajouté avec succès:', response);\n        this.notificationService.showSuccess(\n          `${userName} a été ajouté comme ${\n            role === 'admin' ? 'administrateur' : 'membre'\n          } à l'équipe`\n        );\n        // Recharger l'équipe pour mettre à jour la liste des membres\n        this.loadEquipe(equipeId);\n        this.loading = false;\n      },\n      error: (error) => {\n        console.error(\"Erreur lors de l'ajout du membre:\", error);\n        this.error =\n          \"Impossible d'ajouter le membre. Veuillez réessayer plus tard.\";\n        this.notificationService.showError(\n          \"Erreur lors de l'ajout du membre: \" + error.message\n        );\n        this.loading = false;\n      },\n    });\n  }\n\n  // Méthode pour obtenir le nom complet d'un membre à partir de son ID\n  getMembreName(membreId: string): string {\n    // Chercher d'abord dans la liste des utilisateurs\n    const user = this.availableUsers.find(\n      (u) => u._id === membreId || u.id === membreId\n    );\n    if (user) {\n      if (user.firstName && user.lastName) {\n        return `${user.firstName} ${user.lastName}`;\n      } else if (user.name) {\n        return user.name;\n      }\n    }\n\n    // Chercher ensuite dans la liste des membres\n    const membre = this.availableMembers.find(\n      (m) => m._id === membreId || m.id === membreId\n    );\n    if (membre && membre.name) {\n      return membre.name;\n    }\n\n    // Si aucun nom n'est trouvé, retourner l'ID\n    return membreId;\n  }\n\n  // Méthode pour obtenir l'email d'un membre\n  getMembreEmail(membreId: string): string {\n    const user = this.availableUsers.find(\n      (u) => u._id === membreId || u.id === membreId\n    );\n    if (user && user.email) {\n      return user.email;\n    }\n    return 'Non renseigné';\n  }\n\n  // Méthode pour obtenir la profession d'un membre\n  getMembreProfession(membreId: string): string {\n    const user = this.availableUsers.find(\n      (u) => u._id === membreId || u.id === membreId\n    );\n    if (user) {\n      if (user.profession) {\n        return user.profession === 'etudiant' ? 'Étudiant' : 'Professeur';\n      } else if (user.role) {\n        return user.role === 'etudiant' ? 'Étudiant' : 'Professeur';\n      }\n    }\n    return 'Non spécifié';\n  }\n\n  // Méthode pour obtenir le rôle d'un membre dans l'équipe\n  getMembreRole(_membreId: string): string {\n    // Cette méthode nécessiterait d'avoir accès aux rôles des membres dans l'équipe\n    // Pour l'instant, nous retournons une valeur par défaut\n    return 'Membre';\n  }\n\n  removeMembreFromEquipe(membreId: string): void {\n    console.log('Méthode removeMembreFromEquipe appelée avec ID:', membreId);\n    console.log('État actuel - this.equipeId:', this.equipeId);\n    console.log('État actuel - this.equipe:', this.equipe);\n\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\n    const equipeId = this.equipeId || (this.equipe && this.equipe._id);\n\n    if (!equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de retirer le membre.\";\n      this.notificationService.showError(\n        \"ID d'équipe manquant. Impossible de retirer le membre.\"\n      );\n      return;\n    }\n\n    if (!membreId) {\n      console.error('ID de membre manquant');\n      this.error = 'ID de membre manquant. Impossible de retirer le membre.';\n      this.notificationService.showError(\n        'ID de membre manquant. Impossible de retirer le membre.'\n      );\n      return;\n    }\n\n    // Obtenir le nom du membre pour l'afficher dans le message de confirmation\n    const membreName = this.getMembreName(membreId);\n\n    console.log(\n      `Tentative de retrait de l'utilisateur ${membreId} (${membreName}) de l'équipe ${equipeId}`\n    );\n\n    try {\n      if (\n        confirm(`Êtes-vous sûr de vouloir retirer ${membreName} de l'équipe?`)\n      ) {\n        console.log('Confirmation acceptée, suppression en cours...');\n\n        this.loading = true;\n        this.error = null;\n\n        // Ajouter un délai pour s'assurer que l'utilisateur voit le chargement\n        setTimeout(() => {\n          this.equipeService\n            .removeMembreFromEquipe(equipeId, membreId)\n            .subscribe({\n              next: (response) => {\n                console.log(\n                  `Utilisateur \"${membreName}\" retiré avec succès de l'équipe:`,\n                  response\n                );\n                this.loading = false;\n                this.notificationService.showSuccess(\n                  `${membreName} a été retiré avec succès de l'équipe`\n                );\n\n                // Recharger l'équipe pour mettre à jour la liste des membres\n                this.loadEquipe(equipeId);\n              },\n              error: (error) => {\n                console.error(\n                  `Erreur lors du retrait de l'utilisateur \"${membreName}\":`,\n                  error\n                );\n                console.error(\"Détails de l'erreur:\", {\n                  status: error.status,\n                  message: error.message,\n                  error: error,\n                });\n\n                this.loading = false;\n                this.error = `Impossible de retirer l'utilisateur \"${membreName}\" de l'équipe: ${\n                  error.message || 'Erreur inconnue'\n                }`;\n                this.notificationService.showError(\n                  `Erreur lors du retrait du membre: ${this.error}`\n                );\n              },\n            });\n        }, 500);\n      } else {\n        console.log(\"Suppression annulée par l'utilisateur\");\n      }\n    } catch (error: any) {\n      console.error('Exception lors du retrait du membre:', error);\n      this.error = `Exception: ${error?.message || 'Erreur inconnue'}`;\n      this.notificationService.showError(`Exception: ${this.error}`);\n    }\n  }\n\n  // Méthode pour supprimer l'équipe\n  deleteEquipe(): void {\n    console.log('Méthode deleteEquipe appelée dans equipe-form.component.ts');\n    console.log('État actuel - this.equipeId:', this.equipeId);\n    console.log('État actuel - this.equipe:', this.equipe);\n\n    // Utiliser this.equipe._id si this.equipeId n'est pas défini\n    const equipeId = this.equipeId || (this.equipe && this.equipe._id);\n\n    if (!equipeId) {\n      console.error(\"ID d'équipe manquant\");\n      this.error = \"ID d'équipe manquant. Impossible de supprimer l'équipe.\";\n      this.notificationService.showError(\n        \"ID d'équipe manquant. Impossible de supprimer l'équipe.\"\n      );\n      return;\n    }\n\n    console.log(\"ID de l'équipe à supprimer (final):\", equipeId);\n\n    try {\n      if (\n        confirm(\n          `Êtes-vous sûr de vouloir supprimer l'équipe \"${this.equipe.name}\"? Cette action est irréversible.`\n        )\n      ) {\n        console.log('Confirmation acceptée, suppression en cours...');\n\n        this.loading = true;\n        this.error = null;\n\n        // Ajouter un délai pour s'assurer que l'utilisateur voit le chargement\n        setTimeout(() => {\n          this.equipeService.deleteEquipe(equipeId).subscribe({\n            next: (response) => {\n              console.log('Équipe supprimée avec succès, réponse:', response);\n              this.loading = false;\n              this.notificationService.showSuccess(\n                `L'équipe \"${this.equipe.name}\" a été supprimée avec succès.`\n              );\n\n              // Ajouter un délai avant la redirection\n              setTimeout(() => {\n                this.router.navigate(['/admin/equipes']);\n              }, 500);\n            },\n            error: (error) => {\n              console.error(\n                \"Erreur lors de la suppression de l'équipe:\",\n                error\n              );\n              console.error(\"Détails de l'erreur:\", {\n                status: error.status,\n                message: error.message,\n                error: error,\n              });\n\n              this.loading = false;\n              this.error = `Impossible de supprimer l'équipe: ${\n                error.message || 'Erreur inconnue'\n              }`;\n              this.notificationService.showError(\n                `Erreur lors de la suppression: ${this.error}`\n              );\n            },\n          });\n        }, 500);\n      } else {\n        console.log(\"Suppression annulée par l'utilisateur\");\n      }\n    } catch (error: any) {\n      console.error('Exception lors de la suppression:', error);\n      this.error = `Exception: ${error?.message || 'Erreur inconnue'}`;\n      this.notificationService.showError(`Exception: ${this.error}`);\n    }\n  }\n}\n", "import { Component, OnInit } from '@angular/core';\nimport { Router, NavigationEnd } from '@angular/router';\nimport { Observable } from 'rxjs';\nimport { filter } from 'rxjs/operators';\nimport { EquipeService } from 'src/app/services/equipe.service';\nimport { Location } from '@angular/common';\n\n@Component({\n  selector: 'app-equipe-layout',\n  templateUrl: './equipe-layout.component.html',\n  styleUrls: ['./equipe-layout.component.css'],\n})\nexport class EquipeLayoutComponent implements OnInit {\n  sidebarVisible$: Observable<boolean> = new Observable<boolean>();\n\n  // Page properties\n  pageTitle: string = 'Gestion des Équipes';\n  pageSubtitle: string = 'Organisez et gérez vos équipes de projet';\n\n  // Statistics\n  totalEquipes: number = 0;\n  totalMembres: number = 0;\n  totalProjets: number = 0;\n\n  constructor(\n    private router: Router,\n    private location: Location,\n    private equipeService: EquipeService\n  ) {}\n\n  ngOnInit(): void {\n    this.loadStatistics();\n    this.updatePageTitle();\n\n    // Listen to route changes to update page title\n    this.router.events\n      .pipe(filter((event) => event instanceof NavigationEnd))\n      .subscribe(() => {\n        this.updatePageTitle();\n      });\n  }\n\n  loadStatistics(): void {\n    // Load teams statistics\n    this.equipeService.getEquipes().subscribe({\n      next: (equipes) => {\n        this.totalEquipes = equipes.length;\n\n        // Calculate total members across all teams\n        this.totalMembres = equipes.reduce((total, equipe) => {\n          return total + (equipe.members ? equipe.members.length : 0);\n        }, 0);\n\n        // For now, set projects to 0 (can be updated when project service is available)\n        this.totalProjets = 0;\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement des statistiques:', error);\n      },\n    });\n  }\n\n  updatePageTitle(): void {\n    const url = this.router.url;\n\n    if (url.includes('/equipes/liste')) {\n      this.pageTitle = 'Liste des Équipes';\n      this.pageSubtitle = 'Consultez et gérez toutes vos équipes';\n    } else if (url.includes('/equipes/nouveau')) {\n      this.pageTitle = 'Créer une Équipe';\n      this.pageSubtitle = 'Créez une nouvelle équipe pour vos projets';\n    } else if (url.includes('/equipes/mes-equipes')) {\n      this.pageTitle = 'Mes Équipes';\n      this.pageSubtitle = 'Équipes dont vous êtes membre ou administrateur';\n    } else if (url.includes('/equipes/detail')) {\n      this.pageTitle = \"Détails de l'Équipe\";\n      this.pageSubtitle = 'Informations et gestion des membres';\n    } else if (url.includes('/equipes/edit')) {\n      this.pageTitle = \"Modifier l'Équipe\";\n      this.pageSubtitle = 'Modifiez les informations de votre équipe';\n    } else {\n      this.pageTitle = 'Gestion des Équipes';\n      this.pageSubtitle = 'Organisez et gérez vos équipes de projet';\n    }\n  }\n\n  goBack(): void {\n    this.location.back();\n  }\n}\n", "<div\n  class=\"min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] relative overflow-hidden\"\n>\n  <!-- Background decorative elements -->\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n    <div\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"\n    ></div>\n    <div\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"\n    ></div>\n\n    <!-- Grid pattern -->\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\n      <div class=\"h-full grid grid-cols-12\">\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Layout Container -->\n  <div class=\"flex h-screen relative z-10\">\n    <!-- Sidebar Navigation -->\n    <div\n      class=\"w-80 bg-white dark:bg-[#1a1a1a] shadow-xl dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] border-r border-[#4f5fad]/20 dark:border-[#00f7ff]/20 flex flex-col\"\n    >\n      <!-- Header -->\n      <div class=\"p-6 border-b border-[#4f5fad]/20 dark:border-[#00f7ff]/20\">\n        <div class=\"flex items-center space-x-3\">\n          <div\n            class=\"w-10 h-10 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] rounded-xl flex items-center justify-center\"\n          >\n            <i class=\"fas fa-users text-white text-lg\"></i>\n          </div>\n          <div>\n            <h1\n              class=\"text-xl font-bold text-[#4f5fad] dark:text-[#00f7ff] tracking-wide\"\n            >\n              Équipes\n            </h1>\n            <p class=\"text-sm text-[#6d6870] dark:text-[#e0e0e0]\">\n              Gestion collaborative\n            </p>\n          </div>\n        </div>\n      </div>\n\n      <!-- Navigation Menu -->\n      <nav class=\"flex-1 p-4 space-y-2\">\n        <!-- Liste des équipes -->\n        <a\n          routerLink=\"/equipes/liste\"\n          routerLinkActive=\"active bg-gradient-to-r from-[#4f5fad]/10 to-[#7826b5]/10 dark:from-[#00f7ff]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#00f7ff] font-medium\"\n          class=\"group flex items-center px-4 py-3 text-sm font-medium rounded-xl text-[#6d6870] dark:text-[#e0e0e0] hover:bg-[#4f5fad]/10 dark:hover:bg-[#00f7ff]/10 hover:text-[#4f5fad] dark:hover:text-[#00f7ff] transition-all duration-300\"\n        >\n          <div class=\"relative\">\n            <i\n              class=\"fas fa-list-ul w-5 h-5 mr-3 group-hover:scale-110 transition-transform\"\n            ></i>\n            <div\n              class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#00f7ff]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\n            ></div>\n          </div>\n          <span class=\"relative\">Liste des équipes</span>\n        </a>\n\n        <!-- Créer une équipe -->\n        <a\n          routerLink=\"/equipes/nouveau\"\n          routerLinkActive=\"active bg-gradient-to-r from-[#4f5fad]/10 to-[#7826b5]/10 dark:from-[#00f7ff]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#00f7ff] font-medium\"\n          class=\"group flex items-center px-4 py-3 text-sm font-medium rounded-xl text-[#6d6870] dark:text-[#e0e0e0] hover:bg-[#4f5fad]/10 dark:hover:bg-[#00f7ff]/10 hover:text-[#4f5fad] dark:hover:text-[#00f7ff] transition-all duration-300\"\n        >\n          <div class=\"relative\">\n            <i\n              class=\"fas fa-plus-circle w-5 h-5 mr-3 group-hover:scale-110 transition-transform\"\n            ></i>\n            <div\n              class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#00f7ff]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\n            ></div>\n          </div>\n          <span class=\"relative\">Créer une équipe</span>\n        </a>\n\n        <!-- Mes équipes -->\n        <a\n          routerLink=\"/equipes/mes-equipes\"\n          routerLinkActive=\"active bg-gradient-to-r from-[#4f5fad]/10 to-[#7826b5]/10 dark:from-[#00f7ff]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#00f7ff] font-medium\"\n          class=\"group flex items-center px-4 py-3 text-sm font-medium rounded-xl text-[#6d6870] dark:text-[#e0e0e0] hover:bg-[#4f5fad]/10 dark:hover:bg-[#00f7ff]/10 hover:text-[#4f5fad] dark:hover:text-[#00f7ff] transition-all duration-300\"\n        >\n          <div class=\"relative\">\n            <i\n              class=\"fas fa-user-friends w-5 h-5 mr-3 group-hover:scale-110 transition-transform\"\n            ></i>\n            <div\n              class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#00f7ff]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\n            ></div>\n          </div>\n          <span class=\"relative\">Mes équipes</span>\n        </a>\n\n        <!-- Divider -->\n        <div\n          class=\"my-4 border-t border-[#4f5fad]/20 dark:border-[#00f7ff]/20\"\n        ></div>\n\n        <!-- Statistiques -->\n        <div class=\"px-4 py-3\">\n          <h3\n            class=\"text-xs font-semibold text-[#6d6870] dark:text-[#a0a0a0] uppercase tracking-wider mb-3\"\n          >\n            Statistiques\n          </h3>\n          <div class=\"space-y-3\">\n            <div class=\"flex items-center justify-between\">\n              <span class=\"text-sm text-[#6d6870] dark:text-[#e0e0e0]\"\n                >Équipes créées</span\n              >\n              <span\n                class=\"text-sm font-medium text-[#4f5fad] dark:text-[#00f7ff]\"\n                >0</span\n              >\n            </div>\n            <div class=\"flex items-center justify-between\">\n              <span class=\"text-sm text-[#6d6870] dark:text-[#e0e0e0]\"\n                >Membres actifs</span\n              >\n              <span class=\"text-sm font-medium text-[#00ff9d]\">0</span>\n            </div>\n            <div class=\"flex items-center justify-between\">\n              <span class=\"text-sm text-[#6d6870] dark:text-[#e0e0e0]\"\n                >Projets en cours</span\n              >\n              <span\n                class=\"text-sm font-medium text-[#ff6b69] dark:text-[#ff3b30]\"\n                >0</span\n              >\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      <!-- Footer -->\n      <div class=\"p-4 border-t border-[#4f5fad]/20 dark:border-[#00f7ff]/20\">\n        <button\n          onclick=\"history.back()\"\n          class=\"w-full bg-[#6d6870]/20 dark:bg-[#a0a0a0]/20 text-[#6d6870] dark:text-[#e0e0e0] px-4 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 hover:bg-[#6d6870]/30 dark:hover:bg-[#a0a0a0]/30 flex items-center justify-center\"\n        >\n          <i class=\"fas fa-arrow-left mr-2\"></i>\n          Retour à l'accueil\n        </button>\n      </div>\n    </div>\n\n    <!-- Main Content Area -->\n    <div class=\"flex-1 flex flex-col overflow-hidden\">\n      <!-- Top Bar -->\n      <header\n        class=\"bg-white dark:bg-[#1a1a1a] shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] border-b border-[#4f5fad]/20 dark:border-[#00f7ff]/20 px-6 py-4\"\n      >\n        <div class=\"flex items-center justify-between\">\n          <!-- Page Title -->\n          <div class=\"flex items-center space-x-4\">\n            <div\n              class=\"w-8 h-8 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] rounded-lg flex items-center justify-center\"\n            >\n              <i class=\"fas fa-users text-white text-sm\"></i>\n            </div>\n            <div>\n              <h2 class=\"text-xl font-bold text-[#4f5fad] dark:text-[#00f7ff]\">\n                Gestion des Équipes\n              </h2>\n              <p class=\"text-sm text-[#6d6870] dark:text-[#e0e0e0]\">\n                Organisez et gérez vos équipes de projet\n              </p>\n            </div>\n          </div>\n\n          <!-- Actions -->\n          <div class=\"flex items-center space-x-3\">\n            <!-- Search -->\n            <div class=\"relative\">\n              <input\n                type=\"text\"\n                placeholder=\"Rechercher...\"\n                class=\"w-64 pl-10 pr-4 py-2 bg-[#f0f4f8] dark:bg-[#0a0a0a] border border-[#4f5fad]/20 dark:border-[#00f7ff]/20 rounded-lg text-[#6d6870] dark:text-[#e0e0e0] placeholder-[#6d6870]/50 dark:placeholder-[#a0a0a0] focus:outline-none focus:ring-2 focus:ring-[#4f5fad] dark:focus:ring-[#00f7ff] focus:border-transparent transition-all\"\n              />\n              <div\n                class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\n              >\n                <i class=\"fas fa-search text-[#6d6870] dark:text-[#a0a0a0]\"></i>\n              </div>\n            </div>\n\n            <!-- Quick Actions -->\n            <button\n              routerLink=\"/equipes/nouveau\"\n              class=\"bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] text-white px-4 py-2 rounded-lg font-medium transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-[0_0_25px_rgba(79,95,173,0.4)] dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)] flex items-center\"\n            >\n              <i class=\"fas fa-plus mr-2\"></i>\n              Nouvelle équipe\n            </button>\n          </div>\n        </div>\n      </header>\n\n      <!-- Content Area -->\n      <main class=\"flex-1 overflow-auto p-6\">\n        <router-outlet></router-outlet>\n      </main>\n    </div>\n  </div>\n</div>\n", "import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { EquipeService } from 'src/app/services/equipe.service';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { Equipe } from 'src/app/models/equipe.model';\nimport { finalize } from 'rxjs/operators';\n\n@Component({\n  selector: 'app-equipe-list',\n  templateUrl: './equipe-list.component.html',\n  styleUrls: ['./equipe-list.component.css']\n})\nexport class EquipeListComponent implements OnInit {\n  equipes: Equipe[] = [];\n  loading = false;\n  error: string | null = null;\n\n  constructor(\n    private equipeService: EquipeService,\n    private router: Router,\n    private notificationService: NotificationService\n  ) {}\n\n  ngOnInit(): void {\n    this.loadEquipes();\n  }\n\n  loadEquipes(): void {\n    this.loading = true;\n    this.error = null;\n\n    this.equipeService.getEquipes().pipe(\n      finalize(() => this.loading = false)\n    ).subscribe({\n      next: (data) => {\n        console.log('Équipes chargées:', data);\n        this.equipes = data;\n\n        // Trier les équipes par nom\n        this.equipes.sort((a, b) => {\n          if (a.name && b.name) {\n            return a.name.localeCompare(b.name);\n          }\n          return 0;\n        });\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement des équipes:', error);\n        this.error = 'Impossible de charger les équipes. Veuillez réessayer plus tard.';\n        this.notificationService.showError('Erreur lors du chargement des équipes');\n      }\n    });\n  }\n\n  navigateToAddEquipe(): void {\n    this.router.navigate(['/equipes/ajouter']);\n  }\n\n  navigateToEditEquipe(id: string): void {\n    this.router.navigate(['/equipes/modifier', id]);\n  }\n\n  navigateToEquipeDetail(id: string): void {\n    this.router.navigate(['/equipes/detail', id]);\n  }\n\n  deleteEquipe(id: string): void {\n    if (!id) {\n      console.error('ID est indéfini');\n      this.notificationService.showError('ID d\\'équipe invalide');\n      return;\n    }\n\n    // Trouver le nom de l'équipe pour l'afficher dans le message de confirmation\n    const equipe = this.equipes.find(e => e._id === id);\n    const equipeName = equipe?.name || 'cette équipe';\n\n    if (confirm(`Êtes-vous sûr de vouloir supprimer l'équipe \"${equipeName}\" ?`)) {\n      this.loading = true;\n\n      this.equipeService.deleteEquipe(id).pipe(\n        finalize(() => this.loading = false)\n      ).subscribe({\n        next: () => {\n          console.log('Équipe supprimée avec succès');\n          this.notificationService.showSuccess(`L'équipe \"${equipeName}\" a été supprimée avec succès`);\n          this.loadEquipes();\n        },\n        error: (error) => {\n          console.error('Erreur lors de la suppression de l\\'équipe:', error);\n          this.error = 'Impossible de supprimer l\\'équipe. Veuillez réessayer plus tard.';\n          this.notificationService.showError(`Erreur lors de la suppression de l'équipe \"${equipeName}\"`);\n        }\n      });\n    }\n  }\n\n  navigateToTasks(id: string): void {\n    if (!id) {\n      console.error('ID est indéfini');\n      this.notificationService.showError('ID d\\'équipe invalide');\n      return;\n    }\n\n    const equipe = this.equipes.find(e => e._id === id);\n    const equipeName = equipe?.name || 'cette équipe';\n\n    // Naviguer vers la page des tâches de l'équipe\n    this.router.navigate(['/tasks', id]);    }\n}\n\n", "<div\n  class=\"min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] relative overflow-hidden\"\n>\n  <!-- Background decorative elements -->\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n    <div\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"\n    ></div>\n    <div\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/3 dark:to-transparent blur-3xl\"\n    ></div>\n\n    <!-- Grid pattern -->\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\n      <div class=\"h-full grid grid-cols-12\">\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#00f7ff]\"></div>\n      </div>\n    </div>\n  </div>\n\n  <div class=\"max-w-7xl mx-auto p-6 relative z-10\">\n    <!-- Header futuriste -->\n    <div class=\"mb-8 relative\">\n      <!-- Decorative top border -->\n      <div\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad]\"\n      ></div>\n      <div\n        class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] blur-md\"\n      ></div>\n\n      <div\n        class=\"bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] p-6 backdrop-blur-sm border border-[#4f5fad]/20 dark:border-[#00f7ff]/20\"\n      >\n        <div\n          class=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\"\n        >\n          <div class=\"mb-4 lg:mb-0\">\n            <h1\n              class=\"text-3xl font-bold text-[#4f5fad] dark:text-[#00f7ff] mb-2 tracking-wide\"\n            >\n              Équipes\n            </h1>\n            <p class=\"text-[#6d6870] dark:text-[#e0e0e0] text-sm\">\n              Gérez vos équipes et leurs membres avec style futuriste\n            </p>\n          </div>\n\n          <button\n            (click)=\"navigateToAddEquipe()\"\n            class=\"relative overflow-hidden group bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-[0_0_25px_rgba(79,95,173,0.4)] dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)]\"\n          >\n            <i\n              class=\"fas fa-plus mr-2 group-hover:rotate-90 transition-transform duration-300\"\n            ></i>\n            Nouvelle Équipe\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Loading Indicator -->\n    <div\n      *ngIf=\"loading\"\n      class=\"flex flex-col items-center justify-center py-16\"\n    >\n      <div class=\"relative\">\n        <div\n          class=\"w-12 h-12 border-3 border-[#4f5fad]/20 dark:border-[#00f7ff]/20 border-t-[#4f5fad] dark:border-t-[#00f7ff] rounded-full animate-spin\"\n        ></div>\n        <div\n          class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#00f7ff]/20 blur-xl rounded-full transform scale-150 -z-10\"\n        ></div>\n      </div>\n      <p\n        class=\"mt-4 text-[#4f5fad] dark:text-[#00f7ff] text-sm font-medium tracking-wide\"\n      >\n        Chargement des équipes...\n      </p>\n    </div>\n\n    <!-- Error Alert -->\n    <div *ngIf=\"error\" class=\"mb-6\">\n      <div\n        class=\"bg-[#ff6b69]/10 dark:bg-[#ff3b30]/10 border-l-4 border-[#ff6b69] dark:border-[#ff3b30] rounded-lg p-4 backdrop-blur-sm\"\n      >\n        <div class=\"flex items-start\">\n          <div class=\"text-[#ff6b69] dark:text-[#ff3b30] mr-3 text-xl\">\n            <i class=\"fas fa-exclamation-triangle\"></i>\n          </div>\n          <div class=\"flex-1\">\n            <h3 class=\"font-semibold text-[#ff6b69] dark:text-[#ff3b30] mb-1\">\n              Erreur de chargement des équipes\n            </h3>\n            <p class=\"text-sm text-[#6d6870] dark:text-[#e0e0e0]\">\n              {{ error }}\n            </p>\n            <button\n              (click)=\"loadEquipes()\"\n              class=\"mt-3 bg-[#ff6b69]/20 dark:bg-[#ff3b30]/20 text-[#ff6b69] dark:text-[#ff3b30] px-3 py-1.5 rounded-lg text-sm font-medium hover:bg-[#ff6b69]/30 dark:hover:bg-[#ff3b30]/30 transition-colors\"\n            >\n              <i class=\"fas fa-sync-alt mr-1.5\"></i> Réessayer\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- No Teams -->\n    <div\n      *ngIf=\"!loading && !error && equipes.length === 0\"\n      class=\"text-center py-16\"\n    >\n      <div\n        class=\"w-20 h-20 mx-auto mb-6 text-[#4f5fad] dark:text-[#00f7ff] opacity-70\"\n      >\n        <i class=\"fas fa-users text-5xl\"></i>\n      </div>\n      <h3 class=\"text-xl font-semibold text-[#4f5fad] dark:text-[#00f7ff] mb-2\">\n        Aucune équipe trouvée\n      </h3>\n      <p class=\"text-[#6d6870] dark:text-[#e0e0e0] text-sm mb-6\">\n        Commencez par créer une nouvelle équipe pour organiser vos projets et\n        membres\n      </p>\n      <button\n        (click)=\"navigateToAddEquipe()\"\n        class=\"bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-[0_0_25px_rgba(79,95,173,0.4)] dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)]\"\n      >\n        <i class=\"fas fa-plus-circle mr-2\"></i>\n        Créer une équipe\n      </button>\n    </div>\n\n    <!-- Teams Grid -->\n    <div\n      *ngIf=\"!loading && equipes.length > 0\"\n      class=\"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6\"\n    >\n      <div\n        *ngFor=\"let equipe of equipes\"\n        class=\"group bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden hover:shadow-xl dark:hover:shadow-[0_12px_40px_rgba(0,0,0,0.4)] transition-all duration-300 hover:-translate-y-2 border border-[#4f5fad]/20 dark:border-[#00f7ff]/20 backdrop-blur-sm\"\n      >\n        <!-- Header avec gradient -->\n        <div class=\"relative\">\n          <div\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad]\"\n          ></div>\n          <div\n            class=\"absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 blur-md transition-opacity duration-300\"\n          ></div>\n\n          <div class=\"p-6\">\n            <div class=\"flex items-start justify-between mb-4\">\n              <div class=\"flex-1\">\n                <h3\n                  class=\"text-lg font-bold text-[#4f5fad] dark:text-[#00f7ff] mb-2 group-hover:scale-[1.02] transition-transform duration-300 origin-left\"\n                >\n                  {{ equipe.name }}\n                </h3>\n                <div class=\"flex items-center text-xs\">\n                  <span\n                    class=\"bg-[#4f5fad]/10 dark:bg-[#00f7ff]/10 text-[#4f5fad] dark:text-[#00f7ff] px-2 py-1 rounded-full font-medium\"\n                  >\n                    <i class=\"fas fa-users mr-1\"></i>\n                    {{ equipe.members?.length || 0 }} membre(s)\n                  </span>\n                </div>\n              </div>\n\n              <!-- Avatar de l'équipe -->\n              <div\n                class=\"w-12 h-12 bg-gradient-to-br from-[#4f5fad] to-[#7826b5] dark:from-[#00f7ff] dark:to-[#4f5fad] rounded-full flex items-center justify-center shadow-lg\"\n              >\n                <i class=\"fas fa-users text-white text-lg\"></i>\n              </div>\n            </div>\n\n            <!-- Description -->\n            <p\n              class=\"text-sm text-[#6d6870] dark:text-[#e0e0e0] line-clamp-2 mb-4\"\n            >\n              {{\n                equipe.description && equipe.description.length > 80\n                  ? (equipe.description | slice : 0 : 80) + \"...\"\n                  : equipe.description || \"Aucune description disponible\"\n              }}\n            </p>\n          </div>\n        </div>\n\n        <!-- Actions -->\n        <div class=\"px-6 pb-6\">\n          <div\n            class=\"flex items-center justify-between pt-4 border-t border-[#4f5fad]/10 dark:border-[#00f7ff]/10\"\n          >\n            <!-- Bouton Détails -->\n            <button\n              (click)=\"equipe._id && navigateToEquipeDetail(equipe._id)\"\n              class=\"text-[#4f5fad] dark:text-[#00f7ff] hover:text-[#7826b5] dark:hover:text-[#4f5fad] text-sm font-medium transition-colors flex items-center group/details\"\n            >\n              <i\n                class=\"fas fa-eye mr-1 group-hover/details:scale-110 transition-transform\"\n              ></i>\n              Détails\n            </button>\n\n            <!-- Actions rapides -->\n            <div class=\"flex items-center space-x-2\">\n              <button\n                (click)=\"equipe._id && navigateToEditEquipe(equipe._id)\"\n                class=\"p-2 text-[#6d6870] dark:text-[#e0e0e0] hover:text-[#4f5fad] dark:hover:text-[#00f7ff] hover:bg-[#4f5fad]/10 dark:hover:bg-[#00f7ff]/10 rounded-lg transition-all\"\n                title=\"Modifier l'équipe\"\n              >\n                <i class=\"fas fa-edit\"></i>\n              </button>\n\n              <button\n                (click)=\"equipe._id && navigateToTasks(equipe._id)\"\n                class=\"p-2 text-[#6d6870] dark:text-[#e0e0e0] hover:text-[#00ff9d] hover:bg-[#00ff9d]/10 rounded-lg transition-all\"\n                title=\"Gérer les tâches\"\n              >\n                <i class=\"fas fa-tasks\"></i>\n              </button>\n\n              <button\n                (click)=\"equipe._id && deleteEquipe(equipe._id)\"\n                class=\"p-2 text-[#6d6870] dark:text-[#e0e0e0] hover:text-[#ff6b69] dark:hover:text-[#ff3b30] hover:bg-[#ff6b69]/10 dark:hover:bg-[#ff3b30]/10 rounded-lg transition-all\"\n                title=\"Supprimer l'équipe\"\n              >\n                <i class=\"fas fa-trash\"></i>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n", "import { Component, OnInit } from '@angular/core';\nimport { EquipeService } from 'src/app/services/equipe.service';\nimport { MembreService } from 'src/app/services/membre.service';\nimport { Equipe } from 'src/app/models/equipe.model';\nimport { Membre } from 'src/app/models/membre.model';\nimport { forkJoin } from 'rxjs';\n\n// Add Bootstrap type declaration\ndeclare global {\n  interface Window {\n    bootstrap: any;\n  }\n}\n\n@Component({\n  selector: 'app-equipe',\n  templateUrl: './equipe.component.html',\n  styleUrls: ['./equipe.component.css'],\n})\nexport class EquipeComponent implements OnInit {\n  equipes: Equipe[] = [];\n  newEquipe: Equipe = { name: '', description: '' };\n  selectedEquipe: Equipe | null = null;\n  isEditing = false;\n  membres: Membre[] = [];\n  loading = false;\n  error = '';\n\n  constructor(\n    private equipeService: EquipeService,\n    private membreService: MembreService\n  ) {}\n\n  ngOnInit(): void {\n    this.loadEquipes();\n    this.loadMembres();\n  }\n\n  loadEquipes() {\n    this.loading = true;\n    this.equipeService.getEquipes().subscribe({\n      next: (data) => {\n        console.log('Loaded equipes:', data);\n        this.equipes = data;\n        this.loading = false;\n      },\n      error: (error) => {\n        console.error('Error loading equipes:', error);\n        this.error = 'Erreur lors du chargement des équipes: ' + error.message;\n        this.loading = false;\n      }\n    });\n  }\n\n  loadMembres() {\n    this.loading = true;\n    this.membreService.getMembres().subscribe({\n      next: (data) => {\n        console.log('Loaded membres:', data);\n        this.membres = data;\n        this.loading = false;\n      },\n      error: (error) => {\n        console.error('Error loading membres:', error);\n        this.error = 'Erreur lors du chargement des membres: ' + error.message;\n        this.loading = false;\n      }\n    });\n  }\n\n  addEquipe() {\n    console.log('Adding equipe:', this.newEquipe);\n\n    if (!this.newEquipe.name) {\n      console.error('Team name is required');\n      this.error = 'Le nom de l\\'équipe est requis';\n      return;\n    }\n\n    this.loading = true;\n    this.error = '';\n\n    this.equipeService.addEquipe(this.newEquipe).subscribe({\n      next: (response) => {\n        console.log('Equipe added successfully:', response);\n        this.loadEquipes();\n        this.newEquipe = { name: '', description: '' }; // Clear input\n        this.loading = false;\n\n        // Afficher un message de succès temporaire\n        const successMessage = 'Équipe créée avec succès!';\n        this.error = ''; // Effacer les erreurs précédentes\n        alert(successMessage);\n      },\n      error: (error) => {\n        console.error('Error adding equipe:', error);\n        this.error = 'Erreur lors de la création de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n        this.loading = false;\n      }\n    });\n  }\n\n  editEquipe(equipe: Equipe) {\n    this.isEditing = true;\n    // Créer une copie profonde pour éviter de modifier l'objet original\n    this.newEquipe = {\n      _id: equipe._id,\n      name: equipe.name || '',\n      description: equipe.description || '',\n      admin: equipe.admin,\n      members: equipe.members ? [...equipe.members] : []\n    };\n  }\n\n  cancelEdit() {\n    this.isEditing = false;\n    this.newEquipe = { name: '', description: '' };\n    this.error = ''; // Effacer les erreurs\n  }\n\n  updateSelectedEquipe() {\n    if (!this.newEquipe.name) {\n      console.error('Team name is required');\n      this.error = 'Le nom de l\\'équipe est requis';\n      return;\n    }\n\n    if (this.newEquipe._id) {\n      this.loading = true;\n      this.error = '';\n\n      this.equipeService.updateEquipe(this.newEquipe._id, this.newEquipe).subscribe({\n        next: (updatedEquipe) => {\n          console.log('Team updated successfully:', updatedEquipe);\n          this.loadEquipes();\n          this.isEditing = false;\n          this.newEquipe = { name: '', description: '' };\n          this.loading = false;\n\n          // Afficher un message de succès temporaire\n          const successMessage = 'Équipe mise à jour avec succès!';\n          alert(successMessage);\n        },\n        error: (error) => {\n          console.error('Error updating team:', error);\n          this.error = 'Erreur lors de la mise à jour de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n          this.loading = false;\n        }\n      });\n    } else {\n      this.error = 'ID de l\\'équipe manquant pour la mise à jour';\n    }\n  }\n\n  deleteEquipe(id: string) {\n    if (!id) {\n      console.error('ID is undefined');\n      this.error = 'ID de l\\'équipe non défini';\n      return;\n    }\n\n    if (confirm('Êtes-vous sûr de vouloir supprimer cette équipe? Cette action est irréversible.')) {\n      this.loading = true;\n      this.error = '';\n\n      this.equipeService.deleteEquipe(id).subscribe({\n        next: (response) => {\n          console.log('Team deleted successfully:', response);\n\n          // Si l'équipe en cours d'édition est celle qui vient d'être supprimée, réinitialiser le formulaire\n          if (this.isEditing && this.newEquipe._id === id) {\n            this.isEditing = false;\n            this.newEquipe = { name: '', description: '' };\n          }\n\n          this.loadEquipes();\n          this.loading = false;\n\n          // Afficher un message de succès\n          alert('Équipe supprimée avec succès');\n        },\n        error: (error) => {\n          console.error('Error deleting team:', error);\n          this.error = 'Erreur lors de la suppression de l\\'équipe: ' + (error.error?.message || error.message || 'Unknown error');\n          this.loading = false;\n        }\n      });\n    }\n  }\n\n  showMembreModal(equipe: Equipe) {\n    this.selectedEquipe = equipe;\n    // Ouvrir le modal avec Bootstrap 5\n    const modalRef = document.getElementById('membreModal');\n    if (modalRef) {\n      try {\n        // Ensure Bootstrap is properly loaded\n        if (typeof window !== 'undefined' && window.bootstrap) {\n          const modal = new window.bootstrap.Modal(modalRef);\n          modal.show();\n        } else {\n          console.error('Bootstrap is not loaded properly');\n          alert('Erreur: Bootstrap n\\'est pas chargé correctement');\n        }\n      } catch (error) {\n        console.error('Error showing modal:', error);\n      }\n    } else {\n      console.error('Modal element not found');\n    }\n  }\n\n  addMembreToEquipe(teamId: string | undefined, membreId: string) {\n    if (!teamId) {\n      console.error('Team ID is undefined');\n      alert('ID de l\\'équipe non défini');\n      return;\n    }\n\n    if (!membreId || membreId.trim() === '') {\n      console.error('Member ID is empty');\n      alert('L\\'ID du membre est requis');\n      return;\n    }\n\n    this.loading = true;\n\n    // Create a proper Membre object that matches what the API expects\n    const membre: Membre = { id: membreId };\n\n    this.equipeService.addMembreToEquipe(teamId, membre).subscribe({\n      next: (response) => {\n        console.log('Member added successfully:', response);\n        this.loadEquipes();\n        this.loading = false;\n\n        // Afficher un message de succès\n        alert('Membre ajouté avec succès à l\\'équipe');\n      },\n      error: (error) => {\n        console.error('Error adding member:', error);\n        this.error = 'Erreur lors de l\\'ajout du membre: ' + (error.error?.message || error.message || 'Unknown error');\n        alert(this.error);\n        this.loading = false;\n      }\n    });\n  }\n\n  removeMembreFromEquipe(teamId: string | undefined, membreId: string) {\n    if (!teamId) {\n      console.error('Team ID is undefined');\n      alert('ID de l\\'équipe non défini');\n      return;\n    }\n\n    if (!membreId) {\n      console.error('Member ID is undefined');\n      alert('ID du membre non défini');\n      return;\n    }\n\n    if (confirm('Êtes-vous sûr de vouloir retirer ce membre de l\\'équipe?')) {\n      this.loading = true;\n\n      this.equipeService.removeMembreFromEquipe(teamId, membreId).subscribe({\n        next: (response) => {\n          console.log('Member removed successfully:', response);\n          this.loadEquipes();\n          this.loading = false;\n\n          // Si l'équipe sélectionnée est celle dont on vient de retirer un membre, mettre à jour l'équipe sélectionnée\n          if (this.selectedEquipe && this.selectedEquipe._id === teamId) {\n            const updatedEquipe = this.equipes.find(e => e._id === teamId);\n            if (updatedEquipe) {\n              this.selectedEquipe = updatedEquipe;\n            }\n          }\n        },\n        error: (error) => {\n          console.error('Error removing member:', error);\n          this.error = 'Erreur lors de la suppression du membre: ' + (error.error?.message || error.message || 'Unknown error');\n          alert(this.error);\n          this.loading = false;\n        }\n      });\n    }\n  }\n}\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n", "<div class=\"container mt-4\">\n  <!-- Message d'erreur -->\n  <div\n    *ngIf=\"error\"\n    class=\"alert alert-danger alert-dismissible fade show\"\n    role=\"alert\"\n  >\n    {{ error }}\n    <button\n      type=\"button\"\n      class=\"btn-close\"\n      (click)=\"error = ''\"\n      aria-label=\"Close\"\n    ></button>\n  </div>\n\n  <!-- Indicateur de chargement -->\n  <div *ngIf=\"loading\" class=\"d-flex justify-content-center mb-4\">\n    <div class=\"spinner-border text-primary\" role=\"status\">\n      <span class=\"visually-hidden\">Chargement...</span>\n    </div>\n  </div>\n\n  <!-- Liste des équipes -->\n  <div class=\"row mb-4\">\n    <div class=\"col-12\">\n      <div class=\"d-flex justify-content-between align-items-center mb-3\">\n        <h2>Liste des équipes</h2>\n        <button class=\"btn btn-sm btn-outline-primary\" (click)=\"loadEquipes()\">\n          <i class=\"bi bi-arrow-clockwise\"></i> Rafraîchir\n        </button>\n      </div>\n\n      <div *ngIf=\"equipes.length === 0 && !loading\" class=\"alert alert-info\">\n        Aucune équipe trouvée. Créez votre première équipe ci-dessous.\n      </div>\n\n      <div *ngIf=\"equipes.length > 0\" class=\"table-responsive\">\n        <table class=\"table table-striped\">\n          <thead>\n            <tr>\n              <th>Nom</th>\n              <th>Description</th>\n              <th>Admin</th>\n              <th>Membres</th>\n              <th>Actions</th>\n            </tr>\n          </thead>\n          <tbody>\n            <tr *ngFor=\"let equipe of equipes\">\n              <td>{{ equipe.name }}</td>\n              <td>{{ equipe.description }}</td>\n              <td>{{ equipe.admin }}</td>\n              <td>{{ equipe.members?.length || 0 }} membres</td>\n              <td>\n                <button\n                  class=\"btn btn-sm btn-info me-2\"\n                  (click)=\"editEquipe(equipe)\"\n                >\n                  Modifier\n                </button>\n                <button\n                  class=\"btn btn-sm btn-danger me-2\"\n                  (click)=\"equipe._id && deleteEquipe(equipe._id)\"\n                >\n                  Supprimer\n                </button>\n                <button\n                  class=\"btn btn-sm btn-primary\"\n                  (click)=\"showMembreModal(equipe)\"\n                >\n                  Gérer membres\n                </button>\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n  </div>\n\n  <!-- Formulaire de création d'équipe -->\n  <div class=\"row mb-4\">\n    <div class=\"col-12\">\n      <div class=\"card\">\n        <div class=\"card-header bg-primary text-white\">\n          <h3 class=\"mb-0\">\n            {{ isEditing ? \"Modifier une équipe\" : \"Créer une équipe\" }}\n          </h3>\n        </div>\n        <div class=\"card-body\">\n          <form>\n            <div class=\"mb-3\">\n              <label for=\"name\" class=\"form-label\"\n                >Nom de l'équipe <span class=\"text-danger\">*</span></label\n              >\n              <input\n                #nameInput\n                type=\"text\"\n                class=\"form-control\"\n                id=\"name\"\n                [value]=\"newEquipe.name\"\n                (input)=\"newEquipe.name = nameInput.value\"\n                required\n                [class.is-invalid]=\"\n                  !newEquipe.name && (isEditing || newEquipe.name === '')\n                \"\n                placeholder=\"Entrez le nom de l'équipe\"\n              />\n              <div class=\"invalid-feedback\">Le nom de l'équipe est requis</div>\n            </div>\n            <div class=\"mb-3\">\n              <label for=\"description\" class=\"form-label\">Description</label>\n              <textarea\n                #descInput\n                class=\"form-control\"\n                id=\"description\"\n                [value]=\"newEquipe.description || ''\"\n                (input)=\"newEquipe.description = descInput.value\"\n                rows=\"3\"\n                placeholder=\"Entrez une description pour cette équipe\"\n              ></textarea>\n              <small class=\"text-muted\"\n                >Une brève description de l'équipe et de son objectif</small\n              >\n            </div>\n            <div class=\"d-flex\">\n              <button\n                type=\"button\"\n                class=\"btn btn-primary\"\n                [disabled]=\"!newEquipe.name || loading\"\n                (click)=\"isEditing ? updateSelectedEquipe() : addEquipe()\"\n              >\n                <span\n                  *ngIf=\"loading\"\n                  class=\"spinner-border spinner-border-sm me-1\"\n                  role=\"status\"\n                  aria-hidden=\"true\"\n                ></span>\n                {{ isEditing ? \"Mettre à jour\" : \"Créer\" }}\n              </button>\n              <button\n                *ngIf=\"isEditing\"\n                type=\"button\"\n                class=\"btn btn-secondary ms-2\"\n                (click)=\"cancelEdit()\"\n              >\n                Annuler\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Modal pour gérer les membres -->\n  <div class=\"modal fade\" id=\"membreModal\" tabindex=\"-1\" aria-hidden=\"true\">\n    <div class=\"modal-dialog\">\n      <div class=\"modal-content\">\n        <div class=\"modal-header\">\n          <h5 class=\"modal-title\">Gérer les membres de l'équipe</h5>\n          <button\n            type=\"button\"\n            class=\"btn-close\"\n            data-bs-dismiss=\"modal\"\n            aria-label=\"Close\"\n          ></button>\n        </div>\n        <div class=\"modal-body\">\n          <div *ngIf=\"selectedEquipe\">\n            <h6>Équipe: {{ selectedEquipe.name }}</h6>\n\n            <!-- Liste des membres actuels -->\n            <div class=\"mb-3\">\n              <h6>Membres actuels:</h6>\n              <div\n                *ngIf=\"\n                  selectedEquipe.members && selectedEquipe.members.length > 0;\n                  else noMembers\n                \"\n              >\n                <ul class=\"list-group\">\n                  <li\n                    class=\"list-group-item d-flex justify-content-between align-items-center\"\n                    *ngFor=\"let membreId of selectedEquipe.members\"\n                  >\n                    <span>{{ membreId }}</span>\n                    <button\n                      class=\"btn btn-sm btn-danger\"\n                      (click)=\"\n                        removeMembreFromEquipe(selectedEquipe._id, membreId)\n                      \"\n                    >\n                      Retirer\n                    </button>\n                  </li>\n                </ul>\n              </div>\n              <ng-template #noMembers>\n                <p class=\"text-muted\">Aucun membre dans cette équipe</p>\n              </ng-template>\n            </div>\n\n            <!-- Formulaire pour ajouter un membre -->\n            <div class=\"mb-3\">\n              <h6>Ajouter un membre:</h6>\n              <div class=\"input-group mb-2\">\n                <input\n                  #membreIdInput\n                  type=\"text\"\n                  class=\"form-control\"\n                  placeholder=\"ID du membre\"\n                />\n                <button\n                  class=\"btn btn-primary\"\n                  [disabled]=\"\n                    !selectedEquipe ||\n                    !selectedEquipe._id ||\n                    !membreIdInput.value\n                  \"\n                  (click)=\"\n                    addMembreToEquipe(selectedEquipe._id, membreIdInput.value);\n                    membreIdInput.value = ''\n                  \"\n                >\n                  Ajouter\n                </button>\n              </div>\n              <small class=\"text-muted\"\n                >Entrez l'ID du membre à ajouter à l'équipe</small\n              >\n            </div>\n\n            <!-- Informations supplémentaires -->\n            <div class=\"alert alert-info mt-3\">\n              <p class=\"mb-0\">\n                <strong>Note:</strong> Pour ajouter un membre, vous devez\n                d'abord créer le membre dans la section des membres.\n              </p>\n            </div>\n          </div>\n        </div>\n        <div class=\"modal-footer\">\n          <button\n            type=\"button\"\n            class=\"btn btn-secondary\"\n            data-bs-dismiss=\"modal\"\n          >\n            Fermer\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n", "import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { EquipeListComponent } from './equipe-list/equipe-list.component';\nimport { EquipeFormComponent } from './equipe-form/equipe-form.component';\nimport { EquipeDetailComponent } from './equipe-detail/equipe-detail.component';\nimport { TaskListComponent } from './task-list/task-list.component';\nimport { EquipeComponent } from './equipe/equipe.component';\nimport { EquipeLayoutComponent } from './equipe-layout/equipe-layout.component';\n\nconst routes: Routes = [\n  {\n    path: '',\n    component: EquipeLayoutComponent,\n    children: [\n      // Liste des équipes\n      { path: '', component: EquipeListComponent },\n      { path: 'liste', component: EquipeListComponent },\n      { path: 'mes-equipes', component: EquipeListComponent },\n\n      // Formulaire pour ajouter une nouvelle équipe\n      { path: 'ajouter', component: EquipeFormComponent },\n      { path: 'nouveau', component: EquipeFormComponent },\n\n      // Formulaire pour modifier une équipe existante\n      { path: 'modifier/:id', component: EquipeFormComponent },\n\n      // Détails d'une équipe spécifique\n      { path: 'detail/:id', component: EquipeDetailComponent },\n\n      // Gestion des tâches d'une équipe\n      { path: 'tasks/:id', component: TaskListComponent },\n    ],\n  },\n];\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule],\n})\nexport class EquipesRoutingModule {}\n", "import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\nimport { EquipesRoutingModule } from './equipes-routing.module';\nimport { EquipeListComponent } from './equipe-list/equipe-list.component';\nimport { EquipeFormComponent } from './equipe-form/equipe-form.component';\nimport { EquipeDetailComponent } from './equipe-detail/equipe-detail.component';\nimport { TaskListComponent } from './task-list/task-list.component';\nimport { AiChatComponent } from './ai-chat/ai-chat.component';\nimport { EquipeComponent } from './equipe/equipe.component';\nimport { NotificationComponent } from './notification/notification.component';\nimport { HttpClientModule } from '@angular/common/http';\nimport { FormsModule } from '@angular/forms';\nimport { DragDropModule } from '@angular/cdk/drag-drop';\nimport { EquipeLayoutComponent } from './equipe-layout/equipe-layout.component';\n\n@NgModule({\n  declarations: [\n    EquipeListComponent,\n    EquipeFormComponent,\n    EquipeDetailComponent,\n    TaskListComponent,\n    AiChatComponent,\n    EquipeComponent,\n    NotificationComponent,\n    EquipeLayoutComponent,\n  ],\n  imports: [\n    CommonModule,\n    EquipesRoutingModule,\n    FormsModule,\n    DragDropModule,\n    HttpClientModule,\n  ],\n  providers: [],\n})\nexport class EquipesModule {}\n", "import { Component, OnInit, OnDestroy } from '@angular/core';\r\nimport { Subscription } from 'rxjs';\r\nimport { NotificationService, Notification } from 'src/app/services/notification.service';\r\n\r\n@Component({\r\n  selector: 'app-notification',\r\n  template: `\r\n    <div *ngIf=\"notification\"\r\n         class=\"notification-container\"\r\n         [ngClass]=\"'alert-' + notification.type\">\r\n      <div class=\"notification-content\">\r\n        <i class=\"bi\"\r\n           [ngClass]=\"{\r\n             'bi-check-circle-fill': notification.type === 'success',\r\n             'bi-exclamation-triangle-fill': notification.type === 'warning',\r\n             'bi-info-circle-fill': notification.type === 'info',\r\n             'bi-x-circle-fill': notification.type === 'error'\r\n           }\"></i>\r\n        <span>{{ notification.message }}</span>\r\n      </div>\r\n      <button type=\"button\" class=\"btn-close\" (click)=\"closeNotification()\"></button>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    .notification-container {\r\n      position: fixed;\r\n      top: 20px;\r\n      right: 20px;\r\n      min-width: 300px;\r\n      z-index: 9999;\r\n      padding: 15px;\r\n      border-radius: 4px;\r\n      box-shadow: 0 4px 8px rgba(0,0,0,0.1);\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n    }\r\n    .notification-content {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 10px;\r\n    }\r\n    .alert-success {\r\n      background-color: #d4edda;\r\n      border-color: #c3e6cb;\r\n      color: #155724;\r\n    }\r\n    .alert-error {\r\n      background-color: #f8d7da;\r\n      border-color: #f5c6cb;\r\n      color: #721c24;\r\n    }\r\n    .alert-info {\r\n      background-color: #d1ecf1;\r\n      border-color: #bee5eb;\r\n      color: #0c5460;\r\n    }\r\n    .alert-warning {\r\n      background-color: #fff3cd;\r\n      border-color: #ffeeba;\r\n      color: #856404;\r\n    }\r\n  `]\r\n})\r\nexport class NotificationComponent implements OnInit, OnDestroy {\r\n  notification: Notification | null = null;\r\n  private subscription: Subscription = new Subscription();\r\n\r\n  constructor(private notificationService: NotificationService) {}\r\n\r\n  ngOnInit(): void {\r\n    this.subscription = this.notificationService.getNotifications().subscribe(notification => {\r\n      this.notification = notification;\r\n    });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.subscription.unsubscribe();\r\n  }\r\n\r\n  closeNotification(): void {\r\n    this.notificationService.clear();\r\n  }\r\n}", "import { Component, OnInit } from '@angular/core';\nimport {\n  CdkDragDrop,\n  moveItemInArray,\n  transferArrayItem,\n} from '@angular/cdk/drag-drop';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { TaskService } from 'src/app/services/task.service';\nimport { EquipeService } from 'src/app/services/equipe.service';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport { NotificationService } from 'src/app/services/notification.service';\nimport { Task } from 'src/app/models/task.model';\nimport { Equipe } from 'src/app//models/equipe.model';\nimport { User } from 'src/app/models/user.model';\nimport { finalize } from 'rxjs/operators';\n\n@Component({\n  selector: 'app-task-list',\n  templateUrl: './task-list.component.html',\n  styleUrls: ['./task-list.component.css'],\n})\nexport class TaskListComponent implements OnInit {\n  tasks: Task[] = [];\n  teamId: string | null = null;\n  team: Equipe | null = null;\n  loading = false;\n  error: string | null = null;\n  users: User[] = [];\n  newTask!: Task;\n  editingTask: Task | null = null;\n  showTaskForm = false;\n\n  // Filtres\n  statusFilter: string = 'all';\n  priorityFilter: string = 'all';\n  searchTerm: string = '';\n\n  constructor(\n    private taskService: TaskService,\n    private equipeService: EquipeService,\n    private userService: AuthuserService,\n    private route: ActivatedRoute,\n    private router: Router,\n    private notificationService: NotificationService\n  ) {}\n\n  ngOnInit(): void {\n    // Initialiser la nouvelle tâche\n    this.newTask = this.initializeNewTask();\n\n    this.route.paramMap.subscribe((params) => {\n      this.teamId = params.get('id');\n      if (this.teamId) {\n        this.loadTeamDetails(this.teamId);\n        this.loadTasks(this.teamId);\n        this.loadUsers();\n      } else {\n        this.error = \"ID d'équipe manquant\";\n        this.notificationService.showError(\"ID d'équipe manquant\");\n      }\n    });\n  }\n\n  loadTeamDetails(teamId: string): void {\n    this.loading = true;\n\n    // Utiliser les données de test si l'API n'est pas disponible\n    const useMockData = false; // Mettre à true pour utiliser les données de test\n\n    if (useMockData) {\n      // Données de test pour simuler les détails de l'équipe\n      const mockTeam: Equipe = {\n        _id: teamId,\n        name: 'Équipe ' + teamId,\n        description: \"Description de l'équipe \" + teamId,\n        admin: 'admin123',\n        members: [],\n      };\n\n      setTimeout(() => {\n        this.team = mockTeam;\n        this.loading = false;\n        console.log(\"Détails de l'équipe chargés (mock):\", this.team);\n      }, 300);\n    } else {\n      // Utiliser l'API réelle\n      this.equipeService\n        .getEquipe(teamId)\n        .pipe(finalize(() => (this.loading = false)))\n        .subscribe({\n          next: (data) => {\n            this.team = data;\n            console.log(\"Détails de l'équipe chargés depuis l'API:\", this.team);\n          },\n          error: (error) => {\n            console.error(\n              \"Erreur lors du chargement des détails de l'équipe:\",\n              error\n            );\n            this.error = \"Impossible de charger les détails de l'équipe\";\n            this.notificationService.showError(\n              \"Erreur lors du chargement des détails de l'équipe\"\n            );\n\n            // Fallback aux données de test en cas d'erreur\n            const mockTeam: Equipe = {\n              _id: teamId,\n              name: 'Équipe ' + teamId + ' (fallback)',\n              description: \"Description de l'équipe \" + teamId,\n              admin: 'admin123',\n              members: [],\n            };\n\n            this.team = mockTeam;\n          },\n        });\n    }\n  }\n\n  loadTasks(teamId: string): void {\n    this.loading = true;\n\n    // Utiliser les données de test si l'API n'est pas disponible\n    const useMockData = false; // Mettre à true pour utiliser les données de test\n\n    if (useMockData) {\n      // Données de test pour simuler les tâches\n      const mockTasks: Task[] = [\n        {\n          _id: '1',\n          title: 'Tâche 1',\n          description: 'Description de la tâche 1',\n          status: 'todo',\n          priority: 'high',\n          teamId: teamId,\n        },\n        {\n          _id: '2',\n          title: 'Tâche 2',\n          description: 'Description de la tâche 2',\n          status: 'todo',\n          priority: 'medium',\n          teamId: teamId,\n        },\n        {\n          _id: '3',\n          title: 'Tâche 3',\n          description: 'Description de la tâche 3',\n          status: 'in-progress',\n          priority: 'high',\n          teamId: teamId,\n        },\n        {\n          _id: '4',\n          title: 'Tâche 4',\n          description: 'Description de la tâche 4',\n          status: 'done',\n          priority: 'low',\n          teamId: teamId,\n        },\n      ];\n\n      setTimeout(() => {\n        this.tasks = mockTasks;\n        this.sortTasks();\n        this.loading = false;\n        console.log('Tâches chargées (mock):', this.tasks);\n      }, 500);\n    } else {\n      // Utiliser l'API réelle\n      this.taskService\n        .getTasksByTeam(teamId)\n        .pipe(finalize(() => (this.loading = false)))\n        .subscribe({\n          next: (data: Task[]) => {\n            this.tasks = data;\n            this.sortTasks();\n            console.log(\"Tâches chargées depuis l'API:\", this.tasks);\n          },\n          error: (error: any) => {\n            console.error('Erreur lors du chargement des tâches:', error);\n            this.error = 'Impossible de charger les tâches';\n            this.notificationService.showError(\n              'Erreur lors du chargement des tâches'\n            );\n\n            // Fallback aux données de test en cas d'erreur\n            const mockTasks: Task[] = [\n              {\n                _id: '1',\n                title: 'Tâche 1 (fallback)',\n                description: 'Description de la tâche 1',\n                status: 'todo',\n                priority: 'high',\n                teamId: teamId,\n              },\n              {\n                _id: '2',\n                title: 'Tâche 2 (fallback)',\n                description: 'Description de la tâche 2',\n                status: 'todo',\n                priority: 'medium',\n                teamId: teamId,\n              },\n            ];\n\n            this.tasks = mockTasks;\n            this.sortTasks();\n            console.log('Tâches chargées (fallback):', this.tasks);\n          },\n        });\n    }\n  }\n\n  // Gestion du glisser-déposer\n  drop(event: CdkDragDrop<Task[]>) {\n    if (event.previousContainer === event.container) {\n      // Déplacement dans la même liste\n      moveItemInArray(\n        event.container.data,\n        event.previousIndex,\n        event.currentIndex\n      );\n    } else {\n      // Déplacement entre listes\n      transferArrayItem(\n        event.previousContainer.data,\n        event.container.data,\n        event.previousIndex,\n        event.currentIndex\n      );\n\n      // Mettre à jour le statut de la tâche\n      const task = event.container.data[event.currentIndex];\n      let newStatus: 'todo' | 'in-progress' | 'done';\n\n      if (event.container.id === 'todo-list') {\n        newStatus = 'todo';\n      } else if (event.container.id === 'in-progress-list') {\n        newStatus = 'in-progress';\n      } else {\n        newStatus = 'done';\n      }\n\n      if (task._id && task.status !== newStatus) {\n        task.status = newStatus;\n        this.updateTaskStatus(task, newStatus);\n      }\n    }\n  }\n\n  loadUsers(): void {\n    // Utiliser les données de test si l'API n'est pas disponible\n    const useMockData = false; // Mettre à true pour utiliser les données de test\n\n    if (useMockData) {\n      // Données de test pour simuler les utilisateurs\n      const mockUsers: User[] = [\n        {\n          _id: 'user1',\n          username: 'john_doe',\n          email: '<EMAIL>',\n          role: 'admin',\n          isActive: true,\n        },\n        {\n          _id: 'user2',\n          username: 'jane_smith',\n          email: '<EMAIL>',\n          role: 'student',\n          isActive: true,\n        },\n      ];\n\n      setTimeout(() => {\n        this.users = mockUsers;\n        console.log('Utilisateurs chargés (mock):', this.users);\n      }, 400);\n    } else {\n      // TODO: Implémenter l'API réelle pour récupérer les utilisateurs\n      // Pour l'instant, utiliser les données mockées\n      const mockUsers: User[] = [\n        {\n          _id: 'user1',\n          username: 'john_doe',\n          email: '<EMAIL>',\n          role: 'admin',\n          isActive: true,\n        },\n        {\n          _id: 'user2',\n          username: 'jane_smith',\n          email: '<EMAIL>',\n          role: 'student',\n          isActive: true,\n        },\n      ];\n\n      this.users = mockUsers;\n      console.log('Utilisateurs chargés (mock API):', this.users);\n    }\n  }\n\n  getUserName(userId: string): string {\n    const user = this.users.find((u) => u._id === userId || u.id === userId);\n    if (user) {\n      if (user.firstName && user.lastName) {\n        return `${user.firstName} ${user.lastName}`;\n      } else if (user.name) {\n        return user.name;\n      }\n    }\n    return 'Utilisateur inconnu';\n  }\n\n  createTask(): void {\n    if (!this.teamId) {\n      this.notificationService.showError(\"ID d'équipe manquant\");\n      return;\n    }\n\n    this.newTask.teamId = this.teamId;\n\n    this.loading = true;\n    this.taskService\n      .createTask(this.newTask)\n      .pipe(finalize(() => (this.loading = false)))\n      .subscribe({\n        next: (data: Task) => {\n          this.tasks.push(data);\n          this.sortTasks();\n          this.newTask = this.initializeNewTask();\n          this.showTaskForm = false;\n          this.notificationService.showSuccess('Tâche créée avec succès');\n        },\n        error: (error: any) => {\n          console.error('Erreur lors de la création de la tâche:', error);\n          this.notificationService.showError(\n            'Erreur lors de la création de la tâche'\n          );\n        },\n      });\n  }\n\n  updateTask(): void {\n    if (!this.editingTask || !this.editingTask._id) {\n      this.notificationService.showError('Tâche invalide');\n      return;\n    }\n\n    this.loading = true;\n    this.taskService\n      .updateTask(this.editingTask._id, this.editingTask)\n      .pipe(finalize(() => (this.loading = false)))\n      .subscribe({\n        next: (data: Task) => {\n          const index = this.tasks.findIndex((t) => t._id === data._id);\n          if (index !== -1) {\n            this.tasks[index] = data;\n          }\n          this.editingTask = null;\n          this.notificationService.showSuccess('Tâche mise à jour avec succès');\n        },\n        error: (error: any) => {\n          console.error('Erreur lors de la mise à jour de la tâche:', error);\n          this.notificationService.showError(\n            'Erreur lors de la mise à jour de la tâche'\n          );\n        },\n      });\n  }\n\n  deleteTask(id: string): void {\n    if (confirm('Êtes-vous sûr de vouloir supprimer cette tâche ?')) {\n      this.loading = true;\n      this.taskService\n        .deleteTask(id)\n        .pipe(finalize(() => (this.loading = false)))\n        .subscribe({\n          next: () => {\n            this.tasks = this.tasks.filter((t) => t._id !== id);\n            this.notificationService.showSuccess('Tâche supprimée avec succès');\n          },\n          error: (error: any) => {\n            console.error('Erreur lors de la suppression de la tâche:', error);\n            this.notificationService.showError(\n              'Erreur lors de la suppression de la tâche'\n            );\n          },\n        });\n    }\n  }\n\n  updateTaskStatus(task: Task, status: 'todo' | 'in-progress' | 'done'): void {\n    if (!task._id) return;\n\n    this.loading = true;\n    this.taskService\n      .updateTaskStatus(task._id, status)\n      .pipe(finalize(() => (this.loading = false)))\n      .subscribe({\n        next: (data: Task) => {\n          const index = this.tasks.findIndex((t) => t._id === data._id);\n          if (index !== -1) {\n            this.tasks[index] = data;\n          }\n          this.notificationService.showSuccess('Statut de la tâche mis à jour');\n        },\n        error: (error: any) => {\n          console.error('Erreur lors de la mise à jour du statut:', error);\n          this.notificationService.showError(\n            'Erreur lors de la mise à jour du statut'\n          );\n        },\n      });\n  }\n\n  editTask(task: Task): void {\n    this.editingTask = { ...task };\n  }\n\n  cancelEdit(): void {\n    this.editingTask = null;\n  }\n\n  toggleTaskForm(): void {\n    this.showTaskForm = !this.showTaskForm;\n    if (this.showTaskForm) {\n      this.newTask = this.initializeNewTask();\n    }\n  }\n\n  initializeNewTask(): Task {\n    return {\n      title: '',\n      description: '',\n      status: 'todo',\n      priority: 'medium',\n      teamId: this.teamId || '',\n      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Par défaut, une semaine à partir d'aujourd'hui\n    };\n  }\n\n  sortTasks(): void {\n    // Trier par priorité (high > medium > low) puis par statut (todo > in-progress > done)\n    this.tasks.sort((a, b) => {\n      const priorityOrder: { [key: string]: number } = {\n        high: 0,\n        medium: 1,\n        low: 2,\n      };\n      const statusOrder: { [key: string]: number } = {\n        todo: 0,\n        'in-progress': 1,\n        done: 2,\n      };\n\n      // D'abord par priorité\n      if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {\n        return priorityOrder[a.priority] - priorityOrder[b.priority];\n      }\n\n      // Ensuite par statut\n      return statusOrder[a.status] - statusOrder[b.status];\n    });\n  }\n\n  // Méthodes de filtrage\n  filterTasks(): Task[] {\n    return this.tasks.filter((task) => {\n      // Filtre par statut\n      if (this.statusFilter !== 'all' && task.status !== this.statusFilter) {\n        return false;\n      }\n\n      // Filtre par priorité\n      if (\n        this.priorityFilter !== 'all' &&\n        task.priority !== this.priorityFilter\n      ) {\n        return false;\n      }\n\n      // Filtre par terme de recherche\n      if (\n        this.searchTerm &&\n        !task.title.toLowerCase().includes(this.searchTerm.toLowerCase())\n      ) {\n        return false;\n      }\n\n      return true;\n    });\n  }\n\n  // Méthodes pour obtenir les tâches par statut\n  getTodoTasks(): Task[] {\n    return this.tasks.filter(\n      (task) =>\n        task.status === 'todo' &&\n        (this.priorityFilter === 'all' ||\n          task.priority === this.priorityFilter) &&\n        (!this.searchTerm ||\n          task.title.toLowerCase().includes(this.searchTerm.toLowerCase()))\n    );\n  }\n\n  getInProgressTasks(): Task[] {\n    return this.tasks.filter(\n      (task) =>\n        task.status === 'in-progress' &&\n        (this.priorityFilter === 'all' ||\n          task.priority === this.priorityFilter) &&\n        (!this.searchTerm ||\n          task.title.toLowerCase().includes(this.searchTerm.toLowerCase()))\n    );\n  }\n\n  getDoneTasks(): Task[] {\n    return this.tasks.filter(\n      (task) =>\n        task.status === 'done' &&\n        (this.priorityFilter === 'all' ||\n          task.priority === this.priorityFilter) &&\n        (!this.searchTerm ||\n          task.title.toLowerCase().includes(this.searchTerm.toLowerCase()))\n    );\n  }\n\n  // Méthodes pour compter les tâches par statut\n  getTodoTasksCount(): number {\n    return this.tasks.filter((task) => task.status === 'todo').length;\n  }\n\n  getInProgressTasksCount(): number {\n    return this.tasks.filter((task) => task.status === 'in-progress').length;\n  }\n\n  getDoneTasksCount(): number {\n    return this.tasks.filter((task) => task.status === 'done').length;\n  }\n\n  navigateBack(): void {\n    this.router.navigate(['/liste']);\n  }\n}\n", "<div class=\"container-fluid py-5 bg-light\">\n  <div class=\"container\">\n    <!-- En-tête avec titre et actions -->\n    <div class=\"row mb-5\">\n      <div class=\"col-12\">\n        <div\n          class=\"d-flex justify-content-between align-items-center flex-wrap\"\n        >\n          <div>\n            <h1 class=\"display-4 fw-bold text-primary\" *ngIf=\"team\">\n              Tâches: {{ team.name }}\n            </h1>\n            <p class=\"text-muted lead\">Gérez les tâches de votre équipe</p>\n          </div>\n          <div class=\"d-flex gap-2\">\n            <button\n              class=\"btn btn-outline-secondary rounded-pill px-4 py-2\"\n              (click)=\"navigateBack()\"\n            >\n              <i class=\"bi bi-arrow-left me-2\"></i> Retour\n            </button>\n            <button\n              class=\"btn btn-primary rounded-pill px-4 py-2\"\n              (click)=\"toggleTaskForm()\"\n            >\n              <i class=\"bi bi-plus-circle me-2\"></i> Nouvelle tâche\n            </button>\n          </div>\n        </div>\n        <hr class=\"my-4\" />\n      </div>\n    </div>\n\n    <!-- Message de chargement -->\n    <div *ngIf=\"loading\" class=\"row justify-content-center my-5\">\n      <div class=\"col-md-6 text-center\">\n        <div class=\"spinner-grow text-primary mx-1\" role=\"status\">\n          <span class=\"visually-hidden\">Chargement...</span>\n        </div>\n        <div class=\"spinner-grow text-secondary mx-1\" role=\"status\">\n          <span class=\"visually-hidden\">Chargement...</span>\n        </div>\n        <div class=\"spinner-grow text-primary mx-1\" role=\"status\">\n          <span class=\"visually-hidden\">Chargement...</span>\n        </div>\n        <p class=\"mt-3 text-muted\">Chargement des tâches...</p>\n      </div>\n    </div>\n\n    <!-- Message d'erreur -->\n    <div *ngIf=\"error\" class=\"row justify-content-center my-5\">\n      <div class=\"col-md-8\">\n        <div\n          class=\"alert alert-danger shadow-sm border-0 rounded-3 d-flex align-items-center\"\n        >\n          <i class=\"bi bi-exclamation-triangle-fill fs-3 me-3\"></i>\n          <div class=\"flex-grow-1\">\n            {{ error }}\n          </div>\n          <button\n            class=\"btn btn-danger rounded-pill ms-3\"\n            (click)=\"teamId && loadTasks(teamId)\"\n          >\n            <i class=\"bi bi-arrow-clockwise me-1\"></i> Réessayer\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Formulaire de création/édition de tâche -->\n    <div *ngIf=\"showTaskForm || editingTask\" class=\"row mb-5\">\n      <div class=\"col-12\">\n        <div class=\"card border-0 shadow-sm rounded-3\">\n          <div class=\"card-header bg-primary text-white py-3\">\n            <h4 class=\"mb-0\">\n              {{ editingTask ? \"Modifier la tâche\" : \"Nouvelle tâche\" }}\n            </h4>\n          </div>\n          <div class=\"card-body p-4\">\n            <form (ngSubmit)=\"editingTask ? updateTask() : createTask()\">\n              <div class=\"row g-3\">\n                <div class=\"col-md-6\">\n                  <label for=\"taskTitle\" class=\"form-label\">Titre*</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"taskTitle\"\n                    required\n                    [ngModel]=\"editingTask ? editingTask.title : newTask.title\"\n                    (ngModelChange)=\"\n                      editingTask\n                        ? (editingTask.title = $event)\n                        : (newTask.title = $event)\n                    \"\n                    name=\"title\"\n                    placeholder=\"Titre de la tâche\"\n                  />\n                </div>\n                <div class=\"col-md-3\">\n                  <label for=\"taskPriority\" class=\"form-label\">Priorité*</label>\n                  <select\n                    class=\"form-select\"\n                    id=\"taskPriority\"\n                    required\n                    [ngModel]=\"\n                      editingTask ? editingTask.priority : newTask.priority\n                    \"\n                    (ngModelChange)=\"\n                      editingTask\n                        ? (editingTask.priority = $event)\n                        : (newTask.priority = $event)\n                    \"\n                    name=\"priority\"\n                  >\n                    <option value=\"low\">Basse</option>\n                    <option value=\"medium\">Moyenne</option>\n                    <option value=\"high\">Haute</option>\n                  </select>\n                </div>\n                <div class=\"col-md-3\">\n                  <label for=\"taskStatus\" class=\"form-label\">Statut*</label>\n                  <select\n                    class=\"form-select\"\n                    id=\"taskStatus\"\n                    required\n                    [ngModel]=\"\n                      editingTask ? editingTask.status : newTask.status\n                    \"\n                    (ngModelChange)=\"\n                      editingTask\n                        ? (editingTask.status = $event)\n                        : (newTask.status = $event)\n                    \"\n                    name=\"status\"\n                  >\n                    <option value=\"todo\">À faire</option>\n                    <option value=\"in-progress\">En cours</option>\n                    <option value=\"done\">Terminée</option>\n                  </select>\n                </div>\n                <div class=\"col-md-6\">\n                  <label for=\"taskAssignedTo\" class=\"form-label\"\n                    >Assignée à</label\n                  >\n                  <select\n                    class=\"form-select\"\n                    id=\"taskAssignedTo\"\n                    [ngModel]=\"\n                      editingTask ? editingTask.assignedTo : newTask.assignedTo\n                    \"\n                    (ngModelChange)=\"\n                      editingTask\n                        ? (editingTask.assignedTo = $event)\n                        : (newTask.assignedTo = $event)\n                    \"\n                    name=\"assignedTo\"\n                  >\n                    <option [value]=\"null\">Non assignée</option>\n                    <option\n                      *ngFor=\"let user of users\"\n                      [value]=\"user._id || user.id\"\n                    >\n                      {{ getUserName(user._id || user.id || \"\") }}\n                    </option>\n                  </select>\n                </div>\n                <div class=\"col-md-6\">\n                  <label for=\"taskDueDate\" class=\"form-label\"\n                    >Date d'échéance</label\n                  >\n                  <input\n                    type=\"date\"\n                    class=\"form-control\"\n                    id=\"taskDueDate\"\n                    [ngModel]=\"\n                      editingTask ? editingTask.dueDate : newTask.dueDate\n                    \"\n                    (ngModelChange)=\"\n                      editingTask\n                        ? (editingTask.dueDate = $event)\n                        : (newTask.dueDate = $event)\n                    \"\n                    name=\"dueDate\"\n                  />\n                </div>\n                <div class=\"col-12\">\n                  <label for=\"taskDescription\" class=\"form-label\"\n                    >Description</label\n                  >\n                  <textarea\n                    class=\"form-control\"\n                    id=\"taskDescription\"\n                    rows=\"3\"\n                    [ngModel]=\"\n                      editingTask\n                        ? editingTask.description\n                        : newTask.description\n                    \"\n                    (ngModelChange)=\"\n                      editingTask\n                        ? (editingTask.description = $event)\n                        : (newTask.description = $event)\n                    \"\n                    name=\"description\"\n                    placeholder=\"Description détaillée de la tâche\"\n                  ></textarea>\n                </div>\n                <div class=\"col-12 d-flex justify-content-end gap-2 mt-4\">\n                  <button\n                    type=\"button\"\n                    class=\"btn btn-outline-secondary rounded-pill px-4\"\n                    (click)=\"editingTask ? cancelEdit() : toggleTaskForm()\"\n                  >\n                    Annuler\n                  </button>\n                  <button\n                    type=\"submit\"\n                    class=\"btn btn-primary rounded-pill px-4\"\n                  >\n                    {{ editingTask ? \"Mettre à jour\" : \"Créer\" }}\n                  </button>\n                </div>\n              </div>\n            </form>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Filtres et recherche -->\n    <div class=\"row mb-4\" *ngIf=\"tasks.length > 0\">\n      <div class=\"col-12\">\n        <div class=\"card border-0 shadow-sm rounded-3\">\n          <div class=\"card-body p-3\">\n            <div class=\"row g-3\">\n              <div class=\"col-md-4\">\n                <div class=\"input-group\">\n                  <span class=\"input-group-text bg-white border-end-0\">\n                    <i class=\"bi bi-search\"></i>\n                  </span>\n                  <input\n                    type=\"text\"\n                    class=\"form-control border-start-0\"\n                    placeholder=\"Rechercher une tâche...\"\n                    [(ngModel)]=\"searchTerm\"\n                  />\n                </div>\n              </div>\n              <div class=\"col-md-4\">\n                <select class=\"form-select\" [(ngModel)]=\"statusFilter\">\n                  <option value=\"all\">Tous les statuts</option>\n                  <option value=\"todo\">À faire</option>\n                  <option value=\"in-progress\">En cours</option>\n                  <option value=\"done\">Terminées</option>\n                </select>\n              </div>\n              <div class=\"col-md-4\">\n                <select class=\"form-select\" [(ngModel)]=\"priorityFilter\">\n                  <option value=\"all\">Toutes les priorités</option>\n                  <option value=\"high\">Haute</option>\n                  <option value=\"medium\">Moyenne</option>\n                  <option value=\"low\">Basse</option>\n                </select>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Message quand aucune tâche n'est trouvée -->\n    <div\n      *ngIf=\"!loading && !error && tasks.length === 0\"\n      class=\"row justify-content-center my-5\"\n    >\n      <div class=\"col-md-8 text-center\">\n        <div class=\"p-5 bg-white rounded-3 shadow-sm\">\n          <i class=\"bi bi-list-check fs-1 text-muted mb-3\"></i>\n          <h3 class=\"mb-3\">Aucune tâche trouvée</h3>\n          <p class=\"text-muted mb-4\">\n            Commencez par créer une nouvelle tâche pour votre équipe.\n          </p>\n          <button\n            class=\"btn btn-primary rounded-pill px-4 py-2\"\n            (click)=\"toggleTaskForm()\"\n          >\n            <i class=\"bi bi-plus-circle me-2\"></i> Créer une tâche\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Liste des tâches -->\n    <div class=\"row g-4\" *ngIf=\"tasks.length > 0\">\n      <!-- Colonne \"À faire\" -->\n      <div class=\"col-md-4\">\n        <div class=\"card border-0 shadow-sm rounded-3 h-100\">\n          <div class=\"card-header bg-primary text-white py-3\">\n            <h5 class=\"mb-0 d-flex align-items-center\">\n              <i class=\"bi bi-list-task me-2\"></i>\n              À faire\n              <span class=\"badge bg-white text-primary rounded-pill ms-2\">\n                {{ getTodoTasksCount() }}\n              </span>\n            </h5>\n          </div>\n          <div class=\"card-body p-3\">\n            <div\n              class=\"task-list\"\n              cdkDropList\n              #todoList=\"cdkDropList\"\n              [cdkDropListData]=\"getTodoTasks()\"\n              [cdkDropListConnectedTo]=\"[inProgressList, doneList]\"\n              id=\"todo-list\"\n              (cdkDropListDropped)=\"drop($event)\"\n            >\n              <div\n                *ngFor=\"let task of getTodoTasks()\"\n                class=\"task-card mb-3 p-3 rounded-3 shadow-sm\"\n                [ngClass]=\"'priority-' + task.priority\"\n                cdkDrag\n              >\n                <div\n                  class=\"d-flex justify-content-between align-items-start mb-2\"\n                >\n                  <h6 class=\"mb-0 text-truncate\">{{ task.title }}</h6>\n                  <div class=\"dropdown\">\n                    <button\n                      class=\"btn btn-sm btn-link text-dark p-0\"\n                      type=\"button\"\n                      data-bs-toggle=\"dropdown\"\n                    >\n                      <i class=\"bi bi-three-dots-vertical\"></i>\n                    </button>\n                    <ul class=\"dropdown-menu dropdown-menu-end\">\n                      <li>\n                        <button class=\"dropdown-item\" (click)=\"editTask(task)\">\n                          <i class=\"bi bi-pencil me-2\"></i> Modifier\n                        </button>\n                      </li>\n                      <li>\n                        <button\n                          class=\"dropdown-item\"\n                          (click)=\"updateTaskStatus(task, 'in-progress')\"\n                        >\n                          <i class=\"bi bi-arrow-right me-2\"></i> Déplacer vers\n                          \"En cours\"\n                        </button>\n                      </li>\n                      <li>\n                        <button\n                          class=\"dropdown-item\"\n                          (click)=\"updateTaskStatus(task, 'done')\"\n                        >\n                          <i class=\"bi bi-check2-all me-2\"></i> Marquer comme\n                          terminée\n                        </button>\n                      </li>\n                      <li><hr class=\"dropdown-divider\" /></li>\n                      <li>\n                        <button\n                          class=\"dropdown-item text-danger\"\n                          (click)=\"task._id && deleteTask(task._id)\"\n                        >\n                          <i class=\"bi bi-trash me-2\"></i> Supprimer\n                        </button>\n                      </li>\n                    </ul>\n                  </div>\n                </div>\n                <p class=\"small text-muted mb-2 task-description\">\n                  {{ task.description || \"Aucune description\" }}\n                </p>\n                <div class=\"d-flex justify-content-between align-items-center\">\n                  <span\n                    class=\"badge\"\n                    [ngClass]=\"{\n                      'bg-danger': task.priority === 'high',\n                      'bg-warning text-dark': task.priority === 'medium',\n                      'bg-info text-dark': task.priority === 'low'\n                    }\"\n                  >\n                    {{\n                      task.priority === \"high\"\n                        ? \"Haute\"\n                        : task.priority === \"medium\"\n                        ? \"Moyenne\"\n                        : \"Basse\"\n                    }}\n                  </span>\n                  <small class=\"text-muted\" *ngIf=\"task.assignedTo\">\n                    {{ getUserName(task.assignedTo) }}\n                  </small>\n                </div>\n                <!-- Poignée de glisser-déposer -->\n                <div class=\"task-drag-handle\" cdkDragHandle>\n                  <i class=\"bi bi-grip-horizontal\"></i>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Colonne \"En cours\" -->\n      <div class=\"col-md-4\">\n        <div class=\"card border-0 shadow-sm rounded-3 h-100\">\n          <div class=\"card-header bg-warning py-3\">\n            <h5 class=\"mb-0 d-flex align-items-center\">\n              <i class=\"bi bi-hourglass-split me-2\"></i>\n              En cours\n              <span class=\"badge bg-white text-warning rounded-pill ms-2\">\n                {{ getInProgressTasksCount() }}\n              </span>\n            </h5>\n          </div>\n          <div class=\"card-body p-3\">\n            <div\n              class=\"task-list\"\n              cdkDropList\n              #inProgressList=\"cdkDropList\"\n              [cdkDropListData]=\"getInProgressTasks()\"\n              [cdkDropListConnectedTo]=\"[todoList, doneList]\"\n              id=\"in-progress-list\"\n              (cdkDropListDropped)=\"drop($event)\"\n            >\n              <div\n                *ngFor=\"let task of getInProgressTasks()\"\n                class=\"task-card mb-3 p-3 rounded-3 shadow-sm\"\n                [ngClass]=\"'priority-' + task.priority\"\n                cdkDrag\n              >\n                <div\n                  class=\"d-flex justify-content-between align-items-start mb-2\"\n                >\n                  <h6 class=\"mb-0 text-truncate\">{{ task.title }}</h6>\n                  <div class=\"dropdown\">\n                    <button\n                      class=\"btn btn-sm btn-link text-dark p-0\"\n                      type=\"button\"\n                      data-bs-toggle=\"dropdown\"\n                    >\n                      <i class=\"bi bi-three-dots-vertical\"></i>\n                    </button>\n                    <ul class=\"dropdown-menu dropdown-menu-end\">\n                      <li>\n                        <button class=\"dropdown-item\" (click)=\"editTask(task)\">\n                          <i class=\"bi bi-pencil me-2\"></i> Modifier\n                        </button>\n                      </li>\n                      <li>\n                        <button\n                          class=\"dropdown-item\"\n                          (click)=\"updateTaskStatus(task, 'todo')\"\n                        >\n                          <i class=\"bi bi-arrow-left me-2\"></i> Déplacer vers \"À\n                          faire\"\n                        </button>\n                      </li>\n                      <li>\n                        <button\n                          class=\"dropdown-item\"\n                          (click)=\"updateTaskStatus(task, 'done')\"\n                        >\n                          <i class=\"bi bi-check2-all me-2\"></i> Marquer comme\n                          terminée\n                        </button>\n                      </li>\n                      <li><hr class=\"dropdown-divider\" /></li>\n                      <li>\n                        <button\n                          class=\"dropdown-item text-danger\"\n                          (click)=\"task._id && deleteTask(task._id)\"\n                        >\n                          <i class=\"bi bi-trash me-2\"></i> Supprimer\n                        </button>\n                      </li>\n                    </ul>\n                  </div>\n                </div>\n                <p class=\"small text-muted mb-2 task-description\">\n                  {{ task.description || \"Aucune description\" }}\n                </p>\n                <div class=\"d-flex justify-content-between align-items-center\">\n                  <span\n                    class=\"badge\"\n                    [ngClass]=\"{\n                      'bg-danger': task.priority === 'high',\n                      'bg-warning text-dark': task.priority === 'medium',\n                      'bg-info text-dark': task.priority === 'low'\n                    }\"\n                  >\n                    {{\n                      task.priority === \"high\"\n                        ? \"Haute\"\n                        : task.priority === \"medium\"\n                        ? \"Moyenne\"\n                        : \"Basse\"\n                    }}\n                  </span>\n                  <small class=\"text-muted\" *ngIf=\"task.assignedTo\">\n                    {{ getUserName(task.assignedTo) }}\n                  </small>\n                </div>\n                <!-- Poignée de glisser-déposer -->\n                <div class=\"task-drag-handle\" cdkDragHandle>\n                  <i class=\"bi bi-grip-horizontal\"></i>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Colonne \"Terminées\" -->\n      <div class=\"col-md-4\">\n        <div class=\"card border-0 shadow-sm rounded-3 h-100\">\n          <div class=\"card-header bg-success text-white py-3\">\n            <h5 class=\"mb-0 d-flex align-items-center\">\n              <i class=\"bi bi-check2-all me-2\"></i>\n              Terminées\n              <span class=\"badge bg-white text-success rounded-pill ms-2\">\n                {{ getDoneTasksCount() }}\n              </span>\n            </h5>\n          </div>\n          <div class=\"card-body p-3\">\n            <div\n              class=\"task-list\"\n              cdkDropList\n              #doneList=\"cdkDropList\"\n              [cdkDropListData]=\"getDoneTasks()\"\n              [cdkDropListConnectedTo]=\"[todoList, inProgressList]\"\n              id=\"done-list\"\n              (cdkDropListDropped)=\"drop($event)\"\n            >\n              <div\n                *ngFor=\"let task of getDoneTasks()\"\n                class=\"task-card mb-3 p-3 rounded-3 shadow-sm completed-task\"\n                [ngClass]=\"'priority-' + task.priority\"\n                cdkDrag\n              >\n                <div\n                  class=\"d-flex justify-content-between align-items-start mb-2\"\n                >\n                  <h6 class=\"mb-0 text-truncate\">{{ task.title }}</h6>\n                  <div class=\"dropdown\">\n                    <button\n                      class=\"btn btn-sm btn-link text-dark p-0\"\n                      type=\"button\"\n                      data-bs-toggle=\"dropdown\"\n                    >\n                      <i class=\"bi bi-three-dots-vertical\"></i>\n                    </button>\n                    <ul class=\"dropdown-menu dropdown-menu-end\">\n                      <li>\n                        <button class=\"dropdown-item\" (click)=\"editTask(task)\">\n                          <i class=\"bi bi-pencil me-2\"></i> Modifier\n                        </button>\n                      </li>\n                      <li>\n                        <button\n                          class=\"dropdown-item\"\n                          (click)=\"updateTaskStatus(task, 'todo')\"\n                        >\n                          <i class=\"bi bi-arrow-left me-2\"></i> Déplacer vers \"À\n                          faire\"\n                        </button>\n                      </li>\n                      <li>\n                        <button\n                          class=\"dropdown-item\"\n                          (click)=\"updateTaskStatus(task, 'in-progress')\"\n                        >\n                          <i class=\"bi bi-arrow-left me-2\"></i> Déplacer vers\n                          \"En cours\"\n                        </button>\n                      </li>\n                      <li><hr class=\"dropdown-divider\" /></li>\n                      <li>\n                        <button\n                          class=\"dropdown-item text-danger\"\n                          (click)=\"task._id && deleteTask(task._id)\"\n                        >\n                          <i class=\"bi bi-trash me-2\"></i> Supprimer\n                        </button>\n                      </li>\n                    </ul>\n                  </div>\n                </div>\n                <p class=\"small text-muted mb-2 task-description\">\n                  {{ task.description || \"Aucune description\" }}\n                </p>\n                <div class=\"d-flex justify-content-between align-items-center\">\n                  <span\n                    class=\"badge\"\n                    [ngClass]=\"{\n                      'bg-danger': task.priority === 'high',\n                      'bg-warning text-dark': task.priority === 'medium',\n                      'bg-info text-dark': task.priority === 'low'\n                    }\"\n                  >\n                    {{\n                      task.priority === \"high\"\n                        ? \"Haute\"\n                        : task.priority === \"medium\"\n                        ? \"Moyenne\"\n                        : \"Basse\"\n                    }}\n                  </span>\n                  <small class=\"text-muted\" *ngIf=\"task.assignedTo\">\n                    {{ getUserName(task.assignedTo) }}\n                  </small>\n                </div>\n                <!-- Poignée de glisser-déposer -->\n                <div class=\"task-drag-handle\" cdkDragHandle>\n                  <i class=\"bi bi-grip-horizontal\"></i>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "names": ["finalize", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵproperty", "ɵɵpureFunction2", "_c0", "message_r6", "role", "ɵɵadvance", "ɵɵpureFunction1", "_c1", "_c2", "_c3", "content", "ɵɵsanitizeHtml", "ɵɵtextInterpolate2", "ctx_r1", "getCurrentTime", "_c5", "ctx_r12", "getColorForIndex", "i_r11", "_c6", "getGradientForIndex", "_c7", "ɵɵtextInterpolate", "entity_r10", "assignedTo", "ɵɵpureFunction3", "_c8", "task_r15", "priority", "title", "_c9", "ɵɵtextInterpolate1", "description", "ɵɵtemplate", "AiChatComponent_div_7_div_14_div_14_Template", "AiChatComponent_div_7_div_14_div_22_Template", "ctx_r8", "getIconForModule", "name", "tasks", "length", "task_r20", "AiChatComponent_div_7_div_16_div_12_Template", "i_r18", "ɵɵattribute", "entity_r17", "AiChatComponent_div_7_div_14_Template", "AiChatComponent_div_7_div_16_Template", "ɵɵlistener", "AiChatComponent_div_7_Template_button_click_50_listener", "ɵɵrestoreView", "_r22", "ctx_r21", "ɵɵnextContext", "ɵɵresetView", "createTasks", "ctx_r3", "generatedContent", "projectTitle", "entities", "countTasks", "team", "members", "ctx_r4", "error", "AiChatComponent_div_10_Template_input_ngModelChange_12_listener", "$event", "_r24", "ctx_r23", "AiChatComponent_div_10_Template_button_click_17_listener", "ctx_r25", "generateTasks", "ctx_r5", "isGenerating", "trim", "AiChatComponent", "constructor", "aiService", "taskService", "notificationService", "messages", "userQuestion", "isAskingQuestion", "ngOnInit", "push", "showError", "memberCount", "effectiveMemberCount", "Math", "max", "showWarning", "console", "log", "loadingMessageIndex", "teamMembers", "map", "memberId", "index", "id", "generateProjectTasks", "pipe", "subscribe", "next", "result", "handleGenerationError", "showSuccess", "message", "messageIndex", "errorDetails", "askQuestion", "question", "projectContext", "askProjectQuestion", "response", "_id", "createdCount", "totalTasks", "member", "userId", "memberNameToIdMap", "for<PERSON>ach", "entity", "assignedMemberId", "memberName", "randomMemberIndex", "floor", "random", "taskData", "task", "status", "teamId", "createTask", "reduce", "total", "gradients", "colors", "moduleName", "toLowerCase", "includes", "now", "Date", "hours", "getHours", "toString", "padStart", "minutes", "getMinutes", "ɵɵdirectiveInject", "i1", "AiService", "i2", "TaskService", "i3", "NotificationService", "selectors", "inputs", "decls", "vars", "consts", "template", "AiChatComponent_Template", "rf", "ctx", "AiChatComponent_div_5_Template", "AiChatComponent_div_6_Template", "AiChatComponent_div_7_Template", "AiChatComponent_div_8_Template", "AiChatComponent_div_10_Template", "AiChatComponent_Template_input_ngModelChange_16_listener", "AiChatComponent_Template_input_keyup_enter_16_listener", "AiChatComponent_Template_button_click_17_listener", "EquipeDetailComponent_div_0_div_122_div_4_Template_button_click_14_listener", "restoredCtx", "_r10", "membre_r8", "$implicit", "ctx_r9", "removeMembreFromEquipe", "getUserProfession", "user", "getUserName", "user_r15", "ɵɵtextInterpolate4", "firstName", "lastName", "email", "profession", "EquipeDetailComponent_div_0_div_122_div_10_option_8_Template", "EquipeDetailComponent_div_0_div_122_div_10_Template_button_click_26_listener", "_r17", "_r11", "ɵɵreference", "_r13", "ctx_r16", "addMembre", "value", "checked", "ctx_r7", "availableUsers", "EquipeDetailComponent_div_0_div_122_div_4_Template", "EquipeDetailComponent_div_0_div_122_div_9_Template", "EquipeDetailComponent_div_0_div_122_div_10_Template", "ctx_r2", "user_r24", "EquipeDetailComponent_div_0_ng_template_123_div_14_option_8_Template", "EquipeDetailComponent_div_0_ng_template_123_div_14_Template_button_click_26_listener", "_r26", "_r20", "ctx_r19", "EquipeDetailComponent_div_0_ng_template_123_div_13_Template", "EquipeDetailComponent_div_0_ng_template_123_div_14_Template", "EquipeDetailComponent_div_0_Template_button_click_52_listener", "_r28", "ctx_r27", "navigateToTasks", "EquipeDetailComponent_div_0_Template_button_click_55_listener", "ctx_r29", "navigateToEditEquipe", "EquipeDetailComponent_div_0_Template_button_click_59_listener", "ctx_r30", "navigateToEquipeList", "EquipeDetailComponent_div_0_Template_button_click_62_listener", "ctx_r31", "deleteEquipe", "EquipeDetailComponent_div_0_div_122_Template", "EquipeDetailComponent_div_0_ng_template_123_Template", "ɵɵtemplateRefExtractor", "ctx_r0", "equipe", "formatDate", "createdAt", "admin", "_r3", "EquipeDetailComponent_div_1_Template_button_click_8_listener", "_r33", "ctx_r32", "EquipeDetailComponent", "equipeService", "userService", "route", "router", "loading", "equipeId", "newMembre", "memberNames", "snapshot", "paramMap", "get", "loadUsers", "loadEquipe", "mockUsers", "username", "isActive", "setTimeout", "allUsers", "memberUserIds", "m", "filter", "updateMemberNames", "membreId", "find", "u", "getMembreName", "getEquipe", "data", "loadTeamMembers", "getTeamMembers", "navigate", "date", "date<PERSON><PERSON>j", "isNaN", "getTime", "toLocaleDateString", "day", "month", "year", "isAlreadyMember", "some", "alert", "membre", "userName", "<PERSON><PERSON><PERSON>", "addMembreToEquipe", "updateAvailableUsers", "confirm", "EquipeService", "AuthService", "ActivatedRoute", "Router", "EquipeDetailComponent_Template", "EquipeDetailComponent_div_0_Template", "EquipeDetailComponent_div_1_Template", "EquipeFormComponent_div_33_button_45_Template_button_click_0_listener", "isEditMode", "EquipeFormComponent_div_33_Template_form_ngSubmit_11_listener", "_r12", "ctx_r11", "onSubmit", "EquipeFormComponent_div_33_Template_input_input_20_listener", "ctx_r13", "updateName", "EquipeFormComponent_div_33_div_22_Template", "EquipeFormComponent_div_33_Template_textarea_input_31_listener", "_r5", "ctx_r14", "updateDescription", "EquipeFormComponent_div_33_Template_button_click_42_listener", "ctx_r15", "cancel", "EquipeFormComponent_div_33_button_45_Template", "EquipeFormComponent_div_33_span_47_Template", "EquipeFormComponent_div_33_i_48_Template", "nameExists", "submitting", "nameError", "descriptionError", "EquipeFormComponent", "membreService", "checkingName", "existingEquipes", "availableMembers", "currentUserId", "getCurrentUser", "loadAllEquipes", "loadAllMembers", "loadAllUsers", "addButton", "document", "getElementById", "addEventListener", "token", "localStorage", "getItem", "getProfile", "getMembres", "membres", "getAllUsers", "users", "getEquipes", "equipes", "loadMembersDetails", "getUserById", "userData", "findIndex", "checkNameExists", "e", "warn", "equipeToSave", "updateEquipe", "addEquipe", "getMembreEmail", "getMembreProfession", "getMembreRole", "_membreId", "membreName", "MembreService", "i4", "i5", "EquipeFormComponent_Template", "EquipeFormComponent_Template_button_click_28_listener", "EquipeFormComponent_div_31_Template", "EquipeFormComponent_div_32_Template", "EquipeFormComponent_div_33_Template", "NavigationEnd", "Observable", "EquipeLayoutComponent", "location", "sidebarVisible$", "pageTitle", "pageSubtitle", "totalEquipes", "totalMembres", "totalProjets", "loadStatistics", "updatePageTitle", "events", "event", "url", "goBack", "back", "Location", "EquipeLayoutComponent_Template", "EquipeListComponent_div_32_Template_button_click_10_listener", "loadEquipes", "EquipeListComponent_div_33_Template_button_click_7_listener", "_r7", "ctx_r6", "navigateToAddEquipe", "EquipeListComponent_div_34_div_1_Template_button_click_20_listener", "equipe_r9", "ctx_r10", "navigateToEquipeDetail", "EquipeListComponent_div_34_div_1_Template_button_click_24_listener", "EquipeListComponent_div_34_div_1_Template_button_click_26_listener", "EquipeListComponent_div_34_div_1_Template_button_click_28_listener", "ɵɵpipeBind3", "EquipeListComponent_div_34_div_1_Template", "EquipeListComponent", "sort", "a", "b", "localeCompare", "equipeName", "EquipeListComponent_Template", "EquipeListComponent_Template_button_click_28_listener", "EquipeListComponent_div_31_Template", "EquipeListComponent_div_32_Template", "EquipeListComponent_div_33_Template", "EquipeListComponent_div_34_Template", "EquipeComponent_div_1_Template_button_click_2_listener", "EquipeComponent_div_12_tr_15_Template_button_click_10_listener", "_r14", "equipe_r12", "editEquipe", "EquipeComponent_div_12_tr_15_Template_button_click_12_listener", "EquipeComponent_div_12_tr_15_Template_button_click_14_listener", "showMembreModal", "EquipeComponent_div_12_tr_15_Template", "EquipeComponent_button_41_Template_button_click_0_listener", "_r18", "ctx_r17", "cancelEdit", "EquipeComponent_div_50_div_6_li_2_Template_button_click_3_listener", "membreId_r24", "selectedEquipe", "EquipeComponent_div_50_div_6_li_2_Template", "EquipeComponent_div_50_div_6_Template", "EquipeComponent_div_50_ng_template_7_Template", "EquipeComponent_div_50_Template_button_click_15_listener", "EquipeComponent", "newEquipe", "isEditing", "loadMembres", "successMessage", "updateSelectedEquipe", "updatedEquipe", "modalRef", "window", "bootstrap", "modal", "Modal", "show", "EquipeComponent_Template", "EquipeComponent_div_1_Template", "EquipeComponent_div_2_Template", "EquipeComponent_Template_button_click_8_listener", "EquipeComponent_div_11_Template", "EquipeComponent_div_12_Template", "EquipeComponent_Template_input_input_26_listener", "_r29", "_r4", "EquipeComponent_Template_textarea_input_33_listener", "EquipeComponent_Template_button_click_38_listener", "EquipeComponent_span_39_Template", "EquipeComponent_button_41_Template", "EquipeComponent_div_50_Template", "ɵɵclassProp", "RouterModule", "TaskListComponent", "routes", "path", "component", "children", "EquipesRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "exports", "CommonModule", "NotificationComponent", "HttpClientModule", "FormsModule", "DragDropModule", "EquipesModule", "declarations", "Subscription", "NotificationComponent_div_0_Template_button_click_5_listener", "_r2", "closeNotification", "notification", "type", "ɵɵpureFunction4", "subscription", "getNotifications", "ngOnDestroy", "unsubscribe", "clear", "NotificationComponent_Template", "NotificationComponent_div_0_Template", "moveItemInArray", "transferArrayItem", "TaskListComponent_div_18_Template_button_click_6_listener", "_r8", "loadTasks", "user_r10", "TaskListComponent_div_19_Template_form_ngSubmit_7_listener", "editingTask", "updateTask", "TaskListComponent_div_19_Template_input_ngModelChange_12_listener", "newTask", "TaskListComponent_div_19_Template_select_ngModelChange_16_listener", "TaskListComponent_div_19_Template_select_ngModelChange_26_listener", "TaskListComponent_div_19_Template_select_ngModelChange_36_listener", "TaskListComponent_div_19_option_39_Template", "TaskListComponent_div_19_Template_input_ngModelChange_43_listener", "dueDate", "TaskListComponent_div_19_Template_textarea_ngModelChange_47_listener", "ctx_r18", "TaskListComponent_div_19_Template_button_click_49_listener", "toggleTaskForm", "TaskListComponent_div_20_Template_input_ngModelChange_9_listener", "_r21", "ctx_r20", "searchTerm", "TaskListComponent_div_20_Template_select_ngModelChange_11_listener", "ctx_r22", "statusFilter", "TaskListComponent_div_20_Template_select_ngModelChange_21_listener", "priorityFilter", "TaskListComponent_div_21_Template_button_click_8_listener", "_r25", "ctx_r24", "ctx_r33", "task_r32", "TaskListComponent_div_22_div_12_Template_button_click_9_listener", "_r36", "ctx_r35", "editTask", "TaskListComponent_div_22_div_12_Template_button_click_13_listener", "ctx_r37", "updateTaskStatus", "TaskListComponent_div_22_div_12_Template_button_click_17_listener", "ctx_r38", "TaskListComponent_div_22_div_12_Template_button_click_23_listener", "ctx_r39", "deleteTask", "TaskListComponent_div_22_div_12_small_31_Template", "ctx_r41", "task_r40", "TaskListComponent_div_22_div_24_Template_button_click_9_listener", "_r44", "ctx_r43", "TaskListComponent_div_22_div_24_Template_button_click_13_listener", "ctx_r45", "TaskListComponent_div_22_div_24_Template_button_click_17_listener", "ctx_r46", "TaskListComponent_div_22_div_24_Template_button_click_23_listener", "ctx_r47", "TaskListComponent_div_22_div_24_small_31_Template", "ctx_r49", "task_r48", "TaskListComponent_div_22_div_36_Template_button_click_9_listener", "_r52", "ctx_r51", "TaskListComponent_div_22_div_36_Template_button_click_13_listener", "ctx_r53", "TaskListComponent_div_22_div_36_Template_button_click_17_listener", "ctx_r54", "TaskListComponent_div_22_div_36_Template_button_click_23_listener", "ctx_r55", "TaskListComponent_div_22_div_36_small_31_Template", "TaskListComponent_div_22_Template_div_cdkDropListDropped_10_listener", "_r57", "ctx_r56", "drop", "TaskListComponent_div_22_div_12_Template", "TaskListComponent_div_22_Template_div_cdkDropListDropped_22_listener", "ctx_r58", "TaskListComponent_div_22_div_24_Template", "TaskListComponent_div_22_Template_div_cdkDropListDropped_34_listener", "ctx_r59", "TaskListComponent_div_22_div_36_Template", "getTodoTasksCount", "getTodoTasks", "_r30", "getInProgressTasksCount", "getInProgressTasks", "getDoneTasksCount", "getDoneTasks", "showTaskForm", "initializeNewTask", "params", "loadTeamDetails", "useMockData", "mockTeam", "mockTasks", "sortTasks", "getTasksByTeam", "previousContainer", "container", "previousIndex", "currentIndex", "newStatus", "t", "priorityOrder", "high", "medium", "low", "statusOrder", "todo", "done", "filterTasks", "navigateBack", "AuthuserService", "TaskListComponent_Template", "TaskListComponent_h1_6_Template", "TaskListComponent_Template_button_click_10_listener", "TaskListComponent_Template_button_click_13_listener", "TaskListComponent_div_17_Template", "TaskListComponent_div_18_Template", "TaskListComponent_div_19_Template", "TaskListComponent_div_20_Template", "TaskListComponent_div_21_Template", "TaskListComponent_div_22_Template"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}