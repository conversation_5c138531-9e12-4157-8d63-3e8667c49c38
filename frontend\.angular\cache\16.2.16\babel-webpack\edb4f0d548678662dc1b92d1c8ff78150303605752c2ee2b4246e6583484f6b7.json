{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReunionsRoutingModule } from './reunions-routing.module';\nimport { ReunionListComponent } from './reunion-list/reunion-list.component';\nimport { ReunionDetailComponent } from './reunion-detail/reunion-detail.component';\nimport { ReunionFormComponent } from './reunion-form/reunion-form.component';\nimport { RouterModule } from '@angular/router';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { PipesModule } from '../../../pipes/pipes.module';\nimport { ReunionEditComponent } from './reunion-edit/reunion-edit.component';\nexport let ReunionsModule = class ReunionsModule {};\nReunionsModule = __decorate([NgModule({\n  declarations: [ReunionListComponent, ReunionDetailComponent, ReunionFormComponent, ReunionEditComponent],\n  imports: [CommonModule, ReunionsRoutingModule, RouterModule, FormsModule, ReactiveFormsModule, PipesModule]\n})], ReunionsModule);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}