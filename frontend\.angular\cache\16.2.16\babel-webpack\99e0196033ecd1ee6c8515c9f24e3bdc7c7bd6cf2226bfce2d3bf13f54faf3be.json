{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../services/message.service\";\nimport * as i2 from \"../../../../services/auth.service\";\nimport * as i3 from \"../../../../services/toast.service\";\nimport * as i4 from \"../../../../services/theme.service\";\nimport * as i5 from \"../../../../services/mock-data.service\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nconst _c0 = [\"searchInput\"];\nfunction MessageLayoutComponent_div_15_div_3_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 43);\n  }\n}\nfunction MessageLayoutComponent_div_15_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵlistener(\"click\", function MessageLayoutComponent_div_15_div_3_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const theme_r8 = restoredCtx.$implicit;\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.selectTheme(theme_r8.name));\n    });\n    i0.ɵɵelement(1, \"div\", 40);\n    i0.ɵɵelementStart(2, \"span\", 41);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, MessageLayoutComponent_div_15_div_3_i_4_Template, 1, 0, \"i\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const theme_r8 = ctx.$implicit;\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"background\", theme_r8.gradients.primary);\n    i0.ɵɵclassProp(\"border-white\", (ctx_r7.currentTheme == null ? null : ctx_r7.currentTheme.name) === theme_r8.name)(\"border-gray-500\", (ctx_r7.currentTheme == null ? null : ctx_r7.currentTheme.name) !== theme_r8.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(theme_r8.displayName);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r7.currentTheme == null ? null : ctx_r7.currentTheme.name) === theme_r8.name);\n  }\n}\nfunction MessageLayoutComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"div\", 37);\n    i0.ɵɵtext(2, \" Choisir un th\\u00E8me \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_15_div_3_Template, 5, 8, \"div\", 38);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.availableThemes);\n  }\n}\nfunction MessageLayoutComponent_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function MessageLayoutComponent_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.clearSearch());\n    });\n    i0.ɵɵelement(1, \"i\", 45);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageLayoutComponent_span_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 46);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.notifications.length > 9 ? \"9+\" : ctx_r3.notifications.length, \" \");\n  }\n}\nfunction MessageLayoutComponent_div_38_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵlistener(\"click\", function MessageLayoutComponent_div_38_div_1_div_3_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r20);\n      const result_r18 = restoredCtx.$implicit;\n      const ctx_r19 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r19.selectConversation(result_r18));\n    });\n    i0.ɵɵelementStart(1, \"div\", 4);\n    i0.ɵɵelement(2, \"img\", 55);\n    i0.ɵɵelementStart(3, \"div\", 56)(4, \"h4\", 57);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 58);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const result_r18 = ctx.$implicit;\n    const ctx_r17 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r17.getConversationAvatar(result_r18), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r17.getConversationName(result_r18));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r17.getConversationName(result_r18), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r17.getLastMessagePreview(result_r18), \" \");\n  }\n}\nfunction MessageLayoutComponent_div_38_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"div\", 52);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_38_div_1_div_3_Template, 8, 4, \"div\", 53);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" R\\u00E9sultats de recherche (\", ctx_r14.searchResults.length, \") \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r14.searchResults);\n  }\n}\nfunction MessageLayoutComponent_div_38_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵelement(1, \"i\", 60);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Aucun r\\u00E9sultat trouv\\u00E9\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageLayoutComponent_div_38_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵelement(1, \"div\", 65);\n    i0.ɵɵelementStart(2, \"p\", 66);\n    i0.ɵɵtext(3, \"Chargement des conversations...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageLayoutComponent_div_38_div_3_div_2_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 73);\n  }\n}\nfunction MessageLayoutComponent_div_38_div_3_div_2_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 74);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const conversation_r25 = i0.ɵɵnextContext().$implicit;\n    const ctx_r27 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r27.getUnreadCount(conversation_r25) > 99 ? \"99+\" : ctx_r27.getUnreadCount(conversation_r25), \" \");\n  }\n}\nfunction MessageLayoutComponent_div_38_div_3_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵlistener(\"click\", function MessageLayoutComponent_div_38_div_3_div_2_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r30);\n      const conversation_r25 = restoredCtx.$implicit;\n      const ctx_r29 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r29.selectConversation(conversation_r25));\n    });\n    i0.ɵɵelementStart(1, \"div\", 4)(2, \"div\", 9);\n    i0.ɵɵelement(3, \"img\", 55);\n    i0.ɵɵtemplate(4, MessageLayoutComponent_div_38_div_3_div_2_div_4_Template, 1, 0, \"div\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 56)(6, \"div\", 69)(7, \"h4\", 57);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 70);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 71)(12, \"p\", 58);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, MessageLayoutComponent_div_38_div_3_div_2_span_14_Template, 2, 1, \"span\", 72);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const conversation_r25 = ctx.$implicit;\n    const ctx_r22 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"bg-gray-700\", ctx_r22.selectedConversationId === conversation_r25.id)(\"border-l-4\", ctx_r22.selectedConversationId === conversation_r25.id)(\"border-blue-500\", ctx_r22.selectedConversationId === conversation_r25.id);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r22.getConversationAvatar(conversation_r25), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r22.getConversationName(conversation_r25));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !conversation_r25.isGroup && ctx_r22.isUserOnline(conversation_r25.participants == null ? null : conversation_r25.participants[0]));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r22.getConversationName(conversation_r25), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r22.formatLastMessageTime(conversation_r25.lastMessage == null ? null : conversation_r25.lastMessage.timestamp), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r22.getLastMessagePreview(conversation_r25), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r22.getUnreadCount(conversation_r25) > 0);\n  }\n}\nfunction MessageLayoutComponent_div_38_div_3_div_3_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Charger plus\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageLayoutComponent_div_38_div_3_div_3_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Chargement...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageLayoutComponent_div_38_div_3_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"button\", 76);\n    i0.ɵɵlistener(\"click\", function MessageLayoutComponent_div_38_div_3_div_3_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r33 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r33.loadMoreConversations());\n    });\n    i0.ɵɵtemplate(2, MessageLayoutComponent_div_38_div_3_div_3_span_2_Template, 2, 0, \"span\", 50);\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_38_div_3_div_3_span_3_Template, 2, 0, \"span\", 50);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r23.isLoadingConversations);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r23.isLoadingConversations);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r23.isLoadingConversations);\n  }\n}\nfunction MessageLayoutComponent_div_38_div_3_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵelement(1, \"i\", 77);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Aucune conversation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 78);\n    i0.ɵɵtext(5, \" Commencez une nouvelle conversation dans l'onglet Contacts \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageLayoutComponent_div_38_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, MessageLayoutComponent_div_38_div_3_div_1_Template, 4, 0, \"div\", 61);\n    i0.ɵɵtemplate(2, MessageLayoutComponent_div_38_div_3_div_2_Template, 15, 13, \"div\", 62);\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_38_div_3_div_3_Template, 4, 3, \"div\", 63);\n    i0.ɵɵtemplate(4, MessageLayoutComponent_div_38_div_3_div_4_Template, 6, 0, \"div\", 49);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.isLoadingConversations && ctx_r16.conversations.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r16.conversations)(\"ngForTrackBy\", ctx_r16.trackByConversationId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.hasMoreConversations);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.conversations.length === 0 && !ctx_r16.isLoadingConversations);\n  }\n}\nfunction MessageLayoutComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtemplate(1, MessageLayoutComponent_div_38_div_1_Template, 4, 2, \"div\", 48);\n    i0.ɵɵtemplate(2, MessageLayoutComponent_div_38_div_2_Template, 4, 0, \"div\", 49);\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_38_div_3_Template, 5, 5, \"div\", 50);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isSearching && ctx_r4.searchResults.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isSearching && ctx_r4.searchResults.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.isSearching);\n  }\n}\nfunction MessageLayoutComponent_div_39_div_1_div_3_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 73);\n  }\n}\nfunction MessageLayoutComponent_div_39_div_1_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 9);\n    i0.ɵɵelement(2, \"img\", 55);\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_39_div_1_div_3_div_1_div_3_Template, 1, 0, \"div\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 56)(5, \"h4\", 57);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 58);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 83);\n    i0.ɵɵelement(10, \"i\", 84);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const result_r39 = i0.ɵɵnextContext().$implicit;\n    const ctx_r40 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", result_r39.image || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", result_r39.username);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r40.isUserOnline(result_r39));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", result_r39.username, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(result_r39.email);\n  }\n}\nfunction MessageLayoutComponent_div_39_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r44 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 81);\n    i0.ɵɵlistener(\"click\", function MessageLayoutComponent_div_39_div_1_div_3_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r44);\n      const result_r39 = restoredCtx.$implicit;\n      const ctx_r43 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r43.isUser(result_r39) ? ctx_r43.startConversationWithUser(result_r39) : null);\n    });\n    i0.ɵɵtemplate(1, MessageLayoutComponent_div_39_div_1_div_3_div_1_Template, 11, 5, \"div\", 82);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const result_r39 = ctx.$implicit;\n    const ctx_r38 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r38.isUser(result_r39));\n  }\n}\nfunction MessageLayoutComponent_div_39_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"div\", 52);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_39_div_1_div_3_Template, 2, 1, \"div\", 80);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r35 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" R\\u00E9sultats de recherche (\", ctx_r35.searchResults.length, \") \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r35.searchResults);\n  }\n}\nfunction MessageLayoutComponent_div_39_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵelement(1, \"i\", 60);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Aucun utilisateur trouv\\u00E9\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageLayoutComponent_div_39_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵelement(1, \"div\", 65);\n    i0.ɵɵelementStart(2, \"p\", 66);\n    i0.ɵɵtext(3, \"Chargement des utilisateurs...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageLayoutComponent_div_39_div_3_div_2_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 73);\n  }\n}\nfunction MessageLayoutComponent_div_39_div_3_div_2_p_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 90);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r49 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", user_r49.role, \" \");\n  }\n}\nfunction MessageLayoutComponent_div_39_div_3_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r54 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 81);\n    i0.ɵɵlistener(\"click\", function MessageLayoutComponent_div_39_div_3_div_2_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r54);\n      const user_r49 = restoredCtx.$implicit;\n      const ctx_r53 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r53.startConversationWithUser(user_r49));\n    });\n    i0.ɵɵelementStart(1, \"div\", 4)(2, \"div\", 9);\n    i0.ɵɵelement(3, \"img\", 55);\n    i0.ɵɵtemplate(4, MessageLayoutComponent_div_39_div_3_div_2_div_4_Template, 1, 0, \"div\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 56)(6, \"h4\", 57);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 58);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, MessageLayoutComponent_div_39_div_3_div_2_p_10_Template, 2, 1, \"p\", 86);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 87)(12, \"div\", 88);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 89);\n    i0.ɵɵelement(15, \"i\", 84);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const user_r49 = ctx.$implicit;\n    const ctx_r46 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", user_r49.image || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", user_r49.username);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r46.isUserOnline(user_r49));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", user_r49.username, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r49.email);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", user_r49.role);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"bg-green-600\", ctx_r46.isUserOnline(user_r49))(\"text-green-100\", ctx_r46.isUserOnline(user_r49))(\"bg-gray-600\", !ctx_r46.isUserOnline(user_r49))(\"text-gray-300\", !ctx_r46.isUserOnline(user_r49));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r46.isUserOnline(user_r49) ? \"En ligne\" : \"Hors ligne\", \" \");\n  }\n}\nfunction MessageLayoutComponent_div_39_div_3_div_3_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Charger plus\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageLayoutComponent_div_39_div_3_div_3_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Chargement...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageLayoutComponent_div_39_div_3_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r58 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"button\", 76);\n    i0.ɵɵlistener(\"click\", function MessageLayoutComponent_div_39_div_3_div_3_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r58);\n      const ctx_r57 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r57.loadMoreUsers());\n    });\n    i0.ɵɵtemplate(2, MessageLayoutComponent_div_39_div_3_div_3_span_2_Template, 2, 0, \"span\", 50);\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_39_div_3_div_3_span_3_Template, 2, 0, \"span\", 50);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r47 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r47.isLoadingUsers);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r47.isLoadingUsers);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r47.isLoadingUsers);\n  }\n}\nfunction MessageLayoutComponent_div_39_div_3_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵelement(1, \"i\", 91);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Aucun utilisateur trouv\\u00E9\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageLayoutComponent_div_39_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, MessageLayoutComponent_div_39_div_3_div_1_Template, 4, 0, \"div\", 61);\n    i0.ɵɵtemplate(2, MessageLayoutComponent_div_39_div_3_div_2_Template, 16, 15, \"div\", 85);\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_39_div_3_div_3_Template, 4, 3, \"div\", 63);\n    i0.ɵɵtemplate(4, MessageLayoutComponent_div_39_div_3_div_4_Template, 4, 0, \"div\", 49);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r37 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r37.isLoadingUsers && ctx_r37.users.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r37.users)(\"ngForTrackBy\", ctx_r37.trackByUserId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r37.hasMoreUsers);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r37.users.length === 0 && !ctx_r37.isLoadingUsers);\n  }\n}\nfunction MessageLayoutComponent_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 79);\n    i0.ɵɵtemplate(1, MessageLayoutComponent_div_39_div_1_Template, 4, 2, \"div\", 48);\n    i0.ɵɵtemplate(2, MessageLayoutComponent_div_39_div_2_Template, 4, 0, \"div\", 49);\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_39_div_3_Template, 5, 5, \"div\", 50);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isSearching && ctx_r5.searchResults.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isSearching && ctx_r5.searchResults.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.isSearching);\n  }\n}\nfunction MessageLayoutComponent_div_40_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵelement(1, \"div\", 65);\n    i0.ɵɵelementStart(2, \"p\", 66);\n    i0.ɵɵtext(3, \"Chargement des notifications...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageLayoutComponent_div_40_div_2_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 100);\n  }\n}\nfunction MessageLayoutComponent_div_40_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r65 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 94);\n    i0.ɵɵlistener(\"click\", function MessageLayoutComponent_div_40_div_2_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r65);\n      const notification_r62 = restoredCtx.$implicit;\n      const ctx_r64 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r64.markNotificationAsRead(notification_r62));\n    });\n    i0.ɵɵelementStart(1, \"div\", 95)(2, \"div\", 96);\n    i0.ɵɵelement(3, \"i\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 56)(5, \"h4\", 57);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 97);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 98);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, MessageLayoutComponent_div_40_div_2_div_11_Template, 1, 0, \"div\", 99);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const notification_r62 = ctx.$implicit;\n    const ctx_r60 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"bg-gray-700\", !notification_r62.isRead);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"bg-blue-600\", notification_r62.type === \"NEW_MESSAGE\")(\"bg-green-600\", notification_r62.type === \"FRIEND_REQUEST\")(\"bg-yellow-600\", notification_r62.type === \"GROUP_INVITE\")(\"bg-purple-600\", notification_r62.type === \"MESSAGE_REACTION\")(\"bg-red-600\", notification_r62.type === \"SYSTEM_ALERT\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"fa-message\", notification_r62.type === \"NEW_MESSAGE\")(\"fa-user-plus\", notification_r62.type === \"FRIEND_REQUEST\")(\"fa-users\", notification_r62.type === \"GROUP_INVITE\")(\"fa-heart\", notification_r62.type === \"MESSAGE_REACTION\")(\"fa-exclamation-triangle\", notification_r62.type === \"SYSTEM_ALERT\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r60.getNotificationTitle(notification_r62), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", notification_r62.content, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r60.formatLastMessageTime(notification_r62.timestamp), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !notification_r62.isRead);\n  }\n}\nfunction MessageLayoutComponent_div_40_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵelement(1, \"i\", 101);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Aucune notification\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 78);\n    i0.ɵɵtext(5, \" Vous serez notifi\\u00E9 des nouveaux messages et \\u00E9v\\u00E9nements \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageLayoutComponent_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 92);\n    i0.ɵɵtemplate(1, MessageLayoutComponent_div_40_div_1_Template, 4, 0, \"div\", 61);\n    i0.ɵɵtemplate(2, MessageLayoutComponent_div_40_div_2_Template, 12, 26, \"div\", 93);\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_40_div_3_Template, 6, 0, \"div\", 49);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isLoadingNotifications && ctx_r6.notifications.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.notifications)(\"ngForTrackBy\", ctx_r6.trackByNotificationId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.notifications.length === 0 && !ctx_r6.isLoadingNotifications);\n  }\n}\nexport class MessageLayoutComponent {\n  constructor(messageService, authService, toastService, themeService, mockDataService, route, router, cdr) {\n    this.messageService = messageService;\n    this.authService = authService;\n    this.toastService = toastService;\n    this.themeService = themeService;\n    this.mockDataService = mockDataService;\n    this.route = route;\n    this.router = router;\n    this.cdr = cdr;\n    // État du composant\n    this.currentUser = null;\n    this.conversations = [];\n    this.users = [];\n    this.notifications = [];\n    // Navigation et UI\n    this.activeTab = 'conversations';\n    this.selectedConversationId = null;\n    this.isMobileMenuOpen = false;\n    this.isSearching = false;\n    // Thème\n    this.currentTheme = null;\n    this.availableThemes = [];\n    this.showThemeSelector = false;\n    // Recherche\n    this.searchQuery = '';\n    this.searchResults = [];\n    // États de chargement\n    this.isLoadingConversations = false;\n    this.isLoadingUsers = false;\n    this.isLoadingNotifications = false;\n    // Pagination\n    this.conversationsPage = 1;\n    this.usersPage = 1;\n    this.hasMoreConversations = true;\n    this.hasMoreUsers = true;\n    // Subscriptions\n    this.subscriptions = [];\n    // Observables\n    this.searchQuery$ = new BehaviorSubject('');\n  }\n  ngOnInit() {\n    this.initializeComponent();\n    this.setupSubscriptions();\n    this.loadInitialData();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  // ============================================================================\n  // MÉTHODES D'INITIALISATION\n  // ============================================================================\n  initializeComponent() {\n    // Récupérer l'utilisateur actuel\n    this.currentUser = this.authService.getCurrentUser();\n    if (!this.currentUser) {\n      this.router.navigate(['/login']);\n      return;\n    }\n    // Initialiser les thèmes\n    this.currentTheme = this.themeService.getCurrentTheme();\n    this.availableThemes = this.themeService.getAvailableThemes();\n    // Écouter les changements de route\n    this.route.params.subscribe(params => {\n      const conversationId = params['conversationId'];\n      if (conversationId) {\n        this.selectedConversationId = conversationId;\n        this.markConversationAsSelected(conversationId);\n      }\n    });\n  }\n  setupSubscriptions() {\n    // Subscription pour les nouveaux messages\n    const messagesSub = this.messageService.subscribeToMessages().subscribe(message => {\n      if (message) {\n        this.handleNewMessage(message);\n      }\n    });\n    // Subscription pour les notifications\n    const notificationsSub = this.messageService.subscribeToNotifications().subscribe(notification => {\n      if (notification) {\n        this.handleNewNotification(notification);\n      }\n    });\n    // Subscription pour la recherche\n    const searchSub = this.searchQuery$.subscribe(query => {\n      this.performSearch(query);\n    });\n    // Subscription pour les changements de thème\n    const themeSub = this.themeService.currentTheme$.subscribe(theme => {\n      this.currentTheme = theme;\n      this.cdr.detectChanges();\n    });\n    this.subscriptions.push(messagesSub, notificationsSub, searchSub, themeSub);\n  }\n  loadInitialData() {\n    this.loadConversations();\n    this.loadUsers();\n    this.loadNotifications();\n    // Charger l'utilisateur actuel depuis les données de test\n    if (!this.currentUser) {\n      this.currentUser = this.mockDataService.getCurrentUser();\n    }\n  }\n  // ============================================================================\n  // MÉTHODES DE CHARGEMENT DES DONNÉES\n  // ============================================================================\n  loadConversations(page = 1) {\n    if (this.isLoadingConversations) return;\n    this.isLoadingConversations = true;\n    this.messageService.getConversations(25, page).subscribe({\n      next: conversations => {\n        if (page === 1) {\n          this.conversations = conversations;\n        } else {\n          this.conversations.push(...conversations);\n        }\n        this.conversationsPage = page;\n        this.hasMoreConversations = conversations.length === 25;\n        this.isLoadingConversations = false;\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.warn('Service principal indisponible, utilisation des données de test:', error);\n        // Fallback sur les données de test\n        this.mockDataService.getConversations().subscribe({\n          next: conversations => {\n            if (page === 1) {\n              this.conversations = conversations;\n            } else {\n              this.conversations.push(...conversations);\n            }\n            this.conversationsPage = page;\n            this.hasMoreConversations = false; // Pas de pagination pour les données de test\n            this.isLoadingConversations = false;\n            this.cdr.detectChanges();\n            if (page === 1) {\n              this.toastService.showInfo('Mode démo - Données de test chargées');\n            }\n          },\n          error: mockError => {\n            console.error('Erreur lors du chargement des données de test:', mockError);\n            this.isLoadingConversations = false;\n            this.toastService.showError('Erreur lors du chargement des conversations');\n          }\n        });\n      }\n    });\n  }\n  loadUsers(page = 1) {\n    if (this.isLoadingUsers) return;\n    this.isLoadingUsers = true;\n    this.messageService.getAllUsers(false, '', page, 25).subscribe({\n      next: users => {\n        if (page === 1) {\n          this.users = users;\n        } else {\n          this.users.push(...users);\n        }\n        this.usersPage = page;\n        this.hasMoreUsers = users.length === 25;\n        this.isLoadingUsers = false;\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.warn('Service principal indisponible, utilisation des données de test:', error);\n        // Fallback sur les données de test\n        this.mockDataService.getUsers().subscribe({\n          next: users => {\n            if (page === 1) {\n              this.users = users;\n            } else {\n              this.users.push(...users);\n            }\n            this.usersPage = page;\n            this.hasMoreUsers = false; // Pas de pagination pour les données de test\n            this.isLoadingUsers = false;\n            this.cdr.detectChanges();\n          },\n          error: mockError => {\n            console.error('Erreur lors du chargement des données de test:', mockError);\n            this.isLoadingUsers = false;\n            this.toastService.showError('Erreur lors du chargement des utilisateurs');\n          }\n        });\n      }\n    });\n  }\n  loadNotifications() {\n    if (this.isLoadingNotifications) return;\n    this.isLoadingNotifications = true;\n    this.messageService.getNotifications().subscribe({\n      next: notifications => {\n        this.notifications = notifications;\n        this.isLoadingNotifications = false;\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.warn('Service principal indisponible, utilisation des données de test:', error);\n        // Fallback sur les données de test\n        this.mockDataService.getNotifications().subscribe({\n          next: notifications => {\n            this.notifications = notifications;\n            this.isLoadingNotifications = false;\n            this.cdr.detectChanges();\n          },\n          error: mockError => {\n            console.error('Erreur lors du chargement des données de test:', mockError);\n            this.isLoadingNotifications = false;\n            this.toastService.showError('Erreur lors du chargement des notifications');\n          }\n        });\n      }\n    });\n  }\n  // ============================================================================\n  // MÉTHODES DE GESTION DES ÉVÉNEMENTS\n  // ============================================================================\n  handleNewMessage(message) {\n    // Mettre à jour la conversation correspondante\n    const conversationIndex = this.conversations.findIndex(conv => conv.id === message.conversationId);\n    if (conversationIndex !== -1) {\n      // Mettre à jour le dernier message\n      this.conversations[conversationIndex].lastMessage = message;\n      // Déplacer la conversation en haut de la liste\n      const conversation = this.conversations.splice(conversationIndex, 1)[0];\n      this.conversations.unshift(conversation);\n      this.cdr.detectChanges();\n    }\n  }\n  handleNewNotification(notification) {\n    // Ajouter la nouvelle notification en haut de la liste\n    this.notifications.unshift(notification);\n    this.cdr.detectChanges();\n    // Afficher une notification toast si ce n'est pas l'onglet actif\n    if (this.activeTab !== 'notifications') {\n      this.toastService.showInfo('Nouvelle notification reçue');\n    }\n  }\n  markConversationAsSelected(conversationId) {\n    // Marquer la conversation comme sélectionnée visuellement\n    this.selectedConversationId = conversationId;\n    this.cdr.detectChanges();\n  }\n  // ============================================================================\n  // MÉTHODES DE NAVIGATION ET UI\n  // ============================================================================\n  switchTab(tab) {\n    this.activeTab = tab;\n    this.searchQuery = '';\n    this.searchResults = [];\n    this.isSearching = false;\n    // Charger les données si nécessaire\n    switch (tab) {\n      case 'conversations':\n        if (this.conversations.length === 0) {\n          this.loadConversations();\n        }\n        break;\n      case 'users':\n        if (this.users.length === 0) {\n          this.loadUsers();\n        }\n        break;\n      case 'notifications':\n        if (this.notifications.length === 0) {\n          this.loadNotifications();\n        }\n        break;\n    }\n  }\n  selectConversation(conversation) {\n    if (!conversation.id) return;\n    this.selectedConversationId = conversation.id;\n    this.router.navigate(['/messages', conversation.id]);\n    // Fermer le menu mobile si ouvert\n    this.isMobileMenuOpen = false;\n  }\n  startConversationWithUser(user) {\n    if (!user.id && !user._id) return;\n    const userId = user.id || user._id;\n    // Créer ou récupérer la conversation avec cet utilisateur\n    this.messageService.createOrGetConversation(userId).subscribe({\n      next: conversation => {\n        this.selectConversation(conversation);\n      },\n      error: error => {\n        console.error('Erreur lors de la création de la conversation:', error);\n        this.toastService.showError('Erreur lors de la création de la conversation');\n      }\n    });\n  }\n  toggleMobileMenu() {\n    this.isMobileMenuOpen = !this.isMobileMenuOpen;\n  }\n  // ============================================================================\n  // MÉTHODES DE RECHERCHE\n  // ============================================================================\n  onSearchInput(event) {\n    const query = event.target.value.trim();\n    this.searchQuery = query;\n    this.searchQuery$.next(query);\n  }\n  performSearch(query) {\n    if (!query) {\n      this.searchResults = [];\n      this.isSearching = false;\n      return;\n    }\n    this.isSearching = true;\n    if (this.activeTab === 'conversations') {\n      this.searchResults = this.conversations.filter(conv => conv.isGroup ? conv.groupName?.toLowerCase().includes(query.toLowerCase()) : conv.participants?.some(p => p.username?.toLowerCase().includes(query.toLowerCase())));\n    } else if (this.activeTab === 'users') {\n      this.searchResults = this.users.filter(user => user.username?.toLowerCase().includes(query.toLowerCase()) || user.email?.toLowerCase().includes(query.toLowerCase()));\n    }\n    this.cdr.detectChanges();\n  }\n  clearSearch() {\n    this.searchQuery = '';\n    this.searchResults = [];\n    this.isSearching = false;\n    this.searchQuery$.next('');\n  }\n  // ============================================================================\n  // MÉTHODES DE PAGINATION\n  // ============================================================================\n  loadMoreConversations() {\n    if (this.hasMoreConversations && !this.isLoadingConversations) {\n      this.loadConversations(this.conversationsPage + 1);\n    }\n  }\n  loadMoreUsers() {\n    if (this.hasMoreUsers && !this.isLoadingUsers) {\n      this.loadUsers(this.usersPage + 1);\n    }\n  }\n  // ============================================================================\n  // MÉTHODES UTILITAIRES POUR LE TEMPLATE\n  // ============================================================================\n  getConversationName(conversation) {\n    if (conversation.isGroup) {\n      return conversation.groupName || 'Groupe sans nom';\n    }\n    if (!this.currentUser) return 'Conversation';\n    const currentUserId = this.currentUser.id || this.currentUser._id;\n    const otherParticipant = conversation.participants?.find(p => (p.id || p._id) !== currentUserId);\n    return otherParticipant?.username || 'Utilisateur inconnu';\n  }\n  getConversationAvatar(conversation) {\n    if (conversation.isGroup) {\n      return conversation.groupPhoto || '/assets/images/default-group.png';\n    }\n    if (!this.currentUser) return '/assets/images/default-avatar.png';\n    const currentUserId = this.currentUser.id || this.currentUser._id;\n    const otherParticipant = conversation.participants?.find(p => (p.id || p._id) !== currentUserId);\n    return otherParticipant?.image || '/assets/images/default-avatar.png';\n  }\n  getLastMessagePreview(conversation) {\n    if (!conversation.lastMessage) return 'Aucun message';\n    const message = conversation.lastMessage;\n    if (message.type === 'TEXT') {\n      return message.content || '';\n    } else if (message.type === 'IMAGE') {\n      return '📷 Image';\n    } else if (message.type === 'FILE') {\n      return '📎 Fichier';\n    } else if (message.type === 'VOICE_MESSAGE') {\n      return '🎤 Message vocal';\n    } else if (message.type === 'VIDEO') {\n      return '🎥 Vidéo';\n    }\n    return 'Message';\n  }\n  formatLastMessageTime(timestamp) {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    const now = new Date();\n    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n    if (diffInHours < 1) {\n      return \"À l'instant\";\n    } else if (diffInHours < 24) {\n      return date.toLocaleTimeString('fr-FR', {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    } else if (diffInHours < 168) {\n      // 7 jours\n      return date.toLocaleDateString('fr-FR', {\n        weekday: 'short'\n      });\n    } else {\n      return date.toLocaleDateString('fr-FR', {\n        day: '2-digit',\n        month: '2-digit'\n      });\n    }\n  }\n  getUnreadCount(conversation) {\n    return conversation.unreadCount || 0;\n  }\n  isUserOnline(user) {\n    return user.isOnline || false;\n  }\n  trackByConversationId(index, conversation) {\n    return conversation.id || conversation._id || index.toString();\n  }\n  trackByUserId(index, user) {\n    return user.id || user._id || index.toString();\n  }\n  trackByNotificationId(index, notification) {\n    return notification.id || notification._id || index.toString();\n  }\n  markNotificationAsRead(notification) {\n    if (!notification.id || notification.isRead) return;\n    this.messageService.markNotificationAsRead(notification.id).subscribe({\n      next: () => {\n        notification.isRead = true;\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.error('Erreur lors du marquage de la notification comme lue:', error);\n        this.toastService.showError('Erreur lors du marquage de la notification');\n      }\n    });\n  }\n  // Type guards pour différencier User et Conversation dans les résultats de recherche\n  isUser(item) {\n    return 'username' in item && 'email' in item;\n  }\n  isConversation(item) {\n    return 'participants' in item || 'isGroup' in item;\n  }\n  getNotificationTitle(notification) {\n    switch (notification.type) {\n      case 'NEW_MESSAGE':\n        return 'Nouveau message';\n      case 'FRIEND_REQUEST':\n        return \"Demande d'ami\";\n      case 'GROUP_INVITE':\n        return 'Invitation de groupe';\n      case 'MESSAGE_REACTION':\n        return 'Réaction à un message';\n      case 'SYSTEM_ALERT':\n        return 'Alerte système';\n      default:\n        return 'Notification';\n    }\n  }\n  // ============================================================================\n  // MÉTHODES DE GESTION DES THÈMES\n  // ============================================================================\n  selectTheme(themeName) {\n    this.themeService.setTheme(themeName);\n    this.showThemeSelector = false;\n    this.toastService.showSuccess(`Thème \"${this.themeService.getCurrentTheme().displayName}\" appliqué`);\n  }\n  static {\n    this.ɵfac = function MessageLayoutComponent_Factory(t) {\n      return new (t || MessageLayoutComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.ToastService), i0.ɵɵdirectiveInject(i4.ThemeService), i0.ɵɵdirectiveInject(i5.MockDataService), i0.ɵɵdirectiveInject(i6.ActivatedRoute), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MessageLayoutComponent,\n      selectors: [[\"app-message-layout\"]],\n      viewQuery: function MessageLayoutComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.searchInput = _t.first);\n        }\n      },\n      decls: 47,\n      vars: 44,\n      consts: [[1, \"message-layout\", \"h-screen\", \"bg-gray-900\", \"text-white\", \"flex\"], [1, \"sidebar\", \"w-80\", \"bg-gray-800\", \"border-r\", \"border-gray-700\", \"flex\", \"flex-col\"], [1, \"sidebar-header\", \"p-4\", \"border-b\", \"border-gray-700\", \"bg-gray-800\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-4\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [1, \"w-10\", \"h-10\", \"rounded-full\", \"border-2\", \"border-blue-500\", 3, \"src\", \"alt\"], [1, \"font-semibold\", \"text-white\"], [1, \"text-sm\", \"text-green-400\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [1, \"relative\"], [\"title\", \"Changer de th\\u00E8me\", 1, \"p-2\", \"rounded-lg\", \"bg-gray-700\", \"hover:bg-gray-600\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-palette\", \"text-blue-400\"], [\"class\", \"absolute top-full right-0 mt-2 bg-gray-800 rounded-lg shadow-lg border border-gray-700 p-2 z-50 min-w-48\", 4, \"ngIf\"], [1, \"md:hidden\", \"p-2\", \"rounded-lg\", \"bg-gray-700\", \"hover:bg-gray-600\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"text-white\"], [\"type\", \"text\", \"placeholder\", \"Rechercher...\", 1, \"w-full\", \"bg-gray-700\", \"border\", \"border-gray-600\", \"rounded-lg\", \"px-4\", \"py-2\", \"pl-10\", \"text-white\", \"placeholder-gray-400\", \"focus:outline-none\", \"focus:border-blue-500\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [\"searchInput\", \"\"], [1, \"fas\", \"fa-search\", \"absolute\", \"left-3\", \"top-3\", \"text-gray-400\"], [\"class\", \"absolute right-3 top-3 text-gray-400 hover:text-white\", 3, \"click\", 4, \"ngIf\"], [1, \"tabs\", \"flex\", \"border-b\", \"border-gray-700\"], [1, \"tab\", \"flex-1\", \"py-3\", \"px-4\", \"text-center\", \"transition-all\", \"duration-200\", 3, \"click\"], [1, \"fas\", \"fa-comments\", \"mb-1\"], [1, \"text-xs\"], [1, \"fas\", \"fa-users\", \"mb-1\"], [1, \"tab\", \"flex-1\", \"py-3\", \"px-4\", \"text-center\", \"transition-all\", \"duration-200\", \"relative\", 3, \"click\"], [1, \"fas\", \"fa-bell\", \"mb-1\"], [\"class\", \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\", 4, \"ngIf\"], [1, \"sidebar-content\", \"flex-1\", \"overflow-y-auto\"], [\"class\", \"conversations-list\", 4, \"ngIf\"], [\"class\", \"users-list\", 4, \"ngIf\"], [\"class\", \"notifications-list\", 4, \"ngIf\"], [1, \"main-content\", \"flex-1\", \"flex\", \"flex-col\"], [1, \"md:hidden\", \"p-4\", \"border-b\", \"border-gray-700\", \"bg-gray-800\"], [1, \"p-2\", \"rounded-lg\", \"bg-gray-700\", \"hover:bg-gray-600\", 3, \"click\"], [1, \"fas\", \"fa-bars\", \"text-white\"], [1, \"flex-1\"], [1, \"absolute\", \"top-full\", \"right-0\", \"mt-2\", \"bg-gray-800\", \"rounded-lg\", \"shadow-lg\", \"border\", \"border-gray-700\", \"p-2\", \"z-50\", \"min-w-48\"], [1, \"text-xs\", \"text-gray-400\", \"mb-2\", \"px-2\"], [\"class\", \"flex items-center space-x-3 p-2 hover:bg-gray-700 rounded cursor-pointer transition-colors\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"p-2\", \"hover:bg-gray-700\", \"rounded\", \"cursor-pointer\", \"transition-colors\", 3, \"click\"], [1, \"w-4\", \"h-4\", \"rounded-full\", \"border-2\"], [1, \"text-white\", \"text-sm\"], [\"class\", \"fas fa-check text-blue-400 text-xs ml-auto\", 4, \"ngIf\"], [1, \"fas\", \"fa-check\", \"text-blue-400\", \"text-xs\", \"ml-auto\"], [1, \"absolute\", \"right-3\", \"top-3\", \"text-gray-400\", \"hover:text-white\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"absolute\", \"-top-1\", \"-right-1\", \"bg-red-500\", \"text-white\", \"text-xs\", \"rounded-full\", \"w-5\", \"h-5\", \"flex\", \"items-center\", \"justify-center\"], [1, \"conversations-list\"], [\"class\", \"search-results\", 4, \"ngIf\"], [\"class\", \"p-8 text-center text-gray-400\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"search-results\"], [1, \"p-3\", \"text-sm\", \"text-gray-400\", \"border-b\", \"border-gray-700\"], [\"class\", \"conversation-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"conversation-item\", \"p-4\", \"hover:bg-gray-700\", \"cursor-pointer\", \"border-b\", \"border-gray-700\", \"transition-colors\", 3, \"click\"], [1, \"w-12\", \"h-12\", \"rounded-full\", 3, \"src\", \"alt\"], [1, \"flex-1\", \"min-w-0\"], [1, \"font-medium\", \"text-white\", \"truncate\"], [1, \"text-sm\", \"text-gray-400\", \"truncate\"], [1, \"p-8\", \"text-center\", \"text-gray-400\"], [1, \"fas\", \"fa-search\", \"text-4xl\", \"mb-4\"], [\"class\", \"p-8 text-center\", 4, \"ngIf\"], [\"class\", \"conversation-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors relative\", 3, \"bg-gray-700\", \"border-l-4\", \"border-blue-500\", \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"p-4 text-center\", 4, \"ngIf\"], [1, \"p-8\", \"text-center\"], [1, \"animate-spin\", \"rounded-full\", \"h-8\", \"w-8\", \"border-b-2\", \"border-blue-500\", \"mx-auto\"], [1, \"text-gray-400\", \"mt-2\"], [1, \"conversation-item\", \"p-4\", \"hover:bg-gray-700\", \"cursor-pointer\", \"border-b\", \"border-gray-700\", \"transition-colors\", \"relative\", 3, \"click\"], [\"class\", \"absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-gray-800\", 4, \"ngIf\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"text-xs\", \"text-gray-400\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mt-1\"], [\"class\", \"bg-blue-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center\", 4, \"ngIf\"], [1, \"absolute\", \"bottom-0\", \"right-0\", \"w-3\", \"h-3\", \"bg-green-500\", \"rounded-full\", \"border-2\", \"border-gray-800\"], [1, \"bg-blue-500\", \"text-white\", \"text-xs\", \"rounded-full\", \"px-2\", \"py-1\", \"min-w-[20px]\", \"text-center\"], [1, \"p-4\", \"text-center\"], [1, \"text-blue-400\", \"hover:text-blue-300\", \"disabled:text-gray-500\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-comments\", \"text-4xl\", \"mb-4\"], [1, \"text-sm\", \"mt-2\"], [1, \"users-list\"], [\"class\", \"user-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"user-item\", \"p-4\", \"hover:bg-gray-700\", \"cursor-pointer\", \"border-b\", \"border-gray-700\", \"transition-colors\", 3, \"click\"], [\"class\", \"flex items-center space-x-3\", 4, \"ngIf\"], [1, \"text-blue-400\"], [1, \"fas\", \"fa-comment\"], [\"class\", \"user-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"text-xs text-gray-500\", 4, \"ngIf\"], [1, \"text-right\"], [1, \"text-xs\", \"px-2\", \"py-1\", \"rounded-full\"], [1, \"text-blue-400\", \"mt-1\"], [1, \"text-xs\", \"text-gray-500\"], [1, \"fas\", \"fa-users\", \"text-4xl\", \"mb-4\"], [1, \"notifications-list\"], [\"class\", \"notification-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors\", 3, \"bg-gray-700\", \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"notification-item\", \"p-4\", \"hover:bg-gray-700\", \"cursor-pointer\", \"border-b\", \"border-gray-700\", \"transition-colors\", 3, \"click\"], [1, \"flex\", \"items-start\", \"space-x-3\"], [1, \"notification-icon\", \"p-2\", \"rounded-full\"], [1, \"text-sm\", \"text-gray-400\", \"mt-1\"], [1, \"text-xs\", \"text-gray-500\", \"mt-2\"], [\"class\", \"w-2 h-2 bg-blue-500 rounded-full\", 4, \"ngIf\"], [1, \"w-2\", \"h-2\", \"bg-blue-500\", \"rounded-full\"], [1, \"fas\", \"fa-bell\", \"text-4xl\", \"mb-4\"]],\n      template: function MessageLayoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4);\n          i0.ɵɵelement(5, \"img\", 5);\n          i0.ɵɵelementStart(6, \"div\")(7, \"h3\", 6);\n          i0.ɵɵtext(8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"p\", 7);\n          i0.ɵɵtext(10, \"En ligne\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"div\", 8)(12, \"div\", 9)(13, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function MessageLayoutComponent_Template_button_click_13_listener() {\n            return ctx.showThemeSelector = !ctx.showThemeSelector;\n          });\n          i0.ɵɵelement(14, \"i\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(15, MessageLayoutComponent_div_15_Template, 4, 1, \"div\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function MessageLayoutComponent_Template_button_click_16_listener() {\n            return ctx.toggleMobileMenu();\n          });\n          i0.ɵɵelement(17, \"i\", 14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"div\", 9)(19, \"input\", 15, 16);\n          i0.ɵɵlistener(\"ngModelChange\", function MessageLayoutComponent_Template_input_ngModelChange_19_listener($event) {\n            return ctx.searchQuery = $event;\n          })(\"input\", function MessageLayoutComponent_Template_input_input_19_listener($event) {\n            return ctx.onSearchInput($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(21, \"i\", 17);\n          i0.ɵɵtemplate(22, MessageLayoutComponent_button_22_Template, 2, 0, \"button\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"div\", 19)(24, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function MessageLayoutComponent_Template_button_click_24_listener() {\n            return ctx.switchTab(\"conversations\");\n          });\n          i0.ɵɵelement(25, \"i\", 21);\n          i0.ɵɵelementStart(26, \"div\", 22);\n          i0.ɵɵtext(27, \"Discussions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function MessageLayoutComponent_Template_button_click_28_listener() {\n            return ctx.switchTab(\"users\");\n          });\n          i0.ɵɵelement(29, \"i\", 23);\n          i0.ɵɵelementStart(30, \"div\", 22);\n          i0.ɵɵtext(31, \"Contacts\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"button\", 24);\n          i0.ɵɵlistener(\"click\", function MessageLayoutComponent_Template_button_click_32_listener() {\n            return ctx.switchTab(\"notifications\");\n          });\n          i0.ɵɵelement(33, \"i\", 25);\n          i0.ɵɵelementStart(34, \"div\", 22);\n          i0.ɵɵtext(35, \"Notifications\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(36, MessageLayoutComponent_span_36_Template, 2, 1, \"span\", 26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"div\", 27);\n          i0.ɵɵtemplate(38, MessageLayoutComponent_div_38_Template, 4, 3, \"div\", 28);\n          i0.ɵɵtemplate(39, MessageLayoutComponent_div_39_Template, 4, 3, \"div\", 29);\n          i0.ɵɵtemplate(40, MessageLayoutComponent_div_40_Template, 4, 4, \"div\", 30);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"div\", 31)(42, \"div\", 32)(43, \"button\", 33);\n          i0.ɵɵlistener(\"click\", function MessageLayoutComponent_Template_button_click_43_listener() {\n            return ctx.toggleMobileMenu();\n          });\n          i0.ɵɵelement(44, \"i\", 34);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(45, \"div\", 35);\n          i0.ɵɵelement(46, \"router-outlet\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"hidden\", !ctx.isMobileMenuOpen)(\"md:flex\", true);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"src\", (ctx.currentUser == null ? null : ctx.currentUser.image) || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx.currentUser == null ? null : ctx.currentUser.username);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.currentUser == null ? null : ctx.currentUser.username, \" \");\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.showThemeSelector);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.searchQuery);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.activeTab === \"conversations\")(\"text-blue-400\", ctx.activeTab === \"conversations\")(\"border-b-2\", ctx.activeTab === \"conversations\")(\"border-blue-500\", ctx.activeTab === \"conversations\")(\"text-gray-400\", ctx.activeTab !== \"conversations\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"active\", ctx.activeTab === \"users\")(\"text-blue-400\", ctx.activeTab === \"users\")(\"border-b-2\", ctx.activeTab === \"users\")(\"border-blue-500\", ctx.activeTab === \"users\")(\"text-gray-400\", ctx.activeTab !== \"users\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"active\", ctx.activeTab === \"notifications\")(\"text-blue-400\", ctx.activeTab === \"notifications\")(\"border-b-2\", ctx.activeTab === \"notifications\")(\"border-blue-500\", ctx.activeTab === \"notifications\")(\"text-gray-400\", ctx.activeTab !== \"notifications\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.notifications.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"conversations\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"users\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"notifications\");\n        }\n      },\n      dependencies: [i7.NgForOf, i7.NgIf, i6.RouterOutlet, i8.DefaultValueAccessor, i8.NgControlStatus, i8.NgModel],\n      styles: [\"\\n\\n\\n\\n\\n.message-layout[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n  font-family: \\\"Inter\\\", -apple-system, BlinkMacSystemFont, sans-serif;\\n}\\n\\n\\n\\n\\n\\n\\n.sidebar[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 20rem;\\n  flex-direction: column;\\n  border-right-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\\n  background: linear-gradient(180deg, #1f2937 0%, #111827 100%);\\n  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);\\n}\\n\\n.sidebar-header[_ngcontent-%COMP%] {\\n  border-bottom-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\\n  padding: 1rem;\\n  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);\\n}\\n\\n\\n\\n\\n\\n\\n.tabs[_ngcontent-%COMP%] {\\n  display: flex;\\n  border-bottom-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\\n  background: linear-gradient(135deg, #374151 0%, #1f2937 100%);\\n}\\n\\n.tab[_ngcontent-%COMP%] {\\n  flex: 1 1 0%;\\n  cursor: pointer;\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  text-align: center;\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n  position: relative;\\n}\\n\\n.tab[_ngcontent-%COMP%]:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n}\\n\\n.conversation-item.tab[_ngcontent-%COMP%]:hover {\\n  border-left-width: 4px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\\n  background: linear-gradient(135deg, #1e3a8a 0%, #1d4ed8 100%);\\n  box-shadow: 0 2px 10px rgba(59, 130, 246, 0.2);\\n}\\n\\n.tab.active[_ngcontent-%COMP%] {\\n  border-bottom-width: 2px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\\n  background: linear-gradient(135deg, #1e3a8a 0%, #1d4ed8 100%);\\n  box-shadow: 0 2px 10px rgba(59, 130, 246, 0.3);\\n}\\n\\n.tab[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-bottom: 0.25rem;\\n  display: block;\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\n\\n\\n\\n\\n\\n\\n.sidebar-content[_ngcontent-%COMP%] {\\n  flex: 1 1 0%;\\n  overflow-y: auto;\\n  scrollbar-width: thin;\\n  scrollbar-color: #374151 #1f2937;\\n}\\n\\n.sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n.sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #1f2937;\\n}\\n\\n.sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #374151;\\n  border-radius: 3px;\\n}\\n\\n.sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #4b5563;\\n}\\n\\n\\n\\n\\n\\n\\n.conversation-item[_ngcontent-%COMP%], .user-item[_ngcontent-%COMP%], .notification-item[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  border-bottom-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\\n  padding: 1rem;\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n}\\n\\n.conversation-item[_ngcontent-%COMP%]:hover, .user-item[_ngcontent-%COMP%]:hover, .notification-item[_ngcontent-%COMP%]:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n}\\n\\n.conversation-item[_ngcontent-%COMP%], .user-item[_ngcontent-%COMP%], .notification-item[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.conversation-item[_ngcontent-%COMP%]:hover, .user-item[_ngcontent-%COMP%]:hover, .notification-item[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #374151 0%, #1f2937 100%);\\n  transform: translateX(2px);\\n}\\n\\n.conversation-item.bg-gray-700[_ngcontent-%COMP%] {\\n  border-left-width: 4px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\\n  background: linear-gradient(135deg, #1e3a8a 0%, #1d4ed8 100%);\\n  box-shadow: 0 2px 10px rgba(59, 130, 246, 0.2);\\n}\\n\\n\\n\\n\\n\\n\\n.user-avatar[_ngcontent-%COMP%], .conversation-avatar[_ngcontent-%COMP%] {\\n  height: 3rem;\\n  width: 3rem;\\n  border-radius: 9999px;\\n  border-width: 2px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\\n  transition: all 0.2s ease;\\n}\\n\\n.user-avatar[_ngcontent-%COMP%]:hover, .conversation-avatar[_ngcontent-%COMP%]:hover {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\\n  box-shadow: 0 0 15px rgba(59, 130, 246, 0.4);\\n}\\n\\n.online-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0px;\\n  right: 0px;\\n  height: 0.75rem;\\n  width: 0.75rem;\\n  border-radius: 9999px;\\n  border-width: 2px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(31 41 55 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%,\\n  100% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0.7;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n.unread-badge[_ngcontent-%COMP%] {\\n  min-width: 20px;\\n  border-radius: 9999px;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n  text-align: center;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\\n  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);\\n  animation: _ngcontent-%COMP%_badgePulse 2s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_badgePulse {\\n  0%,\\n  100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.05);\\n  }\\n}\\n\\n.notification-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -0.25rem;\\n  right: -0.25rem;\\n  display: flex;\\n  height: 1.25rem;\\n  width: 1.25rem;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 9999px;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\\n  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.4);\\n  animation: _ngcontent-%COMP%_notificationPulse 1s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_notificationPulse {\\n  0%,\\n  100% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n  50% {\\n    transform: scale(1.1);\\n    opacity: 0.8;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n.main-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex: 1 1 0%;\\n  flex-direction: column;\\n  background: linear-gradient(180deg, #0f172a 0%, #111827 100%);\\n}\\n\\n\\n\\n\\n\\n\\n@media (max-width: 768px) {\\n  .sidebar[_ngcontent-%COMP%] {\\n    position: fixed;\\n    top: 0px;\\n    bottom: 0px;\\n    left: 0px;\\n    z-index: 50;\\n    width: 20rem;\\n    transform: translateX(-100%);\\n    transition: transform 0.3s ease-in-out;\\n  }\\n\\n  .sidebar.show[_ngcontent-%COMP%] {\\n    transform: translateX(0);\\n  }\\n\\n  .main-content[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵlistener", "MessageLayoutComponent_div_15_div_3_Template_div_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r11", "theme_r8", "$implicit", "ctx_r10", "ɵɵnextContext", "ɵɵresetView", "selectTheme", "name", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "MessageLayoutComponent_div_15_div_3_i_4_Template", "ɵɵadvance", "ɵɵstyleProp", "gradients", "primary", "ɵɵclassProp", "ctx_r7", "currentTheme", "ɵɵtextInterpolate", "displayName", "ɵɵproperty", "MessageLayoutComponent_div_15_div_3_Template", "ctx_r0", "availableThemes", "MessageLayoutComponent_button_22_Template_button_click_0_listener", "_r13", "ctx_r12", "clearSearch", "ɵɵtextInterpolate1", "ctx_r3", "notifications", "length", "MessageLayoutComponent_div_38_div_1_div_3_Template_div_click_0_listener", "_r20", "result_r18", "ctx_r19", "selectConversation", "ctx_r17", "getConversationAvatar", "ɵɵsanitizeUrl", "getConversationName", "getLastMessagePreview", "MessageLayoutComponent_div_38_div_1_div_3_Template", "ctx_r14", "searchResults", "ctx_r27", "getUnreadCount", "conversation_r25", "MessageLayoutComponent_div_38_div_3_div_2_Template_div_click_0_listener", "_r30", "ctx_r29", "MessageLayoutComponent_div_38_div_3_div_2_div_4_Template", "MessageLayoutComponent_div_38_div_3_div_2_span_14_Template", "ctx_r22", "selectedConversationId", "id", "isGroup", "isUserOnline", "participants", "formatLastMessageTime", "lastMessage", "timestamp", "MessageLayoutComponent_div_38_div_3_div_3_Template_button_click_1_listener", "_r34", "ctx_r33", "loadMoreConversations", "MessageLayoutComponent_div_38_div_3_div_3_span_2_Template", "MessageLayoutComponent_div_38_div_3_div_3_span_3_Template", "ctx_r23", "isLoadingConversations", "MessageLayoutComponent_div_38_div_3_div_1_Template", "MessageLayoutComponent_div_38_div_3_div_2_Template", "MessageLayoutComponent_div_38_div_3_div_3_Template", "MessageLayoutComponent_div_38_div_3_div_4_Template", "ctx_r16", "conversations", "trackByConversationId", "hasMoreConversations", "MessageLayoutComponent_div_38_div_1_Template", "MessageLayoutComponent_div_38_div_2_Template", "MessageLayoutComponent_div_38_div_3_Template", "ctx_r4", "isSearching", "MessageLayoutComponent_div_39_div_1_div_3_div_1_div_3_Template", "result_r39", "image", "username", "ctx_r40", "email", "MessageLayoutComponent_div_39_div_1_div_3_Template_div_click_0_listener", "_r44", "ctx_r43", "isUser", "startConversationWithUser", "MessageLayoutComponent_div_39_div_1_div_3_div_1_Template", "ctx_r38", "MessageLayoutComponent_div_39_div_1_div_3_Template", "ctx_r35", "user_r49", "role", "MessageLayoutComponent_div_39_div_3_div_2_Template_div_click_0_listener", "_r54", "ctx_r53", "MessageLayoutComponent_div_39_div_3_div_2_div_4_Template", "MessageLayoutComponent_div_39_div_3_div_2_p_10_Template", "ctx_r46", "MessageLayoutComponent_div_39_div_3_div_3_Template_button_click_1_listener", "_r58", "ctx_r57", "loadMoreUsers", "MessageLayoutComponent_div_39_div_3_div_3_span_2_Template", "MessageLayoutComponent_div_39_div_3_div_3_span_3_Template", "ctx_r47", "isLoadingUsers", "MessageLayoutComponent_div_39_div_3_div_1_Template", "MessageLayoutComponent_div_39_div_3_div_2_Template", "MessageLayoutComponent_div_39_div_3_div_3_Template", "MessageLayoutComponent_div_39_div_3_div_4_Template", "ctx_r37", "users", "trackByUserId", "hasMoreUsers", "MessageLayoutComponent_div_39_div_1_Template", "MessageLayoutComponent_div_39_div_2_Template", "MessageLayoutComponent_div_39_div_3_Template", "ctx_r5", "MessageLayoutComponent_div_40_div_2_Template_div_click_0_listener", "_r65", "notification_r62", "ctx_r64", "markNotificationAsRead", "MessageLayoutComponent_div_40_div_2_div_11_Template", "isRead", "type", "ctx_r60", "getNotificationTitle", "content", "MessageLayoutComponent_div_40_div_1_Template", "MessageLayoutComponent_div_40_div_2_Template", "MessageLayoutComponent_div_40_div_3_Template", "ctx_r6", "isLoadingNotifications", "trackByNotificationId", "MessageLayoutComponent", "constructor", "messageService", "authService", "toastService", "themeService", "mockDataService", "route", "router", "cdr", "currentUser", "activeTab", "isMobileMenuOpen", "showThemeSelector", "searchQuery", "conversationsPage", "usersPage", "subscriptions", "searchQuery$", "ngOnInit", "initializeComponent", "setupSubscriptions", "loadInitialData", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "getCurrentUser", "navigate", "getCurrentTheme", "getAvailableThemes", "params", "subscribe", "conversationId", "markConversationAsSelected", "messagesSub", "subscribeToMessages", "message", "handleNewMessage", "notificationsSub", "subscribeToNotifications", "notification", "handleNewNotification", "searchSub", "query", "performSearch", "themeSub", "currentTheme$", "theme", "detectChanges", "push", "loadConversations", "loadUsers", "loadNotifications", "page", "getConversations", "next", "error", "console", "warn", "showInfo", "mockError", "showError", "getAllUsers", "getUsers", "getNotifications", "conversationIndex", "findIndex", "conv", "conversation", "splice", "unshift", "switchTab", "tab", "user", "_id", "userId", "createOrGetConversation", "toggleMobileMenu", "onSearchInput", "event", "target", "value", "trim", "filter", "groupName", "toLowerCase", "includes", "some", "p", "currentUserId", "otherParticipant", "find", "groupPhoto", "date", "Date", "now", "diffInHours", "getTime", "toLocaleTimeString", "hour", "minute", "toLocaleDateString", "weekday", "day", "month", "unreadCount", "isOnline", "index", "toString", "item", "isConversation", "themeName", "setTheme", "showSuccess", "ɵɵdirectiveInject", "i1", "MessageService", "i2", "AuthService", "i3", "ToastService", "i4", "ThemeService", "i5", "MockDataService", "i6", "ActivatedRoute", "Router", "ChangeDetectorRef", "selectors", "viewQuery", "MessageLayoutComponent_Query", "rf", "ctx", "MessageLayoutComponent_Template_button_click_13_listener", "MessageLayoutComponent_div_15_Template", "MessageLayoutComponent_Template_button_click_16_listener", "MessageLayoutComponent_Template_input_ngModelChange_19_listener", "$event", "MessageLayoutComponent_Template_input_input_19_listener", "MessageLayoutComponent_button_22_Template", "MessageLayoutComponent_Template_button_click_24_listener", "MessageLayoutComponent_Template_button_click_28_listener", "MessageLayoutComponent_Template_button_click_32_listener", "MessageLayoutComponent_span_36_Template", "MessageLayoutComponent_div_38_Template", "MessageLayoutComponent_div_39_Template", "MessageLayoutComponent_div_40_Template", "MessageLayoutComponent_Template_button_click_43_listener"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-layout\\message-layout.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-layout\\message-layout.component.html"], "sourcesContent": ["import {\n  <PERSON>mponent,\n  <PERSON><PERSON>nit,\n  <PERSON><PERSON><PERSON>roy,\n  ChangeDetectorRef,\n  ViewChild,\n  ElementRef,\n} from '@angular/core';\nimport { Subscription, BehaviorSubject } from 'rxjs';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { MessageService } from '../../../../services/message.service';\nimport { AuthService } from '../../../../services/auth.service';\nimport { ToastService } from '../../../../services/toast.service';\nimport { ThemeService, Theme } from '../../../../services/theme.service';\nimport { MockDataService } from '../../../../services/mock-data.service';\nimport {\n  User,\n  Conversation,\n  Message,\n  Notification,\n} from '../../../../models/message.model';\n\n@Component({\n  selector: 'app-message-layout',\n  templateUrl: './message-layout.component.html',\n  styleUrls: ['./message-layout.component.css'],\n})\nexport class MessageLayoutComponent implements OnInit, OnDestroy {\n  @ViewChild('searchInput') searchInput!: ElementRef;\n\n  // État du composant\n  currentUser: User | null = null;\n  conversations: Conversation[] = [];\n  users: User[] = [];\n  notifications: Notification[] = [];\n\n  // Navigation et UI\n  activeTab: 'conversations' | 'users' | 'notifications' = 'conversations';\n  selectedConversationId: string | null = null;\n  isMobileMenuOpen = false;\n  isSearching = false;\n\n  // Thème\n  currentTheme: Theme | null = null;\n  availableThemes: Theme[] = [];\n  showThemeSelector = false;\n\n  // Recherche\n  searchQuery = '';\n  searchResults: (Conversation | User)[] = [];\n\n  // États de chargement\n  isLoadingConversations = false;\n  isLoadingUsers = false;\n  isLoadingNotifications = false;\n\n  // Pagination\n  conversationsPage = 1;\n  usersPage = 1;\n  hasMoreConversations = true;\n  hasMoreUsers = true;\n\n  // Subscriptions\n  private subscriptions: Subscription[] = [];\n\n  // Observables\n  private searchQuery$ = new BehaviorSubject<string>('');\n\n  constructor(\n    private messageService: MessageService,\n    private authService: AuthService,\n    private toastService: ToastService,\n    private themeService: ThemeService,\n    private mockDataService: MockDataService,\n    private route: ActivatedRoute,\n    private router: Router,\n    private cdr: ChangeDetectorRef\n  ) {}\n\n  ngOnInit(): void {\n    this.initializeComponent();\n    this.setupSubscriptions();\n    this.loadInitialData();\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\n  }\n\n  // ============================================================================\n  // MÉTHODES D'INITIALISATION\n  // ============================================================================\n\n  private initializeComponent(): void {\n    // Récupérer l'utilisateur actuel\n    this.currentUser = this.authService.getCurrentUser();\n\n    if (!this.currentUser) {\n      this.router.navigate(['/login']);\n      return;\n    }\n\n    // Initialiser les thèmes\n    this.currentTheme = this.themeService.getCurrentTheme();\n    this.availableThemes = this.themeService.getAvailableThemes();\n\n    // Écouter les changements de route\n    this.route.params.subscribe((params) => {\n      const conversationId = params['conversationId'];\n      if (conversationId) {\n        this.selectedConversationId = conversationId;\n        this.markConversationAsSelected(conversationId);\n      }\n    });\n  }\n\n  private setupSubscriptions(): void {\n    // Subscription pour les nouveaux messages\n    const messagesSub = this.messageService\n      .subscribeToMessages()\n      .subscribe((message) => {\n        if (message) {\n          this.handleNewMessage(message);\n        }\n      });\n\n    // Subscription pour les notifications\n    const notificationsSub = this.messageService\n      .subscribeToNotifications()\n      .subscribe((notification) => {\n        if (notification) {\n          this.handleNewNotification(notification);\n        }\n      });\n\n    // Subscription pour la recherche\n    const searchSub = this.searchQuery$.subscribe((query) => {\n      this.performSearch(query);\n    });\n\n    // Subscription pour les changements de thème\n    const themeSub = this.themeService.currentTheme$.subscribe((theme) => {\n      this.currentTheme = theme;\n      this.cdr.detectChanges();\n    });\n\n    this.subscriptions.push(messagesSub, notificationsSub, searchSub, themeSub);\n  }\n\n  private loadInitialData(): void {\n    this.loadConversations();\n    this.loadUsers();\n    this.loadNotifications();\n\n    // Charger l'utilisateur actuel depuis les données de test\n    if (!this.currentUser) {\n      this.currentUser = this.mockDataService.getCurrentUser();\n    }\n  }\n\n  // ============================================================================\n  // MÉTHODES DE CHARGEMENT DES DONNÉES\n  // ============================================================================\n\n  loadConversations(page: number = 1): void {\n    if (this.isLoadingConversations) return;\n\n    this.isLoadingConversations = true;\n\n    this.messageService.getConversations(25, page).subscribe({\n      next: (conversations) => {\n        if (page === 1) {\n          this.conversations = conversations;\n        } else {\n          this.conversations.push(...conversations);\n        }\n\n        this.conversationsPage = page;\n        this.hasMoreConversations = conversations.length === 25;\n        this.isLoadingConversations = false;\n        this.cdr.detectChanges();\n      },\n      error: (error) => {\n        console.warn(\n          'Service principal indisponible, utilisation des données de test:',\n          error\n        );\n        // Fallback sur les données de test\n        this.mockDataService.getConversations().subscribe({\n          next: (conversations) => {\n            if (page === 1) {\n              this.conversations = conversations;\n            } else {\n              this.conversations.push(...conversations);\n            }\n            this.conversationsPage = page;\n            this.hasMoreConversations = false; // Pas de pagination pour les données de test\n            this.isLoadingConversations = false;\n            this.cdr.detectChanges();\n            if (page === 1) {\n              this.toastService.showInfo(\n                'Mode démo - Données de test chargées'\n              );\n            }\n          },\n          error: (mockError) => {\n            console.error(\n              'Erreur lors du chargement des données de test:',\n              mockError\n            );\n            this.isLoadingConversations = false;\n            this.toastService.showError(\n              'Erreur lors du chargement des conversations'\n            );\n          },\n        });\n      },\n    });\n  }\n\n  loadUsers(page: number = 1): void {\n    if (this.isLoadingUsers) return;\n\n    this.isLoadingUsers = true;\n\n    this.messageService.getAllUsers(false, '', page, 25).subscribe({\n      next: (users) => {\n        if (page === 1) {\n          this.users = users;\n        } else {\n          this.users.push(...users);\n        }\n\n        this.usersPage = page;\n        this.hasMoreUsers = users.length === 25;\n        this.isLoadingUsers = false;\n        this.cdr.detectChanges();\n      },\n      error: (error) => {\n        console.warn(\n          'Service principal indisponible, utilisation des données de test:',\n          error\n        );\n        // Fallback sur les données de test\n        this.mockDataService.getUsers().subscribe({\n          next: (users) => {\n            if (page === 1) {\n              this.users = users;\n            } else {\n              this.users.push(...users);\n            }\n            this.usersPage = page;\n            this.hasMoreUsers = false; // Pas de pagination pour les données de test\n            this.isLoadingUsers = false;\n            this.cdr.detectChanges();\n          },\n          error: (mockError) => {\n            console.error(\n              'Erreur lors du chargement des données de test:',\n              mockError\n            );\n            this.isLoadingUsers = false;\n            this.toastService.showError(\n              'Erreur lors du chargement des utilisateurs'\n            );\n          },\n        });\n      },\n    });\n  }\n\n  loadNotifications(): void {\n    if (this.isLoadingNotifications) return;\n\n    this.isLoadingNotifications = true;\n\n    this.messageService.getNotifications().subscribe({\n      next: (notifications) => {\n        this.notifications = notifications;\n        this.isLoadingNotifications = false;\n        this.cdr.detectChanges();\n      },\n      error: (error) => {\n        console.warn(\n          'Service principal indisponible, utilisation des données de test:',\n          error\n        );\n        // Fallback sur les données de test\n        this.mockDataService.getNotifications().subscribe({\n          next: (notifications) => {\n            this.notifications = notifications;\n            this.isLoadingNotifications = false;\n            this.cdr.detectChanges();\n          },\n          error: (mockError) => {\n            console.error(\n              'Erreur lors du chargement des données de test:',\n              mockError\n            );\n            this.isLoadingNotifications = false;\n            this.toastService.showError(\n              'Erreur lors du chargement des notifications'\n            );\n          },\n        });\n      },\n    });\n  }\n\n  // ============================================================================\n  // MÉTHODES DE GESTION DES ÉVÉNEMENTS\n  // ============================================================================\n\n  private handleNewMessage(message: Message): void {\n    // Mettre à jour la conversation correspondante\n    const conversationIndex = this.conversations.findIndex(\n      (conv) => conv.id === message.conversationId\n    );\n\n    if (conversationIndex !== -1) {\n      // Mettre à jour le dernier message\n      this.conversations[conversationIndex].lastMessage = message;\n\n      // Déplacer la conversation en haut de la liste\n      const conversation = this.conversations.splice(conversationIndex, 1)[0];\n      this.conversations.unshift(conversation);\n\n      this.cdr.detectChanges();\n    }\n  }\n\n  private handleNewNotification(notification: Notification): void {\n    // Ajouter la nouvelle notification en haut de la liste\n    this.notifications.unshift(notification);\n    this.cdr.detectChanges();\n\n    // Afficher une notification toast si ce n'est pas l'onglet actif\n    if (this.activeTab !== 'notifications') {\n      this.toastService.showInfo('Nouvelle notification reçue');\n    }\n  }\n\n  private markConversationAsSelected(conversationId: string): void {\n    // Marquer la conversation comme sélectionnée visuellement\n    this.selectedConversationId = conversationId;\n    this.cdr.detectChanges();\n  }\n\n  // ============================================================================\n  // MÉTHODES DE NAVIGATION ET UI\n  // ============================================================================\n\n  switchTab(tab: 'conversations' | 'users' | 'notifications'): void {\n    this.activeTab = tab;\n    this.searchQuery = '';\n    this.searchResults = [];\n    this.isSearching = false;\n\n    // Charger les données si nécessaire\n    switch (tab) {\n      case 'conversations':\n        if (this.conversations.length === 0) {\n          this.loadConversations();\n        }\n        break;\n      case 'users':\n        if (this.users.length === 0) {\n          this.loadUsers();\n        }\n        break;\n      case 'notifications':\n        if (this.notifications.length === 0) {\n          this.loadNotifications();\n        }\n        break;\n    }\n  }\n\n  selectConversation(conversation: Conversation): void {\n    if (!conversation.id) return;\n\n    this.selectedConversationId = conversation.id;\n    this.router.navigate(['/messages', conversation.id]);\n\n    // Fermer le menu mobile si ouvert\n    this.isMobileMenuOpen = false;\n  }\n\n  startConversationWithUser(user: User): void {\n    if (!user.id && !user._id) return;\n\n    const userId = user.id || user._id!;\n\n    // Créer ou récupérer la conversation avec cet utilisateur\n    this.messageService.createOrGetConversation(userId).subscribe({\n      next: (conversation) => {\n        this.selectConversation(conversation);\n      },\n      error: (error) => {\n        console.error('Erreur lors de la création de la conversation:', error);\n        this.toastService.showError(\n          'Erreur lors de la création de la conversation'\n        );\n      },\n    });\n  }\n\n  toggleMobileMenu(): void {\n    this.isMobileMenuOpen = !this.isMobileMenuOpen;\n  }\n\n  // ============================================================================\n  // MÉTHODES DE RECHERCHE\n  // ============================================================================\n\n  onSearchInput(event: any): void {\n    const query = event.target.value.trim();\n    this.searchQuery = query;\n    this.searchQuery$.next(query);\n  }\n\n  private performSearch(query: string): void {\n    if (!query) {\n      this.searchResults = [];\n      this.isSearching = false;\n      return;\n    }\n\n    this.isSearching = true;\n\n    if (this.activeTab === 'conversations') {\n      this.searchResults = this.conversations.filter((conv) =>\n        conv.isGroup\n          ? conv.groupName?.toLowerCase().includes(query.toLowerCase())\n          : conv.participants?.some((p) =>\n              p.username?.toLowerCase().includes(query.toLowerCase())\n            )\n      );\n    } else if (this.activeTab === 'users') {\n      this.searchResults = this.users.filter(\n        (user) =>\n          user.username?.toLowerCase().includes(query.toLowerCase()) ||\n          user.email?.toLowerCase().includes(query.toLowerCase())\n      );\n    }\n\n    this.cdr.detectChanges();\n  }\n\n  clearSearch(): void {\n    this.searchQuery = '';\n    this.searchResults = [];\n    this.isSearching = false;\n    this.searchQuery$.next('');\n  }\n\n  // ============================================================================\n  // MÉTHODES DE PAGINATION\n  // ============================================================================\n\n  loadMoreConversations(): void {\n    if (this.hasMoreConversations && !this.isLoadingConversations) {\n      this.loadConversations(this.conversationsPage + 1);\n    }\n  }\n\n  loadMoreUsers(): void {\n    if (this.hasMoreUsers && !this.isLoadingUsers) {\n      this.loadUsers(this.usersPage + 1);\n    }\n  }\n\n  // ============================================================================\n  // MÉTHODES UTILITAIRES POUR LE TEMPLATE\n  // ============================================================================\n\n  getConversationName(conversation: Conversation): string {\n    if (conversation.isGroup) {\n      return conversation.groupName || 'Groupe sans nom';\n    }\n\n    if (!this.currentUser) return 'Conversation';\n\n    const currentUserId = this.currentUser.id || this.currentUser._id;\n    const otherParticipant = conversation.participants?.find(\n      (p) => (p.id || p._id) !== currentUserId\n    );\n\n    return otherParticipant?.username || 'Utilisateur inconnu';\n  }\n\n  getConversationAvatar(conversation: Conversation): string {\n    if (conversation.isGroup) {\n      return conversation.groupPhoto || '/assets/images/default-group.png';\n    }\n\n    if (!this.currentUser) return '/assets/images/default-avatar.png';\n\n    const currentUserId = this.currentUser.id || this.currentUser._id;\n    const otherParticipant = conversation.participants?.find(\n      (p) => (p.id || p._id) !== currentUserId\n    );\n\n    return otherParticipant?.image || '/assets/images/default-avatar.png';\n  }\n\n  getLastMessagePreview(conversation: Conversation): string {\n    if (!conversation.lastMessage) return 'Aucun message';\n\n    const message = conversation.lastMessage;\n\n    if (message.type === 'TEXT') {\n      return message.content || '';\n    } else if (message.type === 'IMAGE') {\n      return '📷 Image';\n    } else if (message.type === 'FILE') {\n      return '📎 Fichier';\n    } else if (message.type === 'VOICE_MESSAGE') {\n      return '🎤 Message vocal';\n    } else if (message.type === 'VIDEO') {\n      return '🎥 Vidéo';\n    }\n\n    return 'Message';\n  }\n\n  formatLastMessageTime(timestamp: Date | string | undefined): string {\n    if (!timestamp) return '';\n\n    const date = new Date(timestamp);\n    const now = new Date();\n    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n\n    if (diffInHours < 1) {\n      return \"À l'instant\";\n    } else if (diffInHours < 24) {\n      return date.toLocaleTimeString('fr-FR', {\n        hour: '2-digit',\n        minute: '2-digit',\n      });\n    } else if (diffInHours < 168) {\n      // 7 jours\n      return date.toLocaleDateString('fr-FR', { weekday: 'short' });\n    } else {\n      return date.toLocaleDateString('fr-FR', {\n        day: '2-digit',\n        month: '2-digit',\n      });\n    }\n  }\n\n  getUnreadCount(conversation: Conversation): number {\n    return conversation.unreadCount || 0;\n  }\n\n  isUserOnline(user: User): boolean {\n    return user.isOnline || false;\n  }\n\n  trackByConversationId(index: number, conversation: Conversation): string {\n    return conversation.id || conversation._id || index.toString();\n  }\n\n  trackByUserId(index: number, user: User): string {\n    return user.id || user._id || index.toString();\n  }\n\n  trackByNotificationId(index: number, notification: Notification): string {\n    return notification.id || notification._id || index.toString();\n  }\n\n  markNotificationAsRead(notification: Notification): void {\n    if (!notification.id || notification.isRead) return;\n\n    this.messageService.markNotificationAsRead(notification.id).subscribe({\n      next: () => {\n        notification.isRead = true;\n        this.cdr.detectChanges();\n      },\n      error: (error) => {\n        console.error(\n          'Erreur lors du marquage de la notification comme lue:',\n          error\n        );\n        this.toastService.showError(\n          'Erreur lors du marquage de la notification'\n        );\n      },\n    });\n  }\n\n  // Type guards pour différencier User et Conversation dans les résultats de recherche\n  isUser(item: User | Conversation): item is User {\n    return 'username' in item && 'email' in item;\n  }\n\n  isConversation(item: User | Conversation): item is Conversation {\n    return 'participants' in item || 'isGroup' in item;\n  }\n\n  getNotificationTitle(notification: Notification): string {\n    switch (notification.type) {\n      case 'NEW_MESSAGE':\n        return 'Nouveau message';\n      case 'FRIEND_REQUEST':\n        return \"Demande d'ami\";\n      case 'GROUP_INVITE':\n        return 'Invitation de groupe';\n      case 'MESSAGE_REACTION':\n        return 'Réaction à un message';\n      case 'SYSTEM_ALERT':\n        return 'Alerte système';\n      default:\n        return 'Notification';\n    }\n  }\n\n  // ============================================================================\n  // MÉTHODES DE GESTION DES THÈMES\n  // ============================================================================\n\n  selectTheme(themeName: string): void {\n    this.themeService.setTheme(themeName);\n    this.showThemeSelector = false;\n    this.toastService.showSuccess(\n      `Thème \"${this.themeService.getCurrentTheme().displayName}\" appliqué`\n    );\n  }\n}\n", "<!-- ============================================================================\n     LAYOUT PRINCIPAL DE MESSAGERIE - STYLE WHATSAPP\n     ============================================================================ -->\n\n<div class=\"message-layout h-screen bg-gray-900 text-white flex\">\n  <!-- ========================================================================\n       SIDEBAR GAUCHE - CONVERSATIONS/UTILISATEURS/NOTIFICATIONS\n       ======================================================================== -->\n  <div\n    class=\"sidebar w-80 bg-gray-800 border-r border-gray-700 flex flex-col\"\n    [class.hidden]=\"!isMobileMenuOpen\"\n    [class.md:flex]=\"true\"\n  >\n    <!-- En-tête de la sidebar -->\n    <div class=\"sidebar-header p-4 border-b border-gray-700 bg-gray-800\">\n      <div class=\"flex items-center justify-between mb-4\">\n        <div class=\"flex items-center space-x-3\">\n          <img\n            [src]=\"currentUser?.image || '/assets/images/default-avatar.png'\"\n            [alt]=\"currentUser?.username\"\n            class=\"w-10 h-10 rounded-full border-2 border-blue-500\"\n          />\n          <div>\n            <h3 class=\"font-semibold text-white\">\n              {{ currentUser?.username }}\n            </h3>\n            <p class=\"text-sm text-green-400\">En ligne</p>\n          </div>\n        </div>\n\n        <!-- Actions de l'en-tête -->\n        <div class=\"flex items-center space-x-2\">\n          <!-- Sélecteur de thème -->\n          <div class=\"relative\">\n            <button\n              class=\"p-2 rounded-lg bg-gray-700 hover:bg-gray-600 transition-colors\"\n              (click)=\"showThemeSelector = !showThemeSelector\"\n              title=\"Changer de thème\"\n            >\n              <i class=\"fas fa-palette text-blue-400\"></i>\n            </button>\n\n            <!-- Menu des thèmes -->\n            <div\n              *ngIf=\"showThemeSelector\"\n              class=\"absolute top-full right-0 mt-2 bg-gray-800 rounded-lg shadow-lg border border-gray-700 p-2 z-50 min-w-48\"\n            >\n              <div class=\"text-xs text-gray-400 mb-2 px-2\">\n                Choisir un thème\n              </div>\n              <div\n                *ngFor=\"let theme of availableThemes\"\n                class=\"flex items-center space-x-3 p-2 hover:bg-gray-700 rounded cursor-pointer transition-colors\"\n                (click)=\"selectTheme(theme.name)\"\n              >\n                <div\n                  class=\"w-4 h-4 rounded-full border-2\"\n                  [style.background]=\"theme.gradients.primary\"\n                  [class.border-white]=\"currentTheme?.name === theme.name\"\n                  [class.border-gray-500]=\"currentTheme?.name !== theme.name\"\n                ></div>\n                <span class=\"text-white text-sm\">{{ theme.displayName }}</span>\n                <i\n                  *ngIf=\"currentTheme?.name === theme.name\"\n                  class=\"fas fa-check text-blue-400 text-xs ml-auto\"\n                ></i>\n              </div>\n            </div>\n          </div>\n\n          <!-- Bouton menu mobile -->\n          <button\n            class=\"md:hidden p-2 rounded-lg bg-gray-700 hover:bg-gray-600\"\n            (click)=\"toggleMobileMenu()\"\n          >\n            <i class=\"fas fa-times text-white\"></i>\n          </button>\n        </div>\n      </div>\n\n      <!-- Barre de recherche -->\n      <div class=\"relative\">\n        <input\n          #searchInput\n          type=\"text\"\n          [(ngModel)]=\"searchQuery\"\n          (input)=\"onSearchInput($event)\"\n          placeholder=\"Rechercher...\"\n          class=\"w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 pl-10 text-white placeholder-gray-400 focus:outline-none focus:border-blue-500\"\n        />\n        <i class=\"fas fa-search absolute left-3 top-3 text-gray-400\"></i>\n        <button\n          *ngIf=\"searchQuery\"\n          (click)=\"clearSearch()\"\n          class=\"absolute right-3 top-3 text-gray-400 hover:text-white\"\n        >\n          <i class=\"fas fa-times\"></i>\n        </button>\n      </div>\n    </div>\n\n    <!-- Onglets de navigation -->\n    <div class=\"tabs flex border-b border-gray-700\">\n      <button\n        class=\"tab flex-1 py-3 px-4 text-center transition-all duration-200\"\n        [class.active]=\"activeTab === 'conversations'\"\n        [class.text-blue-400]=\"activeTab === 'conversations'\"\n        [class.border-b-2]=\"activeTab === 'conversations'\"\n        [class.border-blue-500]=\"activeTab === 'conversations'\"\n        [class.text-gray-400]=\"activeTab !== 'conversations'\"\n        (click)=\"switchTab('conversations')\"\n      >\n        <i class=\"fas fa-comments mb-1\"></i>\n        <div class=\"text-xs\">Discussions</div>\n      </button>\n\n      <button\n        class=\"tab flex-1 py-3 px-4 text-center transition-all duration-200\"\n        [class.active]=\"activeTab === 'users'\"\n        [class.text-blue-400]=\"activeTab === 'users'\"\n        [class.border-b-2]=\"activeTab === 'users'\"\n        [class.border-blue-500]=\"activeTab === 'users'\"\n        [class.text-gray-400]=\"activeTab !== 'users'\"\n        (click)=\"switchTab('users')\"\n      >\n        <i class=\"fas fa-users mb-1\"></i>\n        <div class=\"text-xs\">Contacts</div>\n      </button>\n\n      <button\n        class=\"tab flex-1 py-3 px-4 text-center transition-all duration-200 relative\"\n        [class.active]=\"activeTab === 'notifications'\"\n        [class.text-blue-400]=\"activeTab === 'notifications'\"\n        [class.border-b-2]=\"activeTab === 'notifications'\"\n        [class.border-blue-500]=\"activeTab === 'notifications'\"\n        [class.text-gray-400]=\"activeTab !== 'notifications'\"\n        (click)=\"switchTab('notifications')\"\n      >\n        <i class=\"fas fa-bell mb-1\"></i>\n        <div class=\"text-xs\">Notifications</div>\n        <span\n          *ngIf=\"notifications.length > 0\"\n          class=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\"\n        >\n          {{ notifications.length > 9 ? \"9+\" : notifications.length }}\n        </span>\n      </button>\n    </div>\n\n    <!-- Contenu de la sidebar -->\n    <div class=\"sidebar-content flex-1 overflow-y-auto\">\n      <!-- ====================================================================\n           ONGLET CONVERSATIONS\n           ==================================================================== -->\n      <div *ngIf=\"activeTab === 'conversations'\" class=\"conversations-list\">\n        <!-- Résultats de recherche -->\n        <div\n          *ngIf=\"isSearching && searchResults.length > 0\"\n          class=\"search-results\"\n        >\n          <div class=\"p-3 text-sm text-gray-400 border-b border-gray-700\">\n            Résultats de recherche ({{ searchResults.length }})\n          </div>\n          <div\n            *ngFor=\"let result of searchResults\"\n            class=\"conversation-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors\"\n            (click)=\"selectConversation(result)\"\n          >\n            <div class=\"flex items-center space-x-3\">\n              <img\n                [src]=\"getConversationAvatar(result)\"\n                [alt]=\"getConversationName(result)\"\n                class=\"w-12 h-12 rounded-full\"\n              />\n              <div class=\"flex-1 min-w-0\">\n                <h4 class=\"font-medium text-white truncate\">\n                  {{ getConversationName(result) }}\n                </h4>\n                <p class=\"text-sm text-gray-400 truncate\">\n                  {{ getLastMessagePreview(result) }}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Message si aucun résultat -->\n        <div\n          *ngIf=\"isSearching && searchResults.length === 0\"\n          class=\"p-8 text-center text-gray-400\"\n        >\n          <i class=\"fas fa-search text-4xl mb-4\"></i>\n          <p>Aucun résultat trouvé</p>\n        </div>\n\n        <!-- Liste des conversations -->\n        <div *ngIf=\"!isSearching\">\n          <!-- Indicateur de chargement -->\n          <div\n            *ngIf=\"isLoadingConversations && conversations.length === 0\"\n            class=\"p-8 text-center\"\n          >\n            <div\n              class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto\"\n            ></div>\n            <p class=\"text-gray-400 mt-2\">Chargement des conversations...</p>\n          </div>\n\n          <!-- Conversations -->\n          <div\n            *ngFor=\"\n              let conversation of conversations;\n              trackBy: trackByConversationId\n            \"\n            class=\"conversation-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors relative\"\n            [class.bg-gray-700]=\"selectedConversationId === conversation.id\"\n            [class.border-l-4]=\"selectedConversationId === conversation.id\"\n            [class.border-blue-500]=\"selectedConversationId === conversation.id\"\n            (click)=\"selectConversation(conversation)\"\n          >\n            <div class=\"flex items-center space-x-3\">\n              <!-- Avatar avec indicateur en ligne -->\n              <div class=\"relative\">\n                <img\n                  [src]=\"getConversationAvatar(conversation)\"\n                  [alt]=\"getConversationName(conversation)\"\n                  class=\"w-12 h-12 rounded-full\"\n                />\n                <div\n                  *ngIf=\"!conversation.isGroup && isUserOnline(conversation.participants?.[0]!)\"\n                  class=\"absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-gray-800\"\n                ></div>\n              </div>\n\n              <!-- Informations de la conversation -->\n              <div class=\"flex-1 min-w-0\">\n                <div class=\"flex items-center justify-between\">\n                  <h4 class=\"font-medium text-white truncate\">\n                    {{ getConversationName(conversation) }}\n                  </h4>\n                  <span class=\"text-xs text-gray-400\">\n                    {{\n                      formatLastMessageTime(conversation.lastMessage?.timestamp)\n                    }}\n                  </span>\n                </div>\n\n                <div class=\"flex items-center justify-between mt-1\">\n                  <p class=\"text-sm text-gray-400 truncate\">\n                    {{ getLastMessagePreview(conversation) }}\n                  </p>\n\n                  <!-- Badge de messages non lus -->\n                  <span\n                    *ngIf=\"getUnreadCount(conversation) > 0\"\n                    class=\"bg-blue-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center\"\n                  >\n                    {{\n                      getUnreadCount(conversation) > 99\n                        ? \"99+\"\n                        : getUnreadCount(conversation)\n                    }}\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Bouton charger plus -->\n          <div *ngIf=\"hasMoreConversations\" class=\"p-4 text-center\">\n            <button\n              (click)=\"loadMoreConversations()\"\n              [disabled]=\"isLoadingConversations\"\n              class=\"text-blue-400 hover:text-blue-300 disabled:text-gray-500\"\n            >\n              <span *ngIf=\"!isLoadingConversations\">Charger plus</span>\n              <span *ngIf=\"isLoadingConversations\">Chargement...</span>\n            </button>\n          </div>\n\n          <!-- Message si aucune conversation -->\n          <div\n            *ngIf=\"conversations.length === 0 && !isLoadingConversations\"\n            class=\"p-8 text-center text-gray-400\"\n          >\n            <i class=\"fas fa-comments text-4xl mb-4\"></i>\n            <p>Aucune conversation</p>\n            <p class=\"text-sm mt-2\">\n              Commencez une nouvelle conversation dans l'onglet Contacts\n            </p>\n          </div>\n        </div>\n      </div>\n\n      <!-- ====================================================================\n           ONGLET UTILISATEURS/CONTACTS\n           ==================================================================== -->\n      <div *ngIf=\"activeTab === 'users'\" class=\"users-list\">\n        <!-- Résultats de recherche -->\n        <div\n          *ngIf=\"isSearching && searchResults.length > 0\"\n          class=\"search-results\"\n        >\n          <div class=\"p-3 text-sm text-gray-400 border-b border-gray-700\">\n            Résultats de recherche ({{ searchResults.length }})\n          </div>\n          <div\n            *ngFor=\"let result of searchResults\"\n            class=\"user-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors\"\n            (click)=\"isUser(result) ? startConversationWithUser(result) : null\"\n          >\n            <div class=\"flex items-center space-x-3\" *ngIf=\"isUser(result)\">\n              <div class=\"relative\">\n                <img\n                  [src]=\"result.image || '/assets/images/default-avatar.png'\"\n                  [alt]=\"result.username\"\n                  class=\"w-12 h-12 rounded-full\"\n                />\n                <div\n                  *ngIf=\"isUserOnline(result)\"\n                  class=\"absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-gray-800\"\n                ></div>\n              </div>\n              <div class=\"flex-1 min-w-0\">\n                <h4 class=\"font-medium text-white truncate\">\n                  {{ result.username }}\n                </h4>\n                <p class=\"text-sm text-gray-400 truncate\">{{ result.email }}</p>\n              </div>\n              <div class=\"text-blue-400\">\n                <i class=\"fas fa-comment\"></i>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Message si aucun résultat -->\n        <div\n          *ngIf=\"isSearching && searchResults.length === 0\"\n          class=\"p-8 text-center text-gray-400\"\n        >\n          <i class=\"fas fa-search text-4xl mb-4\"></i>\n          <p>Aucun utilisateur trouvé</p>\n        </div>\n\n        <!-- Liste des utilisateurs -->\n        <div *ngIf=\"!isSearching\">\n          <!-- Indicateur de chargement -->\n          <div\n            *ngIf=\"isLoadingUsers && users.length === 0\"\n            class=\"p-8 text-center\"\n          >\n            <div\n              class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto\"\n            ></div>\n            <p class=\"text-gray-400 mt-2\">Chargement des utilisateurs...</p>\n          </div>\n\n          <!-- Utilisateurs -->\n          <div\n            *ngFor=\"let user of users; trackBy: trackByUserId\"\n            class=\"user-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors\"\n            (click)=\"startConversationWithUser(user)\"\n          >\n            <div class=\"flex items-center space-x-3\">\n              <!-- Avatar avec indicateur en ligne -->\n              <div class=\"relative\">\n                <img\n                  [src]=\"user.image || '/assets/images/default-avatar.png'\"\n                  [alt]=\"user.username\"\n                  class=\"w-12 h-12 rounded-full\"\n                />\n                <div\n                  *ngIf=\"isUserOnline(user)\"\n                  class=\"absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-gray-800\"\n                ></div>\n              </div>\n\n              <!-- Informations de l'utilisateur -->\n              <div class=\"flex-1 min-w-0\">\n                <h4 class=\"font-medium text-white truncate\">\n                  {{ user.username }}\n                </h4>\n                <p class=\"text-sm text-gray-400 truncate\">{{ user.email }}</p>\n                <p class=\"text-xs text-gray-500\" *ngIf=\"user.role\">\n                  {{ user.role }}\n                </p>\n              </div>\n\n              <!-- Statut en ligne -->\n              <div class=\"text-right\">\n                <div\n                  class=\"text-xs px-2 py-1 rounded-full\"\n                  [class.bg-green-600]=\"isUserOnline(user)\"\n                  [class.text-green-100]=\"isUserOnline(user)\"\n                  [class.bg-gray-600]=\"!isUserOnline(user)\"\n                  [class.text-gray-300]=\"!isUserOnline(user)\"\n                >\n                  {{ isUserOnline(user) ? \"En ligne\" : \"Hors ligne\" }}\n                </div>\n                <div class=\"text-blue-400 mt-1\">\n                  <i class=\"fas fa-comment\"></i>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Bouton charger plus -->\n          <div *ngIf=\"hasMoreUsers\" class=\"p-4 text-center\">\n            <button\n              (click)=\"loadMoreUsers()\"\n              [disabled]=\"isLoadingUsers\"\n              class=\"text-blue-400 hover:text-blue-300 disabled:text-gray-500\"\n            >\n              <span *ngIf=\"!isLoadingUsers\">Charger plus</span>\n              <span *ngIf=\"isLoadingUsers\">Chargement...</span>\n            </button>\n          </div>\n\n          <!-- Message si aucun utilisateur -->\n          <div\n            *ngIf=\"users.length === 0 && !isLoadingUsers\"\n            class=\"p-8 text-center text-gray-400\"\n          >\n            <i class=\"fas fa-users text-4xl mb-4\"></i>\n            <p>Aucun utilisateur trouvé</p>\n          </div>\n        </div>\n      </div>\n\n      <!-- ====================================================================\n           ONGLET NOTIFICATIONS\n           ==================================================================== -->\n      <div *ngIf=\"activeTab === 'notifications'\" class=\"notifications-list\">\n        <!-- Indicateur de chargement -->\n        <div\n          *ngIf=\"isLoadingNotifications && notifications.length === 0\"\n          class=\"p-8 text-center\"\n        >\n          <div\n            class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto\"\n          ></div>\n          <p class=\"text-gray-400 mt-2\">Chargement des notifications...</p>\n        </div>\n\n        <!-- Notifications -->\n        <div\n          *ngFor=\"\n            let notification of notifications;\n            trackBy: trackByNotificationId\n          \"\n          class=\"notification-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors\"\n          [class.bg-gray-700]=\"!notification.isRead\"\n          (click)=\"markNotificationAsRead(notification)\"\n        >\n          <div class=\"flex items-start space-x-3\">\n            <!-- Icône de notification -->\n            <div\n              class=\"notification-icon p-2 rounded-full\"\n              [class.bg-blue-600]=\"notification.type === 'NEW_MESSAGE'\"\n              [class.bg-green-600]=\"notification.type === 'FRIEND_REQUEST'\"\n              [class.bg-yellow-600]=\"notification.type === 'GROUP_INVITE'\"\n              [class.bg-purple-600]=\"notification.type === 'MESSAGE_REACTION'\"\n              [class.bg-red-600]=\"notification.type === 'SYSTEM_ALERT'\"\n            >\n              <i\n                class=\"fas\"\n                [class.fa-message]=\"notification.type === 'NEW_MESSAGE'\"\n                [class.fa-user-plus]=\"notification.type === 'FRIEND_REQUEST'\"\n                [class.fa-users]=\"notification.type === 'GROUP_INVITE'\"\n                [class.fa-heart]=\"notification.type === 'MESSAGE_REACTION'\"\n                [class.fa-exclamation-triangle]=\"\n                  notification.type === 'SYSTEM_ALERT'\n                \"\n                class=\"text-white text-sm\"\n              ></i>\n            </div>\n\n            <!-- Contenu de la notification -->\n            <div class=\"flex-1 min-w-0\">\n              <h4 class=\"font-medium text-white truncate\">\n                {{ getNotificationTitle(notification) }}\n              </h4>\n              <p class=\"text-sm text-gray-400 mt-1\">\n                {{ notification.content }}\n              </p>\n              <p class=\"text-xs text-gray-500 mt-2\">\n                {{ formatLastMessageTime(notification.timestamp) }}\n              </p>\n            </div>\n\n            <!-- Indicateur non lu -->\n            <div\n              *ngIf=\"!notification.isRead\"\n              class=\"w-2 h-2 bg-blue-500 rounded-full\"\n            ></div>\n          </div>\n        </div>\n\n        <!-- Message si aucune notification -->\n        <div\n          *ngIf=\"notifications.length === 0 && !isLoadingNotifications\"\n          class=\"p-8 text-center text-gray-400\"\n        >\n          <i class=\"fas fa-bell text-4xl mb-4\"></i>\n          <p>Aucune notification</p>\n          <p class=\"text-sm mt-2\">\n            Vous serez notifié des nouveaux messages et événements\n          </p>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- ========================================================================\n       ZONE PRINCIPALE - CHAT OU MESSAGE DE BIENVENUE\n       ======================================================================== -->\n  <div class=\"main-content flex-1 flex flex-col\">\n    <!-- Bouton menu mobile -->\n    <div class=\"md:hidden p-4 border-b border-gray-700 bg-gray-800\">\n      <button\n        class=\"p-2 rounded-lg bg-gray-700 hover:bg-gray-600\"\n        (click)=\"toggleMobileMenu()\"\n      >\n        <i class=\"fas fa-bars text-white\"></i>\n      </button>\n    </div>\n\n    <!-- Contenu principal -->\n    <div class=\"flex-1\">\n      <router-outlet></router-outlet>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAQA,SAAuBA,eAAe,QAAQ,MAAM;;;;;;;;;;;;;ICsDpCC,EAAA,CAAAC,SAAA,YAGK;;;;;;IAfPD,EAAA,CAAAE,cAAA,cAIC;IADCF,EAAA,CAAAG,UAAA,mBAAAC,kEAAA;MAAA,MAAAC,WAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAC,IAAA;MAAA,MAAAC,QAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,OAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAASX,EAAA,CAAAY,WAAA,CAAAF,OAAA,CAAAG,WAAA,CAAAL,QAAA,CAAAM,IAAA,CAAuB;IAAA,EAAC;IAEjCd,EAAA,CAAAC,SAAA,cAKO;IACPD,EAAA,CAAAE,cAAA,eAAiC;IAAAF,EAAA,CAAAe,MAAA,GAAuB;IAAAf,EAAA,CAAAgB,YAAA,EAAO;IAC/DhB,EAAA,CAAAiB,UAAA,IAAAC,gDAAA,gBAGK;IACPlB,EAAA,CAAAgB,YAAA,EAAM;;;;;IATFhB,EAAA,CAAAmB,SAAA,GAA4C;IAA5CnB,EAAA,CAAAoB,WAAA,eAAAZ,QAAA,CAAAa,SAAA,CAAAC,OAAA,CAA4C;IAC5CtB,EAAA,CAAAuB,WAAA,kBAAAC,MAAA,CAAAC,YAAA,kBAAAD,MAAA,CAAAC,YAAA,CAAAX,IAAA,MAAAN,QAAA,CAAAM,IAAA,CAAwD,qBAAAU,MAAA,CAAAC,YAAA,kBAAAD,MAAA,CAAAC,YAAA,CAAAX,IAAA,MAAAN,QAAA,CAAAM,IAAA;IAGzBd,EAAA,CAAAmB,SAAA,GAAuB;IAAvBnB,EAAA,CAAA0B,iBAAA,CAAAlB,QAAA,CAAAmB,WAAA,CAAuB;IAErD3B,EAAA,CAAAmB,SAAA,GAAuC;IAAvCnB,EAAA,CAAA4B,UAAA,UAAAJ,MAAA,CAAAC,YAAA,kBAAAD,MAAA,CAAAC,YAAA,CAAAX,IAAA,MAAAN,QAAA,CAAAM,IAAA,CAAuC;;;;;IApB9Cd,EAAA,CAAAE,cAAA,cAGC;IAEGF,EAAA,CAAAe,MAAA,8BACF;IAAAf,EAAA,CAAAgB,YAAA,EAAM;IACNhB,EAAA,CAAAiB,UAAA,IAAAY,4CAAA,kBAgBM;IACR7B,EAAA,CAAAgB,YAAA,EAAM;;;;IAhBgBhB,EAAA,CAAAmB,SAAA,GAAkB;IAAlBnB,EAAA,CAAA4B,UAAA,YAAAE,MAAA,CAAAC,eAAA,CAAkB;;;;;;IAwC5C/B,EAAA,CAAAE,cAAA,iBAIC;IAFCF,EAAA,CAAAG,UAAA,mBAAA6B,kEAAA;MAAAhC,EAAA,CAAAM,aAAA,CAAA2B,IAAA;MAAA,MAAAC,OAAA,GAAAlC,EAAA,CAAAW,aAAA;MAAA,OAASX,EAAA,CAAAY,WAAA,CAAAsB,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAGvBnC,EAAA,CAAAC,SAAA,YAA4B;IAC9BD,EAAA,CAAAgB,YAAA,EAAS;;;;;IA2CThB,EAAA,CAAAE,cAAA,eAGC;IACCF,EAAA,CAAAe,MAAA,GACF;IAAAf,EAAA,CAAAgB,YAAA,EAAO;;;;IADLhB,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAAoC,kBAAA,MAAAC,MAAA,CAAAC,aAAA,CAAAC,MAAA,cAAAF,MAAA,CAAAC,aAAA,CAAAC,MAAA,MACF;;;;;;IAkBEvC,EAAA,CAAAE,cAAA,cAIC;IADCF,EAAA,CAAAG,UAAA,mBAAAqC,wEAAA;MAAA,MAAAnC,WAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAmC,IAAA;MAAA,MAAAC,UAAA,GAAArC,WAAA,CAAAI,SAAA;MAAA,MAAAkC,OAAA,GAAA3C,EAAA,CAAAW,aAAA;MAAA,OAASX,EAAA,CAAAY,WAAA,CAAA+B,OAAA,CAAAC,kBAAA,CAAAF,UAAA,CAA0B;IAAA,EAAC;IAEpC1C,EAAA,CAAAE,cAAA,aAAyC;IACvCF,EAAA,CAAAC,SAAA,cAIE;IACFD,EAAA,CAAAE,cAAA,cAA4B;IAExBF,EAAA,CAAAe,MAAA,GACF;IAAAf,EAAA,CAAAgB,YAAA,EAAK;IACLhB,EAAA,CAAAE,cAAA,YAA0C;IACxCF,EAAA,CAAAe,MAAA,GACF;IAAAf,EAAA,CAAAgB,YAAA,EAAI;;;;;IAVJhB,EAAA,CAAAmB,SAAA,GAAqC;IAArCnB,EAAA,CAAA4B,UAAA,QAAAiB,OAAA,CAAAC,qBAAA,CAAAJ,UAAA,GAAA1C,EAAA,CAAA+C,aAAA,CAAqC,QAAAF,OAAA,CAAAG,mBAAA,CAAAN,UAAA;IAMnC1C,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAAoC,kBAAA,MAAAS,OAAA,CAAAG,mBAAA,CAAAN,UAAA,OACF;IAEE1C,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAAoC,kBAAA,MAAAS,OAAA,CAAAI,qBAAA,CAAAP,UAAA,OACF;;;;;IAxBR1C,EAAA,CAAAE,cAAA,cAGC;IAEGF,EAAA,CAAAe,MAAA,GACF;IAAAf,EAAA,CAAAgB,YAAA,EAAM;IACNhB,EAAA,CAAAiB,UAAA,IAAAiC,kDAAA,kBAoBM;IACRlD,EAAA,CAAAgB,YAAA,EAAM;;;;IAvBFhB,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAAoC,kBAAA,mCAAAe,OAAA,CAAAC,aAAA,CAAAb,MAAA,OACF;IAEqBvC,EAAA,CAAAmB,SAAA,GAAgB;IAAhBnB,EAAA,CAAA4B,UAAA,YAAAuB,OAAA,CAAAC,aAAA,CAAgB;;;;;IAuBvCpD,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAC,SAAA,YAA2C;IAC3CD,EAAA,CAAAE,cAAA,QAAG;IAAAF,EAAA,CAAAe,MAAA,sCAAqB;IAAAf,EAAA,CAAAgB,YAAA,EAAI;;;;;IAM5BhB,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAC,SAAA,cAEO;IACPD,EAAA,CAAAE,cAAA,YAA8B;IAAAF,EAAA,CAAAe,MAAA,sCAA+B;IAAAf,EAAA,CAAAgB,YAAA,EAAI;;;;;IAuB7DhB,EAAA,CAAAC,SAAA,cAGO;;;;;IAsBLD,EAAA,CAAAE,cAAA,eAGC;IACCF,EAAA,CAAAe,MAAA,GAKF;IAAAf,EAAA,CAAAgB,YAAA,EAAO;;;;;IALLhB,EAAA,CAAAmB,SAAA,GAKF;IALEnB,EAAA,CAAAoC,kBAAA,MAAAiB,OAAA,CAAAC,cAAA,CAAAC,gBAAA,iBAAAF,OAAA,CAAAC,cAAA,CAAAC,gBAAA,OAKF;;;;;;IArDRvD,EAAA,CAAAE,cAAA,cAUC;IADCF,EAAA,CAAAG,UAAA,mBAAAqD,wEAAA;MAAA,MAAAnD,WAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAmD,IAAA;MAAA,MAAAF,gBAAA,GAAAlD,WAAA,CAAAI,SAAA;MAAA,MAAAiD,OAAA,GAAA1D,EAAA,CAAAW,aAAA;MAAA,OAASX,EAAA,CAAAY,WAAA,CAAA8C,OAAA,CAAAd,kBAAA,CAAAW,gBAAA,CAAgC;IAAA,EAAC;IAE1CvD,EAAA,CAAAE,cAAA,aAAyC;IAGrCF,EAAA,CAAAC,SAAA,cAIE;IACFD,EAAA,CAAAiB,UAAA,IAAA0C,wDAAA,kBAGO;IACT3D,EAAA,CAAAgB,YAAA,EAAM;IAGNhB,EAAA,CAAAE,cAAA,cAA4B;IAGtBF,EAAA,CAAAe,MAAA,GACF;IAAAf,EAAA,CAAAgB,YAAA,EAAK;IACLhB,EAAA,CAAAE,cAAA,eAAoC;IAClCF,EAAA,CAAAe,MAAA,IAGF;IAAAf,EAAA,CAAAgB,YAAA,EAAO;IAGThB,EAAA,CAAAE,cAAA,eAAoD;IAEhDF,EAAA,CAAAe,MAAA,IACF;IAAAf,EAAA,CAAAgB,YAAA,EAAI;IAGJhB,EAAA,CAAAiB,UAAA,KAAA2C,0DAAA,mBASO;IACT5D,EAAA,CAAAgB,YAAA,EAAM;;;;;IAhDVhB,EAAA,CAAAuB,WAAA,gBAAAsC,OAAA,CAAAC,sBAAA,KAAAP,gBAAA,CAAAQ,EAAA,CAAgE,eAAAF,OAAA,CAAAC,sBAAA,KAAAP,gBAAA,CAAAQ,EAAA,qBAAAF,OAAA,CAAAC,sBAAA,KAAAP,gBAAA,CAAAQ,EAAA;IAS1D/D,EAAA,CAAAmB,SAAA,GAA2C;IAA3CnB,EAAA,CAAA4B,UAAA,QAAAiC,OAAA,CAAAf,qBAAA,CAAAS,gBAAA,GAAAvD,EAAA,CAAA+C,aAAA,CAA2C,QAAAc,OAAA,CAAAb,mBAAA,CAAAO,gBAAA;IAK1CvD,EAAA,CAAAmB,SAAA,GAA4E;IAA5EnB,EAAA,CAAA4B,UAAA,UAAA2B,gBAAA,CAAAS,OAAA,IAAAH,OAAA,CAAAI,YAAA,CAAAV,gBAAA,CAAAW,YAAA,kBAAAX,gBAAA,CAAAW,YAAA,KAA4E;IAS3ElE,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAAoC,kBAAA,MAAAyB,OAAA,CAAAb,mBAAA,CAAAO,gBAAA,OACF;IAEEvD,EAAA,CAAAmB,SAAA,GAGF;IAHEnB,EAAA,CAAAoC,kBAAA,MAAAyB,OAAA,CAAAM,qBAAA,CAAAZ,gBAAA,CAAAa,WAAA,kBAAAb,gBAAA,CAAAa,WAAA,CAAAC,SAAA,OAGF;IAKErE,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAAoC,kBAAA,MAAAyB,OAAA,CAAAZ,qBAAA,CAAAM,gBAAA,OACF;IAIGvD,EAAA,CAAAmB,SAAA,GAAsC;IAAtCnB,EAAA,CAAA4B,UAAA,SAAAiC,OAAA,CAAAP,cAAA,CAAAC,gBAAA,MAAsC;;;;;IAqB7CvD,EAAA,CAAAE,cAAA,WAAsC;IAAAF,EAAA,CAAAe,MAAA,mBAAY;IAAAf,EAAA,CAAAgB,YAAA,EAAO;;;;;IACzDhB,EAAA,CAAAE,cAAA,WAAqC;IAAAF,EAAA,CAAAe,MAAA,oBAAa;IAAAf,EAAA,CAAAgB,YAAA,EAAO;;;;;;IAP7DhB,EAAA,CAAAE,cAAA,cAA0D;IAEtDF,EAAA,CAAAG,UAAA,mBAAAmE,2EAAA;MAAAtE,EAAA,CAAAM,aAAA,CAAAiE,IAAA;MAAA,MAAAC,OAAA,GAAAxE,EAAA,CAAAW,aAAA;MAAA,OAASX,EAAA,CAAAY,WAAA,CAAA4D,OAAA,CAAAC,qBAAA,EAAuB;IAAA,EAAC;IAIjCzE,EAAA,CAAAiB,UAAA,IAAAyD,yDAAA,mBAAyD;IACzD1E,EAAA,CAAAiB,UAAA,IAAA0D,yDAAA,mBAAyD;IAC3D3E,EAAA,CAAAgB,YAAA,EAAS;;;;IALPhB,EAAA,CAAAmB,SAAA,GAAmC;IAAnCnB,EAAA,CAAA4B,UAAA,aAAAgD,OAAA,CAAAC,sBAAA,CAAmC;IAG5B7E,EAAA,CAAAmB,SAAA,GAA6B;IAA7BnB,EAAA,CAAA4B,UAAA,UAAAgD,OAAA,CAAAC,sBAAA,CAA6B;IAC7B7E,EAAA,CAAAmB,SAAA,GAA4B;IAA5BnB,EAAA,CAAA4B,UAAA,SAAAgD,OAAA,CAAAC,sBAAA,CAA4B;;;;;IAKvC7E,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAC,SAAA,YAA6C;IAC7CD,EAAA,CAAAE,cAAA,QAAG;IAAAF,EAAA,CAAAe,MAAA,0BAAmB;IAAAf,EAAA,CAAAgB,YAAA,EAAI;IAC1BhB,EAAA,CAAAE,cAAA,YAAwB;IACtBF,EAAA,CAAAe,MAAA,mEACF;IAAAf,EAAA,CAAAgB,YAAA,EAAI;;;;;IA7FRhB,EAAA,CAAAE,cAAA,UAA0B;IAExBF,EAAA,CAAAiB,UAAA,IAAA6D,kDAAA,kBAQM;IAGN9E,EAAA,CAAAiB,UAAA,IAAA8D,kDAAA,oBAyDM;IAGN/E,EAAA,CAAAiB,UAAA,IAAA+D,kDAAA,kBASM;IAGNhF,EAAA,CAAAiB,UAAA,IAAAgE,kDAAA,kBASM;IACRjF,EAAA,CAAAgB,YAAA,EAAM;;;;IA5FDhB,EAAA,CAAAmB,SAAA,GAA0D;IAA1DnB,EAAA,CAAA4B,UAAA,SAAAsD,OAAA,CAAAL,sBAAA,IAAAK,OAAA,CAAAC,aAAA,CAAA5C,MAAA,OAA0D;IAYzBvC,EAAA,CAAAmB,SAAA,GACjB;IADiBnB,EAAA,CAAA4B,UAAA,YAAAsD,OAAA,CAAAC,aAAA,CACjB,iBAAAD,OAAA,CAAAE,qBAAA;IAyDbpF,EAAA,CAAAmB,SAAA,GAA0B;IAA1BnB,EAAA,CAAA4B,UAAA,SAAAsD,OAAA,CAAAG,oBAAA,CAA0B;IAa7BrF,EAAA,CAAAmB,SAAA,GAA2D;IAA3DnB,EAAA,CAAA4B,UAAA,SAAAsD,OAAA,CAAAC,aAAA,CAAA5C,MAAA,WAAA2C,OAAA,CAAAL,sBAAA,CAA2D;;;;;IAhIlE7E,EAAA,CAAAE,cAAA,cAAsE;IAEpEF,EAAA,CAAAiB,UAAA,IAAAqE,4CAAA,kBA4BM;IAGNtF,EAAA,CAAAiB,UAAA,IAAAsE,4CAAA,kBAMM;IAGNvF,EAAA,CAAAiB,UAAA,IAAAuE,4CAAA,kBA+FM;IACRxF,EAAA,CAAAgB,YAAA,EAAM;;;;IAvIDhB,EAAA,CAAAmB,SAAA,GAA6C;IAA7CnB,EAAA,CAAA4B,UAAA,SAAA6D,MAAA,CAAAC,WAAA,IAAAD,MAAA,CAAArC,aAAA,CAAAb,MAAA,KAA6C;IA+B7CvC,EAAA,CAAAmB,SAAA,GAA+C;IAA/CnB,EAAA,CAAA4B,UAAA,SAAA6D,MAAA,CAAAC,WAAA,IAAAD,MAAA,CAAArC,aAAA,CAAAb,MAAA,OAA+C;IAQ5CvC,EAAA,CAAAmB,SAAA,GAAkB;IAAlBnB,EAAA,CAAA4B,UAAA,UAAA6D,MAAA,CAAAC,WAAA,CAAkB;;;;;IA0HhB1F,EAAA,CAAAC,SAAA,cAGO;;;;;IAVXD,EAAA,CAAAE,cAAA,aAAgE;IAE5DF,EAAA,CAAAC,SAAA,cAIE;IACFD,EAAA,CAAAiB,UAAA,IAAA0E,8DAAA,kBAGO;IACT3F,EAAA,CAAAgB,YAAA,EAAM;IACNhB,EAAA,CAAAE,cAAA,cAA4B;IAExBF,EAAA,CAAAe,MAAA,GACF;IAAAf,EAAA,CAAAgB,YAAA,EAAK;IACLhB,EAAA,CAAAE,cAAA,YAA0C;IAAAF,EAAA,CAAAe,MAAA,GAAkB;IAAAf,EAAA,CAAAgB,YAAA,EAAI;IAElEhB,EAAA,CAAAE,cAAA,cAA2B;IACzBF,EAAA,CAAAC,SAAA,aAA8B;IAChCD,EAAA,CAAAgB,YAAA,EAAM;;;;;IAjBFhB,EAAA,CAAAmB,SAAA,GAA2D;IAA3DnB,EAAA,CAAA4B,UAAA,QAAAgE,UAAA,CAAAC,KAAA,yCAAA7F,EAAA,CAAA+C,aAAA,CAA2D,QAAA6C,UAAA,CAAAE,QAAA;IAK1D9F,EAAA,CAAAmB,SAAA,GAA0B;IAA1BnB,EAAA,CAAA4B,UAAA,SAAAmE,OAAA,CAAA9B,YAAA,CAAA2B,UAAA,EAA0B;IAM3B5F,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAAoC,kBAAA,MAAAwD,UAAA,CAAAE,QAAA,MACF;IAC0C9F,EAAA,CAAAmB,SAAA,GAAkB;IAAlBnB,EAAA,CAAA0B,iBAAA,CAAAkE,UAAA,CAAAI,KAAA,CAAkB;;;;;;IArBlEhG,EAAA,CAAAE,cAAA,cAIC;IADCF,EAAA,CAAAG,UAAA,mBAAA8F,wEAAA;MAAA,MAAA5F,WAAA,GAAAL,EAAA,CAAAM,aAAA,CAAA4F,IAAA;MAAA,MAAAN,UAAA,GAAAvF,WAAA,CAAAI,SAAA;MAAA,MAAA0F,OAAA,GAAAnG,EAAA,CAAAW,aAAA;MAAA,OAASX,EAAA,CAAAY,WAAA,CAAAuF,OAAA,CAAAC,MAAA,CAAAR,UAAA,CAAc,GAAGO,OAAA,CAAAE,yBAAA,CAAAT,UAAA,CAAiC,GAAG,IAAI;IAAA,EAAC;IAEnE5F,EAAA,CAAAiB,UAAA,IAAAqF,wDAAA,mBAqBM;IACRtG,EAAA,CAAAgB,YAAA,EAAM;;;;;IAtBsChB,EAAA,CAAAmB,SAAA,GAAoB;IAApBnB,EAAA,CAAA4B,UAAA,SAAA2E,OAAA,CAAAH,MAAA,CAAAR,UAAA,EAAoB;;;;;IAZlE5F,EAAA,CAAAE,cAAA,cAGC;IAEGF,EAAA,CAAAe,MAAA,GACF;IAAAf,EAAA,CAAAgB,YAAA,EAAM;IACNhB,EAAA,CAAAiB,UAAA,IAAAuF,kDAAA,kBA2BM;IACRxG,EAAA,CAAAgB,YAAA,EAAM;;;;IA9BFhB,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAAoC,kBAAA,mCAAAqE,OAAA,CAAArD,aAAA,CAAAb,MAAA,OACF;IAEqBvC,EAAA,CAAAmB,SAAA,GAAgB;IAAhBnB,EAAA,CAAA4B,UAAA,YAAA6E,OAAA,CAAArD,aAAA,CAAgB;;;;;IA8BvCpD,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAC,SAAA,YAA2C;IAC3CD,EAAA,CAAAE,cAAA,QAAG;IAAAF,EAAA,CAAAe,MAAA,oCAAwB;IAAAf,EAAA,CAAAgB,YAAA,EAAI;;;;;IAM/BhB,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAC,SAAA,cAEO;IACPD,EAAA,CAAAE,cAAA,YAA8B;IAAAF,EAAA,CAAAe,MAAA,qCAA8B;IAAAf,EAAA,CAAAgB,YAAA,EAAI;;;;;IAiB5DhB,EAAA,CAAAC,SAAA,cAGO;;;;;IASPD,EAAA,CAAAE,cAAA,YAAmD;IACjDF,EAAA,CAAAe,MAAA,GACF;IAAAf,EAAA,CAAAgB,YAAA,EAAI;;;;IADFhB,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAAoC,kBAAA,MAAAsE,QAAA,CAAAC,IAAA,MACF;;;;;;IA3BN3G,EAAA,CAAAE,cAAA,cAIC;IADCF,EAAA,CAAAG,UAAA,mBAAAyG,wEAAA;MAAA,MAAAvG,WAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAuG,IAAA;MAAA,MAAAH,QAAA,GAAArG,WAAA,CAAAI,SAAA;MAAA,MAAAqG,OAAA,GAAA9G,EAAA,CAAAW,aAAA;MAAA,OAASX,EAAA,CAAAY,WAAA,CAAAkG,OAAA,CAAAT,yBAAA,CAAAK,QAAA,CAA+B;IAAA,EAAC;IAEzC1G,EAAA,CAAAE,cAAA,aAAyC;IAGrCF,EAAA,CAAAC,SAAA,cAIE;IACFD,EAAA,CAAAiB,UAAA,IAAA8F,wDAAA,kBAGO;IACT/G,EAAA,CAAAgB,YAAA,EAAM;IAGNhB,EAAA,CAAAE,cAAA,cAA4B;IAExBF,EAAA,CAAAe,MAAA,GACF;IAAAf,EAAA,CAAAgB,YAAA,EAAK;IACLhB,EAAA,CAAAE,cAAA,YAA0C;IAAAF,EAAA,CAAAe,MAAA,GAAgB;IAAAf,EAAA,CAAAgB,YAAA,EAAI;IAC9DhB,EAAA,CAAAiB,UAAA,KAAA+F,uDAAA,gBAEI;IACNhH,EAAA,CAAAgB,YAAA,EAAM;IAGNhB,EAAA,CAAAE,cAAA,eAAwB;IAQpBF,EAAA,CAAAe,MAAA,IACF;IAAAf,EAAA,CAAAgB,YAAA,EAAM;IACNhB,EAAA,CAAAE,cAAA,eAAgC;IAC9BF,EAAA,CAAAC,SAAA,aAA8B;IAChCD,EAAA,CAAAgB,YAAA,EAAM;;;;;IAlCJhB,EAAA,CAAAmB,SAAA,GAAyD;IAAzDnB,EAAA,CAAA4B,UAAA,QAAA8E,QAAA,CAAAb,KAAA,yCAAA7F,EAAA,CAAA+C,aAAA,CAAyD,QAAA2D,QAAA,CAAAZ,QAAA;IAKxD9F,EAAA,CAAAmB,SAAA,GAAwB;IAAxBnB,EAAA,CAAA4B,UAAA,SAAAqF,OAAA,CAAAhD,YAAA,CAAAyC,QAAA,EAAwB;IAQzB1G,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAAoC,kBAAA,MAAAsE,QAAA,CAAAZ,QAAA,MACF;IAC0C9F,EAAA,CAAAmB,SAAA,GAAgB;IAAhBnB,EAAA,CAAA0B,iBAAA,CAAAgF,QAAA,CAAAV,KAAA,CAAgB;IACxBhG,EAAA,CAAAmB,SAAA,GAAe;IAAfnB,EAAA,CAAA4B,UAAA,SAAA8E,QAAA,CAAAC,IAAA,CAAe;IAS/C3G,EAAA,CAAAmB,SAAA,GAAyC;IAAzCnB,EAAA,CAAAuB,WAAA,iBAAA0F,OAAA,CAAAhD,YAAA,CAAAyC,QAAA,EAAyC,mBAAAO,OAAA,CAAAhD,YAAA,CAAAyC,QAAA,mBAAAO,OAAA,CAAAhD,YAAA,CAAAyC,QAAA,qBAAAO,OAAA,CAAAhD,YAAA,CAAAyC,QAAA;IAKzC1G,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAAoC,kBAAA,MAAA6E,OAAA,CAAAhD,YAAA,CAAAyC,QAAA,mCACF;;;;;IAeF1G,EAAA,CAAAE,cAAA,WAA8B;IAAAF,EAAA,CAAAe,MAAA,mBAAY;IAAAf,EAAA,CAAAgB,YAAA,EAAO;;;;;IACjDhB,EAAA,CAAAE,cAAA,WAA6B;IAAAF,EAAA,CAAAe,MAAA,oBAAa;IAAAf,EAAA,CAAAgB,YAAA,EAAO;;;;;;IAPrDhB,EAAA,CAAAE,cAAA,cAAkD;IAE9CF,EAAA,CAAAG,UAAA,mBAAA+G,2EAAA;MAAAlH,EAAA,CAAAM,aAAA,CAAA6G,IAAA;MAAA,MAAAC,OAAA,GAAApH,EAAA,CAAAW,aAAA;MAAA,OAASX,EAAA,CAAAY,WAAA,CAAAwG,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAIzBrH,EAAA,CAAAiB,UAAA,IAAAqG,yDAAA,mBAAiD;IACjDtH,EAAA,CAAAiB,UAAA,IAAAsG,yDAAA,mBAAiD;IACnDvH,EAAA,CAAAgB,YAAA,EAAS;;;;IALPhB,EAAA,CAAAmB,SAAA,GAA2B;IAA3BnB,EAAA,CAAA4B,UAAA,aAAA4F,OAAA,CAAAC,cAAA,CAA2B;IAGpBzH,EAAA,CAAAmB,SAAA,GAAqB;IAArBnB,EAAA,CAAA4B,UAAA,UAAA4F,OAAA,CAAAC,cAAA,CAAqB;IACrBzH,EAAA,CAAAmB,SAAA,GAAoB;IAApBnB,EAAA,CAAA4B,UAAA,SAAA4F,OAAA,CAAAC,cAAA,CAAoB;;;;;IAK/BzH,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAC,SAAA,YAA0C;IAC1CD,EAAA,CAAAE,cAAA,QAAG;IAAAF,EAAA,CAAAe,MAAA,oCAAwB;IAAAf,EAAA,CAAAgB,YAAA,EAAI;;;;;IA/EnChB,EAAA,CAAAE,cAAA,UAA0B;IAExBF,EAAA,CAAAiB,UAAA,IAAAyG,kDAAA,kBAQM;IAGN1H,EAAA,CAAAiB,UAAA,IAAA0G,kDAAA,oBA8CM;IAGN3H,EAAA,CAAAiB,UAAA,IAAA2G,kDAAA,kBASM;IAGN5H,EAAA,CAAAiB,UAAA,IAAA4G,kDAAA,kBAMM;IACR7H,EAAA,CAAAgB,YAAA,EAAM;;;;IA9EDhB,EAAA,CAAAmB,SAAA,GAA0C;IAA1CnB,EAAA,CAAA4B,UAAA,SAAAkG,OAAA,CAAAL,cAAA,IAAAK,OAAA,CAAAC,KAAA,CAAAxF,MAAA,OAA0C;IAW1BvC,EAAA,CAAAmB,SAAA,GAAU;IAAVnB,EAAA,CAAA4B,UAAA,YAAAkG,OAAA,CAAAC,KAAA,CAAU,iBAAAD,OAAA,CAAAE,aAAA;IAgDvBhI,EAAA,CAAAmB,SAAA,GAAkB;IAAlBnB,EAAA,CAAA4B,UAAA,SAAAkG,OAAA,CAAAG,YAAA,CAAkB;IAarBjI,EAAA,CAAAmB,SAAA,GAA2C;IAA3CnB,EAAA,CAAA4B,UAAA,SAAAkG,OAAA,CAAAC,KAAA,CAAAxF,MAAA,WAAAuF,OAAA,CAAAL,cAAA,CAA2C;;;;;IA5HlDzH,EAAA,CAAAE,cAAA,cAAsD;IAEpDF,EAAA,CAAAiB,UAAA,IAAAiH,4CAAA,kBAmCM;IAGNlI,EAAA,CAAAiB,UAAA,IAAAkH,4CAAA,kBAMM;IAGNnI,EAAA,CAAAiB,UAAA,IAAAmH,4CAAA,kBAiFM;IACRpI,EAAA,CAAAgB,YAAA,EAAM;;;;IAhIDhB,EAAA,CAAAmB,SAAA,GAA6C;IAA7CnB,EAAA,CAAA4B,UAAA,SAAAyG,MAAA,CAAA3C,WAAA,IAAA2C,MAAA,CAAAjF,aAAA,CAAAb,MAAA,KAA6C;IAsC7CvC,EAAA,CAAAmB,SAAA,GAA+C;IAA/CnB,EAAA,CAAA4B,UAAA,SAAAyG,MAAA,CAAA3C,WAAA,IAAA2C,MAAA,CAAAjF,aAAA,CAAAb,MAAA,OAA+C;IAQ5CvC,EAAA,CAAAmB,SAAA,GAAkB;IAAlBnB,EAAA,CAAA4B,UAAA,UAAAyG,MAAA,CAAA3C,WAAA,CAAkB;;;;;IAyFxB1F,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAC,SAAA,cAEO;IACPD,EAAA,CAAAE,cAAA,YAA8B;IAAAF,EAAA,CAAAe,MAAA,sCAA+B;IAAAf,EAAA,CAAAgB,YAAA,EAAI;;;;;IAkD/DhB,EAAA,CAAAC,SAAA,eAGO;;;;;;IAjDXD,EAAA,CAAAE,cAAA,cAQC;IADCF,EAAA,CAAAG,UAAA,mBAAAmI,kEAAA;MAAA,MAAAjI,WAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAiI,IAAA;MAAA,MAAAC,gBAAA,GAAAnI,WAAA,CAAAI,SAAA;MAAA,MAAAgI,OAAA,GAAAzI,EAAA,CAAAW,aAAA;MAAA,OAASX,EAAA,CAAAY,WAAA,CAAA6H,OAAA,CAAAC,sBAAA,CAAAF,gBAAA,CAAoC;IAAA,EAAC;IAE9CxI,EAAA,CAAAE,cAAA,cAAwC;IAUpCF,EAAA,CAAAC,SAAA,YAUK;IACPD,EAAA,CAAAgB,YAAA,EAAM;IAGNhB,EAAA,CAAAE,cAAA,cAA4B;IAExBF,EAAA,CAAAe,MAAA,GACF;IAAAf,EAAA,CAAAgB,YAAA,EAAK;IACLhB,EAAA,CAAAE,cAAA,YAAsC;IACpCF,EAAA,CAAAe,MAAA,GACF;IAAAf,EAAA,CAAAgB,YAAA,EAAI;IACJhB,EAAA,CAAAE,cAAA,YAAsC;IACpCF,EAAA,CAAAe,MAAA,IACF;IAAAf,EAAA,CAAAgB,YAAA,EAAI;IAINhB,EAAA,CAAAiB,UAAA,KAAA0H,mDAAA,kBAGO;IACT3I,EAAA,CAAAgB,YAAA,EAAM;;;;;IA5CNhB,EAAA,CAAAuB,WAAA,iBAAAiH,gBAAA,CAAAI,MAAA,CAA0C;IAOtC5I,EAAA,CAAAmB,SAAA,GAAyD;IAAzDnB,EAAA,CAAAuB,WAAA,gBAAAiH,gBAAA,CAAAK,IAAA,mBAAyD,iBAAAL,gBAAA,CAAAK,IAAA,wCAAAL,gBAAA,CAAAK,IAAA,sCAAAL,gBAAA,CAAAK,IAAA,uCAAAL,gBAAA,CAAAK,IAAA;IAQvD7I,EAAA,CAAAmB,SAAA,GAAwD;IAAxDnB,EAAA,CAAAuB,WAAA,eAAAiH,gBAAA,CAAAK,IAAA,mBAAwD,iBAAAL,gBAAA,CAAAK,IAAA,mCAAAL,gBAAA,CAAAK,IAAA,iCAAAL,gBAAA,CAAAK,IAAA,oDAAAL,gBAAA,CAAAK,IAAA;IAcxD7I,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAAoC,kBAAA,MAAA0G,OAAA,CAAAC,oBAAA,CAAAP,gBAAA,OACF;IAEExI,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAAoC,kBAAA,MAAAoG,gBAAA,CAAAQ,OAAA,MACF;IAEEhJ,EAAA,CAAAmB,SAAA,GACF;IADEnB,EAAA,CAAAoC,kBAAA,MAAA0G,OAAA,CAAA3E,qBAAA,CAAAqE,gBAAA,CAAAnE,SAAA,OACF;IAKCrE,EAAA,CAAAmB,SAAA,GAA0B;IAA1BnB,EAAA,CAAA4B,UAAA,UAAA4G,gBAAA,CAAAI,MAAA,CAA0B;;;;;IAOjC5I,EAAA,CAAAE,cAAA,cAGC;IACCF,EAAA,CAAAC,SAAA,aAAyC;IACzCD,EAAA,CAAAE,cAAA,QAAG;IAAAF,EAAA,CAAAe,MAAA,0BAAmB;IAAAf,EAAA,CAAAgB,YAAA,EAAI;IAC1BhB,EAAA,CAAAE,cAAA,YAAwB;IACtBF,EAAA,CAAAe,MAAA,8EACF;IAAAf,EAAA,CAAAgB,YAAA,EAAI;;;;;IA3ERhB,EAAA,CAAAE,cAAA,cAAsE;IAEpEF,EAAA,CAAAiB,UAAA,IAAAgI,4CAAA,kBAQM;IAGNjJ,EAAA,CAAAiB,UAAA,IAAAiI,4CAAA,oBAmDM;IAGNlJ,EAAA,CAAAiB,UAAA,IAAAkI,4CAAA,kBASM;IACRnJ,EAAA,CAAAgB,YAAA,EAAM;;;;IA1EDhB,EAAA,CAAAmB,SAAA,GAA0D;IAA1DnB,EAAA,CAAA4B,UAAA,SAAAwH,MAAA,CAAAC,sBAAA,IAAAD,MAAA,CAAA9G,aAAA,CAAAC,MAAA,OAA0D;IAY3BvC,EAAA,CAAAmB,SAAA,GACjB;IADiBnB,EAAA,CAAA4B,UAAA,YAAAwH,MAAA,CAAA9G,aAAA,CACjB,iBAAA8G,MAAA,CAAAE,qBAAA;IAoDdtJ,EAAA,CAAAmB,SAAA,GAA2D;IAA3DnB,EAAA,CAAA4B,UAAA,SAAAwH,MAAA,CAAA9G,aAAA,CAAAC,MAAA,WAAA6G,MAAA,CAAAC,sBAAA,CAA2D;;;AD1dtE,OAAM,MAAOE,sBAAsB;EAyCjCC,YACUC,cAA8B,EAC9BC,WAAwB,EACxBC,YAA0B,EAC1BC,YAA0B,EAC1BC,eAAgC,EAChCC,KAAqB,EACrBC,MAAc,EACdC,GAAsB;IAPtB,KAAAP,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IA9Cb;IACA,KAAAC,WAAW,GAAgB,IAAI;IAC/B,KAAA9E,aAAa,GAAmB,EAAE;IAClC,KAAA4C,KAAK,GAAW,EAAE;IAClB,KAAAzF,aAAa,GAAmB,EAAE;IAElC;IACA,KAAA4H,SAAS,GAAgD,eAAe;IACxE,KAAApG,sBAAsB,GAAkB,IAAI;IAC5C,KAAAqG,gBAAgB,GAAG,KAAK;IACxB,KAAAzE,WAAW,GAAG,KAAK;IAEnB;IACA,KAAAjE,YAAY,GAAiB,IAAI;IACjC,KAAAM,eAAe,GAAY,EAAE;IAC7B,KAAAqI,iBAAiB,GAAG,KAAK;IAEzB;IACA,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAjH,aAAa,GAA4B,EAAE;IAE3C;IACA,KAAAyB,sBAAsB,GAAG,KAAK;IAC9B,KAAA4C,cAAc,GAAG,KAAK;IACtB,KAAA4B,sBAAsB,GAAG,KAAK;IAE9B;IACA,KAAAiB,iBAAiB,GAAG,CAAC;IACrB,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAlF,oBAAoB,GAAG,IAAI;IAC3B,KAAA4C,YAAY,GAAG,IAAI;IAEnB;IACQ,KAAAuC,aAAa,GAAmB,EAAE;IAE1C;IACQ,KAAAC,YAAY,GAAG,IAAI1K,eAAe,CAAS,EAAE,CAAC;EAWnD;EAEH2K,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,eAAe,EAAE;EACxB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACN,aAAa,CAACO,OAAO,CAAEC,GAAG,IAAKA,GAAG,CAACC,WAAW,EAAE,CAAC;EACxD;EAEA;EACA;EACA;EAEQN,mBAAmBA,CAAA;IACzB;IACA,IAAI,CAACV,WAAW,GAAG,IAAI,CAACP,WAAW,CAACwB,cAAc,EAAE;IAEpD,IAAI,CAAC,IAAI,CAACjB,WAAW,EAAE;MACrB,IAAI,CAACF,MAAM,CAACoB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAChC;;IAGF;IACA,IAAI,CAAC1J,YAAY,GAAG,IAAI,CAACmI,YAAY,CAACwB,eAAe,EAAE;IACvD,IAAI,CAACrJ,eAAe,GAAG,IAAI,CAAC6H,YAAY,CAACyB,kBAAkB,EAAE;IAE7D;IACA,IAAI,CAACvB,KAAK,CAACwB,MAAM,CAACC,SAAS,CAAED,MAAM,IAAI;MACrC,MAAME,cAAc,GAAGF,MAAM,CAAC,gBAAgB,CAAC;MAC/C,IAAIE,cAAc,EAAE;QAClB,IAAI,CAAC1H,sBAAsB,GAAG0H,cAAc;QAC5C,IAAI,CAACC,0BAA0B,CAACD,cAAc,CAAC;;IAEnD,CAAC,CAAC;EACJ;EAEQZ,kBAAkBA,CAAA;IACxB;IACA,MAAMc,WAAW,GAAG,IAAI,CAACjC,cAAc,CACpCkC,mBAAmB,EAAE,CACrBJ,SAAS,CAAEK,OAAO,IAAI;MACrB,IAAIA,OAAO,EAAE;QACX,IAAI,CAACC,gBAAgB,CAACD,OAAO,CAAC;;IAElC,CAAC,CAAC;IAEJ;IACA,MAAME,gBAAgB,GAAG,IAAI,CAACrC,cAAc,CACzCsC,wBAAwB,EAAE,CAC1BR,SAAS,CAAES,YAAY,IAAI;MAC1B,IAAIA,YAAY,EAAE;QAChB,IAAI,CAACC,qBAAqB,CAACD,YAAY,CAAC;;IAE5C,CAAC,CAAC;IAEJ;IACA,MAAME,SAAS,GAAG,IAAI,CAACzB,YAAY,CAACc,SAAS,CAAEY,KAAK,IAAI;MACtD,IAAI,CAACC,aAAa,CAACD,KAAK,CAAC;IAC3B,CAAC,CAAC;IAEF;IACA,MAAME,QAAQ,GAAG,IAAI,CAACzC,YAAY,CAAC0C,aAAa,CAACf,SAAS,CAAEgB,KAAK,IAAI;MACnE,IAAI,CAAC9K,YAAY,GAAG8K,KAAK;MACzB,IAAI,CAACvC,GAAG,CAACwC,aAAa,EAAE;IAC1B,CAAC,CAAC;IAEF,IAAI,CAAChC,aAAa,CAACiC,IAAI,CAACf,WAAW,EAAEI,gBAAgB,EAAEI,SAAS,EAAEG,QAAQ,CAAC;EAC7E;EAEQxB,eAAeA,CAAA;IACrB,IAAI,CAAC6B,iBAAiB,EAAE;IACxB,IAAI,CAACC,SAAS,EAAE;IAChB,IAAI,CAACC,iBAAiB,EAAE;IAExB;IACA,IAAI,CAAC,IAAI,CAAC3C,WAAW,EAAE;MACrB,IAAI,CAACA,WAAW,GAAG,IAAI,CAACJ,eAAe,CAACqB,cAAc,EAAE;;EAE5D;EAEA;EACA;EACA;EAEAwB,iBAAiBA,CAACG,IAAA,GAAe,CAAC;IAChC,IAAI,IAAI,CAAChI,sBAAsB,EAAE;IAEjC,IAAI,CAACA,sBAAsB,GAAG,IAAI;IAElC,IAAI,CAAC4E,cAAc,CAACqD,gBAAgB,CAAC,EAAE,EAAED,IAAI,CAAC,CAACtB,SAAS,CAAC;MACvDwB,IAAI,EAAG5H,aAAa,IAAI;QACtB,IAAI0H,IAAI,KAAK,CAAC,EAAE;UACd,IAAI,CAAC1H,aAAa,GAAGA,aAAa;SACnC,MAAM;UACL,IAAI,CAACA,aAAa,CAACsH,IAAI,CAAC,GAAGtH,aAAa,CAAC;;QAG3C,IAAI,CAACmF,iBAAiB,GAAGuC,IAAI;QAC7B,IAAI,CAACxH,oBAAoB,GAAGF,aAAa,CAAC5C,MAAM,KAAK,EAAE;QACvD,IAAI,CAACsC,sBAAsB,GAAG,KAAK;QACnC,IAAI,CAACmF,GAAG,CAACwC,aAAa,EAAE;MAC1B,CAAC;MACDQ,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACC,IAAI,CACV,kEAAkE,EAClEF,KAAK,CACN;QACD;QACA,IAAI,CAACnD,eAAe,CAACiD,gBAAgB,EAAE,CAACvB,SAAS,CAAC;UAChDwB,IAAI,EAAG5H,aAAa,IAAI;YACtB,IAAI0H,IAAI,KAAK,CAAC,EAAE;cACd,IAAI,CAAC1H,aAAa,GAAGA,aAAa;aACnC,MAAM;cACL,IAAI,CAACA,aAAa,CAACsH,IAAI,CAAC,GAAGtH,aAAa,CAAC;;YAE3C,IAAI,CAACmF,iBAAiB,GAAGuC,IAAI;YAC7B,IAAI,CAACxH,oBAAoB,GAAG,KAAK,CAAC,CAAC;YACnC,IAAI,CAACR,sBAAsB,GAAG,KAAK;YACnC,IAAI,CAACmF,GAAG,CAACwC,aAAa,EAAE;YACxB,IAAIK,IAAI,KAAK,CAAC,EAAE;cACd,IAAI,CAAClD,YAAY,CAACwD,QAAQ,CACxB,sCAAsC,CACvC;;UAEL,CAAC;UACDH,KAAK,EAAGI,SAAS,IAAI;YACnBH,OAAO,CAACD,KAAK,CACX,gDAAgD,EAChDI,SAAS,CACV;YACD,IAAI,CAACvI,sBAAsB,GAAG,KAAK;YACnC,IAAI,CAAC8E,YAAY,CAAC0D,SAAS,CACzB,6CAA6C,CAC9C;UACH;SACD,CAAC;MACJ;KACD,CAAC;EACJ;EAEAV,SAASA,CAACE,IAAA,GAAe,CAAC;IACxB,IAAI,IAAI,CAACpF,cAAc,EAAE;IAEzB,IAAI,CAACA,cAAc,GAAG,IAAI;IAE1B,IAAI,CAACgC,cAAc,CAAC6D,WAAW,CAAC,KAAK,EAAE,EAAE,EAAET,IAAI,EAAE,EAAE,CAAC,CAACtB,SAAS,CAAC;MAC7DwB,IAAI,EAAGhF,KAAK,IAAI;QACd,IAAI8E,IAAI,KAAK,CAAC,EAAE;UACd,IAAI,CAAC9E,KAAK,GAAGA,KAAK;SACnB,MAAM;UACL,IAAI,CAACA,KAAK,CAAC0E,IAAI,CAAC,GAAG1E,KAAK,CAAC;;QAG3B,IAAI,CAACwC,SAAS,GAAGsC,IAAI;QACrB,IAAI,CAAC5E,YAAY,GAAGF,KAAK,CAACxF,MAAM,KAAK,EAAE;QACvC,IAAI,CAACkF,cAAc,GAAG,KAAK;QAC3B,IAAI,CAACuC,GAAG,CAACwC,aAAa,EAAE;MAC1B,CAAC;MACDQ,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACC,IAAI,CACV,kEAAkE,EAClEF,KAAK,CACN;QACD;QACA,IAAI,CAACnD,eAAe,CAAC0D,QAAQ,EAAE,CAAChC,SAAS,CAAC;UACxCwB,IAAI,EAAGhF,KAAK,IAAI;YACd,IAAI8E,IAAI,KAAK,CAAC,EAAE;cACd,IAAI,CAAC9E,KAAK,GAAGA,KAAK;aACnB,MAAM;cACL,IAAI,CAACA,KAAK,CAAC0E,IAAI,CAAC,GAAG1E,KAAK,CAAC;;YAE3B,IAAI,CAACwC,SAAS,GAAGsC,IAAI;YACrB,IAAI,CAAC5E,YAAY,GAAG,KAAK,CAAC,CAAC;YAC3B,IAAI,CAACR,cAAc,GAAG,KAAK;YAC3B,IAAI,CAACuC,GAAG,CAACwC,aAAa,EAAE;UAC1B,CAAC;UACDQ,KAAK,EAAGI,SAAS,IAAI;YACnBH,OAAO,CAACD,KAAK,CACX,gDAAgD,EAChDI,SAAS,CACV;YACD,IAAI,CAAC3F,cAAc,GAAG,KAAK;YAC3B,IAAI,CAACkC,YAAY,CAAC0D,SAAS,CACzB,4CAA4C,CAC7C;UACH;SACD,CAAC;MACJ;KACD,CAAC;EACJ;EAEAT,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAACvD,sBAAsB,EAAE;IAEjC,IAAI,CAACA,sBAAsB,GAAG,IAAI;IAElC,IAAI,CAACI,cAAc,CAAC+D,gBAAgB,EAAE,CAACjC,SAAS,CAAC;MAC/CwB,IAAI,EAAGzK,aAAa,IAAI;QACtB,IAAI,CAACA,aAAa,GAAGA,aAAa;QAClC,IAAI,CAAC+G,sBAAsB,GAAG,KAAK;QACnC,IAAI,CAACW,GAAG,CAACwC,aAAa,EAAE;MAC1B,CAAC;MACDQ,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACC,IAAI,CACV,kEAAkE,EAClEF,KAAK,CACN;QACD;QACA,IAAI,CAACnD,eAAe,CAAC2D,gBAAgB,EAAE,CAACjC,SAAS,CAAC;UAChDwB,IAAI,EAAGzK,aAAa,IAAI;YACtB,IAAI,CAACA,aAAa,GAAGA,aAAa;YAClC,IAAI,CAAC+G,sBAAsB,GAAG,KAAK;YACnC,IAAI,CAACW,GAAG,CAACwC,aAAa,EAAE;UAC1B,CAAC;UACDQ,KAAK,EAAGI,SAAS,IAAI;YACnBH,OAAO,CAACD,KAAK,CACX,gDAAgD,EAChDI,SAAS,CACV;YACD,IAAI,CAAC/D,sBAAsB,GAAG,KAAK;YACnC,IAAI,CAACM,YAAY,CAAC0D,SAAS,CACzB,6CAA6C,CAC9C;UACH;SACD,CAAC;MACJ;KACD,CAAC;EACJ;EAEA;EACA;EACA;EAEQxB,gBAAgBA,CAACD,OAAgB;IACvC;IACA,MAAM6B,iBAAiB,GAAG,IAAI,CAACtI,aAAa,CAACuI,SAAS,CACnDC,IAAI,IAAKA,IAAI,CAAC5J,EAAE,KAAK6H,OAAO,CAACJ,cAAc,CAC7C;IAED,IAAIiC,iBAAiB,KAAK,CAAC,CAAC,EAAE;MAC5B;MACA,IAAI,CAACtI,aAAa,CAACsI,iBAAiB,CAAC,CAACrJ,WAAW,GAAGwH,OAAO;MAE3D;MACA,MAAMgC,YAAY,GAAG,IAAI,CAACzI,aAAa,CAAC0I,MAAM,CAACJ,iBAAiB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACvE,IAAI,CAACtI,aAAa,CAAC2I,OAAO,CAACF,YAAY,CAAC;MAExC,IAAI,CAAC5D,GAAG,CAACwC,aAAa,EAAE;;EAE5B;EAEQP,qBAAqBA,CAACD,YAA0B;IACtD;IACA,IAAI,CAAC1J,aAAa,CAACwL,OAAO,CAAC9B,YAAY,CAAC;IACxC,IAAI,CAAChC,GAAG,CAACwC,aAAa,EAAE;IAExB;IACA,IAAI,IAAI,CAACtC,SAAS,KAAK,eAAe,EAAE;MACtC,IAAI,CAACP,YAAY,CAACwD,QAAQ,CAAC,6BAA6B,CAAC;;EAE7D;EAEQ1B,0BAA0BA,CAACD,cAAsB;IACvD;IACA,IAAI,CAAC1H,sBAAsB,GAAG0H,cAAc;IAC5C,IAAI,CAACxB,GAAG,CAACwC,aAAa,EAAE;EAC1B;EAEA;EACA;EACA;EAEAuB,SAASA,CAACC,GAAgD;IACxD,IAAI,CAAC9D,SAAS,GAAG8D,GAAG;IACpB,IAAI,CAAC3D,WAAW,GAAG,EAAE;IACrB,IAAI,CAACjH,aAAa,GAAG,EAAE;IACvB,IAAI,CAACsC,WAAW,GAAG,KAAK;IAExB;IACA,QAAQsI,GAAG;MACT,KAAK,eAAe;QAClB,IAAI,IAAI,CAAC7I,aAAa,CAAC5C,MAAM,KAAK,CAAC,EAAE;UACnC,IAAI,CAACmK,iBAAiB,EAAE;;QAE1B;MACF,KAAK,OAAO;QACV,IAAI,IAAI,CAAC3E,KAAK,CAACxF,MAAM,KAAK,CAAC,EAAE;UAC3B,IAAI,CAACoK,SAAS,EAAE;;QAElB;MACF,KAAK,eAAe;QAClB,IAAI,IAAI,CAACrK,aAAa,CAACC,MAAM,KAAK,CAAC,EAAE;UACnC,IAAI,CAACqK,iBAAiB,EAAE;;QAE1B;;EAEN;EAEAhK,kBAAkBA,CAACgL,YAA0B;IAC3C,IAAI,CAACA,YAAY,CAAC7J,EAAE,EAAE;IAEtB,IAAI,CAACD,sBAAsB,GAAG8J,YAAY,CAAC7J,EAAE;IAC7C,IAAI,CAACgG,MAAM,CAACoB,QAAQ,CAAC,CAAC,WAAW,EAAEyC,YAAY,CAAC7J,EAAE,CAAC,CAAC;IAEpD;IACA,IAAI,CAACoG,gBAAgB,GAAG,KAAK;EAC/B;EAEA9D,yBAAyBA,CAAC4H,IAAU;IAClC,IAAI,CAACA,IAAI,CAAClK,EAAE,IAAI,CAACkK,IAAI,CAACC,GAAG,EAAE;IAE3B,MAAMC,MAAM,GAAGF,IAAI,CAAClK,EAAE,IAAIkK,IAAI,CAACC,GAAI;IAEnC;IACA,IAAI,CAACzE,cAAc,CAAC2E,uBAAuB,CAACD,MAAM,CAAC,CAAC5C,SAAS,CAAC;MAC5DwB,IAAI,EAAGa,YAAY,IAAI;QACrB,IAAI,CAAChL,kBAAkB,CAACgL,YAAY,CAAC;MACvC,CAAC;MACDZ,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;QACtE,IAAI,CAACrD,YAAY,CAAC0D,SAAS,CACzB,+CAA+C,CAChD;MACH;KACD,CAAC;EACJ;EAEAgB,gBAAgBA,CAAA;IACd,IAAI,CAAClE,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;EAChD;EAEA;EACA;EACA;EAEAmE,aAAaA,CAACC,KAAU;IACtB,MAAMpC,KAAK,GAAGoC,KAAK,CAACC,MAAM,CAACC,KAAK,CAACC,IAAI,EAAE;IACvC,IAAI,CAACrE,WAAW,GAAG8B,KAAK;IACxB,IAAI,CAAC1B,YAAY,CAACsC,IAAI,CAACZ,KAAK,CAAC;EAC/B;EAEQC,aAAaA,CAACD,KAAa;IACjC,IAAI,CAACA,KAAK,EAAE;MACV,IAAI,CAAC/I,aAAa,GAAG,EAAE;MACvB,IAAI,CAACsC,WAAW,GAAG,KAAK;MACxB;;IAGF,IAAI,CAACA,WAAW,GAAG,IAAI;IAEvB,IAAI,IAAI,CAACwE,SAAS,KAAK,eAAe,EAAE;MACtC,IAAI,CAAC9G,aAAa,GAAG,IAAI,CAAC+B,aAAa,CAACwJ,MAAM,CAAEhB,IAAI,IAClDA,IAAI,CAAC3J,OAAO,GACR2J,IAAI,CAACiB,SAAS,EAAEC,WAAW,EAAE,CAACC,QAAQ,CAAC3C,KAAK,CAAC0C,WAAW,EAAE,CAAC,GAC3DlB,IAAI,CAACzJ,YAAY,EAAE6K,IAAI,CAAEC,CAAC,IACxBA,CAAC,CAAClJ,QAAQ,EAAE+I,WAAW,EAAE,CAACC,QAAQ,CAAC3C,KAAK,CAAC0C,WAAW,EAAE,CAAC,CACxD,CACN;KACF,MAAM,IAAI,IAAI,CAAC3E,SAAS,KAAK,OAAO,EAAE;MACrC,IAAI,CAAC9G,aAAa,GAAG,IAAI,CAAC2E,KAAK,CAAC4G,MAAM,CACnCV,IAAI,IACHA,IAAI,CAACnI,QAAQ,EAAE+I,WAAW,EAAE,CAACC,QAAQ,CAAC3C,KAAK,CAAC0C,WAAW,EAAE,CAAC,IAC1DZ,IAAI,CAACjI,KAAK,EAAE6I,WAAW,EAAE,CAACC,QAAQ,CAAC3C,KAAK,CAAC0C,WAAW,EAAE,CAAC,CAC1D;;IAGH,IAAI,CAAC7E,GAAG,CAACwC,aAAa,EAAE;EAC1B;EAEArK,WAAWA,CAAA;IACT,IAAI,CAACkI,WAAW,GAAG,EAAE;IACrB,IAAI,CAACjH,aAAa,GAAG,EAAE;IACvB,IAAI,CAACsC,WAAW,GAAG,KAAK;IACxB,IAAI,CAAC+E,YAAY,CAACsC,IAAI,CAAC,EAAE,CAAC;EAC5B;EAEA;EACA;EACA;EAEAtI,qBAAqBA,CAAA;IACnB,IAAI,IAAI,CAACY,oBAAoB,IAAI,CAAC,IAAI,CAACR,sBAAsB,EAAE;MAC7D,IAAI,CAAC6H,iBAAiB,CAAC,IAAI,CAACpC,iBAAiB,GAAG,CAAC,CAAC;;EAEtD;EAEAjD,aAAaA,CAAA;IACX,IAAI,IAAI,CAACY,YAAY,IAAI,CAAC,IAAI,CAACR,cAAc,EAAE;MAC7C,IAAI,CAACkF,SAAS,CAAC,IAAI,CAACpC,SAAS,GAAG,CAAC,CAAC;;EAEtC;EAEA;EACA;EACA;EAEAvH,mBAAmBA,CAAC4K,YAA0B;IAC5C,IAAIA,YAAY,CAAC5J,OAAO,EAAE;MACxB,OAAO4J,YAAY,CAACgB,SAAS,IAAI,iBAAiB;;IAGpD,IAAI,CAAC,IAAI,CAAC3E,WAAW,EAAE,OAAO,cAAc;IAE5C,MAAMgF,aAAa,GAAG,IAAI,CAAChF,WAAW,CAAClG,EAAE,IAAI,IAAI,CAACkG,WAAW,CAACiE,GAAG;IACjE,MAAMgB,gBAAgB,GAAGtB,YAAY,CAAC1J,YAAY,EAAEiL,IAAI,CACrDH,CAAC,IAAK,CAACA,CAAC,CAACjL,EAAE,IAAIiL,CAAC,CAACd,GAAG,MAAMe,aAAa,CACzC;IAED,OAAOC,gBAAgB,EAAEpJ,QAAQ,IAAI,qBAAqB;EAC5D;EAEAhD,qBAAqBA,CAAC8K,YAA0B;IAC9C,IAAIA,YAAY,CAAC5J,OAAO,EAAE;MACxB,OAAO4J,YAAY,CAACwB,UAAU,IAAI,kCAAkC;;IAGtE,IAAI,CAAC,IAAI,CAACnF,WAAW,EAAE,OAAO,mCAAmC;IAEjE,MAAMgF,aAAa,GAAG,IAAI,CAAChF,WAAW,CAAClG,EAAE,IAAI,IAAI,CAACkG,WAAW,CAACiE,GAAG;IACjE,MAAMgB,gBAAgB,GAAGtB,YAAY,CAAC1J,YAAY,EAAEiL,IAAI,CACrDH,CAAC,IAAK,CAACA,CAAC,CAACjL,EAAE,IAAIiL,CAAC,CAACd,GAAG,MAAMe,aAAa,CACzC;IAED,OAAOC,gBAAgB,EAAErJ,KAAK,IAAI,mCAAmC;EACvE;EAEA5C,qBAAqBA,CAAC2K,YAA0B;IAC9C,IAAI,CAACA,YAAY,CAACxJ,WAAW,EAAE,OAAO,eAAe;IAErD,MAAMwH,OAAO,GAAGgC,YAAY,CAACxJ,WAAW;IAExC,IAAIwH,OAAO,CAAC/C,IAAI,KAAK,MAAM,EAAE;MAC3B,OAAO+C,OAAO,CAAC5C,OAAO,IAAI,EAAE;KAC7B,MAAM,IAAI4C,OAAO,CAAC/C,IAAI,KAAK,OAAO,EAAE;MACnC,OAAO,UAAU;KAClB,MAAM,IAAI+C,OAAO,CAAC/C,IAAI,KAAK,MAAM,EAAE;MAClC,OAAO,YAAY;KACpB,MAAM,IAAI+C,OAAO,CAAC/C,IAAI,KAAK,eAAe,EAAE;MAC3C,OAAO,kBAAkB;KAC1B,MAAM,IAAI+C,OAAO,CAAC/C,IAAI,KAAK,OAAO,EAAE;MACnC,OAAO,UAAU;;IAGnB,OAAO,SAAS;EAClB;EAEA1E,qBAAqBA,CAACE,SAAoC;IACxD,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IAEzB,MAAMgL,IAAI,GAAG,IAAIC,IAAI,CAACjL,SAAS,CAAC;IAChC,MAAMkL,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAME,WAAW,GAAG,CAACD,GAAG,CAACE,OAAO,EAAE,GAAGJ,IAAI,CAACI,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAEvE,IAAID,WAAW,GAAG,CAAC,EAAE;MACnB,OAAO,aAAa;KACrB,MAAM,IAAIA,WAAW,GAAG,EAAE,EAAE;MAC3B,OAAOH,IAAI,CAACK,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;OACT,CAAC;KACH,MAAM,IAAIJ,WAAW,GAAG,GAAG,EAAE;MAC5B;MACA,OAAOH,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAO,CAAE,CAAC;KAC9D,MAAM;MACL,OAAOT,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;QACtCE,GAAG,EAAE,SAAS;QACdC,KAAK,EAAE;OACR,CAAC;;EAEN;EAEA1M,cAAcA,CAACsK,YAA0B;IACvC,OAAOA,YAAY,CAACqC,WAAW,IAAI,CAAC;EACtC;EAEAhM,YAAYA,CAACgK,IAAU;IACrB,OAAOA,IAAI,CAACiC,QAAQ,IAAI,KAAK;EAC/B;EAEA9K,qBAAqBA,CAAC+K,KAAa,EAAEvC,YAA0B;IAC7D,OAAOA,YAAY,CAAC7J,EAAE,IAAI6J,YAAY,CAACM,GAAG,IAAIiC,KAAK,CAACC,QAAQ,EAAE;EAChE;EAEApI,aAAaA,CAACmI,KAAa,EAAElC,IAAU;IACrC,OAAOA,IAAI,CAAClK,EAAE,IAAIkK,IAAI,CAACC,GAAG,IAAIiC,KAAK,CAACC,QAAQ,EAAE;EAChD;EAEA9G,qBAAqBA,CAAC6G,KAAa,EAAEnE,YAA0B;IAC7D,OAAOA,YAAY,CAACjI,EAAE,IAAIiI,YAAY,CAACkC,GAAG,IAAIiC,KAAK,CAACC,QAAQ,EAAE;EAChE;EAEA1H,sBAAsBA,CAACsD,YAA0B;IAC/C,IAAI,CAACA,YAAY,CAACjI,EAAE,IAAIiI,YAAY,CAACpD,MAAM,EAAE;IAE7C,IAAI,CAACa,cAAc,CAACf,sBAAsB,CAACsD,YAAY,CAACjI,EAAE,CAAC,CAACwH,SAAS,CAAC;MACpEwB,IAAI,EAAEA,CAAA,KAAK;QACTf,YAAY,CAACpD,MAAM,GAAG,IAAI;QAC1B,IAAI,CAACoB,GAAG,CAACwC,aAAa,EAAE;MAC1B,CAAC;MACDQ,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CACX,uDAAuD,EACvDA,KAAK,CACN;QACD,IAAI,CAACrD,YAAY,CAAC0D,SAAS,CACzB,4CAA4C,CAC7C;MACH;KACD,CAAC;EACJ;EAEA;EACAjH,MAAMA,CAACiK,IAAyB;IAC9B,OAAO,UAAU,IAAIA,IAAI,IAAI,OAAO,IAAIA,IAAI;EAC9C;EAEAC,cAAcA,CAACD,IAAyB;IACtC,OAAO,cAAc,IAAIA,IAAI,IAAI,SAAS,IAAIA,IAAI;EACpD;EAEAtH,oBAAoBA,CAACiD,YAA0B;IAC7C,QAAQA,YAAY,CAACnD,IAAI;MACvB,KAAK,aAAa;QAChB,OAAO,iBAAiB;MAC1B,KAAK,gBAAgB;QACnB,OAAO,eAAe;MACxB,KAAK,cAAc;QACjB,OAAO,sBAAsB;MAC/B,KAAK,kBAAkB;QACrB,OAAO,uBAAuB;MAChC,KAAK,cAAc;QACjB,OAAO,gBAAgB;MACzB;QACE,OAAO,cAAc;;EAE3B;EAEA;EACA;EACA;EAEAhI,WAAWA,CAAC0P,SAAiB;IAC3B,IAAI,CAAC3G,YAAY,CAAC4G,QAAQ,CAACD,SAAS,CAAC;IACrC,IAAI,CAACnG,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACT,YAAY,CAAC8G,WAAW,CAC3B,UAAU,IAAI,CAAC7G,YAAY,CAACwB,eAAe,EAAE,CAACzJ,WAAW,YAAY,CACtE;EACH;;;uBAxlBW4H,sBAAsB,EAAAvJ,EAAA,CAAA0Q,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA5Q,EAAA,CAAA0Q,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA9Q,EAAA,CAAA0Q,iBAAA,CAAAK,EAAA,CAAAC,YAAA,GAAAhR,EAAA,CAAA0Q,iBAAA,CAAAO,EAAA,CAAAC,YAAA,GAAAlR,EAAA,CAAA0Q,iBAAA,CAAAS,EAAA,CAAAC,eAAA,GAAApR,EAAA,CAAA0Q,iBAAA,CAAAW,EAAA,CAAAC,cAAA,GAAAtR,EAAA,CAAA0Q,iBAAA,CAAAW,EAAA,CAAAE,MAAA,GAAAvR,EAAA,CAAA0Q,iBAAA,CAAA1Q,EAAA,CAAAwR,iBAAA;IAAA;EAAA;;;YAAtBjI,sBAAsB;MAAAkI,SAAA;MAAAC,SAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;UCvBnC5R,EAAA,CAAAE,cAAA,aAAiE;UAavDF,EAAA,CAAAC,SAAA,aAIE;UACFD,EAAA,CAAAE,cAAA,UAAK;UAEDF,EAAA,CAAAe,MAAA,GACF;UAAAf,EAAA,CAAAgB,YAAA,EAAK;UACLhB,EAAA,CAAAE,cAAA,WAAkC;UAAAF,EAAA,CAAAe,MAAA,gBAAQ;UAAAf,EAAA,CAAAgB,YAAA,EAAI;UAKlDhB,EAAA,CAAAE,cAAA,cAAyC;UAKnCF,EAAA,CAAAG,UAAA,mBAAA2R,yDAAA;YAAA,OAAAD,GAAA,CAAAzH,iBAAA,IAAAyH,GAAA,CAAAzH,iBAAA;UAAA,EAAgD;UAGhDpK,EAAA,CAAAC,SAAA,aAA4C;UAC9CD,EAAA,CAAAgB,YAAA,EAAS;UAGThB,EAAA,CAAAiB,UAAA,KAAA8Q,sCAAA,kBAwBM;UACR/R,EAAA,CAAAgB,YAAA,EAAM;UAGNhB,EAAA,CAAAE,cAAA,kBAGC;UADCF,EAAA,CAAAG,UAAA,mBAAA6R,yDAAA;YAAA,OAASH,GAAA,CAAAxD,gBAAA,EAAkB;UAAA,EAAC;UAE5BrO,EAAA,CAAAC,SAAA,aAAuC;UACzCD,EAAA,CAAAgB,YAAA,EAAS;UAKbhB,EAAA,CAAAE,cAAA,cAAsB;UAIlBF,EAAA,CAAAG,UAAA,2BAAA8R,gEAAAC,MAAA;YAAA,OAAAL,GAAA,CAAAxH,WAAA,GAAA6H,MAAA;UAAA,EAAyB,mBAAAC,wDAAAD,MAAA;YAAA,OAChBL,GAAA,CAAAvD,aAAA,CAAA4D,MAAA,CAAqB;UAAA,EADL;UAH3BlS,EAAA,CAAAgB,YAAA,EAOE;UACFhB,EAAA,CAAAC,SAAA,aAAiE;UACjED,EAAA,CAAAiB,UAAA,KAAAmR,yCAAA,qBAMS;UACXpS,EAAA,CAAAgB,YAAA,EAAM;UAIRhB,EAAA,CAAAE,cAAA,eAAgD;UAQ5CF,EAAA,CAAAG,UAAA,mBAAAkS,yDAAA;YAAA,OAASR,GAAA,CAAA9D,SAAA,CAAU,eAAe,CAAC;UAAA,EAAC;UAEpC/N,EAAA,CAAAC,SAAA,aAAoC;UACpCD,EAAA,CAAAE,cAAA,eAAqB;UAAAF,EAAA,CAAAe,MAAA,mBAAW;UAAAf,EAAA,CAAAgB,YAAA,EAAM;UAGxChB,EAAA,CAAAE,cAAA,kBAQC;UADCF,EAAA,CAAAG,UAAA,mBAAAmS,yDAAA;YAAA,OAAST,GAAA,CAAA9D,SAAA,CAAU,OAAO,CAAC;UAAA,EAAC;UAE5B/N,EAAA,CAAAC,SAAA,aAAiC;UACjCD,EAAA,CAAAE,cAAA,eAAqB;UAAAF,EAAA,CAAAe,MAAA,gBAAQ;UAAAf,EAAA,CAAAgB,YAAA,EAAM;UAGrChB,EAAA,CAAAE,cAAA,kBAQC;UADCF,EAAA,CAAAG,UAAA,mBAAAoS,yDAAA;YAAA,OAASV,GAAA,CAAA9D,SAAA,CAAU,eAAe,CAAC;UAAA,EAAC;UAEpC/N,EAAA,CAAAC,SAAA,aAAgC;UAChCD,EAAA,CAAAE,cAAA,eAAqB;UAAAF,EAAA,CAAAe,MAAA,qBAAa;UAAAf,EAAA,CAAAgB,YAAA,EAAM;UACxChB,EAAA,CAAAiB,UAAA,KAAAuR,uCAAA,mBAKO;UACTxS,EAAA,CAAAgB,YAAA,EAAS;UAIXhB,EAAA,CAAAE,cAAA,eAAoD;UAIlDF,EAAA,CAAAiB,UAAA,KAAAwR,sCAAA,kBA0IM;UAKNzS,EAAA,CAAAiB,UAAA,KAAAyR,sCAAA,kBAmIM;UAKN1S,EAAA,CAAAiB,UAAA,KAAA0R,sCAAA,kBA6EM;UACR3S,EAAA,CAAAgB,YAAA,EAAM;UAMRhB,EAAA,CAAAE,cAAA,eAA+C;UAKzCF,EAAA,CAAAG,UAAA,mBAAAyS,yDAAA;YAAA,OAASf,GAAA,CAAAxD,gBAAA,EAAkB;UAAA,EAAC;UAE5BrO,EAAA,CAAAC,SAAA,aAAsC;UACxCD,EAAA,CAAAgB,YAAA,EAAS;UAIXhB,EAAA,CAAAE,cAAA,eAAoB;UAClBF,EAAA,CAAAC,SAAA,qBAA+B;UACjCD,EAAA,CAAAgB,YAAA,EAAM;;;UAzgBNhB,EAAA,CAAAmB,SAAA,GAAkC;UAAlCnB,EAAA,CAAAuB,WAAA,YAAAsQ,GAAA,CAAA1H,gBAAA,CAAkC;UAQ1BnK,EAAA,CAAAmB,SAAA,GAAiE;UAAjEnB,EAAA,CAAA4B,UAAA,SAAAiQ,GAAA,CAAA5H,WAAA,kBAAA4H,GAAA,CAAA5H,WAAA,CAAApE,KAAA,0CAAA7F,EAAA,CAAA+C,aAAA,CAAiE,QAAA8O,GAAA,CAAA5H,WAAA,kBAAA4H,GAAA,CAAA5H,WAAA,CAAAnE,QAAA;UAM/D9F,EAAA,CAAAmB,SAAA,GACF;UADEnB,EAAA,CAAAoC,kBAAA,MAAAyP,GAAA,CAAA5H,WAAA,kBAAA4H,GAAA,CAAA5H,WAAA,CAAAnE,QAAA,MACF;UAmBG9F,EAAA,CAAAmB,SAAA,GAAuB;UAAvBnB,EAAA,CAAA4B,UAAA,SAAAiQ,GAAA,CAAAzH,iBAAA,CAAuB;UAyC5BpK,EAAA,CAAAmB,SAAA,GAAyB;UAAzBnB,EAAA,CAAA4B,UAAA,YAAAiQ,GAAA,CAAAxH,WAAA,CAAyB;UAOxBrK,EAAA,CAAAmB,SAAA,GAAiB;UAAjBnB,EAAA,CAAA4B,UAAA,SAAAiQ,GAAA,CAAAxH,WAAA,CAAiB;UAapBrK,EAAA,CAAAmB,SAAA,GAA8C;UAA9CnB,EAAA,CAAAuB,WAAA,WAAAsQ,GAAA,CAAA3H,SAAA,qBAA8C,kBAAA2H,GAAA,CAAA3H,SAAA,oCAAA2H,GAAA,CAAA3H,SAAA,yCAAA2H,GAAA,CAAA3H,SAAA,uCAAA2H,GAAA,CAAA3H,SAAA;UAa9ClK,EAAA,CAAAmB,SAAA,GAAsC;UAAtCnB,EAAA,CAAAuB,WAAA,WAAAsQ,GAAA,CAAA3H,SAAA,aAAsC,kBAAA2H,GAAA,CAAA3H,SAAA,4BAAA2H,GAAA,CAAA3H,SAAA,iCAAA2H,GAAA,CAAA3H,SAAA,+BAAA2H,GAAA,CAAA3H,SAAA;UAatClK,EAAA,CAAAmB,SAAA,GAA8C;UAA9CnB,EAAA,CAAAuB,WAAA,WAAAsQ,GAAA,CAAA3H,SAAA,qBAA8C,kBAAA2H,GAAA,CAAA3H,SAAA,oCAAA2H,GAAA,CAAA3H,SAAA,yCAAA2H,GAAA,CAAA3H,SAAA,uCAAA2H,GAAA,CAAA3H,SAAA;UAU3ClK,EAAA,CAAAmB,SAAA,GAA8B;UAA9BnB,EAAA,CAAA4B,UAAA,SAAAiQ,GAAA,CAAAvP,aAAA,CAAAC,MAAA,KAA8B;UAa7BvC,EAAA,CAAAmB,SAAA,GAAmC;UAAnCnB,EAAA,CAAA4B,UAAA,SAAAiQ,GAAA,CAAA3H,SAAA,qBAAmC;UA+InClK,EAAA,CAAAmB,SAAA,GAA2B;UAA3BnB,EAAA,CAAA4B,UAAA,SAAAiQ,GAAA,CAAA3H,SAAA,aAA2B;UAwI3BlK,EAAA,CAAAmB,SAAA,GAAmC;UAAnCnB,EAAA,CAAA4B,UAAA,SAAAiQ,GAAA,CAAA3H,SAAA,qBAAmC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}