"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([["src_app_views_front_equipes_equipes_module_ts"],{

/***/ 6014:
/*!******************************************************************!*\
  !*** ./src/app/views/front/equipes/ai-chat/ai-chat.component.ts ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AiChatComponent: () => (/* binding */ AiChatComponent)
/* harmony export */ });
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rxjs/operators */ 9475);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var src_app_services_ai_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/services/ai.service */ 1412);
/* harmony import */ var src_app_services_task_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/services/task.service */ 2611);
/* harmony import */ var src_app_services_notification_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/services/notification.service */ 7473);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/forms */ 4456);







const _c0 = function (a0, a1) {
  return {
    "user-message": a0,
    "assistant-message": a1
  };
};
const _c1 = function (a0) {
  return {
    "flex-row-reverse": a0
  };
};
const _c2 = function (a0, a1) {
  return {
    "bg-primary": a0,
    "bg-success": a1
  };
};
const _c3 = function (a0, a1) {
  return {
    "user-bubble": a0,
    "assistant-bubble": a1
  };
};
function AiChatComponent_div_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 19)(1, "div", 20)(2, "div", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](3, "i", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](4, "div", 22);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](5, "p", 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](6, "div", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const message_r6 = ctx.$implicit;
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpureFunction2"](8, _c0, message_r6.role === "user", message_r6.role === "assistant"));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpureFunction1"](11, _c1, message_r6.role === "user"));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpureFunction2"](13, _c2, message_r6.role === "user", message_r6.role === "assistant"));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngClass", message_r6.role === "user" ? "bi-person-fill" : "bi-robot");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpureFunction2"](16, _c3, message_r6.role === "user", message_r6.role === "assistant"));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("innerHTML", message_r6.content, _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵsanitizeHtml"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate2"](" ", message_r6.role === "user" ? "Vous" : "Assistant IA", " \u2022 ", ctx_r1.getCurrentTime(), " ");
  }
}
function AiChatComponent_div_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 25)(1, "div", 26)(2, "div", 27);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](3, "i", 28);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](4, "div", 29)(5, "div", 30);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](6, "span")(7, "span")(8, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()();
  }
}
const _c4 = "linear-gradient(45deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7))";
const _c5 = function (a1) {
  return {
    "background": _c4,
    "border-left": a1
  };
};
const _c6 = function (a0) {
  return {
    "background": a0
  };
};
const _c7 = function (a0) {
  return {
    "color": a0
  };
};
function AiChatComponent_div_7_div_14_div_14_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 78)(1, "div", 79);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](2, "i", 80);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](3, "div")(4, "div", 81);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](5, "Responsable");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](6, "div", 82);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r14 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    const i_r11 = ctx_r14.index;
    const entity_r10 = ctx_r14.$implicit;
    const ctx_r12 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngStyle", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpureFunction1"](4, _c5, "4px solid " + ctx_r12.getColorForIndex(i_r11)));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngStyle", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpureFunction1"](6, _c6, ctx_r12.getGradientForIndex(i_r11)));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngStyle", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpureFunction1"](8, _c7, ctx_r12.getColorForIndex(i_r11)));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](entity_r10.assignedTo);
  }
}
const _c8 = function (a0, a1, a2) {
  return {
    "high-priority": a0,
    "medium-priority": a1,
    "low-priority": a2
  };
};
const _c9 = function (a0, a1, a2) {
  return {
    "bg-danger": a0,
    "bg-warning text-dark": a1,
    "bg-info text-dark": a2
  };
};
function AiChatComponent_div_7_div_14_div_22_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 83)(1, "div", 84)(2, "h6", 85);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](4, "span", 86);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](6, "div", 87);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const task_r15 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpureFunction3"](5, _c8, task_r15.priority === "high", task_r15.priority === "medium", task_r15.priority === "low"));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](task_r15.title);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpureFunction3"](9, _c9, task_r15.priority === "high", task_r15.priority === "medium", task_r15.priority === "low"));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", task_r15.priority === "high" ? "Haute" : task_r15.priority === "medium" ? "Moyenne" : "Basse", " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", task_r15.description, " ");
  }
}
function AiChatComponent_div_7_div_14_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 61)(1, "div", 62)(2, "div", 63)(3, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](5, "div", 64)(6, "div", 65);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](7, "i", 66);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](8, "h5", 67);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](10, "div", 68)(11, "div", 69)(12, "p", 70);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](13);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](14, AiChatComponent_div_7_div_14_div_14_Template, 8, 10, "div", 71);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](15, "div", 72)(16, "h6", 73);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](17, "i", 74);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](18, " T\u00E2ches \u00E0 r\u00E9aliser ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](19, "span", 75);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](20);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](21, "div", 76);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](22, AiChatComponent_div_7_div_14_div_22_Template, 8, 13, "div", 77);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const entity_r10 = ctx.$implicit;
    const i_r11 = ctx.index;
    const ctx_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngStyle", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpureFunction1"](12, _c6, ctx_r8.getGradientForIndex(i_r11)));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"]("Module ", i_r11 + 1, "");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngStyle", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpureFunction1"](14, _c6, ctx_r8.getGradientForIndex(i_r11)));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngClass", ctx_r8.getIconForModule(entity_r10.name))("ngStyle", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpureFunction1"](16, _c7, ctx_r8.getColorForIndex(i_r11)));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](entity_r10.name);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](entity_r10.description);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", entity_r10.assignedTo);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngStyle", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpureFunction1"](18, _c7, ctx_r8.getColorForIndex(i_r11)));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngStyle", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpureFunction1"](20, _c6, ctx_r8.getGradientForIndex(i_r11)));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", entity_r10.tasks.length, " t\u00E2ches ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngForOf", entity_r10.tasks);
  }
}
function AiChatComponent_div_7_div_16_div_12_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 97)(1, "div", 98)(2, "h6", 53);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](4, "span", 99);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](6, "p", 100);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const task_r20 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](task_r20.title);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpureFunction3"](4, _c9, task_r20.priority === "high", task_r20.priority === "medium", task_r20.priority === "low"));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", task_r20.priority === "high" ? "Haute" : task_r20.priority === "medium" ? "Moyenne" : "Basse", " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](task_r20.description);
  }
}
function AiChatComponent_div_7_div_16_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 88)(1, "h2", 89)(2, "button", 90)(3, "strong");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](5, "span", 91);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](7, "div", 92)(8, "div", 93)(9, "p", 94);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](10);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](11, "div", 95);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](12, AiChatComponent_div_7_div_16_div_12_Template, 8, 8, "div", 96);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const entity_r17 = ctx.$implicit;
    const i_r18 = ctx.index;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("id", "heading" + i_r18);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵattribute"]("data-bs-target", "#collapse" + i_r18)("aria-expanded", i_r18 === 0)("aria-controls", "collapse" + i_r18);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](entity_r17.name);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"]("", entity_r17.tasks.length, " t\u00E2ches");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("id", "collapse" + i_r18);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵattribute"]("aria-labelledby", "heading" + i_r18);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](entity_r17.description);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngForOf", entity_r17.tasks);
  }
}
function AiChatComponent_div_7_Template(rf, ctx) {
  if (rf & 1) {
    const _r22 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 31)(1, "div", 32)(2, "div", 33)(3, "div")(4, "h5", 34);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](5, "i", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](7, "p", 36);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](8, "i", 37);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](10, "span", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](11, "i", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](12);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](13, "div", 40);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](14, AiChatComponent_div_7_div_14_Template, 23, 22, "div", 41);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](15, "div", 42);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](16, AiChatComponent_div_7_div_16_Template, 13, 10, "div", 43);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](17, "div", 44)(18, "div", 45)(19, "div", 46)(20, "div", 47)(21, "div", 48)(22, "h5", 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](23, "i", 50);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](24, " Plan de projet pr\u00EAt \u00E0 \u00EAtre impl\u00E9ment\u00E9 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](25, "div", 51)(26, "div", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](27, "1");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](28, "div")(29, "h6", 53);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](30, "Cr\u00E9ation des t\u00E2ches");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](31, "p", 54);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](32);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](33, "div", 51)(34, "div", 55);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](35, "2");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](36, "div")(37, "h6", 53);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](38, "Assignation aux membres");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](39, "p", 54);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](40);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](41, "div", 26)(42, "div", 56);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](43, "3");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](44, "div")(45, "h6", 53);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](46, "Suivi du projet");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](47, "p", 54);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](48, "Vous pourrez suivre l'avancement dans le tableau de bord des t\u00E2ches");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](49, "div", 57)(50, "button", 58);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function AiChatComponent_div_7_Template_button_click_50_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r22);
      const ctx_r21 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r21.createTasks());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](51, "i", 59);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](52, " Cr\u00E9er les t\u00E2ches ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](53, "div", 60);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](54, "i", 37);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](55, " Cette action est irr\u00E9versible ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()()()()();
  }
  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" Plan du projet \"", ctx_r3.generatedContent.projectTitle, "\" ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate2"](" ", ctx_r3.generatedContent.entities.length, " modules g\u00E9n\u00E9r\u00E9s avec ", ctx_r3.countTasks(ctx_r3.generatedContent), " t\u00E2ches au total ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", ctx_r3.team && ctx_r3.team.members ? ctx_r3.team.members.length : 0, " membres ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngForOf", ctx_r3.generatedContent.entities);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngForOf", ctx_r3.generatedContent.entities);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](16);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"]("", ctx_r3.countTasks(ctx_r3.generatedContent), " t\u00E2ches seront cr\u00E9\u00E9es dans le syst\u00E8me");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"]("Les t\u00E2ches seront assign\u00E9es aux ", ctx_r3.team && ctx_r3.team.members ? ctx_r3.team.members.length : 0, " membres de l'\u00E9quipe");
  }
}
function AiChatComponent_div_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 101);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", ctx_r4.error, " ");
  }
}
function AiChatComponent_div_10_Template(rf, ctx) {
  if (rf & 1) {
    const _r24 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 102)(1, "div", 103)(2, "div", 104)(3, "h6", 105);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](4, "i", 106);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](5, " G\u00E9n\u00E9rer des t\u00E2ches avec l'IA ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](6, "div", 107)(7, "label", 108);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](8, "Entrez le titre de votre projet");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](9, "div", 13)(10, "span", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](11, "i", 109);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](12, "input", 110);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("ngModelChange", function AiChatComponent_div_10_Template_input_ngModelChange_12_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r24);
      const ctx_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r23.projectTitle = $event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](13, "small", 111);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](14, "i", 37);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](15);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](16, "div", 112)(17, "button", 113);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function AiChatComponent_div_10_Template_button_click_17_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r24);
      const ctx_r25 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r25.generateTasks());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](18, "i", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](19);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()()();
  }
  if (rf & 2) {
    const ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](12);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngModel", ctx_r5.projectTitle)("disabled", ctx_r5.isGenerating);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" L'IA g\u00E9n\u00E9rera ", ctx_r5.team && ctx_r5.team.members ? ctx_r5.team.members.length : 3, " modules, un pour chaque membre de l'\u00E9quipe. ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("disabled", ctx_r5.isGenerating || !ctx_r5.projectTitle.trim());
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngClass", ctx_r5.isGenerating ? "bi-hourglass-split spin" : "bi-magic");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", ctx_r5.isGenerating ? "G\u00E9n\u00E9ration en cours..." : "G\u00E9n\u00E9rer des t\u00E2ches", " ");
  }
}
class AiChatComponent {
  constructor(aiService, taskService, notificationService) {
    this.aiService = aiService;
    this.taskService = taskService;
    this.notificationService = notificationService;
    this.projectTitle = '';
    this.isGenerating = false;
    this.generatedContent = null;
    this.error = null;
    // Pour le chat
    this.messages = [];
    this.userQuestion = '';
    this.isAskingQuestion = false;
  }
  ngOnInit() {
    // Ajouter un message de bienvenue
    this.messages.push({
      role: 'assistant',
      content: 'Bonjour ! Je suis votre assistant IA pour la gestion de projet. Entrez le titre de votre projet pour que je puisse vous aider à le diviser en tâches, ou posez-moi une question sur la gestion de projet.'
    });
  }
  generateTasks() {
    if (!this.projectTitle.trim()) {
      this.notificationService.showError('Veuillez entrer un titre de projet');
      return;
    }
    // Vérifier si l'équipe a des membres, sinon utiliser un nombre par défaut
    let memberCount = this.team && this.team.members ? this.team.members.length : 3;
    // S'assurer que nous avons au moins 3 entités pour un projet significatif
    const effectiveMemberCount = Math.max(memberCount, 3);
    if (memberCount === 0) {
      this.notificationService.showWarning("L'équipe n'a pas de membres. Des tâches génériques seront créées.");
    }
    this.isGenerating = true;
    this.error = null;
    console.log(`Génération de tâches pour ${effectiveMemberCount} entités (équipe de ${memberCount} membres)`);
    // Ajouter la demande aux messages
    this.messages.push({
      role: 'user',
      content: `Génère des tâches pour le projet "${this.projectTitle}" avec exactement ${effectiveMemberCount} entités, une pour chaque membre de l'équipe. Chaque entité doit représenter un module distinct du projet.`
    });
    // Ajouter un message de chargement
    const loadingMessageIndex = this.messages.length;
    this.messages.push({
      role: 'assistant',
      content: 'Je génère des tâches pour votre projet...'
    });
    // Récupérer les informations sur les membres de l'équipe
    let teamMembers = [];
    if (this.team && this.team.members) {
      // Utiliser les IDs des membres
      teamMembers = this.team.members.map((memberId, index) => {
        return {
          id: memberId,
          name: `Membre ${index + 1}`,
          role: 'membre'
        };
      });
      console.log("Informations sur les membres passées à l'IA:", teamMembers);
    }
    this.aiService.generateProjectTasks(this.projectTitle, memberCount, teamMembers).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_4__.finalize)(() => this.isGenerating = false)).subscribe({
      next: result => {
        if (!result || !result.entities || result.entities.length === 0) {
          console.error("Résultat invalide reçu de l'API:", result);
          this.handleGenerationError(loadingMessageIndex, 'Format de réponse invalide');
          return;
        }
        this.generatedContent = result;
        // Remplacer le message de chargement par la réponse
        this.messages[loadingMessageIndex] = {
          role: 'assistant',
          content: `J'ai généré ${result.entities.length} entités pour votre projet "${result.projectTitle}" avec un total de ${this.countTasks(result)} tâches.`
        };
        this.notificationService.showSuccess('Tâches générées avec succès');
      },
      error: error => {
        console.error('Erreur lors de la génération des tâches:', error);
        this.handleGenerationError(loadingMessageIndex, error.message || 'Erreur inconnue');
      }
    });
  }
  // Méthode pour gérer les erreurs de génération
  handleGenerationError(messageIndex, errorDetails) {
    this.error = 'Impossible de générer les tâches. Veuillez réessayer.';
    // Remplacer le message de chargement par le message d'erreur
    this.messages[messageIndex] = {
      role: 'assistant',
      content: "Désolé, je n'ai pas pu générer les tâches. Veuillez réessayer avec un titre de projet différent."
    };
    this.notificationService.showError('Erreur lors de la génération des tâches: ' + errorDetails);
  }
  askQuestion() {
    if (!this.userQuestion.trim()) {
      return;
    }
    const question = this.userQuestion.trim();
    this.userQuestion = '';
    this.isAskingQuestion = true;
    // Ajouter la question aux messages
    this.messages.push({
      role: 'user',
      content: question
    });
    const projectContext = {
      title: this.projectTitle || (this.generatedContent ? this.generatedContent.projectTitle : ''),
      description: "Projet géré par l'équipe " + (this.team ? this.team.name : '')
    };
    this.aiService.askProjectQuestion(question, projectContext).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_4__.finalize)(() => this.isAskingQuestion = false)).subscribe({
      next: response => {
        // Ajouter la réponse aux messages
        this.messages.push({
          role: 'assistant',
          content: response
        });
      },
      error: error => {
        console.error("Erreur lors de la demande à l'IA:", error);
        // Ajouter l'erreur aux messages
        this.messages.push({
          role: 'assistant',
          content: "Désolé, je n'ai pas pu répondre à votre question. Veuillez réessayer."
        });
        this.notificationService.showError("Erreur lors de la communication avec l'IA");
      }
    });
  }
  createTasks() {
    if (!this.generatedContent || !this.team || !this.team._id) {
      this.notificationService.showError('Aucune tâche générée ou équipe invalide');
      return;
    }
    let createdCount = 0;
    const totalTasks = this.countTasks(this.generatedContent);
    // Vérifier si l'équipe a des membres
    if (!this.team.members || this.team.members.length === 0) {
      this.notificationService.showError("L'équipe n'a pas de membres pour assigner les tâches");
      return;
    }
    // Préparer la liste des membres de l'équipe
    const teamMembers = this.team.members.map(member => {
      return typeof member === 'string' ? member : member.userId;
    });
    // Créer un mapping des noms de membres vers leurs IDs
    const memberNameToIdMap = {};
    teamMembers.forEach((memberId, index) => {
      memberNameToIdMap[`Membre ${index + 1}`] = memberId;
    });
    console.log("Membres de l'équipe disponibles pour l'assignation:", teamMembers);
    console.log('Mapping des noms de membres vers leurs IDs:', memberNameToIdMap);
    // Pour chaque entité
    this.generatedContent.entities.forEach(entity => {
      // Déterminer le membre assigné à cette entité
      let assignedMemberId;
      // Si l'IA a suggéré une assignation
      if (entity.assignedTo) {
        // Essayer de trouver l'ID du membre à partir du nom suggéré
        const memberName = entity.assignedTo;
        if (memberNameToIdMap[memberName]) {
          assignedMemberId = memberNameToIdMap[memberName];
          console.log(`Assignation suggérée par l'IA: Entité "${entity.name}" assignée à "${memberName}" (ID: ${assignedMemberId})`);
        } else {
          // Si le nom n'est pas trouvé, assigner aléatoirement
          const randomMemberIndex = Math.floor(Math.random() * teamMembers.length);
          assignedMemberId = teamMembers[randomMemberIndex];
          console.log(`Nom de membre "${memberName}" non trouvé, assignation aléatoire à l'index ${randomMemberIndex}`);
        }
      } else {
        // Si pas d'assignation suggérée, assigner aléatoirement
        const randomMemberIndex = Math.floor(Math.random() * teamMembers.length);
        assignedMemberId = teamMembers[randomMemberIndex];
        console.log(`Pas d'assignation suggérée, assignation aléatoire à l'index ${randomMemberIndex}`);
      }
      // Pour chaque tâche dans l'entité
      entity.tasks.forEach(taskData => {
        const task = {
          title: taskData.title,
          description: `[${entity.name}] ${taskData.description}`,
          status: taskData.status || 'todo',
          priority: taskData.priority || 'medium',
          teamId: this.team._id || '',
          // Utiliser l'ID du membre assigné à l'entité
          assignedTo: assignedMemberId
        };
        this.taskService.createTask(task).subscribe({
          next: () => {
            createdCount++;
            if (createdCount === totalTasks) {
              this.notificationService.showSuccess(`${createdCount} tâches créées avec succès et assignées aux membres de l'équipe`);
              // Réinitialiser après création
              this.generatedContent = null;
              this.projectTitle = '';
            }
          },
          error: error => {
            console.error('Erreur lors de la création de la tâche:', error);
            this.notificationService.showError('Erreur lors de la création des tâches');
          }
        });
      });
    });
  }
  countTasks(content) {
    if (!content || !content.entities) return 0;
    return content.entities.reduce((total, entity) => {
      return total + (entity.tasks ? entity.tasks.length : 0);
    }, 0);
  }
  // Méthode pour obtenir un dégradé de couleur basé sur l'index
  getGradientForIndex(index) {
    // Liste de dégradés prédéfinis
    const gradients = ['linear-gradient(45deg, #007bff, #6610f2)', 'linear-gradient(45deg, #11998e, #38ef7d)', 'linear-gradient(45deg, #FC5C7D, #6A82FB)', 'linear-gradient(45deg, #FF8008, #FFC837)', 'linear-gradient(45deg, #8E2DE2, #4A00E0)', 'linear-gradient(45deg, #2193b0, #6dd5ed)', 'linear-gradient(45deg, #373B44, #4286f4)', 'linear-gradient(45deg, #834d9b, #d04ed6)', 'linear-gradient(45deg, #0cebeb, #20e3b2, #29ffc6)' // Turquoise
    ];
    // Utiliser le modulo pour s'assurer que nous ne dépassons pas le tableau
    return gradients[index % gradients.length];
  }
  // Méthode pour obtenir une couleur unique basée sur l'index
  getColorForIndex(index) {
    // Liste de couleurs prédéfinies
    const colors = ['#007bff', '#11998e', '#FC5C7D', '#FF8008', '#8E2DE2', '#2193b0', '#373B44', '#834d9b', '#0cebeb' // Turquoise
    ];
    // Utiliser le modulo pour s'assurer que nous ne dépassons pas le tableau
    return colors[index % colors.length];
  }
  // Méthode pour obtenir une icône en fonction du nom du module
  getIconForModule(moduleName) {
    // Convertir le nom du module en minuscules pour faciliter la comparaison
    const name = moduleName.toLowerCase();
    // Mapper les noms de modules courants à des icônes Bootstrap
    if (name.includes('crud') || name.includes('api') || name.includes('données') || name.includes('base')) {
      return 'bi-database-fill';
    } else if (name.includes('interface') || name.includes('ui') || name.includes('front') || name.includes('utilisateur')) {
      return 'bi-window';
    } else if (name.includes('déploiement') || name.includes('serveur') || name.includes('cloud')) {
      return 'bi-cloud-arrow-up-fill';
    } else if (name.includes('test') || name.includes('qualité') || name.includes('qa')) {
      return 'bi-bug-fill';
    } else if (name.includes('sécurité') || name.includes('auth')) {
      return 'bi-shield-lock-fill';
    } else if (name.includes('paiement') || name.includes('transaction')) {
      return 'bi-credit-card-fill';
    } else if (name.includes('utilisateur') || name.includes('user') || name.includes('profil')) {
      return 'bi-person-fill';
    } else if (name.includes('doc') || name.includes('documentation')) {
      return 'bi-file-text-fill';
    } else if (name.includes('mobile') || name.includes('app')) {
      return 'bi-phone-fill';
    } else if (name.includes('backend') || name.includes('serveur')) {
      return 'bi-server';
    } else if (name.includes('analytics') || name.includes('statistique') || name.includes('seo')) {
      return 'bi-graph-up';
    }
    // Icône par défaut si aucune correspondance n'est trouvée
    return 'bi-code-square';
  }
  // Méthode pour obtenir l'heure actuelle au format HH:MM
  getCurrentTime() {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  }
  static {
    this.ɵfac = function AiChatComponent_Factory(t) {
      return new (t || AiChatComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](src_app_services_ai_service__WEBPACK_IMPORTED_MODULE_0__.AiService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](src_app_services_task_service__WEBPACK_IMPORTED_MODULE_1__.TaskService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](src_app_services_notification_service__WEBPACK_IMPORTED_MODULE_2__.NotificationService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineComponent"]({
      type: AiChatComponent,
      selectors: [["app-ai-chat"]],
      inputs: {
        team: "team"
      },
      decls: 19,
      vars: 9,
      consts: [[1, "ai-chat-container", "w-100"], [1, "card", "border-0", "shadow-sm", "w-100"], [1, "card-body", "p-0"], [1, "chat-messages", "p-3"], ["chatContainer", ""], ["class", "message mb-3", 3, "ngClass", 4, "ngFor", "ngForOf"], ["class", "message assistant-message mb-3", 4, "ngIf"], ["class", "generated-content p-4 border-top", 4, "ngIf"], ["class", "alert alert-danger m-3", 4, "ngIf"], [1, "chat-input", "p-3", "border-top"], ["class", "mb-4", 4, "ngIf"], [1, "card", "border-0", "bg-light", "rounded-4", "shadow-sm"], [1, "card-body", "p-2"], [1, "input-group"], [1, "input-group-text", "bg-white", "border-0"], [1, "bi", "bi-chat-dots", "text-primary"], ["type", "text", "placeholder", "Posez une question sur la gestion de projet...", 1, "form-control", "border-0", "bg-white", "shadow-none", 3, "ngModel", "disabled", "ngModelChange", "keyup.enter"], [1, "btn", "btn-primary", "rounded-circle", 2, "width", "38px", "height", "38px", 3, "disabled", "click"], [1, "bi", 3, "ngClass"], [1, "message", "mb-3", 3, "ngClass"], [1, "d-flex", 3, "ngClass"], [1, "message-avatar", "rounded-circle", "d-flex", "align-items-center", "justify-content-center", "me-2", 3, "ngClass"], [1, "message-bubble", "p-3", "rounded-4", "shadow-sm", 3, "ngClass"], [1, "mb-0", 3, "innerHTML"], [1, "message-time", "small", "text-muted", "mt-1", "text-end"], [1, "message", "assistant-message", "mb-3"], [1, "d-flex"], [1, "message-avatar", "rounded-circle", "d-flex", "align-items-center", "justify-content-center", "me-2", "bg-success"], [1, "bi", "bi-robot"], [1, "message-bubble", "assistant-bubble", "p-3", "rounded-4", "shadow-sm"], [1, "typing-indicator"], [1, "generated-content", "p-4", "border-top"], [1, "generated-header", "mb-4", "p-3", "rounded-4", "shadow-sm", 2, "background", "linear-gradient(120deg, rgba(13, 110, 253, 0.1), rgba(102, 16, 242, 0.1))"], [1, "d-flex", "justify-content-between", "align-items-center"], [1, "text-primary", "mb-1"], [1, "bi", "bi-diagram-3-fill", "me-2"], [1, "text-muted", "mb-0"], [1, "bi", "bi-info-circle", "me-1"], [1, "badge", "bg-primary", "rounded-pill", "px-3", "py-2"], [1, "bi", "bi-people-fill", "me-1"], [1, "row", "mb-4"], ["class", "col-lg-3 col-md-4 col-sm-6 mb-4", 4, "ngFor", "ngForOf"], ["id", "generatedTasksAccordion", 1, "accordion", "d-none"], ["class", "accordion-item border-0 mb-2", 4, "ngFor", "ngForOf"], [1, "mt-5"], [1, "card", "border-0", "rounded-4", "shadow-sm", "create-tasks-card"], [1, "card-body", "p-4"], [1, "row", "align-items-center"], [1, "col-lg-8"], [1, "mb-3", "text-success"], [1, "bi", "bi-check-circle-fill", "me-2"], [1, "d-flex", "mb-3"], [1, "step-circle", "bg-success", "text-white", "me-3"], [1, "mb-1"], [1, "text-muted", "mb-0", "small"], [1, "step-circle", "bg-primary", "text-white", "me-3"], [1, "step-circle", "bg-info", "text-white", "me-3"], [1, "col-lg-4", "text-center", "mt-4", "mt-lg-0"], [1, "btn", "btn-success", "btn-lg", "rounded-pill", "px-5", "py-3", "shadow", "create-button", 3, "click"], [1, "bi", "bi-plus-circle-fill", "me-2"], [1, "text-muted", "small", "mt-2"], [1, "col-lg-3", "col-md-4", "col-sm-6", "mb-4"], [1, "module-card", "card", "h-100", "border-0", "shadow-sm"], [1, "module-ribbon", 3, "ngStyle"], [1, "card-header", "text-white", "position-relative", "py-4", 3, "ngStyle"], [1, "module-icon-large", "rounded-circle", "bg-white", "d-flex", "align-items-center", "justify-content-center", "shadow"], [1, "bi", 3, "ngClass", "ngStyle"], [1, "mt-3", "mb-0", "text-center"], [1, "card-body"], [1, "description-box", "p-3", "rounded-3", "bg-light", "mb-3"], [1, "mb-0"], ["class", "assignation-badge mb-3 p-3 rounded-3 d-flex align-items-center", 3, "ngStyle", 4, "ngIf"], [1, "task-header", "d-flex", "justify-content-between", "align-items-center", "mb-3", "pb-2", "border-bottom"], [1, "mb-0", "d-flex", "align-items-center"], [1, "bi", "bi-list-check", "me-2", 3, "ngStyle"], [1, "badge", "rounded-pill", 3, "ngStyle"], [1, "task-list"], ["class", "task-item mb-3 p-3 rounded-3 shadow-sm", 3, "ngClass", 4, "ngFor", "ngForOf"], [1, "assignation-badge", "mb-3", "p-3", "rounded-3", "d-flex", "align-items-center", 3, "ngStyle"], [1, "member-avatar", "rounded-circle", "me-3", "d-flex", "align-items-center", "justify-content-center", "text-white", 3, "ngStyle"], [1, "bi", "bi-person-fill"], [1, "small", "text-muted"], [1, "fw-bold", 3, "ngStyle"], [1, "task-item", "mb-3", "p-3", "rounded-3", "shadow-sm", 3, "ngClass"], [1, "d-flex", "justify-content-between", "align-items-center", "mb-2"], [1, "mb-0", "task-title"], [1, "badge", "rounded-pill", 3, "ngClass"], [1, "task-description", "text-muted", "small"], [1, "accordion-item", "border-0", "mb-2"], [1, "accordion-header", 3, "id"], ["type", "button", "data-bs-toggle", "collapse", 1, "accordion-button", "collapsed"], [1, "badge", "bg-primary", "rounded-pill", "ms-2"], ["data-bs-parent", "#generatedTasksAccordion", 1, "accordion-collapse", "collapse", 3, "id"], [1, "accordion-body"], [1, "text-muted", "mb-3"], [1, "list-group"], ["class", "list-group-item list-group-item-action", 4, "ngFor", "ngForOf"], [1, "list-group-item", "list-group-item-action"], [1, "d-flex", "w-100", "justify-content-between"], [1, "badge", 3, "ngClass"], [1, "mb-1", "small"], [1, "alert", "alert-danger", "m-3"], [1, "mb-4"], [1, "card", "border-0", "bg-light", "rounded-4", "shadow-sm", "mb-3"], [1, "card-body", "p-3"], [1, "mb-3", "d-flex", "align-items-center"], [1, "bi", "bi-stars", "me-2", "text-primary"], [1, "mb-3"], ["for", "projectTitle", 1, "form-label", "small", "text-muted"], [1, "bi", "bi-lightbulb", "text-primary"], ["type", "text", "id", "projectTitle", "placeholder", "Ex: Site e-commerce, Application mobile, Syst\u00E8me de gestion...", 1, "form-control", "border-0", "bg-white", "shadow-none", 3, "ngModel", "disabled", "ngModelChange"], [1, "text-muted", "mt-1", "d-block"], [1, "d-grid"], [1, "btn", "btn-primary", "rounded-3", 3, "disabled", "click"]],
      template: function AiChatComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "div", 3, 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](5, AiChatComponent_div_5_Template, 8, 19, "div", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](6, AiChatComponent_div_6_Template, 9, 0, "div", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](7, AiChatComponent_div_7_Template, 56, 8, "div", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](8, AiChatComponent_div_8_Template, 2, 1, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](9, "div", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](10, AiChatComponent_div_10_Template, 20, 6, "div", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](11, "div", 11)(12, "div", 12)(13, "div", 13)(14, "span", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](15, "i", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](16, "input", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("ngModelChange", function AiChatComponent_Template_input_ngModelChange_16_listener($event) {
            return ctx.userQuestion = $event;
          })("keyup.enter", function AiChatComponent_Template_input_keyup_enter_16_listener() {
            return ctx.askQuestion();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](17, "button", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function AiChatComponent_Template_button_click_17_listener() {
            return ctx.askQuestion();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](18, "i", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()()()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngForOf", ctx.messages);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.isGenerating || ctx.isAskingQuestion);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.generatedContent);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.error);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx.generatedContent);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngModel", ctx.userQuestion)("disabled", ctx.isAskingQuestion);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("disabled", ctx.isAskingQuestion || !ctx.userQuestion.trim());
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngClass", ctx.isAskingQuestion ? "bi-hourglass-split spin" : "bi-send-fill");
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_5__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_5__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_5__.NgIf, _angular_common__WEBPACK_IMPORTED_MODULE_5__.NgStyle, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.NgModel],
      styles: [".ai-chat-container[_ngcontent-%COMP%] {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.card[_ngcontent-%COMP%] {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.card-body[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  flex: 1;\n  overflow: hidden;\n}\n\n.chat-messages[_ngcontent-%COMP%] {\n  flex: 1;\n  overflow-y: auto;\n  max-height: 500px; \n\n}\n\n.message[_ngcontent-%COMP%] {\n  display: flex;\n  margin-bottom: 15px;\n}\n\n.user-message[_ngcontent-%COMP%] {\n  justify-content: flex-end;\n}\n\n.assistant-message[_ngcontent-%COMP%] {\n  justify-content: flex-start;\n}\n\n\n\n.message-avatar[_ngcontent-%COMP%] {\n  width: 36px;\n  height: 36px;\n  font-size: 1rem;\n  color: white;\n  flex-shrink: 0;\n}\n\n\n\n.message-bubble[_ngcontent-%COMP%] {\n  max-width: 80%;\n  word-wrap: break-word;\n  position: relative;\n  transition: all 0.3s ease;\n}\n\n.message-bubble[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n}\n\n.user-bubble[_ngcontent-%COMP%] {\n  background: linear-gradient(45deg, #007bff, #6610f2);\n  color: white;\n  border-top-right-radius: 0 !important;\n}\n\n.assistant-bubble[_ngcontent-%COMP%] {\n  background-color: white;\n  color: #343a40;\n  border-top-left-radius: 0 !important;\n}\n\n\n\n.message-time[_ngcontent-%COMP%] {\n  font-size: 0.7rem;\n  opacity: 0.7;\n}\n\n.user-bubble[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%] {\n  color: rgba(255, 255, 255, 0.8) !important;\n}\n\n\n\n.typing-indicator[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n}\n\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\n  height: 8px;\n  width: 8px;\n  background-color: #343a40;\n  border-radius: 50%;\n  display: inline-block;\n  margin-right: 5px;\n  animation: _ngcontent-%COMP%_typing 1s infinite ease-in-out;\n}\n\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1) {\n  animation-delay: 0s;\n}\n\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2) {\n  animation-delay: 0.2s;\n}\n\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(3) {\n  animation-delay: 0.4s;\n  margin-right: 0;\n}\n\n@keyframes _ngcontent-%COMP%_typing {\n  0% {\n    transform: translateY(0px);\n    opacity: 0.4;\n  }\n  50% {\n    transform: translateY(-5px);\n    opacity: 0.8;\n  }\n  100% {\n    transform: translateY(0px);\n    opacity: 0.4;\n  }\n}\n\n\n\n.spin[_ngcontent-%COMP%] {\n  animation: _ngcontent-%COMP%_spin 1.5s infinite linear;\n}\n\n@keyframes _ngcontent-%COMP%_spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n.generated-content[_ngcontent-%COMP%] {\n  background-color: #f8f9fa;\n  border-radius: 0.25rem;\n  max-height: 800px; \n\n  overflow-y: auto;\n  width: 100%; \n\n}\n\n\n\n.generated-header[_ngcontent-%COMP%] {\n  border-left: 5px solid #007bff;\n}\n\n\n\n.generated-content[_ngcontent-%COMP%]   .module-card[_ngcontent-%COMP%] {\n  transition: all 0.3s ease;\n  border: none;\n  height: 100%;\n  border-radius: 16px;\n  overflow: hidden;\n  position: relative;\n}\n\n.generated-content[_ngcontent-%COMP%]   .module-card[_ngcontent-%COMP%]:hover {\n  transform: translateY(-8px);\n  box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;\n}\n\n\n\n.module-ribbon[_ngcontent-%COMP%] {\n  position: absolute;\n  top: 15px;\n  right: -35px;\n  transform: rotate(45deg);\n  width: 150px;\n  text-align: center;\n  padding: 5px;\n  font-size: 0.8rem;\n  font-weight: bold;\n  color: white;\n  z-index: 10;\n  box-shadow: 0 3px 10px rgba(0,0,0,0.1);\n}\n\n.generated-content[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\n  border-radius: 0;\n  font-weight: 600;\n  padding: 30px 15px;\n  text-align: center;\n}\n\n\n\n.module-icon-large[_ngcontent-%COMP%] {\n  width: 70px;\n  height: 70px;\n  font-size: 2rem;\n  margin: 0 auto;\n  position: relative;\n  z-index: 5;\n}\n\n\n\n.member-avatar[_ngcontent-%COMP%] {\n  width: 45px;\n  height: 45px;\n  font-size: 1.2rem;\n  flex-shrink: 0;\n}\n\n\n\n.assignation-badge[_ngcontent-%COMP%] {\n  box-shadow: 0 3px 10px rgba(0,0,0,0.05);\n  transition: all 0.3s ease;\n}\n\n.assignation-badge[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(0,0,0,0.08);\n}\n\n\n\n.description-box[_ngcontent-%COMP%] {\n  border-left: 4px solid #e9ecef;\n  font-style: italic;\n}\n\n\n\n.task-list[_ngcontent-%COMP%] {\n  max-height: 300px;\n  overflow-y: auto;\n  padding-right: 5px;\n  margin-bottom: 10px;\n}\n\n.task-header[_ngcontent-%COMP%] {\n  position: sticky;\n  top: 0;\n  background-color: white;\n  z-index: 5;\n}\n\n.task-item[_ngcontent-%COMP%] {\n  transition: all 0.3s ease;\n  border-left: 4px solid transparent;\n  background-color: white;\n}\n\n.task-item[_ngcontent-%COMP%]:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;\n}\n\n.high-priority[_ngcontent-%COMP%] {\n  border-left-color: #dc3545;\n}\n\n.medium-priority[_ngcontent-%COMP%] {\n  border-left-color: #ffc107;\n}\n\n.low-priority[_ngcontent-%COMP%] {\n  border-left-color: #17a2b8;\n}\n\n.task-title[_ngcontent-%COMP%] {\n  font-weight: 600;\n  color: #343a40;\n}\n\n.task-description[_ngcontent-%COMP%] {\n  padding-top: 8px;\n  border-top: 1px dashed #dee2e6;\n  margin-top: 5px;\n}\n\n\n\n@keyframes _ngcontent-%COMP%_fadeIn {\n  from { opacity: 0; transform: translateY(10px); }\n  to { opacity: 1; transform: translateY(0); }\n}\n\n\n\n.create-tasks-card[_ngcontent-%COMP%] {\n  background: linear-gradient(120deg, rgba(255,255,255,1), rgba(248,249,250,1));\n  border-left: 5px solid #28a745;\n}\n\n\n\n.step-circle[_ngcontent-%COMP%] {\n  width: 30px;\n  height: 30px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  flex-shrink: 0;\n}\n\n\n\n.create-button[_ngcontent-%COMP%] {\n  transition: all 0.3s ease;\n  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3) !important;\n}\n\n.create-button[_ngcontent-%COMP%]:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 10px 25px rgba(40, 167, 69, 0.4) !important;\n}\n\n.accordion-button[_ngcontent-%COMP%]:not(.collapsed) {\n  background-color: #e7f1ff;\n  color: #0d6efd;\n}\n\n.accordion-button[_ngcontent-%COMP%]:focus {\n  box-shadow: none;\n  border-color: rgba(0,0,0,.125);\n}\n\n.chat-input[_ngcontent-%COMP%] {\n  background-color: #fff;\n  border-top: 1px solid #dee2e6;\n}\n/*# sourceMappingURL=data:application/json;base64,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 */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 6334:
/*!******************************************************************************!*\
  !*** ./src/app/views/front/equipes/equipe-detail/equipe-detail.component.ts ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   EquipeDetailComponent: () => (/* binding */ EquipeDetailComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var src_app_services_equipe_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/services/equipe.service */ 8133);
/* harmony import */ var src_app_services_auth_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/services/auth.service */ 4796);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _ai_chat_ai_chat_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ai-chat/ai-chat.component */ 6014);







const _c0 = function (a0, a1, a2) {
  return {
    "bg-primary": a0,
    "bg-success": a1,
    "bg-secondary": a2
  };
};
const _c1 = function (a0, a1, a2) {
  return {
    "bi-mortarboard-fill": a0,
    "bi-briefcase-fill": a1,
    "bi-person-fill": a2
  };
};
const _c2 = function (a0, a1) {
  return {
    "bg-success bg-opacity-10 text-success": a0,
    "bg-primary bg-opacity-10 text-primary": a1
  };
};
const _c3 = function (a0, a1) {
  return {
    "bi-person-fill-gear": a0,
    "bi-person": a1
  };
};
function EquipeDetailComponent_div_0_div_122_div_4_Template(rf, ctx) {
  if (rf & 1) {
    const _r10 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 86)(1, "div", 87)(2, "div", 88)(3, "div", 89);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](4, "i", 90);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](5, "div")(6, "h6", 91);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](8, "div", 92)(9, "span", 93);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](10, "i", 90);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](11);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](12, "small", 94);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](13);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](14, "button", 95);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function EquipeDetailComponent_div_0_div_122_div_4_Template_button_click_14_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r10);
      const membre_r8 = restoredCtx.$implicit;
      const ctx_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](3);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r9.removeMembreFromEquipe(membre_r8._id));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](15, "i", 96);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const membre_r8 = ctx.$implicit;
    const ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpureFunction3"](7, _c0, ctx_r5.getUserProfession(membre_r8.user) === "etudiant", ctx_r5.getUserProfession(membre_r8.user) === "professeur", !ctx_r5.getUserProfession(membre_r8.user)));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpureFunction3"](11, _c1, ctx_r5.getUserProfession(membre_r8.user) === "etudiant", ctx_r5.getUserProfession(membre_r8.user) === "professeur", !ctx_r5.getUserProfession(membre_r8.user)));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", ctx_r5.getUserName(membre_r8.user), " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpureFunction2"](15, _c2, membre_r8.role === "admin", membre_r8.role === "membre"));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpureFunction2"](18, _c3, membre_r8.role === "admin", membre_r8.role === "membre"));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", membre_r8.role === "admin" ? "Administrateur" : "Membre", " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](ctx_r5.getUserProfession(membre_r8.user) === "etudiant" ? "\u00C9tudiant" : ctx_r5.getUserProfession(membre_r8.user) === "professeur" ? "Professeur" : "Utilisateur");
  }
}
function EquipeDetailComponent_div_0_div_122_div_9_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 97);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "i", 98);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](2, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](3, " Aucun utilisateur disponible. Veuillez d'abord cr\u00E9er des utilisateurs. ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
}
function EquipeDetailComponent_div_0_div_122_div_10_option_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "option", 120);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const user_r15 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("value", user_r15._id || user_r15.id);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate4"](" ", user_r15.firstName || "", " ", user_r15.lastName || user_r15.name || user_r15.id, " ", user_r15.email ? "- " + user_r15.email : "", " ", user_r15.profession ? "(" + (user_r15.profession === "etudiant" ? "\u00C9tudiant" : "Professeur") + ")" : "", " ");
  }
}
function EquipeDetailComponent_div_0_div_122_div_10_Template(rf, ctx) {
  if (rf & 1) {
    const _r17 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 99)(1, "div", 100)(2, "label", 101);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](3, "Utilisateur");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](4, "select", 102, 103)(6, "option", 104);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](7, " S\u00E9lectionnez un utilisateur ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](8, EquipeDetailComponent_div_0_div_122_div_10_option_8_Template, 2, 5, "option", 105);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](9, "div", 100)(10, "label", 106);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](11, "R\u00F4le dans l'\u00E9quipe");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](12, "div", 107)(13, "div", 108);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](14, "input", 109, 110);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](16, "label", 111);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](17, "i", 112);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](18, " Membre ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](19, "div", 108);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](20, "input", 113, 114);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](22, "label", 115);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](23, "i", 116);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](24, " Admin ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](25, "div", 117)(26, "button", 118);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function EquipeDetailComponent_div_0_div_122_div_10_Template_button_click_26_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r17);
      const _r11 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵreference"](5);
      const _r13 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵreference"](15);
      const ctx_r16 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](3);
      ctx_r16.addMembre(_r11.value, _r13.checked ? "membre" : "admin");
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](_r11.value = "");
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](27, "i", 119);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](28, " Ajouter \u00E0 l'\u00E9quipe ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const _r11 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵreference"](5);
    const ctx_r7 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngForOf", ctx_r7.availableUsers);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](18);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("disabled", !_r11.value);
  }
}
function EquipeDetailComponent_div_0_div_122_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 77)(1, "div", 43)(2, "div", 78)(3, "div", 79);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](4, EquipeDetailComponent_div_0_div_122_div_4_Template, 16, 21, "div", 80);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](5, "div", 81)(6, "h5", 82);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](7, "i", 83);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](8, " Ajouter un membre ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](9, EquipeDetailComponent_div_0_div_122_div_9_Template, 4, 0, "div", 84);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](10, EquipeDetailComponent_div_0_div_122_div_10_Template, 29, 2, "div", 85);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngForOf", ctx_r2.teamMembers);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx_r2.availableUsers.length === 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx_r2.availableUsers.length > 0);
  }
}
function EquipeDetailComponent_div_0_ng_template_123_div_13_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 97);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "i", 98);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](2, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](3, " Aucun utilisateur disponible. Veuillez d'abord cr\u00E9er des utilisateurs. ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
}
function EquipeDetailComponent_div_0_ng_template_123_div_14_option_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "option", 120);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const user_r24 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("value", user_r24._id || user_r24.id);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate4"](" ", user_r24.firstName || "", " ", user_r24.lastName || user_r24.name || user_r24.id, " ", user_r24.email ? "- " + user_r24.email : "", " ", user_r24.profession ? "(" + (user_r24.profession === "etudiant" ? "\u00C9tudiant" : "Professeur") + ")" : "", " ");
  }
}
function EquipeDetailComponent_div_0_ng_template_123_div_14_Template(rf, ctx) {
  if (rf & 1) {
    const _r26 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 99)(1, "div", 100)(2, "label", 124);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](3, "Utilisateur");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](4, "select", 125, 126)(6, "option", 104);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](7, " S\u00E9lectionnez un utilisateur ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](8, EquipeDetailComponent_div_0_ng_template_123_div_14_option_8_Template, 2, 5, "option", 105);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](9, "div", 100)(10, "label", 127);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](11, "R\u00F4le dans l'\u00E9quipe");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](12, "div", 107)(13, "div", 108);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](14, "input", 128, 129);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](16, "label", 130);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](17, "i", 112);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](18, " Membre ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](19, "div", 108);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](20, "input", 131, 132);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](22, "label", 133);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](23, "i", 116);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](24, " Admin ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](25, "div", 117)(26, "button", 118);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function EquipeDetailComponent_div_0_ng_template_123_div_14_Template_button_click_26_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r26);
      const _r20 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵreference"](5);
      const _r22 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵreference"](15);
      const ctx_r25 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](3);
      ctx_r25.addMembre(_r20.value, _r22.checked ? "membre" : "admin");
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](_r20.value = "");
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](27, "i", 119);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](28, " Ajouter \u00E0 l'\u00E9quipe ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const _r20 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵreference"](5);
    const ctx_r19 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngForOf", ctx_r19.availableUsers);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](18);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("disabled", !_r20.value);
  }
}
function EquipeDetailComponent_div_0_ng_template_123_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 43)(1, "div", 78)(2, "div", 121)(3, "div", 122);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](4, "i", 123);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](5, "h5", 94);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](6, "Aucun membre dans cette \u00E9quipe");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](7, "p", 94);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](8, " Ajoutez des membres \u00E0 l'\u00E9quipe en utilisant le formulaire ci-contre. ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](9, "div", 81)(10, "h5", 82);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](11, "i", 83);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](12, " Ajouter un membre ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](13, EquipeDetailComponent_div_0_ng_template_123_div_13_Template, 4, 0, "div", 84);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](14, EquipeDetailComponent_div_0_ng_template_123_div_14_Template, 29, 2, "div", 85);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](13);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx_r4.availableUsers.length === 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx_r4.availableUsers.length > 0);
  }
}
function EquipeDetailComponent_div_0_Template(rf, ctx) {
  if (rf & 1) {
    const _r28 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 2)(1, "div", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](2, "div", 4)(3, "div", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](4, "div", 6)(5, "div", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](6, "div", 8)(7, "div", 8)(8, "div", 8)(9, "div", 8)(10, "div", 8)(11, "div", 8)(12, "div", 8)(13, "div", 8)(14, "div", 8)(15, "div", 8)(16, "div", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](17, "div", 9)(18, "div", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](19, "div", 11)(20, "div", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](21, "div", 13)(22, "div", 14)(23, "div", 15)(24, "h1", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](25);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](26, "p", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](27, " Gestion et collaboration d'\u00E9quipe moderne ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](28, "div", 18)(29, "div", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](30, "i", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](31, "span", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](32);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](33, "small", 22);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](34, "Membres");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](35, "div", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](36, "i", 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](37, "span", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](38, "0");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](39, "small", 22);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](40, "T\u00E2ches");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](41, "div", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](42, "i", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](43, "span", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](44);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](45, "small", 22);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](46, "Cr\u00E9\u00E9e le");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](47, "div", 26)(48, "h4", 27);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](49, "i", 28);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](50, "Actions rapides ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](51, "div", 29)(52, "button", 30);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function EquipeDetailComponent_div_0_Template_button_click_52_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r28);
      const ctx_r27 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r27.navigateToTasks());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](53, "i", 31);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](54, " G\u00E9rer les t\u00E2ches ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](55, "button", 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function EquipeDetailComponent_div_0_Template_button_click_55_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r28);
      const ctx_r29 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r29.navigateToEditEquipe());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](56, "i", 33);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](57, " Modifier l'\u00E9quipe ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](58, "div", 34)(59, "button", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function EquipeDetailComponent_div_0_Template_button_click_59_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r28);
      const ctx_r30 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r30.navigateToEquipeList());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](60, "i", 36);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](61, " Retour ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](62, "button", 37);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function EquipeDetailComponent_div_0_Template_button_click_62_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r28);
      const ctx_r31 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r31.deleteEquipe());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](63, "i", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](64, " Supprimer ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](65, "div", 39)(66, "div", 40)(67, "div", 41)(68, "div", 42)(69, "div", 43)(70, "div", 44)(71, "div", 45);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](72, "i", 46);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](73, "h3", 47);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](74, "\u00C0 propos");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](75, "p", 48);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](76, " D\u00E9tails et informations sur l'\u00E9quipe ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](77, "div", 49)(78, "div", 50)(79, "h4", 51);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](80, "Description");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](81, "span", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](82, "i", 53);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](83);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](84, "div", 54)(85, "p", 55);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](86);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](87, "div", 56)(88, "span", 57);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](89, "i", 58);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](90);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](91, "span", 59);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](92, "i", 60);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](93);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](94, "span", 61);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](95, "i", 62);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](96, " Gestion de projet ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()()()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](97, "div", 39)(98, "div", 40)(99, "div", 41)(100, "div", 63)(101, "div", 64)(102, "h3", 65)(103, "div", 66);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](104, "i", 67);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](105, " Assistant IA Gemini ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](106, "span", 68);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](107, "i", 69);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](108, " G\u00E9n\u00E9ration de t\u00E2ches intelligente ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](109, "div", 42);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](110, "app-ai-chat", 70);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](111, "div", 39)(112, "div", 40)(113, "div", 41)(114, "div", 71)(115, "h3", 65)(116, "div", 72);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](117, "i", 73);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](118, " Membres de l'\u00E9quipe ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](119, "span", 74);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](120);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](121, "div", 42);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](122, EquipeDetailComponent_div_0_div_122_Template, 11, 3, "div", 75);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](123, EquipeDetailComponent_div_0_ng_template_123_Template, 15, 2, "ng-template", null, 76, _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplateRefExtractor"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()()()();
  }
  if (rf & 2) {
    const _r3 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵreference"](124);
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](25);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", ctx_r0.equipe.name, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"]((ctx_r0.equipe.members == null ? null : ctx_r0.equipe.members.length) || 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](12);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](ctx_r0.formatDate(ctx_r0.equipe.createdAt));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](39);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" Admin: ", ctx_r0.equipe.admin ? ctx_r0.getUserName(ctx_r0.equipe.admin) || ctx_r0.equipe.admin : "Non d\u00E9fini", " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", ctx_r0.equipe.description || "Aucune description disponible pour cette \u00E9quipe.", " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", (ctx_r0.equipe.members == null ? null : ctx_r0.equipe.members.length) || 0, " membres ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" Cr\u00E9\u00E9e le ", ctx_r0.formatDate(ctx_r0.equipe.createdAt), " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](17);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("team", ctx_r0.equipe);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](10);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", ctx_r0.teamMembers.length || 0, " membres ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx_r0.teamMembers && ctx_r0.teamMembers.length > 0)("ngIfElse", _r3);
  }
}
function EquipeDetailComponent_div_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r33 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 134)(1, "div", 135)(2, "div", 136)(3, "div", 137)(4, "div", 138);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](5, "i", 139);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](6, "div", 140);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](7, " \u00C9quipe non trouv\u00E9e ou en cours de chargement... ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](8, "button", 141);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function EquipeDetailComponent_div_1_Template_button_click_8_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r33);
      const ctx_r32 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r32.navigateToEquipeList());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](9, "i", 142);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](10, " Retour \u00E0 la liste des \u00E9quipes ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()()();
  }
}
class EquipeDetailComponent {
  constructor(equipeService, userService,
  // TODO: Will be used when implementing real user API calls
  route, router) {
    this.equipeService = equipeService;
    this.userService = userService;
    this.route = route;
    this.router = router;
    this.equipe = null;
    this.loading = false;
    this.error = null;
    this.equipeId = null;
    this.newMembre = {
      id: '',
      role: 'membre'
    };
    this.availableUsers = [];
    this.memberNames = {}; // Map pour stocker les noms des membres
    this.teamMembers = []; // Liste des membres de l'équipe avec leurs détails
  }

  ngOnInit() {
    this.equipeId = this.route.snapshot.paramMap.get('id');
    // Charger tous les utilisateurs disponibles
    this.loadUsers();
    if (this.equipeId) {
      this.loadEquipe(this.equipeId);
    } else {
      this.error = "ID d'équipe non spécifié";
    }
  }
  // Méthode pour charger tous les utilisateurs
  loadUsers() {
    // TODO: Implémenter l'API pour récupérer les utilisateurs
    // Pour l'instant, utiliser des données mockées
    const mockUsers = [{
      _id: 'user1',
      username: 'john_doe',
      email: '<EMAIL>',
      role: 'admin',
      isActive: true
    }, {
      _id: 'user2',
      username: 'jane_smith',
      email: '<EMAIL>',
      role: 'student',
      isActive: true
    }, {
      _id: 'user3',
      username: 'bob_wilson',
      email: '<EMAIL>',
      role: 'teacher',
      isActive: true
    }];
    // Simuler un délai d'API
    setTimeout(() => {
      // Stocker tous les utilisateurs pour la recherche de noms
      const allUsers = [...mockUsers];
      console.log('Tous les utilisateurs chargés (mock):', allUsers);
      // Filtrer les utilisateurs disponibles (non membres de l'équipe)
      if (this.teamMembers && this.teamMembers.length > 0) {
        const memberUserIds = this.teamMembers.map(m => m.user);
        this.availableUsers = mockUsers.filter(user => !memberUserIds.includes(user._id || user.id || ''));
      } else {
        this.availableUsers = mockUsers;
      }
      console.log('Utilisateurs disponibles:', this.availableUsers);
      // Si l'équipe est déjà chargée, mettre à jour les noms des membres
      if (this.equipe && this.equipe.members) {
        this.updateMemberNames();
      }
    }, 500);
  }
  // Méthode pour mettre à jour les noms des membres
  updateMemberNames() {
    if (!this.equipe || !this.equipe.members) return;
    this.equipe.members.forEach(membreId => {
      const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);
      if (user && user.name) {
        this.memberNames[membreId] = user.name;
      } else {
        // Si l'utilisateur n'est pas trouvé dans la liste, essayer de le récupérer individuellement
        // TODO: Implémenter getUser dans AuthuserService
        // Pour l'instant, utiliser l'ID comme nom par défaut
        this.memberNames[membreId] = membreId;
      }
    });
  }
  // Méthode pour obtenir le nom d'un membre
  getMembreName(membreId) {
    return this.memberNames[membreId] || membreId;
  }
  // Méthode pour obtenir le nom d'un utilisateur à partir de son ID
  getUserName(userId) {
    if (!userId) {
      return 'Non défini';
    }
    const user = this.availableUsers.find(u => u._id === userId || u.id === userId);
    if (user) {
      if (user.firstName && user.lastName) {
        return `${user.firstName} ${user.lastName}`;
      } else if (user.name) {
        return user.name;
      }
    }
    return userId;
  }
  // Méthode pour obtenir la profession d'un utilisateur à partir de son ID
  getUserProfession(userId) {
    if (!userId) {
      return '';
    }
    const user = this.availableUsers.find(u => u._id === userId || u.id === userId);
    if (user) {
      return user.profession || user.role || '';
    }
    return '';
  }
  loadEquipe(id) {
    this.loading = true;
    this.error = null;
    this.equipeService.getEquipe(id).subscribe({
      next: data => {
        console.log("Détails de l'équipe chargés:", data);
        this.equipe = data;
        // Charger les détails des membres de l'équipe
        this.loadTeamMembers(id);
        // Mettre à jour les noms des membres
        if (this.equipe && this.equipe.members && this.equipe.members.length > 0) {
          this.updateMemberNames();
        }
        this.loading = false;
      },
      error: error => {
        console.error("Erreur lors du chargement des détails de l'équipe:", error);
        this.error = "Impossible de charger les détails de l'équipe. Veuillez réessayer plus tard.";
        this.loading = false;
      }
    });
  }
  // Méthode pour charger les détails des membres de l'équipe
  loadTeamMembers(teamId) {
    this.equipeService.getTeamMembers(teamId).subscribe({
      next: members => {
        console.log('Détails des membres chargés:', members);
        this.teamMembers = members;
      },
      error: error => {
        console.error('Erreur lors du chargement des détails des membres:', error);
      }
    });
  }
  navigateToEditEquipe() {
    if (this.equipeId) {
      this.router.navigate(['/equipes/modifier', this.equipeId]);
    }
  }
  navigateToEquipeList() {
    this.router.navigate(['/equipes/liste']);
  }
  navigateToTasks() {
    if (this.equipeId) {
      this.router.navigate(['/equipes/tasks', this.equipeId]);
    }
  }
  // Méthode pour formater les dates
  formatDate(date) {
    if (!date) {
      return 'N/A';
    }
    try {
      let dateObj;
      if (typeof date === 'string') {
        dateObj = new Date(date);
      } else {
        dateObj = date;
      }
      if (isNaN(dateObj.getTime())) {
        return 'Date invalide';
      }
      // Format: JJ/MM/AAAA
      return dateObj.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    } catch (error) {
      console.error('Erreur lors du formatage de la date:', error);
      return 'Erreur de date';
    }
  }
  // Méthode pour ajouter un membre à l'équipe
  addMembre(userId, role) {
    console.log(`Ajout de l'utilisateur ${userId} avec le rôle ${role}`);
    if (!this.equipeId || !userId) {
      console.error("ID d'équipe ou ID d'utilisateur manquant");
      this.error = "ID d'équipe ou ID d'utilisateur manquant";
      return;
    }
    // Vérifier si l'utilisateur est déjà membre de l'équipe
    const isAlreadyMember = this.teamMembers.some(m => m.user === userId);
    if (isAlreadyMember) {
      this.error = "Cet utilisateur est déjà membre de l'équipe";
      alert("Cet utilisateur est déjà membre de l'équipe");
      return;
    }
    // Créer l'objet membre avec le rôle spécifié
    const membre = {
      id: userId,
      role: role || 'membre'
    };
    // Récupérer les informations de l'utilisateur pour afficher un message plus informatif
    const userName = this.getUserName(userId);
    const roleName = role === 'admin' ? 'administrateur' : 'membre';
    this.equipeService.addMembreToEquipe(this.equipeId, membre).subscribe({
      next: response => {
        console.log(`Utilisateur "${userName}" ajouté comme ${roleName} avec succès:`, response);
        // Afficher un message de succès
        alert(`Utilisateur "${userName}" ajouté comme ${roleName} avec succès`);
        // Recharger les membres de l'équipe
        this.loadTeamMembers(this.equipeId);
        // Recharger l'équipe pour mettre à jour la liste des membres
        this.loadEquipe(this.equipeId);
        // Mettre à jour la liste des utilisateurs disponibles
        this.updateAvailableUsers();
      },
      error: error => {
        console.error("Erreur lors de l'ajout de l'utilisateur comme membre:", error);
        this.error = `Impossible d'ajouter l'utilisateur "${userName}" comme ${roleName}. Veuillez réessayer plus tard.`;
        alert(this.error);
      }
    });
  }
  // Méthode pour mettre à jour la liste des utilisateurs disponibles
  updateAvailableUsers() {
    // TODO: Implémenter l'API pour récupérer les utilisateurs
    // Pour l'instant, utiliser les données mockées de loadUsers()
    this.loadUsers();
  }
  // Ancienne méthode maintenue pour compatibilité
  addMembreToEquipe() {
    if (!this.equipeId || !this.newMembre.id) {
      console.error("ID d'équipe ou ID de membre manquant");
      return;
    }
    this.addMembre(this.newMembre.id, this.newMembre.role || 'membre');
  }
  removeMembreFromEquipe(membreId) {
    console.log('Méthode removeMembreFromEquipe appelée avec ID:', membreId);
    if (!this.equipeId) {
      console.error("ID d'équipe manquant");
      this.error = "ID d'équipe manquant. Impossible de retirer le membre.";
      return;
    }
    // Dans notre implémentation actuelle, membre._id est l'ID de l'utilisateur
    const userId = membreId;
    // Récupérer le nom de l'utilisateur pour un message plus informatif
    const userName = this.getUserName(userId);
    console.log(`Tentative de retrait de l'utilisateur ${userId} (${userName}) de l'équipe ${this.equipeId}`);
    if (confirm(`Êtes-vous sûr de vouloir retirer l'utilisateur "${userName}" de l'équipe?`)) {
      console.log('Confirmation acceptée, suppression en cours...');
      this.loading = true;
      this.error = null;
      this.equipeService.removeMembreFromEquipe(this.equipeId, userId).subscribe({
        next: response => {
          console.log(`Utilisateur "${userName}" retiré avec succès de l'équipe:`, response);
          this.loading = false;
          // Afficher un message de succès
          alert(`Utilisateur "${userName}" retiré avec succès de l'équipe`);
          // Recharger les membres de l'équipe
          this.loadTeamMembers(this.equipeId);
          // Recharger l'équipe pour mettre à jour la liste des membres
          this.loadEquipe(this.equipeId);
          // Mettre à jour la liste des utilisateurs disponibles
          this.updateAvailableUsers();
        },
        error: error => {
          console.error(`Erreur lors du retrait de l'utilisateur "${userName}":`, error);
          this.loading = false;
          this.error = `Impossible de retirer l'utilisateur "${userName}" de l'équipe: ${error.message || 'Erreur inconnue'}`;
        }
      });
    } else {
      console.log("Suppression annulée par l'utilisateur");
    }
  }
  deleteEquipe() {
    console.log('Méthode deleteEquipe appelée');
    if (!this.equipeId) {
      console.error("ID d'équipe manquant");
      this.error = "ID d'équipe manquant. Impossible de supprimer l'équipe.";
      return;
    }
    console.log("ID de l'équipe à supprimer:", this.equipeId);
    if (confirm(`Êtes-vous sûr de vouloir supprimer l'équipe "${this.equipe?.name}"? Cette action est irréversible.`)) {
      console.log('Confirmation acceptée, suppression en cours...');
      this.loading = true;
      this.error = null;
      this.equipeService.deleteEquipe(this.equipeId).subscribe({
        next: () => {
          console.log('Équipe supprimée avec succès');
          this.loading = false;
          alert('Équipe supprimée avec succès');
          this.router.navigate(['/equipes/liste']);
        },
        error: error => {
          console.error("Erreur lors de la suppression de l'équipe:", error);
          this.loading = false;
          this.error = `Impossible de supprimer l'équipe: ${error.message || 'Erreur inconnue'}`;
          alert(`Erreur lors de la suppression: ${this.error}`);
        }
      });
    } else {
      console.log("Suppression annulée par l'utilisateur");
    }
  }
  static {
    this.ɵfac = function EquipeDetailComponent_Factory(t) {
      return new (t || EquipeDetailComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](src_app_services_equipe_service__WEBPACK_IMPORTED_MODULE_0__.EquipeService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](src_app_services_auth_service__WEBPACK_IMPORTED_MODULE_1__.AuthService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_4__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_4__.Router));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineComponent"]({
      type: EquipeDetailComponent,
      selectors: [["app-equipe-detail"]],
      decls: 2,
      vars: 2,
      consts: [["class", "min-h-screen bg-[#f0f4f8] dark:bg-[#0a0a0a] relative overflow-hidden", 4, "ngIf"], ["class", "container-fluid py-5 bg-light", 4, "ngIf"], [1, "min-h-screen", "bg-[#f0f4f8]", "dark:bg-[#0a0a0a]", "relative", "overflow-hidden"], [1, "absolute", "inset-0", "overflow-hidden", "pointer-events-none"], [1, "absolute", "top-[15%]", "left-[10%]", "w-64", "h-64", "rounded-full", "bg-gradient-to-br", "from-[#4f5fad]/5", "to-transparent", "dark:from-[#00f7ff]/3", "dark:to-transparent", "blur-3xl"], [1, "absolute", "bottom-[20%]", "right-[10%]", "w-80", "h-80", "rounded-full", "bg-gradient-to-tl", "from-[#4f5fad]/5", "to-transparent", "dark:from-[#00f7ff]/3", "dark:to-transparent", "blur-3xl"], [1, "absolute", "inset-0", "opacity-5", "dark:opacity-[0.03]"], [1, "h-full", "grid", "grid-cols-12"], [1, "border-r", "border-[#4f5fad]", "dark:border-[#00f7ff]"], [1, "max-w-7xl", "mx-auto", "p-6", "relative", "z-10"], [1, "mb-8", "relative"], [1, "absolute", "top-0", "left-0", "right-0", "h-1", "bg-gradient-to-r", "from-[#4f5fad]", "to-[#7826b5]", "dark:from-[#00f7ff]", "dark:to-[#4f5fad]"], [1, "absolute", "top-0", "left-0", "right-0", "h-1", "bg-gradient-to-r", "from-[#4f5fad]", "to-[#7826b5]", "dark:from-[#00f7ff]", "dark:to-[#4f5fad]", "blur-md"], [1, "bg-white", "dark:bg-[#1a1a1a]", "rounded-xl", "shadow-lg", "dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]", "overflow-hidden", "border", "border-[#4f5fad]/20", "dark:border-[#00f7ff]/20"], [1, "grid", "grid-cols-1", "lg:grid-cols-3", "gap-0"], [1, "lg:col-span-2", "bg-gradient-to-r", "from-[#4f5fad]", "to-[#7826b5]", "dark:from-[#00f7ff]", "dark:to-[#4f5fad]", "p-6"], [1, "text-3xl", "font-bold", "text-white", "mb-2", "tracking-wide"], [1, "text-white/80", "text-sm", "mb-6"], [1, "grid", "grid-cols-3", "gap-4"], [1, "bg-white/20", "backdrop-blur-sm", "rounded-xl", "p-4", "text-white", "text-center"], [1, "fas", "fa-users", "text-2xl", "mb-2", "block"], [1, "text-xl", "font-bold", "block"], [1, "text-white/80"], [1, "fas", "fa-tasks", "text-2xl", "mb-2", "block"], [1, "fas", "fa-calendar-check", "text-2xl", "mb-2", "block"], [1, "text-sm", "font-bold", "block"], [1, "bg-white", "dark:bg-[#1a1a1a]", "p-6", "flex", "flex-col", "justify-center"], [1, "text-lg", "font-bold", "text-[#4f5fad]", "dark:text-[#00f7ff]", "mb-4", "flex", "items-center"], [1, "fas", "fa-bolt", "mr-2"], [1, "space-y-3"], [1, "w-full", "bg-gradient-to-r", "from-[#4f5fad]", "to-[#7826b5]", "dark:from-[#00f7ff]", "dark:to-[#4f5fad]", "text-white", "px-4", "py-3", "rounded-xl", "font-medium", "transition-all", "duration-300", "hover:scale-105", "shadow-lg", 3, "click"], [1, "fas", "fa-tasks", "mr-2"], [1, "w-full", "bg-[#4f5fad]/20", "dark:bg-[#00f7ff]/20", "text-[#4f5fad]", "dark:text-[#00f7ff]", "px-4", "py-3", "rounded-xl", "font-medium", "transition-all", "duration-300", "hover:scale-105", 3, "click"], [1, "fas", "fa-edit", "mr-2"], [1, "grid", "grid-cols-2", "gap-2"], [1, "bg-[#6d6870]/20", "dark:bg-[#a0a0a0]/20", "text-[#6d6870]", "dark:text-[#e0e0e0]", "px-4", "py-2", "rounded-xl", "font-medium", "transition-all", "duration-300", "hover:scale-105", 3, "click"], [1, "fas", "fa-arrow-left", "mr-1"], [1, "bg-[#ff6b69]/20", "dark:bg-[#ff3b30]/20", "text-[#ff6b69]", "dark:text-[#ff3b30]", "px-4", "py-2", "rounded-xl", "font-medium", "transition-all", "duration-300", "hover:scale-105", 3, "click"], [1, "fas", "fa-trash", "mr-1"], [1, "row", "mb-5"], [1, "col-12"], [1, "card", "border-0", "shadow-sm", "rounded-4", "overflow-hidden", "hover-card"], [1, "card-body", "p-0"], [1, "row", "g-0"], [1, "col-md-3", "bg-primary", "text-white", "p-4", "d-flex", "flex-column", "justify-content-center", "align-items-center", "text-center"], [1, "icon-circle", "bg-white", "text-primary", "mb-3"], [1, "bi", "bi-info-circle-fill", "fs-1"], [1, "mb-2"], [1, "mb-0", "text-white-50"], [1, "col-md-9", "p-4"], [1, "d-flex", "justify-content-between", "align-items-center", "mb-4"], [1, "text-primary", "mb-0"], [1, "badge", "bg-light", "text-primary", "rounded-pill", "px-3", "py-2"], [1, "bi", "bi-person-fill-gear", "me-1"], [1, "description-box", "p-3", "bg-light", "rounded-4", "mb-4"], [1, "lead", "mb-0"], [1, "d-flex", "flex-wrap", "gap-2", "mt-4"], [1, "badge", "bg-primary", "bg-opacity-10", "text-primary", "rounded-pill", "px-3", "py-2"], [1, "bi", "bi-people-fill", "me-1"], [1, "badge", "bg-success", "bg-opacity-10", "text-success", "rounded-pill", "px-3", "py-2"], [1, "bi", "bi-calendar-check", "me-1"], [1, "badge", "bg-info", "bg-opacity-10", "text-info", "rounded-pill", "px-3", "py-2"], [1, "bi", "bi-kanban", "me-1"], [1, "card-header", "border-0", "py-4", 2, "background", "linear-gradient(45deg, #8e2de2, #4a00e0)"], [1, "d-flex", "justify-content-between", "align-items-center"], [1, "mb-0", "text-white", "d-flex", "align-items-center"], [1, "icon-circle", "bg-white", "text-primary", "me-3"], [1, "bi", "bi-robot"], [1, "badge", "bg-white", "text-primary", "rounded-pill", "px-3", "py-2"], [1, "bi", "bi-magic", "me-1"], [3, "team"], [1, "card-header", "border-0", "py-4", "d-flex", "justify-content-between", "align-items-center", 2, "background", "linear-gradient(45deg, #11998e, #38ef7d)"], [1, "icon-circle", "bg-white", "text-success", "me-3"], [1, "bi", "bi-people-fill"], [1, "badge", "bg-white", "text-success", "rounded-pill", "px-3", "py-2"], ["class", "p-0", 4, "ngIf", "ngIfElse"], ["noMembers", ""], [1, "p-0"], [1, "col-md-8"], [1, "member-grid", "p-4"], ["class", "member-card mb-3 p-3 rounded-4 shadow-sm transition", 4, "ngFor", "ngForOf"], [1, "col-md-4", "bg-light", "p-4"], [1, "d-flex", "align-items-center", "mb-4", "text-success"], [1, "bi", "bi-person-plus-fill", "me-2"], ["class", "alert alert-info border-0 rounded-4 shadow-sm d-flex align-items-center", 4, "ngIf"], ["class", "add-member-form", 4, "ngIf"], [1, "member-card", "mb-3", "p-3", "rounded-4", "shadow-sm", "transition"], [1, "d-flex", "justify-content-between", "align-items-start"], [1, "d-flex", "align-items-center"], [1, "member-avatar", "rounded-circle", "text-white", "me-3", 3, "ngClass"], [1, "bi", 3, "ngClass"], [1, "mb-0", "fw-bold"], [1, "d-flex", "align-items-center", "mt-1"], [1, "badge", "rounded-pill", "me-2", 3, "ngClass"], [1, "text-muted"], ["title", "Retirer de l'\u00E9quipe", 1, "btn", "btn-sm", "btn-outline-danger", "rounded-circle", 3, "click"], [1, "bi", "bi-trash"], [1, "alert", "alert-info", "border-0", "rounded-4", "shadow-sm", "d-flex", "align-items-center"], [1, "bi", "bi-info-circle-fill", "fs-4", "me-3", "text-primary"], [1, "add-member-form"], [1, "mb-3"], ["for", "userSelect", 1, "form-label", "fw-medium"], ["id", "userSelect", 1, "form-select", "border-0", "shadow-sm", "rounded-4", "py-2"], ["userSelect", ""], ["value", "", "selected", "", "disabled", ""], [3, "value", 4, "ngFor", "ngForOf"], ["for", "roleSelect", 1, "form-label", "fw-medium"], [1, "d-flex", "gap-2"], [1, "form-check", "flex-grow-1"], ["type", "radio", "name", "roleRadio", "id", "roleMembre", "value", "membre", "checked", "", 1, "form-check-input"], ["roleMembre", ""], ["for", "roleMembre", 1, "form-check-label", "w-100", "p-2", "border", "rounded-4", "text-center"], [1, "bi", "bi-person", "d-block", "fs-4", "mb-1"], ["type", "radio", "name", "roleRadio", "id", "roleAdmin", "value", "admin", 1, "form-check-input"], ["roleAdmin", ""], ["for", "roleAdmin", 1, "form-check-label", "w-100", "p-2", "border", "rounded-4", "text-center"], [1, "bi", "bi-person-fill-gear", "d-block", "fs-4", "mb-1"], [1, "d-grid"], ["type", "button", 1, "btn", "btn-success", "rounded-4", "py-2", "shadow-sm", 3, "disabled", "click"], [1, "bi", "bi-plus-circle", "me-2"], [3, "value"], [1, "text-center", "py-5"], [1, "empty-state-icon", "mb-4"], [1, "bi", "bi-people", "fs-1", "text-muted"], ["for", "userSelect2", 1, "form-label", "fw-medium"], ["id", "userSelect2", 1, "form-select", "border-0", "shadow-sm", "rounded-4", "py-2"], ["userSelect2", ""], ["for", "roleSelect2", 1, "form-label", "fw-medium"], ["type", "radio", "name", "roleRadio2", "id", "roleMembre2", "value", "membre", "checked", "", 1, "form-check-input"], ["roleMembre2", ""], ["for", "roleMembre2", 1, "form-check-label", "w-100", "p-2", "border", "rounded-4", "text-center"], ["type", "radio", "name", "roleRadio2", "id", "roleAdmin2", "value", "admin", 1, "form-check-input"], ["roleAdmin2", ""], ["for", "roleAdmin2", 1, "form-check-label", "w-100", "p-2", "border", "rounded-4", "text-center"], [1, "container-fluid", "py-5", "bg-light"], [1, "container"], [1, "row", "justify-content-center"], [1, "col-md-8", "text-center"], [1, "alert", "alert-warning", "shadow-sm", "border-0", "rounded-3", "d-flex", "align-items-center", "p-4"], [1, "bi", "bi-exclamation-triangle-fill", "fs-1", "me-4", "text-warning"], [1, "fs-5"], [1, "btn", "btn-outline-primary", "rounded-pill", "mt-4", 3, "click"], [1, "bi", "bi-arrow-left", "me-2"]],
      template: function EquipeDetailComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](0, EquipeDetailComponent_div_0_Template, 125, 11, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](1, EquipeDetailComponent_div_1_Template, 11, 0, "div", 1);
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.equipe);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx.equipe);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_5__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_5__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_5__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.NgSelectOption, _angular_forms__WEBPACK_IMPORTED_MODULE_6__["ɵNgSelectMultipleOption"], _ai_chat_ai_chat_component__WEBPACK_IMPORTED_MODULE_2__.AiChatComponent],
      styles: ["\n\n.cursor-pointer[_ngcontent-%COMP%] {\n  cursor: pointer;\n}\n\nsummary[_ngcontent-%COMP%]:hover {\n  text-decoration: underline;\n}\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImVxdWlwZS1kZXRhaWwuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSwyQ0FBMkM7QUFDM0M7RUFDRSxlQUFlO0FBQ2pCOztBQUVBO0VBQ0UsMEJBQTBCO0FBQzVCIiwiZmlsZSI6ImVxdWlwZS1kZXRhaWwuY29tcG9uZW50LmNzcyIsInNvdXJjZXNDb250ZW50IjpbIi8qIFN0eWxlcyBwb3VyIGxlIGNvbXBvc2FudCBlcXVpcGUtZGV0YWlsICovXHJcbi5jdXJzb3ItcG9pbnRlciB7XHJcbiAgY3Vyc29yOiBwb2ludGVyO1xyXG59XHJcblxyXG5zdW1tYXJ5OmhvdmVyIHtcclxuICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTtcclxufSJdfQ== */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvZXF1aXBlcy9lcXVpcGUtZGV0YWlsL2VxdWlwZS1kZXRhaWwuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSwyQ0FBMkM7QUFDM0M7RUFDRSxlQUFlO0FBQ2pCOztBQUVBO0VBQ0UsMEJBQTBCO0FBQzVCO0FBQ0Esd2dCQUF3Z0IiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBTdHlsZXMgcG91ciBsZSBjb21wb3NhbnQgZXF1aXBlLWRldGFpbCAqL1xyXG4uY3Vyc29yLXBvaW50ZXIge1xyXG4gIGN1cnNvcjogcG9pbnRlcjtcclxufVxyXG5cclxuc3VtbWFyeTpob3ZlciB7XHJcbiAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */", "\n\n  .bg-gradient-primary[_ngcontent-%COMP%] {\n    background: linear-gradient(45deg, #007bff, #6610f2) !important;\n  }\n\n  .bg-gradient-light[_ngcontent-%COMP%] {\n    background: linear-gradient(to right, #f8f9fa, #e9ecef) !important;\n  }\n\n  \n\n  .transition[_ngcontent-%COMP%] {\n    transition: all 0.3s ease;\n  }\n\n  \n\n  .hover-card[_ngcontent-%COMP%] {\n    transition: all 0.3s ease;\n  }\n\n  .hover-card[_ngcontent-%COMP%]:hover {\n    transform: translateY(-5px);\n    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;\n  }\n\n  \n\n  .member-card[_ngcontent-%COMP%] {\n    background-color: white;\n    transition: all 0.3s ease;\n    border-left: 4px solid transparent;\n  }\n\n  .member-card[_ngcontent-%COMP%]:hover {\n    transform: translateY(-3px);\n    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1) !important;\n  }\n\n  \n\n  .member-avatar[_ngcontent-%COMP%] {\n    width: 45px;\n    height: 45px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-size: 1.2rem;\n  }\n\n  \n\n  .icon-circle[_ngcontent-%COMP%] {\n    width: 40px;\n    height: 40px;\n    border-radius: 50%;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-size: 1.2rem;\n  }\n\n  \n\n  .description-box[_ngcontent-%COMP%] {\n    border-left: 4px solid #007bff;\n  }\n\n  \n\n  .btn[_ngcontent-%COMP%] {\n    transition: all 0.3s ease;\n  }\n\n  .btn[_ngcontent-%COMP%]:hover {\n    transform: translateY(-2px);\n  }\n\n  \n\n  .badge[_ngcontent-%COMP%] {\n    font-weight: 500;\n    letter-spacing: 0.5px;\n  }\n\n  \n\n  .form-select[_ngcontent-%COMP%], .form-control[_ngcontent-%COMP%] {\n    transition: all 0.2s ease;\n  }\n\n  .form-select[_ngcontent-%COMP%]:focus, .form-control[_ngcontent-%COMP%]:focus {\n    border-color: #007bff;\n    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);\n  }\n\n  \n\n  .empty-state-icon[_ngcontent-%COMP%] {\n    width: 80px;\n    height: 80px;\n    margin: 0 auto;\n    background-color: #f8f9fa;\n    border-radius: 50%;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-size: 2rem;\n    color: #adb5bd;\n  }\n\n  \n\n  .form-check-label[_ngcontent-%COMP%] {\n    cursor: pointer;\n    transition: all 0.2s ease;\n  }\n\n  .form-check-input[_ngcontent-%COMP%]:checked    + .form-check-label[_ngcontent-%COMP%] {\n    background-color: rgba(13, 110, 253, 0.1);\n    border-color: #007bff;\n  }\n\n  \n\n  .rounded-4[_ngcontent-%COMP%] {\n    border-radius: 0.75rem !important;\n  }\n\n  \n\n  .member-grid[_ngcontent-%COMP%] {\n    max-height: 500px;\n    overflow-y: auto;\n  }"]
    });
  }
}

/***/ }),

/***/ 5458:
/*!**************************************************************************!*\
  !*** ./src/app/views/front/equipes/equipe-form/equipe-form.component.ts ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   EquipeFormComponent: () => (/* binding */ EquipeFormComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var src_app_services_equipe_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/services/equipe.service */ 8133);
/* harmony import */ var src_app_services_membre_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/services/membre.service */ 1622);
/* harmony import */ var src_app_services_auth_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/services/auth.service */ 4796);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var src_app_services_notification_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/services/notification.service */ 7473);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/forms */ 4456);








function EquipeFormComponent_div_31_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 21)(1, "div", 22);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](2, "div", 23)(3, "div", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](4, "p", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](5, " Chargement des donn\u00E9es... ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
  }
}
function EquipeFormComponent_div_32_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 26)(1, "div", 27)(2, "div", 28)(3, "div", 29);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](4, "i", 30);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](5, "div", 31)(6, "h3", 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](7, " Erreur ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](8, "p", 33);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()()();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", ctx_r1.error, " ");
  }
}
function EquipeFormComponent_div_33_div_19_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 69);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](1, "i", 70);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](2, " Ce nom d'\u00E9quipe existe d\u00E9j\u00E0. Veuillez en choisir un autre. ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
}
function EquipeFormComponent_div_33_div_20_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 69);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](1, "i", 70);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](2, " Le nom de l'\u00E9quipe doit contenir au moins 3 caract\u00E8res. ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
}
function EquipeFormComponent_div_33_div_21_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 71);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](1, "i", 72);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](2, " Le nom de l'\u00E9quipe est requis. ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
}
function EquipeFormComponent_div_33_div_32_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 69);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](1, "i", 70);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](2, " La description doit contenir au moins 10 caract\u00E8res. ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
}
function EquipeFormComponent_div_33_div_33_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 71);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](1, "i", 72);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](2, " La description de l'\u00E9quipe est requise. ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
}
function EquipeFormComponent_div_33_div_40_div_7_tr_20_small_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "small", 101);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const membreId_r20 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"]("ID: ", membreId_r20, "");
  }
}
function EquipeFormComponent_div_33_div_40_div_7_tr_20_a_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "a", 102);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const membreId_r20 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]().$implicit;
    const ctx_r22 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpropertyInterpolate1"]("href", "mailto:", ctx_r22.getMembreEmail(membreId_r20), "", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵsanitizeUrl"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", ctx_r22.getMembreEmail(membreId_r20), " ");
  }
}
function EquipeFormComponent_div_33_div_40_div_7_tr_20_span_7_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "span", 103);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1, "Non renseign\u00E9");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
}
const _c0 = function (a0, a1, a2) {
  return {
    "bg-primary": a0,
    "bg-success": a1,
    "bg-secondary": a2
  };
};
const _c1 = function (a0, a1, a2) {
  return {
    "bi-mortarboard-fill": a0,
    "bi-briefcase-fill": a1,
    "bi-question-circle-fill": a2
  };
};
function EquipeFormComponent_div_33_div_40_div_7_tr_20_Template(rf, ctx) {
  if (rf & 1) {
    const _r27 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "tr", 93)(1, "td")(2, "span", 94);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](4, EquipeFormComponent_div_33_div_40_div_7_tr_20_small_4_Template, 2, 1, "small", 95);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](5, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](6, EquipeFormComponent_div_33_div_40_div_7_tr_20_a_6_Template, 2, 2, "a", 96);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](7, EquipeFormComponent_div_33_div_40_div_7_tr_20_span_7_Template, 2, 0, "span", 97);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](8, "td")(9, "span", 98);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](10, "i", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](11);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](12, "td", 91)(13, "button", 99);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function EquipeFormComponent_div_33_div_40_div_7_tr_20_Template_button_click_13_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r27);
      const membreId_r20 = restoredCtx.$implicit;
      const ctx_r26 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](4);
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r26.removeMembreFromEquipe(membreId_r20));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](14, "i", 100);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const membreId_r20 = ctx.$implicit;
    const ctx_r19 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](ctx_r19.getMembreName(membreId_r20));
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx_r19.getMembreName(membreId_r20) !== membreId_r20);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx_r19.getMembreEmail(membreId_r20) !== "Non renseign\u00E9");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx_r19.getMembreEmail(membreId_r20) === "Non renseign\u00E9");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpureFunction3"](7, _c0, ctx_r19.getMembreProfession(membreId_r20) === "\u00C9tudiant", ctx_r19.getMembreProfession(membreId_r20) === "Professeur", ctx_r19.getMembreProfession(membreId_r20) === "Non sp\u00E9cifi\u00E9"));
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpureFunction3"](11, _c1, ctx_r19.getMembreProfession(membreId_r20) === "\u00C9tudiant", ctx_r19.getMembreProfession(membreId_r20) === "Professeur", ctx_r19.getMembreProfession(membreId_r20) === "Non sp\u00E9cifi\u00E9"));
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", ctx_r19.getMembreProfession(membreId_r20), " ");
  }
}
function EquipeFormComponent_div_33_div_40_div_7_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div")(1, "div", 84)(2, "table", 85)(3, "thead", 86)(4, "tr")(5, "th")(6, "div", 87);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](7, "i", 88);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](8, " Nom et Pr\u00E9nom ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](9, "th")(10, "div", 87);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](11, "i", 89);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](12, " Email ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](13, "th")(14, "div", 87);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](15, "i", 90);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](16, " Statut ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](17, "th", 91);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](18, "Actions");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](19, "tbody");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](20, EquipeFormComponent_div_33_div_40_div_7_tr_20_Template, 15, 15, "tr", 92);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const ctx_r14 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](20);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngForOf", ctx_r14.equipe.members);
  }
}
function EquipeFormComponent_div_33_div_40_ng_template_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 104);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](1, "i", 105);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](2, "h5", 106);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](3, " Aucun membre dans cette \u00E9quipe ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](4, "p", 107);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](5, " Ajoutez des membres \u00E0 l'\u00E9quipe en utilisant le formulaire ci-dessous. ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
  }
}
function EquipeFormComponent_div_33_div_40_div_14_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 58);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](1, "i", 59);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](2, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](3, " Aucun utilisateur disponible. Veuillez d'abord cr\u00E9er des utilisateurs. ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
  }
}
function EquipeFormComponent_div_33_div_40_div_15_option_10_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "option", 125);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const user_r31 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("value", user_r31._id || user_r31.id);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate4"](" ", user_r31.firstName || "", " ", user_r31.lastName || user_r31.name || user_r31.id, " ", user_r31.email ? "- " + user_r31.email : "", " ", user_r31.profession ? "(" + (user_r31.profession === "etudiant" ? "\u00C9tudiant" : "Professeur") + ")" : user_r31.role ? "(" + (user_r31.role === "etudiant" ? "\u00C9tudiant" : "Professeur") + ")" : "", " ");
  }
}
function EquipeFormComponent_div_33_div_40_div_15_Template(rf, ctx) {
  if (rf & 1) {
    const _r33 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 108)(1, "div", 40)(2, "div", 109)(3, "div", 110)(4, "label", 111);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](5, "Utilisateur");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](6, "select", 112, 113)(8, "option", 114);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](9, " S\u00E9lectionnez un utilisateur ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](10, EquipeFormComponent_div_33_div_40_div_15_option_10_Template, 2, 5, "option", 115);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](11, "div", 116)(12, "label", 117);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](13, "R\u00F4le dans l'\u00E9quipe");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](14, "select", 118, 119)(16, "option", 120);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](17, "Membre");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](18, "option", 121);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](19, "Administrateur");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](20, "div", 122)(21, "button", 123);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function EquipeFormComponent_div_33_div_40_div_15_Template_button_click_21_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r33);
      const _r28 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵreference"](7);
      const _r30 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵreference"](15);
      const ctx_r32 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](3);
      ctx_r32.addMembreToEquipe(_r28.value, _r30.value);
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](_r28.value = "");
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](22, "i", 124);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](23, " Ajouter ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()()();
  }
  if (rf & 2) {
    const _r28 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵreference"](7);
    const ctx_r18 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](10);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngForOf", ctx_r18.availableUsers);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](11);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("disabled", !_r28.value);
  }
}
function EquipeFormComponent_div_33_div_40_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 61)(1, "div", 73)(2, "div", 74)(3, "h4", 75);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](4, "i", 76);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](5, " Membres de l'\u00E9quipe ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](6, "div", 40);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](7, EquipeFormComponent_div_33_div_40_div_7_Template, 21, 1, "div", 77);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](8, EquipeFormComponent_div_33_div_40_ng_template_8_Template, 6, 0, "ng-template", null, 78, _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplateRefExtractor"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](10, "div", 79)(11, "h5", 80);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](12, "i", 81);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](13, " Ajouter un membre ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](14, EquipeFormComponent_div_33_div_40_div_14_Template, 4, 0, "div", 82);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](15, EquipeFormComponent_div_33_div_40_div_15_Template, 24, 2, "div", 83);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const _r15 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵreference"](9);
    const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx_r10.equipe.members && ctx_r10.equipe.members.length > 0)("ngIfElse", _r15);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx_r10.availableUsers.length === 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx_r10.availableUsers.length > 0);
  }
}
function EquipeFormComponent_div_33_button_47_Template(rf, ctx) {
  if (rf & 1) {
    const _r35 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "button", 126);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function EquipeFormComponent_div_33_button_47_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r35);
      const ctx_r34 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r34.deleteEquipe());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](1, "i", 127);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](2, " Supprimer ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
}
function EquipeFormComponent_div_33_span_49_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](0, "span", 128);
  }
}
const _c2 = function (a0, a1) {
  return {
    "bi-save": a0,
    "bi-plus-circle": a1
  };
};
function EquipeFormComponent_div_33_i_50_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](0, "i", 39);
  }
  if (rf & 2) {
    const ctx_r13 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpureFunction2"](1, _c2, ctx_r13.isEditMode, !ctx_r13.isEditMode));
  }
}
const _c3 = function (a0, a1) {
  return {
    "bi-pencil-square": a0,
    "bi-plus-circle": a1
  };
};
function EquipeFormComponent_div_33_Template(rf, ctx) {
  if (rf & 1) {
    const _r37 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 34)(1, "div", 35)(2, "div", 36)(3, "div", 37)(4, "h3", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](5, "i", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](7, "div", 40)(8, "form", 41);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("ngSubmit", function EquipeFormComponent_div_33_Template_form_ngSubmit_8_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r37);
      const ctx_r36 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r36.onSubmit());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](9, "div", 42)(10, "label", 43);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](11, "Nom de l'\u00E9quipe ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](12, "span", 44);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](13, "*");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](14, "div", 45)(15, "span", 46);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](16, "i", 47);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](17, "input", 48, 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("input", function EquipeFormComponent_div_33_Template_input_input_17_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r37);
      const _r3 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵreference"](18);
      const ctx_r38 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r38.updateName(_r3.value));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](19, EquipeFormComponent_div_33_div_19_Template, 3, 0, "div", 50);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](20, EquipeFormComponent_div_33_div_20_Template, 3, 0, "div", 50);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](21, EquipeFormComponent_div_33_div_21_Template, 3, 0, "div", 51);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](22, "div", 42)(23, "label", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](24, "Description ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](25, "span", 44);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](26, "*");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](27, "div", 45)(28, "span", 53);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](29, "i", 54);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](30, "textarea", 55, 56);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("input", function EquipeFormComponent_div_33_Template_textarea_input_30_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r37);
      const _r7 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵreference"](31);
      const ctx_r39 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r39.updateDescription(_r7.value));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](32, EquipeFormComponent_div_33_div_32_Template, 3, 0, "div", 50);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](33, EquipeFormComponent_div_33_div_33_Template, 3, 0, "div", 51);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](34, "input", 57);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](35, "div", 42)(36, "div", 58);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](37, "i", 59);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](38, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](39, " Un administrateur par d\u00E9faut sera assign\u00E9 \u00E0 cette \u00E9quipe. ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](40, EquipeFormComponent_div_33_div_40_Template, 16, 4, "div", 60);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](41, "div", 61)(42, "div", 62)(43, "div")(44, "button", 63);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function EquipeFormComponent_div_33_Template_button_click_44_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r37);
      const ctx_r40 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r40.cancel());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](45, "i", 64);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](46, " Retour ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](47, EquipeFormComponent_div_33_button_47_Template, 3, 0, "button", 65);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](48, "button", 66);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](49, EquipeFormComponent_div_33_span_49_Template, 1, 0, "span", 67);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](50, EquipeFormComponent_div_33_i_50_Template, 1, 4, "i", 68);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](51);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()()()()()();
  }
  if (rf & 2) {
    const _r3 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵreference"](18);
    const _r7 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵreference"](31);
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpureFunction2"](20, _c3, ctx_r2.isEditMode, !ctx_r2.isEditMode));
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", ctx_r2.isEditMode ? "Informations de l'\u00E9quipe" : "D\u00E9tails de la nouvelle \u00E9quipe", " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](11);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵclassProp"]("is-invalid", ctx_r2.nameExists || ctx_r2.nameError && _r3.value.length > 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("value", ctx_r2.equipe.name || "");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx_r2.nameExists);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx_r2.nameError && _r3.value.length > 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx_r2.error && !ctx_r2.equipe.name);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵclassProp"]("is-invalid", ctx_r2.descriptionError && _r7.value.length > 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("value", ctx_r2.equipe.description || "");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx_r2.descriptionError && _r7.value.length > 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx_r2.error && !ctx_r2.equipe.description);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("value", ctx_r2.equipe.admin);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx_r2.isEditMode && ctx_r2.equipe._id);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx_r2.isEditMode && ctx_r2.equipeId);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("disabled", ctx_r2.submitting || !ctx_r2.equipe.name || !ctx_r2.equipe.description || ctx_r2.nameExists || ctx_r2.nameError || ctx_r2.descriptionError);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx_r2.submitting);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", !ctx_r2.submitting);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", ctx_r2.isEditMode ? "Mettre \u00E0 jour" : "Cr\u00E9er l'\u00E9quipe", " ");
  }
}
class EquipeFormComponent {
  constructor(equipeService, membreService, userService, route, router, notificationService) {
    this.equipeService = equipeService;
    this.membreService = membreService;
    this.userService = userService;
    this.route = route;
    this.router = router;
    this.notificationService = notificationService;
    this.equipe = {
      name: '',
      description: '',
      admin: '' // Sera défini avec l'ID de l'utilisateur connecté
    };

    this.isEditMode = false;
    this.loading = false;
    this.submitting = false;
    this.error = null;
    this.equipeId = null;
    this.nameExists = false;
    this.nameError = false;
    this.descriptionError = false;
    this.checkingName = false;
    this.existingEquipes = [];
    this.availableMembers = []; // Liste des membres disponibles
    this.availableUsers = []; // Liste des utilisateurs disponibles
    this.currentUserId = null; // ID de l'utilisateur connecté
  }

  ngOnInit() {
    console.log('EquipeFormComponent initialized');
    // Récupérer l'ID de l'utilisateur connecté
    this.getCurrentUser();
    // Charger toutes les équipes pour vérifier les noms existants
    this.loadAllEquipes();
    // Charger tous les membres disponibles
    this.loadAllMembers();
    // Charger tous les utilisateurs disponibles
    this.loadAllUsers();
    try {
      // Vérifier si nous sommes en mode édition (si un ID est présent dans l'URL)
      this.equipeId = this.route.snapshot.paramMap.get('id');
      this.isEditMode = !!this.equipeId;
      console.log('Edit mode:', this.isEditMode, 'ID:', this.equipeId);
      if (this.isEditMode && this.equipeId) {
        this.loadEquipe(this.equipeId);
        // Ajouter un délai pour s'assurer que l'équipe est chargée
        setTimeout(() => {
          console.log('Après délai - this.equipeId:', this.equipeId);
          console.log('Après délai - this.equipe:', this.equipe);
        }, 1000);
      }
    } catch (error) {
      console.error('Error in ngOnInit:', error);
      this.error = "Erreur d'initialisation";
    }
    // Ajouter un gestionnaire d'événements pour le bouton d'ajout de membre
    setTimeout(() => {
      const addButton = document.getElementById('addMembreButton');
      if (addButton) {
        console.log("Bouton d'ajout de membre trouvé");
        addButton.addEventListener('click', () => {
          console.log("Bouton d'ajout de membre cliqué");
        });
      } else {
        console.log("Bouton d'ajout de membre non trouvé");
      }
    }, 2000);
  }
  getCurrentUser() {
    const token = localStorage.getItem('token');
    if (token) {
      this.userService.getProfile(token).subscribe({
        next: user => {
          console.log('Utilisateur connecté:', user);
          this.currentUserId = user._id || user.id;
          // Définir l'admin de l'équipe avec l'ID de l'utilisateur connecté
          if (!this.isEditMode && this.currentUserId) {
            this.equipe.admin = this.currentUserId;
            console.log('Admin défini pour nouvelle équipe:', this.equipe.admin);
          }
        },
        error: error => {
          console.error('Erreur lors de la récupération du profil utilisateur:', error);
          this.error = "Impossible de récupérer les informations de l'utilisateur connecté.";
        }
      });
    } else {
      this.error = "Aucun token d'authentification trouvé. Veuillez vous reconnecter.";
    }
  }
  loadAllMembers() {
    this.membreService.getMembres().subscribe({
      next: membres => {
        this.availableMembers = membres;
        console.log('Membres disponibles chargés:', membres);
      },
      error: error => {
        console.error('Erreur lors du chargement des membres:', error);
        this.error = 'Impossible de charger la liste des membres. Veuillez réessayer plus tard.';
      }
    });
  }
  loadAllUsers() {
    const token = localStorage.getItem('token');
    if (token) {
      this.userService.getAllUsers(token).subscribe({
        next: users => {
          this.availableUsers = users;
          console.log('Utilisateurs disponibles chargés:', users);
        },
        error: error => {
          console.error('Erreur lors du chargement des utilisateurs:', error);
          this.error = 'Impossible de charger la liste des utilisateurs. Veuillez réessayer plus tard.';
        }
      });
    }
  }
  loadAllEquipes() {
    this.equipeService.getEquipes().subscribe({
      next: equipes => {
        this.existingEquipes = equipes;
        console.log('Équipes existantes chargées:', equipes);
      },
      error: error => {
        console.error('Erreur lors du chargement des équipes:', error);
      }
    });
  }
  loadEquipe(id) {
    console.log('Loading equipe with ID:', id);
    this.loading = true;
    this.error = null;
    this.equipeService.getEquipe(id).subscribe({
      next: data => {
        console.log('Équipe chargée:', data);
        this.equipe = data;
        // Vérifier que l'ID est correctement défini
        console.log("ID de l'équipe après chargement:", this.equipe._id);
        console.log('this.equipeId:', this.equipeId);
        // Si l'équipe a des membres, récupérer les informations de chaque membre
        if (this.equipe.members && this.equipe.members.length > 0) {
          this.loadMembersDetails();
        }
        this.loading = false;
      },
      error: error => {
        console.error("Erreur lors du chargement de l'équipe:", error);
        this.error = "Impossible de charger les détails de l'équipe. Veuillez réessayer plus tard.";
        this.loading = false;
      }
    });
  }
  // Méthode pour récupérer les détails des membres de l'équipe
  loadMembersDetails() {
    if (!this.equipe.members || this.equipe.members.length === 0) {
      return;
    }
    console.log("Chargement des détails des membres de l'équipe...");
    // Pour chaque membre de l'équipe, essayer de trouver ses informations dans la liste des utilisateurs
    this.equipe.members.forEach(membreId => {
      // Chercher d'abord dans la liste des utilisateurs
      const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);
      if (user) {
        console.log(`Membre ${membreId} trouvé dans la liste des utilisateurs:`, user);
        // Vérifier si toutes les informations nécessaires sont présentes
        if (!user.email || !user.profession && !user.role) {
          // Si des informations manquent, essayer de les récupérer depuis l'API
          const token = localStorage.getItem('token');
          if (token) {
            this.userService.getUserById(membreId, token).subscribe({
              next: userData => {
                console.log(`Détails supplémentaires de l'utilisateur ${membreId} récupérés:`, userData);
                // Mettre à jour l'utilisateur dans la liste avec les nouvelles informations
                const index = this.availableUsers.findIndex(u => u._id === membreId || u.id === membreId);
                if (index !== -1) {
                  this.availableUsers[index] = {
                    ...this.availableUsers[index],
                    ...userData
                  };
                }
              },
              error: error => {
                console.error(`Erreur lors de la récupération des détails supplémentaires de l'utilisateur ${membreId}:`, error);
              }
            });
          }
        }
      } else {
        // Si non trouvé, essayer de récupérer l'utilisateur depuis l'API
        const token = localStorage.getItem('token');
        if (token) {
          this.userService.getUserById(membreId, token).subscribe({
            next: userData => {
              console.log(`Détails de l'utilisateur ${membreId} récupérés:`, userData);
              // Ajouter l'utilisateur à la liste des utilisateurs disponibles s'il n'y est pas déjà
              if (!this.availableUsers.some(u => u._id === userData._id || u.id === userData.id)) {
                this.availableUsers.push(userData);
              }
            },
            error: error => {
              console.error(`Erreur lors de la récupération des détails de l'utilisateur ${membreId}:`, error);
            }
          });
        }
      }
    });
  }
  checkNameExists(name) {
    // En mode édition, ignorer l'équipe actuelle
    if (this.isEditMode && this.equipeId) {
      return this.existingEquipes.some(e => e.name === name && e._id !== this.equipeId);
    }
    // En mode création, vérifier tous les noms
    return this.existingEquipes.some(e => e.name === name);
  }
  updateName(value) {
    console.log('Name updated:', value);
    this.equipe.name = value;
    // Vérifier si le nom existe déjà
    this.nameExists = this.checkNameExists(value);
    if (this.nameExists) {
      console.warn("Ce nom d'équipe existe déjà");
    }
    // Vérifier si le nom a au moins 3 caractères
    this.nameError = value.length > 0 && value.length < 3;
    if (this.nameError) {
      console.warn('Le nom doit contenir au moins 3 caractères');
    }
  }
  updateDescription(value) {
    console.log('Description updated:', value);
    this.equipe.description = value;
    // Vérifier si la description a au moins 10 caractères
    this.descriptionError = value.length > 0 && value.length < 10;
    if (this.descriptionError) {
      console.warn('La description doit contenir au moins 10 caractères');
    }
  }
  onSubmit() {
    console.log('Form submitted with:', this.equipe);
    // Vérifier si le nom est présent et valide
    if (!this.equipe.name) {
      this.error = "Le nom de l'équipe est requis.";
      return;
    }
    if (this.equipe.name.length < 3) {
      this.nameError = true;
      this.error = "Le nom de l'équipe doit contenir au moins 3 caractères.";
      return;
    }
    // Vérifier si la description est présente et valide
    if (!this.equipe.description) {
      this.error = "La description de l'équipe est requise.";
      return;
    }
    if (this.equipe.description.length < 10) {
      this.descriptionError = true;
      this.error = "La description de l'équipe doit contenir au moins 10 caractères.";
      return;
    }
    // Vérifier si le nom existe déjà avant de soumettre
    if (this.checkNameExists(this.equipe.name)) {
      this.nameExists = true;
      this.error = 'Une équipe avec ce nom existe déjà. Veuillez choisir un autre nom.';
      return;
    }
    this.submitting = true;
    this.error = null;
    // S'assurer que l'admin est défini
    if (!this.equipe.admin && this.currentUserId) {
      this.equipe.admin = this.currentUserId;
    }
    if (!this.equipe.admin) {
      this.error = "Impossible de déterminer l'administrateur de l'équipe. Veuillez vous reconnecter.";
      return;
    }
    // Créer une copie de l'objet équipe pour éviter les problèmes de référence
    const equipeToSave = {
      name: this.equipe.name,
      description: this.equipe.description || '',
      admin: this.equipe.admin
    };
    // Ajouter l'ID si nous sommes en mode édition
    if (this.isEditMode && this.equipeId) {
      equipeToSave._id = this.equipeId;
    }
    console.log('Données à envoyer:', equipeToSave);
    if (this.isEditMode && this.equipeId) {
      // Mode édition
      this.equipeService.updateEquipe(this.equipeId, equipeToSave).subscribe({
        next: response => {
          console.log('Équipe mise à jour avec succès:', response);
          this.submitting = false;
          this.notificationService.showSuccess(`L'équipe "${response.name}" a été mise à jour avec succès.`);
          this.router.navigate(['/equipes/liste']);
        },
        error: error => {
          console.error("Erreur lors de la mise à jour de l'équipe:", error);
          this.error = `Impossible de mettre à jour l'équipe: ${error.message}`;
          this.submitting = false;
          this.notificationService.showError(`Erreur: ${error.message}`);
        }
      });
    } else {
      // Mode ajout
      this.equipeService.addEquipe(equipeToSave).subscribe({
        next: response => {
          console.log('Équipe ajoutée avec succès:', response);
          this.submitting = false;
          this.notificationService.showSuccess(`L'équipe "${response.name}" a été créée avec succès.`);
          this.router.navigate(['/equipes/liste']);
        },
        error: error => {
          console.error("Erreur lors de l'ajout de l'équipe:", error);
          this.error = `Impossible d'ajouter l'équipe: ${error.message}`;
          this.submitting = false;
          this.notificationService.showError(`Erreur: ${error.message}`);
        }
      });
    }
  }
  cancel() {
    console.log('Form cancelled');
    this.router.navigate(['/equipes/liste']);
  }
  // Méthodes pour gérer les membres
  addMembreToEquipe(membreId, role = 'membre') {
    console.log('Début de addMembreToEquipe avec membreId:', membreId, 'et rôle:', role);
    console.log('État actuel - this.equipeId:', this.equipeId);
    console.log('État actuel - this.equipe:', this.equipe);
    // Utiliser this.equipe._id si this.equipeId n'est pas défini
    const equipeId = this.equipeId || this.equipe && this.equipe._id;
    console.log('equipeId calculé:', equipeId);
    if (!equipeId || !membreId) {
      console.error("ID d'équipe ou ID de membre manquant");
      this.error = "ID d'équipe ou ID de membre manquant";
      console.log('equipeId:', equipeId, 'membreId:', membreId);
      // Afficher un message à l'utilisateur
      this.notificationService.showError("Impossible d'ajouter le membre: ID d'équipe ou ID de membre manquant");
      return;
    }
    // Vérifier si le membre est déjà dans l'équipe
    if (this.equipe.members && this.equipe.members.includes(membreId)) {
      this.notificationService.showError("Ce membre fait déjà partie de l'équipe");
      return;
    }
    // Récupérer les informations de l'utilisateur pour afficher un message plus informatif
    const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);
    const userName = user ? user.firstName && user.lastName ? `${user.firstName} ${user.lastName}` : user.name || membreId : membreId;
    // Créer l'objet membre avec le rôle spécifié
    const membre = {
      id: membreId,
      role: role
    };
    this.loading = true;
    console.log(`Ajout de l'utilisateur "${userName}" comme ${role} à l'équipe ${equipeId}`);
    this.equipeService.addMembreToEquipe(equipeId, membre).subscribe({
      next: response => {
        console.log('Membre ajouté avec succès:', response);
        this.notificationService.showSuccess(`${userName} a été ajouté comme ${role === 'admin' ? 'administrateur' : 'membre'} à l'équipe`);
        // Recharger l'équipe pour mettre à jour la liste des membres
        this.loadEquipe(equipeId);
        this.loading = false;
      },
      error: error => {
        console.error("Erreur lors de l'ajout du membre:", error);
        this.error = "Impossible d'ajouter le membre. Veuillez réessayer plus tard.";
        this.notificationService.showError("Erreur lors de l'ajout du membre: " + error.message);
        this.loading = false;
      }
    });
  }
  // Méthode pour obtenir le nom complet d'un membre à partir de son ID
  getMembreName(membreId) {
    // Chercher d'abord dans la liste des utilisateurs
    const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);
    if (user) {
      if (user.firstName && user.lastName) {
        return `${user.firstName} ${user.lastName}`;
      } else if (user.name) {
        return user.name;
      }
    }
    // Chercher ensuite dans la liste des membres
    const membre = this.availableMembers.find(m => m._id === membreId || m.id === membreId);
    if (membre && membre.name) {
      return membre.name;
    }
    // Si aucun nom n'est trouvé, retourner l'ID
    return membreId;
  }
  // Méthode pour obtenir l'email d'un membre
  getMembreEmail(membreId) {
    const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);
    if (user && user.email) {
      return user.email;
    }
    return 'Non renseigné';
  }
  // Méthode pour obtenir la profession d'un membre
  getMembreProfession(membreId) {
    const user = this.availableUsers.find(u => u._id === membreId || u.id === membreId);
    if (user) {
      if (user.profession) {
        return user.profession === 'etudiant' ? 'Étudiant' : 'Professeur';
      } else if (user.role) {
        return user.role === 'etudiant' ? 'Étudiant' : 'Professeur';
      }
    }
    return 'Non spécifié';
  }
  // Méthode pour obtenir le rôle d'un membre dans l'équipe
  getMembreRole(_membreId) {
    // Cette méthode nécessiterait d'avoir accès aux rôles des membres dans l'équipe
    // Pour l'instant, nous retournons une valeur par défaut
    return 'Membre';
  }
  removeMembreFromEquipe(membreId) {
    console.log('Méthode removeMembreFromEquipe appelée avec ID:', membreId);
    console.log('État actuel - this.equipeId:', this.equipeId);
    console.log('État actuel - this.equipe:', this.equipe);
    // Utiliser this.equipe._id si this.equipeId n'est pas défini
    const equipeId = this.equipeId || this.equipe && this.equipe._id;
    if (!equipeId) {
      console.error("ID d'équipe manquant");
      this.error = "ID d'équipe manquant. Impossible de retirer le membre.";
      this.notificationService.showError("ID d'équipe manquant. Impossible de retirer le membre.");
      return;
    }
    if (!membreId) {
      console.error('ID de membre manquant');
      this.error = 'ID de membre manquant. Impossible de retirer le membre.';
      this.notificationService.showError('ID de membre manquant. Impossible de retirer le membre.');
      return;
    }
    // Obtenir le nom du membre pour l'afficher dans le message de confirmation
    const membreName = this.getMembreName(membreId);
    console.log(`Tentative de retrait de l'utilisateur ${membreId} (${membreName}) de l'équipe ${equipeId}`);
    try {
      if (confirm(`Êtes-vous sûr de vouloir retirer ${membreName} de l'équipe?`)) {
        console.log('Confirmation acceptée, suppression en cours...');
        this.loading = true;
        this.error = null;
        // Ajouter un délai pour s'assurer que l'utilisateur voit le chargement
        setTimeout(() => {
          this.equipeService.removeMembreFromEquipe(equipeId, membreId).subscribe({
            next: response => {
              console.log(`Utilisateur "${membreName}" retiré avec succès de l'équipe:`, response);
              this.loading = false;
              this.notificationService.showSuccess(`${membreName} a été retiré avec succès de l'équipe`);
              // Recharger l'équipe pour mettre à jour la liste des membres
              this.loadEquipe(equipeId);
            },
            error: error => {
              console.error(`Erreur lors du retrait de l'utilisateur "${membreName}":`, error);
              console.error("Détails de l'erreur:", {
                status: error.status,
                message: error.message,
                error: error
              });
              this.loading = false;
              this.error = `Impossible de retirer l'utilisateur "${membreName}" de l'équipe: ${error.message || 'Erreur inconnue'}`;
              this.notificationService.showError(`Erreur lors du retrait du membre: ${this.error}`);
            }
          });
        }, 500);
      } else {
        console.log("Suppression annulée par l'utilisateur");
      }
    } catch (error) {
      console.error('Exception lors du retrait du membre:', error);
      this.error = `Exception: ${error?.message || 'Erreur inconnue'}`;
      this.notificationService.showError(`Exception: ${this.error}`);
    }
  }
  // Méthode pour supprimer l'équipe
  deleteEquipe() {
    console.log('Méthode deleteEquipe appelée dans equipe-form.component.ts');
    console.log('État actuel - this.equipeId:', this.equipeId);
    console.log('État actuel - this.equipe:', this.equipe);
    // Utiliser this.equipe._id si this.equipeId n'est pas défini
    const equipeId = this.equipeId || this.equipe && this.equipe._id;
    if (!equipeId) {
      console.error("ID d'équipe manquant");
      this.error = "ID d'équipe manquant. Impossible de supprimer l'équipe.";
      this.notificationService.showError("ID d'équipe manquant. Impossible de supprimer l'équipe.");
      return;
    }
    console.log("ID de l'équipe à supprimer (final):", equipeId);
    try {
      if (confirm(`Êtes-vous sûr de vouloir supprimer l'équipe "${this.equipe.name}"? Cette action est irréversible.`)) {
        console.log('Confirmation acceptée, suppression en cours...');
        this.loading = true;
        this.error = null;
        // Ajouter un délai pour s'assurer que l'utilisateur voit le chargement
        setTimeout(() => {
          this.equipeService.deleteEquipe(equipeId).subscribe({
            next: response => {
              console.log('Équipe supprimée avec succès, réponse:', response);
              this.loading = false;
              this.notificationService.showSuccess(`L'équipe "${this.equipe.name}" a été supprimée avec succès.`);
              // Ajouter un délai avant la redirection
              setTimeout(() => {
                this.router.navigate(['/equipes/liste']);
              }, 500);
            },
            error: error => {
              console.error("Erreur lors de la suppression de l'équipe:", error);
              console.error("Détails de l'erreur:", {
                status: error.status,
                message: error.message,
                error: error
              });
              this.loading = false;
              this.error = `Impossible de supprimer l'équipe: ${error.message || 'Erreur inconnue'}`;
              this.notificationService.showError(`Erreur lors de la suppression: ${this.error}`);
            }
          });
        }, 500);
      } else {
        console.log("Suppression annulée par l'utilisateur");
      }
    } catch (error) {
      console.error('Exception lors de la suppression:', error);
      this.error = `Exception: ${error?.message || 'Erreur inconnue'}`;
      this.notificationService.showError(`Exception: ${this.error}`);
    }
  }
  static {
    this.ɵfac = function EquipeFormComponent_Factory(t) {
      return new (t || EquipeFormComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](src_app_services_equipe_service__WEBPACK_IMPORTED_MODULE_0__.EquipeService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](src_app_services_membre_service__WEBPACK_IMPORTED_MODULE_1__.MembreService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](src_app_services_auth_service__WEBPACK_IMPORTED_MODULE_2__.AuthService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_5__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_5__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](src_app_services_notification_service__WEBPACK_IMPORTED_MODULE_3__.NotificationService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineComponent"]({
      type: EquipeFormComponent,
      selectors: [["app-equipe-form"]],
      decls: 34,
      vars: 5,
      consts: [[1, "min-h-screen", "bg-[#f0f4f8]", "dark:bg-[#0a0a0a]", "relative", "overflow-hidden"], [1, "absolute", "inset-0", "overflow-hidden", "pointer-events-none"], [1, "absolute", "top-[15%]", "left-[10%]", "w-64", "h-64", "rounded-full", "bg-gradient-to-br", "from-[#4f5fad]/5", "to-transparent", "dark:from-[#00f7ff]/3", "dark:to-transparent", "blur-3xl"], [1, "absolute", "bottom-[20%]", "right-[10%]", "w-80", "h-80", "rounded-full", "bg-gradient-to-tl", "from-[#4f5fad]/5", "to-transparent", "dark:from-[#00f7ff]/3", "dark:to-transparent", "blur-3xl"], [1, "absolute", "inset-0", "opacity-5", "dark:opacity-[0.03]"], [1, "h-full", "grid", "grid-cols-12"], [1, "border-r", "border-[#4f5fad]", "dark:border-[#00f7ff]"], [1, "max-w-4xl", "mx-auto", "p-6", "relative", "z-10"], [1, "mb-8", "relative"], [1, "absolute", "top-0", "left-0", "right-0", "h-1", "bg-gradient-to-r", "from-[#4f5fad]", "to-[#7826b5]", "dark:from-[#00f7ff]", "dark:to-[#4f5fad]"], [1, "absolute", "top-0", "left-0", "right-0", "h-1", "bg-gradient-to-r", "from-[#4f5fad]", "to-[#7826b5]", "dark:from-[#00f7ff]", "dark:to-[#4f5fad]", "blur-md"], [1, "bg-white", "dark:bg-[#1a1a1a]", "rounded-xl", "shadow-lg", "dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]", "p-6", "backdrop-blur-sm", "border", "border-[#4f5fad]/20", "dark:border-[#00f7ff]/20"], [1, "flex", "flex-col", "lg:flex-row", "lg:items-center", "lg:justify-between"], [1, "mb-4", "lg:mb-0"], [1, "text-3xl", "font-bold", "text-[#4f5fad]", "dark:text-[#00f7ff]", "mb-2", "tracking-wide"], [1, "text-[#6d6870]", "dark:text-[#a0a0a0]", "text-sm"], [1, "bg-[#4f5fad]/20", "dark:bg-[#00f7ff]/20", "text-[#4f5fad]", "dark:text-[#00f7ff]", "px-6", "py-3", "rounded-xl", "font-medium", "transition-all", "duration-300", "hover:scale-105", "hover:bg-[#4f5fad]/30", "dark:hover:bg-[#00f7ff]/30", 3, "click"], [1, "fas", "fa-arrow-left", "mr-2"], ["class", "flex flex-col items-center justify-center py-16", 4, "ngIf"], ["class", "mb-6", 4, "ngIf"], ["class", "row justify-content-center", 4, "ngIf"], [1, "flex", "flex-col", "items-center", "justify-center", "py-16"], [1, "relative"], [1, "w-12", "h-12", "border-3", "border-[#4f5fad]/20", "dark:border-[#00f7ff]/20", "border-t-[#4f5fad]", "dark:border-t-[#00f7ff]", "rounded-full", "animate-spin"], [1, "absolute", "inset-0", "bg-[#4f5fad]/20", "dark:bg-[#00f7ff]/20", "blur-xl", "rounded-full", "transform", "scale-150", "-z-10"], [1, "mt-4", "text-[#4f5fad]", "dark:text-[#00f7ff]", "text-sm", "font-medium", "tracking-wide"], [1, "mb-6"], [1, "bg-[#ff6b69]/10", "dark:bg-[#ff3b30]/10", "border-l-4", "border-[#ff6b69]", "dark:border-[#ff3b30]", "rounded-lg", "p-4", "backdrop-blur-sm"], [1, "flex", "items-start"], [1, "text-[#ff6b69]", "dark:text-[#ff3b30]", "mr-3", "text-xl"], [1, "fas", "fa-exclamation-triangle"], [1, "flex-1"], [1, "font-semibold", "text-[#ff6b69]", "dark:text-[#ff3b30]", "mb-1"], [1, "text-sm", "text-[#6d6870]", "dark:text-[#e0e0e0]"], [1, "row", "justify-content-center"], [1, "col-lg-8"], [1, "card", "border-0", "shadow-sm", "rounded-3", "overflow-hidden"], [1, "card-header", "bg-gradient-primary", "text-white", "border-0", "py-4"], [1, "mb-0"], [1, "bi", 3, "ngClass"], [1, "card-body", "p-4"], [1, "row", "g-3", 3, "ngSubmit"], [1, "col-12", "mb-3"], ["for", "name", 1, "form-label", "fw-medium", "text-[#4f5fad]", "dark:text-[#00f7ff]"], [1, "text-[#ff6b69]", "dark:text-[#ff3b30]"], [1, "input-group"], [1, "input-group-text", "bg-[#f0f4f8]", "dark:bg-[#0a0a0a]", "border-0"], [1, "bi", "bi-people-fill", "text-[#4f5fad]", "dark:text-[#00f7ff]"], ["type", "text", "id", "name", "required", "", "minlength", "3", "placeholder", "Entrez le nom de l'\u00E9quipe", 1, "form-control", "bg-[#f0f4f8]", "dark:bg-[#0a0a0a]", "border-0", "text-[#6d6870]", "dark:text-[#e0e0e0]", "placeholder-[#6d6870]/50", "dark:placeholder-[#a0a0a0]", 3, "value", "input"], ["nameInput", ""], ["class", "invalid-feedback d-block small mt-1", 4, "ngIf"], ["class", "text-danger small mt-1", 4, "ngIf"], ["for", "description", 1, "form-label", "fw-medium", "text-[#4f5fad]", "dark:text-[#00f7ff]"], [1, "input-group-text", "bg-[#f0f4f8]", "dark:bg-[#0a0a0a]", "border-0", "align-self-start"], [1, "bi", "bi-card-text", "text-[#4f5fad]", "dark:text-[#00f7ff]"], ["id", "description", "rows", "4", "required", "", "minlength", "10", "placeholder", "D\u00E9crivez l'objectif et les activit\u00E9s de cette \u00E9quipe", 1, "form-control", "bg-[#f0f4f8]", "dark:bg-[#0a0a0a]", "border-0", "text-[#6d6870]", "dark:text-[#e0e0e0]", "placeholder-[#6d6870]/50", "dark:placeholder-[#a0a0a0]", 3, "value", "input"], ["descInput", ""], ["type", "hidden", 3, "value"], [1, "alert", "alert-info", "border-0", "rounded-3", "shadow-sm", "d-flex", "align-items-center"], [1, "bi", "bi-info-circle-fill", "fs-4", "me-3", "text-primary"], ["class", "col-12 mt-4", 4, "ngIf"], [1, "col-12", "mt-4"], [1, "d-flex", "gap-3", "justify-content-between"], ["type", "button", 1, "btn", "btn-outline-secondary", "rounded-pill", "px-4", "py-2", 3, "click"], [1, "bi", "bi-arrow-left", "me-2"], ["type", "button", "class", "btn btn-outline-danger rounded-pill px-4 py-2 ms-2", 3, "click", 4, "ngIf"], ["type", "submit", 1, "btn", "btn-primary", "rounded-pill", "px-4", "py-2", "shadow-sm", 3, "disabled"], ["class", "spinner-border spinner-border-sm me-2", 4, "ngIf"], ["class", "bi", 3, "ngClass", 4, "ngIf"], [1, "invalid-feedback", "d-block", "small", "mt-1"], [1, "bi", "bi-exclamation-triangle-fill", "me-1"], [1, "text-danger", "small", "mt-1"], [1, "bi", "bi-exclamation-circle-fill", "me-1"], [1, "card", "border-0", "shadow-sm", "rounded-3", "mb-4"], [1, "card-header", "bg-light", "border-0", "py-3"], [1, "mb-0", "d-flex", "align-items-center"], [1, "bi", "bi-people-fill", "text-primary", "me-2"], [4, "ngIf", "ngIfElse"], ["noMembers", ""], [1, "mt-4"], [1, "d-flex", "align-items-center", "mb-3"], [1, "bi", "bi-person-plus-fill", "text-primary", "me-2"], ["class", "alert alert-info border-0 rounded-3 shadow-sm d-flex align-items-center", 4, "ngIf"], ["class", "card border-0 bg-light rounded-3 mb-3", 4, "ngIf"], [1, "table-responsive"], [1, "table", "table-hover", "align-middle"], [1, "table-light"], [1, "d-flex", "align-items-center"], [1, "bi", "bi-person", "text-primary", "me-2"], [1, "bi", "bi-envelope", "text-primary", "me-2"], [1, "bi", "bi-briefcase", "text-primary", "me-2"], [1, "text-center"], ["class", "transition hover-row", 4, "ngFor", "ngForOf"], [1, "transition", "hover-row"], [1, "fw-medium"], ["class", "text-muted d-block", 4, "ngIf"], ["class", "text-decoration-none", 3, "href", 4, "ngIf"], ["class", "text-muted fst-italic", 4, "ngIf"], [1, "badge", "rounded-pill", "px-3", "py-2", "shadow-sm", 3, "ngClass"], ["type", "button", "title", "Retirer de l'\u00E9quipe", 1, "btn", "btn-sm", "btn-outline-danger", "rounded-circle", 3, "click"], [1, "bi", "bi-trash"], [1, "text-muted", "d-block"], [1, "text-decoration-none", 3, "href"], [1, "text-muted", "fst-italic"], [1, "text-center", "py-4"], [1, "bi", "bi-people", "fs-1", "text-muted", "mb-3", "d-block"], [1, "text-muted"], [1, "text-[#6d6870]", "dark:text-[#e0e0e0]"], [1, "card", "border-0", "bg-light", "rounded-3", "mb-3"], [1, "row", "g-3"], [1, "col-md-6"], ["for", "userSelect", 1, "form-label", "fw-medium", "text-[#4f5fad]", "dark:text-[#00f7ff]"], ["id", "userSelect", 1, "form-select", "border-0", "shadow-sm", "bg-[#f0f4f8]", "dark:bg-[#0a0a0a]", "text-[#6d6870]", "dark:text-[#e0e0e0]"], ["userSelect", ""], ["value", "", "selected", "", "disabled", ""], [3, "value", 4, "ngFor", "ngForOf"], [1, "col-md-4"], ["for", "roleSelect", 1, "form-label", "fw-medium", "text-[#4f5fad]", "dark:text-[#00f7ff]"], ["id", "roleSelect", 1, "form-select", "border-0", "shadow-sm", "bg-[#f0f4f8]", "dark:bg-[#0a0a0a]", "text-[#6d6870]", "dark:text-[#e0e0e0]"], ["roleSelect", ""], ["value", "membre", "selected", ""], ["value", "admin"], [1, "col-md-2", "d-flex", "align-items-end"], ["type", "button", "id", "addMembreButton", 1, "btn", "btn-primary", "rounded-pill", "w-100", "shadow-sm", 3, "disabled", "click"], [1, "bi", "bi-plus-circle", "me-1"], [3, "value"], ["type", "button", 1, "btn", "btn-outline-danger", "rounded-pill", "px-4", "py-2", "ms-2", 3, "click"], [1, "bi", "bi-trash", "me-2"], [1, "spinner-border", "spinner-border-sm", "me-2"]],
      template: function EquipeFormComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 0)(1, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](2, "div", 2)(3, "div", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](4, "div", 4)(5, "div", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](6, "div", 6)(7, "div", 6)(8, "div", 6)(9, "div", 6)(10, "div", 6)(11, "div", 6)(12, "div", 6)(13, "div", 6)(14, "div", 6)(15, "div", 6)(16, "div", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](17, "div", 7)(18, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](19, "div", 9)(20, "div", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](21, "div", 11)(22, "div", 12)(23, "div", 13)(24, "h1", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](25);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](26, "p", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](27);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](28, "button", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function EquipeFormComponent_Template_button_click_28_listener() {
            return ctx.cancel();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](29, "i", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](30, " Retour \u00E0 la liste ");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](31, EquipeFormComponent_div_31_Template, 6, 0, "div", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](32, EquipeFormComponent_div_32_Template, 10, 1, "div", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](33, EquipeFormComponent_div_33_Template, 52, 23, "div", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](25);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", ctx.isEditMode ? "Modifier l'\u00E9quipe" : "Nouvelle \u00E9quipe", " ");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", ctx.isEditMode ? "Modifiez les informations et les membres de votre \u00E9quipe" : "Cr\u00E9ez une nouvelle \u00E9quipe pour organiser vos projets et membres", " ");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.loading);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.error);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", !ctx.loading);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_6__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_6__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_6__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_7__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_7__.NgSelectOption, _angular_forms__WEBPACK_IMPORTED_MODULE_7__["ɵNgSelectMultipleOption"], _angular_forms__WEBPACK_IMPORTED_MODULE_7__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_7__.NgForm],
      styles: [".cursor-pointer[_ngcontent-%COMP%] {\n      cursor: pointer;\n    }\n    summary[_ngcontent-%COMP%]:hover {\n      text-decoration: underline;\n    }\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImVxdWlwZS1mb3JtLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkdBQUc7TUFDRyxlQUFlO0lBQ2pCO0lBQ0E7TUFDRSwwQkFBMEI7SUFDNUIiLCJmaWxlIjoiZXF1aXBlLWZvcm0uY29tcG9uZW50LmNzcyIsInNvdXJjZXNDb250ZW50IjpbIiAgIC5jdXJzb3ItcG9pbnRlciB7XHJcbiAgICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgIH1cclxuICAgIHN1bW1hcnk6aG92ZXIge1xyXG4gICAgICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTtcclxuICAgIH0iXX0= */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvZXF1aXBlcy9lcXVpcGUtZm9ybS9lcXVpcGUtZm9ybS5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJHQUFHO01BQ0csZUFBZTtJQUNqQjtJQUNBO01BQ0UsMEJBQTBCO0lBQzVCO0FBQ0osd2NBQXdjIiwic291cmNlc0NvbnRlbnQiOlsiICAgLmN1cnNvci1wb2ludGVyIHtcclxuICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgfVxyXG4gICAgc3VtbWFyeTpob3ZlciB7XHJcbiAgICAgIHRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lO1xyXG4gICAgfSJdLCJzb3VyY2VSb290IjoiIn0= */", "\n\n  .bg-gradient-primary[_ngcontent-%COMP%] {\n    background: linear-gradient(45deg, #4f5fad, #7826b5) !important;\n  }\n\n  \n\n  .transition[_ngcontent-%COMP%] {\n    transition: all 0.3s ease;\n  }\n\n  .hover-row[_ngcontent-%COMP%]:hover {\n    background-color: rgba(79, 95, 173, 0.05) !important;\n    transform: translateY(-2px);\n    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\n  }\n\n  \n\n  .form-control[_ngcontent-%COMP%]:focus, .form-select[_ngcontent-%COMP%]:focus {\n    border-color: #4f5fad !important;\n    box-shadow: 0 0 0 0.25rem rgba(79, 95, 173, 0.25) !important;\n  }\n\n  \n\n  .dark[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus, .dark[_ngcontent-%COMP%]   .form-select[_ngcontent-%COMP%]:focus {\n    border-color: #00f7ff !important;\n    box-shadow: 0 0 0 0.25rem rgba(0, 247, 255, 0.25) !important;\n  }\n\n  \n\n  .btn[_ngcontent-%COMP%] {\n    transition: all 0.3s ease;\n  }\n\n  .btn[_ngcontent-%COMP%]:hover {\n    transform: translateY(-2px);\n  }\n\n  \n\n  .dark[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder, .dark[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]::placeholder {\n    color: rgba(160, 160, 160, 0.8) !important;\n  }\n\n  \n\n  .dark[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%] {\n    color: #e0e0e0 !important;\n  }\n\n  .dark[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\n    color: #00f7ff !important;\n  }\n\n  \n\n  .dark[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%] {\n    background-color: #1a1a1a !important;\n    border-color: rgba(0, 247, 255, 0.2) !important;\n  }\n\n  .dark[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\n    background: linear-gradient(45deg, #00f7ff, #4f5fad) !important;\n  }\n\n  .dark[_ngcontent-%COMP%]   .bg-light[_ngcontent-%COMP%] {\n    background-color: #0a0a0a !important;\n  }"]
    });
  }
}

/***/ }),

/***/ 7530:
/*!******************************************************************************!*\
  !*** ./src/app/views/front/equipes/equipe-layout/equipe-layout.component.ts ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   EquipeLayoutComponent: () => (/* binding */ EquipeLayoutComponent)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rxjs */ 3942);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rxjs/operators */ 1567);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var src_app_services_equipe_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/services/equipe.service */ 8133);







class EquipeLayoutComponent {
  constructor(router, location, equipeService) {
    this.router = router;
    this.location = location;
    this.equipeService = equipeService;
    this.sidebarVisible$ = new rxjs__WEBPACK_IMPORTED_MODULE_1__.Observable();
    // Page properties
    this.pageTitle = 'Gestion des Équipes';
    this.pageSubtitle = 'Organisez et gérez vos équipes de projet';
    // Statistics
    this.totalEquipes = 0;
    this.totalMembres = 0;
    this.totalProjets = 0;
  }
  ngOnInit() {
    this.loadStatistics();
    this.updatePageTitle();
    // Listen to route changes to update page title
    this.router.events.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.filter)(event => event instanceof _angular_router__WEBPACK_IMPORTED_MODULE_3__.NavigationEnd)).subscribe(() => {
      this.updatePageTitle();
    });
  }
  loadStatistics() {
    // Load teams statistics
    this.equipeService.getEquipes().subscribe({
      next: equipes => {
        this.totalEquipes = equipes.length;
        // Calculate total members across all teams
        this.totalMembres = equipes.reduce((total, equipe) => {
          return total + (equipe.members ? equipe.members.length : 0);
        }, 0);
        // For now, set projects to 0 (can be updated when project service is available)
        this.totalProjets = 0;
      },
      error: error => {
        console.error('Erreur lors du chargement des statistiques:', error);
      }
    });
  }
  updatePageTitle() {
    const url = this.router.url;
    if (url.includes('/equipes/liste')) {
      this.pageTitle = 'Liste des Équipes';
      this.pageSubtitle = 'Consultez et gérez toutes vos équipes';
    } else if (url.includes('/equipes/nouveau')) {
      this.pageTitle = 'Créer une Équipe';
      this.pageSubtitle = 'Créez une nouvelle équipe pour vos projets';
    } else if (url.includes('/equipes/mes-equipes')) {
      this.pageTitle = 'Mes Équipes';
      this.pageSubtitle = 'Équipes dont vous êtes membre ou administrateur';
    } else if (url.includes('/equipes/detail')) {
      this.pageTitle = "Détails de l'Équipe";
      this.pageSubtitle = 'Informations et gestion des membres';
    } else if (url.includes('/equipes/edit')) {
      this.pageTitle = "Modifier l'Équipe";
      this.pageSubtitle = 'Modifiez les informations de votre équipe';
    } else {
      this.pageTitle = 'Gestion des Équipes';
      this.pageSubtitle = 'Organisez et gérez vos équipes de projet';
    }
  }
  goBack() {
    this.location.back();
  }
  static {
    this.ɵfac = function EquipeLayoutComponent_Factory(t) {
      return new (t || EquipeLayoutComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_3__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_common__WEBPACK_IMPORTED_MODULE_5__.Location), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](src_app_services_equipe_service__WEBPACK_IMPORTED_MODULE_0__.EquipeService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineComponent"]({
      type: EquipeLayoutComponent,
      selectors: [["app-equipe-layout"]],
      decls: 92,
      vars: 0,
      consts: [[1, "min-h-screen", "bg-[#f0f4f8]", "dark:bg-[#0a0a0a]", "relative", "overflow-hidden"], [1, "absolute", "inset-0", "overflow-hidden", "pointer-events-none"], [1, "absolute", "top-[15%]", "left-[10%]", "w-64", "h-64", "rounded-full", "bg-gradient-to-br", "from-[#4f5fad]/5", "to-transparent", "dark:from-[#00f7ff]/3", "dark:to-transparent", "blur-3xl"], [1, "absolute", "bottom-[20%]", "right-[10%]", "w-80", "h-80", "rounded-full", "bg-gradient-to-tl", "from-[#4f5fad]/5", "to-transparent", "dark:from-[#00f7ff]/3", "dark:to-transparent", "blur-3xl"], [1, "absolute", "inset-0", "opacity-5", "dark:opacity-[0.03]"], [1, "h-full", "grid", "grid-cols-12"], [1, "border-r", "border-[#4f5fad]", "dark:border-[#00f7ff]"], [1, "flex", "h-screen", "relative", "z-10"], [1, "w-80", "bg-white", "dark:bg-[#1a1a1a]", "shadow-xl", "dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]", "border-r", "border-[#4f5fad]/20", "dark:border-[#00f7ff]/20", "flex", "flex-col"], [1, "p-6", "border-b", "border-[#4f5fad]/20", "dark:border-[#00f7ff]/20"], [1, "flex", "items-center", "space-x-3"], [1, "w-10", "h-10", "bg-gradient-to-r", "from-[#4f5fad]", "to-[#7826b5]", "dark:from-[#00f7ff]", "dark:to-[#4f5fad]", "rounded-xl", "flex", "items-center", "justify-center"], [1, "fas", "fa-users", "text-white", "text-lg"], [1, "text-xl", "font-bold", "text-[#4f5fad]", "dark:text-[#00f7ff]", "tracking-wide"], [1, "text-sm", "text-[#6d6870]", "dark:text-[#e0e0e0]"], [1, "flex-1", "p-4", "space-y-2"], ["routerLink", "/equipes/liste", "routerLinkActive", "active bg-gradient-to-r from-[#4f5fad]/10 to-[#7826b5]/10 dark:from-[#00f7ff]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#00f7ff] font-medium", 1, "group", "flex", "items-center", "px-4", "py-3", "text-sm", "font-medium", "rounded-xl", "text-[#6d6870]", "dark:text-[#e0e0e0]", "hover:bg-[#4f5fad]/10", "dark:hover:bg-[#00f7ff]/10", "hover:text-[#4f5fad]", "dark:hover:text-[#00f7ff]", "transition-all", "duration-300"], [1, "relative"], [1, "fas", "fa-list-ul", "w-5", "h-5", "mr-3", "group-hover:scale-110", "transition-transform"], [1, "absolute", "inset-0", "bg-[#4f5fad]/20", "dark:bg-[#00f7ff]/20", "opacity-0", "group-hover:opacity-100", "transition-opacity", "blur-md", "rounded-full"], ["routerLink", "/equipes/nouveau", "routerLinkActive", "active bg-gradient-to-r from-[#4f5fad]/10 to-[#7826b5]/10 dark:from-[#00f7ff]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#00f7ff] font-medium", 1, "group", "flex", "items-center", "px-4", "py-3", "text-sm", "font-medium", "rounded-xl", "text-[#6d6870]", "dark:text-[#e0e0e0]", "hover:bg-[#4f5fad]/10", "dark:hover:bg-[#00f7ff]/10", "hover:text-[#4f5fad]", "dark:hover:text-[#00f7ff]", "transition-all", "duration-300"], [1, "fas", "fa-plus-circle", "w-5", "h-5", "mr-3", "group-hover:scale-110", "transition-transform"], ["routerLink", "/equipes/mes-equipes", "routerLinkActive", "active bg-gradient-to-r from-[#4f5fad]/10 to-[#7826b5]/10 dark:from-[#00f7ff]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#00f7ff] font-medium", 1, "group", "flex", "items-center", "px-4", "py-3", "text-sm", "font-medium", "rounded-xl", "text-[#6d6870]", "dark:text-[#e0e0e0]", "hover:bg-[#4f5fad]/10", "dark:hover:bg-[#00f7ff]/10", "hover:text-[#4f5fad]", "dark:hover:text-[#00f7ff]", "transition-all", "duration-300"], [1, "fas", "fa-user-friends", "w-5", "h-5", "mr-3", "group-hover:scale-110", "transition-transform"], [1, "my-4", "border-t", "border-[#4f5fad]/20", "dark:border-[#00f7ff]/20"], [1, "px-4", "py-3"], [1, "text-xs", "font-semibold", "text-[#6d6870]", "dark:text-[#a0a0a0]", "uppercase", "tracking-wider", "mb-3"], [1, "space-y-3"], [1, "flex", "items-center", "justify-between"], [1, "text-sm", "font-medium", "text-[#4f5fad]", "dark:text-[#00f7ff]"], [1, "text-sm", "font-medium", "text-[#00ff9d]"], [1, "text-sm", "font-medium", "text-[#ff6b69]", "dark:text-[#ff3b30]"], [1, "p-4", "border-t", "border-[#4f5fad]/20", "dark:border-[#00f7ff]/20"], ["onclick", "history.back()", 1, "w-full", "bg-[#6d6870]/20", "dark:bg-[#a0a0a0]/20", "text-[#6d6870]", "dark:text-[#e0e0e0]", "px-4", "py-3", "rounded-xl", "font-medium", "transition-all", "duration-300", "hover:scale-105", "hover:bg-[#6d6870]/30", "dark:hover:bg-[#a0a0a0]/30", "flex", "items-center", "justify-center"], [1, "fas", "fa-arrow-left", "mr-2"], [1, "flex-1", "flex", "flex-col", "overflow-hidden"], [1, "bg-white", "dark:bg-[#1a1a1a]", "shadow-md", "dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]", "border-b", "border-[#4f5fad]/20", "dark:border-[#00f7ff]/20", "px-6", "py-4"], [1, "flex", "items-center", "space-x-4"], [1, "w-8", "h-8", "bg-gradient-to-r", "from-[#4f5fad]", "to-[#7826b5]", "dark:from-[#00f7ff]", "dark:to-[#4f5fad]", "rounded-lg", "flex", "items-center", "justify-center"], [1, "fas", "fa-users", "text-white", "text-sm"], [1, "text-xl", "font-bold", "text-[#4f5fad]", "dark:text-[#00f7ff]"], ["type", "text", "placeholder", "Rechercher...", 1, "w-64", "pl-10", "pr-4", "py-2", "bg-[#f0f4f8]", "dark:bg-[#0a0a0a]", "border", "border-[#4f5fad]/20", "dark:border-[#00f7ff]/20", "rounded-lg", "text-[#6d6870]", "dark:text-[#e0e0e0]", "placeholder-[#6d6870]/50", "dark:placeholder-[#a0a0a0]", "focus:outline-none", "focus:ring-2", "focus:ring-[#4f5fad]", "dark:focus:ring-[#00f7ff]", "focus:border-transparent", "transition-all"], [1, "absolute", "inset-y-0", "left-0", "pl-3", "flex", "items-center", "pointer-events-none"], [1, "fas", "fa-search", "text-[#6d6870]", "dark:text-[#a0a0a0]"], ["routerLink", "/equipes/nouveau", 1, "bg-gradient-to-r", "from-[#4f5fad]", "to-[#7826b5]", "dark:from-[#00f7ff]", "dark:to-[#4f5fad]", "text-white", "px-4", "py-2", "rounded-lg", "font-medium", "transition-all", "duration-300", "hover:scale-105", "shadow-lg", "hover:shadow-[0_0_25px_rgba(79,95,173,0.4)]", "dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)]", "flex", "items-center"], [1, "fas", "fa-plus", "mr-2"], [1, "flex-1", "overflow-auto", "p-6"]],
      template: function EquipeLayoutComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 0)(1, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](2, "div", 2)(3, "div", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](4, "div", 4)(5, "div", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](6, "div", 6)(7, "div", 6)(8, "div", 6)(9, "div", 6)(10, "div", 6)(11, "div", 6)(12, "div", 6)(13, "div", 6)(14, "div", 6)(15, "div", 6)(16, "div", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](17, "div", 7)(18, "div", 8)(19, "div", 9)(20, "div", 10)(21, "div", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](22, "i", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](23, "div")(24, "h1", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](25, " \u00C9quipes ");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](26, "p", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](27, " Gestion collaborative ");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](28, "nav", 15)(29, "a", 16)(30, "div", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](31, "i", 18)(32, "div", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](33, "span", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](34, "Liste des \u00E9quipes");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](35, "a", 20)(36, "div", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](37, "i", 21)(38, "div", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](39, "span", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](40, "Cr\u00E9er une \u00E9quipe");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](41, "a", 22)(42, "div", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](43, "i", 23)(44, "div", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](45, "span", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](46, "Mes \u00E9quipes");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](47, "div", 24);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](48, "div", 25)(49, "h3", 26);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](50, " Statistiques ");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](51, "div", 27)(52, "div", 28)(53, "span", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](54, "\u00C9quipes cr\u00E9\u00E9es");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](55, "span", 29);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](56, "0");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](57, "div", 28)(58, "span", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](59, "Membres actifs");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](60, "span", 30);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](61, "0");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](62, "div", 28)(63, "span", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](64, "Projets en cours");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](65, "span", 31);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](66, "0");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](67, "div", 32)(68, "button", 33);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](69, "i", 34);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](70, " Retour \u00E0 l'accueil ");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](71, "div", 35)(72, "header", 36)(73, "div", 28)(74, "div", 37)(75, "div", 38);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](76, "i", 39);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](77, "div")(78, "h2", 40);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](79, " Gestion des \u00C9quipes ");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](80, "p", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](81, " Organisez et g\u00E9rez vos \u00E9quipes de projet ");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](82, "div", 10)(83, "div", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](84, "input", 41);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](85, "div", 42);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](86, "i", 43);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](87, "button", 44);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](88, "i", 45);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](89, " Nouvelle \u00E9quipe ");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](90, "main", 46);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](91, "router-outlet");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()();
        }
      },
      dependencies: [_angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterOutlet, _angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterLink, _angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterLinkActive],
      styles: ["\n\n\n\n\n@keyframes _ngcontent-%COMP%_fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes _ngcontent-%COMP%_slideIn {\n  from {\n    opacity: 0;\n    transform: translateX(-20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n@keyframes _ngcontent-%COMP%_glow {\n  0%,\n  100% {\n    box-shadow: 0 0 5px rgba(79, 95, 173, 0.3);\n  }\n  50% {\n    box-shadow: 0 0 20px rgba(79, 95, 173, 0.6);\n  }\n}\n\n@keyframes _ngcontent-%COMP%_pulse {\n  0%,\n  100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.05);\n  }\n}\n\n\n\n.equipe-layout[_ngcontent-%COMP%] {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #f0f4f8 0%, #e8f2ff 100%);\n  position: relative;\n  overflow: hidden;\n}\n\n.dark[_ngcontent-%COMP%]   .equipe-layout[_ngcontent-%COMP%] {\n  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);\n}\n\n\n\n.sidebar[_ngcontent-%COMP%] {\n  width: 320px;\n  background: rgba(255, 255, 255, 0.95);\n  -webkit-backdrop-filter: blur(20px);\n          backdrop-filter: blur(20px);\n  border-right: 1px solid rgba(79, 95, 173, 0.2);\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  animation: _ngcontent-%COMP%_slideIn 0.5s ease-out;\n}\n\n.dark[_ngcontent-%COMP%]   .sidebar[_ngcontent-%COMP%] {\n  background: rgba(26, 26, 26, 0.95);\n  border-right: 1px solid rgba(0, 247, 255, 0.2);\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\n}\n\n\n\n.nav-item[_ngcontent-%COMP%] {\n  position: relative;\n  overflow: hidden;\n  border-radius: 12px;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.nav-item[_ngcontent-%COMP%]:hover {\n  transform: translateX(4px);\n  box-shadow: 0 4px 12px rgba(79, 95, 173, 0.2);\n}\n\n.dark[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]:hover {\n  box-shadow: 0 4px 12px rgba(0, 247, 255, 0.2);\n}\n\n.nav-item.active[_ngcontent-%COMP%] {\n  background: linear-gradient(\n    135deg,\n    rgba(79, 95, 173, 0.1) 0%,\n    rgba(120, 38, 181, 0.1) 100%\n  );\n  border-left: 3px solid #4f5fad;\n}\n\n.dark[_ngcontent-%COMP%]   .nav-item.active[_ngcontent-%COMP%] {\n  background: linear-gradient(\n    135deg,\n    rgba(0, 247, 255, 0.2) 0%,\n    rgba(79, 95, 173, 0.2) 100%\n  );\n  border-left: 3px solid #00f7ff;\n}\n\n\n\n.nav-icon[_ngcontent-%COMP%] {\n  position: relative;\n  transition: all 0.3s ease;\n}\n\n.nav-item[_ngcontent-%COMP%]:hover   .nav-icon[_ngcontent-%COMP%] {\n  transform: scale(1.1);\n  filter: drop-shadow(0 0 8px rgba(79, 95, 173, 0.5));\n}\n\n.dark[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]:hover   .nav-icon[_ngcontent-%COMP%] {\n  filter: drop-shadow(0 0 8px rgba(0, 247, 255, 0.5));\n}\n\n\n\n.stats-section[_ngcontent-%COMP%] {\n  background: linear-gradient(\n    135deg,\n    rgba(79, 95, 173, 0.05) 0%,\n    rgba(120, 38, 181, 0.05) 100%\n  );\n  border-radius: 12px;\n  padding: 16px;\n  margin: 16px 0;\n  border: 1px solid rgba(79, 95, 173, 0.1);\n}\n\n.dark[_ngcontent-%COMP%]   .stats-section[_ngcontent-%COMP%] {\n  background: linear-gradient(\n    135deg,\n    rgba(0, 247, 255, 0.1) 0%,\n    rgba(79, 95, 173, 0.1) 100%\n  );\n  border: 1px solid rgba(0, 247, 255, 0.2);\n}\n\n.stat-item[_ngcontent-%COMP%] {\n  padding: 8px 0;\n  border-bottom: 1px solid rgba(79, 95, 173, 0.1);\n  transition: all 0.3s ease;\n}\n\n.stat-item[_ngcontent-%COMP%]:last-child {\n  border-bottom: none;\n}\n\n.stat-item[_ngcontent-%COMP%]:hover {\n  background: rgba(79, 95, 173, 0.05);\n  border-radius: 8px;\n  padding-left: 8px;\n}\n\n.dark[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]:hover {\n  background: rgba(0, 247, 255, 0.1);\n}\n\n\n\n.main-content[_ngcontent-%COMP%] {\n  flex: 1;\n  background: rgba(255, 255, 255, 0.8);\n  -webkit-backdrop-filter: blur(20px);\n          backdrop-filter: blur(20px);\n  border-radius: 20px 0 0 20px;\n  margin: 20px 0 20px 0;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n  animation: _ngcontent-%COMP%_fadeIn 0.6s ease-out;\n}\n\n.dark[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%] {\n  background: rgba(26, 26, 26, 0.8);\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);\n}\n\n\n\n.header[_ngcontent-%COMP%] {\n  background: linear-gradient(\n    135deg,\n    rgba(255, 255, 255, 0.9) 0%,\n    rgba(240, 244, 248, 0.9) 100%\n  );\n  -webkit-backdrop-filter: blur(20px);\n          backdrop-filter: blur(20px);\n  border-bottom: 1px solid rgba(79, 95, 173, 0.2);\n}\n\n.dark[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%] {\n  background: linear-gradient(\n    135deg,\n    rgba(26, 26, 26, 0.9) 0%,\n    rgba(10, 10, 10, 0.9) 100%\n  );\n  border-bottom: 1px solid rgba(0, 247, 255, 0.2);\n}\n\n\n\n.search-input[_ngcontent-%COMP%] {\n  background: rgba(240, 244, 248, 0.8);\n  border: 1px solid rgba(79, 95, 173, 0.2);\n  border-radius: 12px;\n  padding: 12px 16px 12px 40px;\n  transition: all 0.3s ease;\n}\n\n.search-input[_ngcontent-%COMP%]:focus {\n  background: rgba(255, 255, 255, 0.9);\n  border-color: #4f5fad;\n  box-shadow: 0 0 0 3px rgba(79, 95, 173, 0.1);\n  transform: scale(1.02);\n}\n\n.dark[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%] {\n  background: rgba(10, 10, 10, 0.8);\n  border: 1px solid rgba(0, 247, 255, 0.2);\n}\n\n.dark[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%]:focus {\n  background: rgba(26, 26, 26, 0.9);\n  border-color: #00f7ff;\n  box-shadow: 0 0 0 3px rgba(0, 247, 255, 0.1);\n}\n\n\n\n.btn-primary[_ngcontent-%COMP%] {\n  background: linear-gradient(135deg, #4f5fad 0%, #7826b5 100%);\n  border: none;\n  border-radius: 12px;\n  padding: 12px 24px;\n  color: white;\n  font-weight: 600;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  position: relative;\n  overflow: hidden;\n}\n\n.btn-primary[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(79, 95, 173, 0.4);\n}\n\n.dark[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%] {\n  background: linear-gradient(135deg, #00f7ff 0%, #4f5fad 100%);\n}\n\n.dark[_ngcontent-%COMP%]   .btn-primary[_ngcontent-%COMP%]:hover {\n  box-shadow: 0 8px 25px rgba(0, 247, 255, 0.4);\n}\n\n.btn-secondary[_ngcontent-%COMP%] {\n  background: rgba(109, 104, 112, 0.2);\n  border: 1px solid rgba(109, 104, 112, 0.3);\n  border-radius: 12px;\n  padding: 12px 24px;\n  color: #6d6870;\n  font-weight: 500;\n  transition: all 0.3s ease;\n}\n\n.btn-secondary[_ngcontent-%COMP%]:hover {\n  background: rgba(109, 104, 112, 0.3);\n  transform: translateY(-1px);\n}\n\n.dark[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%] {\n  background: rgba(160, 160, 160, 0.2);\n  border: 1px solid rgba(160, 160, 160, 0.3);\n  color: #e0e0e0;\n}\n\n.dark[_ngcontent-%COMP%]   .btn-secondary[_ngcontent-%COMP%]:hover {\n  background: rgba(160, 160, 160, 0.3);\n}\n\n/*# sourceMappingURL=data:application/json;base64,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 */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 2390:
/*!**************************************************************************!*\
  !*** ./src/app/views/front/equipes/equipe-list/equipe-list.component.ts ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   EquipeListComponent: () => (/* binding */ EquipeListComponent)
/* harmony export */ });
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rxjs/operators */ 9475);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var src_app_services_equipe_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/services/equipe.service */ 8133);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var src_app_services_notification_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/services/notification.service */ 7473);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/common */ 316);






function EquipeListComponent_div_31_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 22)(1, "div", 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](2, "div", 24)(3, "div", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](4, "p", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](5, " Chargement des \u00E9quipes... ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
  }
}
function EquipeListComponent_div_32_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 27)(1, "div", 28)(2, "div", 29)(3, "div", 30);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](4, "i", 31);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](5, "div", 32)(6, "h3", 33);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](7, " Erreur de chargement des \u00E9quipes ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](8, "p", 34);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](10, "button", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function EquipeListComponent_div_32_Template_button_click_10_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r5);
      const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r4.loadEquipes());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](11, "i", 36);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](12, " R\u00E9essayer ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()()();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"](" ", ctx_r1.error, " ");
  }
}
function EquipeListComponent_div_33_Template(rf, ctx) {
  if (rf & 1) {
    const _r7 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 37)(1, "div", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](2, "i", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](3, "h3", 40);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](4, " Aucune \u00E9quipe trouv\u00E9e ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](5, "p", 41);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](6, " Commencez par cr\u00E9er une nouvelle \u00E9quipe pour organiser vos projets et membres ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](7, "button", 42);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function EquipeListComponent_div_33_Template_button_click_7_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r7);
      const ctx_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r6.navigateToAddEquipe());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](8, "i", 43);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](9, " Cr\u00E9er une \u00E9quipe ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
  }
}
function EquipeListComponent_div_34_div_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r11 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 46)(1, "div", 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](2, "div", 9)(3, "div", 47);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](4, "div", 48)(5, "div", 49)(6, "div", 32)(7, "h3", 50);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](9, "div", 51)(10, "span", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](11, "i", 53);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](12);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](13, "div", 54);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](14, "i", 55);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](15, "p", 56);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](16);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpipe"](17, "slice");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](18, "div", 57)(19, "div", 58)(20, "button", 59);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function EquipeListComponent_div_34_div_1_Template_button_click_20_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r11);
      const equipe_r9 = restoredCtx.$implicit;
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](equipe_r9._id && ctx_r10.navigateToEquipeDetail(equipe_r9._id));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](21, "i", 60);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](22, " D\u00E9tails ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](23, "div", 61)(24, "button", 62);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function EquipeListComponent_div_34_div_1_Template_button_click_24_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r11);
      const equipe_r9 = restoredCtx.$implicit;
      const ctx_r12 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](equipe_r9._id && ctx_r12.navigateToEditEquipe(equipe_r9._id));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](25, "i", 63);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](26, "button", 64);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function EquipeListComponent_div_34_div_1_Template_button_click_26_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r11);
      const equipe_r9 = restoredCtx.$implicit;
      const ctx_r13 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](equipe_r9._id && ctx_r13.navigateToTasks(equipe_r9._id));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](27, "i", 65);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](28, "button", 66);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function EquipeListComponent_div_34_div_1_Template_button_click_28_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r11);
      const equipe_r9 = restoredCtx.$implicit;
      const ctx_r14 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](equipe_r9._id && ctx_r14.deleteEquipe(equipe_r9._id));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](29, "i", 67);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()()();
  }
  if (rf & 2) {
    const equipe_r9 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"](" ", equipe_r9.name, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"](" ", (equipe_r9.members == null ? null : equipe_r9.members.length) || 0, " membre(s) ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"](" ", equipe_r9.description && equipe_r9.description.length > 80 ? _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵpipeBind3"](17, 3, equipe_r9.description, 0, 80) + "..." : equipe_r9.description || "Aucune description disponible", " ");
  }
}
function EquipeListComponent_div_34_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 44);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](1, EquipeListComponent_div_34_div_1_Template, 30, 7, "div", 45);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngForOf", ctx_r3.equipes);
  }
}
class EquipeListComponent {
  constructor(equipeService, router, notificationService) {
    this.equipeService = equipeService;
    this.router = router;
    this.notificationService = notificationService;
    this.equipes = [];
    this.loading = false;
    this.error = null;
  }
  ngOnInit() {
    this.loadEquipes();
  }
  loadEquipes() {
    this.loading = true;
    this.error = null;
    this.equipeService.getEquipes().pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_3__.finalize)(() => this.loading = false)).subscribe({
      next: data => {
        console.log('Équipes chargées:', data);
        this.equipes = data;
        // Trier les équipes par nom
        this.equipes.sort((a, b) => {
          if (a.name && b.name) {
            return a.name.localeCompare(b.name);
          }
          return 0;
        });
      },
      error: error => {
        console.error('Erreur lors du chargement des équipes:', error);
        this.error = 'Impossible de charger les équipes. Veuillez réessayer plus tard.';
        this.notificationService.showError('Erreur lors du chargement des équipes');
      }
    });
  }
  navigateToAddEquipe() {
    this.router.navigate(['/equipes/ajouter']);
  }
  navigateToEditEquipe(id) {
    this.router.navigate(['/equipes/modifier', id]);
  }
  navigateToEquipeDetail(id) {
    this.router.navigate(['/equipes/detail', id]);
  }
  deleteEquipe(id) {
    if (!id) {
      console.error('ID est indéfini');
      this.notificationService.showError('ID d\'équipe invalide');
      return;
    }
    // Trouver le nom de l'équipe pour l'afficher dans le message de confirmation
    const equipe = this.equipes.find(e => e._id === id);
    const equipeName = equipe?.name || 'cette équipe';
    if (confirm(`Êtes-vous sûr de vouloir supprimer l'équipe "${equipeName}" ?`)) {
      this.loading = true;
      this.equipeService.deleteEquipe(id).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_3__.finalize)(() => this.loading = false)).subscribe({
        next: () => {
          console.log('Équipe supprimée avec succès');
          this.notificationService.showSuccess(`L'équipe "${equipeName}" a été supprimée avec succès`);
          this.loadEquipes();
        },
        error: error => {
          console.error('Erreur lors de la suppression de l\'équipe:', error);
          this.error = 'Impossible de supprimer l\'équipe. Veuillez réessayer plus tard.';
          this.notificationService.showError(`Erreur lors de la suppression de l'équipe "${equipeName}"`);
        }
      });
    }
  }
  navigateToTasks(id) {
    if (!id) {
      console.error('ID est indéfini');
      this.notificationService.showError('ID d\'équipe invalide');
      return;
    }
    const equipe = this.equipes.find(e => e._id === id);
    const equipeName = equipe?.name || 'cette équipe';
    // Naviguer vers la page des tâches de l'équipe
    this.router.navigate(['/tasks', id]);
  }
  static {
    this.ɵfac = function EquipeListComponent_Factory(t) {
      return new (t || EquipeListComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](src_app_services_equipe_service__WEBPACK_IMPORTED_MODULE_0__.EquipeService), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_4__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](src_app_services_notification_service__WEBPACK_IMPORTED_MODULE_1__.NotificationService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineComponent"]({
      type: EquipeListComponent,
      selectors: [["app-equipe-list"]],
      decls: 35,
      vars: 4,
      consts: [[1, "min-h-screen", "bg-[#f0f4f8]", "dark:bg-[#0a0a0a]", "relative", "overflow-hidden"], [1, "absolute", "inset-0", "overflow-hidden", "pointer-events-none"], [1, "absolute", "top-[15%]", "left-[10%]", "w-64", "h-64", "rounded-full", "bg-gradient-to-br", "from-[#4f5fad]/5", "to-transparent", "dark:from-[#00f7ff]/3", "dark:to-transparent", "blur-3xl"], [1, "absolute", "bottom-[20%]", "right-[10%]", "w-80", "h-80", "rounded-full", "bg-gradient-to-tl", "from-[#4f5fad]/5", "to-transparent", "dark:from-[#00f7ff]/3", "dark:to-transparent", "blur-3xl"], [1, "absolute", "inset-0", "opacity-5", "dark:opacity-[0.03]"], [1, "h-full", "grid", "grid-cols-12"], [1, "border-r", "border-[#4f5fad]", "dark:border-[#00f7ff]"], [1, "max-w-7xl", "mx-auto", "p-6", "relative", "z-10"], [1, "mb-8", "relative"], [1, "absolute", "top-0", "left-0", "right-0", "h-1", "bg-gradient-to-r", "from-[#4f5fad]", "to-[#7826b5]", "dark:from-[#00f7ff]", "dark:to-[#4f5fad]"], [1, "absolute", "top-0", "left-0", "right-0", "h-1", "bg-gradient-to-r", "from-[#4f5fad]", "to-[#7826b5]", "dark:from-[#00f7ff]", "dark:to-[#4f5fad]", "blur-md"], [1, "bg-white", "dark:bg-[#1a1a1a]", "rounded-xl", "shadow-lg", "dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]", "p-6", "backdrop-blur-sm", "border", "border-[#4f5fad]/20", "dark:border-[#00f7ff]/20"], [1, "flex", "flex-col", "lg:flex-row", "lg:items-center", "lg:justify-between"], [1, "mb-4", "lg:mb-0"], [1, "text-3xl", "font-bold", "text-[#4f5fad]", "dark:text-[#00f7ff]", "mb-2", "tracking-wide"], [1, "text-[#6d6870]", "dark:text-[#e0e0e0]", "text-sm"], [1, "relative", "overflow-hidden", "group", "bg-gradient-to-r", "from-[#4f5fad]", "to-[#7826b5]", "dark:from-[#00f7ff]", "dark:to-[#4f5fad]", "text-white", "px-6", "py-3", "rounded-xl", "font-medium", "transition-all", "duration-300", "hover:scale-105", "shadow-lg", "hover:shadow-[0_0_25px_rgba(79,95,173,0.4)]", "dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)]", 3, "click"], [1, "fas", "fa-plus", "mr-2", "group-hover:rotate-90", "transition-transform", "duration-300"], ["class", "flex flex-col items-center justify-center py-16", 4, "ngIf"], ["class", "mb-6", 4, "ngIf"], ["class", "text-center py-16", 4, "ngIf"], ["class", "grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6", 4, "ngIf"], [1, "flex", "flex-col", "items-center", "justify-center", "py-16"], [1, "relative"], [1, "w-12", "h-12", "border-3", "border-[#4f5fad]/20", "dark:border-[#00f7ff]/20", "border-t-[#4f5fad]", "dark:border-t-[#00f7ff]", "rounded-full", "animate-spin"], [1, "absolute", "inset-0", "bg-[#4f5fad]/20", "dark:bg-[#00f7ff]/20", "blur-xl", "rounded-full", "transform", "scale-150", "-z-10"], [1, "mt-4", "text-[#4f5fad]", "dark:text-[#00f7ff]", "text-sm", "font-medium", "tracking-wide"], [1, "mb-6"], [1, "bg-[#ff6b69]/10", "dark:bg-[#ff3b30]/10", "border-l-4", "border-[#ff6b69]", "dark:border-[#ff3b30]", "rounded-lg", "p-4", "backdrop-blur-sm"], [1, "flex", "items-start"], [1, "text-[#ff6b69]", "dark:text-[#ff3b30]", "mr-3", "text-xl"], [1, "fas", "fa-exclamation-triangle"], [1, "flex-1"], [1, "font-semibold", "text-[#ff6b69]", "dark:text-[#ff3b30]", "mb-1"], [1, "text-sm", "text-[#6d6870]", "dark:text-[#e0e0e0]"], [1, "mt-3", "bg-[#ff6b69]/20", "dark:bg-[#ff3b30]/20", "text-[#ff6b69]", "dark:text-[#ff3b30]", "px-3", "py-1.5", "rounded-lg", "text-sm", "font-medium", "hover:bg-[#ff6b69]/30", "dark:hover:bg-[#ff3b30]/30", "transition-colors", 3, "click"], [1, "fas", "fa-sync-alt", "mr-1.5"], [1, "text-center", "py-16"], [1, "w-20", "h-20", "mx-auto", "mb-6", "text-[#4f5fad]", "dark:text-[#00f7ff]", "opacity-70"], [1, "fas", "fa-users", "text-5xl"], [1, "text-xl", "font-semibold", "text-[#4f5fad]", "dark:text-[#00f7ff]", "mb-2"], [1, "text-[#6d6870]", "dark:text-[#e0e0e0]", "text-sm", "mb-6"], [1, "bg-gradient-to-r", "from-[#4f5fad]", "to-[#7826b5]", "dark:from-[#00f7ff]", "dark:to-[#4f5fad]", "text-white", "px-6", "py-3", "rounded-xl", "font-medium", "transition-all", "duration-300", "hover:scale-105", "shadow-lg", "hover:shadow-[0_0_25px_rgba(79,95,173,0.4)]", "dark:hover:shadow-[0_0_25px_rgba(0,247,255,0.4)]", 3, "click"], [1, "fas", "fa-plus-circle", "mr-2"], [1, "grid", "grid-cols-1", "md:grid-cols-2", "xl:grid-cols-3", "gap-6"], ["class", "group bg-white dark:bg-[#1a1a1a] rounded-xl shadow-lg dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)] overflow-hidden hover:shadow-xl dark:hover:shadow-[0_12px_40px_rgba(0,0,0,0.4)] transition-all duration-300 hover:-translate-y-2 border border-[#4f5fad]/20 dark:border-[#00f7ff]/20 backdrop-blur-sm", 4, "ngFor", "ngForOf"], [1, "group", "bg-white", "dark:bg-[#1a1a1a]", "rounded-xl", "shadow-lg", "dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]", "overflow-hidden", "hover:shadow-xl", "dark:hover:shadow-[0_12px_40px_rgba(0,0,0,0.4)]", "transition-all", "duration-300", "hover:-translate-y-2", "border", "border-[#4f5fad]/20", "dark:border-[#00f7ff]/20", "backdrop-blur-sm"], [1, "absolute", "top-0", "left-0", "right-0", "h-1", "bg-gradient-to-r", "from-[#4f5fad]", "to-[#7826b5]", "dark:from-[#00f7ff]", "dark:to-[#4f5fad]", "opacity-0", "group-hover:opacity-100", "blur-md", "transition-opacity", "duration-300"], [1, "p-6"], [1, "flex", "items-start", "justify-between", "mb-4"], [1, "text-lg", "font-bold", "text-[#4f5fad]", "dark:text-[#00f7ff]", "mb-2", "group-hover:scale-[1.02]", "transition-transform", "duration-300", "origin-left"], [1, "flex", "items-center", "text-xs"], [1, "bg-[#4f5fad]/10", "dark:bg-[#00f7ff]/10", "text-[#4f5fad]", "dark:text-[#00f7ff]", "px-2", "py-1", "rounded-full", "font-medium"], [1, "fas", "fa-users", "mr-1"], [1, "w-12", "h-12", "bg-gradient-to-br", "from-[#4f5fad]", "to-[#7826b5]", "dark:from-[#00f7ff]", "dark:to-[#4f5fad]", "rounded-full", "flex", "items-center", "justify-center", "shadow-lg"], [1, "fas", "fa-users", "text-white", "text-lg"], [1, "text-sm", "text-[#6d6870]", "dark:text-[#e0e0e0]", "line-clamp-2", "mb-4"], [1, "px-6", "pb-6"], [1, "flex", "items-center", "justify-between", "pt-4", "border-t", "border-[#4f5fad]/10", "dark:border-[#00f7ff]/10"], [1, "text-[#4f5fad]", "dark:text-[#00f7ff]", "hover:text-[#7826b5]", "dark:hover:text-[#4f5fad]", "text-sm", "font-medium", "transition-colors", "flex", "items-center", "group/details", 3, "click"], [1, "fas", "fa-eye", "mr-1", "group-hover/details:scale-110", "transition-transform"], [1, "flex", "items-center", "space-x-2"], ["title", "Modifier l'\u00E9quipe", 1, "p-2", "text-[#6d6870]", "dark:text-[#e0e0e0]", "hover:text-[#4f5fad]", "dark:hover:text-[#00f7ff]", "hover:bg-[#4f5fad]/10", "dark:hover:bg-[#00f7ff]/10", "rounded-lg", "transition-all", 3, "click"], [1, "fas", "fa-edit"], ["title", "G\u00E9rer les t\u00E2ches", 1, "p-2", "text-[#6d6870]", "dark:text-[#e0e0e0]", "hover:text-[#00ff9d]", "hover:bg-[#00ff9d]/10", "rounded-lg", "transition-all", 3, "click"], [1, "fas", "fa-tasks"], ["title", "Supprimer l'\u00E9quipe", 1, "p-2", "text-[#6d6870]", "dark:text-[#e0e0e0]", "hover:text-[#ff6b69]", "dark:hover:text-[#ff3b30]", "hover:bg-[#ff6b69]/10", "dark:hover:bg-[#ff3b30]/10", "rounded-lg", "transition-all", 3, "click"], [1, "fas", "fa-trash"]],
      template: function EquipeListComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 0)(1, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](2, "div", 2)(3, "div", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](4, "div", 4)(5, "div", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](6, "div", 6)(7, "div", 6)(8, "div", 6)(9, "div", 6)(10, "div", 6)(11, "div", 6)(12, "div", 6)(13, "div", 6)(14, "div", 6)(15, "div", 6)(16, "div", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](17, "div", 7)(18, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](19, "div", 9)(20, "div", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](21, "div", 11)(22, "div", 12)(23, "div", 13)(24, "h1", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](25, " \u00C9quipes ");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](26, "p", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](27, " G\u00E9rez vos \u00E9quipes et leurs membres avec style futuriste ");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](28, "button", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function EquipeListComponent_Template_button_click_28_listener() {
            return ctx.navigateToAddEquipe();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](29, "i", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](30, " Nouvelle \u00C9quipe ");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](31, EquipeListComponent_div_31_Template, 6, 0, "div", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](32, EquipeListComponent_div_32_Template, 13, 1, "div", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](33, EquipeListComponent_div_33_Template, 10, 0, "div", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](34, EquipeListComponent_div_34_Template, 2, 1, "div", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](31);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.loading);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.error);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", !ctx.loading && !ctx.error && ctx.equipes.length === 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", !ctx.loading && ctx.equipes.length > 0);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_5__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_5__.NgIf, _angular_common__WEBPACK_IMPORTED_MODULE_5__.SlicePipe],
      styles: [".hover-shadow[_ngcontent-%COMP%]:hover {\n    transform: translateY(-5px);\n    box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;\n  }\n\n  .transition[_ngcontent-%COMP%] {\n    transition: all 0.3s ease;\n  }\n\n  .card-header.bg-primary[_ngcontent-%COMP%] {\n    background: linear-gradient(45deg, #007bff, #6610f2) !important;\n  }\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImVxdWlwZS1saXN0LmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkVBQUU7SUFDRSwyQkFBMkI7SUFDM0Isa0RBQWtEO0VBQ3BEOztFQUVBO0lBQ0UseUJBQXlCO0VBQzNCOztFQUVBO0lBQ0UsK0RBQStEO0VBQ2pFIiwiZmlsZSI6ImVxdWlwZS1saXN0LmNvbXBvbmVudC5jc3MiLCJzb3VyY2VzQ29udGVudCI6WyIgIC5ob3Zlci1zaGFkb3c6aG92ZXIge1xyXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01cHgpO1xyXG4gICAgYm94LXNoYWRvdzogMCAxMHB4IDIwcHggcmdiYSgwLDAsMCwwLjEpICFpbXBvcnRhbnQ7XHJcbiAgfVxyXG5cclxuICAudHJhbnNpdGlvbiB7XHJcbiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xyXG4gIH1cclxuXHJcbiAgLmNhcmQtaGVhZGVyLmJnLXByaW1hcnkge1xyXG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDQ1ZGVnLCAjMDA3YmZmLCAjNjYxMGYyKSAhaW1wb3J0YW50O1xyXG4gIH0iXX0= */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvZXF1aXBlcy9lcXVpcGUtbGlzdC9lcXVpcGUtbGlzdC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJFQUFFO0lBQ0UsMkJBQTJCO0lBQzNCLGtEQUFrRDtFQUNwRDs7RUFFQTtJQUNFLHlCQUF5QjtFQUMzQjs7RUFFQTtJQUNFLCtEQUErRDtFQUNqRTtBQUNGLDR1QkFBNHVCIiwic291cmNlc0NvbnRlbnQiOlsiICAuaG92ZXItc2hhZG93OmhvdmVyIHtcclxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNXB4KTtcclxuICAgIGJveC1zaGFkb3c6IDAgMTBweCAyMHB4IHJnYmEoMCwwLDAsMC4xKSAhaW1wb3J0YW50O1xyXG4gIH1cclxuXHJcbiAgLnRyYW5zaXRpb24ge1xyXG4gICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxuICB9XHJcblxyXG4gIC5jYXJkLWhlYWRlci5iZy1wcmltYXJ5IHtcclxuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg0NWRlZywgIzAwN2JmZiwgIzY2MTBmMikgIWltcG9ydGFudDtcclxuICB9Il0sInNvdXJjZVJvb3QiOiIifQ== */"]
    });
  }
}

/***/ }),

/***/ 138:
/*!****************************************************************!*\
  !*** ./src/app/views/front/equipes/equipe/equipe.component.ts ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   EquipeComponent: () => (/* binding */ EquipeComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var src_app_services_equipe_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/services/equipe.service */ 8133);
/* harmony import */ var src_app_services_membre_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/services/membre.service */ 1622);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/forms */ 4456);





function EquipeComponent_div_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r10 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](2, "button", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function EquipeComponent_div_1_Template_button_click_2_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r10);
      const ctx_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r9.error = "");
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"](" ", ctx_r0.error, " ");
  }
}
function EquipeComponent_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 40)(1, "div", 41)(2, "span", 42);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](3, "Chargement...");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
  }
}
function EquipeComponent_div_11_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 43);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1, " Aucune \u00E9quipe trouv\u00E9e. Cr\u00E9ez votre premi\u00E8re \u00E9quipe ci-dessous. ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
}
function EquipeComponent_div_12_tr_15_Template(rf, ctx) {
  if (rf & 1) {
    const _r14 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "tr")(1, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](3, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](5, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](7, "td");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](9, "td")(10, "button", 47);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function EquipeComponent_div_12_tr_15_Template_button_click_10_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r14);
      const equipe_r12 = restoredCtx.$implicit;
      const ctx_r13 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r13.editEquipe(equipe_r12));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](11, " Modifier ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](12, "button", 48);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function EquipeComponent_div_12_tr_15_Template_button_click_12_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r14);
      const equipe_r12 = restoredCtx.$implicit;
      const ctx_r15 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](equipe_r12._id && ctx_r15.deleteEquipe(equipe_r12._id));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](13, " Supprimer ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](14, "button", 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function EquipeComponent_div_12_tr_15_Template_button_click_14_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r14);
      const equipe_r12 = restoredCtx.$implicit;
      const ctx_r16 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r16.showMembreModal(equipe_r12));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](15, " G\u00E9rer membres ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const equipe_r12 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](equipe_r12.name);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](equipe_r12.description);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](equipe_r12.admin);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"]("", (equipe_r12.members == null ? null : equipe_r12.members.length) || 0, " membres");
  }
}
function EquipeComponent_div_12_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 44)(1, "table", 45)(2, "thead")(3, "tr")(4, "th");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](5, "Nom");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](6, "th");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](7, "Description");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](8, "th");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](9, "Admin");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](10, "th");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](11, "Membres");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](12, "th");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](13, "Actions");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](14, "tbody");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](15, EquipeComponent_div_12_tr_15_Template, 16, 4, "tr", 46);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](15);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngForOf", ctx_r3.equipes);
  }
}
function EquipeComponent_span_39_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](0, "span", 50);
  }
}
function EquipeComponent_button_41_Template(rf, ctx) {
  if (rf & 1) {
    const _r18 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "button", 51);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function EquipeComponent_button_41_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r18);
      const ctx_r17 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r17.cancelEdit());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1, " Annuler ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
}
function EquipeComponent_div_50_div_6_li_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r26 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "li", 61)(1, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](3, "button", 62);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function EquipeComponent_div_50_div_6_li_2_Template_button_click_3_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r26);
      const membreId_r24 = restoredCtx.$implicit;
      const ctx_r25 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](3);
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx_r25.removeMembreFromEquipe(ctx_r25.selectedEquipe._id, membreId_r24));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](4, " Retirer ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const membreId_r24 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate"](membreId_r24);
  }
}
function EquipeComponent_div_50_div_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div")(1, "ul", 59);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](2, EquipeComponent_div_50_div_6_li_2_Template, 5, 1, "li", 60);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r19 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngForOf", ctx_r19.selectedEquipe.members);
  }
}
function EquipeComponent_div_50_ng_template_7_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "p", 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](1, "Aucun membre dans cette \u00E9quipe");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
  }
}
function EquipeComponent_div_50_Template(rf, ctx) {
  if (rf & 1) {
    const _r28 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div")(1, "h6");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](3, "div", 14)(4, "h6");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](5, "Membres actuels:");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](6, EquipeComponent_div_50_div_6_Template, 3, 1, "div", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](7, EquipeComponent_div_50_ng_template_7_Template, 2, 0, "ng-template", null, 53, _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplateRefExtractor"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](9, "div", 14)(10, "h6");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](11, "Ajouter un membre:");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](12, "div", 54);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](13, "input", 55, 56);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](15, "button", 57);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function EquipeComponent_div_50_Template_button_click_15_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r28);
      const _r22 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵreference"](14);
      const ctx_r27 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
      ctx_r27.addMembreToEquipe(ctx_r27.selectedEquipe._id, _r22.value);
      return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](_r22.value = "");
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](16, " Ajouter ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](17, "small", 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](18, "Entrez l'ID du membre \u00E0 ajouter \u00E0 l'\u00E9quipe");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](19, "div", 58)(20, "p", 12)(21, "strong");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](22, "Note:");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](23, " Pour ajouter un membre, vous devez d'abord cr\u00E9er le membre dans la section des membres. ");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const _r20 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵreference"](8);
    const _r22 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵreference"](14);
    const ctx_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"]("\u00C9quipe: ", ctx_r8.selectedEquipe.name, "");
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx_r8.selectedEquipe.members && ctx_r8.selectedEquipe.members.length > 0)("ngIfElse", _r20);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("disabled", !ctx_r8.selectedEquipe || !ctx_r8.selectedEquipe._id || !_r22.value);
  }
}
class EquipeComponent {
  constructor(equipeService, membreService) {
    this.equipeService = equipeService;
    this.membreService = membreService;
    this.equipes = [];
    this.newEquipe = {
      name: '',
      description: ''
    };
    this.selectedEquipe = null;
    this.isEditing = false;
    this.membres = [];
    this.loading = false;
    this.error = '';
  }
  ngOnInit() {
    this.loadEquipes();
    this.loadMembres();
  }
  loadEquipes() {
    this.loading = true;
    this.equipeService.getEquipes().subscribe({
      next: data => {
        console.log('Loaded equipes:', data);
        this.equipes = data;
        this.loading = false;
      },
      error: error => {
        console.error('Error loading equipes:', error);
        this.error = 'Erreur lors du chargement des équipes: ' + error.message;
        this.loading = false;
      }
    });
  }
  loadMembres() {
    this.loading = true;
    this.membreService.getMembres().subscribe({
      next: data => {
        console.log('Loaded membres:', data);
        this.membres = data;
        this.loading = false;
      },
      error: error => {
        console.error('Error loading membres:', error);
        this.error = 'Erreur lors du chargement des membres: ' + error.message;
        this.loading = false;
      }
    });
  }
  addEquipe() {
    console.log('Adding equipe:', this.newEquipe);
    if (!this.newEquipe.name) {
      console.error('Team name is required');
      this.error = 'Le nom de l\'équipe est requis';
      return;
    }
    this.loading = true;
    this.error = '';
    this.equipeService.addEquipe(this.newEquipe).subscribe({
      next: response => {
        console.log('Equipe added successfully:', response);
        this.loadEquipes();
        this.newEquipe = {
          name: '',
          description: ''
        }; // Clear input
        this.loading = false;
        // Afficher un message de succès temporaire
        const successMessage = 'Équipe créée avec succès!';
        this.error = ''; // Effacer les erreurs précédentes
        alert(successMessage);
      },
      error: error => {
        console.error('Error adding equipe:', error);
        this.error = 'Erreur lors de la création de l\'équipe: ' + (error.error?.message || error.message || 'Unknown error');
        this.loading = false;
      }
    });
  }
  editEquipe(equipe) {
    this.isEditing = true;
    // Créer une copie profonde pour éviter de modifier l'objet original
    this.newEquipe = {
      _id: equipe._id,
      name: equipe.name || '',
      description: equipe.description || '',
      admin: equipe.admin,
      members: equipe.members ? [...equipe.members] : []
    };
  }
  cancelEdit() {
    this.isEditing = false;
    this.newEquipe = {
      name: '',
      description: ''
    };
    this.error = ''; // Effacer les erreurs
  }

  updateSelectedEquipe() {
    if (!this.newEquipe.name) {
      console.error('Team name is required');
      this.error = 'Le nom de l\'équipe est requis';
      return;
    }
    if (this.newEquipe._id) {
      this.loading = true;
      this.error = '';
      this.equipeService.updateEquipe(this.newEquipe._id, this.newEquipe).subscribe({
        next: updatedEquipe => {
          console.log('Team updated successfully:', updatedEquipe);
          this.loadEquipes();
          this.isEditing = false;
          this.newEquipe = {
            name: '',
            description: ''
          };
          this.loading = false;
          // Afficher un message de succès temporaire
          const successMessage = 'Équipe mise à jour avec succès!';
          alert(successMessage);
        },
        error: error => {
          console.error('Error updating team:', error);
          this.error = 'Erreur lors de la mise à jour de l\'équipe: ' + (error.error?.message || error.message || 'Unknown error');
          this.loading = false;
        }
      });
    } else {
      this.error = 'ID de l\'équipe manquant pour la mise à jour';
    }
  }
  deleteEquipe(id) {
    if (!id) {
      console.error('ID is undefined');
      this.error = 'ID de l\'équipe non défini';
      return;
    }
    if (confirm('Êtes-vous sûr de vouloir supprimer cette équipe? Cette action est irréversible.')) {
      this.loading = true;
      this.error = '';
      this.equipeService.deleteEquipe(id).subscribe({
        next: response => {
          console.log('Team deleted successfully:', response);
          // Si l'équipe en cours d'édition est celle qui vient d'être supprimée, réinitialiser le formulaire
          if (this.isEditing && this.newEquipe._id === id) {
            this.isEditing = false;
            this.newEquipe = {
              name: '',
              description: ''
            };
          }
          this.loadEquipes();
          this.loading = false;
          // Afficher un message de succès
          alert('Équipe supprimée avec succès');
        },
        error: error => {
          console.error('Error deleting team:', error);
          this.error = 'Erreur lors de la suppression de l\'équipe: ' + (error.error?.message || error.message || 'Unknown error');
          this.loading = false;
        }
      });
    }
  }
  showMembreModal(equipe) {
    this.selectedEquipe = equipe;
    // Ouvrir le modal avec Bootstrap 5
    const modalRef = document.getElementById('membreModal');
    if (modalRef) {
      try {
        // Ensure Bootstrap is properly loaded
        if (typeof window !== 'undefined' && window.bootstrap) {
          const modal = new window.bootstrap.Modal(modalRef);
          modal.show();
        } else {
          console.error('Bootstrap is not loaded properly');
          alert('Erreur: Bootstrap n\'est pas chargé correctement');
        }
      } catch (error) {
        console.error('Error showing modal:', error);
      }
    } else {
      console.error('Modal element not found');
    }
  }
  addMembreToEquipe(teamId, membreId) {
    if (!teamId) {
      console.error('Team ID is undefined');
      alert('ID de l\'équipe non défini');
      return;
    }
    if (!membreId || membreId.trim() === '') {
      console.error('Member ID is empty');
      alert('L\'ID du membre est requis');
      return;
    }
    this.loading = true;
    // Create a proper Membre object that matches what the API expects
    const membre = {
      id: membreId
    };
    this.equipeService.addMembreToEquipe(teamId, membre).subscribe({
      next: response => {
        console.log('Member added successfully:', response);
        this.loadEquipes();
        this.loading = false;
        // Afficher un message de succès
        alert('Membre ajouté avec succès à l\'équipe');
      },
      error: error => {
        console.error('Error adding member:', error);
        this.error = 'Erreur lors de l\'ajout du membre: ' + (error.error?.message || error.message || 'Unknown error');
        alert(this.error);
        this.loading = false;
      }
    });
  }
  removeMembreFromEquipe(teamId, membreId) {
    if (!teamId) {
      console.error('Team ID is undefined');
      alert('ID de l\'équipe non défini');
      return;
    }
    if (!membreId) {
      console.error('Member ID is undefined');
      alert('ID du membre non défini');
      return;
    }
    if (confirm('Êtes-vous sûr de vouloir retirer ce membre de l\'équipe?')) {
      this.loading = true;
      this.equipeService.removeMembreFromEquipe(teamId, membreId).subscribe({
        next: response => {
          console.log('Member removed successfully:', response);
          this.loadEquipes();
          this.loading = false;
          // Si l'équipe sélectionnée est celle dont on vient de retirer un membre, mettre à jour l'équipe sélectionnée
          if (this.selectedEquipe && this.selectedEquipe._id === teamId) {
            const updatedEquipe = this.equipes.find(e => e._id === teamId);
            if (updatedEquipe) {
              this.selectedEquipe = updatedEquipe;
            }
          }
        },
        error: error => {
          console.error('Error removing member:', error);
          this.error = 'Erreur lors de la suppression du membre: ' + (error.error?.message || error.message || 'Unknown error');
          alert(this.error);
          this.loading = false;
        }
      });
    }
  }
  static {
    this.ɵfac = function EquipeComponent_Factory(t) {
      return new (t || EquipeComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](src_app_services_equipe_service__WEBPACK_IMPORTED_MODULE_0__.EquipeService), _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdirectiveInject"](src_app_services_membre_service__WEBPACK_IMPORTED_MODULE_1__.MembreService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineComponent"]({
      type: EquipeComponent,
      selectors: [["app-equipe"]],
      decls: 54,
      vars: 14,
      consts: [[1, "container", "mt-4"], ["class", "alert alert-danger alert-dismissible fade show", "role", "alert", 4, "ngIf"], ["class", "d-flex justify-content-center mb-4", 4, "ngIf"], [1, "row", "mb-4"], [1, "col-12"], [1, "d-flex", "justify-content-between", "align-items-center", "mb-3"], [1, "btn", "btn-sm", "btn-outline-primary", 3, "click"], [1, "bi", "bi-arrow-clockwise"], ["class", "alert alert-info", 4, "ngIf"], ["class", "table-responsive", 4, "ngIf"], [1, "card"], [1, "card-header", "bg-primary", "text-white"], [1, "mb-0"], [1, "card-body"], [1, "mb-3"], ["for", "name", 1, "form-label"], [1, "text-danger"], ["type", "text", "id", "name", "required", "", "placeholder", "Entrez le nom de l'\u00E9quipe", 1, "form-control", 3, "value", "input"], ["nameInput", ""], [1, "invalid-feedback"], ["for", "description", 1, "form-label"], ["id", "description", "rows", "3", "placeholder", "Entrez une description pour cette \u00E9quipe", 1, "form-control", 3, "value", "input"], ["descInput", ""], [1, "text-muted"], [1, "d-flex"], ["type", "button", 1, "btn", "btn-primary", 3, "disabled", "click"], ["class", "spinner-border spinner-border-sm me-1", "role", "status", "aria-hidden", "true", 4, "ngIf"], ["type", "button", "class", "btn btn-secondary ms-2", 3, "click", 4, "ngIf"], ["id", "membreModal", "tabindex", "-1", "aria-hidden", "true", 1, "modal", "fade"], [1, "modal-dialog"], [1, "modal-content"], [1, "modal-header"], [1, "modal-title"], ["type", "button", "data-bs-dismiss", "modal", "aria-label", "Close", 1, "btn-close"], [1, "modal-body"], [4, "ngIf"], [1, "modal-footer"], ["type", "button", "data-bs-dismiss", "modal", 1, "btn", "btn-secondary"], ["role", "alert", 1, "alert", "alert-danger", "alert-dismissible", "fade", "show"], ["type", "button", "aria-label", "Close", 1, "btn-close", 3, "click"], [1, "d-flex", "justify-content-center", "mb-4"], ["role", "status", 1, "spinner-border", "text-primary"], [1, "visually-hidden"], [1, "alert", "alert-info"], [1, "table-responsive"], [1, "table", "table-striped"], [4, "ngFor", "ngForOf"], [1, "btn", "btn-sm", "btn-info", "me-2", 3, "click"], [1, "btn", "btn-sm", "btn-danger", "me-2", 3, "click"], [1, "btn", "btn-sm", "btn-primary", 3, "click"], ["role", "status", "aria-hidden", "true", 1, "spinner-border", "spinner-border-sm", "me-1"], ["type", "button", 1, "btn", "btn-secondary", "ms-2", 3, "click"], [4, "ngIf", "ngIfElse"], ["noMembers", ""], [1, "input-group", "mb-2"], ["type", "text", "placeholder", "ID du membre", 1, "form-control"], ["membreIdInput", ""], [1, "btn", "btn-primary", 3, "disabled", "click"], [1, "alert", "alert-info", "mt-3"], [1, "list-group"], ["class", "list-group-item d-flex justify-content-between align-items-center", 4, "ngFor", "ngForOf"], [1, "list-group-item", "d-flex", "justify-content-between", "align-items-center"], [1, "btn", "btn-sm", "btn-danger", 3, "click"]],
      template: function EquipeComponent_Template(rf, ctx) {
        if (rf & 1) {
          const _r29 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵgetCurrentView"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](0, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](1, EquipeComponent_div_1_Template, 3, 1, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](2, EquipeComponent_div_2_Template, 4, 0, "div", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](3, "div", 3)(4, "div", 4)(5, "div", 5)(6, "h2");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](7, "Liste des \u00E9quipes");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](8, "button", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function EquipeComponent_Template_button_click_8_listener() {
            return ctx.loadEquipes();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](9, "i", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](10, " Rafra\u00EEchir ");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](11, EquipeComponent_div_11_Template, 2, 0, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](12, EquipeComponent_div_12_Template, 16, 1, "div", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](13, "div", 3)(14, "div", 4)(15, "div", 10)(16, "div", 11)(17, "h3", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](18);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](19, "div", 13)(20, "form")(21, "div", 14)(22, "label", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](23, "Nom de l'\u00E9quipe ");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](24, "span", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](25, "*");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](26, "input", 17, 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("input", function EquipeComponent_Template_input_input_26_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r29);
            const _r4 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵreference"](27);
            return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx.newEquipe.name = _r4.value);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](28, "div", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](29, "Le nom de l'\u00E9quipe est requis");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](30, "div", 14)(31, "label", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](32, "Description");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](33, "textarea", 21, 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("input", function EquipeComponent_Template_textarea_input_33_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵrestoreView"](_r29);
            const _r5 = _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵreference"](34);
            return _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵresetView"](ctx.newEquipe.description = _r5.value);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](35, "small", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](36, "Une br\u00E8ve description de l'\u00E9quipe et de son objectif");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](37, "div", 24)(38, "button", 25);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵlistener"]("click", function EquipeComponent_Template_button_click_38_listener() {
            return ctx.isEditing ? ctx.updateSelectedEquipe() : ctx.addEquipe();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](39, EquipeComponent_span_39_Template, 1, 0, "span", 26);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](40);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](41, EquipeComponent_button_41_Template, 2, 0, "button", 27);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](42, "div", 28)(43, "div", 29)(44, "div", 30)(45, "div", 31)(46, "h5", 32);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](47, "G\u00E9rer les membres de l'\u00E9quipe");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelement"](48, "button", 33);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](49, "div", 34);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtemplate"](50, EquipeComponent_div_50_Template, 24, 4, "div", 35);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementStart"](51, "div", 36)(52, "button", 37);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtext"](53, " Fermer ");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵelementEnd"]()()()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.error);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.loading);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](9);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.equipes.length === 0 && !ctx.loading);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.equipes.length > 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"](" ", ctx.isEditing ? "Modifier une \u00E9quipe" : "Cr\u00E9er une \u00E9quipe", " ");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](8);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵclassProp"]("is-invalid", !ctx.newEquipe.name && (ctx.isEditing || ctx.newEquipe.name === ""));
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("value", ctx.newEquipe.name);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](7);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("value", ctx.newEquipe.description || "");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("disabled", !ctx.newEquipe.name || ctx.loading);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.loading);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵtextInterpolate1"](" ", ctx.isEditing ? "Mettre \u00E0 jour" : "Cr\u00E9er", " ");
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.isEditing);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵadvance"](9);
          _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵproperty"]("ngIf", ctx.selectedEquipe);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_3__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_3__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_4__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_4__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.NgForm],
      styles: ["\n\n.container[_ngcontent-%COMP%] {\n  max-width: 1200px;\n}\n\n.card[_ngcontent-%COMP%] {\n  border: none;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  border-radius: 8px;\n}\n\n.card-header[_ngcontent-%COMP%] {\n  border-radius: 8px 8px 0 0 !important;\n}\n\n.table[_ngcontent-%COMP%] {\n  margin-bottom: 0;\n}\n\n.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\n  border-top: none;\n  font-weight: 600;\n  color: #495057;\n}\n\n.btn-sm[_ngcontent-%COMP%] {\n  padding: 0.25rem 0.5rem;\n  font-size: 0.875rem;\n}\n\n.spinner-border-sm[_ngcontent-%COMP%] {\n  width: 1rem;\n  height: 1rem;\n}\n\n.alert[_ngcontent-%COMP%] {\n  border: none;\n  border-radius: 6px;\n}\n\n.modal-content[_ngcontent-%COMP%] {\n  border: none;\n  border-radius: 8px;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\n}\n\n.list-group-item[_ngcontent-%COMP%] {\n  border: 1px solid #e9ecef;\n  border-radius: 4px;\n  margin-bottom: 0.5rem;\n}\n\n.list-group-item[_ngcontent-%COMP%]:last-child {\n  margin-bottom: 0;\n}\n\n.form-control[_ngcontent-%COMP%]:focus {\n  border-color: #80bdff;\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n\n.btn[_ngcontent-%COMP%]:focus {\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n\n.text-muted[_ngcontent-%COMP%] {\n  color: #6c757d !important;\n}\n\n/*# sourceMappingURL=data:application/json;base64,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 */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 485:
/*!***************************************************************!*\
  !*** ./src/app/views/front/equipes/equipes-routing.module.ts ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   EquipesRoutingModule: () => (/* binding */ EquipesRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _equipe_list_equipe_list_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./equipe-list/equipe-list.component */ 2390);
/* harmony import */ var _equipe_form_equipe_form_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./equipe-form/equipe-form.component */ 5458);
/* harmony import */ var _equipe_detail_equipe_detail_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./equipe-detail/equipe-detail.component */ 6334);
/* harmony import */ var _task_list_task_list_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./task-list/task-list.component */ 5522);
/* harmony import */ var _equipe_equipe_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./equipe/equipe.component */ 138);
/* harmony import */ var _equipe_layout_equipe_layout_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./equipe-layout/equipe-layout.component */ 7530);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/core */ 7580);









const routes = [{
  path: '',
  component: _equipe_layout_equipe_layout_component__WEBPACK_IMPORTED_MODULE_5__.EquipeLayoutComponent,
  children: [
  // Liste des équipes
  {
    path: '',
    component: _equipe_equipe_component__WEBPACK_IMPORTED_MODULE_4__.EquipeComponent
  }, {
    path: 'liste',
    component: _equipe_list_equipe_list_component__WEBPACK_IMPORTED_MODULE_0__.EquipeListComponent
  }, {
    path: 'mes-equipes',
    component: _equipe_list_equipe_list_component__WEBPACK_IMPORTED_MODULE_0__.EquipeListComponent
  },
  // Formulaire pour ajouter une nouvelle équipe
  {
    path: 'ajouter',
    component: _equipe_form_equipe_form_component__WEBPACK_IMPORTED_MODULE_1__.EquipeFormComponent
  }, {
    path: 'nouveau',
    component: _equipe_form_equipe_form_component__WEBPACK_IMPORTED_MODULE_1__.EquipeFormComponent
  },
  // Formulaire pour modifier une équipe existante
  {
    path: 'modifier/:id',
    component: _equipe_form_equipe_form_component__WEBPACK_IMPORTED_MODULE_1__.EquipeFormComponent
  },
  // Détails d'une équipe spécifique
  {
    path: 'detail/:id',
    component: _equipe_detail_equipe_detail_component__WEBPACK_IMPORTED_MODULE_2__.EquipeDetailComponent
  },
  // Gestion des tâches d'une équipe
  {
    path: 'tasks/:id',
    component: _task_list_task_list_component__WEBPACK_IMPORTED_MODULE_3__.TaskListComponent
  }]
}];
class EquipesRoutingModule {
  static {
    this.ɵfac = function EquipesRoutingModule_Factory(t) {
      return new (t || EquipesRoutingModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineNgModule"]({
      type: EquipesRoutingModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineInjector"]({
      imports: [_angular_router__WEBPACK_IMPORTED_MODULE_7__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_7__.RouterModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵsetNgModuleScope"](EquipesRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_7__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_7__.RouterModule]
  });
})();

/***/ }),

/***/ 2012:
/*!*******************************************************!*\
  !*** ./src/app/views/front/equipes/equipes.module.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   EquipesModule: () => (/* binding */ EquipesModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _equipes_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./equipes-routing.module */ 485);
/* harmony import */ var _equipe_list_equipe_list_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./equipe-list/equipe-list.component */ 2390);
/* harmony import */ var _equipe_form_equipe_form_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./equipe-form/equipe-form.component */ 5458);
/* harmony import */ var _equipe_detail_equipe_detail_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./equipe-detail/equipe-detail.component */ 6334);
/* harmony import */ var _task_list_task_list_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./task-list/task-list.component */ 5522);
/* harmony import */ var _ai_chat_ai_chat_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ai-chat/ai-chat.component */ 6014);
/* harmony import */ var _equipe_equipe_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./equipe/equipe.component */ 138);
/* harmony import */ var _notification_notification_component__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./notification/notification.component */ 226);
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/common/http */ 6443);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _angular_cdk_drag_drop__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/cdk/drag-drop */ 854);
/* harmony import */ var _equipe_layout_equipe_layout_component__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./equipe-layout/equipe-layout.component */ 7530);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/core */ 7580);














class EquipesModule {
  static {
    this.ɵfac = function EquipesModule_Factory(t) {
      return new (t || EquipesModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdefineNgModule"]({
      type: EquipesModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵdefineInjector"]({
      imports: [_angular_common__WEBPACK_IMPORTED_MODULE_10__.CommonModule, _equipes_routing_module__WEBPACK_IMPORTED_MODULE_0__.EquipesRoutingModule, _angular_forms__WEBPACK_IMPORTED_MODULE_11__.FormsModule, _angular_cdk_drag_drop__WEBPACK_IMPORTED_MODULE_12__.DragDropModule, _angular_common_http__WEBPACK_IMPORTED_MODULE_13__.HttpClientModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_9__["ɵɵsetNgModuleScope"](EquipesModule, {
    declarations: [_equipe_list_equipe_list_component__WEBPACK_IMPORTED_MODULE_1__.EquipeListComponent, _equipe_form_equipe_form_component__WEBPACK_IMPORTED_MODULE_2__.EquipeFormComponent, _equipe_detail_equipe_detail_component__WEBPACK_IMPORTED_MODULE_3__.EquipeDetailComponent, _task_list_task_list_component__WEBPACK_IMPORTED_MODULE_4__.TaskListComponent, _ai_chat_ai_chat_component__WEBPACK_IMPORTED_MODULE_5__.AiChatComponent, _equipe_equipe_component__WEBPACK_IMPORTED_MODULE_6__.EquipeComponent, _notification_notification_component__WEBPACK_IMPORTED_MODULE_7__.NotificationComponent, _equipe_layout_equipe_layout_component__WEBPACK_IMPORTED_MODULE_8__.EquipeLayoutComponent],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_10__.CommonModule, _equipes_routing_module__WEBPACK_IMPORTED_MODULE_0__.EquipesRoutingModule, _angular_forms__WEBPACK_IMPORTED_MODULE_11__.FormsModule, _angular_cdk_drag_drop__WEBPACK_IMPORTED_MODULE_12__.DragDropModule, _angular_common_http__WEBPACK_IMPORTED_MODULE_13__.HttpClientModule]
  });
})();

/***/ }),

/***/ 226:
/*!****************************************************************************!*\
  !*** ./src/app/views/front/equipes/notification/notification.component.ts ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NotificationComponent: () => (/* binding */ NotificationComponent)
/* harmony export */ });
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rxjs */ 2510);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var src_app_services_notification_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/services/notification.service */ 7473);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/common */ 316);




const _c0 = function (a0, a1, a2, a3) {
  return {
    "bi-check-circle-fill": a0,
    "bi-exclamation-triangle-fill": a1,
    "bi-info-circle-fill": a2,
    "bi-x-circle-fill": a3
  };
};
function NotificationComponent_div_0_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 1)(1, "div", 2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](2, "i", 3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](3, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](5, "button", 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("click", function NotificationComponent_div_0_Template_button_click_5_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵrestoreView"](_r2);
      const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵresetView"](ctx_r1.closeNotification());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngClass", "alert-" + ctx_r0.notification.type);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵpureFunction4"](3, _c0, ctx_r0.notification.type === "success", ctx_r0.notification.type === "warning", ctx_r0.notification.type === "info", ctx_r0.notification.type === "error"));
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate"](ctx_r0.notification.message);
  }
}
class NotificationComponent {
  constructor(notificationService) {
    this.notificationService = notificationService;
    this.notification = null;
    this.subscription = new rxjs__WEBPACK_IMPORTED_MODULE_2__.Subscription();
  }
  ngOnInit() {
    this.subscription = this.notificationService.getNotifications().subscribe(notification => {
      this.notification = notification;
    });
  }
  ngOnDestroy() {
    this.subscription.unsubscribe();
  }
  closeNotification() {
    this.notificationService.clear();
  }
  static {
    this.ɵfac = function NotificationComponent_Factory(t) {
      return new (t || NotificationComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](src_app_services_notification_service__WEBPACK_IMPORTED_MODULE_0__.NotificationService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
      type: NotificationComponent,
      selectors: [["app-notification"]],
      decls: 1,
      vars: 1,
      consts: [["class", "notification-container", 3, "ngClass", 4, "ngIf"], [1, "notification-container", 3, "ngClass"], [1, "notification-content"], [1, "bi", 3, "ngClass"], ["type", "button", 1, "btn-close", 3, "click"]],
      template: function NotificationComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](0, NotificationComponent_div_0_Template, 6, 8, "div", 0);
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.notification);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_3__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_3__.NgIf],
      styles: [".notification-container[_ngcontent-%COMP%] {\n      position: fixed;\n      top: 20px;\n      right: 20px;\n      min-width: 300px;\n      z-index: 9999;\n      padding: 15px;\n      border-radius: 4px;\n      box-shadow: 0 4px 8px rgba(0,0,0,0.1);\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    }\n    .notification-content[_ngcontent-%COMP%] {\n      display: flex;\n      align-items: center;\n      gap: 10px;\n    }\n    .alert-success[_ngcontent-%COMP%] {\n      background-color: #d4edda;\n      border-color: #c3e6cb;\n      color: #155724;\n    }\n    .alert-error[_ngcontent-%COMP%] {\n      background-color: #f8d7da;\n      border-color: #f5c6cb;\n      color: #721c24;\n    }\n    .alert-info[_ngcontent-%COMP%] {\n      background-color: #d1ecf1;\n      border-color: #bee5eb;\n      color: #0c5460;\n    }\n    .alert-warning[_ngcontent-%COMP%] {\n      background-color: #fff3cd;\n      border-color: #ffeeba;\n      color: #856404;\n    }\n  \n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIm5vdGlmaWNhdGlvbi5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtJQUNJO01BQ0UsZUFBZTtNQUNmLFNBQVM7TUFDVCxXQUFXO01BQ1gsZ0JBQWdCO01BQ2hCLGFBQWE7TUFDYixhQUFhO01BQ2Isa0JBQWtCO01BQ2xCLHFDQUFxQztNQUNyQyxhQUFhO01BQ2IsOEJBQThCO01BQzlCLG1CQUFtQjtJQUNyQjtJQUNBO01BQ0UsYUFBYTtNQUNiLG1CQUFtQjtNQUNuQixTQUFTO0lBQ1g7SUFDQTtNQUNFLHlCQUF5QjtNQUN6QixxQkFBcUI7TUFDckIsY0FBYztJQUNoQjtJQUNBO01BQ0UseUJBQXlCO01BQ3pCLHFCQUFxQjtNQUNyQixjQUFjO0lBQ2hCO0lBQ0E7TUFDRSx5QkFBeUI7TUFDekIscUJBQXFCO01BQ3JCLGNBQWM7SUFDaEI7SUFDQTtNQUNFLHlCQUF5QjtNQUN6QixxQkFBcUI7TUFDckIsY0FBYztJQUNoQiIsImZpbGUiOiJub3RpZmljYXRpb24uY29tcG9uZW50LnRzIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgLm5vdGlmaWNhdGlvbi1jb250YWluZXIge1xuICAgICAgcG9zaXRpb246IGZpeGVkO1xuICAgICAgdG9wOiAyMHB4O1xuICAgICAgcmlnaHQ6IDIwcHg7XG4gICAgICBtaW4td2lkdGg6IDMwMHB4O1xuICAgICAgei1pbmRleDogOTk5OTtcbiAgICAgIHBhZGRpbmc6IDE1cHg7XG4gICAgICBib3JkZXItcmFkaXVzOiA0cHg7XG4gICAgICBib3gtc2hhZG93OiAwIDRweCA4cHggcmdiYSgwLDAsMCwwLjEpO1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgfVxuICAgIC5ub3RpZmljYXRpb24tY29udGVudCB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGdhcDogMTBweDtcbiAgICB9XG4gICAgLmFsZXJ0LXN1Y2Nlc3Mge1xuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Q0ZWRkYTtcbiAgICAgIGJvcmRlci1jb2xvcjogI2MzZTZjYjtcbiAgICAgIGNvbG9yOiAjMTU1NzI0O1xuICAgIH1cbiAgICAuYWxlcnQtZXJyb3Ige1xuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZDdkYTtcbiAgICAgIGJvcmRlci1jb2xvcjogI2Y1YzZjYjtcbiAgICAgIGNvbG9yOiAjNzIxYzI0O1xuICAgIH1cbiAgICAuYWxlcnQtaW5mbyB7XG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZDFlY2YxO1xuICAgICAgYm9yZGVyLWNvbG9yOiAjYmVlNWViO1xuICAgICAgY29sb3I6ICMwYzU0NjA7XG4gICAgfVxuICAgIC5hbGVydC13YXJuaW5nIHtcbiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmYzY2Q7XG4gICAgICBib3JkZXItY29sb3I6ICNmZmVlYmE7XG4gICAgICBjb2xvcjogIzg1NjQwNDtcbiAgICB9XG4gICJdfQ== */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvZXF1aXBlcy9ub3RpZmljYXRpb24vbm90aWZpY2F0aW9uLmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0lBQ0k7TUFDRSxlQUFlO01BQ2YsU0FBUztNQUNULFdBQVc7TUFDWCxnQkFBZ0I7TUFDaEIsYUFBYTtNQUNiLGFBQWE7TUFDYixrQkFBa0I7TUFDbEIscUNBQXFDO01BQ3JDLGFBQWE7TUFDYiw4QkFBOEI7TUFDOUIsbUJBQW1CO0lBQ3JCO0lBQ0E7TUFDRSxhQUFhO01BQ2IsbUJBQW1CO01BQ25CLFNBQVM7SUFDWDtJQUNBO01BQ0UseUJBQXlCO01BQ3pCLHFCQUFxQjtNQUNyQixjQUFjO0lBQ2hCO0lBQ0E7TUFDRSx5QkFBeUI7TUFDekIscUJBQXFCO01BQ3JCLGNBQWM7SUFDaEI7SUFDQTtNQUNFLHlCQUF5QjtNQUN6QixxQkFBcUI7TUFDckIsY0FBYztJQUNoQjtJQUNBO01BQ0UseUJBQXlCO01BQ3pCLHFCQUFxQjtNQUNyQixjQUFjO0lBQ2hCOztBQUVKLGc0REFBZzREIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgLm5vdGlmaWNhdGlvbi1jb250YWluZXIge1xuICAgICAgcG9zaXRpb246IGZpeGVkO1xuICAgICAgdG9wOiAyMHB4O1xuICAgICAgcmlnaHQ6IDIwcHg7XG4gICAgICBtaW4td2lkdGg6IDMwMHB4O1xuICAgICAgei1pbmRleDogOTk5OTtcbiAgICAgIHBhZGRpbmc6IDE1cHg7XG4gICAgICBib3JkZXItcmFkaXVzOiA0cHg7XG4gICAgICBib3gtc2hhZG93OiAwIDRweCA4cHggcmdiYSgwLDAsMCwwLjEpO1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgfVxuICAgIC5ub3RpZmljYXRpb24tY29udGVudCB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGdhcDogMTBweDtcbiAgICB9XG4gICAgLmFsZXJ0LXN1Y2Nlc3Mge1xuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Q0ZWRkYTtcbiAgICAgIGJvcmRlci1jb2xvcjogI2MzZTZjYjtcbiAgICAgIGNvbG9yOiAjMTU1NzI0O1xuICAgIH1cbiAgICAuYWxlcnQtZXJyb3Ige1xuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZDdkYTtcbiAgICAgIGJvcmRlci1jb2xvcjogI2Y1YzZjYjtcbiAgICAgIGNvbG9yOiAjNzIxYzI0O1xuICAgIH1cbiAgICAuYWxlcnQtaW5mbyB7XG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZDFlY2YxO1xuICAgICAgYm9yZGVyLWNvbG9yOiAjYmVlNWViO1xuICAgICAgY29sb3I6ICMwYzU0NjA7XG4gICAgfVxuICAgIC5hbGVydC13YXJuaW5nIHtcbiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmYzY2Q7XG4gICAgICBib3JkZXItY29sb3I6ICNmZmVlYmE7XG4gICAgICBjb2xvcjogIzg1NjQwNDtcbiAgICB9XG4gICJdLCJzb3VyY2VSb290IjoiIn0= */"]
    });
  }
}

/***/ }),

/***/ 5522:
/*!**********************************************************************!*\
  !*** ./src/app/views/front/equipes/task-list/task-list.component.ts ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   TaskListComponent: () => (/* binding */ TaskListComponent)
/* harmony export */ });
/* harmony import */ var _angular_cdk_drag_drop__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/cdk/drag-drop */ 854);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rxjs/operators */ 9475);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var src_app_services_task_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/services/task.service */ 2611);
/* harmony import */ var src_app_services_equipe_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/services/equipe.service */ 8133);
/* harmony import */ var src_app_services_authuser_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/services/authuser.service */ 9271);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var src_app_services_notification_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/services/notification.service */ 7473);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/forms */ 4456);











function TaskListComponent_h1_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "h1", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" T\u00E2ches: ", ctx_r0.team.name, " ");
  }
}
function TaskListComponent_div_17_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 18)(1, "div", 19)(2, "div", 20)(3, "span", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](4, "Chargement...");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](5, "div", 22)(6, "span", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](7, "Chargement...");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](8, "div", 20)(9, "span", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](10, "Chargement...");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](11, "p", 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](12, "Chargement des t\u00E2ches...");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
  }
}
function TaskListComponent_div_18_Template(rf, ctx) {
  if (rf & 1) {
    const _r8 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 18)(1, "div", 24)(2, "div", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](3, "i", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](4, "div", 27);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](6, "button", 28);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function TaskListComponent_div_18_Template_button_click_6_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r8);
      const ctx_r7 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r7.teamId && ctx_r7.loadTasks(ctx_r7.teamId));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](7, "i", 29);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](8, " R\u00E9essayer ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", ctx_r2.error, " ");
  }
}
function TaskListComponent_div_19_option_39_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "option", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const user_r10 = ctx.$implicit;
    const ctx_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("value", user_r10._id || user_r10.id);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", ctx_r9.getUserName(user_r10._id || user_r10.id || ""), " ");
  }
}
function TaskListComponent_div_19_Template(rf, ctx) {
  if (rf & 1) {
    const _r12 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 2)(1, "div", 3)(2, "div", 30)(3, "div", 31)(4, "h4", 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](6, "div", 33)(7, "form", 34);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("ngSubmit", function TaskListComponent_div_19_Template_form_ngSubmit_7_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r12);
      const ctx_r11 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r11.editingTask ? ctx_r11.updateTask() : ctx_r11.createTask());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](8, "div", 35)(9, "div", 36)(10, "label", 37);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](11, "Titre*");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](12, "input", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("ngModelChange", function TaskListComponent_div_19_Template_input_ngModelChange_12_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r12);
      const ctx_r13 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r13.editingTask ? ctx_r13.editingTask.title = $event : ctx_r13.newTask.title = $event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](13, "div", 39)(14, "label", 40);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](15, "Priorit\u00E9*");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](16, "select", 41);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("ngModelChange", function TaskListComponent_div_19_Template_select_ngModelChange_16_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r12);
      const ctx_r14 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r14.editingTask ? ctx_r14.editingTask.priority = $event : ctx_r14.newTask.priority = $event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](17, "option", 42);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](18, "Basse");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](19, "option", 43);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](20, "Moyenne");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](21, "option", 44);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](22, "Haute");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](23, "div", 39)(24, "label", 45);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](25, "Statut*");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](26, "select", 46);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("ngModelChange", function TaskListComponent_div_19_Template_select_ngModelChange_26_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r12);
      const ctx_r15 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r15.editingTask ? ctx_r15.editingTask.status = $event : ctx_r15.newTask.status = $event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](27, "option", 47);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](28, "\u00C0 faire");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](29, "option", 48);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](30, "En cours");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](31, "option", 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](32, "Termin\u00E9e");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](33, "div", 36)(34, "label", 50);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](35, "Assign\u00E9e \u00E0");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](36, "select", 51);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("ngModelChange", function TaskListComponent_div_19_Template_select_ngModelChange_36_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r12);
      const ctx_r16 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r16.editingTask ? ctx_r16.editingTask.assignedTo = $event : ctx_r16.newTask.assignedTo = $event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](37, "option", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](38, "Non assign\u00E9e");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](39, TaskListComponent_div_19_option_39_Template, 2, 2, "option", 53);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](40, "div", 36)(41, "label", 54);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](42, "Date d'\u00E9ch\u00E9ance");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](43, "input", 55);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("ngModelChange", function TaskListComponent_div_19_Template_input_ngModelChange_43_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r12);
      const ctx_r17 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r17.editingTask ? ctx_r17.editingTask.dueDate = $event : ctx_r17.newTask.dueDate = $event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](44, "div", 3)(45, "label", 56);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](46, "Description");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](47, "textarea", 57);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("ngModelChange", function TaskListComponent_div_19_Template_textarea_ngModelChange_47_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r12);
      const ctx_r18 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r18.editingTask ? ctx_r18.editingTask.description = $event : ctx_r18.newTask.description = $event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](48, "div", 58)(49, "button", 59);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function TaskListComponent_div_19_Template_button_click_49_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r12);
      const ctx_r19 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r19.editingTask ? ctx_r19.cancelEdit() : ctx_r19.toggleTaskForm());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](50, " Annuler ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](51, "button", 60);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](52);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()()()()()();
  }
  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", ctx_r3.editingTask ? "Modifier la t\u00E2che" : "Nouvelle t\u00E2che", " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngModel", ctx_r3.editingTask ? ctx_r3.editingTask.title : ctx_r3.newTask.title);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngModel", ctx_r3.editingTask ? ctx_r3.editingTask.priority : ctx_r3.newTask.priority);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](10);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngModel", ctx_r3.editingTask ? ctx_r3.editingTask.status : ctx_r3.newTask.status);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](10);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngModel", ctx_r3.editingTask ? ctx_r3.editingTask.assignedTo : ctx_r3.newTask.assignedTo);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("value", null);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngForOf", ctx_r3.users);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngModel", ctx_r3.editingTask ? ctx_r3.editingTask.dueDate : ctx_r3.newTask.dueDate);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngModel", ctx_r3.editingTask ? ctx_r3.editingTask.description : ctx_r3.newTask.description);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", ctx_r3.editingTask ? "Mettre \u00E0 jour" : "Cr\u00E9er", " ");
  }
}
function TaskListComponent_div_20_Template(rf, ctx) {
  if (rf & 1) {
    const _r21 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 61)(1, "div", 3)(2, "div", 30)(3, "div", 62)(4, "div", 35)(5, "div", 63)(6, "div", 64)(7, "span", 65);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](8, "i", 66);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](9, "input", 67);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("ngModelChange", function TaskListComponent_div_20_Template_input_ngModelChange_9_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r21);
      const ctx_r20 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r20.searchTerm = $event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](10, "div", 63)(11, "select", 68);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("ngModelChange", function TaskListComponent_div_20_Template_select_ngModelChange_11_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r21);
      const ctx_r22 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r22.statusFilter = $event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](12, "option", 69);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](13, "Tous les statuts");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](14, "option", 47);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](15, "\u00C0 faire");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](16, "option", 48);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](17, "En cours");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](18, "option", 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](19, "Termin\u00E9es");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](20, "div", 63)(21, "select", 68);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("ngModelChange", function TaskListComponent_div_20_Template_select_ngModelChange_21_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r21);
      const ctx_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r23.priorityFilter = $event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](22, "option", 69);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](23, "Toutes les priorit\u00E9s");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](24, "option", 44);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](25, "Haute");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](26, "option", 43);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](27, "Moyenne");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](28, "option", 42);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](29, "Basse");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()()()()()();
  }
  if (rf & 2) {
    const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngModel", ctx_r4.searchTerm);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngModel", ctx_r4.statusFilter);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](10);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngModel", ctx_r4.priorityFilter);
  }
}
function TaskListComponent_div_21_Template(rf, ctx) {
  if (rf & 1) {
    const _r25 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 18)(1, "div", 70)(2, "div", 71);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](3, "i", 72);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](4, "h3", 73);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](5, "Aucune t\u00E2che trouv\u00E9e");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](6, "p", 74);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](7, " Commencez par cr\u00E9er une nouvelle t\u00E2che pour votre \u00E9quipe. ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](8, "button", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function TaskListComponent_div_21_Template_button_click_8_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r25);
      const ctx_r24 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r24.toggleTaskForm());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](9, "i", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](10, " Cr\u00E9er une t\u00E2che ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()();
  }
}
function TaskListComponent_div_22_div_12_small_31_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "small", 113);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const task_r32 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]().$implicit;
    const ctx_r33 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", ctx_r33.getUserName(task_r32.assignedTo), " ");
  }
}
const _c0 = function (a0, a1, a2) {
  return {
    "bg-danger": a0,
    "bg-warning text-dark": a1,
    "bg-info text-dark": a2
  };
};
function TaskListComponent_div_22_div_12_Template(rf, ctx) {
  if (rf & 1) {
    const _r36 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 94)(1, "div", 95)(2, "h6", 96);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](4, "div", 97)(5, "button", 98);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](6, "i", 99);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](7, "ul", 100)(8, "li")(9, "button", 101);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function TaskListComponent_div_22_div_12_Template_button_click_9_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r36);
      const task_r32 = restoredCtx.$implicit;
      const ctx_r35 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r35.editTask(task_r32));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](10, "i", 102);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](11, " Modifier ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](12, "li")(13, "button", 101);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function TaskListComponent_div_22_div_12_Template_button_click_13_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r36);
      const task_r32 = restoredCtx.$implicit;
      const ctx_r37 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r37.updateTaskStatus(task_r32, "in-progress"));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](14, "i", 103);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](15, " D\u00E9placer vers \"En cours\" ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](16, "li")(17, "button", 101);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function TaskListComponent_div_22_div_12_Template_button_click_17_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r36);
      const task_r32 = restoredCtx.$implicit;
      const ctx_r38 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r38.updateTaskStatus(task_r32, "done"));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](18, "i", 89);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](19, " Marquer comme termin\u00E9e ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](20, "li");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](21, "hr", 104);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](22, "li")(23, "button", 105);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function TaskListComponent_div_22_div_12_Template_button_click_23_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r36);
      const task_r32 = restoredCtx.$implicit;
      const ctx_r39 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](task_r32._id && ctx_r39.deleteTask(task_r32._id));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](24, "i", 106);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](25, " Supprimer ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](26, "p", 107);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](27);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](28, "div", 108)(29, "span", 109);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](30);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](31, TaskListComponent_div_22_div_12_small_31_Template, 2, 1, "small", 110);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](32, "div", 111);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](33, "i", 112);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const task_r32 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngClass", "priority-" + task_r32.priority);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](task_r32.title);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](24);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", task_r32.description || "Aucune description", " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpureFunction3"](6, _c0, task_r32.priority === "high", task_r32.priority === "medium", task_r32.priority === "low"));
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", task_r32.priority === "high" ? "Haute" : task_r32.priority === "medium" ? "Moyenne" : "Basse", " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", task_r32.assignedTo);
  }
}
function TaskListComponent_div_22_div_24_small_31_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "small", 113);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const task_r40 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]().$implicit;
    const ctx_r41 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", ctx_r41.getUserName(task_r40.assignedTo), " ");
  }
}
function TaskListComponent_div_22_div_24_Template(rf, ctx) {
  if (rf & 1) {
    const _r44 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 94)(1, "div", 95)(2, "h6", 96);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](4, "div", 97)(5, "button", 98);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](6, "i", 99);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](7, "ul", 100)(8, "li")(9, "button", 101);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function TaskListComponent_div_22_div_24_Template_button_click_9_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r44);
      const task_r40 = restoredCtx.$implicit;
      const ctx_r43 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r43.editTask(task_r40));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](10, "i", 102);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](11, " Modifier ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](12, "li")(13, "button", 101);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function TaskListComponent_div_22_div_24_Template_button_click_13_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r44);
      const task_r40 = restoredCtx.$implicit;
      const ctx_r45 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r45.updateTaskStatus(task_r40, "todo"));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](14, "i", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](15, " D\u00E9placer vers \"\u00C0 faire\" ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](16, "li")(17, "button", 101);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function TaskListComponent_div_22_div_24_Template_button_click_17_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r44);
      const task_r40 = restoredCtx.$implicit;
      const ctx_r46 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r46.updateTaskStatus(task_r40, "done"));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](18, "i", 89);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](19, " Marquer comme termin\u00E9e ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](20, "li");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](21, "hr", 104);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](22, "li")(23, "button", 105);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function TaskListComponent_div_22_div_24_Template_button_click_23_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r44);
      const task_r40 = restoredCtx.$implicit;
      const ctx_r47 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](task_r40._id && ctx_r47.deleteTask(task_r40._id));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](24, "i", 106);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](25, " Supprimer ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](26, "p", 107);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](27);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](28, "div", 108)(29, "span", 109);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](30);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](31, TaskListComponent_div_22_div_24_small_31_Template, 2, 1, "small", 110);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](32, "div", 111);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](33, "i", 112);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const task_r40 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngClass", "priority-" + task_r40.priority);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](task_r40.title);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](24);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", task_r40.description || "Aucune description", " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpureFunction3"](6, _c0, task_r40.priority === "high", task_r40.priority === "medium", task_r40.priority === "low"));
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", task_r40.priority === "high" ? "Haute" : task_r40.priority === "medium" ? "Moyenne" : "Basse", " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", task_r40.assignedTo);
  }
}
function TaskListComponent_div_22_div_36_small_31_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "small", 113);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const task_r48 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]().$implicit;
    const ctx_r49 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", ctx_r49.getUserName(task_r48.assignedTo), " ");
  }
}
function TaskListComponent_div_22_div_36_Template(rf, ctx) {
  if (rf & 1) {
    const _r52 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 114)(1, "div", 95)(2, "h6", 96);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](4, "div", 97)(5, "button", 98);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](6, "i", 99);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](7, "ul", 100)(8, "li")(9, "button", 101);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function TaskListComponent_div_22_div_36_Template_button_click_9_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r52);
      const task_r48 = restoredCtx.$implicit;
      const ctx_r51 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r51.editTask(task_r48));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](10, "i", 102);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](11, " Modifier ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](12, "li")(13, "button", 101);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function TaskListComponent_div_22_div_36_Template_button_click_13_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r52);
      const task_r48 = restoredCtx.$implicit;
      const ctx_r53 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r53.updateTaskStatus(task_r48, "todo"));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](14, "i", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](15, " D\u00E9placer vers \"\u00C0 faire\" ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](16, "li")(17, "button", 101);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function TaskListComponent_div_22_div_36_Template_button_click_17_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r52);
      const task_r48 = restoredCtx.$implicit;
      const ctx_r54 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r54.updateTaskStatus(task_r48, "in-progress"));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](18, "i", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](19, " D\u00E9placer vers \"En cours\" ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](20, "li");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](21, "hr", 104);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](22, "li")(23, "button", 105);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function TaskListComponent_div_22_div_36_Template_button_click_23_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r52);
      const task_r48 = restoredCtx.$implicit;
      const ctx_r55 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](task_r48._id && ctx_r55.deleteTask(task_r48._id));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](24, "i", 106);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](25, " Supprimer ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](26, "p", 107);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](27);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](28, "div", 108)(29, "span", 109);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](30);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](31, TaskListComponent_div_22_div_36_small_31_Template, 2, 1, "small", 110);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](32, "div", 111);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](33, "i", 112);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const task_r48 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngClass", "priority-" + task_r48.priority);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](task_r48.title);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](24);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", task_r48.description || "Aucune description", " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpureFunction3"](6, _c0, task_r48.priority === "high", task_r48.priority === "medium", task_r48.priority === "low"));
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", task_r48.priority === "high" ? "Haute" : task_r48.priority === "medium" ? "Moyenne" : "Basse", " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", task_r48.assignedTo);
  }
}
const _c1 = function (a0, a1) {
  return [a0, a1];
};
function TaskListComponent_div_22_Template(rf, ctx) {
  if (rf & 1) {
    const _r57 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 75)(1, "div", 63)(2, "div", 76)(3, "div", 31)(4, "h5", 77);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](5, "i", 78);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](6, " \u00C0 faire ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](7, "span", 79);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](9, "div", 62)(10, "div", 80, 81);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("cdkDropListDropped", function TaskListComponent_div_22_Template_div_cdkDropListDropped_10_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r57);
      const ctx_r56 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r56.drop($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](12, TaskListComponent_div_22_div_12_Template, 34, 10, "div", 82);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](13, "div", 63)(14, "div", 76)(15, "div", 83)(16, "h5", 77);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](17, "i", 84);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](18, " En cours ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](19, "span", 85);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](20);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](21, "div", 62)(22, "div", 86, 87);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("cdkDropListDropped", function TaskListComponent_div_22_Template_div_cdkDropListDropped_22_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r57);
      const ctx_r58 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r58.drop($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](24, TaskListComponent_div_22_div_24_Template, 34, 10, "div", 82);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](25, "div", 63)(26, "div", 76)(27, "div", 88)(28, "h5", 77);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](29, "i", 89);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](30, " Termin\u00E9es ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](31, "span", 90);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](32);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](33, "div", 62)(34, "div", 91, 92);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("cdkDropListDropped", function TaskListComponent_div_22_Template_div_cdkDropListDropped_34_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r57);
      const ctx_r59 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r59.drop($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](36, TaskListComponent_div_22_div_36_Template, 34, 10, "div", 93);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()()();
  }
  if (rf & 2) {
    const _r26 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵreference"](11);
    const _r28 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵreference"](23);
    const _r30 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵreference"](35);
    const ctx_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", ctx_r6.getTodoTasksCount(), " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("cdkDropListData", ctx_r6.getTodoTasks())("cdkDropListConnectedTo", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpureFunction2"](12, _c1, _r28, _r30));
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngForOf", ctx_r6.getTodoTasks());
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", ctx_r6.getInProgressTasksCount(), " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("cdkDropListData", ctx_r6.getInProgressTasks())("cdkDropListConnectedTo", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpureFunction2"](15, _c1, _r26, _r30));
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngForOf", ctx_r6.getInProgressTasks());
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", ctx_r6.getDoneTasksCount(), " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("cdkDropListData", ctx_r6.getDoneTasks())("cdkDropListConnectedTo", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpureFunction2"](18, _c1, _r26, _r28));
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngForOf", ctx_r6.getDoneTasks());
  }
}
class TaskListComponent {
  constructor(taskService, equipeService, userService, route, router, notificationService) {
    this.taskService = taskService;
    this.equipeService = equipeService;
    this.userService = userService;
    this.route = route;
    this.router = router;
    this.notificationService = notificationService;
    this.tasks = [];
    this.teamId = null;
    this.team = null;
    this.loading = false;
    this.error = null;
    this.users = [];
    this.editingTask = null;
    this.showTaskForm = false;
    // Filtres
    this.statusFilter = 'all';
    this.priorityFilter = 'all';
    this.searchTerm = '';
  }
  ngOnInit() {
    // Initialiser la nouvelle tâche
    this.newTask = this.initializeNewTask();
    this.route.paramMap.subscribe(params => {
      this.teamId = params.get('id');
      if (this.teamId) {
        this.loadTeamDetails(this.teamId);
        this.loadTasks(this.teamId);
        this.loadUsers();
      } else {
        this.error = "ID d'équipe manquant";
        this.notificationService.showError("ID d'équipe manquant");
      }
    });
  }
  loadTeamDetails(teamId) {
    this.loading = true;
    // Utiliser les données de test si l'API n'est pas disponible
    const useMockData = false; // Mettre à true pour utiliser les données de test
    if (useMockData) {
      // Données de test pour simuler les détails de l'équipe
      const mockTeam = {
        _id: teamId,
        name: 'Équipe ' + teamId,
        description: "Description de l'équipe " + teamId,
        admin: 'admin123',
        members: []
      };
      setTimeout(() => {
        this.team = mockTeam;
        this.loading = false;
        console.log("Détails de l'équipe chargés (mock):", this.team);
      }, 300);
    } else {
      // Utiliser l'API réelle
      this.equipeService.getEquipe(teamId).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.finalize)(() => this.loading = false)).subscribe({
        next: data => {
          this.team = data;
          console.log("Détails de l'équipe chargés depuis l'API:", this.team);
        },
        error: error => {
          console.error("Erreur lors du chargement des détails de l'équipe:", error);
          this.error = "Impossible de charger les détails de l'équipe";
          this.notificationService.showError("Erreur lors du chargement des détails de l'équipe");
          // Fallback aux données de test en cas d'erreur
          const mockTeam = {
            _id: teamId,
            name: 'Équipe ' + teamId + ' (fallback)',
            description: "Description de l'équipe " + teamId,
            admin: 'admin123',
            members: []
          };
          this.team = mockTeam;
        }
      });
    }
  }
  loadTasks(teamId) {
    this.loading = true;
    // Utiliser les données de test si l'API n'est pas disponible
    const useMockData = false; // Mettre à true pour utiliser les données de test
    if (useMockData) {
      // Données de test pour simuler les tâches
      const mockTasks = [{
        _id: '1',
        title: 'Tâche 1',
        description: 'Description de la tâche 1',
        status: 'todo',
        priority: 'high',
        teamId: teamId
      }, {
        _id: '2',
        title: 'Tâche 2',
        description: 'Description de la tâche 2',
        status: 'todo',
        priority: 'medium',
        teamId: teamId
      }, {
        _id: '3',
        title: 'Tâche 3',
        description: 'Description de la tâche 3',
        status: 'in-progress',
        priority: 'high',
        teamId: teamId
      }, {
        _id: '4',
        title: 'Tâche 4',
        description: 'Description de la tâche 4',
        status: 'done',
        priority: 'low',
        teamId: teamId
      }];
      setTimeout(() => {
        this.tasks = mockTasks;
        this.sortTasks();
        this.loading = false;
        console.log('Tâches chargées (mock):', this.tasks);
      }, 500);
    } else {
      // Utiliser l'API réelle
      this.taskService.getTasksByTeam(teamId).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.finalize)(() => this.loading = false)).subscribe({
        next: data => {
          this.tasks = data;
          this.sortTasks();
          console.log("Tâches chargées depuis l'API:", this.tasks);
        },
        error: error => {
          console.error('Erreur lors du chargement des tâches:', error);
          this.error = 'Impossible de charger les tâches';
          this.notificationService.showError('Erreur lors du chargement des tâches');
          // Fallback aux données de test en cas d'erreur
          const mockTasks = [{
            _id: '1',
            title: 'Tâche 1 (fallback)',
            description: 'Description de la tâche 1',
            status: 'todo',
            priority: 'high',
            teamId: teamId
          }, {
            _id: '2',
            title: 'Tâche 2 (fallback)',
            description: 'Description de la tâche 2',
            status: 'todo',
            priority: 'medium',
            teamId: teamId
          }];
          this.tasks = mockTasks;
          this.sortTasks();
          console.log('Tâches chargées (fallback):', this.tasks);
        }
      });
    }
  }
  // Gestion du glisser-déposer
  drop(event) {
    if (event.previousContainer === event.container) {
      // Déplacement dans la même liste
      (0,_angular_cdk_drag_drop__WEBPACK_IMPORTED_MODULE_6__.moveItemInArray)(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      // Déplacement entre listes
      (0,_angular_cdk_drag_drop__WEBPACK_IMPORTED_MODULE_6__.transferArrayItem)(event.previousContainer.data, event.container.data, event.previousIndex, event.currentIndex);
      // Mettre à jour le statut de la tâche
      const task = event.container.data[event.currentIndex];
      let newStatus;
      if (event.container.id === 'todo-list') {
        newStatus = 'todo';
      } else if (event.container.id === 'in-progress-list') {
        newStatus = 'in-progress';
      } else {
        newStatus = 'done';
      }
      if (task._id && task.status !== newStatus) {
        task.status = newStatus;
        this.updateTaskStatus(task, newStatus);
      }
    }
  }
  loadUsers() {
    // Utiliser les données de test si l'API n'est pas disponible
    const useMockData = false; // Mettre à true pour utiliser les données de test
    if (useMockData) {
      // Données de test pour simuler les utilisateurs
      const mockUsers = [{
        _id: 'user1',
        username: 'john_doe',
        email: '<EMAIL>',
        role: 'admin',
        isActive: true
      }, {
        _id: 'user2',
        username: 'jane_smith',
        email: '<EMAIL>',
        role: 'student',
        isActive: true
      }];
      setTimeout(() => {
        this.users = mockUsers;
        console.log('Utilisateurs chargés (mock):', this.users);
      }, 400);
    } else {
      // TODO: Implémenter l'API réelle pour récupérer les utilisateurs
      // Pour l'instant, utiliser les données mockées
      const mockUsers = [{
        _id: 'user1',
        username: 'john_doe',
        email: '<EMAIL>',
        role: 'admin',
        isActive: true
      }, {
        _id: 'user2',
        username: 'jane_smith',
        email: '<EMAIL>',
        role: 'student',
        isActive: true
      }];
      this.users = mockUsers;
      console.log('Utilisateurs chargés (mock API):', this.users);
    }
  }
  getUserName(userId) {
    const user = this.users.find(u => u._id === userId || u.id === userId);
    if (user) {
      if (user.firstName && user.lastName) {
        return `${user.firstName} ${user.lastName}`;
      } else if (user.name) {
        return user.name;
      }
    }
    return 'Utilisateur inconnu';
  }
  createTask() {
    if (!this.teamId) {
      this.notificationService.showError("ID d'équipe manquant");
      return;
    }
    this.newTask.teamId = this.teamId;
    this.loading = true;
    this.taskService.createTask(this.newTask).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.finalize)(() => this.loading = false)).subscribe({
      next: data => {
        this.tasks.push(data);
        this.sortTasks();
        this.newTask = this.initializeNewTask();
        this.showTaskForm = false;
        this.notificationService.showSuccess('Tâche créée avec succès');
      },
      error: error => {
        console.error('Erreur lors de la création de la tâche:', error);
        this.notificationService.showError('Erreur lors de la création de la tâche');
      }
    });
  }
  updateTask() {
    if (!this.editingTask || !this.editingTask._id) {
      this.notificationService.showError('Tâche invalide');
      return;
    }
    this.loading = true;
    this.taskService.updateTask(this.editingTask._id, this.editingTask).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.finalize)(() => this.loading = false)).subscribe({
      next: data => {
        const index = this.tasks.findIndex(t => t._id === data._id);
        if (index !== -1) {
          this.tasks[index] = data;
        }
        this.editingTask = null;
        this.notificationService.showSuccess('Tâche mise à jour avec succès');
      },
      error: error => {
        console.error('Erreur lors de la mise à jour de la tâche:', error);
        this.notificationService.showError('Erreur lors de la mise à jour de la tâche');
      }
    });
  }
  deleteTask(id) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette tâche ?')) {
      this.loading = true;
      this.taskService.deleteTask(id).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.finalize)(() => this.loading = false)).subscribe({
        next: () => {
          this.tasks = this.tasks.filter(t => t._id !== id);
          this.notificationService.showSuccess('Tâche supprimée avec succès');
        },
        error: error => {
          console.error('Erreur lors de la suppression de la tâche:', error);
          this.notificationService.showError('Erreur lors de la suppression de la tâche');
        }
      });
    }
  }
  updateTaskStatus(task, status) {
    if (!task._id) return;
    this.loading = true;
    this.taskService.updateTaskStatus(task._id, status).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.finalize)(() => this.loading = false)).subscribe({
      next: data => {
        const index = this.tasks.findIndex(t => t._id === data._id);
        if (index !== -1) {
          this.tasks[index] = data;
        }
        this.notificationService.showSuccess('Statut de la tâche mis à jour');
      },
      error: error => {
        console.error('Erreur lors de la mise à jour du statut:', error);
        this.notificationService.showError('Erreur lors de la mise à jour du statut');
      }
    });
  }
  editTask(task) {
    this.editingTask = {
      ...task
    };
  }
  cancelEdit() {
    this.editingTask = null;
  }
  toggleTaskForm() {
    this.showTaskForm = !this.showTaskForm;
    if (this.showTaskForm) {
      this.newTask = this.initializeNewTask();
    }
  }
  initializeNewTask() {
    return {
      title: '',
      description: '',
      status: 'todo',
      priority: 'medium',
      teamId: this.teamId || '',
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // Par défaut, une semaine à partir d'aujourd'hui
    };
  }

  sortTasks() {
    // Trier par priorité (high > medium > low) puis par statut (todo > in-progress > done)
    this.tasks.sort((a, b) => {
      const priorityOrder = {
        high: 0,
        medium: 1,
        low: 2
      };
      const statusOrder = {
        todo: 0,
        'in-progress': 1,
        done: 2
      };
      // D'abord par priorité
      if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
        return priorityOrder[a.priority] - priorityOrder[b.priority];
      }
      // Ensuite par statut
      return statusOrder[a.status] - statusOrder[b.status];
    });
  }
  // Méthodes de filtrage
  filterTasks() {
    return this.tasks.filter(task => {
      // Filtre par statut
      if (this.statusFilter !== 'all' && task.status !== this.statusFilter) {
        return false;
      }
      // Filtre par priorité
      if (this.priorityFilter !== 'all' && task.priority !== this.priorityFilter) {
        return false;
      }
      // Filtre par terme de recherche
      if (this.searchTerm && !task.title.toLowerCase().includes(this.searchTerm.toLowerCase())) {
        return false;
      }
      return true;
    });
  }
  // Méthodes pour obtenir les tâches par statut
  getTodoTasks() {
    return this.tasks.filter(task => task.status === 'todo' && (this.priorityFilter === 'all' || task.priority === this.priorityFilter) && (!this.searchTerm || task.title.toLowerCase().includes(this.searchTerm.toLowerCase())));
  }
  getInProgressTasks() {
    return this.tasks.filter(task => task.status === 'in-progress' && (this.priorityFilter === 'all' || task.priority === this.priorityFilter) && (!this.searchTerm || task.title.toLowerCase().includes(this.searchTerm.toLowerCase())));
  }
  getDoneTasks() {
    return this.tasks.filter(task => task.status === 'done' && (this.priorityFilter === 'all' || task.priority === this.priorityFilter) && (!this.searchTerm || task.title.toLowerCase().includes(this.searchTerm.toLowerCase())));
  }
  // Méthodes pour compter les tâches par statut
  getTodoTasksCount() {
    return this.tasks.filter(task => task.status === 'todo').length;
  }
  getInProgressTasksCount() {
    return this.tasks.filter(task => task.status === 'in-progress').length;
  }
  getDoneTasksCount() {
    return this.tasks.filter(task => task.status === 'done').length;
  }
  navigateBack() {
    this.router.navigate(['/liste']);
  }
  static {
    this.ɵfac = function TaskListComponent_Factory(t) {
      return new (t || TaskListComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](src_app_services_task_service__WEBPACK_IMPORTED_MODULE_0__.TaskService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](src_app_services_equipe_service__WEBPACK_IMPORTED_MODULE_1__.EquipeService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](src_app_services_authuser_service__WEBPACK_IMPORTED_MODULE_2__.AuthuserService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_7__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_7__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](src_app_services_notification_service__WEBPACK_IMPORTED_MODULE_3__.NotificationService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineComponent"]({
      type: TaskListComponent,
      selectors: [["app-task-list"]],
      decls: 23,
      vars: 7,
      consts: [[1, "container-fluid", "py-5", "bg-light"], [1, "container"], [1, "row", "mb-5"], [1, "col-12"], [1, "d-flex", "justify-content-between", "align-items-center", "flex-wrap"], ["class", "display-4 fw-bold text-primary", 4, "ngIf"], [1, "text-muted", "lead"], [1, "d-flex", "gap-2"], [1, "btn", "btn-outline-secondary", "rounded-pill", "px-4", "py-2", 3, "click"], [1, "bi", "bi-arrow-left", "me-2"], [1, "btn", "btn-primary", "rounded-pill", "px-4", "py-2", 3, "click"], [1, "bi", "bi-plus-circle", "me-2"], [1, "my-4"], ["class", "row justify-content-center my-5", 4, "ngIf"], ["class", "row mb-5", 4, "ngIf"], ["class", "row mb-4", 4, "ngIf"], ["class", "row g-4", 4, "ngIf"], [1, "display-4", "fw-bold", "text-primary"], [1, "row", "justify-content-center", "my-5"], [1, "col-md-6", "text-center"], ["role", "status", 1, "spinner-grow", "text-primary", "mx-1"], [1, "visually-hidden"], ["role", "status", 1, "spinner-grow", "text-secondary", "mx-1"], [1, "mt-3", "text-muted"], [1, "col-md-8"], [1, "alert", "alert-danger", "shadow-sm", "border-0", "rounded-3", "d-flex", "align-items-center"], [1, "bi", "bi-exclamation-triangle-fill", "fs-3", "me-3"], [1, "flex-grow-1"], [1, "btn", "btn-danger", "rounded-pill", "ms-3", 3, "click"], [1, "bi", "bi-arrow-clockwise", "me-1"], [1, "card", "border-0", "shadow-sm", "rounded-3"], [1, "card-header", "bg-primary", "text-white", "py-3"], [1, "mb-0"], [1, "card-body", "p-4"], [3, "ngSubmit"], [1, "row", "g-3"], [1, "col-md-6"], ["for", "taskTitle", 1, "form-label"], ["type", "text", "id", "taskTitle", "required", "", "name", "title", "placeholder", "Titre de la t\u00E2che", 1, "form-control", 3, "ngModel", "ngModelChange"], [1, "col-md-3"], ["for", "taskPriority", 1, "form-label"], ["id", "taskPriority", "required", "", "name", "priority", 1, "form-select", 3, "ngModel", "ngModelChange"], ["value", "low"], ["value", "medium"], ["value", "high"], ["for", "taskStatus", 1, "form-label"], ["id", "taskStatus", "required", "", "name", "status", 1, "form-select", 3, "ngModel", "ngModelChange"], ["value", "todo"], ["value", "in-progress"], ["value", "done"], ["for", "taskAssignedTo", 1, "form-label"], ["id", "taskAssignedTo", "name", "assignedTo", 1, "form-select", 3, "ngModel", "ngModelChange"], [3, "value"], [3, "value", 4, "ngFor", "ngForOf"], ["for", "taskDueDate", 1, "form-label"], ["type", "date", "id", "taskDueDate", "name", "dueDate", 1, "form-control", 3, "ngModel", "ngModelChange"], ["for", "taskDescription", 1, "form-label"], ["id", "taskDescription", "rows", "3", "name", "description", "placeholder", "Description d\u00E9taill\u00E9e de la t\u00E2che", 1, "form-control", 3, "ngModel", "ngModelChange"], [1, "col-12", "d-flex", "justify-content-end", "gap-2", "mt-4"], ["type", "button", 1, "btn", "btn-outline-secondary", "rounded-pill", "px-4", 3, "click"], ["type", "submit", 1, "btn", "btn-primary", "rounded-pill", "px-4"], [1, "row", "mb-4"], [1, "card-body", "p-3"], [1, "col-md-4"], [1, "input-group"], [1, "input-group-text", "bg-white", "border-end-0"], [1, "bi", "bi-search"], ["type", "text", "placeholder", "Rechercher une t\u00E2che...", 1, "form-control", "border-start-0", 3, "ngModel", "ngModelChange"], [1, "form-select", 3, "ngModel", "ngModelChange"], ["value", "all"], [1, "col-md-8", "text-center"], [1, "p-5", "bg-white", "rounded-3", "shadow-sm"], [1, "bi", "bi-list-check", "fs-1", "text-muted", "mb-3"], [1, "mb-3"], [1, "text-muted", "mb-4"], [1, "row", "g-4"], [1, "card", "border-0", "shadow-sm", "rounded-3", "h-100"], [1, "mb-0", "d-flex", "align-items-center"], [1, "bi", "bi-list-task", "me-2"], [1, "badge", "bg-white", "text-primary", "rounded-pill", "ms-2"], ["cdkDropList", "", "id", "todo-list", 1, "task-list", 3, "cdkDropListData", "cdkDropListConnectedTo", "cdkDropListDropped"], ["todoList", "cdkDropList"], ["class", "task-card mb-3 p-3 rounded-3 shadow-sm", "cdkDrag", "", 3, "ngClass", 4, "ngFor", "ngForOf"], [1, "card-header", "bg-warning", "py-3"], [1, "bi", "bi-hourglass-split", "me-2"], [1, "badge", "bg-white", "text-warning", "rounded-pill", "ms-2"], ["cdkDropList", "", "id", "in-progress-list", 1, "task-list", 3, "cdkDropListData", "cdkDropListConnectedTo", "cdkDropListDropped"], ["inProgressList", "cdkDropList"], [1, "card-header", "bg-success", "text-white", "py-3"], [1, "bi", "bi-check2-all", "me-2"], [1, "badge", "bg-white", "text-success", "rounded-pill", "ms-2"], ["cdkDropList", "", "id", "done-list", 1, "task-list", 3, "cdkDropListData", "cdkDropListConnectedTo", "cdkDropListDropped"], ["doneList", "cdkDropList"], ["class", "task-card mb-3 p-3 rounded-3 shadow-sm completed-task", "cdkDrag", "", 3, "ngClass", 4, "ngFor", "ngForOf"], ["cdkDrag", "", 1, "task-card", "mb-3", "p-3", "rounded-3", "shadow-sm", 3, "ngClass"], [1, "d-flex", "justify-content-between", "align-items-start", "mb-2"], [1, "mb-0", "text-truncate"], [1, "dropdown"], ["type", "button", "data-bs-toggle", "dropdown", 1, "btn", "btn-sm", "btn-link", "text-dark", "p-0"], [1, "bi", "bi-three-dots-vertical"], [1, "dropdown-menu", "dropdown-menu-end"], [1, "dropdown-item", 3, "click"], [1, "bi", "bi-pencil", "me-2"], [1, "bi", "bi-arrow-right", "me-2"], [1, "dropdown-divider"], [1, "dropdown-item", "text-danger", 3, "click"], [1, "bi", "bi-trash", "me-2"], [1, "small", "text-muted", "mb-2", "task-description"], [1, "d-flex", "justify-content-between", "align-items-center"], [1, "badge", 3, "ngClass"], ["class", "text-muted", 4, "ngIf"], ["cdkDragHandle", "", 1, "task-drag-handle"], [1, "bi", "bi-grip-horizontal"], [1, "text-muted"], ["cdkDrag", "", 1, "task-card", "mb-3", "p-3", "rounded-3", "shadow-sm", "completed-task", 3, "ngClass"]],
      template: function TaskListComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "div", 3)(4, "div", 4)(5, "div");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](6, TaskListComponent_h1_6_Template, 2, 1, "h1", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](7, "p", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](8, "G\u00E9rez les t\u00E2ches de votre \u00E9quipe");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](9, "div", 7)(10, "button", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function TaskListComponent_Template_button_click_10_listener() {
            return ctx.navigateBack();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](11, "i", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](12, " Retour ");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](13, "button", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function TaskListComponent_Template_button_click_13_listener() {
            return ctx.toggleTaskForm();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](14, "i", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](15, " Nouvelle t\u00E2che ");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](16, "hr", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](17, TaskListComponent_div_17_Template, 13, 0, "div", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](18, TaskListComponent_div_18_Template, 9, 1, "div", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](19, TaskListComponent_div_19_Template, 53, 10, "div", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](20, TaskListComponent_div_20_Template, 30, 3, "div", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](21, TaskListComponent_div_21_Template, 11, 0, "div", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](22, TaskListComponent_div_22_Template, 37, 21, "div", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.team);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](11);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.loading);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.error);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.showTaskForm || ctx.editingTask);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.tasks.length > 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", !ctx.loading && !ctx.error && ctx.tasks.length === 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.tasks.length > 0);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_8__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_8__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_8__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_9__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_9__.NgSelectOption, _angular_forms__WEBPACK_IMPORTED_MODULE_9__["ɵNgSelectMultipleOption"], _angular_forms__WEBPACK_IMPORTED_MODULE_9__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.SelectControlValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.RequiredValidator, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.NgModel, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.NgForm, _angular_cdk_drag_drop__WEBPACK_IMPORTED_MODULE_6__.CdkDropList, _angular_cdk_drag_drop__WEBPACK_IMPORTED_MODULE_6__.CdkDrag, _angular_cdk_drag_drop__WEBPACK_IMPORTED_MODULE_6__.CdkDragHandle],
      styles: [".task-card[_ngcontent-%COMP%] {\n  background-color: white;\n  border-left: 4px solid transparent;\n  transition: all 0.2s ease;\n  position: relative;\n  cursor: move;\n  margin-bottom: 12px;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.05);\n}\n\n.task-card[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;\n}\n\n.task-card.priority-high[_ngcontent-%COMP%] {\n  border-left-color: #dc3545;\n}\n\n.task-card.priority-medium[_ngcontent-%COMP%] {\n  border-left-color: #ffc107;\n}\n\n.task-card.priority-low[_ngcontent-%COMP%] {\n  border-left-color: #0dcaf0;\n}\n\n.completed-task[_ngcontent-%COMP%] {\n  opacity: 0.7;\n}\n\n.task-description[_ngcontent-%COMP%] {\n  max-height: 3em;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  font-size: 0.9rem;\n  color: #6c757d;\n  margin-bottom: 10px;\n}\n\n.task-list[_ngcontent-%COMP%] {\n  min-height: 50px;\n  max-height: 500px;\n  overflow-y: auto;\n  padding: 8px;\n  border-radius: 4px;\n}\n\n\n\n.card-header.bg-primary[_ngcontent-%COMP%] {\n  background: linear-gradient(45deg, #007bff, #6610f2) !important;\n}\n\n.card-header.bg-warning[_ngcontent-%COMP%] {\n  background: linear-gradient(45deg, #ffc107, #fd7e14) !important;\n}\n\n.card-header.bg-success[_ngcontent-%COMP%] {\n  background: linear-gradient(45deg, #28a745, #20c997) !important;\n}\n\n\n\n.cdk-drag-preview[_ngcontent-%COMP%] {\n  box-sizing: border-box;\n  border-radius: 8px;\n  box-shadow: 0 5px 15px rgba(0,0,0,0.2) !important;\n  opacity: 0.8;\n}\n\n.cdk-drag-placeholder[_ngcontent-%COMP%] {\n  opacity: 0.3;\n}\n\n.cdk-drag-animating[_ngcontent-%COMP%] {\n  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);\n}\n\n.task-list.cdk-drop-list-dragging[_ngcontent-%COMP%]   .task-card[_ngcontent-%COMP%]:not(.cdk-drag-placeholder) {\n  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);\n}\n\n\n\n.task-drag-handle[_ngcontent-%COMP%] {\n  position: absolute;\n  bottom: 5px;\n  right: 5px;\n  color: #adb5bd;\n  cursor: move;\n  font-size: 0.8rem;\n}\n\n\n\n.kanban-column[_ngcontent-%COMP%] {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 4px 6px rgba(0,0,0,0.1);\n}\n\n.kanban-column-header[_ngcontent-%COMP%] {\n  padding: 15px;\n  border-top-left-radius: 8px;\n  border-top-right-radius: 8px;\n}\n\n.kanban-column-content[_ngcontent-%COMP%] {\n  flex-grow: 1;\n  background-color: #f8f9fa;\n  padding: 15px;\n  border-bottom-left-radius: 8px;\n  border-bottom-right-radius: 8px;\n  min-height: 300px;\n  max-height: 600px;\n  overflow-y: auto;\n}\n\n\n  .task-card[_ngcontent-%COMP%] {\n    background-color: white;\n    border-left: 4px solid transparent;\n    transition: all 0.2s ease;\n  }\n\n  .task-card[_ngcontent-%COMP%]:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;\n  }\n\n  .task-card.priority-high[_ngcontent-%COMP%] {\n    border-left-color: #dc3545;\n  }\n\n  .task-card.priority-medium[_ngcontent-%COMP%] {\n    border-left-color: #ffc107;\n  }\n\n  .task-card.priority-low[_ngcontent-%COMP%] {\n    border-left-color: #0dcaf0;\n  }\n\n  .completed-task[_ngcontent-%COMP%] {\n    opacity: 0.7;\n  }\n\n  .task-description[_ngcontent-%COMP%] {\n    max-height: 3em;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    display: -webkit-box;\n    -webkit-line-clamp: 2;\n    -webkit-box-orient: vertical;\n  }\n\n  .task-list[_ngcontent-%COMP%] {\n    max-height: 500px;\n    overflow-y: auto;\n  }\n/*# sourceMappingURL=data:application/json;base64,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 */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ })

}]);
//# sourceMappingURL=src_app_views_front_equipes_equipes_module_ts.js.map