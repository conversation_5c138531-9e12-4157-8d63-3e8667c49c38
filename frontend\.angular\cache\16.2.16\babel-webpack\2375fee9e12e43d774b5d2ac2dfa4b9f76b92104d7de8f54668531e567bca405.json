{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { debounceTime, distinctUntilChanged } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/services/reunion.service\";\nimport * as i3 from \"src/app/services/planning.service\";\nimport * as i4 from \"@app/services/data.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"src/app/services/authuser.service\";\nimport * as i7 from \"src/app/services/toast.service\";\nfunction ReunionFormComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.error.message || \"Une erreur est survenue\", \" \");\n  }\n}\nfunction ReunionFormComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.successMessage, \" \");\n  }\n}\nfunction ReunionFormComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵelement(1, \"i\", 64);\n    i0.ɵɵtext(2, \" Le titre est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵelement(1, \"i\", 64);\n    i0.ɵɵtext(2, \" La date est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵelement(1, \"i\", 64);\n    i0.ɵɵtext(2, \" L'heure de d\\u00E9but est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵelement(1, \"i\", 64);\n    i0.ɵɵtext(2, \" L'heure de fin est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_span_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 65);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 66);\n    i0.ɵɵelement(2, \"circle\", 67)(3, \"path\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" V\\u00E9rification... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_div_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵelement(1, \"i\", 70);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.lienVisioError, \" \");\n  }\n}\nfunction ReunionFormComponent_div_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelement(1, \"i\", 72);\n    i0.ɵɵtext(2, \" Lien disponible \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_div_63_option_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 81);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const planning_r17 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", planning_r17._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(planning_r17.titre);\n  }\n}\nfunction ReunionFormComponent_div_63_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵelement(1, \"i\", 64);\n    i0.ɵɵtext(2, \" Le planning est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionFormComponent_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73)(1, \"h3\", 74);\n    i0.ɵɵelement(2, \"i\", 75);\n    i0.ɵɵtext(3, \" Planning \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"label\", 76);\n    i0.ɵɵelement(5, \"i\", 77);\n    i0.ɵɵtext(6, \" S\\u00E9lectionnez un planning * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"select\", 78)(8, \"option\", 79);\n    i0.ɵɵtext(9, \"Choisissez un planning...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, ReunionFormComponent_div_63_option_10_Template, 2, 2, \"option\", 80);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, ReunionFormComponent_div_63_div_11_Template, 3, 0, \"div\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r9.plannings);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r9.reunionForm.get(\"planning\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r9.reunionForm.get(\"planning\")) == null ? null : tmp_1_0.touched));\n  }\n}\nfunction ReunionFormComponent_div_64_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 90);\n    i0.ɵɵelement(1, \"i\", 53);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r18.selectedPlanning.description, \" \");\n  }\n}\nfunction ReunionFormComponent_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 82)(1, \"h3\", 83);\n    i0.ɵɵelement(2, \"i\", 84);\n    i0.ɵɵtext(3, \" Planning s\\u00E9lectionn\\u00E9 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 85)(5, \"span\", 86);\n    i0.ɵɵelement(6, \"i\", 75);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 87);\n    i0.ɵɵelement(9, \"i\", 88);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, ReunionFormComponent_div_64_div_13_Template, 3, 1, \"div\", 89);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.selectedPlanning.titre, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind2(11, 4, ctx_r10.selectedPlanning.dateDebut, \"dd/MM/yyyy\"), \" - \", i0.ɵɵpipeBind2(12, 7, ctx_r10.selectedPlanning.dateFin, \"dd/MM/yyyy\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.selectedPlanning.description);\n  }\n}\nfunction ReunionFormComponent_ng_container_73_option_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 92);\n    i0.ɵɵelement(1, \"i\", 93);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r21 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r21._id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", user_r21.username, \" \");\n  }\n}\nfunction ReunionFormComponent_ng_container_73_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ReunionFormComponent_ng_container_73_option_1_Template, 3, 2, \"option\", 91);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const users_r19 = ctx.ngIf;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", users_r19);\n  }\n}\nfunction ReunionFormComponent_i_83_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 94);\n  }\n}\nfunction ReunionFormComponent_i_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 95);\n  }\n}\nfunction ReunionFormComponent_i_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 96);\n  }\n}\nexport let ReunionFormComponent = /*#__PURE__*/(() => {\n  class ReunionFormComponent {\n    constructor(fb, reunionService, planningService, userService, route, router, authService, toastService) {\n      this.fb = fb;\n      this.reunionService = reunionService;\n      this.planningService = planningService;\n      this.userService = userService;\n      this.route = route;\n      this.router = router;\n      this.authService = authService;\n      this.toastService = toastService;\n      this.plannings = [];\n      this.loading = true;\n      this.isSubmitting = false;\n      this.error = null;\n      this.successMessage = null;\n      this.isEditMode = false;\n      this.currentReunionId = null;\n      this.planningIdFromUrl = null;\n      this.selectedPlanning = null;\n      this.lienVisioError = null;\n      this.isCheckingLienVisio = false;\n      this.reunionForm = this.fb.group({\n        titre: ['', Validators.required],\n        description: [''],\n        date: ['', Validators.required],\n        heureDebut: ['', Validators.required],\n        heureFin: ['', Validators.required],\n        lieu: [''],\n        lienVisio: [''],\n        planning: ['', Validators.required],\n        participants: [[]]\n      });\n      this.users$ = this.userService.getAllUsers();\n    }\n    ngOnInit() {\n      this.loadPlannings();\n      this.checkEditMode();\n      this.checkPlanningParam();\n      this.setupLienVisioValidation();\n    }\n    checkEditMode() {\n      const reunionId = this.route.snapshot.paramMap.get('id');\n      if (reunionId) {\n        this.isEditMode = true;\n        this.currentReunionId = reunionId;\n        this.loadReunion(reunionId);\n      }\n    }\n    loadPlannings() {\n      const userId = this.authService.getCurrentUserId();\n      if (!userId) return;\n      this.planningService.getPlanningsByUser(userId).subscribe({\n        next: response => {\n          this.plannings = response.plannings || [];\n          console.log('🔍 Plannings chargés:', this.plannings);\n          console.log('🔍 Premier planning:', this.plannings[0]);\n        },\n        error: err => {\n          this.error = err;\n          console.error('❌ Erreur chargement plannings:', err);\n        }\n      });\n    }\n    loadReunion(id) {\n      this.reunionService.getReunionById(id).subscribe({\n        next: reunion => {\n          this.reunionForm.patchValue({\n            titre: reunion.titre,\n            description: reunion.description,\n            dateDebut: this.formatDateForInput(reunion.dateDebut),\n            dateFin: this.formatDateForInput(reunion.dateFin),\n            lieu: reunion.lieu,\n            lienVisio: reunion.lienVisio,\n            planningId: reunion.planningId,\n            participants: reunion.participants\n          });\n          this.loading = false;\n        },\n        error: err => {\n          this.error = err;\n          this.loading = false;\n        }\n      });\n    }\n    formatDateForInput(date) {\n      return new Date(date).toISOString().slice(0, 16); // yyyy-MM-ddTHH:mm\n    }\n\n    checkPlanningParam() {\n      const planningId = this.route.snapshot.queryParamMap.get('planningId');\n      if (planningId) {\n        this.planningIdFromUrl = planningId;\n        // Si un ID de planning est fourni dans les paramètres de requête, le sélectionner automatiquement\n        this.reunionForm.patchValue({\n          planning: planningId\n        });\n        // Récupérer les détails du planning pour l'affichage\n        this.planningService.getPlanningById(planningId).subscribe({\n          next: response => {\n            this.selectedPlanning = response.planning;\n            // Ajouter le planning à la liste locale pour la validation\n            if (this.selectedPlanning && !this.plannings.find(p => p._id === planningId)) {\n              this.plannings.push(this.selectedPlanning);\n              console.log('✅ Planning ajouté à la liste locale pour validation:', this.selectedPlanning);\n            }\n          },\n          error: err => {\n            console.error('Erreur lors de la récupération du planning:', err);\n            this.toastService.error('Planning introuvable', 'Le planning sélectionné n\\'existe pas ou vous n\\'avez pas les permissions pour y accéder');\n          }\n        });\n      }\n    }\n    onSubmit() {\n      if (this.reunionForm.invalid || !this.canSubmit()) {\n        this.toastService.warning('Formulaire invalide', 'Veuillez corriger les erreurs avant de soumettre le formulaire');\n        return;\n      }\n      // Validation de la date par rapport au planning\n      if (!this.validateDateInPlanningRange()) {\n        return;\n      }\n      this.isSubmitting = true;\n      this.error = null;\n      this.successMessage = null;\n      const formValue = this.reunionForm.value;\n      const date = formValue.date; // already in yyyy-MM-dd format from input[type=date]\n      const heureDebut = formValue.heureDebut; // already in HH:mm format from input[type=time]\n      const heureFin = formValue.heureFin;\n      const reunionData = {\n        titre: formValue.titre,\n        description: formValue.description,\n        date: date,\n        heureDebut: heureDebut,\n        heureFin: heureFin,\n        lieu: formValue.lieu,\n        lienVisio: formValue.lienVisio,\n        planning: formValue.planning,\n        participants: formValue.participants || []\n      };\n      console.log('🔍 Données de la réunion à envoyer:', reunionData);\n      console.log('🔍 Planning ID sélectionné:', formValue.planning);\n      console.log('🔍 Type du planning ID:', typeof formValue.planning);\n      console.log('🔍 Plannings disponibles:', this.plannings);\n      this.reunionService.createReunion(reunionData).subscribe({\n        next: () => {\n          this.isSubmitting = false;\n          this.toastService.success('Réunion créée', 'La réunion a été créée avec succès');\n          // Réinitialiser le formulaire pour permettre la création d'une nouvelle réunion\n          this.resetForm();\n          // Redirection immédiate\n          this.router.navigate(['/reunions']);\n        },\n        error: err => {\n          this.isSubmitting = false;\n          console.error('Erreur lors de la création de la réunion:', err);\n          if (err.status === 403) {\n            this.toastService.accessDenied('créer une réunion', err.status);\n          } else if (err.status === 401) {\n            this.toastService.error('Non autorisé', 'Vous devez être connecté pour créer une réunion');\n          } else {\n            const errorMessage = err.error?.message || 'Erreur lors de la création de la réunion';\n            this.toastService.error('Erreur de création', errorMessage, 8000);\n          }\n        }\n      });\n    }\n    resetForm() {\n      // Reset the form to its initial state\n      this.reunionForm.reset({\n        titre: '',\n        description: '',\n        date: '',\n        heureDebut: '',\n        heureFin: '',\n        lieu: '',\n        lienVisio: '',\n        planning: '',\n        participants: []\n      });\n      // Mark the form as pristine and untouched to reset validation states\n      this.reunionForm.markAsPristine();\n      this.reunionForm.markAsUntouched();\n    }\n    goReunion() {\n      this.router.navigate(['/reunions']);\n    }\n    /**\n     * Configure la validation en temps réel du lien visio avec debounce\n     */\n    setupLienVisioValidation() {\n      this.reunionForm.get('lienVisio')?.valueChanges.pipe(debounceTime(500),\n      // Attendre 500ms après la dernière saisie\n      distinctUntilChanged() // Ne déclencher que si la valeur a changé\n      ).subscribe(value => {\n        if (value && value.trim() !== '') {\n          this.checkLienVisioUniqueness(value.trim());\n        } else {\n          this.lienVisioError = null;\n        }\n      });\n    }\n    /**\n     * Vérifie l'unicité du lien visio\n     * @param lienVisio Le lien à vérifier\n     */\n    checkLienVisioUniqueness(lienVisio) {\n      if (!lienVisio || lienVisio.trim() === '') {\n        this.lienVisioError = null;\n        return;\n      }\n      this.isCheckingLienVisio = true;\n      this.lienVisioError = null;\n      // Utiliser la nouvelle route dédiée pour vérifier l'unicité\n      this.reunionService.checkLienVisioUniqueness(lienVisio, this.currentReunionId || undefined).subscribe({\n        next: response => {\n          this.isCheckingLienVisio = false;\n          if (response.success && !response.isUnique) {\n            this.lienVisioError = `Ce lien est déjà utilisé par la réunion \"${response.conflictWith?.titre}\"`;\n          } else {\n            this.lienVisioError = null;\n          }\n        },\n        error: error => {\n          this.isCheckingLienVisio = false;\n          console.error('Erreur lors de la vérification du lien visio:', error);\n          this.lienVisioError = 'Erreur lors de la vérification du lien';\n        }\n      });\n    }\n    /**\n     * Vérifie si le formulaire peut être soumis\n     */\n    canSubmit() {\n      return this.reunionForm.valid && !this.lienVisioError && !this.isCheckingLienVisio;\n    }\n    /**\n     * Valide que la date de la réunion est dans l'intervalle du planning sélectionné\n     */\n    validateDateInPlanningRange() {\n      const formValue = this.reunionForm.value;\n      const reunionDate = formValue.date;\n      const planningId = formValue.planning;\n      if (!reunionDate || !planningId) {\n        return true; // Si pas de date ou planning, laisser la validation backend gérer\n      }\n      // Chercher d'abord dans la liste locale, puis dans selectedPlanning\n      let selectedPlanning = this.plannings.find(p => p._id === planningId);\n      if (!selectedPlanning && this.selectedPlanning && this.selectedPlanning._id === planningId) {\n        selectedPlanning = this.selectedPlanning;\n      }\n      if (!selectedPlanning) {\n        console.warn('⚠️ Planning non trouvé pour validation:', planningId);\n        console.log('📋 Plannings disponibles:', this.plannings.map(p => ({\n          id: p._id,\n          titre: p.titre\n        })));\n        console.log('🎯 Selected planning:', this.selectedPlanning);\n        // Ne pas bloquer si le planning n'est pas trouvé - laisser le backend valider\n        return true;\n      }\n      // Convertir les dates pour comparaison\n      const reunionDateObj = new Date(reunionDate);\n      const planningDateDebut = new Date(selectedPlanning.dateDebut);\n      const planningDateFin = new Date(selectedPlanning.dateFin);\n      // Comparer seulement les dates (sans les heures)\n      reunionDateObj.setHours(0, 0, 0, 0);\n      planningDateDebut.setHours(0, 0, 0, 0);\n      planningDateFin.setHours(0, 0, 0, 0);\n      if (reunionDateObj < planningDateDebut || reunionDateObj > planningDateFin) {\n        this.toastService.error('Date invalide', `La date de la réunion doit être comprise entre le ${planningDateDebut.toLocaleDateString('fr-FR')} et le ${planningDateFin.toLocaleDateString('fr-FR')} (période du planning \"${selectedPlanning.titre}\")`, 10000);\n        return false;\n      }\n      return true;\n    }\n    static {\n      this.ɵfac = function ReunionFormComponent_Factory(t) {\n        return new (t || ReunionFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ReunionService), i0.ɵɵdirectiveInject(i3.PlanningService), i0.ɵɵdirectiveInject(i4.DataService), i0.ɵɵdirectiveInject(i5.ActivatedRoute), i0.ɵɵdirectiveInject(i5.Router), i0.ɵɵdirectiveInject(i6.AuthuserService), i0.ɵɵdirectiveInject(i7.ToastService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ReunionFormComponent,\n        selectors: [[\"app-reunion-form\"]],\n        decls: 87,\n        vars: 24,\n        consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-6\", \"max-w-3xl\"], [1, \"bg-gradient-to-r\", \"from-purple-600\", \"to-indigo-600\", \"rounded-t-lg\", \"p-6\", \"text-white\", \"mb-0\"], [1, \"text-2xl\", \"font-bold\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-plus-circle\", \"mr-3\", \"text-purple-200\"], [1, \"text-purple-100\", \"mt-2\"], [1, \"bg-white\", \"rounded-b-lg\", \"shadow-lg\", \"p-6\", \"border-t-0\", 3, \"formGroup\", \"ngSubmit\"], [\"class\", \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [\"class\", \"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [1, \"grid\", \"grid-cols-1\", \"gap-6\"], [1, \"relative\"], [\"for\", \"titre\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-purple-700\", \"mb-2\"], [1, \"fas\", \"fa-tag\", \"mr-2\", \"text-purple-500\"], [\"id\", \"titre\", \"type\", \"text\", \"formControlName\", \"titre\", \"placeholder\", \"Nom de votre r\\u00E9union...\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-lg\", \"border-2\", \"border-purple-200\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"px-4\", \"py-3\"], [\"class\", \"text-red-500 text-sm mt-2 flex items-center\", 4, \"ngIf\"], [\"for\", \"description\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-indigo-700\", \"mb-2\"], [1, \"fas\", \"fa-align-left\", \"mr-2\", \"text-indigo-500\"], [\"id\", \"description\", \"formControlName\", \"description\", \"rows\", \"3\", \"placeholder\", \"D\\u00E9crivez votre r\\u00E9union...\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-lg\", \"border-2\", \"border-indigo-200\", \"shadow-sm\", \"focus:border-indigo-500\", \"focus:ring-indigo-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"px-4\", \"py-3\"], [1, \"bg-gradient-to-r\", \"from-blue-50\", \"to-cyan-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-blue-200\"], [1, \"text-lg\", \"font-semibold\", \"text-blue-800\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-calendar-clock\", \"mr-2\", \"text-blue-600\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-3\", \"gap-6\"], [\"for\", \"date\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-blue-700\", \"mb-2\"], [1, \"fas\", \"fa-calendar\", \"mr-2\", \"text-blue-500\"], [\"id\", \"date\", \"type\", \"date\", \"formControlName\", \"date\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-lg\", \"border-2\", \"border-blue-200\", \"shadow-sm\", \"focus:border-blue-500\", \"focus:ring-blue-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"px-4\", \"py-3\"], [\"for\", \"heureDebut\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-green-700\", \"mb-2\"], [1, \"fas\", \"fa-play\", \"mr-2\", \"text-green-500\"], [\"id\", \"heureDebut\", \"type\", \"time\", \"formControlName\", \"heureDebut\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-lg\", \"border-2\", \"border-green-200\", \"shadow-sm\", \"focus:border-green-500\", \"focus:ring-green-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"px-4\", \"py-3\"], [\"for\", \"heureFin\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-red-700\", \"mb-2\"], [1, \"fas\", \"fa-stop\", \"mr-2\", \"text-red-500\"], [\"id\", \"heureFin\", \"type\", \"time\", \"formControlName\", \"heureFin\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-lg\", \"border-2\", \"border-red-200\", \"shadow-sm\", \"focus:border-red-500\", \"focus:ring-red-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"px-4\", \"py-3\"], [1, \"bg-gradient-to-r\", \"from-orange-50\", \"to-yellow-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-orange-200\"], [1, \"text-lg\", \"font-semibold\", \"text-orange-800\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-map-marker-alt\", \"mr-2\", \"text-orange-600\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-6\"], [\"for\", \"lieu\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-orange-700\", \"mb-2\"], [1, \"fas\", \"fa-map-marker-alt\", \"mr-2\", \"text-orange-500\"], [\"id\", \"lieu\", \"type\", \"text\", \"formControlName\", \"lieu\", \"placeholder\", \"Salle 101, Bureau A, Google Meet...\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-lg\", \"border-2\", \"border-orange-200\", \"shadow-sm\", \"focus:border-orange-500\", \"focus:ring-orange-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"px-4\", \"py-3\"], [\"for\", \"lienVisio\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-cyan-700\", \"mb-2\"], [1, \"fas\", \"fa-video\", \"mr-2\", \"text-cyan-500\"], [\"class\", \"ml-2 text-xs text-cyan-500\", 4, \"ngIf\"], [\"id\", \"lienVisio\", \"type\", \"url\", \"formControlName\", \"lienVisio\", \"placeholder\", \"https://meet.google.com/...\"], [\"class\", \"text-red-500 text-sm mt-2 flex items-center bg-red-50 p-2 rounded border border-red-200\", 4, \"ngIf\"], [\"class\", \"text-green-600 text-sm mt-2 flex items-center bg-green-50 p-2 rounded border border-green-200\", 4, \"ngIf\"], [\"class\", \"bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg border border-purple-200\", 4, \"ngIf\"], [\"class\", \"bg-gradient-to-r from-purple-50 to-indigo-50 p-4 rounded-lg border-2 border-purple-200 shadow-sm\", 4, \"ngIf\"], [1, \"bg-gradient-to-r\", \"from-emerald-50\", \"to-teal-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-emerald-200\"], [1, \"text-lg\", \"font-semibold\", \"text-emerald-800\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-users\", \"mr-2\", \"text-emerald-600\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-emerald-700\", \"mb-2\"], [1, \"fas\", \"fa-user-friends\", \"mr-2\", \"text-emerald-500\"], [\"formControlName\", \"participants\", \"multiple\", \"\", 1, \"mt-1\", \"block\", \"w-full\", \"px-4\", \"py-3\", \"border-2\", \"border-emerald-200\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-emerald-500\", \"focus:border-emerald-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"text-sm\", \"min-h-[120px]\"], [4, \"ngIf\"], [1, \"text-xs\", \"text-emerald-600\", \"mt-2\"], [1, \"fas\", \"fa-info-circle\", \"mr-1\"], [1, \"mt-8\", \"flex\", \"justify-end\", \"space-x-4\", \"bg-gray-50\", \"p-4\", \"rounded-lg\", \"border-t\", \"border-gray-200\"], [\"type\", \"button\", 1, \"px-6\", \"py-3\", \"border-2\", \"border-gray-300\", \"rounded-lg\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"hover:bg-gray-100\", \"hover:border-gray-400\", \"transition-all\", \"duration-200\", \"flex\", \"items-center\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"mr-2\"], [\"type\", \"submit\", 1, \"px-6\", \"py-3\", \"rounded-lg\", \"text-sm\", \"font-medium\", \"text-white\", \"bg-gradient-to-r\", \"from-purple-600\", \"to-indigo-600\", \"hover:from-purple-700\", \"hover:to-indigo-700\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"transition-all\", \"duration-200\", \"flex\", \"items-center\", \"shadow-lg\", 3, \"disabled\"], [\"class\", \"fas fa-save mr-2\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner fa-spin mr-2\", 4, \"ngIf\"], [\"class\", \"fas fa-search mr-2\", 4, \"ngIf\"], [1, \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [1, \"bg-green-100\", \"border\", \"border-green-400\", \"text-green-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [1, \"text-red-500\", \"text-sm\", \"mt-2\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-exclamation-circle\", \"mr-1\"], [1, \"ml-2\", \"text-xs\", \"text-cyan-500\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", 1, \"inline\", \"h-3\", \"w-3\", \"animate-spin\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"10\", \"stroke\", \"currentColor\", \"stroke-width\", \"4\", 1, \"opacity-25\"], [\"fill\", \"currentColor\", \"d\", \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\", 1, \"opacity-75\"], [1, \"text-red-500\", \"text-sm\", \"mt-2\", \"flex\", \"items-center\", \"bg-red-50\", \"p-2\", \"rounded\", \"border\", \"border-red-200\"], [1, \"fas\", \"fa-exclamation-triangle\", \"mr-2\"], [1, \"text-green-600\", \"text-sm\", \"mt-2\", \"flex\", \"items-center\", \"bg-green-50\", \"p-2\", \"rounded\", \"border\", \"border-green-200\"], [1, \"fas\", \"fa-check-circle\", \"mr-2\"], [1, \"bg-gradient-to-r\", \"from-purple-50\", \"to-pink-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-purple-200\"], [1, \"text-lg\", \"font-semibold\", \"text-purple-800\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-calendar-alt\", \"mr-2\", \"text-purple-600\"], [\"for\", \"planning\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-purple-700\", \"mb-2\"], [1, \"fas\", \"fa-list\", \"mr-2\", \"text-purple-500\"], [\"id\", \"planning\", \"formControlName\", \"planning\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-lg\", \"border-2\", \"border-purple-200\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"px-4\", \"py-3\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [3, \"value\"], [1, \"bg-gradient-to-r\", \"from-purple-50\", \"to-indigo-50\", \"p-4\", \"rounded-lg\", \"border-2\", \"border-purple-200\", \"shadow-sm\"], [1, \"text-lg\", \"font-semibold\", \"text-purple-800\", \"mb-3\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-calendar-check\", \"mr-2\", \"text-purple-600\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"font-semibold\", \"text-purple-800\", \"text-lg\"], [1, \"text-sm\", \"font-medium\", \"text-red-600\", \"bg-red-50\", \"px-2\", \"py-1\", \"rounded-full\", \"border\", \"border-red-200\"], [1, \"fas\", \"fa-clock\", \"mr-1\"], [\"class\", \"text-sm text-indigo-700 mt-2 bg-indigo-50 p-2 rounded border-l-4 border-indigo-300\", 4, \"ngIf\"], [1, \"text-sm\", \"text-indigo-700\", \"mt-2\", \"bg-indigo-50\", \"p-2\", \"rounded\", \"border-l-4\", \"border-indigo-300\"], [\"class\", \"py-2\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"py-2\", 3, \"value\"], [1, \"fas\", \"fa-user\", \"mr-2\"], [1, \"fas\", \"fa-save\", \"mr-2\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"mr-2\"], [1, \"fas\", \"fa-search\", \"mr-2\"]],\n        template: function ReunionFormComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\", 2);\n            i0.ɵɵelement(3, \"i\", 3);\n            i0.ɵɵtext(4);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"p\", 4);\n            i0.ɵɵtext(6);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"form\", 5);\n            i0.ɵɵlistener(\"ngSubmit\", function ReunionFormComponent_Template_form_ngSubmit_7_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵtemplate(8, ReunionFormComponent_div_8_Template, 2, 1, \"div\", 6);\n            i0.ɵɵtemplate(9, ReunionFormComponent_div_9_Template, 2, 1, \"div\", 7);\n            i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9)(12, \"label\", 10);\n            i0.ɵɵelement(13, \"i\", 11);\n            i0.ɵɵtext(14, \" Titre * \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(15, \"input\", 12);\n            i0.ɵɵtemplate(16, ReunionFormComponent_div_16_Template, 3, 0, \"div\", 13);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"div\", 9)(18, \"label\", 14);\n            i0.ɵɵelement(19, \"i\", 15);\n            i0.ɵɵtext(20, \" Description \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(21, \"textarea\", 16);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"div\", 17)(23, \"h3\", 18);\n            i0.ɵɵelement(24, \"i\", 19);\n            i0.ɵɵtext(25, \" Planification \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"div\", 20)(27, \"div\")(28, \"label\", 21);\n            i0.ɵɵelement(29, \"i\", 22);\n            i0.ɵɵtext(30, \" Date * \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(31, \"input\", 23);\n            i0.ɵɵtemplate(32, ReunionFormComponent_div_32_Template, 3, 0, \"div\", 13);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"div\")(34, \"label\", 24);\n            i0.ɵɵelement(35, \"i\", 25);\n            i0.ɵɵtext(36, \" Heure de d\\u00E9but * \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(37, \"input\", 26);\n            i0.ɵɵtemplate(38, ReunionFormComponent_div_38_Template, 3, 0, \"div\", 13);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"div\")(40, \"label\", 27);\n            i0.ɵɵelement(41, \"i\", 28);\n            i0.ɵɵtext(42, \" Heure de fin * \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(43, \"input\", 29);\n            i0.ɵɵtemplate(44, ReunionFormComponent_div_44_Template, 3, 0, \"div\", 13);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(45, \"div\", 30)(46, \"h3\", 31);\n            i0.ɵɵelement(47, \"i\", 32);\n            i0.ɵɵtext(48, \" Localisation \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"div\", 33)(50, \"div\")(51, \"label\", 34);\n            i0.ɵɵelement(52, \"i\", 35);\n            i0.ɵɵtext(53, \" Lieu / Salle \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(54, \"input\", 36);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(55, \"div\")(56, \"label\", 37);\n            i0.ɵɵelement(57, \"i\", 38);\n            i0.ɵɵtext(58, \" Lien Visio \");\n            i0.ɵɵtemplate(59, ReunionFormComponent_span_59_Template, 5, 0, \"span\", 39);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(60, \"input\", 40);\n            i0.ɵɵtemplate(61, ReunionFormComponent_div_61_Template, 3, 1, \"div\", 41);\n            i0.ɵɵtemplate(62, ReunionFormComponent_div_62_Template, 3, 0, \"div\", 42);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(63, ReunionFormComponent_div_63_Template, 12, 2, \"div\", 43);\n            i0.ɵɵtemplate(64, ReunionFormComponent_div_64_Template, 14, 10, \"div\", 44);\n            i0.ɵɵelementStart(65, \"div\", 45)(66, \"h3\", 46);\n            i0.ɵɵelement(67, \"i\", 47);\n            i0.ɵɵtext(68, \" Participants \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(69, \"label\", 48);\n            i0.ɵɵelement(70, \"i\", 49);\n            i0.ɵɵtext(71, \" S\\u00E9lectionnez les participants \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(72, \"select\", 50);\n            i0.ɵɵtemplate(73, ReunionFormComponent_ng_container_73_Template, 2, 1, \"ng-container\", 51);\n            i0.ɵɵpipe(74, \"async\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(75, \"p\", 52);\n            i0.ɵɵelement(76, \"i\", 53);\n            i0.ɵɵtext(77, \" Maintenez Ctrl (ou Cmd) pour s\\u00E9lectionner plusieurs participants \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(78, \"div\", 54)(79, \"button\", 55);\n            i0.ɵɵlistener(\"click\", function ReunionFormComponent_Template_button_click_79_listener() {\n              return ctx.goReunion();\n            });\n            i0.ɵɵelement(80, \"i\", 56);\n            i0.ɵɵtext(81, \" Annuler \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(82, \"button\", 57);\n            i0.ɵɵtemplate(83, ReunionFormComponent_i_83_Template, 1, 0, \"i\", 58);\n            i0.ɵɵtemplate(84, ReunionFormComponent_i_84_Template, 1, 0, \"i\", 59);\n            i0.ɵɵtemplate(85, ReunionFormComponent_i_85_Template, 1, 0, \"i\", 60);\n            i0.ɵɵtext(86);\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            let tmp_5_0;\n            let tmp_6_0;\n            let tmp_7_0;\n            let tmp_8_0;\n            let tmp_12_0;\n            i0.ɵɵadvance(4);\n            i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Modifier la R\\u00E9union\" : \"Nouvelle R\\u00E9union\", \" \");\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Modifiez les d\\u00E9tails de votre r\\u00E9union\" : \"Cr\\u00E9ez une nouvelle r\\u00E9union pour votre \\u00E9quipe\", \" \");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"formGroup\", ctx.reunionForm);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.successMessage);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.reunionForm.get(\"titre\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.reunionForm.get(\"titre\")) == null ? null : tmp_5_0.touched));\n            i0.ɵɵadvance(16);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx.reunionForm.get(\"date\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx.reunionForm.get(\"date\")) == null ? null : tmp_6_0.touched));\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx.reunionForm.get(\"heureDebut\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx.reunionForm.get(\"heureDebut\")) == null ? null : tmp_7_0.touched));\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx.reunionForm.get(\"heureFin\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx.reunionForm.get(\"heureFin\")) == null ? null : tmp_8_0.touched));\n            i0.ɵɵadvance(15);\n            i0.ɵɵproperty(\"ngIf\", ctx.isCheckingLienVisio);\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassMap(\"mt-1 block w-full rounded-lg shadow-sm focus:ring-cyan-500 focus:ring-2 transition-all duration-200 px-4 py-3 \" + (ctx.lienVisioError ? \"border-2 border-red-300 focus:border-red-500\" : \"border-2 border-cyan-200 focus:border-cyan-500\"));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.lienVisioError);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.lienVisioError && !ctx.isCheckingLienVisio && ((tmp_12_0 = ctx.reunionForm.get(\"lienVisio\")) == null ? null : tmp_12_0.value) && ((tmp_12_0 = ctx.reunionForm.get(\"lienVisio\")) == null ? null : tmp_12_0.value.trim()) !== \"\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.planningIdFromUrl);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.planningIdFromUrl && ctx.selectedPlanning);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(74, 22, ctx.users$));\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"disabled\", !ctx.canSubmit() || ctx.isSubmitting);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isSubmitting && !ctx.isCheckingLienVisio);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isSubmitting);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isCheckingLienVisio);\n            i0.ɵɵadvance(1);\n            i0.ɵɵtextInterpolate1(\" \", ctx.isSubmitting ? \"Enregistrement...\" : ctx.isCheckingLienVisio ? \"V\\u00E9rification...\" : \"Cr\\u00E9er la r\\u00E9union\", \" \");\n          }\n        }\n      });\n    }\n  }\n  return ReunionFormComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}