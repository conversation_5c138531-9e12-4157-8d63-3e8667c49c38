{"ast": null, "code": "import { of } from 'rxjs';\nimport { delay } from 'rxjs/operators';\nimport { MessageType } from '../models/message.model';\nimport * as i0 from \"@angular/core\";\nexport class MockDataService {\n  constructor() {\n    // Utilisateurs de test\n    this.mockUsers = [{\n      id: '1',\n      _id: '1',\n      username: '<PERSON>',\n      email: '<EMAIL>',\n      image: '/assets/images/avatars/alice.jpg',\n      isOnline: true,\n      isActive: true,\n      role: 'developer'\n    }, {\n      id: '2',\n      _id: '2',\n      username: '<PERSON>',\n      email: '<EMAIL>',\n      image: '/assets/images/avatars/bob.jpg',\n      isOnline: false,\n      isActive: true,\n      role: 'designer'\n    }, {\n      id: '3',\n      _id: '3',\n      username: '<PERSON>',\n      email: '<EMAIL>',\n      image: '/assets/images/avatars/claire.jpg',\n      isOnline: true,\n      isActive: true,\n      role: 'manager'\n    }, {\n      id: '4',\n      _id: '4',\n      username: '<PERSON>',\n      email: '<EMAIL>',\n      image: '/assets/images/avatars/david.jpg',\n      isOnline: true,\n      isActive: true,\n      role: 'developer'\n    }, {\n      id: '5',\n      _id: '5',\n      username: 'Emma <PERSON>',\n      email: '<EMAIL>',\n      image: '/assets/images/avatars/emma.jpg',\n      isOnline: false,\n      isActive: true,\n      role: 'tester'\n    }];\n    // Messages de test\n    this.mockMessages = [{\n      id: '1',\n      content: 'Salut ! Comment ça va ?',\n      type: MessageType.TEXT,\n      timestamp: new Date(Date.now() - 3600000),\n      sender: this.mockUsers[1],\n      isRead: true,\n      conversationId: 'conv1'\n    }, {\n      id: '2',\n      content: 'Ça va bien merci ! Et toi ?',\n      type: MessageType.TEXT,\n      timestamp: new Date(Date.now() - 3500000),\n      sender: this.mockUsers[0],\n      isRead: true,\n      conversationId: 'conv1'\n    }, {\n      id: '3',\n      content: 'Super ! Tu as vu le nouveau design ?',\n      type: MessageType.TEXT,\n      timestamp: new Date(Date.now() - 1800000),\n      sender: this.mockUsers[1],\n      isRead: false,\n      conversationId: 'conv1'\n    }];\n    // Conversations de test\n    this.mockConversations = [{\n      id: 'conv1',\n      participants: [this.mockUsers[0], this.mockUsers[1]],\n      lastMessage: this.mockMessages[2],\n      unreadCount: 1,\n      isGroup: false,\n      createdAt: new Date(Date.now() - 86400000) // 1 day ago\n    }, {\n      id: 'conv2',\n      participants: [this.mockUsers[0], this.mockUsers[2]],\n      lastMessage: {\n        id: '4',\n        content: 'Réunion à 14h ?',\n        type: MessageType.TEXT,\n        timestamp: new Date(Date.now() - 7200000),\n        sender: this.mockUsers[2],\n        isRead: true,\n        conversationId: 'conv2'\n      },\n      unreadCount: 0,\n      isGroup: false,\n      createdAt: new Date(Date.now() - 172800000) // 2 days ago\n    }, {\n      id: 'conv3',\n      participants: [this.mockUsers[0], this.mockUsers[1], this.mockUsers[2], this.mockUsers[3]],\n      lastMessage: {\n        id: '5',\n        content: 'Nouveau projet lancé ! 🚀',\n        type: MessageType.TEXT,\n        timestamp: new Date(Date.now() - 10800000),\n        sender: this.mockUsers[3],\n        isRead: false,\n        conversationId: 'conv3'\n      },\n      unreadCount: 3,\n      isGroup: true,\n      groupName: 'Équipe DevBridge',\n      groupPhoto: '/assets/images/groups/team.jpg',\n      createdAt: new Date(Date.now() - 259200000) // 3 days ago\n    }];\n    // Notifications de test\n    this.mockNotifications = [{\n      id: 'notif1',\n      type: NotificationType.NEW_MESSAGE,\n      content: 'Nouveau message de Bob Dupont',\n      timestamp: new Date(Date.now() - 1800000),\n      isRead: false,\n      userId: '1'\n    }, {\n      id: 'notif2',\n      type: NotificationType.MESSAGE_REACTION,\n      content: 'Alice a réagi à votre message avec ❤️',\n      timestamp: new Date(Date.now() - 3600000),\n      isRead: true,\n      userId: '1'\n    }, {\n      id: 'notif3',\n      type: NotificationType.GROUP_INVITE,\n      content: 'Vous avez été ajouté au groupe \"Équipe DevBridge\"',\n      timestamp: new Date(Date.now() - 7200000),\n      isRead: false,\n      userId: '1'\n    }];\n  }\n  // ============================================================================\n  // MÉTHODES PUBLIQUES POUR LES TESTS\n  // ============================================================================\n  /**\n   * Récupère tous les utilisateurs\n   */\n  getUsers() {\n    return of(this.mockUsers).pipe(delay(500)); // Simule la latence réseau\n  }\n  /**\n   * Récupère toutes les conversations\n   */\n  getConversations() {\n    return of(this.mockConversations).pipe(delay(300));\n  }\n  /**\n   * Récupère une conversation par ID\n   */\n  getConversation(id) {\n    const conversation = this.mockConversations.find(c => c.id === id);\n    return of(conversation || null).pipe(delay(200));\n  }\n  /**\n   * Récupère les messages d'une conversation\n   */\n  getMessages(conversationId) {\n    const messages = this.mockMessages.filter(m => m.conversationId === conversationId);\n    return of(messages).pipe(delay(300));\n  }\n  /**\n   * Récupère toutes les notifications\n   */\n  getNotifications() {\n    return of(this.mockNotifications).pipe(delay(200));\n  }\n  /**\n   * Simule l'envoi d'un message\n   */\n  sendMessage(content, conversationId, senderId) {\n    const newMessage = {\n      id: `msg_${Date.now()}`,\n      content,\n      type: MessageType.TEXT,\n      timestamp: new Date(),\n      sender: this.mockUsers.find(u => u.id === senderId) || this.mockUsers[0],\n      isRead: false,\n      conversationId\n    };\n    // Ajouter le message à la liste\n    this.mockMessages.push(newMessage);\n    // Mettre à jour la conversation\n    const conversation = this.mockConversations.find(c => c.id === conversationId);\n    if (conversation) {\n      conversation.lastMessage = newMessage;\n    }\n    return of(newMessage).pipe(delay(100));\n  }\n  /**\n   * Simule la création d'une conversation\n   */\n  createConversation(userId, currentUserId) {\n    const otherUser = this.mockUsers.find(u => u.id === userId);\n    const currentUser = this.mockUsers.find(u => u.id === currentUserId);\n    if (!otherUser || !currentUser) {\n      throw new Error('Utilisateur non trouvé');\n    }\n    const newConversation = {\n      id: `conv_${Date.now()}`,\n      participants: [currentUser, otherUser],\n      unreadCount: 0,\n      isGroup: false,\n      createdAt: new Date()\n    };\n    this.mockConversations.unshift(newConversation);\n    return of(newConversation).pipe(delay(200));\n  }\n  /**\n   * Récupère l'utilisateur actuel (pour les tests)\n   */\n  getCurrentUser() {\n    return this.mockUsers[0]; // Alice comme utilisateur actuel\n  }\n  /**\n   * Simule la recherche d'utilisateurs\n   */\n  searchUsers(query) {\n    const results = this.mockUsers.filter(user => user.username.toLowerCase().includes(query.toLowerCase()) || user.email.toLowerCase().includes(query.toLowerCase()));\n    return of(results).pipe(delay(300));\n  }\n  /**\n   * Simule la recherche de conversations\n   */\n  searchConversations(query) {\n    const results = this.mockConversations.filter(conv => {\n      if (conv.isGroup) {\n        return conv.groupName?.toLowerCase().includes(query.toLowerCase());\n      } else {\n        return conv.participants?.some(p => p.username.toLowerCase().includes(query.toLowerCase()));\n      }\n    });\n    return of(results).pipe(delay(300));\n  }\n  static {\n    this.ɵfac = function MockDataService_Factory(t) {\n      return new (t || MockDataService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: MockDataService,\n      factory: MockDataService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["of", "delay", "MessageType", "MockDataService", "constructor", "mockUsers", "id", "_id", "username", "email", "image", "isOnline", "isActive", "role", "mockMessages", "content", "type", "TEXT", "timestamp", "Date", "now", "sender", "isRead", "conversationId", "mockConversations", "participants", "lastMessage", "unreadCount", "isGroup", "createdAt", "groupName", "groupPhoto", "mockNotifications", "NotificationType", "NEW_MESSAGE", "userId", "MESSAGE_REACTION", "GROUP_INVITE", "getUsers", "pipe", "getConversations", "getConversation", "conversation", "find", "c", "getMessages", "messages", "filter", "m", "getNotifications", "sendMessage", "senderId", "newMessage", "u", "push", "createConversation", "currentUserId", "otherUser", "currentUser", "Error", "newConversation", "unshift", "getCurrentUser", "searchUsers", "query", "results", "user", "toLowerCase", "includes", "searchConversations", "conv", "some", "p", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\services\\mock-data.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Observable, of, BehaviorSubject } from 'rxjs';\nimport { delay } from 'rxjs/operators';\nimport {\n  User,\n  Conversation,\n  Message,\n  Notification,\n  MessageType,\n  NotificationType,\n} from '../models/message.model';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class MockDataService {\n  // Utilisateurs de test\n  private mockUsers: User[] = [\n    {\n      id: '1',\n      _id: '1',\n      username: '<PERSON>',\n      email: '<EMAIL>',\n      image: '/assets/images/avatars/alice.jpg',\n      isOnline: true,\n      isActive: true,\n      role: 'developer',\n    },\n    {\n      id: '2',\n      _id: '2',\n      username: '<PERSON>',\n      email: '<EMAIL>',\n      image: '/assets/images/avatars/bob.jpg',\n      isOnline: false,\n      isActive: true,\n      role: 'designer',\n    },\n    {\n      id: '3',\n      _id: '3',\n      username: '<PERSON>',\n      email: '<EMAIL>',\n      image: '/assets/images/avatars/claire.jpg',\n      isOnline: true,\n      isActive: true,\n      role: 'manager',\n    },\n    {\n      id: '4',\n      _id: '4',\n      username: '<PERSON>',\n      email: '<EMAIL>',\n      image: '/assets/images/avatars/david.jpg',\n      isOnline: true,\n      isActive: true,\n      role: 'developer',\n    },\n    {\n      id: '5',\n      _id: '5',\n      username: 'Emma Wilson',\n      email: '<EMAIL>',\n      image: '/assets/images/avatars/emma.jpg',\n      isOnline: false,\n      isActive: true,\n      role: 'tester',\n    },\n  ];\n\n  // Messages de test\n  private mockMessages: Message[] = [\n    {\n      id: '1',\n      content: 'Salut ! Comment ça va ?',\n      type: MessageType.TEXT,\n      timestamp: new Date(Date.now() - 3600000), // 1h ago\n      sender: this.mockUsers[1],\n      isRead: true,\n      conversationId: 'conv1',\n    },\n    {\n      id: '2',\n      content: 'Ça va bien merci ! Et toi ?',\n      type: MessageType.TEXT,\n      timestamp: new Date(Date.now() - 3500000), // 58min ago\n      sender: this.mockUsers[0], // Current user\n      isRead: true,\n      conversationId: 'conv1',\n    },\n    {\n      id: '3',\n      content: 'Super ! Tu as vu le nouveau design ?',\n      type: MessageType.TEXT,\n      timestamp: new Date(Date.now() - 1800000), // 30min ago\n      sender: this.mockUsers[1],\n      isRead: false,\n      conversationId: 'conv1',\n    },\n  ];\n\n  // Conversations de test\n  private mockConversations: Conversation[] = [\n    {\n      id: 'conv1',\n      participants: [this.mockUsers[0], this.mockUsers[1]],\n      lastMessage: this.mockMessages[2],\n      unreadCount: 1,\n      isGroup: false,\n      createdAt: new Date(Date.now() - 86400000), // 1 day ago\n    },\n    {\n      id: 'conv2',\n      participants: [this.mockUsers[0], this.mockUsers[2]],\n      lastMessage: {\n        id: '4',\n        content: 'Réunion à 14h ?',\n        type: MessageType.TEXT,\n        timestamp: new Date(Date.now() - 7200000), // 2h ago\n        sender: this.mockUsers[2],\n        isRead: true,\n        conversationId: 'conv2',\n      },\n      unreadCount: 0,\n      isGroup: false,\n      createdAt: new Date(Date.now() - 172800000), // 2 days ago\n    },\n    {\n      id: 'conv3',\n      participants: [\n        this.mockUsers[0],\n        this.mockUsers[1],\n        this.mockUsers[2],\n        this.mockUsers[3],\n      ],\n      lastMessage: {\n        id: '5',\n        content: 'Nouveau projet lancé ! 🚀',\n        type: MessageType.TEXT,\n        timestamp: new Date(Date.now() - 10800000), // 3h ago\n        sender: this.mockUsers[3],\n        isRead: false,\n        conversationId: 'conv3',\n      },\n      unreadCount: 3,\n      isGroup: true,\n      groupName: 'Équipe DevBridge',\n      groupPhoto: '/assets/images/groups/team.jpg',\n      createdAt: new Date(Date.now() - 259200000), // 3 days ago\n    },\n  ];\n\n  // Notifications de test\n  private mockNotifications: Notification[] = [\n    {\n      id: 'notif1',\n      type: NotificationType.NEW_MESSAGE,\n      content: 'Nouveau message de Bob Dupont',\n      timestamp: new Date(Date.now() - 1800000), // 30min ago\n      isRead: false,\n      userId: '1',\n    },\n    {\n      id: 'notif2',\n      type: NotificationType.MESSAGE_REACTION,\n      content: 'Alice a réagi à votre message avec ❤️',\n      timestamp: new Date(Date.now() - 3600000), // 1h ago\n      isRead: true,\n      userId: '1',\n    },\n    {\n      id: 'notif3',\n      type: NotificationType.GROUP_INVITE,\n      content: 'Vous avez été ajouté au groupe \"Équipe DevBridge\"',\n      timestamp: new Date(Date.now() - 7200000), // 2h ago\n      isRead: false,\n      userId: '1',\n    },\n  ];\n\n  constructor() {}\n\n  // ============================================================================\n  // MÉTHODES PUBLIQUES POUR LES TESTS\n  // ============================================================================\n\n  /**\n   * Récupère tous les utilisateurs\n   */\n  getUsers(): Observable<User[]> {\n    return of(this.mockUsers).pipe(delay(500)); // Simule la latence réseau\n  }\n\n  /**\n   * Récupère toutes les conversations\n   */\n  getConversations(): Observable<Conversation[]> {\n    return of(this.mockConversations).pipe(delay(300));\n  }\n\n  /**\n   * Récupère une conversation par ID\n   */\n  getConversation(id: string): Observable<Conversation | null> {\n    const conversation = this.mockConversations.find((c) => c.id === id);\n    return of(conversation || null).pipe(delay(200));\n  }\n\n  /**\n   * Récupère les messages d'une conversation\n   */\n  getMessages(conversationId: string): Observable<Message[]> {\n    const messages = this.mockMessages.filter(\n      (m) => m.conversationId === conversationId\n    );\n    return of(messages).pipe(delay(300));\n  }\n\n  /**\n   * Récupère toutes les notifications\n   */\n  getNotifications(): Observable<Notification[]> {\n    return of(this.mockNotifications).pipe(delay(200));\n  }\n\n  /**\n   * Simule l'envoi d'un message\n   */\n  sendMessage(\n    content: string,\n    conversationId: string,\n    senderId: string\n  ): Observable<Message> {\n    const newMessage: Message = {\n      id: `msg_${Date.now()}`,\n      content,\n      type: MessageType.TEXT,\n      timestamp: new Date(),\n      sender:\n        this.mockUsers.find((u) => u.id === senderId) || this.mockUsers[0],\n      isRead: false,\n      conversationId,\n    };\n\n    // Ajouter le message à la liste\n    this.mockMessages.push(newMessage);\n\n    // Mettre à jour la conversation\n    const conversation = this.mockConversations.find(\n      (c) => c.id === conversationId\n    );\n    if (conversation) {\n      conversation.lastMessage = newMessage;\n    }\n\n    return of(newMessage).pipe(delay(100));\n  }\n\n  /**\n   * Simule la création d'une conversation\n   */\n  createConversation(\n    userId: string,\n    currentUserId: string\n  ): Observable<Conversation> {\n    const otherUser = this.mockUsers.find((u) => u.id === userId);\n    const currentUser = this.mockUsers.find((u) => u.id === currentUserId);\n\n    if (!otherUser || !currentUser) {\n      throw new Error('Utilisateur non trouvé');\n    }\n\n    const newConversation: Conversation = {\n      id: `conv_${Date.now()}`,\n      participants: [currentUser, otherUser],\n      unreadCount: 0,\n      isGroup: false,\n      createdAt: new Date(),\n    };\n\n    this.mockConversations.unshift(newConversation);\n    return of(newConversation).pipe(delay(200));\n  }\n\n  /**\n   * Récupère l'utilisateur actuel (pour les tests)\n   */\n  getCurrentUser(): User {\n    return this.mockUsers[0]; // Alice comme utilisateur actuel\n  }\n\n  /**\n   * Simule la recherche d'utilisateurs\n   */\n  searchUsers(query: string): Observable<User[]> {\n    const results = this.mockUsers.filter(\n      (user) =>\n        user.username.toLowerCase().includes(query.toLowerCase()) ||\n        user.email.toLowerCase().includes(query.toLowerCase())\n    );\n    return of(results).pipe(delay(300));\n  }\n\n  /**\n   * Simule la recherche de conversations\n   */\n  searchConversations(query: string): Observable<Conversation[]> {\n    const results = this.mockConversations.filter((conv) => {\n      if (conv.isGroup) {\n        return conv.groupName?.toLowerCase().includes(query.toLowerCase());\n      } else {\n        return conv.participants?.some((p) =>\n          p.username.toLowerCase().includes(query.toLowerCase())\n        );\n      }\n    });\n    return of(results).pipe(delay(300));\n  }\n}\n"], "mappings": "AACA,SAAqBA,EAAE,QAAyB,MAAM;AACtD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAKEC,WAAW,QAEN,yBAAyB;;AAKhC,OAAM,MAAOC,eAAe;EAqK1BC,YAAA;IApKA;IACQ,KAAAC,SAAS,GAAW,CAC1B;MACEC,EAAE,EAAE,GAAG;MACPC,GAAG,EAAE,GAAG;MACRC,QAAQ,EAAE,cAAc;MACxBC,KAAK,EAAE,qBAAqB;MAC5BC,KAAK,EAAE,kCAAkC;MACzCC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE;KACP,EACD;MACEP,EAAE,EAAE,GAAG;MACPC,GAAG,EAAE,GAAG;MACRC,QAAQ,EAAE,YAAY;MACtBC,KAAK,EAAE,mBAAmB;MAC1BC,KAAK,EAAE,gCAAgC;MACvCC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE;KACP,EACD;MACEP,EAAE,EAAE,GAAG;MACPC,GAAG,EAAE,GAAG;MACRC,QAAQ,EAAE,iBAAiB;MAC3BC,KAAK,EAAE,sBAAsB;MAC7BC,KAAK,EAAE,mCAAmC;MAC1CC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE;KACP,EACD;MACEP,EAAE,EAAE,GAAG;MACPC,GAAG,EAAE,GAAG;MACRC,QAAQ,EAAE,YAAY;MACtBC,KAAK,EAAE,qBAAqB;MAC5BC,KAAK,EAAE,kCAAkC;MACzCC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE;KACP,EACD;MACEP,EAAE,EAAE,GAAG;MACPC,GAAG,EAAE,GAAG;MACRC,QAAQ,EAAE,aAAa;MACvBC,KAAK,EAAE,oBAAoB;MAC3BC,KAAK,EAAE,iCAAiC;MACxCC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE;KACP,CACF;IAED;IACQ,KAAAC,YAAY,GAAc,CAChC;MACER,EAAE,EAAE,GAAG;MACPS,OAAO,EAAE,yBAAyB;MAClCC,IAAI,EAAEd,WAAW,CAACe,IAAI;MACtBC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,OAAO,CAAC;MACzCC,MAAM,EAAE,IAAI,CAAChB,SAAS,CAAC,CAAC,CAAC;MACzBiB,MAAM,EAAE,IAAI;MACZC,cAAc,EAAE;KACjB,EACD;MACEjB,EAAE,EAAE,GAAG;MACPS,OAAO,EAAE,6BAA6B;MACtCC,IAAI,EAAEd,WAAW,CAACe,IAAI;MACtBC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,OAAO,CAAC;MACzCC,MAAM,EAAE,IAAI,CAAChB,SAAS,CAAC,CAAC,CAAC;MACzBiB,MAAM,EAAE,IAAI;MACZC,cAAc,EAAE;KACjB,EACD;MACEjB,EAAE,EAAE,GAAG;MACPS,OAAO,EAAE,sCAAsC;MAC/CC,IAAI,EAAEd,WAAW,CAACe,IAAI;MACtBC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,OAAO,CAAC;MACzCC,MAAM,EAAE,IAAI,CAAChB,SAAS,CAAC,CAAC,CAAC;MACzBiB,MAAM,EAAE,KAAK;MACbC,cAAc,EAAE;KACjB,CACF;IAED;IACQ,KAAAC,iBAAiB,GAAmB,CAC1C;MACElB,EAAE,EAAE,OAAO;MACXmB,YAAY,EAAE,CAAC,IAAI,CAACpB,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,CAACA,SAAS,CAAC,CAAC,CAAC,CAAC;MACpDqB,WAAW,EAAE,IAAI,CAACZ,YAAY,CAAC,CAAC,CAAC;MACjCa,WAAW,EAAE,CAAC;MACdC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,IAAIV,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,QAAQ,CAAC,CAAE;KAC7C,EACD;MACEd,EAAE,EAAE,OAAO;MACXmB,YAAY,EAAE,CAAC,IAAI,CAACpB,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,CAACA,SAAS,CAAC,CAAC,CAAC,CAAC;MACpDqB,WAAW,EAAE;QACXpB,EAAE,EAAE,GAAG;QACPS,OAAO,EAAE,iBAAiB;QAC1BC,IAAI,EAAEd,WAAW,CAACe,IAAI;QACtBC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,OAAO,CAAC;QACzCC,MAAM,EAAE,IAAI,CAAChB,SAAS,CAAC,CAAC,CAAC;QACzBiB,MAAM,EAAE,IAAI;QACZC,cAAc,EAAE;OACjB;MACDI,WAAW,EAAE,CAAC;MACdC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,IAAIV,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAE;KAC9C,EACD;MACEd,EAAE,EAAE,OAAO;MACXmB,YAAY,EAAE,CACZ,IAAI,CAACpB,SAAS,CAAC,CAAC,CAAC,EACjB,IAAI,CAACA,SAAS,CAAC,CAAC,CAAC,EACjB,IAAI,CAACA,SAAS,CAAC,CAAC,CAAC,EACjB,IAAI,CAACA,SAAS,CAAC,CAAC,CAAC,CAClB;MACDqB,WAAW,EAAE;QACXpB,EAAE,EAAE,GAAG;QACPS,OAAO,EAAE,2BAA2B;QACpCC,IAAI,EAAEd,WAAW,CAACe,IAAI;QACtBC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,QAAQ,CAAC;QAC1CC,MAAM,EAAE,IAAI,CAAChB,SAAS,CAAC,CAAC,CAAC;QACzBiB,MAAM,EAAE,KAAK;QACbC,cAAc,EAAE;OACjB;MACDI,WAAW,EAAE,CAAC;MACdC,OAAO,EAAE,IAAI;MACbE,SAAS,EAAE,kBAAkB;MAC7BC,UAAU,EAAE,gCAAgC;MAC5CF,SAAS,EAAE,IAAIV,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAE;KAC9C,CACF;IAED;IACQ,KAAAY,iBAAiB,GAAmB,CAC1C;MACE1B,EAAE,EAAE,QAAQ;MACZU,IAAI,EAAEiB,gBAAgB,CAACC,WAAW;MAClCnB,OAAO,EAAE,+BAA+B;MACxCG,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,OAAO,CAAC;MACzCE,MAAM,EAAE,KAAK;MACba,MAAM,EAAE;KACT,EACD;MACE7B,EAAE,EAAE,QAAQ;MACZU,IAAI,EAAEiB,gBAAgB,CAACG,gBAAgB;MACvCrB,OAAO,EAAE,uCAAuC;MAChDG,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,OAAO,CAAC;MACzCE,MAAM,EAAE,IAAI;MACZa,MAAM,EAAE;KACT,EACD;MACE7B,EAAE,EAAE,QAAQ;MACZU,IAAI,EAAEiB,gBAAgB,CAACI,YAAY;MACnCtB,OAAO,EAAE,mDAAmD;MAC5DG,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,OAAO,CAAC;MACzCE,MAAM,EAAE,KAAK;MACba,MAAM,EAAE;KACT,CACF;EAEc;EAEf;EACA;EACA;EAEA;;;EAGAG,QAAQA,CAAA;IACN,OAAOtC,EAAE,CAAC,IAAI,CAACK,SAAS,CAAC,CAACkC,IAAI,CAACtC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC9C;EAEA;;;EAGAuC,gBAAgBA,CAAA;IACd,OAAOxC,EAAE,CAAC,IAAI,CAACwB,iBAAiB,CAAC,CAACe,IAAI,CAACtC,KAAK,CAAC,GAAG,CAAC,CAAC;EACpD;EAEA;;;EAGAwC,eAAeA,CAACnC,EAAU;IACxB,MAAMoC,YAAY,GAAG,IAAI,CAAClB,iBAAiB,CAACmB,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACtC,EAAE,KAAKA,EAAE,CAAC;IACpE,OAAON,EAAE,CAAC0C,YAAY,IAAI,IAAI,CAAC,CAACH,IAAI,CAACtC,KAAK,CAAC,GAAG,CAAC,CAAC;EAClD;EAEA;;;EAGA4C,WAAWA,CAACtB,cAAsB;IAChC,MAAMuB,QAAQ,GAAG,IAAI,CAAChC,YAAY,CAACiC,MAAM,CACtCC,CAAC,IAAKA,CAAC,CAACzB,cAAc,KAAKA,cAAc,CAC3C;IACD,OAAOvB,EAAE,CAAC8C,QAAQ,CAAC,CAACP,IAAI,CAACtC,KAAK,CAAC,GAAG,CAAC,CAAC;EACtC;EAEA;;;EAGAgD,gBAAgBA,CAAA;IACd,OAAOjD,EAAE,CAAC,IAAI,CAACgC,iBAAiB,CAAC,CAACO,IAAI,CAACtC,KAAK,CAAC,GAAG,CAAC,CAAC;EACpD;EAEA;;;EAGAiD,WAAWA,CACTnC,OAAe,EACfQ,cAAsB,EACtB4B,QAAgB;IAEhB,MAAMC,UAAU,GAAY;MAC1B9C,EAAE,EAAE,OAAOa,IAAI,CAACC,GAAG,EAAE,EAAE;MACvBL,OAAO;MACPC,IAAI,EAAEd,WAAW,CAACe,IAAI;MACtBC,SAAS,EAAE,IAAIC,IAAI,EAAE;MACrBE,MAAM,EACJ,IAAI,CAAChB,SAAS,CAACsC,IAAI,CAAEU,CAAC,IAAKA,CAAC,CAAC/C,EAAE,KAAK6C,QAAQ,CAAC,IAAI,IAAI,CAAC9C,SAAS,CAAC,CAAC,CAAC;MACpEiB,MAAM,EAAE,KAAK;MACbC;KACD;IAED;IACA,IAAI,CAACT,YAAY,CAACwC,IAAI,CAACF,UAAU,CAAC;IAElC;IACA,MAAMV,YAAY,GAAG,IAAI,CAAClB,iBAAiB,CAACmB,IAAI,CAC7CC,CAAC,IAAKA,CAAC,CAACtC,EAAE,KAAKiB,cAAc,CAC/B;IACD,IAAImB,YAAY,EAAE;MAChBA,YAAY,CAAChB,WAAW,GAAG0B,UAAU;;IAGvC,OAAOpD,EAAE,CAACoD,UAAU,CAAC,CAACb,IAAI,CAACtC,KAAK,CAAC,GAAG,CAAC,CAAC;EACxC;EAEA;;;EAGAsD,kBAAkBA,CAChBpB,MAAc,EACdqB,aAAqB;IAErB,MAAMC,SAAS,GAAG,IAAI,CAACpD,SAAS,CAACsC,IAAI,CAAEU,CAAC,IAAKA,CAAC,CAAC/C,EAAE,KAAK6B,MAAM,CAAC;IAC7D,MAAMuB,WAAW,GAAG,IAAI,CAACrD,SAAS,CAACsC,IAAI,CAAEU,CAAC,IAAKA,CAAC,CAAC/C,EAAE,KAAKkD,aAAa,CAAC;IAEtE,IAAI,CAACC,SAAS,IAAI,CAACC,WAAW,EAAE;MAC9B,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAAC;;IAG3C,MAAMC,eAAe,GAAiB;MACpCtD,EAAE,EAAE,QAAQa,IAAI,CAACC,GAAG,EAAE,EAAE;MACxBK,YAAY,EAAE,CAACiC,WAAW,EAAED,SAAS,CAAC;MACtC9B,WAAW,EAAE,CAAC;MACdC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,IAAIV,IAAI;KACpB;IAED,IAAI,CAACK,iBAAiB,CAACqC,OAAO,CAACD,eAAe,CAAC;IAC/C,OAAO5D,EAAE,CAAC4D,eAAe,CAAC,CAACrB,IAAI,CAACtC,KAAK,CAAC,GAAG,CAAC,CAAC;EAC7C;EAEA;;;EAGA6D,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACzD,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B;EAEA;;;EAGA0D,WAAWA,CAACC,KAAa;IACvB,MAAMC,OAAO,GAAG,IAAI,CAAC5D,SAAS,CAAC0C,MAAM,CAClCmB,IAAI,IACHA,IAAI,CAAC1D,QAAQ,CAAC2D,WAAW,EAAE,CAACC,QAAQ,CAACJ,KAAK,CAACG,WAAW,EAAE,CAAC,IACzDD,IAAI,CAACzD,KAAK,CAAC0D,WAAW,EAAE,CAACC,QAAQ,CAACJ,KAAK,CAACG,WAAW,EAAE,CAAC,CACzD;IACD,OAAOnE,EAAE,CAACiE,OAAO,CAAC,CAAC1B,IAAI,CAACtC,KAAK,CAAC,GAAG,CAAC,CAAC;EACrC;EAEA;;;EAGAoE,mBAAmBA,CAACL,KAAa;IAC/B,MAAMC,OAAO,GAAG,IAAI,CAACzC,iBAAiB,CAACuB,MAAM,CAAEuB,IAAI,IAAI;MACrD,IAAIA,IAAI,CAAC1C,OAAO,EAAE;QAChB,OAAO0C,IAAI,CAACxC,SAAS,EAAEqC,WAAW,EAAE,CAACC,QAAQ,CAACJ,KAAK,CAACG,WAAW,EAAE,CAAC;OACnE,MAAM;QACL,OAAOG,IAAI,CAAC7C,YAAY,EAAE8C,IAAI,CAAEC,CAAC,IAC/BA,CAAC,CAAChE,QAAQ,CAAC2D,WAAW,EAAE,CAACC,QAAQ,CAACJ,KAAK,CAACG,WAAW,EAAE,CAAC,CACvD;;IAEL,CAAC,CAAC;IACF,OAAOnE,EAAE,CAACiE,OAAO,CAAC,CAAC1B,IAAI,CAACtC,KAAK,CAAC,GAAG,CAAC,CAAC;EACrC;;;uBA9SWE,eAAe;IAAA;EAAA;;;aAAfA,eAAe;MAAAsE,OAAA,EAAftE,eAAe,CAAAuE,IAAA;MAAAC,UAAA,EAFd;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}