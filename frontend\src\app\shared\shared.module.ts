import { NgModule } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { HttpClientModule } from '@angular/common/http';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import { AiChatbotComponent } from '../components/ai-chatbot/ai-chatbot.component';
import { HighlightPresencePipe } from './pipes/highlight-presence.pipe';
import { TimeAgoPipe } from './pipes/time-ago.pipe';

@NgModule({
  declarations: [AiChatbotComponent, HighlightPresencePipe, TimeAgoPipe],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    RouterModule,
    HttpClientModule,
  ],
  providers: [DatePipe],
  exports: [
    AiChatbotComponent,
    HighlightPresencePipe,
    TimeAgoPipe,
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    RouterModule,
  ],
})
export class SharedModule {}
