"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([["src_app_views_front_messages_messages_module_ts"],{

/***/ 145:
/*!*********************************************************************!*\
  !*** ./src/app/components/system-status/system-status.component.ts ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SystemStatusComponent: () => (/* binding */ SystemStatusComponent)
/* harmony export */ });
/* harmony import */ var C_Users_gayou_OneDrive_Bureau_Project_PI_devBridge_frontend_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 9204);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rxjs */ 9240);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _services_message_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../services/message.service */ 4537);
/* harmony import */ var _services_mock_data_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../services/mock-data.service */ 9767);
/* harmony import */ var _services_theme_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../services/theme.service */ 487);
/* harmony import */ var _services_toast_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../services/toast.service */ 8397);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/common */ 316);








class SystemStatusComponent {
  constructor(messageService, mockDataService, themeService, toastService) {
    this.messageService = messageService;
    this.mockDataService = mockDataService;
    this.themeService = themeService;
    this.toastService = toastService;
    this.status = {
      backend: 'checking',
      frontend: 'online',
      database: 'checking',
      websocket: 'checking',
      mockData: 'checking',
      theme: 'Chargement...',
      lastCheck: new Date()
    };
    this.isChecking = false;
  }
  ngOnInit() {
    this.checkSystemStatus();
    // Auto-refresh every 30 seconds
    this.subscription = (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.interval)(30000).subscribe(() => {
      this.checkSystemStatus();
    });
    // Listen to theme changes
    this.themeService.currentTheme$.subscribe(theme => {
      this.status.theme = theme.displayName;
    });
  }
  ngOnDestroy() {
    this.subscription?.unsubscribe();
  }
  checkSystemStatus() {
    var _this = this;
    return (0,C_Users_gayou_OneDrive_Bureau_Project_PI_devBridge_frontend_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      _this.isChecking = true;
      _this.status.lastCheck = new Date();
      // Check mock data availability
      try {
        yield _this.mockDataService.getUsers().toPromise();
        _this.status.mockData = 'available';
      } catch {
        _this.status.mockData = 'unavailable';
      }
      // Check backend connectivity
      try {
        yield _this.messageService.getConversations().toPromise();
        _this.status.backend = 'online';
        _this.status.database = 'online';
        _this.status.websocket = 'online';
      } catch {
        _this.status.backend = 'offline';
        _this.status.database = 'offline';
        _this.status.websocket = 'offline';
      }
      _this.isChecking = false;
    })();
  }
  getStatusText(status) {
    switch (status) {
      case 'online':
        return 'En ligne';
      case 'offline':
        return 'Hors ligne';
      case 'checking':
        return 'Vérification...';
      default:
        return 'Inconnu';
    }
  }
  testMockData() {
    this.mockDataService.getUsers().subscribe({
      next: users => {
        this.toastService.showSuccess(`${users.length} utilisateurs de test chargés`);
      },
      error: () => {
        this.toastService.showError('Erreur lors du chargement des données de test');
      }
    });
  }
  testThemes() {
    const themes = this.themeService.getAvailableThemes();
    this.toastService.showInfo(`${themes.length} thèmes disponibles`);
  }
  testNotifications() {
    this.toastService.showSuccess('Test de notification réussi !');
    setTimeout(() => {
      this.toastService.showInfo('Notification d\'information');
    }, 1000);
    setTimeout(() => {
      this.toastService.showWarning('Notification d\'avertissement');
    }, 2000);
  }
  static {
    this.ɵfac = function SystemStatusComponent_Factory(t) {
      return new (t || SystemStatusComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_services_message_service__WEBPACK_IMPORTED_MODULE_1__.MessageService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_services_mock_data_service__WEBPACK_IMPORTED_MODULE_2__.MockDataService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_services_theme_service__WEBPACK_IMPORTED_MODULE_3__.ThemeService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_services_toast_service__WEBPACK_IMPORTED_MODULE_4__.ToastService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineComponent"]({
      type: SystemStatusComponent,
      selectors: [["app-system-status"]],
      decls: 68,
      vars: 79,
      consts: [[1, "system-status-panel", "bg-gray-800", "rounded-lg", "p-6", "border", "border-gray-700"], [1, "flex", "items-center", "justify-between", "mb-4"], [1, "text-lg", "font-semibold", "text-white", "flex", "items-center"], [1, "fas", "fa-heartbeat", "text-blue-400", "mr-2"], [1, "px-3", "py-1", "bg-blue-600", "hover:bg-blue-700", "text-white", "rounded", "text-sm", "transition-colors", 3, "disabled", "click"], [1, "fas", "fa-sync-alt", "mr-1"], [1, "grid", "grid-cols-1", "md:grid-cols-2", "gap-4"], [1, "status-item"], [1, "flex", "items-center", "justify-between"], [1, "text-gray-300"], [1, "flex", "items-center"], [1, "w-3", "h-3", "rounded-full", "mr-2"], [1, "text-sm"], [1, "mt-4", "p-3", "bg-gray-700", "rounded", "border-l-4"], [1, "text-xs", "text-gray-400", "mt-1"], [1, "mt-4", "p-3", "bg-gray-700", "rounded"], [1, "text-sm", "text-blue-400"], [1, "mt-4", "text-xs", "text-gray-500", "text-center"], [1, "mt-6", "grid", "grid-cols-1", "md:grid-cols-3", "gap-2"], [1, "px-3", "py-2", "bg-green-600", "hover:bg-green-700", "text-white", "rounded", "text-sm", "transition-colors", 3, "click"], [1, "fas", "fa-database", "mr-1"], [1, "px-3", "py-2", "bg-purple-600", "hover:bg-purple-700", "text-white", "rounded", "text-sm", "transition-colors", 3, "click"], [1, "fas", "fa-palette", "mr-1"], [1, "px-3", "py-2", "bg-orange-600", "hover:bg-orange-700", "text-white", "rounded", "text-sm", "transition-colors", 3, "click"], [1, "fas", "fa-bell", "mr-1"]],
      template: function SystemStatusComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "h3", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](3, "i", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](4, " \u00C9tat du syst\u00E8me ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](5, "button", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function SystemStatusComponent_Template_button_click_5_listener() {
            return ctx.checkSystemStatus();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](6, "i", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](7);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](8, "div", 6)(9, "div", 7)(10, "div", 8)(11, "span", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](12, "Backend");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](13, "div", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](14, "div", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](15, "span", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](16);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](17, "div", 7)(18, "div", 8)(19, "span", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](20, "Frontend");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](21, "div", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](22, "div", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](23, "span", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](24);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](25, "div", 7)(26, "div", 8)(27, "span", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](28, "Base de donn\u00E9es");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](29, "div", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](30, "div", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](31, "span", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](32);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](33, "div", 7)(34, "div", 8)(35, "span", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](36, "WebSocket");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](37, "div", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](38, "div", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](39, "span", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](40);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](41, "div", 13)(42, "div", 8)(43, "span", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](44, "Donn\u00E9es de test");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](45, "span", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](46);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](47, "p", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](48);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](49, "div", 15)(50, "div", 8)(51, "span", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](52, "Th\u00E8me actuel");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](53, "span", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](54);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](55, "div", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](56);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpipe"](57, "date");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](58, "div", 18)(59, "button", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function SystemStatusComponent_Template_button_click_59_listener() {
            return ctx.testMockData();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](60, "i", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](61, " Test donn\u00E9es ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](62, "button", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function SystemStatusComponent_Template_button_click_62_listener() {
            return ctx.testThemes();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](63, "i", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](64, " Test th\u00E8mes ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](65, "button", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function SystemStatusComponent_Template_button_click_65_listener() {
            return ctx.testNotifications();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](66, "i", 24);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](67, " Test notifs ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("disabled", ctx.isChecking);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵclassProp"]("fa-spin", ctx.isChecking);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", ctx.isChecking ? "V\u00E9rification..." : "Actualiser", " ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](7);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵclassProp"]("bg-green-500", ctx.status.backend === "online")("bg-red-500", ctx.status.backend === "offline")("bg-yellow-500", ctx.status.backend === "checking")("animate-pulse", ctx.status.backend === "checking");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵclassProp"]("text-green-400", ctx.status.backend === "online")("text-red-400", ctx.status.backend === "offline")("text-yellow-400", ctx.status.backend === "checking");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", ctx.getStatusText(ctx.status.backend), " ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵclassProp"]("bg-green-500", ctx.status.frontend === "online")("bg-red-500", ctx.status.frontend === "offline")("bg-yellow-500", ctx.status.frontend === "checking")("animate-pulse", ctx.status.frontend === "checking");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵclassProp"]("text-green-400", ctx.status.frontend === "online")("text-red-400", ctx.status.frontend === "offline")("text-yellow-400", ctx.status.frontend === "checking");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", ctx.getStatusText(ctx.status.frontend), " ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵclassProp"]("bg-green-500", ctx.status.database === "online")("bg-red-500", ctx.status.database === "offline")("bg-yellow-500", ctx.status.database === "checking")("animate-pulse", ctx.status.database === "checking");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵclassProp"]("text-green-400", ctx.status.database === "online")("text-red-400", ctx.status.database === "offline")("text-yellow-400", ctx.status.database === "checking");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", ctx.getStatusText(ctx.status.database), " ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵclassProp"]("bg-green-500", ctx.status.websocket === "online")("bg-red-500", ctx.status.websocket === "offline")("bg-yellow-500", ctx.status.websocket === "checking")("animate-pulse", ctx.status.websocket === "checking");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵclassProp"]("text-green-400", ctx.status.websocket === "online")("text-red-400", ctx.status.websocket === "offline")("text-yellow-400", ctx.status.websocket === "checking");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", ctx.getStatusText(ctx.status.websocket), " ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵclassProp"]("border-green-500", ctx.status.mockData === "available")("border-red-500", ctx.status.mockData === "unavailable");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵclassProp"]("text-green-400", ctx.status.mockData === "available")("text-red-400", ctx.status.mockData === "unavailable");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", ctx.status.mockData === "available" ? "Disponibles" : "Indisponibles", " ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", ctx.status.mockData === "available" ? "Le mode d\u00E9mo est actif avec des donn\u00E9es de test" : "Aucune donn\u00E9e de test disponible", " ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate"](ctx.status.theme);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" Derni\u00E8re v\u00E9rification : ", _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpipeBind2"](57, 76, ctx.status.lastCheck, "medium"), " ");
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_7__.DatePipe],
      styles: [".status-item[_ngcontent-%COMP%] {\n    \n    border-radius: 0.25rem;\n    \n    --tw-bg-opacity: 1;\n    \n    background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\n    \n    padding: 0.75rem\n}\n    \n    .status-item[_ngcontent-%COMP%]:hover {\n    \n    --tw-bg-opacity: 1;\n    \n    background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1))\n}\n  \n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInN5c3RlbS1zdGF0dXMuY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7SUFFTTs7SUFBQSxzQkFBOEI7O0lBQTlCLGtCQUE4Qjs7SUFBOUIseURBQThCOztJQUE5QjtBQUE4Qjs7SUFJOUI7O0lBQUEsa0JBQWtCOztJQUFsQjtBQUFrQiIsImZpbGUiOiJzeXN0ZW0tc3RhdHVzLmNvbXBvbmVudC50cyIsInNvdXJjZXNDb250ZW50IjpbIlxuICAgIC5zdGF0dXMtaXRlbSB7XG4gICAgICBAYXBwbHkgcC0zIGJnLWdyYXktNzAwIHJvdW5kZWQ7XG4gICAgfVxuICAgIFxuICAgIC5zdGF0dXMtaXRlbTpob3ZlciB7XG4gICAgICBAYXBwbHkgYmctZ3JheS02MDA7XG4gICAgfVxuICAiXX0= */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9zeXN0ZW0tc3RhdHVzL3N5c3RlbS1zdGF0dXMuY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7SUFFTTs7SUFBQSxzQkFBOEI7O0lBQTlCLGtCQUE4Qjs7SUFBOUIseURBQThCOztJQUE5QjtBQUE4Qjs7SUFJOUI7O0lBQUEsa0JBQWtCOztJQUFsQjtBQUFrQjs7QUFheEIsNGhCQUE0aEIiLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAuc3RhdHVzLWl0ZW0ge1xuICAgICAgQGFwcGx5IHAtMyBiZy1ncmF5LTcwMCByb3VuZGVkO1xuICAgIH1cbiAgICBcbiAgICAuc3RhdHVzLWl0ZW06aG92ZXIge1xuICAgICAgQGFwcGx5IGJnLWdyYXktNjAwO1xuICAgIH1cbiAgIl0sInNvdXJjZVJvb3QiOiIifQ== */"]
    });
  }
}

/***/ }),

/***/ 9454:
/*!******************************************!*\
  !*** ./src/app/services/call.service.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CallService: () => (/* binding */ CallService)
/* harmony export */ });
/* harmony import */ var C_Users_gayou_OneDrive_Bureau_Project_PI_devBridge_frontend_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 9204);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rxjs */ 5797);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rxjs */ 7919);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rxjs/operators */ 271);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rxjs/operators */ 1318);
/* harmony import */ var _models_message_model__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../models/message.model */ 5293);
/* harmony import */ var _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../graphql/message.graphql */ 8896);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var apollo_angular__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! apollo-angular */ 7797);
/* harmony import */ var _logger_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./logger.service */ 4798);








/**
 * Service unifié pour la gestion des appels vidéo/audio
 * Gère l'état des appels, WebRTC, et la synchronisation
 */
class CallService {
  constructor(apollo, logger) {
    this.apollo = apollo;
    this.logger = logger;
    // ===== ÉTAT PRINCIPAL =====
    this.activeCall = new rxjs__WEBPACK_IMPORTED_MODULE_4__.BehaviorSubject(null);
    this.incomingCall = new rxjs__WEBPACK_IMPORTED_MODULE_4__.BehaviorSubject(null);
    this.callSignals = new rxjs__WEBPACK_IMPORTED_MODULE_4__.BehaviorSubject(null);
    // Observables publics
    this.activeCall$ = this.activeCall.asObservable();
    this.incomingCall$ = this.incomingCall.asObservable();
    this.callSignals$ = this.callSignals.asObservable();
    // ===== ÉTAT DES APPELS =====
    this.currentCallId = null;
    this.callState = 'idle';
    // ===== GESTION AUDIO =====
    this.sounds = {};
    this.isPlaying = {};
    // ===== WEBRTC =====
    this.peerConnection = null;
    this.localStream = null;
    this.remoteStream = null;
    this.localVideoElement = null;
    this.remoteVideoElement = null;
    this.isAudioEnabled = true;
    this.isVideoEnabled = true;
    // Configuration WebRTC
    this.rtcConfig = {
      iceServers: [{
        urls: 'stun:stun.l.google.com:19302'
      }, {
        urls: 'stun:stun1.l.google.com:19302'
      }]
    };
    this.logger.info('CallService', '🚀 Initializing unified CallService...');
    this.initializeSounds();
    this.initializeSubscriptions();
    this.initializeWebRTC();
    this.logger.info('CallService', '✅ CallService initialized successfully');
  }
  ngOnDestroy() {
    this.logger.info('CallService', '🔄 Destroying CallService...');
    this.cleanup();
  }
  // ===== MÉTHODES PUBLIQUES PRINCIPALES =====
  /**
   * Initie un appel
   */
  initiateCall(recipientId, callType, conversationId) {
    this.logger.info('CallService', '📞 Initiating call:', {
      recipientId,
      callType
    });
    if (this.callState !== 'idle') {
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.throwError)(() => new Error('Another call is already in progress'));
    }
    this.setCallState('initiating');
    const callId = this.generateCallId();
    return this.apollo.mutate({
      mutation: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.INITIATE_CALL_MUTATION,
      variables: {
        recipientId,
        callType,
        callId,
        conversationId
        // ✅ offer est maintenant optionnel - sera généré par WebRTC plus tard
      }
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.map)(result => {
      const call = result.data?.initiateCall;
      if (!call) throw new Error('Failed to initiate call');
      this.handleCallInitiated(call);
      return call;
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.catchError)(error => {
      this.logger.error('CallService', 'Error initiating call:', error);
      this.setCallState('idle');
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.throwError)(() => error);
    }));
  }
  /**
   * Accepte un appel entrant
   */
  acceptCall(call) {
    this.logger.info('CallService', '✅ Accepting call:', call.id);
    if (!call) {
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.throwError)(() => new Error('No call to accept'));
    }
    this.setCallState('connecting');
    return this.apollo.mutate({
      mutation: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.ACCEPT_CALL_MUTATION,
      variables: {
        callId: call.id
        // ✅ answer est maintenant optionnel - sera généré par WebRTC plus tard
      }
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.map)(result => {
      const acceptedCall = result.data?.acceptCall;
      if (!acceptedCall) throw new Error('Failed to accept call');
      this.handleCallAccepted(acceptedCall);
      return acceptedCall;
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.catchError)(error => {
      this.logger.error('CallService', 'Error accepting call:', error);
      this.setCallState('idle');
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.throwError)(() => error);
    }));
  }
  /**
   * Rejette un appel
   */
  rejectCall(callId, reason) {
    this.logger.info('CallService', '❌ Rejecting call:', callId);
    this.setCallState('ending');
    return this.apollo.mutate({
      mutation: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.REJECT_CALL_MUTATION,
      variables: {
        callId,
        reason: reason || 'User rejected'
      }
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.map)(result => {
      const success = result.data?.rejectCall;
      if (!success) throw new Error('Failed to reject call');
      this.handleCallEnded();
      return success;
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.catchError)(error => {
      this.logger.error('CallService', 'Error rejecting call:', error);
      this.handleCallEnded(); // Nettoyer même en cas d'erreur
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.throwError)(() => error);
    }));
  }
  /**
   * Termine un appel
   */
  endCall(callId) {
    this.logger.info('CallService', '🔚 Ending call:', callId);
    this.setCallState('ending');
    return this.apollo.mutate({
      mutation: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.END_CALL_MUTATION,
      variables: {
        callId
      }
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.map)(result => {
      const success = result.data?.endCall;
      if (!success) throw new Error('Failed to end call');
      this.handleCallEnded();
      return success;
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.catchError)(error => {
      this.logger.error('CallService', 'Error ending call:', error);
      this.handleCallEnded(); // Nettoyer même en cas d'erreur
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.throwError)(() => error);
    }));
  }
  // ===== GETTERS PUBLICS =====
  get currentCall() {
    return this.activeCall.value;
  }
  get currentIncomingCall() {
    return this.incomingCall.value;
  }
  get isCallActive() {
    return this.callState === 'connected';
  }
  get isCallInProgress() {
    return this.callState !== 'idle';
  }
  // ===== MÉTHODES PRIVÉES =====
  generateCallId() {
    return `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  setCallState(state) {
    this.logger.debug('CallService', `Call state: ${this.callState} → ${state}`);
    this.callState = state;
  }
  handleCallInitiated(call) {
    this.logger.info('CallService', 'Call initiated successfully:', call.id);
    this.currentCallId = call.id;
    this.activeCall.next(call);
    this.setCallState('ringing');
    this.play('ringtone', true);
    this.startOutgoingCallMedia(call.type);
  }
  handleCallAccepted(call) {
    this.logger.info('CallService', 'Call accepted successfully:', call.id);
    this.activeCall.next(call);
    this.incomingCall.next(null);
    this.setCallState('connected');
    this.stop('ringtone');
    this.play('call-connected');
  }
  handleCallEnded() {
    this.logger.info('CallService', 'Call ended, cleaning up');
    this.setCallState('idle');
    this.currentCallId = null;
    this.activeCall.next(null);
    this.incomingCall.next(null);
    this.stopAllSounds();
    this.play('call-end');
    this.cleanupWebRTC();
  }
  handleIncomingCall(call) {
    this.logger.info('CallService', 'Incoming call received:', call.id);
    this.currentCallId = call.id;
    this.incomingCall.next(call);
    this.setCallState('ringing');
    this.play('ringtone', true);
    this.prepareForIncomingCall(call);
  }
  handleCallStatusChange(call) {
    this.logger.info('CallService', 'Call status changed:', call.status);
    if (call.id === this.currentCallId) {
      this.activeCall.next(call);
      switch (call.status) {
        case _models_message_model__WEBPACK_IMPORTED_MODULE_1__.CallStatus.CONNECTED:
          this.setCallState('connected');
          this.stop('ringtone');
          this.play('call-connected');
          break;
        case _models_message_model__WEBPACK_IMPORTED_MODULE_1__.CallStatus.ENDED:
        case _models_message_model__WEBPACK_IMPORTED_MODULE_1__.CallStatus.REJECTED:
          this.handleCallEnded();
          break;
      }
    }
  }
  handleCallSignal(signal) {
    this.logger.debug('CallService', 'Call signal received:', signal.type);
    this.callSignals.next(signal);
    // Traitement WebRTC des signaux sera ajouté ici
  }
  // ===== INITIALISATION =====
  initializeSounds() {
    this.logger.debug('CallService', 'Initializing sounds...');
    this.createSyntheticSounds();
  }
  createSyntheticSounds() {
    this.createSyntheticSound('ringtone', [440, 554.37], 1.5, true);
    this.createSyntheticSound('call-connected', [523.25, 659.25, 783.99], 0.8, false);
    this.createSyntheticSound('call-end', [392, 329.63, 261.63], 1.2, false);
  }
  createSyntheticSound(name, frequencies, duration, loop) {
    try {
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const sampleRate = audioContext.sampleRate;
      const frameCount = sampleRate * duration;
      const buffer = audioContext.createBuffer(1, frameCount, sampleRate);
      const channelData = buffer.getChannelData(0);
      for (let i = 0; i < frameCount; i++) {
        let sample = 0;
        frequencies.forEach(freq => {
          const amplitude = 0.3 / frequencies.length;
          const phase = i / sampleRate * freq * 2 * Math.PI;
          sample += Math.sin(phase) * amplitude;
        });
        const envelope = Math.sin(i / frameCount * Math.PI);
        channelData[i] = sample * envelope;
      }
      const audio = new Audio();
      audio.loop = loop;
      audio.customPlay = () => {
        const source = audioContext.createBufferSource();
        source.buffer = buffer;
        source.loop = loop;
        source.connect(audioContext.destination);
        source.start();
        if (!loop) {
          setTimeout(() => {
            this.isPlaying[name] = false;
          }, duration * 1000);
        }
        return source;
      };
      this.sounds[name] = audio;
      this.isPlaying[name] = false;
    } catch (error) {
      this.logger.error('CallService', `Error creating sound '${name}':`, error);
    }
  }
  initializeSubscriptions() {
    this.logger.debug('CallService', 'Initializing subscriptions...');
    this.subscribeToIncomingCalls();
    this.subscribeToCallStatusChanges();
    this.subscribeToCallSignals();
  }
  subscribeToIncomingCalls() {
    this.apollo.subscribe({
      query: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.INCOMING_CALL_SUBSCRIPTION,
      errorPolicy: 'all'
    }).subscribe({
      next: ({
        data,
        errors
      }) => {
        if (data?.incomingCall) {
          this.handleIncomingCall(data.incomingCall);
        }
        if (errors) {
          this.logger.error('CallService', 'Incoming call subscription errors:', errors);
        }
      },
      error: error => {
        this.logger.error('CallService', 'Error in incoming call subscription:', error);
        setTimeout(() => this.subscribeToIncomingCalls(), 5000);
      }
    });
  }
  subscribeToCallStatusChanges() {
    this.apollo.subscribe({
      query: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.CALL_STATUS_CHANGED_SUBSCRIPTION,
      errorPolicy: 'all'
    }).subscribe({
      next: ({
        data,
        errors
      }) => {
        if (data?.callStatusChanged) {
          this.handleCallStatusChange(data.callStatusChanged);
        }
        if (errors) {
          this.logger.error('CallService', 'Call status subscription errors:', errors);
        }
      },
      error: error => {
        this.logger.error('CallService', 'Error in call status subscription:', error);
        setTimeout(() => this.subscribeToCallStatusChanges(), 5000);
      }
    });
  }
  subscribeToCallSignals() {
    this.apollo.subscribe({
      query: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.CALL_SIGNAL_SUBSCRIPTION,
      errorPolicy: 'all'
    }).subscribe({
      next: ({
        data,
        errors
      }) => {
        if (data?.callSignal) {
          this.handleCallSignal(data.callSignal);
        }
        if (errors) {
          this.logger.error('CallService', 'Call signal subscription errors:', errors);
        }
      },
      error: error => {
        this.logger.error('CallService', 'Error in call signal subscription:', error);
        setTimeout(() => this.subscribeToCallSignals(), 5000);
      }
    });
  }
  initializeWebRTC() {
    this.logger.debug('CallService', 'Initializing WebRTC...');
    this.createPeerConnection();
  }
  // ===== GESTION AUDIO =====
  play(name, loop = false) {
    try {
      const sound = this.sounds[name];
      if (!sound || this.isPlaying[name]) return;
      if (sound.customPlay) {
        sound.currentSource = sound.customPlay();
        this.isPlaying[name] = true;
      }
    } catch (error) {
      this.logger.error('CallService', `Error playing sound '${name}':`, error);
    }
  }
  stop(name) {
    try {
      const sound = this.sounds[name];
      if (!sound || !this.isPlaying[name]) return;
      if (sound.currentSource) {
        sound.currentSource.stop();
        sound.currentSource = null;
      }
      this.isPlaying[name] = false;
    } catch (error) {
      this.logger.error('CallService', `Error stopping sound '${name}':`, error);
    }
  }
  stopAllSounds() {
    Object.keys(this.sounds).forEach(name => this.stop(name));
  }
  // ===== WEBRTC =====
  createPeerConnection() {
    try {
      this.peerConnection = new RTCPeerConnection(this.rtcConfig);
      this.logger.debug('CallService', 'PeerConnection created successfully');
      this.peerConnection.onicecandidate = event => {
        if (event.candidate && this.currentCallId) {
          this.sendSignal('ice-candidate', JSON.stringify(event.candidate));
        }
      };
      this.peerConnection.ontrack = event => {
        this.logger.info('CallService', 'Remote track received:', event.track.kind);
        this.remoteStream = event.streams[0];
        this.attachRemoteStream();
      };
      this.peerConnection.onconnectionstatechange = () => {
        const state = this.peerConnection?.connectionState;
        this.logger.debug('CallService', 'Connection state changed:', state);
        if (state === 'connected') {
          this.logger.info('CallService', '✅ WebRTC connection established');
          this.setCallState('connected');
        } else if (state === 'failed') {
          this.logger.error('CallService', '❌ WebRTC connection failed');
          this.handleCallEnded();
        }
      };
    } catch (error) {
      this.logger.error('CallService', 'Error creating PeerConnection:', error);
    }
  }
  startOutgoingCallMedia(callType) {
    var _this = this;
    return (0,C_Users_gayou_OneDrive_Bureau_Project_PI_devBridge_frontend_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      try {
        _this.logger.info('CallService', '🎥 Starting outgoing call media');
        const stream = yield _this.getUserMedia(callType);
        _this.addLocalStreamToPeerConnection(stream);
        _this.attachLocalStream();
      } catch (error) {
        _this.logger.error('CallService', 'Error starting outgoing call media:', error);
      }
    })();
  }
  prepareForIncomingCall(call) {
    var _this2 = this;
    return (0,C_Users_gayou_OneDrive_Bureau_Project_PI_devBridge_frontend_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      try {
        _this2.logger.debug('CallService', 'Preparing WebRTC for incoming call');
        if (!_this2.peerConnection) {
          _this2.createPeerConnection();
        }
        const stream = yield _this2.getUserMedia(call.type);
        _this2.addLocalStreamToPeerConnection(stream);
      } catch (error) {
        _this2.logger.error('CallService', 'Error preparing for incoming call:', error);
      }
    })();
  }
  getUserMedia(callType) {
    var _this3 = this;
    return (0,C_Users_gayou_OneDrive_Bureau_Project_PI_devBridge_frontend_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      const constraints = {
        audio: true,
        video: callType === _models_message_model__WEBPACK_IMPORTED_MODULE_1__.CallType.VIDEO
      };
      try {
        const stream = yield navigator.mediaDevices.getUserMedia(constraints);
        _this3.localStream = stream;
        return stream;
      } catch (error) {
        _this3.logger.error('CallService', 'Error getting user media:', error);
        throw error;
      }
    })();
  }
  addLocalStreamToPeerConnection(stream) {
    if (!this.peerConnection) return;
    stream.getTracks().forEach(track => {
      this.peerConnection.addTrack(track, stream);
    });
  }
  attachLocalStream() {
    if (this.localVideoElement && this.localStream) {
      this.localVideoElement.srcObject = this.localStream;
    }
  }
  attachRemoteStream() {
    if (this.remoteVideoElement && this.remoteStream) {
      this.remoteVideoElement.srcObject = this.remoteStream;
    }
  }
  sendSignal(signalType, signalData) {
    if (!this.currentCallId) return;
    this.apollo.mutate({
      mutation: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.SEND_CALL_SIGNAL_MUTATION,
      variables: {
        callId: this.currentCallId,
        signalType,
        signalData
      }
    }).subscribe({
      next: () => this.logger.debug('CallService', 'Signal sent:', signalType),
      error: error => this.logger.error('CallService', 'Error sending signal:', error)
    });
  }
  // ===== NETTOYAGE =====
  cleanupWebRTC() {
    this.logger.debug('CallService', 'Cleaning up WebRTC resources');
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop());
      this.localStream = null;
    }
    if (this.remoteStream) {
      this.remoteStream = null;
    }
    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }
    if (this.localVideoElement) {
      this.localVideoElement.srcObject = null;
    }
    if (this.remoteVideoElement) {
      this.remoteVideoElement.srcObject = null;
    }
    // Recréer une nouvelle PeerConnection pour les futurs appels
    this.createPeerConnection();
  }
  cleanup() {
    this.stopAllSounds();
    this.cleanupWebRTC();
    this.activeCall.complete();
    this.incomingCall.complete();
    this.callSignals.complete();
  }
  // ===== MÉTHODES PUBLIQUES UTILITAIRES =====
  /**
   * Attache les éléments vidéo pour l'affichage
   */
  attachVideoElements(localVideo, remoteVideo) {
    this.localVideoElement = localVideo;
    this.remoteVideoElement = remoteVideo;
    if (this.localStream) {
      this.attachLocalStream();
    }
    if (this.remoteStream) {
      this.attachRemoteStream();
    }
  }
  /**
   * Active/désactive l'audio
   */
  toggleAudio() {
    this.isAudioEnabled = !this.isAudioEnabled;
    if (this.localStream) {
      this.localStream.getAudioTracks().forEach(track => {
        track.enabled = this.isAudioEnabled;
      });
    }
    return this.isAudioEnabled;
  }
  /**
   * Active/désactive la vidéo
   */
  toggleVideo() {
    this.isVideoEnabled = !this.isVideoEnabled;
    if (this.localStream) {
      this.localStream.getVideoTracks().forEach(track => {
        track.enabled = this.isVideoEnabled;
      });
    }
    return this.isVideoEnabled;
  }
  /**
   * Méthode de compatibilité pour setVideoElements
   */
  setVideoElements(localVideo, remoteVideo) {
    this.attachVideoElements(localVideo, remoteVideo);
  }
  /**
   * Obtient l'état audio actuel
   */
  get audioEnabled() {
    return this.isAudioEnabled;
  }
  /**
   * Obtient l'état vidéo actuel
   */
  get videoEnabled() {
    return this.isVideoEnabled;
  }
  /**
   * Obtient le stream local
   */
  get localMediaStream() {
    return this.localStream;
  }
  /**
   * Obtient le stream distant
   */
  get remoteMediaStream() {
    return this.remoteStream;
  }
  /**
   * Active les sons (méthode de compatibilité)
   */
  enableSounds() {
    this.logger.debug('CallService', 'Sounds are always enabled in unified service');
  }
  /**
   * Désactive les sons (méthode de compatibilité)
   */
  disableSounds() {
    this.logger.debug('CallService', 'Disabling sounds');
    this.stopAllSounds();
  }
  static {
    this.ɵfac = function CallService_Factory(t) {
      return new (t || CallService)(_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵinject"](apollo_angular__WEBPACK_IMPORTED_MODULE_9__.Apollo), _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵinject"](_logger_service__WEBPACK_IMPORTED_MODULE_3__.LoggerService));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdefineInjectable"]({
      token: CallService,
      factory: CallService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 8096:
/*!*****************************************************************************!*\
  !*** ./src/app/views/front/messages/message-chat/message-chat.component.ts ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MessageChatComponent: () => (/* binding */ MessageChatComponent)
/* harmony export */ });
/* harmony import */ var C_Users_gayou_OneDrive_Bureau_Project_PI_devBridge_frontend_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ 9204);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rxjs */ 5797);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! rxjs */ 9452);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rxjs/operators */ 1567);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rxjs/operators */ 1817);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! rxjs/operators */ 8764);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! rxjs/operators */ 6647);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! rxjs/operators */ 1318);
/* harmony import */ var _models_message_model__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../models/message.model */ 5293);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _services_message_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../services/message.service */ 4537);
/* harmony import */ var _services_auth_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../services/auth.service */ 4796);
/* harmony import */ var _services_toast_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../services/toast.service */ 8397);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _components_system_status_system_status_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../../components/system-status/system-status.component */ 145);












const _c0 = ["messagesContainer"];
const _c1 = ["messageInput"];
const _c2 = ["fileInput"];
const _c3 = ["voiceRecorder"];
function MessageChatComponent_div_0_span_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", ctx_r2.selectedConversation.participants == null ? null : ctx_r2.selectedConversation.participants.length, " participants ");
  }
}
function MessageChatComponent_div_0_span_9_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1, "En ligne");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
}
function MessageChatComponent_div_0_span_10_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1, "Hors ligne");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
}
function MessageChatComponent_div_0_button_12_Template(rf, ctx) {
  if (rf & 1) {
    const _r21 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "button", 37);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_div_0_button_12_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r21);
      const ctx_r20 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r20.startAudioCall());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](1, "i", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
}
function MessageChatComponent_div_0_button_13_Template(rf, ctx) {
  if (rf & 1) {
    const _r23 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "button", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_div_0_button_13_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r23);
      const ctx_r22 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r22.startVideoCall());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](1, "i", 40);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
}
function MessageChatComponent_div_0_div_18_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 41);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](1, "div", 42);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
}
function MessageChatComponent_div_0_div_19_img_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "img", 61);
  }
  if (rf & 2) {
    const message_r24 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("src", message_r24.sender.image || "/assets/images/default-avatar.png", _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵsanitizeUrl"])("alt", message_r24.sender.username);
  }
}
function MessageChatComponent_div_0_div_19_div_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 62);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const message_r24 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", message_r24.sender == null ? null : message_r24.sender.username, " ");
  }
}
function MessageChatComponent_div_0_div_19_div_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 63)(1, "div", 64);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](3, "div", 65);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const message_r24 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" R\u00E9ponse \u00E0 ", message_r24.replyTo.sender == null ? null : message_r24.replyTo.sender.username, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", message_r24.replyTo.content, " ");
  }
}
function MessageChatComponent_div_0_div_19_div_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 66);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const message_r24 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", message_r24.content, " ");
  }
}
function MessageChatComponent_div_0_div_19_div_7_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 70);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const message_r24 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2).$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", message_r24.content, " ");
  }
}
function MessageChatComponent_div_0_div_19_div_7_Template(rf, ctx) {
  if (rf & 1) {
    const _r43 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 67)(1, "img", 68);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_div_0_div_19_div_7_Template_img_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r43);
      const message_r24 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
      const ctx_r41 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r41.openImageViewer(message_r24.attachments == null ? null : message_r24.attachments[0]));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](2, MessageChatComponent_div_0_div_19_div_7_div_2_Template, 2, 1, "div", 69);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const message_r24 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("src", message_r24.attachments == null ? null : message_r24.attachments[0] == null ? null : message_r24.attachments[0].url, _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵsanitizeUrl"])("alt", message_r24.attachments == null ? null : message_r24.attachments[0] == null ? null : message_r24.attachments[0].name);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", message_r24.content);
  }
}
function MessageChatComponent_div_0_div_19_div_8_Template(rf, ctx) {
  if (rf & 1) {
    const _r47 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 71);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](1, "i", 72);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](2, "div", 73)(3, "div", 74);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](5, "div", 75);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](7, "button", 76);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_div_0_div_19_div_8_Template_button_click_7_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r47);
      const message_r24 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
      const ctx_r45 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r45.downloadFile(message_r24.attachments == null ? null : message_r24.attachments[0]));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](8, "i", 77);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const message_r24 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
    const ctx_r30 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate"](message_r24.attachments == null ? null : message_r24.attachments[0] == null ? null : message_r24.attachments[0].name);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", ctx_r30.formatFileSize(message_r24.attachments == null ? null : message_r24.attachments[0] == null ? null : message_r24.attachments[0].size), " ");
  }
}
function MessageChatComponent_div_0_div_19_div_9_Template(rf, ctx) {
  if (rf & 1) {
    const _r51 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 78)(1, "button", 79);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_div_0_div_19_div_9_Template_button_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r51);
      const message_r24 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
      const ctx_r49 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r49.playVoiceMessage(message_r24));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](2, "i", 80);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](3, "div", 81);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const message_r24 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
    const ctx_r31 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", ctx_r31.formatDuration(message_r24.attachments == null ? null : message_r24.attachments[0] == null ? null : message_r24.attachments[0].duration), " ");
  }
}
function MessageChatComponent_div_0_div_19_div_10_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 70);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const message_r24 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2).$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", message_r24.content, " ");
  }
}
function MessageChatComponent_div_0_div_19_div_10_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 82);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](1, "video", 83);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](2, MessageChatComponent_div_0_div_19_div_10_div_2_Template, 2, 1, "div", 69);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const message_r24 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("src", message_r24.attachments == null ? null : message_r24.attachments[0] == null ? null : message_r24.attachments[0].url, _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵsanitizeUrl"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", message_r24.content);
  }
}
function MessageChatComponent_div_0_div_19_div_11_span_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r60 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "span", 86);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_div_0_div_19_div_11_span_1_Template_span_click_0_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r60);
      const reaction_r57 = restoredCtx.$implicit;
      const message_r24 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2).$implicit;
      const ctx_r58 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r58.reactToMessage(message_r24, reaction_r57.emoji));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const reaction_r57 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate2"](" ", reaction_r57.emoji, " ", reaction_r57.count, " ");
  }
}
function MessageChatComponent_div_0_div_19_div_11_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 84);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](1, MessageChatComponent_div_0_div_19_div_11_span_1_Template, 2, 2, "span", 85);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const message_r24 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngForOf", message_r24.reactions);
  }
}
function MessageChatComponent_div_0_div_19_div_15_i_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "i", 92);
  }
}
function MessageChatComponent_div_0_div_19_div_15_i_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "i", 93);
  }
}
function MessageChatComponent_div_0_div_19_div_15_i_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "i", 94);
  }
}
function MessageChatComponent_div_0_div_19_div_15_i_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "i", 95);
  }
}
function MessageChatComponent_div_0_div_19_div_15_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 87);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](1, MessageChatComponent_div_0_div_19_div_15_i_1_Template, 1, 0, "i", 88);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](2, MessageChatComponent_div_0_div_19_div_15_i_2_Template, 1, 0, "i", 89);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](3, MessageChatComponent_div_0_div_19_div_15_i_3_Template, 1, 0, "i", 90);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](4, MessageChatComponent_div_0_div_19_div_15_i_4_Template, 1, 0, "i", 91);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const message_r24 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵclassProp"]("read", message_r24.isRead)("pending", message_r24.isPending)("error", message_r24.isError);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", message_r24.isPending);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", message_r24.isError);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !message_r24.isPending && !message_r24.isError && message_r24.isRead);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !message_r24.isPending && !message_r24.isError && !message_r24.isRead);
  }
}
function MessageChatComponent_div_0_div_19_Template(rf, ctx) {
  if (rf & 1) {
    const _r68 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 43);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](1, MessageChatComponent_div_0_div_19_img_1_Template, 1, 2, "img", 44);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](2, "div", 45);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](3, MessageChatComponent_div_0_div_19_div_3_Template, 2, 1, "div", 46);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](4, MessageChatComponent_div_0_div_19_div_4_Template, 5, 2, "div", 47);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](5, "div", 48);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](6, MessageChatComponent_div_0_div_19_div_6_Template, 2, 1, "div", 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](7, MessageChatComponent_div_0_div_19_div_7_Template, 3, 3, "div", 50);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](8, MessageChatComponent_div_0_div_19_div_8_Template, 9, 2, "div", 51);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](9, MessageChatComponent_div_0_div_19_div_9_Template, 5, 1, "div", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](10, MessageChatComponent_div_0_div_19_div_10_Template, 3, 2, "div", 53);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](11, MessageChatComponent_div_0_div_19_div_11_Template, 2, 1, "div", 54);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](12, "div", 55)(13, "span", 56);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](14);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](15, MessageChatComponent_div_0_div_19_div_15_Template, 5, 10, "div", 57);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](16, "div", 58)(17, "button", 59);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_div_0_div_19_Template_button_click_17_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r68);
      const message_r24 = restoredCtx.$implicit;
      const ctx_r67 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r67.showMessageMenu(message_r24));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](18, "i", 60);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const message_r24 = ctx.$implicit;
    const ctx_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵclassProp"]("my-message", ctx_r9.isMyMessage(message_r24));
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !ctx_r9.isMyMessage(message_r24) && message_r24.sender);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵclassProp"]("my-message", ctx_r9.isMyMessage(message_r24))("other-message", !ctx_r9.isMyMessage(message_r24));
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r9.selectedConversation.isGroup && !ctx_r9.isMyMessage(message_r24));
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", message_r24.replyTo);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngSwitch", message_r24.type);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngSwitchCase", ctx_r9.MessageType.TEXT);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngSwitchCase", ctx_r9.MessageType.IMAGE);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngSwitchCase", ctx_r9.MessageType.FILE);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngSwitchCase", ctx_r9.MessageType.VOICE_MESSAGE);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngSwitchCase", ctx_r9.MessageType.VIDEO);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", message_r24.reactions && message_r24.reactions.length > 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", ctx_r9.formatMessageTime(message_r24.timestamp), " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r9.isMyMessage(message_r24));
  }
}
function MessageChatComponent_div_0_div_20_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 96)(1, "div", 97);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](2, "div", 98)(3, "div", 98)(4, "div", 98);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](5, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate"](ctx_r10.getTypingText());
  }
}
function MessageChatComponent_div_0_div_22_Template(rf, ctx) {
  if (rf & 1) {
    const _r70 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 99)(1, "div", 100)(2, "div")(3, "div", 101);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](5, "div", 102);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](7, "button", 103);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_div_0_div_22_Template_button_click_7_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r70);
      const ctx_r69 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r69.cancelReply());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](8, "i", 104);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r11 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" R\u00E9ponse \u00E0 ", ctx_r11.replyingTo.sender == null ? null : ctx_r11.replyingTo.sender.username, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate"](ctx_r11.replyingTo.content);
  }
}
function MessageChatComponent_div_0_div_23_Template(rf, ctx) {
  if (rf & 1) {
    const _r72 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 99)(1, "div", 100)(2, "div")(3, "div", 105);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](4, "Modification du message");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](5, "div", 102);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](7, "button", 103);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_div_0_div_23_Template_button_click_7_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r72);
      const ctx_r71 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r71.cancelEditing());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](8, "i", 104);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r12 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate"](ctx_r12.editingMessage.content);
  }
}
function MessageChatComponent_div_0_div_24_div_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r77 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 109);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](1, "i", 110);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](2, "span", 111);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](4, "button", 112);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_div_0_div_24_div_2_Template_button_click_4_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r77);
      const i_r75 = restoredCtx.index;
      const ctx_r76 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](3);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r76.removeSelectedFile(i_r75));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](5, "i", 113);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const file_r74 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate"](file_r74.name);
  }
}
function MessageChatComponent_div_0_div_24_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 106)(1, "div", 107);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](2, MessageChatComponent_div_0_div_24_div_2_Template, 6, 1, "div", 108);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r13 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngForOf", ctx_r13.selectedFiles);
  }
}
function MessageChatComponent_div_0_div_25_Template(rf, ctx) {
  if (rf & 1) {
    const _r79 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 114);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](1, "i", 115);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](2, "span", 116);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](4, "button", 117);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_div_0_div_25_Template_button_click_4_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r79);
      const ctx_r78 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r78.stopVoiceRecording());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](5, "i", 118);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](6, "button", 119);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_div_0_div_25_Template_button_click_6_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r79);
      const ctx_r80 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r80.cancelVoiceRecording());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](7, "i", 104);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r14 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate"](ctx_r14.formatDuration(ctx_r14.recordingDuration));
  }
}
function MessageChatComponent_div_0_div_31_Template(rf, ctx) {
  if (rf & 1) {
    const _r82 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 120)(1, "button", 121);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_div_0_div_31_Template_button_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r82);
      const ctx_r81 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r81.openFileSelector());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](2, "i", 110);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](3, "span", 122);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](4, "Fichier");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](5, "button", 121);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_div_0_div_31_Template_button_click_5_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r82);
      const ctx_r83 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r83.openFileSelector());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](6, "i", 123);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](7, "span", 122);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](8, "Image");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
  }
}
function MessageChatComponent_div_0_button_37_Template(rf, ctx) {
  if (rf & 1) {
    const _r85 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "button", 124);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("mousedown", function MessageChatComponent_div_0_button_37_Template_button_mousedown_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r85);
      const ctx_r84 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r84.startVoiceRecording());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](1, "i", 125);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
}
function MessageChatComponent_div_0_button_38_Template(rf, ctx) {
  if (rf & 1) {
    const _r87 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "button", 126);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_div_0_button_38_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r87);
      const ctx_r86 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r86.sendMessage());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](1, "i", 127);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r18 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("disabled", !ctx_r18.canSendMessage());
  }
}
function MessageChatComponent_div_0_Template(rf, ctx) {
  if (rf & 1) {
    const _r89 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 2)(1, "div", 3)(2, "div", 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](3, "img", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](4, "div", 6)(5, "h3");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](7, "p", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](8, MessageChatComponent_div_0_span_8_Template, 2, 1, "span", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](9, MessageChatComponent_div_0_span_9_Template, 2, 0, "span", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](10, MessageChatComponent_div_0_span_10_Template, 2, 0, "span", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](11, "div", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](12, MessageChatComponent_div_0_button_12_Template, 2, 0, "button", 10);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](13, MessageChatComponent_div_0_button_13_Template, 2, 0, "button", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](14, "button", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](15, "i", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](16, "div", 14, 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](18, MessageChatComponent_div_0_div_18_Template, 2, 0, "div", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](19, MessageChatComponent_div_0_div_19_Template, 19, 18, "div", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](20, MessageChatComponent_div_0_div_20_Template, 7, 1, "div", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](21, "div", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](22, MessageChatComponent_div_0_div_22_Template, 9, 2, "div", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](23, MessageChatComponent_div_0_div_23_Template, 9, 1, "div", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](24, MessageChatComponent_div_0_div_24_Template, 3, 1, "div", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](25, MessageChatComponent_div_0_div_25_Template, 8, 1, "div", 22);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](26, "div", 23)(27, "div", 24)(28, "div", 25)(29, "button", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_div_0_Template_button_click_29_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r89);
      const ctx_r88 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r88.toggleAttachmentMenu());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](30, "i", 27);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](31, MessageChatComponent_div_0_div_31_Template, 9, 0, "div", 28);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](32, "button", 29);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function MessageChatComponent_div_0_Template_button_click_32_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r89);
      const ctx_r90 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r90.toggleEmojiPicker());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](33, "i", 30);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](34, "textarea", 31, 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("ngModelChange", function MessageChatComponent_div_0_Template_textarea_ngModelChange_34_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r89);
      const ctx_r91 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r91.messageContent = $event);
    })("keydown", function MessageChatComponent_div_0_Template_textarea_keydown_34_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r89);
      const ctx_r92 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r92.onKeyPress($event));
    })("input", function MessageChatComponent_div_0_Template_textarea_input_34_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r89);
      const ctx_r93 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r93.onTyping());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](36, "div", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](37, MessageChatComponent_div_0_button_37_Template, 2, 0, "button", 33);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](38, MessageChatComponent_div_0_button_38_Template, 2, 1, "button", 34);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](39, "input", 35, 36);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("change", function MessageChatComponent_div_0_Template_input_change_39_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r89);
      const ctx_r94 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r94.onFileSelected($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵclassProp"]("online", !ctx_r0.selectedConversation.isGroup && ctx_r0.isRecipientOnline());
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("src", ctx_r0.selectedConversation.isGroup ? ctx_r0.selectedConversation.groupPhoto : ctx_r0.getRecipientAvatar(), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵsanitizeUrl"])("alt", ctx_r0.selectedConversation.isGroup ? ctx_r0.selectedConversation.groupName : ctx_r0.getRecipientName());
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", ctx_r0.selectedConversation.isGroup ? ctx_r0.selectedConversation.groupName : ctx_r0.getRecipientName(), " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵclassProp"]("online", !ctx_r0.selectedConversation.isGroup && ctx_r0.isRecipientOnline());
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r0.selectedConversation.isGroup);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !ctx_r0.selectedConversation.isGroup && ctx_r0.isRecipientOnline());
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !ctx_r0.selectedConversation.isGroup && !ctx_r0.isRecipientOnline());
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !ctx_r0.selectedConversation.isGroup);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !ctx_r0.selectedConversation.isGroup);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r0.isLoading);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngForOf", ctx_r0.messages)("ngForTrackBy", ctx_r0.trackByMessageId);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r0.typingUsers.length > 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r0.replyingTo);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r0.editingMessage);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r0.selectedFiles.length > 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r0.isRecording);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r0.showAttachmentMenu);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngModel", ctx_r0.messageContent)("disabled", ctx_r0.isRecording);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !ctx_r0.messageContent.trim() && !ctx_r0.selectedFiles.length && !ctx_r0.isRecording);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r0.messageContent.trim() || ctx_r0.selectedFiles.length);
  }
}
function MessageChatComponent_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 128)(1, "div", 129)(2, "div", 130)(3, "div", 131);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](4, "i", 132);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](5, "h1", 133);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](6, "DevBridge Messages");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](7, "p", 134);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](8, "Messagerie professionnelle en temps r\u00E9el");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](9, "div", 135)(10, "div", 136)(11, "div", 137);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](12, "i", 138);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](13, "div")(14, "h4", 139);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](15, "Messages en temps r\u00E9el");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](16, "p", 140);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](17, " Conversations instantan\u00E9es avec notifications ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](18, "div", 136)(19, "div", 141);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](20, "i", 142);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](21, "div")(22, "h4", 139);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](23, "Appels audio/vid\u00E9o");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](24, "p", 140);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](25, "Communication directe int\u00E9gr\u00E9e");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](26, "div", 136)(27, "div", 143);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](28, "i", 144);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](29, "div")(30, "h4", 139);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](31, "Partage de fichiers");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](32, "p", 140);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](33, "Images, documents et m\u00E9dias");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](34, "div", 145)(35, "h3", 146);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](36, "Comment commencer ?");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](37, "div", 147)(38, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](39, "\u2022 S\u00E9lectionnez une conversation dans la sidebar");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](40, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](41, "\u2022 Ou cliquez sur un contact pour d\u00E9marrer une discussion");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](42, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](43, "\u2022 Utilisez la recherche pour trouver rapidement");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](44, "app-system-status");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
  }
}
class MessageChatComponent {
  constructor(messageService, authService, toastService, route, router, cdr, ngZone) {
    this.messageService = messageService;
    this.authService = authService;
    this.toastService = toastService;
    this.route = route;
    this.router = router;
    this.cdr = cdr;
    this.ngZone = ngZone;
    // État du composant
    this.currentUser = null;
    this.selectedConversation = null;
    this.messages = [];
    this.isLoading = false;
    this.isTyping = false;
    this.typingUsers = [];
    // Pagination
    this.currentPage = 1;
    this.hasMoreMessages = true;
    this.loadingMoreMessages = false;
    // Formulaire de message
    this.messageContent = '';
    this.selectedFiles = [];
    this.isRecording = false;
    this.recordingDuration = 0;
    // États UI
    this.showEmojiPicker = false;
    this.showAttachmentMenu = false;
    this.replyingTo = null;
    this.editingMessage = null;
    // Recherche
    this.searchQuery = '';
    this.searchResults = [];
    this.showSearchResults = false;
    // Subscriptions
    this.subscriptions = [];
    // Observables
    this.conversationId$ = new rxjs__WEBPACK_IMPORTED_MODULE_7__.BehaviorSubject(null);
    // Constantes
    this.MessageType = _models_message_model__WEBPACK_IMPORTED_MODULE_1__.MessageType;
    this.CallType = _models_message_model__WEBPACK_IMPORTED_MODULE_1__.CallType;
  }
  ngOnInit() {
    this.initializeComponent();
    this.setupSubscriptions();
  }
  ngAfterViewInit() {
    this.scrollToBottom();
  }
  ngOnDestroy() {
    this.cleanup();
  }
  // ============================================================================
  // MÉTHODES D'INITIALISATION
  // ============================================================================
  initializeComponent() {
    // Récupérer l'utilisateur actuel
    this.currentUser = this.authService.getCurrentUser();
    if (!this.currentUser) {
      this.router.navigate(['/login']);
      return;
    }
    // Écouter les changements de route pour la conversation
    this.route.params.subscribe(params => {
      const conversationId = params['conversationId'];
      if (conversationId) {
        this.conversationId$.next(conversationId);
      }
    });
  }
  setupSubscriptions() {
    // Subscription pour charger la conversation
    const conversationSub = this.conversationId$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_8__.filter)(id => !!id), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__.distinctUntilChanged)(), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_10__.tap)(() => {
      this.isLoading = true;
      this.messages = [];
      this.currentPage = 1;
      this.hasMoreMessages = true;
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_11__.switchMap)(conversationId => this.messageService.getConversation(conversationId, 25, 1)), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_12__.catchError)(error => {
      console.error('Erreur lors du chargement de la conversation:', error);
      this.toastService.showError('Erreur lors du chargement de la conversation');
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_13__.of)(null);
    })).subscribe(conversation => {
      this.isLoading = false;
      if (conversation) {
        this.selectedConversation = conversation;
        this.messages = conversation.messages || [];
        this.scrollToBottom();
        this.markMessagesAsRead();
      }
      this.cdr.detectChanges();
    });
    // Subscription pour les nouveaux messages
    const messagesSub = this.messageService.subscribeToMessages().subscribe(message => {
      if (message && this.selectedConversation && message.conversationId === this.selectedConversation.id) {
        this.addNewMessage(message);
        this.scrollToBottom();
        this.markMessageAsRead(message);
      }
    });
    // Subscription pour les indicateurs de frappe
    const typingSub = this.messageService.subscribeToTypingIndicators().subscribe(event => {
      if (event && this.selectedConversation && event.conversationId === this.selectedConversation.id) {
        this.handleTypingIndicator(event);
      }
    });
    this.subscriptions.push(conversationSub, messagesSub, typingSub);
  }
  cleanup() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
    }
    if (this.recordingInterval) {
      clearInterval(this.recordingInterval);
    }
    this.stopTyping();
  }
  // ============================================================================
  // MÉTHODES DE GESTION DES MESSAGES
  // ============================================================================
  sendMessage() {
    if (!this.canSendMessage()) {
      return;
    }
    const content = this.messageContent.trim();
    const files = this.selectedFiles;
    // Réinitialiser le formulaire
    this.messageContent = '';
    this.selectedFiles = [];
    this.replyingTo = null;
    this.stopTyping();
    if (this.editingMessage) {
      this.updateMessage(content);
      return;
    }
    // Envoyer le message
    if (content || files.length > 0) {
      this.sendNewMessage(content, files);
    }
  }
  canSendMessage() {
    const hasContent = this.messageContent.trim().length > 0;
    const hasFiles = this.selectedFiles.length > 0;
    const hasConversation = !!this.selectedConversation;
    return hasConversation && (hasContent || hasFiles);
  }
  sendNewMessage(content, files) {
    if (!this.selectedConversation || !this.currentUser) return;
    const recipientId = this.getRecipientId();
    if (!recipientId) return;
    // Créer un message temporaire pour l'affichage immédiat
    const tempMessage = {
      id: `temp-${Date.now()}`,
      content,
      type: files.length > 0 ? this.getFileMessageType(files[0]) : _models_message_model__WEBPACK_IMPORTED_MODULE_1__.MessageType.TEXT,
      timestamp: new Date(),
      sender: this.currentUser,
      isPending: true,
      conversationId: this.selectedConversation.id
    };
    this.addNewMessage(tempMessage);
    this.scrollToBottom();
    // Envoyer le message via le service
    const sendObservable = files.length > 0 ? this.messageService.sendMessage(recipientId, content, files[0]) : this.messageService.sendMessage(recipientId, content);
    sendObservable.subscribe({
      next: sentMessage => {
        this.replaceTemporaryMessage(tempMessage.id, sentMessage);
        this.toastService.showSuccess('Message envoyé');
      },
      error: error => {
        console.error("Erreur lors de l'envoi du message:", error);
        this.markMessageAsError(tempMessage.id);
        this.toastService.showError("Erreur lors de l'envoi du message");
      }
    });
  }
  updateMessage(newContent) {
    if (!this.editingMessage) return;
    this.messageService.editMessage(this.editingMessage.id, newContent).subscribe({
      next: updatedMessage => {
        this.updateMessageInList(updatedMessage);
        this.editingMessage = null;
        this.toastService.showSuccess('Message modifié');
      },
      error: error => {
        console.error('Erreur lors de la modification du message:', error);
        this.toastService.showError('Erreur lors de la modification du message');
      }
    });
  }
  deleteMessage(message) {
    if (!message.id || !this.canDeleteMessage(message)) return;
    if (confirm('Êtes-vous sûr de vouloir supprimer ce message ?')) {
      this.messageService.deleteMessage(message.id).subscribe({
        next: () => {
          this.removeMessageFromList(message.id);
          this.toastService.showSuccess('Message supprimé');
        },
        error: error => {
          console.error('Erreur lors de la suppression du message:', error);
          this.toastService.showError('Erreur lors de la suppression du message');
        }
      });
    }
  }
  reactToMessage(message, emoji) {
    if (!message.id) return;
    this.messageService.reactToMessage(message.id, emoji).subscribe({
      next: updatedMessage => {
        this.updateMessageInList(updatedMessage);
      },
      error: error => {
        console.error('Erreur lors de la réaction au message:', error);
        this.toastService.showError('Erreur lors de la réaction');
      }
    });
  }
  // ============================================================================
  // MÉTHODES DE GESTION DES FICHIERS ET MÉDIAS
  // ============================================================================
  onFileSelected(event) {
    const files = event.target.files;
    if (files && files.length > 0) {
      this.selectedFiles = Array.from(files);
      this.showAttachmentMenu = false;
      // Auto-envoyer si c'est juste un fichier sans texte
      if (this.messageContent.trim() === '') {
        this.sendMessage();
      }
    }
  }
  removeSelectedFile(index) {
    this.selectedFiles.splice(index, 1);
  }
  openFileSelector() {
    this.fileInput.nativeElement.click();
  }
  // ============================================================================
  // MÉTHODES D'ENREGISTREMENT VOCAL
  // ============================================================================
  startVoiceRecording() {
    var _this = this;
    return (0,C_Users_gayou_OneDrive_Bureau_Project_PI_devBridge_frontend_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__["default"])(function* () {
      try {
        const stream = yield navigator.mediaDevices.getUserMedia({
          audio: true
        });
        _this.isRecording = true;
        _this.recordingDuration = 0;
        // Démarrer le compteur de durée
        _this.recordingInterval = setInterval(() => {
          _this.recordingDuration++;
        }, 1000);
        // Ici, vous pouvez implémenter l'enregistrement audio
        // avec MediaRecorder API
      } catch (error) {
        console.error("Erreur lors de l'accès au microphone:", error);
        _this.toastService.showError("Impossible d'accéder au microphone");
      }
    })();
  }
  stopVoiceRecording() {
    this.isRecording = false;
    if (this.recordingInterval) {
      clearInterval(this.recordingInterval);
    }
    // Ici, vous pouvez traiter l'enregistrement et l'envoyer
    // comme message vocal
  }

  cancelVoiceRecording() {
    this.isRecording = false;
    this.recordingDuration = 0;
    if (this.recordingInterval) {
      clearInterval(this.recordingInterval);
    }
  }
  // ============================================================================
  // MÉTHODES D'APPELS AUDIO/VIDÉO
  // ============================================================================
  startAudioCall() {
    if (!this.selectedConversation) return;
    const recipientId = this.getRecipientId();
    if (!recipientId) return;
    this.messageService.initiateCall(recipientId, _models_message_model__WEBPACK_IMPORTED_MODULE_1__.CallType.AUDIO).subscribe({
      next: call => {
        this.toastService.showSuccess('Appel audio initié');
        // Rediriger vers l'interface d'appel
      },

      error: error => {
        console.error("Erreur lors de l'initiation de l'appel:", error);
        this.toastService.showError("Erreur lors de l'appel");
      }
    });
  }
  startVideoCall() {
    if (!this.selectedConversation) return;
    const recipientId = this.getRecipientId();
    if (!recipientId) return;
    this.messageService.initiateCall(recipientId, _models_message_model__WEBPACK_IMPORTED_MODULE_1__.CallType.VIDEO).subscribe({
      next: call => {
        this.toastService.showSuccess('Appel vidéo initié');
        // Rediriger vers l'interface d'appel
      },

      error: error => {
        console.error("Erreur lors de l'initiation de l'appel vidéo:", error);
        this.toastService.showError("Erreur lors de l'appel vidéo");
      }
    });
  }
  // ============================================================================
  // MÉTHODES DE GESTION DE LA FRAPPE
  // ============================================================================
  onTyping() {
    if (!this.selectedConversation || this.isTyping) return;
    this.isTyping = true;
    this.messageService.startTyping(this.selectedConversation.id).subscribe();
    // Arrêter la frappe après 3 secondes d'inactivité
    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
    }
    this.typingTimeout = setTimeout(() => {
      this.stopTyping();
    }, 3000);
  }
  stopTyping() {
    if (!this.isTyping || !this.selectedConversation) return;
    this.isTyping = false;
    this.messageService.stopTyping(this.selectedConversation.id).subscribe();
    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
    }
  }
  // ============================================================================
  // MÉTHODES UTILITAIRES
  // ============================================================================
  getRecipientId() {
    if (!this.selectedConversation || !this.currentUser) return null;
    const participants = this.selectedConversation.participants || [];
    const currentUserId = this.currentUser.id || this.currentUser._id;
    const recipient = participants.find(p => (p.id || p._id) !== currentUserId);
    return recipient ? recipient.id || recipient._id : null;
  }
  getFileMessageType(file) {
    const type = file.type.split('/')[0];
    switch (type) {
      case 'image':
        return _models_message_model__WEBPACK_IMPORTED_MODULE_1__.MessageType.IMAGE;
      case 'video':
        return _models_message_model__WEBPACK_IMPORTED_MODULE_1__.MessageType.VIDEO;
      case 'audio':
        return _models_message_model__WEBPACK_IMPORTED_MODULE_1__.MessageType.AUDIO;
      default:
        return _models_message_model__WEBPACK_IMPORTED_MODULE_1__.MessageType.FILE;
    }
  }
  addNewMessage(message) {
    this.messages.push(message);
    this.cdr.detectChanges();
  }
  replaceTemporaryMessage(tempId, realMessage) {
    const index = this.messages.findIndex(m => m.id === tempId);
    if (index !== -1) {
      this.messages[index] = realMessage;
      this.cdr.detectChanges();
    }
  }
  markMessageAsError(messageId) {
    const message = this.messages.find(m => m.id === messageId);
    if (message) {
      message.isPending = false;
      message.isError = true;
      this.cdr.detectChanges();
    }
  }
  updateMessageInList(updatedMessage) {
    const index = this.messages.findIndex(m => m.id === updatedMessage.id);
    if (index !== -1) {
      this.messages[index] = updatedMessage;
      this.cdr.detectChanges();
    }
  }
  removeMessageFromList(messageId) {
    this.messages = this.messages.filter(m => m.id !== messageId);
    this.cdr.detectChanges();
  }
  canDeleteMessage(message) {
    if (!this.currentUser || !message.sender) return false;
    const currentUserId = this.currentUser.id || this.currentUser._id;
    const senderId = message.sender.id || message.sender._id;
    return currentUserId === senderId;
  }
  handleTypingIndicator(event) {
    if (!this.currentUser) return;
    const currentUserId = this.currentUser.id || this.currentUser._id;
    if (event.userId === currentUserId) return; // Ignorer ses propres indicateurs
    if (event.isTyping) {
      // Ajouter l'utilisateur à la liste des utilisateurs en train de taper
      const user = this.selectedConversation?.participants?.find(p => (p.id || p._id) === event.userId);
      if (user && !this.typingUsers.find(u => (u.id || u._id) === event.userId)) {
        this.typingUsers.push(user);
      }
    } else {
      // Retirer l'utilisateur de la liste
      this.typingUsers = this.typingUsers.filter(u => (u.id || u._id) !== event.userId);
    }
    this.cdr.detectChanges();
  }
  markMessagesAsRead() {
    if (!this.messages.length || !this.currentUser) return;
    const unreadMessages = this.messages.filter(m => !m.isRead && m.sender && (m.sender.id || m.sender._id) !== (this.currentUser.id || this.currentUser._id));
    unreadMessages.forEach(message => {
      if (message.id) {
        this.markMessageAsRead(message);
      }
    });
  }
  markMessageAsRead(message) {
    if (!message.id || message.isRead) return;
    this.messageService.markMessageAsRead(message.id).subscribe({
      next: updatedMessage => {
        this.updateMessageInList(updatedMessage);
      },
      error: error => {
        console.error('Erreur lors du marquage comme lu:', error);
      }
    });
  }
  scrollToBottom() {
    this.ngZone.runOutsideAngular(() => {
      setTimeout(() => {
        if (this.messagesContainer) {
          const element = this.messagesContainer.nativeElement;
          element.scrollTop = element.scrollHeight;
        }
      }, 100);
    });
  }
  // ============================================================================
  // MÉTHODES PUBLIQUES POUR LE TEMPLATE
  // ============================================================================
  formatMessageTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    if (diffInHours < 24) {
      return date.toLocaleTimeString('fr-FR', {
        hour: '2-digit',
        minute: '2-digit'
      });
    } else {
      return date.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit'
      });
    }
  }
  isMyMessage(message) {
    if (!this.currentUser || !message.sender) return false;
    const currentUserId = this.currentUser.id || this.currentUser._id;
    const senderId = message.sender.id || message.sender._id;
    return currentUserId === senderId;
  }
  getTypingText() {
    if (this.typingUsers.length === 0) return '';
    if (this.typingUsers.length === 1) {
      return `${this.typingUsers[0].username} est en train d'écrire...`;
    } else {
      return `${this.typingUsers.length} personnes sont en train d'écrire...`;
    }
  }
  onKeyPress(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendMessage();
    } else {
      this.onTyping();
    }
  }
  toggleEmojiPicker() {
    this.showEmojiPicker = !this.showEmojiPicker;
  }
  toggleAttachmentMenu() {
    this.showAttachmentMenu = !this.showAttachmentMenu;
  }
  startEditingMessage(message) {
    this.editingMessage = message;
    this.messageContent = message.content || '';
    this.messageInput.nativeElement.focus();
  }
  cancelEditing() {
    this.editingMessage = null;
    this.messageContent = '';
  }
  setReplyTo(message) {
    this.replyingTo = message;
    this.messageInput.nativeElement.focus();
  }
  cancelReply() {
    this.replyingTo = null;
  }
  // ============================================================================
  // MÉTHODES POUR LE TEMPLATE (MANQUANTES)
  // ============================================================================
  getRecipientName() {
    if (!this.selectedConversation || !this.currentUser) return '';
    const participants = this.selectedConversation.participants || [];
    const currentUserId = this.currentUser.id || this.currentUser._id;
    const recipient = participants.find(p => (p.id || p._id) !== currentUserId);
    return recipient?.username || 'Utilisateur inconnu';
  }
  getRecipientAvatar() {
    if (!this.selectedConversation || !this.currentUser) return '/assets/images/default-avatar.png';
    const participants = this.selectedConversation.participants || [];
    const currentUserId = this.currentUser.id || this.currentUser._id;
    const recipient = participants.find(p => (p.id || p._id) !== currentUserId);
    return recipient?.image || '/assets/images/default-avatar.png';
  }
  isRecipientOnline() {
    if (!this.selectedConversation || !this.currentUser) return false;
    const participants = this.selectedConversation.participants || [];
    const currentUserId = this.currentUser.id || this.currentUser._id;
    const recipient = participants.find(p => (p.id || p._id) !== currentUserId);
    return recipient?.isOnline || false;
  }
  trackByMessageId(index, message) {
    return message.id || message._id || index.toString();
  }
  openImageViewer(attachment) {
    if (!attachment?.url) return;
    // Ouvrir l'image dans une nouvelle fenêtre ou modal
    window.open(attachment.url, '_blank');
  }
  formatFileSize(size) {
    if (!size) return '0 B';
    const units = ['B', 'KB', 'MB', 'GB'];
    let unitIndex = 0;
    let fileSize = size;
    while (fileSize >= 1024 && unitIndex < units.length - 1) {
      fileSize /= 1024;
      unitIndex++;
    }
    return `${fileSize.toFixed(1)} ${units[unitIndex]}`;
  }
  downloadFile(attachment) {
    if (!attachment?.url) return;
    const link = document.createElement('a');
    link.href = attachment.url;
    link.download = attachment.name || 'file';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
  playVoiceMessage(message) {
    if (!message.attachments?.[0]?.url) return;
    this.messageService.playAudio(message.attachments[0].url).catch(error => {
      console.error('Erreur lors de la lecture du message vocal:', error);
      this.toastService.showError('Erreur lors de la lecture du message vocal');
    });
  }
  formatDuration(duration) {
    if (!duration) return '0:00';
    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }
  showMessageMenu(message) {
    // Ici, vous pouvez implémenter un menu contextuel
    // Pour l'instant, on affiche juste les options disponibles
    const actions = [];
    if (this.canDeleteMessage(message)) {
      actions.push('Supprimer');
    }
    if (this.isMyMessage(message)) {
      actions.push('Modifier');
    }
    actions.push('Répondre', 'Transférer', 'Réagir');
    // Vous pouvez implémenter un vrai menu contextuel ici
    console.log('Actions disponibles pour ce message:', actions);
  }
  static {
    this.ɵfac = function MessageChatComponent_Factory(t) {
      return new (t || MessageChatComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_services_message_service__WEBPACK_IMPORTED_MODULE_2__.MessageService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_services_auth_service__WEBPACK_IMPORTED_MODULE_3__.AuthService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_services_toast_service__WEBPACK_IMPORTED_MODULE_4__.ToastService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_14__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_14__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_core__WEBPACK_IMPORTED_MODULE_6__.ChangeDetectorRef), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_core__WEBPACK_IMPORTED_MODULE_6__.NgZone));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineComponent"]({
      type: MessageChatComponent,
      selectors: [["app-message-chat"]],
      viewQuery: function MessageChatComponent_Query(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵviewQuery"](_c0, 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵviewQuery"](_c1, 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵviewQuery"](_c2, 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵviewQuery"](_c3, 5);
        }
        if (rf & 2) {
          let _t;
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵloadQuery"]()) && (ctx.messagesContainer = _t.first);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵloadQuery"]()) && (ctx.messageInput = _t.first);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵloadQuery"]()) && (ctx.fileInput = _t.first);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵloadQuery"]()) && (ctx.voiceRecorder = _t.first);
        }
      },
      decls: 2,
      vars: 2,
      consts: [["class", "chat-container", 4, "ngIf"], ["class", "flex items-center justify-center h-full bg-gray-900 text-gray-400", 4, "ngIf"], [1, "chat-container"], [1, "chat-header"], [1, "user-info"], [1, "user-avatar", 3, "src", "alt"], [1, "user-details"], [1, "user-status"], [4, "ngIf"], [1, "chat-actions"], ["class", "action-btn", "title", "Appel audio", 3, "click", 4, "ngIf"], ["class", "action-btn", "title", "Appel vid\u00E9o", 3, "click", 4, "ngIf"], ["title", "Options", 1, "action-btn"], [1, "fas", "fa-ellipsis-v"], [1, "messages-container"], ["messagesContainer", ""], ["class", "flex justify-center py-4", 4, "ngIf"], ["class", "message", 3, "my-message", 4, "ngFor", "ngForOf", "ngForTrackBy"], ["class", "typing-indicator", 4, "ngIf"], [1, "message-input-container"], ["class", "reply-preview", 4, "ngIf"], ["class", "mb-3", 4, "ngIf"], ["class", "recording-indicator mb-3", 4, "ngIf"], [1, "input-wrapper"], [1, "input-actions"], [1, "relative"], ["title", "Pi\u00E8ce jointe", 1, "input-btn", 3, "click"], [1, "fas", "fa-paperclip"], ["class", "absolute bottom-full left-0 mb-2 bg-gray-800 rounded-lg shadow-lg p-2 space-y-1", 4, "ngIf"], ["title", "Emoji", 1, "input-btn", 3, "click"], [1, "fas", "fa-smile"], ["placeholder", "Tapez votre message...", "rows", "1", 1, "message-input", 3, "ngModel", "disabled", "ngModelChange", "keydown", "input"], ["messageInput", ""], ["class", "input-btn", "title", "Message vocal", 3, "mousedown", 4, "ngIf"], ["class", "send-btn", "title", "Envoyer", 3, "disabled", "click", 4, "ngIf"], ["type", "file", "multiple", "", "accept", "image/*,video/*,audio/*,.pdf,.doc,.docx,.txt", 1, "hidden", 3, "change"], ["fileInput", ""], ["title", "Appel audio", 1, "action-btn", 3, "click"], [1, "fas", "fa-phone"], ["title", "Appel vid\u00E9o", 1, "action-btn", 3, "click"], [1, "fas", "fa-video"], [1, "flex", "justify-center", "py-4"], [1, "animate-spin", "rounded-full", "h-8", "w-8", "border-b-2", "border-blue-500"], [1, "message"], ["class", "message-avatar", 3, "src", "alt", 4, "ngIf"], [1, "message-content"], ["class", "text-xs text-blue-400 mb-1 font-medium", 4, "ngIf"], ["class", "reply-preview mb-2", 4, "ngIf"], [3, "ngSwitch"], ["class", "message-text", 4, "ngSwitchCase"], ["class", "message-image", 4, "ngSwitchCase"], ["class", "message-file", 4, "ngSwitchCase"], ["class", "voice-message", 4, "ngSwitchCase"], ["class", "message-video", 4, "ngSwitchCase"], ["class", "flex flex-wrap gap-1 mt-2", 4, "ngIf"], [1, "flex", "items-center", "justify-between", "mt-1"], [1, "message-time"], ["class", "message-status", 3, "read", "pending", "error", 4, "ngIf"], [1, "message-menu", "absolute", "top-0", "right-0", "hidden", "group-hover:block"], [1, "text-gray-400", "hover:text-white", "p-1", 3, "click"], [1, "fas", "fa-ellipsis-h", "text-xs"], [1, "message-avatar", 3, "src", "alt"], [1, "text-xs", "text-blue-400", "mb-1", "font-medium"], [1, "reply-preview", "mb-2"], [1, "text-xs", "text-gray-400"], [1, "text-sm", "text-gray-300", "truncate"], [1, "message-text"], [1, "message-image"], [1, "message-image", 3, "src", "alt", "click"], ["class", "message-text mt-2", 4, "ngIf"], [1, "message-text", "mt-2"], [1, "message-file"], [1, "file-icon", "fas", "fa-file"], [1, "file-info"], [1, "file-name"], [1, "file-size"], [1, "text-blue-400", "hover:text-blue-300", 3, "click"], [1, "fas", "fa-download"], [1, "voice-message"], [1, "voice-play-btn", 3, "click"], [1, "fas", "fa-play", "text-white", "text-xs"], [1, "voice-duration"], [1, "message-video"], ["controls", "", 1, "max-w-xs", "rounded-lg", 3, "src"], [1, "flex", "flex-wrap", "gap-1", "mt-2"], ["class", "text-xs bg-gray-600 rounded-full px-2 py-1 cursor-pointer", 3, "click", 4, "ngFor", "ngForOf"], [1, "text-xs", "bg-gray-600", "rounded-full", "px-2", "py-1", "cursor-pointer", 3, "click"], [1, "message-status"], ["class", "fas fa-clock", 4, "ngIf"], ["class", "fas fa-exclamation-triangle", 4, "ngIf"], ["class", "fas fa-check-double", 4, "ngIf"], ["class", "fas fa-check", 4, "ngIf"], [1, "fas", "fa-clock"], [1, "fas", "fa-exclamation-triangle"], [1, "fas", "fa-check-double"], [1, "fas", "fa-check"], [1, "typing-indicator"], [1, "typing-dots"], [1, "typing-dot"], [1, "reply-preview"], [1, "reply-header"], [1, "text-xs", "text-blue-400"], [1, "reply-text"], [1, "text-gray-400", "hover:text-white", 3, "click"], [1, "fas", "fa-times"], [1, "text-xs", "text-yellow-400"], [1, "mb-3"], [1, "flex", "flex-wrap", "gap-2"], ["class", "flex items-center space-x-2 bg-gray-700 rounded-lg p-2", 4, "ngFor", "ngForOf"], [1, "flex", "items-center", "space-x-2", "bg-gray-700", "rounded-lg", "p-2"], [1, "fas", "fa-file", "text-blue-400"], [1, "text-sm", "text-white", "truncate", "max-w-32"], [1, "text-red-400", "hover:text-red-300", 3, "click"], [1, "fas", "fa-times", "text-xs"], [1, "recording-indicator", "mb-3"], [1, "fas", "fa-microphone", "text-white"], [1, "recording-time"], [1, "text-white", "hover:text-gray-300", 3, "click"], [1, "fas", "fa-stop"], [1, "text-white", "hover:text-gray-300", "ml-2", 3, "click"], [1, "absolute", "bottom-full", "left-0", "mb-2", "bg-gray-800", "rounded-lg", "shadow-lg", "p-2", "space-y-1"], [1, "flex", "items-center", "space-x-2", "w-full", "p-2", "hover:bg-gray-700", "rounded", "text-left", 3, "click"], [1, "text-white", "text-sm"], [1, "fas", "fa-image", "text-green-400"], ["title", "Message vocal", 1, "input-btn", 3, "mousedown"], [1, "fas", "fa-microphone"], ["title", "Envoyer", 1, "send-btn", 3, "disabled", "click"], [1, "fas", "fa-paper-plane", "text-white"], [1, "flex", "items-center", "justify-center", "h-full", "bg-gray-900", "text-gray-400"], [1, "text-center", "max-w-md", "mx-auto", "p-8"], [1, "mb-8"], [1, "w-24", "h-24", "mx-auto", "bg-gradient-to-br", "from-blue-500", "to-purple-600", "rounded-full", "flex", "items-center", "justify-center", "mb-4"], [1, "fas", "fa-comments", "text-3xl", "text-white"], [1, "text-3xl", "font-bold", "text-white", "mb-2"], [1, "text-gray-400"], [1, "space-y-4", "mb-8"], [1, "flex", "items-center", "space-x-3", "text-left"], [1, "w-10", "h-10", "bg-blue-600", "rounded-full", "flex", "items-center", "justify-center"], [1, "fas", "fa-bolt", "text-white", "text-sm"], [1, "text-white", "font-medium"], [1, "text-sm", "text-gray-400"], [1, "w-10", "h-10", "bg-green-600", "rounded-full", "flex", "items-center", "justify-center"], [1, "fas", "fa-phone", "text-white", "text-sm"], [1, "w-10", "h-10", "bg-purple-600", "rounded-full", "flex", "items-center", "justify-center"], [1, "fas", "fa-file", "text-white", "text-sm"], [1, "bg-gray-800", "rounded-lg", "p-6", "border", "border-gray-700", "mb-6"], [1, "text-lg", "font-semibold", "text-white", "mb-3"], [1, "space-y-2", "text-sm", "text-gray-300"]],
      template: function MessageChatComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](0, MessageChatComponent_div_0_Template, 41, 25, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](1, MessageChatComponent_div_1_Template, 45, 0, "div", 1);
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.selectedConversation);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !ctx.selectedConversation);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_15__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_15__.NgIf, _angular_common__WEBPACK_IMPORTED_MODULE_15__.NgSwitch, _angular_common__WEBPACK_IMPORTED_MODULE_15__.NgSwitchCase, _angular_forms__WEBPACK_IMPORTED_MODULE_16__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_16__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_16__.NgModel, _components_system_status_system_status_component__WEBPACK_IMPORTED_MODULE_5__.SystemStatusComponent],
      styles: ["\n\n\n\n\n.chat-container[_ngcontent-%COMP%] {\n  display: flex;\n  height: 100%;\n  flex-direction: column;\n  --tw-bg-opacity: 1;\n  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n\n\n\n\n\n\n.chat-header[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  border-bottom-width: 1px;\n  --tw-border-opacity: 1;\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\n  --tw-bg-opacity: 1;\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\n  padding: 1rem;\n  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);\n}\n\n.user-info[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n}\n\n.user-info[_ngcontent-%COMP%]    > [_ngcontent-%COMP%]:not([hidden])    ~ [_ngcontent-%COMP%]:not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\n}\n\n.user-avatar[_ngcontent-%COMP%] {\n  height: 2.5rem;\n  width: 2.5rem;\n  border-radius: 9999px;\n  border-width: 2px;\n  --tw-border-opacity: 1;\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\n  box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);\n}\n\n.user-details[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  font-weight: 600;\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n\n.user-status[_ngcontent-%COMP%] {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  --tw-text-opacity: 1;\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\n}\n\n.user-status.online[_ngcontent-%COMP%] {\n  --tw-text-opacity: 1;\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\n}\n\n.chat-actions[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n}\n\n.chat-actions[_ngcontent-%COMP%]    > [_ngcontent-%COMP%]:not([hidden])    ~ [_ngcontent-%COMP%]:not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\n\n.action-btn[_ngcontent-%COMP%] {\n  border-radius: 9999px;\n  --tw-bg-opacity: 1;\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\n  padding: 0.5rem;\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 200ms;\n}\n\n.action-btn[_ngcontent-%COMP%]:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));\n}\n\n.action-btn[_ngcontent-%COMP%] {\n  border: 1px solid rgba(59, 130, 246, 0.3);\n}\n\n.action-btn[_ngcontent-%COMP%]:hover {\n  box-shadow: 0 0 15px rgba(59, 130, 246, 0.4);\n  border-color: rgba(59, 130, 246, 0.6);\n}\n\n.action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  --tw-text-opacity: 1;\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\n}\n\n\n\n\n\n\n.messages-container[_ngcontent-%COMP%] {\n  flex: 1 1 0%;\n}\n\n.messages-container[_ngcontent-%COMP%]    > [_ngcontent-%COMP%]:not([hidden])    ~ [_ngcontent-%COMP%]:not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\n}\n\n.messages-container[_ngcontent-%COMP%] {\n  overflow-y: auto;\n  padding: 1rem;\n  background: linear-gradient(180deg, #111827 0%, #0f172a 100%);\n  scrollbar-width: thin;\n  scrollbar-color: #374151 #1f2937;\n}\n\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar {\n  width: 6px;\n}\n\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-track {\n  background: #1f2937;\n}\n\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\n  background: #374151;\n  border-radius: 3px;\n}\n\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\n  background: #4b5563;\n}\n\n\n\n\n\n\n.message[_ngcontent-%COMP%] {\n  display: flex;\n  max-width: 20rem;\n  align-items: flex-end;\n}\n\n.message[_ngcontent-%COMP%]    > [_ngcontent-%COMP%]:not([hidden])    ~ [_ngcontent-%COMP%]:not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\n\n@media (min-width: 768px) {\n\n  .message[_ngcontent-%COMP%] {\n    max-width: 28rem;\n  }\n}\n\n.message[_ngcontent-%COMP%] {\n  animation: _ngcontent-%COMP%_messageSlideIn 0.3s ease-out;\n}\n\n@keyframes _ngcontent-%COMP%_messageSlideIn {\n  from {\n    opacity: 0;\n    transform: translateY(10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.message.my-message[_ngcontent-%COMP%] {\n  margin-left: auto;\n  flex-direction: row-reverse;\n}\n\n.message.my-message[_ngcontent-%COMP%]    > [_ngcontent-%COMP%]:not([hidden])    ~ [_ngcontent-%COMP%]:not([hidden]) {\n  --tw-space-x-reverse: 1;\n}\n\n.message-avatar[_ngcontent-%COMP%] {\n  height: 2rem;\n  width: 2rem;\n  border-radius: 9999px;\n  border-width: 1px;\n  --tw-border-opacity: 1;\n  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\n}\n\n.message-content[_ngcontent-%COMP%] {\n  max-width: 100%;\n  overflow-wrap: break-word;\n  border-radius: 1rem;\n  padding-left: 1rem;\n  padding-right: 1rem;\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n  position: relative;\n}\n\n.message-content.my-message[_ngcontent-%COMP%] {\n  --tw-bg-opacity: 1;\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\n  box-shadow: 0 2px 10px rgba(59, 130, 246, 0.3);\n}\n\n.message-content.other-message[_ngcontent-%COMP%] {\n  border-width: 1px;\n  --tw-border-opacity: 1;\n  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\n  --tw-bg-opacity: 1;\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n  background: linear-gradient(135deg, #374151 0%, #1f2937 100%);\n}\n\n.message-text[_ngcontent-%COMP%] {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  line-height: 1.625;\n}\n\n.message-time[_ngcontent-%COMP%] {\n  margin-top: 0.25rem;\n  display: block;\n  font-size: 0.75rem;\n  line-height: 1rem;\n  --tw-text-opacity: 1;\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\n}\n\n.message-status[_ngcontent-%COMP%] {\n  margin-top: 0.25rem;\n  font-size: 0.75rem;\n  line-height: 1rem;\n  --tw-text-opacity: 1;\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\n}\n\n.message-status.read[_ngcontent-%COMP%] {\n  --tw-text-opacity: 1;\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\n}\n\n.message-status.pending[_ngcontent-%COMP%] {\n  --tw-text-opacity: 1;\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\n}\n\n.message-status.error[_ngcontent-%COMP%] {\n  --tw-text-opacity: 1;\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\n}\n\n\n\n\n\n\n.message-image[_ngcontent-%COMP%] {\n  max-width: 20rem;\n  cursor: pointer;\n  border-radius: 0.5rem;\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);\n}\n\n.message-file[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n}\n\n.message-file[_ngcontent-%COMP%]    > [_ngcontent-%COMP%]:not([hidden])    ~ [_ngcontent-%COMP%]:not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\n\n.message-file[_ngcontent-%COMP%] {\n  border-radius: 0.5rem;\n  border-width: 1px;\n  --tw-border-opacity: 1;\n  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\n  --tw-bg-opacity: 1;\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\n  padding: 0.75rem;\n}\n\n.file-icon[_ngcontent-%COMP%] {\n  font-size: 1.25rem;\n  line-height: 1.75rem;\n  --tw-text-opacity: 1;\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\n}\n\n.file-info[_ngcontent-%COMP%] {\n  flex: 1 1 0%;\n}\n\n.file-name[_ngcontent-%COMP%] {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  font-weight: 500;\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n\n.file-size[_ngcontent-%COMP%] {\n  font-size: 0.75rem;\n  line-height: 1rem;\n  --tw-text-opacity: 1;\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\n}\n\n.voice-message[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n}\n\n.voice-message[_ngcontent-%COMP%]    > [_ngcontent-%COMP%]:not([hidden])    ~ [_ngcontent-%COMP%]:not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\n}\n\n.voice-message[_ngcontent-%COMP%] {\n  border-radius: 0.5rem;\n  --tw-bg-opacity: 1;\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\n  padding: 0.75rem;\n}\n\n.voice-play-btn[_ngcontent-%COMP%] {\n  display: flex;\n  height: 2rem;\n  width: 2rem;\n  cursor: pointer;\n  align-items: center;\n  justify-content: center;\n  border-radius: 9999px;\n  --tw-bg-opacity: 1;\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\n}\n\n.voice-duration[_ngcontent-%COMP%] {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  --tw-text-opacity: 1;\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\n}\n\n\n\n\n\n\n.typing-indicator[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n}\n\n.typing-indicator[_ngcontent-%COMP%]    > [_ngcontent-%COMP%]:not([hidden])    ~ [_ngcontent-%COMP%]:not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\n\n.typing-indicator[_ngcontent-%COMP%] {\n  padding: 0.75rem;\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  --tw-text-opacity: 1;\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\n}\n\n.typing-dots[_ngcontent-%COMP%] {\n  display: flex;\n}\n\n.typing-dots[_ngcontent-%COMP%]    > [_ngcontent-%COMP%]:not([hidden])    ~ [_ngcontent-%COMP%]:not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\n}\n\n.typing-dot[_ngcontent-%COMP%] {\n  height: 0.5rem;\n  width: 0.5rem;\n  border-radius: 9999px;\n  --tw-bg-opacity: 1;\n  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));\n  animation: _ngcontent-%COMP%_typingPulse 1.4s infinite ease-in-out;\n}\n\n.typing-dot[_ngcontent-%COMP%]:nth-child(1) {\n  animation-delay: -0.32s;\n}\n\n.typing-dot[_ngcontent-%COMP%]:nth-child(2) {\n  animation-delay: -0.16s;\n}\n\n@keyframes _ngcontent-%COMP%_typingPulse {\n  0%, 80%, 100% {\n    transform: scale(0.8);\n    opacity: 0.5;\n  }\n  40% {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n\n\n\n\n\n\n.message-input-container[_ngcontent-%COMP%] {\n  border-top-width: 1px;\n  --tw-border-opacity: 1;\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\n  --tw-bg-opacity: 1;\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\n  padding: 1rem;\n  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);\n}\n\n.reply-preview[_ngcontent-%COMP%] {\n  margin-bottom: 0.75rem;\n  border-radius: 0.5rem;\n  border-left-width: 4px;\n  --tw-border-opacity: 1;\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\n  --tw-bg-opacity: 1;\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\n  padding: 0.5rem;\n}\n\n.reply-header[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.reply-text[_ngcontent-%COMP%] {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  --tw-text-opacity: 1;\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\n}\n\n.input-wrapper[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: flex-end;\n}\n\n.input-wrapper[_ngcontent-%COMP%]    > [_ngcontent-%COMP%]:not([hidden])    ~ [_ngcontent-%COMP%]:not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\n\n.message-input[_ngcontent-%COMP%] {\n  flex: 1 1 0%;\n  resize: none;\n  border-radius: 1rem;\n  border-width: 1px;\n  --tw-border-opacity: 1;\n  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\n  --tw-bg-opacity: 1;\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\n  padding-left: 1rem;\n  padding-right: 1rem;\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n\n.message-input[_ngcontent-%COMP%]::placeholder {\n  --tw-placeholder-opacity: 1;\n  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));\n}\n\n.message-input[_ngcontent-%COMP%] {\n  min-height: 40px;\n  max-height: 120px;\n  transition: all 0.2s ease;\n}\n\n.message-input[_ngcontent-%COMP%]:focus {\n  --tw-border-opacity: 1;\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n  box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);\n}\n\n.input-actions[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n}\n\n.input-actions[_ngcontent-%COMP%]    > [_ngcontent-%COMP%]:not([hidden])    ~ [_ngcontent-%COMP%]:not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\n}\n\n.input-btn[_ngcontent-%COMP%] {\n  border-radius: 9999px;\n  --tw-bg-opacity: 1;\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\n  padding: 0.5rem;\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 200ms;\n}\n\n.input-btn[_ngcontent-%COMP%]:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));\n}\n\n.input-btn[_ngcontent-%COMP%] {\n  border: 1px solid rgba(59, 130, 246, 0.3);\n}\n\n.input-btn[_ngcontent-%COMP%]:hover {\n  box-shadow: 0 0 10px rgba(59, 130, 246, 0.4);\n}\n\n.input-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  --tw-text-opacity: 1;\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\n}\n\n.send-btn[_ngcontent-%COMP%] {\n  border-radius: 9999px;\n  --tw-bg-opacity: 1;\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\n  padding: 0.5rem;\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 200ms;\n}\n\n.send-btn[_ngcontent-%COMP%]:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));\n}\n\n.send-btn[_ngcontent-%COMP%] {\n  box-shadow: 0 2px 10px rgba(59, 130, 246, 0.3);\n}\n\n.send-btn[_ngcontent-%COMP%]:hover {\n  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.5);\n}\n\n.send-btn[_ngcontent-%COMP%]:disabled {\n  cursor: not-allowed;\n  --tw-bg-opacity: 1;\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));\n  box-shadow: none;\n}\n\n\n\n\n\n\n.recording-indicator[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n}\n\n.recording-indicator[_ngcontent-%COMP%]    > [_ngcontent-%COMP%]:not([hidden])    ~ [_ngcontent-%COMP%]:not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\n\n.recording-indicator[_ngcontent-%COMP%] {\n  border-radius: 0.5rem;\n  --tw-bg-opacity: 1;\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\n  padding: 0.75rem;\n  animation: _ngcontent-%COMP%_recordingPulse 1s infinite;\n}\n\n@keyframes _ngcontent-%COMP%_recordingPulse {\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.7;\n  }\n}\n\n.recording-time[_ngcontent-%COMP%] {\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n\n\n\n\n\n\n@media (max-width: 768px) {\n  .message[_ngcontent-%COMP%] {\n    max-width: 20rem;\n  }\n  \n  .chat-header[_ngcontent-%COMP%] {\n    padding-left: 0.75rem;\n    padding-right: 0.75rem;\n    padding-top: 0.5rem;\n    padding-bottom: 0.5rem;\n  }\n  \n  .messages-container[_ngcontent-%COMP%] {\n    padding-left: 0.75rem;\n    padding-right: 0.75rem;\n  }\n  \n  .message-input-container[_ngcontent-%COMP%] {\n    padding-left: 0.75rem;\n    padding-right: 0.75rem;\n  }\n}\n\n/*# sourceMappingURL=data:application/json;base64,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 */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvbWVzc2FnZXMvbWVzc2FnZS1jaGF0L21lc3NhZ2UtY2hhdC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOztpRkFFaUY7O0FBRy9FO0VBQUEsYUFBa0Q7RUFBbEQsWUFBa0Q7RUFBbEQsc0JBQWtEO0VBQWxELGtCQUFrRDtFQUFsRCx5REFBa0Q7RUFBbEQsb0JBQWtEO0VBQWxELG1EQUFBO0FBQWtEOztBQUdwRDs7aUZBRWlGOztBQUcvRTtFQUFBLGFBQWlGO0VBQWpGLG1CQUFpRjtFQUFqRiw4QkFBaUY7RUFBakYsd0JBQWlGO0VBQWpGLHNCQUFpRjtFQUFqRix5REFBaUY7RUFBakYsa0JBQWlGO0VBQWpGLHlEQUFpRjtFQUFqRixhQUFpRjtFQUNqRiw2REFBNkQ7RUFDN0QseUNBQUE7QUFGaUY7O0FBTWpGO0VBQUEsYUFBa0M7RUFBbEMsbUJBQUE7QUFBa0M7O0FBQWxDO0VBQUEsdUJBQWtDO0VBQWxDLHVEQUFrQztFQUFsQyxnRUFBQTtBQUFrQzs7QUFJbEM7RUFBQSxjQUFzRDtFQUF0RCxhQUFzRDtFQUF0RCxxQkFBc0Q7RUFBdEQsaUJBQXNEO0VBQXRELHNCQUFzRDtFQUF0RCwyREFBc0Q7RUFDdEQsNENBQUE7QUFEc0Q7O0FBS3REO0VBQUEsZ0JBQStCO0VBQS9CLG9CQUErQjtFQUEvQixtREFBQTtBQUErQjs7QUFJL0I7RUFBQSxtQkFBNEI7RUFBNUIsb0JBQTRCO0VBQTVCLG9CQUE0QjtFQUE1QixtREFBQTtBQUE0Qjs7QUFJNUI7RUFBQSxvQkFBcUI7RUFBckIsa0RBQUE7QUFBcUI7O0FBSXJCO0VBQUEsYUFBa0M7RUFBbEMsbUJBQUE7QUFBa0M7O0FBQWxDO0VBQUEsdUJBQWtDO0VBQWxDLHNEQUFrQztFQUFsQywrREFBQTtBQUFrQzs7QUFJbEM7RUFBQSxxQkFBaUY7RUFBakYsa0JBQWlGO0VBQWpGLHlEQUFpRjtFQUFqRixlQUFpRjtFQUFqRix3QkFBaUY7RUFBakYsd0RBQWlGO0VBQWpGLDBCQUFBO0FBQWlGOztBQUFqRjtFQUFBLGtCQUFpRjtFQUFqRix5REFBQTtBQUFpRjs7QUFEbkY7RUFFRSx5Q0FBeUM7QUFDM0M7O0FBRUE7RUFDRSw0Q0FBNEM7RUFDNUMscUNBQXFDO0FBQ3ZDOztBQUdFO0VBQUEsb0JBQW9CO0VBQXBCLGtEQUFBO0FBQW9COztBQUd0Qjs7aUZBRWlGOztBQUcvRTtFQUFBLFlBQUE7QUFBMkM7O0FBQTNDO0VBQUEsdUJBQTJDO0VBQTNDLDREQUEyQztFQUEzQyxxREFBQTtBQUEyQzs7QUFBM0M7RUFBQSxnQkFBMkM7RUFBM0MsYUFBMkM7RUFDM0MsNkRBQTZEO0VBQzdELHFCQUFxQjtFQUNyQixnQ0FBQTtBQUgyQzs7QUFNN0M7RUFDRSxVQUFVO0FBQ1o7O0FBRUE7RUFDRSxtQkFBbUI7QUFDckI7O0FBRUE7RUFDRSxtQkFBbUI7RUFDbkIsa0JBQWtCO0FBQ3BCOztBQUVBO0VBQ0UsbUJBQW1CO0FBQ3JCOztBQUVBOztpRkFFaUY7O0FBRy9FO0VBQUEsYUFBb0Q7RUFBcEQsZ0JBQW9EO0VBQXBELHFCQUFBO0FBQW9EOztBQUFwRDtFQUFBLHVCQUFvRDtFQUFwRCxzREFBb0Q7RUFBcEQsK0RBQUE7QUFBb0Q7O0FBQXBEOztFQUFBO0lBQUEsZ0JBQUE7RUFBb0Q7QUFBQTs7QUFEdEQ7RUFFRSx1Q0FBdUM7QUFDekM7O0FBRUE7RUFDRTtJQUNFLFVBQVU7SUFDViwyQkFBMkI7RUFDN0I7RUFDQTtJQUNFLFVBQVU7SUFDVix3QkFBd0I7RUFDMUI7QUFDRjs7QUFHRTtFQUFBLGlCQUErQztFQUEvQywyQkFBQTtBQUErQzs7QUFBL0M7RUFBQSx1QkFBQTtBQUErQzs7QUFJL0M7RUFBQSxZQUFrRDtFQUFsRCxXQUFrRDtFQUFsRCxxQkFBa0Q7RUFBbEQsaUJBQWtEO0VBQWxELHNCQUFrRDtFQUFsRCx5REFBQTtBQUFrRDs7QUFJbEQ7RUFBQSxlQUFtRDtFQUFuRCx5QkFBbUQ7RUFBbkQsbUJBQW1EO0VBQW5ELGtCQUFtRDtFQUFuRCxtQkFBbUQ7RUFBbkQsbUJBQW1EO0VBQW5ELHNCQUFtRDtFQUNuRCxrQkFBQTtBQURtRDs7QUFLbkQ7RUFBQSxrQkFBNkI7RUFBN0IsMERBQTZCO0VBQTdCLG9CQUE2QjtFQUE3QixtREFBNkI7RUFDN0IsNkRBQTZEO0VBQzdELDhDQUFBO0FBRjZCOztBQU03QjtFQUFBLGlCQUFvRDtFQUFwRCxzQkFBb0Q7RUFBcEQseURBQW9EO0VBQXBELGtCQUFvRDtFQUFwRCx5REFBb0Q7RUFBcEQsb0JBQW9EO0VBQXBELG1EQUFvRDtFQUNwRCw2REFBQTtBQURvRDs7QUFLcEQ7RUFBQSxtQkFBOEI7RUFBOUIsb0JBQThCO0VBQTlCLGtCQUFBO0FBQThCOztBQUk5QjtFQUFBLG1CQUF1QztFQUF2QyxjQUF1QztFQUF2QyxrQkFBdUM7RUFBdkMsaUJBQXVDO0VBQXZDLG9CQUF1QztFQUF2QyxtREFBQTtBQUF1Qzs7QUFJdkM7RUFBQSxtQkFBaUM7RUFBakMsa0JBQWlDO0VBQWpDLGlCQUFpQztFQUFqQyxvQkFBaUM7RUFBakMsbURBQUE7QUFBaUM7O0FBSWpDO0VBQUEsb0JBQW9CO0VBQXBCLGtEQUFBO0FBQW9COztBQUlwQjtFQUFBLG9CQUFzQjtFQUF0QixrREFBQTtBQUFzQjs7QUFJdEI7RUFBQSxvQkFBbUI7RUFBbkIsbURBQUE7QUFBbUI7O0FBR3JCOztpRkFFaUY7O0FBRy9FO0VBQUEsZ0JBQXlDO0VBQXpDLGVBQXlDO0VBQXpDLHFCQUF5QztFQUN6Qyx5Q0FBQTtBQUR5Qzs7QUFLekM7RUFBQSxhQUFvRjtFQUFwRixtQkFBQTtBQUFvRjs7QUFBcEY7RUFBQSx1QkFBb0Y7RUFBcEYsc0RBQW9GO0VBQXBGLCtEQUFBO0FBQW9GOztBQUFwRjtFQUFBLHFCQUFvRjtFQUFwRixpQkFBb0Y7RUFBcEYsc0JBQW9GO0VBQXBGLHlEQUFvRjtFQUFwRixrQkFBb0Y7RUFBcEYseURBQW9GO0VBQXBGLGdCQUFBO0FBQW9GOztBQUlwRjtFQUFBLGtCQUE0QjtFQUE1QixvQkFBNEI7RUFBNUIsb0JBQTRCO0VBQTVCLGtEQUFBO0FBQTRCOztBQUk1QjtFQUFBLFlBQUE7QUFBYTs7QUFJYjtFQUFBLG1CQUFxQztFQUFyQyxvQkFBcUM7RUFBckMsZ0JBQXFDO0VBQXJDLG9CQUFxQztFQUFyQyxtREFBQTtBQUFxQzs7QUFJckM7RUFBQSxrQkFBNEI7RUFBNUIsaUJBQTRCO0VBQTVCLG9CQUE0QjtFQUE1QixtREFBQTtBQUE0Qjs7QUFJNUI7RUFBQSxhQUE2RDtFQUE3RCxtQkFBQTtBQUE2RDs7QUFBN0Q7RUFBQSx1QkFBNkQ7RUFBN0QsdURBQTZEO0VBQTdELGdFQUFBO0FBQTZEOztBQUE3RDtFQUFBLHFCQUE2RDtFQUE3RCxrQkFBNkQ7RUFBN0QseURBQTZEO0VBQTdELGdCQUFBO0FBQTZEOztBQUk3RDtFQUFBLGFBQXVGO0VBQXZGLFlBQXVGO0VBQXZGLFdBQXVGO0VBQXZGLGVBQXVGO0VBQXZGLG1CQUF1RjtFQUF2Rix1QkFBdUY7RUFBdkYscUJBQXVGO0VBQXZGLGtCQUF1RjtFQUF2RiwwREFBQTtBQUF1Rjs7QUFJdkY7RUFBQSxtQkFBNEI7RUFBNUIsb0JBQTRCO0VBQTVCLG9CQUE0QjtFQUE1QixtREFBQTtBQUE0Qjs7QUFHOUI7O2lGQUVpRjs7QUFHL0U7RUFBQSxhQUE0RDtFQUE1RCxtQkFBQTtBQUE0RDs7QUFBNUQ7RUFBQSx1QkFBNEQ7RUFBNUQsc0RBQTREO0VBQTVELCtEQUFBO0FBQTREOztBQUE1RDtFQUFBLGdCQUE0RDtFQUE1RCxtQkFBNEQ7RUFBNUQsb0JBQTREO0VBQTVELG9CQUE0RDtFQUE1RCxtREFBQTtBQUE0RDs7QUFJNUQ7RUFBQSxhQUFBO0FBQXFCOztBQUFyQjtFQUFBLHVCQUFxQjtFQUFyQix1REFBcUI7RUFBckIsZ0VBQUE7QUFBcUI7O0FBSXJCO0VBQUEsY0FBdUM7RUFBdkMsYUFBdUM7RUFBdkMscUJBQXVDO0VBQXZDLGtCQUF1QztFQUF2Qyw0REFBdUM7RUFDdkMsZ0RBQUE7QUFEdUM7O0FBSXpDO0VBQ0UsdUJBQXVCO0FBQ3pCOztBQUVBO0VBQ0UsdUJBQXVCO0FBQ3pCOztBQUVBO0VBQ0U7SUFDRSxxQkFBcUI7SUFDckIsWUFBWTtFQUNkO0VBQ0E7SUFDRSxtQkFBbUI7SUFDbkIsVUFBVTtFQUNaO0FBQ0Y7O0FBRUE7O2lGQUVpRjs7QUFHL0U7RUFBQSxxQkFBK0M7RUFBL0Msc0JBQStDO0VBQS9DLHlEQUErQztFQUEvQyxrQkFBK0M7RUFBL0MseURBQStDO0VBQS9DLGFBQStDO0VBQy9DLDZEQUFBO0FBRCtDOztBQUsvQztFQUFBLHNCQUFpRTtFQUFqRSxxQkFBaUU7RUFBakUsc0JBQWlFO0VBQWpFLHNCQUFpRTtFQUFqRSwyREFBaUU7RUFBakUsa0JBQWlFO0VBQWpFLHlEQUFpRTtFQUFqRSxlQUFBO0FBQWlFOztBQUlqRTtFQUFBLGFBQXdDO0VBQXhDLG1CQUF3QztFQUF4Qyw4QkFBQTtBQUF3Qzs7QUFJeEM7RUFBQSxnQkFBcUM7RUFBckMsdUJBQXFDO0VBQXJDLG1CQUFxQztFQUFyQyxtQkFBcUM7RUFBckMsb0JBQXFDO0VBQXJDLG9CQUFxQztFQUFyQyxtREFBQTtBQUFxQzs7QUFJckM7RUFBQSxhQUErQjtFQUEvQixxQkFBQTtBQUErQjs7QUFBL0I7RUFBQSx1QkFBK0I7RUFBL0Isc0RBQStCO0VBQS9CLCtEQUFBO0FBQStCOztBQUkvQjtFQUFBLFlBQWtIO0VBQWxILFlBQWtIO0VBQWxILG1CQUFrSDtFQUFsSCxpQkFBa0g7RUFBbEgsc0JBQWtIO0VBQWxILHlEQUFrSDtFQUFsSCxrQkFBa0g7RUFBbEgseURBQWtIO0VBQWxILGtCQUFrSDtFQUFsSCxtQkFBa0g7RUFBbEgsbUJBQWtIO0VBQWxILHNCQUFrSDtFQUFsSCxvQkFBa0g7RUFBbEgsbURBQUE7QUFBa0g7O0FBQWxIO0VBQUEsMkJBQWtIO0VBQWxILDBEQUFBO0FBQWtIOztBQURwSDtFQUVFLGdCQUFnQjtFQUNoQixpQkFBaUI7RUFDakIseUJBQXlCO0FBQzNCOztBQUdFO0VBQUEsc0JBQW1DO0VBQW5DLDJEQUFtQztFQUFuQyw4QkFBbUM7RUFBbkMsbUJBQW1DO0VBQ25DLDRDQUFBO0FBRG1DOztBQUtuQztFQUFBLGFBQWtDO0VBQWxDLG1CQUFBO0FBQWtDOztBQUFsQztFQUFBLHVCQUFrQztFQUFsQyx1REFBa0M7RUFBbEMsZ0VBQUE7QUFBa0M7O0FBSWxDO0VBQUEscUJBQWlGO0VBQWpGLGtCQUFpRjtFQUFqRix5REFBaUY7RUFBakYsZUFBaUY7RUFBakYsd0JBQWlGO0VBQWpGLHdEQUFpRjtFQUFqRiwwQkFBQTtBQUFpRjs7QUFBakY7RUFBQSxrQkFBaUY7RUFBakYseURBQUE7QUFBaUY7O0FBRG5GO0VBRUUseUNBQXlDO0FBQzNDOztBQUVBO0VBQ0UsNENBQTRDO0FBQzlDOztBQUdFO0VBQUEsb0JBQW9CO0VBQXBCLGtEQUFBO0FBQW9COztBQUlwQjtFQUFBLHFCQUFpRjtFQUFqRixrQkFBaUY7RUFBakYsMERBQWlGO0VBQWpGLGVBQWlGO0VBQWpGLHdCQUFpRjtFQUFqRix3REFBaUY7RUFBakYsMEJBQUE7QUFBaUY7O0FBQWpGO0VBQUEsa0JBQWlGO0VBQWpGLDBEQUFBO0FBQWlGOztBQURuRjtFQUVFLDhDQUE4QztBQUNoRDs7QUFFQTtFQUNFLDhDQUE4QztBQUNoRDs7QUFHRTtFQUFBLG1CQUFxQztFQUFyQyxrQkFBcUM7RUFBckMseURBQXFDO0VBQ3JDLGdCQUFBO0FBRHFDOztBQUl2Qzs7aUZBRWlGOztBQUcvRTtFQUFBLGFBQTREO0VBQTVELG1CQUFBO0FBQTREOztBQUE1RDtFQUFBLHVCQUE0RDtFQUE1RCxzREFBNEQ7RUFBNUQsK0RBQUE7QUFBNEQ7O0FBQTVEO0VBQUEscUJBQTREO0VBQTVELGtCQUE0RDtFQUE1RCwwREFBNEQ7RUFBNUQsZ0JBQTREO0VBQzVELHFDQUFBO0FBRDREOztBQUk5RDtFQUNFO0lBQ0UsVUFBVTtFQUNaO0VBQ0E7SUFDRSxZQUFZO0VBQ2Q7QUFDRjs7QUFHRTtFQUFBLCtHQUEyQjtFQUEzQixvQkFBMkI7RUFBM0IsbURBQUE7QUFBMkI7O0FBRzdCOztpRkFFaUY7O0FBRWpGO0VBRUk7SUFBQSxnQkFBQTtFQUFlOztFQUlmO0lBQUEscUJBQWdCO0lBQWhCLHNCQUFnQjtJQUFoQixtQkFBZ0I7SUFBaEIsc0JBQUE7RUFBZ0I7O0VBSWhCO0lBQUEscUJBQVc7SUFBWCxzQkFBQTtFQUFXOztFQUlYO0lBQUEscUJBQVc7SUFBWCxzQkFBQTtFQUFXO0FBRWY7O0FBeVRBLDRyaEJBQTRyaEIiLCJzb3VyY2VzQ29udGVudCI6WyIvKiA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4gICBTVFlMRVMgUE9VUiBMRSBDT01QT1NBTlQgTUVTU0FHRSBDSEFUIC0gVEjDg8KITUUgU09NQlJFIEZVVFVSSVNURVxuICAgPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PSAqL1xuXG4uY2hhdC1jb250YWluZXIge1xuICBAYXBwbHkgaC1mdWxsIGZsZXggZmxleC1jb2wgYmctZ3JheS05MDAgdGV4dC13aGl0ZTtcbn1cblxuLyogPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICAgRU4tVMODwopURSBEVSBDSEFUXG4gICA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09ICovXG5cbi5jaGF0LWhlYWRlciB7XG4gIEBhcHBseSBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcC00IGJvcmRlci1iIGJvcmRlci1ncmF5LTcwMCBiZy1ncmF5LTgwMDtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzFmMjkzNyAwJSwgIzExMTgyNyAxMDAlKTtcbiAgYm94LXNoYWRvdzogMCAycHggMTBweCByZ2JhKDAsIDAsIDAsIDAuMyk7XG59XG5cbi51c2VyLWluZm8ge1xuICBAYXBwbHkgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zO1xufVxuXG4udXNlci1hdmF0YXIge1xuICBAYXBwbHkgdy0xMCBoLTEwIHJvdW5kZWQtZnVsbCBib3JkZXItMiBib3JkZXItYmx1ZS01MDA7XG4gIGJveC1zaGFkb3c6IDAgMCAxMHB4IHJnYmEoNTksIDEzMCwgMjQ2LCAwLjUpO1xufVxuXG4udXNlci1kZXRhaWxzIGgzIHtcbiAgQGFwcGx5IGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZTtcbn1cblxuLnVzZXItc3RhdHVzIHtcbiAgQGFwcGx5IHRleHQtc20gdGV4dC1ncmF5LTQwMDtcbn1cblxuLnVzZXItc3RhdHVzLm9ubGluZSB7XG4gIEBhcHBseSB0ZXh0LWdyZWVuLTQwMDtcbn1cblxuLmNoYXQtYWN0aW9ucyB7XG4gIEBhcHBseSBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTI7XG59XG5cbi5hY3Rpb24tYnRuIHtcbiAgQGFwcGx5IHAtMiByb3VuZGVkLWZ1bGwgYmctZ3JheS03MDAgaG92ZXI6YmctZ3JheS02MDAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwO1xuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDU5LCAxMzAsIDI0NiwgMC4zKTtcbn1cblxuLmFjdGlvbi1idG46aG92ZXIge1xuICBib3gtc2hhZG93OiAwIDAgMTVweCByZ2JhKDU5LCAxMzAsIDI0NiwgMC40KTtcbiAgYm9yZGVyLWNvbG9yOiByZ2JhKDU5LCAxMzAsIDI0NiwgMC42KTtcbn1cblxuLmFjdGlvbi1idG4gaSB7XG4gIEBhcHBseSB0ZXh0LWJsdWUtNDAwO1xufVxuXG4vKiA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4gICBaT05FIERFUyBNRVNTQUdFU1xuICAgPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PSAqL1xuXG4ubWVzc2FnZXMtY29udGFpbmVyIHtcbiAgQGFwcGx5IGZsZXgtMSBvdmVyZmxvdy15LWF1dG8gcC00IHNwYWNlLXktNDtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDE4MGRlZywgIzExMTgyNyAwJSwgIzBmMTcyYSAxMDAlKTtcbiAgc2Nyb2xsYmFyLXdpZHRoOiB0aGluO1xuICBzY3JvbGxiYXItY29sb3I6ICMzNzQxNTEgIzFmMjkzNztcbn1cblxuLm1lc3NhZ2VzLWNvbnRhaW5lcjo6LXdlYmtpdC1zY3JvbGxiYXIge1xuICB3aWR0aDogNnB4O1xufVxuXG4ubWVzc2FnZXMtY29udGFpbmVyOjotd2Via2l0LXNjcm9sbGJhci10cmFjayB7XG4gIGJhY2tncm91bmQ6ICMxZjI5Mzc7XG59XG5cbi5tZXNzYWdlcy1jb250YWluZXI6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHtcbiAgYmFja2dyb3VuZDogIzM3NDE1MTtcbiAgYm9yZGVyLXJhZGl1czogM3B4O1xufVxuXG4ubWVzc2FnZXMtY29udGFpbmVyOjotd2Via2l0LXNjcm9sbGJhci10aHVtYjpob3ZlciB7XG4gIGJhY2tncm91bmQ6ICM0YjU1NjM7XG59XG5cbi8qID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbiAgIE1FU1NBR0VTXG4gICA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09ICovXG5cbi5tZXNzYWdlIHtcbiAgQGFwcGx5IGZsZXggaXRlbXMtZW5kIHNwYWNlLXgtMiBtYXgtdy14cyBtZDptYXgtdy1tZDtcbiAgYW5pbWF0aW9uOiBtZXNzYWdlU2xpZGVJbiAwLjNzIGVhc2Utb3V0O1xufVxuXG5Aa2V5ZnJhbWVzIG1lc3NhZ2VTbGlkZUluIHtcbiAgZnJvbSB7XG4gICAgb3BhY2l0eTogMDtcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMTBweCk7XG4gIH1cbiAgdG8ge1xuICAgIG9wYWNpdHk6IDE7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApO1xuICB9XG59XG5cbi5tZXNzYWdlLm15LW1lc3NhZ2Uge1xuICBAYXBwbHkgbWwtYXV0byBmbGV4LXJvdy1yZXZlcnNlIHNwYWNlLXgtcmV2ZXJzZTtcbn1cblxuLm1lc3NhZ2UtYXZhdGFyIHtcbiAgQGFwcGx5IHctOCBoLTggcm91bmRlZC1mdWxsIGJvcmRlciBib3JkZXItZ3JheS02MDA7XG59XG5cbi5tZXNzYWdlLWNvbnRlbnQge1xuICBAYXBwbHkgcm91bmRlZC0yeGwgcHgtNCBweS0yIG1heC13LWZ1bGwgYnJlYWstd29yZHM7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbn1cblxuLm1lc3NhZ2UtY29udGVudC5teS1tZXNzYWdlIHtcbiAgQGFwcGx5IGJnLWJsdWUtNjAwIHRleHQtd2hpdGU7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMzYjgyZjYgMCUsICMxZDRlZDggMTAwJSk7XG4gIGJveC1zaGFkb3c6IDAgMnB4IDEwcHggcmdiYSg1OSwgMTMwLCAyNDYsIDAuMyk7XG59XG5cbi5tZXNzYWdlLWNvbnRlbnQub3RoZXItbWVzc2FnZSB7XG4gIEBhcHBseSBiZy1ncmF5LTcwMCB0ZXh0LXdoaXRlIGJvcmRlciBib3JkZXItZ3JheS02MDA7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMzNzQxNTEgMCUsICMxZjI5MzcgMTAwJSk7XG59XG5cbi5tZXNzYWdlLXRleHQge1xuICBAYXBwbHkgdGV4dC1zbSBsZWFkaW5nLXJlbGF4ZWQ7XG59XG5cbi5tZXNzYWdlLXRpbWUge1xuICBAYXBwbHkgdGV4dC14cyB0ZXh0LWdyYXktNDAwIG10LTEgYmxvY2s7XG59XG5cbi5tZXNzYWdlLXN0YXR1cyB7XG4gIEBhcHBseSB0ZXh0LXhzIHRleHQtZ3JheS00MDAgbXQtMTtcbn1cblxuLm1lc3NhZ2Utc3RhdHVzLnJlYWQge1xuICBAYXBwbHkgdGV4dC1ibHVlLTQwMDtcbn1cblxuLm1lc3NhZ2Utc3RhdHVzLnBlbmRpbmcge1xuICBAYXBwbHkgdGV4dC15ZWxsb3ctNDAwO1xufVxuXG4ubWVzc2FnZS1zdGF0dXMuZXJyb3Ige1xuICBAYXBwbHkgdGV4dC1yZWQtNDAwO1xufVxuXG4vKiA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4gICBUWVBFUyBERSBNRVNTQUdFUyBTUMODwolDSUFVWFxuICAgPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PSAqL1xuXG4ubWVzc2FnZS1pbWFnZSB7XG4gIEBhcHBseSByb3VuZGVkLWxnIG1heC13LXhzIGN1cnNvci1wb2ludGVyO1xuICBib3gtc2hhZG93OiAwIDRweCAxNXB4IHJnYmEoMCwgMCwgMCwgMC4zKTtcbn1cblxuLm1lc3NhZ2UtZmlsZSB7XG4gIEBhcHBseSBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgcC0zIGJnLWdyYXktODAwIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1ncmF5LTYwMDtcbn1cblxuLmZpbGUtaWNvbiB7XG4gIEBhcHBseSB0ZXh0LWJsdWUtNDAwIHRleHQteGw7XG59XG5cbi5maWxlLWluZm8ge1xuICBAYXBwbHkgZmxleC0xO1xufVxuXG4uZmlsZS1uYW1lIHtcbiAgQGFwcGx5IHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC13aGl0ZTtcbn1cblxuLmZpbGUtc2l6ZSB7XG4gIEBhcHBseSB0ZXh0LXhzIHRleHQtZ3JheS00MDA7XG59XG5cbi52b2ljZS1tZXNzYWdlIHtcbiAgQGFwcGx5IGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyBwLTMgYmctZ3JheS04MDAgcm91bmRlZC1sZztcbn1cblxuLnZvaWNlLXBsYXktYnRuIHtcbiAgQGFwcGx5IHctOCBoLTggcm91bmRlZC1mdWxsIGJnLWJsdWUtNjAwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGN1cnNvci1wb2ludGVyO1xufVxuXG4udm9pY2UtZHVyYXRpb24ge1xuICBAYXBwbHkgdGV4dC1zbSB0ZXh0LWdyYXktNDAwO1xufVxuXG4vKiA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4gICBJTkRJQ0FURVVSUyBERSBGUkFQUEVcbiAgID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT0gKi9cblxuLnR5cGluZy1pbmRpY2F0b3Ige1xuICBAYXBwbHkgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHAtMyB0ZXh0LWdyYXktNDAwIHRleHQtc207XG59XG5cbi50eXBpbmctZG90cyB7XG4gIEBhcHBseSBmbGV4IHNwYWNlLXgtMTtcbn1cblxuLnR5cGluZy1kb3Qge1xuICBAYXBwbHkgdy0yIGgtMiBiZy1ncmF5LTQwMCByb3VuZGVkLWZ1bGw7XG4gIGFuaW1hdGlvbjogdHlwaW5nUHVsc2UgMS40cyBpbmZpbml0ZSBlYXNlLWluLW91dDtcbn1cblxuLnR5cGluZy1kb3Q6bnRoLWNoaWxkKDEpIHtcbiAgYW5pbWF0aW9uLWRlbGF5OiAtMC4zMnM7XG59XG5cbi50eXBpbmctZG90Om50aC1jaGlsZCgyKSB7XG4gIGFuaW1hdGlvbi1kZWxheTogLTAuMTZzO1xufVxuXG5Aa2V5ZnJhbWVzIHR5cGluZ1B1bHNlIHtcbiAgMCUsIDgwJSwgMTAwJSB7XG4gICAgdHJhbnNmb3JtOiBzY2FsZSgwLjgpO1xuICAgIG9wYWNpdHk6IDAuNTtcbiAgfVxuICA0MCUge1xuICAgIHRyYW5zZm9ybTogc2NhbGUoMSk7XG4gICAgb3BhY2l0eTogMTtcbiAgfVxufVxuXG4vKiA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4gICBaT05FIERFIFNBSVNJRVxuICAgPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PSAqL1xuXG4ubWVzc2FnZS1pbnB1dC1jb250YWluZXIge1xuICBAYXBwbHkgcC00IGJvcmRlci10IGJvcmRlci1ncmF5LTcwMCBiZy1ncmF5LTgwMDtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzFmMjkzNyAwJSwgIzExMTgyNyAxMDAlKTtcbn1cblxuLnJlcGx5LXByZXZpZXcge1xuICBAYXBwbHkgbWItMyBwLTIgYmctZ3JheS03MDAgcm91bmRlZC1sZyBib3JkZXItbC00IGJvcmRlci1ibHVlLTUwMDtcbn1cblxuLnJlcGx5LWhlYWRlciB7XG4gIEBhcHBseSBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW47XG59XG5cbi5yZXBseS10ZXh0IHtcbiAgQGFwcGx5IHRleHQtc20gdGV4dC1ncmF5LTMwMCB0cnVuY2F0ZTtcbn1cblxuLmlucHV0LXdyYXBwZXIge1xuICBAYXBwbHkgZmxleCBpdGVtcy1lbmQgc3BhY2UteC0yO1xufVxuXG4ubWVzc2FnZS1pbnB1dCB7XG4gIEBhcHBseSBmbGV4LTEgYmctZ3JheS03MDAgYm9yZGVyIGJvcmRlci1ncmF5LTYwMCByb3VuZGVkLTJ4bCBweC00IHB5LTIgdGV4dC13aGl0ZSBwbGFjZWhvbGRlci1ncmF5LTQwMCByZXNpemUtbm9uZTtcbiAgbWluLWhlaWdodDogNDBweDtcbiAgbWF4LWhlaWdodDogMTIwcHg7XG4gIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XG59XG5cbi5tZXNzYWdlLWlucHV0OmZvY3VzIHtcbiAgQGFwcGx5IG91dGxpbmUtbm9uZSBib3JkZXItYmx1ZS01MDA7XG4gIGJveC1zaGFkb3c6IDAgMCAxMHB4IHJnYmEoNTksIDEzMCwgMjQ2LCAwLjMpO1xufVxuXG4uaW5wdXQtYWN0aW9ucyB7XG4gIEBhcHBseSBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTE7XG59XG5cbi5pbnB1dC1idG4ge1xuICBAYXBwbHkgcC0yIHJvdW5kZWQtZnVsbCBiZy1ncmF5LTcwMCBob3ZlcjpiZy1ncmF5LTYwMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDA7XG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoNTksIDEzMCwgMjQ2LCAwLjMpO1xufVxuXG4uaW5wdXQtYnRuOmhvdmVyIHtcbiAgYm94LXNoYWRvdzogMCAwIDEwcHggcmdiYSg1OSwgMTMwLCAyNDYsIDAuNCk7XG59XG5cbi5pbnB1dC1idG4gaSB7XG4gIEBhcHBseSB0ZXh0LWJsdWUtNDAwO1xufVxuXG4uc2VuZC1idG4ge1xuICBAYXBwbHkgcC0yIHJvdW5kZWQtZnVsbCBiZy1ibHVlLTYwMCBob3ZlcjpiZy1ibHVlLTcwMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDA7XG4gIGJveC1zaGFkb3c6IDAgMnB4IDEwcHggcmdiYSg1OSwgMTMwLCAyNDYsIDAuMyk7XG59XG5cbi5zZW5kLWJ0bjpob3ZlciB7XG4gIGJveC1zaGFkb3c6IDAgNHB4IDE1cHggcmdiYSg1OSwgMTMwLCAyNDYsIDAuNSk7XG59XG5cbi5zZW5kLWJ0bjpkaXNhYmxlZCB7XG4gIEBhcHBseSBiZy1ncmF5LTYwMCBjdXJzb3Itbm90LWFsbG93ZWQ7XG4gIGJveC1zaGFkb3c6IG5vbmU7XG59XG5cbi8qID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbiAgIEVOUkVHSVNUUkVNRU5UIFZPQ0FMXG4gICA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09ICovXG5cbi5yZWNvcmRpbmctaW5kaWNhdG9yIHtcbiAgQGFwcGx5IGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBwLTMgYmctcmVkLTYwMCByb3VuZGVkLWxnO1xuICBhbmltYXRpb246IHJlY29yZGluZ1B1bHNlIDFzIGluZmluaXRlO1xufVxuXG5Aa2V5ZnJhbWVzIHJlY29yZGluZ1B1bHNlIHtcbiAgMCUsIDEwMCUge1xuICAgIG9wYWNpdHk6IDE7XG4gIH1cbiAgNTAlIHtcbiAgICBvcGFjaXR5OiAwLjc7XG4gIH1cbn1cblxuLnJlY29yZGluZy10aW1lIHtcbiAgQGFwcGx5IHRleHQtd2hpdGUgZm9udC1tb25vO1xufVxuXG4vKiA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4gICBSRVNQT05TSVZFXG4gICA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09ICovXG5cbkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAubWVzc2FnZSB7XG4gICAgQGFwcGx5IG1heC13LXhzO1xuICB9XG4gIFxuICAuY2hhdC1oZWFkZXIge1xuICAgIEBhcHBseSBweC0zIHB5LTI7XG4gIH1cbiAgXG4gIC5tZXNzYWdlcy1jb250YWluZXIge1xuICAgIEBhcHBseSBweC0zO1xuICB9XG4gIFxuICAubWVzc2FnZS1pbnB1dC1jb250YWluZXIge1xuICAgIEBhcHBseSBweC0zO1xuICB9XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */"]
    });
  }
}

/***/ }),

/***/ 4682:
/*!*******************************************************************************!*\
  !*** ./src/app/views/front/messages/messages-list/messages-list.component.ts ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MessagesListComponent: () => (/* binding */ MessagesListComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 7580);

class MessagesListComponent {
  static {
    this.ɵfac = function MessagesListComponent_Factory(t) {
      return new (t || MessagesListComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineComponent"]({
      type: MessagesListComponent,
      selectors: [["app-messages-list"]],
      decls: 0,
      vars: 0,
      template: function MessagesListComponent_Template(rf, ctx) {},
      styles: ["/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJtZXNzYWdlcy1saXN0LmNvbXBvbmVudC5jc3MifQ== */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvbWVzc2FnZXMvbWVzc2FnZXMtbGlzdC9tZXNzYWdlcy1saXN0LmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUNBLDRLQUE0SyIsInNvdXJjZVJvb3QiOiIifQ== */"]
    });
  }
}

/***/ }),

/***/ 7031:
/*!*****************************************************************!*\
  !*** ./src/app/views/front/messages/messages-routing.module.ts ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MessagesRoutingModule: () => (/* binding */ MessagesRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _message_chat_message_chat_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./message-chat/message-chat.component */ 8096);
/* harmony import */ var _message_layout_message_layout_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./message-layout/message-layout.component */ 8076);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 7580);





const routes = [{
  path: '',
  component: _message_layout_message_layout_component__WEBPACK_IMPORTED_MODULE_1__.MessageLayoutComponent,
  children: [
  // Route par défaut - affiche le layout sans conversation sélectionnée
  {
    path: '',
    component: _message_chat_message_chat_component__WEBPACK_IMPORTED_MODULE_0__.MessageChatComponent,
    data: {
      title: 'Messages'
    }
  },
  // Route pour une conversation spécifique
  {
    path: ':conversationId',
    component: _message_chat_message_chat_component__WEBPACK_IMPORTED_MODULE_0__.MessageChatComponent,
    data: {
      title: 'Chat'
    }
  }]
}];
class MessagesRoutingModule {
  static {
    this.ɵfac = function MessagesRoutingModule_Factory(t) {
      return new (t || MessagesRoutingModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineNgModule"]({
      type: MessagesRoutingModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineInjector"]({
      imports: [_angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵsetNgModuleScope"](MessagesRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterModule]
  });
})();

/***/ }),

/***/ 8534:
/*!*********************************************************!*\
  !*** ./src/app/views/front/messages/messages.module.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MessagesModule: () => (/* binding */ MessagesModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _messages_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./messages-routing.module */ 7031);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var apollo_angular__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! apollo-angular */ 7797);
/* harmony import */ var _message_chat_message_chat_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./message-chat/message-chat.component */ 8096);
/* harmony import */ var _messages_list_messages_list_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./messages-list/messages-list.component */ 4682);
/* harmony import */ var _user_list_user_list_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./user-list/user-list.component */ 3122);
/* harmony import */ var _message_layout_message_layout_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./message-layout/message-layout.component */ 8076);
/* harmony import */ var _components_system_status_system_status_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../components/system-status/system-status.component */ 145);
/* harmony import */ var src_app_services_user_status_service__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! src/app/services/user-status.service */ 9722);
/* harmony import */ var src_app_services_message_service__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! src/app/services/message.service */ 4537);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/core */ 7580);













class MessagesModule {
  static {
    this.ɵfac = function MessagesModule_Factory(t) {
      return new (t || MessagesModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdefineNgModule"]({
      type: MessagesModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdefineInjector"]({
      providers: [src_app_services_user_status_service__WEBPACK_IMPORTED_MODULE_6__.UserStatusService, src_app_services_message_service__WEBPACK_IMPORTED_MODULE_7__.MessageService],
      imports: [_angular_common__WEBPACK_IMPORTED_MODULE_9__.CommonModule, _messages_routing_module__WEBPACK_IMPORTED_MODULE_0__.MessagesRoutingModule, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.FormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.ReactiveFormsModule, apollo_angular__WEBPACK_IMPORTED_MODULE_11__.ApolloModule, _angular_router__WEBPACK_IMPORTED_MODULE_12__.RouterModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵsetNgModuleScope"](MessagesModule, {
    declarations: [_message_chat_message_chat_component__WEBPACK_IMPORTED_MODULE_1__.MessageChatComponent, _messages_list_messages_list_component__WEBPACK_IMPORTED_MODULE_2__.MessagesListComponent, _user_list_user_list_component__WEBPACK_IMPORTED_MODULE_3__.UserListComponent, _message_layout_message_layout_component__WEBPACK_IMPORTED_MODULE_4__.MessageLayoutComponent, _components_system_status_system_status_component__WEBPACK_IMPORTED_MODULE_5__.SystemStatusComponent],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_9__.CommonModule, _messages_routing_module__WEBPACK_IMPORTED_MODULE_0__.MessagesRoutingModule, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.FormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.ReactiveFormsModule, apollo_angular__WEBPACK_IMPORTED_MODULE_11__.ApolloModule, _angular_router__WEBPACK_IMPORTED_MODULE_12__.RouterModule]
  });
})();

/***/ }),

/***/ 3122:
/*!***********************************************************************!*\
  !*** ./src/app/views/front/messages/user-list/user-list.component.ts ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   UserListComponent: () => (/* binding */ UserListComponent)
/* harmony export */ });
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rxjs */ 2510);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! rxjs */ 9240);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! rxjs/operators */ 271);
/* harmony import */ var src_app_models_message_model__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/models/message.model */ 5293);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var src_app_services_message_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/services/message.service */ 4537);
/* harmony import */ var src_app_services_call_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/services/call.service */ 9454);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var src_app_services_authuser_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/services/authuser.service */ 9271);
/* harmony import */ var src_app_services_toast_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/app/services/toast.service */ 8397);
/* harmony import */ var src_app_services_logger_service__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/app/services/logger.service */ 4798);
/* harmony import */ var _app_services_theme_service__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @app/services/theme.service */ 487);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/common */ 316);














function UserListComponent_div_57_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "div", 44)(1, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](3, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtextInterpolate2"]("Affichage de ", ctx_r0.users.length, " sur ", ctx_r0.totalUsers, " utilisateurs");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtextInterpolate2"]("Page ", ctx_r0.currentPage, " sur ", ctx_r0.totalPages, "");
  }
}
function UserListComponent_div_59_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "div", 45);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](1, "div", 46);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](2, "div", 47);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](3, "Chargement des utilisateurs...");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
  }
}
function UserListComponent_div_60_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "div", 48)(1, "div", 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](2, "i", 50);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](3, "h3", 51);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](4, "Aucun utilisateur trouv\u00E9");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](5, "p", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](6, " Essayez un autre terme de recherche ou effacez les filtres ");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
  }
}
function UserListComponent_ul_61_li_1_span_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](0, "span", 66);
  }
}
function UserListComponent_ul_61_li_1_button_11_Template(rf, ctx) {
  if (rf & 1) {
    const _r13 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "button", 67);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("click", function UserListComponent_ul_61_li_1_button_11_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵrestoreView"](_r13);
      const user_r7 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"]().$implicit;
      const ctx_r11 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵresetView"](ctx_r11.startAudioCall(user_r7.id || user_r7._id));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](1, "i", 68);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
  }
}
function UserListComponent_ul_61_li_1_button_12_Template(rf, ctx) {
  if (rf & 1) {
    const _r16 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "button", 69);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("click", function UserListComponent_ul_61_li_1_button_12_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵrestoreView"](_r16);
      const user_r7 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"]().$implicit;
      const ctx_r14 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵresetView"](ctx_r14.startVideoCall(user_r7.id || user_r7._id));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](1, "i", 70);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
  }
}
function UserListComponent_ul_61_li_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r18 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "li", 55)(1, "div", 56);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("click", function UserListComponent_ul_61_li_1_Template_div_click_1_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵrestoreView"](_r18);
      const user_r7 = restoredCtx.$implicit;
      const ctx_r17 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵresetView"](ctx_r17.startConversation(user_r7.id || user_r7._id));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](2, "div", 57);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](3, "img", 58);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](4, UserListComponent_ul_61_li_1_span_4_Template, 1, 0, "span", 59);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](5, "div", 60)(6, "h3", 61);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](8, "p", 62);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](10, "div", 63);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](11, UserListComponent_ul_61_li_1_button_11_Template, 2, 0, "button", 64);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](12, UserListComponent_ul_61_li_1_button_12_Template, 2, 0, "button", 65);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const user_r7 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("src", user_r7.image || "assets/images/default-avatar.png", _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵsanitizeUrl"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", user_r7.isOnline);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtextInterpolate1"](" ", user_r7.username, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtextInterpolate"](user_r7.email);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", user_r7.isOnline);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", user_r7.isOnline);
  }
}
function UserListComponent_ul_61_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "ul", 53);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](1, UserListComponent_ul_61_li_1_Template, 13, 6, "li", 54);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngForOf", ctx_r3.users);
  }
}
function UserListComponent_div_62_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "div", 71)(1, "div", 72);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](2, "div", 73)(3, "div", 74)(4, "div", 75);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](5, "div", 47);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](6, " Chargement de plus d'utilisateurs... ");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
  }
}
function UserListComponent_div_63_Template(rf, ctx) {
  if (rf & 1) {
    const _r20 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "div", 76)(1, "button", 77);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("click", function UserListComponent_div_63_Template_button_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵrestoreView"](_r20);
      const ctx_r19 = _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵresetView"](ctx_r19.loadNextPage());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](2, "i", 78);
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](3, " Charger plus d'utilisateurs ");
    _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
  }
}
class UserListComponent {
  constructor(MessageService, callService, router, route, authService, toastService, logger, themeService) {
    this.MessageService = MessageService;
    this.callService = callService;
    this.router = router;
    this.route = route;
    this.authService = authService;
    this.toastService = toastService;
    this.logger = logger;
    this.themeService = themeService;
    this.users = [];
    this.loading = true;
    this.currentUserId = null;
    // Pagination
    this.currentPage = 1;
    this.pageSize = 10;
    this.totalUsers = 0;
    this.totalPages = 0;
    this.hasNextPage = false;
    this.hasPreviousPage = false;
    // Sorting and filtering
    this.sortBy = 'username';
    this.sortOrder = 'asc';
    this.filterForm = new _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormGroup({
      searchQuery: new _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormControl(''),
      isOnline: new _angular_forms__WEBPACK_IMPORTED_MODULE_8__.FormControl(null)
    });
    // Auto-refresh
    this.autoRefreshEnabled = true;
    this.autoRefreshInterval = 30000; // 30 seconds
    this.loadingMore = false;
    this.subscriptions = new rxjs__WEBPACK_IMPORTED_MODULE_9__.Subscription();
    this.isDarkMode$ = this.themeService.currentTheme$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_10__.map)(theme => theme.name === 'dark'));
  }
  ngOnInit() {
    this.currentUserId = this.authService.getCurrentUserId();
    this.setupFilterListeners();
    this.setupAutoRefresh();
    this.loadUsers();
  }
  setupFilterListeners() {
    // Subscribe to search query changes
    const searchSub = this.filterForm.get('searchQuery').valueChanges.subscribe(() => {
      this.resetPagination();
      this.loadUsers();
    });
    this.subscriptions.add(searchSub);
    // Subscribe to online status filter changes
    const onlineSub = this.filterForm.get('isOnline').valueChanges.subscribe(() => {
      this.resetPagination();
      this.loadUsers();
    });
    this.subscriptions.add(onlineSub);
  }
  setupAutoRefresh() {
    if (this.autoRefreshEnabled) {
      this.autoRefreshSubscription = (0,rxjs__WEBPACK_IMPORTED_MODULE_11__.interval)(this.autoRefreshInterval).subscribe(() => {
        if (!this.loading && !this.filterForm.get('searchQuery')?.value) {
          this.loadUsers(true);
        }
      });
    }
  }
  toggleAutoRefresh() {
    this.autoRefreshEnabled = !this.autoRefreshEnabled;
    if (this.autoRefreshEnabled) {
      this.setupAutoRefresh();
    } else if (this.autoRefreshSubscription) {
      this.autoRefreshSubscription.unsubscribe();
      this.autoRefreshSubscription = undefined;
    }
  }
  resetPagination() {
    this.currentPage = 1;
  }
  // Get searchQuery from the form
  get searchQuery() {
    return this.filterForm.get('searchQuery')?.value || '';
  }
  // Set searchQuery in the form
  set searchQuery(value) {
    this.filterForm.get('searchQuery')?.setValue(value);
  }
  // Helper function for template type casting
  $any(item) {
    return item;
  }
  loadUsers(forceRefresh = false) {
    if (this.loadingMore) return;
    this.loading = true;
    const searchQuery = this.filterForm.get('searchQuery')?.value || '';
    const isOnline = this.filterForm.get('isOnline')?.value;
    const sub = this.MessageService.getAllUsers(forceRefresh, searchQuery, this.currentPage, this.pageSize, this.sortBy, this.sortOrder, isOnline === true ? true : undefined).subscribe({
      next: users => {
        if (!Array.isArray(users)) {
          this.users = [];
          this.loading = false;
          this.loadingMore = false;
          this.toastService.showError('Failed to load users: Invalid data');
          return;
        }
        // If first page, replace users array; otherwise append
        if (this.currentPage === 1) {
          // Filter out current user
          this.users = users.filter(user => {
            if (!user) return false;
            const userId = user.id || user._id;
            return userId !== this.currentUserId;
          });
        } else {
          // Append new users to existing array, avoiding duplicates and filtering out current user
          const newUsers = users.filter(newUser => {
            if (!newUser) return false;
            const userId = newUser.id || newUser._id;
            return userId !== this.currentUserId && !this.users.some(existingUser => (existingUser.id || existingUser._id) === userId);
          });
          this.users = [...this.users, ...newUsers];
        }
        // Update pagination metadata from service
        const pagination = this.MessageService.currentUserPagination;
        this.totalUsers = pagination.totalCount;
        this.totalPages = pagination.totalPages;
        this.hasNextPage = pagination.hasNextPage;
        this.hasPreviousPage = pagination.hasPreviousPage;
        this.loading = false;
        this.loadingMore = false;
      },
      error: error => {
        this.loading = false;
        this.loadingMore = false;
        this.toastService.showError(`Failed to load users: ${error.message || 'Unknown error'}`);
        if (this.currentPage === 1) {
          this.users = [];
        }
      },
      complete: () => {
        this.loading = false;
        this.loadingMore = false;
      }
    });
    this.subscriptions.add(sub);
  }
  startConversation(userId) {
    if (!userId) {
      this.toastService.showError('Cannot start conversation with undefined user');
      return;
    }
    this.toastService.showInfo('Creating conversation...');
    this.MessageService.createConversation(userId).subscribe({
      next: conversation => {
        if (!conversation || !conversation.id) {
          this.toastService.showError('Failed to create conversation: Invalid response');
          return;
        }
        this.router.navigate(['/messages/conversations/chat', conversation.id]).then(success => {
          if (!success) {
            this.toastService.showError('Failed to open conversation');
          }
        });
      },
      error: error => {
        this.toastService.showError(`Failed to create conversation: ${error.message || 'Unknown error'}`);
      }
    });
  }
  startAudioCall(userId) {
    if (!userId) return;
    this.callService.initiateCall(userId, src_app_models_message_model__WEBPACK_IMPORTED_MODULE_0__.CallType.AUDIO).subscribe({
      next: call => {
        this.toastService.showSuccess('Audio call initiated');
      },
      error: error => {
        this.toastService.showError('Failed to initiate audio call');
      }
    });
  }
  startVideoCall(userId) {
    if (!userId) return;
    this.callService.initiateCall(userId, src_app_models_message_model__WEBPACK_IMPORTED_MODULE_0__.CallType.VIDEO).subscribe({
      next: call => {
        this.toastService.showSuccess('Video call initiated');
      },
      error: error => {
        this.toastService.showError('Failed to initiate video call');
      }
    });
  }
  loadNextPage() {
    if (this.hasNextPage && !this.loading) {
      this.loadingMore = true;
      this.currentPage++;
      this.loadUsers();
    }
  }
  loadPreviousPage() {
    if (this.hasPreviousPage && !this.loading) {
      this.loadingMore = true;
      this.currentPage--;
      this.loadUsers();
    }
  }
  refreshUsers() {
    this.resetPagination();
    this.loadUsers(true);
  }
  clearFilters() {
    this.filterForm.reset({
      searchQuery: '',
      isOnline: null
    });
    this.resetPagination();
    this.loadUsers(true);
  }
  changeSortOrder(field) {
    if (this.sortBy === field) {
      // Toggle sort order if clicking the same field
      this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
    } else {
      // Set new sort field with default ascending order
      this.sortBy = field;
      this.sortOrder = 'asc';
    }
    this.resetPagination();
    this.loadUsers(true);
  }
  /**
   * Navigue vers la liste des conversations
   */
  goBackToConversations() {
    this.router.navigate(['/messages/conversations']);
  }
  ngOnDestroy() {
    this.subscriptions.unsubscribe();
    if (this.autoRefreshSubscription) {
      this.autoRefreshSubscription.unsubscribe();
    }
  }
  static {
    this.ɵfac = function UserListComponent_Factory(t) {
      return new (t || UserListComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](src_app_services_message_service__WEBPACK_IMPORTED_MODULE_1__.MessageService), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](src_app_services_call_service__WEBPACK_IMPORTED_MODULE_2__.CallService), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_12__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_12__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](src_app_services_authuser_service__WEBPACK_IMPORTED_MODULE_3__.AuthuserService), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](src_app_services_toast_service__WEBPACK_IMPORTED_MODULE_4__.ToastService), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](src_app_services_logger_service__WEBPACK_IMPORTED_MODULE_5__.LoggerService), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdirectiveInject"](_app_services_theme_service__WEBPACK_IMPORTED_MODULE_6__.ThemeService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdefineComponent"]({
      type: UserListComponent,
      selectors: [["app-user-list"]],
      decls: 64,
      vars: 18,
      consts: [[1, "flex", "flex-col", "h-full", "futuristic-users-container"], [1, "absolute", "inset-0", "overflow-hidden", "pointer-events-none"], [1, "absolute", "top-[15%]", "left-[10%]", "w-64", "h-64", "rounded-full", "bg-gradient-to-br", "from-[#4f5fad]/5", "to-transparent", "dark:from-[#00f7ff]/10", "dark:to-transparent", "blur-3xl"], [1, "absolute", "bottom-[20%]", "right-[10%]", "w-80", "h-80", "rounded-full", "bg-gradient-to-tl", "from-[#4f5fad]/5", "to-transparent", "dark:from-[#00f7ff]/10", "dark:to-transparent", "blur-3xl"], [1, "absolute", "top-[40%]", "right-[30%]", "w-40", "h-40", "rounded-full", "bg-gradient-to-br", "from-transparent", "to-transparent", "dark:from-[#00f7ff]/5", "dark:to-transparent", "blur-3xl", "opacity-0", "dark:opacity-100"], [1, "absolute", "bottom-[60%]", "left-[25%]", "w-32", "h-32", "rounded-full", "bg-gradient-to-tl", "from-transparent", "to-transparent", "dark:from-[#00f7ff]/5", "dark:to-transparent", "blur-3xl", "opacity-0", "dark:opacity-100"], [1, "absolute", "inset-0", "opacity-5", "dark:opacity-0"], [1, "h-full", "grid", "grid-cols-12"], [1, "border-r", "border-[#4f5fad]"], [1, "absolute", "inset-0", "opacity-0", "dark:opacity-100", "overflow-hidden"], [1, "h-px", "w-full", "bg-[#00f7ff]/20", "absolute", "animate-scan"], [1, "futuristic-users-header"], [1, "flex", "justify-between", "items-center", "mb-4"], [1, "futuristic-title"], [1, "flex", "space-x-2"], ["title", "Rafra\u00EEchir la liste", 1, "futuristic-action-button", 3, "click"], [1, "fas", "fa-sync-alt"], [1, "futuristic-action-button", 3, "click"], [1, "fas", "fa-arrow-left"], [1, "space-y-3"], [1, "relative"], ["type", "text", "placeholder", "Rechercher des utilisateurs...", 1, "w-full", "pl-10", "pr-4", "py-2", "rounded-lg", "futuristic-input-field", 3, "ngModel", "ngModelChange"], [1, "fas", "fa-search", "absolute", "left-3", "top-3", "text-[#6d6870]", "dark:text-[#a0a0a0]"], [1, "flex", "items-center", "justify-between"], [1, "flex", "items-center", "space-x-4"], [1, "flex", "items-center", "space-x-2"], [1, "futuristic-checkbox-container"], ["type", "checkbox", "id", "onlineFilter", 1, "futuristic-checkbox", 3, "checked", "change"], [1, "futuristic-checkbox-checkmark"], ["for", "onlineFilter", 1, "futuristic-label"], [1, "futuristic-label"], [1, "futuristic-select", 3, "change"], ["value", "username", 3, "selected"], ["value", "email", 3, "selected"], ["value", "lastActive", 3, "selected"], [1, "futuristic-sort-button", 3, "title", "click"], [1, "futuristic-clear-button", 3, "click"], ["class", "flex justify-between items-center futuristic-pagination-info", 4, "ngIf"], [1, "futuristic-users-list", 3, "scroll"], ["class", "futuristic-loading-container", 4, "ngIf"], ["class", "futuristic-empty-state", 4, "ngIf"], ["class", "futuristic-users-grid", 4, "ngIf"], ["class", "futuristic-loading-more", 4, "ngIf"], ["class", "futuristic-load-more-container", 4, "ngIf"], [1, "flex", "justify-between", "items-center", "futuristic-pagination-info"], [1, "futuristic-loading-container"], [1, "futuristic-loading-circle"], [1, "futuristic-loading-text"], [1, "futuristic-empty-state"], [1, "futuristic-empty-icon"], [1, "fas", "fa-users"], [1, "futuristic-empty-title"], [1, "futuristic-empty-text"], [1, "futuristic-users-grid"], ["class", "futuristic-user-card", 4, "ngFor", "ngForOf"], [1, "futuristic-user-card"], [1, "futuristic-user-content", 3, "click"], [1, "futuristic-avatar"], ["alt", "User avatar", 3, "src"], ["class", "futuristic-online-indicator", 4, "ngIf"], [1, "futuristic-user-info"], [1, "futuristic-username"], [1, "futuristic-user-email"], [1, "futuristic-call-buttons"], ["class", "futuristic-call-button", "title", "Appel audio", 3, "click", 4, "ngIf"], ["class", "futuristic-call-button", "title", "Appel vid\u00E9o", 3, "click", 4, "ngIf"], [1, "futuristic-online-indicator"], ["title", "Appel audio", 1, "futuristic-call-button", 3, "click"], [1, "fas", "fa-phone"], ["title", "Appel vid\u00E9o", 1, "futuristic-call-button", 3, "click"], [1, "fas", "fa-video"], [1, "futuristic-loading-more"], [1, "futuristic-loading-dots"], [1, "futuristic-loading-dot", 2, "animation-delay", "0s"], [1, "futuristic-loading-dot", 2, "animation-delay", "0.2s"], [1, "futuristic-loading-dot", 2, "animation-delay", "0.4s"], [1, "futuristic-load-more-container"], [1, "futuristic-load-more-button", 3, "click"], [1, "fas", "fa-chevron-down", "mr-2"]],
      template: function UserListComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](0, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵpipe"](1, "async");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](2, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](3, "div", 2)(4, "div", 3)(5, "div", 4)(6, "div", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](7, "div", 6)(8, "div", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](9, "div", 8)(10, "div", 8)(11, "div", 8)(12, "div", 8)(13, "div", 8)(14, "div", 8)(15, "div", 8)(16, "div", 8)(17, "div", 8)(18, "div", 8)(19, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](20, "div", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](21, "div", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](22, "div", 11)(23, "div", 12)(24, "h1", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](25, "Nouvelle Conversation");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](26, "div", 14)(27, "button", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("click", function UserListComponent_Template_button_click_27_listener() {
            return ctx.refreshUsers();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](28, "i", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](29, "button", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("click", function UserListComponent_Template_button_click_29_listener() {
            return ctx.goBackToConversations();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](30, "i", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](31, "div", 19)(32, "div", 20)(33, "input", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("ngModelChange", function UserListComponent_Template_input_ngModelChange_33_listener($event) {
            return ctx.searchQuery = $event;
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](34, "i", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](35, "div", 23)(36, "div", 24)(37, "div", 25)(38, "label", 26)(39, "input", 27);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("change", function UserListComponent_Template_input_change_39_listener($event) {
            let tmp_b_0;
            return (tmp_b_0 = ctx.filterForm.get("isOnline")) == null ? null : tmp_b_0.setValue($event.target.checked ? true : null);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](40, "span", 28);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](41, "label", 29);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](42, "En ligne uniquement");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](43, "div", 25)(44, "span", 30);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](45, "Trier par:");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](46, "select", 31);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("change", function UserListComponent_Template_select_change_46_listener($event) {
            return ctx.changeSortOrder($event.target.value);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](47, "option", 32);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](48, " Nom ");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](49, "option", 33);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](50, " Email ");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](51, "option", 34);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](52, " Derni\u00E8re activit\u00E9 ");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](53, "button", 35);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("click", function UserListComponent_Template_button_click_53_listener() {
            ctx.sortOrder = ctx.sortOrder === "asc" ? "desc" : "asc";
            return ctx.loadUsers(true);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelement"](54, "i");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](55, "button", 36);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("click", function UserListComponent_Template_button_click_55_listener() {
            return ctx.clearFilters();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtext"](56, " Effacer les filtres ");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](57, UserListComponent_div_57_Template, 5, 4, "div", 37);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementStart"](58, "div", 38);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵlistener"]("scroll", function UserListComponent_Template_div_scroll_58_listener($event) {
            return $event.target.scrollTop + $event.target.clientHeight >= $event.target.scrollHeight - 200 && ctx.loadNextPage();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](59, UserListComponent_div_59_Template, 4, 0, "div", 39);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](60, UserListComponent_div_60_Template, 7, 0, "div", 40);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](61, UserListComponent_ul_61_Template, 2, 1, "ul", 41);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](62, UserListComponent_div_62_Template, 7, 0, "div", 42);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵtemplate"](63, UserListComponent_div_63_Template, 4, 0, "div", 43);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          let tmp_2_0;
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵclassProp"]("dark", _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵpipeBind1"](1, 16, ctx.isDarkMode$));
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](33);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngModel", ctx.searchQuery);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("checked", ((tmp_2_0 = ctx.filterForm.get("isOnline")) == null ? null : tmp_2_0.value) === true);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](8);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("selected", ctx.sortBy === "username");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("selected", ctx.sortBy === "email");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("selected", ctx.sortBy === "lastActive");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("title", ctx.sortOrder === "asc" ? "Ordre croissant" : "Ordre d\u00E9croissant");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵclassMap"](ctx.sortOrder === "asc" ? "fas fa-sort-up" : "fas fa-sort-down");
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", ctx.totalUsers > 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", ctx.loading && !ctx.users.length);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", !ctx.loading && ctx.users.length === 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", ctx.users.length > 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", ctx.loading && ctx.users.length > 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵproperty"]("ngIf", ctx.hasNextPage && !ctx.loading);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_13__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_13__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.NgSelectOption, _angular_forms__WEBPACK_IMPORTED_MODULE_8__["ɵNgSelectMultipleOption"], _angular_forms__WEBPACK_IMPORTED_MODULE_8__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.NgModel, _angular_common__WEBPACK_IMPORTED_MODULE_13__.AsyncPipe],
      styles: ["/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJ1c2VyLWxpc3QuY29tcG9uZW50LmNzcyJ9 */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvbWVzc2FnZXMvdXNlci1saXN0L3VzZXItbGlzdC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSxvS0FBb0siLCJzb3VyY2VSb290IjoiIn0= */"]
    });
  }
}

/***/ })

}]);
//# sourceMappingURL=src_app_views_front_messages_messages_module_ts.js.map