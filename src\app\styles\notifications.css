/* ============================================================================
   NOTIFICATIONS - STYLES POUR LE SYSTÈME DE NOTIFICATIONS
   ============================================================================ */

.notification-container {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  max-width: 24rem;
  pointer-events: none;
}

.notification {
  background: var(--color-surface, #1f2937);
  border: 1px solid var(--color-border, #374151);
  border-radius: 0.5rem;
  padding: 1rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  pointer-events: auto;
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.notification.show {
  transform: translateX(0);
  opacity: 1;
}

.notification.hide {
  transform: translateX(100%);
  opacity: 0;
}

/* Types de notifications */
.notification.success {
  border-left: 4px solid #10b981;
  background: linear-gradient(135deg, #065f46 0%, #047857 100%);
}

.notification.error {
  border-left: 4px solid #ef4444;
  background: linear-gradient(135deg, #7f1d1d 0%, #991b1b 100%);
}

.notification.warning {
  border-left: 4px solid #f59e0b;
  background: linear-gradient(135deg, #78350f 0%, #92400e 100%);
}

.notification.info {
  border-left: 4px solid #3b82f6;
  background: linear-gradient(135deg, #1e3a8a 0%, #1d4ed8 100%);
}

/* Contenu de la notification */
.notification-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.notification-icon {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  color: white;
  margin-right: 0.75rem;
}

.notification.success .notification-icon {
  background: #10b981;
}

.notification.error .notification-icon {
  background: #ef4444;
}

.notification.warning .notification-icon {
  background: #f59e0b;
}

.notification.info .notification-icon {
  background: #3b82f6;
}

.notification-title {
  font-weight: 600;
  color: var(--color-text, #ffffff);
  font-size: 0.875rem;
  flex: 1;
}

.notification-close {
  background: none;
  border: none;
  color: var(--color-text-secondary, #9ca3af);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

.notification-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--color-text, #ffffff);
}

.notification-content {
  color: var(--color-text-secondary, #9ca3af);
  font-size: 0.875rem;
  line-height: 1.4;
}

.notification-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.75rem;
}

.notification-action {
  padding: 0.375rem 0.75rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.notification-action.primary {
  background: var(--color-primary, #3b82f6);
  color: white;
}

.notification-action.primary:hover {
  background: var(--color-secondary, #2563eb);
}

.notification-action.secondary {
  background: transparent;
  color: var(--color-text-secondary, #9ca3af);
  border: 1px solid var(--color-border, #374151);
}

.notification-action.secondary:hover {
  background: rgba(255, 255, 255, 0.05);
  color: var(--color-text, #ffffff);
}

/* Barre de progression */
.notification-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: var(--color-primary, #3b82f6);
  transition: width linear;
  border-radius: 0 0 0.5rem 0.5rem;
}

.notification.success .notification-progress {
  background: #10b981;
}

.notification.error .notification-progress {
  background: #ef4444;
}

.notification.warning .notification-progress {
  background: #f59e0b;
}

/* Animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

.notification.shake {
  animation: shake 0.5s ease-in-out;
}

/* Notifications en liste */
.notification-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  max-height: 70vh;
  overflow-y: auto;
  padding: 1rem;
}

.notification-list-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--color-surface, #1f2937);
  border: 1px solid var(--color-border, #374151);
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.notification-list-item:hover {
  background: var(--color-primary, #3b82f6);
  transform: translateX(2px);
}

.notification-list-item.unread {
  border-left: 4px solid var(--color-primary, #3b82f6);
  background: linear-gradient(135deg, #1e3a8a 0%, #1d4ed8 100%);
}

.notification-list-item.unread::before {
  content: '';
  position: absolute;
  top: 50%;
  right: 1rem;
  transform: translateY(-50%);
  width: 0.5rem;
  height: 0.5rem;
  background: var(--color-primary, #3b82f6);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

/* Responsive */
@media (max-width: 768px) {
  .notification-container {
    top: 0.5rem;
    right: 0.5rem;
    left: 0.5rem;
    max-width: none;
  }
  
  .notification {
    padding: 0.75rem;
  }
  
  .notification-title {
    font-size: 0.8rem;
  }
  
  .notification-content {
    font-size: 0.8rem;
  }
}

/* Dark mode adjustments */
.dark .notification {
  background: #1f2937;
  border-color: #374151;
  color: #ffffff;
}

.dark .notification-close {
  color: #9ca3af;
}

.dark .notification-close:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
}
