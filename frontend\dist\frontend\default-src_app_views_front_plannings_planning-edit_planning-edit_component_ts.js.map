{"version": 3, "file": "default-src_app_views_front_plannings_planning-edit_planning-edit_component_ts.js", "mappings": ";;;;;;;;;;;;;;;;;;;;AAC+E;;;;;;;;;;ICW3EC,4DAAA,cAAgG;IAC9FA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAAM,MAAA,CAAAC,KAAA,MACF;;;;;IAwBUP,4DAAA,WAA8D;IAAAA,oDAAA,+BAAwB;IAAAA,0DAAA,EAAO;;;;;IAC7FA,4DAAA,WAA+D;IAAAA,oDAAA,wCAA4B;IAAAA,0DAAA,EAAO;;;;;IAHpGA,4DAAA,cAA0I;IACxIA,uDAAA,YAA8C;IAC9CA,wDAAA,IAAAU,4CAAA,mBAA6F;IAC7FV,wDAAA,IAAAW,4CAAA,mBAAkG;IACpGX,0DAAA,EAAM;;;;;;IAFGA,uDAAA,GAAqD;IAArDA,wDAAA,UAAAa,OAAA,GAAAC,MAAA,CAAAC,YAAA,CAAAC,GAAA,4BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAqD;IACrDjB,uDAAA,GAAsD;IAAtDA,wDAAA,UAAAkB,OAAA,GAAAJ,MAAA,CAAAC,YAAA,CAAAC,GAAA,4BAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,cAAsD;;;;;IAwFjEjB,4DAAA,iBAA4E;IAC1EA,oDAAA,GACF;IAAAA,0DAAA,EAAS;;;;IAFmCA,wDAAA,UAAAmB,OAAA,CAAAC,GAAA,CAAkB;IAC5DpB,uDAAA,GACF;IADEA,gEAAA,MAAAmB,OAAA,CAAAE,QAAA,MACF;;;;;IAEFrB,4DAAA,cAAwJ;IACtJA,uDAAA,YAA8C;IAC9CA,oDAAA,2DACF;IAAAA,0DAAA,EAAM;;;;;IAuBNA,uDAAA,YAAmD;;;;;IACnDA,uDAAA,YAA6D;;;ADlJ/D,MAAOsB,qBAAqB;EAOhCC,YACUC,EAAe,EACfC,eAAgC,EAChCC,WAAwB,EACxBC,KAAqB,EACrBC,MAAc,EACdC,YAA0B;IAL1B,KAAAL,EAAE,GAAFA,EAAE;IACF,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IAXtB,KAAAC,MAAM,GAAG,IAAI,CAACJ,WAAW,CAACK,WAAW,EAAE;IAEvC,KAAAxB,KAAK,GAAW,EAAE;IAClB,KAAAyB,SAAS,GAAY,KAAK;EASvB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,UAAU,GAAG,IAAI,CAACP,KAAK,CAACQ,QAAQ,CAACC,QAAQ,CAACpB,GAAG,CAAC,IAAI,CAAE;IACzD,IAAI,CAACqB,QAAQ,EAAE;IACf,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAD,QAAQA,CAAA;IACN,IAAI,CAACtB,YAAY,GAAG,IAAI,CAACS,EAAE,CAACe,KAAK,CAAC;MAChCC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACzC,sDAAU,CAAC0C,QAAQ,EAAE1C,sDAAU,CAAC2C,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3DC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,SAAS,EAAE,CAAC,EAAE,EAAE7C,sDAAU,CAAC0C,QAAQ,CAAC;MACpCI,OAAO,EAAE,CAAC,EAAE,EAAE9C,sDAAU,CAAC0C,QAAQ,CAAC;MAClCK,IAAI,EAAE,CAAC,EAAE,CAAC;MACVC,YAAY,EAAE,CAAC,EAAE,EAAEhD,sDAAU,CAAC0C,QAAQ,CAAC,CAAE;KAC1C,CAAC;EACJ;;EAEAH,YAAYA,CAAA;IACV,IAAI,CAACb,eAAe,CAACuB,eAAe,CAAC,IAAI,CAACd,UAAU,CAAC,CAACe,SAAS,CAAC;MAC9DC,IAAI,EAAGC,QAAa,IAAI;QACtB,MAAMC,QAAQ,GAAGD,QAAQ,CAACC,QAAQ;QAElC,IAAI,CAACrC,YAAY,CAACsC,UAAU,CAAC;UAC3Bb,KAAK,EAAEY,QAAQ,CAACZ,KAAK;UACrBG,WAAW,EAAES,QAAQ,CAACT,WAAW;UACjCC,SAAS,EAAEQ,QAAQ,CAACR,SAAS;UAC7BC,OAAO,EAAEO,QAAQ,CAACP,OAAO;UACzBC,IAAI,EAAEM,QAAQ,CAACN;SAChB,CAAC;QAEF,MAAMQ,iBAAiB,GAAG,IAAI,CAACvC,YAAY,CAACC,GAAG,CAC7C,cAAc,CACF;QACdsC,iBAAiB,CAACC,KAAK,EAAE;QAEzBH,QAAQ,CAACL,YAAY,CAACS,OAAO,CAAEC,CAAM,IAAI;UACvCH,iBAAiB,CAACI,IAAI,CAAC,IAAI,CAAClC,EAAE,CAACmC,OAAO,CAACF,CAAC,CAACrC,GAAG,CAAC,CAAC;QAChD,CAAC,CAAC;MACJ,CAAC;MACDb,KAAK,EAAGqD,GAAG,IAAI;QACbC,OAAO,CAACtD,KAAK,CAAC,wCAAwC,EAAEqD,GAAG,CAAC;QAC5D,IAAIA,GAAG,CAACE,MAAM,KAAK,GAAG,EAAE;UACtB,IAAI,CAACjC,YAAY,CAACkC,SAAS,CACzB,sEAAsE,CACvE;SACF,MAAM,IAAIH,GAAG,CAACE,MAAM,KAAK,GAAG,EAAE;UAC7B,IAAI,CAACjC,YAAY,CAACkC,SAAS,CACzB,oDAAoD,CACrD;SACF,MAAM;UACL,MAAMC,YAAY,GAChBJ,GAAG,CAACrD,KAAK,EAAE0D,OAAO,IAAI,uCAAuC;UAC/D,IAAI,CAACpC,YAAY,CAACkC,SAAS,CAACC,YAAY,CAAC;;MAE7C;KACD,CAAC;EACJ;EAEAE,QAAQA,CAAA;IACN,IAAI,IAAI,CAACnD,YAAY,CAACoD,OAAO,EAAE;MAC7BN,OAAO,CAACO,GAAG,CAAC,yCAAyC,CAAC;MAEtD;MACA,IAAI,CAACC,oBAAoB,EAAE;MAE3B,IAAI,CAACxC,YAAY,CAACyC,WAAW,CAC3B,gEAAgE,CACjE;MACD;;IAEF,IAAI,CAACtC,SAAS,GAAG,IAAI;IACrB,MAAMuC,SAAS,GAAG,IAAI,CAACxD,YAAY,CAACyD,KAAK;IACzCX,OAAO,CAACO,GAAG,CAAC,oCAAoC,EAAEG,SAAS,CAAC;IAE5D;IACA,IAAI3B,SAAS,GAAG2B,SAAS,CAAC3B,SAAS;IACnC,IAAIC,OAAO,GAAG0B,SAAS,CAAC1B,OAAO;IAE/B;IACA,IAAI,OAAOD,SAAS,KAAK,QAAQ,EAAE;MACjCA,SAAS,GAAG,IAAI6B,IAAI,CAAC7B,SAAS,CAAC;;IAGjC,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;MAC/BA,OAAO,GAAG,IAAI4B,IAAI,CAAC5B,OAAO,CAAC;;IAG7B;IACA;IACA,MAAM6B,eAAe,GAAG;MACtBlC,KAAK,EAAE+B,SAAS,CAAC/B,KAAK;MACtBG,WAAW,EAAE4B,SAAS,CAAC5B,WAAW,IAAI,EAAE;MACxCG,IAAI,EAAEyB,SAAS,CAACzB,IAAI,IAAI,EAAE;MAC1BF,SAAS,EAAEA,SAAS;MACpBC,OAAO,EAAEA,OAAO;MAChBE,YAAY,EAAEwB,SAAS,CAACxB,YAAY,IAAI;KACzC;IAEDc,OAAO,CAACO,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAAClC,UAAU,CAAC;IAChE2B,OAAO,CAACO,GAAG,CAAC,oBAAoB,EAAEM,eAAe,CAAC;IAElD,IAAI;MACF,IAAI,CAACjD,eAAe,CACjBkD,cAAc,CAAC,IAAI,CAACzC,UAAU,EAAEwC,eAAe,CAAC,CAChDzB,SAAS,CAAC;QACTC,IAAI,EAAGC,QAAa,IAAI;UACtBU,OAAO,CAACO,GAAG,CAAC,kCAAkC,EAAEjB,QAAQ,CAAC;UACzD,IAAI,CAACnB,SAAS,GAAG,KAAK;UAEtB;UACA,IAAI,CAACH,YAAY,CAAC+C,WAAW,CAC3B,uCAAuC,CACxC;UAED;UACAf,OAAO,CAACO,GAAG,CACT,iDAAiD,EACjD,IAAI,CAAClC,UAAU,CAChB;UAED;UACA2C,UAAU,CAAC,MAAK;YACd,IAAI,CAACjD,MAAM,CAACkD,QAAQ,CAAC,CAAC,YAAY,EAAE,IAAI,CAAC5C,UAAU,CAAC,CAAC,CAAC6C,IAAI,CACvDC,SAAS,IAAKnB,OAAO,CAACO,GAAG,CAAC,sBAAsB,EAAEY,SAAS,CAAC,EAC5DpB,GAAG,IAAKC,OAAO,CAACtD,KAAK,CAAC,wBAAwB,EAAEqD,GAAG,CAAC,CACtD;UACH,CAAC,EAAE,GAAG,CAAC;QACT,CAAC;QACDrD,KAAK,EAAGqD,GAAQ,IAAI;UAClB,IAAI,CAAC5B,SAAS,GAAG,KAAK;UACtB6B,OAAO,CAACtD,KAAK,CAAC,4CAA4C,EAAEqD,GAAG,CAAC;UAEhE;UACA,IAAIA,GAAG,CAACE,MAAM,KAAK,GAAG,EAAE;YACtB,IAAI,CAACjC,YAAY,CAACkC,SAAS,CACzB,qEAAqE,CACtE;WACF,MAAM,IAAIH,GAAG,CAACE,MAAM,KAAK,GAAG,EAAE;YAC7B,IAAI,CAACjC,YAAY,CAACkC,SAAS,CACzB,sDAAsD,CACvD;WACF,MAAM;YACL;YACA,MAAMC,YAAY,GAChBJ,GAAG,CAACrD,KAAK,EAAE0D,OAAO,IAClB,2CAA2C;YAC7C,IAAI,CAACpC,YAAY,CAACkC,SAAS,CAACC,YAAY,EAAE,IAAI,CAAC;;UAGjD;UACA,IAAIJ,GAAG,CAACrD,KAAK,EAAE;YACbsD,OAAO,CAACtD,KAAK,CAAC,sBAAsB,EAAEqD,GAAG,CAACrD,KAAK,CAAC;;QAEpD;OACD,CAAC;KACL,CAAC,OAAO0E,CAAC,EAAE;MACV,IAAI,CAACjD,SAAS,GAAG,KAAK;MACtB,MAAMgC,YAAY,GAAGiB,CAAC,YAAYC,KAAK,GAAGD,CAAC,CAAChB,OAAO,GAAGkB,MAAM,CAACF,CAAC,CAAC;MAC/D,IAAI,CAACpD,YAAY,CAACkC,SAAS,CACzB,qCAAqCC,YAAY,EAAE,CACpD;MACDH,OAAO,CAACtD,KAAK,CAAC,mCAAmC,EAAE0E,CAAC,CAAC;;EAEzD;EAEA;EACAZ,oBAAoBA,CAAA;IAClBe,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtE,YAAY,CAACuE,QAAQ,CAAC,CAAC9B,OAAO,CAAE+B,GAAG,IAAI;MACtD,MAAM5B,OAAO,GAAG,IAAI,CAAC5C,YAAY,CAACC,GAAG,CAACuE,GAAG,CAAC;MAC1C,IAAI5B,OAAO,EAAE;QACXA,OAAO,CAAC6B,aAAa,EAAE;;IAE3B,CAAC,CAAC;EACJ;;;uBA7LWlE,qBAAqB,EAAAtB,+DAAA,CAAA0F,uDAAA,GAAA1F,+DAAA,CAAA4F,2EAAA,GAAA5F,+DAAA,CAAA8F,mEAAA,GAAA9F,+DAAA,CAAAgG,2DAAA,GAAAhG,+DAAA,CAAAgG,mDAAA,GAAAhG,+DAAA,CAAAmG,qEAAA;IAAA;EAAA;;;YAArB7E,qBAAqB;MAAA+E,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCZlC3G,4DAAA,aAAmD;UAI7CA,uDAAA,WAAgD;UAChDA,oDAAA,6BACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,WAAgC;UAAAA,oDAAA,kDAAsC;UAAAA,0DAAA,EAAI;UAG5EA,4DAAA,cAA2H;UAA1FA,wDAAA,sBAAA8G,wDAAA;YAAA,OAAYF,GAAA,CAAA1C,QAAA,EAAU;UAAA,EAAC;UAEtDlE,wDAAA,IAAA+G,oCAAA,iBAEM;UACN/G,4DAAA,aAAoC;UAI9BA,uDAAA,aAAuD;UACvDA,oDAAA,0CACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,eAAmD;UAI7CA,uDAAA,aAA+C;UAC/CA,oDAAA,iBACF;UAAAA,0DAAA,EAAQ;UACRA,uDAAA,iBAME;UACFA,wDAAA,KAAAgH,qCAAA,kBAIM;UACRhH,0DAAA,EAAM;UAGNA,4DAAA,WAAK;UAEDA,uDAAA,aAA0D;UAC1DA,oDAAA,sBACF;UAAAA,0DAAA,EAAQ;UACRA,uDAAA,iBAKE;UACJA,0DAAA,EAAM;UAKVA,4DAAA,eAA4F;UAExFA,uDAAA,aAAuD;UACvDA,oDAAA,kCACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,eAAmD;UAI7CA,uDAAA,aAAuD;UACvDA,oDAAA,8BACF;UAAAA,0DAAA,EAAQ;UACRA,uDAAA,iBAIE;UACJA,0DAAA,EAAM;UAGNA,4DAAA,WAAK;UAEDA,uDAAA,aAAuD;UACvDA,oDAAA,uBACF;UAAAA,0DAAA,EAAQ;UACRA,uDAAA,iBAIE;UACJA,0DAAA,EAAM;UAKVA,4DAAA,eAAkG;UAE9FA,uDAAA,aAAsD;UACtDA,oDAAA,qBACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,iBAA8D;UAC5DA,uDAAA,aAAgD;UAChDA,oDAAA,sCACF;UAAAA,0DAAA,EAAQ;UACRA,uDAAA,oBAKY;UACdA,0DAAA,EAAM;UAGNA,4DAAA,eAAkG;UAE9FA,uDAAA,aAAkD;UAClDA,oDAAA,sBACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,iBAA+D;UAC7DA,uDAAA,aAAyD;UACzDA,oDAAA,8CACF;UAAAA,0DAAA,EAAQ;UACRA,4DAAA,kBAIC;UACCA,wDAAA,KAAAiH,wCAAA,qBAES;;UACXjH,0DAAA,EAAS;UACTA,wDAAA,KAAAkH,qCAAA,kBAGM;UACNlH,4DAAA,aAAyC;UACvCA,uDAAA,aAAuC;UACvCA,oDAAA,+EACF;UAAAA,0DAAA,EAAI;UAKRA,4DAAA,eAAgG;UAK5FA,uDAAA,aAAiC;UACjCA,oDAAA,iBACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAKC;UAHCA,wDAAA,mBAAAmH,wDAAA;YAAA,OAASP,GAAA,CAAA1C,QAAA,EAAU;UAAA,EAAC;UAIpBlE,wDAAA,KAAAoH,mCAAA,gBAAmD;UACnDpH,wDAAA,KAAAqH,mCAAA,gBAA6D;UAC7DrH,oDAAA,IACF;UAAAA,0DAAA,EAAS;;;;;;UAtJPA,uDAAA,GAA0B;UAA1BA,wDAAA,cAAA4G,GAAA,CAAA7F,YAAA,CAA0B;UAExBf,uDAAA,GAAW;UAAXA,wDAAA,SAAA4G,GAAA,CAAArG,KAAA,CAAW;UAqBPP,uDAAA,IAAiG;UAAjGA,yDAAA,qBAAAuH,OAAA,GAAAX,GAAA,CAAA7F,YAAA,CAAAC,GAAA,4BAAAuG,OAAA,CAAApD,OAAA,OAAAoD,OAAA,GAAAX,GAAA,CAAA7F,YAAA,CAAAC,GAAA,4BAAAuG,OAAA,CAAAC,OAAA,EAAiG;UAG7FxH,uDAAA,GAA8E;UAA9EA,wDAAA,WAAAyH,OAAA,GAAAb,GAAA,CAAA7F,YAAA,CAAAC,GAAA,4BAAAyG,OAAA,CAAAtD,OAAA,OAAAsD,OAAA,GAAAb,GAAA,CAAA7F,YAAA,CAAAC,GAAA,4BAAAyG,OAAA,CAAAD,OAAA,EAA8E;UA2F7DxH,uDAAA,IAAiB;UAAjBA,wDAAA,YAAAA,yDAAA,SAAA4G,GAAA,CAAA9E,MAAA,EAAiB;UAItC9B,uDAAA,GAA4F;UAA5FA,wDAAA,WAAA2H,OAAA,GAAAf,GAAA,CAAA7F,YAAA,CAAAC,GAAA,mCAAA2G,OAAA,CAAAxD,OAAA,OAAAwD,OAAA,GAAAf,GAAA,CAAA7F,YAAA,CAAAC,GAAA,mCAAA2G,OAAA,CAAAH,OAAA,EAA4F;UAuBlGxH,uDAAA,GAA8C;UAA9CA,wDAAA,aAAA4G,GAAA,CAAA5E,SAAA,IAAA4E,GAAA,CAAA7F,YAAA,CAAAoD,OAAA,CAA8C;UAGjBnE,uDAAA,GAAgB;UAAhBA,wDAAA,UAAA4G,GAAA,CAAA5E,SAAA,CAAgB;UACLhC,uDAAA,GAAe;UAAfA,wDAAA,SAAA4G,GAAA,CAAA5E,SAAA,CAAe;UACvDhC,uDAAA,GACF;UADEA,gEAAA,MAAA4G,GAAA,CAAA5E,SAAA,8DACF", "sources": ["./src/app/views/front/plannings/planning-edit/planning-edit.component.ts", "./src/app/views/front/plannings/planning-edit/planning-edit.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { <PERSON><PERSON><PERSON>y, FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { PlanningService } from '@app/services/planning.service';\nimport { DataService } from '@app/services/data.service';\nimport { ToastService } from '@app/services/toast.service';\n\n@Component({\n  selector: 'app-planning-edit',\n  templateUrl: './planning-edit.component.html',\n  styleUrls: ['./planning-edit.component.css'],\n})\nexport class PlanningEditComponent implements OnInit {\n  planningForm!: FormGroup;\n  users$ = this.userService.getAllUsers();\n  planningId!: string;\n  error: string = '';\n  isLoading: boolean = false;\n\n  constructor(\n    private fb: FormBuilder,\n    private planningService: PlanningService,\n    private userService: DataService,\n    private route: ActivatedRoute,\n    private router: Router,\n    private toastService: ToastService\n  ) {}\n\n  ngOnInit(): void {\n    this.planningId = this.route.snapshot.paramMap.get('id')!;\n    this.initForm();\n    this.loadPlanning();\n  }\n\n  initForm(): void {\n    this.planningForm = this.fb.group({\n      titre: ['', [Validators.required, Validators.minLength(3)]],\n      description: [''],\n      dateDebut: ['', Validators.required],\n      dateFin: ['', Validators.required],\n      lieu: [''],\n      participants: [[], Validators.required], // FormArray for multiple participants\n    });\n  }\n\n  loadPlanning(): void {\n    this.planningService.getPlanningById(this.planningId).subscribe({\n      next: (response: any) => {\n        const planning = response.planning;\n\n        this.planningForm.patchValue({\n          titre: planning.titre,\n          description: planning.description,\n          dateDebut: planning.dateDebut,\n          dateFin: planning.dateFin,\n          lieu: planning.lieu,\n        });\n\n        const participantsArray = this.planningForm.get(\n          'participants'\n        ) as FormArray;\n        participantsArray.clear();\n\n        planning.participants.forEach((p: any) => {\n          participantsArray.push(this.fb.control(p._id));\n        });\n      },\n      error: (err) => {\n        console.error('Erreur lors du chargement du planning:', err);\n        if (err.status === 403) {\n          this.toastService.showError(\n            \"Accès refusé : vous n'avez pas les droits pour accéder à ce planning\"\n          );\n        } else if (err.status === 404) {\n          this.toastService.showError(\n            \"Le planning demandé n'existe pas ou a été supprimé\"\n          );\n        } else {\n          const errorMessage =\n            err.error?.message || 'Erreur lors du chargement du planning';\n          this.toastService.showError(errorMessage);\n        }\n      },\n    });\n  }\n\n  onSubmit(): void {\n    if (this.planningForm.invalid) {\n      console.log('Formulaire invalide, soumission annulée');\n\n      // Marquer tous les champs comme \"touched\" pour afficher les erreurs\n      this.markFormGroupTouched();\n\n      this.toastService.showWarning(\n        'Veuillez corriger les erreurs avant de soumettre le formulaire'\n      );\n      return;\n    }\n    this.isLoading = true;\n    const formValue = this.planningForm.value;\n    console.log('Données du formulaire à soumettre:', formValue);\n\n    // Vérifier que les dates sont au bon format\n    let dateDebut = formValue.dateDebut;\n    let dateFin = formValue.dateFin;\n\n    // S'assurer que les dates sont des objets Date\n    if (typeof dateDebut === 'string') {\n      dateDebut = new Date(dateDebut);\n    }\n\n    if (typeof dateFin === 'string') {\n      dateFin = new Date(dateFin);\n    }\n\n    // Créer un objet avec seulement les propriétés à mettre à jour\n    // sans utiliser le type Planning complet pour éviter les erreurs de typage\n    const updatedPlanning = {\n      titre: formValue.titre,\n      description: formValue.description || '',\n      lieu: formValue.lieu || '',\n      dateDebut: dateDebut,\n      dateFin: dateFin,\n      participants: formValue.participants || [],\n    };\n\n    console.log('Mise à jour du planning avec ID:', this.planningId);\n    console.log('Données formatées:', updatedPlanning);\n\n    try {\n      this.planningService\n        .updatePlanning(this.planningId, updatedPlanning)\n        .subscribe({\n          next: (response: any) => {\n            console.log('Planning mis à jour avec succès:', response);\n            this.isLoading = false;\n\n            // Afficher un toast de succès\n            this.toastService.showSuccess(\n              'Le planning a été modifié avec succès'\n            );\n\n            // Redirection vers la page de détail du planning\n            console.log(\n              'Redirection vers la page de détail du planning:',\n              this.planningId\n            );\n\n            // Utiliser setTimeout pour s'assurer que la redirection se produit après le traitement\n            setTimeout(() => {\n              this.router.navigate(['/plannings', this.planningId]).then(\n                (navigated) => console.log('Redirection réussie:', navigated),\n                (err) => console.error('Erreur de redirection:', err)\n              );\n            }, 100);\n          },\n          error: (err: any) => {\n            this.isLoading = false;\n            console.error('Erreur lors de la mise à jour du planning:', err);\n\n            // Gestion spécifique des erreurs d'autorisation\n            if (err.status === 403) {\n              this.toastService.showError(\n                \"Accès refusé : vous n'avez pas les droits pour modifier ce planning\"\n              );\n            } else if (err.status === 401) {\n              this.toastService.showError(\n                'Vous devez être connecté pour effectuer cette action'\n              );\n            } else {\n              // Autres erreurs\n              const errorMessage =\n                err.error?.message ||\n                'Erreur lors de la mise à jour du planning';\n              this.toastService.showError(errorMessage, 8000);\n            }\n\n            // Afficher plus de détails sur l'erreur dans la console\n            if (err.error) {\n              console.error(\"Détails de l'erreur:\", err.error);\n            }\n          },\n        });\n    } catch (e) {\n      this.isLoading = false;\n      const errorMessage = e instanceof Error ? e.message : String(e);\n      this.toastService.showError(\n        `Exception lors de la mise à jour: ${errorMessage}`\n      );\n      console.error('Exception lors de la mise à jour:', e);\n    }\n  }\n\n  // Marquer tous les champs comme \"touched\" pour déclencher l'affichage des erreurs\n  markFormGroupTouched() {\n    Object.keys(this.planningForm.controls).forEach((key) => {\n      const control = this.planningForm.get(key);\n      if (control) {\n        control.markAsTouched();\n      }\n    });\n  }\n}\n", "<div class=\"container mx-auto px-4 py-6 max-w-3xl\">\n  <!-- En-tête avec gradient coloré -->\n  <div class=\"bg-gradient-to-r from-purple-600 to-indigo-600 rounded-t-lg p-6 text-white mb-0\">\n    <h1 class=\"text-2xl font-bold flex items-center\">\n      <i class=\"fas fa-edit mr-3 text-purple-200\"></i>\n      Modifier le Planning\n    </h1>\n    <p class=\"text-purple-100 mt-2\">Modifiez les détails de votre planning</p>\n  </div>\n\n  <form [formGroup]=\"planningForm\" (ngSubmit)=\"onSubmit()\" novalidate class=\"bg-white rounded-b-lg shadow-lg p-6 border-t-0\">\n    <!-- Message d'erreur -->\n    <div *ngIf=\"error\" class=\"mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\">\n      {{ error }}\n    </div>\n    <div class=\"grid grid-cols-1 gap-6\">\n      <!-- Section Informations générales -->\n      <div class=\"bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg border border-purple-200\">\n        <h3 class=\"text-lg font-semibold text-purple-800 mb-4 flex items-center\">\n          <i class=\"fas fa-info-circle mr-2 text-purple-600\"></i>\n          Informations générales\n        </h3>\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <!-- Titre -->\n          <div>\n            <label class=\"block text-sm font-medium text-purple-700 mb-2\">\n              <i class=\"fas fa-tag mr-2 text-purple-500\"></i>\n              Titre *\n            </label>\n            <input\n              type=\"text\"\n              formControlName=\"titre\"\n              class=\"mt-1 block w-full px-4 py-3 border-2 border-purple-200 rounded-lg shadow-sm focus:ring-purple-500 focus:border-purple-500 focus:ring-2 transition-all duration-200\"\n              [class.border-red-300]=\"planningForm.get('titre')?.invalid && planningForm.get('titre')?.touched\"\n              placeholder=\"Nom de votre planning...\"\n            />\n            <div *ngIf=\"planningForm.get('titre')?.invalid && planningForm.get('titre')?.touched\" class=\"text-red-500 text-sm mt-2 flex items-center\">\n              <i class=\"fas fa-exclamation-circle mr-1\"></i>\n              <span *ngIf=\"planningForm.get('titre')?.errors?.['required']\">Le titre est obligatoire</span>\n              <span *ngIf=\"planningForm.get('titre')?.errors?.['minlength']\">Au moins 3 caractères requis</span>\n            </div>\n          </div>\n\n          <!-- Lieu -->\n          <div>\n            <label class=\"block text-sm font-medium text-orange-700 mb-2\">\n              <i class=\"fas fa-map-marker-alt mr-2 text-orange-500\"></i>\n              Lieu / Salle\n            </label>\n            <input\n              type=\"text\"\n              formControlName=\"lieu\"\n              class=\"mt-1 block w-full px-4 py-3 border-2 border-orange-200 rounded-lg shadow-sm focus:ring-orange-500 focus:border-orange-500 focus:ring-2 transition-all duration-200\"\n              placeholder=\"Salle, bureau, lieu de l'événement...\"\n            />\n          </div>\n        </div>\n      </div>\n\n      <!-- Section Période -->\n      <div class=\"bg-gradient-to-r from-blue-50 to-cyan-50 p-4 rounded-lg border border-blue-200\">\n        <h3 class=\"text-lg font-semibold text-blue-800 mb-4 flex items-center\">\n          <i class=\"fas fa-calendar-week mr-2 text-blue-600\"></i>\n          Période du planning\n        </h3>\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <!-- Date début -->\n          <div>\n            <label class=\"block text-sm font-medium text-green-700 mb-2\">\n              <i class=\"fas fa-calendar-day mr-2 text-green-500\"></i>\n              Date de début *\n            </label>\n            <input\n              type=\"date\"\n              formControlName=\"dateDebut\"\n              class=\"mt-1 block w-full px-4 py-3 border-2 border-green-200 rounded-lg shadow-sm focus:ring-green-500 focus:border-green-500 focus:ring-2 transition-all duration-200\"\n            />\n          </div>\n\n          <!-- Date fin -->\n          <div>\n            <label class=\"block text-sm font-medium text-red-700 mb-2\">\n              <i class=\"fas fa-calendar-check mr-2 text-red-500\"></i>\n              Date de fin *\n            </label>\n            <input\n              type=\"date\"\n              formControlName=\"dateFin\"\n              class=\"mt-1 block w-full px-4 py-3 border-2 border-red-200 rounded-lg shadow-sm focus:ring-red-500 focus:border-red-500 focus:ring-2 transition-all duration-200\"\n            />\n          </div>\n        </div>\n      </div>\n\n      <!-- Section Description -->\n      <div class=\"bg-gradient-to-r from-indigo-50 to-purple-50 p-4 rounded-lg border border-indigo-200\">\n        <h3 class=\"text-lg font-semibold text-indigo-800 mb-4 flex items-center\">\n          <i class=\"fas fa-align-left mr-2 text-indigo-600\"></i>\n          Description\n        </h3>\n        <label class=\"block text-sm font-medium text-indigo-700 mb-2\">\n          <i class=\"fas fa-edit mr-2 text-indigo-500\"></i>\n          Décrivez votre planning\n        </label>\n        <textarea\n          formControlName=\"description\"\n          class=\"mt-1 block w-full px-4 py-3 border-2 border-indigo-200 rounded-lg shadow-sm focus:ring-indigo-500 focus:border-indigo-500 focus:ring-2 transition-all duration-200\"\n          rows=\"4\"\n          placeholder=\"Décrivez les objectifs, le contexte ou les détails de ce planning...\"\n        ></textarea>\n      </div>\n\n      <!-- Section Participants -->\n      <div class=\"bg-gradient-to-r from-emerald-50 to-teal-50 p-4 rounded-lg border border-emerald-200\">\n        <h3 class=\"text-lg font-semibold text-emerald-800 mb-4 flex items-center\">\n          <i class=\"fas fa-users mr-2 text-emerald-600\"></i>\n          Participants\n        </h3>\n        <label class=\"block text-sm font-medium text-emerald-700 mb-2\">\n          <i class=\"fas fa-user-friends mr-2 text-emerald-500\"></i>\n          Sélectionnez les participants *\n        </label>\n        <select\n          formControlName=\"participants\"\n          multiple\n          class=\"mt-1 block w-full px-4 py-3 border-2 border-emerald-200 rounded-lg shadow-sm focus:ring-emerald-500 focus:border-emerald-500 focus:ring-2 transition-all duration-200 text-sm min-h-[120px]\"\n        >\n          <option *ngFor=\"let user of users$ | async\" [value]=\"user._id\" class=\"py-2\">\n            {{ user.username }}\n          </option>\n        </select>\n        <div *ngIf=\"planningForm.get('participants')?.invalid && planningForm.get('participants')?.touched\" class=\"text-red-500 text-sm mt-2 flex items-center\">\n          <i class=\"fas fa-exclamation-circle mr-1\"></i>\n          Veuillez sélectionner au moins un participant\n        </div>\n        <p class=\"text-xs text-emerald-600 mt-2\">\n          <i class=\"fas fa-info-circle mr-1\"></i>\n          Maintenez Ctrl (ou Cmd) pour sélectionner plusieurs participants\n        </p>\n      </div>\n    </div>\n\n    <!-- Boutons d'action avec design amélioré -->\n    <div class=\"mt-8 flex justify-end space-x-4 bg-gray-50 p-4 rounded-lg border-t border-gray-200\">\n      <button\n        type=\"button\"\n        routerLink=\"/plannings\"\n        class=\"px-6 py-3 border-2 border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-100 hover:border-gray-400 transition-all duration-200 flex items-center\">\n        <i class=\"fas fa-times mr-2\"></i>\n        Annuler\n      </button>\n      <button\n        type=\"button\"\n        (click)=\"onSubmit()\"\n        [disabled]=\"isLoading || planningForm.invalid\"\n        class=\"px-6 py-3 rounded-lg text-sm font-medium text-white bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center shadow-lg\"\n      >\n        <i class=\"fas fa-save mr-2\" *ngIf=\"!isLoading\"></i>\n        <i class=\"fas fa-spinner fa-spin mr-2\" *ngIf=\"isLoading\"></i>\n        {{ isLoading ? 'Enregistrement...' : 'Enregistrer les modifications' }}\n      </button>\n    </div>\n  </form>\n</div>"], "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "error", "ɵɵelement", "ɵɵtemplate", "PlanningEditComponent_div_20_span_2_Template", "PlanningEditComponent_div_20_span_3_Template", "ɵɵproperty", "tmp_0_0", "ctx_r1", "planningForm", "get", "errors", "tmp_1_0", "user_r8", "_id", "username", "PlanningEditComponent", "constructor", "fb", "planningService", "userService", "route", "router", "toastService", "users$", "getAllUsers", "isLoading", "ngOnInit", "planningId", "snapshot", "paramMap", "initForm", "loadPlanning", "group", "titre", "required", "<PERSON><PERSON><PERSON><PERSON>", "description", "dateDebut", "dateFin", "lieu", "participants", "getPlanningById", "subscribe", "next", "response", "planning", "patchValue", "participantsArray", "clear", "for<PERSON>ach", "p", "push", "control", "err", "console", "status", "showError", "errorMessage", "message", "onSubmit", "invalid", "log", "markFormGroupTouched", "showWarning", "formValue", "value", "Date", "updatedPlanning", "updatePlanning", "showSuccess", "setTimeout", "navigate", "then", "navigated", "e", "Error", "String", "Object", "keys", "controls", "key", "<PERSON><PERSON><PERSON><PERSON>ched", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "PlanningService", "i3", "DataService", "i4", "ActivatedRoute", "Router", "i5", "ToastService", "selectors", "decls", "vars", "consts", "template", "PlanningEditComponent_Template", "rf", "ctx", "ɵɵlistener", "PlanningEditComponent_Template_form_ngSubmit_7_listener", "PlanningEditComponent_div_8_Template", "PlanningEditComponent_div_20_Template", "PlanningEditComponent_option_57_Template", "PlanningEditComponent_div_59_Template", "PlanningEditComponent_Template_button_click_67_listener", "PlanningEditComponent_i_68_Template", "PlanningEditComponent_i_69_Template", "ɵɵclassProp", "tmp_2_0", "touched", "tmp_3_0", "ɵɵpipeBind1", "tmp_5_0"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}