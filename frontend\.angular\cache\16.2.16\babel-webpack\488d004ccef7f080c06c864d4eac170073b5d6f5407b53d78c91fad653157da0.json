{"ast": null, "code": "import { CalendarView } from 'angular-calendar';\nimport { trigger, state, style, animate, transition } from '@angular/animations';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@app/services/planning.service\";\nimport * as i3 from \"@app/services/reunion.service\";\nimport * as i4 from \"@app/services/authuser.service\";\nimport * as i5 from \"@angular/platform-browser\";\nimport * as i6 from \"@app/services/toast.service\";\nfunction PlanningDetailComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelement(1, \"div\", 8);\n    i0.ɵɵelementStart(2, \"p\", 9);\n    i0.ɵɵtext(3, \"Chargement des d\\u00E9tails...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PlanningDetailComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 12);\n    i0.ɵɵelement(3, \"path\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction PlanningDetailComponent_div_7_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 19);\n    i0.ɵɵelement(2, \"path\", 54)(3, \"path\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r3.planning.lieu);\n  }\n}\nfunction PlanningDetailComponent_div_7_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const participant_r6 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    i0.ɵɵstyleProp(\"animation-delay\", i_r7 * 0.1 + \"s\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(participant_r6.username);\n  }\n}\nfunction PlanningDetailComponent_div_7_div_88_li_8_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const event_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", event_r9.meta.description, \" \");\n  }\n}\nfunction PlanningDetailComponent_div_7_div_88_li_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 61)(1, \"div\", 62)(2, \"div\", 63);\n    i0.ɵɵelement(3, \"strong\", 64);\n    i0.ɵɵpipe(4, \"highlightPresence\");\n    i0.ɵɵelementStart(5, \"div\", 65);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(6, \"svg\", 66);\n    i0.ɵɵelement(7, \"path\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵpipe(10, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, PlanningDetailComponent_div_7_div_88_li_8_div_11_Template, 2, 1, \"div\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(12, \"div\", 69)(13, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function PlanningDetailComponent_div_7_div_88_li_8_Template_button_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r14);\n      const event_r9 = restoredCtx.$implicit;\n      const ctx_r13 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r13.editReunion(event_r9.meta.id));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(14, \"svg\", 71);\n    i0.ɵɵelement(15, \"path\", 33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(16, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function PlanningDetailComponent_div_7_div_88_li_8_Template_button_click_16_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r14);\n      const event_r9 = restoredCtx.$implicit;\n      const ctx_r15 = i0.ɵɵnextContext(3);\n      ctx_r15.deleteReunion(event_r9.meta.id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(17, \"svg\", 71);\n    i0.ɵɵelement(18, \"path\", 35);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const event_r9 = ctx.$implicit;\n    const i_r10 = ctx.index;\n    i0.ɵɵstyleProp(\"animation-delay\", i_r10 * 0.1 + \"s\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(4, 6, event_r9.title), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind2(9, 8, event_r9.start, \"shortTime\"), \" - \", i0.ɵɵpipeBind2(10, 11, event_r9.end, \"shortTime\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", event_r9.meta == null ? null : event_r9.meta.description);\n  }\n}\nfunction PlanningDetailComponent_div_7_div_88_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"h3\")(2, \"span\", 11);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 58);\n    i0.ɵɵelement(4, \"path\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(7, \"ul\", 59);\n    i0.ɵɵtemplate(8, PlanningDetailComponent_div_7_div_88_li_8_Template, 19, 14, \"li\", 60);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"@fadeInUp\", undefined);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" D\\u00E9tails pour le \", i0.ɵɵpipeBind2(6, 3, ctx_r5.selectedDate, \"fullDate\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.selectedDayEvents);\n  }\n}\nfunction PlanningDetailComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵlistener(\"mouseenter\", function PlanningDetailComponent_div_7_Template_div_mouseenter_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.onCardMouseEnter());\n    })(\"mouseleave\", function PlanningDetailComponent_div_7_Template_div_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.onCardMouseLeave());\n    });\n    i0.ɵɵelementStart(1, \"div\", 15)(2, \"h1\", 16);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"p\", 17);\n    i0.ɵɵpipe(5, \"highlightPresence\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 18)(7, \"h2\");\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(8, \"svg\", 19);\n    i0.ɵɵelement(9, \"path\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Informations \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(11, \"div\", 21);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(12, \"svg\", 19);\n    i0.ɵɵelement(13, \"path\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15, \" Du \");\n    i0.ɵɵelementStart(16, \"strong\");\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(19, \" au \");\n    i0.ɵɵelementStart(20, \"strong\");\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(23, PlanningDetailComponent_div_7_div_23_Template, 6, 1, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 18)(25, \"h2\");\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(26, \"svg\", 19);\n    i0.ɵɵelement(27, \"path\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(28, \" Participants \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(29, \"div\", 25);\n    i0.ɵɵtemplate(30, PlanningDetailComponent_div_7_div_30_Template, 3, 3, \"div\", 26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 18)(32, \"div\", 27)(33, \"h2\");\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(34, \"svg\", 19);\n    i0.ɵɵelement(35, \"path\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(36, \" R\\u00E9unions associ\\u00E9es \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(37, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function PlanningDetailComponent_div_7_Template_button_click_37_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.nouvelleReunion());\n    });\n    i0.ɵɵelementStart(38, \"span\", 11);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(39, \"svg\", 29);\n    i0.ɵɵelement(40, \"path\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(41, \" Nouvelle R\\u00E9union \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(42, \"div\", 31)(43, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function PlanningDetailComponent_div_7_Template_button_click_43_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.editPlanning());\n    });\n    i0.ɵɵelementStart(44, \"span\", 11);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(45, \"svg\", 29);\n    i0.ɵɵelement(46, \"path\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(47, \" Modifier Planning \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(48, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function PlanningDetailComponent_div_7_Template_button_click_48_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.deletePlanning());\n    });\n    i0.ɵɵelementStart(49, \"span\", 11);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(50, \"svg\", 29);\n    i0.ɵɵelement(51, \"path\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(52, \" Supprimer Planning \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(53, \"div\", 36)(54, \"div\", 37)(55, \"div\", 38)(56, \"div\")(57, \"p\", 39);\n    i0.ɵɵtext(58, \"Total R\\u00E9unions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"p\", 40);\n    i0.ɵɵtext(60);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(61, \"div\", 41);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(62, \"svg\", 42);\n    i0.ɵɵelement(63, \"path\", 43);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(64, \"div\", 44)(65, \"div\", 38)(66, \"div\")(67, \"p\", 39);\n    i0.ɵɵtext(68, \"P\\u00E9riode\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(69, \"p\", 45);\n    i0.ɵɵtext(70);\n    i0.ɵɵpipe(71, \"date\");\n    i0.ɵɵpipe(72, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(73, \"div\", 46);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(74, \"svg\", 47);\n    i0.ɵɵelement(75, \"path\", 22);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(76, \"div\", 48)(77, \"div\", 38)(78, \"div\")(79, \"p\", 39);\n    i0.ɵɵtext(80, \"Participants\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(81, \"p\", 40);\n    i0.ɵɵtext(82);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(83, \"div\", 49);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(84, \"svg\", 50);\n    i0.ɵɵelement(85, \"path\", 24);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(86, \"div\", 51)(87, \"mwl-calendar-month-view\", 52);\n    i0.ɵɵlistener(\"dayClicked\", function PlanningDetailComponent_div_7_Template_mwl_calendar_month_view_dayClicked_87_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.handleDayClick($event.day));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(88, PlanningDetailComponent_div_7_div_88_Template, 9, 6, \"div\", 53);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@cardHover\", ctx_r2.cardState);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"@fadeInUp\", undefined);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.planning.titre);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(5, 18, ctx_r2.planning.description), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"@fadeInUp\", ctx_r2.sectionStates.info);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(18, 20, ctx_r2.planning.dateDebut, \"mediumDate\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(22, 23, ctx_r2.planning.dateFin, \"mediumDate\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.planning.lieu);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"@fadeInUp\", ctx_r2.sectionStates.participants);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.planning.participants);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"@fadeInUp\", ctx_r2.sectionStates.reunions);\n    i0.ɵɵadvance(29);\n    i0.ɵɵtextInterpolate((ctx_r2.planning.reunions == null ? null : ctx_r2.planning.reunions.length) || 0);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind2(71, 26, ctx_r2.planning.dateDebut, \"shortDate\"), \" - \", i0.ɵɵpipeBind2(72, 29, ctx_r2.planning.dateFin, \"shortDate\"), \" \");\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate((ctx_r2.planning.participants == null ? null : ctx_r2.planning.participants.length) || 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"viewDate\", ctx_r2.viewDate)(\"events\", ctx_r2.events);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedDayEvents.length > 0);\n  }\n}\nexport let PlanningDetailComponent = /*#__PURE__*/(() => {\n  class PlanningDetailComponent {\n    constructor(route, router, planningService, reunionService, authService, cdr, sanitizer, toastService) {\n      this.route = route;\n      this.router = router;\n      this.planningService = planningService;\n      this.reunionService = reunionService;\n      this.authService = authService;\n      this.cdr = cdr;\n      this.sanitizer = sanitizer;\n      this.toastService = toastService;\n      this.planning = null;\n      this.loading = true;\n      this.error = null;\n      this.isCreator = false;\n      this.selectedDayEvents = [];\n      this.selectedDate = null;\n      this.cardState = 'default';\n      // Calendar setup\n      this.view = CalendarView.Month;\n      this.viewDate = new Date();\n      this.events = [];\n      // Pour les animations\n      this.sectionStates = {\n        info: false,\n        participants: false,\n        reunions: false\n      };\n    }\n    ngOnInit() {\n      this.loadPlanningDetails();\n      // Activer les animations des sections avec un délai\n      setTimeout(() => {\n        this.sectionStates.info = true;\n      }, 300);\n      setTimeout(() => {\n        this.sectionStates.participants = true;\n      }, 600);\n      setTimeout(() => {\n        this.sectionStates.reunions = true;\n      }, 900);\n    }\n    loadPlanningDetails() {\n      const id = this.route.snapshot.paramMap.get('id');\n      if (!id) {\n        this.loading = false;\n        this.toastService.error('Erreur de navigation', 'ID de planning non fourni');\n        return;\n      }\n      this.planningService.getPlanningById(id).subscribe({\n        next: planning => {\n          this.planning = planning.planning;\n          this.isCreator = planning.planning.createur._id === this.authService.getCurrentUserId();\n          this.loading = false;\n          // Créer les événements pour le calendrier avec des couleurs personnalisées\n          this.events = this.planning.reunions.map((reunion, index) => {\n            const startStr = `${reunion.date.substring(0, 10)}T${reunion.heureDebut}:00`;\n            const endStr = `${reunion.date.substring(0, 10)}T${reunion.heureFin}:00`;\n            // Générer une couleur basée sur l'index pour différencier les événements\n            const hue = index * 137 % 360; // Formule pour distribuer les couleurs\n            return {\n              start: new Date(startStr),\n              end: new Date(endStr),\n              title: reunion.titre,\n              allDay: false,\n              color: {\n                primary: `hsl(${hue}, 70%, 50%)`,\n                secondary: `hsl(${hue}, 70%, 90%)`\n              },\n              meta: {\n                description: reunion.description || '',\n                id: reunion._id\n              }\n            };\n          });\n          this.cdr.detectChanges();\n        },\n        error: err => {\n          this.loading = false;\n          console.error('Erreur:', err);\n          if (err.status === 403) {\n            this.toastService.accessDenied('accéder à ce planning', err.status);\n          } else if (err.status === 404) {\n            this.toastService.error('Planning introuvable', 'Le planning demandé n\\'existe pas ou a été supprimé');\n          } else {\n            const errorMessage = err.error?.message || 'Erreur lors du chargement du planning';\n            this.toastService.error('Erreur de chargement', errorMessage);\n          }\n        }\n      });\n    }\n    handleDayClick(day) {\n      this.selectedDate = day.date;\n      this.selectedDayEvents = day.events;\n      // Animation pour l'affichage des événements\n      if (day.events.length > 0) {\n        // Effet de scroll doux vers les détails des événements\n        setTimeout(() => {\n          const dayEventsElement = document.querySelector('.day-events');\n          if (dayEventsElement) {\n            dayEventsElement.scrollIntoView({\n              behavior: 'smooth',\n              block: 'nearest'\n            });\n          }\n        }, 100);\n      }\n    }\n    // Méthodes pour les animations\n    onCardMouseEnter() {\n      this.cardState = 'hovered';\n    }\n    onCardMouseLeave() {\n      this.cardState = 'default';\n    }\n    editPlanning() {\n      if (this.planning) {\n        this.router.navigate(['/plannings/edit', this.planning._id]);\n      }\n    }\n    deletePlanning() {\n      if (this.planning && confirm('Supprimer définitivement ce planning ?')) {\n        this.planningService.deletePlanning(this.planning._id).subscribe({\n          next: () => {\n            this.toastService.success('Planning supprimé', 'Le planning a été supprimé avec succès');\n            this.router.navigate(['/plannings']);\n          },\n          error: err => {\n            console.error('Erreur lors de la suppression du planning:', err);\n            if (err.status === 403) {\n              this.toastService.accessDenied('supprimer ce planning', err.status);\n            } else if (err.status === 401) {\n              this.toastService.error('Non autorisé', 'Vous devez être connecté pour supprimer un planning');\n            } else {\n              const errorMessage = err.error?.message || 'Erreur lors de la suppression du planning';\n              this.toastService.error('Erreur de suppression', errorMessage, 8000);\n            }\n          }\n        });\n      }\n    }\n    nouvelleReunion() {\n      if (this.planning) {\n        // Rediriger vers le formulaire de création de réunion avec l'ID du planning préselectionné\n        this.router.navigate(['/reunions/nouvelleReunion'], {\n          queryParams: {\n            planningId: this.planning._id\n          }\n        });\n      }\n    }\n    /**\n     * Modifie une réunion\n     * @param reunionId ID de la réunion à modifier\n     */\n    editReunion(reunionId) {\n      if (reunionId) {\n        this.router.navigate(['/reunions/modifier', reunionId]);\n      }\n    }\n    /**\n     * Supprime une réunion après confirmation\n     * @param reunionId ID de la réunion à supprimer\n     */\n    deleteReunion(reunionId) {\n      if (confirm('Êtes-vous sûr de vouloir supprimer cette réunion ?')) {\n        this.reunionService.deleteReunion(reunionId).subscribe({\n          next: response => {\n            console.log('Réunion supprimée avec succès:', response);\n            this.toastService.success('Réunion supprimée', 'La réunion a été supprimée avec succès');\n            // Recharger les détails du planning pour mettre à jour le calendrier\n            this.loadPlanningDetails();\n            // Vider les événements du jour sélectionné si la réunion supprimée était affichée\n            this.selectedDayEvents = this.selectedDayEvents.filter(event => event.meta?.id !== reunionId);\n          },\n          error: error => {\n            console.error('Erreur lors de la suppression:', error);\n            if (error.status === 403) {\n              this.toastService.accessDenied('supprimer cette réunion', error.status);\n            } else if (error.status === 401) {\n              this.toastService.error('Non autorisé', 'Vous devez être connecté pour supprimer une réunion');\n            } else {\n              const errorMessage = error.error?.message || 'Erreur lors de la suppression de la réunion';\n              this.toastService.error('Erreur de suppression', errorMessage, 8000);\n            }\n          }\n        });\n      }\n    }\n    formatDescription(description) {\n      // Recherche la chaîne \"(presence obligatoire)\" (insensible à la casse) et la remplace par une version en rouge\n      const formattedText = description.replace(/\\(presence obligatoire\\)/gi, '<span class=\"text-red-600 font-semibold\">(presence obligatoire)</span>');\n      // Sanitize le HTML pour éviter les problèmes de sécurité\n      return this.sanitizer.bypassSecurityTrustHtml(formattedText);\n    }\n    static {\n      this.ɵfac = function PlanningDetailComponent_Factory(t) {\n        return new (t || PlanningDetailComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.PlanningService), i0.ɵɵdirectiveInject(i3.ReunionService), i0.ɵɵdirectiveInject(i4.AuthuserService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i5.DomSanitizer), i0.ɵɵdirectiveInject(i6.ToastService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: PlanningDetailComponent,\n        selectors: [[\"app-planning-detail\"]],\n        decls: 8,\n        vars: 3,\n        consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-6\"], [1, \"back-button\", \"mb-4\", \"flex\", \"items-center\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 0 20 20\", \"fill\", \"currentColor\", 1, \"h-5\", \"w-5\"], [\"fill-rule\", \"evenodd\", \"d\", \"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\", \"clip-rule\", \"evenodd\"], [\"class\", \"text-center py-8\", 4, \"ngIf\"], [\"class\", \"bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-lg shadow-md mb-6 animate__animated animate__fadeIn\", 4, \"ngIf\"], [\"class\", \"planning-card\", 3, \"mouseenter\", \"mouseleave\", 4, \"ngIf\"], [1, \"text-center\", \"py-8\"], [1, \"loading-spinner\"], [1, \"text-purple-600\", \"mt-3\", \"font-medium\"], [1, \"bg-red-100\", \"border-l-4\", \"border-red-500\", \"text-red-700\", \"p-4\", \"rounded-lg\", \"shadow-md\", \"mb-6\", \"animate__animated\", \"animate__fadeIn\"], [1, \"flex\", \"items-center\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-6\", \"w-6\", \"text-red-500\", \"mr-3\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\"], [1, \"planning-card\", 3, \"mouseenter\", \"mouseleave\"], [1, \"planning-header\"], [1, \"mb-2\"], [1, \"text-base\", 3, \"innerHTML\"], [1, \"planning-section\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"info-item\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"], [\"class\", \"info-item\", 4, \"ngIf\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z\"], [1, \"participants-list\"], [\"class\", \"participant-badge\", 3, \"animation-delay\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mb-4\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"mr-1\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 6v6m0 0v6m0-6h6m-6 0H6\"], [1, \"flex\", \"justify-end\", \"space-x-3\", \"mb-4\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"], [1, \"btn\", \"btn-danger\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-3\", \"gap-4\", \"mb-6\"], [1, \"bg-gradient-to-br\", \"from-purple-50\", \"to-indigo-50\", \"p-4\", \"rounded-lg\", \"shadow-sm\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"text-sm\", \"text-gray-500\"], [1, \"text-2xl\", \"font-bold\", \"text-gray-800\"], [1, \"bg-purple-100\", \"p-3\", \"rounded-full\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-6\", \"w-6\", \"text-purple-600\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [1, \"bg-gradient-to-br\", \"from-blue-50\", \"to-cyan-50\", \"p-4\", \"rounded-lg\", \"shadow-sm\"], [1, \"text-lg\", \"font-bold\", \"text-gray-800\"], [1, \"bg-blue-100\", \"p-3\", \"rounded-full\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-6\", \"w-6\", \"text-blue-600\"], [1, \"bg-gradient-to-br\", \"from-green-50\", \"to-emerald-50\", \"p-4\", \"rounded-lg\", \"shadow-sm\"], [1, \"bg-green-100\", \"p-3\", \"rounded-full\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-6\", \"w-6\", \"text-green-600\"], [1, \"calendar-container\"], [3, \"viewDate\", \"events\", \"dayClicked\"], [\"class\", \"day-events\", 4, \"ngIf\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"], [1, \"participant-badge\"], [1, \"day-events\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"mr-2\"], [1, \"space-y-3\"], [\"class\", \"event-item bg-white p-4 rounded-lg shadow-sm border border-gray-100\", 3, \"animation-delay\", 4, \"ngFor\", \"ngForOf\"], [1, \"event-item\", \"bg-white\", \"p-4\", \"rounded-lg\", \"shadow-sm\", \"border\", \"border-gray-100\"], [1, \"flex\", \"justify-between\", \"items-start\"], [1, \"flex-1\"], [3, \"innerHTML\"], [1, \"flex\", \"items-center\", \"text-gray-600\", \"mt-1\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"mr-1\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"], [\"class\", \"mt-2 text-sm text-gray-500\", 4, \"ngIf\"], [1, \"flex\", \"space-x-2\", \"ml-4\"], [\"title\", \"Modifier la r\\u00E9union\", 1, \"text-blue-500\", \"hover:text-blue-700\", \"transition-colors\", \"duration-300\", \"p-1\", \"rounded-full\", \"hover:bg-blue-50\", 3, \"click\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\"], [\"title\", \"Supprimer la r\\u00E9union\", 1, \"text-red-500\", \"hover:text-red-700\", \"transition-colors\", \"duration-300\", \"p-1\", \"rounded-full\", \"hover:bg-red-50\", 3, \"click\"], [1, \"mt-2\", \"text-sm\", \"text-gray-500\"]],\n        template: function PlanningDetailComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"button\", 1);\n            i0.ɵɵlistener(\"click\", function PlanningDetailComponent_Template_button_click_1_listener() {\n              return ctx.router.navigate([\"/plannings\"]);\n            });\n            i0.ɵɵnamespaceSVG();\n            i0.ɵɵelementStart(2, \"svg\", 2);\n            i0.ɵɵelement(3, \"path\", 3);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(4, \" Retour aux plannings \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(5, PlanningDetailComponent_div_5_Template, 4, 0, \"div\", 4);\n            i0.ɵɵtemplate(6, PlanningDetailComponent_div_6_Template, 6, 1, \"div\", 5);\n            i0.ɵɵtemplate(7, PlanningDetailComponent_div_7_Template, 89, 32, \"div\", 6);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.planning);\n          }\n        },\n        styles: [\"@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_pulse{0%{box-shadow:0 0 #7c3aed66}70%{box-shadow:0 0 0 10px #7c3aed00}to{box-shadow:0 0 #7c3aed00}}@keyframes _ngcontent-%COMP%_rotate{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.container[_ngcontent-%COMP%]{max-width:1200px;animation:_ngcontent-%COMP%_fadeInUp .5s ease-out}.planning-card[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ffffff 0%,#f8f9fa 100%);border-radius:12px;box-shadow:0 10px 30px #00000014;overflow:hidden;transition:all .3s ease;position:relative}.planning-card[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;width:100%;height:5px;background:linear-gradient(90deg,#7c3aed,#4f46e5,#3b82f6)}.planning-header[_ngcontent-%COMP%]{padding:2rem;position:relative;overflow:hidden;background:linear-gradient(135deg,rgba(124,58,237,.05) 0%,rgba(79,70,229,.1) 100%);border-bottom:1px solid rgba(0,0,0,.05)}.planning-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2rem;font-weight:700;color:#2d3748;margin-bottom:.5rem;position:relative;display:inline-block}.planning-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;bottom:-5px;left:0;width:40px;height:3px;background:linear-gradient(90deg,#7c3aed,#4f46e5);transition:width .3s ease}.planning-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]:hover:after{width:100%}.planning-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#4a5568;font-size:1.1rem;line-height:1.6}.planning-section[_ngcontent-%COMP%]{padding:1.5rem 2rem;border-bottom:1px solid rgba(0,0,0,.05);animation:_ngcontent-%COMP%_fadeInUp .5s ease-out;animation-fill-mode:both}.planning-section[_ngcontent-%COMP%]:nth-child(2){animation-delay:.1s}.planning-section[_ngcontent-%COMP%]:nth-child(3){animation-delay:.2s}.planning-section[_ngcontent-%COMP%]:nth-child(4){animation-delay:.3s}.planning-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;color:#2d3748;margin-bottom:1rem;display:flex;align-items:center}.planning-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{margin-right:.5rem;color:#7c3aed}.info-item[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:.75rem;padding:.5rem;border-radius:8px;transition:all .2s ease}.info-item[_ngcontent-%COMP%]:hover{background-color:#7c3aed0d}.info-item[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{color:#7c3aed;margin-right:.75rem;flex-shrink:0}.info-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#4a5568;font-size:1rem}.info-item[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{color:#2d3748;font-weight:600}.participants-list[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:.5rem}.participant-badge[_ngcontent-%COMP%]{display:flex;align-items:center;padding:.5rem 1rem;background:linear-gradient(135deg,#f9fafb 0%,#f3f4f6 100%);border:1px solid #e5e7eb;border-radius:9999px;transition:all .2s ease}.participant-badge[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 6px -1px #0000001a;background:linear-gradient(135deg,#f3f4f6 0%,#e5e7eb 100%)}.participant-badge[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#4b5563;font-weight:500}.btn[_ngcontent-%COMP%]{padding:.625rem 1.25rem;font-weight:500;border-radius:8px;transition:all .3s ease;position:relative;overflow:hidden;z-index:1}.btn[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:all .5s ease;z-index:-1}.btn[_ngcontent-%COMP%]:hover:before{left:100%}.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#7c3aed 0%,#6d28d9 100%);color:#fff}.btn-primary[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#6d28d9 0%,#5b21b6 100%);transform:translateY(-2px);box-shadow:0 4px 12px #6d28d94d}.btn-secondary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#3b82f6 0%,#2563eb 100%);color:#fff}.btn-secondary[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#2563eb 0%,#1d4ed8 100%);transform:translateY(-2px);box-shadow:0 4px 12px #2563eb4d}.btn-danger[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ef4444 0%,#dc2626 100%);color:#fff}.btn-danger[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#dc2626 0%,#b91c1c 100%);transform:translateY(-2px);box-shadow:0 4px 12px #dc26264d}.calendar-container[_ngcontent-%COMP%]{margin-top:1.5rem;border-radius:12px;overflow:hidden;box-shadow:0 4px 6px -1px #0000001a}.day-events[_ngcontent-%COMP%]{margin-top:1.5rem;padding:1.5rem;background:linear-gradient(135deg,#f9fafb 0%,#f3f4f6 100%);border-radius:12px;box-shadow:0 4px 6px -1px #0000000d;animation:_ngcontent-%COMP%_fadeInUp .4s ease-out}.day-events[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#4b5563;font-weight:600;margin-bottom:1rem;padding-bottom:.5rem;border-bottom:2px solid rgba(124,58,237,.2)}.event-item[_ngcontent-%COMP%]{padding:1rem;margin-bottom:.75rem;background:white;border-radius:8px;border-left:4px solid #7c3aed;box-shadow:0 2px 4px #0000000d;transition:all .2s ease}.event-item[_ngcontent-%COMP%]:hover{transform:translate(5px);box-shadow:0 4px 8px #0000001a}.event-item[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{display:block;color:#2d3748;margin-bottom:.25rem}.event-item[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{color:#6b7280;font-size:.875rem}.back-button[_ngcontent-%COMP%]{display:inline-flex;align-items:center;padding:.5rem 1rem;color:#7c3aed;font-weight:500;border-radius:8px;transition:all .2s ease}.back-button[_ngcontent-%COMP%]:hover{background-color:#7c3aed0d;color:#6d28d9;transform:translate(-5px)}.back-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{margin-right:.5rem}.loading-spinner[_ngcontent-%COMP%]{width:50px;height:50px;border:3px solid rgba(124,58,237,.1);border-radius:50%;border-top-color:#7c3aed;animation:_ngcontent-%COMP%_rotate 1s linear infinite;margin:2rem auto}\"],\n        data: {\n          animation: [\n          // Animation pour l'entrée des sections\n          trigger('fadeInUp', [transition(':enter', [style({\n            opacity: 0,\n            transform: 'translateY(20px)'\n          }), animate('0.5s ease-out', style({\n            opacity: 1,\n            transform: 'translateY(0)'\n          }))])]),\n          // Animation pour le survol des cartes\n          trigger('cardHover', [state('default', style({\n            transform: 'scale(1)',\n            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'\n          })), state('hovered', style({\n            transform: 'scale(1.02)',\n            boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'\n          })), transition('default => hovered', [animate('0.2s ease-in-out')]), transition('hovered => default', [animate('0.2s ease-in-out')])])]\n        }\n      });\n    }\n  }\n  return PlanningDetailComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}