"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([["default-src_app_views_front_plannings_planning-edit_planning-edit_component_ts"],{

/***/ 652:
/*!********************************************************************************!*\
  !*** ./src/app/views/front/plannings/planning-edit/planning-edit.component.ts ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   PlanningEditComponent: () => (/* binding */ PlanningEditComponent)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _app_services_planning_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @app/services/planning.service */ 6543);
/* harmony import */ var _app_services_data_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @app/services/data.service */ 8490);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _app_services_toast_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @app/services/toast.service */ 8397);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/common */ 316);








function PlanningEditComponent_div_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", ctx_r0.error, " ");
  }
}
function PlanningEditComponent_div_20_span_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, "Le titre est obligatoire");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function PlanningEditComponent_div_20_span_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, "Au moins 3 caract\u00E8res requis");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function PlanningEditComponent_div_20_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 50);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "i", 51);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](2, PlanningEditComponent_div_20_span_2_Template, 2, 0, "span", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](3, PlanningEditComponent_div_20_span_3_Template, 2, 0, "span", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    let tmp_0_0;
    let tmp_1_0;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", (tmp_0_0 = ctx_r1.planningForm.get("titre")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors["required"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", (tmp_1_0 = ctx_r1.planningForm.get("titre")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors["minlength"]);
  }
}
function PlanningEditComponent_option_57_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "option", 53);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const user_r8 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("value", user_r8._id);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", user_r8.username, " ");
  }
}
function PlanningEditComponent_div_59_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 50);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "i", 51);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](2, " Veuillez s\u00E9lectionner au moins un participant ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function PlanningEditComponent_i_68_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](0, "i", 54);
  }
}
function PlanningEditComponent_i_69_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](0, "i", 55);
  }
}
class PlanningEditComponent {
  constructor(fb, planningService, userService, route, router, toastService) {
    this.fb = fb;
    this.planningService = planningService;
    this.userService = userService;
    this.route = route;
    this.router = router;
    this.toastService = toastService;
    this.users$ = this.userService.getAllUsers();
    this.error = '';
    this.isLoading = false;
  }
  ngOnInit() {
    this.planningId = this.route.snapshot.paramMap.get('id');
    this.initForm();
    this.loadPlanning();
  }
  initForm() {
    this.planningForm = this.fb.group({
      titre: ['', [_angular_forms__WEBPACK_IMPORTED_MODULE_4__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.Validators.minLength(3)]],
      description: [''],
      dateDebut: ['', _angular_forms__WEBPACK_IMPORTED_MODULE_4__.Validators.required],
      dateFin: ['', _angular_forms__WEBPACK_IMPORTED_MODULE_4__.Validators.required],
      lieu: [''],
      participants: [[], _angular_forms__WEBPACK_IMPORTED_MODULE_4__.Validators.required] // FormArray for multiple participants
    });
  }

  loadPlanning() {
    this.planningService.getPlanningById(this.planningId).subscribe({
      next: response => {
        const planning = response.planning;
        this.planningForm.patchValue({
          titre: planning.titre,
          description: planning.description,
          dateDebut: planning.dateDebut,
          dateFin: planning.dateFin,
          lieu: planning.lieu
        });
        const participantsArray = this.planningForm.get('participants');
        participantsArray.clear();
        planning.participants.forEach(p => {
          participantsArray.push(this.fb.control(p._id));
        });
      },
      error: err => {
        console.error('Erreur lors du chargement du planning:', err);
        if (err.status === 403) {
          this.toastService.showError("Accès refusé : vous n'avez pas les droits pour accéder à ce planning");
        } else if (err.status === 404) {
          this.toastService.showError("Le planning demandé n'existe pas ou a été supprimé");
        } else {
          const errorMessage = err.error?.message || 'Erreur lors du chargement du planning';
          this.toastService.showError(errorMessage);
        }
      }
    });
  }
  onSubmit() {
    if (this.planningForm.invalid) {
      console.log('Formulaire invalide, soumission annulée');
      // Marquer tous les champs comme "touched" pour afficher les erreurs
      this.markFormGroupTouched();
      this.toastService.showWarning('Veuillez corriger les erreurs avant de soumettre le formulaire');
      return;
    }
    this.isLoading = true;
    const formValue = this.planningForm.value;
    console.log('Données du formulaire à soumettre:', formValue);
    // Vérifier que les dates sont au bon format
    let dateDebut = formValue.dateDebut;
    let dateFin = formValue.dateFin;
    // S'assurer que les dates sont des objets Date
    if (typeof dateDebut === 'string') {
      dateDebut = new Date(dateDebut);
    }
    if (typeof dateFin === 'string') {
      dateFin = new Date(dateFin);
    }
    // Créer un objet avec seulement les propriétés à mettre à jour
    // sans utiliser le type Planning complet pour éviter les erreurs de typage
    const updatedPlanning = {
      titre: formValue.titre,
      description: formValue.description || '',
      lieu: formValue.lieu || '',
      dateDebut: dateDebut,
      dateFin: dateFin,
      participants: formValue.participants || []
    };
    console.log('Mise à jour du planning avec ID:', this.planningId);
    console.log('Données formatées:', updatedPlanning);
    try {
      this.planningService.updatePlanning(this.planningId, updatedPlanning).subscribe({
        next: response => {
          console.log('Planning mis à jour avec succès:', response);
          this.isLoading = false;
          // Afficher un toast de succès
          this.toastService.showSuccess('Le planning a été modifié avec succès');
          // Redirection vers la page de détail du planning
          console.log('Redirection vers la page de détail du planning:', this.planningId);
          // Utiliser setTimeout pour s'assurer que la redirection se produit après le traitement
          setTimeout(() => {
            this.router.navigate(['/plannings', this.planningId]).then(navigated => console.log('Redirection réussie:', navigated), err => console.error('Erreur de redirection:', err));
          }, 100);
        },
        error: err => {
          this.isLoading = false;
          console.error('Erreur lors de la mise à jour du planning:', err);
          // Gestion spécifique des erreurs d'autorisation
          if (err.status === 403) {
            this.toastService.showError("Accès refusé : vous n'avez pas les droits pour modifier ce planning");
          } else if (err.status === 401) {
            this.toastService.showError('Vous devez être connecté pour effectuer cette action');
          } else {
            // Autres erreurs
            const errorMessage = err.error?.message || 'Erreur lors de la mise à jour du planning';
            this.toastService.showError(errorMessage, 8000);
          }
          // Afficher plus de détails sur l'erreur dans la console
          if (err.error) {
            console.error("Détails de l'erreur:", err.error);
          }
        }
      });
    } catch (e) {
      this.isLoading = false;
      const errorMessage = e instanceof Error ? e.message : String(e);
      this.toastService.showError(`Exception lors de la mise à jour: ${errorMessage}`);
      console.error('Exception lors de la mise à jour:', e);
    }
  }
  // Marquer tous les champs comme "touched" pour déclencher l'affichage des erreurs
  markFormGroupTouched() {
    Object.keys(this.planningForm.controls).forEach(key => {
      const control = this.planningForm.get(key);
      if (control) {
        control.markAsTouched();
      }
    });
  }
  static {
    this.ɵfac = function PlanningEditComponent_Factory(t) {
      return new (t || PlanningEditComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormBuilder), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_app_services_planning_service__WEBPACK_IMPORTED_MODULE_0__.PlanningService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_app_services_data_service__WEBPACK_IMPORTED_MODULE_1__.DataService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_5__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_5__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_app_services_toast_service__WEBPACK_IMPORTED_MODULE_2__.ToastService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineComponent"]({
      type: PlanningEditComponent,
      selectors: [["app-planning-edit"]],
      decls: 71,
      vars: 13,
      consts: [[1, "container", "mx-auto", "px-4", "py-6", "max-w-3xl"], [1, "bg-gradient-to-r", "from-purple-600", "to-indigo-600", "rounded-t-lg", "p-6", "text-white", "mb-0"], [1, "text-2xl", "font-bold", "flex", "items-center"], [1, "fas", "fa-edit", "mr-3", "text-purple-200"], [1, "text-purple-100", "mt-2"], ["novalidate", "", 1, "bg-white", "rounded-b-lg", "shadow-lg", "p-6", "border-t-0", 3, "formGroup", "ngSubmit"], ["class", "mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded", 4, "ngIf"], [1, "grid", "grid-cols-1", "gap-6"], [1, "bg-gradient-to-r", "from-purple-50", "to-pink-50", "p-4", "rounded-lg", "border", "border-purple-200"], [1, "text-lg", "font-semibold", "text-purple-800", "mb-4", "flex", "items-center"], [1, "fas", "fa-info-circle", "mr-2", "text-purple-600"], [1, "grid", "grid-cols-1", "md:grid-cols-2", "gap-6"], [1, "block", "text-sm", "font-medium", "text-purple-700", "mb-2"], [1, "fas", "fa-tag", "mr-2", "text-purple-500"], ["type", "text", "formControlName", "titre", "placeholder", "Nom de votre planning...", 1, "mt-1", "block", "w-full", "px-4", "py-3", "border-2", "border-purple-200", "rounded-lg", "shadow-sm", "focus:ring-purple-500", "focus:border-purple-500", "focus:ring-2", "transition-all", "duration-200"], ["class", "text-red-500 text-sm mt-2 flex items-center", 4, "ngIf"], [1, "block", "text-sm", "font-medium", "text-orange-700", "mb-2"], [1, "fas", "fa-map-marker-alt", "mr-2", "text-orange-500"], ["type", "text", "formControlName", "lieu", "placeholder", "Salle, bureau, lieu de l'\u00E9v\u00E9nement...", 1, "mt-1", "block", "w-full", "px-4", "py-3", "border-2", "border-orange-200", "rounded-lg", "shadow-sm", "focus:ring-orange-500", "focus:border-orange-500", "focus:ring-2", "transition-all", "duration-200"], [1, "bg-gradient-to-r", "from-blue-50", "to-cyan-50", "p-4", "rounded-lg", "border", "border-blue-200"], [1, "text-lg", "font-semibold", "text-blue-800", "mb-4", "flex", "items-center"], [1, "fas", "fa-calendar-week", "mr-2", "text-blue-600"], [1, "block", "text-sm", "font-medium", "text-green-700", "mb-2"], [1, "fas", "fa-calendar-day", "mr-2", "text-green-500"], ["type", "date", "formControlName", "dateDebut", 1, "mt-1", "block", "w-full", "px-4", "py-3", "border-2", "border-green-200", "rounded-lg", "shadow-sm", "focus:ring-green-500", "focus:border-green-500", "focus:ring-2", "transition-all", "duration-200"], [1, "block", "text-sm", "font-medium", "text-red-700", "mb-2"], [1, "fas", "fa-calendar-check", "mr-2", "text-red-500"], ["type", "date", "formControlName", "dateFin", 1, "mt-1", "block", "w-full", "px-4", "py-3", "border-2", "border-red-200", "rounded-lg", "shadow-sm", "focus:ring-red-500", "focus:border-red-500", "focus:ring-2", "transition-all", "duration-200"], [1, "bg-gradient-to-r", "from-indigo-50", "to-purple-50", "p-4", "rounded-lg", "border", "border-indigo-200"], [1, "text-lg", "font-semibold", "text-indigo-800", "mb-4", "flex", "items-center"], [1, "fas", "fa-align-left", "mr-2", "text-indigo-600"], [1, "block", "text-sm", "font-medium", "text-indigo-700", "mb-2"], [1, "fas", "fa-edit", "mr-2", "text-indigo-500"], ["formControlName", "description", "rows", "4", "placeholder", "D\u00E9crivez les objectifs, le contexte ou les d\u00E9tails de ce planning...", 1, "mt-1", "block", "w-full", "px-4", "py-3", "border-2", "border-indigo-200", "rounded-lg", "shadow-sm", "focus:ring-indigo-500", "focus:border-indigo-500", "focus:ring-2", "transition-all", "duration-200"], [1, "bg-gradient-to-r", "from-emerald-50", "to-teal-50", "p-4", "rounded-lg", "border", "border-emerald-200"], [1, "text-lg", "font-semibold", "text-emerald-800", "mb-4", "flex", "items-center"], [1, "fas", "fa-users", "mr-2", "text-emerald-600"], [1, "block", "text-sm", "font-medium", "text-emerald-700", "mb-2"], [1, "fas", "fa-user-friends", "mr-2", "text-emerald-500"], ["formControlName", "participants", "multiple", "", 1, "mt-1", "block", "w-full", "px-4", "py-3", "border-2", "border-emerald-200", "rounded-lg", "shadow-sm", "focus:ring-emerald-500", "focus:border-emerald-500", "focus:ring-2", "transition-all", "duration-200", "text-sm", "min-h-[120px]"], ["class", "py-2", 3, "value", 4, "ngFor", "ngForOf"], [1, "text-xs", "text-emerald-600", "mt-2"], [1, "fas", "fa-info-circle", "mr-1"], [1, "mt-8", "flex", "justify-end", "space-x-4", "bg-gray-50", "p-4", "rounded-lg", "border-t", "border-gray-200"], ["type", "button", "routerLink", "/plannings", 1, "px-6", "py-3", "border-2", "border-gray-300", "rounded-lg", "text-sm", "font-medium", "text-gray-700", "hover:bg-gray-100", "hover:border-gray-400", "transition-all", "duration-200", "flex", "items-center"], [1, "fas", "fa-times", "mr-2"], ["type", "button", 1, "px-6", "py-3", "rounded-lg", "text-sm", "font-medium", "text-white", "bg-gradient-to-r", "from-purple-600", "to-indigo-600", "hover:from-purple-700", "hover:to-indigo-700", "disabled:opacity-50", "disabled:cursor-not-allowed", "transition-all", "duration-200", "flex", "items-center", "shadow-lg", 3, "disabled", "click"], ["class", "fas fa-save mr-2", 4, "ngIf"], ["class", "fas fa-spinner fa-spin mr-2", 4, "ngIf"], [1, "mb-4", "bg-red-100", "border", "border-red-400", "text-red-700", "px-4", "py-3", "rounded"], [1, "text-red-500", "text-sm", "mt-2", "flex", "items-center"], [1, "fas", "fa-exclamation-circle", "mr-1"], [4, "ngIf"], [1, "py-2", 3, "value"], [1, "fas", "fa-save", "mr-2"], [1, "fas", "fa-spinner", "fa-spin", "mr-2"]],
      template: function PlanningEditComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "h1", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](3, "i", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](4, " Modifier le Planning ");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](5, "p", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](6, "Modifiez les d\u00E9tails de votre planning");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](7, "form", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("ngSubmit", function PlanningEditComponent_Template_form_ngSubmit_7_listener() {
            return ctx.onSubmit();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](8, PlanningEditComponent_div_8_Template, 2, 1, "div", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](9, "div", 7)(10, "div", 8)(11, "h3", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](12, "i", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](13, " Informations g\u00E9n\u00E9rales ");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](14, "div", 11)(15, "div")(16, "label", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](17, "i", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](18, " Titre * ");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](19, "input", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](20, PlanningEditComponent_div_20_Template, 4, 2, "div", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](21, "div")(22, "label", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](23, "i", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](24, " Lieu / Salle ");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](25, "input", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](26, "div", 19)(27, "h3", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](28, "i", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](29, " P\u00E9riode du planning ");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](30, "div", 11)(31, "div")(32, "label", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](33, "i", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](34, " Date de d\u00E9but * ");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](35, "input", 24);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](36, "div")(37, "label", 25);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](38, "i", 26);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](39, " Date de fin * ");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](40, "input", 27);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](41, "div", 28)(42, "h3", 29);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](43, "i", 30);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](44, " Description ");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](45, "label", 31);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](46, "i", 32);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](47, " D\u00E9crivez votre planning ");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](48, "textarea", 33);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](49, "div", 34)(50, "h3", 35);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](51, "i", 36);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](52, " Participants ");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](53, "label", 37);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](54, "i", 38);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](55, " S\u00E9lectionnez les participants * ");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](56, "select", 39);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](57, PlanningEditComponent_option_57_Template, 2, 2, "option", 40);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](58, "async");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](59, PlanningEditComponent_div_59_Template, 3, 0, "div", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](60, "p", 41);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](61, "i", 42);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](62, " Maintenez Ctrl (ou Cmd) pour s\u00E9lectionner plusieurs participants ");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](63, "div", 43)(64, "button", 44);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](65, "i", 45);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](66, " Annuler ");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](67, "button", 46);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function PlanningEditComponent_Template_button_click_67_listener() {
            return ctx.onSubmit();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](68, PlanningEditComponent_i_68_Template, 1, 0, "i", 47);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](69, PlanningEditComponent_i_69_Template, 1, 0, "i", 48);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](70);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()();
        }
        if (rf & 2) {
          let tmp_2_0;
          let tmp_3_0;
          let tmp_5_0;
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](7);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("formGroup", ctx.planningForm);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.error);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](11);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵclassProp"]("border-red-300", ((tmp_2_0 = ctx.planningForm.get("titre")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.planningForm.get("titre")) == null ? null : tmp_2_0.touched));
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ((tmp_3_0 = ctx.planningForm.get("titre")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.planningForm.get("titre")) == null ? null : tmp_3_0.touched));
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](37);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngForOf", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind1"](58, 11, ctx.users$));
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ((tmp_5_0 = ctx.planningForm.get("participants")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.planningForm.get("participants")) == null ? null : tmp_5_0.touched));
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](8);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("disabled", ctx.isLoading || ctx.planningForm.invalid);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx.isLoading);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.isLoading);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", ctx.isLoading ? "Enregistrement..." : "Enregistrer les modifications", " ");
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_6__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_6__.NgIf, _angular_router__WEBPACK_IMPORTED_MODULE_5__.RouterLink, _angular_forms__WEBPACK_IMPORTED_MODULE_4__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_4__.NgSelectOption, _angular_forms__WEBPACK_IMPORTED_MODULE_4__["ɵNgSelectMultipleOption"], _angular_forms__WEBPACK_IMPORTED_MODULE_4__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.SelectMultipleControlValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControlName, _angular_common__WEBPACK_IMPORTED_MODULE_6__.AsyncPipe],
      styles: ["/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJwbGFubmluZy1lZGl0LmNvbXBvbmVudC5jc3MifQ== */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvcGxhbm5pbmdzL3BsYW5uaW5nLWVkaXQvcGxhbm5pbmctZWRpdC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSw0S0FBNEsiLCJzb3VyY2VSb290IjoiIn0= */"]
    });
  }
}

/***/ })

}]);
//# sourceMappingURL=default-src_app_views_front_plannings_planning-edit_planning-edit_component_ts.js.map