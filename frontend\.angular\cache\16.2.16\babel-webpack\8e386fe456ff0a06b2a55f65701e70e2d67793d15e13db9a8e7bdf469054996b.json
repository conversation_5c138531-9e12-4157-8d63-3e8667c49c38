{"ast": null, "code": "import { CommonModule, DatePipe } from '@angular/common';\nimport { PlanningsRoutingModule } from './plannings-routing.module';\nimport { PlanningListComponent } from './planning-list/planning-list.component';\nimport { PlanningDetailComponent } from './planning-detail/planning-detail.component';\nimport { PlanningFormComponent } from './planning-form/planning-form.component';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { PlanningEditComponent } from './planning-edit/planning-edit.component';\nimport { PipesModule } from '../../../pipes/pipes.module';\nimport * as i0 from \"@angular/core\";\nexport class PlanningsModule {\n  static {\n    this.ɵfac = function PlanningsModule_Factory(t) {\n      return new (t || PlanningsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: PlanningsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [DatePipe],\n      imports: [CommonModule, PlanningsRoutingModule, FormsModule, ReactiveFormsModule, PipesModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(PlanningsModule, {\n    declarations: [PlanningListComponent, PlanningDetailComponent, PlanningFormComponent, PlanningEditComponent],\n    imports: [CommonModule, PlanningsRoutingModule, FormsModule, ReactiveFormsModule, PipesModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "DatePipe", "PlanningsRoutingModule", "PlanningListComponent", "PlanningDetailComponent", "PlanningFormComponent", "FormsModule", "ReactiveFormsModule", "PlanningEditComponent", "PipesModule", "PlanningsModule", "imports", "declarations"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\plannings\\plannings.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule, DatePipe } from '@angular/common';\n\nimport { PlanningsRoutingModule } from './plannings-routing.module';\nimport { PlanningListComponent } from './planning-list/planning-list.component';\nimport { PlanningDetailComponent } from './planning-detail/planning-detail.component';\nimport { PlanningFormComponent } from './planning-form/planning-form.component';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { PlanningEditComponent } from './planning-edit/planning-edit.component';\n\nimport { PipesModule } from '../../../pipes/pipes.module';\nimport { CalendarModule } from 'angular-calendar';\n\n@NgModule({\n  declarations: [\n    PlanningListComponent,\n    PlanningDetailComponent,\n    PlanningFormComponent,\n    PlanningEditComponent,\n  ],\n  imports: [\n    CommonModule,\n    PlanningsRoutingModule,\n    FormsModule,\n    ReactiveFormsModule,\n    PipesModule,\n  ],\n  providers: [DatePipe],\n})\nexport class PlanningsModule {}\n"], "mappings": "AACA,SAASA,YAAY,EAAEC,QAAQ,QAAQ,iBAAiB;AAExD,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,qBAAqB,QAAQ,yCAAyC;AAE/E,SAASC,WAAW,QAAQ,6BAA6B;;AAmBzD,OAAM,MAAOC,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA;IAAe;EAAA;;;iBAFf,CAACT,QAAQ,CAAC;MAAAU,OAAA,GANnBX,YAAY,EACZE,sBAAsB,EACtBI,WAAW,EACXC,mBAAmB,EACnBE,WAAW;IAAA;EAAA;;;2EAIFC,eAAe;IAAAE,YAAA,GAdxBT,qBAAqB,EACrBC,uBAAuB,EACvBC,qBAAqB,EACrBG,qBAAqB;IAAAG,OAAA,GAGrBX,YAAY,EACZE,sBAAsB,EACtBI,WAAW,EACXC,mBAAmB,EACnBE,WAAW;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}