{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { HighlightPresencePipe } from '../shared/pipes/highlight-presence.pipe';\nimport { TimeAgoPipe } from '../shared/pipes/time-ago.pipe';\nimport * as i0 from \"@angular/core\";\nexport class PipesModule {\n  static {\n    this.ɵfac = function PipesModule_Factory(t) {\n      return new (t || PipesModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: PipesModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(PipesModule, {\n    declarations: [HighlightPresencePipe, TimeAgoPipe],\n    imports: [CommonModule],\n    exports: [HighlightPresencePipe, TimeAgoPipe]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "HighlightPresencePipe", "TimeAgoPipe", "PipesModule", "declarations", "imports", "exports"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\pipes\\pipes.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { HighlightPresencePipe } from '../shared/pipes/highlight-presence.pipe';\nimport { TimeAgoPipe } from '../shared/pipes/time-ago.pipe';\n\n@NgModule({\n  declarations: [\n    HighlightPresencePipe,\n    TimeAgoPipe\n  ],\n  imports: [\n    CommonModule\n  ],\n  exports: [\n    HighlightPresencePipe,\n    TimeAgoPipe\n  ]\n})\nexport class PipesModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,WAAW,QAAQ,+BAA+B;;AAe3D,OAAM,MAAOC,WAAW;;;uBAAXA,WAAW;IAAA;EAAA;;;YAAXA;IAAW;EAAA;;;gBAPpBH,YAAY;IAAA;EAAA;;;2EAOHG,WAAW;IAAAC,YAAA,GAXpBH,qBAAqB,EACrBC,WAAW;IAAAG,OAAA,GAGXL,YAAY;IAAAM,OAAA,GAGZL,qBAAqB,EACrBC,WAAW;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}