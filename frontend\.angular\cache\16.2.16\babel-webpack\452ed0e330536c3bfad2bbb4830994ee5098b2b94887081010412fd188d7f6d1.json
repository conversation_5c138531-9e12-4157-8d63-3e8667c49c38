{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { interval } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/message.service\";\nimport * as i2 from \"../../services/mock-data.service\";\nimport * as i3 from \"../../services/theme.service\";\nimport * as i4 from \"../../services/toast.service\";\nimport * as i5 from \"@angular/common\";\nexport class SystemStatusComponent {\n  constructor(messageService, mockDataService, themeService, toastService) {\n    this.messageService = messageService;\n    this.mockDataService = mockDataService;\n    this.themeService = themeService;\n    this.toastService = toastService;\n    this.status = {\n      backend: 'checking',\n      frontend: 'online',\n      database: 'checking',\n      websocket: 'checking',\n      mockData: 'checking',\n      theme: 'Chargement...',\n      lastCheck: new Date()\n    };\n    this.isChecking = false;\n  }\n  ngOnInit() {\n    this.checkSystemStatus();\n    // Auto-refresh every 30 seconds\n    this.subscription = interval(30000).subscribe(() => {\n      this.checkSystemStatus();\n    });\n    // Listen to theme changes\n    this.themeService.currentTheme$.subscribe(theme => {\n      this.status.theme = theme.displayName;\n    });\n  }\n  ngOnDestroy() {\n    this.subscription?.unsubscribe();\n  }\n  checkSystemStatus() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.isChecking = true;\n      _this.status.lastCheck = new Date();\n      // Check mock data availability\n      try {\n        yield _this.mockDataService.getUsers().toPromise();\n        _this.status.mockData = 'available';\n      } catch {\n        _this.status.mockData = 'unavailable';\n      }\n      // Check backend connectivity\n      try {\n        yield _this.messageService.getConversations().toPromise();\n        _this.status.backend = 'online';\n        _this.status.database = 'online';\n        _this.status.websocket = 'online';\n      } catch {\n        _this.status.backend = 'offline';\n        _this.status.database = 'offline';\n        _this.status.websocket = 'offline';\n      }\n      _this.isChecking = false;\n    })();\n  }\n  getStatusText(status) {\n    switch (status) {\n      case 'online':\n        return 'En ligne';\n      case 'offline':\n        return 'Hors ligne';\n      case 'checking':\n        return 'Vérification...';\n      default:\n        return 'Inconnu';\n    }\n  }\n  testMockData() {\n    this.mockDataService.getUsers().subscribe({\n      next: users => {\n        this.toastService.showSuccess(`${users.length} utilisateurs de test chargés`);\n      },\n      error: () => {\n        this.toastService.showError('Erreur lors du chargement des données de test');\n      }\n    });\n  }\n  testThemes() {\n    const themes = this.themeService.getAvailableThemes();\n    this.toastService.showInfo(`${themes.length} thèmes disponibles`);\n  }\n  testNotifications() {\n    this.toastService.showSuccess('Test de notification réussi !');\n    setTimeout(() => {\n      this.toastService.showInfo('Notification d\\'information');\n    }, 1000);\n    setTimeout(() => {\n      this.toastService.showWarning('Notification d\\'avertissement');\n    }, 2000);\n  }\n  static {\n    this.ɵfac = function SystemStatusComponent_Factory(t) {\n      return new (t || SystemStatusComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.MockDataService), i0.ɵɵdirectiveInject(i3.ThemeService), i0.ɵɵdirectiveInject(i4.ToastService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SystemStatusComponent,\n      selectors: [[\"app-system-status\"]],\n      decls: 68,\n      vars: 79,\n      consts: [[1, \"system-status-panel\", \"bg-gray-800\", \"rounded-lg\", \"p-6\", \"border\", \"border-gray-700\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-4\"], [1, \"text-lg\", \"font-semibold\", \"text-white\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-heartbeat\", \"text-blue-400\", \"mr-2\"], [1, \"px-3\", \"py-1\", \"bg-blue-600\", \"hover:bg-blue-700\", \"text-white\", \"rounded\", \"text-sm\", \"transition-colors\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-sync-alt\", \"mr-1\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-4\"], [1, \"status-item\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"text-gray-300\"], [1, \"flex\", \"items-center\"], [1, \"w-3\", \"h-3\", \"rounded-full\", \"mr-2\"], [1, \"text-sm\"], [1, \"mt-4\", \"p-3\", \"bg-gray-700\", \"rounded\", \"border-l-4\"], [1, \"text-xs\", \"text-gray-400\", \"mt-1\"], [1, \"mt-4\", \"p-3\", \"bg-gray-700\", \"rounded\"], [1, \"text-sm\", \"text-blue-400\"], [1, \"mt-4\", \"text-xs\", \"text-gray-500\", \"text-center\"], [1, \"mt-6\", \"grid\", \"grid-cols-1\", \"md:grid-cols-3\", \"gap-2\"], [1, \"px-3\", \"py-2\", \"bg-green-600\", \"hover:bg-green-700\", \"text-white\", \"rounded\", \"text-sm\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-database\", \"mr-1\"], [1, \"px-3\", \"py-2\", \"bg-purple-600\", \"hover:bg-purple-700\", \"text-white\", \"rounded\", \"text-sm\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-palette\", \"mr-1\"], [1, \"px-3\", \"py-2\", \"bg-orange-600\", \"hover:bg-orange-700\", \"text-white\", \"rounded\", \"text-sm\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-bell\", \"mr-1\"]],\n      template: function SystemStatusComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h3\", 2);\n          i0.ɵɵelement(3, \"i\", 3);\n          i0.ɵɵtext(4, \" \\u00C9tat du syst\\u00E8me \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function SystemStatusComponent_Template_button_click_5_listener() {\n            return ctx.checkSystemStatus();\n          });\n          i0.ɵɵelement(6, \"i\", 5);\n          i0.ɵɵtext(7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 6)(9, \"div\", 7)(10, \"div\", 8)(11, \"span\", 9);\n          i0.ɵɵtext(12, \"Backend\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 10);\n          i0.ɵɵelement(14, \"div\", 11);\n          i0.ɵɵelementStart(15, \"span\", 12);\n          i0.ɵɵtext(16);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8)(19, \"span\", 9);\n          i0.ɵɵtext(20, \"Frontend\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\", 10);\n          i0.ɵɵelement(22, \"div\", 11);\n          i0.ɵɵelementStart(23, \"span\", 12);\n          i0.ɵɵtext(24);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(25, \"div\", 7)(26, \"div\", 8)(27, \"span\", 9);\n          i0.ɵɵtext(28, \"Base de donn\\u00E9es\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"div\", 10);\n          i0.ɵɵelement(30, \"div\", 11);\n          i0.ɵɵelementStart(31, \"span\", 12);\n          i0.ɵɵtext(32);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(33, \"div\", 7)(34, \"div\", 8)(35, \"span\", 9);\n          i0.ɵɵtext(36, \"WebSocket\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"div\", 10);\n          i0.ɵɵelement(38, \"div\", 11);\n          i0.ɵɵelementStart(39, \"span\", 12);\n          i0.ɵɵtext(40);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(41, \"div\", 13)(42, \"div\", 8)(43, \"span\", 9);\n          i0.ɵɵtext(44, \"Donn\\u00E9es de test\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"span\", 12);\n          i0.ɵɵtext(46);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(47, \"p\", 14);\n          i0.ɵɵtext(48);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(49, \"div\", 15)(50, \"div\", 8)(51, \"span\", 9);\n          i0.ɵɵtext(52, \"Th\\u00E8me actuel\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"span\", 16);\n          i0.ɵɵtext(54);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(55, \"div\", 17);\n          i0.ɵɵtext(56);\n          i0.ɵɵpipe(57, \"date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"div\", 18)(59, \"button\", 19);\n          i0.ɵɵlistener(\"click\", function SystemStatusComponent_Template_button_click_59_listener() {\n            return ctx.testMockData();\n          });\n          i0.ɵɵelement(60, \"i\", 20);\n          i0.ɵɵtext(61, \" Test donn\\u00E9es \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function SystemStatusComponent_Template_button_click_62_listener() {\n            return ctx.testThemes();\n          });\n          i0.ɵɵelement(63, \"i\", 22);\n          i0.ɵɵtext(64, \" Test th\\u00E8mes \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function SystemStatusComponent_Template_button_click_65_listener() {\n            return ctx.testNotifications();\n          });\n          i0.ɵɵelement(66, \"i\", 24);\n          i0.ɵɵtext(67, \" Test notifs \");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", ctx.isChecking);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"fa-spin\", ctx.isChecking);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isChecking ? \"V\\u00E9rification...\" : \"Actualiser\", \" \");\n          i0.ɵɵadvance(7);\n          i0.ɵɵclassProp(\"bg-green-500\", ctx.status.backend === \"online\")(\"bg-red-500\", ctx.status.backend === \"offline\")(\"bg-yellow-500\", ctx.status.backend === \"checking\")(\"animate-pulse\", ctx.status.backend === \"checking\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"text-green-400\", ctx.status.backend === \"online\")(\"text-red-400\", ctx.status.backend === \"offline\")(\"text-yellow-400\", ctx.status.backend === \"checking\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.getStatusText(ctx.status.backend), \" \");\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"bg-green-500\", ctx.status.frontend === \"online\")(\"bg-red-500\", ctx.status.frontend === \"offline\")(\"bg-yellow-500\", ctx.status.frontend === \"checking\")(\"animate-pulse\", ctx.status.frontend === \"checking\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"text-green-400\", ctx.status.frontend === \"online\")(\"text-red-400\", ctx.status.frontend === \"offline\")(\"text-yellow-400\", ctx.status.frontend === \"checking\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.getStatusText(ctx.status.frontend), \" \");\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"bg-green-500\", ctx.status.database === \"online\")(\"bg-red-500\", ctx.status.database === \"offline\")(\"bg-yellow-500\", ctx.status.database === \"checking\")(\"animate-pulse\", ctx.status.database === \"checking\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"text-green-400\", ctx.status.database === \"online\")(\"text-red-400\", ctx.status.database === \"offline\")(\"text-yellow-400\", ctx.status.database === \"checking\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.getStatusText(ctx.status.database), \" \");\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"bg-green-500\", ctx.status.websocket === \"online\")(\"bg-red-500\", ctx.status.websocket === \"offline\")(\"bg-yellow-500\", ctx.status.websocket === \"checking\")(\"animate-pulse\", ctx.status.websocket === \"checking\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"text-green-400\", ctx.status.websocket === \"online\")(\"text-red-400\", ctx.status.websocket === \"offline\")(\"text-yellow-400\", ctx.status.websocket === \"checking\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.getStatusText(ctx.status.websocket), \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"border-green-500\", ctx.status.mockData === \"available\")(\"border-red-500\", ctx.status.mockData === \"unavailable\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"text-green-400\", ctx.status.mockData === \"available\")(\"text-red-400\", ctx.status.mockData === \"unavailable\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.status.mockData === \"available\" ? \"Disponibles\" : \"Indisponibles\", \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.status.mockData === \"available\" ? \"Le mode d\\u00E9mo est actif avec des donn\\u00E9es de test\" : \"Aucune donn\\u00E9e de test disponible\", \" \");\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.status.theme);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" Derni\\u00E8re v\\u00E9rification : \", i0.ɵɵpipeBind2(57, 76, ctx.status.lastCheck, \"medium\"), \" \");\n        }\n      },\n      dependencies: [i5.DatePipe],\n      styles: [\".status-item[_ngcontent-%COMP%] {\\n    \\n    border-radius: 0.25rem;\\n    \\n    --tw-bg-opacity: 1;\\n    \\n    background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n    \\n    padding: 0.75rem\\n}\\n    \\n    .status-item[_ngcontent-%COMP%]:hover {\\n    \\n    --tw-bg-opacity: 1;\\n    \\n    background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1))\\n}\\n  \\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInN5c3RlbS1zdGF0dXMuY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7SUFFTTs7SUFBQSxzQkFBOEI7O0lBQTlCLGtCQUE4Qjs7SUFBOUIseURBQThCOztJQUE5QjtBQUE4Qjs7SUFJOUI7O0lBQUEsa0JBQWtCOztJQUFsQjtBQUFrQiIsImZpbGUiOiJzeXN0ZW0tc3RhdHVzLmNvbXBvbmVudC50cyIsInNvdXJjZXNDb250ZW50IjpbIlxuICAgIC5zdGF0dXMtaXRlbSB7XG4gICAgICBAYXBwbHkgcC0zIGJnLWdyYXktNzAwIHJvdW5kZWQ7XG4gICAgfVxuICAgIFxuICAgIC5zdGF0dXMtaXRlbTpob3ZlciB7XG4gICAgICBAYXBwbHkgYmctZ3JheS02MDA7XG4gICAgfVxuICAiXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29tcG9uZW50cy9zeXN0ZW0tc3RhdHVzL3N5c3RlbS1zdGF0dXMuY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7SUFFTTs7SUFBQSxzQkFBOEI7O0lBQTlCLGtCQUE4Qjs7SUFBOUIseURBQThCOztJQUE5QjtBQUE4Qjs7SUFJOUI7O0lBQUEsa0JBQWtCOztJQUFsQjtBQUFrQjs7QUFheEIsNGhCQUE0aEIiLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAuc3RhdHVzLWl0ZW0ge1xuICAgICAgQGFwcGx5IHAtMyBiZy1ncmF5LTcwMCByb3VuZGVkO1xuICAgIH1cbiAgICBcbiAgICAuc3RhdHVzLWl0ZW06aG92ZXIge1xuICAgICAgQGFwcGx5IGJnLWdyYXktNjAwO1xuICAgIH1cbiAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["interval", "SystemStatusComponent", "constructor", "messageService", "mockDataService", "themeService", "toastService", "status", "backend", "frontend", "database", "websocket", "mockData", "theme", "<PERSON><PERSON><PERSON><PERSON>", "Date", "isChecking", "ngOnInit", "checkSystemStatus", "subscription", "subscribe", "currentTheme$", "displayName", "ngOnDestroy", "unsubscribe", "_this", "_asyncToGenerator", "getUsers", "to<PERSON>romise", "getConversations", "getStatusText", "testMockData", "next", "users", "showSuccess", "length", "error", "showError", "testThemes", "themes", "getAvailableThemes", "showInfo", "testNotifications", "setTimeout", "showWarning", "i0", "ɵɵdirectiveInject", "i1", "MessageService", "i2", "MockDataService", "i3", "ThemeService", "i4", "ToastService", "selectors", "decls", "vars", "consts", "template", "SystemStatusComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "SystemStatusComponent_Template_button_click_5_listener", "SystemStatusComponent_Template_button_click_59_listener", "SystemStatusComponent_Template_button_click_62_listener", "SystemStatusComponent_Template_button_click_65_listener", "ɵɵadvance", "ɵɵproperty", "ɵɵclassProp", "ɵɵtextInterpolate1", "ɵɵtextInterpolate", "ɵɵpipeBind2"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\components\\system-status\\system-status.component.ts"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { Subscription, interval } from 'rxjs';\nimport { MessageService } from '../../services/message.service';\nimport { MockDataService } from '../../services/mock-data.service';\nimport { ThemeService } from '../../services/theme.service';\nimport { ToastService } from '../../services/toast.service';\n\ninterface SystemStatus {\n  backend: 'online' | 'offline' | 'checking';\n  frontend: 'online' | 'offline' | 'checking';\n  database: 'online' | 'offline' | 'checking';\n  websocket: 'online' | 'offline' | 'checking';\n  mockData: 'available' | 'unavailable';\n  theme: string;\n  lastCheck: Date;\n}\n\n@Component({\n  selector: 'app-system-status',\n  template: `\n    <div class=\"system-status-panel bg-gray-800 rounded-lg p-6 border border-gray-700\">\n      <div class=\"flex items-center justify-between mb-4\">\n        <h3 class=\"text-lg font-semibold text-white flex items-center\">\n          <i class=\"fas fa-heartbeat text-blue-400 mr-2\"></i>\n          État du système\n        </h3>\n        <button \n          (click)=\"checkSystemStatus()\"\n          class=\"px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors\"\n          [disabled]=\"isChecking\"\n        >\n          <i class=\"fas fa-sync-alt mr-1\" [class.fa-spin]=\"isChecking\"></i>\n          {{ isChecking ? 'Vérification...' : 'Actualiser' }}\n        </button>\n      </div>\n\n      <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n        <!-- Backend Status -->\n        <div class=\"status-item\">\n          <div class=\"flex items-center justify-between\">\n            <span class=\"text-gray-300\">Backend</span>\n            <div class=\"flex items-center\">\n              <div \n                class=\"w-3 h-3 rounded-full mr-2\"\n                [class.bg-green-500]=\"status.backend === 'online'\"\n                [class.bg-red-500]=\"status.backend === 'offline'\"\n                [class.bg-yellow-500]=\"status.backend === 'checking'\"\n                [class.animate-pulse]=\"status.backend === 'checking'\"\n              ></div>\n              <span class=\"text-sm\" [class.text-green-400]=\"status.backend === 'online'\"\n                    [class.text-red-400]=\"status.backend === 'offline'\"\n                    [class.text-yellow-400]=\"status.backend === 'checking'\">\n                {{ getStatusText(status.backend) }}\n              </span>\n            </div>\n          </div>\n        </div>\n\n        <!-- Frontend Status -->\n        <div class=\"status-item\">\n          <div class=\"flex items-center justify-between\">\n            <span class=\"text-gray-300\">Frontend</span>\n            <div class=\"flex items-center\">\n              <div \n                class=\"w-3 h-3 rounded-full mr-2\"\n                [class.bg-green-500]=\"status.frontend === 'online'\"\n                [class.bg-red-500]=\"status.frontend === 'offline'\"\n                [class.bg-yellow-500]=\"status.frontend === 'checking'\"\n                [class.animate-pulse]=\"status.frontend === 'checking'\"\n              ></div>\n              <span class=\"text-sm\" [class.text-green-400]=\"status.frontend === 'online'\"\n                    [class.text-red-400]=\"status.frontend === 'offline'\"\n                    [class.text-yellow-400]=\"status.frontend === 'checking'\">\n                {{ getStatusText(status.frontend) }}\n              </span>\n            </div>\n          </div>\n        </div>\n\n        <!-- Database Status -->\n        <div class=\"status-item\">\n          <div class=\"flex items-center justify-between\">\n            <span class=\"text-gray-300\">Base de données</span>\n            <div class=\"flex items-center\">\n              <div \n                class=\"w-3 h-3 rounded-full mr-2\"\n                [class.bg-green-500]=\"status.database === 'online'\"\n                [class.bg-red-500]=\"status.database === 'offline'\"\n                [class.bg-yellow-500]=\"status.database === 'checking'\"\n                [class.animate-pulse]=\"status.database === 'checking'\"\n              ></div>\n              <span class=\"text-sm\" [class.text-green-400]=\"status.database === 'online'\"\n                    [class.text-red-400]=\"status.database === 'offline'\"\n                    [class.text-yellow-400]=\"status.database === 'checking'\">\n                {{ getStatusText(status.database) }}\n              </span>\n            </div>\n          </div>\n        </div>\n\n        <!-- WebSocket Status -->\n        <div class=\"status-item\">\n          <div class=\"flex items-center justify-between\">\n            <span class=\"text-gray-300\">WebSocket</span>\n            <div class=\"flex items-center\">\n              <div \n                class=\"w-3 h-3 rounded-full mr-2\"\n                [class.bg-green-500]=\"status.websocket === 'online'\"\n                [class.bg-red-500]=\"status.websocket === 'offline'\"\n                [class.bg-yellow-500]=\"status.websocket === 'checking'\"\n                [class.animate-pulse]=\"status.websocket === 'checking'\"\n              ></div>\n              <span class=\"text-sm\" [class.text-green-400]=\"status.websocket === 'online'\"\n                    [class.text-red-400]=\"status.websocket === 'offline'\"\n                    [class.text-yellow-400]=\"status.websocket === 'checking'\">\n                {{ getStatusText(status.websocket) }}\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Mock Data Status -->\n      <div class=\"mt-4 p-3 bg-gray-700 rounded border-l-4\" \n           [class.border-green-500]=\"status.mockData === 'available'\"\n           [class.border-red-500]=\"status.mockData === 'unavailable'\">\n        <div class=\"flex items-center justify-between\">\n          <span class=\"text-gray-300\">Données de test</span>\n          <span class=\"text-sm\" [class.text-green-400]=\"status.mockData === 'available'\"\n                [class.text-red-400]=\"status.mockData === 'unavailable'\">\n            {{ status.mockData === 'available' ? 'Disponibles' : 'Indisponibles' }}\n          </span>\n        </div>\n        <p class=\"text-xs text-gray-400 mt-1\">\n          {{ status.mockData === 'available' ? \n             'Le mode démo est actif avec des données de test' : \n             'Aucune donnée de test disponible' }}\n        </p>\n      </div>\n\n      <!-- Theme Status -->\n      <div class=\"mt-4 p-3 bg-gray-700 rounded\">\n        <div class=\"flex items-center justify-between\">\n          <span class=\"text-gray-300\">Thème actuel</span>\n          <span class=\"text-sm text-blue-400\">{{ status.theme }}</span>\n        </div>\n      </div>\n\n      <!-- Last Check -->\n      <div class=\"mt-4 text-xs text-gray-500 text-center\">\n        Dernière vérification : {{ status.lastCheck | date:'medium' }}\n      </div>\n\n      <!-- Test Actions -->\n      <div class=\"mt-6 grid grid-cols-1 md:grid-cols-3 gap-2\">\n        <button \n          (click)=\"testMockData()\"\n          class=\"px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded text-sm transition-colors\"\n        >\n          <i class=\"fas fa-database mr-1\"></i>\n          Test données\n        </button>\n        <button \n          (click)=\"testThemes()\"\n          class=\"px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded text-sm transition-colors\"\n        >\n          <i class=\"fas fa-palette mr-1\"></i>\n          Test thèmes\n        </button>\n        <button \n          (click)=\"testNotifications()\"\n          class=\"px-3 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded text-sm transition-colors\"\n        >\n          <i class=\"fas fa-bell mr-1\"></i>\n          Test notifs\n        </button>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .status-item {\n      @apply p-3 bg-gray-700 rounded;\n    }\n    \n    .status-item:hover {\n      @apply bg-gray-600;\n    }\n  `]\n})\nexport class SystemStatusComponent implements OnInit, OnDestroy {\n  status: SystemStatus = {\n    backend: 'checking',\n    frontend: 'online', // Frontend is obviously online if this component is running\n    database: 'checking',\n    websocket: 'checking',\n    mockData: 'checking' as any,\n    theme: 'Chargement...',\n    lastCheck: new Date()\n  };\n\n  isChecking = false;\n  private subscription?: Subscription;\n\n  constructor(\n    private messageService: MessageService,\n    private mockDataService: MockDataService,\n    private themeService: ThemeService,\n    private toastService: ToastService\n  ) {}\n\n  ngOnInit(): void {\n    this.checkSystemStatus();\n    \n    // Auto-refresh every 30 seconds\n    this.subscription = interval(30000).subscribe(() => {\n      this.checkSystemStatus();\n    });\n\n    // Listen to theme changes\n    this.themeService.currentTheme$.subscribe(theme => {\n      this.status.theme = theme.displayName;\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.subscription?.unsubscribe();\n  }\n\n  async checkSystemStatus(): Promise<void> {\n    this.isChecking = true;\n    this.status.lastCheck = new Date();\n\n    // Check mock data availability\n    try {\n      await this.mockDataService.getUsers().toPromise();\n      this.status.mockData = 'available';\n    } catch {\n      this.status.mockData = 'unavailable';\n    }\n\n    // Check backend connectivity\n    try {\n      await this.messageService.getConversations().toPromise();\n      this.status.backend = 'online';\n      this.status.database = 'online';\n      this.status.websocket = 'online';\n    } catch {\n      this.status.backend = 'offline';\n      this.status.database = 'offline';\n      this.status.websocket = 'offline';\n    }\n\n    this.isChecking = false;\n  }\n\n  getStatusText(status: string): string {\n    switch (status) {\n      case 'online': return 'En ligne';\n      case 'offline': return 'Hors ligne';\n      case 'checking': return 'Vérification...';\n      default: return 'Inconnu';\n    }\n  }\n\n  testMockData(): void {\n    this.mockDataService.getUsers().subscribe({\n      next: (users) => {\n        this.toastService.showSuccess(`${users.length} utilisateurs de test chargés`);\n      },\n      error: () => {\n        this.toastService.showError('Erreur lors du chargement des données de test');\n      }\n    });\n  }\n\n  testThemes(): void {\n    const themes = this.themeService.getAvailableThemes();\n    this.toastService.showInfo(`${themes.length} thèmes disponibles`);\n  }\n\n  testNotifications(): void {\n    this.toastService.showSuccess('Test de notification réussi !');\n    setTimeout(() => {\n      this.toastService.showInfo('Notification d\\'information');\n    }, 1000);\n    setTimeout(() => {\n      this.toastService.showWarning('Notification d\\'avertissement');\n    }, 2000);\n  }\n}\n"], "mappings": ";AACA,SAAuBA,QAAQ,QAAQ,MAAM;;;;;;;AA4L7C,OAAM,MAAOC,qBAAqB;EAchCC,YACUC,cAA8B,EAC9BC,eAAgC,EAChCC,YAA0B,EAC1BC,YAA0B;IAH1B,KAAAH,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,YAAY,GAAZA,YAAY;IAjBtB,KAAAC,MAAM,GAAiB;MACrBC,OAAO,EAAE,UAAU;MACnBC,QAAQ,EAAE,QAAQ;MAClBC,QAAQ,EAAE,UAAU;MACpBC,SAAS,EAAE,UAAU;MACrBC,QAAQ,EAAE,UAAiB;MAC3BC,KAAK,EAAE,eAAe;MACtBC,SAAS,EAAE,IAAIC,IAAI;KACpB;IAED,KAAAC,UAAU,GAAG,KAAK;EAQf;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACC,YAAY,GAAGnB,QAAQ,CAAC,KAAK,CAAC,CAACoB,SAAS,CAAC,MAAK;MACjD,IAAI,CAACF,iBAAiB,EAAE;IAC1B,CAAC,CAAC;IAEF;IACA,IAAI,CAACb,YAAY,CAACgB,aAAa,CAACD,SAAS,CAACP,KAAK,IAAG;MAChD,IAAI,CAACN,MAAM,CAACM,KAAK,GAAGA,KAAK,CAACS,WAAW;IACvC,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACJ,YAAY,EAAEK,WAAW,EAAE;EAClC;EAEMN,iBAAiBA,CAAA;IAAA,IAAAO,KAAA;IAAA,OAAAC,iBAAA;MACrBD,KAAI,CAACT,UAAU,GAAG,IAAI;MACtBS,KAAI,CAAClB,MAAM,CAACO,SAAS,GAAG,IAAIC,IAAI,EAAE;MAElC;MACA,IAAI;QACF,MAAMU,KAAI,CAACrB,eAAe,CAACuB,QAAQ,EAAE,CAACC,SAAS,EAAE;QACjDH,KAAI,CAAClB,MAAM,CAACK,QAAQ,GAAG,WAAW;OACnC,CAAC,MAAM;QACNa,KAAI,CAAClB,MAAM,CAACK,QAAQ,GAAG,aAAa;;MAGtC;MACA,IAAI;QACF,MAAMa,KAAI,CAACtB,cAAc,CAAC0B,gBAAgB,EAAE,CAACD,SAAS,EAAE;QACxDH,KAAI,CAAClB,MAAM,CAACC,OAAO,GAAG,QAAQ;QAC9BiB,KAAI,CAAClB,MAAM,CAACG,QAAQ,GAAG,QAAQ;QAC/Be,KAAI,CAAClB,MAAM,CAACI,SAAS,GAAG,QAAQ;OACjC,CAAC,MAAM;QACNc,KAAI,CAAClB,MAAM,CAACC,OAAO,GAAG,SAAS;QAC/BiB,KAAI,CAAClB,MAAM,CAACG,QAAQ,GAAG,SAAS;QAChCe,KAAI,CAAClB,MAAM,CAACI,SAAS,GAAG,SAAS;;MAGnCc,KAAI,CAACT,UAAU,GAAG,KAAK;IAAC;EAC1B;EAEAc,aAAaA,CAACvB,MAAc;IAC1B,QAAQA,MAAM;MACZ,KAAK,QAAQ;QAAE,OAAO,UAAU;MAChC,KAAK,SAAS;QAAE,OAAO,YAAY;MACnC,KAAK,UAAU;QAAE,OAAO,iBAAiB;MACzC;QAAS,OAAO,SAAS;;EAE7B;EAEAwB,YAAYA,CAAA;IACV,IAAI,CAAC3B,eAAe,CAACuB,QAAQ,EAAE,CAACP,SAAS,CAAC;MACxCY,IAAI,EAAGC,KAAK,IAAI;QACd,IAAI,CAAC3B,YAAY,CAAC4B,WAAW,CAAC,GAAGD,KAAK,CAACE,MAAM,+BAA+B,CAAC;MAC/E,CAAC;MACDC,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAC9B,YAAY,CAAC+B,SAAS,CAAC,+CAA+C,CAAC;MAC9E;KACD,CAAC;EACJ;EAEAC,UAAUA,CAAA;IACR,MAAMC,MAAM,GAAG,IAAI,CAAClC,YAAY,CAACmC,kBAAkB,EAAE;IACrD,IAAI,CAAClC,YAAY,CAACmC,QAAQ,CAAC,GAAGF,MAAM,CAACJ,MAAM,qBAAqB,CAAC;EACnE;EAEAO,iBAAiBA,CAAA;IACf,IAAI,CAACpC,YAAY,CAAC4B,WAAW,CAAC,+BAA+B,CAAC;IAC9DS,UAAU,CAAC,MAAK;MACd,IAAI,CAACrC,YAAY,CAACmC,QAAQ,CAAC,6BAA6B,CAAC;IAC3D,CAAC,EAAE,IAAI,CAAC;IACRE,UAAU,CAAC,MAAK;MACd,IAAI,CAACrC,YAAY,CAACsC,WAAW,CAAC,+BAA+B,CAAC;IAChE,CAAC,EAAE,IAAI,CAAC;EACV;;;uBAnGW3C,qBAAqB,EAAA4C,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,YAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAArBrD,qBAAqB;MAAAsD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAzK9BhB,EAAA,CAAAkB,cAAA,aAAmF;UAG7ElB,EAAA,CAAAmB,SAAA,WAAmD;UACnDnB,EAAA,CAAAoB,MAAA,kCACF;UAAApB,EAAA,CAAAqB,YAAA,EAAK;UACLrB,EAAA,CAAAkB,cAAA,gBAIC;UAHClB,EAAA,CAAAsB,UAAA,mBAAAC,uDAAA;YAAA,OAASN,GAAA,CAAA5C,iBAAA,EAAmB;UAAA,EAAC;UAI7B2B,EAAA,CAAAmB,SAAA,WAAiE;UACjEnB,EAAA,CAAAoB,MAAA,GACF;UAAApB,EAAA,CAAAqB,YAAA,EAAS;UAGXrB,EAAA,CAAAkB,cAAA,aAAmD;UAIjBlB,EAAA,CAAAoB,MAAA,eAAO;UAAApB,EAAA,CAAAqB,YAAA,EAAO;UAC1CrB,EAAA,CAAAkB,cAAA,eAA+B;UAC7BlB,EAAA,CAAAmB,SAAA,eAMO;UACPnB,EAAA,CAAAkB,cAAA,gBAE8D;UAC5DlB,EAAA,CAAAoB,MAAA,IACF;UAAApB,EAAA,CAAAqB,YAAA,EAAO;UAMbrB,EAAA,CAAAkB,cAAA,cAAyB;UAEOlB,EAAA,CAAAoB,MAAA,gBAAQ;UAAApB,EAAA,CAAAqB,YAAA,EAAO;UAC3CrB,EAAA,CAAAkB,cAAA,eAA+B;UAC7BlB,EAAA,CAAAmB,SAAA,eAMO;UACPnB,EAAA,CAAAkB,cAAA,gBAE+D;UAC7DlB,EAAA,CAAAoB,MAAA,IACF;UAAApB,EAAA,CAAAqB,YAAA,EAAO;UAMbrB,EAAA,CAAAkB,cAAA,cAAyB;UAEOlB,EAAA,CAAAoB,MAAA,4BAAe;UAAApB,EAAA,CAAAqB,YAAA,EAAO;UAClDrB,EAAA,CAAAkB,cAAA,eAA+B;UAC7BlB,EAAA,CAAAmB,SAAA,eAMO;UACPnB,EAAA,CAAAkB,cAAA,gBAE+D;UAC7DlB,EAAA,CAAAoB,MAAA,IACF;UAAApB,EAAA,CAAAqB,YAAA,EAAO;UAMbrB,EAAA,CAAAkB,cAAA,cAAyB;UAEOlB,EAAA,CAAAoB,MAAA,iBAAS;UAAApB,EAAA,CAAAqB,YAAA,EAAO;UAC5CrB,EAAA,CAAAkB,cAAA,eAA+B;UAC7BlB,EAAA,CAAAmB,SAAA,eAMO;UACPnB,EAAA,CAAAkB,cAAA,gBAEgE;UAC9DlB,EAAA,CAAAoB,MAAA,IACF;UAAApB,EAAA,CAAAqB,YAAA,EAAO;UAOfrB,EAAA,CAAAkB,cAAA,eAEgE;UAEhClB,EAAA,CAAAoB,MAAA,4BAAe;UAAApB,EAAA,CAAAqB,YAAA,EAAO;UAClDrB,EAAA,CAAAkB,cAAA,gBAC+D;UAC7DlB,EAAA,CAAAoB,MAAA,IACF;UAAApB,EAAA,CAAAqB,YAAA,EAAO;UAETrB,EAAA,CAAAkB,cAAA,aAAsC;UACpClB,EAAA,CAAAoB,MAAA,IAGF;UAAApB,EAAA,CAAAqB,YAAA,EAAI;UAINrB,EAAA,CAAAkB,cAAA,eAA0C;UAEVlB,EAAA,CAAAoB,MAAA,yBAAY;UAAApB,EAAA,CAAAqB,YAAA,EAAO;UAC/CrB,EAAA,CAAAkB,cAAA,gBAAoC;UAAAlB,EAAA,CAAAoB,MAAA,IAAkB;UAAApB,EAAA,CAAAqB,YAAA,EAAO;UAKjErB,EAAA,CAAAkB,cAAA,eAAoD;UAClDlB,EAAA,CAAAoB,MAAA,IACF;;UAAApB,EAAA,CAAAqB,YAAA,EAAM;UAGNrB,EAAA,CAAAkB,cAAA,eAAwD;UAEpDlB,EAAA,CAAAsB,UAAA,mBAAAE,wDAAA;YAAA,OAASP,GAAA,CAAA/B,YAAA,EAAc;UAAA,EAAC;UAGxBc,EAAA,CAAAmB,SAAA,aAAoC;UACpCnB,EAAA,CAAAoB,MAAA,2BACF;UAAApB,EAAA,CAAAqB,YAAA,EAAS;UACTrB,EAAA,CAAAkB,cAAA,kBAGC;UAFClB,EAAA,CAAAsB,UAAA,mBAAAG,wDAAA;YAAA,OAASR,GAAA,CAAAxB,UAAA,EAAY;UAAA,EAAC;UAGtBO,EAAA,CAAAmB,SAAA,aAAmC;UACnCnB,EAAA,CAAAoB,MAAA,0BACF;UAAApB,EAAA,CAAAqB,YAAA,EAAS;UACTrB,EAAA,CAAAkB,cAAA,kBAGC;UAFClB,EAAA,CAAAsB,UAAA,mBAAAI,wDAAA;YAAA,OAAST,GAAA,CAAApB,iBAAA,EAAmB;UAAA,EAAC;UAG7BG,EAAA,CAAAmB,SAAA,aAAgC;UAChCnB,EAAA,CAAAoB,MAAA,qBACF;UAAApB,EAAA,CAAAqB,YAAA,EAAS;;;UAlJPrB,EAAA,CAAA2B,SAAA,GAAuB;UAAvB3B,EAAA,CAAA4B,UAAA,aAAAX,GAAA,CAAA9C,UAAA,CAAuB;UAES6B,EAAA,CAAA2B,SAAA,GAA4B;UAA5B3B,EAAA,CAAA6B,WAAA,YAAAZ,GAAA,CAAA9C,UAAA,CAA4B;UAC5D6B,EAAA,CAAA2B,SAAA,GACF;UADE3B,EAAA,CAAA8B,kBAAA,MAAAb,GAAA,CAAA9C,UAAA,8CACF;UAWQ6B,EAAA,CAAA2B,SAAA,GAAkD;UAAlD3B,EAAA,CAAA6B,WAAA,iBAAAZ,GAAA,CAAAvD,MAAA,CAAAC,OAAA,cAAkD,eAAAsD,GAAA,CAAAvD,MAAA,CAAAC,OAAA,iCAAAsD,GAAA,CAAAvD,MAAA,CAAAC,OAAA,kCAAAsD,GAAA,CAAAvD,MAAA,CAAAC,OAAA;UAK9BqC,EAAA,CAAA2B,SAAA,GAAoD;UAApD3B,EAAA,CAAA6B,WAAA,mBAAAZ,GAAA,CAAAvD,MAAA,CAAAC,OAAA,cAAoD,iBAAAsD,GAAA,CAAAvD,MAAA,CAAAC,OAAA,mCAAAsD,GAAA,CAAAvD,MAAA,CAAAC,OAAA;UAGxEqC,EAAA,CAAA2B,SAAA,GACF;UADE3B,EAAA,CAAA8B,kBAAA,MAAAb,GAAA,CAAAhC,aAAA,CAAAgC,GAAA,CAAAvD,MAAA,CAAAC,OAAA,OACF;UAYEqC,EAAA,CAAA2B,SAAA,GAAmD;UAAnD3B,EAAA,CAAA6B,WAAA,iBAAAZ,GAAA,CAAAvD,MAAA,CAAAE,QAAA,cAAmD,eAAAqD,GAAA,CAAAvD,MAAA,CAAAE,QAAA,iCAAAqD,GAAA,CAAAvD,MAAA,CAAAE,QAAA,kCAAAqD,GAAA,CAAAvD,MAAA,CAAAE,QAAA;UAK/BoC,EAAA,CAAA2B,SAAA,GAAqD;UAArD3B,EAAA,CAAA6B,WAAA,mBAAAZ,GAAA,CAAAvD,MAAA,CAAAE,QAAA,cAAqD,iBAAAqD,GAAA,CAAAvD,MAAA,CAAAE,QAAA,mCAAAqD,GAAA,CAAAvD,MAAA,CAAAE,QAAA;UAGzEoC,EAAA,CAAA2B,SAAA,GACF;UADE3B,EAAA,CAAA8B,kBAAA,MAAAb,GAAA,CAAAhC,aAAA,CAAAgC,GAAA,CAAAvD,MAAA,CAAAE,QAAA,OACF;UAYEoC,EAAA,CAAA2B,SAAA,GAAmD;UAAnD3B,EAAA,CAAA6B,WAAA,iBAAAZ,GAAA,CAAAvD,MAAA,CAAAG,QAAA,cAAmD,eAAAoD,GAAA,CAAAvD,MAAA,CAAAG,QAAA,iCAAAoD,GAAA,CAAAvD,MAAA,CAAAG,QAAA,kCAAAoD,GAAA,CAAAvD,MAAA,CAAAG,QAAA;UAK/BmC,EAAA,CAAA2B,SAAA,GAAqD;UAArD3B,EAAA,CAAA6B,WAAA,mBAAAZ,GAAA,CAAAvD,MAAA,CAAAG,QAAA,cAAqD,iBAAAoD,GAAA,CAAAvD,MAAA,CAAAG,QAAA,mCAAAoD,GAAA,CAAAvD,MAAA,CAAAG,QAAA;UAGzEmC,EAAA,CAAA2B,SAAA,GACF;UADE3B,EAAA,CAAA8B,kBAAA,MAAAb,GAAA,CAAAhC,aAAA,CAAAgC,GAAA,CAAAvD,MAAA,CAAAG,QAAA,OACF;UAYEmC,EAAA,CAAA2B,SAAA,GAAoD;UAApD3B,EAAA,CAAA6B,WAAA,iBAAAZ,GAAA,CAAAvD,MAAA,CAAAI,SAAA,cAAoD,eAAAmD,GAAA,CAAAvD,MAAA,CAAAI,SAAA,iCAAAmD,GAAA,CAAAvD,MAAA,CAAAI,SAAA,kCAAAmD,GAAA,CAAAvD,MAAA,CAAAI,SAAA;UAKhCkC,EAAA,CAAA2B,SAAA,GAAsD;UAAtD3B,EAAA,CAAA6B,WAAA,mBAAAZ,GAAA,CAAAvD,MAAA,CAAAI,SAAA,cAAsD,iBAAAmD,GAAA,CAAAvD,MAAA,CAAAI,SAAA,mCAAAmD,GAAA,CAAAvD,MAAA,CAAAI,SAAA;UAG1EkC,EAAA,CAAA2B,SAAA,GACF;UADE3B,EAAA,CAAA8B,kBAAA,MAAAb,GAAA,CAAAhC,aAAA,CAAAgC,GAAA,CAAAvD,MAAA,CAAAI,SAAA,OACF;UAQHkC,EAAA,CAAA2B,SAAA,GAA0D;UAA1D3B,EAAA,CAAA6B,WAAA,qBAAAZ,GAAA,CAAAvD,MAAA,CAAAK,QAAA,iBAA0D,mBAAAkD,GAAA,CAAAvD,MAAA,CAAAK,QAAA;UAIrCiC,EAAA,CAAA2B,SAAA,GAAwD;UAAxD3B,EAAA,CAAA6B,WAAA,mBAAAZ,GAAA,CAAAvD,MAAA,CAAAK,QAAA,iBAAwD,iBAAAkD,GAAA,CAAAvD,MAAA,CAAAK,QAAA;UAE5EiC,EAAA,CAAA2B,SAAA,GACF;UADE3B,EAAA,CAAA8B,kBAAA,MAAAb,GAAA,CAAAvD,MAAA,CAAAK,QAAA,wDACF;UAGAiC,EAAA,CAAA2B,SAAA,GAGF;UAHE3B,EAAA,CAAA8B,kBAAA,MAAAb,GAAA,CAAAvD,MAAA,CAAAK,QAAA,8HAGF;UAOsCiC,EAAA,CAAA2B,SAAA,GAAkB;UAAlB3B,EAAA,CAAA+B,iBAAA,CAAAd,GAAA,CAAAvD,MAAA,CAAAM,KAAA,CAAkB;UAMxDgC,EAAA,CAAA2B,SAAA,GACF;UADE3B,EAAA,CAAA8B,kBAAA,wCAAA9B,EAAA,CAAAgC,WAAA,SAAAf,GAAA,CAAAvD,MAAA,CAAAO,SAAA,iBACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}