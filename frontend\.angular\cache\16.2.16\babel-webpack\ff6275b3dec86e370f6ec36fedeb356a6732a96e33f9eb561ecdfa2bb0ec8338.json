{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { PlanningsRoutingModule } from './plannings-routing.module';\nimport { PlanningListComponent } from './planning-list/planning-list.component';\nimport { PlanningDetailComponent } from './planning-detail/planning-detail.component';\nimport { PlanningFormComponent } from './planning-form/planning-form.component';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { PlanningEditComponent } from './planning-edit/planning-edit.component';\nimport { CalendarModule, DateAdapter } from 'angular-calendar';\nimport { adapterFactory } from 'angular-calendar/date-adapters/date-fns';\nimport { PipesModule } from '../../../pipes/pipes.module';\nexport let PlanningsModule = class PlanningsModule {};\nPlanningsModule = __decorate([NgModule({\n  declarations: [PlanningListComponent, PlanningDetailComponent, PlanningFormComponent, PlanningEditComponent],\n  imports: [CommonModule, PlanningsRoutingModule, FormsModule, ReactiveFormsModule, CalendarModule.forRoot({\n    provide: DateAdapter,\n    useFactory: adapterFactory\n  }), PipesModule]\n})], PlanningsModule);", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}