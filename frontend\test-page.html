<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DevBridge Messages - Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        }
        .neon-glow {
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
        }
        .animate-pulse-slow {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body class="bg-gray-900 text-white">
    <div class="min-h-screen flex">
        <!-- Sidebar -->
        <div class="w-80 bg-gray-800 border-r border-gray-700 flex flex-col">
            <!-- Header -->
            <div class="p-4 border-b border-gray-700 bg-gray-800">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 gradient-bg rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-white"></i>
                        </div>
                        <div>
                            <h2 class="font-semibold text-white">Utilisateur Test</h2>
                            <p class="text-sm text-green-400">En ligne</p>
                        </div>
                    </div>
                    
                    <!-- Theme Selector -->
                    <div class="relative">
                        <button id="themeBtn" class="p-2 rounded-lg bg-gray-700 hover:bg-gray-600 transition-colors">
                            <i class="fas fa-palette text-blue-400"></i>
                        </button>
                        
                        <div id="themeMenu" class="hidden absolute top-full right-0 mt-2 bg-gray-800 rounded-lg shadow-lg border border-gray-700 p-2 z-50 min-w-48">
                            <div class="text-xs text-gray-400 mb-2 px-2">Choisir un thème</div>
                            <div class="theme-option flex items-center space-x-3 p-2 hover:bg-gray-700 rounded cursor-pointer transition-colors" data-theme="dark">
                                <div class="w-4 h-4 rounded-full border-2 border-white" style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);"></div>
                                <span class="text-white text-sm">Sombre</span>
                                <i class="fas fa-check text-blue-400 text-xs ml-auto"></i>
                            </div>
                            <div class="theme-option flex items-center space-x-3 p-2 hover:bg-gray-700 rounded cursor-pointer transition-colors" data-theme="neon">
                                <div class="w-4 h-4 rounded-full border-2 border-gray-500" style="background: linear-gradient(135deg, #00ffff 0%, #0080ff 100%);"></div>
                                <span class="text-white text-sm">Néon</span>
                            </div>
                            <div class="theme-option flex items-center space-x-3 p-2 hover:bg-gray-700 rounded cursor-pointer transition-colors" data-theme="purple">
                                <div class="w-4 h-4 rounded-full border-2 border-gray-500" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);"></div>
                                <span class="text-white text-sm">Violet</span>
                            </div>
                            <div class="theme-option flex items-center space-x-3 p-2 hover:bg-gray-700 rounded cursor-pointer transition-colors" data-theme="ocean">
                                <div class="w-4 h-4 rounded-full border-2 border-gray-500" style="background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);"></div>
                                <span class="text-white text-sm">Océan</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search -->
            <div class="p-4">
                <div class="relative">
                    <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    <input type="text" placeholder="Rechercher..." class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 pl-10 text-white placeholder-gray-400 focus:outline-none focus:border-blue-500">
                </div>
            </div>

            <!-- Tabs -->
            <div class="flex border-b border-gray-700">
                <button class="tab-btn flex-1 py-3 px-4 text-center transition-all duration-200 cursor-pointer text-blue-400 border-b-2 border-blue-500 bg-blue-900" data-tab="conversations">
                    <i class="fas fa-comments block text-lg mb-1"></i>
                    <span class="text-xs">Discussions</span>
                </button>
                <button class="tab-btn flex-1 py-3 px-4 text-center transition-all duration-200 cursor-pointer text-gray-400 hover:bg-gray-700" data-tab="users">
                    <i class="fas fa-users block text-lg mb-1"></i>
                    <span class="text-xs">Contacts</span>
                </button>
                <button class="tab-btn flex-1 py-3 px-4 text-center transition-all duration-200 cursor-pointer text-gray-400 hover:bg-gray-700" data-tab="notifications">
                    <i class="fas fa-bell block text-lg mb-1"></i>
                    <span class="text-xs">Notifications</span>
                    <div class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">3</div>
                </button>
            </div>

            <!-- Content -->
            <div class="flex-1 overflow-y-auto">
                <!-- Conversations Tab -->
                <div id="conversations-tab" class="tab-content">
                    <div class="conversation-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-all duration-200 bg-gray-700 border-l-4 border-blue-500">
                        <div class="flex items-center space-x-3">
                            <div class="relative">
                                <img src="https://via.placeholder.com/48/4F46E5/FFFFFF?text=A" class="w-12 h-12 rounded-full">
                                <div class="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-gray-800 animate-pulse-slow"></div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between">
                                    <h4 class="font-medium text-white truncate">Alice Martin</h4>
                                    <span class="text-xs text-gray-400">14:30</span>
                                </div>
                                <p class="text-sm text-gray-400 truncate">Super ! Tu as vu le nouveau design ?</p>
                            </div>
                            <div class="bg-blue-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center neon-glow">1</div>
                        </div>
                    </div>

                    <div class="conversation-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-all duration-200">
                        <div class="flex items-center space-x-3">
                            <div class="relative">
                                <img src="https://via.placeholder.com/48/10B981/FFFFFF?text=C" class="w-12 h-12 rounded-full">
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between">
                                    <h4 class="font-medium text-white truncate">Claire Rousseau</h4>
                                    <span class="text-xs text-gray-400">12:00</span>
                                </div>
                                <p class="text-sm text-gray-400 truncate">Réunion à 14h ?</p>
                            </div>
                        </div>
                    </div>

                    <div class="conversation-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-all duration-200">
                        <div class="flex items-center space-x-3">
                            <div class="relative">
                                <img src="https://via.placeholder.com/48/8B5CF6/FFFFFF?text=E" class="w-12 h-12 rounded-full">
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between">
                                    <h4 class="font-medium text-white truncate">Équipe DevBridge</h4>
                                    <span class="text-xs text-gray-400">09:15</span>
                                </div>
                                <p class="text-sm text-gray-400 truncate">Nouveau projet lancé ! 🚀</p>
                            </div>
                            <div class="bg-blue-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">3</div>
                        </div>
                    </div>
                </div>

                <!-- Users Tab -->
                <div id="users-tab" class="tab-content hidden">
                    <div class="user-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-all duration-200">
                        <div class="flex items-center space-x-3">
                            <div class="relative">
                                <img src="https://via.placeholder.com/48/F59E0B/FFFFFF?text=B" class="w-12 h-12 rounded-full">
                                <div class="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-gray-800"></div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <h4 class="font-medium text-white truncate">Bob Dupont</h4>
                                <p class="text-sm text-gray-400 truncate"><EMAIL></p>
                            </div>
                            <div class="text-blue-400">
                                <i class="fas fa-comment"></i>
                            </div>
                        </div>
                    </div>

                    <div class="user-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-all duration-200">
                        <div class="flex items-center space-x-3">
                            <div class="relative">
                                <img src="https://via.placeholder.com/48/EF4444/FFFFFF?text=D" class="w-12 h-12 rounded-full">
                                <div class="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-gray-800"></div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <h4 class="font-medium text-white truncate">David Chen</h4>
                                <p class="text-sm text-gray-400 truncate"><EMAIL></p>
                            </div>
                            <div class="text-blue-400">
                                <i class="fas fa-comment"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notifications Tab -->
                <div id="notifications-tab" class="tab-content hidden">
                    <div class="notification-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-all duration-200 bg-gray-700">
                        <div class="flex items-start space-x-3">
                            <div class="notification-icon p-2 rounded-full bg-blue-600">
                                <i class="fas fa-message text-white text-sm"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <h4 class="font-medium text-white truncate">Nouveau message</h4>
                                <p class="text-sm text-gray-400 mt-1">Nouveau message de Bob Dupont</p>
                                <p class="text-xs text-gray-500 mt-2">Il y a 30 min</p>
                            </div>
                            <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col bg-gray-900">
            <!-- Welcome Message -->
            <div class="flex items-center justify-center h-full">
                <div class="text-center max-w-md mx-auto p-8">
                    <!-- Logo -->
                    <div class="mb-8">
                        <div class="w-24 h-24 mx-auto gradient-bg rounded-full flex items-center justify-center mb-4 neon-glow">
                            <i class="fas fa-comments text-3xl text-white"></i>
                        </div>
                        <h1 class="text-3xl font-bold text-white mb-2">DevBridge Messages</h1>
                        <p class="text-gray-400">Messagerie professionnelle en temps réel</p>
                    </div>

                    <!-- Features -->
                    <div class="space-y-4 mb-8">
                        <div class="flex items-center space-x-3 text-left">
                            <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-bolt text-white text-sm"></i>
                            </div>
                            <div>
                                <h4 class="text-white font-medium">Messages en temps réel</h4>
                                <p class="text-sm text-gray-400">Conversations instantanées avec notifications</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-3 text-left">
                            <div class="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-phone text-white text-sm"></i>
                            </div>
                            <div>
                                <h4 class="text-white font-medium">Appels audio/vidéo</h4>
                                <p class="text-sm text-gray-400">Communication directe intégrée</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-3 text-left">
                            <div class="w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-file text-white text-sm"></i>
                            </div>
                            <div>
                                <h4 class="text-white font-medium">Partage de fichiers</h4>
                                <p class="text-sm text-gray-400">Images, documents et médias</p>
                            </div>
                        </div>
                    </div>

                    <!-- Status Panel -->
                    <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
                        <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                            <i class="fas fa-heartbeat text-blue-400 mr-2"></i>
                            État du système
                        </h3>
                        
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-300">Frontend</span>
                                <div class="flex items-center">
                                    <div class="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                                    <span class="text-sm text-green-400">En ligne</span>
                                </div>
                            </div>
                            
                            <div class="flex items-center justify-between">
                                <span class="text-gray-300">Backend</span>
                                <div class="flex items-center">
                                    <div class="w-3 h-3 rounded-full bg-yellow-500 mr-2 animate-pulse"></div>
                                    <span class="text-sm text-yellow-400">Test...</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-green-900 border-l-4 border-green-500 p-3 rounded">
                            <p class="text-sm text-green-300">✅ Mode démo actif - Données de test disponibles</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Theme selector
        document.getElementById('themeBtn').addEventListener('click', function() {
            const menu = document.getElementById('themeMenu');
            menu.classList.toggle('hidden');
        });

        // Tab switching
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const tab = this.dataset.tab;
                
                // Update active tab
                document.querySelectorAll('.tab-btn').forEach(b => {
                    b.classList.remove('text-blue-400', 'border-b-2', 'border-blue-500', 'bg-blue-900');
                    b.classList.add('text-gray-400');
                });
                this.classList.add('text-blue-400', 'border-b-2', 'border-blue-500', 'bg-blue-900');
                this.classList.remove('text-gray-400');
                
                // Show content
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.add('hidden');
                });
                document.getElementById(tab + '-tab').classList.remove('hidden');
            });
        });

        // Theme switching
        document.querySelectorAll('.theme-option').forEach(option => {
            option.addEventListener('click', function() {
                const theme = this.dataset.theme;
                
                // Update active theme indicator
                document.querySelectorAll('.theme-option .fa-check').forEach(check => {
                    check.style.display = 'none';
                });
                document.querySelectorAll('.theme-option .border-white').forEach(border => {
                    border.classList.remove('border-white');
                    border.classList.add('border-gray-500');
                });
                
                this.querySelector('.fa-check').style.display = 'inline';
                this.querySelector('div').classList.add('border-white');
                this.querySelector('div').classList.remove('border-gray-500');
                
                // Hide menu
                document.getElementById('themeMenu').classList.add('hidden');
                
                // Show notification
                alert(`Thème "${this.querySelector('span').textContent}" appliqué !`);
            });
        });

        // Click outside to close theme menu
        document.addEventListener('click', function(e) {
            if (!e.target.closest('#themeBtn') && !e.target.closest('#themeMenu')) {
                document.getElementById('themeMenu').classList.add('hidden');
            }
        });

        console.log('🚀 DevBridge Messages - Page de test chargée');
        console.log('✅ Interface fonctionnelle');
        console.log('🎨 Thèmes disponibles');
        console.log('📱 Design responsive');
    </script>
</body>
</html>
