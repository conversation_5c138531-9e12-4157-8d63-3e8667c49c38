{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { PlanningsRoutingModule } from './plannings-routing.module';\nimport { PlanningListComponent } from './planning-list/planning-list.component';\nimport { PlanningDetailComponent } from './planning-detail/planning-detail.component';\nimport { PlanningFormComponent } from './planning-form/planning-form.component';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { PlanningEditComponent } from './planning-edit/planning-edit.component';\nimport { CalendarModule, DateAdapter } from 'angular-calendar';\nimport { adapterFactory } from 'angular-calendar/date-adapters/date-fns';\nimport { PipesModule } from '../../../pipes/pipes.module';\nexport let PlanningsModule = class PlanningsModule {};\nPlanningsModule = __decorate([NgModule({\n  declarations: [PlanningListComponent, PlanningDetailComponent, PlanningFormComponent, PlanningEditComponent],\n  imports: [CommonModule, PlanningsRoutingModule, FormsModule, ReactiveFormsModule, CalendarModule.forRoot({\n    provide: DateAdapter,\n    useFactory: adapterFactory\n  }), PipesModule]\n})], PlanningsModule);", "map": {"version": 3, "names": ["NgModule", "CommonModule", "PlanningsRoutingModule", "PlanningListComponent", "PlanningDetailComponent", "PlanningFormComponent", "FormsModule", "ReactiveFormsModule", "PlanningEditComponent", "CalendarModule", "DateAdapter", "adapterFactory", "PipesModule", "PlanningsModule", "__decorate", "declarations", "imports", "forRoot", "provide", "useFactory"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\plannings\\plannings.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\nimport { PlanningsRoutingModule } from './plannings-routing.module';\nimport { PlanningListComponent } from './planning-list/planning-list.component';\nimport { PlanningDetailComponent } from './planning-detail/planning-detail.component';\nimport { PlanningFormComponent } from './planning-form/planning-form.component';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { PlanningEditComponent } from './planning-edit/planning-edit.component';\nimport { CalendarModule, DateAdapter } from 'angular-calendar';\nimport { adapterFactory } from 'angular-calendar/date-adapters/date-fns';\nimport { PipesModule } from '../../../pipes/pipes.module';\n\n@NgModule({\n  declarations: [\n    PlanningListComponent,\n    PlanningDetailComponent,\n    PlanningFormComponent,\n    PlanningEditComponent,\n  ],\n  imports: [\n    CommonModule,\n    PlanningsRoutingModule,\n    FormsModule,\n    ReactiveFormsModule,\n    CalendarModule.forRoot({\n      provide: DateAdapter,\n      useFactory: adapterFactory,\n    }),\n    PipesModule,\n  ],\n})\nexport class PlanningsModule {}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,cAAc,EAAEC,WAAW,QAAQ,kBAAkB;AAC9D,SAASC,cAAc,QAAQ,yCAAyC;AACxE,SAASC,WAAW,QAAQ,6BAA6B;AAqBlD,WAAMC,eAAe,GAArB,MAAMA,eAAe,GAAG;AAAlBA,eAAe,GAAAC,UAAA,EAnB3Bd,QAAQ,CAAC;EACRe,YAAY,EAAE,CACZZ,qBAAqB,EACrBC,uBAAuB,EACvBC,qBAAqB,EACrBG,qBAAqB,CACtB;EACDQ,OAAO,EAAE,CACPf,YAAY,EACZC,sBAAsB,EACtBI,WAAW,EACXC,mBAAmB,EACnBE,cAAc,CAACQ,OAAO,CAAC;IACrBC,OAAO,EAAER,WAAW;IACpBS,UAAU,EAAER;GACb,CAAC,EACFC,WAAW;CAEd,CAAC,C,EACWC,eAAe,CAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}