import {
  Compo<PERSON>,
  On<PERSON>nit,
  <PERSON><PERSON><PERSON><PERSON>,
  AfterViewInit,
  ViewChild,
  ElementRef,
  ChangeDetectorRef,
  NgZone,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription, BehaviorSubject, combineLatest, of } from 'rxjs';
import {
  debounceTime,
  distinctUntilChanged,
  switchMap,
  catchError,
  tap,
  filter,
  map,
} from 'rxjs/operators';
import { MessageService } from '../../../../services/message.service';
import { AuthService } from '../../../../services/auth.service';
import { ToastService } from '../../../../services/toast.service';
import {
  Message,
  Conversation,
  User,
  MessageType,
  CallType,
  Attachment,
} from '../../../../models/message.model';

@Component({
  selector: 'app-message-chat',
  templateUrl: './message-chat.component.html',
  styleUrls: ['./message-chat.component.css'],
})
export class MessageChatComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('messagesContainer') messagesContainer!: ElementRef;
  @ViewChild('messageInput') messageInput!: ElementRef;
  @ViewChild('fileInput') fileInput!: ElementRef;
  @ViewChild('voiceRecorder') voiceRecorder!: ElementRef;

  // État du composant
  currentUser: User | null = null;
  selectedConversation: Conversation | null = null;
  messages: Message[] = [];
  isLoading = false;
  isTyping = false;
  typingUsers: User[] = [];

  // Pagination
  currentPage = 1;
  hasMoreMessages = true;
  loadingMoreMessages = false;

  // Formulaire de message
  messageContent = '';
  selectedFiles: File[] = [];
  isRecording = false;
  recordingDuration = 0;

  // États UI
  showEmojiPicker = false;
  showAttachmentMenu = false;
  replyingTo: Message | null = null;
  editingMessage: Message | null = null;

  // Recherche
  searchQuery = '';
  searchResults: Message[] = [];
  showSearchResults = false;

  // Subscriptions
  private subscriptions: Subscription[] = [];
  private typingTimeout: any;
  private recordingInterval: any;

  // Observables
  private conversationId$ = new BehaviorSubject<string | null>(null);

  // Constantes
  readonly MessageType = MessageType;
  readonly CallType = CallType;

  constructor(
    private messageService: MessageService,
    private authService: AuthService,
    private toastService: ToastService,
    private route: ActivatedRoute,
    private router: Router,
    private cdr: ChangeDetectorRef,
    private ngZone: NgZone
  ) {}

  ngOnInit(): void {
    this.initializeComponent();
    this.setupSubscriptions();
  }

  ngAfterViewInit(): void {
    this.scrollToBottom();
  }

  ngOnDestroy(): void {
    this.cleanup();
  }

  // ============================================================================
  // MÉTHODES D'INITIALISATION
  // ============================================================================

  private initializeComponent(): void {
    // Récupérer l'utilisateur actuel
    this.currentUser = this.authService.getCurrentUser();

    if (!this.currentUser) {
      this.router.navigate(['/login']);
      return;
    }

    // Écouter les changements de route pour la conversation
    this.route.params.subscribe((params) => {
      const conversationId = params['conversationId'];
      if (conversationId) {
        this.conversationId$.next(conversationId);
      }
    });
  }

  private setupSubscriptions(): void {
    // Subscription pour charger la conversation
    const conversationSub = this.conversationId$
      .pipe(
        filter((id) => !!id),
        distinctUntilChanged(),
        tap(() => {
          this.isLoading = true;
          this.messages = [];
          this.currentPage = 1;
          this.hasMoreMessages = true;
        }),
        switchMap((conversationId) =>
          this.messageService.getConversation(conversationId!, 25, 1)
        ),
        catchError((error) => {
          console.error('Erreur lors du chargement de la conversation:', error);
          this.toastService.showError(
            'Erreur lors du chargement de la conversation'
          );
          return of(null);
        })
      )
      .subscribe((conversation) => {
        this.isLoading = false;
        if (conversation) {
          this.selectedConversation = conversation;
          this.messages = conversation.messages || [];
          this.scrollToBottom();
          this.markMessagesAsRead();
        }
        this.cdr.detectChanges();
      });

    // Subscription pour les nouveaux messages
    const messagesSub = this.messageService
      .subscribeToMessages()
      .subscribe((message) => {
        if (
          message &&
          this.selectedConversation &&
          message.conversationId === this.selectedConversation.id
        ) {
          this.addNewMessage(message);
          this.scrollToBottom();
          this.markMessageAsRead(message);
        }
      });

    // Subscription pour les indicateurs de frappe
    const typingSub = this.messageService
      .subscribeToTypingIndicators()
      .subscribe((event) => {
        if (
          event &&
          this.selectedConversation &&
          event.conversationId === this.selectedConversation.id
        ) {
          this.handleTypingIndicator(event);
        }
      });

    this.subscriptions.push(conversationSub, messagesSub, typingSub);
  }

  private cleanup(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
    }
    if (this.recordingInterval) {
      clearInterval(this.recordingInterval);
    }
    this.stopTyping();
  }

  // ============================================================================
  // MÉTHODES DE GESTION DES MESSAGES
  // ============================================================================

  sendMessage(): void {
    if (!this.canSendMessage()) {
      return;
    }

    const content = this.messageContent.trim();
    const files = this.selectedFiles;

    // Réinitialiser le formulaire
    this.messageContent = '';
    this.selectedFiles = [];
    this.replyingTo = null;
    this.stopTyping();

    if (this.editingMessage) {
      this.updateMessage(content);
      return;
    }

    // Envoyer le message
    if (content || files.length > 0) {
      this.sendNewMessage(content, files);
    }
  }

  canSendMessage(): boolean {
    const hasContent = this.messageContent.trim().length > 0;
    const hasFiles = this.selectedFiles.length > 0;
    const hasConversation = !!this.selectedConversation;

    return hasConversation && (hasContent || hasFiles);
  }

  private sendNewMessage(content: string, files: File[]): void {
    if (!this.selectedConversation || !this.currentUser) return;

    const recipientId = this.getRecipientId();
    if (!recipientId) return;

    // Créer un message temporaire pour l'affichage immédiat
    const tempMessage: Message = {
      id: `temp-${Date.now()}`,
      content,
      type:
        files.length > 0 ? this.getFileMessageType(files[0]) : MessageType.TEXT,
      timestamp: new Date(),
      sender: this.currentUser,
      isPending: true,
      conversationId: this.selectedConversation.id,
    };

    this.addNewMessage(tempMessage);
    this.scrollToBottom();

    // Envoyer le message via le service
    const sendObservable =
      files.length > 0
        ? this.messageService.sendMessageWithFile(
            this.currentUser.id || this.currentUser._id!,
            recipientId,
            content,
            files[0]
          )
        : this.messageService.sendMessage(
            this.currentUser.id || this.currentUser._id!,
            recipientId,
            content
          );

    sendObservable.subscribe({
      next: (sentMessage) => {
        this.replaceTemporaryMessage(tempMessage.id!, sentMessage);
        this.toastService.showSuccess('Message envoyé');
      },
      error: (error) => {
        console.error("Erreur lors de l'envoi du message:", error);
        this.markMessageAsError(tempMessage.id!);
        this.toastService.showError("Erreur lors de l'envoi du message");
      },
    });
  }

  private updateMessage(newContent: string): void {
    if (!this.editingMessage) return;

    this.messageService
      .editMessage(this.editingMessage.id!, newContent)
      .subscribe({
        next: (updatedMessage) => {
          this.updateMessageInList(updatedMessage);
          this.editingMessage = null;
          this.toastService.showSuccess('Message modifié');
        },
        error: (error) => {
          console.error('Erreur lors de la modification du message:', error);
          this.toastService.showError(
            'Erreur lors de la modification du message'
          );
        },
      });
  }

  deleteMessage(message: Message): void {
    if (!message.id || !this.canDeleteMessage(message)) return;

    if (confirm('Êtes-vous sûr de vouloir supprimer ce message ?')) {
      this.messageService.deleteMessage(message.id).subscribe({
        next: () => {
          this.removeMessageFromList(message.id!);
          this.toastService.showSuccess('Message supprimé');
        },
        error: (error) => {
          console.error('Erreur lors de la suppression du message:', error);
          this.toastService.showError(
            'Erreur lors de la suppression du message'
          );
        },
      });
    }
  }

  reactToMessage(message: Message, emoji: string): void {
    if (!message.id) return;

    this.messageService.reactToMessage(message.id, emoji).subscribe({
      next: (updatedMessage) => {
        this.updateMessageInList(updatedMessage);
      },
      error: (error) => {
        console.error('Erreur lors de la réaction au message:', error);
        this.toastService.showError('Erreur lors de la réaction');
      },
    });
  }

  // ============================================================================
  // MÉTHODES DE GESTION DES FICHIERS ET MÉDIAS
  // ============================================================================

  onFileSelected(event: any): void {
    const files = event.target.files;
    if (files && files.length > 0) {
      this.selectedFiles = Array.from(files);
      this.showAttachmentMenu = false;

      // Auto-envoyer si c'est juste un fichier sans texte
      if (this.messageContent.trim() === '') {
        this.sendMessage();
      }
    }
  }

  removeSelectedFile(index: number): void {
    this.selectedFiles.splice(index, 1);
  }

  openFileSelector(): void {
    this.fileInput.nativeElement.click();
  }

  // ============================================================================
  // MÉTHODES D'ENREGISTREMENT VOCAL
  // ============================================================================

  async startVoiceRecording(): Promise<void> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      this.isRecording = true;
      this.recordingDuration = 0;

      // Démarrer le compteur de durée
      this.recordingInterval = setInterval(() => {
        this.recordingDuration++;
      }, 1000);

      // Ici, vous pouvez implémenter l'enregistrement audio
      // avec MediaRecorder API
    } catch (error) {
      console.error("Erreur lors de l'accès au microphone:", error);
      this.toastService.showError("Impossible d'accéder au microphone");
    }
  }

  stopVoiceRecording(): void {
    this.isRecording = false;
    if (this.recordingInterval) {
      clearInterval(this.recordingInterval);
    }

    // Ici, vous pouvez traiter l'enregistrement et l'envoyer
    // comme message vocal
  }

  cancelVoiceRecording(): void {
    this.isRecording = false;
    this.recordingDuration = 0;
    if (this.recordingInterval) {
      clearInterval(this.recordingInterval);
    }
  }

  // ============================================================================
  // MÉTHODES D'APPELS AUDIO/VIDÉO
  // ============================================================================

  startAudioCall(): void {
    if (!this.selectedConversation) return;

    const recipientId = this.getRecipientId();
    if (!recipientId) return;

    this.messageService.initiateCall(recipientId, CallType.AUDIO).subscribe({
      next: (call) => {
        this.toastService.showSuccess('Appel audio initié');
        // Rediriger vers l'interface d'appel
      },
      error: (error) => {
        console.error("Erreur lors de l'initiation de l'appel:", error);
        this.toastService.showError("Erreur lors de l'appel");
      },
    });
  }

  startVideoCall(): void {
    if (!this.selectedConversation) return;

    const recipientId = this.getRecipientId();
    if (!recipientId) return;

    this.messageService.initiateCall(recipientId, CallType.VIDEO).subscribe({
      next: (call) => {
        this.toastService.showSuccess('Appel vidéo initié');
        // Rediriger vers l'interface d'appel
      },
      error: (error) => {
        console.error("Erreur lors de l'initiation de l'appel vidéo:", error);
        this.toastService.showError("Erreur lors de l'appel vidéo");
      },
    });
  }

  // ============================================================================
  // MÉTHODES DE GESTION DE LA FRAPPE
  // ============================================================================

  onTyping(): void {
    if (!this.selectedConversation || this.isTyping) return;

    this.isTyping = true;
    this.messageService.startTyping(this.selectedConversation.id!).subscribe();

    // Arrêter la frappe après 3 secondes d'inactivité
    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
    }

    this.typingTimeout = setTimeout(() => {
      this.stopTyping();
    }, 3000);
  }

  stopTyping(): void {
    if (!this.isTyping || !this.selectedConversation) return;

    this.isTyping = false;
    this.messageService.stopTyping(this.selectedConversation.id!).subscribe();

    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
    }
  }

  // ============================================================================
  // MÉTHODES UTILITAIRES
  // ============================================================================

  private getRecipientId(): string | null {
    if (!this.selectedConversation || !this.currentUser) return null;

    const participants = this.selectedConversation.participants || [];
    const currentUserId = this.currentUser.id || this.currentUser._id;

    const recipient = participants.find(
      (p) => (p.id || p._id) !== currentUserId
    );

    return recipient ? recipient.id || recipient._id! : null;
  }

  private getFileMessageType(file: File): MessageType {
    const type = file.type.split('/')[0];
    switch (type) {
      case 'image':
        return MessageType.IMAGE;
      case 'video':
        return MessageType.VIDEO;
      case 'audio':
        return MessageType.AUDIO;
      default:
        return MessageType.FILE;
    }
  }

  private addNewMessage(message: Message): void {
    this.messages.push(message);
    this.cdr.detectChanges();
  }

  private replaceTemporaryMessage(tempId: string, realMessage: Message): void {
    const index = this.messages.findIndex((m) => m.id === tempId);
    if (index !== -1) {
      this.messages[index] = realMessage;
      this.cdr.detectChanges();
    }
  }

  private markMessageAsError(messageId: string): void {
    const message = this.messages.find((m) => m.id === messageId);
    if (message) {
      message.isPending = false;
      message.isError = true;
      this.cdr.detectChanges();
    }
  }

  private updateMessageInList(updatedMessage: Message): void {
    const index = this.messages.findIndex((m) => m.id === updatedMessage.id);
    if (index !== -1) {
      this.messages[index] = updatedMessage;
      this.cdr.detectChanges();
    }
  }

  private removeMessageFromList(messageId: string): void {
    this.messages = this.messages.filter((m) => m.id !== messageId);
    this.cdr.detectChanges();
  }

  private canDeleteMessage(message: Message): boolean {
    if (!this.currentUser || !message.sender) return false;

    const currentUserId = this.currentUser.id || this.currentUser._id;
    const senderId = message.sender.id || message.sender._id;

    return currentUserId === senderId;
  }

  private handleTypingIndicator(event: any): void {
    if (!this.currentUser) return;

    const currentUserId = this.currentUser.id || this.currentUser._id;

    if (event.userId === currentUserId) return; // Ignorer ses propres indicateurs

    if (event.isTyping) {
      // Ajouter l'utilisateur à la liste des utilisateurs en train de taper
      const user = this.selectedConversation?.participants?.find(
        (p) => (p.id || p._id) === event.userId
      );
      if (
        user &&
        !this.typingUsers.find((u) => (u.id || u._id) === event.userId)
      ) {
        this.typingUsers.push(user);
      }
    } else {
      // Retirer l'utilisateur de la liste
      this.typingUsers = this.typingUsers.filter(
        (u) => (u.id || u._id) !== event.userId
      );
    }

    this.cdr.detectChanges();
  }

  private markMessagesAsRead(): void {
    if (!this.messages.length || !this.currentUser) return;

    const unreadMessages = this.messages.filter(
      (m) =>
        !m.isRead &&
        m.sender &&
        (m.sender.id || m.sender._id) !==
          (this.currentUser!.id || this.currentUser!._id)
    );

    unreadMessages.forEach((message) => {
      if (message.id) {
        this.markMessageAsRead(message);
      }
    });
  }

  private markMessageAsRead(message: Message): void {
    if (!message.id || message.isRead) return;

    this.messageService.markMessageAsRead(message.id).subscribe({
      next: (updatedMessage) => {
        this.updateMessageInList(updatedMessage);
      },
      error: (error) => {
        console.error('Erreur lors du marquage comme lu:', error);
      },
    });
  }

  private scrollToBottom(): void {
    this.ngZone.runOutsideAngular(() => {
      setTimeout(() => {
        if (this.messagesContainer) {
          const element = this.messagesContainer.nativeElement;
          element.scrollTop = element.scrollHeight;
        }
      }, 100);
    });
  }

  // ============================================================================
  // MÉTHODES PUBLIQUES POUR LE TEMPLATE
  // ============================================================================

  formatMessageTime(timestamp: Date | string): string {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString('fr-FR', {
        hour: '2-digit',
        minute: '2-digit',
      });
    } else {
      return date.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
      });
    }
  }

  isMyMessage(message: Message): boolean {
    if (!this.currentUser || !message.sender) return false;

    const currentUserId = this.currentUser.id || this.currentUser._id;
    const senderId = message.sender.id || message.sender._id;

    return currentUserId === senderId;
  }

  getTypingText(): string {
    if (this.typingUsers.length === 0) return '';

    if (this.typingUsers.length === 1) {
      return `${this.typingUsers[0].username} est en train d'écrire...`;
    } else {
      return `${this.typingUsers.length} personnes sont en train d'écrire...`;
    }
  }

  onKeyPress(event: KeyboardEvent): void {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendMessage();
    } else {
      this.onTyping();
    }
  }

  toggleEmojiPicker(): void {
    this.showEmojiPicker = !this.showEmojiPicker;
  }

  toggleAttachmentMenu(): void {
    this.showAttachmentMenu = !this.showAttachmentMenu;
  }

  startEditingMessage(message: Message): void {
    this.editingMessage = message;
    this.messageContent = message.content || '';
    this.messageInput.nativeElement.focus();
  }

  cancelEditing(): void {
    this.editingMessage = null;
    this.messageContent = '';
  }

  setReplyTo(message: Message): void {
    this.replyingTo = message;
    this.messageInput.nativeElement.focus();
  }

  cancelReply(): void {
    this.replyingTo = null;
  }

  // ============================================================================
  // MÉTHODES POUR LE TEMPLATE (MANQUANTES)
  // ============================================================================

  getRecipientName(): string {
    if (!this.selectedConversation || !this.currentUser) return '';

    const participants = this.selectedConversation.participants || [];
    const currentUserId = this.currentUser.id || this.currentUser._id;

    const recipient = participants.find(
      (p) => (p.id || p._id) !== currentUserId
    );

    return recipient?.username || 'Utilisateur inconnu';
  }

  getRecipientAvatar(): string {
    if (!this.selectedConversation || !this.currentUser)
      return '/assets/images/default-avatar.png';

    const participants = this.selectedConversation.participants || [];
    const currentUserId = this.currentUser.id || this.currentUser._id;

    const recipient = participants.find(
      (p) => (p.id || p._id) !== currentUserId
    );

    return recipient?.image || '/assets/images/default-avatar.png';
  }

  isRecipientOnline(): boolean {
    if (!this.selectedConversation || !this.currentUser) return false;

    const participants = this.selectedConversation.participants || [];
    const currentUserId = this.currentUser.id || this.currentUser._id;

    const recipient = participants.find(
      (p) => (p.id || p._id) !== currentUserId
    );

    return recipient?.isOnline || false;
  }

  trackByMessageId(index: number, message: Message): string {
    return message.id || message._id || index.toString();
  }

  openImageViewer(attachment: Attachment | undefined): void {
    if (!attachment?.url) return;

    // Ouvrir l'image dans une nouvelle fenêtre ou modal
    window.open(attachment.url, '_blank');
  }

  formatFileSize(size: number | undefined): string {
    if (!size) return '0 B';

    const units = ['B', 'KB', 'MB', 'GB'];
    let unitIndex = 0;
    let fileSize = size;

    while (fileSize >= 1024 && unitIndex < units.length - 1) {
      fileSize /= 1024;
      unitIndex++;
    }

    return `${fileSize.toFixed(1)} ${units[unitIndex]}`;
  }

  downloadFile(attachment: Attachment | undefined): void {
    if (!attachment?.url) return;

    const link = document.createElement('a');
    link.href = attachment.url;
    link.download = attachment.name || 'file';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  playVoiceMessage(message: Message): void {
    if (!message.attachments?.[0]?.url) return;

    this.messageService.playAudio(message.attachments[0].url).catch((error) => {
      console.error('Erreur lors de la lecture du message vocal:', error);
      this.toastService.showError('Erreur lors de la lecture du message vocal');
    });
  }

  formatDuration(duration: number | undefined): string {
    if (!duration) return '0:00';

    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;

    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  showMessageMenu(message: Message): void {
    // Ici, vous pouvez implémenter un menu contextuel
    // Pour l'instant, on affiche juste les options disponibles
    const actions = [];

    if (this.canDeleteMessage(message)) {
      actions.push('Supprimer');
    }

    if (this.isMyMessage(message)) {
      actions.push('Modifier');
    }

    actions.push('Répondre', 'Transférer', 'Réagir');

    // Vous pouvez implémenter un vrai menu contextuel ici
    console.log('Actions disponibles pour ce message:', actions);
  }
}
