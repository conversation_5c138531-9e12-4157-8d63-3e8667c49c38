"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([["src_app_views_front_signup_signup_module_ts"],{

/***/ 6195:
/*!*************************************************************!*\
  !*** ./src/app/views/front/signup/signup-routing.module.ts ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SignupRoutingModule: () => (/* binding */ SignupRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _signup_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./signup.component */ 5555);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);




const routes = [{
  path: '',
  component: _signup_component__WEBPACK_IMPORTED_MODULE_0__.SignupComponent
}];
class SignupRoutingModule {
  static {
    this.ɵfac = function SignupRoutingModule_Factory(t) {
      return new (t || SignupRoutingModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineNgModule"]({
      type: SignupRoutingModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjector"]({
      imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵsetNgModuleScope"](SignupRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
  });
})();

/***/ }),

/***/ 5555:
/*!********************************************************!*\
  !*** ./src/app/views/front/signup/signup.component.ts ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SignupComponent: () => (/* binding */ SignupComponent)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _services_auth_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../services/auth.service */ 4796);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 316);






function SignupComponent_div_52_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 36)(1, "div", 37)(2, "div", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](3, "i", 39)(4, "div", 40);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](5, "div", 41)(6, "p", 42);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"](" ", ctx_r0.error, " ");
  }
}
function SignupComponent_div_53_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 43)(1, "div", 37)(2, "div", 44);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](3, "i", 45)(4, "div", 46);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](5, "div", 41)(6, "p", 47);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"](" ", ctx_r1.message, " ");
  }
}
class SignupComponent {
  constructor(fb, authService, router) {
    this.fb = fb;
    this.authService = authService;
    this.router = router;
    this.message = '';
    this.error = '';
    this.submittedEmail = '';
    this.signupForm = this.fb.group({
      fullName: ['', _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required],
      email: ['', [_angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.email]],
      password: ['', _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required]
    });
  }
  onSignupSubmit() {
    if (this.signupForm.invalid) return;
    const signupData = this.signupForm.value;
    this.submittedEmail = signupData.email;
    this.authService.signup(signupData).subscribe({
      next: res => {
        console.log('Signup successful:', res);
        this.message = res.message;
        this.error = '';
        // Attendre un court instant avant de rediriger
        setTimeout(() => {
          // Rediriger vers le composant de vérification d'email
          this.router.navigate(['/verify-email'], {
            queryParams: {
              email: this.submittedEmail
            }
          });
        }, 500);
      },
      error: err => {
        console.error('Signup error:', err);
        // Si l'utilisateur existe déjà mais que nous avons besoin de vérifier l'email
        if (err.error && err.error.message === 'Email already exists' && err.error.needsVerification) {
          // Rediriger vers la vérification d'email
          this.router.navigate(['/verify-email'], {
            queryParams: {
              email: this.submittedEmail
            }
          });
          return;
        }
        // Gérer les autres erreurs
        this.error = err.error?.message || 'Signup failed';
        this.message = '';
      }
    });
  }
  static {
    this.ɵfac = function SignupComponent_Factory(t) {
      return new (t || SignupComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormBuilder), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_services_auth_service__WEBPACK_IMPORTED_MODULE_0__.AuthService), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_3__.Router));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
      type: SignupComponent,
      selectors: [["app-signup"]],
      decls: 65,
      vars: 3,
      consts: [[1, "min-h-screen", "bg-[#edf1f4]", "dark:bg-[#121212]", "flex", "items-center", "justify-center", "p-4", "relative", "futuristic-layout"], [1, "absolute", "inset-0", "overflow-hidden", "pointer-events-none"], [1, "absolute", "top-[15%]", "left-[10%]", "w-64", "h-64", "rounded-full", "bg-gradient-to-br", "from-[#4f5fad]/5", "to-transparent", "dark:from-[#6d78c9]/3", "dark:to-transparent", "blur-3xl"], [1, "absolute", "bottom-[20%]", "right-[10%]", "w-80", "h-80", "rounded-full", "bg-gradient-to-tl", "from-[#4f5fad]/5", "to-transparent", "dark:from-[#6d78c9]/3", "dark:to-transparent", "blur-3xl"], [1, "absolute", "inset-0", "opacity-5", "dark:opacity-[0.03]"], [1, "h-full", "grid", "grid-cols-12"], [1, "border-r", "border-[#4f5fad]", "dark:border-[#6d78c9]"], [1, "w-full", "max-w-md", "relative", "z-10"], [1, "bg-white", "dark:bg-[#1e1e1e]", "rounded-xl", "shadow-lg", "dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]", "overflow-hidden", "backdrop-blur-sm", "border", "border-[#edf1f4]/50", "dark:border-[#2a2a2a]", "relative"], [1, "absolute", "top-0", "left-0", "right-0", "h-1", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]"], [1, "absolute", "top-0", "left-0", "right-0", "h-1", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "blur-md"], [1, "p-6", "text-center"], [1, "text-2xl", "font-bold", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "bg-clip-text", "text-transparent"], [1, "text-sm", "text-[#6d6870]", "dark:text-[#a0a0a0]", "mt-2"], [1, "p-6"], [1, "space-y-5", 3, "formGroup", "ngSubmit"], [1, "group"], [1, "flex", "items-center", "text-sm", "font-medium", "text-[#4f5fad]", "dark:text-[#6d78c9]", "mb-2"], [1, "fas", "fa-user", "mr-1.5", "text-xs"], [1, "relative"], ["formControlName", "fullName", "placeholder", "John Doe", 1, "w-full", "px-4", "py-2.5", "text-sm", "rounded-lg", "border", "border-[#bdc6cc]", "dark:border-[#2a2a2a]", "bg-white", "dark:bg-[#1e1e1e]", "text-[#6d6870]", "dark:text-[#e0e0e0]", "focus:outline-none", "focus:border-[#4f5fad]", "dark:focus:border-[#6d78c9]", "focus:ring-2", "focus:ring-[#4f5fad]/20", "dark:focus:ring-[#6d78c9]/20", "transition-all"], [1, "absolute", "inset-y-0", "left-0", "pl-3", "flex", "items-center", "pointer-events-none", "opacity-0", "group-focus-within:opacity-100", "transition-opacity"], [1, "w-0.5", "h-4", "bg-gradient-to-b", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "rounded-full"], [1, "fas", "fa-envelope", "mr-1.5", "text-xs"], ["formControlName", "email", "type", "email", "placeholder", "<EMAIL>", 1, "w-full", "px-4", "py-2.5", "text-sm", "rounded-lg", "border", "border-[#bdc6cc]", "dark:border-[#2a2a2a]", "bg-white", "dark:bg-[#1e1e1e]", "text-[#6d6870]", "dark:text-[#e0e0e0]", "focus:outline-none", "focus:border-[#4f5fad]", "dark:focus:border-[#6d78c9]", "focus:ring-2", "focus:ring-[#4f5fad]/20", "dark:focus:ring-[#6d78c9]/20", "transition-all"], [1, "fas", "fa-lock", "mr-1.5", "text-xs"], ["formControlName", "password", "type", "password", "placeholder", "\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022", 1, "w-full", "px-4", "py-2.5", "text-sm", "rounded-lg", "border", "border-[#bdc6cc]", "dark:border-[#2a2a2a]", "bg-white", "dark:bg-[#1e1e1e]", "text-[#6d6870]", "dark:text-[#e0e0e0]", "focus:outline-none", "focus:border-[#4f5fad]", "dark:focus:border-[#6d78c9]", "focus:ring-2", "focus:ring-[#4f5fad]/20", "dark:focus:ring-[#6d78c9]/20", "transition-all"], ["class", "bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-3 backdrop-blur-sm", 4, "ngIf"], ["class", "bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-3 backdrop-blur-sm", 4, "ngIf"], ["type", "submit", 1, "w-full", "relative", "overflow-hidden", "group", "mt-6"], [1, "absolute", "inset-0", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#3d4a85]", "dark:to-[#6d78c9]", "rounded-lg", "transition-transform", "duration-300", "group-hover:scale-105"], [1, "absolute", "inset-0", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#3d4a85]", "dark:to-[#6d78c9]", "rounded-lg", "opacity-0", "group-hover:opacity-100", "blur-xl", "transition-opacity", "duration-300"], [1, "relative", "flex", "items-center", "justify-center", "text-white", "font-medium", "py-2.5", "px-4", "rounded-lg", "transition-all", "z-10"], [1, "fas", "fa-user-plus", "mr-2"], [1, "text-center", "text-sm", "text-[#6d6870]", "dark:text-[#a0a0a0]", "space-y-2", "pt-4"], ["routerLink", "/login", 1, "text-[#4f5fad]", "dark:text-[#6d78c9]", "hover:text-[#3d4a85]", "dark:hover:text-[#4f5fad]", "transition-colors", "font-medium"], [1, "bg-[#ff6b69]/10", "dark:bg-[#ff6b69]/5", "border", "border-[#ff6b69]", "dark:border-[#ff6b69]/30", "rounded-lg", "p-3", "backdrop-blur-sm"], [1, "flex", "items-start"], [1, "text-[#ff6b69]", "dark:text-[#ff8785]", "mr-2", "text-base", "relative"], [1, "fas", "fa-exclamation-triangle"], [1, "absolute", "inset-0", "bg-[#ff6b69]/20", "dark:bg-[#ff8785]/20", "blur-xl", "rounded-full", "transform", "scale-150", "-z-10"], [1, "flex-1"], [1, "text-xs", "text-[#ff6b69]", "dark:text-[#ff8785]"], [1, "bg-[#4f5fad]/10", "dark:bg-[#6d78c9]/5", "border", "border-[#4f5fad]", "dark:border-[#6d78c9]/30", "rounded-lg", "p-3", "backdrop-blur-sm"], [1, "text-[#4f5fad]", "dark:text-[#6d78c9]", "mr-2", "text-base", "relative"], [1, "fas", "fa-check-circle"], [1, "absolute", "inset-0", "bg-[#4f5fad]/20", "dark:bg-[#6d78c9]/20", "blur-xl", "rounded-full", "transform", "scale-150", "-z-10"], [1, "text-xs", "text-[#4f5fad]", "dark:text-[#6d78c9]"]],
      template: function SignupComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 0)(1, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](2, "div", 2)(3, "div", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](4, "div", 4)(5, "div", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](6, "div", 6)(7, "div", 6)(8, "div", 6)(9, "div", 6)(10, "div", 6)(11, "div", 6)(12, "div", 6)(13, "div", 6)(14, "div", 6)(15, "div", 6)(16, "div", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](17, "div", 7)(18, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](19, "div", 9)(20, "div", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](21, "div", 11)(22, "h1", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](23, " Create Account ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](24, "p", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](25, " Sign up to join DevBridge ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](26, "div", 14)(27, "form", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("ngSubmit", function SignupComponent_Template_form_ngSubmit_27_listener() {
            return ctx.onSignupSubmit();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](28, "div", 16)(29, "label", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](30, "i", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](31, " Full Name ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](32, "div", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](33, "input", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](34, "div", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](35, "div", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](36, "div", 16)(37, "label", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](38, "i", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](39, " Email ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](40, "div", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](41, "input", 24);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](42, "div", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](43, "div", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](44, "div", 16)(45, "label", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](46, "i", 25);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](47, " Password ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](48, "div", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](49, "input", 26);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](50, "div", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](51, "div", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](52, SignupComponent_div_52_Template, 8, 1, "div", 27);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](53, SignupComponent_div_53_Template, 8, 1, "div", 28);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](54, "button", 29);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](55, "div", 30)(56, "div", 31);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](57, "span", 32);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](58, "i", 33);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](59, " Sign Up ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](60, "div", 34)(61, "div");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](62, " Already have an account? ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](63, "a", 35);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](64, " Sign in ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()()()()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](27);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("formGroup", ctx.signupForm);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](25);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.error);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.message);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_2__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_2__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormControlName, _angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterLink],
      styles: ["/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJzaWdudXAuY29tcG9uZW50LmNzcyJ9 */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvc2lnbnVwL3NpZ251cC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7O0FBRUEsZ0tBQWdLIiwic291cmNlUm9vdCI6IiJ9 */"]
    });
  }
}

/***/ }),

/***/ 4418:
/*!*****************************************************!*\
  !*** ./src/app/views/front/signup/signup.module.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SignupModule: () => (/* binding */ SignupModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _signup_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./signup-routing.module */ 6195);
/* harmony import */ var _signup_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./signup.component */ 5555);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 7580);





class SignupModule {
  static {
    this.ɵfac = function SignupModule_Factory(t) {
      return new (t || SignupModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineNgModule"]({
      type: SignupModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineInjector"]({
      imports: [_angular_common__WEBPACK_IMPORTED_MODULE_3__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.ReactiveFormsModule, _signup_routing_module__WEBPACK_IMPORTED_MODULE_0__.SignupRoutingModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵsetNgModuleScope"](SignupModule, {
    declarations: [_signup_component__WEBPACK_IMPORTED_MODULE_1__.SignupComponent],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_3__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.ReactiveFormsModule, _signup_routing_module__WEBPACK_IMPORTED_MODULE_0__.SignupRoutingModule]
  });
})();

/***/ })

}]);
//# sourceMappingURL=src_app_views_front_signup_signup_module_ts.js.map