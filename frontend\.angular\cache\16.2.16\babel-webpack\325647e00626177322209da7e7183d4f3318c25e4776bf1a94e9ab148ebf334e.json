{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule, DatePipe } from '@angular/common';\nimport { PlanningsRoutingModule } from './plannings-routing.module';\nimport { PlanningListComponent } from './planning-list/planning-list.component';\nimport { PlanningDetailComponent } from './planning-detail/planning-detail.component';\nimport { PlanningFormComponent } from './planning-form/planning-form.component';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { PlanningEditComponent } from './planning-edit/planning-edit.component';\nimport { PipesModule } from '../../../pipes/pipes.module';\nexport let PlanningsModule = class PlanningsModule {};\nPlanningsModule = __decorate([NgModule({\n  declarations: [PlanningListComponent, PlanningDetailComponent, PlanningFormComponent, PlanningEditComponent],\n  imports: [CommonModule, PlanningsRoutingModule, FormsModule, ReactiveFormsModule, PipesModule],\n  providers: [DatePipe]\n})], PlanningsModule);", "map": {"version": 3, "names": ["NgModule", "CommonModule", "DatePipe", "PlanningsRoutingModule", "PlanningListComponent", "PlanningDetailComponent", "PlanningFormComponent", "FormsModule", "ReactiveFormsModule", "PlanningEditComponent", "PipesModule", "PlanningsModule", "__decorate", "declarations", "imports", "providers"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\plannings\\plannings.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule, DatePipe } from '@angular/common';\n\nimport { PlanningsRoutingModule } from './plannings-routing.module';\nimport { PlanningListComponent } from './planning-list/planning-list.component';\nimport { PlanningDetailComponent } from './planning-detail/planning-detail.component';\nimport { PlanningFormComponent } from './planning-form/planning-form.component';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { PlanningEditComponent } from './planning-edit/planning-edit.component';\n\nimport { PipesModule } from '../../../pipes/pipes.module';\n\n@NgModule({\n  declarations: [\n    PlanningListComponent,\n    PlanningDetailComponent,\n    PlanningFormComponent,\n    PlanningEditComponent,\n  ],\n  imports: [\n    CommonModule,\n    PlanningsRoutingModule,\n    FormsModule,\n    ReactiveFormsModule,\n\n    PipesModule,\n  ],\n  providers: [DatePipe],\n})\nexport class PlanningsModule {}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,EAAEC,QAAQ,QAAQ,iBAAiB;AAExD,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,qBAAqB,QAAQ,yCAAyC;AAE/E,SAASC,WAAW,QAAQ,6BAA6B;AAmBlD,WAAMC,eAAe,GAArB,MAAMA,eAAe,GAAG;AAAlBA,eAAe,GAAAC,UAAA,EAjB3BZ,QAAQ,CAAC;EACRa,YAAY,EAAE,CACZT,qBAAqB,EACrBC,uBAAuB,EACvBC,qBAAqB,EACrBG,qBAAqB,CACtB;EACDK,OAAO,EAAE,CACPb,YAAY,EACZE,sBAAsB,EACtBI,WAAW,EACXC,mBAAmB,EAEnBE,WAAW,CACZ;EACDK,SAAS,EAAE,CAACb,QAAQ;CACrB,CAAC,C,EACWS,eAAe,CAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}