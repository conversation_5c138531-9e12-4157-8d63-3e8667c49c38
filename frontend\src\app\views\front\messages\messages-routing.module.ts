import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MessageChatComponent } from './message-chat/message-chat.component';
import { MessagesListComponent } from './messages-list/messages-list.component';
import { UserListComponent } from './user-list/user-list.component';
import { MessageLayoutComponent } from './message-layout/message-layout.component';

const routes: Routes = [
  {
    path: '',
    component: MessageLayoutComponent,
    children: [
      // Route par défaut - affiche le layout sans conversation sélectionnée
      {
        path: '',
        component: MessageChatComponent,
        data: { title: 'Messages' },
      },
      // Route pour une conversation spécifique
      {
        path: ':conversationId',
        component: MessageChatComponent,
        data: { title: 'Chat' },
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class MessagesRoutingModule {}
