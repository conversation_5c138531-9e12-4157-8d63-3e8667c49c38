"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([["default-src_app_views_front_messages_message-layout_message-layout_component_ts"],{

/***/ 9767:
/*!***********************************************!*\
  !*** ./src/app/services/mock-data.service.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MockDataService: () => (/* binding */ MockDataService)
/* harmony export */ });
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rxjs */ 9452);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rxjs/operators */ 5074);
/* harmony import */ var _models_message_model__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../models/message.model */ 5293);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 7580);




class MockDataService {
  constructor() {
    // Utilisateurs de test
    this.mockUsers = [{
      id: '1',
      _id: '1',
      username: 'Alice Martin',
      email: '<EMAIL>',
      image: '/assets/images/avatars/alice.jpg',
      isOnline: true,
      isActive: true,
      role: 'developer'
    }, {
      id: '2',
      _id: '2',
      username: 'Bob Dupont',
      email: '<EMAIL>',
      image: '/assets/images/avatars/bob.jpg',
      isOnline: false,
      isActive: true,
      role: 'designer'
    }, {
      id: '3',
      _id: '3',
      username: 'Claire Rousseau',
      email: '<EMAIL>',
      image: '/assets/images/avatars/claire.jpg',
      isOnline: true,
      isActive: true,
      role: 'manager'
    }, {
      id: '4',
      _id: '4',
      username: 'David Chen',
      email: '<EMAIL>',
      image: '/assets/images/avatars/david.jpg',
      isOnline: true,
      isActive: true,
      role: 'developer'
    }, {
      id: '5',
      _id: '5',
      username: 'Emma Wilson',
      email: '<EMAIL>',
      image: '/assets/images/avatars/emma.jpg',
      isOnline: false,
      isActive: true,
      role: 'tester'
    }];
    // Messages de test
    this.mockMessages = [{
      id: '1',
      content: 'Salut ! Comment ça va ?',
      type: _models_message_model__WEBPACK_IMPORTED_MODULE_0__.MessageType.TEXT,
      timestamp: new Date(Date.now() - 3600000),
      sender: this.mockUsers[1],
      isRead: true,
      conversationId: 'conv1'
    }, {
      id: '2',
      content: 'Ça va bien merci ! Et toi ?',
      type: _models_message_model__WEBPACK_IMPORTED_MODULE_0__.MessageType.TEXT,
      timestamp: new Date(Date.now() - 3500000),
      sender: this.mockUsers[0],
      isRead: true,
      conversationId: 'conv1'
    }, {
      id: '3',
      content: 'Super ! Tu as vu le nouveau design ?',
      type: _models_message_model__WEBPACK_IMPORTED_MODULE_0__.MessageType.TEXT,
      timestamp: new Date(Date.now() - 1800000),
      sender: this.mockUsers[1],
      isRead: false,
      conversationId: 'conv1'
    }];
    // Conversations de test
    this.mockConversations = [{
      id: 'conv1',
      participants: [this.mockUsers[0], this.mockUsers[1]],
      lastMessage: this.mockMessages[2],
      unreadCount: 1,
      isGroup: false,
      createdAt: new Date(Date.now() - 86400000) // 1 day ago
    }, {
      id: 'conv2',
      participants: [this.mockUsers[0], this.mockUsers[2]],
      lastMessage: {
        id: '4',
        content: 'Réunion à 14h ?',
        type: _models_message_model__WEBPACK_IMPORTED_MODULE_0__.MessageType.TEXT,
        timestamp: new Date(Date.now() - 7200000),
        sender: this.mockUsers[2],
        isRead: true,
        conversationId: 'conv2'
      },
      unreadCount: 0,
      isGroup: false,
      createdAt: new Date(Date.now() - 172800000) // 2 days ago
    }, {
      id: 'conv3',
      participants: [this.mockUsers[0], this.mockUsers[1], this.mockUsers[2], this.mockUsers[3]],
      lastMessage: {
        id: '5',
        content: 'Nouveau projet lancé ! 🚀',
        type: _models_message_model__WEBPACK_IMPORTED_MODULE_0__.MessageType.TEXT,
        timestamp: new Date(Date.now() - 10800000),
        sender: this.mockUsers[3],
        isRead: false,
        conversationId: 'conv3'
      },
      unreadCount: 3,
      isGroup: true,
      groupName: 'Équipe DevBridge',
      groupPhoto: '/assets/images/groups/team.jpg',
      createdAt: new Date(Date.now() - 259200000) // 3 days ago
    }];
    // Notifications de test
    this.mockNotifications = [{
      id: 'notif1',
      type: 'NEW_MESSAGE',
      content: 'Nouveau message de Bob Dupont',
      timestamp: new Date(Date.now() - 1800000),
      isRead: false
    }, {
      id: 'notif2',
      type: 'MESSAGE_REACTION',
      content: 'Alice a réagi à votre message avec ❤️',
      timestamp: new Date(Date.now() - 3600000),
      isRead: true
    }, {
      id: 'notif3',
      type: 'GROUP_INVITE',
      content: 'Vous avez été ajouté au groupe "Équipe DevBridge"',
      timestamp: new Date(Date.now() - 7200000),
      isRead: false
    }];
  }
  // ============================================================================
  // MÉTHODES PUBLIQUES POUR LES TESTS
  // ============================================================================
  /**
   * Récupère tous les utilisateurs
   */
  getUsers() {
    return (0,rxjs__WEBPACK_IMPORTED_MODULE_1__.of)(this.mockUsers).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.delay)(500)); // Simule la latence réseau
  }
  /**
   * Récupère toutes les conversations
   */
  getConversations() {
    return (0,rxjs__WEBPACK_IMPORTED_MODULE_1__.of)(this.mockConversations).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.delay)(300));
  }
  /**
   * Récupère une conversation par ID
   */
  getConversation(id) {
    const conversation = this.mockConversations.find(c => c.id === id);
    return (0,rxjs__WEBPACK_IMPORTED_MODULE_1__.of)(conversation || null).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.delay)(200));
  }
  /**
   * Récupère les messages d'une conversation
   */
  getMessages(conversationId) {
    const messages = this.mockMessages.filter(m => m.conversationId === conversationId);
    return (0,rxjs__WEBPACK_IMPORTED_MODULE_1__.of)(messages).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.delay)(300));
  }
  /**
   * Récupère toutes les notifications
   */
  getNotifications() {
    return (0,rxjs__WEBPACK_IMPORTED_MODULE_1__.of)(this.mockNotifications).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.delay)(200));
  }
  /**
   * Simule l'envoi d'un message
   */
  sendMessage(content, conversationId, senderId) {
    const newMessage = {
      id: `msg_${Date.now()}`,
      content,
      type: _models_message_model__WEBPACK_IMPORTED_MODULE_0__.MessageType.TEXT,
      timestamp: new Date(),
      sender: this.mockUsers.find(u => u.id === senderId) || this.mockUsers[0],
      isRead: false,
      conversationId
    };
    // Ajouter le message à la liste
    this.mockMessages.push(newMessage);
    // Mettre à jour la conversation
    const conversation = this.mockConversations.find(c => c.id === conversationId);
    if (conversation) {
      conversation.lastMessage = newMessage;
    }
    return (0,rxjs__WEBPACK_IMPORTED_MODULE_1__.of)(newMessage).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.delay)(100));
  }
  /**
   * Simule la création d'une conversation
   */
  createConversation(userId, currentUserId) {
    const otherUser = this.mockUsers.find(u => u.id === userId);
    const currentUser = this.mockUsers.find(u => u.id === currentUserId);
    if (!otherUser || !currentUser) {
      throw new Error('Utilisateur non trouvé');
    }
    const newConversation = {
      id: `conv_${Date.now()}`,
      participants: [currentUser, otherUser],
      unreadCount: 0,
      isGroup: false,
      createdAt: new Date()
    };
    this.mockConversations.unshift(newConversation);
    return (0,rxjs__WEBPACK_IMPORTED_MODULE_1__.of)(newConversation).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.delay)(200));
  }
  /**
   * Récupère l'utilisateur actuel (pour les tests)
   */
  getCurrentUser() {
    return this.mockUsers[0]; // Alice comme utilisateur actuel
  }
  /**
   * Simule la recherche d'utilisateurs
   */
  searchUsers(query) {
    const results = this.mockUsers.filter(user => user.username.toLowerCase().includes(query.toLowerCase()) || user.email.toLowerCase().includes(query.toLowerCase()));
    return (0,rxjs__WEBPACK_IMPORTED_MODULE_1__.of)(results).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.delay)(300));
  }
  /**
   * Simule la recherche de conversations
   */
  searchConversations(query) {
    const results = this.mockConversations.filter(conv => {
      if (conv.isGroup) {
        return conv.groupName?.toLowerCase().includes(query.toLowerCase());
      } else {
        return conv.participants?.some(p => p.username.toLowerCase().includes(query.toLowerCase()));
      }
    });
    return (0,rxjs__WEBPACK_IMPORTED_MODULE_1__.of)(results).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.delay)(300));
  }
  static {
    this.ɵfac = function MockDataService_Factory(t) {
      return new (t || MockDataService)();
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineInjectable"]({
      token: MockDataService,
      factory: MockDataService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 8397:
/*!*******************************************!*\
  !*** ./src/app/services/toast.service.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ToastService: () => (/* binding */ ToastService)
/* harmony export */ });
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rxjs */ 5797);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);


class ToastService {
  constructor() {
    this.toastsSubject = new rxjs__WEBPACK_IMPORTED_MODULE_0__.BehaviorSubject([]);
    this.toasts$ = this.toastsSubject.asObservable();
    this.currentId = 0;
  }
  generateId() {
    return Math.random().toString(36).substr(2, 9);
  }
  addToast(toast) {
    const newToast = {
      ...toast,
      id: this.generateId(),
      duration: toast.duration || 5000
    };
    const currentToasts = this.toastsSubject.value;
    this.toastsSubject.next([...currentToasts, newToast]);
    // Auto-remove toast after duration
    if (newToast.duration && newToast.duration > 0) {
      setTimeout(() => {
        this.removeToast(newToast.id);
      }, newToast.duration);
    }
  }
  show(message, type = 'info', duration = 5000) {
    const id = this.generateId();
    const toast = {
      id,
      type,
      title: '',
      message,
      duration
    };
    const currentToasts = this.toastsSubject.value;
    this.toastsSubject.next([...currentToasts, toast]);
    if (duration > 0) {
      setTimeout(() => this.dismiss(id), duration);
    }
  }
  showSuccess(message, duration = 3000) {
    this.show(message, 'success', duration);
  }
  showError(message, duration = 5000) {
    this.show(message, 'error', duration);
  }
  showWarning(message, duration = 4000) {
    this.show(message, 'warning', duration);
  }
  showInfo(message, duration = 3000) {
    this.show(message, 'info', duration);
  }
  dismiss(id) {
    const currentToasts = this.toastsSubject.value.filter(t => t.id !== id);
    this.toastsSubject.next(currentToasts);
  }
  success(title, message, duration) {
    this.addToast({
      type: 'success',
      title,
      message,
      duration,
      icon: 'check-circle'
    });
  }
  error(title, message, duration, action) {
    this.addToast({
      type: 'error',
      title,
      message,
      duration: duration || 8000,
      icon: 'x-circle',
      action
    });
  }
  warning(title, message, duration) {
    this.addToast({
      type: 'warning',
      title,
      message,
      duration,
      icon: 'exclamation-triangle'
    });
  }
  // Méthodes spécifiques pour les erreurs d'autorisation
  accessDenied(action = 'effectuer cette action', code) {
    const codeText = code ? ` (Code: ${code})` : '';
    this.error('Accès refusé', `Vous n'avez pas les permissions nécessaires pour ${action}${codeText}`, 8000, {
      label: 'Comprendre les rôles',
      handler: () => {
        // Optionnel: rediriger vers une page d'aide
        console.log("Redirection vers l'aide sur les rôles");
      }
    });
  }
  ownershipRequired(resource = 'cette ressource') {
    this.error('Propriétaire requis', `Seul le propriétaire ou un administrateur peut modifier ${resource}`, 8000);
  }
  removeToast(id) {
    const currentToasts = this.toastsSubject.value;
    this.toastsSubject.next(currentToasts.filter(toast => toast.id !== id));
  }
  clear() {
    this.toastsSubject.next([]);
  }
  static {
    this.ɵfac = function ToastService_Factory(t) {
      return new (t || ToastService)();
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
      token: ToastService,
      factory: ToastService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 8076:
/*!*********************************************************************************!*\
  !*** ./src/app/views/front/messages/message-layout/message-layout.component.ts ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MessageLayoutComponent: () => (/* binding */ MessageLayoutComponent)
/* harmony export */ });
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rxjs */ 5797);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _services_message_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../services/message.service */ 4537);
/* harmony import */ var _services_auth_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../services/auth.service */ 4796);
/* harmony import */ var _services_toast_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../services/toast.service */ 8397);
/* harmony import */ var _services_theme_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../services/theme.service */ 487);
/* harmony import */ var _services_mock_data_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../../services/mock-data.service */ 9767);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/forms */ 4456);










const _c0 = ["searchInput"];
function MessageLayoutComponent_div_15_div_3_i_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](0, "i", 43);
  }
}
function MessageLayoutComponent_div_15_div_3_Template(rf, ctx) {
  if (rf & 1) {
    const _r11 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function MessageLayoutComponent_div_15_div_3_Template_div_click_0_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r11);
      const theme_r8 = restoredCtx.$implicit;
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx_r10.selectTheme(theme_r8.name));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](1, "div", 40);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](2, "span", 41);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](4, MessageLayoutComponent_div_15_div_3_i_4_Template, 1, 0, "i", 42);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const theme_r8 = ctx.$implicit;
    const ctx_r7 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵstyleProp"]("background", theme_r8.gradients.primary);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵclassProp"]("border-white", (ctx_r7.currentTheme == null ? null : ctx_r7.currentTheme.name) === theme_r8.name)("border-gray-500", (ctx_r7.currentTheme == null ? null : ctx_r7.currentTheme.name) !== theme_r8.name);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](theme_r8.displayName);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", (ctx_r7.currentTheme == null ? null : ctx_r7.currentTheme.name) === theme_r8.name);
  }
}
function MessageLayoutComponent_div_15_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 36)(1, "div", 37);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](2, " Choisir un th\u00E8me ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](3, MessageLayoutComponent_div_15_div_3_Template, 5, 8, "div", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", ctx_r0.availableThemes);
  }
}
function MessageLayoutComponent_button_22_Template(rf, ctx) {
  if (rf & 1) {
    const _r13 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "button", 44);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function MessageLayoutComponent_button_22_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r13);
      const ctx_r12 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx_r12.clearSearch());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](1, "i", 45);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
}
function MessageLayoutComponent_span_36_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "span", 46);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", ctx_r3.notifications.length > 9 ? "9+" : ctx_r3.notifications.length, " ");
  }
}
function MessageLayoutComponent_div_38_div_1_div_3_Template(rf, ctx) {
  if (rf & 1) {
    const _r20 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 54);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function MessageLayoutComponent_div_38_div_1_div_3_Template_div_click_0_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r20);
      const result_r18 = restoredCtx.$implicit;
      const ctx_r19 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](3);
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx_r19.selectConversation(result_r18));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "div", 4);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](2, "img", 55);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](3, "div", 56)(4, "h4", 57);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](6, "p", 58);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const result_r18 = ctx.$implicit;
    const ctx_r17 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("src", ctx_r17.getConversationAvatar(result_r18), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵsanitizeUrl"])("alt", ctx_r17.getConversationName(result_r18));
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", ctx_r17.getConversationName(result_r18), " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", ctx_r17.getLastMessagePreview(result_r18), " ");
  }
}
function MessageLayoutComponent_div_38_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 51)(1, "div", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](3, MessageLayoutComponent_div_38_div_1_div_3_Template, 8, 4, "div", 53);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r14 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" R\u00E9sultats de recherche (", ctx_r14.searchResults.length, ") ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", ctx_r14.searchResults);
  }
}
function MessageLayoutComponent_div_38_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 59);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](1, "i", 60);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](2, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3, "Aucun r\u00E9sultat trouv\u00E9");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
  }
}
function MessageLayoutComponent_div_38_div_3_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 64);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](1, "div", 65);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](2, "p", 66);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3, "Chargement des conversations...");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
  }
}
function MessageLayoutComponent_div_38_div_3_div_2_div_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](0, "div", 73);
  }
}
function MessageLayoutComponent_div_38_div_3_div_2_span_14_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "span", 74);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const conversation_r25 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;
    const ctx_r27 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", ctx_r27.getUnreadCount(conversation_r25) > 99 ? "99+" : ctx_r27.getUnreadCount(conversation_r25), " ");
  }
}
function MessageLayoutComponent_div_38_div_3_div_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r30 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 67);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function MessageLayoutComponent_div_38_div_3_div_2_Template_div_click_0_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r30);
      const conversation_r25 = restoredCtx.$implicit;
      const ctx_r29 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](3);
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx_r29.selectConversation(conversation_r25));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "div", 4)(2, "div", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](3, "img", 55);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](4, MessageLayoutComponent_div_38_div_3_div_2_div_4_Template, 1, 0, "div", 68);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](5, "div", 56)(6, "div", 69)(7, "h4", 57);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](9, "span", 70);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](10);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](11, "div", 71)(12, "p", 58);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](13);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](14, MessageLayoutComponent_div_38_div_3_div_2_span_14_Template, 2, 1, "span", 72);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const conversation_r25 = ctx.$implicit;
    const ctx_r22 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵclassProp"]("bg-gray-700", ctx_r22.selectedConversationId === conversation_r25.id)("border-l-4", ctx_r22.selectedConversationId === conversation_r25.id)("border-blue-500", ctx_r22.selectedConversationId === conversation_r25.id);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("src", ctx_r22.getConversationAvatar(conversation_r25), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵsanitizeUrl"])("alt", ctx_r22.getConversationName(conversation_r25));
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", !conversation_r25.isGroup && ctx_r22.isUserOnline(conversation_r25.participants == null ? null : conversation_r25.participants[0]));
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", ctx_r22.getConversationName(conversation_r25), " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", ctx_r22.formatLastMessageTime(conversation_r25.lastMessage == null ? null : conversation_r25.lastMessage.timestamp), " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", ctx_r22.getLastMessagePreview(conversation_r25), " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r22.getUnreadCount(conversation_r25) > 0);
  }
}
function MessageLayoutComponent_div_38_div_3_div_3_span_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "Charger plus");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
}
function MessageLayoutComponent_div_38_div_3_div_3_span_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "Chargement...");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
}
function MessageLayoutComponent_div_38_div_3_div_3_Template(rf, ctx) {
  if (rf & 1) {
    const _r34 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 75)(1, "button", 76);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function MessageLayoutComponent_div_38_div_3_div_3_Template_button_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r34);
      const ctx_r33 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](3);
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx_r33.loadMoreConversations());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](2, MessageLayoutComponent_div_38_div_3_div_3_span_2_Template, 2, 0, "span", 50);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](3, MessageLayoutComponent_div_38_div_3_div_3_span_3_Template, 2, 0, "span", 50);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("disabled", ctx_r23.isLoadingConversations);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", !ctx_r23.isLoadingConversations);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r23.isLoadingConversations);
  }
}
function MessageLayoutComponent_div_38_div_3_div_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 59);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](1, "i", 77);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](2, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3, "Aucune conversation");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](4, "p", 78);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](5, " Commencez une nouvelle conversation dans l'onglet Contacts ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
  }
}
function MessageLayoutComponent_div_38_div_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](1, MessageLayoutComponent_div_38_div_3_div_1_Template, 4, 0, "div", 61);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](2, MessageLayoutComponent_div_38_div_3_div_2_Template, 15, 13, "div", 62);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](3, MessageLayoutComponent_div_38_div_3_div_3_Template, 4, 3, "div", 63);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](4, MessageLayoutComponent_div_38_div_3_div_4_Template, 6, 0, "div", 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r16 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r16.isLoadingConversations && ctx_r16.conversations.length === 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", ctx_r16.conversations)("ngForTrackBy", ctx_r16.trackByConversationId);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r16.hasMoreConversations);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r16.conversations.length === 0 && !ctx_r16.isLoadingConversations);
  }
}
function MessageLayoutComponent_div_38_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 47);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](1, MessageLayoutComponent_div_38_div_1_Template, 4, 2, "div", 48);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](2, MessageLayoutComponent_div_38_div_2_Template, 4, 0, "div", 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](3, MessageLayoutComponent_div_38_div_3_Template, 5, 5, "div", 50);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r4.isSearching && ctx_r4.searchResults.length > 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r4.isSearching && ctx_r4.searchResults.length === 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", !ctx_r4.isSearching);
  }
}
function MessageLayoutComponent_div_39_div_1_div_3_div_1_div_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](0, "div", 73);
  }
}
function MessageLayoutComponent_div_39_div_1_div_3_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 4)(1, "div", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](2, "img", 55);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](3, MessageLayoutComponent_div_39_div_1_div_3_div_1_div_3_Template, 1, 0, "div", 68);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](4, "div", 56)(5, "h4", 57);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](7, "p", 58);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](9, "div", 83);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](10, "i", 84);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const result_r39 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;
    const ctx_r40 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("src", result_r39.image || "/assets/images/default-avatar.png", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵsanitizeUrl"])("alt", result_r39.username);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r40.isUserOnline(result_r39));
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", result_r39.username, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](result_r39.email);
  }
}
function MessageLayoutComponent_div_39_div_1_div_3_Template(rf, ctx) {
  if (rf & 1) {
    const _r44 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 81);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function MessageLayoutComponent_div_39_div_1_div_3_Template_div_click_0_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r44);
      const result_r39 = restoredCtx.$implicit;
      const ctx_r43 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](3);
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx_r43.isUser(result_r39) ? ctx_r43.startConversationWithUser(result_r39) : null);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](1, MessageLayoutComponent_div_39_div_1_div_3_div_1_Template, 11, 5, "div", 82);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const result_r39 = ctx.$implicit;
    const ctx_r38 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r38.isUser(result_r39));
  }
}
function MessageLayoutComponent_div_39_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 51)(1, "div", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](3, MessageLayoutComponent_div_39_div_1_div_3_Template, 2, 1, "div", 80);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r35 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" R\u00E9sultats de recherche (", ctx_r35.searchResults.length, ") ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", ctx_r35.searchResults);
  }
}
function MessageLayoutComponent_div_39_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 59);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](1, "i", 60);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](2, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3, "Aucun utilisateur trouv\u00E9");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
  }
}
function MessageLayoutComponent_div_39_div_3_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 64);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](1, "div", 65);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](2, "p", 66);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3, "Chargement des utilisateurs...");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
  }
}
function MessageLayoutComponent_div_39_div_3_div_2_div_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](0, "div", 73);
  }
}
function MessageLayoutComponent_div_39_div_3_div_2_p_10_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "p", 90);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const user_r49 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", user_r49.role, " ");
  }
}
function MessageLayoutComponent_div_39_div_3_div_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r54 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 81);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function MessageLayoutComponent_div_39_div_3_div_2_Template_div_click_0_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r54);
      const user_r49 = restoredCtx.$implicit;
      const ctx_r53 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](3);
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx_r53.startConversationWithUser(user_r49));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "div", 4)(2, "div", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](3, "img", 55);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](4, MessageLayoutComponent_div_39_div_3_div_2_div_4_Template, 1, 0, "div", 68);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](5, "div", 56)(6, "h4", 57);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](8, "p", 58);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](10, MessageLayoutComponent_div_39_div_3_div_2_p_10_Template, 2, 1, "p", 86);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](11, "div", 87)(12, "div", 88);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](13);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](14, "div", 89);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](15, "i", 84);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const user_r49 = ctx.$implicit;
    const ctx_r46 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("src", user_r49.image || "/assets/images/default-avatar.png", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵsanitizeUrl"])("alt", user_r49.username);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r46.isUserOnline(user_r49));
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", user_r49.username, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](user_r49.email);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", user_r49.role);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵclassProp"]("bg-green-600", ctx_r46.isUserOnline(user_r49))("text-green-100", ctx_r46.isUserOnline(user_r49))("bg-gray-600", !ctx_r46.isUserOnline(user_r49))("text-gray-300", !ctx_r46.isUserOnline(user_r49));
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", ctx_r46.isUserOnline(user_r49) ? "En ligne" : "Hors ligne", " ");
  }
}
function MessageLayoutComponent_div_39_div_3_div_3_span_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "Charger plus");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
}
function MessageLayoutComponent_div_39_div_3_div_3_span_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1, "Chargement...");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
}
function MessageLayoutComponent_div_39_div_3_div_3_Template(rf, ctx) {
  if (rf & 1) {
    const _r58 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 75)(1, "button", 76);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function MessageLayoutComponent_div_39_div_3_div_3_Template_button_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r58);
      const ctx_r57 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](3);
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx_r57.loadMoreUsers());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](2, MessageLayoutComponent_div_39_div_3_div_3_span_2_Template, 2, 0, "span", 50);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](3, MessageLayoutComponent_div_39_div_3_div_3_span_3_Template, 2, 0, "span", 50);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r47 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("disabled", ctx_r47.isLoadingUsers);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", !ctx_r47.isLoadingUsers);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r47.isLoadingUsers);
  }
}
function MessageLayoutComponent_div_39_div_3_div_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 59);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](1, "i", 91);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](2, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3, "Aucun utilisateur trouv\u00E9");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
  }
}
function MessageLayoutComponent_div_39_div_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](1, MessageLayoutComponent_div_39_div_3_div_1_Template, 4, 0, "div", 61);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](2, MessageLayoutComponent_div_39_div_3_div_2_Template, 16, 15, "div", 85);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](3, MessageLayoutComponent_div_39_div_3_div_3_Template, 4, 3, "div", 63);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](4, MessageLayoutComponent_div_39_div_3_div_4_Template, 4, 0, "div", 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r37 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r37.isLoadingUsers && ctx_r37.users.length === 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", ctx_r37.users)("ngForTrackBy", ctx_r37.trackByUserId);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r37.hasMoreUsers);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r37.users.length === 0 && !ctx_r37.isLoadingUsers);
  }
}
function MessageLayoutComponent_div_39_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 79);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](1, MessageLayoutComponent_div_39_div_1_Template, 4, 2, "div", 48);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](2, MessageLayoutComponent_div_39_div_2_Template, 4, 0, "div", 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](3, MessageLayoutComponent_div_39_div_3_Template, 5, 5, "div", 50);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r5.isSearching && ctx_r5.searchResults.length > 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r5.isSearching && ctx_r5.searchResults.length === 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", !ctx_r5.isSearching);
  }
}
function MessageLayoutComponent_div_40_div_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 64);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](1, "div", 65);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](2, "p", 66);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3, "Chargement des notifications...");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
  }
}
function MessageLayoutComponent_div_40_div_2_div_11_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](0, "div", 100);
  }
}
function MessageLayoutComponent_div_40_div_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r65 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 94);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function MessageLayoutComponent_div_40_div_2_Template_div_click_0_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵrestoreView"](_r65);
      const notification_r62 = restoredCtx.$implicit;
      const ctx_r64 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵresetView"](ctx_r64.markNotificationAsRead(notification_r62));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "div", 95)(2, "div", 96);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](3, "i", 41);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](4, "div", 56)(5, "h4", 57);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](7, "p", 97);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](9, "p", 98);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](10);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](11, MessageLayoutComponent_div_40_div_2_div_11_Template, 1, 0, "div", 99);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const notification_r62 = ctx.$implicit;
    const ctx_r60 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵclassProp"]("bg-gray-700", !notification_r62.isRead);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵclassProp"]("bg-blue-600", notification_r62.type === "NEW_MESSAGE")("bg-green-600", notification_r62.type === "FRIEND_REQUEST")("bg-yellow-600", notification_r62.type === "GROUP_INVITE")("bg-purple-600", notification_r62.type === "MESSAGE_REACTION")("bg-red-600", notification_r62.type === "SYSTEM_ALERT");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵclassProp"]("fa-message", notification_r62.type === "NEW_MESSAGE")("fa-user-plus", notification_r62.type === "FRIEND_REQUEST")("fa-users", notification_r62.type === "GROUP_INVITE")("fa-heart", notification_r62.type === "MESSAGE_REACTION")("fa-exclamation-triangle", notification_r62.type === "SYSTEM_ALERT");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", ctx_r60.getNotificationTitle(notification_r62), " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", notification_r62.content, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", ctx_r60.formatLastMessageTime(notification_r62.timestamp), " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", !notification_r62.isRead);
  }
}
function MessageLayoutComponent_div_40_div_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 59);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](1, "i", 101);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](2, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3, "Aucune notification");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](4, "p", 78);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](5, " Vous serez notifi\u00E9 des nouveaux messages et \u00E9v\u00E9nements ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
  }
}
function MessageLayoutComponent_div_40_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 92);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](1, MessageLayoutComponent_div_40_div_1_Template, 4, 0, "div", 61);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](2, MessageLayoutComponent_div_40_div_2_Template, 12, 26, "div", 93);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](3, MessageLayoutComponent_div_40_div_3_Template, 6, 0, "div", 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r6.isLoadingNotifications && ctx_r6.notifications.length === 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", ctx_r6.notifications)("ngForTrackBy", ctx_r6.trackByNotificationId);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r6.notifications.length === 0 && !ctx_r6.isLoadingNotifications);
  }
}
class MessageLayoutComponent {
  constructor(messageService, authService, toastService, themeService, mockDataService, route, router, cdr) {
    this.messageService = messageService;
    this.authService = authService;
    this.toastService = toastService;
    this.themeService = themeService;
    this.mockDataService = mockDataService;
    this.route = route;
    this.router = router;
    this.cdr = cdr;
    // État du composant
    this.currentUser = null;
    this.conversations = [];
    this.users = [];
    this.notifications = [];
    // Navigation et UI
    this.activeTab = 'conversations';
    this.selectedConversationId = null;
    this.isMobileMenuOpen = false;
    this.isSearching = false;
    // Thème
    this.currentTheme = null;
    this.availableThemes = [];
    this.showThemeSelector = false;
    // Recherche
    this.searchQuery = '';
    this.searchResults = [];
    // États de chargement
    this.isLoadingConversations = false;
    this.isLoadingUsers = false;
    this.isLoadingNotifications = false;
    // Pagination
    this.conversationsPage = 1;
    this.usersPage = 1;
    this.hasMoreConversations = true;
    this.hasMoreUsers = true;
    // Subscriptions
    this.subscriptions = [];
    // Observables
    this.searchQuery$ = new rxjs__WEBPACK_IMPORTED_MODULE_6__.BehaviorSubject('');
  }
  ngOnInit() {
    this.initializeComponent();
    this.setupSubscriptions();
    this.loadInitialData();
  }
  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }
  // ============================================================================
  // MÉTHODES D'INITIALISATION
  // ============================================================================
  initializeComponent() {
    // Récupérer l'utilisateur actuel
    this.currentUser = this.authService.getCurrentUser();
    if (!this.currentUser) {
      this.router.navigate(['/login']);
      return;
    }
    // Initialiser les thèmes
    this.currentTheme = this.themeService.getCurrentTheme();
    this.availableThemes = this.themeService.getAvailableThemes();
    // Écouter les changements de route
    this.route.params.subscribe(params => {
      const conversationId = params['conversationId'];
      if (conversationId) {
        this.selectedConversationId = conversationId;
        this.markConversationAsSelected(conversationId);
      }
    });
  }
  setupSubscriptions() {
    // Subscription pour les nouveaux messages
    const messagesSub = this.messageService.subscribeToMessages().subscribe(message => {
      if (message) {
        this.handleNewMessage(message);
      }
    });
    // Subscription pour les notifications
    const notificationsSub = this.messageService.subscribeToNotifications().subscribe(notification => {
      if (notification) {
        this.handleNewNotification(notification);
      }
    });
    // Subscription pour la recherche
    const searchSub = this.searchQuery$.subscribe(query => {
      this.performSearch(query);
    });
    // Subscription pour les changements de thème
    const themeSub = this.themeService.currentTheme$.subscribe(theme => {
      this.currentTheme = theme;
      this.cdr.detectChanges();
    });
    this.subscriptions.push(messagesSub, notificationsSub, searchSub, themeSub);
  }
  loadInitialData() {
    this.loadConversations();
    this.loadUsers();
    this.loadNotifications();
    // Charger l'utilisateur actuel depuis les données de test
    if (!this.currentUser) {
      this.currentUser = this.mockDataService.getCurrentUser();
    }
  }
  // ============================================================================
  // MÉTHODES DE CHARGEMENT DES DONNÉES
  // ============================================================================
  loadConversations(page = 1) {
    if (this.isLoadingConversations) return;
    this.isLoadingConversations = true;
    this.messageService.getConversations().subscribe({
      next: conversations => {
        if (page === 1) {
          this.conversations = conversations;
        } else {
          this.conversations.push(...conversations);
        }
        this.conversationsPage = page;
        this.hasMoreConversations = conversations.length === 25;
        this.isLoadingConversations = false;
        this.cdr.detectChanges();
      },
      error: error => {
        console.warn('Service principal indisponible, utilisation des données de test:', error);
        // Fallback sur les données de test
        this.mockDataService.getConversations().subscribe({
          next: conversations => {
            if (page === 1) {
              this.conversations = conversations;
            } else {
              this.conversations.push(...conversations);
            }
            this.conversationsPage = page;
            this.hasMoreConversations = false; // Pas de pagination pour les données de test
            this.isLoadingConversations = false;
            this.cdr.detectChanges();
            if (page === 1) {
              this.toastService.showInfo('Mode démo - Données de test chargées');
            }
          },
          error: mockError => {
            console.error('Erreur lors du chargement des données de test:', mockError);
            this.isLoadingConversations = false;
            this.toastService.showError('Erreur lors du chargement des conversations');
          }
        });
      }
    });
  }
  loadUsers(page = 1) {
    if (this.isLoadingUsers) return;
    this.isLoadingUsers = true;
    this.messageService.getAllUsers(false, '', page, 25).subscribe({
      next: users => {
        if (page === 1) {
          this.users = users;
        } else {
          this.users.push(...users);
        }
        this.usersPage = page;
        this.hasMoreUsers = users.length === 25;
        this.isLoadingUsers = false;
        this.cdr.detectChanges();
      },
      error: error => {
        console.warn('Service principal indisponible, utilisation des données de test:', error);
        // Fallback sur les données de test
        this.mockDataService.getUsers().subscribe({
          next: users => {
            if (page === 1) {
              this.users = users;
            } else {
              this.users.push(...users);
            }
            this.usersPage = page;
            this.hasMoreUsers = false; // Pas de pagination pour les données de test
            this.isLoadingUsers = false;
            this.cdr.detectChanges();
          },
          error: mockError => {
            console.error('Erreur lors du chargement des données de test:', mockError);
            this.isLoadingUsers = false;
            this.toastService.showError('Erreur lors du chargement des utilisateurs');
          }
        });
      }
    });
  }
  loadNotifications() {
    if (this.isLoadingNotifications) return;
    this.isLoadingNotifications = true;
    this.messageService.getNotifications().subscribe({
      next: notifications => {
        this.notifications = notifications;
        this.isLoadingNotifications = false;
        this.cdr.detectChanges();
      },
      error: error => {
        console.warn('Service principal indisponible, utilisation des données de test:', error);
        // Fallback sur les données de test
        this.mockDataService.getNotifications().subscribe({
          next: notifications => {
            this.notifications = notifications;
            this.isLoadingNotifications = false;
            this.cdr.detectChanges();
          },
          error: mockError => {
            console.error('Erreur lors du chargement des données de test:', mockError);
            this.isLoadingNotifications = false;
            this.toastService.showError('Erreur lors du chargement des notifications');
          }
        });
      }
    });
  }
  // ============================================================================
  // MÉTHODES DE GESTION DES ÉVÉNEMENTS
  // ============================================================================
  handleNewMessage(message) {
    // Mettre à jour la conversation correspondante
    const conversationIndex = this.conversations.findIndex(conv => conv.id === message.conversationId);
    if (conversationIndex !== -1) {
      // Mettre à jour le dernier message
      this.conversations[conversationIndex].lastMessage = message;
      // Déplacer la conversation en haut de la liste
      const conversation = this.conversations.splice(conversationIndex, 1)[0];
      this.conversations.unshift(conversation);
      this.cdr.detectChanges();
    }
  }
  handleNewNotification(notification) {
    // Ajouter la nouvelle notification en haut de la liste
    this.notifications.unshift(notification);
    this.cdr.detectChanges();
    // Afficher une notification toast si ce n'est pas l'onglet actif
    if (this.activeTab !== 'notifications') {
      this.toastService.showInfo('Nouvelle notification reçue');
    }
  }
  markConversationAsSelected(conversationId) {
    // Marquer la conversation comme sélectionnée visuellement
    this.selectedConversationId = conversationId;
    this.cdr.detectChanges();
  }
  // ============================================================================
  // MÉTHODES DE NAVIGATION ET UI
  // ============================================================================
  switchTab(tab) {
    this.activeTab = tab;
    this.searchQuery = '';
    this.searchResults = [];
    this.isSearching = false;
    // Charger les données si nécessaire
    switch (tab) {
      case 'conversations':
        if (this.conversations.length === 0) {
          this.loadConversations();
        }
        break;
      case 'users':
        if (this.users.length === 0) {
          this.loadUsers();
        }
        break;
      case 'notifications':
        if (this.notifications.length === 0) {
          this.loadNotifications();
        }
        break;
    }
  }
  selectConversation(conversation) {
    if (!conversation.id) return;
    this.selectedConversationId = conversation.id;
    this.router.navigate(['/messages', conversation.id]);
    // Fermer le menu mobile si ouvert
    this.isMobileMenuOpen = false;
  }
  startConversationWithUser(user) {
    if (!user.id && !user._id) return;
    const userId = user.id || user._id;
    // Créer ou récupérer la conversation avec cet utilisateur
    this.messageService.createOrGetConversation(userId).subscribe({
      next: conversation => {
        this.selectConversation(conversation);
      },
      error: error => {
        console.warn('Service principal indisponible, utilisation des données de test:', error);
        // Fallback sur les données de test
        const currentUserId = this.currentUser?.id || '1';
        this.mockDataService.createConversation(userId, currentUserId).subscribe({
          next: conversation => {
            this.conversations.unshift(conversation);
            this.selectConversation(conversation);
            this.toastService.showSuccess('Conversation créée (mode démo)');
          },
          error: mockError => {
            console.error('Erreur lors de la création de la conversation:', mockError);
            this.toastService.showError('Erreur lors de la création de la conversation');
          }
        });
      }
    });
  }
  toggleMobileMenu() {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
  }
  // ============================================================================
  // MÉTHODES DE RECHERCHE
  // ============================================================================
  onSearchInput(event) {
    const query = event.target.value.trim();
    this.searchQuery = query;
    this.searchQuery$.next(query);
  }
  performSearch(query) {
    if (!query) {
      this.searchResults = [];
      this.isSearching = false;
      return;
    }
    this.isSearching = true;
    if (this.activeTab === 'conversations') {
      this.searchResults = this.conversations.filter(conv => conv.isGroup ? conv.groupName?.toLowerCase().includes(query.toLowerCase()) : conv.participants?.some(p => p.username?.toLowerCase().includes(query.toLowerCase())));
    } else if (this.activeTab === 'users') {
      this.searchResults = this.users.filter(user => user.username?.toLowerCase().includes(query.toLowerCase()) || user.email?.toLowerCase().includes(query.toLowerCase()));
    }
    this.cdr.detectChanges();
  }
  clearSearch() {
    this.searchQuery = '';
    this.searchResults = [];
    this.isSearching = false;
    this.searchQuery$.next('');
  }
  // ============================================================================
  // MÉTHODES DE PAGINATION
  // ============================================================================
  loadMoreConversations() {
    if (this.hasMoreConversations && !this.isLoadingConversations) {
      this.loadConversations(this.conversationsPage + 1);
    }
  }
  loadMoreUsers() {
    if (this.hasMoreUsers && !this.isLoadingUsers) {
      this.loadUsers(this.usersPage + 1);
    }
  }
  // ============================================================================
  // MÉTHODES UTILITAIRES POUR LE TEMPLATE
  // ============================================================================
  getConversationName(conversation) {
    if (conversation.isGroup) {
      return conversation.groupName || 'Groupe sans nom';
    }
    if (!this.currentUser) return 'Conversation';
    const currentUserId = this.currentUser.id || this.currentUser._id;
    const otherParticipant = conversation.participants?.find(p => (p.id || p._id) !== currentUserId);
    return otherParticipant?.username || 'Utilisateur inconnu';
  }
  getConversationAvatar(conversation) {
    if (conversation.isGroup) {
      return conversation.groupPhoto || '/assets/images/default-group.png';
    }
    if (!this.currentUser) return '/assets/images/default-avatar.png';
    const currentUserId = this.currentUser.id || this.currentUser._id;
    const otherParticipant = conversation.participants?.find(p => (p.id || p._id) !== currentUserId);
    return otherParticipant?.image || '/assets/images/default-avatar.png';
  }
  getLastMessagePreview(conversation) {
    if (!conversation.lastMessage) return 'Aucun message';
    const message = conversation.lastMessage;
    if (message.type === 'TEXT') {
      return message.content || '';
    } else if (message.type === 'IMAGE') {
      return '📷 Image';
    } else if (message.type === 'FILE') {
      return '📎 Fichier';
    } else if (message.type === 'VOICE_MESSAGE') {
      return '🎤 Message vocal';
    } else if (message.type === 'VIDEO') {
      return '🎥 Vidéo';
    }
    return 'Message';
  }
  formatLastMessageTime(timestamp) {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    if (diffInHours < 1) {
      return "À l'instant";
    } else if (diffInHours < 24) {
      return date.toLocaleTimeString('fr-FR', {
        hour: '2-digit',
        minute: '2-digit'
      });
    } else if (diffInHours < 168) {
      // 7 jours
      return date.toLocaleDateString('fr-FR', {
        weekday: 'short'
      });
    } else {
      return date.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit'
      });
    }
  }
  getUnreadCount(conversation) {
    return conversation.unreadCount || 0;
  }
  isUserOnline(user) {
    return user.isOnline || false;
  }
  trackByConversationId(index, conversation) {
    return conversation.id || conversation._id || index.toString();
  }
  trackByUserId(index, user) {
    return user.id || user._id || index.toString();
  }
  trackByNotificationId(index, notification) {
    return notification.id || index.toString();
  }
  markNotificationAsRead(notification) {
    if (!notification.id || notification.isRead) return;
    this.messageService.markNotificationAsRead(notification.id).subscribe({
      next: () => {
        notification.isRead = true;
        this.cdr.detectChanges();
      },
      error: error => {
        console.error('Erreur lors du marquage de la notification comme lue:', error);
        this.toastService.showError('Erreur lors du marquage de la notification');
      }
    });
  }
  // Type guards pour différencier User et Conversation dans les résultats de recherche
  isUser(item) {
    return 'username' in item && 'email' in item;
  }
  isConversation(item) {
    return 'participants' in item || 'isGroup' in item;
  }
  getNotificationTitle(notification) {
    switch (notification.type) {
      case 'NEW_MESSAGE':
        return 'Nouveau message';
      case 'FRIEND_REQUEST':
        return "Demande d'ami";
      case 'GROUP_INVITE':
        return 'Invitation de groupe';
      case 'MESSAGE_REACTION':
        return 'Réaction à un message';
      case 'SYSTEM_ALERT':
        return 'Alerte système';
      default:
        return 'Notification';
    }
  }
  // ============================================================================
  // MÉTHODES DE GESTION DES THÈMES
  // ============================================================================
  selectTheme(themeName) {
    this.themeService.setTheme(themeName);
    this.showThemeSelector = false;
    this.toastService.showSuccess(`Thème "${this.themeService.getCurrentTheme().displayName}" appliqué`);
  }
  static {
    this.ɵfac = function MessageLayoutComponent_Factory(t) {
      return new (t || MessageLayoutComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_message_service__WEBPACK_IMPORTED_MODULE_0__.MessageService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_auth_service__WEBPACK_IMPORTED_MODULE_1__.AuthService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_toast_service__WEBPACK_IMPORTED_MODULE_2__.ToastService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_theme_service__WEBPACK_IMPORTED_MODULE_3__.ThemeService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_services_mock_data_service__WEBPACK_IMPORTED_MODULE_4__.MockDataService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_7__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_7__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_core__WEBPACK_IMPORTED_MODULE_5__.ChangeDetectorRef));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineComponent"]({
      type: MessageLayoutComponent,
      selectors: [["app-message-layout"]],
      viewQuery: function MessageLayoutComponent_Query(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵviewQuery"](_c0, 5);
        }
        if (rf & 2) {
          let _t;
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵloadQuery"]()) && (ctx.searchInput = _t.first);
        }
      },
      decls: 47,
      vars: 44,
      consts: [[1, "message-layout", "h-screen", "bg-gray-900", "text-white", "flex"], [1, "sidebar", "w-80", "bg-gray-800", "border-r", "border-gray-700", "flex", "flex-col"], [1, "sidebar-header", "p-4", "border-b", "border-gray-700", "bg-gray-800"], [1, "flex", "items-center", "justify-between", "mb-4"], [1, "flex", "items-center", "space-x-3"], [1, "w-10", "h-10", "rounded-full", "border-2", "border-blue-500", 3, "src", "alt"], [1, "font-semibold", "text-white"], [1, "text-sm", "text-green-400"], [1, "flex", "items-center", "space-x-2"], [1, "relative"], ["title", "Changer de th\u00E8me", 1, "p-2", "rounded-lg", "bg-gray-700", "hover:bg-gray-600", "transition-colors", 3, "click"], [1, "fas", "fa-palette", "text-blue-400"], ["class", "absolute top-full right-0 mt-2 bg-gray-800 rounded-lg shadow-lg border border-gray-700 p-2 z-50 min-w-48", 4, "ngIf"], [1, "md:hidden", "p-2", "rounded-lg", "bg-gray-700", "hover:bg-gray-600", 3, "click"], [1, "fas", "fa-times", "text-white"], ["type", "text", "placeholder", "Rechercher...", 1, "w-full", "bg-gray-700", "border", "border-gray-600", "rounded-lg", "px-4", "py-2", "pl-10", "text-white", "placeholder-gray-400", "focus:outline-none", "focus:border-blue-500", 3, "ngModel", "ngModelChange", "input"], ["searchInput", ""], [1, "fas", "fa-search", "absolute", "left-3", "top-3", "text-gray-400"], ["class", "absolute right-3 top-3 text-gray-400 hover:text-white", 3, "click", 4, "ngIf"], [1, "tabs", "flex", "border-b", "border-gray-700"], [1, "tab", "flex-1", "py-3", "px-4", "text-center", "transition-all", "duration-200", 3, "click"], [1, "fas", "fa-comments", "mb-1"], [1, "text-xs"], [1, "fas", "fa-users", "mb-1"], [1, "tab", "flex-1", "py-3", "px-4", "text-center", "transition-all", "duration-200", "relative", 3, "click"], [1, "fas", "fa-bell", "mb-1"], ["class", "absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center", 4, "ngIf"], [1, "sidebar-content", "flex-1", "overflow-y-auto"], ["class", "conversations-list", 4, "ngIf"], ["class", "users-list", 4, "ngIf"], ["class", "notifications-list", 4, "ngIf"], [1, "main-content", "flex-1", "flex", "flex-col"], [1, "md:hidden", "p-4", "border-b", "border-gray-700", "bg-gray-800"], [1, "p-2", "rounded-lg", "bg-gray-700", "hover:bg-gray-600", 3, "click"], [1, "fas", "fa-bars", "text-white"], [1, "flex-1"], [1, "absolute", "top-full", "right-0", "mt-2", "bg-gray-800", "rounded-lg", "shadow-lg", "border", "border-gray-700", "p-2", "z-50", "min-w-48"], [1, "text-xs", "text-gray-400", "mb-2", "px-2"], ["class", "flex items-center space-x-3 p-2 hover:bg-gray-700 rounded cursor-pointer transition-colors", 3, "click", 4, "ngFor", "ngForOf"], [1, "flex", "items-center", "space-x-3", "p-2", "hover:bg-gray-700", "rounded", "cursor-pointer", "transition-colors", 3, "click"], [1, "w-4", "h-4", "rounded-full", "border-2"], [1, "text-white", "text-sm"], ["class", "fas fa-check text-blue-400 text-xs ml-auto", 4, "ngIf"], [1, "fas", "fa-check", "text-blue-400", "text-xs", "ml-auto"], [1, "absolute", "right-3", "top-3", "text-gray-400", "hover:text-white", 3, "click"], [1, "fas", "fa-times"], [1, "absolute", "-top-1", "-right-1", "bg-red-500", "text-white", "text-xs", "rounded-full", "w-5", "h-5", "flex", "items-center", "justify-center"], [1, "conversations-list"], ["class", "search-results", 4, "ngIf"], ["class", "p-8 text-center text-gray-400", 4, "ngIf"], [4, "ngIf"], [1, "search-results"], [1, "p-3", "text-sm", "text-gray-400", "border-b", "border-gray-700"], ["class", "conversation-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors", 3, "click", 4, "ngFor", "ngForOf"], [1, "conversation-item", "p-4", "hover:bg-gray-700", "cursor-pointer", "border-b", "border-gray-700", "transition-colors", 3, "click"], [1, "w-12", "h-12", "rounded-full", 3, "src", "alt"], [1, "flex-1", "min-w-0"], [1, "font-medium", "text-white", "truncate"], [1, "text-sm", "text-gray-400", "truncate"], [1, "p-8", "text-center", "text-gray-400"], [1, "fas", "fa-search", "text-4xl", "mb-4"], ["class", "p-8 text-center", 4, "ngIf"], ["class", "conversation-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors relative", 3, "bg-gray-700", "border-l-4", "border-blue-500", "click", 4, "ngFor", "ngForOf", "ngForTrackBy"], ["class", "p-4 text-center", 4, "ngIf"], [1, "p-8", "text-center"], [1, "animate-spin", "rounded-full", "h-8", "w-8", "border-b-2", "border-blue-500", "mx-auto"], [1, "text-gray-400", "mt-2"], [1, "conversation-item", "p-4", "hover:bg-gray-700", "cursor-pointer", "border-b", "border-gray-700", "transition-colors", "relative", 3, "click"], ["class", "absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-gray-800", 4, "ngIf"], [1, "flex", "items-center", "justify-between"], [1, "text-xs", "text-gray-400"], [1, "flex", "items-center", "justify-between", "mt-1"], ["class", "bg-blue-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center", 4, "ngIf"], [1, "absolute", "bottom-0", "right-0", "w-3", "h-3", "bg-green-500", "rounded-full", "border-2", "border-gray-800"], [1, "bg-blue-500", "text-white", "text-xs", "rounded-full", "px-2", "py-1", "min-w-[20px]", "text-center"], [1, "p-4", "text-center"], [1, "text-blue-400", "hover:text-blue-300", "disabled:text-gray-500", 3, "disabled", "click"], [1, "fas", "fa-comments", "text-4xl", "mb-4"], [1, "text-sm", "mt-2"], [1, "users-list"], ["class", "user-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors", 3, "click", 4, "ngFor", "ngForOf"], [1, "user-item", "p-4", "hover:bg-gray-700", "cursor-pointer", "border-b", "border-gray-700", "transition-colors", 3, "click"], ["class", "flex items-center space-x-3", 4, "ngIf"], [1, "text-blue-400"], [1, "fas", "fa-comment"], ["class", "user-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors", 3, "click", 4, "ngFor", "ngForOf", "ngForTrackBy"], ["class", "text-xs text-gray-500", 4, "ngIf"], [1, "text-right"], [1, "text-xs", "px-2", "py-1", "rounded-full"], [1, "text-blue-400", "mt-1"], [1, "text-xs", "text-gray-500"], [1, "fas", "fa-users", "text-4xl", "mb-4"], [1, "notifications-list"], ["class", "notification-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors", 3, "bg-gray-700", "click", 4, "ngFor", "ngForOf", "ngForTrackBy"], [1, "notification-item", "p-4", "hover:bg-gray-700", "cursor-pointer", "border-b", "border-gray-700", "transition-colors", 3, "click"], [1, "flex", "items-start", "space-x-3"], [1, "notification-icon", "p-2", "rounded-full"], [1, "text-sm", "text-gray-400", "mt-1"], [1, "text-xs", "text-gray-500", "mt-2"], ["class", "w-2 h-2 bg-blue-500 rounded-full", 4, "ngIf"], [1, "w-2", "h-2", "bg-blue-500", "rounded-full"], [1, "fas", "fa-bell", "text-4xl", "mb-4"]],
      template: function MessageLayoutComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "div", 3)(4, "div", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](5, "img", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](6, "div")(7, "h3", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](8);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](9, "p", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](10, "En ligne");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](11, "div", 8)(12, "div", 9)(13, "button", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function MessageLayoutComponent_Template_button_click_13_listener() {
            return ctx.showThemeSelector = !ctx.showThemeSelector;
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](14, "i", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](15, MessageLayoutComponent_div_15_Template, 4, 1, "div", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](16, "button", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function MessageLayoutComponent_Template_button_click_16_listener() {
            return ctx.toggleMobileMenu();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](17, "i", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](18, "div", 9)(19, "input", 15, 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("ngModelChange", function MessageLayoutComponent_Template_input_ngModelChange_19_listener($event) {
            return ctx.searchQuery = $event;
          })("input", function MessageLayoutComponent_Template_input_input_19_listener($event) {
            return ctx.onSearchInput($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](21, "i", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](22, MessageLayoutComponent_button_22_Template, 2, 0, "button", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](23, "div", 19)(24, "button", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function MessageLayoutComponent_Template_button_click_24_listener() {
            return ctx.switchTab("conversations");
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](25, "i", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](26, "div", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](27, "Discussions");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](28, "button", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function MessageLayoutComponent_Template_button_click_28_listener() {
            return ctx.switchTab("users");
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](29, "i", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](30, "div", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](31, "Contacts");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](32, "button", 24);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function MessageLayoutComponent_Template_button_click_32_listener() {
            return ctx.switchTab("notifications");
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](33, "i", 25);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](34, "div", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](35, "Notifications");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](36, MessageLayoutComponent_span_36_Template, 2, 1, "span", 26);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](37, "div", 27);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](38, MessageLayoutComponent_div_38_Template, 4, 3, "div", 28);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](39, MessageLayoutComponent_div_39_Template, 4, 3, "div", 29);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](40, MessageLayoutComponent_div_40_Template, 4, 4, "div", 30);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](41, "div", 31)(42, "div", 32)(43, "button", 33);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function MessageLayoutComponent_Template_button_click_43_listener() {
            return ctx.toggleMobileMenu();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](44, "i", 34);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](45, "div", 35);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](46, "router-outlet");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵclassProp"]("hidden", !ctx.isMobileMenuOpen)("md:flex", true);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("src", (ctx.currentUser == null ? null : ctx.currentUser.image) || "/assets/images/default-avatar.png", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵsanitizeUrl"])("alt", ctx.currentUser == null ? null : ctx.currentUser.username);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", ctx.currentUser == null ? null : ctx.currentUser.username, " ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](7);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.showThemeSelector);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngModel", ctx.searchQuery);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.searchQuery);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵclassProp"]("active", ctx.activeTab === "conversations")("text-blue-400", ctx.activeTab === "conversations")("border-b-2", ctx.activeTab === "conversations")("border-blue-500", ctx.activeTab === "conversations")("text-gray-400", ctx.activeTab !== "conversations");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵclassProp"]("active", ctx.activeTab === "users")("text-blue-400", ctx.activeTab === "users")("border-b-2", ctx.activeTab === "users")("border-blue-500", ctx.activeTab === "users")("text-gray-400", ctx.activeTab !== "users");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵclassProp"]("active", ctx.activeTab === "notifications")("text-blue-400", ctx.activeTab === "notifications")("border-b-2", ctx.activeTab === "notifications")("border-blue-500", ctx.activeTab === "notifications")("text-gray-400", ctx.activeTab !== "notifications");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.notifications.length > 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.activeTab === "conversations");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.activeTab === "users");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.activeTab === "notifications");
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_8__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_8__.NgIf, _angular_router__WEBPACK_IMPORTED_MODULE_7__.RouterOutlet, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.NgModel],
      styles: ["\n\n\n\n\n.message-layout[_ngcontent-%COMP%] {\n  height: 100vh;\n  --tw-bg-opacity: 1;\n  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n  font-family: \"Inter\", -apple-system, BlinkMacSystemFont, sans-serif;\n}\n\n\n\n\n\n\n.sidebar[_ngcontent-%COMP%] {\n  display: flex;\n  width: 20rem;\n  flex-direction: column;\n  border-right-width: 1px;\n  --tw-border-opacity: 1;\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\n  --tw-bg-opacity: 1;\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\n  background: linear-gradient(180deg, #1f2937 0%, #111827 100%);\n  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);\n}\n\n.sidebar-header[_ngcontent-%COMP%] {\n  border-bottom-width: 1px;\n  --tw-border-opacity: 1;\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\n  --tw-bg-opacity: 1;\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\n  padding: 1rem;\n  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);\n}\n\n\n\n\n\n\n.tabs[_ngcontent-%COMP%] {\n  display: flex;\n  border-bottom-width: 1px;\n  --tw-border-opacity: 1;\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\n  background: linear-gradient(135deg, #374151 0%, #1f2937 100%);\n}\n\n.tab[_ngcontent-%COMP%] {\n  flex: 1 1 0%;\n  cursor: pointer;\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n  padding-left: 1rem;\n  padding-right: 1rem;\n  text-align: center;\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 200ms;\n  position: relative;\n}\n\n.tab[_ngcontent-%COMP%]:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\n}\n\n.conversation-item.tab[_ngcontent-%COMP%]:hover {\n  border-left-width: 4px;\n  --tw-border-opacity: 1;\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\n  background: linear-gradient(135deg, #1e3a8a 0%, #1d4ed8 100%);\n  box-shadow: 0 2px 10px rgba(59, 130, 246, 0.2);\n}\n\n.tab.active[_ngcontent-%COMP%] {\n  border-bottom-width: 2px;\n  --tw-border-opacity: 1;\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\n  --tw-text-opacity: 1;\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\n  background: linear-gradient(135deg, #1e3a8a 0%, #1d4ed8 100%);\n  box-shadow: 0 2px 10px rgba(59, 130, 246, 0.3);\n}\n\n.tab[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  margin-bottom: 0.25rem;\n  display: block;\n  font-size: 1.125rem;\n  line-height: 1.75rem;\n}\n\n\n\n\n\n\n.sidebar-content[_ngcontent-%COMP%] {\n  flex: 1 1 0%;\n  overflow-y: auto;\n  scrollbar-width: thin;\n  scrollbar-color: #374151 #1f2937;\n}\n\n.sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar {\n  width: 6px;\n}\n\n.sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\n  background: #1f2937;\n}\n\n.sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\n  background: #374151;\n  border-radius: 3px;\n}\n\n.sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\n  background: #4b5563;\n}\n\n\n\n\n\n\n.conversation-item[_ngcontent-%COMP%], .user-item[_ngcontent-%COMP%], .notification-item[_ngcontent-%COMP%] {\n  cursor: pointer;\n  border-bottom-width: 1px;\n  --tw-border-opacity: 1;\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\n  padding: 1rem;\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 200ms;\n}\n\n.conversation-item[_ngcontent-%COMP%]:hover, .user-item[_ngcontent-%COMP%]:hover, .notification-item[_ngcontent-%COMP%]:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\n}\n\n.conversation-item[_ngcontent-%COMP%], .user-item[_ngcontent-%COMP%], .notification-item[_ngcontent-%COMP%] {\n  position: relative;\n}\n\n.conversation-item[_ngcontent-%COMP%]:hover, .user-item[_ngcontent-%COMP%]:hover, .notification-item[_ngcontent-%COMP%]:hover {\n  background: linear-gradient(135deg, #374151 0%, #1f2937 100%);\n  transform: translateX(2px);\n}\n\n.conversation-item.bg-gray-700[_ngcontent-%COMP%] {\n  border-left-width: 4px;\n  --tw-border-opacity: 1;\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\n  background: linear-gradient(135deg, #1e3a8a 0%, #1d4ed8 100%);\n  box-shadow: 0 2px 10px rgba(59, 130, 246, 0.2);\n}\n\n\n\n\n\n\n.user-avatar[_ngcontent-%COMP%], .conversation-avatar[_ngcontent-%COMP%] {\n  height: 3rem;\n  width: 3rem;\n  border-radius: 9999px;\n  border-width: 2px;\n  --tw-border-opacity: 1;\n  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\n  transition: all 0.2s ease;\n}\n\n.user-avatar[_ngcontent-%COMP%]:hover, .conversation-avatar[_ngcontent-%COMP%]:hover {\n  --tw-border-opacity: 1;\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\n  box-shadow: 0 0 15px rgba(59, 130, 246, 0.4);\n}\n\n.online-indicator[_ngcontent-%COMP%] {\n  position: absolute;\n  bottom: 0px;\n  right: 0px;\n  height: 0.75rem;\n  width: 0.75rem;\n  border-radius: 9999px;\n  border-width: 2px;\n  --tw-border-opacity: 1;\n  border-color: rgb(31 41 55 / var(--tw-border-opacity, 1));\n  --tw-bg-opacity: 1;\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\n}\n\n@keyframes _ngcontent-%COMP%_pulse {\n  0%,\n  100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.7;\n  }\n}\n\n\n\n\n\n\n.unread-badge[_ngcontent-%COMP%] {\n  min-width: 20px;\n  border-radius: 9999px;\n  --tw-bg-opacity: 1;\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n  padding-top: 0.25rem;\n  padding-bottom: 0.25rem;\n  text-align: center;\n  font-size: 0.75rem;\n  line-height: 1rem;\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\n  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);\n  animation: _ngcontent-%COMP%_badgePulse 2s infinite;\n}\n\n@keyframes _ngcontent-%COMP%_badgePulse {\n  0%,\n  100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.05);\n  }\n}\n\n.notification-badge[_ngcontent-%COMP%] {\n  position: absolute;\n  top: -0.25rem;\n  right: -0.25rem;\n  display: flex;\n  height: 1.25rem;\n  width: 1.25rem;\n  align-items: center;\n  justify-content: center;\n  border-radius: 9999px;\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\n  font-size: 0.75rem;\n  line-height: 1rem;\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\n  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.4);\n  animation: _ngcontent-%COMP%_notificationPulse 1s infinite;\n}\n\n@keyframes _ngcontent-%COMP%_notificationPulse {\n  0%,\n  100% {\n    transform: scale(1);\n    opacity: 1;\n  }\n  50% {\n    transform: scale(1.1);\n    opacity: 0.8;\n  }\n}\n\n\n\n\n\n\n.main-content[_ngcontent-%COMP%] {\n  display: flex;\n  flex: 1 1 0%;\n  flex-direction: column;\n  background: linear-gradient(180deg, #0f172a 0%, #111827 100%);\n}\n\n\n\n\n\n\n@media (max-width: 768px) {\n  .sidebar[_ngcontent-%COMP%] {\n    position: fixed;\n    top: 0px;\n    bottom: 0px;\n    left: 0px;\n    z-index: 50;\n    width: 20rem;\n    transform: translateX(-100%);\n    transition: transform 0.3s ease-in-out;\n  }\n\n  .sidebar.show[_ngcontent-%COMP%] {\n    transform: translateX(0);\n  }\n\n  .main-content[_ngcontent-%COMP%] {\n    width: 100%;\n  }\n}\n\n/*# sourceMappingURL=data:application/json;base64,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 */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 5074:
/*!****************************************************************!*\
  !*** ./node_modules/rxjs/dist/esm/internal/operators/delay.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   delay: () => (/* binding */ delay)
/* harmony export */ });
/* harmony import */ var _scheduler_async__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../scheduler/async */ 8473);
/* harmony import */ var _delayWhen__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./delayWhen */ 1534);
/* harmony import */ var _observable_timer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../observable/timer */ 4876);



function delay(due, scheduler = _scheduler_async__WEBPACK_IMPORTED_MODULE_0__.asyncScheduler) {
  const duration = (0,_observable_timer__WEBPACK_IMPORTED_MODULE_1__.timer)(due, scheduler);
  return (0,_delayWhen__WEBPACK_IMPORTED_MODULE_2__.delayWhen)(() => duration);
}

/***/ }),

/***/ 1534:
/*!********************************************************************!*\
  !*** ./node_modules/rxjs/dist/esm/internal/operators/delayWhen.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   delayWhen: () => (/* binding */ delayWhen)
/* harmony export */ });
/* harmony import */ var _observable_concat__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../observable/concat */ 4665);
/* harmony import */ var _take__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./take */ 4334);
/* harmony import */ var _ignoreElements__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ignoreElements */ 7242);
/* harmony import */ var _mapTo__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./mapTo */ 7378);
/* harmony import */ var _mergeMap__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./mergeMap */ 3255);
/* harmony import */ var _observable_innerFrom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../observable/innerFrom */ 2645);






function delayWhen(delayDurationSelector, subscriptionDelay) {
  if (subscriptionDelay) {
    return source => (0,_observable_concat__WEBPACK_IMPORTED_MODULE_0__.concat)(subscriptionDelay.pipe((0,_take__WEBPACK_IMPORTED_MODULE_1__.take)(1), (0,_ignoreElements__WEBPACK_IMPORTED_MODULE_2__.ignoreElements)()), source.pipe(delayWhen(delayDurationSelector)));
  }
  return (0,_mergeMap__WEBPACK_IMPORTED_MODULE_3__.mergeMap)((value, index) => (0,_observable_innerFrom__WEBPACK_IMPORTED_MODULE_4__.innerFrom)(delayDurationSelector(value, index)).pipe((0,_take__WEBPACK_IMPORTED_MODULE_1__.take)(1), (0,_mapTo__WEBPACK_IMPORTED_MODULE_5__.mapTo)(value)));
}

/***/ }),

/***/ 7242:
/*!*************************************************************************!*\
  !*** ./node_modules/rxjs/dist/esm/internal/operators/ignoreElements.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ignoreElements: () => (/* binding */ ignoreElements)
/* harmony export */ });
/* harmony import */ var _util_lift__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/lift */ 3200);
/* harmony import */ var _OperatorSubscriber__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./OperatorSubscriber */ 1687);
/* harmony import */ var _util_noop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util/noop */ 4318);



function ignoreElements() {
  return (0,_util_lift__WEBPACK_IMPORTED_MODULE_0__.operate)((source, subscriber) => {
    source.subscribe((0,_OperatorSubscriber__WEBPACK_IMPORTED_MODULE_1__.createOperatorSubscriber)(subscriber, _util_noop__WEBPACK_IMPORTED_MODULE_2__.noop));
  });
}

/***/ })

}]);
//# sourceMappingURL=default-src_app_views_front_messages_message-layout_message-layout_component_ts.js.map