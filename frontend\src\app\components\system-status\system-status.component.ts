import { Component, OnInit, OnDestroy } from '@angular/core';
import { Subscription, interval } from 'rxjs';
import { MessageService } from '../../services/message.service';
import { MockDataService } from '../../services/mock-data.service';
import { ThemeService } from '../../services/theme.service';
import { ToastService } from '../../services/toast.service';

interface SystemStatus {
  backend: 'online' | 'offline' | 'checking';
  frontend: 'online' | 'offline' | 'checking';
  database: 'online' | 'offline' | 'checking';
  websocket: 'online' | 'offline' | 'checking';
  mockData: 'available' | 'unavailable';
  theme: string;
  lastCheck: Date;
}

@Component({
  selector: 'app-system-status',
  template: `
    <div class="system-status-panel bg-gray-800 rounded-lg p-6 border border-gray-700">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-white flex items-center">
          <i class="fas fa-heartbeat text-blue-400 mr-2"></i>
          État du système
        </h3>
        <button 
          (click)="checkSystemStatus()"
          class="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors"
          [disabled]="isChecking"
        >
          <i class="fas fa-sync-alt mr-1" [class.fa-spin]="isChecking"></i>
          {{ isChecking ? 'Vérification...' : 'Actualiser' }}
        </button>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Backend Status -->
        <div class="status-item">
          <div class="flex items-center justify-between">
            <span class="text-gray-300">Backend</span>
            <div class="flex items-center">
              <div 
                class="w-3 h-3 rounded-full mr-2"
                [class.bg-green-500]="status.backend === 'online'"
                [class.bg-red-500]="status.backend === 'offline'"
                [class.bg-yellow-500]="status.backend === 'checking'"
                [class.animate-pulse]="status.backend === 'checking'"
              ></div>
              <span class="text-sm" [class.text-green-400]="status.backend === 'online'"
                    [class.text-red-400]="status.backend === 'offline'"
                    [class.text-yellow-400]="status.backend === 'checking'">
                {{ getStatusText(status.backend) }}
              </span>
            </div>
          </div>
        </div>

        <!-- Frontend Status -->
        <div class="status-item">
          <div class="flex items-center justify-between">
            <span class="text-gray-300">Frontend</span>
            <div class="flex items-center">
              <div 
                class="w-3 h-3 rounded-full mr-2"
                [class.bg-green-500]="status.frontend === 'online'"
                [class.bg-red-500]="status.frontend === 'offline'"
                [class.bg-yellow-500]="status.frontend === 'checking'"
                [class.animate-pulse]="status.frontend === 'checking'"
              ></div>
              <span class="text-sm" [class.text-green-400]="status.frontend === 'online'"
                    [class.text-red-400]="status.frontend === 'offline'"
                    [class.text-yellow-400]="status.frontend === 'checking'">
                {{ getStatusText(status.frontend) }}
              </span>
            </div>
          </div>
        </div>

        <!-- Database Status -->
        <div class="status-item">
          <div class="flex items-center justify-between">
            <span class="text-gray-300">Base de données</span>
            <div class="flex items-center">
              <div 
                class="w-3 h-3 rounded-full mr-2"
                [class.bg-green-500]="status.database === 'online'"
                [class.bg-red-500]="status.database === 'offline'"
                [class.bg-yellow-500]="status.database === 'checking'"
                [class.animate-pulse]="status.database === 'checking'"
              ></div>
              <span class="text-sm" [class.text-green-400]="status.database === 'online'"
                    [class.text-red-400]="status.database === 'offline'"
                    [class.text-yellow-400]="status.database === 'checking'">
                {{ getStatusText(status.database) }}
              </span>
            </div>
          </div>
        </div>

        <!-- WebSocket Status -->
        <div class="status-item">
          <div class="flex items-center justify-between">
            <span class="text-gray-300">WebSocket</span>
            <div class="flex items-center">
              <div 
                class="w-3 h-3 rounded-full mr-2"
                [class.bg-green-500]="status.websocket === 'online'"
                [class.bg-red-500]="status.websocket === 'offline'"
                [class.bg-yellow-500]="status.websocket === 'checking'"
                [class.animate-pulse]="status.websocket === 'checking'"
              ></div>
              <span class="text-sm" [class.text-green-400]="status.websocket === 'online'"
                    [class.text-red-400]="status.websocket === 'offline'"
                    [class.text-yellow-400]="status.websocket === 'checking'">
                {{ getStatusText(status.websocket) }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Mock Data Status -->
      <div class="mt-4 p-3 bg-gray-700 rounded border-l-4" 
           [class.border-green-500]="status.mockData === 'available'"
           [class.border-red-500]="status.mockData === 'unavailable'">
        <div class="flex items-center justify-between">
          <span class="text-gray-300">Données de test</span>
          <span class="text-sm" [class.text-green-400]="status.mockData === 'available'"
                [class.text-red-400]="status.mockData === 'unavailable'">
            {{ status.mockData === 'available' ? 'Disponibles' : 'Indisponibles' }}
          </span>
        </div>
        <p class="text-xs text-gray-400 mt-1">
          {{ status.mockData === 'available' ? 
             'Le mode démo est actif avec des données de test' : 
             'Aucune donnée de test disponible' }}
        </p>
      </div>

      <!-- Theme Status -->
      <div class="mt-4 p-3 bg-gray-700 rounded">
        <div class="flex items-center justify-between">
          <span class="text-gray-300">Thème actuel</span>
          <span class="text-sm text-blue-400">{{ status.theme }}</span>
        </div>
      </div>

      <!-- Last Check -->
      <div class="mt-4 text-xs text-gray-500 text-center">
        Dernière vérification : {{ status.lastCheck | date:'medium' }}
      </div>

      <!-- Test Actions -->
      <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-2">
        <button 
          (click)="testMockData()"
          class="px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded text-sm transition-colors"
        >
          <i class="fas fa-database mr-1"></i>
          Test données
        </button>
        <button 
          (click)="testThemes()"
          class="px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded text-sm transition-colors"
        >
          <i class="fas fa-palette mr-1"></i>
          Test thèmes
        </button>
        <button 
          (click)="testNotifications()"
          class="px-3 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded text-sm transition-colors"
        >
          <i class="fas fa-bell mr-1"></i>
          Test notifs
        </button>
      </div>
    </div>
  `,
  styles: [`
    .status-item {
      @apply p-3 bg-gray-700 rounded;
    }
    
    .status-item:hover {
      @apply bg-gray-600;
    }
  `]
})
export class SystemStatusComponent implements OnInit, OnDestroy {
  status: SystemStatus = {
    backend: 'checking',
    frontend: 'online', // Frontend is obviously online if this component is running
    database: 'checking',
    websocket: 'checking',
    mockData: 'checking' as any,
    theme: 'Chargement...',
    lastCheck: new Date()
  };

  isChecking = false;
  private subscription?: Subscription;

  constructor(
    private messageService: MessageService,
    private mockDataService: MockDataService,
    private themeService: ThemeService,
    private toastService: ToastService
  ) {}

  ngOnInit(): void {
    this.checkSystemStatus();
    
    // Auto-refresh every 30 seconds
    this.subscription = interval(30000).subscribe(() => {
      this.checkSystemStatus();
    });

    // Listen to theme changes
    this.themeService.currentTheme$.subscribe(theme => {
      this.status.theme = theme.displayName;
    });
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  async checkSystemStatus(): Promise<void> {
    this.isChecking = true;
    this.status.lastCheck = new Date();

    // Check mock data availability
    try {
      await this.mockDataService.getUsers().toPromise();
      this.status.mockData = 'available';
    } catch {
      this.status.mockData = 'unavailable';
    }

    // Check backend connectivity
    try {
      await this.messageService.getConversations().toPromise();
      this.status.backend = 'online';
      this.status.database = 'online';
      this.status.websocket = 'online';
    } catch {
      this.status.backend = 'offline';
      this.status.database = 'offline';
      this.status.websocket = 'offline';
    }

    this.isChecking = false;
  }

  getStatusText(status: string): string {
    switch (status) {
      case 'online': return 'En ligne';
      case 'offline': return 'Hors ligne';
      case 'checking': return 'Vérification...';
      default: return 'Inconnu';
    }
  }

  testMockData(): void {
    this.mockDataService.getUsers().subscribe({
      next: (users) => {
        this.toastService.showSuccess(`${users.length} utilisateurs de test chargés`);
      },
      error: () => {
        this.toastService.showError('Erreur lors du chargement des données de test');
      }
    });
  }

  testThemes(): void {
    const themes = this.themeService.getAvailableThemes();
    this.toastService.showInfo(`${themes.length} thèmes disponibles`);
  }

  testNotifications(): void {
    this.toastService.showSuccess('Test de notification réussi !');
    setTimeout(() => {
      this.toastService.showInfo('Notification d\'information');
    }, 1000);
    setTimeout(() => {
      this.toastService.showWarning('Notification d\'avertissement');
    }, 2000);
  }
}
