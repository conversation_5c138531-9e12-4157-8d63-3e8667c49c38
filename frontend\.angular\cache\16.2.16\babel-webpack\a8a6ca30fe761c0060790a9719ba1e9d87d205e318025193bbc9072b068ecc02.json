{"ast": null, "code": "import { Subscription, interval } from 'rxjs';\nimport { CallType } from 'src/app/models/message.model';\nimport { FormControl, FormGroup } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/message.service\";\nimport * as i2 from \"src/app/services/call.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"src/app/services/authuser.service\";\nimport * as i5 from \"src/app/services/toast.service\";\nimport * as i6 from \"src/app/services/logger.service\";\nimport * as i7 from \"@app/services/theme.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nfunction UserListComponent_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"Affichage de \", ctx_r0.users.length, \" sur \", ctx_r0.totalUsers, \" utilisateurs\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"Page \", ctx_r0.currentPage, \" sur \", ctx_r0.totalPages, \"\");\n  }\n}\nfunction UserListComponent_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵelement(1, \"div\", 46);\n    i0.ɵɵelementStart(2, \"div\", 47);\n    i0.ɵɵtext(3, \"Chargement des utilisateurs...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserListComponent_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 49);\n    i0.ɵɵelement(2, \"i\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 51);\n    i0.ɵɵtext(4, \"Aucun utilisateur trouv\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 52);\n    i0.ɵɵtext(6, \" Essayez un autre terme de recherche ou effacez les filtres \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserListComponent_ul_61_li_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 66);\n  }\n}\nfunction UserListComponent_ul_61_li_1_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ul_61_li_1_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const user_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.startAudioCall(user_r7.id || user_r7._id));\n    });\n    i0.ɵɵelement(1, \"i\", 68);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserListComponent_ul_61_li_1_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ul_61_li_1_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const user_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.startVideoCall(user_r7.id || user_r7._id));\n    });\n    i0.ɵɵelement(1, \"i\", 70);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserListComponent_ul_61_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 55)(1, \"div\", 56);\n    i0.ɵɵlistener(\"click\", function UserListComponent_ul_61_li_1_Template_div_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r18);\n      const user_r7 = restoredCtx.$implicit;\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.startConversation(user_r7.id || user_r7._id));\n    });\n    i0.ɵɵelementStart(2, \"div\", 57);\n    i0.ɵɵelement(3, \"img\", 58);\n    i0.ɵɵtemplate(4, UserListComponent_ul_61_li_1_span_4_Template, 1, 0, \"span\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 60)(6, \"h3\", 61);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 62);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 63);\n    i0.ɵɵtemplate(11, UserListComponent_ul_61_li_1_button_11_Template, 2, 0, \"button\", 64);\n    i0.ɵɵtemplate(12, UserListComponent_ul_61_li_1_button_12_Template, 2, 0, \"button\", 65);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const user_r7 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", user_r7.image || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", user_r7.isOnline);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", user_r7.username, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r7.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", user_r7.isOnline);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", user_r7.isOnline);\n  }\n}\nfunction UserListComponent_ul_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 53);\n    i0.ɵɵtemplate(1, UserListComponent_ul_61_li_1_Template, 13, 6, \"li\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.users);\n  }\n}\nfunction UserListComponent_div_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"div\", 72);\n    i0.ɵɵelement(2, \"div\", 73)(3, \"div\", 74)(4, \"div\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 47);\n    i0.ɵɵtext(6, \" Chargement de plus d'utilisateurs... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserListComponent_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 76)(1, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function UserListComponent_div_63_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.loadNextPage());\n    });\n    i0.ɵɵelement(2, \"i\", 78);\n    i0.ɵɵtext(3, \" Charger plus d'utilisateurs \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class UserListComponent {\n  constructor(MessageService, callService, router, route, authService, toastService, logger, themeService) {\n    this.MessageService = MessageService;\n    this.callService = callService;\n    this.router = router;\n    this.route = route;\n    this.authService = authService;\n    this.toastService = toastService;\n    this.logger = logger;\n    this.themeService = themeService;\n    this.users = [];\n    this.loading = true;\n    this.currentUserId = null;\n    // Pagination\n    this.currentPage = 1;\n    this.pageSize = 10;\n    this.totalUsers = 0;\n    this.totalPages = 0;\n    this.hasNextPage = false;\n    this.hasPreviousPage = false;\n    // Sorting and filtering\n    this.sortBy = 'username';\n    this.sortOrder = 'asc';\n    this.filterForm = new FormGroup({\n      searchQuery: new FormControl(''),\n      isOnline: new FormControl(null)\n    });\n    // Auto-refresh\n    this.autoRefreshEnabled = true;\n    this.autoRefreshInterval = 30000; // 30 seconds\n    this.loadingMore = false;\n    this.subscriptions = new Subscription();\n    this.isDarkMode$ = this.themeService.darkMode$;\n  }\n  ngOnInit() {\n    this.currentUserId = this.authService.getCurrentUserId();\n    this.setupFilterListeners();\n    this.setupAutoRefresh();\n    this.loadUsers();\n  }\n  setupFilterListeners() {\n    // Subscribe to search query changes\n    const searchSub = this.filterForm.get('searchQuery').valueChanges.subscribe(() => {\n      this.resetPagination();\n      this.loadUsers();\n    });\n    this.subscriptions.add(searchSub);\n    // Subscribe to online status filter changes\n    const onlineSub = this.filterForm.get('isOnline').valueChanges.subscribe(() => {\n      this.resetPagination();\n      this.loadUsers();\n    });\n    this.subscriptions.add(onlineSub);\n  }\n  setupAutoRefresh() {\n    if (this.autoRefreshEnabled) {\n      this.autoRefreshSubscription = interval(this.autoRefreshInterval).subscribe(() => {\n        if (!this.loading && !this.filterForm.get('searchQuery')?.value) {\n          this.loadUsers(true);\n        }\n      });\n    }\n  }\n  toggleAutoRefresh() {\n    this.autoRefreshEnabled = !this.autoRefreshEnabled;\n    if (this.autoRefreshEnabled) {\n      this.setupAutoRefresh();\n    } else if (this.autoRefreshSubscription) {\n      this.autoRefreshSubscription.unsubscribe();\n      this.autoRefreshSubscription = undefined;\n    }\n  }\n  resetPagination() {\n    this.currentPage = 1;\n  }\n  // Get searchQuery from the form\n  get searchQuery() {\n    return this.filterForm.get('searchQuery')?.value || '';\n  }\n  // Set searchQuery in the form\n  set searchQuery(value) {\n    this.filterForm.get('searchQuery')?.setValue(value);\n  }\n  // Helper function for template type casting\n  $any(item) {\n    return item;\n  }\n  loadUsers(forceRefresh = false) {\n    if (this.loadingMore) return;\n    this.loading = true;\n    const searchQuery = this.filterForm.get('searchQuery')?.value || '';\n    const isOnline = this.filterForm.get('isOnline')?.value;\n    const sub = this.MessageService.getAllUsers(forceRefresh, searchQuery, this.currentPage, this.pageSize, this.sortBy, this.sortOrder, isOnline === true ? true : undefined).subscribe({\n      next: users => {\n        if (!Array.isArray(users)) {\n          this.users = [];\n          this.loading = false;\n          this.loadingMore = false;\n          this.toastService.showError('Failed to load users: Invalid data');\n          return;\n        }\n        // If first page, replace users array; otherwise append\n        if (this.currentPage === 1) {\n          // Filter out current user\n          this.users = users.filter(user => {\n            if (!user) return false;\n            const userId = user.id || user._id;\n            return userId !== this.currentUserId;\n          });\n        } else {\n          // Append new users to existing array, avoiding duplicates and filtering out current user\n          const newUsers = users.filter(newUser => {\n            if (!newUser) return false;\n            const userId = newUser.id || newUser._id;\n            return userId !== this.currentUserId && !this.users.some(existingUser => (existingUser.id || existingUser._id) === userId);\n          });\n          this.users = [...this.users, ...newUsers];\n        }\n        // Update pagination metadata from service\n        const pagination = this.MessageService.currentUserPagination;\n        this.totalUsers = pagination.totalCount;\n        this.totalPages = pagination.totalPages;\n        this.hasNextPage = pagination.hasNextPage;\n        this.hasPreviousPage = pagination.hasPreviousPage;\n        this.loading = false;\n        this.loadingMore = false;\n      },\n      error: error => {\n        this.loading = false;\n        this.loadingMore = false;\n        this.toastService.showError(`Failed to load users: ${error.message || 'Unknown error'}`);\n        if (this.currentPage === 1) {\n          this.users = [];\n        }\n      },\n      complete: () => {\n        this.loading = false;\n        this.loadingMore = false;\n      }\n    });\n    this.subscriptions.add(sub);\n  }\n  startConversation(userId) {\n    if (!userId) {\n      this.toastService.showError('Cannot start conversation with undefined user');\n      return;\n    }\n    this.toastService.showInfo('Creating conversation...');\n    this.MessageService.createConversation(userId).subscribe({\n      next: conversation => {\n        if (!conversation || !conversation.id) {\n          this.toastService.showError('Failed to create conversation: Invalid response');\n          return;\n        }\n        this.router.navigate(['/messages/conversations/chat', conversation.id]).then(success => {\n          if (!success) {\n            this.toastService.showError('Failed to open conversation');\n          }\n        });\n      },\n      error: error => {\n        this.toastService.showError(`Failed to create conversation: ${error.message || 'Unknown error'}`);\n      }\n    });\n  }\n  startAudioCall(userId) {\n    if (!userId) return;\n    this.callService.initiateCall(userId, CallType.AUDIO).subscribe({\n      next: call => {\n        this.toastService.showSuccess('Audio call initiated');\n      },\n      error: error => {\n        this.toastService.showError('Failed to initiate audio call');\n      }\n    });\n  }\n  startVideoCall(userId) {\n    if (!userId) return;\n    this.callService.initiateCall(userId, CallType.VIDEO).subscribe({\n      next: call => {\n        this.toastService.showSuccess('Video call initiated');\n      },\n      error: error => {\n        this.toastService.showError('Failed to initiate video call');\n      }\n    });\n  }\n  loadNextPage() {\n    if (this.hasNextPage && !this.loading) {\n      this.loadingMore = true;\n      this.currentPage++;\n      this.loadUsers();\n    }\n  }\n  loadPreviousPage() {\n    if (this.hasPreviousPage && !this.loading) {\n      this.loadingMore = true;\n      this.currentPage--;\n      this.loadUsers();\n    }\n  }\n  refreshUsers() {\n    this.resetPagination();\n    this.loadUsers(true);\n  }\n  clearFilters() {\n    this.filterForm.reset({\n      searchQuery: '',\n      isOnline: null\n    });\n    this.resetPagination();\n    this.loadUsers(true);\n  }\n  changeSortOrder(field) {\n    if (this.sortBy === field) {\n      // Toggle sort order if clicking the same field\n      this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';\n    } else {\n      // Set new sort field with default ascending order\n      this.sortBy = field;\n      this.sortOrder = 'asc';\n    }\n    this.resetPagination();\n    this.loadUsers(true);\n  }\n  /**\n   * Navigue vers la liste des conversations\n   */\n  goBackToConversations() {\n    this.router.navigate(['/messages/conversations']);\n  }\n  ngOnDestroy() {\n    this.subscriptions.unsubscribe();\n    if (this.autoRefreshSubscription) {\n      this.autoRefreshSubscription.unsubscribe();\n    }\n  }\n  static {\n    this.ɵfac = function UserListComponent_Factory(t) {\n      return new (t || UserListComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.CallService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.AuthuserService), i0.ɵɵdirectiveInject(i5.ToastService), i0.ɵɵdirectiveInject(i6.LoggerService), i0.ɵɵdirectiveInject(i7.ThemeService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: UserListComponent,\n      selectors: [[\"app-user-list\"]],\n      decls: 64,\n      vars: 18,\n      consts: [[1, \"flex\", \"flex-col\", \"h-full\", \"futuristic-users-container\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/10\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#00f7ff]/10\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"top-[40%]\", \"right-[30%]\", \"w-40\", \"h-40\", \"rounded-full\", \"bg-gradient-to-br\", \"from-transparent\", \"to-transparent\", \"dark:from-[#00f7ff]/5\", \"dark:to-transparent\", \"blur-3xl\", \"opacity-0\", \"dark:opacity-100\"], [1, \"absolute\", \"bottom-[60%]\", \"left-[25%]\", \"w-32\", \"h-32\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-transparent\", \"to-transparent\", \"dark:from-[#00f7ff]/5\", \"dark:to-transparent\", \"blur-3xl\", \"opacity-0\", \"dark:opacity-100\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-0\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\"], [1, \"absolute\", \"inset-0\", \"opacity-0\", \"dark:opacity-100\", \"overflow-hidden\"], [1, \"h-px\", \"w-full\", \"bg-[#00f7ff]/20\", \"absolute\", \"animate-scan\"], [1, \"futuristic-users-header\"], [1, \"flex\", \"justify-between\", \"items-center\", \"mb-4\"], [1, \"futuristic-title\"], [1, \"flex\", \"space-x-2\"], [\"title\", \"Rafra\\u00EEchir la liste\", 1, \"futuristic-action-button\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\"], [1, \"futuristic-action-button\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\"], [1, \"space-y-3\"], [1, \"relative\"], [\"type\", \"text\", \"placeholder\", \"Rechercher des utilisateurs...\", 1, \"w-full\", \"pl-10\", \"pr-4\", \"py-2\", \"rounded-lg\", \"futuristic-input-field\", 3, \"ngModel\", \"ngModelChange\"], [1, \"fas\", \"fa-search\", \"absolute\", \"left-3\", \"top-3\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [1, \"futuristic-checkbox-container\"], [\"type\", \"checkbox\", \"id\", \"onlineFilter\", 1, \"futuristic-checkbox\", 3, \"checked\", \"change\"], [1, \"futuristic-checkbox-checkmark\"], [\"for\", \"onlineFilter\", 1, \"futuristic-label\"], [1, \"futuristic-label\"], [1, \"futuristic-select\", 3, \"change\"], [\"value\", \"username\", 3, \"selected\"], [\"value\", \"email\", 3, \"selected\"], [\"value\", \"lastActive\", 3, \"selected\"], [1, \"futuristic-sort-button\", 3, \"title\", \"click\"], [1, \"futuristic-clear-button\", 3, \"click\"], [\"class\", \"flex justify-between items-center futuristic-pagination-info\", 4, \"ngIf\"], [1, \"futuristic-users-list\", 3, \"scroll\"], [\"class\", \"futuristic-loading-container\", 4, \"ngIf\"], [\"class\", \"futuristic-empty-state\", 4, \"ngIf\"], [\"class\", \"futuristic-users-grid\", 4, \"ngIf\"], [\"class\", \"futuristic-loading-more\", 4, \"ngIf\"], [\"class\", \"futuristic-load-more-container\", 4, \"ngIf\"], [1, \"flex\", \"justify-between\", \"items-center\", \"futuristic-pagination-info\"], [1, \"futuristic-loading-container\"], [1, \"futuristic-loading-circle\"], [1, \"futuristic-loading-text\"], [1, \"futuristic-empty-state\"], [1, \"futuristic-empty-icon\"], [1, \"fas\", \"fa-users\"], [1, \"futuristic-empty-title\"], [1, \"futuristic-empty-text\"], [1, \"futuristic-users-grid\"], [\"class\", \"futuristic-user-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"futuristic-user-card\"], [1, \"futuristic-user-content\", 3, \"click\"], [1, \"futuristic-avatar\"], [\"alt\", \"User avatar\", 3, \"src\"], [\"class\", \"futuristic-online-indicator\", 4, \"ngIf\"], [1, \"futuristic-user-info\"], [1, \"futuristic-username\"], [1, \"futuristic-user-email\"], [1, \"futuristic-call-buttons\"], [\"class\", \"futuristic-call-button\", \"title\", \"Appel audio\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"futuristic-call-button\", \"title\", \"Appel vid\\u00E9o\", 3, \"click\", 4, \"ngIf\"], [1, \"futuristic-online-indicator\"], [\"title\", \"Appel audio\", 1, \"futuristic-call-button\", 3, \"click\"], [1, \"fas\", \"fa-phone\"], [\"title\", \"Appel vid\\u00E9o\", 1, \"futuristic-call-button\", 3, \"click\"], [1, \"fas\", \"fa-video\"], [1, \"futuristic-loading-more\"], [1, \"futuristic-loading-dots\"], [1, \"futuristic-loading-dot\", 2, \"animation-delay\", \"0s\"], [1, \"futuristic-loading-dot\", 2, \"animation-delay\", \"0.2s\"], [1, \"futuristic-loading-dot\", 2, \"animation-delay\", \"0.4s\"], [1, \"futuristic-load-more-container\"], [1, \"futuristic-load-more-button\", 3, \"click\"], [1, \"fas\", \"fa-chevron-down\", \"mr-2\"]],\n      template: function UserListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵpipe(1, \"async\");\n          i0.ɵɵelementStart(2, \"div\", 1);\n          i0.ɵɵelement(3, \"div\", 2)(4, \"div\", 3)(5, \"div\", 4)(6, \"div\", 5);\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7);\n          i0.ɵɵelement(9, \"div\", 8)(10, \"div\", 8)(11, \"div\", 8)(12, \"div\", 8)(13, \"div\", 8)(14, \"div\", 8)(15, \"div\", 8)(16, \"div\", 8)(17, \"div\", 8)(18, \"div\", 8)(19, \"div\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 9);\n          i0.ɵɵelement(21, \"div\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"div\", 11)(23, \"div\", 12)(24, \"h1\", 13);\n          i0.ɵɵtext(25, \"Nouvelle Conversation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 14)(27, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function UserListComponent_Template_button_click_27_listener() {\n            return ctx.refreshUsers();\n          });\n          i0.ɵɵelement(28, \"i\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function UserListComponent_Template_button_click_29_listener() {\n            return ctx.goBackToConversations();\n          });\n          i0.ɵɵelement(30, \"i\", 18);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(31, \"div\", 19)(32, \"div\", 20)(33, \"input\", 21);\n          i0.ɵɵlistener(\"ngModelChange\", function UserListComponent_Template_input_ngModelChange_33_listener($event) {\n            return ctx.searchQuery = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(34, \"i\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"div\", 23)(36, \"div\", 24)(37, \"div\", 25)(38, \"label\", 26)(39, \"input\", 27);\n          i0.ɵɵlistener(\"change\", function UserListComponent_Template_input_change_39_listener($event) {\n            let tmp_b_0;\n            return (tmp_b_0 = ctx.filterForm.get(\"isOnline\")) == null ? null : tmp_b_0.setValue($event.target.checked ? true : null);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(40, \"span\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"label\", 29);\n          i0.ɵɵtext(42, \"En ligne uniquement\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(43, \"div\", 25)(44, \"span\", 30);\n          i0.ɵɵtext(45, \"Trier par:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"select\", 31);\n          i0.ɵɵlistener(\"change\", function UserListComponent_Template_select_change_46_listener($event) {\n            return ctx.changeSortOrder($event.target.value);\n          });\n          i0.ɵɵelementStart(47, \"option\", 32);\n          i0.ɵɵtext(48, \" Nom \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"option\", 33);\n          i0.ɵɵtext(50, \" Email \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"option\", 34);\n          i0.ɵɵtext(52, \" Derni\\u00E8re activit\\u00E9 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(53, \"button\", 35);\n          i0.ɵɵlistener(\"click\", function UserListComponent_Template_button_click_53_listener() {\n            ctx.sortOrder = ctx.sortOrder === \"asc\" ? \"desc\" : \"asc\";\n            return ctx.loadUsers(true);\n          });\n          i0.ɵɵelement(54, \"i\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(55, \"button\", 36);\n          i0.ɵɵlistener(\"click\", function UserListComponent_Template_button_click_55_listener() {\n            return ctx.clearFilters();\n          });\n          i0.ɵɵtext(56, \" Effacer les filtres \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(57, UserListComponent_div_57_Template, 5, 4, \"div\", 37);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(58, \"div\", 38);\n          i0.ɵɵlistener(\"scroll\", function UserListComponent_Template_div_scroll_58_listener($event) {\n            return $event.target.scrollTop + $event.target.clientHeight >= $event.target.scrollHeight - 200 && ctx.loadNextPage();\n          });\n          i0.ɵɵtemplate(59, UserListComponent_div_59_Template, 4, 0, \"div\", 39);\n          i0.ɵɵtemplate(60, UserListComponent_div_60_Template, 7, 0, \"div\", 40);\n          i0.ɵɵtemplate(61, UserListComponent_ul_61_Template, 2, 1, \"ul\", 41);\n          i0.ɵɵtemplate(62, UserListComponent_div_62_Template, 7, 0, \"div\", 42);\n          i0.ɵɵtemplate(63, UserListComponent_div_63_Template, 4, 0, \"div\", 43);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          let tmp_2_0;\n          i0.ɵɵclassProp(\"dark\", i0.ɵɵpipeBind1(1, 16, ctx.isDarkMode$));\n          i0.ɵɵadvance(33);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"checked\", ((tmp_2_0 = ctx.filterForm.get(\"isOnline\")) == null ? null : tmp_2_0.value) === true);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"selected\", ctx.sortBy === \"username\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"selected\", ctx.sortBy === \"email\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"selected\", ctx.sortBy === \"lastActive\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"title\", ctx.sortOrder === \"asc\" ? \"Ordre croissant\" : \"Ordre d\\u00E9croissant\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassMap(ctx.sortOrder === \"asc\" ? \"fas fa-sort-up\" : \"fas fa-sort-down\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.totalUsers > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading && !ctx.users.length);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.users.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.users.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading && ctx.users.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasNextPage && !ctx.loading);\n        }\n      },\n      dependencies: [i8.NgForOf, i8.NgIf, i9.NgSelectOption, i9.ɵNgSelectMultipleOption, i9.DefaultValueAccessor, i9.NgControlStatus, i9.NgModel, i8.AsyncPipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJ1c2VyLWxpc3QuY29tcG9uZW50LmNzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvbWVzc2FnZXMvdXNlci1saXN0L3VzZXItbGlzdC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSxvS0FBb0siLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subscription", "interval", "CallType", "FormControl", "FormGroup", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate2", "ctx_r0", "users", "length", "totalUsers", "currentPage", "totalPages", "ɵɵelement", "ɵɵlistener", "UserListComponent_ul_61_li_1_button_11_Template_button_click_0_listener", "ɵɵrestoreView", "_r13", "user_r7", "ɵɵnextContext", "$implicit", "ctx_r11", "ɵɵresetView", "startAudioCall", "id", "_id", "UserListComponent_ul_61_li_1_button_12_Template_button_click_0_listener", "_r16", "ctx_r14", "startVideoCall", "UserListComponent_ul_61_li_1_Template_div_click_1_listener", "restoredCtx", "_r18", "ctx_r17", "startConversation", "ɵɵtemplate", "UserListComponent_ul_61_li_1_span_4_Template", "UserListComponent_ul_61_li_1_button_11_Template", "UserListComponent_ul_61_li_1_button_12_Template", "ɵɵproperty", "image", "ɵɵsanitizeUrl", "isOnline", "ɵɵtextInterpolate1", "username", "ɵɵtextInterpolate", "email", "UserListComponent_ul_61_li_1_Template", "ctx_r3", "UserListComponent_div_63_Template_button_click_1_listener", "_r20", "ctx_r19", "loadNextPage", "UserListComponent", "constructor", "MessageService", "callService", "router", "route", "authService", "toastService", "logger", "themeService", "loading", "currentUserId", "pageSize", "hasNextPage", "hasPreviousPage", "sortBy", "sortOrder", "filterForm", "searchQuery", "autoRefreshEnabled", "autoRefreshInterval", "loadingMore", "subscriptions", "isDarkMode$", "darkMode$", "ngOnInit", "getCurrentUserId", "setupFilterListeners", "setupAutoRefresh", "loadUsers", "searchSub", "get", "valueChanges", "subscribe", "resetPagination", "add", "onlineSub", "autoRefreshSubscription", "value", "toggleAutoRefresh", "unsubscribe", "undefined", "setValue", "$any", "item", "forceRefresh", "sub", "getAllUsers", "next", "Array", "isArray", "showError", "filter", "user", "userId", "newUsers", "newUser", "some", "existingUser", "pagination", "currentUserPagination", "totalCount", "error", "message", "complete", "showInfo", "createConversation", "conversation", "navigate", "then", "success", "initiateCall", "AUDIO", "call", "showSuccess", "VIDEO", "loadPreviousPage", "refreshUsers", "clearFilters", "reset", "changeSortOrder", "field", "goBackToConversations", "ngOnDestroy", "ɵɵdirectiveInject", "i1", "i2", "CallService", "i3", "Router", "ActivatedRoute", "i4", "AuthuserService", "i5", "ToastService", "i6", "LoggerService", "i7", "ThemeService", "selectors", "decls", "vars", "consts", "template", "UserListComponent_Template", "rf", "ctx", "UserListComponent_Template_button_click_27_listener", "UserListComponent_Template_button_click_29_listener", "UserListComponent_Template_input_ngModelChange_33_listener", "$event", "UserListComponent_Template_input_change_39_listener", "tmp_b_0", "target", "checked", "UserListComponent_Template_select_change_46_listener", "UserListComponent_Template_button_click_53_listener", "UserListComponent_Template_button_click_55_listener", "UserListComponent_div_57_Template", "UserListComponent_Template_div_scroll_58_listener", "scrollTop", "clientHeight", "scrollHeight", "UserListComponent_div_59_Template", "UserListComponent_div_60_Template", "UserListComponent_ul_61_Template", "UserListComponent_div_62_Template", "UserListComponent_div_63_Template", "ɵɵclassProp", "ɵɵpipeBind1", "tmp_2_0", "ɵɵclassMap"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\user-list\\user-list.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\user-list\\user-list.component.html"], "sourcesContent": ["// user-list.component.ts\nimport { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';\nimport { Subscription, interval, Observable } from 'rxjs';\nimport { User } from 'src/app/models/user.model';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport { ToastService } from 'src/app/services/toast.service';\nimport { MessageService } from 'src/app/services/message.service';\nimport { CallService } from 'src/app/services/call.service';\nimport { CallType, Call } from 'src/app/models/message.model';\nimport { LoggerService } from 'src/app/services/logger.service';\nimport { FormControl, FormGroup } from '@angular/forms';\nimport { ThemeService } from '@app/services/theme.service';\n\n@Component({\n  selector: 'app-user-list',\n  templateUrl: './user-list.component.html',\n  styleUrls: ['./user-list.component.css'],\n})\nexport class UserListComponent implements OnInit, OnDestroy {\n  users: User[] = [];\n  loading = true;\n  currentUserId: string | null = null;\n  isDarkMode$: Observable<boolean>;\n\n  // Pagination\n  currentPage = 1;\n  pageSize = 10;\n  totalUsers = 0;\n  totalPages = 0;\n  hasNextPage = false;\n  hasPreviousPage = false;\n\n  // Sorting and filtering\n  sortBy = 'username';\n  sortOrder = 'asc';\n  filterForm = new FormGroup({\n    searchQuery: new FormControl(''),\n    isOnline: new FormControl<boolean | null>(null),\n  });\n\n  // Auto-refresh\n  autoRefreshEnabled = true;\n  autoRefreshInterval = 30000; // 30 seconds\n  private autoRefreshSubscription?: Subscription;\n\n  private loadingMore = false;\n  private subscriptions: Subscription = new Subscription();\n\n  constructor(\n    private MessageService: MessageService,\n    private callService: CallService,\n    public router: Router,\n    public route: ActivatedRoute,\n    private authService: AuthuserService,\n    private toastService: ToastService,\n    private logger: LoggerService,\n    private themeService: ThemeService\n  ) {\n    this.isDarkMode$ = this.themeService.darkMode$;\n  }\n\n  ngOnInit(): void {\n    this.currentUserId = this.authService.getCurrentUserId();\n    this.setupFilterListeners();\n    this.setupAutoRefresh();\n    this.loadUsers();\n  }\n\n  private setupFilterListeners(): void {\n    // Subscribe to search query changes\n    const searchSub = this.filterForm\n      .get('searchQuery')!\n      .valueChanges.subscribe(() => {\n        this.resetPagination();\n        this.loadUsers();\n      });\n\n    this.subscriptions.add(searchSub);\n\n    // Subscribe to online status filter changes\n    const onlineSub = this.filterForm\n      .get('isOnline')!\n      .valueChanges.subscribe(() => {\n        this.resetPagination();\n        this.loadUsers();\n      });\n\n    this.subscriptions.add(onlineSub);\n  }\n\n  private setupAutoRefresh(): void {\n    if (this.autoRefreshEnabled) {\n      this.autoRefreshSubscription = interval(\n        this.autoRefreshInterval\n      ).subscribe(() => {\n        if (!this.loading && !this.filterForm.get('searchQuery')?.value) {\n          this.loadUsers(true);\n        }\n      });\n    }\n  }\n\n  toggleAutoRefresh(): void {\n    this.autoRefreshEnabled = !this.autoRefreshEnabled;\n\n    if (this.autoRefreshEnabled) {\n      this.setupAutoRefresh();\n    } else if (this.autoRefreshSubscription) {\n      this.autoRefreshSubscription.unsubscribe();\n      this.autoRefreshSubscription = undefined;\n    }\n  }\n\n  resetPagination(): void {\n    this.currentPage = 1;\n  }\n\n  // Get searchQuery from the form\n  get searchQuery(): string {\n    return this.filterForm.get('searchQuery')?.value || '';\n  }\n\n  // Set searchQuery in the form\n  set searchQuery(value: string) {\n    this.filterForm.get('searchQuery')?.setValue(value);\n  }\n\n  // Helper function for template type casting\n  $any(item: any): any {\n    return item;\n  }\n\n  loadUsers(forceRefresh = false): void {\n    if (this.loadingMore) return;\n\n    this.loading = true;\n\n    const searchQuery = this.filterForm.get('searchQuery')?.value || '';\n    const isOnline = this.filterForm.get('isOnline')?.value;\n\n    const sub = this.MessageService.getAllUsers(\n      forceRefresh,\n      searchQuery,\n      this.currentPage,\n      this.pageSize,\n      this.sortBy,\n      this.sortOrder,\n      isOnline === true ? true : undefined\n    ).subscribe({\n      next: (users) => {\n        if (!Array.isArray(users)) {\n          this.users = [];\n          this.loading = false;\n          this.loadingMore = false;\n          this.toastService.showError('Failed to load users: Invalid data');\n          return;\n        }\n\n        // If first page, replace users array; otherwise append\n        if (this.currentPage === 1) {\n          // Filter out current user\n          this.users = users.filter((user) => {\n            if (!user) return false;\n            const userId = user.id || user._id;\n            return userId !== this.currentUserId;\n          });\n        } else {\n          // Append new users to existing array, avoiding duplicates and filtering out current user\n          const newUsers = users.filter((newUser) => {\n            if (!newUser) return false;\n            const userId = newUser.id || newUser._id;\n            return (\n              userId !== this.currentUserId &&\n              !this.users.some(\n                (existingUser) =>\n                  (existingUser.id || existingUser._id) === userId\n              )\n            );\n          });\n\n          this.users = [...this.users, ...newUsers];\n        }\n\n        // Update pagination metadata from service\n        const pagination = this.MessageService.currentUserPagination;\n        this.totalUsers = pagination.totalCount;\n        this.totalPages = pagination.totalPages;\n        this.hasNextPage = pagination.hasNextPage;\n        this.hasPreviousPage = pagination.hasPreviousPage;\n\n        this.loading = false;\n        this.loadingMore = false;\n      },\n      error: (error) => {\n        this.loading = false;\n        this.loadingMore = false;\n        this.toastService.showError(\n          `Failed to load users: ${error.message || 'Unknown error'}`\n        );\n\n        if (this.currentPage === 1) {\n          this.users = [];\n        }\n      },\n      complete: () => {\n        this.loading = false;\n        this.loadingMore = false;\n      },\n    });\n\n    this.subscriptions.add(sub);\n  }\n\n  startConversation(userId: string | undefined) {\n    if (!userId) {\n      this.toastService.showError(\n        'Cannot start conversation with undefined user'\n      );\n      return;\n    }\n\n    this.toastService.showInfo('Creating conversation...');\n\n    this.MessageService.createConversation(userId).subscribe({\n      next: (conversation) => {\n        if (!conversation || !conversation.id) {\n          this.toastService.showError(\n            'Failed to create conversation: Invalid response'\n          );\n          return;\n        }\n\n        this.router\n          .navigate(['/messages/conversations/chat', conversation.id])\n          .then((success) => {\n            if (!success) {\n              this.toastService.showError('Failed to open conversation');\n            }\n          });\n      },\n      error: (error) => {\n        this.toastService.showError(\n          `Failed to create conversation: ${error.message || 'Unknown error'}`\n        );\n      },\n    });\n  }\n\n  startAudioCall(userId: string): void {\n    if (!userId) return;\n\n    this.callService.initiateCall(userId, CallType.AUDIO).subscribe({\n      next: (call: Call) => {\n        this.toastService.showSuccess('Audio call initiated');\n      },\n      error: (error: any) => {\n        this.toastService.showError('Failed to initiate audio call');\n      },\n    });\n  }\n\n  startVideoCall(userId: string): void {\n    if (!userId) return;\n\n    this.callService.initiateCall(userId, CallType.VIDEO).subscribe({\n      next: (call: Call) => {\n        this.toastService.showSuccess('Video call initiated');\n      },\n      error: (error: any) => {\n        this.toastService.showError('Failed to initiate video call');\n      },\n    });\n  }\n\n  loadNextPage(): void {\n    if (this.hasNextPage && !this.loading) {\n      this.loadingMore = true;\n      this.currentPage++;\n      this.loadUsers();\n    }\n  }\n\n  loadPreviousPage(): void {\n    if (this.hasPreviousPage && !this.loading) {\n      this.loadingMore = true;\n      this.currentPage--;\n      this.loadUsers();\n    }\n  }\n\n  refreshUsers(): void {\n    this.resetPagination();\n    this.loadUsers(true);\n  }\n\n  clearFilters(): void {\n    this.filterForm.reset({\n      searchQuery: '',\n      isOnline: null,\n    });\n    this.resetPagination();\n    this.loadUsers(true);\n  }\n\n  changeSortOrder(field: string): void {\n    if (this.sortBy === field) {\n      // Toggle sort order if clicking the same field\n      this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';\n    } else {\n      // Set new sort field with default ascending order\n      this.sortBy = field;\n      this.sortOrder = 'asc';\n    }\n\n    this.resetPagination();\n    this.loadUsers(true);\n  }\n\n  /**\n   * Navigue vers la liste des conversations\n   */\n  goBackToConversations(): void {\n    this.router.navigate(['/messages/conversations']);\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.unsubscribe();\n    if (this.autoRefreshSubscription) {\n      this.autoRefreshSubscription.unsubscribe();\n    }\n  }\n}\n", "<div\n  class=\"flex flex-col h-full futuristic-users-container\"\n  [class.dark]=\"isDarkMode$ | async\"\n>\n  <!-- Background decorative elements -->\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n    <!-- Gradient orbs -->\n    <div\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/10 dark:to-transparent blur-3xl\"\n    ></div>\n    <div\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/10 dark:to-transparent blur-3xl\"\n    ></div>\n\n    <!-- Additional glow effects for dark mode -->\n    <div\n      class=\"absolute top-[40%] right-[30%] w-40 h-40 rounded-full bg-gradient-to-br from-transparent to-transparent dark:from-[#00f7ff]/5 dark:to-transparent blur-3xl opacity-0 dark:opacity-100\"\n    ></div>\n    <div\n      class=\"absolute bottom-[60%] left-[25%] w-32 h-32 rounded-full bg-gradient-to-tl from-transparent to-transparent dark:from-[#00f7ff]/5 dark:to-transparent blur-3xl opacity-0 dark:opacity-100\"\n    ></div>\n\n    <!-- Grid pattern for light mode -->\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-0\">\n      <div class=\"h-full grid grid-cols-12\">\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n      </div>\n    </div>\n\n    <!-- Horizontal scan line effect for dark mode -->\n    <div class=\"absolute inset-0 opacity-0 dark:opacity-100 overflow-hidden\">\n      <div class=\"h-px w-full bg-[#00f7ff]/20 absolute animate-scan\"></div>\n    </div>\n  </div>\n  <!-- En-tête -->\n  <div class=\"futuristic-users-header\">\n    <div class=\"flex justify-between items-center mb-4\">\n      <h1 class=\"futuristic-title\">Nouvelle Conversation</h1>\n      <div class=\"flex space-x-2\">\n        <button\n          (click)=\"refreshUsers()\"\n          class=\"futuristic-action-button\"\n          title=\"Rafraîchir la liste\"\n        >\n          <i class=\"fas fa-sync-alt\"></i>\n        </button>\n        <button\n          (click)=\"goBackToConversations()\"\n          class=\"futuristic-action-button\"\n        >\n          <i class=\"fas fa-arrow-left\"></i>\n        </button>\n      </div>\n    </div>\n\n    <!-- Recherche et filtres -->\n    <div class=\"space-y-3\">\n      <!-- Recherche -->\n      <div class=\"relative\">\n        <input\n          [ngModel]=\"searchQuery\"\n          (ngModelChange)=\"searchQuery = $event\"\n          type=\"text\"\n          placeholder=\"Rechercher des utilisateurs...\"\n          class=\"w-full pl-10 pr-4 py-2 rounded-lg futuristic-input-field\"\n        />\n        <i\n          class=\"fas fa-search absolute left-3 top-3 text-[#6d6870] dark:text-[#a0a0a0]\"\n        ></i>\n      </div>\n\n      <!-- Filtres -->\n      <div class=\"flex items-center justify-between\">\n        <div class=\"flex items-center space-x-4\">\n          <!-- Filtre en ligne -->\n          <div class=\"flex items-center space-x-2\">\n            <label class=\"futuristic-checkbox-container\">\n              <input\n                type=\"checkbox\"\n                id=\"onlineFilter\"\n                class=\"futuristic-checkbox\"\n                [checked]=\"filterForm.get('isOnline')?.value === true\"\n                (change)=\"\n                  filterForm\n                    .get('isOnline')\n                    ?.setValue($any($event.target).checked ? true : null)\n                \"\n              />\n              <span class=\"futuristic-checkbox-checkmark\"></span>\n            </label>\n            <label for=\"onlineFilter\" class=\"futuristic-label\"\n              >En ligne uniquement</label\n            >\n          </div>\n\n          <!-- Options de tri -->\n          <div class=\"flex items-center space-x-2\">\n            <span class=\"futuristic-label\">Trier par:</span>\n            <select\n              (change)=\"changeSortOrder($any($event.target).value)\"\n              class=\"futuristic-select\"\n            >\n              <option [selected]=\"sortBy === 'username'\" value=\"username\">\n                Nom\n              </option>\n              <option [selected]=\"sortBy === 'email'\" value=\"email\">\n                Email\n              </option>\n              <option [selected]=\"sortBy === 'lastActive'\" value=\"lastActive\">\n                Dernière activité\n              </option>\n            </select>\n            <button\n              (click)=\"\n                sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';\n                loadUsers(true)\n              \"\n              class=\"futuristic-sort-button\"\n              [title]=\"\n                sortOrder === 'asc' ? 'Ordre croissant' : 'Ordre décroissant'\n              \"\n            >\n              <i\n                [class]=\"\n                  sortOrder === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down'\n                \"\n              ></i>\n            </button>\n          </div>\n        </div>\n\n        <!-- Effacer les filtres -->\n        <button (click)=\"clearFilters()\" class=\"futuristic-clear-button\">\n          Effacer les filtres\n        </button>\n      </div>\n\n      <!-- Info pagination -->\n      <div\n        *ngIf=\"totalUsers > 0\"\n        class=\"flex justify-between items-center futuristic-pagination-info\"\n      >\n        <span\n          >Affichage de {{ users.length }} sur\n          {{ totalUsers }} utilisateurs</span\n        >\n        <span>Page {{ currentPage }} sur {{ totalPages }}</span>\n      </div>\n    </div>\n  </div>\n\n  <!-- Liste des utilisateurs -->\n  <div\n    class=\"futuristic-users-list\"\n    (scroll)=\"\n      $any($event.target).scrollTop + $any($event.target).clientHeight >=\n        $any($event.target).scrollHeight - 200 && loadNextPage()\n    \"\n  >\n    <!-- État de chargement -->\n    <div *ngIf=\"loading && !users.length\" class=\"futuristic-loading-container\">\n      <div class=\"futuristic-loading-circle\"></div>\n      <div class=\"futuristic-loading-text\">Chargement des utilisateurs...</div>\n    </div>\n\n    <!-- État vide -->\n    <div *ngIf=\"!loading && users.length === 0\" class=\"futuristic-empty-state\">\n      <div class=\"futuristic-empty-icon\">\n        <i class=\"fas fa-users\"></i>\n      </div>\n      <h3 class=\"futuristic-empty-title\">Aucun utilisateur trouvé</h3>\n      <p class=\"futuristic-empty-text\">\n        Essayez un autre terme de recherche ou effacez les filtres\n      </p>\n    </div>\n\n    <!-- Liste des utilisateurs -->\n    <ul *ngIf=\"users.length > 0\" class=\"futuristic-users-grid\">\n      <li *ngFor=\"let user of users\" class=\"futuristic-user-card\">\n        <div\n          class=\"futuristic-user-content\"\n          (click)=\"startConversation(user.id || user._id)\"\n        >\n          <div class=\"futuristic-avatar\">\n            <img\n              [src]=\"user.image || 'assets/images/default-avatar.png'\"\n              alt=\"User avatar\"\n            />\n            <span\n              *ngIf=\"user.isOnline\"\n              class=\"futuristic-online-indicator\"\n            ></span>\n          </div>\n          <div class=\"futuristic-user-info\">\n            <h3 class=\"futuristic-username\">\n              {{ user.username }}\n            </h3>\n            <p class=\"futuristic-user-email\">{{ user.email }}</p>\n          </div>\n        </div>\n\n        <!-- Boutons d'appel -->\n        <div class=\"futuristic-call-buttons\">\n          <button\n            *ngIf=\"user.isOnline\"\n            (click)=\"startAudioCall(user.id || user._id)\"\n            class=\"futuristic-call-button\"\n            title=\"Appel audio\"\n          >\n            <i class=\"fas fa-phone\"></i>\n          </button>\n          <button\n            *ngIf=\"user.isOnline\"\n            (click)=\"startVideoCall(user.id || user._id)\"\n            class=\"futuristic-call-button\"\n            title=\"Appel vidéo\"\n          >\n            <i class=\"fas fa-video\"></i>\n          </button>\n        </div>\n      </li>\n    </ul>\n\n    <!-- Indicateur de chargement supplémentaire -->\n    <div *ngIf=\"loading && users.length > 0\" class=\"futuristic-loading-more\">\n      <div class=\"futuristic-loading-dots\">\n        <div class=\"futuristic-loading-dot\" style=\"animation-delay: 0s\"></div>\n        <div class=\"futuristic-loading-dot\" style=\"animation-delay: 0.2s\"></div>\n        <div class=\"futuristic-loading-dot\" style=\"animation-delay: 0.4s\"></div>\n      </div>\n      <div class=\"futuristic-loading-text\">\n        Chargement de plus d'utilisateurs...\n      </div>\n    </div>\n\n    <!-- Bouton de chargement supplémentaire -->\n    <div *ngIf=\"hasNextPage && !loading\" class=\"futuristic-load-more-container\">\n      <button (click)=\"loadNextPage()\" class=\"futuristic-load-more-button\">\n        <i class=\"fas fa-chevron-down mr-2\"></i>\n        Charger plus d'utilisateurs\n      </button>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAEA,SAASA,YAAY,EAAEC,QAAQ,QAAoB,MAAM;AAOzD,SAASC,QAAQ,QAAc,8BAA8B;AAE7D,SAASC,WAAW,EAAEC,SAAS,QAAQ,gBAAgB;;;;;;;;;;;;;ICyIjDC,EAAA,CAAAC,cAAA,cAGC;IAEID,EAAA,CAAAE,MAAA,GAC4B;IAAAF,EAAA,CAAAG,YAAA,EAC9B;IACDH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAHrDH,EAAA,CAAAI,SAAA,GAC4B;IAD5BJ,EAAA,CAAAK,kBAAA,kBAAAC,MAAA,CAAAC,KAAA,CAAAC,MAAA,WAAAF,MAAA,CAAAG,UAAA,kBAC4B;IAEzBT,EAAA,CAAAI,SAAA,GAA2C;IAA3CJ,EAAA,CAAAK,kBAAA,UAAAC,MAAA,CAAAI,WAAA,WAAAJ,MAAA,CAAAK,UAAA,KAA2C;;;;;IAcrDX,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAY,SAAA,cAA6C;IAC7CZ,EAAA,CAAAC,cAAA,cAAqC;IAAAD,EAAA,CAAAE,MAAA,qCAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAI3EH,EAAA,CAAAC,cAAA,cAA2E;IAEvED,EAAA,CAAAY,SAAA,YAA4B;IAC9BZ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAAmC;IAAAD,EAAA,CAAAE,MAAA,oCAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChEH,EAAA,CAAAC,cAAA,YAAiC;IAC/BD,EAAA,CAAAE,MAAA,mEACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAeEH,EAAA,CAAAY,SAAA,eAGQ;;;;;;IAYVZ,EAAA,CAAAC,cAAA,iBAKC;IAHCD,EAAA,CAAAa,UAAA,mBAAAC,wEAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAjB,EAAA,CAAAkB,aAAA,GAAAC,SAAA;MAAA,MAAAC,OAAA,GAAApB,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAqB,WAAA,CAAAD,OAAA,CAAAE,cAAA,CAAAL,OAAA,CAAAM,EAAA,IAAAN,OAAA,CAAAO,GAAA,CAAmC;IAAA,EAAC;IAI7CxB,EAAA,CAAAY,SAAA,YAA4B;IAC9BZ,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAKC;IAHCD,EAAA,CAAAa,UAAA,mBAAAY,wEAAA;MAAAzB,EAAA,CAAAe,aAAA,CAAAW,IAAA;MAAA,MAAAT,OAAA,GAAAjB,EAAA,CAAAkB,aAAA,GAAAC,SAAA;MAAA,MAAAQ,OAAA,GAAA3B,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAqB,WAAA,CAAAM,OAAA,CAAAC,cAAA,CAAAX,OAAA,CAAAM,EAAA,IAAAN,OAAA,CAAAO,GAAA,CAAmC;IAAA,EAAC;IAI7CxB,EAAA,CAAAY,SAAA,YAA4B;IAC9BZ,EAAA,CAAAG,YAAA,EAAS;;;;;;IAxCbH,EAAA,CAAAC,cAAA,aAA4D;IAGxDD,EAAA,CAAAa,UAAA,mBAAAgB,2DAAA;MAAA,MAAAC,WAAA,GAAA9B,EAAA,CAAAe,aAAA,CAAAgB,IAAA;MAAA,MAAAd,OAAA,GAAAa,WAAA,CAAAX,SAAA;MAAA,MAAAa,OAAA,GAAAhC,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAqB,WAAA,CAAAW,OAAA,CAAAC,iBAAA,CAAAhB,OAAA,CAAAM,EAAA,IAAAN,OAAA,CAAAO,GAAA,CAAsC;IAAA,EAAC;IAEhDxB,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAAY,SAAA,cAGE;IACFZ,EAAA,CAAAkC,UAAA,IAAAC,4CAAA,mBAGQ;IACVnC,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAkC;IAE9BD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,YAAiC;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAKzDH,EAAA,CAAAC,cAAA,eAAqC;IACnCD,EAAA,CAAAkC,UAAA,KAAAE,+CAAA,qBAOS;IACTpC,EAAA,CAAAkC,UAAA,KAAAG,+CAAA,qBAOS;IACXrC,EAAA,CAAAG,YAAA,EAAM;;;;IAlCAH,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAsC,UAAA,QAAArB,OAAA,CAAAsB,KAAA,wCAAAvC,EAAA,CAAAwC,aAAA,CAAwD;IAIvDxC,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAsC,UAAA,SAAArB,OAAA,CAAAwB,QAAA,CAAmB;IAMpBzC,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAA0C,kBAAA,MAAAzB,OAAA,CAAA0B,QAAA,MACF;IACiC3C,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAA4C,iBAAA,CAAA3B,OAAA,CAAA4B,KAAA,CAAgB;IAOhD7C,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAsC,UAAA,SAAArB,OAAA,CAAAwB,QAAA,CAAmB;IAQnBzC,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAsC,UAAA,SAAArB,OAAA,CAAAwB,QAAA,CAAmB;;;;;IAnC5BzC,EAAA,CAAAC,cAAA,aAA2D;IACzDD,EAAA,CAAAkC,UAAA,IAAAY,qCAAA,kBA0CK;IACP9C,EAAA,CAAAG,YAAA,EAAK;;;;IA3CkBH,EAAA,CAAAI,SAAA,GAAQ;IAARJ,EAAA,CAAAsC,UAAA,YAAAS,MAAA,CAAAxC,KAAA,CAAQ;;;;;IA8C/BP,EAAA,CAAAC,cAAA,cAAyE;IAErED,EAAA,CAAAY,SAAA,cAAsE;IAGxEZ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAqC;IACnCD,EAAA,CAAAE,MAAA,6CACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAIRH,EAAA,CAAAC,cAAA,cAA4E;IAClED,EAAA,CAAAa,UAAA,mBAAAmC,0DAAA;MAAAhD,EAAA,CAAAe,aAAA,CAAAkC,IAAA;MAAA,MAAAC,OAAA,GAAAlD,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAqB,WAAA,CAAA6B,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAC9BnD,EAAA,CAAAY,SAAA,YAAwC;IACxCZ,EAAA,CAAAE,MAAA,oCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;ADvOf,OAAM,MAAOiD,iBAAiB;EA8B5BC,YACUC,cAA8B,EAC9BC,WAAwB,EACzBC,MAAc,EACdC,KAAqB,EACpBC,WAA4B,EAC5BC,YAA0B,EAC1BC,MAAqB,EACrBC,YAA0B;IAP1B,KAAAP,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACZ,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACJ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IArCtB,KAAAtD,KAAK,GAAW,EAAE;IAClB,KAAAuD,OAAO,GAAG,IAAI;IACd,KAAAC,aAAa,GAAkB,IAAI;IAGnC;IACA,KAAArD,WAAW,GAAG,CAAC;IACf,KAAAsD,QAAQ,GAAG,EAAE;IACb,KAAAvD,UAAU,GAAG,CAAC;IACd,KAAAE,UAAU,GAAG,CAAC;IACd,KAAAsD,WAAW,GAAG,KAAK;IACnB,KAAAC,eAAe,GAAG,KAAK;IAEvB;IACA,KAAAC,MAAM,GAAG,UAAU;IACnB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,UAAU,GAAG,IAAItE,SAAS,CAAC;MACzBuE,WAAW,EAAE,IAAIxE,WAAW,CAAC,EAAE,CAAC;MAChC2C,QAAQ,EAAE,IAAI3C,WAAW,CAAiB,IAAI;KAC/C,CAAC;IAEF;IACA,KAAAyE,kBAAkB,GAAG,IAAI;IACzB,KAAAC,mBAAmB,GAAG,KAAK,CAAC,CAAC;IAGrB,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,aAAa,GAAiB,IAAI/E,YAAY,EAAE;IAYtD,IAAI,CAACgF,WAAW,GAAG,IAAI,CAACd,YAAY,CAACe,SAAS;EAChD;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACd,aAAa,GAAG,IAAI,CAACL,WAAW,CAACoB,gBAAgB,EAAE;IACxD,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,SAAS,EAAE;EAClB;EAEQF,oBAAoBA,CAAA;IAC1B;IACA,MAAMG,SAAS,GAAG,IAAI,CAACb,UAAU,CAC9Bc,GAAG,CAAC,aAAa,CAAE,CACnBC,YAAY,CAACC,SAAS,CAAC,MAAK;MAC3B,IAAI,CAACC,eAAe,EAAE;MACtB,IAAI,CAACL,SAAS,EAAE;IAClB,CAAC,CAAC;IAEJ,IAAI,CAACP,aAAa,CAACa,GAAG,CAACL,SAAS,CAAC;IAEjC;IACA,MAAMM,SAAS,GAAG,IAAI,CAACnB,UAAU,CAC9Bc,GAAG,CAAC,UAAU,CAAE,CAChBC,YAAY,CAACC,SAAS,CAAC,MAAK;MAC3B,IAAI,CAACC,eAAe,EAAE;MACtB,IAAI,CAACL,SAAS,EAAE;IAClB,CAAC,CAAC;IAEJ,IAAI,CAACP,aAAa,CAACa,GAAG,CAACC,SAAS,CAAC;EACnC;EAEQR,gBAAgBA,CAAA;IACtB,IAAI,IAAI,CAACT,kBAAkB,EAAE;MAC3B,IAAI,CAACkB,uBAAuB,GAAG7F,QAAQ,CACrC,IAAI,CAAC4E,mBAAmB,CACzB,CAACa,SAAS,CAAC,MAAK;QACf,IAAI,CAAC,IAAI,CAACvB,OAAO,IAAI,CAAC,IAAI,CAACO,UAAU,CAACc,GAAG,CAAC,aAAa,CAAC,EAAEO,KAAK,EAAE;UAC/D,IAAI,CAACT,SAAS,CAAC,IAAI,CAAC;;MAExB,CAAC,CAAC;;EAEN;EAEAU,iBAAiBA,CAAA;IACf,IAAI,CAACpB,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;IAElD,IAAI,IAAI,CAACA,kBAAkB,EAAE;MAC3B,IAAI,CAACS,gBAAgB,EAAE;KACxB,MAAM,IAAI,IAAI,CAACS,uBAAuB,EAAE;MACvC,IAAI,CAACA,uBAAuB,CAACG,WAAW,EAAE;MAC1C,IAAI,CAACH,uBAAuB,GAAGI,SAAS;;EAE5C;EAEAP,eAAeA,CAAA;IACb,IAAI,CAAC5E,WAAW,GAAG,CAAC;EACtB;EAEA;EACA,IAAI4D,WAAWA,CAAA;IACb,OAAO,IAAI,CAACD,UAAU,CAACc,GAAG,CAAC,aAAa,CAAC,EAAEO,KAAK,IAAI,EAAE;EACxD;EAEA;EACA,IAAIpB,WAAWA,CAACoB,KAAa;IAC3B,IAAI,CAACrB,UAAU,CAACc,GAAG,CAAC,aAAa,CAAC,EAAEW,QAAQ,CAACJ,KAAK,CAAC;EACrD;EAEA;EACAK,IAAIA,CAACC,IAAS;IACZ,OAAOA,IAAI;EACb;EAEAf,SAASA,CAACgB,YAAY,GAAG,KAAK;IAC5B,IAAI,IAAI,CAACxB,WAAW,EAAE;IAEtB,IAAI,CAACX,OAAO,GAAG,IAAI;IAEnB,MAAMQ,WAAW,GAAG,IAAI,CAACD,UAAU,CAACc,GAAG,CAAC,aAAa,CAAC,EAAEO,KAAK,IAAI,EAAE;IACnE,MAAMjD,QAAQ,GAAG,IAAI,CAAC4B,UAAU,CAACc,GAAG,CAAC,UAAU,CAAC,EAAEO,KAAK;IAEvD,MAAMQ,GAAG,GAAG,IAAI,CAAC5C,cAAc,CAAC6C,WAAW,CACzCF,YAAY,EACZ3B,WAAW,EACX,IAAI,CAAC5D,WAAW,EAChB,IAAI,CAACsD,QAAQ,EACb,IAAI,CAACG,MAAM,EACX,IAAI,CAACC,SAAS,EACd3B,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAGoD,SAAS,CACrC,CAACR,SAAS,CAAC;MACVe,IAAI,EAAG7F,KAAK,IAAI;QACd,IAAI,CAAC8F,KAAK,CAACC,OAAO,CAAC/F,KAAK,CAAC,EAAE;UACzB,IAAI,CAACA,KAAK,GAAG,EAAE;UACf,IAAI,CAACuD,OAAO,GAAG,KAAK;UACpB,IAAI,CAACW,WAAW,GAAG,KAAK;UACxB,IAAI,CAACd,YAAY,CAAC4C,SAAS,CAAC,oCAAoC,CAAC;UACjE;;QAGF;QACA,IAAI,IAAI,CAAC7F,WAAW,KAAK,CAAC,EAAE;UAC1B;UACA,IAAI,CAACH,KAAK,GAAGA,KAAK,CAACiG,MAAM,CAAEC,IAAI,IAAI;YACjC,IAAI,CAACA,IAAI,EAAE,OAAO,KAAK;YACvB,MAAMC,MAAM,GAAGD,IAAI,CAAClF,EAAE,IAAIkF,IAAI,CAACjF,GAAG;YAClC,OAAOkF,MAAM,KAAK,IAAI,CAAC3C,aAAa;UACtC,CAAC,CAAC;SACH,MAAM;UACL;UACA,MAAM4C,QAAQ,GAAGpG,KAAK,CAACiG,MAAM,CAAEI,OAAO,IAAI;YACxC,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;YAC1B,MAAMF,MAAM,GAAGE,OAAO,CAACrF,EAAE,IAAIqF,OAAO,CAACpF,GAAG;YACxC,OACEkF,MAAM,KAAK,IAAI,CAAC3C,aAAa,IAC7B,CAAC,IAAI,CAACxD,KAAK,CAACsG,IAAI,CACbC,YAAY,IACX,CAACA,YAAY,CAACvF,EAAE,IAAIuF,YAAY,CAACtF,GAAG,MAAMkF,MAAM,CACnD;UAEL,CAAC,CAAC;UAEF,IAAI,CAACnG,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK,EAAE,GAAGoG,QAAQ,CAAC;;QAG3C;QACA,MAAMI,UAAU,GAAG,IAAI,CAACzD,cAAc,CAAC0D,qBAAqB;QAC5D,IAAI,CAACvG,UAAU,GAAGsG,UAAU,CAACE,UAAU;QACvC,IAAI,CAACtG,UAAU,GAAGoG,UAAU,CAACpG,UAAU;QACvC,IAAI,CAACsD,WAAW,GAAG8C,UAAU,CAAC9C,WAAW;QACzC,IAAI,CAACC,eAAe,GAAG6C,UAAU,CAAC7C,eAAe;QAEjD,IAAI,CAACJ,OAAO,GAAG,KAAK;QACpB,IAAI,CAACW,WAAW,GAAG,KAAK;MAC1B,CAAC;MACDyC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACpD,OAAO,GAAG,KAAK;QACpB,IAAI,CAACW,WAAW,GAAG,KAAK;QACxB,IAAI,CAACd,YAAY,CAAC4C,SAAS,CACzB,yBAAyBW,KAAK,CAACC,OAAO,IAAI,eAAe,EAAE,CAC5D;QAED,IAAI,IAAI,CAACzG,WAAW,KAAK,CAAC,EAAE;UAC1B,IAAI,CAACH,KAAK,GAAG,EAAE;;MAEnB,CAAC;MACD6G,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAACtD,OAAO,GAAG,KAAK;QACpB,IAAI,CAACW,WAAW,GAAG,KAAK;MAC1B;KACD,CAAC;IAEF,IAAI,CAACC,aAAa,CAACa,GAAG,CAACW,GAAG,CAAC;EAC7B;EAEAjE,iBAAiBA,CAACyE,MAA0B;IAC1C,IAAI,CAACA,MAAM,EAAE;MACX,IAAI,CAAC/C,YAAY,CAAC4C,SAAS,CACzB,+CAA+C,CAChD;MACD;;IAGF,IAAI,CAAC5C,YAAY,CAAC0D,QAAQ,CAAC,0BAA0B,CAAC;IAEtD,IAAI,CAAC/D,cAAc,CAACgE,kBAAkB,CAACZ,MAAM,CAAC,CAACrB,SAAS,CAAC;MACvDe,IAAI,EAAGmB,YAAY,IAAI;QACrB,IAAI,CAACA,YAAY,IAAI,CAACA,YAAY,CAAChG,EAAE,EAAE;UACrC,IAAI,CAACoC,YAAY,CAAC4C,SAAS,CACzB,iDAAiD,CAClD;UACD;;QAGF,IAAI,CAAC/C,MAAM,CACRgE,QAAQ,CAAC,CAAC,8BAA8B,EAAED,YAAY,CAAChG,EAAE,CAAC,CAAC,CAC3DkG,IAAI,CAAEC,OAAO,IAAI;UAChB,IAAI,CAACA,OAAO,EAAE;YACZ,IAAI,CAAC/D,YAAY,CAAC4C,SAAS,CAAC,6BAA6B,CAAC;;QAE9D,CAAC,CAAC;MACN,CAAC;MACDW,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACvD,YAAY,CAAC4C,SAAS,CACzB,kCAAkCW,KAAK,CAACC,OAAO,IAAI,eAAe,EAAE,CACrE;MACH;KACD,CAAC;EACJ;EAEA7F,cAAcA,CAACoF,MAAc;IAC3B,IAAI,CAACA,MAAM,EAAE;IAEb,IAAI,CAACnD,WAAW,CAACoE,YAAY,CAACjB,MAAM,EAAE7G,QAAQ,CAAC+H,KAAK,CAAC,CAACvC,SAAS,CAAC;MAC9De,IAAI,EAAGyB,IAAU,IAAI;QACnB,IAAI,CAAClE,YAAY,CAACmE,WAAW,CAAC,sBAAsB,CAAC;MACvD,CAAC;MACDZ,KAAK,EAAGA,KAAU,IAAI;QACpB,IAAI,CAACvD,YAAY,CAAC4C,SAAS,CAAC,+BAA+B,CAAC;MAC9D;KACD,CAAC;EACJ;EAEA3E,cAAcA,CAAC8E,MAAc;IAC3B,IAAI,CAACA,MAAM,EAAE;IAEb,IAAI,CAACnD,WAAW,CAACoE,YAAY,CAACjB,MAAM,EAAE7G,QAAQ,CAACkI,KAAK,CAAC,CAAC1C,SAAS,CAAC;MAC9De,IAAI,EAAGyB,IAAU,IAAI;QACnB,IAAI,CAAClE,YAAY,CAACmE,WAAW,CAAC,sBAAsB,CAAC;MACvD,CAAC;MACDZ,KAAK,EAAGA,KAAU,IAAI;QACpB,IAAI,CAACvD,YAAY,CAAC4C,SAAS,CAAC,+BAA+B,CAAC;MAC9D;KACD,CAAC;EACJ;EAEApD,YAAYA,CAAA;IACV,IAAI,IAAI,CAACc,WAAW,IAAI,CAAC,IAAI,CAACH,OAAO,EAAE;MACrC,IAAI,CAACW,WAAW,GAAG,IAAI;MACvB,IAAI,CAAC/D,WAAW,EAAE;MAClB,IAAI,CAACuE,SAAS,EAAE;;EAEpB;EAEA+C,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAC9D,eAAe,IAAI,CAAC,IAAI,CAACJ,OAAO,EAAE;MACzC,IAAI,CAACW,WAAW,GAAG,IAAI;MACvB,IAAI,CAAC/D,WAAW,EAAE;MAClB,IAAI,CAACuE,SAAS,EAAE;;EAEpB;EAEAgD,YAAYA,CAAA;IACV,IAAI,CAAC3C,eAAe,EAAE;IACtB,IAAI,CAACL,SAAS,CAAC,IAAI,CAAC;EACtB;EAEAiD,YAAYA,CAAA;IACV,IAAI,CAAC7D,UAAU,CAAC8D,KAAK,CAAC;MACpB7D,WAAW,EAAE,EAAE;MACf7B,QAAQ,EAAE;KACX,CAAC;IACF,IAAI,CAAC6C,eAAe,EAAE;IACtB,IAAI,CAACL,SAAS,CAAC,IAAI,CAAC;EACtB;EAEAmD,eAAeA,CAACC,KAAa;IAC3B,IAAI,IAAI,CAAClE,MAAM,KAAKkE,KAAK,EAAE;MACzB;MACA,IAAI,CAACjE,SAAS,GAAG,IAAI,CAACA,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK;KAC3D,MAAM;MACL;MACA,IAAI,CAACD,MAAM,GAAGkE,KAAK;MACnB,IAAI,CAACjE,SAAS,GAAG,KAAK;;IAGxB,IAAI,CAACkB,eAAe,EAAE;IACtB,IAAI,CAACL,SAAS,CAAC,IAAI,CAAC;EACtB;EAEA;;;EAGAqD,qBAAqBA,CAAA;IACnB,IAAI,CAAC9E,MAAM,CAACgE,QAAQ,CAAC,CAAC,yBAAyB,CAAC,CAAC;EACnD;EAEAe,WAAWA,CAAA;IACT,IAAI,CAAC7D,aAAa,CAACkB,WAAW,EAAE;IAChC,IAAI,IAAI,CAACH,uBAAuB,EAAE;MAChC,IAAI,CAACA,uBAAuB,CAACG,WAAW,EAAE;;EAE9C;;;uBAxTWxC,iBAAiB,EAAApD,EAAA,CAAAwI,iBAAA,CAAAC,EAAA,CAAAnF,cAAA,GAAAtD,EAAA,CAAAwI,iBAAA,CAAAE,EAAA,CAAAC,WAAA,GAAA3I,EAAA,CAAAwI,iBAAA,CAAAI,EAAA,CAAAC,MAAA,GAAA7I,EAAA,CAAAwI,iBAAA,CAAAI,EAAA,CAAAE,cAAA,GAAA9I,EAAA,CAAAwI,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAAhJ,EAAA,CAAAwI,iBAAA,CAAAS,EAAA,CAAAC,YAAA,GAAAlJ,EAAA,CAAAwI,iBAAA,CAAAW,EAAA,CAAAC,aAAA,GAAApJ,EAAA,CAAAwI,iBAAA,CAAAa,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAjBlG,iBAAiB;MAAAmG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnB9B7J,EAAA,CAAAC,cAAA,aAGC;;UAECD,EAAA,CAAAC,cAAA,aAAkE;UAEhED,EAAA,CAAAY,SAAA,aAEO;UAcPZ,EAAA,CAAAC,cAAA,aAAuD;UAEnDD,EAAA,CAAAY,SAAA,aAA6C;UAW/CZ,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,cAAyE;UACvED,EAAA,CAAAY,SAAA,eAAqE;UACvEZ,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,eAAqC;UAEJD,EAAA,CAAAE,MAAA,6BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvDH,EAAA,CAAAC,cAAA,eAA4B;UAExBD,EAAA,CAAAa,UAAA,mBAAAkJ,oDAAA;YAAA,OAASD,GAAA,CAAA7B,YAAA,EAAc;UAAA,EAAC;UAIxBjI,EAAA,CAAAY,SAAA,aAA+B;UACjCZ,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAGC;UAFCD,EAAA,CAAAa,UAAA,mBAAAmJ,oDAAA;YAAA,OAASF,GAAA,CAAAxB,qBAAA,EAAuB;UAAA,EAAC;UAGjCtI,EAAA,CAAAY,SAAA,aAAiC;UACnCZ,EAAA,CAAAG,YAAA,EAAS;UAKbH,EAAA,CAAAC,cAAA,eAAuB;UAKjBD,EAAA,CAAAa,UAAA,2BAAAoJ,2DAAAC,MAAA;YAAA,OAAAJ,GAAA,CAAAxF,WAAA,GAAA4F,MAAA;UAAA,EAAsC;UAFxClK,EAAA,CAAAG,YAAA,EAME;UACFH,EAAA,CAAAY,SAAA,aAEK;UACPZ,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAA+C;UAUrCD,EAAA,CAAAa,UAAA,oBAAAsJ,oDAAAD,MAAA;YAAA,IAAAE,OAAA;YAAA,QAAAA,OAAA,GAERN,GAAA,CAAAzF,UAAA,CAAAc,GAAA,CACD,UAAU,CAAC,mBADViF,OAAA,CAAAtE,QAAA,CAAAoE,MAAA,CAAAG,MAAA,CAAAC,OAAA,GAEF,IAAI,GAAG,IAAI,CACjB;UAAA,EADiB;UATHtK,EAAA,CAAAG,YAAA,EAUE;UACFH,EAAA,CAAAY,SAAA,gBAAmD;UACrDZ,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,iBACG;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EACrB;UAIHH,EAAA,CAAAC,cAAA,eAAyC;UACRD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAChDH,EAAA,CAAAC,cAAA,kBAGC;UAFCD,EAAA,CAAAa,UAAA,oBAAA0J,qDAAAL,MAAA;YAAA,OAAUJ,GAAA,CAAA1B,eAAA,CAAA8B,MAAA,CAAAG,MAAA,CAAA3E,KAAA,CAA0C;UAAA,EAAC;UAGrD1F,EAAA,CAAAC,cAAA,kBAA4D;UAC1DD,EAAA,CAAAE,MAAA,aACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAsD;UACpDD,EAAA,CAAAE,MAAA,eACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAgE;UAC9DD,EAAA,CAAAE,MAAA,qCACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEXH,EAAA,CAAAC,cAAA,kBASC;UARCD,EAAA,CAAAa,UAAA,mBAAA2J,oDAAA;YAAAV,GAAA,CAAA1F,SAAA,GAAA0F,GAAA,CAAA1F,SAAA,KAC6C,KAAK,GAC/D,MAAM,GAAG,KAAK;YAAA,OACd0F,GAAA,CAAA7E,SAAA,CAAU,IAAI,CACf;UAAA,EADe;UAMDjF,EAAA,CAAAY,SAAA,SAIK;UACPZ,EAAA,CAAAG,YAAA,EAAS;UAKbH,EAAA,CAAAC,cAAA,kBAAiE;UAAzDD,EAAA,CAAAa,UAAA,mBAAA4J,oDAAA;YAAA,OAASX,GAAA,CAAA5B,YAAA,EAAc;UAAA,EAAC;UAC9BlI,EAAA,CAAAE,MAAA,6BACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIXH,EAAA,CAAAkC,UAAA,KAAAwI,iCAAA,kBASM;UACR1K,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,eAMC;UAJCD,EAAA,CAAAa,UAAA,oBAAA8J,kDAAAT,MAAA;YAAA,OAAAA,MAAA,CAAAG,MAAA,CAAAO,SAAA,GAAAV,MAAA,CAAAG,MAAA,CAAAQ,YAAA,IAAAX,MAAA,CAAAG,MAAA,CAAAS,YAAA,GAE8C,GAAG,IAAIhB,GAAA,CAAA3G,YAAA,EAEzD;UAAA,EADK;UAGDnD,EAAA,CAAAkC,UAAA,KAAA6I,iCAAA,kBAGM;UAGN/K,EAAA,CAAAkC,UAAA,KAAA8I,iCAAA,kBAQM;UAGNhL,EAAA,CAAAkC,UAAA,KAAA+I,gCAAA,iBA4CK;UAGLjL,EAAA,CAAAkC,UAAA,KAAAgJ,iCAAA,kBASM;UAGNlL,EAAA,CAAAkC,UAAA,KAAAiJ,iCAAA,kBAKM;UACRnL,EAAA,CAAAG,YAAA,EAAM;;;;UA1PNH,EAAA,CAAAoL,WAAA,SAAApL,EAAA,CAAAqL,WAAA,QAAAvB,GAAA,CAAAnF,WAAA,EAAkC;UAoE1B3E,EAAA,CAAAI,SAAA,IAAuB;UAAvBJ,EAAA,CAAAsC,UAAA,YAAAwH,GAAA,CAAAxF,WAAA,CAAuB;UAqBjBtE,EAAA,CAAAI,SAAA,GAAsD;UAAtDJ,EAAA,CAAAsC,UAAA,cAAAgJ,OAAA,GAAAxB,GAAA,CAAAzF,UAAA,CAAAc,GAAA,+BAAAmG,OAAA,CAAA5F,KAAA,WAAsD;UAqBhD1F,EAAA,CAAAI,SAAA,GAAkC;UAAlCJ,EAAA,CAAAsC,UAAA,aAAAwH,GAAA,CAAA3F,MAAA,gBAAkC;UAGlCnE,EAAA,CAAAI,SAAA,GAA+B;UAA/BJ,EAAA,CAAAsC,UAAA,aAAAwH,GAAA,CAAA3F,MAAA,aAA+B;UAG/BnE,EAAA,CAAAI,SAAA,GAAoC;UAApCJ,EAAA,CAAAsC,UAAA,aAAAwH,GAAA,CAAA3F,MAAA,kBAAoC;UAU5CnE,EAAA,CAAAI,SAAA,GAEC;UAFDJ,EAAA,CAAAsC,UAAA,UAAAwH,GAAA,CAAA1F,SAAA,0DAEC;UAGCpE,EAAA,CAAAI,SAAA,GAEC;UAFDJ,EAAA,CAAAuL,UAAA,CAAAzB,GAAA,CAAA1F,SAAA,mDAEC;UAcRpE,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAsC,UAAA,SAAAwH,GAAA,CAAArJ,UAAA,KAAoB;UAqBnBT,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAsC,UAAA,SAAAwH,GAAA,CAAAhG,OAAA,KAAAgG,GAAA,CAAAvJ,KAAA,CAAAC,MAAA,CAA8B;UAM9BR,EAAA,CAAAI,SAAA,GAAoC;UAApCJ,EAAA,CAAAsC,UAAA,UAAAwH,GAAA,CAAAhG,OAAA,IAAAgG,GAAA,CAAAvJ,KAAA,CAAAC,MAAA,OAAoC;UAWrCR,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAsC,UAAA,SAAAwH,GAAA,CAAAvJ,KAAA,CAAAC,MAAA,KAAsB;UA+CrBR,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAAsC,UAAA,SAAAwH,GAAA,CAAAhG,OAAA,IAAAgG,GAAA,CAAAvJ,KAAA,CAAAC,MAAA,KAAiC;UAYjCR,EAAA,CAAAI,SAAA,GAA6B;UAA7BJ,EAAA,CAAAsC,UAAA,SAAAwH,GAAA,CAAA7F,WAAA,KAAA6F,GAAA,CAAAhG,OAAA,CAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}