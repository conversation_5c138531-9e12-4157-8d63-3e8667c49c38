{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@app/services/reunion.service\";\nimport * as i3 from \"@angular/platform-browser\";\nimport * as i4 from \"@app/services/toast.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"../../../../shared/pipes/highlight-presence.pipe\";\nfunction ReunionDetailComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵelement(1, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionDetailComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction ReunionDetailComponent_div_7_li_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const participant_r6 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", participant_r6.username, \" (\", participant_r6.email, \") \");\n  }\n}\nfunction ReunionDetailComponent_div_7_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"h2\", 26);\n    i0.ɵɵtext(2, \"Lieu:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 27);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 22);\n    i0.ɵɵelement(5, \"path\", 31)(6, \"path\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(7, \"span\", 24);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r4.reunion.lieu);\n  }\n}\nfunction ReunionDetailComponent_div_7_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"h2\", 26);\n    i0.ɵɵtext(2, \"Lien Visio:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 33);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(4, \"svg\", 34);\n    i0.ɵɵelement(5, \"path\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" Rejoindre la r\\u00E9union \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", ctx_r5.reunion.lienVisio, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ReunionDetailComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11)(2, \"div\")(3, \"h1\", 12);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"p\", 13);\n    i0.ɵɵpipe(6, \"highlightPresence\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 14)(8, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function ReunionDetailComponent_div_7_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.editReunion());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(9, \"svg\", 16);\n    i0.ɵɵelement(10, \"path\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11, \" Modifier \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(12, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function ReunionDetailComponent_div_7_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.deleteReunion());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(13, \"svg\", 16);\n    i0.ɵɵelement(14, \"path\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15, \" Supprimer \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(16, \"div\", 20)(17, \"div\", 21);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(18, \"svg\", 22);\n    i0.ɵɵelement(19, \"path\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(20, \"span\", 24);\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"div\", 25)(24, \"h2\", 26);\n    i0.ɵɵtext(25, \"Cr\\u00E9ateur:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 27)(27, \"span\", 24);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"div\", 20)(30, \"h2\", 26);\n    i0.ɵɵtext(31, \"Participants:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"ul\", 28);\n    i0.ɵɵtemplate(33, ReunionDetailComponent_div_7_li_33_Template, 2, 2, \"li\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 20)(35, \"h2\", 26);\n    i0.ɵɵtext(36, \"Planning:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 24)(38, \"p\");\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"p\");\n    i0.ɵɵtext(41);\n    i0.ɵɵpipe(42, \"date\");\n    i0.ɵɵpipe(43, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(44, ReunionDetailComponent_div_7_div_44_Template, 9, 1, \"div\", 30);\n    i0.ɵɵtemplate(45, ReunionDetailComponent_div_7_div_45_Template, 7, 1, \"div\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.reunion.titre);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", i0.ɵɵpipeBind1(6, 13, ctx_r2.reunion.description), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(16);\n    i0.ɵɵtextInterpolate3(\" \", i0.ɵɵpipeBind2(22, 15, ctx_r2.reunion.date, \"fullDate\"), \", \", ctx_r2.reunion.heureDebut, \" - \", ctx_r2.reunion.heureFin, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.reunion.createur == null ? null : ctx_r2.reunion.createur.username, \" (\", ctx_r2.reunion.createur == null ? null : ctx_r2.reunion.createur.email, \")\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.reunion.participants);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.reunion.planning == null ? null : ctx_r2.reunion.planning.titre);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"Du \", i0.ɵɵpipeBind2(42, 18, ctx_r2.reunion.planning == null ? null : ctx_r2.reunion.planning.dateDebut, \"mediumDate\"), \" au \", i0.ɵɵpipeBind2(43, 21, ctx_r2.reunion.planning == null ? null : ctx_r2.reunion.planning.dateFin, \"mediumDate\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.reunion.lieu);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.reunion.lienVisio);\n  }\n}\nexport class ReunionDetailComponent {\n  constructor(route, router, reunionService, sanitizer, toastService) {\n    this.route = route;\n    this.router = router;\n    this.reunionService = reunionService;\n    this.sanitizer = sanitizer;\n    this.toastService = toastService;\n    this.reunion = null;\n    this.loading = true;\n    this.error = null;\n  }\n  ngOnInit() {\n    this.loadReunionDetails();\n  }\n  loadReunionDetails() {\n    const id = this.route.snapshot.paramMap.get('id');\n    if (!id) {\n      this.error = 'ID de réunion non fourni';\n      this.loading = false;\n      return;\n    }\n    this.reunionService.getReunionById(id).subscribe({\n      next: response => {\n        this.reunion = response.reunion;\n        this.loading = false;\n      },\n      error: err => {\n        this.error = err.error?.message || 'Erreur lors du chargement';\n        this.loading = false;\n        console.error('Erreur:', err);\n      }\n    });\n  }\n  formatDescription(description) {\n    if (!description) return this.sanitizer.bypassSecurityTrustHtml('');\n    // Recherche la chaîne \"(presence obligatoire)\" (insensible à la casse) et la remplace par une version en rouge\n    const formattedText = description.replace(/\\(presence obligatoire\\)/gi, '<span class=\"text-red-600 font-semibold\">(presence obligatoire)</span>');\n    // Sanitize le HTML pour éviter les problèmes de sécurité\n    return this.sanitizer.bypassSecurityTrustHtml(formattedText);\n  }\n  editReunion() {\n    if (this.reunion) {\n      this.router.navigate(['/reunions/edit', this.reunion._id]);\n    }\n  }\n  /**\n   * Supprime la réunion après confirmation\n   */\n  deleteReunion() {\n    if (!this.reunion) return;\n    if (confirm('Êtes-vous sûr de vouloir supprimer cette réunion ? Cette action est irréversible.')) {\n      this.reunionService.deleteReunion(this.reunion._id).subscribe({\n        next: response => {\n          console.log('Réunion supprimée avec succès:', response);\n          // Afficher le toast de succès\n          this.toastService.success('Réunion supprimée', 'La réunion a été supprimée avec succès');\n          // Rediriger vers la liste des réunions\n          this.router.navigate(['/reunions']);\n        },\n        error: error => {\n          console.error('Erreur lors de la suppression:', error);\n          // Gestion spécifique des erreurs d'autorisation\n          if (error.status === 403) {\n            this.toastService.accessDenied('supprimer cette réunion', error.status);\n          } else if (error.status === 401) {\n            this.toastService.error('Non autorisé', 'Vous devez être connecté pour supprimer une réunion');\n          } else {\n            const errorMessage = error.error?.message || 'Erreur lors de la suppression de la réunion';\n            this.toastService.error('Erreur de suppression', errorMessage, 8000);\n          }\n        }\n      });\n    }\n  }\n  static {\n    this.ɵfac = function ReunionDetailComponent_Factory(t) {\n      return new (t || ReunionDetailComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ReunionService), i0.ɵɵdirectiveInject(i3.DomSanitizer), i0.ɵɵdirectiveInject(i4.ToastService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ReunionDetailComponent,\n      selectors: [[\"app-reunion-detail\"]],\n      decls: 8,\n      vars: 3,\n      consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-6\"], [1, \"mb-4\", \"flex\", \"items-center\", \"text-purple-600\", \"hover:text-purple-800\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 0 20 20\", \"fill\", \"currentColor\", 1, \"h-5\", \"w-5\", \"mr-1\"], [\"fill-rule\", \"evenodd\", \"d\", \"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\", \"clip-rule\", \"evenodd\"], [\"class\", \"text-center py-8\", 4, \"ngIf\"], [\"class\", \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [\"class\", \"bg-white rounded-lg shadow-md p-6\", 4, \"ngIf\"], [1, \"text-center\", \"py-8\"], [1, \"animate-spin\", \"rounded-full\", \"h-12\", \"w-12\", \"border-b-2\", \"border-purple-600\", \"mx-auto\"], [1, \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [1, \"bg-white\", \"rounded-lg\", \"shadow-md\", \"p-6\"], [1, \"flex\", \"justify-between\", \"items-start\", \"mb-4\"], [1, \"text-2xl\", \"font-bold\", \"text-gray-800\"], [1, \"mt-1\", 3, \"innerHTML\"], [1, \"flex\", \"space-x-2\"], [1, \"px-4\", \"py-2\", \"bg-blue-500\", \"text-white\", \"rounded\", \"hover:bg-blue-600\", \"transition-colors\", \"flex\", \"items-center\", 3, \"click\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"mr-1\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"], [1, \"px-4\", \"py-2\", \"bg-red-500\", \"text-white\", \"rounded\", \"hover:bg-red-600\", \"transition-colors\", \"flex\", \"items-center\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"], [1, \"mb-6\"], [1, \"flex\", \"items-center\", \"mb-2\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"text-gray-500\", \"mr-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"], [1, \"text-gray-700\"], [1, \"mb-4\"], [1, \"text-lg\", \"font-semibold\", \"mb-2\", \"text-gray-800\"], [1, \"flex\", \"items-center\"], [1, \"list-disc\", \"pl-5\"], [\"class\", \"text-gray-700\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"mb-6\", 4, \"ngIf\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"], [\"target\", \"_blank\", 1, \"text-blue-600\", \"hover:underline\", \"flex\", \"items-center\", 3, \"href\"], [\"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"mr-2\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\"]],\n      template: function ReunionDetailComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"button\", 1);\n          i0.ɵɵlistener(\"click\", function ReunionDetailComponent_Template_button_click_1_listener() {\n            return ctx.router.navigate([\"/reunions\"]);\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(2, \"svg\", 2);\n          i0.ɵɵelement(3, \"path\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(4, \" Retour aux r\\u00E9unions \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, ReunionDetailComponent_div_5_Template, 2, 0, \"div\", 4);\n          i0.ɵɵtemplate(6, ReunionDetailComponent_div_6_Template, 2, 1, \"div\", 5);\n          i0.ɵɵtemplate(7, ReunionDetailComponent_div_7_Template, 46, 24, \"div\", 6);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.reunion);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i5.DatePipe, i6.HighlightPresencePipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJyZXVuaW9uLWRldGFpbC5jb21wb25lbnQuY3NzIn0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvcmV1bmlvbnMvcmV1bmlvbi1kZXRhaWwvcmV1bmlvbi1kZXRhaWwuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsNEtBQTRLIiwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵnamespaceHTML", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "error", "ɵɵtextInterpolate2", "participant_r6", "username", "email", "ɵɵnamespaceSVG", "ɵɵtextInterpolate", "ctx_r4", "reunion", "lieu", "ɵɵproperty", "ctx_r5", "lienVisio", "ɵɵsanitizeUrl", "ɵɵlistener", "ReunionDetailComponent_div_7_Template_button_click_8_listener", "ɵɵrestoreView", "_r8", "ctx_r7", "ɵɵnextContext", "ɵɵresetView", "editReunion", "ReunionDetailComponent_div_7_Template_button_click_12_listener", "ctx_r9", "deleteReunion", "ɵɵtemplate", "ReunionDetailComponent_div_7_li_33_Template", "ReunionDetailComponent_div_7_div_44_Template", "ReunionDetailComponent_div_7_div_45_Template", "ctx_r2", "titre", "ɵɵpipeBind1", "description", "ɵɵsanitizeHtml", "ɵɵtextInterpolate3", "ɵɵpipeBind2", "date", "heureDebut", "heure<PERSON>in", "<PERSON>ur", "participants", "planning", "dateDebut", "dateFin", "ReunionDetailComponent", "constructor", "route", "router", "reunionService", "sanitizer", "toastService", "loading", "ngOnInit", "loadReunionDetails", "id", "snapshot", "paramMap", "get", "getReunionById", "subscribe", "next", "response", "err", "message", "console", "formatDescription", "bypassSecurityTrustHtml", "formattedText", "replace", "navigate", "_id", "confirm", "log", "success", "status", "accessDenied", "errorMessage", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "ReunionService", "i3", "Dom<PERSON><PERSON><PERSON>zer", "i4", "ToastService", "selectors", "decls", "vars", "consts", "template", "ReunionDetailComponent_Template", "rf", "ctx", "ReunionDetailComponent_Template_button_click_1_listener", "ReunionDetailComponent_div_5_Template", "ReunionDetailComponent_div_6_Template", "ReunionDetailComponent_div_7_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\reunions\\reunion-detail\\reunion-detail.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\reunions\\reunion-detail\\reunion-detail.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { ReunionService } from '@app/services/reunion.service';\nimport { DomSanitizer, SafeHtml } from '@angular/platform-browser';\nimport { ToastService } from '@app/services/toast.service';\n\n@Component({\n  selector: 'app-reunion-detail',\n  templateUrl: './reunion-detail.component.html',\n  styleUrls: ['./reunion-detail.component.css']\n})\nexport class ReunionDetailComponent implements OnInit {\n  reunion: any = null;\n  loading = true;\n  error: string | null = null;\n\n  constructor(\n    private route: ActivatedRoute,\n    public router: Router,\n    private reunionService: ReunionService,\n    private sanitizer: DomSanitizer,\n    private toastService: ToastService\n  ) {}\n\n  ngOnInit(): void {\n    this.loadReunionDetails();\n  }\n\n  loadReunionDetails(): void {\n    const id = this.route.snapshot.paramMap.get('id');\n    if (!id) {\n      this.error = 'ID de réunion non fourni';\n      this.loading = false;\n      return;\n    }\n\n    this.reunionService.getReunionById(id).subscribe({\n      next: (response: any) => {\n        this.reunion = response.reunion;\n        this.loading = false;\n      },\n      error: (err: any) => {\n        this.error = err.error?.message || 'Erreur lors du chargement';\n        this.loading = false;\n        console.error('Erreur:', err);\n      }\n    });\n  }\n\n  formatDescription(description: string): SafeHtml {\n    if (!description) return this.sanitizer.bypassSecurityTrustHtml('');\n\n    // Recherche la chaîne \"(presence obligatoire)\" (insensible à la casse) et la remplace par une version en rouge\n    const formattedText = description.replace(\n      /\\(presence obligatoire\\)/gi,\n      '<span class=\"text-red-600 font-semibold\">(presence obligatoire)</span>'\n    );\n\n    // Sanitize le HTML pour éviter les problèmes de sécurité\n    return this.sanitizer.bypassSecurityTrustHtml(formattedText);\n  }\n\n  editReunion(): void {\n    if (this.reunion) {\n      this.router.navigate(['/reunions/edit', this.reunion._id]);\n    }\n  }\n\n  /**\n   * Supprime la réunion après confirmation\n   */\n  deleteReunion(): void {\n    if (!this.reunion) return;\n\n    if (confirm('Êtes-vous sûr de vouloir supprimer cette réunion ? Cette action est irréversible.')) {\n      this.reunionService.deleteReunion(this.reunion._id).subscribe({\n        next: (response) => {\n          console.log('Réunion supprimée avec succès:', response);\n\n          // Afficher le toast de succès\n          this.toastService.success(\n            'Réunion supprimée',\n            'La réunion a été supprimée avec succès'\n          );\n\n          // Rediriger vers la liste des réunions\n          this.router.navigate(['/reunions']);\n        },\n        error: (error) => {\n          console.error('Erreur lors de la suppression:', error);\n\n          // Gestion spécifique des erreurs d'autorisation\n          if (error.status === 403) {\n            this.toastService.accessDenied('supprimer cette réunion', error.status);\n          } else if (error.status === 401) {\n            this.toastService.error(\n              'Non autorisé',\n              'Vous devez être connecté pour supprimer une réunion'\n            );\n          } else {\n            const errorMessage = error.error?.message || 'Erreur lors de la suppression de la réunion';\n            this.toastService.error(\n              'Erreur de suppression',\n              errorMessage,\n              8000\n            );\n          }\n        }\n      });\n    }\n  }\n}", "<div class=\"container mx-auto px-4 py-6\">\n  <!-- Bouton retour -->\n  <button (click)=\"router.navigate(['/reunions'])\"\n          class=\"mb-4 flex items-center text-purple-600 hover:text-purple-800\">\n    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-1\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n      <path fill-rule=\"evenodd\" d=\"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\" clip-rule=\"evenodd\" />\n    </svg>\n    Retour aux réunions\n  </button>\n\n  <!-- Chargement -->\n  <div *ngIf=\"loading\" class=\"text-center py-8\">\n    <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto\"></div>\n  </div>\n\n  <!-- Erreur -->\n  <div *ngIf=\"error\" class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n    {{ error }}\n  </div>\n\n  <!-- Détails de la réunion -->\n  <div *ngIf=\"!loading && reunion\" class=\"bg-white rounded-lg shadow-md p-6\">\n    <!-- Titre de la réunion -->\n    <div class=\"flex justify-between items-start mb-4\">\n      <div>\n        <h1 class=\"text-2xl font-bold text-gray-800\">{{ reunion.titre }}</h1>\n        <p class=\"mt-1\" [innerHTML]=\"reunion.description | highlightPresence\"></p>\n      </div>\n      <div class=\"flex space-x-2\">\n        <button (click)=\"editReunion()\"\n                class=\"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors flex items-center\">\n          <svg class=\"h-4 w-4 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n          </svg>\n          Modifier\n        </button>\n        <button (click)=\"deleteReunion()\"\n                class=\"px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors flex items-center\">\n          <svg class=\"h-4 w-4 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n          </svg>\n          Supprimer\n        </button>\n      </div>\n    </div>\n\n    <!-- Date et heure -->\n    <div class=\"mb-6\">\n      <div class=\"flex items-center mb-2\">\n        <svg class=\"h-5 w-5 text-gray-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n        </svg>\n        <span class=\"text-gray-700\">\n          {{ reunion.date | date:'fullDate' }}, {{ reunion.heureDebut }} - {{ reunion.heureFin }}\n        </span>\n      </div>\n    </div>\n\n    <!-- Créateur -->\n    <div class=\"mb-4\">\n      <h2 class=\"text-lg font-semibold mb-2 text-gray-800\">Créateur:</h2>\n      <div class=\"flex items-center\">\n        <span class=\"text-gray-700\">{{ reunion.createur?.username }} ({{ reunion.createur?.email }})</span>\n      </div>\n    </div>\n\n    <!-- Participants -->\n    <div class=\"mb-6\">\n      <h2 class=\"text-lg font-semibold mb-2 text-gray-800\">Participants:</h2>\n      <ul class=\"list-disc pl-5\">\n        <li *ngFor=\"let participant of reunion.participants\" class=\"text-gray-700\">\n          {{ participant.username }} ({{ participant.email }})\n        </li>\n      </ul>\n    </div>\n\n    <!-- Planning -->\n    <div class=\"mb-6\">\n      <h2 class=\"text-lg font-semibold mb-2 text-gray-800\">Planning:</h2>\n      <div class=\"text-gray-700\">\n        <p>{{ reunion.planning?.titre }}</p>\n        <p>Du {{ reunion.planning?.dateDebut | date:'mediumDate' }} au {{ reunion.planning?.dateFin | date:'mediumDate' }}</p>\n      </div>\n    </div>\n\n    <!-- Lieu -->\n    <div *ngIf=\"reunion.lieu\" class=\"mb-6\">\n      <h2 class=\"text-lg font-semibold mb-2 text-gray-800\">Lieu:</h2>\n      <div class=\"flex items-center\">\n        <svg class=\"h-5 w-5 text-gray-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n        </svg>\n        <span class=\"text-gray-700\">{{ reunion.lieu }}</span>\n      </div>\n    </div>\n\n    <!-- Lien Visio -->\n    <div *ngIf=\"reunion.lienVisio\" class=\"mb-6\">\n      <h2 class=\"text-lg font-semibold mb-2 text-gray-800\">Lien Visio:</h2>\n      <a [href]=\"reunion.lienVisio\" class=\"text-blue-600 hover:underline flex items-center\" target=\"_blank\">\n        <svg class=\"h-5 w-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\" />\n        </svg>\n        Rejoindre la réunion\n      </a>\n    </div>\n  </div>\n</div>"], "mappings": ";;;;;;;;;;ICWEA,EAAA,CAAAC,eAAA,EAA8C;IAA9CD,EAAA,CAAAE,cAAA,aAA8C;IAC5CF,EAAA,CAAAG,SAAA,aAA4F;IAC9FH,EAAA,CAAAI,YAAA,EAAM;;;;;;IAGNJ,EAAA,CAAAC,eAAA,EAAgG;IAAhGD,EAAA,CAAAE,cAAA,aAAgG;IAC9FF,EAAA,CAAAK,MAAA,GACF;IAAAL,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAoDMT,EAAA,CAAAE,cAAA,aAA2E;IACzEF,EAAA,CAAAK,MAAA,GACF;IAAAL,EAAA,CAAAI,YAAA,EAAK;;;;IADHJ,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAU,kBAAA,MAAAC,cAAA,CAAAC,QAAA,QAAAD,cAAA,CAAAE,KAAA,OACF;;;;;IAcJb,EAAA,CAAAE,cAAA,cAAuC;IACgBF,EAAA,CAAAK,MAAA,YAAK;IAAAL,EAAA,CAAAI,YAAA,EAAK;IAC/DJ,EAAA,CAAAE,cAAA,cAA+B;IAC7BF,EAAA,CAAAc,cAAA,EAA8F;IAA9Fd,EAAA,CAAAE,cAAA,cAA8F;IAC5FF,EAAA,CAAAG,SAAA,eAA+J;IAEjKH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,eAAA,EAA4B;IAA5BD,EAAA,CAAAE,cAAA,eAA4B;IAAAF,EAAA,CAAAK,MAAA,GAAkB;IAAAL,EAAA,CAAAI,YAAA,EAAO;;;;IAAzBJ,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAAe,iBAAA,CAAAC,MAAA,CAAAC,OAAA,CAAAC,IAAA,CAAkB;;;;;IAKlDlB,EAAA,CAAAE,cAAA,cAA4C;IACWF,EAAA,CAAAK,MAAA,kBAAW;IAAAL,EAAA,CAAAI,YAAA,EAAK;IACrEJ,EAAA,CAAAE,cAAA,YAAsG;IACpGF,EAAA,CAAAc,cAAA,EAAgF;IAAhFd,EAAA,CAAAE,cAAA,cAAgF;IAC9EF,EAAA,CAAAG,SAAA,eAA+M;IACjNH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAK,MAAA,kCACF;IAAAL,EAAA,CAAAI,YAAA,EAAI;;;;IALDJ,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAmB,UAAA,SAAAC,MAAA,CAAAH,OAAA,CAAAI,SAAA,EAAArB,EAAA,CAAAsB,aAAA,CAA0B;;;;;;;IA/EjCtB,EAAA,CAAAC,eAAA,EAA2E;IAA3ED,EAAA,CAAAE,cAAA,cAA2E;IAIxBF,EAAA,CAAAK,MAAA,GAAmB;IAAAL,EAAA,CAAAI,YAAA,EAAK;IACrEJ,EAAA,CAAAG,SAAA,YAA0E;;IAC5EH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,cAA4B;IAClBF,EAAA,CAAAuB,UAAA,mBAAAC,8DAAA;MAAAxB,EAAA,CAAAyB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAA3B,EAAA,CAAA4B,aAAA;MAAA,OAAS5B,EAAA,CAAA6B,WAAA,CAAAF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAE7B9B,EAAA,CAAAc,cAAA,EAAgF;IAAhFd,EAAA,CAAAE,cAAA,cAAgF;IAC9EF,EAAA,CAAAG,SAAA,gBAAmM;IACrMH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAK,MAAA,kBACF;IAAAL,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,eAAA,EAC6G;IAD7GD,EAAA,CAAAE,cAAA,kBAC6G;IADrGF,EAAA,CAAAuB,UAAA,mBAAAQ,+DAAA;MAAA/B,EAAA,CAAAyB,aAAA,CAAAC,GAAA;MAAA,MAAAM,MAAA,GAAAhC,EAAA,CAAA4B,aAAA;MAAA,OAAS5B,EAAA,CAAA6B,WAAA,CAAAG,MAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAE/BjC,EAAA,CAAAc,cAAA,EAAgF;IAAhFd,EAAA,CAAAE,cAAA,eAAgF;IAC9EF,EAAA,CAAAG,SAAA,gBAAyM;IAC3MH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAK,MAAA,mBACF;IAAAL,EAAA,CAAAI,YAAA,EAAS;IAKbJ,EAAA,CAAAC,eAAA,EAAkB;IAAlBD,EAAA,CAAAE,cAAA,eAAkB;IAEdF,EAAA,CAAAc,cAAA,EAA8F;IAA9Fd,EAAA,CAAAE,cAAA,eAA8F;IAC5FF,EAAA,CAAAG,SAAA,gBAAmK;IACrKH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,eAAA,EAA4B;IAA5BD,EAAA,CAAAE,cAAA,gBAA4B;IAC1BF,EAAA,CAAAK,MAAA,IACF;;IAAAL,EAAA,CAAAI,YAAA,EAAO;IAKXJ,EAAA,CAAAE,cAAA,eAAkB;IACqCF,EAAA,CAAAK,MAAA,sBAAS;IAAAL,EAAA,CAAAI,YAAA,EAAK;IACnEJ,EAAA,CAAAE,cAAA,eAA+B;IACDF,EAAA,CAAAK,MAAA,IAAgE;IAAAL,EAAA,CAAAI,YAAA,EAAO;IAKvGJ,EAAA,CAAAE,cAAA,eAAkB;IACqCF,EAAA,CAAAK,MAAA,qBAAa;IAAAL,EAAA,CAAAI,YAAA,EAAK;IACvEJ,EAAA,CAAAE,cAAA,cAA2B;IACzBF,EAAA,CAAAkC,UAAA,KAAAC,2CAAA,iBAEK;IACPnC,EAAA,CAAAI,YAAA,EAAK;IAIPJ,EAAA,CAAAE,cAAA,eAAkB;IACqCF,EAAA,CAAAK,MAAA,iBAAS;IAAAL,EAAA,CAAAI,YAAA,EAAK;IACnEJ,EAAA,CAAAE,cAAA,eAA2B;IACtBF,EAAA,CAAAK,MAAA,IAA6B;IAAAL,EAAA,CAAAI,YAAA,EAAI;IACpCJ,EAAA,CAAAE,cAAA,SAAG;IAAAF,EAAA,CAAAK,MAAA,IAA+G;;;IAAAL,EAAA,CAAAI,YAAA,EAAI;IAK1HJ,EAAA,CAAAkC,UAAA,KAAAE,4CAAA,kBASM;IAGNpC,EAAA,CAAAkC,UAAA,KAAAG,4CAAA,kBAQM;IACRrC,EAAA,CAAAI,YAAA,EAAM;;;;IAlF6CJ,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAe,iBAAA,CAAAuB,MAAA,CAAArB,OAAA,CAAAsB,KAAA,CAAmB;IAChDvC,EAAA,CAAAM,SAAA,GAAqD;IAArDN,EAAA,CAAAmB,UAAA,cAAAnB,EAAA,CAAAwC,WAAA,QAAAF,MAAA,CAAArB,OAAA,CAAAwB,WAAA,GAAAzC,EAAA,CAAA0C,cAAA,CAAqD;IA2BnE1C,EAAA,CAAAM,SAAA,IACF;IADEN,EAAA,CAAA2C,kBAAA,MAAA3C,EAAA,CAAA4C,WAAA,SAAAN,MAAA,CAAArB,OAAA,CAAA4B,IAAA,qBAAAP,MAAA,CAAArB,OAAA,CAAA6B,UAAA,SAAAR,MAAA,CAAArB,OAAA,CAAA8B,QAAA,MACF;IAQ4B/C,EAAA,CAAAM,SAAA,GAAgE;IAAhEN,EAAA,CAAAU,kBAAA,KAAA4B,MAAA,CAAArB,OAAA,CAAA+B,QAAA,kBAAAV,MAAA,CAAArB,OAAA,CAAA+B,QAAA,CAAApC,QAAA,QAAA0B,MAAA,CAAArB,OAAA,CAAA+B,QAAA,kBAAAV,MAAA,CAAArB,OAAA,CAAA+B,QAAA,CAAAnC,KAAA,MAAgE;IAQhEb,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAAmB,UAAA,YAAAmB,MAAA,CAAArB,OAAA,CAAAgC,YAAA,CAAuB;IAUhDjD,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAAe,iBAAA,CAAAuB,MAAA,CAAArB,OAAA,CAAAiC,QAAA,kBAAAZ,MAAA,CAAArB,OAAA,CAAAiC,QAAA,CAAAX,KAAA,CAA6B;IAC7BvC,EAAA,CAAAM,SAAA,GAA+G;IAA/GN,EAAA,CAAAU,kBAAA,QAAAV,EAAA,CAAA4C,WAAA,SAAAN,MAAA,CAAArB,OAAA,CAAAiC,QAAA,kBAAAZ,MAAA,CAAArB,OAAA,CAAAiC,QAAA,CAAAC,SAAA,yBAAAnD,EAAA,CAAA4C,WAAA,SAAAN,MAAA,CAAArB,OAAA,CAAAiC,QAAA,kBAAAZ,MAAA,CAAArB,OAAA,CAAAiC,QAAA,CAAAE,OAAA,oBAA+G;IAKhHpD,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAAmB,UAAA,SAAAmB,MAAA,CAAArB,OAAA,CAAAC,IAAA,CAAkB;IAYlBlB,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAAmB,UAAA,SAAAmB,MAAA,CAAArB,OAAA,CAAAI,SAAA,CAAuB;;;ADvFjC,OAAM,MAAOgC,sBAAsB;EAKjCC,YACUC,KAAqB,EACtBC,MAAc,EACbC,cAA8B,EAC9BC,SAAuB,EACvBC,YAA0B;IAJ1B,KAAAJ,KAAK,GAALA,KAAK;IACN,KAAAC,MAAM,GAANA,MAAM;IACL,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,YAAY,GAAZA,YAAY;IATtB,KAAA1C,OAAO,GAAQ,IAAI;IACnB,KAAA2C,OAAO,GAAG,IAAI;IACd,KAAAnD,KAAK,GAAkB,IAAI;EAQxB;EAEHoD,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEAA,kBAAkBA,CAAA;IAChB,MAAMC,EAAE,GAAG,IAAI,CAACR,KAAK,CAACS,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACjD,IAAI,CAACH,EAAE,EAAE;MACP,IAAI,CAACtD,KAAK,GAAG,0BAA0B;MACvC,IAAI,CAACmD,OAAO,GAAG,KAAK;MACpB;;IAGF,IAAI,CAACH,cAAc,CAACU,cAAc,CAACJ,EAAE,CAAC,CAACK,SAAS,CAAC;MAC/CC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAACrD,OAAO,GAAGqD,QAAQ,CAACrD,OAAO;QAC/B,IAAI,CAAC2C,OAAO,GAAG,KAAK;MACtB,CAAC;MACDnD,KAAK,EAAG8D,GAAQ,IAAI;QAClB,IAAI,CAAC9D,KAAK,GAAG8D,GAAG,CAAC9D,KAAK,EAAE+D,OAAO,IAAI,2BAA2B;QAC9D,IAAI,CAACZ,OAAO,GAAG,KAAK;QACpBa,OAAO,CAAChE,KAAK,CAAC,SAAS,EAAE8D,GAAG,CAAC;MAC/B;KACD,CAAC;EACJ;EAEAG,iBAAiBA,CAACjC,WAAmB;IACnC,IAAI,CAACA,WAAW,EAAE,OAAO,IAAI,CAACiB,SAAS,CAACiB,uBAAuB,CAAC,EAAE,CAAC;IAEnE;IACA,MAAMC,aAAa,GAAGnC,WAAW,CAACoC,OAAO,CACvC,4BAA4B,EAC5B,wEAAwE,CACzE;IAED;IACA,OAAO,IAAI,CAACnB,SAAS,CAACiB,uBAAuB,CAACC,aAAa,CAAC;EAC9D;EAEA9C,WAAWA,CAAA;IACT,IAAI,IAAI,CAACb,OAAO,EAAE;MAChB,IAAI,CAACuC,MAAM,CAACsB,QAAQ,CAAC,CAAC,gBAAgB,EAAE,IAAI,CAAC7D,OAAO,CAAC8D,GAAG,CAAC,CAAC;;EAE9D;EAEA;;;EAGA9C,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAAChB,OAAO,EAAE;IAEnB,IAAI+D,OAAO,CAAC,mFAAmF,CAAC,EAAE;MAChG,IAAI,CAACvB,cAAc,CAACxB,aAAa,CAAC,IAAI,CAAChB,OAAO,CAAC8D,GAAG,CAAC,CAACX,SAAS,CAAC;QAC5DC,IAAI,EAAGC,QAAQ,IAAI;UACjBG,OAAO,CAACQ,GAAG,CAAC,gCAAgC,EAAEX,QAAQ,CAAC;UAEvD;UACA,IAAI,CAACX,YAAY,CAACuB,OAAO,CACvB,mBAAmB,EACnB,wCAAwC,CACzC;UAED;UACA,IAAI,CAAC1B,MAAM,CAACsB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;QACrC,CAAC;QACDrE,KAAK,EAAGA,KAAK,IAAI;UACfgE,OAAO,CAAChE,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;UAEtD;UACA,IAAIA,KAAK,CAAC0E,MAAM,KAAK,GAAG,EAAE;YACxB,IAAI,CAACxB,YAAY,CAACyB,YAAY,CAAC,yBAAyB,EAAE3E,KAAK,CAAC0E,MAAM,CAAC;WACxE,MAAM,IAAI1E,KAAK,CAAC0E,MAAM,KAAK,GAAG,EAAE;YAC/B,IAAI,CAACxB,YAAY,CAAClD,KAAK,CACrB,cAAc,EACd,qDAAqD,CACtD;WACF,MAAM;YACL,MAAM4E,YAAY,GAAG5E,KAAK,CAACA,KAAK,EAAE+D,OAAO,IAAI,6CAA6C;YAC1F,IAAI,CAACb,YAAY,CAAClD,KAAK,CACrB,uBAAuB,EACvB4E,YAAY,EACZ,IAAI,CACL;;QAEL;OACD,CAAC;;EAEN;;;uBAnGWhC,sBAAsB,EAAArD,EAAA,CAAAsF,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAxF,EAAA,CAAAsF,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAzF,EAAA,CAAAsF,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAA3F,EAAA,CAAAsF,iBAAA,CAAAM,EAAA,CAAAC,YAAA,GAAA7F,EAAA,CAAAsF,iBAAA,CAAAQ,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAtB1C,sBAAsB;MAAA2C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXnCtG,EAAA,CAAAE,cAAA,aAAyC;UAE/BF,EAAA,CAAAuB,UAAA,mBAAAiF,wDAAA;YAAA,OAASD,GAAA,CAAA/C,MAAA,CAAAsB,QAAA,EAAiB,WAAW,EAAE;UAAA,EAAC;UAE9C9E,EAAA,CAAAc,cAAA,EAAqG;UAArGd,EAAA,CAAAE,cAAA,aAAqG;UACnGF,EAAA,CAAAG,SAAA,cAA0L;UAC5LH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,MAAA,iCACF;UAAAL,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAkC,UAAA,IAAAuE,qCAAA,iBAEM;UAGNzG,EAAA,CAAAkC,UAAA,IAAAwE,qCAAA,iBAEM;UAGN1G,EAAA,CAAAkC,UAAA,IAAAyE,qCAAA,mBAsFM;UACR3G,EAAA,CAAAI,YAAA,EAAM;;;UAjGEJ,EAAA,CAAAM,SAAA,GAAa;UAAbN,EAAA,CAAAmB,UAAA,SAAAoF,GAAA,CAAA3C,OAAA,CAAa;UAKb5D,EAAA,CAAAM,SAAA,GAAW;UAAXN,EAAA,CAAAmB,UAAA,SAAAoF,GAAA,CAAA9F,KAAA,CAAW;UAKXT,EAAA,CAAAM,SAAA,GAAyB;UAAzBN,EAAA,CAAAmB,UAAA,UAAAoF,GAAA,CAAA3C,OAAA,IAAA2C,GAAA,CAAAtF,OAAA,CAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}