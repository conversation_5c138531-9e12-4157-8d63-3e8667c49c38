<!-- ============================================================================
     LAYOUT PRINCIPAL DE MESSAGERIE - STYLE WHATSAPP
     ============================================================================ -->

<div class="message-layout h-screen bg-gray-900 text-white flex">
  <!-- ========================================================================
       SIDEBAR GAUCHE - CONVERSATIONS/UTILISATEURS/NOTIFICATIONS
       ======================================================================== -->
  <div
    class="sidebar w-80 bg-gray-800 border-r border-gray-700 flex flex-col"
    [class.hidden]="!isMobileMenuOpen"
    [class.md:flex]="true"
  >
    <!-- En-tête de la sidebar -->
    <div class="sidebar-header p-4 border-b border-gray-700 bg-gray-800">
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-3">
          <img
            [src]="currentUser?.image || '/assets/images/default-avatar.png'"
            [alt]="currentUser?.username"
            class="w-10 h-10 rounded-full border-2 border-blue-500"
          />
          <div>
            <h3 class="font-semibold text-white">
              {{ currentUser?.username }}
            </h3>
            <p class="text-sm text-green-400">En ligne</p>
          </div>
        </div>

        <!-- Actions de l'en-tête -->
        <div class="flex items-center space-x-2">
          <!-- Sélecteur de thème -->
          <div class="relative">
            <button
              class="p-2 rounded-lg bg-gray-700 hover:bg-gray-600 transition-colors"
              (click)="showThemeSelector = !showThemeSelector"
              title="Changer de thème"
            >
              <i class="fas fa-palette text-blue-400"></i>
            </button>

            <!-- Menu des thèmes -->
            <div
              *ngIf="showThemeSelector"
              class="absolute top-full right-0 mt-2 bg-gray-800 rounded-lg shadow-lg border border-gray-700 p-2 z-50 min-w-48"
            >
              <div class="text-xs text-gray-400 mb-2 px-2">
                Choisir un thème
              </div>
              <div
                *ngFor="let theme of availableThemes"
                class="flex items-center space-x-3 p-2 hover:bg-gray-700 rounded cursor-pointer transition-colors"
                (click)="selectTheme(theme.name)"
              >
                <div
                  class="w-4 h-4 rounded-full border-2"
                  [style.background]="theme.gradients.primary"
                  [class.border-white]="currentTheme?.name === theme.name"
                  [class.border-gray-500]="currentTheme?.name !== theme.name"
                ></div>
                <span class="text-white text-sm">{{ theme.displayName }}</span>
                <i
                  *ngIf="currentTheme?.name === theme.name"
                  class="fas fa-check text-blue-400 text-xs ml-auto"
                ></i>
              </div>
            </div>
          </div>

          <!-- Bouton menu mobile -->
          <button
            class="md:hidden p-2 rounded-lg bg-gray-700 hover:bg-gray-600"
            (click)="toggleMobileMenu()"
          >
            <i class="fas fa-times text-white"></i>
          </button>
        </div>
      </div>

      <!-- Barre de recherche -->
      <div class="relative">
        <input
          #searchInput
          type="text"
          [(ngModel)]="searchQuery"
          (input)="onSearchInput($event)"
          placeholder="Rechercher..."
          class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 pl-10 text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
        />
        <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
        <button
          *ngIf="searchQuery"
          (click)="clearSearch()"
          class="absolute right-3 top-3 text-gray-400 hover:text-white"
        >
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>

    <!-- Onglets de navigation -->
    <div class="tabs flex border-b border-gray-700">
      <button
        class="tab flex-1 py-3 px-4 text-center transition-all duration-200"
        [class.active]="activeTab === 'conversations'"
        [class.text-blue-400]="activeTab === 'conversations'"
        [class.border-b-2]="activeTab === 'conversations'"
        [class.border-blue-500]="activeTab === 'conversations'"
        [class.text-gray-400]="activeTab !== 'conversations'"
        (click)="switchTab('conversations')"
      >
        <i class="fas fa-comments mb-1"></i>
        <div class="text-xs">Discussions</div>
      </button>

      <button
        class="tab flex-1 py-3 px-4 text-center transition-all duration-200"
        [class.active]="activeTab === 'users'"
        [class.text-blue-400]="activeTab === 'users'"
        [class.border-b-2]="activeTab === 'users'"
        [class.border-blue-500]="activeTab === 'users'"
        [class.text-gray-400]="activeTab !== 'users'"
        (click)="switchTab('users')"
      >
        <i class="fas fa-users mb-1"></i>
        <div class="text-xs">Contacts</div>
      </button>

      <button
        class="tab flex-1 py-3 px-4 text-center transition-all duration-200 relative"
        [class.active]="activeTab === 'notifications'"
        [class.text-blue-400]="activeTab === 'notifications'"
        [class.border-b-2]="activeTab === 'notifications'"
        [class.border-blue-500]="activeTab === 'notifications'"
        [class.text-gray-400]="activeTab !== 'notifications'"
        (click)="switchTab('notifications')"
      >
        <i class="fas fa-bell mb-1"></i>
        <div class="text-xs">Notifications</div>
        <span
          *ngIf="notifications.length > 0"
          class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center"
        >
          {{ notifications.length > 9 ? "9+" : notifications.length }}
        </span>
      </button>
    </div>

    <!-- Contenu de la sidebar -->
    <div class="sidebar-content flex-1 overflow-y-auto">
      <!-- ====================================================================
           ONGLET CONVERSATIONS
           ==================================================================== -->
      <div *ngIf="activeTab === 'conversations'" class="conversations-list">
        <!-- Résultats de recherche -->
        <div
          *ngIf="isSearching && searchResults.length > 0"
          class="search-results"
        >
          <div class="p-3 text-sm text-gray-400 border-b border-gray-700">
            Résultats de recherche ({{ searchResults.length }})
          </div>
          <div
            *ngFor="let result of searchResults"
            class="conversation-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors"
            (click)="selectConversation(result)"
          >
            <div class="flex items-center space-x-3">
              <img
                [src]="getConversationAvatar(result)"
                [alt]="getConversationName(result)"
                class="w-12 h-12 rounded-full"
              />
              <div class="flex-1 min-w-0">
                <h4 class="font-medium text-white truncate">
                  {{ getConversationName(result) }}
                </h4>
                <p class="text-sm text-gray-400 truncate">
                  {{ getLastMessagePreview(result) }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Message si aucun résultat -->
        <div
          *ngIf="isSearching && searchResults.length === 0"
          class="p-8 text-center text-gray-400"
        >
          <i class="fas fa-search text-4xl mb-4"></i>
          <p>Aucun résultat trouvé</p>
        </div>

        <!-- Liste des conversations -->
        <div *ngIf="!isSearching">
          <!-- Indicateur de chargement -->
          <div
            *ngIf="isLoadingConversations && conversations.length === 0"
            class="p-8 text-center"
          >
            <div
              class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"
            ></div>
            <p class="text-gray-400 mt-2">Chargement des conversations...</p>
          </div>

          <!-- Conversations -->
          <div
            *ngFor="
              let conversation of conversations;
              trackBy: trackByConversationId
            "
            class="conversation-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors relative"
            [class.bg-gray-700]="selectedConversationId === conversation.id"
            [class.border-l-4]="selectedConversationId === conversation.id"
            [class.border-blue-500]="selectedConversationId === conversation.id"
            (click)="selectConversation(conversation)"
          >
            <div class="flex items-center space-x-3">
              <!-- Avatar avec indicateur en ligne -->
              <div class="relative">
                <img
                  [src]="getConversationAvatar(conversation)"
                  [alt]="getConversationName(conversation)"
                  class="w-12 h-12 rounded-full"
                />
                <div
                  *ngIf="!conversation.isGroup && isUserOnline(conversation.participants?.[0]!)"
                  class="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-gray-800"
                ></div>
              </div>

              <!-- Informations de la conversation -->
              <div class="flex-1 min-w-0">
                <div class="flex items-center justify-between">
                  <h4 class="font-medium text-white truncate">
                    {{ getConversationName(conversation) }}
                  </h4>
                  <span class="text-xs text-gray-400">
                    {{
                      formatLastMessageTime(conversation.lastMessage?.timestamp)
                    }}
                  </span>
                </div>

                <div class="flex items-center justify-between mt-1">
                  <p class="text-sm text-gray-400 truncate">
                    {{ getLastMessagePreview(conversation) }}
                  </p>

                  <!-- Badge de messages non lus -->
                  <span
                    *ngIf="getUnreadCount(conversation) > 0"
                    class="bg-blue-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center"
                  >
                    {{
                      getUnreadCount(conversation) > 99
                        ? "99+"
                        : getUnreadCount(conversation)
                    }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Bouton charger plus -->
          <div *ngIf="hasMoreConversations" class="p-4 text-center">
            <button
              (click)="loadMoreConversations()"
              [disabled]="isLoadingConversations"
              class="text-blue-400 hover:text-blue-300 disabled:text-gray-500"
            >
              <span *ngIf="!isLoadingConversations">Charger plus</span>
              <span *ngIf="isLoadingConversations">Chargement...</span>
            </button>
          </div>

          <!-- Message si aucune conversation -->
          <div
            *ngIf="conversations.length === 0 && !isLoadingConversations"
            class="p-8 text-center text-gray-400"
          >
            <i class="fas fa-comments text-4xl mb-4"></i>
            <p>Aucune conversation</p>
            <p class="text-sm mt-2">
              Commencez une nouvelle conversation dans l'onglet Contacts
            </p>
          </div>
        </div>
      </div>

      <!-- ====================================================================
           ONGLET UTILISATEURS/CONTACTS
           ==================================================================== -->
      <div *ngIf="activeTab === 'users'" class="users-list">
        <!-- Résultats de recherche -->
        <div
          *ngIf="isSearching && searchResults.length > 0"
          class="search-results"
        >
          <div class="p-3 text-sm text-gray-400 border-b border-gray-700">
            Résultats de recherche ({{ searchResults.length }})
          </div>
          <div
            *ngFor="let result of searchResults"
            class="user-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors"
            (click)="isUser(result) ? startConversationWithUser(result) : null"
          >
            <div class="flex items-center space-x-3" *ngIf="isUser(result)">
              <div class="relative">
                <img
                  [src]="result.image || '/assets/images/default-avatar.png'"
                  [alt]="result.username"
                  class="w-12 h-12 rounded-full"
                />
                <div
                  *ngIf="isUserOnline(result)"
                  class="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-gray-800"
                ></div>
              </div>
              <div class="flex-1 min-w-0">
                <h4 class="font-medium text-white truncate">
                  {{ result.username }}
                </h4>
                <p class="text-sm text-gray-400 truncate">{{ result.email }}</p>
              </div>
              <div class="text-blue-400">
                <i class="fas fa-comment"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- Message si aucun résultat -->
        <div
          *ngIf="isSearching && searchResults.length === 0"
          class="p-8 text-center text-gray-400"
        >
          <i class="fas fa-search text-4xl mb-4"></i>
          <p>Aucun utilisateur trouvé</p>
        </div>

        <!-- Liste des utilisateurs -->
        <div *ngIf="!isSearching">
          <!-- Indicateur de chargement -->
          <div
            *ngIf="isLoadingUsers && users.length === 0"
            class="p-8 text-center"
          >
            <div
              class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"
            ></div>
            <p class="text-gray-400 mt-2">Chargement des utilisateurs...</p>
          </div>

          <!-- Utilisateurs -->
          <div
            *ngFor="let user of users; trackBy: trackByUserId"
            class="user-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors"
            (click)="startConversationWithUser(user)"
          >
            <div class="flex items-center space-x-3">
              <!-- Avatar avec indicateur en ligne -->
              <div class="relative">
                <img
                  [src]="user.image || '/assets/images/default-avatar.png'"
                  [alt]="user.username"
                  class="w-12 h-12 rounded-full"
                />
                <div
                  *ngIf="isUserOnline(user)"
                  class="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-gray-800"
                ></div>
              </div>

              <!-- Informations de l'utilisateur -->
              <div class="flex-1 min-w-0">
                <h4 class="font-medium text-white truncate">
                  {{ user.username }}
                </h4>
                <p class="text-sm text-gray-400 truncate">{{ user.email }}</p>
                <p class="text-xs text-gray-500" *ngIf="user.role">
                  {{ user.role }}
                </p>
              </div>

              <!-- Statut en ligne -->
              <div class="text-right">
                <div
                  class="text-xs px-2 py-1 rounded-full"
                  [class.bg-green-600]="isUserOnline(user)"
                  [class.text-green-100]="isUserOnline(user)"
                  [class.bg-gray-600]="!isUserOnline(user)"
                  [class.text-gray-300]="!isUserOnline(user)"
                >
                  {{ isUserOnline(user) ? "En ligne" : "Hors ligne" }}
                </div>
                <div class="text-blue-400 mt-1">
                  <i class="fas fa-comment"></i>
                </div>
              </div>
            </div>
          </div>

          <!-- Bouton charger plus -->
          <div *ngIf="hasMoreUsers" class="p-4 text-center">
            <button
              (click)="loadMoreUsers()"
              [disabled]="isLoadingUsers"
              class="text-blue-400 hover:text-blue-300 disabled:text-gray-500"
            >
              <span *ngIf="!isLoadingUsers">Charger plus</span>
              <span *ngIf="isLoadingUsers">Chargement...</span>
            </button>
          </div>

          <!-- Message si aucun utilisateur -->
          <div
            *ngIf="users.length === 0 && !isLoadingUsers"
            class="p-8 text-center text-gray-400"
          >
            <i class="fas fa-users text-4xl mb-4"></i>
            <p>Aucun utilisateur trouvé</p>
          </div>
        </div>
      </div>

      <!-- ====================================================================
           ONGLET NOTIFICATIONS
           ==================================================================== -->
      <div *ngIf="activeTab === 'notifications'" class="notifications-list">
        <!-- Indicateur de chargement -->
        <div
          *ngIf="isLoadingNotifications && notifications.length === 0"
          class="p-8 text-center"
        >
          <div
            class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"
          ></div>
          <p class="text-gray-400 mt-2">Chargement des notifications...</p>
        </div>

        <!-- Notifications -->
        <div
          *ngFor="
            let notification of notifications;
            trackBy: trackByNotificationId
          "
          class="notification-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors"
          [class.bg-gray-700]="!notification.isRead"
          (click)="markNotificationAsRead(notification)"
        >
          <div class="flex items-start space-x-3">
            <!-- Icône de notification -->
            <div
              class="notification-icon p-2 rounded-full"
              [class.bg-blue-600]="notification.type === 'NEW_MESSAGE'"
              [class.bg-green-600]="notification.type === 'FRIEND_REQUEST'"
              [class.bg-yellow-600]="notification.type === 'GROUP_INVITE'"
              [class.bg-purple-600]="notification.type === 'MESSAGE_REACTION'"
              [class.bg-red-600]="notification.type === 'SYSTEM_ALERT'"
            >
              <i
                class="fas"
                [class.fa-message]="notification.type === 'NEW_MESSAGE'"
                [class.fa-user-plus]="notification.type === 'FRIEND_REQUEST'"
                [class.fa-users]="notification.type === 'GROUP_INVITE'"
                [class.fa-heart]="notification.type === 'MESSAGE_REACTION'"
                [class.fa-exclamation-triangle]="
                  notification.type === 'SYSTEM_ALERT'
                "
                class="text-white text-sm"
              ></i>
            </div>

            <!-- Contenu de la notification -->
            <div class="flex-1 min-w-0">
              <h4 class="font-medium text-white truncate">
                {{ getNotificationTitle(notification) }}
              </h4>
              <p class="text-sm text-gray-400 mt-1">
                {{ notification.content }}
              </p>
              <p class="text-xs text-gray-500 mt-2">
                {{ formatLastMessageTime(notification.timestamp) }}
              </p>
            </div>

            <!-- Indicateur non lu -->
            <div
              *ngIf="!notification.isRead"
              class="w-2 h-2 bg-blue-500 rounded-full"
            ></div>
          </div>
        </div>

        <!-- Message si aucune notification -->
        <div
          *ngIf="notifications.length === 0 && !isLoadingNotifications"
          class="p-8 text-center text-gray-400"
        >
          <i class="fas fa-bell text-4xl mb-4"></i>
          <p>Aucune notification</p>
          <p class="text-sm mt-2">
            Vous serez notifié des nouveaux messages et événements
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- ========================================================================
       ZONE PRINCIPALE - CHAT OU MESSAGE DE BIENVENUE
       ======================================================================== -->
  <div class="main-content flex-1 flex flex-col">
    <!-- Bouton menu mobile -->
    <div class="md:hidden p-4 border-b border-gray-700 bg-gray-800">
      <button
        class="p-2 rounded-lg bg-gray-700 hover:bg-gray-600"
        (click)="toggleMobileMenu()"
      >
        <i class="fas fa-bars text-white"></i>
      </button>
    </div>

    <!-- Contenu principal -->
    <div class="flex-1">
      <router-outlet></router-outlet>
    </div>
  </div>
</div>
