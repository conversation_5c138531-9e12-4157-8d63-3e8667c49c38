{"version": 3, "file": "default-src_app_services_ai_service_ts-src_app_services_equipe_service_ts-src_app_services_me-02f595.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;AAC4E;AAChC;AACK;AACS;;AAKpD,MAAOO,SAAS;EAKpBC,YAAA;IAFQ,KAAAC,YAAY,GAAY,IAAI;IAGlC;IACA;IACA,MAAMC,MAAM,GAAGJ,qEAAW,CAACK,YAAY,IAAI,yCAAyC;IAEpF,IAAI;MACF;MACA,IAAI,CAACC,KAAK,GAAG,IAAIZ,qEAAkB,CAACU,MAAM,CAAC;MAC3C;MACA,IAAI,CAACG,KAAK,GAAG,IAAI,CAACD,KAAK,CAACE,kBAAkB,CAAC;QAAED,KAAK,EAAE;MAAgB,CAAE,CAAC;MACvEE,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;KACjD,CAAC,OAAOC,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;MAEvE;MACA;MACA,IAAI,CAACL,KAAK,GAAG,EAAwB;MACrC,IAAI,CAACC,KAAK,GAAG;QACXK,eAAe,EAAEA,CAAA,KAAMC,OAAO,CAACC,OAAO,CAAC;UACrCC,QAAQ,EAAE;YAAEC,IAAI,EAAEA,CAAA,KAAM;UAA2B;SACpD;OAC4B;MAE/B;MACA,IAAI,CAACb,YAAY,GAAG,KAAK;;EAE7B;EAEA;;;;;;;EAOAc,oBAAoBA,CAACC,YAAoB,EAAEC,WAAmB,EAAEC,WAAmB;IACjF;IACA,MAAMC,oBAAoB,GAAGC,IAAI,CAACC,GAAG,CAACJ,WAAW,EAAE,CAAC,CAAC;IAErD;IACA,MAAMK,YAAY,GAAG,IAAI,CAACC,sBAAsB,CAACP,YAAY,EAAEG,oBAAoB,EAAED,WAAW,CAAC;IAEjG;IACA,IAAI,CAAC,IAAI,CAACM,cAAc,EAAE,EAAE;MAC1BjB,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;MAClF,OAAO,IAAIf,4CAAU,CAACgC,QAAQ,IAAG;QAC/BC,UAAU,CAAC,MAAK;UACdD,QAAQ,CAACE,IAAI,CAACL,YAAY,CAAC;UAC3BG,QAAQ,CAACG,QAAQ,EAAE;QACrB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC;;IAGJ;IACA,IAAIC,eAAe,GAAG,EAAE;IACxB,IAAIX,WAAW,IAAIA,WAAW,CAACY,MAAM,GAAG,CAAC,EAAE;MACzCD,eAAe,GAAG;;QAEhBX,WAAW,CAACtB,GAAG,CAAC,CAACmC,MAAM,EAAEC,KAAK,KAAI;QAClC,MAAMC,UAAU,GAAGF,MAAM,CAACG,IAAI,IAAIH,MAAM,CAACI,SAAS,IAAIJ,MAAM,CAACK,QAAQ,IAAI,UAAUJ,KAAK,GAAG,CAAC,EAAE;QAC9F,MAAMK,UAAU,GAAGN,MAAM,CAACO,IAAI,IAAI,QAAQ;QAC1C,OAAO,KAAKL,UAAU,KAAKI,UAAU,GAAG;MAC1C,CAAC,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC;OACZ;;IAGH,MAAMC,MAAM,GAAG;wFACqExB,YAAY;2BACzEG,oBAAoB;QACvCU,eAAe;;uCAEgBV,oBAAoB;;;;;;;;;;;;;;;2BAehCH,YAAY;;;;;;;;;;;;;;;;;KAiBlC;IAED,IAAI;MACF,OAAOtB,0CAAI,CAAC,IAAI,CAACW,KAAK,CAACK,eAAe,CAAC8B,MAAM,CAAC,CAAC,CAC5CC,IAAI,CACH7C,mDAAG,CAAC8C,MAAM,IAAG;QACX,IAAI;UACF,MAAMC,UAAU,GAAGD,MAAM,CAAC7B,QAAQ,CAACC,IAAI,EAAE;UACzC;UACA,MAAM8B,SAAS,GAAGD,UAAU,CAACE,KAAK,CAAC,aAAa,CAAC;UACjD,IAAID,SAAS,EAAE;YACb,OAAOE,IAAI,CAACC,KAAK,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC;WAChC,MAAM;YACLrC,OAAO,CAACyC,IAAI,CAAC,kFAAkF,CAAC;YAChG,OAAO1B,YAAY;;SAEtB,CAAC,OAAOb,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;UAC7D,OAAOa,YAAY;;MAEvB,CAAC,CAAC;MACF;MACAzB,0DAAU,CAACY,KAAK,IAAG;QACjBF,OAAO,CAACE,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;QAChE;QACA,IAAI,CAACwC,oBAAoB,EAAE;QAC3B,OAAOtD,wCAAE,CAAC2B,YAAY,CAAC;MACzB,CAAC,CAAC,CACH;KACJ,CAAC,OAAOb,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,IAAI,CAACwC,oBAAoB,EAAE;MAC3B,OAAOtD,wCAAE,CAAC2B,YAAY,CAAC;;EAE3B;EAEA;EACQC,sBAAsBA,CAACP,YAAoB,EAAEC,WAAmB,EAAEC,WAAmB;IAC3F;IACA,MAAMgC,WAAW,GAAa,EAAE;IAChC,IAAIhC,WAAW,IAAIA,WAAW,CAACY,MAAM,GAAG,CAAC,EAAE;MACzCZ,WAAW,CAACiC,OAAO,CAAC,CAACpB,MAAM,EAAEC,KAAK,KAAI;QACpC,MAAMC,UAAU,GAAGF,MAAM,CAACG,IAAI,IAAIH,MAAM,CAACI,SAAS,KAC/BJ,MAAM,CAACI,SAAS,IAAIJ,MAAM,CAACK,QAAQ,GAAG,GAAGL,MAAM,CAACI,SAAS,IAAIJ,MAAM,CAACK,QAAQ,EAAE,GAAG,IAAI,CAAC,IACvF,UAAUJ,KAAK,GAAG,CAAC,EAAE;QACvCkB,WAAW,CAACE,IAAI,CAACnB,UAAU,CAAC;MAC9B,CAAC,CAAC;;IAGJ;IACA,OAAOiB,WAAW,CAACpB,MAAM,GAAGb,WAAW,EAAE;MACvCiC,WAAW,CAACE,IAAI,CAAC,UAAUF,WAAW,CAACpB,MAAM,GAAG,CAAC,EAAE,CAAC;;IAGtD;IACA,IAAId,YAAY,CAACqC,WAAW,EAAE,CAACC,QAAQ,CAAC,WAAW,CAAC,IAAItC,YAAY,CAACqC,WAAW,EAAE,CAACC,QAAQ,CAAC,YAAY,CAAC,IAAItC,YAAY,CAACqC,WAAW,EAAE,CAACC,QAAQ,CAAC,UAAU,CAAC,EAAE;MAC5J,MAAMC,iBAAiB,GAAG,CACxB;QACErB,IAAI,EAAE,mBAAmB;QACzBsB,WAAW,EAAE,8CAA8C;QAC3DC,UAAU,EAAEP,WAAW,CAAC,CAAC,CAAC,IAAI,aAAa;QAC3CQ,KAAK,EAAE,CACL;UACEC,KAAK,EAAE,oBAAoB;UAC3BH,WAAW,EAAE,+EAA+E;UAC5FI,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE;SACT,EACD;UACEF,KAAK,EAAE,mBAAmB;UAC1BH,WAAW,EAAE,6DAA6D;UAC1EI,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAE;SACT;OAEJ,EACD;QACE3B,IAAI,EAAE,uBAAuB;QAC7BsB,WAAW,EAAE,4CAA4C;QACzDC,UAAU,EAAEP,WAAW,CAAC,CAAC,CAAC,IAAI,aAAa;QAC3CQ,KAAK,EAAE,CACL;UACEC,KAAK,EAAE,gBAAgB;UACvBH,WAAW,EAAE,oDAAoD;UACjEI,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE;SACT,EACD;UACEF,KAAK,EAAE,gBAAgB;UACvBH,WAAW,EAAE,iDAAiD;UAC9DI,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAE;SACT;OAEJ,EACD;QACE3B,IAAI,EAAE,aAAa;QACnBsB,WAAW,EAAE,qCAAqC;QAClDC,UAAU,EAAEP,WAAW,CAAC,CAAC,CAAC,IAAI,aAAa;QAC3CQ,KAAK,EAAE,CACL;UACEC,KAAK,EAAE,uBAAuB;UAC9BH,WAAW,EAAE,0CAA0C;UACvDI,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAE;SACT,EACD;UACEF,KAAK,EAAE,qBAAqB;UAC5BH,WAAW,EAAE,wDAAwD;UACrEI,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE;SACT;OAEJ,EACD;QACE3B,IAAI,EAAE,sBAAsB;QAC5BsB,WAAW,EAAE,uCAAuC;QACpDC,UAAU,EAAEP,WAAW,CAAC,CAAC,CAAC,IAAI,aAAa;QAC3CQ,KAAK,EAAE,CACL;UACEC,KAAK,EAAE,kBAAkB;UACzBH,WAAW,EAAE,sDAAsD;UACnEI,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE;SACT,EACD;UACEF,KAAK,EAAE,sBAAsB;UAC7BH,WAAW,EAAE,uEAAuE;UACpFI,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAE;SACT;OAEJ,EACD;QACE3B,IAAI,EAAE,mBAAmB;QACzBsB,WAAW,EAAE,sCAAsC;QACnDC,UAAU,EAAEP,WAAW,CAAC,CAAC,CAAC,IAAI,aAAa;QAC3CQ,KAAK,EAAE,CACL;UACEC,KAAK,EAAE,iBAAiB;UACxBH,WAAW,EAAE,4DAA4D;UACzEI,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE;SACT,EACD;UACEF,KAAK,EAAE,uBAAuB;UAC9BH,WAAW,EAAE,+DAA+D;UAC5EI,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE;SACT;OAEJ,EACD;QACE3B,IAAI,EAAE,iBAAiB;QACvBsB,WAAW,EAAE,4CAA4C;QACzDC,UAAU,EAAEP,WAAW,CAAC,CAAC,CAAC,IAAI,aAAa;QAC3CQ,KAAK,EAAE,CACL;UACEC,KAAK,EAAE,cAAc;UACrBH,WAAW,EAAE,oDAAoD;UACjEI,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAE;SACT,EACD;UACEF,KAAK,EAAE,kBAAkB;UACzBH,WAAW,EAAE,yCAAyC;UACtDI,QAAQ,EAAE,KAAK;UACfC,MAAM,EAAE;SACT;OAEJ,CACF;MAED;MACA,OAAO;QACL7C,YAAY,EAAEA,YAAY;QAC1B8C,QAAQ,EAAEP,iBAAiB,CAACQ,KAAK,CAAC,CAAC,EAAE9C,WAAW;OACjD;;IAGH;IACA,MAAM+C,WAAW,GAAG,CAClB;MAAE9B,IAAI,EAAE,SAAS;MAAEsB,WAAW,EAAE;IAA2C,CAAE,EAC7E;MAAEtB,IAAI,EAAE,UAAU;MAAEsB,WAAW,EAAE;IAA0C,CAAE,EAC7E;MAAEtB,IAAI,EAAE,iBAAiB;MAAEsB,WAAW,EAAE;IAA6C,CAAE,EACvF;MAAEtB,IAAI,EAAE,OAAO;MAAEsB,WAAW,EAAE;IAA4B,CAAE,EAC5D;MAAEtB,IAAI,EAAE,aAAa;MAAEsB,WAAW,EAAE;IAA+C,CAAE,EACrF;MAAEtB,IAAI,EAAE,eAAe;MAAEsB,WAAW,EAAE;IAAyC,CAAE,CAClF;IAED,OAAO;MACLxC,YAAY,EAAEA,YAAY;MAC1B8C,QAAQ,EAAEG,KAAK,CAACvE,IAAI,CAAC;QAAEoC,MAAM,EAAEb;MAAW,CAAE,EAAE,CAACiD,CAAC,EAAEC,CAAC,MAAM;QACvDjC,IAAI,EAAE8B,WAAW,CAACG,CAAC,GAAGH,WAAW,CAAClC,MAAM,CAAC,CAACI,IAAI;QAC9CsB,WAAW,EAAEQ,WAAW,CAACG,CAAC,GAAGH,WAAW,CAAClC,MAAM,CAAC,CAAC0B,WAAW;QAC5DC,UAAU,EAAEP,WAAW,CAACiB,CAAC,CAAC,IAAI,aAAa;QAC3CT,KAAK,EAAE,CACL;UACEC,KAAK,EAAE,cAAcK,WAAW,CAACG,CAAC,GAAGH,WAAW,CAAClC,MAAM,CAAC,CAACI,IAAI,EAAE;UAC/DsB,WAAW,EAAE,+BAA+BQ,WAAW,CAACG,CAAC,GAAGH,WAAW,CAAClC,MAAM,CAAC,CAACI,IAAI,CAACmB,WAAW,EAAE,EAAE;UACpGO,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE;SACT,EACD;UACEF,KAAK,EAAE,kBAAkBK,WAAW,CAACG,CAAC,GAAGH,WAAW,CAAClC,MAAM,CAAC,CAACI,IAAI,EAAE;UACnEsB,WAAW,EAAE,qCAAqCQ,WAAW,CAACG,CAAC,GAAGH,WAAW,CAAClC,MAAM,CAAC,CAACI,IAAI,CAACmB,WAAW,EAAE,EAAE;UAC1GO,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAE;SACT,EACD;UACEF,KAAK,EAAE,SAASK,WAAW,CAACG,CAAC,GAAGH,WAAW,CAAClC,MAAM,CAAC,CAACI,IAAI,EAAE;UAC1DsB,WAAW,EAAE,iCAAiCQ,WAAW,CAACG,CAAC,GAAGH,WAAW,CAAClC,MAAM,CAAC,CAACI,IAAI,CAACmB,WAAW,EAAE,EAAE;UACtGO,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAE;SACT;OAEJ,CAAC;KACH;EACH;EAEA;EACQrC,cAAcA,CAAA;IACpB,OAAO,IAAI,CAACvB,YAAY;EAC1B;EAEA;EACQgD,oBAAoBA,CAAA;IAC1B,IAAI,CAAChD,YAAY,GAAG,KAAK;IACzBM,OAAO,CAACyC,IAAI,CAAC,mEAAmE,CAAC;EACnF;EAEA;;;;;;EAMAoB,kBAAkBA,CAACC,QAAgB,EAAEC,cAAmB;IACtD;IACA,MAAMC,iBAAiB,GAAG,CACxB,sBAAsBD,cAAc,CAACX,KAAK,IAAI,UAAU,+FAA+F,EACvJ,0CAA0CW,cAAc,CAACX,KAAK,IAAI,UAAU,gGAAgG,EAC5K,uCAAuCU,QAAQ,8IAA8I,EAC7L,mIAAmI,CACpI;IAED;IACA,MAAMG,yBAAyB,GAAGA,CAAA,KAAK;MACrC,MAAMC,WAAW,GAAGrD,IAAI,CAACsD,KAAK,CAACtD,IAAI,CAACuD,MAAM,EAAE,GAAGJ,iBAAiB,CAACzC,MAAM,CAAC;MACxE,OAAOyC,iBAAiB,CAACE,WAAW,CAAC;IACvC,CAAC;IAED;IACA,IAAI,CAAC,IAAI,CAACjD,cAAc,EAAE,EAAE;MAC1BjB,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;MAC/E,OAAOb,wCAAE,CAAC6E,yBAAyB,EAAE,CAAC;;IAGxC,MAAMhC,MAAM,GAAG;;eAEJ8B,cAAc,CAACX,KAAK,IAAI,cAAc;qBAChCW,cAAc,CAACd,WAAW,IAAI,eAAe;;kBAEhDa,QAAQ;;;KAGrB;IAED,IAAI;MACF,OAAO3E,0CAAI,CAAC,IAAI,CAACW,KAAK,CAACK,eAAe,CAAC8B,MAAM,CAAC,CAAC,CAC5CC,IAAI,CACH7C,mDAAG,CAAC8C,MAAM,IAAG;QACX,IAAI;UACF,OAAOA,MAAM,CAAC7B,QAAQ,CAACC,IAAI,EAAE;SAC9B,CAAC,OAAOL,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;UACrE,OAAO+D,yBAAyB,EAAE;;MAEtC,CAAC,CAAC,EACF3E,0DAAU,CAACY,KAAK,IAAG;QACjBF,OAAO,CAACE,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;QAChE,IAAI,CAACwC,oBAAoB,EAAE;QAC3B,OAAOtD,wCAAE,CAAC6E,yBAAyB,EAAE,CAAC;MACxC,CAAC,CAAC,CACH;KACJ,CAAC,OAAO/D,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE,IAAI,CAACwC,oBAAoB,EAAE;MAC3B,OAAOtD,wCAAE,CAAC6E,yBAAyB,EAAE,CAAC;;EAE1C;;;uBA9YWzE,SAAS;IAAA;EAAA;;;aAATA,SAAS;MAAA6E,OAAA,EAAT7E,SAAS,CAAA8E,IAAA;MAAAC,UAAA,EAFR;IAAM;EAAA;;;;;;;;;;;;;;;;;;;;;;ACL0B;AACQ;AAEK;;;AAKrD,MAAOG,aAAa;EAGxBjF,YAAoBkF,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,GAAGrF,qEAAW,CAACsF,UAAU,OAAO;IAG/C7E,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC2E,MAAM,CAAC;EACtC;EAEAE,UAAUA,CAAA;IACR9E,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC2E,MAAM,CAAC;IAChD,OAAO,IAAI,CAACD,IAAI,CAACI,GAAG,CAAW,IAAI,CAACH,MAAM,CAAC,CAAC1C,IAAI,CAC9CuC,mDAAG,CAAEO,IAAI,IAAKhF,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE+E,IAAI,CAAC,CAAC,EACnD1F,0DAAU,CAAC,IAAI,CAAC2F,WAAW,CAAC,CAC7B;EACH;EAEAC,SAASA,CAACC,EAAU;IAClBnF,OAAO,CAACC,GAAG,CAAC,yBAAyBkF,EAAE,UAAU,IAAI,CAACP,MAAM,IAAIO,EAAE,EAAE,CAAC;IACrE,OAAO,IAAI,CAACR,IAAI,CAACI,GAAG,CAAS,GAAG,IAAI,CAACH,MAAM,IAAIO,EAAE,EAAE,CAAC,CAACjD,IAAI,CACvDuC,mDAAG,CAAEO,IAAI,IAAKhF,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE+E,IAAI,CAAC,CAAC,EAClD1F,0DAAU,CAAC,IAAI,CAAC2F,WAAW,CAAC,CAC7B;EACH;EAEAG,SAASA,CAACC,MAAc;IACtBrF,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEoF,MAAM,CAAC;IACnC,OAAO,IAAI,CAACV,IAAI,CAACW,IAAI,CAAS,IAAI,CAACV,MAAM,EAAES,MAAM,CAAC,CAACnD,IAAI,CACrDuC,mDAAG,CAAEO,IAAI,IAAKhF,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE+E,IAAI,CAAC,CAAC,EACzD1F,0DAAU,CAAC,IAAI,CAAC2F,WAAW,CAAC,CAC7B;EACH;EAEAM,YAAYA,CAACJ,EAAU,EAAEE,MAAc;IACrCrF,OAAO,CAACC,GAAG,CAAC,yBAAyBkF,EAAE,GAAG,EAAEE,MAAM,CAAC;IACnD,OAAO,IAAI,CAACV,IAAI,CAACa,GAAG,CAAS,GAAG,IAAI,CAACZ,MAAM,IAAIO,EAAE,EAAE,EAAEE,MAAM,CAAC,CAACnD,IAAI,CAC/DuC,mDAAG,CAAEO,IAAI,IAAKhF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE+E,IAAI,CAAC,CAAC,EAC3D1F,0DAAU,CAAC,IAAI,CAAC2F,WAAW,CAAC,CAC7B;EACH;EAEAQ,YAAYA,CAACN,EAAU;IACrBnF,OAAO,CAACC,GAAG,CAAC,yBAAyBkF,EAAE,EAAE,CAAC;IAC1CnF,OAAO,CAACC,GAAG,CAAC,YAAY,IAAI,CAAC2E,MAAM,IAAIO,EAAE,EAAE,CAAC;IAE5C,OAAO,IAAI,CAACR,IAAI,CAACe,MAAM,CAAC,GAAG,IAAI,CAACd,MAAM,IAAIO,EAAE,EAAE,CAAC,CAACjD,IAAI,CAClDuC,mDAAG,CAAEO,IAAI,IAAKhF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE+E,IAAI,CAAC,CAAC,EAC3D1F,0DAAU,CAAEY,KAAK,IAAI;MACnBF,OAAO,CAACE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CF,OAAO,CAACE,KAAK,CAAC,cAAc,EAAE,GAAG,IAAI,CAAC0E,MAAM,IAAIO,EAAE,EAAE,CAAC;MACrD,OAAO,IAAI,CAACF,WAAW,CAAC/E,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEAyF,iBAAiBA,CAACC,MAAc,EAAEC,MAAW;IAC3C7F,OAAO,CAACC,GAAG,CAAC,yBAAyB2F,MAAM,GAAG,EAAEC,MAAM,CAAC;IAEvD;IACA,MAAMC,UAAU,GAAG;MACjBC,MAAM,EAAEF,MAAM,CAACV,EAAE;MACjBpD,IAAI,EAAE8D,MAAM,CAAC9D,IAAI,IAAI,QAAQ,CAAE;KAChC;;IAED/B,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE6F,UAAU,CAAC;IAC9C9F,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,OAAO2F,MAAM,EAAE,QAAQ,EAAEA,MAAM,CAAC;IAC7D5F,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,OAAO4F,MAAM,CAACV,EAAE,EAAE,QAAQ,EAAEU,MAAM,CAACV,EAAE,CAAC;IAEnE;IACA,OAAO,IAAI,CAACR,IAAI,CACbW,IAAI,CAAM,GAAG,IAAI,CAACV,MAAM,IAAIgB,MAAM,UAAU,EAAEE,UAAU,CAAC,CACzD5D,IAAI,CACHuC,mDAAG,CAAEO,IAAI,IAAKhF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE+E,IAAI,CAAC,CAAC,EAC3D1F,0DAAU,CAAC,IAAI,CAAC2F,WAAW,CAAC,CAC7B;EACL;EAEAe,sBAAsBA,CAACJ,MAAc,EAAEK,QAAgB;IACrDjG,OAAO,CAACC,GAAG,CAAC,mBAAmBgG,QAAQ,cAAcL,MAAM,EAAE,CAAC;IAC9D5F,OAAO,CAACC,GAAG,CAAC,YAAY,IAAI,CAAC2E,MAAM,IAAIgB,MAAM,YAAYK,QAAQ,EAAE,CAAC;IAEpE;IACA,OAAO,IAAI,CAACtB,IAAI,CACbe,MAAM,CAAM,GAAG,IAAI,CAACd,MAAM,IAAIgB,MAAM,YAAYK,QAAQ,EAAE,CAAC,CAC3D/D,IAAI,CACHuC,mDAAG,CAAEO,IAAI,IAAKhF,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE+E,IAAI,CAAC,CAAC,EAC7D1F,0DAAU,CAAEY,KAAK,IAAI;MACnBF,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CF,OAAO,CAACE,KAAK,CACX,cAAc,EACd,GAAG,IAAI,CAAC0E,MAAM,IAAIgB,MAAM,YAAYK,QAAQ,EAAE,CAC/C;MACD,OAAO,IAAI,CAAChB,WAAW,CAAC/E,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKAgG,cAAcA,CAACN,MAAc;IAC3B5F,OAAO,CAACC,GAAG,CAAC,kCAAkC2F,MAAM,EAAE,CAAC;IACvD;IACA,OAAO,IAAI,CAACjB,IAAI,CAACI,GAAG,CAAM,GAAG,IAAI,CAACH,MAAM,IAAIgB,MAAM,EAAE,CAAC,CAAC1D,IAAI,CACxD7C,mDAAG,CAAE8G,IAAI,IAAI;MACXnG,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEkG,IAAI,CAAC;MACxC;MACA,IAAIA,IAAI,IAAIA,IAAI,CAACC,OAAO,EAAE;QACxB,OAAOD,IAAI,CAACC,OAAO,CAAC/G,GAAG,CAAEgH,QAAgB,KAAM;UAC7CC,IAAI,EAAED,QAAQ;UACdtE,IAAI,EAAE,QAAQ;UACdwE,GAAG,EAAEF,QAAQ,CAAE;SAChB,CAAC,CAAC;;;MAEL,OAAO,EAAE;IACX,CAAC,CAAC,EACF5B,mDAAG,CAAEO,IAAI,IAAKhF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE+E,IAAI,CAAC,CAAC,EAC3D1F,0DAAU,CAAC,IAAI,CAAC2F,WAAW,CAAC,CAC7B;EACH;EAEQA,WAAWA,CAAC/E,KAAwB;IAC1C,IAAIsG,YAAY,GAAG,EAAE;IAErB,IAAItG,KAAK,CAACA,KAAK,YAAYuG,UAAU,EAAE;MACrC;MACAD,YAAY,GAAG,kBAAkBtG,KAAK,CAACA,KAAK,CAACwG,OAAO,EAAE;KACvD,MAAM;MACL;MACA,MAAMpD,MAAM,GAAGpD,KAAK,CAACoD,MAAM;MAC3B,MAAMoD,OAAO,GAAGxG,KAAK,CAACA,KAAK,EAAEwG,OAAO,IAAIxG,KAAK,CAACyG,UAAU;MAExDH,YAAY,GAAG,wBAAwBlD,MAAM,cAAcoD,OAAO,EAAE;MAEpE;MACA1G,OAAO,CAACE,KAAK,CAAC,gBAAgB,EAAE;QAC9BoD,MAAM,EAAEpD,KAAK,CAACoD,MAAM;QACpBqD,UAAU,EAAEzG,KAAK,CAACyG,UAAU;QAC5BC,GAAG,EAAE1G,KAAK,CAAC0G,GAAG;QACd1G,KAAK,EAAEA,KAAK,CAACA;OACd,CAAC;MAEF,IAAIoD,MAAM,KAAK,CAAC,EAAE;QAChBtD,OAAO,CAACE,KAAK,CACX,uEAAuE,CACxE;;;IAILF,OAAO,CAACE,KAAK,CAAC,YAAY,EAAEsG,YAAY,CAAC;IACzC,OAAOhC,gDAAU,CAAC,MAAM,IAAIqC,KAAK,CAACL,YAAY,CAAC,CAAC;EAClD;;;uBAvJW9B,aAAa,EAAAoC,sDAAA,CAAAE,4DAAA;IAAA;EAAA;;;aAAbtC,aAAa;MAAAL,OAAA,EAAbK,aAAa,CAAAJ,IAAA;MAAAC,UAAA,EAFZ;IAAM;EAAA;;;;;;;;;;;;;;;;;;;;;ACN0B;AACG;AAEY;;;AAKvD,MAAO2C,aAAa;EAGxBzH,YAAoBkF,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,GAAGrF,kEAAW,CAACsF,UAAU,aAAa;IAGrD7E,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC2E,MAAM,CAAC;EAC7C;EAEAuC,UAAUA,CAAA;IACRnH,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAAC2E,MAAM,CAAC;IAClD,OAAO,IAAI,CAACD,IAAI,CAACI,GAAG,CAAW,IAAI,CAACH,MAAM,CAAC,CAAC1C,IAAI,CAC9CuC,mDAAG,CAAEO,IAAI,IAAKhF,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE+E,IAAI,CAAC,CAAC,EACrD1F,0DAAU,CAAC,IAAI,CAAC2F,WAAW,CAAC,CAC7B;EACH;EAEAmC,SAASA,CAACjC,EAAU;IAClBnF,OAAO,CAACC,GAAG,CAAC,2BAA2BkF,EAAE,UAAU,IAAI,CAACP,MAAM,IAAIO,EAAE,EAAE,CAAC;IACvE,OAAO,IAAI,CAACR,IAAI,CAACI,GAAG,CAAS,GAAG,IAAI,CAACH,MAAM,IAAIO,EAAE,EAAE,CAAC,CAACjD,IAAI,CACvDuC,mDAAG,CAAEO,IAAI,IAAKhF,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE+E,IAAI,CAAC,CAAC,EACpD1F,0DAAU,CAAC,IAAI,CAAC2F,WAAW,CAAC,CAC7B;EACH;EAEAoC,SAASA,CAACxB,MAAc;IACtB7F,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE4F,MAAM,CAAC;IACrC,OAAO,IAAI,CAAClB,IAAI,CAACW,IAAI,CAAS,IAAI,CAACV,MAAM,EAAEiB,MAAM,CAAC,CAAC3D,IAAI,CACrDuC,mDAAG,CAAEO,IAAI,IAAKhF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE+E,IAAI,CAAC,CAAC,EAC3D1F,0DAAU,CAAC,IAAI,CAAC2F,WAAW,CAAC,CAC7B;EACH;EAEAqC,YAAYA,CAACnC,EAAU;IACrBnF,OAAO,CAACC,GAAG,CAAC,2BAA2BkF,EAAE,EAAE,CAAC;IAC5C,OAAO,IAAI,CAACR,IAAI,CAACe,MAAM,CAAC,GAAG,IAAI,CAACd,MAAM,IAAIO,EAAE,EAAE,CAAC,CAACjD,IAAI,CAClDuC,mDAAG,CAAEO,IAAI,IAAKhF,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE+E,IAAI,CAAC,CAAC,EAC7D1F,0DAAU,CAAC,IAAI,CAAC2F,WAAW,CAAC,CAC7B;EACH;EAEQA,WAAWA,CAAC/E,KAAwB;IAC1C,IAAIsG,YAAY,GAAG,EAAE;IAErB,IAAItG,KAAK,CAACA,KAAK,YAAYuG,UAAU,EAAE;MACrC;MACAD,YAAY,GAAG,kBAAkBtG,KAAK,CAACA,KAAK,CAACwG,OAAO,EAAE;KACvD,MAAM;MACL;MACA,MAAMpD,MAAM,GAAGpD,KAAK,CAACoD,MAAM;MAC3B,MAAMoD,OAAO,GAAGxG,KAAK,CAACA,KAAK,EAAEwG,OAAO,IAAIxG,KAAK,CAACyG,UAAU;MAExDH,YAAY,GAAG,wBAAwBlD,MAAM,cAAcoD,OAAO,EAAE;MAEpE;MACA1G,OAAO,CAACE,KAAK,CAAC,gBAAgB,EAAE;QAC9BoD,MAAM,EAAEpD,KAAK,CAACoD,MAAM;QACpBqD,UAAU,EAAEzG,KAAK,CAACyG,UAAU;QAC5BC,GAAG,EAAE1G,KAAK,CAAC0G,GAAG;QACd1G,KAAK,EAAEA,KAAK,CAACA;OACd,CAAC;MAEF,IAAIoD,MAAM,KAAK,CAAC,EAAE;QAChBtD,OAAO,CAACE,KAAK,CACX,uEAAuE,CACxE;;;IAILF,OAAO,CAACE,KAAK,CAAC,YAAY,EAAEsG,YAAY,CAAC;IACzC,OAAOhC,gDAAU,CAAC,MAAM,IAAIqC,KAAK,CAACL,YAAY,CAAC,CAAC;EAClD;;;uBArEWU,aAAa,EAAAJ,sDAAA,CAAAE,4DAAA;IAAA;EAAA;;;aAAbE,aAAa;MAAA7C,OAAA,EAAb6C,aAAa,CAAA5C,IAAA;MAAAC,UAAA,EAFZ;IAAM;EAAA;;;;;;;;;;;;;;;;;ACP+B;;AAW7C,MAAOiD,mBAAmB;EAG9B/H,YAAA;IAFQ,KAAAgI,mBAAmB,GAAG,IAAIF,iDAAe,CAAsB,IAAI,CAAC;EAE7D;EAEfG,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACD,mBAAmB,CAACE,YAAY,EAAE;EAChD;EAEAC,WAAWA,CAAClB,OAAe,EAAEmB,OAAA,GAAkB,IAAI;IACjD,IAAI,CAACC,IAAI,CAAC;MAAEpB,OAAO;MAAEqB,IAAI,EAAE,SAAS;MAAEF;IAAO,CAAE,CAAC;EAClD;EAEAG,SAASA,CAACtB,OAAe,EAAEmB,OAAA,GAAkB,IAAI;IAC/C,IAAI,CAACC,IAAI,CAAC;MAAEpB,OAAO;MAAEqB,IAAI,EAAE,OAAO;MAAEF;IAAO,CAAE,CAAC;EAChD;EAEAI,QAAQA,CAACvB,OAAe,EAAEmB,OAAA,GAAkB,IAAI;IAC9C,IAAI,CAACC,IAAI,CAAC;MAAEpB,OAAO;MAAEqB,IAAI,EAAE,MAAM;MAAEF;IAAO,CAAE,CAAC;EAC/C;EAEAK,WAAWA,CAACxB,OAAe,EAAEmB,OAAA,GAAkB,IAAI;IACjD,IAAI,CAACC,IAAI,CAAC;MAAEpB,OAAO;MAAEqB,IAAI,EAAE,SAAS;MAAEF;IAAO,CAAE,CAAC;EAClD;EAEQC,IAAIA,CAACK,YAA0B;IACrC,IAAI,CAACV,mBAAmB,CAACrG,IAAI,CAAC+G,YAAY,CAAC;IAE3C,IAAIA,YAAY,CAACN,OAAO,EAAE;MACxB1G,UAAU,CAAC,MAAK;QACd;QACA,IAAI,IAAI,CAACsG,mBAAmB,CAACW,KAAK,KAAKD,YAAY,EAAE;UACnD,IAAI,CAACV,mBAAmB,CAACrG,IAAI,CAAC,IAAI,CAAC;;MAEvC,CAAC,EAAE+G,YAAY,CAACN,OAAO,CAAC;;EAE5B;EAEAQ,KAAKA,CAAA;IACH,IAAI,CAACZ,mBAAmB,CAACrG,IAAI,CAAC,IAAI,CAAC;EACrC;;;uBAxCWoG,mBAAmB;IAAA;EAAA;;;aAAnBA,mBAAmB;MAAAnD,OAAA,EAAnBmD,mBAAmB,CAAAlD,IAAA;MAAAC,UAAA,EAFlB;IAAM;EAAA;;;;;;;;;;;;;;;;;;;;;ACR0B;AACG;AACY;;;AAMvD,MAAO+D,WAAW;EAGtB7I,YAAoBkF,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAG,GAAGrF,kEAAW,CAACsF,UAAU,OAAO;IAG/C7E,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC2E,MAAM,CAAC;EAC3C;EAEA;EACA2D,QAAQA,CAAA;IACN,OAAO,IAAI,CAAC5D,IAAI,CAACI,GAAG,CAAS,IAAI,CAACH,MAAM,CAAC,CAAC1C,IAAI,CAC5CuC,mDAAG,CAAEO,IAAI,IAAKhF,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE+E,IAAI,CAAC,CAAC,EACnD1F,0DAAU,CAAC,IAAI,CAAC2F,WAAW,CAAC,CAC7B;EACH;EAEA;EACAuD,cAAcA,CAAC5C,MAAc;IAC3B,OAAO,IAAI,CAACjB,IAAI,CAACI,GAAG,CAAS,GAAG,IAAI,CAACH,MAAM,SAASgB,MAAM,EAAE,CAAC,CAAC1D,IAAI,CAChEuC,mDAAG,CAAEO,IAAI,IAAKhF,OAAO,CAACC,GAAG,CAAC,kBAAkB2F,MAAM,YAAY,EAAEZ,IAAI,CAAC,CAAC,EACtE1F,0DAAU,CAAC,IAAI,CAAC2F,WAAW,CAAC,CAC7B;EACH;EAEA;EACAwD,OAAOA,CAACtD,EAAU;IAChB,OAAO,IAAI,CAACR,IAAI,CAACI,GAAG,CAAO,GAAG,IAAI,CAACH,MAAM,IAAIO,EAAE,EAAE,CAAC,CAACjD,IAAI,CACrDuC,mDAAG,CAAEO,IAAI,IAAKhF,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE+E,IAAI,CAAC,CAAC,EAClD1F,0DAAU,CAAC,IAAI,CAAC2F,WAAW,CAAC,CAC7B;EACH;EAEA;EACAyD,UAAUA,CAACC,IAAU;IACnB,OAAO,IAAI,CAAChE,IAAI,CAACW,IAAI,CAAO,IAAI,CAACV,MAAM,EAAE+D,IAAI,CAAC,CAACzG,IAAI,CACjDuC,mDAAG,CAAEO,IAAI,IAAKhF,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE+E,IAAI,CAAC,CAAC,EACjD1F,0DAAU,CAAC,IAAI,CAAC2F,WAAW,CAAC,CAC7B;EACH;EAEA;EACA2D,UAAUA,CAACzD,EAAU,EAAEwD,IAAU;IAC/B,OAAO,IAAI,CAAChE,IAAI,CAACa,GAAG,CAAO,GAAG,IAAI,CAACZ,MAAM,IAAIO,EAAE,EAAE,EAAEwD,IAAI,CAAC,CAACzG,IAAI,CAC3DuC,mDAAG,CAAEO,IAAI,IAAKhF,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE+E,IAAI,CAAC,CAAC,EACjD1F,0DAAU,CAAC,IAAI,CAAC2F,WAAW,CAAC,CAC7B;EACH;EAEA;EACA4D,UAAUA,CAAC1D,EAAU;IACnB,OAAO,IAAI,CAACR,IAAI,CAACe,MAAM,CAAC,GAAG,IAAI,CAACd,MAAM,IAAIO,EAAE,EAAE,CAAC,CAACjD,IAAI,CAClDuC,mDAAG,CAAEO,IAAI,IAAKhF,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE+E,IAAI,CAAC,CAAC,EACjD1F,0DAAU,CAAC,IAAI,CAAC2F,WAAW,CAAC,CAC7B;EACH;EAEA;EACA6D,gBAAgBA,CACd3D,EAAU,EACV7B,MAAuC;IAEvC,OAAO,IAAI,CAACqB,IAAI,CACboE,KAAK,CAAO,GAAG,IAAI,CAACnE,MAAM,IAAIO,EAAE,SAAS,EAAE;MAAE7B;IAAM,CAAE,CAAC,CACtDpB,IAAI,CACHuC,mDAAG,CAAEO,IAAI,IAAKhF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE+E,IAAI,CAAC,CAAC,EACxD1F,0DAAU,CAAC,IAAI,CAAC2F,WAAW,CAAC,CAC7B;EACL;EAEA;EACQA,WAAWA,CAAC/E,KAAwB;IAC1C,IAAIsG,YAAY,GAAG,EAAE;IACrB,IAAItG,KAAK,CAACA,KAAK,YAAYuG,UAAU,EAAE;MACrC;MACAD,YAAY,GAAG,UAAUtG,KAAK,CAACA,KAAK,CAACwG,OAAO,EAAE;KAC/C,MAAM;MACL;MACAF,YAAY,GAAG,eAAetG,KAAK,CAACoD,MAAM,cAAcpD,KAAK,CAACwG,OAAO,EAAE;;IAEzE1G,OAAO,CAACE,KAAK,CAACsG,YAAY,CAAC;IAC3B,OAAOhC,gDAAU,CAAC,MAAM,IAAIqC,KAAK,CAACL,YAAY,CAAC,CAAC;EAClD;;;uBAhFW8B,WAAW,EAAAxB,sDAAA,CAAAE,4DAAA;IAAA;EAAA;;;aAAXsB,WAAW;MAAAjE,OAAA,EAAXiE,WAAW,CAAAhE,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACRgB;AAC0I;AACnI;AACE;AACgB;AAC4C;AACO;AACV;AACH;AAClB;AACvC;;AAE1C;AACA;AACA;AACA;AACA;AACA,SAAS2G,YAAYA,CAACC,IAAI,EAAEC,MAAM,EAAEC,mBAAmB,EAAE;EACrD,KAAK,IAAIC,GAAG,IAAIF,MAAM,EAAE;IACpB,IAAIA,MAAM,CAACG,cAAc,CAACD,GAAG,CAAC,EAAE;MAC5B,MAAMlD,KAAK,GAAGgD,MAAM,CAACE,GAAG,CAAC;MACzB,IAAIlD,KAAK,EAAE;QACP+C,IAAI,CAACK,WAAW,CAACF,GAAG,EAAElD,KAAK,EAAEiD,mBAAmB,EAAEI,GAAG,CAACH,GAAG,CAAC,GAAG,WAAW,GAAG,EAAE,CAAC;MAClF,CAAC,MACI;QACDH,IAAI,CAACO,cAAc,CAACJ,GAAG,CAAC;MAC5B;IACJ;EACJ;EACA,OAAOH,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASQ,4BAA4BA,CAACC,OAAO,EAAEC,MAAM,EAAE;EACnD,MAAMC,UAAU,GAAGD,MAAM,GAAG,EAAE,GAAG,MAAM;EACvCX,YAAY,CAACU,OAAO,CAACG,KAAK,EAAE;IACxB,cAAc,EAAEF,MAAM,GAAG,EAAE,GAAG,MAAM;IACpC,mBAAmB,EAAEA,MAAM,GAAG,EAAE,GAAG,MAAM;IACzC,6BAA6B,EAAEA,MAAM,GAAG,EAAE,GAAG,aAAa;IAC1D,aAAa,EAAEC,UAAU;IACzB,iBAAiB,EAAEA,UAAU;IAC7B,qBAAqB,EAAEA,UAAU;IACjC,kBAAkB,EAAEA;EACxB,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,gBAAgBA,CAACJ,OAAO,EAAEC,MAAM,EAAER,mBAAmB,EAAE;EAC5DH,YAAY,CAACU,OAAO,CAACG,KAAK,EAAE;IACxBE,QAAQ,EAAEJ,MAAM,GAAG,EAAE,GAAG,OAAO;IAC/BK,GAAG,EAAEL,MAAM,GAAG,EAAE,GAAG,GAAG;IACtBM,OAAO,EAAEN,MAAM,GAAG,EAAE,GAAG,GAAG;IAC1BO,IAAI,EAAEP,MAAM,GAAG,EAAE,GAAG;EACxB,CAAC,EAAER,mBAAmB,CAAC;AAC3B;AACA;AACA;AACA;AACA;AACA,SAASgB,iBAAiBA,CAACC,SAAS,EAAEC,gBAAgB,EAAE;EACpD,OAAOA,gBAAgB,IAAIA,gBAAgB,IAAI,MAAM,GAC/CD,SAAS,GAAG,GAAG,GAAGC,gBAAgB,GAClCD,SAAS;AACnB;;AAEA;AACA,SAASE,qBAAqBA,CAACpE,KAAK,EAAE;EAClC;EACA,MAAMqE,UAAU,GAAGrE,KAAK,CAACtF,WAAW,CAAC,CAAC,CAAC4J,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI;EACpE,OAAOC,UAAU,CAACvE,KAAK,CAAC,GAAGqE,UAAU;AACzC;AACA;AACA,SAASG,kCAAkCA,CAAChB,OAAO,EAAE;EACjD,MAAMiB,aAAa,GAAGC,gBAAgB,CAAClB,OAAO,CAAC;EAC/C,MAAMmB,sBAAsB,GAAGC,qBAAqB,CAACH,aAAa,EAAE,qBAAqB,CAAC;EAC1F,MAAMI,QAAQ,GAAGF,sBAAsB,CAACG,IAAI,CAACC,IAAI,IAAIA,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,KAAK,CAAC;EAC5F;EACA,IAAI,CAACF,QAAQ,EAAE;IACX,OAAO,CAAC;EACZ;EACA;EACA;EACA,MAAMG,aAAa,GAAGL,sBAAsB,CAACL,OAAO,CAACO,QAAQ,CAAC;EAC9D,MAAMI,YAAY,GAAGL,qBAAqB,CAACH,aAAa,EAAE,qBAAqB,CAAC;EAChF,MAAMS,SAAS,GAAGN,qBAAqB,CAACH,aAAa,EAAE,kBAAkB,CAAC;EAC1E,OAAQL,qBAAqB,CAACa,YAAY,CAACD,aAAa,CAAC,CAAC,GACtDZ,qBAAqB,CAACc,SAAS,CAACF,aAAa,CAAC,CAAC;AACvD;AACA;AACA,SAASJ,qBAAqBA,CAACH,aAAa,EAAElL,IAAI,EAAE;EAChD,MAAMyG,KAAK,GAAGyE,aAAa,CAACU,gBAAgB,CAAC5L,IAAI,CAAC;EAClD,OAAOyG,KAAK,CAACoF,KAAK,CAAC,GAAG,CAAC,CAACnO,GAAG,CAACoO,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;AACpD;;AAEA;AACA,SAASC,oBAAoBA,CAAC/B,OAAO,EAAE;EACnC,MAAMgC,UAAU,GAAGhC,OAAO,CAACiC,qBAAqB,CAAC,CAAC;EAClD;EACA;EACA;EACA;EACA,OAAO;IACH3B,GAAG,EAAE0B,UAAU,CAAC1B,GAAG;IACnB4B,KAAK,EAAEF,UAAU,CAACE,KAAK;IACvBC,MAAM,EAAEH,UAAU,CAACG,MAAM;IACzB3B,IAAI,EAAEwB,UAAU,CAACxB,IAAI;IACrB4B,KAAK,EAAEJ,UAAU,CAACI,KAAK;IACvBC,MAAM,EAAEL,UAAU,CAACK,MAAM;IACzBC,CAAC,EAAEN,UAAU,CAACM,CAAC;IACfC,CAAC,EAAEP,UAAU,CAACO;EAClB,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAACR,UAAU,EAAEM,CAAC,EAAEC,CAAC,EAAE;EAC1C,MAAM;IAAEjC,GAAG;IAAE6B,MAAM;IAAE3B,IAAI;IAAE0B;EAAM,CAAC,GAAGF,UAAU;EAC/C,OAAOO,CAAC,IAAIjC,GAAG,IAAIiC,CAAC,IAAIJ,MAAM,IAAIG,CAAC,IAAI9B,IAAI,IAAI8B,CAAC,IAAIJ,KAAK;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASO,gBAAgBA,CAACT,UAAU,EAAE1B,GAAG,EAAEE,IAAI,EAAE;EAC7CwB,UAAU,CAAC1B,GAAG,IAAIA,GAAG;EACrB0B,UAAU,CAACG,MAAM,GAAGH,UAAU,CAAC1B,GAAG,GAAG0B,UAAU,CAACK,MAAM;EACtDL,UAAU,CAACxB,IAAI,IAAIA,IAAI;EACvBwB,UAAU,CAACE,KAAK,GAAGF,UAAU,CAACxB,IAAI,GAAGwB,UAAU,CAACI,KAAK;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,uBAAuBA,CAACC,IAAI,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;EAClE,MAAM;IAAExC,GAAG;IAAE4B,KAAK;IAAEC,MAAM;IAAE3B,IAAI;IAAE4B,KAAK;IAAEC;EAAO,CAAC,GAAGM,IAAI;EACxD,MAAMI,UAAU,GAAGX,KAAK,GAAGQ,SAAS;EACpC,MAAMI,UAAU,GAAGX,MAAM,GAAGO,SAAS;EACrC,OAAQE,QAAQ,GAAGxC,GAAG,GAAG0C,UAAU,IAC/BF,QAAQ,GAAGX,MAAM,GAAGa,UAAU,IAC9BH,QAAQ,GAAGrC,IAAI,GAAGuC,UAAU,IAC5BF,QAAQ,GAAGX,KAAK,GAAGa,UAAU;AACrC;;AAEA;AACA,MAAME,qBAAqB,CAAC;EACxBpP,WAAWA,CAACqP,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B;IACA,IAAI,CAACC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC9B;EACA;EACA3G,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC0G,SAAS,CAAC1G,KAAK,CAAC,CAAC;EAC1B;EACA;EACA4G,KAAKA,CAACC,QAAQ,EAAE;IACZ,IAAI,CAAC7G,KAAK,CAAC,CAAC;IACZ,IAAI,CAAC0G,SAAS,CAACI,GAAG,CAAC,IAAI,CAACL,SAAS,EAAE;MAC/BM,cAAc,EAAE,IAAI,CAACC,yBAAyB,CAAC;IACnD,CAAC,CAAC;IACFH,QAAQ,CAACtM,OAAO,CAACgJ,OAAO,IAAI;MACxB,IAAI,CAACmD,SAAS,CAACI,GAAG,CAACvD,OAAO,EAAE;QACxBwD,cAAc,EAAE;UAAElD,GAAG,EAAEN,OAAO,CAAC0D,SAAS;UAAElD,IAAI,EAAER,OAAO,CAAC2D;QAAW,CAAC;QACpE3B,UAAU,EAAED,oBAAoB,CAAC/B,OAAO;MAC5C,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;EACA4D,YAAYA,CAACC,KAAK,EAAE;IAChB,MAAMC,MAAM,GAAG3F,sEAAe,CAAC0F,KAAK,CAAC;IACrC,MAAME,cAAc,GAAG,IAAI,CAACZ,SAAS,CAAChK,GAAG,CAAC2K,MAAM,CAAC;IACjD,IAAI,CAACC,cAAc,EAAE;MACjB,OAAO,IAAI;IACf;IACA,MAAMP,cAAc,GAAGO,cAAc,CAACP,cAAc;IACpD,IAAIQ,MAAM;IACV,IAAIC,OAAO;IACX,IAAIH,MAAM,KAAK,IAAI,CAACZ,SAAS,EAAE;MAC3B,MAAMgB,sBAAsB,GAAG,IAAI,CAACT,yBAAyB,CAAC,CAAC;MAC/DO,MAAM,GAAGE,sBAAsB,CAAC5D,GAAG;MACnC2D,OAAO,GAAGC,sBAAsB,CAAC1D,IAAI;IACzC,CAAC,MACI;MACDwD,MAAM,GAAGF,MAAM,CAACJ,SAAS;MACzBO,OAAO,GAAGH,MAAM,CAACH,UAAU;IAC/B;IACA,MAAMQ,aAAa,GAAGX,cAAc,CAAClD,GAAG,GAAG0D,MAAM;IACjD,MAAMI,cAAc,GAAGZ,cAAc,CAAChD,IAAI,GAAGyD,OAAO;IACpD;IACA;IACA,IAAI,CAACd,SAAS,CAACnM,OAAO,CAAC,CAACqJ,QAAQ,EAAEgE,IAAI,KAAK;MACvC,IAAIhE,QAAQ,CAAC2B,UAAU,IAAI8B,MAAM,KAAKO,IAAI,IAAIP,MAAM,CAACQ,QAAQ,CAACD,IAAI,CAAC,EAAE;QACjE5B,gBAAgB,CAACpC,QAAQ,CAAC2B,UAAU,EAAEmC,aAAa,EAAEC,cAAc,CAAC;MACxE;IACJ,CAAC,CAAC;IACFZ,cAAc,CAAClD,GAAG,GAAG0D,MAAM;IAC3BR,cAAc,CAAChD,IAAI,GAAGyD,OAAO;IAC7B,OAAO;MAAE3D,GAAG,EAAE6D,aAAa;MAAE3D,IAAI,EAAE4D;IAAe,CAAC;EACvD;EACA;AACJ;AACA;AACA;AACA;AACA;EACIX,yBAAyBA,CAAA,EAAG;IACxB,OAAO;MAAEnD,GAAG,EAAEiE,MAAM,CAACC,OAAO;MAAEhE,IAAI,EAAE+D,MAAM,CAACE;IAAQ,CAAC;EACxD;AACJ;;AAEA;AACA,SAASC,aAAaA,CAACL,IAAI,EAAE;EACzB,MAAMM,KAAK,GAAGN,IAAI,CAACO,SAAS,CAAC,IAAI,CAAC;EAClC,MAAMC,iBAAiB,GAAGF,KAAK,CAACG,gBAAgB,CAAC,MAAM,CAAC;EACxD,MAAMC,QAAQ,GAAGV,IAAI,CAACU,QAAQ,CAAC7N,WAAW,CAAC,CAAC;EAC5C;EACAyN,KAAK,CAACK,eAAe,CAAC,IAAI,CAAC;EAC3B,KAAK,IAAIhN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6M,iBAAiB,CAAClP,MAAM,EAAEqC,CAAC,EAAE,EAAE;IAC/C6M,iBAAiB,CAAC7M,CAAC,CAAC,CAACgN,eAAe,CAAC,IAAI,CAAC;EAC9C;EACA,IAAID,QAAQ,KAAK,QAAQ,EAAE;IACvBE,kBAAkB,CAACZ,IAAI,EAAEM,KAAK,CAAC;EACnC,CAAC,MACI,IAAII,QAAQ,KAAK,OAAO,IAAIA,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,UAAU,EAAE;IAC/EG,iBAAiB,CAACb,IAAI,EAAEM,KAAK,CAAC;EAClC;EACAQ,YAAY,CAAC,QAAQ,EAAEd,IAAI,EAAEM,KAAK,EAAEM,kBAAkB,CAAC;EACvDE,YAAY,CAAC,yBAAyB,EAAEd,IAAI,EAAEM,KAAK,EAAEO,iBAAiB,CAAC;EACvE,OAAOP,KAAK;AAChB;AACA;AACA,SAASQ,YAAYA,CAACC,QAAQ,EAAEf,IAAI,EAAEM,KAAK,EAAEU,QAAQ,EAAE;EACnD,MAAMC,kBAAkB,GAAGjB,IAAI,CAACS,gBAAgB,CAACM,QAAQ,CAAC;EAC1D,IAAIE,kBAAkB,CAAC3P,MAAM,EAAE;IAC3B,MAAM4P,aAAa,GAAGZ,KAAK,CAACG,gBAAgB,CAACM,QAAQ,CAAC;IACtD,KAAK,IAAIpN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsN,kBAAkB,CAAC3P,MAAM,EAAEqC,CAAC,EAAE,EAAE;MAChDqN,QAAQ,CAACC,kBAAkB,CAACtN,CAAC,CAAC,EAAEuN,aAAa,CAACvN,CAAC,CAAC,CAAC;IACrD;EACJ;AACJ;AACA;AACA,IAAIwN,aAAa,GAAG,CAAC;AACrB;AACA,SAASN,iBAAiBA,CAAC1F,MAAM,EAAEmF,KAAK,EAAE;EACtC;EACA,IAAIA,KAAK,CAACxI,IAAI,KAAK,MAAM,EAAE;IACvBwI,KAAK,CAACnI,KAAK,GAAGgD,MAAM,CAAChD,KAAK;EAC9B;EACA;EACA;EACA;EACA,IAAImI,KAAK,CAACxI,IAAI,KAAK,OAAO,IAAIwI,KAAK,CAAC5O,IAAI,EAAE;IACtC4O,KAAK,CAAC5O,IAAI,GAAI,aAAY4O,KAAK,CAAC5O,IAAK,IAAGyP,aAAa,EAAG,EAAC;EAC7D;AACJ;AACA;AACA,SAASP,kBAAkBA,CAACzF,MAAM,EAAEmF,KAAK,EAAE;EACvC,MAAMc,OAAO,GAAGd,KAAK,CAACe,UAAU,CAAC,IAAI,CAAC;EACtC,IAAID,OAAO,EAAE;IACT;IACA;IACA,IAAI;MACAA,OAAO,CAACE,SAAS,CAACnG,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;IACnC,CAAC,CACD,MAAM,CAAE;EACZ;AACJ;;AAEA;AACA,MAAMoG,2BAA2B,GAAGxH,sFAA+B,CAAC;EAAEyH,OAAO,EAAE;AAAK,CAAC,CAAC;AACtF;AACA,MAAMC,0BAA0B,GAAG1H,sFAA+B,CAAC;EAAEyH,OAAO,EAAE;AAAM,CAAC,CAAC;AACtF;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,uBAAuB,GAAG,GAAG;AACnC;AACA,MAAMC,uBAAuB,GAAG,IAAIC,GAAG,CAAC;AACpC;AACA,UAAU,CACb,CAAC;AACF;AACA;AACA;AACA,MAAMC,OAAO,CAAC;EACV;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS,IAAI,CAAC,EAAE,IAAI,CAACC,cAAc,IAAI,IAAI,CAACA,cAAc,CAACF,QAAQ,CAAC;EACpF;EACA,IAAIA,QAAQA,CAAC3J,KAAK,EAAE;IAChB,MAAM8J,QAAQ,GAAGhI,4EAAqB,CAAC9B,KAAK,CAAC;IAC7C,IAAI8J,QAAQ,KAAK,IAAI,CAACF,SAAS,EAAE;MAC7B,IAAI,CAACA,SAAS,GAAGE,QAAQ;MACzB,IAAI,CAACC,6BAA6B,CAAC,CAAC;MACpC,IAAI,CAACC,QAAQ,CAACxP,OAAO,CAACyP,MAAM,IAAI1G,4BAA4B,CAAC0G,MAAM,EAAEH,QAAQ,CAAC,CAAC;IACnF;EACJ;EACAzS,WAAWA,CAACmM,OAAO,EAAE0G,OAAO,EAAExD,SAAS,EAAEyD,OAAO,EAAEC,cAAc,EAAEC,iBAAiB,EAAE;IACjF,IAAI,CAACH,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACxD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACyD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG;MAAExE,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IACvC;IACA,IAAI,CAACwE,gBAAgB,GAAG;MAAEzE,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IACtC;AACR;AACA;AACA;IACQ,IAAI,CAACyE,mBAAmB,GAAG,KAAK;IAChC;IACA,IAAI,CAACC,WAAW,GAAG,IAAIrI,yCAAO,CAAC,CAAC;IAChC;IACA,IAAI,CAACsI,wBAAwB,GAAGrI,8CAAY,CAACsI,KAAK;IAClD;IACA,IAAI,CAACC,sBAAsB,GAAGvI,8CAAY,CAACsI,KAAK;IAChD;IACA,IAAI,CAACE,mBAAmB,GAAGxI,8CAAY,CAACsI,KAAK;IAC7C;IACA,IAAI,CAACG,mBAAmB,GAAGzI,8CAAY,CAACsI,KAAK;IAC7C;IACA,IAAI,CAACI,gBAAgB,GAAG,IAAI;IAC5B;IACA,IAAI,CAACC,0BAA0B,GAAG,IAAI;IACtC;IACA,IAAI,CAAChB,QAAQ,GAAG,EAAE;IAClB;IACA,IAAI,CAACiB,gBAAgB,GAAG,IAAIxB,GAAG,CAAC,CAAC;IACjC;IACA,IAAI,CAACyB,UAAU,GAAG,KAAK;IACvB;AACR;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,CAAC;IACvB,IAAI,CAACvB,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACwB,aAAa,GAAG,IAAIhJ,yCAAO,CAAC,CAAC;IAClC;IACA,IAAI,CAACiJ,OAAO,GAAG,IAAIjJ,yCAAO,CAAC,CAAC;IAC5B;IACA,IAAI,CAACkJ,QAAQ,GAAG,IAAIlJ,yCAAO,CAAC,CAAC;IAC7B;IACA,IAAI,CAACmJ,KAAK,GAAG,IAAInJ,yCAAO,CAAC,CAAC;IAC1B;IACA,IAAI,CAACoJ,OAAO,GAAG,IAAIpJ,yCAAO,CAAC,CAAC;IAC5B;IACA,IAAI,CAACqJ,MAAM,GAAG,IAAIrJ,yCAAO,CAAC,CAAC;IAC3B;IACA,IAAI,CAACsJ,OAAO,GAAG,IAAItJ,yCAAO,CAAC,CAAC;IAC5B;AACR;AACA;AACA;IACQ,IAAI,CAACuJ,KAAK,GAAG,IAAI,CAAClB,WAAW;IAC7B;IACA,IAAI,CAACmB,YAAY,GAAIvE,KAAK,IAAK;MAC3B,IAAI,CAAC+D,aAAa,CAACpS,IAAI,CAAC,CAAC;MACzB;MACA,IAAI,IAAI,CAACgR,QAAQ,CAAC7Q,MAAM,EAAE;QACtB,MAAM0S,YAAY,GAAG,IAAI,CAACC,gBAAgB,CAACzE,KAAK,CAAC;QACjD,IAAIwE,YAAY,IAAI,CAAC,IAAI,CAACZ,gBAAgB,CAAC5H,GAAG,CAACwI,YAAY,CAAC,IAAI,CAAC,IAAI,CAAClC,QAAQ,EAAE;UAC5E,IAAI,CAACoC,uBAAuB,CAACF,YAAY,EAAExE,KAAK,CAAC;QACrD;MACJ,CAAC,MACI,IAAI,CAAC,IAAI,CAACsC,QAAQ,EAAE;QACrB,IAAI,CAACoC,uBAAuB,CAAC,IAAI,CAACC,YAAY,EAAE3E,KAAK,CAAC;MAC1D;IACJ,CAAC;IACD;IACA,IAAI,CAAC4E,YAAY,GAAI5E,KAAK,IAAK;MAC3B,MAAM6E,eAAe,GAAG,IAAI,CAACC,yBAAyB,CAAC9E,KAAK,CAAC;MAC7D,IAAI,CAAC,IAAI,CAACmD,mBAAmB,EAAE;QAC3B,MAAM4B,SAAS,GAAG3T,IAAI,CAAC4T,GAAG,CAACH,eAAe,CAACpG,CAAC,GAAG,IAAI,CAACwG,qBAAqB,CAACxG,CAAC,CAAC;QAC5E,MAAMyG,SAAS,GAAG9T,IAAI,CAAC4T,GAAG,CAACH,eAAe,CAACnG,CAAC,GAAG,IAAI,CAACuG,qBAAqB,CAACvG,CAAC,CAAC;QAC5E,MAAMyG,eAAe,GAAGJ,SAAS,GAAGG,SAAS,IAAI,IAAI,CAACrC,OAAO,CAACuC,kBAAkB;QAChF;QACA;QACA;QACA;QACA,IAAID,eAAe,EAAE;UACjB,MAAME,cAAc,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAI,IAAI,CAACC,cAAc,GAAG,IAAI,CAACC,kBAAkB,CAACzF,KAAK,CAAC;UACzF,MAAM0F,SAAS,GAAG,IAAI,CAAClD,cAAc;UACrC,IAAI,CAAC6C,cAAc,EAAE;YACjB,IAAI,CAACM,gBAAgB,CAAC3F,KAAK,CAAC;YAC5B;UACJ;UACA;UACA;UACA;UACA,IAAI,CAAC0F,SAAS,IAAK,CAACA,SAAS,CAACE,UAAU,CAAC,CAAC,IAAI,CAACF,SAAS,CAACG,WAAW,CAAC,CAAE,EAAE;YACrE;YACA;YACA7F,KAAK,CAAC8F,cAAc,CAAC,CAAC;YACtB,IAAI,CAAC3C,mBAAmB,GAAG,IAAI;YAC/B,IAAI,CAACL,OAAO,CAACiD,GAAG,CAAC,MAAM,IAAI,CAACC,kBAAkB,CAAChG,KAAK,CAAC,CAAC;UAC1D;QACJ;QACA;MACJ;MACA;MACA;MACA;MACAA,KAAK,CAAC8F,cAAc,CAAC,CAAC;MACtB,MAAMG,0BAA0B,GAAG,IAAI,CAACC,8BAA8B,CAACrB,eAAe,CAAC;MACvF,IAAI,CAACsB,SAAS,GAAG,IAAI;MACrB,IAAI,CAACC,yBAAyB,GAAGvB,eAAe;MAChD,IAAI,CAACwB,4BAA4B,CAACJ,0BAA0B,CAAC;MAC7D,IAAI,IAAI,CAACzD,cAAc,EAAE;QACrB,IAAI,CAAC8D,0BAA0B,CAACL,0BAA0B,EAAEpB,eAAe,CAAC;MAChF,CAAC,MACI;QACD;QACA;QACA,MAAM0B,MAAM,GAAG,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACxB,qBAAqB;QAC5F,MAAMyB,eAAe,GAAG,IAAI,CAACxD,gBAAgB;QAC7CwD,eAAe,CAACjI,CAAC,GAAGwH,0BAA0B,CAACxH,CAAC,GAAG8H,MAAM,CAAC9H,CAAC,GAAG,IAAI,CAACwE,iBAAiB,CAACxE,CAAC;QACtFiI,eAAe,CAAChI,CAAC,GAAGuH,0BAA0B,CAACvH,CAAC,GAAG6H,MAAM,CAAC7H,CAAC,GAAG,IAAI,CAACuE,iBAAiB,CAACvE,CAAC;QACtF,IAAI,CAACiI,0BAA0B,CAACD,eAAe,CAACjI,CAAC,EAAEiI,eAAe,CAAChI,CAAC,CAAC;MACzE;MACA;MACA;MACA;MACA,IAAI,IAAI,CAAC0E,WAAW,CAACwD,SAAS,CAAC9U,MAAM,EAAE;QACnC,IAAI,CAACgR,OAAO,CAACiD,GAAG,CAAC,MAAM;UACnB,IAAI,CAAC3C,WAAW,CAACzR,IAAI,CAAC;YAClBgK,MAAM,EAAE,IAAI;YACZkJ,eAAe,EAAEoB,0BAA0B;YAC3CjG,KAAK;YACL6G,QAAQ,EAAE,IAAI,CAACC,gBAAgB,CAACb,0BAA0B,CAAC;YAC3Dc,KAAK,EAAE,IAAI,CAACC;UAChB,CAAC,CAAC;QACN,CAAC,CAAC;MACN;IACJ,CAAC;IACD;IACA,IAAI,CAACC,UAAU,GAAIjH,KAAK,IAAK;MACzB,IAAI,CAAC2F,gBAAgB,CAAC3F,KAAK,CAAC;IAChC,CAAC;IACD;IACA,IAAI,CAACkH,gBAAgB,GAAIlH,KAAK,IAAK;MAC/B,IAAI,IAAI,CAAC2C,QAAQ,CAAC7Q,MAAM,EAAE;QACtB,MAAM0S,YAAY,GAAG,IAAI,CAACC,gBAAgB,CAACzE,KAAK,CAAC;QACjD,IAAIwE,YAAY,IAAI,CAAC,IAAI,CAACZ,gBAAgB,CAAC5H,GAAG,CAACwI,YAAY,CAAC,IAAI,CAAC,IAAI,CAAClC,QAAQ,EAAE;UAC5EtC,KAAK,CAAC8F,cAAc,CAAC,CAAC;QAC1B;MACJ,CAAC,MACI,IAAI,CAAC,IAAI,CAACxD,QAAQ,EAAE;QACrB;QACA;QACAtC,KAAK,CAAC8F,cAAc,CAAC,CAAC;MAC1B;IACJ,CAAC;IACD,IAAI,CAACqB,eAAe,CAAChL,OAAO,CAAC,CAACiL,UAAU,CAACvE,OAAO,CAACwE,aAAa,IAAI,IAAI,CAAC;IACvE,IAAI,CAACC,gBAAgB,GAAG,IAAIlI,qBAAqB,CAACC,SAAS,CAAC;IAC5D2D,iBAAiB,CAACuE,gBAAgB,CAAC,IAAI,CAAC;EAC5C;EACA;AACJ;AACA;AACA;EACIC,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA;EACAC,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC/C,YAAY;EAC5B;EACA;AACJ;AACA;AACA;EACIgD,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC/B,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC4B,qBAAqB,CAAC,CAAC,GAAG,IAAI,CAACE,cAAc,CAAC,CAAC;EACnF;EACA;EACAE,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAAClF,QAAQ,GAAGkF,OAAO,CAACjY,GAAG,CAACgT,MAAM,IAAIlI,oEAAa,CAACkI,MAAM,CAAC,CAAC;IAC5D,IAAI,CAACD,QAAQ,CAACxP,OAAO,CAACyP,MAAM,IAAI1G,4BAA4B,CAAC0G,MAAM,EAAE,IAAI,CAACN,QAAQ,CAAC,CAAC;IACpF,IAAI,CAACI,6BAA6B,CAAC,CAAC;IACpC;IACA;IACA;IACA;IACA,MAAMoF,eAAe,GAAG,IAAI1F,GAAG,CAAC,CAAC;IACjC,IAAI,CAACwB,gBAAgB,CAACzQ,OAAO,CAACyP,MAAM,IAAI;MACpC,IAAI,IAAI,CAACD,QAAQ,CAAC1F,OAAO,CAAC2F,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;QACpCkF,eAAe,CAACC,GAAG,CAACnF,MAAM,CAAC;MAC/B;IACJ,CAAC,CAAC;IACF,IAAI,CAACgB,gBAAgB,GAAGkE,eAAe;IACvC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIE,mBAAmBA,CAACC,QAAQ,EAAE;IAC1B,IAAI,CAACC,gBAAgB,GAAGD,QAAQ;IAChC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIE,uBAAuBA,CAACF,QAAQ,EAAE;IAC9B,IAAI,CAACG,oBAAoB,GAAGH,QAAQ;IACpC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACId,eAAeA,CAACkB,WAAW,EAAE;IACzB,MAAMlM,OAAO,GAAGzB,oEAAa,CAAC2N,WAAW,CAAC;IAC1C,IAAIlM,OAAO,KAAK,IAAI,CAACwI,YAAY,EAAE;MAC/B,IAAI,IAAI,CAACA,YAAY,EAAE;QACnB,IAAI,CAAC2D,2BAA2B,CAAC,IAAI,CAAC3D,YAAY,CAAC;MACvD;MACA,IAAI,CAAC7B,OAAO,CAACyF,iBAAiB,CAAC,MAAM;QACjCpM,OAAO,CAACqM,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACjE,YAAY,EAAEtC,0BAA0B,CAAC;QACpF9F,OAAO,CAACqM,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAACjE,YAAY,EAAExC,2BAA2B,CAAC;QACtF5F,OAAO,CAACqM,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACtB,gBAAgB,EAAEjF,0BAA0B,CAAC;MAC5F,CAAC,CAAC;MACF,IAAI,CAACwG,iBAAiB,GAAGC,SAAS;MAClC,IAAI,CAAC/D,YAAY,GAAGxI,OAAO;IAC/B;IACA,IAAI,OAAOwM,UAAU,KAAK,WAAW,IAAI,IAAI,CAAChE,YAAY,YAAYgE,UAAU,EAAE;MAC9E,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACjE,YAAY,CAACkE,eAAe;IAC7D;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;EACIC,mBAAmBA,CAACC,eAAe,EAAE;IACjC,IAAI,CAACrF,gBAAgB,GAAGqF,eAAe,GAAGrO,oEAAa,CAACqO,eAAe,CAAC,GAAG,IAAI;IAC/E,IAAI,CAACtF,mBAAmB,CAACuF,WAAW,CAAC,CAAC;IACtC,IAAID,eAAe,EAAE;MACjB,IAAI,CAACtF,mBAAmB,GAAG,IAAI,CAACV,cAAc,CACzCkG,MAAM,CAAC,EAAE,CAAC,CACVC,SAAS,CAAC,MAAM,IAAI,CAACC,8BAA8B,CAAC,CAAC,CAAC;IAC/D;IACA,OAAO,IAAI;EACf;EACA;EACA/B,UAAUA,CAACgC,MAAM,EAAE;IACf,IAAI,CAACC,cAAc,GAAGD,MAAM;IAC5B,OAAO,IAAI;EACf;EACA;EACAE,OAAOA,CAAA,EAAG;IACN,IAAI,CAAChB,2BAA2B,CAAC,IAAI,CAAC3D,YAAY,CAAC;IACnD;IACA;IACA,IAAI,IAAI,CAACiB,UAAU,CAAC,CAAC,EAAE;MACnB;MACA;MACA,IAAI,CAACjB,YAAY,EAAE4E,MAAM,CAAC,CAAC;IAC/B;IACA,IAAI,CAACC,OAAO,EAAED,MAAM,CAAC,CAAC;IACtB,IAAI,CAACE,eAAe,CAAC,CAAC;IACtB,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAAC1G,iBAAiB,CAAC2G,cAAc,CAAC,IAAI,CAAC;IAC3C,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAAC7F,aAAa,CAACnS,QAAQ,CAAC,CAAC;IAC7B,IAAI,CAACoS,OAAO,CAACpS,QAAQ,CAAC,CAAC;IACvB,IAAI,CAACqS,QAAQ,CAACrS,QAAQ,CAAC,CAAC;IACxB,IAAI,CAACsS,KAAK,CAACtS,QAAQ,CAAC,CAAC;IACrB,IAAI,CAACuS,OAAO,CAACvS,QAAQ,CAAC,CAAC;IACvB,IAAI,CAACwS,MAAM,CAACxS,QAAQ,CAAC,CAAC;IACtB,IAAI,CAACyS,OAAO,CAACzS,QAAQ,CAAC,CAAC;IACvB,IAAI,CAACwR,WAAW,CAACxR,QAAQ,CAAC,CAAC;IAC3B,IAAI,CAAC+Q,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACiB,gBAAgB,CAAChL,KAAK,CAAC,CAAC;IAC7B,IAAI,CAAC4J,cAAc,GAAGkG,SAAS;IAC/B,IAAI,CAACjF,mBAAmB,CAACuF,WAAW,CAAC,CAAC;IACtC,IAAI,CAAC1B,gBAAgB,CAAC1O,KAAK,CAAC,CAAC;IAC7B,IAAI,CAAC8K,gBAAgB,GACjB,IAAI,CAACiB,YAAY,GACb,IAAI,CAACiE,gBAAgB,GACjB,IAAI,CAACR,oBAAoB,GACrB,IAAI,CAACF,gBAAgB,GACjB,IAAI,CAACsB,OAAO,GACR,IAAI,CAACH,cAAc,GACf,IAAI;EACpC;EACA;EACAzD,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACzC,mBAAmB,IAAI,IAAI,CAACH,iBAAiB,CAAC4C,UAAU,CAAC,IAAI,CAAC;EAC9E;EACA;EACAiE,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAClF,YAAY,CAACrI,KAAK,CAACO,SAAS,GAAG,IAAI,CAAC4L,iBAAiB,IAAI,EAAE;IAChE,IAAI,CAACvF,gBAAgB,GAAG;MAAEzE,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IACtC,IAAI,CAACuE,iBAAiB,GAAG;MAAExE,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;EAC3C;EACA;AACJ;AACA;AACA;EACIoL,aAAaA,CAAClH,MAAM,EAAE;IAClB,IAAI,CAAC,IAAI,CAACgB,gBAAgB,CAAC5H,GAAG,CAAC4G,MAAM,CAAC,IAAI,IAAI,CAACD,QAAQ,CAAC1F,OAAO,CAAC2F,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;MAC1E,IAAI,CAACgB,gBAAgB,CAACmE,GAAG,CAACnF,MAAM,CAAC;MACjC1G,4BAA4B,CAAC0G,MAAM,EAAE,IAAI,CAAC;IAC9C;EACJ;EACA;AACJ;AACA;AACA;EACImH,YAAYA,CAACnH,MAAM,EAAE;IACjB,IAAI,IAAI,CAACgB,gBAAgB,CAAC5H,GAAG,CAAC4G,MAAM,CAAC,EAAE;MACnC,IAAI,CAACgB,gBAAgB,CAAC3N,MAAM,CAAC2M,MAAM,CAAC;MACpC1G,4BAA4B,CAAC0G,MAAM,EAAE,IAAI,CAACN,QAAQ,CAAC;IACvD;EACJ;EACA;EACA0H,aAAaA,CAACC,SAAS,EAAE;IACrB,IAAI,CAACpG,UAAU,GAAGoG,SAAS;IAC3B,OAAO,IAAI;EACf;EACA;EACAC,kBAAkBA,CAACxE,SAAS,EAAE;IAC1B,IAAI,CAAClD,cAAc,GAAGkD,SAAS;EACnC;EACA;AACJ;AACA;EACIyE,mBAAmBA,CAAA,EAAG;IAClB,MAAM3N,QAAQ,GAAG,IAAI,CAACoJ,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC1C,gBAAgB,GAAG,IAAI,CAACD,iBAAiB;IACnF,OAAO;MAAExE,CAAC,EAAEjC,QAAQ,CAACiC,CAAC;MAAEC,CAAC,EAAElC,QAAQ,CAACkC;IAAE,CAAC;EAC3C;EACA;AACJ;AACA;AACA;EACI0L,mBAAmBA,CAACzR,KAAK,EAAE;IACvB,IAAI,CAACuK,gBAAgB,GAAG;MAAEzE,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IACtC,IAAI,CAACuE,iBAAiB,CAACxE,CAAC,GAAG9F,KAAK,CAAC8F,CAAC;IAClC,IAAI,CAACwE,iBAAiB,CAACvE,CAAC,GAAG/F,KAAK,CAAC+F,CAAC;IAClC,IAAI,CAAC,IAAI,CAAC8D,cAAc,EAAE;MACtB,IAAI,CAACmE,0BAA0B,CAAChO,KAAK,CAAC8F,CAAC,EAAE9F,KAAK,CAAC+F,CAAC,CAAC;IACrD;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACI2L,oBAAoBA,CAAC1R,KAAK,EAAE;IACxB,IAAI,CAAC2R,iBAAiB,GAAG3R,KAAK;IAC9B,OAAO,IAAI;EACf;EACA;EACA4R,4BAA4BA,CAAA,EAAG;IAC3B,MAAM/N,QAAQ,GAAG,IAAI,CAAC4J,yBAAyB;IAC/C,IAAI5J,QAAQ,IAAI,IAAI,CAACgG,cAAc,EAAE;MACjC,IAAI,CAAC8D,0BAA0B,CAAC,IAAI,CAACJ,8BAA8B,CAAC1J,QAAQ,CAAC,EAAEA,QAAQ,CAAC;IAC5F;EACJ;EACA;EACAoN,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACvG,wBAAwB,CAAC2F,WAAW,CAAC,CAAC;IAC3C,IAAI,CAACzF,sBAAsB,CAACyF,WAAW,CAAC,CAAC;IACzC,IAAI,CAACxF,mBAAmB,CAACwF,WAAW,CAAC,CAAC;EAC1C;EACA;EACAS,eAAeA,CAAA,EAAG;IACd,IAAI,CAACe,QAAQ,EAAEjB,MAAM,CAAC,CAAC;IACvB,IAAI,CAACkB,WAAW,EAAEC,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACF,QAAQ,GAAG,IAAI,CAACC,WAAW,GAAG,IAAI;EAC3C;EACA;EACAf,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACjC,YAAY,EAAE8B,MAAM,CAAC,CAAC;IAC3B,IAAI,CAACoB,eAAe,EAAED,OAAO,CAAC,CAAC;IAC/B,IAAI,CAACjD,YAAY,GAAG,IAAI,CAACkD,eAAe,GAAG,IAAI;EACnD;EACA;AACJ;AACA;AACA;EACIhF,gBAAgBA,CAAC3F,KAAK,EAAE;IACpB;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACgD,iBAAiB,CAAC4C,UAAU,CAAC,IAAI,CAAC,EAAE;MAC1C;IACJ;IACA,IAAI,CAACgE,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAAC5G,iBAAiB,CAAC4H,YAAY,CAAC,IAAI,CAAC;IACzC,IAAI,CAAClI,6BAA6B,CAAC,CAAC;IACpC,IAAI,IAAI,CAACC,QAAQ,EAAE;MACf,IAAI,CAACgC,YAAY,CAACrI,KAAK,CAACuO,uBAAuB,GAC3C,IAAI,CAACC,wBAAwB;IACrC;IACA,IAAI,CAAC,IAAI,CAAC3H,mBAAmB,EAAE;MAC3B;IACJ;IACA,IAAI,CAACc,QAAQ,CAACtS,IAAI,CAAC;MAAEgK,MAAM,EAAE,IAAI;MAAEqE;IAAM,CAAC,CAAC;IAC3C,IAAI,IAAI,CAACwC,cAAc,EAAE;MACrB;MACA,IAAI,CAACA,cAAc,CAACuI,cAAc,CAAC,CAAC;MACpC,IAAI,CAACC,4BAA4B,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QAC3C,IAAI,CAACC,qBAAqB,CAAClL,KAAK,CAAC;QACjC,IAAI,CAACmL,wBAAwB,CAAC,CAAC;QAC/B,IAAI,CAACnI,iBAAiB,CAAC4H,YAAY,CAAC,IAAI,CAAC;MAC7C,CAAC,CAAC;IACN,CAAC,MACI;MACD;MACA;MACA;MACA,IAAI,CAAC3H,iBAAiB,CAACxE,CAAC,GAAG,IAAI,CAACyE,gBAAgB,CAACzE,CAAC;MAClD,MAAMoG,eAAe,GAAG,IAAI,CAACC,yBAAyB,CAAC9E,KAAK,CAAC;MAC7D,IAAI,CAACiD,iBAAiB,CAACvE,CAAC,GAAG,IAAI,CAACwE,gBAAgB,CAACxE,CAAC;MAClD,IAAI,CAACoE,OAAO,CAACiD,GAAG,CAAC,MAAM;QACnB,IAAI,CAAC7B,KAAK,CAACvS,IAAI,CAAC;UACZgK,MAAM,EAAE,IAAI;UACZkL,QAAQ,EAAE,IAAI,CAACC,gBAAgB,CAACjC,eAAe,CAAC;UAChDuG,SAAS,EAAEvG,eAAe;UAC1B7E;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;MACF,IAAI,CAACmL,wBAAwB,CAAC,CAAC;MAC/B,IAAI,CAACnI,iBAAiB,CAAC4H,YAAY,CAAC,IAAI,CAAC;IAC7C;EACJ;EACA;EACA5E,kBAAkBA,CAAChG,KAAK,EAAE;IACtB,IAAIqL,YAAY,CAACrL,KAAK,CAAC,EAAE;MACrB,IAAI,CAACsL,mBAAmB,GAAGhG,IAAI,CAACC,GAAG,CAAC,CAAC;IACzC;IACA,IAAI,CAAC7C,6BAA6B,CAAC,CAAC;IACpC,MAAM6I,aAAa,GAAG,IAAI,CAAC/I,cAAc;IACzC,IAAI+I,aAAa,EAAE;MACf,MAAMpP,OAAO,GAAG,IAAI,CAACwI,YAAY;MACjC,MAAMyE,MAAM,GAAGjN,OAAO,CAACqP,UAAU;MACjC,MAAMC,WAAW,GAAI,IAAI,CAAChE,YAAY,GAAG,IAAI,CAACiE,yBAAyB,CAAC,CAAE;MAC1E,MAAMC,MAAM,GAAI,IAAI,CAACnC,OAAO,GAAG,IAAI,CAACA,OAAO,IAAI,IAAI,CAACnK,SAAS,CAACuM,aAAa,CAAC,EAAE,CAAE;MAChF;MACA,MAAMC,UAAU,GAAG,IAAI,CAACrR,cAAc,CAAC,CAAC;MACxC;MACA4O,MAAM,CAAC0C,YAAY,CAACH,MAAM,EAAExP,OAAO,CAAC;MACpC;MACA;MACA,IAAI,CAACsM,iBAAiB,GAAGtM,OAAO,CAACG,KAAK,CAACO,SAAS,IAAI,EAAE;MACtD;MACA;MACA,IAAI,CAAC2N,QAAQ,GAAG,IAAI,CAACuB,qBAAqB,CAAC,CAAC;MAC5C;MACA;MACA;MACAxP,gBAAgB,CAACJ,OAAO,EAAE,KAAK,EAAEgG,uBAAuB,CAAC;MACzD,IAAI,CAAC9C,SAAS,CAAC2M,IAAI,CAACC,WAAW,CAAC7C,MAAM,CAAC8C,YAAY,CAACT,WAAW,EAAEtP,OAAO,CAAC,CAAC;MAC1E,IAAI,CAACgQ,yBAAyB,CAAC/C,MAAM,EAAEyC,UAAU,CAAC,CAACI,WAAW,CAAC,IAAI,CAACzB,QAAQ,CAAC;MAC7E,IAAI,CAACxG,OAAO,CAACrS,IAAI,CAAC;QAAEgK,MAAM,EAAE,IAAI;QAAEqE;MAAM,CAAC,CAAC,CAAC,CAAC;MAC5CuL,aAAa,CAACa,KAAK,CAAC,CAAC;MACrB,IAAI,CAACC,iBAAiB,GAAGd,aAAa;MACtC,IAAI,CAACe,aAAa,GAAGf,aAAa,CAACgB,YAAY,CAAC,IAAI,CAAC;IACzD,CAAC,MACI;MACD,IAAI,CAACvI,OAAO,CAACrS,IAAI,CAAC;QAAEgK,MAAM,EAAE,IAAI;QAAEqE;MAAM,CAAC,CAAC;MAC1C,IAAI,CAACqM,iBAAiB,GAAG,IAAI,CAACC,aAAa,GAAG5D,SAAS;IAC3D;IACA;IACA;IACA,IAAI,CAACpB,gBAAgB,CAAC9H,KAAK,CAAC+L,aAAa,GAAGA,aAAa,CAACiB,oBAAoB,CAAC,CAAC,GAAG,EAAE,CAAC;EAC1F;EACA;AACJ;AACA;AACA;AACA;AACA;EACI9H,uBAAuBA,CAAC+H,gBAAgB,EAAEzM,KAAK,EAAE;IAC7C;IACA;IACA,IAAI,IAAI,CAACqJ,cAAc,EAAE;MACrBrJ,KAAK,CAAC0M,eAAe,CAAC,CAAC;IAC3B;IACA,MAAM9G,UAAU,GAAG,IAAI,CAACA,UAAU,CAAC,CAAC;IACpC,MAAM+G,eAAe,GAAGtB,YAAY,CAACrL,KAAK,CAAC;IAC3C,MAAM4M,sBAAsB,GAAG,CAACD,eAAe,IAAI3M,KAAK,CAAC6M,MAAM,KAAK,CAAC;IACrE,MAAMxE,WAAW,GAAG,IAAI,CAAC1D,YAAY;IACrC,MAAM1E,MAAM,GAAG3F,sEAAe,CAAC0F,KAAK,CAAC;IACrC,MAAM8M,gBAAgB,GAAG,CAACH,eAAe,IACrC,IAAI,CAACrB,mBAAmB,IACxB,IAAI,CAACA,mBAAmB,GAAGpJ,uBAAuB,GAAGoD,IAAI,CAACC,GAAG,CAAC,CAAC;IACnE,MAAMwH,WAAW,GAAGJ,eAAe,GAC7B9R,mFAAgC,CAACmF,KAAK,CAAC,GACvClF,kFAA+B,CAACkF,KAAK,CAAC;IAC5C;IACA;IACA;IACA;IACA;IACA;IACA,IAAIC,MAAM,IAAIA,MAAM,CAAC+M,SAAS,IAAIhN,KAAK,CAAC1H,IAAI,KAAK,WAAW,EAAE;MAC1D0H,KAAK,CAAC8F,cAAc,CAAC,CAAC;IAC1B;IACA;IACA,IAAIF,UAAU,IAAIgH,sBAAsB,IAAIE,gBAAgB,IAAIC,WAAW,EAAE;MACzE;IACJ;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACpK,QAAQ,CAAC7Q,MAAM,EAAE;MACtB,MAAMmb,UAAU,GAAG5E,WAAW,CAAC/L,KAAK;MACpC,IAAI,CAACwO,wBAAwB,GAAGmC,UAAU,CAACpC,uBAAuB,IAAI,EAAE;MACxEoC,UAAU,CAACpC,uBAAuB,GAAG,aAAa;IACtD;IACA,IAAI,CAAC1H,mBAAmB,GAAG,IAAI,CAACgD,SAAS,GAAG,KAAK;IACjD;IACA;IACA,IAAI,CAACyD,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACnD,kBAAkB,GAAG,IAAI,CAAC9B,YAAY,CAACvG,qBAAqB,CAAC,CAAC;IACnE,IAAI,CAACiF,wBAAwB,GAAG,IAAI,CAACL,iBAAiB,CAACkK,WAAW,CAAChE,SAAS,CAAC,IAAI,CAACtE,YAAY,CAAC;IAC/F,IAAI,CAACrB,sBAAsB,GAAG,IAAI,CAACP,iBAAiB,CAACmK,SAAS,CAACjE,SAAS,CAAC,IAAI,CAACjC,UAAU,CAAC;IACzF,IAAI,CAACzD,mBAAmB,GAAG,IAAI,CAACR,iBAAiB,CAC5CoK,QAAQ,CAAC,IAAI,CAAC5S,cAAc,CAAC,CAAC,CAAC,CAC/B0O,SAAS,CAACmE,WAAW,IAAI,IAAI,CAACC,eAAe,CAACD,WAAW,CAAC,CAAC;IAChE,IAAI,IAAI,CAAC3J,gBAAgB,EAAE;MACvB,IAAI,CAAC6J,aAAa,GAAGrP,oBAAoB,CAAC,IAAI,CAACwF,gBAAgB,CAAC;IACpE;IACA;IACA;IACA;IACA,MAAM8J,eAAe,GAAG,IAAI,CAACtF,gBAAgB;IAC7C,IAAI,CAACuF,wBAAwB,GACzBD,eAAe,IAAIA,eAAe,CAACvF,QAAQ,IAAI,CAACuF,eAAe,CAACE,SAAS,GACnE;MAAEjP,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC,GACd,IAAI,CAACiP,4BAA4B,CAAC,IAAI,CAAClH,kBAAkB,EAAEgG,gBAAgB,EAAEzM,KAAK,CAAC;IAC7F,MAAM6E,eAAe,GAAI,IAAI,CAACI,qBAAqB,GAC/C,IAAI,CAACmB,yBAAyB,GAC1B,IAAI,CAACtB,yBAAyB,CAAC9E,KAAK,CAAE;IAC9C,IAAI,CAACgH,sBAAsB,GAAG;MAAEvI,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IAC5C,IAAI,CAACkP,qCAAqC,GAAG;MAAEnP,CAAC,EAAEoG,eAAe,CAACpG,CAAC;MAAEC,CAAC,EAAEmG,eAAe,CAACnG;IAAE,CAAC;IAC3F,IAAI,CAAC8G,cAAc,GAAGF,IAAI,CAACC,GAAG,CAAC,CAAC;IAChC,IAAI,CAACvC,iBAAiB,CAAC6K,aAAa,CAAC,IAAI,EAAE7N,KAAK,CAAC;EACrD;EACA;EACAkL,qBAAqBA,CAAClL,KAAK,EAAE;IACzB;IACA;IACA;IACA;IACAzD,gBAAgB,CAAC,IAAI,CAACoI,YAAY,EAAE,IAAI,EAAExC,uBAAuB,CAAC;IAClE,IAAI,CAACqH,OAAO,CAACgC,UAAU,CAACU,YAAY,CAAC,IAAI,CAACvH,YAAY,EAAE,IAAI,CAAC6E,OAAO,CAAC;IACrE,IAAI,CAACC,eAAe,CAAC,CAAC;IACtB,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC1B,IAAI,CAACjD,kBAAkB,GACnB,IAAI,CAAC8G,aAAa,GACd,IAAI,CAACO,YAAY,GACb,IAAI,CAACrF,iBAAiB,GAClBC,SAAS;IACzB;IACA,IAAI,CAAC5F,OAAO,CAACiD,GAAG,CAAC,MAAM;MACnB,MAAML,SAAS,GAAG,IAAI,CAAClD,cAAc;MACrC,MAAMuL,YAAY,GAAGrI,SAAS,CAAC6G,YAAY,CAAC,IAAI,CAAC;MACjD,MAAM1H,eAAe,GAAG,IAAI,CAACC,yBAAyB,CAAC9E,KAAK,CAAC;MAC7D,MAAM6G,QAAQ,GAAG,IAAI,CAACC,gBAAgB,CAACjC,eAAe,CAAC;MACvD,MAAMmJ,sBAAsB,GAAGtI,SAAS,CAACuI,gBAAgB,CAACpJ,eAAe,CAACpG,CAAC,EAAEoG,eAAe,CAACnG,CAAC,CAAC;MAC/F,IAAI,CAACwF,KAAK,CAACvS,IAAI,CAAC;QAAEgK,MAAM,EAAE,IAAI;QAAEkL,QAAQ;QAAEuE,SAAS,EAAEvG,eAAe;QAAE7E;MAAM,CAAC,CAAC;MAC9E,IAAI,CAACqE,OAAO,CAAC1S,IAAI,CAAC;QACduc,IAAI,EAAE,IAAI;QACVH,YAAY;QACZI,aAAa,EAAE,IAAI,CAAC7B,aAAa;QACjC5G,SAAS,EAAEA,SAAS;QACpB0I,iBAAiB,EAAE,IAAI,CAAC/B,iBAAiB;QACzC2B,sBAAsB;QACtBnH,QAAQ;QACRuE,SAAS,EAAEvG,eAAe;QAC1B7E;MACJ,CAAC,CAAC;MACF0F,SAAS,CAAC2I,IAAI,CAAC,IAAI,EAAEN,YAAY,EAAE,IAAI,CAACzB,aAAa,EAAE,IAAI,CAACD,iBAAiB,EAAE2B,sBAAsB,EAAEnH,QAAQ,EAAEhC,eAAe,EAAE7E,KAAK,CAAC;MACxI,IAAI,CAACwC,cAAc,GAAG,IAAI,CAAC6J,iBAAiB;IAChD,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACI/F,0BAA0BA,CAAC;IAAE7H,CAAC;IAAEC;EAAE,CAAC,EAAE;IAAED,CAAC,EAAE6P,IAAI;IAAE5P,CAAC,EAAE6P;EAAK,CAAC,EAAE;IACvD;IACA,IAAIC,YAAY,GAAG,IAAI,CAACnC,iBAAiB,CAACoC,gCAAgC,CAAC,IAAI,EAAEhQ,CAAC,EAAEC,CAAC,CAAC;IACtF;IACA;IACA;IACA;IACA,IAAI,CAAC8P,YAAY,IACb,IAAI,CAAChM,cAAc,KAAK,IAAI,CAAC6J,iBAAiB,IAC9C,IAAI,CAACA,iBAAiB,CAAC4B,gBAAgB,CAACxP,CAAC,EAAEC,CAAC,CAAC,EAAE;MAC/C8P,YAAY,GAAG,IAAI,CAACnC,iBAAiB;IACzC;IACA,IAAImC,YAAY,IAAIA,YAAY,KAAK,IAAI,CAAChM,cAAc,EAAE;MACtD,IAAI,CAACM,OAAO,CAACiD,GAAG,CAAC,MAAM;QACnB;QACA,IAAI,CAAC3B,MAAM,CAACzS,IAAI,CAAC;UAAEuc,IAAI,EAAE,IAAI;UAAExI,SAAS,EAAE,IAAI,CAAClD;QAAe,CAAC,CAAC;QAChE,IAAI,CAACA,cAAc,CAACkM,IAAI,CAAC,IAAI,CAAC;QAC9B;QACA,IAAI,CAAClM,cAAc,GAAGgM,YAAY;QAClC,IAAI,CAAChM,cAAc,CAACmM,KAAK,CAAC,IAAI,EAAElQ,CAAC,EAAEC,CAAC,EAAE8P,YAAY,KAAK,IAAI,CAACnC,iBAAiB;QACzE;QACA;QACAmC,YAAY,CAACI,eAAe,GAC1B,IAAI,CAACtC,aAAa,GAClB5D,SAAS,CAAC;QAChB,IAAI,CAACvE,OAAO,CAACxS,IAAI,CAAC;UACduc,IAAI,EAAE,IAAI;UACVxI,SAAS,EAAE8I,YAAY;UACvBT,YAAY,EAAES,YAAY,CAACjC,YAAY,CAAC,IAAI;QAChD,CAAC,CAAC;MACN,CAAC,CAAC;IACN;IACA;IACA,IAAI,IAAI,CAAC3G,UAAU,CAAC,CAAC,EAAE;MACnB,IAAI,CAACpD,cAAc,CAACqM,0BAA0B,CAACP,IAAI,EAAEC,IAAI,CAAC;MAC1D,IAAI,CAAC/L,cAAc,CAACsM,SAAS,CAAC,IAAI,EAAErQ,CAAC,EAAEC,CAAC,EAAE,IAAI,CAACsI,sBAAsB,CAAC;MACtE,IAAI,IAAI,CAACR,iBAAiB,EAAE;QACxB,IAAI,CAACuI,sBAAsB,CAACtQ,CAAC,EAAEC,CAAC,CAAC;MACrC,CAAC,MACI;QACD,IAAI,CAACqQ,sBAAsB,CAACtQ,CAAC,GAAG,IAAI,CAACgP,wBAAwB,CAAChP,CAAC,EAAEC,CAAC,GAAG,IAAI,CAAC+O,wBAAwB,CAAC/O,CAAC,CAAC;MACzG;IACJ;EACJ;EACA;AACJ;AACA;AACA;EACIqN,qBAAqBA,CAAA,EAAG;IACpB,MAAMiD,aAAa,GAAG,IAAI,CAAC9G,gBAAgB;IAC3C,MAAM+G,YAAY,GAAG,IAAI,CAACA,YAAY;IACtC,MAAMzB,eAAe,GAAGwB,aAAa,GAAGA,aAAa,CAAC/G,QAAQ,GAAG,IAAI;IACrE,IAAIiH,OAAO;IACX,IAAI1B,eAAe,IAAIwB,aAAa,EAAE;MAClC;MACA;MACA,MAAMG,QAAQ,GAAGH,aAAa,CAACtB,SAAS,GAAG,IAAI,CAACjH,kBAAkB,GAAG,IAAI;MACzE,MAAM2I,OAAO,GAAGJ,aAAa,CAACK,aAAa,CAACC,kBAAkB,CAAC9B,eAAe,EAAEwB,aAAa,CAACpN,OAAO,CAAC;MACtGwN,OAAO,CAACG,aAAa,CAAC,CAAC;MACvBL,OAAO,GAAGM,WAAW,CAACJ,OAAO,EAAE,IAAI,CAAC/P,SAAS,CAAC;MAC9C,IAAI,CAACoL,WAAW,GAAG2E,OAAO;MAC1B,IAAIJ,aAAa,CAACtB,SAAS,EAAE;QACzB+B,gBAAgB,CAACP,OAAO,EAAEC,QAAQ,CAAC;MACvC,CAAC,MACI;QACDD,OAAO,CAAC5S,KAAK,CAACO,SAAS,GAAG6S,YAAY,CAAC,IAAI,CAACzK,qBAAqB,CAACxG,CAAC,EAAE,IAAI,CAACwG,qBAAqB,CAACvG,CAAC,CAAC;MACtG;IACJ,CAAC,MACI;MACDwQ,OAAO,GAAGrO,aAAa,CAAC,IAAI,CAAC8D,YAAY,CAAC;MAC1C8K,gBAAgB,CAACP,OAAO,EAAE,IAAI,CAACzI,kBAAkB,CAAC;MAClD,IAAI,IAAI,CAACgC,iBAAiB,EAAE;QACxByG,OAAO,CAAC5S,KAAK,CAACO,SAAS,GAAG,IAAI,CAAC4L,iBAAiB;MACpD;IACJ;IACAhN,YAAY,CAACyT,OAAO,CAAC5S,KAAK,EAAE;MACxB;MACA;MACA,gBAAgB,EAAE,MAAM;MACxB;MACA,QAAQ,EAAE,GAAG;MACb,UAAU,EAAE,OAAO;MACnB,KAAK,EAAE,GAAG;MACV,MAAM,EAAE,GAAG;MACX,SAAS,EAAG,GAAE,IAAI,CAACuG,OAAO,CAAC8M,MAAM,IAAI,IAAK;IAC9C,CAAC,EAAExN,uBAAuB,CAAC;IAC3BjG,4BAA4B,CAACgT,OAAO,EAAE,KAAK,CAAC;IAC5CA,OAAO,CAACU,SAAS,CAAC7H,GAAG,CAAC,kBAAkB,CAAC;IACzCmH,OAAO,CAACW,YAAY,CAAC,KAAK,EAAE,IAAI,CAAChM,UAAU,CAAC;IAC5C,IAAIoL,YAAY,EAAE;MACd,IAAIhb,KAAK,CAAC6b,OAAO,CAACb,YAAY,CAAC,EAAE;QAC7BA,YAAY,CAAC9b,OAAO,CAAC4c,SAAS,IAAIb,OAAO,CAACU,SAAS,CAAC7H,GAAG,CAACgI,SAAS,CAAC,CAAC;MACvE,CAAC,MACI;QACDb,OAAO,CAACU,SAAS,CAAC7H,GAAG,CAACkH,YAAY,CAAC;MACvC;IACJ;IACA,OAAOC,OAAO;EAClB;EACA;AACJ;AACA;AACA;EACIlE,4BAA4BA,CAAA,EAAG;IAC3B;IACA,IAAI,CAAC,IAAI,CAAC7E,SAAS,EAAE;MACjB,OAAOxV,OAAO,CAACC,OAAO,CAAC,CAAC;IAC5B;IACA,MAAMof,eAAe,GAAG,IAAI,CAACvI,YAAY,CAACrJ,qBAAqB,CAAC,CAAC;IACjE;IACA,IAAI,CAACoM,QAAQ,CAACoF,SAAS,CAAC7H,GAAG,CAAC,oBAAoB,CAAC;IACjD;IACA,IAAI,CAACgH,sBAAsB,CAACiB,eAAe,CAACrT,IAAI,EAAEqT,eAAe,CAACvT,GAAG,CAAC;IACtE;IACA;IACA;IACA;IACA,MAAMwT,QAAQ,GAAG9S,kCAAkC,CAAC,IAAI,CAACqN,QAAQ,CAAC;IAClE,IAAIyF,QAAQ,KAAK,CAAC,EAAE;MAChB,OAAOtf,OAAO,CAACC,OAAO,CAAC,CAAC;IAC5B;IACA,OAAO,IAAI,CAACkS,OAAO,CAACyF,iBAAiB,CAAC,MAAM;MACxC,OAAO,IAAI5X,OAAO,CAACC,OAAO,IAAI;QAC1B,MAAMsf,OAAO,GAAKlQ,KAAK,IAAK;UACxB,IAAI,CAACA,KAAK,IACL1F,sEAAe,CAAC0F,KAAK,CAAC,KAAK,IAAI,CAACwK,QAAQ,IAAIxK,KAAK,CAACmQ,YAAY,KAAK,WAAY,EAAE;YAClF,IAAI,CAAC3F,QAAQ,EAAE4F,mBAAmB,CAAC,eAAe,EAAEF,OAAO,CAAC;YAC5Dtf,OAAO,CAAC,CAAC;YACTyf,YAAY,CAACjY,OAAO,CAAC;UACzB;QACJ,CAAE;QACF;QACA;QACA;QACA,MAAMA,OAAO,GAAG1G,UAAU,CAACwe,OAAO,EAAED,QAAQ,GAAG,GAAG,CAAC;QACnD,IAAI,CAACzF,QAAQ,CAAChC,gBAAgB,CAAC,eAAe,EAAE0H,OAAO,CAAC;MAC5D,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;EACAxE,yBAAyBA,CAAA,EAAG;IACxB,MAAM4E,iBAAiB,GAAG,IAAI,CAAClI,oBAAoB;IACnD,MAAMmI,mBAAmB,GAAGD,iBAAiB,GAAGA,iBAAiB,CAACrI,QAAQ,GAAG,IAAI;IACjF,IAAIwD,WAAW;IACf,IAAI8E,mBAAmB,EAAE;MACrB,IAAI,CAAC5F,eAAe,GAAG2F,iBAAiB,CAACjB,aAAa,CAACC,kBAAkB,CAACiB,mBAAmB,EAAED,iBAAiB,CAAC1O,OAAO,CAAC;MACzH,IAAI,CAAC+I,eAAe,CAAC4E,aAAa,CAAC,CAAC;MACpC9D,WAAW,GAAG+D,WAAW,CAAC,IAAI,CAAC7E,eAAe,EAAE,IAAI,CAACtL,SAAS,CAAC;IACnE,CAAC,MACI;MACDoM,WAAW,GAAG5K,aAAa,CAAC,IAAI,CAAC8D,YAAY,CAAC;IAClD;IACA;IACA;IACA8G,WAAW,CAACnP,KAAK,CAACkU,aAAa,GAAG,MAAM;IACxC/E,WAAW,CAACmE,SAAS,CAAC7H,GAAG,CAAC,sBAAsB,CAAC;IACjD,OAAO0D,WAAW;EACtB;EACA;AACJ;AACA;AACA;AACA;EACIkC,4BAA4BA,CAAC8C,WAAW,EAAEhE,gBAAgB,EAAEzM,KAAK,EAAE;IAC/D,MAAM0Q,aAAa,GAAGjE,gBAAgB,KAAK,IAAI,CAAC9H,YAAY,GAAG,IAAI,GAAG8H,gBAAgB;IACtF,MAAMkE,aAAa,GAAGD,aAAa,GAAGA,aAAa,CAACtS,qBAAqB,CAAC,CAAC,GAAGqS,WAAW;IACzF,MAAMG,KAAK,GAAGvF,YAAY,CAACrL,KAAK,CAAC,GAAGA,KAAK,CAAC6Q,aAAa,CAAC,CAAC,CAAC,GAAG7Q,KAAK;IAClE,MAAML,cAAc,GAAG,IAAI,CAACmR,0BAA0B,CAAC,CAAC;IACxD,MAAMrS,CAAC,GAAGmS,KAAK,CAACG,KAAK,GAAGJ,aAAa,CAAChU,IAAI,GAAGgD,cAAc,CAAChD,IAAI;IAChE,MAAM+B,CAAC,GAAGkS,KAAK,CAACI,KAAK,GAAGL,aAAa,CAAClU,GAAG,GAAGkD,cAAc,CAAClD,GAAG;IAC9D,OAAO;MACHgC,CAAC,EAAEkS,aAAa,CAAChU,IAAI,GAAG8T,WAAW,CAAC9T,IAAI,GAAG8B,CAAC;MAC5CC,CAAC,EAAEiS,aAAa,CAAClU,GAAG,GAAGgU,WAAW,CAAChU,GAAG,GAAGiC;IAC7C,CAAC;EACL;EACA;EACAoG,yBAAyBA,CAAC9E,KAAK,EAAE;IAC7B,MAAML,cAAc,GAAG,IAAI,CAACmR,0BAA0B,CAAC,CAAC;IACxD,MAAMF,KAAK,GAAGvF,YAAY,CAACrL,KAAK,CAAC;IAC3B;IACE;IACA;IACA;IACA;IACA;IACA;IACAA,KAAK,CAACiR,OAAO,CAAC,CAAC,CAAC,IAAIjR,KAAK,CAACkR,cAAc,CAAC,CAAC,CAAC,IAAI;MAAEH,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAE,CAAC,GACvEhR,KAAK;IACX,MAAMvB,CAAC,GAAGmS,KAAK,CAACG,KAAK,GAAGpR,cAAc,CAAChD,IAAI;IAC3C,MAAM+B,CAAC,GAAGkS,KAAK,CAACI,KAAK,GAAGrR,cAAc,CAAClD,GAAG;IAC1C;IACA;IACA,IAAI,IAAI,CAACmM,gBAAgB,EAAE;MACvB,MAAMuI,SAAS,GAAG,IAAI,CAACvI,gBAAgB,CAACwI,YAAY,CAAC,CAAC;MACtD,IAAID,SAAS,EAAE;QACX,MAAME,QAAQ,GAAG,IAAI,CAACzI,gBAAgB,CAAC0I,cAAc,CAAC,CAAC;QACvDD,QAAQ,CAAC5S,CAAC,GAAGA,CAAC;QACd4S,QAAQ,CAAC3S,CAAC,GAAGA,CAAC;QACd,OAAO2S,QAAQ,CAACE,eAAe,CAACJ,SAAS,CAACK,OAAO,CAAC,CAAC,CAAC;MACxD;IACJ;IACA,OAAO;MAAE/S,CAAC;MAAEC;IAAE,CAAC;EACnB;EACA;EACAwH,8BAA8BA,CAAC0K,KAAK,EAAE;IAClC,MAAMa,iBAAiB,GAAG,IAAI,CAACjP,cAAc,GAAG,IAAI,CAACA,cAAc,CAACkP,QAAQ,GAAG,IAAI;IACnF,IAAI;MAAEjT,CAAC;MAAEC;IAAE,CAAC,GAAG,IAAI,CAAC8H,iBAAiB,GAC/B,IAAI,CAACA,iBAAiB,CAACoK,KAAK,EAAE,IAAI,EAAE,IAAI,CAACnK,kBAAkB,EAAE,IAAI,CAACgH,wBAAwB,CAAC,GAC3FmD,KAAK;IACX,IAAI,IAAI,CAACc,QAAQ,KAAK,GAAG,IAAID,iBAAiB,KAAK,GAAG,EAAE;MACpD/S,CAAC,GACG,IAAI,CAACuG,qBAAqB,CAACvG,CAAC,IACvB,IAAI,CAAC8H,iBAAiB,GAAG,IAAI,CAACiH,wBAAwB,CAAC/O,CAAC,GAAG,CAAC,CAAC;IAC1E,CAAC,MACI,IAAI,IAAI,CAACgT,QAAQ,KAAK,GAAG,IAAID,iBAAiB,KAAK,GAAG,EAAE;MACzDhT,CAAC,GACG,IAAI,CAACwG,qBAAqB,CAACxG,CAAC,IACvB,IAAI,CAAC+H,iBAAiB,GAAG,IAAI,CAACiH,wBAAwB,CAAChP,CAAC,GAAG,CAAC,CAAC;IAC1E;IACA,IAAI,IAAI,CAAC8O,aAAa,EAAE;MACpB;MACA;MACA,MAAM;QAAE9O,CAAC,EAAEkT,OAAO;QAAEjT,CAAC,EAAEkT;MAAQ,CAAC,GAAG,CAAC,IAAI,CAACpL,iBAAiB,GACpD,IAAI,CAACiH,wBAAwB,GAC7B;QAAEhP,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC;MACpB,MAAMmT,YAAY,GAAG,IAAI,CAACtE,aAAa;MACvC,MAAM;QAAEhP,KAAK,EAAEuT,YAAY;QAAEtT,MAAM,EAAEuT;MAAc,CAAC,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;MAC7E,MAAMC,IAAI,GAAGJ,YAAY,CAACpV,GAAG,GAAGmV,OAAO;MACvC,MAAMM,IAAI,GAAGL,YAAY,CAACvT,MAAM,IAAIyT,aAAa,GAAGH,OAAO,CAAC;MAC5D,MAAMO,IAAI,GAAGN,YAAY,CAAClV,IAAI,GAAGgV,OAAO;MACxC,MAAMS,IAAI,GAAGP,YAAY,CAACxT,KAAK,IAAIyT,YAAY,GAAGH,OAAO,CAAC;MAC1DlT,CAAC,GAAG4T,OAAO,CAAC5T,CAAC,EAAE0T,IAAI,EAAEC,IAAI,CAAC;MAC1B1T,CAAC,GAAG2T,OAAO,CAAC3T,CAAC,EAAEuT,IAAI,EAAEC,IAAI,CAAC;IAC9B;IACA,OAAO;MAAEzT,CAAC;MAAEC;IAAE,CAAC;EACnB;EACA;EACA2H,4BAA4BA,CAACiM,qBAAqB,EAAE;IAChD,MAAM;MAAE7T,CAAC;MAAEC;IAAE,CAAC,GAAG4T,qBAAqB;IACtC,MAAMvL,KAAK,GAAG,IAAI,CAACC,sBAAsB;IACzC,MAAMuL,uBAAuB,GAAG,IAAI,CAAC3E,qCAAqC;IAC1E;IACA,MAAM4E,OAAO,GAAGphB,IAAI,CAAC4T,GAAG,CAACvG,CAAC,GAAG8T,uBAAuB,CAAC9T,CAAC,CAAC;IACvD,MAAMgU,OAAO,GAAGrhB,IAAI,CAAC4T,GAAG,CAACtG,CAAC,GAAG6T,uBAAuB,CAAC7T,CAAC,CAAC;IACvD;IACA;IACA;IACA;IACA,IAAI8T,OAAO,GAAG,IAAI,CAAC3P,OAAO,CAAC6P,+BAA+B,EAAE;MACxD3L,KAAK,CAACtI,CAAC,GAAGA,CAAC,GAAG8T,uBAAuB,CAAC9T,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MAChD8T,uBAAuB,CAAC9T,CAAC,GAAGA,CAAC;IACjC;IACA,IAAIgU,OAAO,GAAG,IAAI,CAAC5P,OAAO,CAAC6P,+BAA+B,EAAE;MACxD3L,KAAK,CAACrI,CAAC,GAAGA,CAAC,GAAG6T,uBAAuB,CAAC7T,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MAChD6T,uBAAuB,CAAC7T,CAAC,GAAGA,CAAC;IACjC;IACA,OAAOqI,KAAK;EAChB;EACA;EACArE,6BAA6BA,CAAA,EAAG;IAC5B,IAAI,CAAC,IAAI,CAACiC,YAAY,IAAI,CAAC,IAAI,CAAChC,QAAQ,EAAE;MACtC;IACJ;IACA,MAAMgQ,YAAY,GAAG,IAAI,CAAChQ,QAAQ,CAAC7Q,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC8T,UAAU,CAAC,CAAC;IACnE,IAAI+M,YAAY,KAAK,IAAI,CAAChP,0BAA0B,EAAE;MAClD,IAAI,CAACA,0BAA0B,GAAGgP,YAAY;MAC9CzW,4BAA4B,CAAC,IAAI,CAACyI,YAAY,EAAEgO,YAAY,CAAC;IACjE;EACJ;EACA;EACArK,2BAA2BA,CAACnM,OAAO,EAAE;IACjCA,OAAO,CAACiU,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC7L,YAAY,EAAEtC,0BAA0B,CAAC;IACvF9F,OAAO,CAACiU,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAAC7L,YAAY,EAAExC,2BAA2B,CAAC;IACzF5F,OAAO,CAACiU,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAClJ,gBAAgB,EAAEjF,0BAA0B,CAAC;EAC/F;EACA;AACJ;AACA;AACA;AACA;EACI0E,0BAA0BA,CAAClI,CAAC,EAAEC,CAAC,EAAE;IAC7B,MAAM7B,SAAS,GAAG6S,YAAY,CAACjR,CAAC,EAAEC,CAAC,CAAC;IACpC,MAAMkU,MAAM,GAAG,IAAI,CAACjO,YAAY,CAACrI,KAAK;IACtC;IACA;IACA;IACA,IAAI,IAAI,CAACmM,iBAAiB,IAAI,IAAI,EAAE;MAChC,IAAI,CAACA,iBAAiB,GAClBmK,MAAM,CAAC/V,SAAS,IAAI+V,MAAM,CAAC/V,SAAS,IAAI,MAAM,GAAG+V,MAAM,CAAC/V,SAAS,GAAG,EAAE;IAC9E;IACA;IACA;IACA;IACA+V,MAAM,CAAC/V,SAAS,GAAGD,iBAAiB,CAACC,SAAS,EAAE,IAAI,CAAC4L,iBAAiB,CAAC;EAC3E;EACA;AACJ;AACA;AACA;AACA;EACIsG,sBAAsBA,CAACtQ,CAAC,EAAEC,CAAC,EAAE;IACzB;IACA;IACA,MAAM5B,gBAAgB,GAAG,IAAI,CAACoL,gBAAgB,EAAED,QAAQ,GAAGS,SAAS,GAAG,IAAI,CAACD,iBAAiB;IAC7F,MAAM5L,SAAS,GAAG6S,YAAY,CAACjR,CAAC,EAAEC,CAAC,CAAC;IACpC,IAAI,CAAC8L,QAAQ,CAAClO,KAAK,CAACO,SAAS,GAAGD,iBAAiB,CAACC,SAAS,EAAEC,gBAAgB,CAAC;EAClF;EACA;AACJ;AACA;AACA;EACIgK,gBAAgBA,CAAC+L,eAAe,EAAE;IAC9B,MAAMC,cAAc,GAAG,IAAI,CAAC7N,qBAAqB;IACjD,IAAI6N,cAAc,EAAE;MAChB,OAAO;QAAErU,CAAC,EAAEoU,eAAe,CAACpU,CAAC,GAAGqU,cAAc,CAACrU,CAAC;QAAEC,CAAC,EAAEmU,eAAe,CAACnU,CAAC,GAAGoU,cAAc,CAACpU;MAAE,CAAC;IAC/F;IACA,OAAO;MAAED,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;EACzB;EACA;EACAyM,wBAAwBA,CAAA,EAAG;IACvB,IAAI,CAACoC,aAAa,GAAG,IAAI,CAACO,YAAY,GAAGpF,SAAS;IAClD,IAAI,CAACpB,gBAAgB,CAAC1O,KAAK,CAAC,CAAC;EACjC;EACA;AACJ;AACA;AACA;EACIuQ,8BAA8BA,CAAA,EAAG;IAC7B,IAAI;MAAE1K,CAAC;MAAEC;IAAE,CAAC,GAAG,IAAI,CAACuE,iBAAiB;IACrC,IAAKxE,CAAC,KAAK,CAAC,IAAIC,CAAC,KAAK,CAAC,IAAK,IAAI,CAACkH,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAClC,gBAAgB,EAAE;MACrE;IACJ;IACA;IACA,MAAM+M,WAAW,GAAG,IAAI,CAAC9L,YAAY,CAACvG,qBAAqB,CAAC,CAAC;IAC7D,MAAMyT,YAAY,GAAG,IAAI,CAACnO,gBAAgB,CAACtF,qBAAqB,CAAC,CAAC;IAClE;IACA;IACA,IAAKyT,YAAY,CAACtT,KAAK,KAAK,CAAC,IAAIsT,YAAY,CAACrT,MAAM,KAAK,CAAC,IACrDiS,WAAW,CAAClS,KAAK,KAAK,CAAC,IAAIkS,WAAW,CAACjS,MAAM,KAAK,CAAE,EAAE;MACvD;IACJ;IACA,MAAMuU,YAAY,GAAGlB,YAAY,CAAClV,IAAI,GAAG8T,WAAW,CAAC9T,IAAI;IACzD,MAAMqW,aAAa,GAAGvC,WAAW,CAACpS,KAAK,GAAGwT,YAAY,CAACxT,KAAK;IAC5D,MAAM4U,WAAW,GAAGpB,YAAY,CAACpV,GAAG,GAAGgU,WAAW,CAAChU,GAAG;IACtD,MAAMyW,cAAc,GAAGzC,WAAW,CAACnS,MAAM,GAAGuT,YAAY,CAACvT,MAAM;IAC/D;IACA;IACA,IAAIuT,YAAY,CAACtT,KAAK,GAAGkS,WAAW,CAAClS,KAAK,EAAE;MACxC,IAAIwU,YAAY,GAAG,CAAC,EAAE;QAClBtU,CAAC,IAAIsU,YAAY;MACrB;MACA,IAAIC,aAAa,GAAG,CAAC,EAAE;QACnBvU,CAAC,IAAIuU,aAAa;MACtB;IACJ,CAAC,MACI;MACDvU,CAAC,GAAG,CAAC;IACT;IACA;IACA;IACA,IAAIoT,YAAY,CAACrT,MAAM,GAAGiS,WAAW,CAACjS,MAAM,EAAE;MAC1C,IAAIyU,WAAW,GAAG,CAAC,EAAE;QACjBvU,CAAC,IAAIuU,WAAW;MACpB;MACA,IAAIC,cAAc,GAAG,CAAC,EAAE;QACpBxU,CAAC,IAAIwU,cAAc;MACvB;IACJ,CAAC,MACI;MACDxU,CAAC,GAAG,CAAC;IACT;IACA,IAAID,CAAC,KAAK,IAAI,CAACwE,iBAAiB,CAACxE,CAAC,IAAIC,CAAC,KAAK,IAAI,CAACuE,iBAAiB,CAACvE,CAAC,EAAE;MAClE,IAAI,CAAC0L,mBAAmB,CAAC;QAAE1L,CAAC;QAAED;MAAE,CAAC,CAAC;IACtC;EACJ;EACA;EACAgH,kBAAkBA,CAACzF,KAAK,EAAE;IACtB,MAAMrH,KAAK,GAAG,IAAI,CAACmL,cAAc;IACjC,IAAI,OAAOnL,KAAK,KAAK,QAAQ,EAAE;MAC3B,OAAOA,KAAK;IAChB,CAAC,MACI,IAAI0S,YAAY,CAACrL,KAAK,CAAC,EAAE;MAC1B,OAAOrH,KAAK,CAACwa,KAAK;IACtB;IACA,OAAOxa,KAAK,GAAGA,KAAK,CAACya,KAAK,GAAG,CAAC;EAClC;EACA;EACA9F,eAAeA,CAACtN,KAAK,EAAE;IACnB,MAAMqT,gBAAgB,GAAG,IAAI,CAAC/L,gBAAgB,CAACvH,YAAY,CAACC,KAAK,CAAC;IAClE,IAAIqT,gBAAgB,EAAE;MAClB,MAAMpT,MAAM,GAAG3F,sEAAe,CAAC0F,KAAK,CAAC;MACrC;MACA;MACA,IAAI,IAAI,CAACuN,aAAa,IAClBtN,MAAM,KAAK,IAAI,CAACyD,gBAAgB,IAChCzD,MAAM,CAACQ,QAAQ,CAAC,IAAI,CAACiD,gBAAgB,CAAC,EAAE;QACxC9E,gBAAgB,CAAC,IAAI,CAAC2O,aAAa,EAAE8F,gBAAgB,CAAC5W,GAAG,EAAE4W,gBAAgB,CAAC1W,IAAI,CAAC;MACrF;MACA,IAAI,CAACsI,qBAAqB,CAACxG,CAAC,IAAI4U,gBAAgB,CAAC1W,IAAI;MACrD,IAAI,CAACsI,qBAAqB,CAACvG,CAAC,IAAI2U,gBAAgB,CAAC5W,GAAG;MACpD;MACA;MACA,IAAI,CAAC,IAAI,CAAC+F,cAAc,EAAE;QACtB,IAAI,CAACU,gBAAgB,CAACzE,CAAC,IAAI4U,gBAAgB,CAAC1W,IAAI;QAChD,IAAI,CAACuG,gBAAgB,CAACxE,CAAC,IAAI2U,gBAAgB,CAAC5W,GAAG;QAC/C,IAAI,CAACkK,0BAA0B,CAAC,IAAI,CAACzD,gBAAgB,CAACzE,CAAC,EAAE,IAAI,CAACyE,gBAAgB,CAACxE,CAAC,CAAC;MACrF;IACJ;EACJ;EACA;EACAoS,0BAA0BA,CAAA,EAAG;IACzB,OAAQ,IAAI,CAACxJ,gBAAgB,CAAChI,SAAS,CAAChK,GAAG,CAAC,IAAI,CAAC+J,SAAS,CAAC,EAAEM,cAAc,IACvE,IAAI,CAAC2H,gBAAgB,CAAC1H,yBAAyB,CAAC,CAAC;EACzD;EACA;AACJ;AACA;AACA;AACA;AACA;EACIpF,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAAC8Y,iBAAiB,KAAK5K,SAAS,EAAE;MACtC,IAAI,CAAC4K,iBAAiB,GAAG9Y,qEAAc,CAAC,IAAI,CAACmK,YAAY,CAAC;IAC9D;IACA,OAAO,IAAI,CAAC2O,iBAAiB;EACjC;EACA;EACAnH,yBAAyBA,CAACoH,aAAa,EAAE1H,UAAU,EAAE;IACjD,MAAM2H,gBAAgB,GAAG,IAAI,CAAClJ,iBAAiB,IAAI,QAAQ;IAC3D,IAAIkJ,gBAAgB,KAAK,QAAQ,EAAE;MAC/B,OAAOD,aAAa;IACxB;IACA,IAAIC,gBAAgB,KAAK,QAAQ,EAAE;MAC/B,MAAMC,WAAW,GAAG,IAAI,CAACpU,SAAS;MAClC;MACA;MACA;MACA,OAAQwM,UAAU,IACd4H,WAAW,CAACC,iBAAiB,IAC7BD,WAAW,CAACE,uBAAuB,IACnCF,WAAW,CAACG,oBAAoB,IAChCH,WAAW,CAACI,mBAAmB,IAC/BJ,WAAW,CAACzH,IAAI;IACxB;IACA,OAAOtR,oEAAa,CAAC8Y,gBAAgB,CAAC;EAC1C;EACA;EACAxB,eAAeA,CAAA,EAAG;IACd;IACA;IACA,IAAI,CAAC,IAAI,CAAClE,YAAY,IAAK,CAAC,IAAI,CAACA,YAAY,CAACvP,KAAK,IAAI,CAAC,IAAI,CAACuP,YAAY,CAACtP,MAAO,EAAE;MAC/E,IAAI,CAACsP,YAAY,GAAG,IAAI,CAACtD,QAAQ,GAC3B,IAAI,CAACA,QAAQ,CAACpM,qBAAqB,CAAC,CAAC,GACrC,IAAI,CAACqI,kBAAkB;IACjC;IACA,OAAO,IAAI,CAACqH,YAAY;EAC5B;EACA;EACArJ,gBAAgBA,CAACzE,KAAK,EAAE;IACpB,OAAO,IAAI,CAAC2C,QAAQ,CAAClF,IAAI,CAACmF,MAAM,IAAI;MAChC,OAAO5C,KAAK,CAACC,MAAM,KAAKD,KAAK,CAACC,MAAM,KAAK2C,MAAM,IAAIA,MAAM,CAACnC,QAAQ,CAACT,KAAK,CAACC,MAAM,CAAC,CAAC;IACrF,CAAC,CAAC;EACN;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAASyP,YAAYA,CAACjR,CAAC,EAAEC,CAAC,EAAE;EACxB;EACA;EACA,OAAQ,eAActN,IAAI,CAAC0iB,KAAK,CAACrV,CAAC,CAAE,OAAMrN,IAAI,CAAC0iB,KAAK,CAACpV,CAAC,CAAE,QAAO;AACnE;AACA;AACA,SAAS2T,OAAOA,CAAC1Z,KAAK,EAAEob,GAAG,EAAE1iB,GAAG,EAAE;EAC9B,OAAOD,IAAI,CAACC,GAAG,CAAC0iB,GAAG,EAAE3iB,IAAI,CAAC2iB,GAAG,CAAC1iB,GAAG,EAAEsH,KAAK,CAAC,CAAC;AAC9C;AACA;AACA,SAAS0S,YAAYA,CAACrL,KAAK,EAAE;EACzB;EACA;EACA;EACA,OAAOA,KAAK,CAAC1H,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA,SAASkX,WAAWA,CAACJ,OAAO,EAAE/P,SAAS,EAAE;EACrC,MAAM2U,SAAS,GAAG5E,OAAO,CAAC4E,SAAS;EACnC,IAAIA,SAAS,CAACliB,MAAM,KAAK,CAAC,IAAIkiB,SAAS,CAAC,CAAC,CAAC,CAACC,QAAQ,KAAK5U,SAAS,CAAC6U,YAAY,EAAE;IAC5E,OAAOF,SAAS,CAAC,CAAC,CAAC;EACvB;EACA,MAAMG,OAAO,GAAG9U,SAAS,CAAC+U,aAAa,CAAC,KAAK,CAAC;EAC9CJ,SAAS,CAAC7gB,OAAO,CAACqN,IAAI,IAAI2T,OAAO,CAAClI,WAAW,CAACzL,IAAI,CAAC,CAAC;EACpD,OAAO2T,OAAO;AAClB;AACA;AACA;AACA;AACA;AACA;AACA,SAAS1E,gBAAgBA,CAACxP,MAAM,EAAEoU,UAAU,EAAE;EAC1CpU,MAAM,CAAC3D,KAAK,CAACiC,KAAK,GAAI,GAAE8V,UAAU,CAAC9V,KAAM,IAAG;EAC5C0B,MAAM,CAAC3D,KAAK,CAACkC,MAAM,GAAI,GAAE6V,UAAU,CAAC7V,MAAO,IAAG;EAC9CyB,MAAM,CAAC3D,KAAK,CAACO,SAAS,GAAG6S,YAAY,CAAC2E,UAAU,CAAC1X,IAAI,EAAE0X,UAAU,CAAC5X,GAAG,CAAC;AAC1E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS6X,eAAeA,CAACC,KAAK,EAAEC,SAAS,EAAEC,OAAO,EAAE;EAChD,MAAM/kB,IAAI,GAAGglB,KAAK,CAACF,SAAS,EAAED,KAAK,CAACziB,MAAM,GAAG,CAAC,CAAC;EAC/C,MAAM6iB,EAAE,GAAGD,KAAK,CAACD,OAAO,EAAEF,KAAK,CAACziB,MAAM,GAAG,CAAC,CAAC;EAC3C,IAAIpC,IAAI,KAAKilB,EAAE,EAAE;IACb;EACJ;EACA,MAAM1U,MAAM,GAAGsU,KAAK,CAAC7kB,IAAI,CAAC;EAC1B,MAAMqX,KAAK,GAAG4N,EAAE,GAAGjlB,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC;EAChC,KAAK,IAAIyE,CAAC,GAAGzE,IAAI,EAAEyE,CAAC,KAAKwgB,EAAE,EAAExgB,CAAC,IAAI4S,KAAK,EAAE;IACrCwN,KAAK,CAACpgB,CAAC,CAAC,GAAGogB,KAAK,CAACpgB,CAAC,GAAG4S,KAAK,CAAC;EAC/B;EACAwN,KAAK,CAACI,EAAE,CAAC,GAAG1U,MAAM;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2U,iBAAiBA,CAACC,YAAY,EAAEC,WAAW,EAAE/G,YAAY,EAAEgH,WAAW,EAAE;EAC7E,MAAMrlB,IAAI,GAAGglB,KAAK,CAAC3G,YAAY,EAAE8G,YAAY,CAAC/iB,MAAM,GAAG,CAAC,CAAC;EACzD,MAAM6iB,EAAE,GAAGD,KAAK,CAACK,WAAW,EAAED,WAAW,CAAChjB,MAAM,CAAC;EACjD,IAAI+iB,YAAY,CAAC/iB,MAAM,EAAE;IACrBgjB,WAAW,CAACE,MAAM,CAACL,EAAE,EAAE,CAAC,EAAEE,YAAY,CAACG,MAAM,CAACtlB,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9D;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASulB,aAAaA,CAACJ,YAAY,EAAEC,WAAW,EAAE/G,YAAY,EAAEgH,WAAW,EAAE;EACzE,MAAMJ,EAAE,GAAGD,KAAK,CAACK,WAAW,EAAED,WAAW,CAAChjB,MAAM,CAAC;EACjD,IAAI+iB,YAAY,CAAC/iB,MAAM,EAAE;IACrBgjB,WAAW,CAACE,MAAM,CAACL,EAAE,EAAE,CAAC,EAAEE,YAAY,CAAC9G,YAAY,CAAC,CAAC;EACzD;AACJ;AACA;AACA,SAAS2G,KAAKA,CAAC/b,KAAK,EAAEtH,GAAG,EAAE;EACvB,OAAOD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAAC2iB,GAAG,CAAC1iB,GAAG,EAAEsH,KAAK,CAAC,CAAC;AAC5C;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMuc,sBAAsB,CAAC;EACzBllB,WAAWA,CAACmlB,QAAQ,EAAEnS,iBAAiB,EAAE;IACrC,IAAI,CAACmS,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACnS,iBAAiB,GAAGA,iBAAiB;IAC1C;IACA,IAAI,CAACoS,cAAc,GAAG,EAAE;IACxB;IACA,IAAI,CAACC,WAAW,GAAG,UAAU;IAC7B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,aAAa,GAAG;MACjBC,IAAI,EAAE,IAAI;MACVxO,KAAK,EAAE,CAAC;MACRyO,QAAQ,EAAE;IACd,CAAC;EACL;EACA;AACJ;AACA;AACA;EACIpJ,KAAKA,CAACqJ,KAAK,EAAE;IACT,IAAI,CAACC,SAAS,CAACD,KAAK,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIE,IAAIA,CAACzH,IAAI,EAAElP,QAAQ,EAAEC,QAAQ,EAAE2W,YAAY,EAAE;IACzC,MAAMC,QAAQ,GAAG,IAAI,CAACT,cAAc;IACpC,MAAMU,QAAQ,GAAG,IAAI,CAACC,gCAAgC,CAAC7H,IAAI,EAAElP,QAAQ,EAAEC,QAAQ,EAAE2W,YAAY,CAAC;IAC9F,IAAIE,QAAQ,KAAK,CAAC,CAAC,IAAID,QAAQ,CAAC/jB,MAAM,GAAG,CAAC,EAAE;MACxC,OAAO,IAAI;IACf;IACA,MAAMkkB,YAAY,GAAG,IAAI,CAACX,WAAW,KAAK,YAAY;IACtD,MAAMtH,YAAY,GAAG8H,QAAQ,CAACI,SAAS,CAACC,WAAW,IAAIA,WAAW,CAACX,IAAI,KAAKrH,IAAI,CAAC;IACjF,MAAMiI,oBAAoB,GAAGN,QAAQ,CAACC,QAAQ,CAAC;IAC/C,MAAMjD,eAAe,GAAGgD,QAAQ,CAAC9H,YAAY,CAAC,CAAC5P,UAAU;IACzD,MAAMiY,WAAW,GAAGD,oBAAoB,CAAChY,UAAU;IACnD,MAAM4I,KAAK,GAAGgH,YAAY,GAAG+H,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;IAC9C;IACA,MAAMO,UAAU,GAAG,IAAI,CAACC,gBAAgB,CAACzD,eAAe,EAAEuD,WAAW,EAAErP,KAAK,CAAC;IAC7E;IACA,MAAMwP,aAAa,GAAG,IAAI,CAACC,mBAAmB,CAACzI,YAAY,EAAE8H,QAAQ,EAAE9O,KAAK,CAAC;IAC7E;IACA;IACA,MAAM0P,QAAQ,GAAGZ,QAAQ,CAAC9hB,KAAK,CAAC,CAAC;IACjC;IACAugB,eAAe,CAACuB,QAAQ,EAAE9H,YAAY,EAAE+H,QAAQ,CAAC;IACjDD,QAAQ,CAAC1iB,OAAO,CAAC,CAACujB,OAAO,EAAE1kB,KAAK,KAAK;MACjC;MACA,IAAIykB,QAAQ,CAACzkB,KAAK,CAAC,KAAK0kB,OAAO,EAAE;QAC7B;MACJ;MACA,MAAMC,aAAa,GAAGD,OAAO,CAACnB,IAAI,KAAKrH,IAAI;MAC3C,MAAM3H,MAAM,GAAGoQ,aAAa,GAAGN,UAAU,GAAGE,aAAa;MACzD,MAAMK,eAAe,GAAGD,aAAa,GAC/BzI,IAAI,CAAC1G,qBAAqB,CAAC,CAAC,GAC5BkP,OAAO,CAACnB,IAAI,CAAC7N,cAAc,CAAC,CAAC;MACnC;MACAgP,OAAO,CAACnQ,MAAM,IAAIA,MAAM;MACxB;MACA;MACA;MACA;MACA,IAAIyP,YAAY,EAAE;QACd;QACA;QACAY,eAAe,CAACta,KAAK,CAACO,SAAS,GAAGD,iBAAiB,CAAE,eAAcxL,IAAI,CAAC0iB,KAAK,CAAC4C,OAAO,CAACnQ,MAAM,CAAE,WAAU,EAAEmQ,OAAO,CAAC5Z,gBAAgB,CAAC;QACnI8B,gBAAgB,CAAC8X,OAAO,CAACvY,UAAU,EAAE,CAAC,EAAEoI,MAAM,CAAC;MACnD,CAAC,MACI;QACDqQ,eAAe,CAACta,KAAK,CAACO,SAAS,GAAGD,iBAAiB,CAAE,kBAAiBxL,IAAI,CAAC0iB,KAAK,CAAC4C,OAAO,CAACnQ,MAAM,CAAE,QAAO,EAAEmQ,OAAO,CAAC5Z,gBAAgB,CAAC;QACnI8B,gBAAgB,CAAC8X,OAAO,CAACvY,UAAU,EAAEoI,MAAM,EAAE,CAAC,CAAC;MACnD;IACJ,CAAC,CAAC;IACF;IACA,IAAI,CAAC+O,aAAa,CAACE,QAAQ,GAAG7W,kBAAkB,CAACyX,WAAW,EAAEpX,QAAQ,EAAEC,QAAQ,CAAC;IACjF,IAAI,CAACqW,aAAa,CAACC,IAAI,GAAGY,oBAAoB,CAACZ,IAAI;IACnD,IAAI,CAACD,aAAa,CAACvO,KAAK,GAAGiP,YAAY,GAAGJ,YAAY,CAACnX,CAAC,GAAGmX,YAAY,CAAClX,CAAC;IACzE,OAAO;MAAEyP,aAAa,EAAEJ,YAAY;MAAEA,YAAY,EAAE+H;IAAS,CAAC;EAClE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACInH,KAAKA,CAACT,IAAI,EAAElP,QAAQ,EAAEC,QAAQ,EAAEjN,KAAK,EAAE;IACnC,MAAM8jB,QAAQ,GAAG9jB,KAAK,IAAI,IAAI,IAAIA,KAAK,GAAG,CAAC;IACrC;IACE;IACA,IAAI,CAAC+jB,gCAAgC,CAAC7H,IAAI,EAAElP,QAAQ,EAAEC,QAAQ,CAAC,GACjEjN,KAAK;IACX,MAAM6kB,gBAAgB,GAAG,IAAI,CAACC,iBAAiB;IAC/C,MAAM/I,YAAY,GAAG8I,gBAAgB,CAAC5Z,OAAO,CAACiR,IAAI,CAAC;IACnD,MAAMzC,WAAW,GAAGyC,IAAI,CAAC1G,qBAAqB,CAAC,CAAC;IAChD,IAAIuP,oBAAoB,GAAGF,gBAAgB,CAACf,QAAQ,CAAC;IACrD;IACA;IACA;IACA,IAAIiB,oBAAoB,KAAK7I,IAAI,EAAE;MAC/B6I,oBAAoB,GAAGF,gBAAgB,CAACf,QAAQ,GAAG,CAAC,CAAC;IACzD;IACA;IACA;IACA,IAAI,CAACiB,oBAAoB,KACpBjB,QAAQ,IAAI,IAAI,IAAIA,QAAQ,KAAK,CAAC,CAAC,IAAIA,QAAQ,GAAGe,gBAAgB,CAAC/kB,MAAM,GAAG,CAAC,CAAC,IAC/E,IAAI,CAACklB,wBAAwB,CAAChY,QAAQ,EAAEC,QAAQ,CAAC,EAAE;MACnD8X,oBAAoB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;IAC9C;IACA;IACA;IACA,IAAI9I,YAAY,GAAG,CAAC,CAAC,EAAE;MACnB8I,gBAAgB,CAAC7B,MAAM,CAACjH,YAAY,EAAE,CAAC,CAAC;IAC5C;IACA;IACA;IACA,IAAIgJ,oBAAoB,IAAI,CAAC,IAAI,CAAC/T,iBAAiB,CAAC4C,UAAU,CAACmR,oBAAoB,CAAC,EAAE;MAClF,MAAM5a,OAAO,GAAG4a,oBAAoB,CAACrP,cAAc,CAAC,CAAC;MACrDvL,OAAO,CAAC8a,aAAa,CAACnL,YAAY,CAACL,WAAW,EAAEtP,OAAO,CAAC;MACxD0a,gBAAgB,CAAC7B,MAAM,CAACc,QAAQ,EAAE,CAAC,EAAE5H,IAAI,CAAC;IAC9C,CAAC,MACI;MACDxT,oEAAa,CAAC,IAAI,CAACya,QAAQ,CAAC,CAAClJ,WAAW,CAACR,WAAW,CAAC;MACrDoL,gBAAgB,CAACzjB,IAAI,CAAC8a,IAAI,CAAC;IAC/B;IACA;IACAzC,WAAW,CAACnP,KAAK,CAACO,SAAS,GAAG,EAAE;IAChC;IACA;IACA;IACA,IAAI,CAACqa,mBAAmB,CAAC,CAAC;EAC9B;EACA;EACAxB,SAASA,CAACD,KAAK,EAAE;IACb,IAAI,CAACqB,iBAAiB,GAAGrB,KAAK,CAAC1hB,KAAK,CAAC,CAAC;IACtC,IAAI,CAACmjB,mBAAmB,CAAC,CAAC;EAC9B;EACA;EACAC,iBAAiBA,CAACC,SAAS,EAAE;IACzB,IAAI,CAACC,cAAc,GAAGD,SAAS;EACnC;EACA;EACAvN,KAAKA,CAAA,EAAG;IACJ;IACA,IAAI,CAACiN,iBAAiB,CAAC3jB,OAAO,CAAC+a,IAAI,IAAI;MACnC,MAAM7F,WAAW,GAAG6F,IAAI,CAACxG,cAAc,CAAC,CAAC;MACzC,IAAIW,WAAW,EAAE;QACb,MAAMvL,gBAAgB,GAAG,IAAI,CAACsY,cAAc,CAAC3X,IAAI,CAAC6Z,CAAC,IAAIA,CAAC,CAAC/B,IAAI,KAAKrH,IAAI,CAAC,EAAEpR,gBAAgB;QACzFuL,WAAW,CAAC/L,KAAK,CAACO,SAAS,GAAGC,gBAAgB,IAAI,EAAE;MACxD;IACJ,CAAC,CAAC;IACF,IAAI,CAACsY,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC0B,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACxB,aAAa,CAACC,IAAI,GAAG,IAAI;IAC9B,IAAI,CAACD,aAAa,CAACvO,KAAK,GAAG,CAAC;IAC5B,IAAI,CAACuO,aAAa,CAACE,QAAQ,GAAG,KAAK;EACvC;EACA;AACJ;AACA;AACA;EACI+B,sBAAsBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACT,iBAAiB;EACjC;EACA;EACAvK,YAAYA,CAAC2B,IAAI,EAAE;IACf;IACA;IACA;IACA,MAAMuH,KAAK,GAAG,IAAI,CAACJ,WAAW,KAAK,YAAY,IAAI,IAAI,CAACpL,SAAS,KAAK,KAAK,GACrE,IAAI,CAACmL,cAAc,CAACrhB,KAAK,CAAC,CAAC,CAACyjB,OAAO,CAAC,CAAC,GACrC,IAAI,CAACpC,cAAc;IACzB,OAAOK,KAAK,CAACQ,SAAS,CAACC,WAAW,IAAIA,WAAW,CAACX,IAAI,KAAKrH,IAAI,CAAC;EACpE;EACA;EACAuJ,cAAcA,CAACnX,aAAa,EAAEC,cAAc,EAAE;IAC1C;IACA;IACA;IACA;IACA,IAAI,CAAC6U,cAAc,CAACjiB,OAAO,CAAC,CAAC;MAAEgL;IAAW,CAAC,KAAK;MAC5CS,gBAAgB,CAACT,UAAU,EAAEmC,aAAa,EAAEC,cAAc,CAAC;IAC/D,CAAC,CAAC;IACF;IACA;IACA,IAAI,CAAC6U,cAAc,CAACjiB,OAAO,CAAC,CAAC;MAAEoiB;IAAK,CAAC,KAAK;MACtC,IAAI,IAAI,CAACvS,iBAAiB,CAAC4C,UAAU,CAAC2P,IAAI,CAAC,EAAE;QACzC;QACA;QACAA,IAAI,CAAChL,4BAA4B,CAAC,CAAC;MACvC;IACJ,CAAC,CAAC;EACN;EACA;EACA2M,mBAAmBA,CAAA,EAAG;IAClB,MAAMlB,YAAY,GAAG,IAAI,CAACX,WAAW,KAAK,YAAY;IACtD,IAAI,CAACD,cAAc,GAAG,IAAI,CAAC0B,iBAAiB,CACvClnB,GAAG,CAAC2lB,IAAI,IAAI;MACb,MAAMmC,gBAAgB,GAAGnC,IAAI,CAAC5N,iBAAiB,CAAC,CAAC;MACjD,OAAO;QACH4N,IAAI;QACJhP,MAAM,EAAE,CAAC;QACTzJ,gBAAgB,EAAE4a,gBAAgB,CAACpb,KAAK,CAACO,SAAS,IAAI,EAAE;QACxDsB,UAAU,EAAED,oBAAoB,CAACwZ,gBAAgB;MACrD,CAAC;IACL,CAAC,CAAC,CACG/B,IAAI,CAAC,CAACgC,CAAC,EAAEC,CAAC,KAAK;MAChB,OAAO5B,YAAY,GACb2B,CAAC,CAACxZ,UAAU,CAACxB,IAAI,GAAGib,CAAC,CAACzZ,UAAU,CAACxB,IAAI,GACrCgb,CAAC,CAACxZ,UAAU,CAAC1B,GAAG,GAAGmb,CAAC,CAACzZ,UAAU,CAAC1B,GAAG;IAC7C,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACI6Z,gBAAgBA,CAACzD,eAAe,EAAEuD,WAAW,EAAErP,KAAK,EAAE;IAClD,MAAMiP,YAAY,GAAG,IAAI,CAACX,WAAW,KAAK,YAAY;IACtD,IAAIgB,UAAU,GAAGL,YAAY,GACvBI,WAAW,CAACzZ,IAAI,GAAGkW,eAAe,CAAClW,IAAI,GACvCyZ,WAAW,CAAC3Z,GAAG,GAAGoW,eAAe,CAACpW,GAAG;IAC3C;IACA,IAAIsK,KAAK,KAAK,CAAC,CAAC,EAAE;MACdsP,UAAU,IAAIL,YAAY,GACpBI,WAAW,CAAC7X,KAAK,GAAGsU,eAAe,CAACtU,KAAK,GACzC6X,WAAW,CAAC5X,MAAM,GAAGqU,eAAe,CAACrU,MAAM;IACrD;IACA,OAAO6X,UAAU;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIG,mBAAmBA,CAACzI,YAAY,EAAE8H,QAAQ,EAAE9O,KAAK,EAAE;IAC/C,MAAMiP,YAAY,GAAG,IAAI,CAACX,WAAW,KAAK,YAAY;IACtD,MAAMxC,eAAe,GAAGgD,QAAQ,CAAC9H,YAAY,CAAC,CAAC5P,UAAU;IACzD,MAAM0Z,gBAAgB,GAAGhC,QAAQ,CAAC9H,YAAY,GAAGhH,KAAK,GAAG,CAAC,CAAC,CAAC;IAC5D,IAAIwP,aAAa,GAAG1D,eAAe,CAACmD,YAAY,GAAG,OAAO,GAAG,QAAQ,CAAC,GAAGjP,KAAK;IAC9E,IAAI8Q,gBAAgB,EAAE;MAClB,MAAMzL,KAAK,GAAG4J,YAAY,GAAG,MAAM,GAAG,KAAK;MAC3C,MAAM8B,GAAG,GAAG9B,YAAY,GAAG,OAAO,GAAG,QAAQ;MAC7C;MACA;MACA;MACA;MACA,IAAIjP,KAAK,KAAK,CAAC,CAAC,EAAE;QACdwP,aAAa,IAAIsB,gBAAgB,CAAC1Z,UAAU,CAACiO,KAAK,CAAC,GAAGyG,eAAe,CAACiF,GAAG,CAAC;MAC9E,CAAC,MACI;QACDvB,aAAa,IAAI1D,eAAe,CAACzG,KAAK,CAAC,GAAGyL,gBAAgB,CAAC1Z,UAAU,CAAC2Z,GAAG,CAAC;MAC9E;IACJ;IACA,OAAOvB,aAAa;EACxB;EACA;AACJ;AACA;AACA;AACA;EACIS,wBAAwBA,CAAChY,QAAQ,EAAEC,QAAQ,EAAE;IACzC,IAAI,CAAC,IAAI,CAAC6X,iBAAiB,CAAChlB,MAAM,EAAE;MAChC,OAAO,KAAK;IAChB;IACA,MAAMimB,aAAa,GAAG,IAAI,CAAC3C,cAAc;IACzC,MAAMY,YAAY,GAAG,IAAI,CAACX,WAAW,KAAK,YAAY;IACtD;IACA;IACA,MAAM2C,QAAQ,GAAGD,aAAa,CAAC,CAAC,CAAC,CAACxC,IAAI,KAAK,IAAI,CAACuB,iBAAiB,CAAC,CAAC,CAAC;IACpE,IAAIkB,QAAQ,EAAE;MACV,MAAMC,YAAY,GAAGF,aAAa,CAACA,aAAa,CAACjmB,MAAM,GAAG,CAAC,CAAC,CAACqM,UAAU;MACvE,OAAO6X,YAAY,GAAGhX,QAAQ,IAAIiZ,YAAY,CAAC5Z,KAAK,GAAGY,QAAQ,IAAIgZ,YAAY,CAAC3Z,MAAM;IAC1F,CAAC,MACI;MACD,MAAM4Z,aAAa,GAAGH,aAAa,CAAC,CAAC,CAAC,CAAC5Z,UAAU;MACjD,OAAO6X,YAAY,GAAGhX,QAAQ,IAAIkZ,aAAa,CAACvb,IAAI,GAAGsC,QAAQ,IAAIiZ,aAAa,CAACzb,GAAG;IACxF;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIsZ,gCAAgCA,CAAC7H,IAAI,EAAElP,QAAQ,EAAEC,QAAQ,EAAE8H,KAAK,EAAE;IAC9D,MAAMiP,YAAY,GAAG,IAAI,CAACX,WAAW,KAAK,YAAY;IACtD,MAAMrjB,KAAK,GAAG,IAAI,CAACojB,cAAc,CAACa,SAAS,CAAC,CAAC;MAAEV,IAAI;MAAEpX;IAAW,CAAC,KAAK;MAClE;MACA,IAAIoX,IAAI,KAAKrH,IAAI,EAAE;QACf,OAAO,KAAK;MAChB;MACA,IAAInH,KAAK,EAAE;QACP,MAAMkD,SAAS,GAAG+L,YAAY,GAAGjP,KAAK,CAACtI,CAAC,GAAGsI,KAAK,CAACrI,CAAC;QAClD;QACA;QACA;QACA,IAAI6W,IAAI,KAAK,IAAI,CAACD,aAAa,CAACC,IAAI,IAChC,IAAI,CAACD,aAAa,CAACE,QAAQ,IAC3BvL,SAAS,KAAK,IAAI,CAACqL,aAAa,CAACvO,KAAK,EAAE;UACxC,OAAO,KAAK;QAChB;MACJ;MACA,OAAOiP,YAAY;MACb;MACE;MACAhX,QAAQ,IAAI5N,IAAI,CAACsD,KAAK,CAACyJ,UAAU,CAACxB,IAAI,CAAC,IAAIqC,QAAQ,GAAG5N,IAAI,CAACsD,KAAK,CAACyJ,UAAU,CAACE,KAAK,CAAC,GACpFY,QAAQ,IAAI7N,IAAI,CAACsD,KAAK,CAACyJ,UAAU,CAAC1B,GAAG,CAAC,IAAIwC,QAAQ,GAAG7N,IAAI,CAACsD,KAAK,CAACyJ,UAAU,CAACG,MAAM,CAAC;IAC5F,CAAC,CAAC;IACF,OAAOtM,KAAK,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAACqlB,cAAc,CAACrlB,KAAK,EAAEkc,IAAI,CAAC,GAAG,CAAC,CAAC,GAAGlc,KAAK;EACzE;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAMmmB,wBAAwB,GAAG,IAAI;AACrC;AACA;AACA;AACA;AACA,MAAMC,0BAA0B,GAAG,IAAI;AACvC;AACA;AACA;AACA,MAAMC,WAAW,CAAC;EACdroB,WAAWA,CAACmM,OAAO,EAAE6G,iBAAiB,EAAE3D,SAAS,EAAEyD,OAAO,EAAEC,cAAc,EAAE;IACxE,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC;IACA,IAAI,CAACT,QAAQ,GAAG,KAAK;IACrB;IACA,IAAI,CAACsM,eAAe,GAAG,KAAK;IAC5B;AACR;AACA;AACA;IACQ,IAAI,CAAC0J,kBAAkB,GAAG,KAAK;IAC/B;IACA,IAAI,CAACC,cAAc,GAAG,CAAC;IACvB;AACR;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,MAAM,IAAI;IAChC;IACA,IAAI,CAACC,aAAa,GAAG,MAAM,IAAI;IAC/B;IACA,IAAI,CAAC1U,aAAa,GAAG,IAAIhJ,yCAAO,CAAC,CAAC;IAClC;AACR;AACA;IACQ,IAAI,CAACoJ,OAAO,GAAG,IAAIpJ,yCAAO,CAAC,CAAC;IAC5B;AACR;AACA;AACA;IACQ,IAAI,CAACqJ,MAAM,GAAG,IAAIrJ,yCAAO,CAAC,CAAC;IAC3B;IACA,IAAI,CAACsJ,OAAO,GAAG,IAAItJ,yCAAO,CAAC,CAAC;IAC5B;IACA,IAAI,CAAC2d,MAAM,GAAG,IAAI3d,yCAAO,CAAC,CAAC;IAC3B;IACA,IAAI,CAAC4d,gBAAgB,GAAG,IAAI5d,yCAAO,CAAC,CAAC;IACrC;IACA,IAAI,CAAC6d,gBAAgB,GAAG,IAAI7d,yCAAO,CAAC,CAAC;IACrC;IACA,IAAI,CAAC8d,WAAW,GAAG,KAAK;IACxB;IACA,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB;IACA,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB;IACA,IAAI,CAACC,eAAe,GAAG,IAAI5W,GAAG,CAAC,CAAC;IAChC;IACA,IAAI,CAAC6W,2BAA2B,GAAGje,8CAAY,CAACsI,KAAK;IACrD;IACA,IAAI,CAAC4V,wBAAwB,GAAG,CAAC,CAAC;IAClC;IACA,IAAI,CAACC,0BAA0B,GAAG,CAAC,CAAC;IACpC;IACA,IAAI,CAACC,iBAAiB,GAAG,IAAIre,yCAAO,CAAC,CAAC;IACtC;IACA,IAAI,CAACuY,iBAAiB,GAAG,IAAI;IAC7B;IACA,IAAI,CAAC+F,oBAAoB,GAAG,MAAM;MAC9B,IAAI,CAACtO,cAAc,CAAC,CAAC;MACrB9P,8CAAQ,CAAC,CAAC,EAAEC,yDAAuB,CAAC,CAC/BzI,IAAI,CAAC2I,yDAAS,CAAC,IAAI,CAACge,iBAAiB,CAAC,CAAC,CACvClQ,SAAS,CAAC,MAAM;QACjB,MAAM1I,IAAI,GAAG,IAAI,CAAC8Y,WAAW;QAC7B,MAAMC,UAAU,GAAG,IAAI,CAAChB,cAAc;QACtC,IAAI,IAAI,CAACW,wBAAwB,KAAK,CAAC,CAAC,sCAAsC;UAC1E1Y,IAAI,CAACgZ,QAAQ,CAAC,CAAC,EAAE,CAACD,UAAU,CAAC;QACjC,CAAC,MACI,IAAI,IAAI,CAACL,wBAAwB,KAAK,CAAC,CAAC,wCAAwC;UACjF1Y,IAAI,CAACgZ,QAAQ,CAAC,CAAC,EAAED,UAAU,CAAC;QAChC;QACA,IAAI,IAAI,CAACJ,0BAA0B,KAAK,CAAC,CAAC,0CAA0C;UAChF3Y,IAAI,CAACgZ,QAAQ,CAAC,CAACD,UAAU,EAAE,CAAC,CAAC;QACjC,CAAC,MACI,IAAI,IAAI,CAACJ,0BAA0B,KAAK,CAAC,CAAC,2CAA2C;UACtF3Y,IAAI,CAACgZ,QAAQ,CAACD,UAAU,EAAE,CAAC,CAAC;QAChC;MACJ,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACpd,OAAO,GAAGzB,oEAAa,CAACyB,OAAO,CAAC;IACrC,IAAI,CAACkD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACoa,qBAAqB,CAAC,CAAC,IAAI,CAACtd,OAAO,CAAC,CAAC;IAC1C6G,iBAAiB,CAAC0W,qBAAqB,CAAC,IAAI,CAAC;IAC7C,IAAI,CAACpS,gBAAgB,GAAG,IAAIlI,qBAAqB,CAACC,SAAS,CAAC;IAC5D,IAAI,CAACsa,aAAa,GAAG,IAAIzE,sBAAsB,CAAC,IAAI,CAAC/Y,OAAO,EAAE6G,iBAAiB,CAAC;IAChF,IAAI,CAAC2W,aAAa,CAACxC,iBAAiB,CAAC,CAACnlB,KAAK,EAAEkc,IAAI,KAAK,IAAI,CAACuK,aAAa,CAACzmB,KAAK,EAAEkc,IAAI,EAAE,IAAI,CAAC,CAAC;EAChG;EACA;EACA5E,OAAOA,CAAA,EAAG;IACN,IAAI,CAACyB,cAAc,CAAC,CAAC;IACrB,IAAI,CAACqO,iBAAiB,CAACxnB,QAAQ,CAAC,CAAC;IACjC,IAAI,CAACqnB,2BAA2B,CAACjQ,WAAW,CAAC,CAAC;IAC9C,IAAI,CAACjF,aAAa,CAACnS,QAAQ,CAAC,CAAC;IAC7B,IAAI,CAACuS,OAAO,CAACvS,QAAQ,CAAC,CAAC;IACvB,IAAI,CAACwS,MAAM,CAACxS,QAAQ,CAAC,CAAC;IACtB,IAAI,CAACyS,OAAO,CAACzS,QAAQ,CAAC,CAAC;IACvB,IAAI,CAAC8mB,MAAM,CAAC9mB,QAAQ,CAAC,CAAC;IACtB,IAAI,CAAC+mB,gBAAgB,CAAC/mB,QAAQ,CAAC,CAAC;IAChC,IAAI,CAACgnB,gBAAgB,CAAChnB,QAAQ,CAAC,CAAC;IAChC,IAAI,CAAConB,eAAe,CAACpgB,KAAK,CAAC,CAAC;IAC5B,IAAI,CAAC0gB,WAAW,GAAG,IAAI;IACvB,IAAI,CAAChS,gBAAgB,CAAC1O,KAAK,CAAC,CAAC;IAC7B,IAAI,CAACoK,iBAAiB,CAAC4W,mBAAmB,CAAC,IAAI,CAAC;EACpD;EACA;EACAhU,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACiT,WAAW;EAC3B;EACA;EACAzM,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACyN,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACC,wBAAwB,CAAC,CAAC;EACnC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACInL,KAAKA,CAACT,IAAI,EAAElP,QAAQ,EAAEC,QAAQ,EAAEjN,KAAK,EAAE;IACnC,IAAI,CAAC6nB,gBAAgB,CAAC,CAAC;IACvB;IACA;IACA,IAAI7nB,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC4c,eAAe,EAAE;MACvC5c,KAAK,GAAG,IAAI,CAAC8mB,WAAW,CAAC7b,OAAO,CAACiR,IAAI,CAAC;IAC1C;IACA,IAAI,CAACyL,aAAa,CAAChL,KAAK,CAACT,IAAI,EAAElP,QAAQ,EAAEC,QAAQ,EAAEjN,KAAK,CAAC;IACzD;IACA;IACA,IAAI,CAAC+nB,qBAAqB,CAAC,CAAC;IAC5B;IACA,IAAI,CAACD,wBAAwB,CAAC,CAAC;IAC/B,IAAI,CAAC3V,OAAO,CAACxS,IAAI,CAAC;MAAEuc,IAAI;MAAExI,SAAS,EAAE,IAAI;MAAEqI,YAAY,EAAE,IAAI,CAACxB,YAAY,CAAC2B,IAAI;IAAE,CAAC,CAAC;EACvF;EACA;AACJ;AACA;AACA;EACIQ,IAAIA,CAACR,IAAI,EAAE;IACP,IAAI,CAAC8L,MAAM,CAAC,CAAC;IACb,IAAI,CAAC5V,MAAM,CAACzS,IAAI,CAAC;MAAEuc,IAAI;MAAExI,SAAS,EAAE;IAAK,CAAC,CAAC;EAC/C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI2I,IAAIA,CAACH,IAAI,EAAEH,YAAY,EAAEI,aAAa,EAAEC,iBAAiB,EAAEJ,sBAAsB,EAAEnH,QAAQ,EAAEuE,SAAS,EAAEpL,KAAK,GAAG,CAAC,CAAC,EAAE;IAChH,IAAI,CAACga,MAAM,CAAC,CAAC;IACb,IAAI,CAAC3V,OAAO,CAAC1S,IAAI,CAAC;MACduc,IAAI;MACJH,YAAY;MACZI,aAAa;MACbzI,SAAS,EAAE,IAAI;MACf0I,iBAAiB;MACjBJ,sBAAsB;MACtBnH,QAAQ;MACRuE,SAAS;MACTpL;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACI0V,SAASA,CAACD,KAAK,EAAE;IACb,MAAMwE,aAAa,GAAG,IAAI,CAACnB,WAAW;IACtC,IAAI,CAACA,WAAW,GAAGrD,KAAK;IACxBA,KAAK,CAACtiB,OAAO,CAAC+a,IAAI,IAAIA,IAAI,CAAChE,kBAAkB,CAAC,IAAI,CAAC,CAAC;IACpD,IAAI,IAAI,CAACtE,UAAU,CAAC,CAAC,EAAE;MACnB,MAAMsU,YAAY,GAAGD,aAAa,CAACE,MAAM,CAACjM,IAAI,IAAIA,IAAI,CAACtI,UAAU,CAAC,CAAC,CAAC;MACpE;MACA;MACA,IAAIsU,YAAY,CAACE,KAAK,CAAClM,IAAI,IAAIuH,KAAK,CAACxY,OAAO,CAACiR,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;QACxD,IAAI,CAAC8L,MAAM,CAAC,CAAC;MACjB,CAAC,MACI;QACD,IAAI,CAACL,aAAa,CAACjE,SAAS,CAAC,IAAI,CAACoD,WAAW,CAAC;MAClD;IACJ;IACA,OAAO,IAAI;EACf;EACA;EACA9O,aAAaA,CAACC,SAAS,EAAE;IACrB,IAAI,CAAC0P,aAAa,CAAC1P,SAAS,GAAGA,SAAS;IACxC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIoQ,WAAWA,CAACA,WAAW,EAAE;IACrB,IAAI,CAACtB,SAAS,GAAGsB,WAAW,CAACtmB,KAAK,CAAC,CAAC;IACpC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIumB,eAAeA,CAACjF,WAAW,EAAE;IACzB;IACA;IACA,IAAI,CAACsE,aAAa,CAACtE,WAAW,GAAGA,WAAW;IAC5C,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIoE,qBAAqBA,CAACha,QAAQ,EAAE;IAC5B,MAAMtD,OAAO,GAAGzB,oEAAa,CAAC,IAAI,CAACyB,OAAO,CAAC;IAC3C;IACA;IACA,IAAI,CAACoe,mBAAmB,GACpB9a,QAAQ,CAACxC,OAAO,CAACd,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAACA,OAAO,EAAE,GAAGsD,QAAQ,CAAC,GAAGA,QAAQ,CAAC1L,KAAK,CAAC,CAAC;IAChF,OAAO,IAAI;EACf;EACA;EACAyY,oBAAoBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAAC+N,mBAAmB;EACnC;EACA;AACJ;AACA;AACA;EACIhO,YAAYA,CAAC2B,IAAI,EAAE;IACf,OAAO,IAAI,CAAC2K,WAAW,GACjB,IAAI,CAACc,aAAa,CAACpN,YAAY,CAAC2B,IAAI,CAAC,GACrC,IAAI,CAAC4K,WAAW,CAAC7b,OAAO,CAACiR,IAAI,CAAC;EACxC;EACA;AACJ;AACA;AACA;EACIrI,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACmT,eAAe,CAACwB,IAAI,GAAG,CAAC;EACxC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI1L,SAASA,CAACZ,IAAI,EAAElP,QAAQ,EAAEC,QAAQ,EAAE2W,YAAY,EAAE;IAC9C;IACA,IAAI,IAAI,CAAChH,eAAe,IACpB,CAAC,IAAI,CAAC6L,WAAW,IACjB,CAAC5b,uBAAuB,CAAC,IAAI,CAAC4b,WAAW,EAAEtC,wBAAwB,EAAEnZ,QAAQ,EAAEC,QAAQ,CAAC,EAAE;MAC1F;IACJ;IACA,MAAMvM,MAAM,GAAG,IAAI,CAACinB,aAAa,CAAChE,IAAI,CAACzH,IAAI,EAAElP,QAAQ,EAAEC,QAAQ,EAAE2W,YAAY,CAAC;IAC9E,IAAIljB,MAAM,EAAE;MACR,IAAI,CAACgmB,MAAM,CAAC/mB,IAAI,CAAC;QACbwc,aAAa,EAAEzb,MAAM,CAACyb,aAAa;QACnCJ,YAAY,EAAErb,MAAM,CAACqb,YAAY;QACjCrI,SAAS,EAAE,IAAI;QACfwI;MACJ,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIW,0BAA0BA,CAAC7P,QAAQ,EAAEC,QAAQ,EAAE;IAC3C,IAAI,IAAI,CAACqZ,kBAAkB,EAAE;MACzB;IACJ;IACA,IAAIoC,UAAU;IACd,IAAIC,uBAAuB,GAAG,CAAC,CAAC;IAChC,IAAIC,yBAAyB,GAAG,CAAC,CAAC;IAClC;IACA,IAAI,CAACtT,gBAAgB,CAAChI,SAAS,CAACnM,OAAO,CAAC,CAACqJ,QAAQ,EAAEL,OAAO,KAAK;MAC3D;MACA;MACA,IAAIA,OAAO,KAAK,IAAI,CAACkD,SAAS,IAAI,CAAC7C,QAAQ,CAAC2B,UAAU,IAAIuc,UAAU,EAAE;QAClE;MACJ;MACA,IAAI7b,uBAAuB,CAACrC,QAAQ,CAAC2B,UAAU,EAAEga,wBAAwB,EAAEnZ,QAAQ,EAAEC,QAAQ,CAAC,EAAE;QAC5F,CAAC0b,uBAAuB,EAAEC,yBAAyB,CAAC,GAAGC,0BAA0B,CAAC1e,OAAO,EAAEK,QAAQ,CAAC2B,UAAU,EAAEa,QAAQ,EAAEC,QAAQ,CAAC;QACnI,IAAI0b,uBAAuB,IAAIC,yBAAyB,EAAE;UACtDF,UAAU,GAAGve,OAAO;QACxB;MACJ;IACJ,CAAC,CAAC;IACF;IACA,IAAI,CAACwe,uBAAuB,IAAI,CAACC,yBAAyB,EAAE;MACxD,MAAM;QAAErc,KAAK;QAAEC;MAAO,CAAC,GAAG,IAAI,CAACuE,cAAc,CAAC+X,eAAe,CAAC,CAAC;MAC/D,MAAM3c,UAAU,GAAG;QACfI,KAAK;QACLC,MAAM;QACN/B,GAAG,EAAE,CAAC;QACN4B,KAAK,EAAEE,KAAK;QACZD,MAAM,EAAEE,MAAM;QACd7B,IAAI,EAAE;MACV,CAAC;MACDge,uBAAuB,GAAGI,0BAA0B,CAAC5c,UAAU,EAAEc,QAAQ,CAAC;MAC1E2b,yBAAyB,GAAGI,4BAA4B,CAAC7c,UAAU,EAAEa,QAAQ,CAAC;MAC9E0b,UAAU,GAAGha,MAAM;IACvB;IACA,IAAIga,UAAU,KACTC,uBAAuB,KAAK,IAAI,CAACzB,wBAAwB,IACtD0B,yBAAyB,KAAK,IAAI,CAACzB,0BAA0B,IAC7DuB,UAAU,KAAK,IAAI,CAACpB,WAAW,CAAC,EAAE;MACtC,IAAI,CAACJ,wBAAwB,GAAGyB,uBAAuB;MACvD,IAAI,CAACxB,0BAA0B,GAAGyB,yBAAyB;MAC3D,IAAI,CAACtB,WAAW,GAAGoB,UAAU;MAC7B,IAAI,CAACC,uBAAuB,IAAIC,yBAAyB,KAAKF,UAAU,EAAE;QACtE,IAAI,CAAC5X,OAAO,CAACyF,iBAAiB,CAAC,IAAI,CAAC8Q,oBAAoB,CAAC;MAC7D,CAAC,MACI;QACD,IAAI,CAACtO,cAAc,CAAC,CAAC;MACzB;IACJ;EACJ;EACA;EACAA,cAAcA,CAAA,EAAG;IACb,IAAI,CAACqO,iBAAiB,CAACznB,IAAI,CAAC,CAAC;EACjC;EACA;EACAkoB,gBAAgBA,CAAA,EAAG;IACf,MAAMjH,MAAM,GAAGlY,oEAAa,CAAC,IAAI,CAACyB,OAAO,CAAC,CAACG,KAAK;IAChD,IAAI,CAACyH,aAAa,CAACpS,IAAI,CAAC,CAAC;IACzB,IAAI,CAACknB,WAAW,GAAG,IAAI;IACvB;IACA;IACA;IACA,IAAI,CAACoC,kBAAkB,GAAGrI,MAAM,CAACsI,gBAAgB,IAAItI,MAAM,CAACuI,cAAc,IAAI,EAAE;IAChFvI,MAAM,CAACuI,cAAc,GAAGvI,MAAM,CAACsI,gBAAgB,GAAG,MAAM;IACxD,IAAI,CAACvB,aAAa,CAACvN,KAAK,CAAC,IAAI,CAAC0M,WAAW,CAAC;IAC1C,IAAI,CAACiB,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACd,2BAA2B,CAACjQ,WAAW,CAAC,CAAC;IAC9C,IAAI,CAACoS,qBAAqB,CAAC,CAAC;EAChC;EACA;EACArB,qBAAqBA,CAAA,EAAG;IACpB,MAAM5d,OAAO,GAAGzB,oEAAa,CAAC,IAAI,CAACyB,OAAO,CAAC;IAC3C,IAAI,CAACmL,gBAAgB,CAAC9H,KAAK,CAAC,IAAI,CAAC+a,mBAAmB,CAAC;IACrD;IACA;IACA,IAAI,CAACE,WAAW,GAAG,IAAI,CAACnT,gBAAgB,CAAChI,SAAS,CAAChK,GAAG,CAAC6G,OAAO,CAAC,CAACgC,UAAU;EAC9E;EACA;EACA6b,MAAMA,CAAA,EAAG;IACL,IAAI,CAACnB,WAAW,GAAG,KAAK;IACxB,MAAMjG,MAAM,GAAGlY,oEAAa,CAAC,IAAI,CAACyB,OAAO,CAAC,CAACG,KAAK;IAChDsW,MAAM,CAACuI,cAAc,GAAGvI,MAAM,CAACsI,gBAAgB,GAAG,IAAI,CAACD,kBAAkB;IACzE,IAAI,CAAClC,SAAS,CAAC5lB,OAAO,CAACujB,OAAO,IAAIA,OAAO,CAAC2E,cAAc,CAAC,IAAI,CAAC,CAAC;IAC/D,IAAI,CAAC1B,aAAa,CAAC9P,KAAK,CAAC,CAAC;IAC1B,IAAI,CAACkB,cAAc,CAAC,CAAC;IACrB,IAAI,CAACkO,2BAA2B,CAACjQ,WAAW,CAAC,CAAC;IAC9C,IAAI,CAAC1B,gBAAgB,CAAC1O,KAAK,CAAC,CAAC;EACjC;EACA;AACJ;AACA;AACA;AACA;EACIqV,gBAAgBA,CAACxP,CAAC,EAAEC,CAAC,EAAE;IACnB,OAAO,IAAI,CAAC+b,WAAW,IAAI,IAAI,IAAI9b,kBAAkB,CAAC,IAAI,CAAC8b,WAAW,EAAEhc,CAAC,EAAEC,CAAC,CAAC;EACjF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI+P,gCAAgCA,CAACP,IAAI,EAAEzP,CAAC,EAAEC,CAAC,EAAE;IACzC,OAAO,IAAI,CAACqa,SAAS,CAACtb,IAAI,CAACiZ,OAAO,IAAIA,OAAO,CAAC4E,WAAW,CAACpN,IAAI,EAAEzP,CAAC,EAAEC,CAAC,CAAC,CAAC;EAC1E;EACA;AACJ;AACA;AACA;AACA;AACA;EACI4c,WAAWA,CAACpN,IAAI,EAAEzP,CAAC,EAAEC,CAAC,EAAE;IACpB,IAAI,CAAC,IAAI,CAAC+b,WAAW,IACjB,CAAC9b,kBAAkB,CAAC,IAAI,CAAC8b,WAAW,EAAEhc,CAAC,EAAEC,CAAC,CAAC,IAC3C,CAAC,IAAI,CAAC8Z,cAAc,CAACtK,IAAI,EAAE,IAAI,CAAC,EAAE;MAClC,OAAO,KAAK;IAChB;IACA,MAAMqN,gBAAgB,GAAG,IAAI,CAAC/gB,cAAc,CAAC,CAAC,CAAC+gB,gBAAgB,CAAC9c,CAAC,EAAEC,CAAC,CAAC;IACrE;IACA;IACA,IAAI,CAAC6c,gBAAgB,EAAE;MACnB,OAAO,KAAK;IAChB;IACA,MAAMC,aAAa,GAAG9gB,oEAAa,CAAC,IAAI,CAACyB,OAAO,CAAC;IACjD;IACA;IACA;IACA;IACA;IACA;IACA,OAAOof,gBAAgB,KAAKC,aAAa,IAAIA,aAAa,CAAC/a,QAAQ,CAAC8a,gBAAgB,CAAC;EACzF;EACA;AACJ;AACA;AACA;EACIE,eAAeA,CAAC/E,OAAO,EAAEjB,KAAK,EAAE;IAC5B,MAAMiG,cAAc,GAAG,IAAI,CAAC1C,eAAe;IAC3C,IAAI,CAAC0C,cAAc,CAAC1f,GAAG,CAAC0a,OAAO,CAAC,IAC5BjB,KAAK,CAAC2E,KAAK,CAAClM,IAAI,IAAI;MAChB;MACA;MACA;MACA;MACA,OAAO,IAAI,CAACsK,cAAc,CAACtK,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC4K,WAAW,CAAC7b,OAAO,CAACiR,IAAI,CAAC,GAAG,CAAC,CAAC;IACjF,CAAC,CAAC,EAAE;MACJwN,cAAc,CAAC3T,GAAG,CAAC2O,OAAO,CAAC;MAC3B,IAAI,CAACqD,qBAAqB,CAAC,CAAC;MAC5B,IAAI,CAACqB,qBAAqB,CAAC,CAAC;MAC5B,IAAI,CAACzC,gBAAgB,CAAChnB,IAAI,CAAC;QACvBgqB,SAAS,EAAEjF,OAAO;QAClBkF,QAAQ,EAAE,IAAI;QACdnG;MACJ,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;EACI4F,cAAcA,CAAC3E,OAAO,EAAE;IACpB,IAAI,CAACsC,eAAe,CAAC/iB,MAAM,CAACygB,OAAO,CAAC;IACpC,IAAI,CAACuC,2BAA2B,CAACjQ,WAAW,CAAC,CAAC;IAC9C,IAAI,CAAC4P,gBAAgB,CAACjnB,IAAI,CAAC;MAAEgqB,SAAS,EAAEjF,OAAO;MAAEkF,QAAQ,EAAE;IAAK,CAAC,CAAC;EACtE;EACA;AACJ;AACA;AACA;EACIR,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACnC,2BAA2B,GAAG,IAAI,CAACjW,iBAAiB,CACpDoK,QAAQ,CAAC,IAAI,CAAC5S,cAAc,CAAC,CAAC,CAAC,CAC/B0O,SAAS,CAAClJ,KAAK,IAAI;MACpB,IAAI,IAAI,CAAC4F,UAAU,CAAC,CAAC,EAAE;QACnB,MAAMyN,gBAAgB,GAAG,IAAI,CAAC/L,gBAAgB,CAACvH,YAAY,CAACC,KAAK,CAAC;QAClE,IAAIqT,gBAAgB,EAAE;UAClB,IAAI,CAACsG,aAAa,CAAClC,cAAc,CAACpE,gBAAgB,CAAC5W,GAAG,EAAE4W,gBAAgB,CAAC1W,IAAI,CAAC;QAClF;MACJ,CAAC,MACI,IAAI,IAAI,CAACkJ,WAAW,CAAC,CAAC,EAAE;QACzB,IAAI,CAACkU,qBAAqB,CAAC,CAAC;MAChC;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACIvf,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC,IAAI,CAAC8Y,iBAAiB,EAAE;MACzB,MAAMzH,UAAU,GAAGrR,qEAAc,CAACE,oEAAa,CAAC,IAAI,CAACyB,OAAO,CAAC,CAAC;MAC9D,IAAI,CAACmX,iBAAiB,GAAIzH,UAAU,IAAI,IAAI,CAACxM,SAAU;IAC3D;IACA,OAAO,IAAI,CAACiU,iBAAiB;EACjC;EACA;EACAwG,wBAAwBA,CAAA,EAAG;IACvB,MAAMI,YAAY,GAAG,IAAI,CAACP,aAAa,CAClCpC,sBAAsB,CAAC,CAAC,CACxB4C,MAAM,CAACjM,IAAI,IAAIA,IAAI,CAACtI,UAAU,CAAC,CAAC,CAAC;IACtC,IAAI,CAACmT,SAAS,CAAC5lB,OAAO,CAACujB,OAAO,IAAIA,OAAO,CAAC+E,eAAe,CAAC,IAAI,EAAEvB,YAAY,CAAC,CAAC;EAClF;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAASa,0BAA0BA,CAAC5c,UAAU,EAAEc,QAAQ,EAAE;EACtD,MAAM;IAAExC,GAAG;IAAE6B,MAAM;IAAEE;EAAO,CAAC,GAAGL,UAAU;EAC1C,MAAMgB,UAAU,GAAGX,MAAM,GAAG4Z,0BAA0B;EACtD,IAAInZ,QAAQ,IAAIxC,GAAG,GAAG0C,UAAU,IAAIF,QAAQ,IAAIxC,GAAG,GAAG0C,UAAU,EAAE;IAC9D,OAAO,CAAC,CAAC;EACb,CAAC,MACI,IAAIF,QAAQ,IAAIX,MAAM,GAAGa,UAAU,IAAIF,QAAQ,IAAIX,MAAM,GAAGa,UAAU,EAAE;IACzE,OAAO,CAAC,CAAC;EACb;;EACA,OAAO,CAAC,CAAC;AACb;AACA;AACA;AACA;AACA;AACA;AACA,SAAS6b,4BAA4BA,CAAC7c,UAAU,EAAEa,QAAQ,EAAE;EACxD,MAAM;IAAErC,IAAI;IAAE0B,KAAK;IAAEE;EAAM,CAAC,GAAGJ,UAAU;EACzC,MAAMe,UAAU,GAAGX,KAAK,GAAG6Z,0BAA0B;EACrD,IAAIpZ,QAAQ,IAAIrC,IAAI,GAAGuC,UAAU,IAAIF,QAAQ,IAAIrC,IAAI,GAAGuC,UAAU,EAAE;IAChE,OAAO,CAAC,CAAC;EACb,CAAC,MACI,IAAIF,QAAQ,IAAIX,KAAK,GAAGa,UAAU,IAAIF,QAAQ,IAAIX,KAAK,GAAGa,UAAU,EAAE;IACvE,OAAO,CAAC,CAAC;EACb;;EACA,OAAO,CAAC,CAAC;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2b,0BAA0BA,CAAC1e,OAAO,EAAEgC,UAAU,EAAEa,QAAQ,EAAEC,QAAQ,EAAE;EACzE,MAAM4c,gBAAgB,GAAGd,0BAA0B,CAAC5c,UAAU,EAAEc,QAAQ,CAAC;EACzE,MAAM6c,kBAAkB,GAAGd,4BAA4B,CAAC7c,UAAU,EAAEa,QAAQ,CAAC;EAC7E,IAAI2b,uBAAuB,GAAG,CAAC,CAAC;EAChC,IAAIC,yBAAyB,GAAG,CAAC,CAAC;EAClC;EACA;EACA;EACA;EACA,IAAIiB,gBAAgB,EAAE;IAClB,MAAMhc,SAAS,GAAG1D,OAAO,CAAC0D,SAAS;IACnC,IAAIgc,gBAAgB,KAAK,CAAC,CAAC,sCAAsC;MAC7D,IAAIhc,SAAS,GAAG,CAAC,EAAE;QACf8a,uBAAuB,GAAG,CAAC,CAAC;MAChC;IACJ,CAAC,MACI,IAAIxe,OAAO,CAAC4f,YAAY,GAAGlc,SAAS,GAAG1D,OAAO,CAAC6f,YAAY,EAAE;MAC9DrB,uBAAuB,GAAG,CAAC,CAAC;IAChC;EACJ;;EACA,IAAImB,kBAAkB,EAAE;IACpB,MAAMhc,UAAU,GAAG3D,OAAO,CAAC2D,UAAU;IACrC,IAAIgc,kBAAkB,KAAK,CAAC,CAAC,0CAA0C;MACnE,IAAIhc,UAAU,GAAG,CAAC,EAAE;QAChB8a,yBAAyB,GAAG,CAAC,CAAC;MAClC;IACJ,CAAC,MACI,IAAIze,OAAO,CAAC8f,WAAW,GAAGnc,UAAU,GAAG3D,OAAO,CAAC+f,WAAW,EAAE;MAC7DtB,yBAAyB,GAAG,CAAC,CAAC;IAClC;EACJ;;EACA,OAAO,CAACD,uBAAuB,EAAEC,yBAAyB,CAAC;AAC/D;;AAEA;AACA,MAAMuB,2BAA2B,GAAG5hB,sFAA+B,CAAC;EAChEyH,OAAO,EAAE,KAAK;EACdoa,OAAO,EAAE;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EACnBrsB,WAAWA,CAAC8S,OAAO,EAAEzD,SAAS,EAAE;IAC5B,IAAI,CAACyD,OAAO,GAAGA,OAAO;IACtB;IACA,IAAI,CAACwZ,cAAc,GAAG,IAAIla,GAAG,CAAC,CAAC;IAC/B;IACA,IAAI,CAACma,cAAc,GAAG,IAAIna,GAAG,CAAC,CAAC;IAC/B;IACA,IAAI,CAACoa,oBAAoB,GAAG,EAAE;IAC9B;IACA,IAAI,CAACC,gBAAgB,GAAG,IAAIld,GAAG,CAAC,CAAC;IACjC;AACR;AACA;AACA;IACQ,IAAI,CAACmd,kBAAkB,GAAIxO,IAAI,IAAKA,IAAI,CAACtI,UAAU,CAAC,CAAC;IACrD;AACR;AACA;AACA;IACQ,IAAI,CAACsH,WAAW,GAAG,IAAInS,yCAAO,CAAC,CAAC;IAChC;AACR;AACA;AACA;IACQ,IAAI,CAACoS,SAAS,GAAG,IAAIpS,yCAAO,CAAC,CAAC;IAC9B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC4hB,MAAM,GAAG,IAAI5hB,yCAAO,CAAC,CAAC;IAC3B;AACR;AACA;AACA;IACQ,IAAI,CAAC6hB,4BAA4B,GAAI5c,KAAK,IAAK;MAC3C,IAAI,IAAI,CAACwc,oBAAoB,CAAC1qB,MAAM,GAAG,CAAC,EAAE;QACtCkO,KAAK,CAAC8F,cAAc,CAAC,CAAC;MAC1B;IACJ,CAAC;IACD;IACA,IAAI,CAAC+W,4BAA4B,GAAI7c,KAAK,IAAK;MAC3C,IAAI,IAAI,CAACwc,oBAAoB,CAAC1qB,MAAM,GAAG,CAAC,EAAE;QACtC;QACA;QACA;QACA,IAAI,IAAI,CAAC0qB,oBAAoB,CAACM,IAAI,CAAC,IAAI,CAACJ,kBAAkB,CAAC,EAAE;UACzD1c,KAAK,CAAC8F,cAAc,CAAC,CAAC;QAC1B;QACA,IAAI,CAACoH,WAAW,CAACvb,IAAI,CAACqO,KAAK,CAAC;MAChC;IACJ,CAAC;IACD,IAAI,CAACX,SAAS,GAAGA,SAAS;EAC9B;EACA;EACAqa,qBAAqBA,CAACrL,IAAI,EAAE;IACxB,IAAI,CAAC,IAAI,CAACiO,cAAc,CAACtgB,GAAG,CAACqS,IAAI,CAAC,EAAE;MAChC,IAAI,CAACiO,cAAc,CAACvU,GAAG,CAACsG,IAAI,CAAC;IACjC;EACJ;EACA;EACA9G,gBAAgBA,CAACgO,IAAI,EAAE;IACnB,IAAI,CAACgH,cAAc,CAACxU,GAAG,CAACwN,IAAI,CAAC;IAC7B;IACA;IACA;IACA,IAAI,IAAI,CAACgH,cAAc,CAAC/B,IAAI,KAAK,CAAC,EAAE;MAChC,IAAI,CAAC1X,OAAO,CAACyF,iBAAiB,CAAC,MAAM;QACjC;QACA;QACA,IAAI,CAAClJ,SAAS,CAACmJ,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACqU,4BAA4B,EAAEV,2BAA2B,CAAC;MAChH,CAAC,CAAC;IACN;EACJ;EACA;EACAvC,mBAAmBA,CAACvL,IAAI,EAAE;IACtB,IAAI,CAACiO,cAAc,CAACrmB,MAAM,CAACoY,IAAI,CAAC;EACpC;EACA;EACA1E,cAAcA,CAAC4L,IAAI,EAAE;IACjB,IAAI,CAACgH,cAAc,CAACtmB,MAAM,CAACsf,IAAI,CAAC;IAChC,IAAI,CAAC3K,YAAY,CAAC2K,IAAI,CAAC;IACvB,IAAI,IAAI,CAACgH,cAAc,CAAC/B,IAAI,KAAK,CAAC,EAAE;MAChC,IAAI,CAACnb,SAAS,CAAC+Q,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACyM,4BAA4B,EAAEV,2BAA2B,CAAC;IACnH;EACJ;EACA;AACJ;AACA;AACA;AACA;EACItO,aAAaA,CAAC0H,IAAI,EAAEvV,KAAK,EAAE;IACvB;IACA,IAAI,IAAI,CAACwc,oBAAoB,CAACvf,OAAO,CAACsY,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;MAC9C;IACJ;IACA,IAAI,CAACiH,oBAAoB,CAACppB,IAAI,CAACmiB,IAAI,CAAC;IACpC,IAAI,IAAI,CAACiH,oBAAoB,CAAC1qB,MAAM,KAAK,CAAC,EAAE;MACxC,MAAMuZ,YAAY,GAAGrL,KAAK,CAAC1H,IAAI,CAACykB,UAAU,CAAC,OAAO,CAAC;MACnD;MACA;MACA;MACA,IAAI,CAACN,gBAAgB,CAChB/c,GAAG,CAAC2L,YAAY,GAAG,UAAU,GAAG,SAAS,EAAE;QAC5C6E,OAAO,EAAG8M,CAAC,IAAK,IAAI,CAAC7P,SAAS,CAACxb,IAAI,CAACqrB,CAAC,CAAC;QACtCC,OAAO,EAAE;MACb,CAAC,CAAC,CACGvd,GAAG,CAAC,QAAQ,EAAE;QACfwQ,OAAO,EAAG8M,CAAC,IAAK,IAAI,CAACL,MAAM,CAAChrB,IAAI,CAACqrB,CAAC,CAAC;QACnC;QACA;QACAC,OAAO,EAAE;MACb,CAAC;MACG;MACA;MACA;MACA;MAAA,CACCvd,GAAG,CAAC,aAAa,EAAE;QACpBwQ,OAAO,EAAE,IAAI,CAAC0M,4BAA4B;QAC1CK,OAAO,EAAEd;MACb,CAAC,CAAC;MACF;MACA;MACA,IAAI,CAAC9Q,YAAY,EAAE;QACf,IAAI,CAACoR,gBAAgB,CAAC/c,GAAG,CAAC,WAAW,EAAE;UACnCwQ,OAAO,EAAG8M,CAAC,IAAK,IAAI,CAAC9P,WAAW,CAACvb,IAAI,CAACqrB,CAAC,CAAC;UACxCC,OAAO,EAAEd;QACb,CAAC,CAAC;MACN;MACA,IAAI,CAACrZ,OAAO,CAACyF,iBAAiB,CAAC,MAAM;QACjC,IAAI,CAACkU,gBAAgB,CAACtpB,OAAO,CAAC,CAAC+pB,MAAM,EAAEhrB,IAAI,KAAK;UAC5C,IAAI,CAACmN,SAAS,CAACmJ,gBAAgB,CAACtW,IAAI,EAAEgrB,MAAM,CAAChN,OAAO,EAAEgN,MAAM,CAACD,OAAO,CAAC;QACzE,CAAC,CAAC;MACN,CAAC,CAAC;IACN;EACJ;EACA;EACArS,YAAYA,CAAC2K,IAAI,EAAE;IACf,MAAMvjB,KAAK,GAAG,IAAI,CAACwqB,oBAAoB,CAACvf,OAAO,CAACsY,IAAI,CAAC;IACrD,IAAIvjB,KAAK,GAAG,CAAC,CAAC,EAAE;MACZ,IAAI,CAACwqB,oBAAoB,CAACxH,MAAM,CAAChjB,KAAK,EAAE,CAAC,CAAC;MAC1C,IAAI,IAAI,CAACwqB,oBAAoB,CAAC1qB,MAAM,KAAK,CAAC,EAAE;QACxC,IAAI,CAACqrB,qBAAqB,CAAC,CAAC;MAChC;IACJ;EACJ;EACA;EACAvX,UAAUA,CAAC2P,IAAI,EAAE;IACb,OAAO,IAAI,CAACiH,oBAAoB,CAACvf,OAAO,CAACsY,IAAI,CAAC,GAAG,CAAC,CAAC;EACvD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACInI,QAAQA,CAACvB,UAAU,EAAE;IACjB,MAAMuR,OAAO,GAAG,CAAC,IAAI,CAACT,MAAM,CAAC;IAC7B,IAAI9Q,UAAU,IAAIA,UAAU,KAAK,IAAI,CAACxM,SAAS,EAAE;MAC7C;MACA;MACA;MACA+d,OAAO,CAAChqB,IAAI,CAAC,IAAI3D,4CAAU,CAAEgC,QAAQ,IAAK;QACtC,OAAO,IAAI,CAACqR,OAAO,CAACyF,iBAAiB,CAAC,MAAM;UACxC,MAAM8U,YAAY,GAAG,IAAI;UACzB,MAAM7b,QAAQ,GAAIxB,KAAK,IAAK;YACxB,IAAI,IAAI,CAACwc,oBAAoB,CAAC1qB,MAAM,EAAE;cAClCL,QAAQ,CAACE,IAAI,CAACqO,KAAK,CAAC;YACxB;UACJ,CAAC;UACD6L,UAAU,CAACrD,gBAAgB,CAAC,QAAQ,EAAEhH,QAAQ,EAAE6b,YAAY,CAAC;UAC7D,OAAO,MAAM;YACTxR,UAAU,CAACuE,mBAAmB,CAAC,QAAQ,EAAE5O,QAAQ,EAAE6b,YAAY,CAAC;UACpE,CAAC;QACL,CAAC,CAAC;MACN,CAAC,CAAC,CAAC;IACP;IACA,OAAOliB,2CAAK,CAAC,GAAGiiB,OAAO,CAAC;EAC5B;EACAE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACf,cAAc,CAACppB,OAAO,CAACoqB,QAAQ,IAAI,IAAI,CAAC5T,cAAc,CAAC4T,QAAQ,CAAC,CAAC;IACtE,IAAI,CAACjB,cAAc,CAACnpB,OAAO,CAACoqB,QAAQ,IAAI,IAAI,CAAC3D,mBAAmB,CAAC2D,QAAQ,CAAC,CAAC;IAC3E,IAAI,CAACJ,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACjQ,WAAW,CAACtb,QAAQ,CAAC,CAAC;IAC3B,IAAI,CAACub,SAAS,CAACvb,QAAQ,CAAC,CAAC;EAC7B;EACA;EACAurB,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACV,gBAAgB,CAACtpB,OAAO,CAAC,CAAC+pB,MAAM,EAAEhrB,IAAI,KAAK;MAC5C,IAAI,CAACmN,SAAS,CAAC+Q,mBAAmB,CAACle,IAAI,EAAEgrB,MAAM,CAAChN,OAAO,EAAEgN,MAAM,CAACD,OAAO,CAAC;IAC5E,CAAC,CAAC;IACF,IAAI,CAACR,gBAAgB,CAAC7jB,KAAK,CAAC,CAAC;EACjC;EACA;IAAS,IAAI,CAAC/D,IAAI,YAAA2oB,yBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFpB,gBAAgB,EAA1BhlB,uDAAE,CAA0CA,kDAAS,GAArDA,uDAAE,CAAgE+C,sDAAQ;IAAA,CAA6C;EAAE;EACzN;IAAS,IAAI,CAACujB,KAAK,kBAD6EtmB,iEAAE;MAAAwmB,KAAA,EACYxB,gBAAgB;MAAAznB,OAAA,EAAhBynB,gBAAgB,CAAAxnB,IAAA;MAAAC,UAAA,EAAc;IAAM,EAAG;EAAE;AAC3J;AACA;EAAA,QAAAgpB,SAAA,oBAAAA,SAAA,KAHoGzmB,gEAAE,CAGXglB,gBAAgB,EAAc,CAAC;IAC9G/jB,IAAI,EAAEiB,sDAAU;IAChBykB,IAAI,EAAE,CAAC;MAAElpB,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEwD,IAAI,EAAEjB,kDAASqmB;IAAC,CAAC,EAAE;MAAEplB,IAAI,EAAEoQ,SAAS;MAAEuV,UAAU,EAAE,CAAC;QACnF3lB,IAAI,EAAEkB,kDAAM;QACZwkB,IAAI,EAAE,CAAC5jB,sDAAQ;MACnB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;;AAExB;AACA,MAAM8jB,cAAc,GAAG;EACnB9Y,kBAAkB,EAAE,CAAC;EACrBsN,+BAA+B,EAAE;AACrC,CAAC;AACD;AACA;AACA;AACA,MAAMyL,QAAQ,CAAC;EACXnuB,WAAWA,CAACqP,SAAS,EAAEyD,OAAO,EAAEC,cAAc,EAAEC,iBAAiB,EAAE;IAC/D,IAAI,CAAC3D,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACyD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;EAC9C;EACA;AACJ;AACA;AACA;AACA;EACIob,UAAUA,CAACjiB,OAAO,EAAE+gB,MAAM,GAAGgB,cAAc,EAAE;IACzC,OAAO,IAAI7b,OAAO,CAAClG,OAAO,EAAE+gB,MAAM,EAAE,IAAI,CAAC7d,SAAS,EAAE,IAAI,CAACyD,OAAO,EAAE,IAAI,CAACC,cAAc,EAAE,IAAI,CAACC,iBAAiB,CAAC;EAClH;EACA;AACJ;AACA;AACA;EACIqb,cAAcA,CAACliB,OAAO,EAAE;IACpB,OAAO,IAAIkc,WAAW,CAAClc,OAAO,EAAE,IAAI,CAAC6G,iBAAiB,EAAE,IAAI,CAAC3D,SAAS,EAAE,IAAI,CAACyD,OAAO,EAAE,IAAI,CAACC,cAAc,CAAC;EAC9G;EACA;IAAS,IAAI,CAAClO,IAAI,YAAAypB,iBAAAb,CAAA;MAAA,YAAAA,CAAA,IAAwFU,QAAQ,EAzClB9mB,uDAAE,CAyCkC+C,sDAAQ,GAzC5C/C,uDAAE,CAyCuDA,kDAAS,GAzClEA,uDAAE,CAyC6EE,kEAAgB,GAzC/FF,uDAAE,CAyC0GglB,gBAAgB;IAAA,CAA6C;EAAE;EAC3Q;IAAS,IAAI,CAACsB,KAAK,kBA1C6EtmB,iEAAE;MAAAwmB,KAAA,EA0CYM,QAAQ;MAAAvpB,OAAA,EAARupB,QAAQ,CAAAtpB,IAAA;MAAAC,UAAA,EAAc;IAAM,EAAG;EAAE;AACnJ;AACA;EAAA,QAAAgpB,SAAA,oBAAAA,SAAA,KA5CoGzmB,gEAAE,CA4CX8mB,QAAQ,EAAc,CAAC;IACtG7lB,IAAI,EAAEiB,sDAAU;IAChBykB,IAAI,EAAE,CAAC;MAAElpB,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEwD,IAAI,EAAEoQ,SAAS;MAAEuV,UAAU,EAAE,CAAC;QAC9D3lB,IAAI,EAAEkB,kDAAM;QACZwkB,IAAI,EAAE,CAAC5jB,sDAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAE9B,IAAI,EAAEjB,kDAASqmB;IAAC,CAAC,EAAE;MAAEplB,IAAI,EAAEf,kEAAgBgnB;IAAC,CAAC,EAAE;MAAEjmB,IAAI,EAAE+jB;IAAiB,CAAC,CAAC;EAAE,CAAC;AAAA;;AAErG;AACA;AACA;AACA;AACA;AACA;AACA,MAAMmC,eAAe,GAAG,IAAI/kB,0DAAc,CAAC,iBAAiB,CAAC;;AAE7D;AACA;AACA;AACA;AACA;AACA,SAASglB,iBAAiBA,CAACje,IAAI,EAAEtO,IAAI,EAAE;EACnC,IAAIsO,IAAI,CAACyT,QAAQ,KAAK,CAAC,EAAE;IACrB,MAAM7c,KAAK,CAAE,GAAElF,IAAK,wCAAuC,GAAI,0BAAyBsO,IAAI,CAACU,QAAS,IAAG,CAAC;EAC9G;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMwd,eAAe,GAAG,IAAIjlB,0DAAc,CAAC,eAAe,CAAC;AAC3D;AACA,MAAMklB,aAAa,CAAC;EAChB;EACA,IAAIrc,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAAC3J,KAAK,EAAE;IAChB,IAAI,CAAC4J,SAAS,GAAG9H,4EAAqB,CAAC9B,KAAK,CAAC;IAC7C,IAAI,CAACimB,aAAa,CAACjtB,IAAI,CAAC,IAAI,CAAC;EACjC;EACA3B,WAAWA,CAACmM,OAAO,EAAE0iB,UAAU,EAAE;IAC7B,IAAI,CAAC1iB,OAAO,GAAGA,OAAO;IACtB;IACA,IAAI,CAACyiB,aAAa,GAAG,IAAI7jB,yCAAO,CAAC,CAAC;IAClC,IAAI,CAACwH,SAAS,GAAG,KAAK;IACtB,IAAI,OAAOub,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/CW,iBAAiB,CAACtiB,OAAO,CAACqf,aAAa,EAAE,eAAe,CAAC;IAC7D;IACA,IAAI,CAACsD,WAAW,GAAGD,UAAU;EACjC;EACAvB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACsB,aAAa,CAAChtB,QAAQ,CAAC,CAAC;EACjC;EACA;IAAS,IAAI,CAACiD,IAAI,YAAAkqB,sBAAAtB,CAAA;MAAA,YAAAA,CAAA,IAAwFkB,aAAa,EApGvBtnB,gEAAE,CAoGuCA,sDAAa,GApGtDA,gEAAE,CAoGiEmnB,eAAe;IAAA,CAA4E;EAAE;EAChQ;IAAS,IAAI,CAACU,IAAI,kBArG8E7nB,gEAAE;MAAAiB,IAAA,EAqGJqmB,aAAa;MAAAS,SAAA;MAAAC,SAAA;MAAAC,MAAA;QAAAhd,QAAA;MAAA;MAAAid,UAAA;MAAAC,QAAA,GArGXnoB,iEAAE,CAqGiL,CAAC;QAAEqoB,OAAO,EAAEhB,eAAe;QAAEiB,WAAW,EAAEhB;MAAc,CAAC,CAAC;IAAA,EAAiB;EAAE;AACpW;AACA;EAAA,QAAAb,SAAA,oBAAAA,SAAA,KAvGoGzmB,gEAAE,CAuGXsnB,aAAa,EAAc,CAAC;IAC3GrmB,IAAI,EAAEoB,qDAAS;IACfskB,IAAI,EAAE,CAAC;MACCzc,QAAQ,EAAE,iBAAiB;MAC3Bge,UAAU,EAAE,IAAI;MAChBK,IAAI,EAAE;QACF,OAAO,EAAE;MACb,CAAC;MACDC,SAAS,EAAE,CAAC;QAAEH,OAAO,EAAEhB,eAAe;QAAEiB,WAAW,EAAEhB;MAAc,CAAC;IACxE,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAErmB,IAAI,EAAEjB,sDAAa4nB;IAAC,CAAC,EAAE;MAAE3mB,IAAI,EAAEoQ,SAAS;MAAEuV,UAAU,EAAE,CAAC;QACvF3lB,IAAI,EAAEkB,kDAAM;QACZwkB,IAAI,EAAE,CAACQ,eAAe;MAC1B,CAAC,EAAE;QACClmB,IAAI,EAAEqB,oDAAQA;MAClB,CAAC,EAAE;QACCrB,IAAI,EAAEsB,oDAAQA;MAClB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE0I,QAAQ,EAAE,CAAC;MACvChK,IAAI,EAAEuB,iDAAK;MACXmkB,IAAI,EAAE,CAAC,uBAAuB;IAClC,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA,MAAM8B,oBAAoB,GAAG,IAAIrmB,0DAAc,CAAC,oBAAoB,CAAC;AACrE;AACA;AACA;AACA;AACA,MAAMsmB,kBAAkB,CAAC;EACrB/vB,WAAWA,CAACgwB,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;EAClC;EACA;IAAS,IAAI,CAACnrB,IAAI,YAAAorB,2BAAAxC,CAAA;MAAA,YAAAA,CAAA,IAAwFsC,kBAAkB,EA3I5B1oB,gEAAE,CA2I4CA,uDAAc;IAAA,CAA4C;EAAE;EAC1M;IAAS,IAAI,CAAC6nB,IAAI,kBA5I8E7nB,gEAAE;MAAAiB,IAAA,EA4IJynB,kBAAkB;MAAAX,SAAA;MAAAE,MAAA;QAAA/pB,IAAA;MAAA;MAAAgqB,UAAA;MAAAC,QAAA,GA5IhBnoB,iEAAE,CA4IsH,CAAC;QAAEqoB,OAAO,EAAEI,oBAAoB;QAAEH,WAAW,EAAEI;MAAmB,CAAC,CAAC;IAAA,EAAiB;EAAE;AACnT;AACA;EAAA,QAAAjC,SAAA,oBAAAA,SAAA,KA9IoGzmB,gEAAE,CA8IX0oB,kBAAkB,EAAc,CAAC;IAChHznB,IAAI,EAAEoB,qDAAS;IACfskB,IAAI,EAAE,CAAC;MACCzc,QAAQ,EAAE,iCAAiC;MAC3Cge,UAAU,EAAE,IAAI;MAChBM,SAAS,EAAE,CAAC;QAAEH,OAAO,EAAEI,oBAAoB;QAAEH,WAAW,EAAEI;MAAmB,CAAC;IAClF,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEznB,IAAI,EAAEjB,uDAAc6oB;IAAC,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE3qB,IAAI,EAAE,CAAC;MACzF+C,IAAI,EAAEuB,iDAAKA;IACf,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA,MAAMsmB,gBAAgB,GAAG,IAAI1mB,0DAAc,CAAC,gBAAgB,CAAC;AAC7D;AACA;AACA;AACA;AACA,MAAM2mB,cAAc,CAAC;EACjB;EACA,IAAI1S,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC2S,UAAU;EAC1B;EACA,IAAI3S,SAASA,CAAC/U,KAAK,EAAE;IACjB,IAAI,CAAC0nB,UAAU,GAAG5lB,4EAAqB,CAAC9B,KAAK,CAAC;EAClD;EACA3I,WAAWA,CAACgwB,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACK,UAAU,GAAG,KAAK;EAC3B;EACA;IAAS,IAAI,CAACxrB,IAAI,YAAAyrB,uBAAA7C,CAAA;MAAA,YAAAA,CAAA,IAAwF2C,cAAc,EA/KxB/oB,gEAAE,CA+KwCA,uDAAc;IAAA,CAA4C;EAAE;EACtM;IAAS,IAAI,CAAC6nB,IAAI,kBAhL8E7nB,gEAAE;MAAAiB,IAAA,EAgLJ8nB,cAAc;MAAAhB,SAAA;MAAAE,MAAA;QAAA/pB,IAAA;QAAAmY,SAAA;MAAA;MAAA6R,UAAA;MAAAC,QAAA,GAhLZnoB,iEAAE,CAgLsI,CAAC;QAAEqoB,OAAO,EAAES,gBAAgB;QAAER,WAAW,EAAES;MAAe,CAAC,CAAC;IAAA,EAAiB;EAAE;AAC3T;AACA;EAAA,QAAAtC,SAAA,oBAAAA,SAAA,KAlLoGzmB,gEAAE,CAkLX+oB,cAAc,EAAc,CAAC;IAC5G9nB,IAAI,EAAEoB,qDAAS;IACfskB,IAAI,EAAE,CAAC;MACCzc,QAAQ,EAAE,6BAA6B;MACvCge,UAAU,EAAE,IAAI;MAChBM,SAAS,EAAE,CAAC;QAAEH,OAAO,EAAES,gBAAgB;QAAER,WAAW,EAAES;MAAe,CAAC;IAC1E,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE9nB,IAAI,EAAEjB,uDAAc6oB;IAAC,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE3qB,IAAI,EAAE,CAAC;MACzF+C,IAAI,EAAEuB,iDAAKA;IACf,CAAC,CAAC;IAAE6T,SAAS,EAAE,CAAC;MACZpV,IAAI,EAAEuB,iDAAKA;IACf,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAM0mB,eAAe,GAAG,IAAI9mB,0DAAc,CAAC,iBAAiB,CAAC;AAE7D,MAAM+mB,eAAe,GAAG,UAAU;AAClC;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAG,IAAIhnB,0DAAc,CAAC,aAAa,CAAC;AACvD;AACA,MAAMinB,OAAO,CAAC;EACV;IAAS,IAAI,CAACnE,cAAc,GAAG,EAAE;EAAE;EACnC;EACA,IAAIja,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS,IAAK,IAAI,CAACgJ,aAAa,IAAI,IAAI,CAACA,aAAa,CAACjJ,QAAS;EAChF;EACA,IAAIA,QAAQA,CAAC3J,KAAK,EAAE;IAChB,IAAI,CAAC4J,SAAS,GAAG9H,4EAAqB,CAAC9B,KAAK,CAAC;IAC7C,IAAI,CAACgoB,QAAQ,CAACre,QAAQ,GAAG,IAAI,CAACC,SAAS;EAC3C;EACAvS,WAAWA,CAAA,CACX;EACAmM,OAAO,EACP;EACAoP,aAAa;EACb;AACJ;AACA;AACA;EACIlM,SAAS,EAAEyD,OAAO,EAAE8d,iBAAiB,EAAE1D,MAAM,EAAE2D,IAAI,EAAEC,QAAQ,EAAEC,kBAAkB,EAAEC,WAAW,EAAElC,WAAW,EAAE;IACzG,IAAI,CAAC3iB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACoP,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACzI,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC8d,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACE,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAClC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACmC,UAAU,GAAG,IAAIlmB,yCAAO,CAAC,CAAC;IAC/B;IACA,IAAI,CAACiJ,OAAO,GAAG,IAAIlK,wDAAY,CAAC,CAAC;IACjC;IACA,IAAI,CAACmK,QAAQ,GAAG,IAAInK,wDAAY,CAAC,CAAC;IAClC;IACA,IAAI,CAACoK,KAAK,GAAG,IAAIpK,wDAAY,CAAC,CAAC;IAC/B;IACA,IAAI,CAACqK,OAAO,GAAG,IAAIrK,wDAAY,CAAC,CAAC;IACjC;IACA,IAAI,CAACsK,MAAM,GAAG,IAAItK,wDAAY,CAAC,CAAC;IAChC;IACA,IAAI,CAACuK,OAAO,GAAG,IAAIvK,wDAAY,CAAC,CAAC;IACjC;AACR;AACA;AACA;IACQ,IAAI,CAACwK,KAAK,GAAG,IAAI7U,4CAAU,CAAEgC,QAAQ,IAAK;MACtC,MAAMyvB,YAAY,GAAG,IAAI,CAACP,QAAQ,CAACrc,KAAK,CACnC7R,IAAI,CAAC7C,oDAAG,CAACuxB,UAAU,KAAK;QACzBxlB,MAAM,EAAE,IAAI;QACZkJ,eAAe,EAAEsc,UAAU,CAACtc,eAAe;QAC3C7E,KAAK,EAAEmhB,UAAU,CAACnhB,KAAK;QACvB+G,KAAK,EAAEoa,UAAU,CAACpa,KAAK;QACvBF,QAAQ,EAAEsa,UAAU,CAACta;MACzB,CAAC,CAAC,CAAC,CAAC,CACCqC,SAAS,CAACzX,QAAQ,CAAC;MACxB,OAAO,MAAM;QACTyvB,YAAY,CAAClY,WAAW,CAAC,CAAC;MAC9B,CAAC;IACL,CAAC,CAAC;IACF,IAAI,CAAC2X,QAAQ,GAAGG,QAAQ,CAAC1C,UAAU,CAACjiB,OAAO,EAAE;MACzCiJ,kBAAkB,EAAE8X,MAAM,IAAIA,MAAM,CAAC9X,kBAAkB,IAAI,IAAI,GAAG8X,MAAM,CAAC9X,kBAAkB,GAAG,CAAC;MAC/FsN,+BAA+B,EAAEwK,MAAM,IAAIA,MAAM,CAACxK,+BAA+B,IAAI,IAAI,GACnFwK,MAAM,CAACxK,+BAA+B,GACtC,CAAC;MACP/C,MAAM,EAAEuN,MAAM,EAAEvN;IACpB,CAAC,CAAC;IACF,IAAI,CAACgR,QAAQ,CAACprB,IAAI,GAAG,IAAI;IACzB;IACA;IACA;IACAmrB,OAAO,CAACnE,cAAc,CAACnpB,IAAI,CAAC,IAAI,CAAC;IACjC,IAAI8pB,MAAM,EAAE;MACR,IAAI,CAACkE,eAAe,CAAClE,MAAM,CAAC;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI3R,aAAa,EAAE;MACf,IAAI,CAACoV,QAAQ,CAACzW,kBAAkB,CAACqB,aAAa,CAAC8V,YAAY,CAAC;MAC5D9V,aAAa,CAAC+V,OAAO,CAAC,IAAI,CAAC;IAC/B;IACA,IAAI,CAACC,WAAW,CAAC,IAAI,CAACZ,QAAQ,CAAC;IAC/B,IAAI,CAACa,aAAa,CAAC,IAAI,CAACb,QAAQ,CAAC;EACrC;EACA;AACJ;AACA;AACA;EACInZ,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACmZ,QAAQ,CAACnZ,qBAAqB,CAAC,CAAC;EAChD;EACA;EACAE,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACiZ,QAAQ,CAACjZ,cAAc,CAAC,CAAC;EACzC;EACA;EACAmC,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC8W,QAAQ,CAAC9W,KAAK,CAAC,CAAC;EACzB;EACA;AACJ;AACA;EACIM,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACwW,QAAQ,CAACxW,mBAAmB,CAAC,CAAC;EAC9C;EACA;AACJ;AACA;AACA;EACIC,mBAAmBA,CAACzR,KAAK,EAAE;IACvB,IAAI,CAACgoB,QAAQ,CAACvW,mBAAmB,CAACzR,KAAK,CAAC;EAC5C;EACA8oB,eAAeA,CAAA,EAAG;IACd;IACA;IACA,IAAI,CAAC3e,OAAO,CAACyF,iBAAiB,CAAC,MAAM;MACjC;MACA;MACA;MACA;MACA,IAAI,CAACzF,OAAO,CAAC4e,QAAQ,CAACjvB,IAAI,CAAC4I,qDAAI,CAAC,CAAC,CAAC,EAAED,yDAAS,CAAC,IAAI,CAAC6lB,UAAU,CAAC,CAAC,CAAC/X,SAAS,CAAC,MAAM;QAC5E,IAAI,CAACyY,kBAAkB,CAAC,CAAC;QACzB,IAAI,CAACC,qBAAqB,CAAC,CAAC;QAC5B,IAAI,IAAI,CAACC,gBAAgB,EAAE;UACvB,IAAI,CAAClB,QAAQ,CAACvW,mBAAmB,CAAC,IAAI,CAACyX,gBAAgB,CAAC;QAC5D;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAC,WAAWA,CAACC,OAAO,EAAE;IACjB,MAAMC,kBAAkB,GAAGD,OAAO,CAAC,qBAAqB,CAAC;IACzD,MAAME,cAAc,GAAGF,OAAO,CAAC,kBAAkB,CAAC;IAClD;IACA;IACA,IAAIC,kBAAkB,IAAI,CAACA,kBAAkB,CAACE,WAAW,EAAE;MACvD,IAAI,CAACP,kBAAkB,CAAC,CAAC;IAC7B;IACA;IACA,IAAIM,cAAc,IAAI,CAACA,cAAc,CAACC,WAAW,IAAI,IAAI,CAACL,gBAAgB,EAAE;MACxE,IAAI,CAAClB,QAAQ,CAACvW,mBAAmB,CAAC,IAAI,CAACyX,gBAAgB,CAAC;IAC5D;EACJ;EACAvE,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC/R,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAAC4W,UAAU,CAAC,IAAI,CAAC;IACvC;IACA,MAAMnwB,KAAK,GAAG0uB,OAAO,CAACnE,cAAc,CAACtf,OAAO,CAAC,IAAI,CAAC;IAClD,IAAIjL,KAAK,GAAG,CAAC,CAAC,EAAE;MACZ0uB,OAAO,CAACnE,cAAc,CAACvH,MAAM,CAAChjB,KAAK,EAAE,CAAC,CAAC;IAC3C;IACA;IACA,IAAI,CAAC8Q,OAAO,CAACyF,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAAC0Y,UAAU,CAACtvB,IAAI,CAAC,CAAC;MACtB,IAAI,CAACsvB,UAAU,CAACrvB,QAAQ,CAAC,CAAC;MAC1B,IAAI,CAAC+uB,QAAQ,CAACrX,OAAO,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN;EACA;EACAqY,kBAAkBA,CAAA,EAAG;IACjB,MAAMxlB,OAAO,GAAG,IAAI,CAACA,OAAO,CAACqf,aAAa;IAC1C,IAAInT,WAAW,GAAGlM,OAAO;IACzB,IAAI,IAAI,CAACimB,mBAAmB,EAAE;MAC1B/Z,WAAW,GACPlM,OAAO,CAACkmB,OAAO,KAAK3Z,SAAS,GACvBvM,OAAO,CAACkmB,OAAO,CAAC,IAAI,CAACD,mBAAmB,CAAC;MACzC;MACEjmB,OAAO,CAAC8a,aAAa,EAAEoL,OAAO,CAAC,IAAI,CAACD,mBAAmB,CAAC;IACxE;IACA,IAAI/Z,WAAW,KAAK,OAAOyV,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAChEW,iBAAiB,CAACpW,WAAW,EAAE,SAAS,CAAC;IAC7C;IACA,IAAI,CAACsY,QAAQ,CAACxZ,eAAe,CAACkB,WAAW,IAAIlM,OAAO,CAAC;EACzD;EACA;EACAmmB,mBAAmBA,CAAA,EAAG;IAClB,MAAMC,QAAQ,GAAG,IAAI,CAACxZ,eAAe;IACrC,IAAI,CAACwZ,QAAQ,EAAE;MACX,OAAO,IAAI;IACf;IACA,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MAC9B,OAAO,IAAI,CAACpmB,OAAO,CAACqf,aAAa,CAAC6G,OAAO,CAACE,QAAQ,CAAC;IACvD;IACA,OAAO7nB,oEAAa,CAAC6nB,QAAQ,CAAC;EAClC;EACA;EACAhB,WAAWA,CAACiB,GAAG,EAAE;IACbA,GAAG,CAACze,aAAa,CAACmF,SAAS,CAAC,MAAM;MAC9B,IAAI,CAACsZ,GAAG,CAAC5c,UAAU,CAAC,CAAC,EAAE;QACnB,MAAM6c,GAAG,GAAG,IAAI,CAAC5B,IAAI;QACrB,MAAM/c,cAAc,GAAG,IAAI,CAACA,cAAc;QAC1C,MAAM2H,WAAW,GAAG,IAAI,CAACrD,oBAAoB,GACvC;UACEH,QAAQ,EAAE,IAAI,CAACG,oBAAoB,CAAC4X,WAAW;UAC/Cpe,OAAO,EAAE,IAAI,CAACwG,oBAAoB,CAAC7S,IAAI;UACvC8Z,aAAa,EAAE,IAAI,CAACuR;QACxB,CAAC,GACC,IAAI;QACV,MAAM1R,OAAO,GAAG,IAAI,CAAChH,gBAAgB,GAC/B;UACED,QAAQ,EAAE,IAAI,CAACC,gBAAgB,CAAC8X,WAAW;UAC3Cpe,OAAO,EAAE,IAAI,CAACsG,gBAAgB,CAAC3S,IAAI;UACnCmY,SAAS,EAAE,IAAI,CAACxF,gBAAgB,CAACwF,SAAS;UAC1C2B,aAAa,EAAE,IAAI,CAACuR;QACxB,CAAC,GACC,IAAI;QACV4B,GAAG,CAAClgB,QAAQ,GAAG,IAAI,CAACA,QAAQ;QAC5BkgB,GAAG,CAAC9Q,QAAQ,GAAG,IAAI,CAACA,QAAQ;QAC5B8Q,GAAG,CAAC1e,cAAc,GACd,OAAOA,cAAc,KAAK,QAAQ,IAAIA,cAAc,GAC9CA,cAAc,GACdnJ,2EAAoB,CAACmJ,cAAc,CAAC;QAC9C0e,GAAG,CAAChc,iBAAiB,GAAG,IAAI,CAACA,iBAAiB;QAC9Cgc,GAAG,CAACvT,YAAY,GAAG,IAAI,CAACA,YAAY;QACpCuT,GAAG,CACE1Z,mBAAmB,CAAC,IAAI,CAACwZ,mBAAmB,CAAC,CAAC,CAAC,CAC/Cna,uBAAuB,CAACsD,WAAW,CAAC,CACpCzD,mBAAmB,CAACkH,OAAO,CAAC,CAC5B7E,oBAAoB,CAAC,IAAI,CAACmJ,gBAAgB,IAAI,QAAQ,CAAC;QAC5D,IAAIiP,GAAG,EAAE;UACLD,GAAG,CAACxY,aAAa,CAACyY,GAAG,CAAC9pB,KAAK,CAAC;QAChC;MACJ;IACJ,CAAC,CAAC;IACF;IACA6pB,GAAG,CAACze,aAAa,CAACtR,IAAI,CAAC4I,qDAAI,CAAC,CAAC,CAAC,CAAC,CAAC6N,SAAS,CAAC,MAAM;MAC5C;MACA,IAAI,IAAI,CAAC4V,WAAW,EAAE;QAClB0D,GAAG,CAACpb,UAAU,CAAC,IAAI,CAAC0X,WAAW,CAAC6B,QAAQ,CAAC;QACzC;MACJ;MACA;MACA;MACA,IAAIvX,MAAM,GAAG,IAAI,CAACjN,OAAO,CAACqf,aAAa,CAACvE,aAAa;MACrD,OAAO7N,MAAM,EAAE;QACX,IAAIA,MAAM,CAACwG,SAAS,CAACnP,QAAQ,CAAC+f,eAAe,CAAC,EAAE;UAC5CgC,GAAG,CAACpb,UAAU,CAACsZ,OAAO,CAACnE,cAAc,CAAC9e,IAAI,CAAC8X,IAAI,IAAI;YAC/C,OAAOA,IAAI,CAACpZ,OAAO,CAACqf,aAAa,KAAKpS,MAAM;UAChD,CAAC,CAAC,EAAEuX,QAAQ,IAAI,IAAI,CAAC;UACrB;QACJ;QACAvX,MAAM,GAAGA,MAAM,CAAC6N,aAAa;MACjC;IACJ,CAAC,CAAC;EACN;EACA;EACAuK,aAAaA,CAACgB,GAAG,EAAE;IACfA,GAAG,CAACxe,OAAO,CAACkF,SAAS,CAACwZ,UAAU,IAAI;MAChC,IAAI,CAAC1e,OAAO,CAAC2e,IAAI,CAAC;QAAEhnB,MAAM,EAAE,IAAI;QAAEqE,KAAK,EAAE0iB,UAAU,CAAC1iB;MAAM,CAAC,CAAC;MAC5D;MACA;MACA,IAAI,CAAC+gB,kBAAkB,CAAC6B,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;IACFJ,GAAG,CAACve,QAAQ,CAACiF,SAAS,CAAC2Z,YAAY,IAAI;MACnC,IAAI,CAAC5e,QAAQ,CAAC0e,IAAI,CAAC;QAAEhnB,MAAM,EAAE,IAAI;QAAEqE,KAAK,EAAE6iB,YAAY,CAAC7iB;MAAM,CAAC,CAAC;IACnE,CAAC,CAAC;IACFwiB,GAAG,CAACte,KAAK,CAACgF,SAAS,CAAC4Z,QAAQ,IAAI;MAC5B,IAAI,CAAC5e,KAAK,CAACye,IAAI,CAAC;QACZhnB,MAAM,EAAE,IAAI;QACZkL,QAAQ,EAAEic,QAAQ,CAACjc,QAAQ;QAC3BuE,SAAS,EAAE0X,QAAQ,CAAC1X,SAAS;QAC7BpL,KAAK,EAAE8iB,QAAQ,CAAC9iB;MACpB,CAAC,CAAC;MACF;MACA;MACA,IAAI,CAAC+gB,kBAAkB,CAAC6B,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;IACFJ,GAAG,CAACre,OAAO,CAAC+E,SAAS,CAAC6Z,UAAU,IAAI;MAChC,IAAI,CAAC5e,OAAO,CAACwe,IAAI,CAAC;QACdjd,SAAS,EAAEqd,UAAU,CAACrd,SAAS,CAACnQ,IAAI;QACpC2Y,IAAI,EAAE,IAAI;QACVH,YAAY,EAAEgV,UAAU,CAAChV;MAC7B,CAAC,CAAC;IACN,CAAC,CAAC;IACFyU,GAAG,CAACpe,MAAM,CAAC8E,SAAS,CAAC8Z,SAAS,IAAI;MAC9B,IAAI,CAAC5e,MAAM,CAACue,IAAI,CAAC;QACbjd,SAAS,EAAEsd,SAAS,CAACtd,SAAS,CAACnQ,IAAI;QACnC2Y,IAAI,EAAE;MACV,CAAC,CAAC;IACN,CAAC,CAAC;IACFsU,GAAG,CAACne,OAAO,CAAC6E,SAAS,CAAC+Z,SAAS,IAAI;MAC/B,IAAI,CAAC5e,OAAO,CAACse,IAAI,CAAC;QACdxU,aAAa,EAAE8U,SAAS,CAAC9U,aAAa;QACtCJ,YAAY,EAAEkV,SAAS,CAAClV,YAAY;QACpCK,iBAAiB,EAAE6U,SAAS,CAAC7U,iBAAiB,CAAC7Y,IAAI;QACnDmQ,SAAS,EAAEud,SAAS,CAACvd,SAAS,CAACnQ,IAAI;QACnCyY,sBAAsB,EAAEiV,SAAS,CAACjV,sBAAsB;QACxDE,IAAI,EAAE,IAAI;QACVrH,QAAQ,EAAEoc,SAAS,CAACpc,QAAQ;QAC5BuE,SAAS,EAAE6X,SAAS,CAAC7X,SAAS;QAC9BpL,KAAK,EAAEijB,SAAS,CAACjjB;MACrB,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;EACAohB,eAAeA,CAAClE,MAAM,EAAE;IACpB,MAAM;MAAExL,QAAQ;MAAE5N,cAAc;MAAE0C,iBAAiB;MAAEyI,YAAY;MAAElG,eAAe;MAAEma,gBAAgB;MAAEd,mBAAmB;MAAE5O;IAAkB,CAAC,GAAG0J,MAAM;IACvJ,IAAI,CAAC5a,QAAQ,GAAG4gB,gBAAgB,IAAI,IAAI,GAAG,KAAK,GAAGA,gBAAgB;IACnE,IAAI,CAACpf,cAAc,GAAGA,cAAc,IAAI,CAAC;IACzC,IAAI4N,QAAQ,EAAE;MACV,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IAC5B;IACA,IAAIlL,iBAAiB,EAAE;MACnB,IAAI,CAACA,iBAAiB,GAAGA,iBAAiB;IAC9C;IACA,IAAIyI,YAAY,EAAE;MACd,IAAI,CAACA,YAAY,GAAGA,YAAY;IACpC;IACA,IAAIlG,eAAe,EAAE;MACjB,IAAI,CAACA,eAAe,GAAGA,eAAe;IAC1C;IACA,IAAIqZ,mBAAmB,EAAE;MACrB,IAAI,CAACA,mBAAmB,GAAGA,mBAAmB;IAClD;IACA,IAAI5O,gBAAgB,EAAE;MAClB,IAAI,CAACA,gBAAgB,GAAGA,gBAAgB;IAC5C;EACJ;EACA;EACAoO,qBAAqBA,CAAA,EAAG;IACpB;IACA,IAAI,CAACjf,QAAQ,CAACof,OAAO,CAChBtvB,IAAI,CAAC6I,0DAAS,CAAC,IAAI,CAACqH,QAAQ,CAAC;IAClC;IACA3N,oDAAG,CAAE6S,OAAO,IAAK;MACb,MAAMsb,mBAAmB,GAAGtb,OAAO,CAC9BsS,MAAM,CAACvX,MAAM,IAAIA,MAAM,CAACkc,WAAW,KAAK,IAAI,CAAC,CAC7ClvB,GAAG,CAACgT,MAAM,IAAIA,MAAM,CAACzG,OAAO,CAAC;MAClC;MACA;MACA;MACA,IAAI,IAAI,CAAC6kB,WAAW,IAAI,IAAI,CAACoB,mBAAmB,EAAE;QAC9Ce,mBAAmB,CAAC/vB,IAAI,CAAC,IAAI,CAAC+I,OAAO,CAAC;MAC1C;MACA,IAAI,CAACwkB,QAAQ,CAAC/Y,WAAW,CAACub,mBAAmB,CAAC;IAClD,CAAC,CAAC;IACF;IACA5nB,0DAAS,CAAEsM,OAAO,IAAK;MACnB,OAAO1M,2CAAK,CAAC,GAAG0M,OAAO,CAACjY,GAAG,CAACse,IAAI,IAAI;QAChC,OAAOA,IAAI,CAAC0Q,aAAa,CAACnsB,IAAI,CAAC6I,0DAAS,CAAC4S,IAAI,CAAC,CAAC;MACnD,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,EAAE9S,yDAAS,CAAC,IAAI,CAAC6lB,UAAU,CAAC,CAAC,CAC1B/X,SAAS,CAACka,cAAc,IAAI;MAC7B;MACA,MAAMC,OAAO,GAAG,IAAI,CAAC1C,QAAQ;MAC7B,MAAM/d,MAAM,GAAGwgB,cAAc,CAACjnB,OAAO,CAACqf,aAAa;MACnD4H,cAAc,CAAC9gB,QAAQ,GAAG+gB,OAAO,CAACvZ,aAAa,CAAClH,MAAM,CAAC,GAAGygB,OAAO,CAACtZ,YAAY,CAACnH,MAAM,CAAC;IAC1F,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAAC/N,IAAI,YAAAyuB,gBAAA7F,CAAA;MAAA,YAAAA,CAAA,IAAwFiD,OAAO,EA7iBjBrpB,gEAAE,CA6iBiCA,sDAAa,GA7iBhDA,gEAAE,CA6iB2DopB,aAAa,OA7iB1EppB,gEAAE,CA6iBqH+C,sDAAQ,GA7iB/H/C,gEAAE,CA6iB0IA,kDAAS,GA7iBrJA,gEAAE,CA6iBgKA,4DAAmB,GA7iBrLA,gEAAE,CA6iBgMkpB,eAAe,MA7iBjNlpB,gEAAE,CA6iB4OmE,8DAAmB,MA7iBjQnE,gEAAE,CA6iB4R8mB,QAAQ,GA7iBtS9mB,gEAAE,CA6iBiTA,6DAAoB,GA7iBvUA,gEAAE,CA6iBkVqnB,eAAe,OA7iBnWrnB,gEAAE,CA6iB0YmnB,eAAe;IAAA,CAA4E;EAAE;EACzkB;IAAS,IAAI,CAACU,IAAI,kBA9iB8E7nB,gEAAE;MAAAiB,IAAA,EA8iBJooB,OAAO;MAAAtB,SAAA;MAAAsE,cAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA,EAAAC,QAAA;QAAA,IAAAF,EAAA;UA9iBLvsB,6DAAE,CAAAysB,QAAA,EA8iBokC3D,gBAAgB;UA9iBtlC9oB,6DAAE,CAAAysB,QAAA,EA8iB2qChE,oBAAoB;UA9iBjsCzoB,6DAAE,CAAAysB,QAAA,EA8iB6vCpF,eAAe;QAAA;QAAA,IAAAkF,EAAA;UAAA,IAAAI,EAAA;UA9iB9wC3sB,6DAAE,CAAA2sB,EAAA,GAAF3sB,0DAAE,QAAAwsB,GAAA,CAAA3b,gBAAA,GAAA8b,EAAA,CAAAG,KAAA;UAAF9sB,6DAAE,CAAA2sB,EAAA,GAAF3sB,0DAAE,QAAAwsB,GAAA,CAAAzb,oBAAA,GAAA4b,EAAA,CAAAG,KAAA;UAAF9sB,6DAAE,CAAA2sB,EAAA,GAAF3sB,0DAAE,QAAAwsB,GAAA,CAAAlhB,QAAA,GAAAqhB,EAAA;QAAA;MAAA;MAAA3E,SAAA;MAAA+E,QAAA;MAAAC,YAAA,WAAAC,qBAAAV,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFvsB,0DAAE,sBAAAwsB,GAAA,CAAAvhB,QAAA,uBAAAuhB,GAAA,CAAAlD,QAAA,CAAA/a,UAAA;QAAA;MAAA;MAAA0Z,MAAA;QAAA/pB,IAAA;QAAAmc,QAAA;QAAA0Q,mBAAA;QAAArZ,eAAA;QAAAjF,cAAA;QAAA+d,gBAAA;QAAAvf,QAAA;QAAAkE,iBAAA;QAAAyI,YAAA;QAAAuE,gBAAA;MAAA;MAAAgR,OAAA;QAAAxgB,OAAA;QAAAC,QAAA;QAAAC,KAAA;QAAAC,OAAA;QAAAC,MAAA;QAAAC,OAAA;QAAAC,KAAA;MAAA;MAAAmgB,QAAA;MAAAlF,UAAA;MAAAC,QAAA,GAAFnoB,iEAAE,CA8iBw8B,CAAC;QAAEqoB,OAAO,EAAElB,eAAe;QAAEmB,WAAW,EAAEe;MAAQ,CAAC,CAAC,GA9iB9/BrpB,mEAAE;IAAA,EA8iB+1C;EAAE;AACv8C;AACA;EAAA,QAAAymB,SAAA,oBAAAA,SAAA,KAhjBoGzmB,gEAAE,CAgjBXqpB,OAAO,EAAc,CAAC;IACrGpoB,IAAI,EAAEoB,qDAAS;IACfskB,IAAI,EAAE,CAAC;MACCzc,QAAQ,EAAE,WAAW;MACrBkjB,QAAQ,EAAE,SAAS;MACnBlF,UAAU,EAAE,IAAI;MAChBK,IAAI,EAAE;QACF,OAAO,EAAEY,eAAe;QACxB,2BAA2B,EAAE,UAAU;QACvC,2BAA2B,EAAE;MACjC,CAAC;MACDX,SAAS,EAAE,CAAC;QAAEH,OAAO,EAAElB,eAAe;QAAEmB,WAAW,EAAEe;MAAQ,CAAC;IAClE,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEpoB,IAAI,EAAEjB,sDAAa4nB;IAAC,CAAC,EAAE;MAAE3mB,IAAI,EAAEoQ,SAAS;MAAEuV,UAAU,EAAE,CAAC;QACvF3lB,IAAI,EAAEkB,kDAAM;QACZwkB,IAAI,EAAE,CAACyC,aAAa;MACxB,CAAC,EAAE;QACCnoB,IAAI,EAAEqB,oDAAQA;MAClB,CAAC,EAAE;QACCrB,IAAI,EAAEsB,oDAAQA;MAClB,CAAC;IAAE,CAAC,EAAE;MAAEtB,IAAI,EAAEoQ,SAAS;MAAEuV,UAAU,EAAE,CAAC;QAClC3lB,IAAI,EAAEkB,kDAAM;QACZwkB,IAAI,EAAE,CAAC5jB,sDAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAE9B,IAAI,EAAEjB,kDAASqmB;IAAC,CAAC,EAAE;MAAEplB,IAAI,EAAEjB,4DAAmBksB;IAAC,CAAC,EAAE;MAAEjrB,IAAI,EAAEoQ,SAAS;MAAEuV,UAAU,EAAE,CAAC;QACtF3lB,IAAI,EAAEqB,oDAAQA;MAClB,CAAC,EAAE;QACCrB,IAAI,EAAEkB,kDAAM;QACZwkB,IAAI,EAAE,CAACuC,eAAe;MAC1B,CAAC;IAAE,CAAC,EAAE;MAAEjoB,IAAI,EAAEkD,8DAAmB;MAAEyiB,UAAU,EAAE,CAAC;QAC5C3lB,IAAI,EAAEqB,oDAAQA;MAClB,CAAC;IAAE,CAAC,EAAE;MAAErB,IAAI,EAAE6lB;IAAS,CAAC,EAAE;MAAE7lB,IAAI,EAAEjB,6DAAoBosB;IAAC,CAAC,EAAE;MAAEnrB,IAAI,EAAEqmB,aAAa;MAAEV,UAAU,EAAE,CAAC;QAC1F3lB,IAAI,EAAEqB,oDAAQA;MAClB,CAAC,EAAE;QACCrB,IAAI,EAAEyB,gDAAIA;MACd,CAAC,EAAE;QACCzB,IAAI,EAAEkB,kDAAM;QACZwkB,IAAI,EAAE,CAACU,eAAe;MAC1B,CAAC;IAAE,CAAC,EAAE;MAAEpmB,IAAI,EAAEooB,OAAO;MAAEzC,UAAU,EAAE,CAAC;QAChC3lB,IAAI,EAAEqB,oDAAQA;MAClB,CAAC,EAAE;QACCrB,IAAI,EAAEsB,oDAAQA;MAClB,CAAC,EAAE;QACCtB,IAAI,EAAEkB,kDAAM;QACZwkB,IAAI,EAAE,CAACQ,eAAe;MAC1B,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAE7b,QAAQ,EAAE,CAAC;MACvCrK,IAAI,EAAE0B,2DAAe;MACrBgkB,IAAI,EAAE,CAACU,eAAe,EAAE;QAAEiG,WAAW,EAAE;MAAK,CAAC;IACjD,CAAC,CAAC;IAAEzc,gBAAgB,EAAE,CAAC;MACnB5P,IAAI,EAAE2B,wDAAY;MAClB+jB,IAAI,EAAE,CAACmC,gBAAgB;IAC3B,CAAC,CAAC;IAAE/X,oBAAoB,EAAE,CAAC;MACvB9P,IAAI,EAAE2B,wDAAY;MAClB+jB,IAAI,EAAE,CAAC8B,oBAAoB;IAC/B,CAAC,CAAC;IAAEvqB,IAAI,EAAE,CAAC;MACP+C,IAAI,EAAEuB,iDAAK;MACXmkB,IAAI,EAAE,CAAC,aAAa;IACxB,CAAC,CAAC;IAAEtM,QAAQ,EAAE,CAAC;MACXpZ,IAAI,EAAEuB,iDAAK;MACXmkB,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEoE,mBAAmB,EAAE,CAAC;MACtB9pB,IAAI,EAAEuB,iDAAK;MACXmkB,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAEjV,eAAe,EAAE,CAAC;MAClBzQ,IAAI,EAAEuB,iDAAK;MACXmkB,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEla,cAAc,EAAE,CAAC;MACjBxL,IAAI,EAAEuB,iDAAK;MACXmkB,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAE6D,gBAAgB,EAAE,CAAC;MACnBvpB,IAAI,EAAEuB,iDAAK;MACXmkB,IAAI,EAAE,CAAC,yBAAyB;IACpC,CAAC,CAAC;IAAE1b,QAAQ,EAAE,CAAC;MACXhK,IAAI,EAAEuB,iDAAK;MACXmkB,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAExX,iBAAiB,EAAE,CAAC;MACpBlO,IAAI,EAAEuB,iDAAK;MACXmkB,IAAI,EAAE,CAAC,0BAA0B;IACrC,CAAC,CAAC;IAAE/O,YAAY,EAAE,CAAC;MACf3W,IAAI,EAAEuB,iDAAK;MACXmkB,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC,CAAC;IAAExK,gBAAgB,EAAE,CAAC;MACnBlb,IAAI,EAAEuB,iDAAK;MACXmkB,IAAI,EAAE,CAAC,yBAAyB;IACpC,CAAC,CAAC;IAAEha,OAAO,EAAE,CAAC;MACV1L,IAAI,EAAE4B,kDAAM;MACZ8jB,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC,CAAC;IAAE/Z,QAAQ,EAAE,CAAC;MACX3L,IAAI,EAAE4B,kDAAM;MACZ8jB,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAE9Z,KAAK,EAAE,CAAC;MACR5L,IAAI,EAAE4B,kDAAM;MACZ8jB,IAAI,EAAE,CAAC,cAAc;IACzB,CAAC,CAAC;IAAE7Z,OAAO,EAAE,CAAC;MACV7L,IAAI,EAAE4B,kDAAM;MACZ8jB,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC,CAAC;IAAE5Z,MAAM,EAAE,CAAC;MACT9L,IAAI,EAAE4B,kDAAM;MACZ8jB,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAE3Z,OAAO,EAAE,CAAC;MACV/L,IAAI,EAAE4B,kDAAM;MACZ8jB,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC,CAAC;IAAE1Z,KAAK,EAAE,CAAC;MACRhM,IAAI,EAAE4B,kDAAM;MACZ8jB,IAAI,EAAE,CAAC,cAAc;IACzB,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA,MAAM4G,mBAAmB,GAAG,IAAInrB,0DAAc,CAAC,kBAAkB,CAAC;AAClE;AACA;AACA;AACA;AACA;AACA;AACA,MAAMorB,gBAAgB,CAAC;EACnB70B,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAAC80B,MAAM,GAAG,IAAI1iB,GAAG,CAAC,CAAC;IACvB,IAAI,CAACG,SAAS,GAAG,KAAK;EAC1B;EACA;EACA,IAAID,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAAC3J,KAAK,EAAE;IAChB,IAAI,CAAC4J,SAAS,GAAG9H,4EAAqB,CAAC9B,KAAK,CAAC;EACjD;EACA2kB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACwH,MAAM,CAAClsB,KAAK,CAAC,CAAC;EACvB;EACA;IAAS,IAAI,CAAC/D,IAAI,YAAAkwB,yBAAAtH,CAAA;MAAA,YAAAA,CAAA,IAAwFoH,gBAAgB;IAAA,CAAmD;EAAE;EAC/K;IAAS,IAAI,CAAC3F,IAAI,kBAvrB8E7nB,gEAAE;MAAAiB,IAAA,EAurBJusB,gBAAgB;MAAAzF,SAAA;MAAAE,MAAA;QAAAhd,QAAA;MAAA;MAAAmiB,QAAA;MAAAlF,UAAA;MAAAC,QAAA,GAvrBdnoB,iEAAE,CAurB6I,CAAC;QAAEqoB,OAAO,EAAEkF,mBAAmB;QAAEjF,WAAW,EAAEkF;MAAiB,CAAC,CAAC;IAAA,EAAiD;EAAE;AACvW;AACA;EAAA,QAAA/G,SAAA,oBAAAA,SAAA,KAzrBoGzmB,gEAAE,CAyrBXwtB,gBAAgB,EAAc,CAAC;IAC9GvsB,IAAI,EAAEoB,qDAAS;IACfskB,IAAI,EAAE,CAAC;MACCzc,QAAQ,EAAE,oBAAoB;MAC9BkjB,QAAQ,EAAE,kBAAkB;MAC5BlF,UAAU,EAAE,IAAI;MAChBM,SAAS,EAAE,CAAC;QAAEH,OAAO,EAAEkF,mBAAmB;QAAEjF,WAAW,EAAEkF;MAAiB,CAAC;IAC/E,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEviB,QAAQ,EAAE,CAAC;MACzBhK,IAAI,EAAEuB,iDAAK;MACXmkB,IAAI,EAAE,CAAC,0BAA0B;IACrC,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,IAAIgH,gBAAgB,GAAG,CAAC;AACxB;AACA,MAAMC,WAAW,CAAC;EACd;EACA;IAAS,IAAI,CAACC,UAAU,GAAG,EAAE;EAAE;EAC/B;EACA,IAAI5iB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS,IAAK,CAAC,CAAC,IAAI,CAAC4iB,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC7iB,QAAS;EACpE;EACA,IAAIA,QAAQA,CAAC3J,KAAK,EAAE;IAChB;IACA;IACA;IACA;IACA,IAAI,CAAC0oB,YAAY,CAAC/e,QAAQ,GAAG,IAAI,CAACC,SAAS,GAAG9H,4EAAqB,CAAC9B,KAAK,CAAC;EAC9E;EACA3I,WAAWA,CAAA,CACX;EACAmM,OAAO,EAAE2kB,QAAQ,EAAEC,kBAAkB,EAAEqE,iBAAiB,EAAEvE,IAAI,EAAEsE,MAAM,EAAEjI,MAAM,EAAE;IAC5E,IAAI,CAAC/gB,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC4kB,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACqE,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACvE,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACsE,MAAM,GAAGA,MAAM;IACpB;IACA,IAAI,CAAClE,UAAU,GAAG,IAAIlmB,yCAAO,CAAC,CAAC;IAC/B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACsf,WAAW,GAAG,EAAE;IACrB;AACR;AACA;AACA;IACQ,IAAI,CAAC3kB,EAAE,GAAI,iBAAgBsvB,gBAAgB,EAAG,EAAC;IAC/C;AACR;AACA;AACA;IACQ,IAAI,CAACxM,cAAc,GAAG,MAAM,IAAI;IAChC;IACA,IAAI,CAACC,aAAa,GAAG,MAAM,IAAI;IAC/B;IACA,IAAI,CAACpU,OAAO,GAAG,IAAIvK,wDAAY,CAAC,CAAC;IACjC;AACR;AACA;IACQ,IAAI,CAACqK,OAAO,GAAG,IAAIrK,wDAAY,CAAC,CAAC;IACjC;AACR;AACA;AACA;IACQ,IAAI,CAACsK,MAAM,GAAG,IAAItK,wDAAY,CAAC,CAAC;IAChC;IACA,IAAI,CAAC4e,MAAM,GAAG,IAAI5e,wDAAY,CAAC,CAAC;IAChC;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACurB,cAAc,GAAG,IAAIjjB,GAAG,CAAC,CAAC;IAC/B,IAAI,OAAO0b,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/CW,iBAAiB,CAACtiB,OAAO,CAACqf,aAAa,EAAE,aAAa,CAAC;IAC3D;IACA,IAAI,CAAC6F,YAAY,GAAGP,QAAQ,CAACzC,cAAc,CAACliB,OAAO,CAAC;IACpD,IAAI,CAACklB,YAAY,CAAC9rB,IAAI,GAAG,IAAI;IAC7B,IAAI2nB,MAAM,EAAE;MACR,IAAI,CAACkE,eAAe,CAAClE,MAAM,CAAC;IAChC;IACA,IAAI,CAACmE,YAAY,CAAC7I,cAAc,GAAG,CAACjD,IAAI,EAAElH,IAAI,KAAK;MAC/C,OAAO,IAAI,CAACmK,cAAc,CAACjD,IAAI,CAAChgB,IAAI,EAAE8Y,IAAI,CAAC9Y,IAAI,CAAC;IACpD,CAAC;IACD,IAAI,CAAC8rB,YAAY,CAAC5I,aAAa,GAAG,CAACzmB,KAAK,EAAEujB,IAAI,EAAElH,IAAI,KAAK;MACrD,OAAO,IAAI,CAACoK,aAAa,CAACzmB,KAAK,EAAEujB,IAAI,CAAChgB,IAAI,EAAE8Y,IAAI,CAAC9Y,IAAI,CAAC;IAC1D,CAAC;IACD,IAAI,CAAC+vB,2BAA2B,CAAC,IAAI,CAACjE,YAAY,CAAC;IACnD,IAAI,CAACG,aAAa,CAAC,IAAI,CAACH,YAAY,CAAC;IACrC4D,WAAW,CAACC,UAAU,CAAC9xB,IAAI,CAAC,IAAI,CAAC;IACjC,IAAI+xB,MAAM,EAAE;MACRA,MAAM,CAACL,MAAM,CAAC/c,GAAG,CAAC,IAAI,CAAC;IAC3B;EACJ;EACA;EACAuZ,OAAOA,CAACpT,IAAI,EAAE;IACV,IAAI,CAACmX,cAAc,CAACtd,GAAG,CAACmG,IAAI,CAAC;IAC7B,IAAI,IAAI,CAACmT,YAAY,CAACzb,UAAU,CAAC,CAAC,EAAE;MAChC,IAAI,CAAC2f,iBAAiB,CAAC,CAAC;IAC5B;EACJ;EACA;EACApD,UAAUA,CAACjU,IAAI,EAAE;IACb,IAAI,CAACmX,cAAc,CAACpvB,MAAM,CAACiY,IAAI,CAAC;IAChC,IAAI,IAAI,CAACmT,YAAY,CAACzb,UAAU,CAAC,CAAC,EAAE;MAChC,IAAI,CAAC2f,iBAAiB,CAAC,CAAC;IAC5B;EACJ;EACA;EACAC,cAAcA,CAAA,EAAG;IACb,OAAOvxB,KAAK,CAACvE,IAAI,CAAC,IAAI,CAAC21B,cAAc,CAAC,CAAC1P,IAAI,CAAC,CAACgC,CAAC,EAAEC,CAAC,KAAK;MAClD,MAAM6N,gBAAgB,GAAG9N,CAAC,CAACgJ,QAAQ,CAC9BhZ,iBAAiB,CAAC,CAAC,CACnB+d,uBAAuB,CAAC9N,CAAC,CAAC+I,QAAQ,CAAChZ,iBAAiB,CAAC,CAAC,CAAC;MAC5D;MACA;MACA;MACA,OAAO8d,gBAAgB,GAAGE,IAAI,CAACC,2BAA2B,GAAG,CAAC,CAAC,GAAG,CAAC;IACvE,CAAC,CAAC;EACN;EACAtI,WAAWA,CAAA,EAAG;IACV,MAAMtrB,KAAK,GAAGizB,WAAW,CAACC,UAAU,CAACjoB,OAAO,CAAC,IAAI,CAAC;IAClD,IAAIjL,KAAK,GAAG,CAAC,CAAC,EAAE;MACZizB,WAAW,CAACC,UAAU,CAAClQ,MAAM,CAAChjB,KAAK,EAAE,CAAC,CAAC;IAC3C;IACA,IAAI,IAAI,CAACmzB,MAAM,EAAE;MACb,IAAI,CAACA,MAAM,CAACL,MAAM,CAAC7uB,MAAM,CAAC,IAAI,CAAC;IACnC;IACA,IAAI,CAACovB,cAAc,CAACzsB,KAAK,CAAC,CAAC;IAC3B,IAAI,CAACyoB,YAAY,CAAC/X,OAAO,CAAC,CAAC;IAC3B,IAAI,CAAC2X,UAAU,CAACtvB,IAAI,CAAC,CAAC;IACtB,IAAI,CAACsvB,UAAU,CAACrvB,QAAQ,CAAC,CAAC;EAC9B;EACA;EACA0zB,2BAA2BA,CAAC9C,GAAG,EAAE;IAC7B,IAAI,IAAI,CAAC3B,IAAI,EAAE;MACX,IAAI,CAACA,IAAI,CAAC5X,MAAM,CACXxW,IAAI,CAAC6I,0DAAS,CAAC,IAAI,CAACulB,IAAI,CAACloB,KAAK,CAAC,EAAEyC,yDAAS,CAAC,IAAI,CAAC6lB,UAAU,CAAC,CAAC,CAC5D/X,SAAS,CAACvQ,KAAK,IAAI6pB,GAAG,CAACxY,aAAa,CAACrR,KAAK,CAAC,CAAC;IACrD;IACA6pB,GAAG,CAACze,aAAa,CAACmF,SAAS,CAAC,MAAM;MAC9B,MAAM2M,QAAQ,GAAGjb,kEAAW,CAAC,IAAI,CAACyf,WAAW,CAAC,CAACzqB,GAAG,CAACye,IAAI,IAAI;QACvD,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;UAC1B,MAAMwX,qBAAqB,GAAGZ,WAAW,CAACC,UAAU,CAACznB,IAAI,CAACqoB,IAAI,IAAIA,IAAI,CAACpwB,EAAE,KAAK2Y,IAAI,CAAC;UACnF,IAAI,CAACwX,qBAAqB,KAAK,OAAO/H,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;YAC3EvtB,OAAO,CAACyC,IAAI,CAAE,2DAA0Dqb,IAAK,GAAE,CAAC;UACpF;UACA,OAAOwX,qBAAqB;QAChC;QACA,OAAOxX,IAAI;MACf,CAAC,CAAC;MACF,IAAI,IAAI,CAAC8W,MAAM,EAAE;QACb,IAAI,CAACA,MAAM,CAACL,MAAM,CAAC3xB,OAAO,CAACkb,IAAI,IAAI;UAC/B,IAAIwH,QAAQ,CAAC5Y,OAAO,CAACoR,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YAC/BwH,QAAQ,CAACziB,IAAI,CAACib,IAAI,CAAC;UACvB;QACJ,CAAC,CAAC;MACN;MACA;MACA;MACA,IAAI,CAAC,IAAI,CAAC0X,0BAA0B,EAAE;QAClC,MAAMC,iBAAiB,GAAG,IAAI,CAACZ,iBAAiB,CAC3Ca,2BAA2B,CAAC,IAAI,CAAC9pB,OAAO,CAAC,CACzCvM,GAAG,CAACs2B,UAAU,IAAIA,UAAU,CAACC,aAAa,CAAC,CAAC,CAAC3K,aAAa,CAAC;QAChE,IAAI,CAAC6F,YAAY,CAAC5H,qBAAqB,CAACuM,iBAAiB,CAAC;QAC1D;QACA;QACA,IAAI,CAACD,0BAA0B,GAAG,IAAI;MAC1C;MACAvD,GAAG,CAAClgB,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAC5BkgB,GAAG,CAAC9Q,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAC5B8Q,GAAG,CAAC5T,eAAe,GAAGnU,4EAAqB,CAAC,IAAI,CAACmU,eAAe,CAAC;MACjE4T,GAAG,CAAClK,kBAAkB,GAAG7d,4EAAqB,CAAC,IAAI,CAAC6d,kBAAkB,CAAC;MACvEkK,GAAG,CAACjK,cAAc,GAAG5d,2EAAoB,CAAC,IAAI,CAAC4d,cAAc,EAAE,CAAC,CAAC;MACjEiK,GAAG,CACEnI,WAAW,CAACxE,QAAQ,CAACsE,MAAM,CAAC9L,IAAI,IAAIA,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAC,CAACze,GAAG,CAACk2B,IAAI,IAAIA,IAAI,CAACzE,YAAY,CAAC,CAAC,CAC1F/G,eAAe,CAAC,IAAI,CAACjF,WAAW,CAAC;IAC1C,CAAC,CAAC;EACN;EACA;EACAmM,aAAaA,CAACgB,GAAG,EAAE;IACfA,GAAG,CAACze,aAAa,CAACmF,SAAS,CAAC,MAAM;MAC9B,IAAI,CAACqc,iBAAiB,CAAC,CAAC;MACxB,IAAI,CAACxE,kBAAkB,CAAC6B,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;IACFJ,GAAG,CAACre,OAAO,CAAC+E,SAAS,CAAClJ,KAAK,IAAI;MAC3B,IAAI,CAACmE,OAAO,CAACwe,IAAI,CAAC;QACdjd,SAAS,EAAE,IAAI;QACfwI,IAAI,EAAElO,KAAK,CAACkO,IAAI,CAAC3Y,IAAI;QACrBwY,YAAY,EAAE/N,KAAK,CAAC+N;MACxB,CAAC,CAAC;IACN,CAAC,CAAC;IACFyU,GAAG,CAACpe,MAAM,CAAC8E,SAAS,CAAClJ,KAAK,IAAI;MAC1B,IAAI,CAACoE,MAAM,CAACue,IAAI,CAAC;QACbjd,SAAS,EAAE,IAAI;QACfwI,IAAI,EAAElO,KAAK,CAACkO,IAAI,CAAC3Y;MACrB,CAAC,CAAC;MACF,IAAI,CAACwrB,kBAAkB,CAAC6B,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;IACFJ,GAAG,CAAC9J,MAAM,CAACxP,SAAS,CAAClJ,KAAK,IAAI;MAC1B,IAAI,CAAC0Y,MAAM,CAACiK,IAAI,CAAC;QACbxU,aAAa,EAAEnO,KAAK,CAACmO,aAAa;QAClCJ,YAAY,EAAE/N,KAAK,CAAC+N,YAAY;QAChCrI,SAAS,EAAE,IAAI;QACfwI,IAAI,EAAElO,KAAK,CAACkO,IAAI,CAAC3Y;MACrB,CAAC,CAAC;IACN,CAAC,CAAC;IACFitB,GAAG,CAACne,OAAO,CAAC6E,SAAS,CAAC+Z,SAAS,IAAI;MAC/B,IAAI,CAAC5e,OAAO,CAACse,IAAI,CAAC;QACdxU,aAAa,EAAE8U,SAAS,CAAC9U,aAAa;QACtCJ,YAAY,EAAEkV,SAAS,CAAClV,YAAY;QACpCK,iBAAiB,EAAE6U,SAAS,CAAC7U,iBAAiB,CAAC7Y,IAAI;QACnDmQ,SAAS,EAAEud,SAAS,CAACvd,SAAS,CAACnQ,IAAI;QACnC2Y,IAAI,EAAE+U,SAAS,CAAC/U,IAAI,CAAC3Y,IAAI;QACzByY,sBAAsB,EAAEiV,SAAS,CAACjV,sBAAsB;QACxDnH,QAAQ,EAAEoc,SAAS,CAACpc,QAAQ;QAC5BuE,SAAS,EAAE6X,SAAS,CAAC7X,SAAS;QAC9BpL,KAAK,EAAEijB,SAAS,CAACjjB;MACrB,CAAC,CAAC;MACF;MACA;MACA,IAAI,CAAC+gB,kBAAkB,CAAC6B,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;IACFznB,2CAAK,CAACqnB,GAAG,CAAC7J,gBAAgB,EAAE6J,GAAG,CAAC5J,gBAAgB,CAAC,CAAC1P,SAAS,CAAC,MAAM,IAAI,CAAC6X,kBAAkB,CAAC6B,YAAY,CAAC,CAAC,CAAC;EAC7G;EACA;EACAxB,eAAeA,CAAClE,MAAM,EAAE;IACpB,MAAM;MAAExL,QAAQ;MAAEwR,gBAAgB;MAAEtU,eAAe;MAAEwX,sBAAsB;MAAEC;IAAgB,CAAC,GAAGnJ,MAAM;IACvG,IAAI,CAAC5a,QAAQ,GAAG4gB,gBAAgB,IAAI,IAAI,GAAG,KAAK,GAAGA,gBAAgB;IACnE,IAAI,CAACtU,eAAe,GAAGA,eAAe,IAAI,IAAI,GAAG,KAAK,GAAGA,eAAe;IACxE,IAAI,CAAC0J,kBAAkB,GAAG8N,sBAAsB,IAAI,IAAI,GAAG,KAAK,GAAGA,sBAAsB;IACzF,IAAI,CAAC/Q,WAAW,GAAGgR,eAAe,IAAI,UAAU;IAChD,IAAI3U,QAAQ,EAAE;MACV,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IAC5B;EACJ;EACA;EACA6T,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAClE,YAAY,CAAC3L,SAAS,CAAC,IAAI,CAAC8P,cAAc,CAAC,CAAC,CAAC51B,GAAG,CAACse,IAAI,IAAIA,IAAI,CAACyS,QAAQ,CAAC,CAAC;EACjF;EACA;IAAS,IAAI,CAAC9rB,IAAI,YAAAyxB,oBAAA7I,CAAA;MAAA,YAAAA,CAAA,IAAwFwH,WAAW,EA/6BrB5tB,gEAAE,CA+6BqCA,sDAAa,GA/6BpDA,gEAAE,CA+6B+D8mB,QAAQ,GA/6BzE9mB,gEAAE,CA+6BoFA,6DAAoB,GA/6B1GA,gEAAE,CA+6BqHE,qEAAmB,GA/6B1IF,gEAAE,CA+6BqJmE,8DAAmB,MA/6B1KnE,gEAAE,CA+6BqMutB,mBAAmB,OA/6B1NvtB,gEAAE,CA+6BqQkpB,eAAe;IAAA,CAA4D;EAAE;EACpb;IAAS,IAAI,CAACrB,IAAI,kBAh7B8E7nB,gEAAE;MAAAiB,IAAA,EAg7BJ2sB,WAAW;MAAA7F,SAAA;MAAAC,SAAA;MAAA+E,QAAA;MAAAC,YAAA,WAAAmC,yBAAA5C,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAh7BTvsB,0DAAE,OAAAwsB,GAAA,CAAAnuB,EAAA;UAAF2B,0DAAE,2BAAAwsB,GAAA,CAAAvhB,QAAA,4BAAAuhB,GAAA,CAAAxC,YAAA,CAAAzb,UAAA,+BAAAie,GAAA,CAAAxC,YAAA,CAAAxb,WAAA;QAAA;MAAA;MAAAyZ,MAAA;QAAAjF,WAAA;QAAA9kB,IAAA;QAAA8f,WAAA;QAAA3f,EAAA;QAAAgc,QAAA;QAAApP,QAAA;QAAAsM,eAAA;QAAA4J,cAAA;QAAAC,aAAA;QAAAH,kBAAA;QAAAC,cAAA;MAAA;MAAAiM,OAAA;QAAAngB,OAAA;QAAAF,OAAA;QAAAC,MAAA;QAAAsU,MAAA;MAAA;MAAA+L,QAAA;MAAAlF,UAAA;MAAAC,QAAA,GAAFnoB,iEAAE,CAg7BmiC;MAC7nC;MACA;QAAEqoB,OAAO,EAAEkF,mBAAmB;QAAE8B,QAAQ,EAAEhe;MAAU,CAAC,EACrD;QAAEgX,OAAO,EAAEe,aAAa;QAAEd,WAAW,EAAEsF;MAAY,CAAC,CACvD;IAAA,EAA4C;EAAE;AACvD;AACA;EAAA,QAAAnH,SAAA,oBAAAA,SAAA,KAt7BoGzmB,gEAAE,CAs7BX4tB,WAAW,EAAc,CAAC;IACzG3sB,IAAI,EAAEoB,qDAAS;IACfskB,IAAI,EAAE,CAAC;MACCzc,QAAQ,EAAE,8BAA8B;MACxCkjB,QAAQ,EAAE,aAAa;MACvBlF,UAAU,EAAE,IAAI;MAChBM,SAAS,EAAE;MACP;MACA;QAAEH,OAAO,EAAEkF,mBAAmB;QAAE8B,QAAQ,EAAEhe;MAAU,CAAC,EACrD;QAAEgX,OAAO,EAAEe,aAAa;QAAEd,WAAW,EAAEsF;MAAY,CAAC,CACvD;MACDrF,IAAI,EAAE;QACF,OAAO,EAAE,eAAe;QACxB,WAAW,EAAE,IAAI;QACjB,gCAAgC,EAAE,UAAU;QAC5C,gCAAgC,EAAE,2BAA2B;QAC7D,iCAAiC,EAAE;MACvC;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEtnB,IAAI,EAAEjB,sDAAa4nB;IAAC,CAAC,EAAE;MAAE3mB,IAAI,EAAE6lB;IAAS,CAAC,EAAE;MAAE7lB,IAAI,EAAEjB,6DAAoBosB;IAAC,CAAC,EAAE;MAAEnrB,IAAI,EAAEf,qEAAmBgvB;IAAC,CAAC,EAAE;MAAEjuB,IAAI,EAAEkD,8DAAmB;MAAEyiB,UAAU,EAAE,CAAC;QACpL3lB,IAAI,EAAEqB,oDAAQA;MAClB,CAAC;IAAE,CAAC,EAAE;MAAErB,IAAI,EAAEusB,gBAAgB;MAAE5G,UAAU,EAAE,CAAC;QACzC3lB,IAAI,EAAEqB,oDAAQA;MAClB,CAAC,EAAE;QACCrB,IAAI,EAAEkB,kDAAM;QACZwkB,IAAI,EAAE,CAAC4G,mBAAmB;MAC9B,CAAC,EAAE;QACCtsB,IAAI,EAAEsB,oDAAQA;MAClB,CAAC;IAAE,CAAC,EAAE;MAAEtB,IAAI,EAAEoQ,SAAS;MAAEuV,UAAU,EAAE,CAAC;QAClC3lB,IAAI,EAAEqB,oDAAQA;MAClB,CAAC,EAAE;QACCrB,IAAI,EAAEkB,kDAAM;QACZwkB,IAAI,EAAE,CAACuC,eAAe;MAC1B,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC,EAAkB;IAAElG,WAAW,EAAE,CAAC;MAC1C/hB,IAAI,EAAEuB,iDAAK;MACXmkB,IAAI,EAAE,CAAC,wBAAwB;IACnC,CAAC,CAAC;IAAEzoB,IAAI,EAAE,CAAC;MACP+C,IAAI,EAAEuB,iDAAK;MACXmkB,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAE3I,WAAW,EAAE,CAAC;MACd/c,IAAI,EAAEuB,iDAAK;MACXmkB,IAAI,EAAE,CAAC,wBAAwB;IACnC,CAAC,CAAC;IAAEtoB,EAAE,EAAE,CAAC;MACL4C,IAAI,EAAEuB,iDAAKA;IACf,CAAC,CAAC;IAAE6X,QAAQ,EAAE,CAAC;MACXpZ,IAAI,EAAEuB,iDAAK;MACXmkB,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC,CAAC;IAAE1b,QAAQ,EAAE,CAAC;MACXhK,IAAI,EAAEuB,iDAAK;MACXmkB,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC,CAAC;IAAEpP,eAAe,EAAE,CAAC;MAClBtW,IAAI,EAAEuB,iDAAK;MACXmkB,IAAI,EAAE,CAAC,4BAA4B;IACvC,CAAC,CAAC;IAAExF,cAAc,EAAE,CAAC;MACjBlgB,IAAI,EAAEuB,iDAAK;MACXmkB,IAAI,EAAE,CAAC,2BAA2B;IACtC,CAAC,CAAC;IAAEvF,aAAa,EAAE,CAAC;MAChBngB,IAAI,EAAEuB,iDAAK;MACXmkB,IAAI,EAAE,CAAC,0BAA0B;IACrC,CAAC,CAAC;IAAE1F,kBAAkB,EAAE,CAAC;MACrBhgB,IAAI,EAAEuB,iDAAK;MACXmkB,IAAI,EAAE,CAAC,+BAA+B;IAC1C,CAAC,CAAC;IAAEzF,cAAc,EAAE,CAAC;MACjBjgB,IAAI,EAAEuB,iDAAK;MACXmkB,IAAI,EAAE,CAAC,2BAA2B;IACtC,CAAC,CAAC;IAAE3Z,OAAO,EAAE,CAAC;MACV/L,IAAI,EAAE4B,kDAAM;MACZ8jB,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAE7Z,OAAO,EAAE,CAAC;MACV7L,IAAI,EAAE4B,kDAAM;MACZ8jB,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAE5Z,MAAM,EAAE,CAAC;MACT9L,IAAI,EAAE4B,kDAAM;MACZ8jB,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC,CAAC;IAAEtF,MAAM,EAAE,CAAC;MACTpgB,IAAI,EAAE4B,kDAAM;MACZ8jB,IAAI,EAAE,CAAC,mBAAmB;IAC9B,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM2I,oBAAoB,GAAG,CACzB1B,WAAW,EACXJ,gBAAgB,EAChBnE,OAAO,EACP/B,aAAa,EACbyB,cAAc,EACdL,kBAAkB,CACrB;AACD,MAAM6G,cAAc,CAAC;EACjB;IAAS,IAAI,CAAC/xB,IAAI,YAAAgyB,uBAAApJ,CAAA;MAAA,YAAAA,CAAA,IAAwFmJ,cAAc;IAAA,CAAkD;EAAE;EAC5K;IAAS,IAAI,CAACE,IAAI,kBA/gC8EzvB,+DAAE;MAAAiB,IAAA,EA+gCSsuB;IAAc,EAU3F;EAAE;EAChC;IAAS,IAAI,CAACI,IAAI,kBA1hC8E3vB,+DAAE;MAAAwoB,SAAA,EA0hCoC,CAAC1B,QAAQ,CAAC;MAAA+I,OAAA,GAAY7sB,wEAAmB;IAAA,EAAI;EAAE;AACzL;AACA;EAAA,QAAAyjB,SAAA,oBAAAA,SAAA,KA5hCoGzmB,gEAAE,CA4hCXuvB,cAAc,EAAc,CAAC;IAC5GtuB,IAAI,EAAE6B,oDAAQ;IACd6jB,IAAI,EAAE,CAAC;MACCkJ,OAAO,EAAEP,oBAAoB;MAC7BQ,OAAO,EAAE,CAAC9sB,wEAAmB,EAAE,GAAGssB,oBAAoB,CAAC;MACvD9G,SAAS,EAAE,CAAC1B,QAAQ;IACxB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpkHA;AACA;AACA;AACA;AACA;AACA,IAAIiJ,UAAU;AACd,CAAC,UAAUA,UAAU,EAAE;EACnB;EACAA,UAAU,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAC/B;EACAA,UAAU,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAC/B;EACAA,UAAU,CAAC,SAAS,CAAC,GAAG,SAAS;EACjC;EACAA,UAAU,CAAC,SAAS,CAAC,GAAG,SAAS;EACjC;EACAA,UAAU,CAAC,OAAO,CAAC,GAAG,OAAO;EAC7B;EACAA,UAAU,CAAC,QAAQ,CAAC,GAAG,QAAQ;AACnC,CAAC,EAAEA,UAAU,KAAKA,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,sBAAsB;AAC1B,CAAC,UAAUA,sBAAsB,EAAE;EAC/BA,sBAAsB,CAAC,sBAAsB,CAAC,GAAG,sBAAsB;EACvEA,sBAAsB,CAAC,QAAQ,CAAC,GAAG,QAAQ;AAC/C,CAAC,EAAEA,sBAAsB,KAAKA,sBAAsB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3D;AACA;AACA;AACA;AACA,IAAIC,OAAO;AACX,CAAC,UAAUA,OAAO,EAAE;EAChB;AACJ;AACA;EACIA,OAAO,CAAC,qBAAqB,CAAC,GAAG,qBAAqB;EACtD;AACJ;AACA;EACIA,OAAO,CAAC,YAAY,CAAC,GAAG,YAAY;EACpC;AACJ;AACA;AACA;EACIA,OAAO,CAAC,gBAAgB,CAAC,GAAG,gBAAgB;EAC5C;AACJ;AACA;AACA;EACIA,OAAO,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;AACtE,CAAC,EAAEA,OAAO,KAAKA,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;;AAE7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,CAAC;AAC9D;AACA;AACA;AACA;AACA,IAAIC,YAAY;AAChB,CAAC,UAAUA,YAAY,EAAE;EACrBA,YAAY,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACvEA,YAAY,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACvEA,YAAY,CAAC,iCAAiC,CAAC,GAAG,iCAAiC;EACnFA,YAAY,CAAC,0BAA0B,CAAC,GAAG,0BAA0B;EACrEA,YAAY,CAAC,iCAAiC,CAAC,GAAG,iCAAiC;EACnFA,YAAY,CAAC,+BAA+B,CAAC,GAAG,+BAA+B;AACnF,CAAC,EAAEA,YAAY,KAAKA,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;AACvC;AACA;AACA;AACA;AACA,IAAIC,kBAAkB;AACtB,CAAC,UAAUA,kBAAkB,EAAE;EAC3B;EACAA,kBAAkB,CAAC,kCAAkC,CAAC,GAAG,kCAAkC;EAC3F;EACAA,kBAAkB,CAAC,qBAAqB,CAAC,GAAG,qBAAqB;EACjE;EACAA,kBAAkB,CAAC,wBAAwB,CAAC,GAAG,wBAAwB;EACvE;EACAA,kBAAkB,CAAC,iBAAiB,CAAC,GAAG,iBAAiB;EACzD;EACAA,kBAAkB,CAAC,YAAY,CAAC,GAAG,YAAY;AACnD,CAAC,EAAEA,kBAAkB,KAAKA,kBAAkB,GAAG,CAAC,CAAC,CAAC,CAAC;AACnD;AACA;AACA;AACA;AACA,IAAIC,eAAe;AACnB,CAAC,UAAUA,eAAe,EAAE;EACxB;EACAA,eAAe,CAAC,8BAA8B,CAAC,GAAG,8BAA8B;EAChF;EACAA,eAAe,CAAC,YAAY,CAAC,GAAG,YAAY;EAC5C;EACAA,eAAe,CAAC,KAAK,CAAC,GAAG,KAAK;EAC9B;EACAA,eAAe,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACpC;EACAA,eAAe,CAAC,MAAM,CAAC,GAAG,MAAM;AACpC,CAAC,EAAEA,eAAe,KAAKA,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7C;AACA;AACA;AACA;AACA,IAAIC,WAAW;AACf,CAAC,UAAUA,WAAW,EAAE;EACpB;EACAA,WAAW,CAAC,4BAA4B,CAAC,GAAG,4BAA4B;EACxE;EACAA,WAAW,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAChC;EACAA,WAAW,CAAC,OAAO,CAAC,GAAG,OAAO;AAClC,CAAC,EAAEA,WAAW,KAAKA,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC;AACrC;AACA;AACA;AACA;AACA,IAAIC,YAAY;AAChB,CAAC,UAAUA,YAAY,EAAE;EACrB;EACAA,YAAY,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;EACvE;EACAA,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM;EAC7B;EACAA,YAAY,CAAC,YAAY,CAAC,GAAG,YAAY;EACzC;EACAA,YAAY,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACjC;EACAA,YAAY,CAAC,YAAY,CAAC,GAAG,YAAY;EACzC;EACAA,YAAY,CAAC,UAAU,CAAC,GAAG,UAAU;EACrC;EACAA,YAAY,CAAC,WAAW,CAAC,GAAG,WAAW;EACvC;EACAA,YAAY,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EACzD;EACAA,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM;EAC7B;EACAA,YAAY,CAAC,yBAAyB,CAAC,GAAG,yBAAyB;EACnE;EACAA,YAAY,CAAC,OAAO,CAAC,GAAG,OAAO;AACnC,CAAC,EAAEA,YAAY,KAAKA,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;AACvC;AACA;AACA;AACA;AACA,IAAIC,QAAQ;AACZ,CAAC,UAAUA,QAAQ,EAAE;EACjBA,QAAQ,CAAC,uBAAuB,CAAC,GAAG,uBAAuB;EAC3DA,QAAQ,CAAC,iBAAiB,CAAC,GAAG,iBAAiB;EAC/CA,QAAQ,CAAC,oBAAoB,CAAC,GAAG,oBAAoB;EACrDA,QAAQ,CAAC,qBAAqB,CAAC,GAAG,qBAAqB;EACvDA,QAAQ,CAAC,gBAAgB,CAAC,GAAG,gBAAgB;EAC7CA,QAAQ,CAAC,YAAY,CAAC,GAAG,YAAY;AACzC,CAAC,EAAEA,QAAQ,KAAKA,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/B;AACA;AACA;AACA,IAAIC,mBAAmB;AACvB,CAAC,UAAUA,mBAAmB,EAAE;EAC5B;EACAA,mBAAmB,CAAC,kBAAkB,CAAC,GAAG,kBAAkB;EAC5D;EACA;EACAA,mBAAmB,CAAC,MAAM,CAAC,GAAG,MAAM;EACpC;EACA;EACA;EACA;EACAA,mBAAmB,CAAC,KAAK,CAAC,GAAG,KAAK;EAClC;EACA;EACAA,mBAAmB,CAAC,MAAM,CAAC,GAAG,MAAM;AACxC,CAAC,EAAEA,mBAAmB,KAAKA,mBAAmB,GAAG,CAAC,CAAC,CAAC,CAAC;AACrD;AACA;AACA;AACA;AACA,IAAIC,oBAAoB;AACxB,CAAC,UAAUA,oBAAoB,EAAE;EAC7B;EACAA,oBAAoB,CAAC,kBAAkB,CAAC,GAAG,kBAAkB;EAC7D;EACAA,oBAAoB,CAAC,cAAc,CAAC,GAAG,cAAc;AACzD,CAAC,EAAEA,oBAAoB,KAAKA,oBAAoB,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,uBAAuB,SAAS5wB,KAAK,CAAC;EACxCpH,WAAWA,CAACiH,OAAO,EAAE;IACjB,KAAK,CAAE,+BAA8BA,OAAQ,EAAC,CAAC;EACnD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgxB,+BAA+B,SAASD,uBAAuB,CAAC;EAClEh4B,WAAWA,CAACiH,OAAO,EAAEpG,QAAQ,EAAE;IAC3B,KAAK,CAACoG,OAAO,CAAC;IACd,IAAI,CAACpG,QAAQ,GAAGA,QAAQ;EAC5B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,MAAMq3B,4BAA4B,SAASF,uBAAuB,CAAC;EAC/Dh4B,WAAWA,CAACiH,OAAO,EAAEpD,MAAM,EAAEqD,UAAU,EAAEixB,YAAY,EAAE;IACnD,KAAK,CAAClxB,OAAO,CAAC;IACd,IAAI,CAACpD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACqD,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACixB,YAAY,GAAGA,YAAY;EACpC;AACJ;AACA;AACA;AACA;AACA;AACA,MAAMC,mCAAmC,SAASJ,uBAAuB,CAAC;AAE1E;AACA;AACA;AACA;AACA;AACA,MAAMK,4BAA4B,SAASL,uBAAuB,CAAC;;AAGnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,gBAAgB,GAAG,2CAA2C;AACpE,MAAMC,mBAAmB,GAAG,QAAQ;AACpC;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAG,QAAQ;AAChC,MAAMC,kBAAkB,GAAG,UAAU;AACrC,IAAIC,IAAI;AACR,CAAC,UAAUA,IAAI,EAAE;EACbA,IAAI,CAAC,kBAAkB,CAAC,GAAG,iBAAiB;EAC5CA,IAAI,CAAC,yBAAyB,CAAC,GAAG,uBAAuB;EACzDA,IAAI,CAAC,cAAc,CAAC,GAAG,aAAa;EACpCA,IAAI,CAAC,eAAe,CAAC,GAAG,cAAc;EACtCA,IAAI,CAAC,sBAAsB,CAAC,GAAG,oBAAoB;AACvD,CAAC,EAAEA,IAAI,KAAKA,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACvB,MAAMC,UAAU,CAAC;EACb34B,WAAWA,CAACK,KAAK,EAAE6I,IAAI,EAAEhJ,MAAM,EAAE04B,MAAM,EAAEC,cAAc,EAAE;IACrD,IAAI,CAACx4B,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC6I,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAChJ,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC04B,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,cAAc,GAAGA,cAAc;EACxC;EACAC,QAAQA,CAAA,EAAG;IACP,IAAIC,EAAE,EAAEC,EAAE;IACV,MAAMC,UAAU,GAAG,CAAC,CAACF,EAAE,GAAG,IAAI,CAACF,cAAc,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,UAAU,KAAKV,mBAAmB;IACzH,MAAMW,OAAO,GAAG,CAAC,CAACF,EAAE,GAAG,IAAI,CAACH,cAAc,MAAM,IAAI,IAAIG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,OAAO,KAAKZ,gBAAgB;IAChH,IAAInxB,GAAG,GAAI,GAAE+xB,OAAQ,IAAGD,UAAW,IAAG,IAAI,CAAC54B,KAAM,IAAG,IAAI,CAAC6I,IAAK,EAAC;IAC/D,IAAI,IAAI,CAAC0vB,MAAM,EAAE;MACbzxB,GAAG,IAAI,UAAU;IACrB;IACA,OAAOA,GAAG;EACd;AACJ;AACA;AACA;AACA;AACA,SAASgyB,gBAAgBA,CAACN,cAAc,EAAE;EACtC,MAAMO,aAAa,GAAG,EAAE;EACxB,IAAIP,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACQ,SAAS,EAAE;IAC1FD,aAAa,CAACh2B,IAAI,CAACy1B,cAAc,CAACQ,SAAS,CAAC;EAChD;EACAD,aAAa,CAACh2B,IAAI,CAAE,GAAEq1B,kBAAmB,IAAGD,eAAgB,EAAC,CAAC;EAC9D,OAAOY,aAAa,CAAC72B,IAAI,CAAC,GAAG,CAAC;AAClC;AAAC,SACc+2B,UAAUA,CAAAC,EAAA;EAAA,OAAAC,WAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,YAAA;EAAAA,WAAA,GAAAG,kKAAA,CAAzB,WAA0BxyB,GAAG,EAAE;IAC3B,IAAI4xB,EAAE;IACN,MAAMa,OAAO,GAAG,IAAIC,OAAO,CAAC,CAAC;IAC7BD,OAAO,CAACE,MAAM,CAAC,cAAc,EAAE,kBAAkB,CAAC;IAClDF,OAAO,CAACE,MAAM,CAAC,mBAAmB,EAAEX,gBAAgB,CAAChyB,GAAG,CAAC0xB,cAAc,CAAC,CAAC;IACzEe,OAAO,CAACE,MAAM,CAAC,gBAAgB,EAAE3yB,GAAG,CAACjH,MAAM,CAAC;IAC5C,IAAI65B,aAAa,GAAG,CAAChB,EAAE,GAAG5xB,GAAG,CAAC0xB,cAAc,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgB,aAAa;IACnG,IAAIA,aAAa,EAAE;MACf,IAAI,EAAEA,aAAa,YAAYF,OAAO,CAAC,EAAE;QACrC,IAAI;UACAE,aAAa,GAAG,IAAIF,OAAO,CAACE,aAAa,CAAC;QAC9C,CAAC,CACD,OAAO/M,CAAC,EAAE;UACN,MAAM,IAAIoL,mCAAmC,CAAE,yCAAwCt1B,IAAI,CAACk3B,SAAS,CAACD,aAAa,CAAE,gBAAe/M,CAAC,CAAC/lB,OAAQ,EAAC,CAAC;QACpJ;MACJ;MACA,KAAK,MAAM,CAACgzB,UAAU,EAAEC,WAAW,CAAC,IAAIH,aAAa,CAACI,OAAO,CAAC,CAAC,EAAE;QAC7D,IAAIF,UAAU,KAAK,gBAAgB,EAAE;UACjC,MAAM,IAAI7B,mCAAmC,CAAE,mCAAkC6B,UAAW,EAAC,CAAC;QAClG,CAAC,MACI,IAAIA,UAAU,KAAK,mBAAmB,EAAE;UACzC,MAAM,IAAI7B,mCAAmC,CAAE,eAAc6B,UAAW,4CAA2C,CAAC;QACxH;QACAL,OAAO,CAACE,MAAM,CAACG,UAAU,EAAEC,WAAW,CAAC;MAC3C;IACJ;IACA,OAAON,OAAO;EAClB,CAAC;EAAA,OAAAJ,WAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SACcU,qBAAqBA,CAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,sBAAA,CAAAlB,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAiB,uBAAA;EAAAA,sBAAA,GAAAhB,kKAAA,CAApC,WAAqCt5B,KAAK,EAAE6I,IAAI,EAAEhJ,MAAM,EAAE04B,MAAM,EAAE5c,IAAI,EAAE6c,cAAc,EAAE;IACpF,MAAM1xB,GAAG,GAAG,IAAIwxB,UAAU,CAACt4B,KAAK,EAAE6I,IAAI,EAAEhJ,MAAM,EAAE04B,MAAM,EAAEC,cAAc,CAAC;IACvE,OAAO;MACH1xB,GAAG,EAAEA,GAAG,CAAC2xB,QAAQ,CAAC,CAAC;MACnB8B,YAAY,EAAEC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEC,iBAAiB,CAAClC,cAAc,CAAC,CAAC,EAAE;QAAEmC,MAAM,EAAE,MAAM;QAAEpB,OAAO,QAAQN,UAAU,CAACnyB,GAAG,CAAC;QAAE6U;MAAK,CAAC;IAC9I,CAAC;EACL,CAAC;EAAA,OAAA2e,sBAAA,CAAAlB,KAAA,OAAAC,SAAA;AAAA;AAAA,SACcuB,gBAAgBA,CAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,IAAA;EAAA,OAAAC,iBAAA,CAAA9B,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAA6B,kBAAA;EAAAA,iBAAA,GAAA5B,kKAAA,CAA/B,WAAgCt5B,KAAK,EAAE6I,IAAI,EAAEhJ,MAAM,EAAE04B,MAAM,EAAE5c,IAAI,EAAE6c,cAAc,GAAG,CAAC,CAAC;EACtF;EACA2C,OAAO,GAAGC,KAAK,EAAE;IACb,MAAM;MAAEt0B,GAAG;MAAEyzB;IAAa,CAAC,SAASR,qBAAqB,CAAC/5B,KAAK,EAAE6I,IAAI,EAAEhJ,MAAM,EAAE04B,MAAM,EAAE5c,IAAI,EAAE6c,cAAc,CAAC;IAC5G,OAAO6C,WAAW,CAACv0B,GAAG,EAAEyzB,YAAY,EAAEY,OAAO,CAAC;EAClD,CAAC;EAAA,OAAAD,iBAAA,CAAA9B,KAAA,OAAAC,SAAA;AAAA;AAAA,SACcgC,WAAWA,CAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,YAAA,CAAApC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAmC,aAAA;EAAAA,YAAA,GAAAlC,kKAAA,CAA1B,WAA2BxyB,GAAG,EAAEyzB,YAAY,EAAEY,OAAO,GAAGC,KAAK,EAAE;IAC3D,IAAI56B,QAAQ;IACZ,IAAI;MACAA,QAAQ,SAAS26B,OAAO,CAACr0B,GAAG,EAAEyzB,YAAY,CAAC;IAC/C,CAAC,CACD,OAAO5N,CAAC,EAAE;MACN8O,mBAAmB,CAAC9O,CAAC,EAAE7lB,GAAG,CAAC;IAC/B;IACA,IAAI,CAACtG,QAAQ,CAACk7B,EAAE,EAAE;MACd,MAAMC,mBAAmB,CAACn7B,QAAQ,EAAEsG,GAAG,CAAC;IAC5C;IACA,OAAOtG,QAAQ;EACnB,CAAC;EAAA,OAAAg7B,YAAA,CAAApC,KAAA,OAAAC,SAAA;AAAA;AACD,SAASoC,mBAAmBA,CAAC9O,CAAC,EAAE7lB,GAAG,EAAE;EACjC,IAAI80B,GAAG,GAAGjP,CAAC;EACX,IAAIiP,GAAG,CAAC/5B,IAAI,KAAK,YAAY,EAAE;IAC3B+5B,GAAG,GAAG,IAAI5D,4BAA4B,CAAE,iCAAgClxB,GAAG,CAAC2xB,QAAQ,CAAC,CAAE,KAAI9L,CAAC,CAAC/lB,OAAQ,EAAC,CAAC;IACvGg1B,GAAG,CAACC,KAAK,GAAGlP,CAAC,CAACkP,KAAK;EACvB,CAAC,MACI,IAAI,EAAElP,CAAC,YAAYkL,4BAA4B,IAChDlL,CAAC,YAAYoL,mCAAmC,CAAC,EAAE;IACnD6D,GAAG,GAAG,IAAIjE,uBAAuB,CAAE,uBAAsB7wB,GAAG,CAAC2xB,QAAQ,CAAC,CAAE,KAAI9L,CAAC,CAAC/lB,OAAQ,EAAC,CAAC;IACxFg1B,GAAG,CAACC,KAAK,GAAGlP,CAAC,CAACkP,KAAK;EACvB;EACA,MAAMD,GAAG;AACb;AAAC,SACcD,mBAAmBA,CAAAG,IAAA,EAAAC,IAAA;EAAA,OAAAC,oBAAA,CAAA5C,KAAA,OAAAC,SAAA;AAAA;AAgBlC;AACA;AACA;AACA;AACA;AAJA,SAAA2C,qBAAA;EAAAA,oBAAA,GAAA1C,kKAAA,CAhBA,WAAmC94B,QAAQ,EAAEsG,GAAG,EAAE;IAC9C,IAAIF,OAAO,GAAG,EAAE;IAChB,IAAIkxB,YAAY;IAChB,IAAI;MACA,MAAMmE,IAAI,SAASz7B,QAAQ,CAACy7B,IAAI,CAAC,CAAC;MAClCr1B,OAAO,GAAGq1B,IAAI,CAAC77B,KAAK,CAACwG,OAAO;MAC5B,IAAIq1B,IAAI,CAAC77B,KAAK,CAAC87B,OAAO,EAAE;QACpBt1B,OAAO,IAAK,IAAGnE,IAAI,CAACk3B,SAAS,CAACsC,IAAI,CAAC77B,KAAK,CAAC87B,OAAO,CAAE,EAAC;QACnDpE,YAAY,GAAGmE,IAAI,CAAC77B,KAAK,CAAC87B,OAAO;MACrC;IACJ,CAAC,CACD,OAAOvP,CAAC,EAAE;MACN;IAAA;IAEJ,MAAM,IAAIkL,4BAA4B,CAAE,uBAAsB/wB,GAAG,CAAC2xB,QAAQ,CAAC,CAAE,MAAKj4B,QAAQ,CAACgD,MAAO,IAAGhD,QAAQ,CAACqG,UAAW,KAAID,OAAQ,EAAC,EAAEpG,QAAQ,CAACgD,MAAM,EAAEhD,QAAQ,CAACqG,UAAU,EAAEixB,YAAY,CAAC;EAC/L,CAAC;EAAA,OAAAkE,oBAAA,CAAA5C,KAAA,OAAAC,SAAA;AAAA;AAMD,SAASqB,iBAAiBA,CAAClC,cAAc,EAAE;EACvC,MAAM+B,YAAY,GAAG,CAAC,CAAC;EACvB,IAAI,CAAC/B,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAAC2D,MAAM,MAAM9jB,SAAS,IAAI,CAACmgB,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACzwB,OAAO,KAAK,CAAC,EAAE;IACxM,MAAMq0B,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IACxC,IAAI,CAAC7D,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACzwB,OAAO,KAAK,CAAC,EAAE;MAC/F1G,UAAU,CAAC,MAAM+6B,UAAU,CAACE,KAAK,CAAC,CAAC,EAAE9D,cAAc,CAACzwB,OAAO,CAAC;IAChE;IACA,IAAIywB,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAAC2D,MAAM,EAAE;MACvF3D,cAAc,CAAC2D,MAAM,CAAChkB,gBAAgB,CAAC,OAAO,EAAE,MAAM;QAClDikB,UAAU,CAACE,KAAK,CAAC,CAAC;MACtB,CAAC,CAAC;IACN;IACA/B,YAAY,CAAC4B,MAAM,GAAGC,UAAU,CAACD,MAAM;EAC3C;EACA,OAAO5B,YAAY;AACvB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgC,UAAUA,CAAC/7B,QAAQ,EAAE;EAC1BA,QAAQ,CAACC,IAAI,GAAG,MAAM;IAClB,IAAID,QAAQ,CAACg8B,UAAU,IAAIh8B,QAAQ,CAACg8B,UAAU,CAAC/6B,MAAM,GAAG,CAAC,EAAE;MACvD,IAAIjB,QAAQ,CAACg8B,UAAU,CAAC/6B,MAAM,GAAG,CAAC,EAAE;QAChCvB,OAAO,CAACyC,IAAI,CAAE,qBAAoBnC,QAAQ,CAACg8B,UAAU,CAAC/6B,MAAO,GAAE,GAC1D,4DAA2D,GAC3D,kEAAiE,CAAC;MAC3E;MACA,IAAIg7B,kBAAkB,CAACj8B,QAAQ,CAACg8B,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;QAC5C,MAAM,IAAI5E,+BAA+B,CAAE,GAAE8E,uBAAuB,CAACl8B,QAAQ,CAAE,EAAC,EAAEA,QAAQ,CAAC;MAC/F;MACA,OAAOm8B,OAAO,CAACn8B,QAAQ,CAAC;IAC5B,CAAC,MACI,IAAIA,QAAQ,CAACo8B,cAAc,EAAE;MAC9B,MAAM,IAAIhF,+BAA+B,CAAE,uBAAsB8E,uBAAuB,CAACl8B,QAAQ,CAAE,EAAC,EAAEA,QAAQ,CAAC;IACnH;IACA,OAAO,EAAE;EACb,CAAC;EACD;AACJ;AACA;EACIA,QAAQ,CAACq8B,YAAY,GAAG,MAAM;IAC1B,IAAIr8B,QAAQ,CAACg8B,UAAU,IAAIh8B,QAAQ,CAACg8B,UAAU,CAAC/6B,MAAM,GAAG,CAAC,EAAE;MACvD,IAAIjB,QAAQ,CAACg8B,UAAU,CAAC/6B,MAAM,GAAG,CAAC,EAAE;QAChCvB,OAAO,CAACyC,IAAI,CAAE,qBAAoBnC,QAAQ,CAACg8B,UAAU,CAAC/6B,MAAO,GAAE,GAC1D,sEAAqE,GACrE,kEAAiE,CAAC;MAC3E;MACA,IAAIg7B,kBAAkB,CAACj8B,QAAQ,CAACg8B,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;QAC5C,MAAM,IAAI5E,+BAA+B,CAAE,GAAE8E,uBAAuB,CAACl8B,QAAQ,CAAE,EAAC,EAAEA,QAAQ,CAAC;MAC/F;MACAN,OAAO,CAACyC,IAAI,CAAE,yCAAwC,GACjD,uCAAsC,CAAC;MAC5C,OAAOm6B,gBAAgB,CAACt8B,QAAQ,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC,MACI,IAAIA,QAAQ,CAACo8B,cAAc,EAAE;MAC9B,MAAM,IAAIhF,+BAA+B,CAAE,gCAA+B8E,uBAAuB,CAACl8B,QAAQ,CAAE,EAAC,EAAEA,QAAQ,CAAC;IAC5H;IACA,OAAO6X,SAAS;EACpB,CAAC;EACD7X,QAAQ,CAACu8B,aAAa,GAAG,MAAM;IAC3B,IAAIv8B,QAAQ,CAACg8B,UAAU,IAAIh8B,QAAQ,CAACg8B,UAAU,CAAC/6B,MAAM,GAAG,CAAC,EAAE;MACvD,IAAIjB,QAAQ,CAACg8B,UAAU,CAAC/6B,MAAM,GAAG,CAAC,EAAE;QAChCvB,OAAO,CAACyC,IAAI,CAAE,qBAAoBnC,QAAQ,CAACg8B,UAAU,CAAC/6B,MAAO,GAAE,GAC1D,sEAAqE,GACrE,kEAAiE,CAAC;MAC3E;MACA,IAAIg7B,kBAAkB,CAACj8B,QAAQ,CAACg8B,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;QAC5C,MAAM,IAAI5E,+BAA+B,CAAE,GAAE8E,uBAAuB,CAACl8B,QAAQ,CAAE,EAAC,EAAEA,QAAQ,CAAC;MAC/F;MACA,OAAOs8B,gBAAgB,CAACt8B,QAAQ,CAAC;IACrC,CAAC,MACI,IAAIA,QAAQ,CAACo8B,cAAc,EAAE;MAC9B,MAAM,IAAIhF,+BAA+B,CAAE,gCAA+B8E,uBAAuB,CAACl8B,QAAQ,CAAE,EAAC,EAAEA,QAAQ,CAAC;IAC5H;IACA,OAAO6X,SAAS;EACpB,CAAC;EACD,OAAO7X,QAAQ;AACnB;AACA;AACA;AACA;AACA,SAASm8B,OAAOA,CAACn8B,QAAQ,EAAE;EACvB,IAAIk4B,EAAE,EAAEC,EAAE,EAAEqE,EAAE,EAAEC,EAAE;EAClB,MAAMC,WAAW,GAAG,EAAE;EACtB,IAAI,CAACvE,EAAE,GAAG,CAACD,EAAE,GAAGl4B,QAAQ,CAACg8B,UAAU,MAAM,IAAI,IAAI9D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,CAACyE,OAAO,MAAM,IAAI,IAAIxE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyE,KAAK,EAAE;IACpI,KAAK,MAAMzvB,IAAI,IAAI,CAACsvB,EAAE,GAAG,CAACD,EAAE,GAAGx8B,QAAQ,CAACg8B,UAAU,MAAM,IAAI,IAAIQ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,CAACG,OAAO,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,KAAK,EAAE;MACnJ,IAAIzvB,IAAI,CAAClN,IAAI,EAAE;QACXy8B,WAAW,CAACn6B,IAAI,CAAC4K,IAAI,CAAClN,IAAI,CAAC;MAC/B;MACA,IAAIkN,IAAI,CAAC0vB,cAAc,EAAE;QACrBH,WAAW,CAACn6B,IAAI,CAAC,OAAO,GACpB4K,IAAI,CAAC0vB,cAAc,CAACC,QAAQ,GAC5B,IAAI,GACJ3vB,IAAI,CAAC0vB,cAAc,CAACE,IAAI,GACxB,SAAS,CAAC;MAClB;MACA,IAAI5vB,IAAI,CAAC6vB,mBAAmB,EAAE;QAC1BN,WAAW,CAACn6B,IAAI,CAAC,SAAS,GAAG4K,IAAI,CAAC6vB,mBAAmB,CAACC,MAAM,GAAG,SAAS,CAAC;MAC7E;IACJ;EACJ;EACA,IAAIP,WAAW,CAACz7B,MAAM,GAAG,CAAC,EAAE;IACxB,OAAOy7B,WAAW,CAACh7B,IAAI,CAAC,EAAE,CAAC;EAC/B,CAAC,MACI;IACD,OAAO,EAAE;EACb;AACJ;AACA;AACA;AACA;AACA,SAAS46B,gBAAgBA,CAACt8B,QAAQ,EAAE;EAChC,IAAIk4B,EAAE,EAAEC,EAAE,EAAEqE,EAAE,EAAEC,EAAE;EAClB,MAAMF,aAAa,GAAG,EAAE;EACxB,IAAI,CAACpE,EAAE,GAAG,CAACD,EAAE,GAAGl4B,QAAQ,CAACg8B,UAAU,MAAM,IAAI,IAAI9D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,CAACyE,OAAO,MAAM,IAAI,IAAIxE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyE,KAAK,EAAE;IACpI,KAAK,MAAMzvB,IAAI,IAAI,CAACsvB,EAAE,GAAG,CAACD,EAAE,GAAGx8B,QAAQ,CAACg8B,UAAU,MAAM,IAAI,IAAIQ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,CAACG,OAAO,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,KAAK,EAAE;MACnJ,IAAIzvB,IAAI,CAACkvB,YAAY,EAAE;QACnBE,aAAa,CAACh6B,IAAI,CAAC4K,IAAI,CAACkvB,YAAY,CAAC;MACzC;IACJ;EACJ;EACA,IAAIE,aAAa,CAACt7B,MAAM,GAAG,CAAC,EAAE;IAC1B,OAAOs7B,aAAa;EACxB,CAAC,MACI;IACD,OAAO1kB,SAAS;EACpB;AACJ;AACA,MAAMqlB,gBAAgB,GAAG,CACrBnG,YAAY,CAACoG,UAAU,EACvBpG,YAAY,CAACqG,MAAM,EACnBrG,YAAY,CAACsG,QAAQ,CACxB;AACD,SAASpB,kBAAkBA,CAACqB,SAAS,EAAE;EACnC,OAAQ,CAAC,CAACA,SAAS,CAACC,YAAY,IAC5BL,gBAAgB,CAACz6B,QAAQ,CAAC66B,SAAS,CAACC,YAAY,CAAC;AACzD;AACA,SAASrB,uBAAuBA,CAACl8B,QAAQ,EAAE;EACvC,IAAIk4B,EAAE,EAAEC,EAAE,EAAEqE,EAAE;EACd,IAAIp2B,OAAO,GAAG,EAAE;EAChB,IAAI,CAAC,CAACpG,QAAQ,CAACg8B,UAAU,IAAIh8B,QAAQ,CAACg8B,UAAU,CAAC/6B,MAAM,KAAK,CAAC,KACzDjB,QAAQ,CAACo8B,cAAc,EAAE;IACzBh2B,OAAO,IAAI,sBAAsB;IACjC,IAAI,CAAC8xB,EAAE,GAAGl4B,QAAQ,CAACo8B,cAAc,MAAM,IAAI,IAAIlE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsF,WAAW,EAAE;MACpFp3B,OAAO,IAAK,WAAUpG,QAAQ,CAACo8B,cAAc,CAACoB,WAAY,EAAC;IAC/D;IACA,IAAI,CAACrF,EAAE,GAAGn4B,QAAQ,CAACo8B,cAAc,MAAM,IAAI,IAAIjE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsF,kBAAkB,EAAE;MAC3Fr3B,OAAO,IAAK,KAAIpG,QAAQ,CAACo8B,cAAc,CAACqB,kBAAmB,EAAC;IAChE;EACJ,CAAC,MACI,IAAI,CAACjB,EAAE,GAAGx8B,QAAQ,CAACg8B,UAAU,MAAM,IAAI,IAAIQ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,EAAE;IAC5E,MAAMkB,cAAc,GAAG19B,QAAQ,CAACg8B,UAAU,CAAC,CAAC,CAAC;IAC7C,IAAIC,kBAAkB,CAACyB,cAAc,CAAC,EAAE;MACpCt3B,OAAO,IAAK,gCAA+Bs3B,cAAc,CAACH,YAAa,EAAC;MACxE,IAAIG,cAAc,CAACC,aAAa,EAAE;QAC9Bv3B,OAAO,IAAK,KAAIs3B,cAAc,CAACC,aAAc,EAAC;MAClD;IACJ;EACJ;EACA,OAAOv3B,OAAO;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,SAASw3B,OAAOA,CAACC,CAAC,EAAE;EAChB,OAAO,IAAI,YAAYD,OAAO,IAAI,IAAI,CAACC,CAAC,GAAGA,CAAC,EAAE,IAAI,IAAI,IAAID,OAAO,CAACC,CAAC,CAAC;AACxE;AAEA,SAASC,gBAAgBA,CAACC,OAAO,EAAEC,UAAU,EAAEC,SAAS,EAAE;EACtD,IAAI,CAACC,MAAM,CAACC,aAAa,EAAE,MAAM,IAAIC,SAAS,CAAC,sCAAsC,CAAC;EACtF,IAAIC,CAAC,GAAGJ,SAAS,CAACrF,KAAK,CAACmF,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC;IAAE16B,CAAC;IAAEg7B,CAAC,GAAG,EAAE;EAC7D,OAAOh7B,CAAC,GAAG,CAAC,CAAC,EAAEi7B,IAAI,CAAC,MAAM,CAAC,EAAEA,IAAI,CAAC,OAAO,CAAC,EAAEA,IAAI,CAAC,QAAQ,CAAC,EAAEj7B,CAAC,CAAC46B,MAAM,CAACC,aAAa,CAAC,GAAG,YAAY;IAAE,OAAO,IAAI;EAAE,CAAC,EAAE76B,CAAC;EACrH,SAASi7B,IAAIA,CAACC,CAAC,EAAE;IAAE,IAAIH,CAAC,CAACG,CAAC,CAAC,EAAEl7B,CAAC,CAACk7B,CAAC,CAAC,GAAG,UAAUX,CAAC,EAAE;MAAE,OAAO,IAAI/9B,OAAO,CAAC,UAAUgnB,CAAC,EAAEC,CAAC,EAAE;QAAEuX,CAAC,CAAC/7B,IAAI,CAAC,CAACi8B,CAAC,EAAEX,CAAC,EAAE/W,CAAC,EAAEC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI0X,MAAM,CAACD,CAAC,EAAEX,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE,CAAC;EAAE;EACzI,SAASY,MAAMA,CAACD,CAAC,EAAEX,CAAC,EAAE;IAAE,IAAI;MAAEa,IAAI,CAACL,CAAC,CAACG,CAAC,CAAC,CAACX,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,OAAO1R,CAAC,EAAE;MAAEwS,MAAM,CAACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEnS,CAAC,CAAC;IAAE;EAAE;EACjF,SAASuS,IAAIA,CAACE,CAAC,EAAE;IAAEA,CAAC,CAAC92B,KAAK,YAAY81B,OAAO,GAAG99B,OAAO,CAACC,OAAO,CAAC6+B,CAAC,CAAC92B,KAAK,CAAC+1B,CAAC,CAAC,CAACzjB,IAAI,CAACykB,OAAO,EAAEC,MAAM,CAAC,GAAGH,MAAM,CAACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEM,CAAC,CAAC;EAAE;EACvH,SAASC,OAAOA,CAAC/2B,KAAK,EAAE;IAAE22B,MAAM,CAAC,MAAM,EAAE32B,KAAK,CAAC;EAAE;EACjD,SAASg3B,MAAMA,CAACh3B,KAAK,EAAE;IAAE22B,MAAM,CAAC,OAAO,EAAE32B,KAAK,CAAC;EAAE;EACjD,SAAS62B,MAAMA,CAACI,CAAC,EAAElB,CAAC,EAAE;IAAE,IAAIkB,CAAC,CAAClB,CAAC,CAAC,EAAES,CAAC,CAACU,KAAK,CAAC,CAAC,EAAEV,CAAC,CAACr9B,MAAM,EAAEw9B,MAAM,CAACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAAE;AACrF;AAEA,OAAOW,eAAe,KAAK,UAAU,GAAGA,eAAe,GAAG,UAAUr/B,KAAK,EAAEs/B,UAAU,EAAE94B,OAAO,EAAE;EAC5F,IAAI+lB,CAAC,GAAG,IAAI5lB,KAAK,CAACH,OAAO,CAAC;EAC1B,OAAO+lB,CAAC,CAAC9qB,IAAI,GAAG,iBAAiB,EAAE8qB,CAAC,CAACvsB,KAAK,GAAGA,KAAK,EAAEusB,CAAC,CAAC+S,UAAU,GAAGA,UAAU,EAAE/S,CAAC;AACpF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgT,cAAc,GAAG,oCAAoC;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACp/B,QAAQ,EAAE;EAC7B,MAAMq/B,WAAW,GAAGr/B,QAAQ,CAACmb,IAAI,CAACmkB,WAAW,CAAC,IAAIC,iBAAiB,CAAC,MAAM,EAAE;IAAEC,KAAK,EAAE;EAAK,CAAC,CAAC,CAAC;EAC7F,MAAMC,cAAc,GAAGC,iBAAiB,CAACL,WAAW,CAAC;EACrD,MAAM,CAACM,OAAO,EAAEC,OAAO,CAAC,GAAGH,cAAc,CAACI,GAAG,CAAC,CAAC;EAC/C,OAAO;IACH9H,MAAM,EAAE+H,wBAAwB,CAACH,OAAO,CAAC;IACzC3/B,QAAQ,EAAE+/B,kBAAkB,CAACH,OAAO;EACxC,CAAC;AACL;AAAC,SACcG,kBAAkBA,CAAAC,IAAA;EAAA,OAAAC,mBAAA,CAAArH,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAoH,oBAAA;EAAAA,mBAAA,GAAAnH,kKAAA,CAAjC,WAAkCf,MAAM,EAAE;IACtC,MAAMmI,YAAY,GAAG,EAAE;IACvB,MAAMC,MAAM,GAAGpI,MAAM,CAACqI,SAAS,CAAC,CAAC;IACjC,OAAO,IAAI,EAAE;MACT,MAAM;QAAEC,IAAI;QAAEv4B;MAAM,CAAC,SAASq4B,MAAM,CAACG,IAAI,CAAC,CAAC;MAC3C,IAAID,IAAI,EAAE;QACN,OAAOtE,UAAU,CAACwE,kBAAkB,CAACL,YAAY,CAAC,CAAC;MACvD;MACAA,YAAY,CAAC39B,IAAI,CAACuF,KAAK,CAAC;IAC5B;EACJ,CAAC;EAAA,OAAAm4B,mBAAA,CAAArH,KAAA,OAAAC,SAAA;AAAA;AACD,SAASiH,wBAAwBA,CAAC/H,MAAM,EAAE;EACtC,OAAO+F,gBAAgB,CAAC,IAAI,EAAEjF,SAAS,EAAE,UAAU2H,0BAA0BA,CAAA,EAAG;IAC5E,MAAML,MAAM,GAAGpI,MAAM,CAACqI,SAAS,CAAC,CAAC;IACjC,OAAO,IAAI,EAAE;MACT,MAAM;QAAEt4B,KAAK;QAAEu4B;MAAK,CAAC,GAAG,MAAMzC,OAAO,CAACuC,MAAM,CAACG,IAAI,CAAC,CAAC,CAAC;MACpD,IAAID,IAAI,EAAE;QACN;MACJ;MACA,MAAM,MAAMzC,OAAO,CAAC7B,UAAU,CAACj0B,KAAK,CAAC,CAAC;IAC1C;EACJ,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA,SAAS43B,iBAAiBA,CAACL,WAAW,EAAE;EACpC,MAAMc,MAAM,GAAGd,WAAW,CAACe,SAAS,CAAC,CAAC;EACtC,MAAMrI,MAAM,GAAG,IAAI0I,cAAc,CAAC;IAC9BllB,KAAKA,CAACqgB,UAAU,EAAE;MACd,IAAI8E,WAAW,GAAG,EAAE;MACpB,OAAOC,IAAI,CAAC,CAAC;MACb,SAASA,IAAIA,CAAA,EAAG;QACZ,OAAOR,MAAM,CACRG,IAAI,CAAC,CAAC,CACNlmB,IAAI,CAAC,CAAC;UAAEtS,KAAK;UAAEu4B;QAAK,CAAC,KAAK;UAC3B,IAAIA,IAAI,EAAE;YACN,IAAIK,WAAW,CAACtzB,IAAI,CAAC,CAAC,EAAE;cACpBwuB,UAAU,CAACh8B,KAAK,CAAC,IAAIu3B,uBAAuB,CAAC,wBAAwB,CAAC,CAAC;cACvE;YACJ;YACAyE,UAAU,CAACgF,KAAK,CAAC,CAAC;YAClB;UACJ;UACAF,WAAW,IAAI54B,KAAK;UACpB,IAAI9F,KAAK,GAAG0+B,WAAW,CAAC1+B,KAAK,CAACm9B,cAAc,CAAC;UAC7C,IAAI0B,cAAc;UAClB,OAAO7+B,KAAK,EAAE;YACV,IAAI;cACA6+B,cAAc,GAAG5+B,IAAI,CAACC,KAAK,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC;YACzC,CAAC,CACD,OAAOmqB,CAAC,EAAE;cACNyP,UAAU,CAACh8B,KAAK,CAAC,IAAIu3B,uBAAuB,CAAE,iCAAgCn1B,KAAK,CAAC,CAAC,CAAE,GAAE,CAAC,CAAC;cAC3F;YACJ;YACA45B,UAAU,CAACkF,OAAO,CAACD,cAAc,CAAC;YAClCH,WAAW,GAAGA,WAAW,CAACK,SAAS,CAAC/+B,KAAK,CAAC,CAAC,CAAC,CAACf,MAAM,CAAC;YACpDe,KAAK,GAAG0+B,WAAW,CAAC1+B,KAAK,CAACm9B,cAAc,CAAC;UAC7C;UACA,OAAOwB,IAAI,CAAC,CAAC;QACjB,CAAC,CAAC,CACGK,KAAK,CAAE7U,CAAC,IAAK;UACd,IAAIiP,GAAG,GAAGjP,CAAC;UACXiP,GAAG,CAACC,KAAK,GAAGlP,CAAC,CAACkP,KAAK;UACnB,IAAID,GAAG,CAAC/5B,IAAI,KAAK,YAAY,EAAE;YAC3B+5B,GAAG,GAAG,IAAI5D,4BAA4B,CAAC,8CAA8C,CAAC;UAC1F,CAAC,MACI;YACD4D,GAAG,GAAG,IAAIjE,uBAAuB,CAAC,+BAA+B,CAAC;UACtE;UACA,MAAMiE,GAAG;QACb,CAAC,CAAC;MACN;IACJ;EACJ,CAAC,CAAC;EACF,OAAOrD,MAAM;AACjB;AACA;AACA;AACA;AACA;AACA,SAASwI,kBAAkBA,CAACU,SAAS,EAAE;EACnC,MAAMC,YAAY,GAAGD,SAAS,CAACA,SAAS,CAAChgC,MAAM,GAAG,CAAC,CAAC;EACpD,MAAMkgC,kBAAkB,GAAG;IACvB/E,cAAc,EAAE8E,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC9E;EAC7F,CAAC;EACD,KAAK,MAAMp8B,QAAQ,IAAIihC,SAAS,EAAE;IAC9B,IAAIjhC,QAAQ,CAACg8B,UAAU,EAAE;MACrB,IAAIoF,cAAc,GAAG,CAAC;MACtB,KAAK,MAAM9D,SAAS,IAAIt9B,QAAQ,CAACg8B,UAAU,EAAE;QACzC,IAAI,CAACmF,kBAAkB,CAACnF,UAAU,EAAE;UAChCmF,kBAAkB,CAACnF,UAAU,GAAG,EAAE;QACtC;QACA,IAAI,CAACmF,kBAAkB,CAACnF,UAAU,CAACoF,cAAc,CAAC,EAAE;UAChDD,kBAAkB,CAACnF,UAAU,CAACoF,cAAc,CAAC,GAAG;YAC5CjgC,KAAK,EAAEigC;UACX,CAAC;QACL;QACA;QACAD,kBAAkB,CAACnF,UAAU,CAACoF,cAAc,CAAC,CAACC,gBAAgB,GAC1D/D,SAAS,CAAC+D,gBAAgB;QAC9BF,kBAAkB,CAACnF,UAAU,CAACoF,cAAc,CAAC,CAACE,iBAAiB,GAC3DhE,SAAS,CAACgE,iBAAiB;QAC/BH,kBAAkB,CAACnF,UAAU,CAACoF,cAAc,CAAC,CAAC7D,YAAY,GACtDD,SAAS,CAACC,YAAY;QAC1B4D,kBAAkB,CAACnF,UAAU,CAACoF,cAAc,CAAC,CAACzD,aAAa,GACvDL,SAAS,CAACK,aAAa;QAC3BwD,kBAAkB,CAACnF,UAAU,CAACoF,cAAc,CAAC,CAACG,aAAa,GACvDjE,SAAS,CAACiE,aAAa;QAC3B;AAChB;AACA;AACA;QACgB,IAAIjE,SAAS,CAACX,OAAO,IAAIW,SAAS,CAACX,OAAO,CAACC,KAAK,EAAE;UAC9C,IAAI,CAACuE,kBAAkB,CAACnF,UAAU,CAACoF,cAAc,CAAC,CAACzE,OAAO,EAAE;YACxDwE,kBAAkB,CAACnF,UAAU,CAACoF,cAAc,CAAC,CAACzE,OAAO,GAAG;cACpDl7B,IAAI,EAAE67B,SAAS,CAACX,OAAO,CAACl7B,IAAI,IAAI,MAAM;cACtCm7B,KAAK,EAAE;YACX,CAAC;UACL;UACA,MAAM4E,OAAO,GAAG,CAAC,CAAC;UAClB,KAAK,MAAMr0B,IAAI,IAAImwB,SAAS,CAACX,OAAO,CAACC,KAAK,EAAE;YACxC,IAAIzvB,IAAI,CAAClN,IAAI,EAAE;cACXuhC,OAAO,CAACvhC,IAAI,GAAGkN,IAAI,CAAClN,IAAI;YAC5B;YACA,IAAIkN,IAAI,CAACkvB,YAAY,EAAE;cACnBmF,OAAO,CAACnF,YAAY,GAAGlvB,IAAI,CAACkvB,YAAY;YAC5C;YACA,IAAIlvB,IAAI,CAAC0vB,cAAc,EAAE;cACrB2E,OAAO,CAAC3E,cAAc,GAAG1vB,IAAI,CAAC0vB,cAAc;YAChD;YACA,IAAI1vB,IAAI,CAAC6vB,mBAAmB,EAAE;cAC1BwE,OAAO,CAACxE,mBAAmB,GAAG7vB,IAAI,CAAC6vB,mBAAmB;YAC1D;YACA,IAAIhD,MAAM,CAACyH,IAAI,CAACD,OAAO,CAAC,CAACvgC,MAAM,KAAK,CAAC,EAAE;cACnCugC,OAAO,CAACvhC,IAAI,GAAG,EAAE;YACrB;YACAkhC,kBAAkB,CAACnF,UAAU,CAACoF,cAAc,CAAC,CAACzE,OAAO,CAACC,KAAK,CAACr6B,IAAI,CAACi/B,OAAO,CAAC;UAC7E;QACJ;MACJ;MACAJ,cAAc,EAAE;IACpB;IACA,IAAIphC,QAAQ,CAAC0hC,aAAa,EAAE;MACxBP,kBAAkB,CAACO,aAAa,GAAG1hC,QAAQ,CAAC0hC,aAAa;IAC7D;EACJ;EACA,OAAOP,kBAAkB;AAC7B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,SAgBeQ,qBAAqBA,CAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,sBAAA,CAAApJ,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAmJ,uBAAA;EAAAA,sBAAA,GAAAlJ,kKAAA,CAApC,WAAqCz5B,MAAM,EAAEG,KAAK,EAAEyiC,MAAM,EAAEjK,cAAc,EAAE;IACxE,MAAMh4B,QAAQ,SAASo6B,gBAAgB,CAAC56B,KAAK,EAAEq4B,IAAI,CAACqK,uBAAuB,EAAE7iC,MAAM,EACnF,YAAa,IAAI,EAAE4C,IAAI,CAACk3B,SAAS,CAAC8I,MAAM,CAAC,EAAEjK,cAAc,CAAC;IAC1D,OAAOoH,aAAa,CAACp/B,QAAQ,CAAC;EAClC,CAAC;EAAA,OAAAgiC,sBAAA,CAAApJ,KAAA,OAAAC,SAAA;AAAA;AAAA,SACch5B,eAAeA,CAAAsiC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,gBAAA,CAAA3J,KAAA,OAAAC,SAAA;AAAA;AAU9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,SAAA0J,iBAAA;EAAAA,gBAAA,GAAAzJ,kKAAA,CAVA,WAA+Bz5B,MAAM,EAAEG,KAAK,EAAEyiC,MAAM,EAAEjK,cAAc,EAAE;IAClE,MAAMh4B,QAAQ,SAASo6B,gBAAgB,CAAC56B,KAAK,EAAEq4B,IAAI,CAAC2K,gBAAgB,EAAEnjC,MAAM,EAC5E,YAAa,KAAK,EAAE4C,IAAI,CAACk3B,SAAS,CAAC8I,MAAM,CAAC,EAAEjK,cAAc,CAAC;IAC3D,MAAMyK,YAAY,SAASziC,QAAQ,CAACy7B,IAAI,CAAC,CAAC;IAC1C,MAAMiH,gBAAgB,GAAG3G,UAAU,CAAC0G,YAAY,CAAC;IACjD,OAAO;MACHziC,QAAQ,EAAE0iC;IACd,CAAC;EACL,CAAC;EAAA,OAAAH,gBAAA,CAAA3J,KAAA,OAAAC,SAAA;AAAA;AAkBD,SAAS8J,uBAAuBA,CAACC,KAAK,EAAE;EACpC;EACA,IAAIA,KAAK,IAAI,IAAI,EAAE;IACf,OAAO/qB,SAAS;EACpB,CAAC,MACI,IAAI,OAAO+qB,KAAK,KAAK,QAAQ,EAAE;IAChC,OAAO;MAAEnhC,IAAI,EAAE,QAAQ;MAAEm7B,KAAK,EAAE,CAAC;QAAE38B,IAAI,EAAE2iC;MAAM,CAAC;IAAE,CAAC;EACvD,CAAC,MACI,IAAIA,KAAK,CAAC3iC,IAAI,EAAE;IACjB,OAAO;MAAEwB,IAAI,EAAE,QAAQ;MAAEm7B,KAAK,EAAE,CAACgG,KAAK;IAAE,CAAC;EAC7C,CAAC,MACI,IAAIA,KAAK,CAAChG,KAAK,EAAE;IAClB,IAAI,CAACgG,KAAK,CAACnhC,IAAI,EAAE;MACb,OAAO;QAAEA,IAAI,EAAE,QAAQ;QAAEm7B,KAAK,EAAEgG,KAAK,CAAChG;MAAM,CAAC;IACjD,CAAC,MACI;MACD,OAAOgG,KAAK;IAChB;EACJ;AACJ;AACA,SAASC,gBAAgBA,CAACC,OAAO,EAAE;EAC/B,IAAIC,QAAQ,GAAG,EAAE;EACjB,IAAI,OAAOD,OAAO,KAAK,QAAQ,EAAE;IAC7BC,QAAQ,GAAG,CAAC;MAAE9iC,IAAI,EAAE6iC;IAAQ,CAAC,CAAC;EAClC,CAAC,MACI;IACD,KAAK,MAAME,YAAY,IAAIF,OAAO,EAAE;MAChC,IAAI,OAAOE,YAAY,KAAK,QAAQ,EAAE;QAClCD,QAAQ,CAACxgC,IAAI,CAAC;UAAEtC,IAAI,EAAE+iC;QAAa,CAAC,CAAC;MACzC,CAAC,MACI;QACDD,QAAQ,CAACxgC,IAAI,CAACygC,YAAY,CAAC;MAC/B;IACJ;EACJ;EACA,OAAOC,8CAA8C,CAACF,QAAQ,CAAC;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,8CAA8CA,CAACrG,KAAK,EAAE;EAC3D,MAAMsG,WAAW,GAAG;IAAEzhC,IAAI,EAAE,MAAM;IAAEm7B,KAAK,EAAE;EAAG,CAAC;EAC/C,MAAMuG,eAAe,GAAG;IAAE1hC,IAAI,EAAE,UAAU;IAAEm7B,KAAK,EAAE;EAAG,CAAC;EACvD,IAAIwG,cAAc,GAAG,KAAK;EAC1B,IAAIC,kBAAkB,GAAG,KAAK;EAC9B,KAAK,MAAMl2B,IAAI,IAAIyvB,KAAK,EAAE;IACtB,IAAI,kBAAkB,IAAIzvB,IAAI,EAAE;MAC5Bg2B,eAAe,CAACvG,KAAK,CAACr6B,IAAI,CAAC4K,IAAI,CAAC;MAChCk2B,kBAAkB,GAAG,IAAI;IAC7B,CAAC,MACI;MACDH,WAAW,CAACtG,KAAK,CAACr6B,IAAI,CAAC4K,IAAI,CAAC;MAC5Bi2B,cAAc,GAAG,IAAI;IACzB;EACJ;EACA,IAAIA,cAAc,IAAIC,kBAAkB,EAAE;IACtC,MAAM,IAAIlM,uBAAuB,CAAC,4HAA4H,CAAC;EACnK;EACA,IAAI,CAACiM,cAAc,IAAI,CAACC,kBAAkB,EAAE;IACxC,MAAM,IAAIlM,uBAAuB,CAAC,kDAAkD,CAAC;EACzF;EACA,IAAIiM,cAAc,EAAE;IAChB,OAAOF,WAAW;EACtB;EACA,OAAOC,eAAe;AAC1B;AACA,SAASG,sBAAsBA,CAACrB,MAAM,EAAEsB,WAAW,EAAE;EACjD,IAAIrL,EAAE;EACN,IAAIsL,+BAA+B,GAAG;IAClChkC,KAAK,EAAE+jC,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC/jC,KAAK;IAClFikC,gBAAgB,EAAEF,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACE,gBAAgB;IACxGC,cAAc,EAAEH,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACG,cAAc;IACpGC,KAAK,EAAEJ,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACI,KAAK;IAClFC,UAAU,EAAEL,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACK,UAAU;IAC5FC,iBAAiB,EAAEN,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACM,iBAAiB;IAC1GC,aAAa,EAAE,CAAC5L,EAAE,GAAGqL,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACO,aAAa,MAAM,IAAI,IAAI5L,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC72B,IAAI;IACtJ0iC,QAAQ,EAAE;EACd,CAAC;EACD,MAAMC,8BAA8B,GAAG/B,MAAM,CAACgC,sBAAsB,IAAI,IAAI;EAC5E,IAAIhC,MAAM,CAAC8B,QAAQ,EAAE;IACjB,IAAIC,8BAA8B,EAAE;MAChC,MAAM,IAAIzM,mCAAmC,CAAC,mFAAmF,CAAC;IACtI;IACAiM,+BAA+B,CAACO,QAAQ,GAAG9B,MAAM,CAAC8B,QAAQ;EAC9D,CAAC,MACI,IAAIC,8BAA8B,EAAE;IACrCR,+BAA+B,GAAGxJ,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEuJ,+BAA+B,CAAC,EAAEvB,MAAM,CAACgC,sBAAsB,CAAC;EACtI,CAAC,MACI;IACD;IACA,MAAMtH,OAAO,GAAGkG,gBAAgB,CAACZ,MAAM,CAAC;IACxCuB,+BAA+B,CAACO,QAAQ,GAAG,CAACpH,OAAO,CAAC;EACxD;EACA,OAAO;IAAEsH,sBAAsB,EAAET;EAAgC,CAAC;AACtE;AACA,SAASU,0BAA0BA,CAACjC,MAAM,EAAE;EACxC,IAAIkC,gBAAgB;EACpB,IAAIlC,MAAM,CAAC8B,QAAQ,EAAE;IACjBI,gBAAgB,GAAGlC,MAAM;EAC7B,CAAC,MACI;IACD;IACA,MAAMtF,OAAO,GAAGkG,gBAAgB,CAACZ,MAAM,CAAC;IACxCkC,gBAAgB,GAAG;MAAEJ,QAAQ,EAAE,CAACpH,OAAO;IAAE,CAAC;EAC9C;EACA,IAAIsF,MAAM,CAAC4B,iBAAiB,EAAE;IAC1BM,gBAAgB,CAACN,iBAAiB,GAAGlB,uBAAuB,CAACV,MAAM,CAAC4B,iBAAiB,CAAC;EAC1F;EACA,OAAOM,gBAAgB;AAC3B;AACA,SAASC,uBAAuBA,CAACnC,MAAM,EAAE;EACrC,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAI7+B,KAAK,CAAC6b,OAAO,CAACgjB,MAAM,CAAC,EAAE;IACrD,MAAMtF,OAAO,GAAGkG,gBAAgB,CAACZ,MAAM,CAAC;IACxC,OAAO;MAAEtF;IAAQ,CAAC;EACtB;EACA,OAAOsF,MAAM;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoC,iBAAiB,GAAG,CACtB,MAAM,EACN,YAAY,EACZ,cAAc,EACd,kBAAkB,EAClB,gBAAgB,EAChB,qBAAqB,CACxB;AACD,MAAMC,oBAAoB,GAAG;EACzBt+B,IAAI,EAAE,CAAC,MAAM,EAAE,YAAY,CAAC;EAC5Bu+B,QAAQ,EAAE,CAAC,kBAAkB,CAAC;EAC9B/kC,KAAK,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,gBAAgB,EAAE,qBAAqB,CAAC;EACxE;EACAglC,MAAM,EAAE,CAAC,MAAM;AACnB,CAAC;AACD,SAASC,mBAAmBA,CAACC,OAAO,EAAE;EAClC,IAAIC,WAAW,GAAG,KAAK;EACvB,KAAK,MAAMC,WAAW,IAAIF,OAAO,EAAE;IAC/B,MAAM;MAAEjjC,IAAI;MAAEm7B;IAAM,CAAC,GAAGgI,WAAW;IACnC,IAAI,CAACD,WAAW,IAAIljC,IAAI,KAAK,MAAM,EAAE;MACjC,MAAM,IAAI01B,uBAAuB,CAAE,iDAAgD11B,IAAK,EAAC,CAAC;IAC9F;IACA,IAAI,CAACi1B,cAAc,CAACj0B,QAAQ,CAAChB,IAAI,CAAC,EAAE;MAChC,MAAM,IAAI01B,uBAAuB,CAAE,4CAA2C11B,IAAK,yBAAwBQ,IAAI,CAACk3B,SAAS,CAACzC,cAAc,CAAE,EAAC,CAAC;IAChJ;IACA,IAAI,CAACtzB,KAAK,CAAC6b,OAAO,CAAC2d,KAAK,CAAC,EAAE;MACvB,MAAM,IAAIzF,uBAAuB,CAAC,6DAA6D,CAAC;IACpG;IACA,IAAIyF,KAAK,CAAC37B,MAAM,KAAK,CAAC,EAAE;MACpB,MAAM,IAAIk2B,uBAAuB,CAAC,4CAA4C,CAAC;IACnF;IACA,MAAM0N,WAAW,GAAG;MAChB5kC,IAAI,EAAE,CAAC;MACP6kC,UAAU,EAAE,CAAC;MACbzI,YAAY,EAAE,CAAC;MACf0I,gBAAgB,EAAE,CAAC;MACnBC,QAAQ,EAAE,CAAC;MACXnI,cAAc,EAAE,CAAC;MACjBG,mBAAmB,EAAE;IACzB,CAAC;IACD,KAAK,MAAM7vB,IAAI,IAAIyvB,KAAK,EAAE;MACtB,KAAK,MAAM5xB,GAAG,IAAIq5B,iBAAiB,EAAE;QACjC,IAAIr5B,GAAG,IAAImC,IAAI,EAAE;UACb03B,WAAW,CAAC75B,GAAG,CAAC,IAAI,CAAC;QACzB;MACJ;IACJ;IACA,MAAMi6B,UAAU,GAAGX,oBAAoB,CAAC7iC,IAAI,CAAC;IAC7C,KAAK,MAAMuJ,GAAG,IAAIq5B,iBAAiB,EAAE;MACjC,IAAI,CAACY,UAAU,CAACxiC,QAAQ,CAACuI,GAAG,CAAC,IAAI65B,WAAW,CAAC75B,GAAG,CAAC,GAAG,CAAC,EAAE;QACnD,MAAM,IAAImsB,uBAAuB,CAAE,sBAAqB11B,IAAK,oBAAmBuJ,GAAI,QAAO,CAAC;MAChG;IACJ;IACA25B,WAAW,GAAG,IAAI;EACtB;AACJ;AACA;AACA;AACA;AACA,SAASO,eAAeA,CAACllC,QAAQ,EAAE;EAC/B,IAAIk4B,EAAE;EACN,IAAIl4B,QAAQ,CAACg8B,UAAU,KAAKnkB,SAAS,IAAI7X,QAAQ,CAACg8B,UAAU,CAAC/6B,MAAM,KAAK,CAAC,EAAE;IACvE,OAAO,KAAK;EAChB;EACA,MAAM07B,OAAO,GAAG,CAACzE,EAAE,GAAGl4B,QAAQ,CAACg8B,UAAU,CAAC,CAAC,CAAC,MAAM,IAAI,IAAI9D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyE,OAAO;EAC7F,IAAIA,OAAO,KAAK9kB,SAAS,EAAE;IACvB,OAAO,KAAK;EAChB;EACA,IAAI8kB,OAAO,CAACC,KAAK,KAAK/kB,SAAS,IAAI8kB,OAAO,CAACC,KAAK,CAAC37B,MAAM,KAAK,CAAC,EAAE;IAC3D,OAAO,KAAK;EAChB;EACA,KAAK,MAAMkM,IAAI,IAAIwvB,OAAO,CAACC,KAAK,EAAE;IAC9B,IAAIzvB,IAAI,KAAK0K,SAAS,IAAImiB,MAAM,CAACyH,IAAI,CAACt0B,IAAI,CAAC,CAAClM,MAAM,KAAK,CAAC,EAAE;MACtD,OAAO,KAAK;IAChB;IACA,IAAIkM,IAAI,CAAClN,IAAI,KAAK4X,SAAS,IAAI1K,IAAI,CAAClN,IAAI,KAAK,EAAE,EAAE;MAC7C,OAAO,KAAK;IAChB;EACJ;EACA,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMklC,YAAY,GAAG,cAAc;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;EACdjmC,WAAWA,CAACE,MAAM,EAAEG,KAAK,EAAEyiC,MAAM,EAAEoD,eAAe,GAAG,CAAC,CAAC,EAAE;IACrD,IAAI,CAAC7lC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACyiC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACoD,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,YAAY,GAAGzlC,OAAO,CAACC,OAAO,CAAC,CAAC;IACrC,IAAI,CAACylC,OAAO,GAAGnmC,MAAM;IACrB,IAAI4iC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACyC,OAAO,EAAE;MAChED,mBAAmB,CAACxC,MAAM,CAACyC,OAAO,CAAC;MACnC,IAAI,CAACY,QAAQ,GAAGrD,MAAM,CAACyC,OAAO;IAClC;EACJ;EACA;AACJ;AACA;AACA;AACA;EACUe,UAAUA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAA5M,kKAAA;MACf,MAAM4M,KAAI,CAACH,YAAY;MACvB,OAAOG,KAAI,CAACJ,QAAQ;IAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACUK,WAAWA,CAAAC,IAAA,EAA+B;IAAA,IAAAC,MAAA;IAAA,OAAA/M,kKAAA,YAA9BgK,OAAO,EAAE9K,cAAc,GAAG,CAAC,CAAC;MAC1C,IAAIE,EAAE,EAAEC,EAAE,EAAEqE,EAAE,EAAEC,EAAE,EAAEqJ,EAAE,EAAEC,EAAE;MAC1B,MAAMF,MAAI,CAACN,YAAY;MACvB,MAAMS,UAAU,GAAGnD,gBAAgB,CAACC,OAAO,CAAC;MAC5C,MAAMmB,sBAAsB,GAAG;QAC3BP,cAAc,EAAE,CAACxL,EAAE,GAAG2N,MAAI,CAAC5D,MAAM,MAAM,IAAI,IAAI/J,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwL,cAAc;QACzFD,gBAAgB,EAAE,CAACtL,EAAE,GAAG0N,MAAI,CAAC5D,MAAM,MAAM,IAAI,IAAI9J,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsL,gBAAgB;QAC7FE,KAAK,EAAE,CAACnH,EAAE,GAAGqJ,MAAI,CAAC5D,MAAM,MAAM,IAAI,IAAIzF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmH,KAAK;QACvEC,UAAU,EAAE,CAACnH,EAAE,GAAGoJ,MAAI,CAAC5D,MAAM,MAAM,IAAI,IAAIxF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmH,UAAU;QACjFC,iBAAiB,EAAE,CAACiC,EAAE,GAAGD,MAAI,CAAC5D,MAAM,MAAM,IAAI,IAAI6D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACjC,iBAAiB;QAC/FC,aAAa,EAAE,CAACiC,EAAE,GAAGF,MAAI,CAAC5D,MAAM,MAAM,IAAI,IAAI8D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACjC,aAAa;QACvFC,QAAQ,EAAE,CAAC,GAAG8B,MAAI,CAACP,QAAQ,EAAEU,UAAU;MAC3C,CAAC;MACD,MAAMC,yBAAyB,GAAGjM,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE4L,MAAI,CAACR,eAAe,CAAC,EAAErN,cAAc,CAAC;MACxG,IAAIkO,WAAW;MACf;MACAL,MAAI,CAACN,YAAY,GAAGM,MAAI,CAACN,YAAY,CAChCnrB,IAAI,CAAC,MAAMva,eAAe,CAACgmC,MAAI,CAACL,OAAO,EAAEK,MAAI,CAACrmC,KAAK,EAAEykC,sBAAsB,EAAEgC,yBAAyB,CAAC,CAAC,CACxG7rB,IAAI,CAAEvY,MAAM,IAAK;QAClB,IAAIq2B,EAAE;QACN,IAAIgN,eAAe,CAACrjC,MAAM,CAAC7B,QAAQ,CAAC,EAAE;UAClC6lC,MAAI,CAACP,QAAQ,CAAC/iC,IAAI,CAACyjC,UAAU,CAAC;UAC9B,MAAMG,eAAe,GAAGnM,MAAM,CAACC,MAAM,CAAC;YAAE2C,KAAK,EAAE,EAAE;YAC7C;YACAn7B,IAAI,EAAE;UAAQ,CAAC,EAAE,CAACy2B,EAAE,GAAGr2B,MAAM,CAAC7B,QAAQ,CAACg8B,UAAU,MAAM,IAAI,IAAI9D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,CAACyE,OAAO,CAAC;UAC1GkJ,MAAI,CAACP,QAAQ,CAAC/iC,IAAI,CAAC4jC,eAAe,CAAC;QACvC,CAAC,MACI;UACD,MAAMC,iBAAiB,GAAGlK,uBAAuB,CAACr6B,MAAM,CAAC7B,QAAQ,CAAC;UAClE,IAAIomC,iBAAiB,EAAE;YACnB1mC,OAAO,CAACyC,IAAI,CAAE,mCAAkCikC,iBAAkB,wCAAuC,CAAC;UAC9G;QACJ;QACAF,WAAW,GAAGrkC,MAAM;MACxB,CAAC,CAAC,CACGm/B,KAAK,CAAE7U,CAAC,IAAK;QACd;QACA0Z,MAAI,CAACN,YAAY,GAAGzlC,OAAO,CAACC,OAAO,CAAC,CAAC;QACrC,MAAMosB,CAAC;MACX,CAAC,CAAC;MACF,MAAM0Z,MAAI,CAACN,YAAY;MACvB,OAAOW,WAAW;IAAC,GAAAtN,KAAA,OAAAC,SAAA;EACvB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUwN,iBAAiBA,CAAAC,IAAA,EAA+B;IAAA,IAAAC,MAAA;IAAA,OAAAzN,kKAAA,YAA9BgK,OAAO,EAAE9K,cAAc,GAAG,CAAC,CAAC;MAChD,IAAIE,EAAE,EAAEC,EAAE,EAAEqE,EAAE,EAAEC,EAAE,EAAEqJ,EAAE,EAAEC,EAAE;MAC1B,MAAMQ,MAAI,CAAChB,YAAY;MACvB,MAAMS,UAAU,GAAGnD,gBAAgB,CAACC,OAAO,CAAC;MAC5C,MAAMmB,sBAAsB,GAAG;QAC3BP,cAAc,EAAE,CAACxL,EAAE,GAAGqO,MAAI,CAACtE,MAAM,MAAM,IAAI,IAAI/J,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwL,cAAc;QACzFD,gBAAgB,EAAE,CAACtL,EAAE,GAAGoO,MAAI,CAACtE,MAAM,MAAM,IAAI,IAAI9J,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsL,gBAAgB;QAC7FE,KAAK,EAAE,CAACnH,EAAE,GAAG+J,MAAI,CAACtE,MAAM,MAAM,IAAI,IAAIzF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmH,KAAK;QACvEC,UAAU,EAAE,CAACnH,EAAE,GAAG8J,MAAI,CAACtE,MAAM,MAAM,IAAI,IAAIxF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmH,UAAU;QACjFC,iBAAiB,EAAE,CAACiC,EAAE,GAAGS,MAAI,CAACtE,MAAM,MAAM,IAAI,IAAI6D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACjC,iBAAiB;QAC/FC,aAAa,EAAE,CAACiC,EAAE,GAAGQ,MAAI,CAACtE,MAAM,MAAM,IAAI,IAAI8D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACjC,aAAa;QACvFC,QAAQ,EAAE,CAAC,GAAGwC,MAAI,CAACjB,QAAQ,EAAEU,UAAU;MAC3C,CAAC;MACD,MAAMC,yBAAyB,GAAGjM,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEsM,MAAI,CAAClB,eAAe,CAAC,EAAErN,cAAc,CAAC;MACxG,MAAMwO,aAAa,GAAG7E,qBAAqB,CAAC4E,MAAI,CAACf,OAAO,EAAEe,MAAI,CAAC/mC,KAAK,EAAEykC,sBAAsB,EAAEgC,yBAAyB,CAAC;MACxH;MACAM,MAAI,CAAChB,YAAY,GAAGgB,MAAI,CAAChB,YAAY,CAChCnrB,IAAI,CAAC,MAAMosB,aAAa;MACzB;MACA;MAAA,CACCxF,KAAK,CAAEyF,QAAQ,IAAK;QACrB,MAAM,IAAIlgC,KAAK,CAAC4+B,YAAY,CAAC;MACjC,CAAC,CAAC,CACG/qB,IAAI,CAAEssB,YAAY,IAAKA,YAAY,CAAC1mC,QAAQ,CAAC,CAC7Coa,IAAI,CAAEpa,QAAQ,IAAK;QACpB,IAAIklC,eAAe,CAACllC,QAAQ,CAAC,EAAE;UAC3BumC,MAAI,CAACjB,QAAQ,CAAC/iC,IAAI,CAACyjC,UAAU,CAAC;UAC9B,MAAMG,eAAe,GAAGnM,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEj6B,QAAQ,CAACg8B,UAAU,CAAC,CAAC,CAAC,CAACW,OAAO,CAAC;UACzE;UACA,IAAI,CAACwJ,eAAe,CAAC1kC,IAAI,EAAE;YACvB0kC,eAAe,CAAC1kC,IAAI,GAAG,OAAO;UAClC;UACA8kC,MAAI,CAACjB,QAAQ,CAAC/iC,IAAI,CAAC4jC,eAAe,CAAC;QACvC,CAAC,MACI;UACD,MAAMC,iBAAiB,GAAGlK,uBAAuB,CAACl8B,QAAQ,CAAC;UAC3D,IAAIomC,iBAAiB,EAAE;YACnB1mC,OAAO,CAACyC,IAAI,CAAE,yCAAwCikC,iBAAkB,wCAAuC,CAAC;UACpH;QACJ;MACJ,CAAC,CAAC,CACGpF,KAAK,CAAE7U,CAAC,IAAK;QACd;QACA;QACA;QACA,IAAIA,CAAC,CAAC/lB,OAAO,KAAK++B,YAAY,EAAE;UAC5B;UACA;UACAzlC,OAAO,CAACE,KAAK,CAACusB,CAAC,CAAC;QACpB;MACJ,CAAC,CAAC;MACF,OAAOqa,aAAa;IAAC,GAAA5N,KAAA,OAAAC,SAAA;EACzB;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,SAgBe8N,WAAWA,CAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,YAAA,CAAApO,KAAA,OAAAC,SAAA;AAAA;AAK1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,SAAAmO,aAAA;EAAAA,YAAA,GAAAlO,kKAAA,CALA,WAA2Bz5B,MAAM,EAAEG,KAAK,EAAEyiC,MAAM,EAAEgF,oBAAoB,EAAE;IACpE,MAAMjnC,QAAQ,SAASo6B,gBAAgB,CAAC56B,KAAK,EAAEq4B,IAAI,CAACqP,YAAY,EAAE7nC,MAAM,EAAE,KAAK,EAAE4C,IAAI,CAACk3B,SAAS,CAAC8I,MAAM,CAAC,EAAEgF,oBAAoB,CAAC;IAC9H,OAAOjnC,QAAQ,CAACy7B,IAAI,CAAC,CAAC;EAC1B,CAAC;EAAA,OAAAuL,YAAA,CAAApO,KAAA,OAAAC,SAAA;AAAA;AAAA,SAkBcsO,YAAYA,CAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,aAAA,CAAA5O,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAA2O,cAAA;EAAAA,aAAA,GAAA1O,kKAAA,CAA3B,WAA4Bz5B,MAAM,EAAEG,KAAK,EAAEyiC,MAAM,EAAEjK,cAAc,EAAE;IAC/D,MAAMh4B,QAAQ,SAASo6B,gBAAgB,CAAC56B,KAAK,EAAEq4B,IAAI,CAAC4P,aAAa,EAAEpoC,MAAM,EAAE,KAAK,EAAE4C,IAAI,CAACk3B,SAAS,CAAC8I,MAAM,CAAC,EAAEjK,cAAc,CAAC;IACzH,OAAOh4B,QAAQ,CAACy7B,IAAI,CAAC,CAAC;EAC1B,CAAC;EAAA,OAAA+L,aAAA,CAAA5O,KAAA,OAAAC,SAAA;AAAA;AAAA,SACc6O,kBAAkBA,CAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,mBAAA,CAAAnP,KAAA,OAAAC,SAAA;AAAA;AAQjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAHA,SAAAkP,oBAAA;EAAAA,mBAAA,GAAAjP,kKAAA,CAxBA,WAAkCz5B,MAAM,EAAEG,KAAK,EAAEyiC,MAAM,EAAEjK,cAAc,EAAE;IACrE,MAAMgQ,iBAAiB,GAAG/F,MAAM,CAACgG,QAAQ,CAAClpC,GAAG,CAAE+jC,OAAO,IAAK;MACvD,OAAO9I,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE6I,OAAO,CAAC,EAAE;QAAEtjC;MAAM,CAAC,CAAC;IAC/D,CAAC,CAAC;IACF,MAAMQ,QAAQ,SAASo6B,gBAAgB,CAAC56B,KAAK,EAAEq4B,IAAI,CAACqQ,oBAAoB,EAAE7oC,MAAM,EAAE,KAAK,EAAE4C,IAAI,CAACk3B,SAAS,CAAC;MAAE8O,QAAQ,EAAED;IAAkB,CAAC,CAAC,EAAEhQ,cAAc,CAAC;IACzJ,OAAOh4B,QAAQ,CAACy7B,IAAI,CAAC,CAAC;EAC1B,CAAC;EAAA,OAAAsM,mBAAA,CAAAnP,KAAA,OAAAC,SAAA;AAAA;AAsBD,MAAMsP,eAAe,CAAC;EAClBhpC,WAAWA,CAACE,MAAM,EAAEkkC,WAAW,EAAE8B,eAAe,GAAG,CAAC,CAAC,EAAE;IACnD,IAAI,CAAChmC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACgmC,eAAe,GAAGA,eAAe;IACtC,IAAI9B,WAAW,CAAC/jC,KAAK,CAACiD,QAAQ,CAAC,GAAG,CAAC,EAAE;MACjC;MACA,IAAI,CAACjD,KAAK,GAAG+jC,WAAW,CAAC/jC,KAAK;IAClC,CAAC,MACI;MACD;MACA,IAAI,CAACA,KAAK,GAAI,UAAS+jC,WAAW,CAAC/jC,KAAM,EAAC;IAC9C;IACA,IAAI,CAACikC,gBAAgB,GAAGF,WAAW,CAACE,gBAAgB,IAAI,CAAC,CAAC;IAC1D,IAAI,CAACC,cAAc,GAAGH,WAAW,CAACG,cAAc,IAAI,EAAE;IACtD,IAAI,CAACC,KAAK,GAAGJ,WAAW,CAACI,KAAK;IAC9B,IAAI,CAACC,UAAU,GAAGL,WAAW,CAACK,UAAU;IACxC,IAAI,CAACC,iBAAiB,GAAGlB,uBAAuB,CAACY,WAAW,CAACM,iBAAiB,CAAC;IAC/E,IAAI,CAACC,aAAa,GAAGP,WAAW,CAACO,aAAa;EAClD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACUjkC,eAAeA,CAAAuoC,IAAA,EAA+B;IAAA,IAAAC,MAAA;IAAA,OAAAvP,kKAAA,YAA9BgK,OAAO,EAAE9K,cAAc,GAAG,CAAC,CAAC;MAC9C,IAAIE,EAAE;MACN,MAAMoQ,eAAe,GAAGpE,0BAA0B,CAACpB,OAAO,CAAC;MAC3D,MAAMyF,6BAA6B,GAAGvO,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEoO,MAAI,CAAChD,eAAe,CAAC,EAAErN,cAAc,CAAC;MAC5G,OAAOn4B,eAAe,CAACwoC,MAAI,CAAChpC,MAAM,EAAEgpC,MAAI,CAAC7oC,KAAK,EAAEw6B,MAAM,CAACC,MAAM,CAAC;QAAEwJ,gBAAgB,EAAE4E,MAAI,CAAC5E,gBAAgB;QAAEC,cAAc,EAAE2E,MAAI,CAAC3E,cAAc;QAAEC,KAAK,EAAE0E,MAAI,CAAC1E,KAAK;QAAEC,UAAU,EAAEyE,MAAI,CAACzE,UAAU;QAAEC,iBAAiB,EAAEwE,MAAI,CAACxE,iBAAiB;QAAEC,aAAa,EAAE,CAAC5L,EAAE,GAAGmQ,MAAI,CAACvE,aAAa,MAAM,IAAI,IAAI5L,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC72B;MAAK,CAAC,EAAEinC,eAAe,CAAC,EAAEC,6BAA6B,CAAC;IAAC,GAAA3P,KAAA,OAAAC,SAAA;EACvX;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACU8I,qBAAqBA,CAAA6G,IAAA,EAA+B;IAAA,IAAAC,MAAA;IAAA,OAAA3P,kKAAA,YAA9BgK,OAAO,EAAE9K,cAAc,GAAG,CAAC,CAAC;MACpD,IAAIE,EAAE;MACN,MAAMoQ,eAAe,GAAGpE,0BAA0B,CAACpB,OAAO,CAAC;MAC3D,MAAMyF,6BAA6B,GAAGvO,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEwO,MAAI,CAACpD,eAAe,CAAC,EAAErN,cAAc,CAAC;MAC5G,OAAO2J,qBAAqB,CAAC8G,MAAI,CAACppC,MAAM,EAAEopC,MAAI,CAACjpC,KAAK,EAAEw6B,MAAM,CAACC,MAAM,CAAC;QAAEwJ,gBAAgB,EAAEgF,MAAI,CAAChF,gBAAgB;QAAEC,cAAc,EAAE+E,MAAI,CAAC/E,cAAc;QAAEC,KAAK,EAAE8E,MAAI,CAAC9E,KAAK;QAAEC,UAAU,EAAE6E,MAAI,CAAC7E,UAAU;QAAEC,iBAAiB,EAAE4E,MAAI,CAAC5E,iBAAiB;QAAEC,aAAa,EAAE,CAAC5L,EAAE,GAAGuQ,MAAI,CAAC3E,aAAa,MAAM,IAAI,IAAI5L,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC72B;MAAK,CAAC,EAAEinC,eAAe,CAAC,EAAEC,6BAA6B,CAAC;IAAC,GAAA3P,KAAA,OAAAC,SAAA;EAC7X;EACA;AACJ;AACA;AACA;EACI6P,SAASA,CAACC,eAAe,EAAE;IACvB,IAAIzQ,EAAE;IACN,OAAO,IAAIkN,WAAW,CAAC,IAAI,CAAC/lC,MAAM,EAAE,IAAI,CAACG,KAAK,EAAEw6B,MAAM,CAACC,MAAM,CAAC;MAAEwJ,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;MAAEC,cAAc,EAAE,IAAI,CAACA,cAAc;MAAEC,KAAK,EAAE,IAAI,CAACA,KAAK;MAAEC,UAAU,EAAE,IAAI,CAACA,UAAU;MAAEC,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;MAAEC,aAAa,EAAE,CAAC5L,EAAE,GAAG,IAAI,CAAC4L,aAAa,MAAM,IAAI,IAAI5L,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC72B;IAAK,CAAC,EAAEsnC,eAAe,CAAC,EAAE,IAAI,CAACtD,eAAe,CAAC;EAC7W;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACUsB,WAAWA,CAAAiC,IAAA,EAA+B;IAAA,IAAAC,MAAA;IAAA,OAAA/P,kKAAA,YAA9BgK,OAAO,EAAE9K,cAAc,GAAG,CAAC,CAAC;MAC1C,MAAMsQ,eAAe,GAAGhF,sBAAsB,CAACR,OAAO,EAAE;QACpDtjC,KAAK,EAAEqpC,MAAI,CAACrpC,KAAK;QACjBikC,gBAAgB,EAAEoF,MAAI,CAACpF,gBAAgB;QACvCC,cAAc,EAAEmF,MAAI,CAACnF,cAAc;QACnCC,KAAK,EAAEkF,MAAI,CAAClF,KAAK;QACjBC,UAAU,EAAEiF,MAAI,CAACjF,UAAU;QAC3BC,iBAAiB,EAAEgF,MAAI,CAAChF,iBAAiB;QACzCC,aAAa,EAAE+E,MAAI,CAAC/E;MACxB,CAAC,CAAC;MACF,MAAMyE,6BAA6B,GAAGvO,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE4O,MAAI,CAACxD,eAAe,CAAC,EAAErN,cAAc,CAAC;MAC5G,OAAO2O,WAAW,CAACkC,MAAI,CAACxpC,MAAM,EAAEwpC,MAAI,CAACrpC,KAAK,EAAE8oC,eAAe,EAAEC,6BAA6B,CAAC;IAAC,GAAA3P,KAAA,OAAAC,SAAA;EAChG;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACUsO,YAAYA,CAAA2B,IAAA,EAA+B;IAAA,IAAAC,MAAA;IAAA,OAAAjQ,kKAAA,YAA9BgK,OAAO,EAAE9K,cAAc,GAAG,CAAC,CAAC;MAC3C,MAAMsQ,eAAe,GAAGlE,uBAAuB,CAACtB,OAAO,CAAC;MACxD,MAAMyF,6BAA6B,GAAGvO,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE8O,MAAI,CAAC1D,eAAe,CAAC,EAAErN,cAAc,CAAC;MAC5G,OAAOmP,YAAY,CAAC4B,MAAI,CAAC1pC,MAAM,EAAE0pC,MAAI,CAACvpC,KAAK,EAAE8oC,eAAe,EAAEC,6BAA6B,CAAC;IAAC,GAAA3P,KAAA,OAAAC,SAAA;EACjG;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACU6O,kBAAkBA,CAAAsB,IAAA,EAAgD;IAAA,IAAAC,MAAA;IAAA,OAAAnQ,kKAAA,YAA/CoQ,wBAAwB,EAAElR,cAAc,GAAG,CAAC,CAAC;MAClE,MAAMuQ,6BAA6B,GAAGvO,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEgP,MAAI,CAAC5D,eAAe,CAAC,EAAErN,cAAc,CAAC;MAC5G,OAAO0P,kBAAkB,CAACuB,MAAI,CAAC5pC,MAAM,EAAE4pC,MAAI,CAACzpC,KAAK,EAAE0pC,wBAAwB,EAAEX,6BAA6B,CAAC;IAAC,GAAA3P,KAAA,OAAAC,SAAA;EAChH;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMl6B,kBAAkB,CAAC;EACrBQ,WAAWA,CAACE,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;EACxB;EACA;AACJ;AACA;EACII,kBAAkBA,CAAC8jC,WAAW,EAAEvL,cAAc,EAAE;IAC5C,IAAI,CAACuL,WAAW,CAAC/jC,KAAK,EAAE;MACpB,MAAM,IAAI23B,uBAAuB,CAAE,6BAA4B,GAC1D,+DAA8D,CAAC;IACxE;IACA,OAAO,IAAIgR,eAAe,CAAC,IAAI,CAAC9oC,MAAM,EAAEkkC,WAAW,EAAEvL,cAAc,CAAC;EACxE;EACA;AACJ;AACA;EACImR,mCAAmCA,CAACrF,aAAa,EAAEP,WAAW,EAAEvL,cAAc,EAAE;IAC5E,IAAI,CAAC8L,aAAa,CAACziC,IAAI,EAAE;MACrB,MAAM,IAAIk2B,mCAAmC,CAAC,6CAA6C,CAAC;IAChG;IACA,IAAI,CAACuM,aAAa,CAACtkC,KAAK,EAAE;MACtB,MAAM,IAAI+3B,mCAAmC,CAAC,8CAA8C,CAAC;IACjG;IACA;AACR;AACA;AACA;IACQ,MAAM6R,oBAAoB,GAAG,CAAC,OAAO,EAAE,mBAAmB,CAAC;IAC3D,KAAK,MAAMp+B,GAAG,IAAIo+B,oBAAoB,EAAE;MACpC,IAAI,CAAC7F,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACv4B,GAAG,CAAC,KAC3E84B,aAAa,CAAC94B,GAAG,CAAC,IAClB,CAACu4B,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACv4B,GAAG,CAAC,MAAM84B,aAAa,CAAC94B,GAAG,CAAC,EAAE;QACrG,IAAIA,GAAG,KAAK,OAAO,EAAE;UACjB,MAAMq+B,eAAe,GAAG9F,WAAW,CAAC/jC,KAAK,CAAC0sB,UAAU,CAAC,SAAS,CAAC,GACzDqX,WAAW,CAAC/jC,KAAK,CAAC8pC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,GACxC/F,WAAW,CAAC/jC,KAAK;UACvB,MAAM+pC,iBAAiB,GAAGzF,aAAa,CAACtkC,KAAK,CAAC0sB,UAAU,CAAC,SAAS,CAAC,GAC7D4X,aAAa,CAACtkC,KAAK,CAAC8pC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,GAC1CxF,aAAa,CAACtkC,KAAK;UACzB,IAAI6pC,eAAe,KAAKE,iBAAiB,EAAE;YACvC;UACJ;QACJ;QACA,MAAM,IAAIhS,mCAAmC,CAAE,wBAAuBvsB,GAAI,4BAA2B,GAChG,KAAIu4B,WAAW,CAACv4B,GAAG,CAAE,wBAAuB84B,aAAa,CAAC94B,GAAG,CAAE,GAAE,CAAC;MAC3E;IACJ;IACA,MAAMw+B,oBAAoB,GAAGxP,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEsJ,WAAW,CAAC,EAAE;MAAE/jC,KAAK,EAAEskC,aAAa,CAACtkC,KAAK;MAAEmkC,KAAK,EAAEG,aAAa,CAACH,KAAK;MAAEC,UAAU,EAAEE,aAAa,CAACF,UAAU;MAAEC,iBAAiB,EAAEC,aAAa,CAACD,iBAAiB;MAAEC;IAAc,CAAC,CAAC;IAC/O,OAAO,IAAIqE,eAAe,CAAC,IAAI,CAAC9oC,MAAM,EAAEmqC,oBAAoB,EAAExR,cAAc,CAAC;EACjF;AACJ", "sources": ["./src/app/services/ai.service.ts", "./src/app/services/equipe.service.ts", "./src/app/services/membre.service.ts", "./src/app/services/notification.service.ts", "./src/app/services/task.service.ts", "./node_modules/@angular/cdk/fesm2022/drag-drop.mjs", "./node_modules/@google/generative-ai/dist/index.mjs"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { GoogleGenerativeAI, GenerativeModel } from '@google/generative-ai';\r\nimport { Observable, from, of } from 'rxjs';\r\nimport { map, catchError } from 'rxjs/operators';\r\nimport { environment } from 'src/environments/environment'\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AiService {\r\n  private genAI!: GoogleGenerativeAI;\r\n  private model!: GenerativeModel;\r\n  private apiAvailable: boolean = true;\r\n\r\n  constructor() {\r\n    // Utiliser une clé API de l'environnement ou une clé de démonstration\r\n    // Pour une utilisation en production, obtenez une clé API sur https://makersuite.google.com/\r\n    const apiKey = environment.geminiApiKey || 'AIzaSyDCXc16FzaVWSJkW4RGboTZ8AD9_PTDL88';\r\n\r\n    try {\r\n      // Initialiser l'API Gemini\r\n      this.genAI = new GoogleGenerativeAI(apiKey);\r\n      // Utiliser le modèle gemini-1.5-pro qui est disponible dans la version actuelle de l'API\r\n      this.model = this.genAI.getGenerativeModel({ model: 'gemini-1.5-pro' });\r\n      console.log('Service AI initialisé avec succès');\r\n    } catch (error) {\r\n      console.error('Erreur lors de l\\'initialisation du service AI:', error);\r\n\r\n      // Créer des objets factices pour éviter les erreurs null\r\n      // Ces objets ne feront rien mais éviteront les erreurs d'exécution\r\n      this.genAI = {} as GoogleGenerativeAI;\r\n      this.model = {\r\n        generateContent: () => Promise.resolve({\r\n          response: { text: () => 'Service AI non disponible' }\r\n        })\r\n      } as unknown as GenerativeModel;\r\n\r\n      // Marquer l'API comme non disponible\r\n      this.apiAvailable = false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Génère des tâches pour un projet en fonction du nombre de membres\r\n   * @param projectTitle Titre du projet\r\n   * @param memberCount Nombre de membres dans l'équipe\r\n   * @param teamMembers Liste des membres de l'équipe (optionnel)\r\n   * @returns Observable contenant les tâches générées\r\n   */\r\n  generateProjectTasks(projectTitle: string, memberCount: number, teamMembers?: any[]): Observable<any> {\r\n    // Si le nombre de membres est trop petit, utiliser au moins 3 entités\r\n    const effectiveMemberCount = Math.max(memberCount, 3);\r\n\r\n    // Données de démonstration à utiliser en cas d'erreur ou si l'API n'est pas disponible\r\n    const fallbackData = this.createFallbackTaskData(projectTitle, effectiveMemberCount, teamMembers);\r\n\r\n    // Si nous savons déjà que l'API n'est pas disponible, retourner directement les données de démonstration\r\n    if (!this.isApiAvailable()) {\r\n      console.log('API Gemini non disponible, utilisation des données de démonstration');\r\n      return new Observable(observer => {\r\n        setTimeout(() => {\r\n          observer.next(fallbackData);\r\n          observer.complete();\r\n        }, 1000); // Simuler un délai d'API\r\n      });\r\n    }\r\n\r\n    // Préparer les informations sur les membres de l'équipe pour le prompt\r\n    let teamMembersInfo = '';\r\n    if (teamMembers && teamMembers.length > 0) {\r\n      teamMembersInfo = `\r\n      Membres de l'équipe:\r\n      ${teamMembers.map((member, index) => {\r\n        const memberName = member.name || member.firstName || member.lastName || `Membre ${index + 1}`;\r\n        const memberRole = member.role || 'membre';\r\n        return `- ${memberName} (${memberRole})`;\r\n      }).join('\\n')}\r\n      `;\r\n    }\r\n\r\n    const prompt = `\r\n      Agis comme un expert en gestion de projet. Je travaille sur un projet intitulé \"${projectTitle}\"\r\n      avec une équipe de ${effectiveMemberCount} membres.\r\n      ${teamMembersInfo}\r\n\r\n      Divise ce projet en exactement ${effectiveMemberCount} entités ou modules principaux qui peuvent être travaillés en parallèle par chaque membre de l'équipe.\r\n\r\n      IMPORTANT: Chaque entité doit être simple, claire et concise (maximum 3-4 mots).\r\n      Exemples d'entités pour un site e-commerce avec 3 membres:\r\n      - CRUD des produits\r\n      - Interface utilisateur\r\n      - Déploiement\r\n\r\n      Pour chaque entité/module:\r\n      1. Donne un nom très court et concis (maximum 3-4 mots)\r\n      2. Fournis une brève description (1 phrase maximum)\r\n      3. Liste 2-3 tâches spécifiques avec leur priorité (haute, moyenne, basse)\r\n\r\n      Réponds au format JSON suivant sans aucun texte supplémentaire:\r\n      {\r\n        \"projectTitle\": \"${projectTitle}\",\r\n        \"entities\": [\r\n          {\r\n            \"name\": \"Nom court de l'entité\",\r\n            \"description\": \"Description très brève de l'entité\",\r\n            \"assignedTo\": \"Nom du membre (optionnel)\",\r\n            \"tasks\": [\r\n              {\r\n                \"title\": \"Titre court de la tâche\",\r\n                \"description\": \"Description brève de la tâche\",\r\n                \"priority\": \"high|medium|low\",\r\n                \"status\": \"todo\"\r\n              }\r\n            ]\r\n          }\r\n        ]\r\n      }\r\n    `;\r\n\r\n    try {\r\n      return from(this.model.generateContent(prompt))\r\n        .pipe(\r\n          map(result => {\r\n            try {\r\n              const textResult = result.response.text();\r\n              // Extraire le JSON de la réponse\r\n              const jsonMatch = textResult.match(/\\{[\\s\\S]*\\}/);\r\n              if (jsonMatch) {\r\n                return JSON.parse(jsonMatch[0]);\r\n              } else {\r\n                console.warn('Format JSON non trouvé dans la réponse, utilisation des données de démonstration');\r\n                return fallbackData;\r\n              }\r\n            } catch (error) {\r\n              console.error('Erreur lors du parsing de la réponse:', error);\r\n              return fallbackData;\r\n            }\r\n          }),\r\n          // Capturer les erreurs au niveau de l'Observable\r\n          catchError(error => {\r\n            console.error('Erreur lors de la génération de contenu:', error);\r\n            // Marquer l'API comme non disponible pour les prochains appels\r\n            this.markApiAsUnavailable();\r\n            return of(fallbackData);\r\n          })\r\n        );\r\n    } catch (error) {\r\n      console.error('Erreur lors de l\\'appel à l\\'API:', error);\r\n      this.markApiAsUnavailable();\r\n      return of(fallbackData);\r\n    }\r\n  }\r\n\r\n  // Méthode pour créer des données de démonstration\r\n  private createFallbackTaskData(projectTitle: string, memberCount: number, teamMembers?: any[]): any {\r\n    // Préparer les informations sur les membres pour l'assignation\r\n    const memberNames: string[] = [];\r\n    if (teamMembers && teamMembers.length > 0) {\r\n      teamMembers.forEach((member, index) => {\r\n        const memberName = member.name || member.firstName ||\r\n                          (member.firstName && member.lastName ? `${member.firstName} ${member.lastName}` : null) ||\r\n                          `Membre ${index + 1}`;\r\n        memberNames.push(memberName);\r\n      });\r\n    }\r\n\r\n    // Si pas assez de noms de membres, compléter avec des noms génériques\r\n    while (memberNames.length < memberCount) {\r\n      memberNames.push(`Membre ${memberNames.length + 1}`);\r\n    }\r\n\r\n    // Données de démonstration pour un site e-commerce\r\n    if (projectTitle.toLowerCase().includes('ecommerce') || projectTitle.toLowerCase().includes('e-commerce') || projectTitle.toLowerCase().includes('boutique')) {\r\n      const ecommerceEntities = [\r\n        {\r\n          name: \"CRUD des produits\",\r\n          description: \"Gestion des produits dans la base de données\",\r\n          assignedTo: memberNames[0] || \"Non assigné\",\r\n          tasks: [\r\n            {\r\n              title: \"Créer API produits\",\r\n              description: \"Développer les endpoints pour créer, lire, modifier et supprimer des produits\",\r\n              priority: \"high\",\r\n              status: \"todo\"\r\n            },\r\n            {\r\n              title: \"Modèle de données\",\r\n              description: \"Concevoir le schéma de la base de données pour les produits\",\r\n              priority: \"medium\",\r\n              status: \"todo\"\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: \"Interface utilisateur\",\r\n          description: \"Développement du frontend de l'application\",\r\n          assignedTo: memberNames[1] || \"Non assigné\",\r\n          tasks: [\r\n            {\r\n              title: \"Page d'accueil\",\r\n              description: \"Créer la page d'accueil avec la liste des produits\",\r\n              priority: \"high\",\r\n              status: \"todo\"\r\n            },\r\n            {\r\n              title: \"Panier d'achat\",\r\n              description: \"Implémenter la fonctionnalité du panier d'achat\",\r\n              priority: \"medium\",\r\n              status: \"todo\"\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: \"Déploiement\",\r\n          description: \"Mise en production de l'application\",\r\n          assignedTo: memberNames[2] || \"Non assigné\",\r\n          tasks: [\r\n            {\r\n              title: \"Configuration serveur\",\r\n              description: \"Configurer le serveur pour l'hébergement\",\r\n              priority: \"medium\",\r\n              status: \"todo\"\r\n            },\r\n            {\r\n              title: \"Tests d'intégration\",\r\n              description: \"Effectuer des tests d'intégration avant le déploiement\",\r\n              priority: \"high\",\r\n              status: \"todo\"\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: \"Gestion utilisateurs\",\r\n          description: \"Système d'authentification et profils\",\r\n          assignedTo: memberNames[3] || \"Non assigné\",\r\n          tasks: [\r\n            {\r\n              title: \"Authentification\",\r\n              description: \"Implémenter le système de connexion et d'inscription\",\r\n              priority: \"high\",\r\n              status: \"todo\"\r\n            },\r\n            {\r\n              title: \"Profils utilisateurs\",\r\n              description: \"Créer les pages de profil et de gestion des informations personnelles\",\r\n              priority: \"medium\",\r\n              status: \"todo\"\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: \"Paiement en ligne\",\r\n          description: \"Intégration des systèmes de paiement\",\r\n          assignedTo: memberNames[4] || \"Non assigné\",\r\n          tasks: [\r\n            {\r\n              title: \"API de paiement\",\r\n              description: \"Intégrer une passerelle de paiement comme Stripe ou PayPal\",\r\n              priority: \"high\",\r\n              status: \"todo\"\r\n            },\r\n            {\r\n              title: \"Sécurité transactions\",\r\n              description: \"Mettre en place les mesures de sécurité pour les transactions\",\r\n              priority: \"high\",\r\n              status: \"todo\"\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: \"SEO & Analytics\",\r\n          description: \"Optimisation pour les moteurs de recherche\",\r\n          assignedTo: memberNames[5] || \"Non assigné\",\r\n          tasks: [\r\n            {\r\n              title: \"Balises méta\",\r\n              description: \"Optimiser les balises méta et la structure du site\",\r\n              priority: \"medium\",\r\n              status: \"todo\"\r\n            },\r\n            {\r\n              title: \"Google Analytics\",\r\n              description: \"Intégrer des outils d'analyse du trafic\",\r\n              priority: \"low\",\r\n              status: \"todo\"\r\n            }\r\n          ]\r\n        }\r\n      ];\r\n\r\n      // Limiter au nombre de membres\r\n      return {\r\n        projectTitle: projectTitle,\r\n        entities: ecommerceEntities.slice(0, memberCount)\r\n      };\r\n    }\r\n\r\n    // Données génériques pour tout autre type de projet\r\n    const moduleTypes = [\r\n      { name: \"Backend\", description: \"Développement du backend de l'application\" },\r\n      { name: \"Frontend\", description: \"Développement de l'interface utilisateur\" },\r\n      { name: \"Base de données\", description: \"Conception et gestion de la base de données\" },\r\n      { name: \"Tests\", description: \"Tests et assurance qualité\" },\r\n      { name: \"Déploiement\", description: \"Configuration et déploiement de l'application\" },\r\n      { name: \"Documentation\", description: \"Rédaction de la documentation technique\" }\r\n    ];\r\n\r\n    return {\r\n      projectTitle: projectTitle,\r\n      entities: Array.from({ length: memberCount }, (_, i) => ({\r\n        name: moduleTypes[i % moduleTypes.length].name,\r\n        description: moduleTypes[i % moduleTypes.length].description,\r\n        assignedTo: memberNames[i] || \"Non assigné\",\r\n        tasks: [\r\n          {\r\n            title: `Conception ${moduleTypes[i % moduleTypes.length].name}`,\r\n            description: `Planifier l'architecture du ${moduleTypes[i % moduleTypes.length].name.toLowerCase()}`,\r\n            priority: 'high',\r\n            status: 'todo'\r\n          },\r\n          {\r\n            title: `Implémentation ${moduleTypes[i % moduleTypes.length].name}`,\r\n            description: `Développer les fonctionnalités du ${moduleTypes[i % moduleTypes.length].name.toLowerCase()}`,\r\n            priority: 'medium',\r\n            status: 'todo'\r\n          },\r\n          {\r\n            title: `Tests ${moduleTypes[i % moduleTypes.length].name}`,\r\n            description: `Tester les fonctionnalités du ${moduleTypes[i % moduleTypes.length].name.toLowerCase()}`,\r\n            priority: 'medium',\r\n            status: 'todo'\r\n          }\r\n        ]\r\n      }))\r\n    };\r\n  }\r\n\r\n  // Méthode pour vérifier si l'API est disponible\r\n  private isApiAvailable(): boolean {\r\n    return this.apiAvailable;\r\n  }\r\n\r\n  // Méthode pour marquer l'API comme non disponible\r\n  private markApiAsUnavailable(): void {\r\n    this.apiAvailable = false;\r\n    console.warn('API Gemini marquée comme non disponible pour les prochains appels');\r\n  }\r\n\r\n  /**\r\n   * Génère une réponse à une question sur le projet\r\n   * @param question Question posée par l'utilisateur\r\n   * @param projectContext Contexte du projet (titre, description, etc.)\r\n   * @returns Observable contenant la réponse générée\r\n   */\r\n  askProjectQuestion(question: string, projectContext: any): Observable<string> {\r\n    // Réponses de secours en cas d'erreur\r\n    const fallbackResponses = [\r\n      `Pour votre projet \"${projectContext.title || 'en cours'}\", je recommande de commencer par définir clairement les objectifs et les livrables attendus.`,\r\n      `La gestion efficace d'un projet comme \"${projectContext.title || 'celui-ci'}\" nécessite une bonne planification et une communication claire entre les membres de l'équipe.`,\r\n      `Pour répondre à votre question sur \"${question}\", je vous suggère de diviser le travail en tâches plus petites et de les assigner aux membres de l'équipe en fonction de leurs compétences.`,\r\n      `Dans le cadre de votre projet, il est important de définir des jalons clairs et de suivre régulièrement l'avancement des travaux.`\r\n    ];\r\n\r\n    // Sélectionner une réponse aléatoire de secours\r\n    const getRandomFallbackResponse = () => {\r\n      const randomIndex = Math.floor(Math.random() * fallbackResponses.length);\r\n      return fallbackResponses[randomIndex];\r\n    };\r\n\r\n    // Si l'API n'est pas disponible, retourner directement une réponse de secours\r\n    if (!this.isApiAvailable()) {\r\n      console.log('API Gemini non disponible, utilisation d\\'une réponse de secours');\r\n      return of(getRandomFallbackResponse());\r\n    }\r\n\r\n    const prompt = `\r\n      Contexte du projet:\r\n      Titre: ${projectContext.title || 'Non spécifié'}\r\n      Description: ${projectContext.description || 'Non spécifiée'}\r\n\r\n      Question: ${question}\r\n\r\n      Réponds de manière concise et professionnelle en tant qu'assistant de gestion de projet.\r\n    `;\r\n\r\n    try {\r\n      return from(this.model.generateContent(prompt))\r\n        .pipe(\r\n          map(result => {\r\n            try {\r\n              return result.response.text();\r\n            } catch (error) {\r\n              console.error('Erreur lors de la récupération de la réponse:', error);\r\n              return getRandomFallbackResponse();\r\n            }\r\n          }),\r\n          catchError(error => {\r\n            console.error('Erreur lors de la génération de contenu:', error);\r\n            this.markApiAsUnavailable();\r\n            return of(getRandomFallbackResponse());\r\n          })\r\n        );\r\n    } catch (error) {\r\n      console.error('Erreur lors de la génération de contenu:', error);\r\n      this.markApiAsUnavailable();\r\n      return of(getRandomFallbackResponse());\r\n    }\r\n  }\r\n}", "import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpErrorResponse } from '@angular/common/http';\r\nimport { Observable, throwError } from 'rxjs';\r\nimport { catchError, tap, map } from 'rxjs/operators';\r\nimport { Equipe } from '../models/equipe.model';\r\nimport { environment } from 'src/environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class EquipeService {\r\n  private apiUrl = `${environment.urlBackend}teams`;\r\n\r\n  constructor(private http: HttpClient) {\r\n    console.log('API URL:', this.apiUrl);\r\n  }\r\n\r\n  getEquipes(): Observable<Equipe[]> {\r\n    console.log('Fetching teams from:', this.apiUrl);\r\n    return this.http.get<Equipe[]>(this.apiUrl).pipe(\r\n      tap((data) => console.log('Teams received:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  getEquipe(id: string): Observable<Equipe> {\r\n    console.log(`Fetching team with id ${id} from: ${this.apiUrl}/${id}`);\r\n    return this.http.get<Equipe>(`${this.apiUrl}/${id}`).pipe(\r\n      tap((data) => console.log('Team received:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  addEquipe(equipe: Equipe): Observable<Equipe> {\r\n    console.log('Adding team:', equipe);\r\n    return this.http.post<Equipe>(this.apiUrl, equipe).pipe(\r\n      tap((data) => console.log('Team added, response:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  updateEquipe(id: string, equipe: Equipe): Observable<Equipe> {\r\n    console.log(`Updating team with id ${id}:`, equipe);\r\n    return this.http.put<Equipe>(`${this.apiUrl}/${id}`, equipe).pipe(\r\n      tap((data) => console.log('Team updated, response:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  deleteEquipe(id: string): Observable<any> {\r\n    console.log(`Deleting team with id ${id}`);\r\n    console.log(`API URL: ${this.apiUrl}/${id}`);\r\n\r\n    return this.http.delete(`${this.apiUrl}/${id}`).pipe(\r\n      tap((data) => console.log('Team deleted, response:', data)),\r\n      catchError((error) => {\r\n        console.error('Error deleting team:', error);\r\n        console.error('Request URL:', `${this.apiUrl}/${id}`);\r\n        return this.handleError(error);\r\n      })\r\n    );\r\n  }\r\n\r\n  addMembreToEquipe(teamId: string, membre: any): Observable<any> {\r\n    console.log(`Adding member to team ${teamId}:`, membre);\r\n\r\n    // Créer l'objet attendu par le backend\r\n    const memberData = {\r\n      userId: membre.id,\r\n      role: membre.role || 'membre', // Utiliser le rôle spécifié ou \"membre\" par défaut\r\n    };\r\n\r\n    console.log('Sending to backend:', memberData);\r\n    console.log('Team ID type:', typeof teamId, 'value:', teamId);\r\n    console.log('User ID type:', typeof membre.id, 'value:', membre.id);\r\n\r\n    // Utiliser la route directe pour ajouter un membre à une équipe\r\n    return this.http\r\n      .post<any>(`${this.apiUrl}/${teamId}/members`, memberData)\r\n      .pipe(\r\n        tap((data) => console.log('Member added, response:', data)),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  removeMembreFromEquipe(teamId: string, membreId: string): Observable<any> {\r\n    console.log(`Removing member ${membreId} from team ${teamId}`);\r\n    console.log(`API URL: ${this.apiUrl}/${teamId}/members/${membreId}`);\r\n\r\n    // Utiliser la route directe pour supprimer un membre d'une équipe\r\n    return this.http\r\n      .delete<any>(`${this.apiUrl}/${teamId}/members/${membreId}`)\r\n      .pipe(\r\n        tap((data) => console.log('Member removed, response:', data)),\r\n        catchError((error) => {\r\n          console.error('Error removing member:', error);\r\n          console.error(\r\n            'Request URL:',\r\n            `${this.apiUrl}/${teamId}/members/${membreId}`\r\n          );\r\n          return this.handleError(error);\r\n        })\r\n      );\r\n  }\r\n\r\n  /**\r\n   * Récupère les détails des membres d'une équipe\r\n   * @param teamId ID de l'équipe\r\n   * @returns Observable contenant la liste des membres avec leurs détails\r\n   */\r\n  getTeamMembers(teamId: string): Observable<any[]> {\r\n    console.log(`Fetching team members for team ${teamId}`);\r\n    // Utiliser la route de l'équipe pour récupérer les détails de l'équipe, qui contient les membres\r\n    return this.http.get<any>(`${this.apiUrl}/${teamId}`).pipe(\r\n      map((team) => {\r\n        console.log('Team data received:', team);\r\n        // Transformer les IDs des membres en objets avec l'ID et le rôle\r\n        if (team && team.members) {\r\n          return team.members.map((memberId: string) => ({\r\n            user: memberId,\r\n            role: 'membre', // Par défaut, tous les membres ont le rôle \"membre\"\r\n            _id: memberId, // Utiliser l'ID du membre comme ID du TeamMember\r\n          }));\r\n        }\r\n        return [];\r\n      }),\r\n      tap((data) => console.log('Team members processed:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  private handleError(error: HttpErrorResponse) {\r\n    let errorMessage = '';\r\n\r\n    if (error.error instanceof ErrorEvent) {\r\n      // Erreur côté client\r\n      errorMessage = `Erreur client: ${error.error.message}`;\r\n    } else {\r\n      // Erreur côté serveur\r\n      const status = error.status;\r\n      const message = error.error?.message || error.statusText;\r\n\r\n      errorMessage = `Erreur serveur: Code ${status}, Message: ${message}`;\r\n\r\n      // Log des détails supplémentaires pour le débogage\r\n      console.error('Error details:', {\r\n        status: error.status,\r\n        statusText: error.statusText,\r\n        url: error.url,\r\n        error: error.error,\r\n      });\r\n\r\n      if (status === 0) {\r\n        console.error(\r\n          \"Le serveur est-il en cours d'exécution? Vérifiez la connexion réseau.\"\r\n        );\r\n      }\r\n    }\r\n\r\n    console.error('API Error:', errorMessage);\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n}\r\n", "import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpErrorResponse } from '@angular/common/http';\r\nimport { Observable, throwError } from 'rxjs';\r\nimport { catchError, tap } from 'rxjs/operators';\r\nimport { Membre } from '../models/membre.model';\r\nimport { environment } from '../../environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class MembreService {\r\n  private apiUrl = `${environment.urlBackend}teammembers`;\r\n\r\n  constructor(private http: HttpClient) {\r\n    console.log('Membre API URL:', this.apiUrl);\r\n  }\r\n\r\n  getMembres(): Observable<Membre[]> {\r\n    console.log('Fetching members from:', this.apiUrl);\r\n    return this.http.get<Membre[]>(this.apiUrl).pipe(\r\n      tap((data) => console.log('Members received:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  getMembre(id: string): Observable<Membre> {\r\n    console.log(`Fetching member with id ${id} from: ${this.apiUrl}/${id}`);\r\n    return this.http.get<Membre>(`${this.apiUrl}/${id}`).pipe(\r\n      tap((data) => console.log('Member received:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  addMembre(membre: Membre): Observable<Membre> {\r\n    console.log('Adding member:', membre);\r\n    return this.http.post<Membre>(this.apiUrl, membre).pipe(\r\n      tap((data) => console.log('Member added, response:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  deleteMembre(id: string): Observable<any> {\r\n    console.log(`Deleting member with id ${id}`);\r\n    return this.http.delete(`${this.apiUrl}/${id}`).pipe(\r\n      tap((data) => console.log('Member deleted, response:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  private handleError(error: HttpErrorResponse) {\r\n    let errorMessage = '';\r\n\r\n    if (error.error instanceof ErrorEvent) {\r\n      // Erreur côté client\r\n      errorMessage = `Erreur client: ${error.error.message}`;\r\n    } else {\r\n      // Erreur côté serveur\r\n      const status = error.status;\r\n      const message = error.error?.message || error.statusText;\r\n\r\n      errorMessage = `Erreur serveur: Code ${status}, Message: ${message}`;\r\n\r\n      // Log des détails supplémentaires pour le débogage\r\n      console.error('Error details:', {\r\n        status: error.status,\r\n        statusText: error.statusText,\r\n        url: error.url,\r\n        error: error.error,\r\n      });\r\n\r\n      if (status === 0) {\r\n        console.error(\r\n          \"Le serveur est-il en cours d'exécution? Vérifiez la connexion réseau.\"\r\n        );\r\n      }\r\n    }\r\n\r\n    console.error('API Error:', errorMessage);\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n}\r\n", "import { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\n\r\nexport interface Notification {\r\n  message: string;\r\n  type: 'success' | 'error' | 'info' | 'warning';\r\n  timeout?: number;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class NotificationService {\r\n  private notificationSubject = new BehaviorSubject<Notification | null>(null);\r\n\r\n  constructor() {}\r\n\r\n  getNotifications(): Observable<Notification | null> {\r\n    return this.notificationSubject.asObservable();\r\n  }\r\n\r\n  showSuccess(message: string, timeout: number = 5000): void {\r\n    this.show({ message, type: 'success', timeout });\r\n  }\r\n\r\n  showError(message: string, timeout: number = 5000): void {\r\n    this.show({ message, type: 'error', timeout });\r\n  }\r\n\r\n  showInfo(message: string, timeout: number = 5000): void {\r\n    this.show({ message, type: 'info', timeout });\r\n  }\r\n\r\n  showWarning(message: string, timeout: number = 5000): void {\r\n    this.show({ message, type: 'warning', timeout });\r\n  }\r\n\r\n  private show(notification: Notification): void {\r\n    this.notificationSubject.next(notification);\r\n\r\n    if (notification.timeout) {\r\n      setTimeout(() => {\r\n        // Effacer la notification seulement si c'est toujours la même\r\n        if (this.notificationSubject.value === notification) {\r\n          this.notificationSubject.next(null);\r\n        }\r\n      }, notification.timeout);\r\n    }\r\n  }\r\n\r\n  clear(): void {\r\n    this.notificationSubject.next(null);\r\n  }\r\n}\r\n", "import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpErrorResponse } from '@angular/common/http';\r\nimport { Observable, throwError } from 'rxjs';\r\nimport { catchError, tap } from 'rxjs/operators';\r\nimport { environment } from '../../environments/environment';\r\nimport { Task } from '../models/task.model';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class TaskService {\r\n  private apiUrl = `${environment.urlBackend}tasks`;\r\n\r\n  constructor(private http: HttpClient) {\r\n    console.log('Task API URL:', this.apiUrl);\r\n  }\r\n\r\n  // Récupérer toutes les tâches\r\n  getTasks(): Observable<Task[]> {\r\n    return this.http.get<Task[]>(this.apiUrl).pipe(\r\n      tap((data) => console.log('Tasks received:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Récupérer les tâches d'une équipe spécifique\r\n  getTasksByTeam(teamId: string): Observable<Task[]> {\r\n    return this.http.get<Task[]>(`${this.apiUrl}/team/${teamId}`).pipe(\r\n      tap((data) => console.log(`Tasks for team ${teamId} received:`, data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Récupérer une tâche par son ID\r\n  getTask(id: string): Observable<Task> {\r\n    return this.http.get<Task>(`${this.apiUrl}/${id}`).pipe(\r\n      tap((data) => console.log('Task received:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Créer une nouvelle tâche\r\n  createTask(task: Task): Observable<Task> {\r\n    return this.http.post<Task>(this.apiUrl, task).pipe(\r\n      tap((data) => console.log('Task created:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Mettre à jour une tâche existante\r\n  updateTask(id: string, task: Task): Observable<Task> {\r\n    return this.http.put<Task>(`${this.apiUrl}/${id}`, task).pipe(\r\n      tap((data) => console.log('Task updated:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Supprimer une tâche\r\n  deleteTask(id: string): Observable<any> {\r\n    return this.http.delete(`${this.apiUrl}/${id}`).pipe(\r\n      tap((data) => console.log('Task deleted:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Mettre à jour le statut d'une tâche\r\n  updateTaskStatus(\r\n    id: string,\r\n    status: 'todo' | 'in-progress' | 'done'\r\n  ): Observable<Task> {\r\n    return this.http\r\n      .patch<Task>(`${this.apiUrl}/${id}/status`, { status })\r\n      .pipe(\r\n        tap((data) => console.log('Task status updated:', data)),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  // Gérer les erreurs HTTP\r\n  private handleError(error: HttpErrorResponse) {\r\n    let errorMessage = '';\r\n    if (error.error instanceof ErrorEvent) {\r\n      // Erreur côté client\r\n      errorMessage = `Error: ${error.error.message}`;\r\n    } else {\r\n      // Erreur côté serveur\r\n      errorMessage = `Error Code: ${error.status}\\nMessage: ${error.message}`;\r\n    }\r\n    console.error(errorMessage);\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n}\r\n", "import * as i0 from '@angular/core';\nimport { Injectable, Inject, InjectionToken, Directive, Optional, SkipSelf, Input, EventEmitter, Self, ContentChildren, ContentChild, Output, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { _getEventTarget, normalizePassiveListenerOptions, _getShadowRoot } from '@angular/cdk/platform';\nimport { coerceBooleanProperty, coerceElement, coerceNumberProperty, coerceArray } from '@angular/cdk/coercion';\nimport { isFakeTouchstartFromScreenReader, isFakeMousedownFromScreenReader } from '@angular/cdk/a11y';\nimport { Subject, Subscription, interval, animationFrameScheduler, Observable, merge } from 'rxjs';\nimport { takeUntil, map, take, startWith, tap, switchMap } from 'rxjs/operators';\nimport * as i1$1 from '@angular/cdk/bidi';\n\n/**\n * Shallow-extends a stylesheet object with another stylesheet-like object.\n * Note that the keys in `source` have to be dash-cased.\n * @docs-private\n */\nfunction extendStyles(dest, source, importantProperties) {\n    for (let key in source) {\n        if (source.hasOwnProperty(key)) {\n            const value = source[key];\n            if (value) {\n                dest.setProperty(key, value, importantProperties?.has(key) ? 'important' : '');\n            }\n            else {\n                dest.removeProperty(key);\n            }\n        }\n    }\n    return dest;\n}\n/**\n * Toggles whether the native drag interactions should be enabled for an element.\n * @param element Element on which to toggle the drag interactions.\n * @param enable Whether the drag interactions should be enabled.\n * @docs-private\n */\nfunction toggleNativeDragInteractions(element, enable) {\n    const userSelect = enable ? '' : 'none';\n    extendStyles(element.style, {\n        'touch-action': enable ? '' : 'none',\n        '-webkit-user-drag': enable ? '' : 'none',\n        '-webkit-tap-highlight-color': enable ? '' : 'transparent',\n        'user-select': userSelect,\n        '-ms-user-select': userSelect,\n        '-webkit-user-select': userSelect,\n        '-moz-user-select': userSelect,\n    });\n}\n/**\n * Toggles whether an element is visible while preserving its dimensions.\n * @param element Element whose visibility to toggle\n * @param enable Whether the element should be visible.\n * @param importantProperties Properties to be set as `!important`.\n * @docs-private\n */\nfunction toggleVisibility(element, enable, importantProperties) {\n    extendStyles(element.style, {\n        position: enable ? '' : 'fixed',\n        top: enable ? '' : '0',\n        opacity: enable ? '' : '0',\n        left: enable ? '' : '-999em',\n    }, importantProperties);\n}\n/**\n * Combines a transform string with an optional other transform\n * that exited before the base transform was applied.\n */\nfunction combineTransforms(transform, initialTransform) {\n    return initialTransform && initialTransform != 'none'\n        ? transform + ' ' + initialTransform\n        : transform;\n}\n\n/** Parses a CSS time value to milliseconds. */\nfunction parseCssTimeUnitsToMs(value) {\n    // Some browsers will return it in seconds, whereas others will return milliseconds.\n    const multiplier = value.toLowerCase().indexOf('ms') > -1 ? 1 : 1000;\n    return parseFloat(value) * multiplier;\n}\n/** Gets the transform transition duration, including the delay, of an element in milliseconds. */\nfunction getTransformTransitionDurationInMs(element) {\n    const computedStyle = getComputedStyle(element);\n    const transitionedProperties = parseCssPropertyValue(computedStyle, 'transition-property');\n    const property = transitionedProperties.find(prop => prop === 'transform' || prop === 'all');\n    // If there's no transition for `all` or `transform`, we shouldn't do anything.\n    if (!property) {\n        return 0;\n    }\n    // Get the index of the property that we're interested in and match\n    // it up to the same index in `transition-delay` and `transition-duration`.\n    const propertyIndex = transitionedProperties.indexOf(property);\n    const rawDurations = parseCssPropertyValue(computedStyle, 'transition-duration');\n    const rawDelays = parseCssPropertyValue(computedStyle, 'transition-delay');\n    return (parseCssTimeUnitsToMs(rawDurations[propertyIndex]) +\n        parseCssTimeUnitsToMs(rawDelays[propertyIndex]));\n}\n/** Parses out multiple values from a computed style into an array. */\nfunction parseCssPropertyValue(computedStyle, name) {\n    const value = computedStyle.getPropertyValue(name);\n    return value.split(',').map(part => part.trim());\n}\n\n/** Gets a mutable version of an element's bounding `ClientRect`. */\nfunction getMutableClientRect(element) {\n    const clientRect = element.getBoundingClientRect();\n    // We need to clone the `clientRect` here, because all the values on it are readonly\n    // and we need to be able to update them. Also we can't use a spread here, because\n    // the values on a `ClientRect` aren't own properties. See:\n    // https://developer.mozilla.org/en-US/docs/Web/API/Element/getBoundingClientRect#Notes\n    return {\n        top: clientRect.top,\n        right: clientRect.right,\n        bottom: clientRect.bottom,\n        left: clientRect.left,\n        width: clientRect.width,\n        height: clientRect.height,\n        x: clientRect.x,\n        y: clientRect.y,\n    };\n}\n/**\n * Checks whether some coordinates are within a `ClientRect`.\n * @param clientRect ClientRect that is being checked.\n * @param x Coordinates along the X axis.\n * @param y Coordinates along the Y axis.\n */\nfunction isInsideClientRect(clientRect, x, y) {\n    const { top, bottom, left, right } = clientRect;\n    return y >= top && y <= bottom && x >= left && x <= right;\n}\n/**\n * Updates the top/left positions of a `ClientRect`, as well as their bottom/right counterparts.\n * @param clientRect `ClientRect` that should be updated.\n * @param top Amount to add to the `top` position.\n * @param left Amount to add to the `left` position.\n */\nfunction adjustClientRect(clientRect, top, left) {\n    clientRect.top += top;\n    clientRect.bottom = clientRect.top + clientRect.height;\n    clientRect.left += left;\n    clientRect.right = clientRect.left + clientRect.width;\n}\n/**\n * Checks whether the pointer coordinates are close to a ClientRect.\n * @param rect ClientRect to check against.\n * @param threshold Threshold around the ClientRect.\n * @param pointerX Coordinates along the X axis.\n * @param pointerY Coordinates along the Y axis.\n */\nfunction isPointerNearClientRect(rect, threshold, pointerX, pointerY) {\n    const { top, right, bottom, left, width, height } = rect;\n    const xThreshold = width * threshold;\n    const yThreshold = height * threshold;\n    return (pointerY > top - yThreshold &&\n        pointerY < bottom + yThreshold &&\n        pointerX > left - xThreshold &&\n        pointerX < right + xThreshold);\n}\n\n/** Keeps track of the scroll position and dimensions of the parents of an element. */\nclass ParentPositionTracker {\n    constructor(_document) {\n        this._document = _document;\n        /** Cached positions of the scrollable parent elements. */\n        this.positions = new Map();\n    }\n    /** Clears the cached positions. */\n    clear() {\n        this.positions.clear();\n    }\n    /** Caches the positions. Should be called at the beginning of a drag sequence. */\n    cache(elements) {\n        this.clear();\n        this.positions.set(this._document, {\n            scrollPosition: this.getViewportScrollPosition(),\n        });\n        elements.forEach(element => {\n            this.positions.set(element, {\n                scrollPosition: { top: element.scrollTop, left: element.scrollLeft },\n                clientRect: getMutableClientRect(element),\n            });\n        });\n    }\n    /** Handles scrolling while a drag is taking place. */\n    handleScroll(event) {\n        const target = _getEventTarget(event);\n        const cachedPosition = this.positions.get(target);\n        if (!cachedPosition) {\n            return null;\n        }\n        const scrollPosition = cachedPosition.scrollPosition;\n        let newTop;\n        let newLeft;\n        if (target === this._document) {\n            const viewportScrollPosition = this.getViewportScrollPosition();\n            newTop = viewportScrollPosition.top;\n            newLeft = viewportScrollPosition.left;\n        }\n        else {\n            newTop = target.scrollTop;\n            newLeft = target.scrollLeft;\n        }\n        const topDifference = scrollPosition.top - newTop;\n        const leftDifference = scrollPosition.left - newLeft;\n        // Go through and update the cached positions of the scroll\n        // parents that are inside the element that was scrolled.\n        this.positions.forEach((position, node) => {\n            if (position.clientRect && target !== node && target.contains(node)) {\n                adjustClientRect(position.clientRect, topDifference, leftDifference);\n            }\n        });\n        scrollPosition.top = newTop;\n        scrollPosition.left = newLeft;\n        return { top: topDifference, left: leftDifference };\n    }\n    /**\n     * Gets the scroll position of the viewport. Note that we use the scrollX and scrollY directly,\n     * instead of going through the `ViewportRuler`, because the first value the ruler looks at is\n     * the top/left offset of the `document.documentElement` which works for most cases, but breaks\n     * if the element is offset by something like the `BlockScrollStrategy`.\n     */\n    getViewportScrollPosition() {\n        return { top: window.scrollY, left: window.scrollX };\n    }\n}\n\n/** Creates a deep clone of an element. */\nfunction deepCloneNode(node) {\n    const clone = node.cloneNode(true);\n    const descendantsWithId = clone.querySelectorAll('[id]');\n    const nodeName = node.nodeName.toLowerCase();\n    // Remove the `id` to avoid having multiple elements with the same id on the page.\n    clone.removeAttribute('id');\n    for (let i = 0; i < descendantsWithId.length; i++) {\n        descendantsWithId[i].removeAttribute('id');\n    }\n    if (nodeName === 'canvas') {\n        transferCanvasData(node, clone);\n    }\n    else if (nodeName === 'input' || nodeName === 'select' || nodeName === 'textarea') {\n        transferInputData(node, clone);\n    }\n    transferData('canvas', node, clone, transferCanvasData);\n    transferData('input, textarea, select', node, clone, transferInputData);\n    return clone;\n}\n/** Matches elements between an element and its clone and allows for their data to be cloned. */\nfunction transferData(selector, node, clone, callback) {\n    const descendantElements = node.querySelectorAll(selector);\n    if (descendantElements.length) {\n        const cloneElements = clone.querySelectorAll(selector);\n        for (let i = 0; i < descendantElements.length; i++) {\n            callback(descendantElements[i], cloneElements[i]);\n        }\n    }\n}\n// Counter for unique cloned radio button names.\nlet cloneUniqueId = 0;\n/** Transfers the data of one input element to another. */\nfunction transferInputData(source, clone) {\n    // Browsers throw an error when assigning the value of a file input programmatically.\n    if (clone.type !== 'file') {\n        clone.value = source.value;\n    }\n    // Radio button `name` attributes must be unique for radio button groups\n    // otherwise original radio buttons can lose their checked state\n    // once the clone is inserted in the DOM.\n    if (clone.type === 'radio' && clone.name) {\n        clone.name = `mat-clone-${clone.name}-${cloneUniqueId++}`;\n    }\n}\n/** Transfers the data of one canvas element to another. */\nfunction transferCanvasData(source, clone) {\n    const context = clone.getContext('2d');\n    if (context) {\n        // In some cases `drawImage` can throw (e.g. if the canvas size is 0x0).\n        // We can't do much about it so just ignore the error.\n        try {\n            context.drawImage(source, 0, 0);\n        }\n        catch { }\n    }\n}\n\n/** Options that can be used to bind a passive event listener. */\nconst passiveEventListenerOptions = normalizePassiveListenerOptions({ passive: true });\n/** Options that can be used to bind an active event listener. */\nconst activeEventListenerOptions = normalizePassiveListenerOptions({ passive: false });\n/**\n * Time in milliseconds for which to ignore mouse events, after\n * receiving a touch event. Used to avoid doing double work for\n * touch devices where the browser fires fake mouse events, in\n * addition to touch events.\n */\nconst MOUSE_EVENT_IGNORE_TIME = 800;\n/** Inline styles to be set as `!important` while dragging. */\nconst dragImportantProperties = new Set([\n    // Needs to be important, because some `mat-table` sets `position: sticky !important`. See #22781.\n    'position',\n]);\n/**\n * Reference to a draggable item. Used to manipulate or dispose of the item.\n */\nclass DragRef {\n    /** Whether starting to drag this element is disabled. */\n    get disabled() {\n        return this._disabled || !!(this._dropContainer && this._dropContainer.disabled);\n    }\n    set disabled(value) {\n        const newValue = coerceBooleanProperty(value);\n        if (newValue !== this._disabled) {\n            this._disabled = newValue;\n            this._toggleNativeDragInteractions();\n            this._handles.forEach(handle => toggleNativeDragInteractions(handle, newValue));\n        }\n    }\n    constructor(element, _config, _document, _ngZone, _viewportRuler, _dragDropRegistry) {\n        this._config = _config;\n        this._document = _document;\n        this._ngZone = _ngZone;\n        this._viewportRuler = _viewportRuler;\n        this._dragDropRegistry = _dragDropRegistry;\n        /**\n         * CSS `transform` applied to the element when it isn't being dragged. We need a\n         * passive transform in order for the dragged element to retain its new position\n         * after the user has stopped dragging and because we need to know the relative\n         * position in case they start dragging again. This corresponds to `element.style.transform`.\n         */\n        this._passiveTransform = { x: 0, y: 0 };\n        /** CSS `transform` that is applied to the element while it's being dragged. */\n        this._activeTransform = { x: 0, y: 0 };\n        /**\n         * Whether the dragging sequence has been started. Doesn't\n         * necessarily mean that the element has been moved.\n         */\n        this._hasStartedDragging = false;\n        /** Emits when the item is being moved. */\n        this._moveEvents = new Subject();\n        /** Subscription to pointer movement events. */\n        this._pointerMoveSubscription = Subscription.EMPTY;\n        /** Subscription to the event that is dispatched when the user lifts their pointer. */\n        this._pointerUpSubscription = Subscription.EMPTY;\n        /** Subscription to the viewport being scrolled. */\n        this._scrollSubscription = Subscription.EMPTY;\n        /** Subscription to the viewport being resized. */\n        this._resizeSubscription = Subscription.EMPTY;\n        /** Cached reference to the boundary element. */\n        this._boundaryElement = null;\n        /** Whether the native dragging interactions have been enabled on the root element. */\n        this._nativeInteractionsEnabled = true;\n        /** Elements that can be used to drag the draggable item. */\n        this._handles = [];\n        /** Registered handles that are currently disabled. */\n        this._disabledHandles = new Set();\n        /** Layout direction of the item. */\n        this._direction = 'ltr';\n        /**\n         * Amount of milliseconds to wait after the user has put their\n         * pointer down before starting to drag the element.\n         */\n        this.dragStartDelay = 0;\n        this._disabled = false;\n        /** Emits as the drag sequence is being prepared. */\n        this.beforeStarted = new Subject();\n        /** Emits when the user starts dragging the item. */\n        this.started = new Subject();\n        /** Emits when the user has released a drag item, before any animations have started. */\n        this.released = new Subject();\n        /** Emits when the user stops dragging an item in the container. */\n        this.ended = new Subject();\n        /** Emits when the user has moved the item into a new container. */\n        this.entered = new Subject();\n        /** Emits when the user removes the item its container by dragging it into another container. */\n        this.exited = new Subject();\n        /** Emits when the user drops the item inside a container. */\n        this.dropped = new Subject();\n        /**\n         * Emits as the user is dragging the item. Use with caution,\n         * because this event will fire for every pixel that the user has dragged.\n         */\n        this.moved = this._moveEvents;\n        /** Handler for the `mousedown`/`touchstart` events. */\n        this._pointerDown = (event) => {\n            this.beforeStarted.next();\n            // Delegate the event based on whether it started from a handle or the element itself.\n            if (this._handles.length) {\n                const targetHandle = this._getTargetHandle(event);\n                if (targetHandle && !this._disabledHandles.has(targetHandle) && !this.disabled) {\n                    this._initializeDragSequence(targetHandle, event);\n                }\n            }\n            else if (!this.disabled) {\n                this._initializeDragSequence(this._rootElement, event);\n            }\n        };\n        /** Handler that is invoked when the user moves their pointer after they've initiated a drag. */\n        this._pointerMove = (event) => {\n            const pointerPosition = this._getPointerPositionOnPage(event);\n            if (!this._hasStartedDragging) {\n                const distanceX = Math.abs(pointerPosition.x - this._pickupPositionOnPage.x);\n                const distanceY = Math.abs(pointerPosition.y - this._pickupPositionOnPage.y);\n                const isOverThreshold = distanceX + distanceY >= this._config.dragStartThreshold;\n                // Only start dragging after the user has moved more than the minimum distance in either\n                // direction. Note that this is preferable over doing something like `skip(minimumDistance)`\n                // in the `pointerMove` subscription, because we're not guaranteed to have one move event\n                // per pixel of movement (e.g. if the user moves their pointer quickly).\n                if (isOverThreshold) {\n                    const isDelayElapsed = Date.now() >= this._dragStartTime + this._getDragStartDelay(event);\n                    const container = this._dropContainer;\n                    if (!isDelayElapsed) {\n                        this._endDragSequence(event);\n                        return;\n                    }\n                    // Prevent other drag sequences from starting while something in the container is still\n                    // being dragged. This can happen while we're waiting for the drop animation to finish\n                    // and can cause errors, because some elements might still be moving around.\n                    if (!container || (!container.isDragging() && !container.isReceiving())) {\n                        // Prevent the default action as soon as the dragging sequence is considered as\n                        // \"started\" since waiting for the next event can allow the device to begin scrolling.\n                        event.preventDefault();\n                        this._hasStartedDragging = true;\n                        this._ngZone.run(() => this._startDragSequence(event));\n                    }\n                }\n                return;\n            }\n            // We prevent the default action down here so that we know that dragging has started. This is\n            // important for touch devices where doing this too early can unnecessarily block scrolling,\n            // if there's a dragging delay.\n            event.preventDefault();\n            const constrainedPointerPosition = this._getConstrainedPointerPosition(pointerPosition);\n            this._hasMoved = true;\n            this._lastKnownPointerPosition = pointerPosition;\n            this._updatePointerDirectionDelta(constrainedPointerPosition);\n            if (this._dropContainer) {\n                this._updateActiveDropContainer(constrainedPointerPosition, pointerPosition);\n            }\n            else {\n                // If there's a position constraint function, we want the element's top/left to be at the\n                // specific position on the page. Use the initial position as a reference if that's the case.\n                const offset = this.constrainPosition ? this._initialClientRect : this._pickupPositionOnPage;\n                const activeTransform = this._activeTransform;\n                activeTransform.x = constrainedPointerPosition.x - offset.x + this._passiveTransform.x;\n                activeTransform.y = constrainedPointerPosition.y - offset.y + this._passiveTransform.y;\n                this._applyRootElementTransform(activeTransform.x, activeTransform.y);\n            }\n            // Since this event gets fired for every pixel while dragging, we only\n            // want to fire it if the consumer opted into it. Also we have to\n            // re-enter the zone because we run all of the events on the outside.\n            if (this._moveEvents.observers.length) {\n                this._ngZone.run(() => {\n                    this._moveEvents.next({\n                        source: this,\n                        pointerPosition: constrainedPointerPosition,\n                        event,\n                        distance: this._getDragDistance(constrainedPointerPosition),\n                        delta: this._pointerDirectionDelta,\n                    });\n                });\n            }\n        };\n        /** Handler that is invoked when the user lifts their pointer up, after initiating a drag. */\n        this._pointerUp = (event) => {\n            this._endDragSequence(event);\n        };\n        /** Handles a native `dragstart` event. */\n        this._nativeDragStart = (event) => {\n            if (this._handles.length) {\n                const targetHandle = this._getTargetHandle(event);\n                if (targetHandle && !this._disabledHandles.has(targetHandle) && !this.disabled) {\n                    event.preventDefault();\n                }\n            }\n            else if (!this.disabled) {\n                // Usually this isn't necessary since the we prevent the default action in `pointerDown`,\n                // but some cases like dragging of links can slip through (see #24403).\n                event.preventDefault();\n            }\n        };\n        this.withRootElement(element).withParent(_config.parentDragRef || null);\n        this._parentPositions = new ParentPositionTracker(_document);\n        _dragDropRegistry.registerDragItem(this);\n    }\n    /**\n     * Returns the element that is being used as a placeholder\n     * while the current element is being dragged.\n     */\n    getPlaceholderElement() {\n        return this._placeholder;\n    }\n    /** Returns the root draggable element. */\n    getRootElement() {\n        return this._rootElement;\n    }\n    /**\n     * Gets the currently-visible element that represents the drag item.\n     * While dragging this is the placeholder, otherwise it's the root element.\n     */\n    getVisibleElement() {\n        return this.isDragging() ? this.getPlaceholderElement() : this.getRootElement();\n    }\n    /** Registers the handles that can be used to drag the element. */\n    withHandles(handles) {\n        this._handles = handles.map(handle => coerceElement(handle));\n        this._handles.forEach(handle => toggleNativeDragInteractions(handle, this.disabled));\n        this._toggleNativeDragInteractions();\n        // Delete any lingering disabled handles that may have been destroyed. Note that we re-create\n        // the set, rather than iterate over it and filter out the destroyed handles, because while\n        // the ES spec allows for sets to be modified while they're being iterated over, some polyfills\n        // use an array internally which may throw an error.\n        const disabledHandles = new Set();\n        this._disabledHandles.forEach(handle => {\n            if (this._handles.indexOf(handle) > -1) {\n                disabledHandles.add(handle);\n            }\n        });\n        this._disabledHandles = disabledHandles;\n        return this;\n    }\n    /**\n     * Registers the template that should be used for the drag preview.\n     * @param template Template that from which to stamp out the preview.\n     */\n    withPreviewTemplate(template) {\n        this._previewTemplate = template;\n        return this;\n    }\n    /**\n     * Registers the template that should be used for the drag placeholder.\n     * @param template Template that from which to stamp out the placeholder.\n     */\n    withPlaceholderTemplate(template) {\n        this._placeholderTemplate = template;\n        return this;\n    }\n    /**\n     * Sets an alternate drag root element. The root element is the element that will be moved as\n     * the user is dragging. Passing an alternate root element is useful when trying to enable\n     * dragging on an element that you might not have access to.\n     */\n    withRootElement(rootElement) {\n        const element = coerceElement(rootElement);\n        if (element !== this._rootElement) {\n            if (this._rootElement) {\n                this._removeRootElementListeners(this._rootElement);\n            }\n            this._ngZone.runOutsideAngular(() => {\n                element.addEventListener('mousedown', this._pointerDown, activeEventListenerOptions);\n                element.addEventListener('touchstart', this._pointerDown, passiveEventListenerOptions);\n                element.addEventListener('dragstart', this._nativeDragStart, activeEventListenerOptions);\n            });\n            this._initialTransform = undefined;\n            this._rootElement = element;\n        }\n        if (typeof SVGElement !== 'undefined' && this._rootElement instanceof SVGElement) {\n            this._ownerSVGElement = this._rootElement.ownerSVGElement;\n        }\n        return this;\n    }\n    /**\n     * Element to which the draggable's position will be constrained.\n     */\n    withBoundaryElement(boundaryElement) {\n        this._boundaryElement = boundaryElement ? coerceElement(boundaryElement) : null;\n        this._resizeSubscription.unsubscribe();\n        if (boundaryElement) {\n            this._resizeSubscription = this._viewportRuler\n                .change(10)\n                .subscribe(() => this._containInsideBoundaryOnResize());\n        }\n        return this;\n    }\n    /** Sets the parent ref that the ref is nested in.  */\n    withParent(parent) {\n        this._parentDragRef = parent;\n        return this;\n    }\n    /** Removes the dragging functionality from the DOM element. */\n    dispose() {\n        this._removeRootElementListeners(this._rootElement);\n        // Do this check before removing from the registry since it'll\n        // stop being considered as dragged once it is removed.\n        if (this.isDragging()) {\n            // Since we move out the element to the end of the body while it's being\n            // dragged, we have to make sure that it's removed if it gets destroyed.\n            this._rootElement?.remove();\n        }\n        this._anchor?.remove();\n        this._destroyPreview();\n        this._destroyPlaceholder();\n        this._dragDropRegistry.removeDragItem(this);\n        this._removeSubscriptions();\n        this.beforeStarted.complete();\n        this.started.complete();\n        this.released.complete();\n        this.ended.complete();\n        this.entered.complete();\n        this.exited.complete();\n        this.dropped.complete();\n        this._moveEvents.complete();\n        this._handles = [];\n        this._disabledHandles.clear();\n        this._dropContainer = undefined;\n        this._resizeSubscription.unsubscribe();\n        this._parentPositions.clear();\n        this._boundaryElement =\n            this._rootElement =\n                this._ownerSVGElement =\n                    this._placeholderTemplate =\n                        this._previewTemplate =\n                            this._anchor =\n                                this._parentDragRef =\n                                    null;\n    }\n    /** Checks whether the element is currently being dragged. */\n    isDragging() {\n        return this._hasStartedDragging && this._dragDropRegistry.isDragging(this);\n    }\n    /** Resets a standalone drag item to its initial position. */\n    reset() {\n        this._rootElement.style.transform = this._initialTransform || '';\n        this._activeTransform = { x: 0, y: 0 };\n        this._passiveTransform = { x: 0, y: 0 };\n    }\n    /**\n     * Sets a handle as disabled. While a handle is disabled, it'll capture and interrupt dragging.\n     * @param handle Handle element that should be disabled.\n     */\n    disableHandle(handle) {\n        if (!this._disabledHandles.has(handle) && this._handles.indexOf(handle) > -1) {\n            this._disabledHandles.add(handle);\n            toggleNativeDragInteractions(handle, true);\n        }\n    }\n    /**\n     * Enables a handle, if it has been disabled.\n     * @param handle Handle element to be enabled.\n     */\n    enableHandle(handle) {\n        if (this._disabledHandles.has(handle)) {\n            this._disabledHandles.delete(handle);\n            toggleNativeDragInteractions(handle, this.disabled);\n        }\n    }\n    /** Sets the layout direction of the draggable item. */\n    withDirection(direction) {\n        this._direction = direction;\n        return this;\n    }\n    /** Sets the container that the item is part of. */\n    _withDropContainer(container) {\n        this._dropContainer = container;\n    }\n    /**\n     * Gets the current position in pixels the draggable outside of a drop container.\n     */\n    getFreeDragPosition() {\n        const position = this.isDragging() ? this._activeTransform : this._passiveTransform;\n        return { x: position.x, y: position.y };\n    }\n    /**\n     * Sets the current position in pixels the draggable outside of a drop container.\n     * @param value New position to be set.\n     */\n    setFreeDragPosition(value) {\n        this._activeTransform = { x: 0, y: 0 };\n        this._passiveTransform.x = value.x;\n        this._passiveTransform.y = value.y;\n        if (!this._dropContainer) {\n            this._applyRootElementTransform(value.x, value.y);\n        }\n        return this;\n    }\n    /**\n     * Sets the container into which to insert the preview element.\n     * @param value Container into which to insert the preview.\n     */\n    withPreviewContainer(value) {\n        this._previewContainer = value;\n        return this;\n    }\n    /** Updates the item's sort order based on the last-known pointer position. */\n    _sortFromLastPointerPosition() {\n        const position = this._lastKnownPointerPosition;\n        if (position && this._dropContainer) {\n            this._updateActiveDropContainer(this._getConstrainedPointerPosition(position), position);\n        }\n    }\n    /** Unsubscribes from the global subscriptions. */\n    _removeSubscriptions() {\n        this._pointerMoveSubscription.unsubscribe();\n        this._pointerUpSubscription.unsubscribe();\n        this._scrollSubscription.unsubscribe();\n    }\n    /** Destroys the preview element and its ViewRef. */\n    _destroyPreview() {\n        this._preview?.remove();\n        this._previewRef?.destroy();\n        this._preview = this._previewRef = null;\n    }\n    /** Destroys the placeholder element and its ViewRef. */\n    _destroyPlaceholder() {\n        this._placeholder?.remove();\n        this._placeholderRef?.destroy();\n        this._placeholder = this._placeholderRef = null;\n    }\n    /**\n     * Clears subscriptions and stops the dragging sequence.\n     * @param event Browser event object that ended the sequence.\n     */\n    _endDragSequence(event) {\n        // Note that here we use `isDragging` from the service, rather than from `this`.\n        // The difference is that the one from the service reflects whether a dragging sequence\n        // has been initiated, whereas the one on `this` includes whether the user has passed\n        // the minimum dragging threshold.\n        if (!this._dragDropRegistry.isDragging(this)) {\n            return;\n        }\n        this._removeSubscriptions();\n        this._dragDropRegistry.stopDragging(this);\n        this._toggleNativeDragInteractions();\n        if (this._handles) {\n            this._rootElement.style.webkitTapHighlightColor =\n                this._rootElementTapHighlight;\n        }\n        if (!this._hasStartedDragging) {\n            return;\n        }\n        this.released.next({ source: this, event });\n        if (this._dropContainer) {\n            // Stop scrolling immediately, instead of waiting for the animation to finish.\n            this._dropContainer._stopScrolling();\n            this._animatePreviewToPlaceholder().then(() => {\n                this._cleanupDragArtifacts(event);\n                this._cleanupCachedDimensions();\n                this._dragDropRegistry.stopDragging(this);\n            });\n        }\n        else {\n            // Convert the active transform into a passive one. This means that next time\n            // the user starts dragging the item, its position will be calculated relatively\n            // to the new passive transform.\n            this._passiveTransform.x = this._activeTransform.x;\n            const pointerPosition = this._getPointerPositionOnPage(event);\n            this._passiveTransform.y = this._activeTransform.y;\n            this._ngZone.run(() => {\n                this.ended.next({\n                    source: this,\n                    distance: this._getDragDistance(pointerPosition),\n                    dropPoint: pointerPosition,\n                    event,\n                });\n            });\n            this._cleanupCachedDimensions();\n            this._dragDropRegistry.stopDragging(this);\n        }\n    }\n    /** Starts the dragging sequence. */\n    _startDragSequence(event) {\n        if (isTouchEvent(event)) {\n            this._lastTouchEventTime = Date.now();\n        }\n        this._toggleNativeDragInteractions();\n        const dropContainer = this._dropContainer;\n        if (dropContainer) {\n            const element = this._rootElement;\n            const parent = element.parentNode;\n            const placeholder = (this._placeholder = this._createPlaceholderElement());\n            const anchor = (this._anchor = this._anchor || this._document.createComment(''));\n            // Needs to happen before the root element is moved.\n            const shadowRoot = this._getShadowRoot();\n            // Insert an anchor node so that we can restore the element's position in the DOM.\n            parent.insertBefore(anchor, element);\n            // There's no risk of transforms stacking when inside a drop container so\n            // we can keep the initial transform up to date any time dragging starts.\n            this._initialTransform = element.style.transform || '';\n            // Create the preview after the initial transform has\n            // been cached, because it can be affected by the transform.\n            this._preview = this._createPreviewElement();\n            // We move the element out at the end of the body and we make it hidden, because keeping it in\n            // place will throw off the consumer's `:last-child` selectors. We can't remove the element\n            // from the DOM completely, because iOS will stop firing all subsequent events in the chain.\n            toggleVisibility(element, false, dragImportantProperties);\n            this._document.body.appendChild(parent.replaceChild(placeholder, element));\n            this._getPreviewInsertionPoint(parent, shadowRoot).appendChild(this._preview);\n            this.started.next({ source: this, event }); // Emit before notifying the container.\n            dropContainer.start();\n            this._initialContainer = dropContainer;\n            this._initialIndex = dropContainer.getItemIndex(this);\n        }\n        else {\n            this.started.next({ source: this, event });\n            this._initialContainer = this._initialIndex = undefined;\n        }\n        // Important to run after we've called `start` on the parent container\n        // so that it has had time to resolve its scrollable parents.\n        this._parentPositions.cache(dropContainer ? dropContainer.getScrollableParents() : []);\n    }\n    /**\n     * Sets up the different variables and subscriptions\n     * that will be necessary for the dragging sequence.\n     * @param referenceElement Element that started the drag sequence.\n     * @param event Browser event object that started the sequence.\n     */\n    _initializeDragSequence(referenceElement, event) {\n        // Stop propagation if the item is inside another\n        // draggable so we don't start multiple drag sequences.\n        if (this._parentDragRef) {\n            event.stopPropagation();\n        }\n        const isDragging = this.isDragging();\n        const isTouchSequence = isTouchEvent(event);\n        const isAuxiliaryMouseButton = !isTouchSequence && event.button !== 0;\n        const rootElement = this._rootElement;\n        const target = _getEventTarget(event);\n        const isSyntheticEvent = !isTouchSequence &&\n            this._lastTouchEventTime &&\n            this._lastTouchEventTime + MOUSE_EVENT_IGNORE_TIME > Date.now();\n        const isFakeEvent = isTouchSequence\n            ? isFakeTouchstartFromScreenReader(event)\n            : isFakeMousedownFromScreenReader(event);\n        // If the event started from an element with the native HTML drag&drop, it'll interfere\n        // with our own dragging (e.g. `img` tags do it by default). Prevent the default action\n        // to stop it from happening. Note that preventing on `dragstart` also seems to work, but\n        // it's flaky and it fails if the user drags it away quickly. Also note that we only want\n        // to do this for `mousedown` since doing the same for `touchstart` will stop any `click`\n        // events from firing on touch devices.\n        if (target && target.draggable && event.type === 'mousedown') {\n            event.preventDefault();\n        }\n        // Abort if the user is already dragging or is using a mouse button other than the primary one.\n        if (isDragging || isAuxiliaryMouseButton || isSyntheticEvent || isFakeEvent) {\n            return;\n        }\n        // If we've got handles, we need to disable the tap highlight on the entire root element,\n        // otherwise iOS will still add it, even though all the drag interactions on the handle\n        // are disabled.\n        if (this._handles.length) {\n            const rootStyles = rootElement.style;\n            this._rootElementTapHighlight = rootStyles.webkitTapHighlightColor || '';\n            rootStyles.webkitTapHighlightColor = 'transparent';\n        }\n        this._hasStartedDragging = this._hasMoved = false;\n        // Avoid multiple subscriptions and memory leaks when multi touch\n        // (isDragging check above isn't enough because of possible temporal and/or dimensional delays)\n        this._removeSubscriptions();\n        this._initialClientRect = this._rootElement.getBoundingClientRect();\n        this._pointerMoveSubscription = this._dragDropRegistry.pointerMove.subscribe(this._pointerMove);\n        this._pointerUpSubscription = this._dragDropRegistry.pointerUp.subscribe(this._pointerUp);\n        this._scrollSubscription = this._dragDropRegistry\n            .scrolled(this._getShadowRoot())\n            .subscribe(scrollEvent => this._updateOnScroll(scrollEvent));\n        if (this._boundaryElement) {\n            this._boundaryRect = getMutableClientRect(this._boundaryElement);\n        }\n        // If we have a custom preview we can't know ahead of time how large it'll be so we position\n        // it next to the cursor. The exception is when the consumer has opted into making the preview\n        // the same size as the root element, in which case we do know the size.\n        const previewTemplate = this._previewTemplate;\n        this._pickupPositionInElement =\n            previewTemplate && previewTemplate.template && !previewTemplate.matchSize\n                ? { x: 0, y: 0 }\n                : this._getPointerPositionInElement(this._initialClientRect, referenceElement, event);\n        const pointerPosition = (this._pickupPositionOnPage =\n            this._lastKnownPointerPosition =\n                this._getPointerPositionOnPage(event));\n        this._pointerDirectionDelta = { x: 0, y: 0 };\n        this._pointerPositionAtLastDirectionChange = { x: pointerPosition.x, y: pointerPosition.y };\n        this._dragStartTime = Date.now();\n        this._dragDropRegistry.startDragging(this, event);\n    }\n    /** Cleans up the DOM artifacts that were added to facilitate the element being dragged. */\n    _cleanupDragArtifacts(event) {\n        // Restore the element's visibility and insert it at its old position in the DOM.\n        // It's important that we maintain the position, because moving the element around in the DOM\n        // can throw off `NgFor` which does smart diffing and re-creates elements only when necessary,\n        // while moving the existing elements in all other cases.\n        toggleVisibility(this._rootElement, true, dragImportantProperties);\n        this._anchor.parentNode.replaceChild(this._rootElement, this._anchor);\n        this._destroyPreview();\n        this._destroyPlaceholder();\n        this._initialClientRect =\n            this._boundaryRect =\n                this._previewRect =\n                    this._initialTransform =\n                        undefined;\n        // Re-enter the NgZone since we bound `document` events on the outside.\n        this._ngZone.run(() => {\n            const container = this._dropContainer;\n            const currentIndex = container.getItemIndex(this);\n            const pointerPosition = this._getPointerPositionOnPage(event);\n            const distance = this._getDragDistance(pointerPosition);\n            const isPointerOverContainer = container._isOverContainer(pointerPosition.x, pointerPosition.y);\n            this.ended.next({ source: this, distance, dropPoint: pointerPosition, event });\n            this.dropped.next({\n                item: this,\n                currentIndex,\n                previousIndex: this._initialIndex,\n                container: container,\n                previousContainer: this._initialContainer,\n                isPointerOverContainer,\n                distance,\n                dropPoint: pointerPosition,\n                event,\n            });\n            container.drop(this, currentIndex, this._initialIndex, this._initialContainer, isPointerOverContainer, distance, pointerPosition, event);\n            this._dropContainer = this._initialContainer;\n        });\n    }\n    /**\n     * Updates the item's position in its drop container, or moves it\n     * into a new one, depending on its current drag position.\n     */\n    _updateActiveDropContainer({ x, y }, { x: rawX, y: rawY }) {\n        // Drop container that draggable has been moved into.\n        let newContainer = this._initialContainer._getSiblingContainerFromPosition(this, x, y);\n        // If we couldn't find a new container to move the item into, and the item has left its\n        // initial container, check whether the it's over the initial container. This handles the\n        // case where two containers are connected one way and the user tries to undo dragging an\n        // item into a new container.\n        if (!newContainer &&\n            this._dropContainer !== this._initialContainer &&\n            this._initialContainer._isOverContainer(x, y)) {\n            newContainer = this._initialContainer;\n        }\n        if (newContainer && newContainer !== this._dropContainer) {\n            this._ngZone.run(() => {\n                // Notify the old container that the item has left.\n                this.exited.next({ item: this, container: this._dropContainer });\n                this._dropContainer.exit(this);\n                // Notify the new container that the item has entered.\n                this._dropContainer = newContainer;\n                this._dropContainer.enter(this, x, y, newContainer === this._initialContainer &&\n                    // If we're re-entering the initial container and sorting is disabled,\n                    // put item the into its starting index to begin with.\n                    newContainer.sortingDisabled\n                    ? this._initialIndex\n                    : undefined);\n                this.entered.next({\n                    item: this,\n                    container: newContainer,\n                    currentIndex: newContainer.getItemIndex(this),\n                });\n            });\n        }\n        // Dragging may have been interrupted as a result of the events above.\n        if (this.isDragging()) {\n            this._dropContainer._startScrollingIfNecessary(rawX, rawY);\n            this._dropContainer._sortItem(this, x, y, this._pointerDirectionDelta);\n            if (this.constrainPosition) {\n                this._applyPreviewTransform(x, y);\n            }\n            else {\n                this._applyPreviewTransform(x - this._pickupPositionInElement.x, y - this._pickupPositionInElement.y);\n            }\n        }\n    }\n    /**\n     * Creates the element that will be rendered next to the user's pointer\n     * and will be used as a preview of the element that is being dragged.\n     */\n    _createPreviewElement() {\n        const previewConfig = this._previewTemplate;\n        const previewClass = this.previewClass;\n        const previewTemplate = previewConfig ? previewConfig.template : null;\n        let preview;\n        if (previewTemplate && previewConfig) {\n            // Measure the element before we've inserted the preview\n            // since the insertion could throw off the measurement.\n            const rootRect = previewConfig.matchSize ? this._initialClientRect : null;\n            const viewRef = previewConfig.viewContainer.createEmbeddedView(previewTemplate, previewConfig.context);\n            viewRef.detectChanges();\n            preview = getRootNode(viewRef, this._document);\n            this._previewRef = viewRef;\n            if (previewConfig.matchSize) {\n                matchElementSize(preview, rootRect);\n            }\n            else {\n                preview.style.transform = getTransform(this._pickupPositionOnPage.x, this._pickupPositionOnPage.y);\n            }\n        }\n        else {\n            preview = deepCloneNode(this._rootElement);\n            matchElementSize(preview, this._initialClientRect);\n            if (this._initialTransform) {\n                preview.style.transform = this._initialTransform;\n            }\n        }\n        extendStyles(preview.style, {\n            // It's important that we disable the pointer events on the preview, because\n            // it can throw off the `document.elementFromPoint` calls in the `CdkDropList`.\n            'pointer-events': 'none',\n            // We have to reset the margin, because it can throw off positioning relative to the viewport.\n            'margin': '0',\n            'position': 'fixed',\n            'top': '0',\n            'left': '0',\n            'z-index': `${this._config.zIndex || 1000}`,\n        }, dragImportantProperties);\n        toggleNativeDragInteractions(preview, false);\n        preview.classList.add('cdk-drag-preview');\n        preview.setAttribute('dir', this._direction);\n        if (previewClass) {\n            if (Array.isArray(previewClass)) {\n                previewClass.forEach(className => preview.classList.add(className));\n            }\n            else {\n                preview.classList.add(previewClass);\n            }\n        }\n        return preview;\n    }\n    /**\n     * Animates the preview element from its current position to the location of the drop placeholder.\n     * @returns Promise that resolves when the animation completes.\n     */\n    _animatePreviewToPlaceholder() {\n        // If the user hasn't moved yet, the transitionend event won't fire.\n        if (!this._hasMoved) {\n            return Promise.resolve();\n        }\n        const placeholderRect = this._placeholder.getBoundingClientRect();\n        // Apply the class that adds a transition to the preview.\n        this._preview.classList.add('cdk-drag-animating');\n        // Move the preview to the placeholder position.\n        this._applyPreviewTransform(placeholderRect.left, placeholderRect.top);\n        // If the element doesn't have a `transition`, the `transitionend` event won't fire. Since\n        // we need to trigger a style recalculation in order for the `cdk-drag-animating` class to\n        // apply its style, we take advantage of the available info to figure out whether we need to\n        // bind the event in the first place.\n        const duration = getTransformTransitionDurationInMs(this._preview);\n        if (duration === 0) {\n            return Promise.resolve();\n        }\n        return this._ngZone.runOutsideAngular(() => {\n            return new Promise(resolve => {\n                const handler = ((event) => {\n                    if (!event ||\n                        (_getEventTarget(event) === this._preview && event.propertyName === 'transform')) {\n                        this._preview?.removeEventListener('transitionend', handler);\n                        resolve();\n                        clearTimeout(timeout);\n                    }\n                });\n                // If a transition is short enough, the browser might not fire the `transitionend` event.\n                // Since we know how long it's supposed to take, add a timeout with a 50% buffer that'll\n                // fire if the transition hasn't completed when it was supposed to.\n                const timeout = setTimeout(handler, duration * 1.5);\n                this._preview.addEventListener('transitionend', handler);\n            });\n        });\n    }\n    /** Creates an element that will be shown instead of the current element while dragging. */\n    _createPlaceholderElement() {\n        const placeholderConfig = this._placeholderTemplate;\n        const placeholderTemplate = placeholderConfig ? placeholderConfig.template : null;\n        let placeholder;\n        if (placeholderTemplate) {\n            this._placeholderRef = placeholderConfig.viewContainer.createEmbeddedView(placeholderTemplate, placeholderConfig.context);\n            this._placeholderRef.detectChanges();\n            placeholder = getRootNode(this._placeholderRef, this._document);\n        }\n        else {\n            placeholder = deepCloneNode(this._rootElement);\n        }\n        // Stop pointer events on the preview so the user can't\n        // interact with it while the preview is animating.\n        placeholder.style.pointerEvents = 'none';\n        placeholder.classList.add('cdk-drag-placeholder');\n        return placeholder;\n    }\n    /**\n     * Figures out the coordinates at which an element was picked up.\n     * @param referenceElement Element that initiated the dragging.\n     * @param event Event that initiated the dragging.\n     */\n    _getPointerPositionInElement(elementRect, referenceElement, event) {\n        const handleElement = referenceElement === this._rootElement ? null : referenceElement;\n        const referenceRect = handleElement ? handleElement.getBoundingClientRect() : elementRect;\n        const point = isTouchEvent(event) ? event.targetTouches[0] : event;\n        const scrollPosition = this._getViewportScrollPosition();\n        const x = point.pageX - referenceRect.left - scrollPosition.left;\n        const y = point.pageY - referenceRect.top - scrollPosition.top;\n        return {\n            x: referenceRect.left - elementRect.left + x,\n            y: referenceRect.top - elementRect.top + y,\n        };\n    }\n    /** Determines the point of the page that was touched by the user. */\n    _getPointerPositionOnPage(event) {\n        const scrollPosition = this._getViewportScrollPosition();\n        const point = isTouchEvent(event)\n            ? // `touches` will be empty for start/end events so we have to fall back to `changedTouches`.\n                // Also note that on real devices we're guaranteed for either `touches` or `changedTouches`\n                // to have a value, but Firefox in device emulation mode has a bug where both can be empty\n                // for `touchstart` and `touchend` so we fall back to a dummy object in order to avoid\n                // throwing an error. The value returned here will be incorrect, but since this only\n                // breaks inside a developer tool and the value is only used for secondary information,\n                // we can get away with it. See https://bugzilla.mozilla.org/show_bug.cgi?id=1615824.\n                event.touches[0] || event.changedTouches[0] || { pageX: 0, pageY: 0 }\n            : event;\n        const x = point.pageX - scrollPosition.left;\n        const y = point.pageY - scrollPosition.top;\n        // if dragging SVG element, try to convert from the screen coordinate system to the SVG\n        // coordinate system\n        if (this._ownerSVGElement) {\n            const svgMatrix = this._ownerSVGElement.getScreenCTM();\n            if (svgMatrix) {\n                const svgPoint = this._ownerSVGElement.createSVGPoint();\n                svgPoint.x = x;\n                svgPoint.y = y;\n                return svgPoint.matrixTransform(svgMatrix.inverse());\n            }\n        }\n        return { x, y };\n    }\n    /** Gets the pointer position on the page, accounting for any position constraints. */\n    _getConstrainedPointerPosition(point) {\n        const dropContainerLock = this._dropContainer ? this._dropContainer.lockAxis : null;\n        let { x, y } = this.constrainPosition\n            ? this.constrainPosition(point, this, this._initialClientRect, this._pickupPositionInElement)\n            : point;\n        if (this.lockAxis === 'x' || dropContainerLock === 'x') {\n            y =\n                this._pickupPositionOnPage.y -\n                    (this.constrainPosition ? this._pickupPositionInElement.y : 0);\n        }\n        else if (this.lockAxis === 'y' || dropContainerLock === 'y') {\n            x =\n                this._pickupPositionOnPage.x -\n                    (this.constrainPosition ? this._pickupPositionInElement.x : 0);\n        }\n        if (this._boundaryRect) {\n            // If not using a custom constrain we need to account for the pickup position in the element\n            // otherwise we do not need to do this, as it has already been accounted for\n            const { x: pickupX, y: pickupY } = !this.constrainPosition\n                ? this._pickupPositionInElement\n                : { x: 0, y: 0 };\n            const boundaryRect = this._boundaryRect;\n            const { width: previewWidth, height: previewHeight } = this._getPreviewRect();\n            const minY = boundaryRect.top + pickupY;\n            const maxY = boundaryRect.bottom - (previewHeight - pickupY);\n            const minX = boundaryRect.left + pickupX;\n            const maxX = boundaryRect.right - (previewWidth - pickupX);\n            x = clamp$1(x, minX, maxX);\n            y = clamp$1(y, minY, maxY);\n        }\n        return { x, y };\n    }\n    /** Updates the current drag delta, based on the user's current pointer position on the page. */\n    _updatePointerDirectionDelta(pointerPositionOnPage) {\n        const { x, y } = pointerPositionOnPage;\n        const delta = this._pointerDirectionDelta;\n        const positionSinceLastChange = this._pointerPositionAtLastDirectionChange;\n        // Amount of pixels the user has dragged since the last time the direction changed.\n        const changeX = Math.abs(x - positionSinceLastChange.x);\n        const changeY = Math.abs(y - positionSinceLastChange.y);\n        // Because we handle pointer events on a per-pixel basis, we don't want the delta\n        // to change for every pixel, otherwise anything that depends on it can look erratic.\n        // To make the delta more consistent, we track how much the user has moved since the last\n        // delta change and we only update it after it has reached a certain threshold.\n        if (changeX > this._config.pointerDirectionChangeThreshold) {\n            delta.x = x > positionSinceLastChange.x ? 1 : -1;\n            positionSinceLastChange.x = x;\n        }\n        if (changeY > this._config.pointerDirectionChangeThreshold) {\n            delta.y = y > positionSinceLastChange.y ? 1 : -1;\n            positionSinceLastChange.y = y;\n        }\n        return delta;\n    }\n    /** Toggles the native drag interactions, based on how many handles are registered. */\n    _toggleNativeDragInteractions() {\n        if (!this._rootElement || !this._handles) {\n            return;\n        }\n        const shouldEnable = this._handles.length > 0 || !this.isDragging();\n        if (shouldEnable !== this._nativeInteractionsEnabled) {\n            this._nativeInteractionsEnabled = shouldEnable;\n            toggleNativeDragInteractions(this._rootElement, shouldEnable);\n        }\n    }\n    /** Removes the manually-added event listeners from the root element. */\n    _removeRootElementListeners(element) {\n        element.removeEventListener('mousedown', this._pointerDown, activeEventListenerOptions);\n        element.removeEventListener('touchstart', this._pointerDown, passiveEventListenerOptions);\n        element.removeEventListener('dragstart', this._nativeDragStart, activeEventListenerOptions);\n    }\n    /**\n     * Applies a `transform` to the root element, taking into account any existing transforms on it.\n     * @param x New transform value along the X axis.\n     * @param y New transform value along the Y axis.\n     */\n    _applyRootElementTransform(x, y) {\n        const transform = getTransform(x, y);\n        const styles = this._rootElement.style;\n        // Cache the previous transform amount only after the first drag sequence, because\n        // we don't want our own transforms to stack on top of each other.\n        // Should be excluded none because none + translate3d(x, y, x) is invalid css\n        if (this._initialTransform == null) {\n            this._initialTransform =\n                styles.transform && styles.transform != 'none' ? styles.transform : '';\n        }\n        // Preserve the previous `transform` value, if there was one. Note that we apply our own\n        // transform before the user's, because things like rotation can affect which direction\n        // the element will be translated towards.\n        styles.transform = combineTransforms(transform, this._initialTransform);\n    }\n    /**\n     * Applies a `transform` to the preview, taking into account any existing transforms on it.\n     * @param x New transform value along the X axis.\n     * @param y New transform value along the Y axis.\n     */\n    _applyPreviewTransform(x, y) {\n        // Only apply the initial transform if the preview is a clone of the original element, otherwise\n        // it could be completely different and the transform might not make sense anymore.\n        const initialTransform = this._previewTemplate?.template ? undefined : this._initialTransform;\n        const transform = getTransform(x, y);\n        this._preview.style.transform = combineTransforms(transform, initialTransform);\n    }\n    /**\n     * Gets the distance that the user has dragged during the current drag sequence.\n     * @param currentPosition Current position of the user's pointer.\n     */\n    _getDragDistance(currentPosition) {\n        const pickupPosition = this._pickupPositionOnPage;\n        if (pickupPosition) {\n            return { x: currentPosition.x - pickupPosition.x, y: currentPosition.y - pickupPosition.y };\n        }\n        return { x: 0, y: 0 };\n    }\n    /** Cleans up any cached element dimensions that we don't need after dragging has stopped. */\n    _cleanupCachedDimensions() {\n        this._boundaryRect = this._previewRect = undefined;\n        this._parentPositions.clear();\n    }\n    /**\n     * Checks whether the element is still inside its boundary after the viewport has been resized.\n     * If not, the position is adjusted so that the element fits again.\n     */\n    _containInsideBoundaryOnResize() {\n        let { x, y } = this._passiveTransform;\n        if ((x === 0 && y === 0) || this.isDragging() || !this._boundaryElement) {\n            return;\n        }\n        // Note: don't use `_clientRectAtStart` here, because we want the latest position.\n        const elementRect = this._rootElement.getBoundingClientRect();\n        const boundaryRect = this._boundaryElement.getBoundingClientRect();\n        // It's possible that the element got hidden away after dragging (e.g. by switching to a\n        // different tab). Don't do anything in this case so we don't clear the user's position.\n        if ((boundaryRect.width === 0 && boundaryRect.height === 0) ||\n            (elementRect.width === 0 && elementRect.height === 0)) {\n            return;\n        }\n        const leftOverflow = boundaryRect.left - elementRect.left;\n        const rightOverflow = elementRect.right - boundaryRect.right;\n        const topOverflow = boundaryRect.top - elementRect.top;\n        const bottomOverflow = elementRect.bottom - boundaryRect.bottom;\n        // If the element has become wider than the boundary, we can't\n        // do much to make it fit so we just anchor it to the left.\n        if (boundaryRect.width > elementRect.width) {\n            if (leftOverflow > 0) {\n                x += leftOverflow;\n            }\n            if (rightOverflow > 0) {\n                x -= rightOverflow;\n            }\n        }\n        else {\n            x = 0;\n        }\n        // If the element has become taller than the boundary, we can't\n        // do much to make it fit so we just anchor it to the top.\n        if (boundaryRect.height > elementRect.height) {\n            if (topOverflow > 0) {\n                y += topOverflow;\n            }\n            if (bottomOverflow > 0) {\n                y -= bottomOverflow;\n            }\n        }\n        else {\n            y = 0;\n        }\n        if (x !== this._passiveTransform.x || y !== this._passiveTransform.y) {\n            this.setFreeDragPosition({ y, x });\n        }\n    }\n    /** Gets the drag start delay, based on the event type. */\n    _getDragStartDelay(event) {\n        const value = this.dragStartDelay;\n        if (typeof value === 'number') {\n            return value;\n        }\n        else if (isTouchEvent(event)) {\n            return value.touch;\n        }\n        return value ? value.mouse : 0;\n    }\n    /** Updates the internal state of the draggable element when scrolling has occurred. */\n    _updateOnScroll(event) {\n        const scrollDifference = this._parentPositions.handleScroll(event);\n        if (scrollDifference) {\n            const target = _getEventTarget(event);\n            // ClientRect dimensions are based on the scroll position of the page and its parent\n            // node so we have to update the cached boundary ClientRect if the user has scrolled.\n            if (this._boundaryRect &&\n                target !== this._boundaryElement &&\n                target.contains(this._boundaryElement)) {\n                adjustClientRect(this._boundaryRect, scrollDifference.top, scrollDifference.left);\n            }\n            this._pickupPositionOnPage.x += scrollDifference.left;\n            this._pickupPositionOnPage.y += scrollDifference.top;\n            // If we're in free drag mode, we have to update the active transform, because\n            // it isn't relative to the viewport like the preview inside a drop list.\n            if (!this._dropContainer) {\n                this._activeTransform.x -= scrollDifference.left;\n                this._activeTransform.y -= scrollDifference.top;\n                this._applyRootElementTransform(this._activeTransform.x, this._activeTransform.y);\n            }\n        }\n    }\n    /** Gets the scroll position of the viewport. */\n    _getViewportScrollPosition() {\n        return (this._parentPositions.positions.get(this._document)?.scrollPosition ||\n            this._parentPositions.getViewportScrollPosition());\n    }\n    /**\n     * Lazily resolves and returns the shadow root of the element. We do this in a function, rather\n     * than saving it in property directly on init, because we want to resolve it as late as possible\n     * in order to ensure that the element has been moved into the shadow DOM. Doing it inside the\n     * constructor might be too early if the element is inside of something like `ngFor` or `ngIf`.\n     */\n    _getShadowRoot() {\n        if (this._cachedShadowRoot === undefined) {\n            this._cachedShadowRoot = _getShadowRoot(this._rootElement);\n        }\n        return this._cachedShadowRoot;\n    }\n    /** Gets the element into which the drag preview should be inserted. */\n    _getPreviewInsertionPoint(initialParent, shadowRoot) {\n        const previewContainer = this._previewContainer || 'global';\n        if (previewContainer === 'parent') {\n            return initialParent;\n        }\n        if (previewContainer === 'global') {\n            const documentRef = this._document;\n            // We can't use the body if the user is in fullscreen mode,\n            // because the preview will render under the fullscreen element.\n            // TODO(crisbeto): dedupe this with the `FullscreenOverlayContainer` eventually.\n            return (shadowRoot ||\n                documentRef.fullscreenElement ||\n                documentRef.webkitFullscreenElement ||\n                documentRef.mozFullScreenElement ||\n                documentRef.msFullscreenElement ||\n                documentRef.body);\n        }\n        return coerceElement(previewContainer);\n    }\n    /** Lazily resolves and returns the dimensions of the preview. */\n    _getPreviewRect() {\n        // Cache the preview element rect if we haven't cached it already or if\n        // we cached it too early before the element dimensions were computed.\n        if (!this._previewRect || (!this._previewRect.width && !this._previewRect.height)) {\n            this._previewRect = this._preview\n                ? this._preview.getBoundingClientRect()\n                : this._initialClientRect;\n        }\n        return this._previewRect;\n    }\n    /** Gets a handle that is the target of an event. */\n    _getTargetHandle(event) {\n        return this._handles.find(handle => {\n            return event.target && (event.target === handle || handle.contains(event.target));\n        });\n    }\n}\n/**\n * Gets a 3d `transform` that can be applied to an element.\n * @param x Desired position of the element along the X axis.\n * @param y Desired position of the element along the Y axis.\n */\nfunction getTransform(x, y) {\n    // Round the transforms since some browsers will\n    // blur the elements for sub-pixel transforms.\n    return `translate3d(${Math.round(x)}px, ${Math.round(y)}px, 0)`;\n}\n/** Clamps a value between a minimum and a maximum. */\nfunction clamp$1(value, min, max) {\n    return Math.max(min, Math.min(max, value));\n}\n/** Determines whether an event is a touch event. */\nfunction isTouchEvent(event) {\n    // This function is called for every pixel that the user has dragged so we need it to be\n    // as fast as possible. Since we only bind mouse events and touch events, we can assume\n    // that if the event's name starts with `t`, it's a touch event.\n    return event.type[0] === 't';\n}\n/**\n * Gets the root HTML element of an embedded view.\n * If the root is not an HTML element it gets wrapped in one.\n */\nfunction getRootNode(viewRef, _document) {\n    const rootNodes = viewRef.rootNodes;\n    if (rootNodes.length === 1 && rootNodes[0].nodeType === _document.ELEMENT_NODE) {\n        return rootNodes[0];\n    }\n    const wrapper = _document.createElement('div');\n    rootNodes.forEach(node => wrapper.appendChild(node));\n    return wrapper;\n}\n/**\n * Matches the target element's size to the source's size.\n * @param target Element that needs to be resized.\n * @param sourceRect Dimensions of the source element.\n */\nfunction matchElementSize(target, sourceRect) {\n    target.style.width = `${sourceRect.width}px`;\n    target.style.height = `${sourceRect.height}px`;\n    target.style.transform = getTransform(sourceRect.left, sourceRect.top);\n}\n\n/**\n * Moves an item one index in an array to another.\n * @param array Array in which to move the item.\n * @param fromIndex Starting index of the item.\n * @param toIndex Index to which the item should be moved.\n */\nfunction moveItemInArray(array, fromIndex, toIndex) {\n    const from = clamp(fromIndex, array.length - 1);\n    const to = clamp(toIndex, array.length - 1);\n    if (from === to) {\n        return;\n    }\n    const target = array[from];\n    const delta = to < from ? -1 : 1;\n    for (let i = from; i !== to; i += delta) {\n        array[i] = array[i + delta];\n    }\n    array[to] = target;\n}\n/**\n * Moves an item from one array to another.\n * @param currentArray Array from which to transfer the item.\n * @param targetArray Array into which to put the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n */\nfunction transferArrayItem(currentArray, targetArray, currentIndex, targetIndex) {\n    const from = clamp(currentIndex, currentArray.length - 1);\n    const to = clamp(targetIndex, targetArray.length);\n    if (currentArray.length) {\n        targetArray.splice(to, 0, currentArray.splice(from, 1)[0]);\n    }\n}\n/**\n * Copies an item from one array to another, leaving it in its\n * original position in current array.\n * @param currentArray Array from which to copy the item.\n * @param targetArray Array into which is copy the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n *\n */\nfunction copyArrayItem(currentArray, targetArray, currentIndex, targetIndex) {\n    const to = clamp(targetIndex, targetArray.length);\n    if (currentArray.length) {\n        targetArray.splice(to, 0, currentArray[currentIndex]);\n    }\n}\n/** Clamps a number between zero and a maximum. */\nfunction clamp(value, max) {\n    return Math.max(0, Math.min(max, value));\n}\n\n/**\n * Strategy that only supports sorting along a single axis.\n * Items are reordered using CSS transforms which allows for sorting to be animated.\n * @docs-private\n */\nclass SingleAxisSortStrategy {\n    constructor(_element, _dragDropRegistry) {\n        this._element = _element;\n        this._dragDropRegistry = _dragDropRegistry;\n        /** Cache of the dimensions of all the items inside the container. */\n        this._itemPositions = [];\n        /** Direction in which the list is oriented. */\n        this.orientation = 'vertical';\n        /**\n         * Keeps track of the item that was last swapped with the dragged item, as well as what direction\n         * the pointer was moving in when the swap occurred and whether the user's pointer continued to\n         * overlap with the swapped item after the swapping occurred.\n         */\n        this._previousSwap = {\n            drag: null,\n            delta: 0,\n            overlaps: false,\n        };\n    }\n    /**\n     * To be called when the drag sequence starts.\n     * @param items Items that are currently in the list.\n     */\n    start(items) {\n        this.withItems(items);\n    }\n    /**\n     * To be called when an item is being sorted.\n     * @param item Item to be sorted.\n     * @param pointerX Position of the item along the X axis.\n     * @param pointerY Position of the item along the Y axis.\n     * @param pointerDelta Direction in which the pointer is moving along each axis.\n     */\n    sort(item, pointerX, pointerY, pointerDelta) {\n        const siblings = this._itemPositions;\n        const newIndex = this._getItemIndexFromPointerPosition(item, pointerX, pointerY, pointerDelta);\n        if (newIndex === -1 && siblings.length > 0) {\n            return null;\n        }\n        const isHorizontal = this.orientation === 'horizontal';\n        const currentIndex = siblings.findIndex(currentItem => currentItem.drag === item);\n        const siblingAtNewPosition = siblings[newIndex];\n        const currentPosition = siblings[currentIndex].clientRect;\n        const newPosition = siblingAtNewPosition.clientRect;\n        const delta = currentIndex > newIndex ? 1 : -1;\n        // How many pixels the item's placeholder should be offset.\n        const itemOffset = this._getItemOffsetPx(currentPosition, newPosition, delta);\n        // How many pixels all the other items should be offset.\n        const siblingOffset = this._getSiblingOffsetPx(currentIndex, siblings, delta);\n        // Save the previous order of the items before moving the item to its new index.\n        // We use this to check whether an item has been moved as a result of the sorting.\n        const oldOrder = siblings.slice();\n        // Shuffle the array in place.\n        moveItemInArray(siblings, currentIndex, newIndex);\n        siblings.forEach((sibling, index) => {\n            // Don't do anything if the position hasn't changed.\n            if (oldOrder[index] === sibling) {\n                return;\n            }\n            const isDraggedItem = sibling.drag === item;\n            const offset = isDraggedItem ? itemOffset : siblingOffset;\n            const elementToOffset = isDraggedItem\n                ? item.getPlaceholderElement()\n                : sibling.drag.getRootElement();\n            // Update the offset to reflect the new position.\n            sibling.offset += offset;\n            // Since we're moving the items with a `transform`, we need to adjust their cached\n            // client rects to reflect their new position, as well as swap their positions in the cache.\n            // Note that we shouldn't use `getBoundingClientRect` here to update the cache, because the\n            // elements may be mid-animation which will give us a wrong result.\n            if (isHorizontal) {\n                // Round the transforms since some browsers will\n                // blur the elements, for sub-pixel transforms.\n                elementToOffset.style.transform = combineTransforms(`translate3d(${Math.round(sibling.offset)}px, 0, 0)`, sibling.initialTransform);\n                adjustClientRect(sibling.clientRect, 0, offset);\n            }\n            else {\n                elementToOffset.style.transform = combineTransforms(`translate3d(0, ${Math.round(sibling.offset)}px, 0)`, sibling.initialTransform);\n                adjustClientRect(sibling.clientRect, offset, 0);\n            }\n        });\n        // Note that it's important that we do this after the client rects have been adjusted.\n        this._previousSwap.overlaps = isInsideClientRect(newPosition, pointerX, pointerY);\n        this._previousSwap.drag = siblingAtNewPosition.drag;\n        this._previousSwap.delta = isHorizontal ? pointerDelta.x : pointerDelta.y;\n        return { previousIndex: currentIndex, currentIndex: newIndex };\n    }\n    /**\n     * Called when an item is being moved into the container.\n     * @param item Item that was moved into the container.\n     * @param pointerX Position of the item along the X axis.\n     * @param pointerY Position of the item along the Y axis.\n     * @param index Index at which the item entered. If omitted, the container will try to figure it\n     *   out automatically.\n     */\n    enter(item, pointerX, pointerY, index) {\n        const newIndex = index == null || index < 0\n            ? // We use the coordinates of where the item entered the drop\n                // zone to figure out at which index it should be inserted.\n                this._getItemIndexFromPointerPosition(item, pointerX, pointerY)\n            : index;\n        const activeDraggables = this._activeDraggables;\n        const currentIndex = activeDraggables.indexOf(item);\n        const placeholder = item.getPlaceholderElement();\n        let newPositionReference = activeDraggables[newIndex];\n        // If the item at the new position is the same as the item that is being dragged,\n        // it means that we're trying to restore the item to its initial position. In this\n        // case we should use the next item from the list as the reference.\n        if (newPositionReference === item) {\n            newPositionReference = activeDraggables[newIndex + 1];\n        }\n        // If we didn't find a new position reference, it means that either the item didn't start off\n        // in this container, or that the item requested to be inserted at the end of the list.\n        if (!newPositionReference &&\n            (newIndex == null || newIndex === -1 || newIndex < activeDraggables.length - 1) &&\n            this._shouldEnterAsFirstChild(pointerX, pointerY)) {\n            newPositionReference = activeDraggables[0];\n        }\n        // Since the item may be in the `activeDraggables` already (e.g. if the user dragged it\n        // into another container and back again), we have to ensure that it isn't duplicated.\n        if (currentIndex > -1) {\n            activeDraggables.splice(currentIndex, 1);\n        }\n        // Don't use items that are being dragged as a reference, because\n        // their element has been moved down to the bottom of the body.\n        if (newPositionReference && !this._dragDropRegistry.isDragging(newPositionReference)) {\n            const element = newPositionReference.getRootElement();\n            element.parentElement.insertBefore(placeholder, element);\n            activeDraggables.splice(newIndex, 0, item);\n        }\n        else {\n            coerceElement(this._element).appendChild(placeholder);\n            activeDraggables.push(item);\n        }\n        // The transform needs to be cleared so it doesn't throw off the measurements.\n        placeholder.style.transform = '';\n        // Note that usually `start` is called together with `enter` when an item goes into a new\n        // container. This will cache item positions, but we need to refresh them since the amount\n        // of items has changed.\n        this._cacheItemPositions();\n    }\n    /** Sets the items that are currently part of the list. */\n    withItems(items) {\n        this._activeDraggables = items.slice();\n        this._cacheItemPositions();\n    }\n    /** Assigns a sort predicate to the strategy. */\n    withSortPredicate(predicate) {\n        this._sortPredicate = predicate;\n    }\n    /** Resets the strategy to its initial state before dragging was started. */\n    reset() {\n        // TODO(crisbeto): may have to wait for the animations to finish.\n        this._activeDraggables.forEach(item => {\n            const rootElement = item.getRootElement();\n            if (rootElement) {\n                const initialTransform = this._itemPositions.find(p => p.drag === item)?.initialTransform;\n                rootElement.style.transform = initialTransform || '';\n            }\n        });\n        this._itemPositions = [];\n        this._activeDraggables = [];\n        this._previousSwap.drag = null;\n        this._previousSwap.delta = 0;\n        this._previousSwap.overlaps = false;\n    }\n    /**\n     * Gets a snapshot of items currently in the list.\n     * Can include items that we dragged in from another list.\n     */\n    getActiveItemsSnapshot() {\n        return this._activeDraggables;\n    }\n    /** Gets the index of a specific item. */\n    getItemIndex(item) {\n        // Items are sorted always by top/left in the cache, however they flow differently in RTL.\n        // The rest of the logic still stands no matter what orientation we're in, however\n        // we need to invert the array when determining the index.\n        const items = this.orientation === 'horizontal' && this.direction === 'rtl'\n            ? this._itemPositions.slice().reverse()\n            : this._itemPositions;\n        return items.findIndex(currentItem => currentItem.drag === item);\n    }\n    /** Used to notify the strategy that the scroll position has changed. */\n    updateOnScroll(topDifference, leftDifference) {\n        // Since we know the amount that the user has scrolled we can shift all of the\n        // client rectangles ourselves. This is cheaper than re-measuring everything and\n        // we can avoid inconsistent behavior where we might be measuring the element before\n        // its position has changed.\n        this._itemPositions.forEach(({ clientRect }) => {\n            adjustClientRect(clientRect, topDifference, leftDifference);\n        });\n        // We need two loops for this, because we want all of the cached\n        // positions to be up-to-date before we re-sort the item.\n        this._itemPositions.forEach(({ drag }) => {\n            if (this._dragDropRegistry.isDragging(drag)) {\n                // We need to re-sort the item manually, because the pointer move\n                // events won't be dispatched while the user is scrolling.\n                drag._sortFromLastPointerPosition();\n            }\n        });\n    }\n    /** Refreshes the position cache of the items and sibling containers. */\n    _cacheItemPositions() {\n        const isHorizontal = this.orientation === 'horizontal';\n        this._itemPositions = this._activeDraggables\n            .map(drag => {\n            const elementToMeasure = drag.getVisibleElement();\n            return {\n                drag,\n                offset: 0,\n                initialTransform: elementToMeasure.style.transform || '',\n                clientRect: getMutableClientRect(elementToMeasure),\n            };\n        })\n            .sort((a, b) => {\n            return isHorizontal\n                ? a.clientRect.left - b.clientRect.left\n                : a.clientRect.top - b.clientRect.top;\n        });\n    }\n    /**\n     * Gets the offset in pixels by which the item that is being dragged should be moved.\n     * @param currentPosition Current position of the item.\n     * @param newPosition Position of the item where the current item should be moved.\n     * @param delta Direction in which the user is moving.\n     */\n    _getItemOffsetPx(currentPosition, newPosition, delta) {\n        const isHorizontal = this.orientation === 'horizontal';\n        let itemOffset = isHorizontal\n            ? newPosition.left - currentPosition.left\n            : newPosition.top - currentPosition.top;\n        // Account for differences in the item width/height.\n        if (delta === -1) {\n            itemOffset += isHorizontal\n                ? newPosition.width - currentPosition.width\n                : newPosition.height - currentPosition.height;\n        }\n        return itemOffset;\n    }\n    /**\n     * Gets the offset in pixels by which the items that aren't being dragged should be moved.\n     * @param currentIndex Index of the item currently being dragged.\n     * @param siblings All of the items in the list.\n     * @param delta Direction in which the user is moving.\n     */\n    _getSiblingOffsetPx(currentIndex, siblings, delta) {\n        const isHorizontal = this.orientation === 'horizontal';\n        const currentPosition = siblings[currentIndex].clientRect;\n        const immediateSibling = siblings[currentIndex + delta * -1];\n        let siblingOffset = currentPosition[isHorizontal ? 'width' : 'height'] * delta;\n        if (immediateSibling) {\n            const start = isHorizontal ? 'left' : 'top';\n            const end = isHorizontal ? 'right' : 'bottom';\n            // Get the spacing between the start of the current item and the end of the one immediately\n            // after it in the direction in which the user is dragging, or vice versa. We add it to the\n            // offset in order to push the element to where it will be when it's inline and is influenced\n            // by the `margin` of its siblings.\n            if (delta === -1) {\n                siblingOffset -= immediateSibling.clientRect[start] - currentPosition[end];\n            }\n            else {\n                siblingOffset += currentPosition[start] - immediateSibling.clientRect[end];\n            }\n        }\n        return siblingOffset;\n    }\n    /**\n     * Checks if pointer is entering in the first position\n     * @param pointerX Position of the user's pointer along the X axis.\n     * @param pointerY Position of the user's pointer along the Y axis.\n     */\n    _shouldEnterAsFirstChild(pointerX, pointerY) {\n        if (!this._activeDraggables.length) {\n            return false;\n        }\n        const itemPositions = this._itemPositions;\n        const isHorizontal = this.orientation === 'horizontal';\n        // `itemPositions` are sorted by position while `activeDraggables` are sorted by child index\n        // check if container is using some sort of \"reverse\" ordering (eg: flex-direction: row-reverse)\n        const reversed = itemPositions[0].drag !== this._activeDraggables[0];\n        if (reversed) {\n            const lastItemRect = itemPositions[itemPositions.length - 1].clientRect;\n            return isHorizontal ? pointerX >= lastItemRect.right : pointerY >= lastItemRect.bottom;\n        }\n        else {\n            const firstItemRect = itemPositions[0].clientRect;\n            return isHorizontal ? pointerX <= firstItemRect.left : pointerY <= firstItemRect.top;\n        }\n    }\n    /**\n     * Gets the index of an item in the drop container, based on the position of the user's pointer.\n     * @param item Item that is being sorted.\n     * @param pointerX Position of the user's pointer along the X axis.\n     * @param pointerY Position of the user's pointer along the Y axis.\n     * @param delta Direction in which the user is moving their pointer.\n     */\n    _getItemIndexFromPointerPosition(item, pointerX, pointerY, delta) {\n        const isHorizontal = this.orientation === 'horizontal';\n        const index = this._itemPositions.findIndex(({ drag, clientRect }) => {\n            // Skip the item itself.\n            if (drag === item) {\n                return false;\n            }\n            if (delta) {\n                const direction = isHorizontal ? delta.x : delta.y;\n                // If the user is still hovering over the same item as last time, their cursor hasn't left\n                // the item after we made the swap, and they didn't change the direction in which they're\n                // dragging, we don't consider it a direction swap.\n                if (drag === this._previousSwap.drag &&\n                    this._previousSwap.overlaps &&\n                    direction === this._previousSwap.delta) {\n                    return false;\n                }\n            }\n            return isHorizontal\n                ? // Round these down since most browsers report client rects with\n                    // sub-pixel precision, whereas the pointer coordinates are rounded to pixels.\n                    pointerX >= Math.floor(clientRect.left) && pointerX < Math.floor(clientRect.right)\n                : pointerY >= Math.floor(clientRect.top) && pointerY < Math.floor(clientRect.bottom);\n        });\n        return index === -1 || !this._sortPredicate(index, item) ? -1 : index;\n    }\n}\n\n/**\n * Proximity, as a ratio to width/height, at which a\n * dragged item will affect the drop container.\n */\nconst DROP_PROXIMITY_THRESHOLD = 0.05;\n/**\n * Proximity, as a ratio to width/height at which to start auto-scrolling the drop list or the\n * viewport. The value comes from trying it out manually until it feels right.\n */\nconst SCROLL_PROXIMITY_THRESHOLD = 0.05;\n/**\n * Reference to a drop list. Used to manipulate or dispose of the container.\n */\nclass DropListRef {\n    constructor(element, _dragDropRegistry, _document, _ngZone, _viewportRuler) {\n        this._dragDropRegistry = _dragDropRegistry;\n        this._ngZone = _ngZone;\n        this._viewportRuler = _viewportRuler;\n        /** Whether starting a dragging sequence from this container is disabled. */\n        this.disabled = false;\n        /** Whether sorting items within the list is disabled. */\n        this.sortingDisabled = false;\n        /**\n         * Whether auto-scrolling the view when the user\n         * moves their pointer close to the edges is disabled.\n         */\n        this.autoScrollDisabled = false;\n        /** Number of pixels to scroll for each frame when auto-scrolling an element. */\n        this.autoScrollStep = 2;\n        /**\n         * Function that is used to determine whether an item\n         * is allowed to be moved into a drop container.\n         */\n        this.enterPredicate = () => true;\n        /** Function that is used to determine whether an item can be sorted into a particular index. */\n        this.sortPredicate = () => true;\n        /** Emits right before dragging has started. */\n        this.beforeStarted = new Subject();\n        /**\n         * Emits when the user has moved a new drag item into this container.\n         */\n        this.entered = new Subject();\n        /**\n         * Emits when the user removes an item from the container\n         * by dragging it into another container.\n         */\n        this.exited = new Subject();\n        /** Emits when the user drops an item inside the container. */\n        this.dropped = new Subject();\n        /** Emits as the user is swapping items while actively dragging. */\n        this.sorted = new Subject();\n        /** Emits when a dragging sequence is started in a list connected to the current one. */\n        this.receivingStarted = new Subject();\n        /** Emits when a dragging sequence is stopped from a list connected to the current one. */\n        this.receivingStopped = new Subject();\n        /** Whether an item in the list is being dragged. */\n        this._isDragging = false;\n        /** Draggable items in the container. */\n        this._draggables = [];\n        /** Drop lists that are connected to the current one. */\n        this._siblings = [];\n        /** Connected siblings that currently have a dragged item. */\n        this._activeSiblings = new Set();\n        /** Subscription to the window being scrolled. */\n        this._viewportScrollSubscription = Subscription.EMPTY;\n        /** Vertical direction in which the list is currently scrolling. */\n        this._verticalScrollDirection = 0 /* AutoScrollVerticalDirection.NONE */;\n        /** Horizontal direction in which the list is currently scrolling. */\n        this._horizontalScrollDirection = 0 /* AutoScrollHorizontalDirection.NONE */;\n        /** Used to signal to the current auto-scroll sequence when to stop. */\n        this._stopScrollTimers = new Subject();\n        /** Shadow root of the current element. Necessary for `elementFromPoint` to resolve correctly. */\n        this._cachedShadowRoot = null;\n        /** Starts the interval that'll auto-scroll the element. */\n        this._startScrollInterval = () => {\n            this._stopScrolling();\n            interval(0, animationFrameScheduler)\n                .pipe(takeUntil(this._stopScrollTimers))\n                .subscribe(() => {\n                const node = this._scrollNode;\n                const scrollStep = this.autoScrollStep;\n                if (this._verticalScrollDirection === 1 /* AutoScrollVerticalDirection.UP */) {\n                    node.scrollBy(0, -scrollStep);\n                }\n                else if (this._verticalScrollDirection === 2 /* AutoScrollVerticalDirection.DOWN */) {\n                    node.scrollBy(0, scrollStep);\n                }\n                if (this._horizontalScrollDirection === 1 /* AutoScrollHorizontalDirection.LEFT */) {\n                    node.scrollBy(-scrollStep, 0);\n                }\n                else if (this._horizontalScrollDirection === 2 /* AutoScrollHorizontalDirection.RIGHT */) {\n                    node.scrollBy(scrollStep, 0);\n                }\n            });\n        };\n        this.element = coerceElement(element);\n        this._document = _document;\n        this.withScrollableParents([this.element]);\n        _dragDropRegistry.registerDropContainer(this);\n        this._parentPositions = new ParentPositionTracker(_document);\n        this._sortStrategy = new SingleAxisSortStrategy(this.element, _dragDropRegistry);\n        this._sortStrategy.withSortPredicate((index, item) => this.sortPredicate(index, item, this));\n    }\n    /** Removes the drop list functionality from the DOM element. */\n    dispose() {\n        this._stopScrolling();\n        this._stopScrollTimers.complete();\n        this._viewportScrollSubscription.unsubscribe();\n        this.beforeStarted.complete();\n        this.entered.complete();\n        this.exited.complete();\n        this.dropped.complete();\n        this.sorted.complete();\n        this.receivingStarted.complete();\n        this.receivingStopped.complete();\n        this._activeSiblings.clear();\n        this._scrollNode = null;\n        this._parentPositions.clear();\n        this._dragDropRegistry.removeDropContainer(this);\n    }\n    /** Whether an item from this list is currently being dragged. */\n    isDragging() {\n        return this._isDragging;\n    }\n    /** Starts dragging an item. */\n    start() {\n        this._draggingStarted();\n        this._notifyReceivingSiblings();\n    }\n    /**\n     * Attempts to move an item into the container.\n     * @param item Item that was moved into the container.\n     * @param pointerX Position of the item along the X axis.\n     * @param pointerY Position of the item along the Y axis.\n     * @param index Index at which the item entered. If omitted, the container will try to figure it\n     *   out automatically.\n     */\n    enter(item, pointerX, pointerY, index) {\n        this._draggingStarted();\n        // If sorting is disabled, we want the item to return to its starting\n        // position if the user is returning it to its initial container.\n        if (index == null && this.sortingDisabled) {\n            index = this._draggables.indexOf(item);\n        }\n        this._sortStrategy.enter(item, pointerX, pointerY, index);\n        // Note that this usually happens inside `_draggingStarted` as well, but the dimensions\n        // can change when the sort strategy moves the item around inside `enter`.\n        this._cacheParentPositions();\n        // Notify siblings at the end so that the item has been inserted into the `activeDraggables`.\n        this._notifyReceivingSiblings();\n        this.entered.next({ item, container: this, currentIndex: this.getItemIndex(item) });\n    }\n    /**\n     * Removes an item from the container after it was dragged into another container by the user.\n     * @param item Item that was dragged out.\n     */\n    exit(item) {\n        this._reset();\n        this.exited.next({ item, container: this });\n    }\n    /**\n     * Drops an item into this container.\n     * @param item Item being dropped into the container.\n     * @param currentIndex Index at which the item should be inserted.\n     * @param previousIndex Index of the item when dragging started.\n     * @param previousContainer Container from which the item got dragged in.\n     * @param isPointerOverContainer Whether the user's pointer was over the\n     *    container when the item was dropped.\n     * @param distance Distance the user has dragged since the start of the dragging sequence.\n     * @param event Event that triggered the dropping sequence.\n     *\n     * @breaking-change 15.0.0 `previousIndex` and `event` parameters to become required.\n     */\n    drop(item, currentIndex, previousIndex, previousContainer, isPointerOverContainer, distance, dropPoint, event = {}) {\n        this._reset();\n        this.dropped.next({\n            item,\n            currentIndex,\n            previousIndex,\n            container: this,\n            previousContainer,\n            isPointerOverContainer,\n            distance,\n            dropPoint,\n            event,\n        });\n    }\n    /**\n     * Sets the draggable items that are a part of this list.\n     * @param items Items that are a part of this list.\n     */\n    withItems(items) {\n        const previousItems = this._draggables;\n        this._draggables = items;\n        items.forEach(item => item._withDropContainer(this));\n        if (this.isDragging()) {\n            const draggedItems = previousItems.filter(item => item.isDragging());\n            // If all of the items being dragged were removed\n            // from the list, abort the current drag sequence.\n            if (draggedItems.every(item => items.indexOf(item) === -1)) {\n                this._reset();\n            }\n            else {\n                this._sortStrategy.withItems(this._draggables);\n            }\n        }\n        return this;\n    }\n    /** Sets the layout direction of the drop list. */\n    withDirection(direction) {\n        this._sortStrategy.direction = direction;\n        return this;\n    }\n    /**\n     * Sets the containers that are connected to this one. When two or more containers are\n     * connected, the user will be allowed to transfer items between them.\n     * @param connectedTo Other containers that the current containers should be connected to.\n     */\n    connectedTo(connectedTo) {\n        this._siblings = connectedTo.slice();\n        return this;\n    }\n    /**\n     * Sets the orientation of the container.\n     * @param orientation New orientation for the container.\n     */\n    withOrientation(orientation) {\n        // TODO(crisbeto): eventually we should be constructing the new sort strategy here based on\n        // the new orientation. For now we can assume that it'll always be `SingleAxisSortStrategy`.\n        this._sortStrategy.orientation = orientation;\n        return this;\n    }\n    /**\n     * Sets which parent elements are can be scrolled while the user is dragging.\n     * @param elements Elements that can be scrolled.\n     */\n    withScrollableParents(elements) {\n        const element = coerceElement(this.element);\n        // We always allow the current element to be scrollable\n        // so we need to ensure that it's in the array.\n        this._scrollableElements =\n            elements.indexOf(element) === -1 ? [element, ...elements] : elements.slice();\n        return this;\n    }\n    /** Gets the scrollable parents that are registered with this drop container. */\n    getScrollableParents() {\n        return this._scrollableElements;\n    }\n    /**\n     * Figures out the index of an item in the container.\n     * @param item Item whose index should be determined.\n     */\n    getItemIndex(item) {\n        return this._isDragging\n            ? this._sortStrategy.getItemIndex(item)\n            : this._draggables.indexOf(item);\n    }\n    /**\n     * Whether the list is able to receive the item that\n     * is currently being dragged inside a connected drop list.\n     */\n    isReceiving() {\n        return this._activeSiblings.size > 0;\n    }\n    /**\n     * Sorts an item inside the container based on its position.\n     * @param item Item to be sorted.\n     * @param pointerX Position of the item along the X axis.\n     * @param pointerY Position of the item along the Y axis.\n     * @param pointerDelta Direction in which the pointer is moving along each axis.\n     */\n    _sortItem(item, pointerX, pointerY, pointerDelta) {\n        // Don't sort the item if sorting is disabled or it's out of range.\n        if (this.sortingDisabled ||\n            !this._clientRect ||\n            !isPointerNearClientRect(this._clientRect, DROP_PROXIMITY_THRESHOLD, pointerX, pointerY)) {\n            return;\n        }\n        const result = this._sortStrategy.sort(item, pointerX, pointerY, pointerDelta);\n        if (result) {\n            this.sorted.next({\n                previousIndex: result.previousIndex,\n                currentIndex: result.currentIndex,\n                container: this,\n                item,\n            });\n        }\n    }\n    /**\n     * Checks whether the user's pointer is close to the edges of either the\n     * viewport or the drop list and starts the auto-scroll sequence.\n     * @param pointerX User's pointer position along the x axis.\n     * @param pointerY User's pointer position along the y axis.\n     */\n    _startScrollingIfNecessary(pointerX, pointerY) {\n        if (this.autoScrollDisabled) {\n            return;\n        }\n        let scrollNode;\n        let verticalScrollDirection = 0 /* AutoScrollVerticalDirection.NONE */;\n        let horizontalScrollDirection = 0 /* AutoScrollHorizontalDirection.NONE */;\n        // Check whether we should start scrolling any of the parent containers.\n        this._parentPositions.positions.forEach((position, element) => {\n            // We have special handling for the `document` below. Also this would be\n            // nicer with a  for...of loop, but it requires changing a compiler flag.\n            if (element === this._document || !position.clientRect || scrollNode) {\n                return;\n            }\n            if (isPointerNearClientRect(position.clientRect, DROP_PROXIMITY_THRESHOLD, pointerX, pointerY)) {\n                [verticalScrollDirection, horizontalScrollDirection] = getElementScrollDirections(element, position.clientRect, pointerX, pointerY);\n                if (verticalScrollDirection || horizontalScrollDirection) {\n                    scrollNode = element;\n                }\n            }\n        });\n        // Otherwise check if we can start scrolling the viewport.\n        if (!verticalScrollDirection && !horizontalScrollDirection) {\n            const { width, height } = this._viewportRuler.getViewportSize();\n            const clientRect = {\n                width,\n                height,\n                top: 0,\n                right: width,\n                bottom: height,\n                left: 0,\n            };\n            verticalScrollDirection = getVerticalScrollDirection(clientRect, pointerY);\n            horizontalScrollDirection = getHorizontalScrollDirection(clientRect, pointerX);\n            scrollNode = window;\n        }\n        if (scrollNode &&\n            (verticalScrollDirection !== this._verticalScrollDirection ||\n                horizontalScrollDirection !== this._horizontalScrollDirection ||\n                scrollNode !== this._scrollNode)) {\n            this._verticalScrollDirection = verticalScrollDirection;\n            this._horizontalScrollDirection = horizontalScrollDirection;\n            this._scrollNode = scrollNode;\n            if ((verticalScrollDirection || horizontalScrollDirection) && scrollNode) {\n                this._ngZone.runOutsideAngular(this._startScrollInterval);\n            }\n            else {\n                this._stopScrolling();\n            }\n        }\n    }\n    /** Stops any currently-running auto-scroll sequences. */\n    _stopScrolling() {\n        this._stopScrollTimers.next();\n    }\n    /** Starts the dragging sequence within the list. */\n    _draggingStarted() {\n        const styles = coerceElement(this.element).style;\n        this.beforeStarted.next();\n        this._isDragging = true;\n        // We need to disable scroll snapping while the user is dragging, because it breaks automatic\n        // scrolling. The browser seems to round the value based on the snapping points which means\n        // that we can't increment/decrement the scroll position.\n        this._initialScrollSnap = styles.msScrollSnapType || styles.scrollSnapType || '';\n        styles.scrollSnapType = styles.msScrollSnapType = 'none';\n        this._sortStrategy.start(this._draggables);\n        this._cacheParentPositions();\n        this._viewportScrollSubscription.unsubscribe();\n        this._listenToScrollEvents();\n    }\n    /** Caches the positions of the configured scrollable parents. */\n    _cacheParentPositions() {\n        const element = coerceElement(this.element);\n        this._parentPositions.cache(this._scrollableElements);\n        // The list element is always in the `scrollableElements`\n        // so we can take advantage of the cached `ClientRect`.\n        this._clientRect = this._parentPositions.positions.get(element).clientRect;\n    }\n    /** Resets the container to its initial state. */\n    _reset() {\n        this._isDragging = false;\n        const styles = coerceElement(this.element).style;\n        styles.scrollSnapType = styles.msScrollSnapType = this._initialScrollSnap;\n        this._siblings.forEach(sibling => sibling._stopReceiving(this));\n        this._sortStrategy.reset();\n        this._stopScrolling();\n        this._viewportScrollSubscription.unsubscribe();\n        this._parentPositions.clear();\n    }\n    /**\n     * Checks whether the user's pointer is positioned over the container.\n     * @param x Pointer position along the X axis.\n     * @param y Pointer position along the Y axis.\n     */\n    _isOverContainer(x, y) {\n        return this._clientRect != null && isInsideClientRect(this._clientRect, x, y);\n    }\n    /**\n     * Figures out whether an item should be moved into a sibling\n     * drop container, based on its current position.\n     * @param item Drag item that is being moved.\n     * @param x Position of the item along the X axis.\n     * @param y Position of the item along the Y axis.\n     */\n    _getSiblingContainerFromPosition(item, x, y) {\n        return this._siblings.find(sibling => sibling._canReceive(item, x, y));\n    }\n    /**\n     * Checks whether the drop list can receive the passed-in item.\n     * @param item Item that is being dragged into the list.\n     * @param x Position of the item along the X axis.\n     * @param y Position of the item along the Y axis.\n     */\n    _canReceive(item, x, y) {\n        if (!this._clientRect ||\n            !isInsideClientRect(this._clientRect, x, y) ||\n            !this.enterPredicate(item, this)) {\n            return false;\n        }\n        const elementFromPoint = this._getShadowRoot().elementFromPoint(x, y);\n        // If there's no element at the pointer position, then\n        // the client rect is probably scrolled out of the view.\n        if (!elementFromPoint) {\n            return false;\n        }\n        const nativeElement = coerceElement(this.element);\n        // The `ClientRect`, that we're using to find the container over which the user is\n        // hovering, doesn't give us any information on whether the element has been scrolled\n        // out of the view or whether it's overlapping with other containers. This means that\n        // we could end up transferring the item into a container that's invisible or is positioned\n        // below another one. We use the result from `elementFromPoint` to get the top-most element\n        // at the pointer position and to find whether it's one of the intersecting drop containers.\n        return elementFromPoint === nativeElement || nativeElement.contains(elementFromPoint);\n    }\n    /**\n     * Called by one of the connected drop lists when a dragging sequence has started.\n     * @param sibling Sibling in which dragging has started.\n     */\n    _startReceiving(sibling, items) {\n        const activeSiblings = this._activeSiblings;\n        if (!activeSiblings.has(sibling) &&\n            items.every(item => {\n                // Note that we have to add an exception to the `enterPredicate` for items that started off\n                // in this drop list. The drag ref has logic that allows an item to return to its initial\n                // container, if it has left the initial container and none of the connected containers\n                // allow it to enter. See `DragRef._updateActiveDropContainer` for more context.\n                return this.enterPredicate(item, this) || this._draggables.indexOf(item) > -1;\n            })) {\n            activeSiblings.add(sibling);\n            this._cacheParentPositions();\n            this._listenToScrollEvents();\n            this.receivingStarted.next({\n                initiator: sibling,\n                receiver: this,\n                items,\n            });\n        }\n    }\n    /**\n     * Called by a connected drop list when dragging has stopped.\n     * @param sibling Sibling whose dragging has stopped.\n     */\n    _stopReceiving(sibling) {\n        this._activeSiblings.delete(sibling);\n        this._viewportScrollSubscription.unsubscribe();\n        this.receivingStopped.next({ initiator: sibling, receiver: this });\n    }\n    /**\n     * Starts listening to scroll events on the viewport.\n     * Used for updating the internal state of the list.\n     */\n    _listenToScrollEvents() {\n        this._viewportScrollSubscription = this._dragDropRegistry\n            .scrolled(this._getShadowRoot())\n            .subscribe(event => {\n            if (this.isDragging()) {\n                const scrollDifference = this._parentPositions.handleScroll(event);\n                if (scrollDifference) {\n                    this._sortStrategy.updateOnScroll(scrollDifference.top, scrollDifference.left);\n                }\n            }\n            else if (this.isReceiving()) {\n                this._cacheParentPositions();\n            }\n        });\n    }\n    /**\n     * Lazily resolves and returns the shadow root of the element. We do this in a function, rather\n     * than saving it in property directly on init, because we want to resolve it as late as possible\n     * in order to ensure that the element has been moved into the shadow DOM. Doing it inside the\n     * constructor might be too early if the element is inside of something like `ngFor` or `ngIf`.\n     */\n    _getShadowRoot() {\n        if (!this._cachedShadowRoot) {\n            const shadowRoot = _getShadowRoot(coerceElement(this.element));\n            this._cachedShadowRoot = (shadowRoot || this._document);\n        }\n        return this._cachedShadowRoot;\n    }\n    /** Notifies any siblings that may potentially receive the item. */\n    _notifyReceivingSiblings() {\n        const draggedItems = this._sortStrategy\n            .getActiveItemsSnapshot()\n            .filter(item => item.isDragging());\n        this._siblings.forEach(sibling => sibling._startReceiving(this, draggedItems));\n    }\n}\n/**\n * Gets whether the vertical auto-scroll direction of a node.\n * @param clientRect Dimensions of the node.\n * @param pointerY Position of the user's pointer along the y axis.\n */\nfunction getVerticalScrollDirection(clientRect, pointerY) {\n    const { top, bottom, height } = clientRect;\n    const yThreshold = height * SCROLL_PROXIMITY_THRESHOLD;\n    if (pointerY >= top - yThreshold && pointerY <= top + yThreshold) {\n        return 1 /* AutoScrollVerticalDirection.UP */;\n    }\n    else if (pointerY >= bottom - yThreshold && pointerY <= bottom + yThreshold) {\n        return 2 /* AutoScrollVerticalDirection.DOWN */;\n    }\n    return 0 /* AutoScrollVerticalDirection.NONE */;\n}\n/**\n * Gets whether the horizontal auto-scroll direction of a node.\n * @param clientRect Dimensions of the node.\n * @param pointerX Position of the user's pointer along the x axis.\n */\nfunction getHorizontalScrollDirection(clientRect, pointerX) {\n    const { left, right, width } = clientRect;\n    const xThreshold = width * SCROLL_PROXIMITY_THRESHOLD;\n    if (pointerX >= left - xThreshold && pointerX <= left + xThreshold) {\n        return 1 /* AutoScrollHorizontalDirection.LEFT */;\n    }\n    else if (pointerX >= right - xThreshold && pointerX <= right + xThreshold) {\n        return 2 /* AutoScrollHorizontalDirection.RIGHT */;\n    }\n    return 0 /* AutoScrollHorizontalDirection.NONE */;\n}\n/**\n * Gets the directions in which an element node should be scrolled,\n * assuming that the user's pointer is already within it scrollable region.\n * @param element Element for which we should calculate the scroll direction.\n * @param clientRect Bounding client rectangle of the element.\n * @param pointerX Position of the user's pointer along the x axis.\n * @param pointerY Position of the user's pointer along the y axis.\n */\nfunction getElementScrollDirections(element, clientRect, pointerX, pointerY) {\n    const computedVertical = getVerticalScrollDirection(clientRect, pointerY);\n    const computedHorizontal = getHorizontalScrollDirection(clientRect, pointerX);\n    let verticalScrollDirection = 0 /* AutoScrollVerticalDirection.NONE */;\n    let horizontalScrollDirection = 0 /* AutoScrollHorizontalDirection.NONE */;\n    // Note that we here we do some extra checks for whether the element is actually scrollable in\n    // a certain direction and we only assign the scroll direction if it is. We do this so that we\n    // can allow other elements to be scrolled, if the current element can't be scrolled anymore.\n    // This allows us to handle cases where the scroll regions of two scrollable elements overlap.\n    if (computedVertical) {\n        const scrollTop = element.scrollTop;\n        if (computedVertical === 1 /* AutoScrollVerticalDirection.UP */) {\n            if (scrollTop > 0) {\n                verticalScrollDirection = 1 /* AutoScrollVerticalDirection.UP */;\n            }\n        }\n        else if (element.scrollHeight - scrollTop > element.clientHeight) {\n            verticalScrollDirection = 2 /* AutoScrollVerticalDirection.DOWN */;\n        }\n    }\n    if (computedHorizontal) {\n        const scrollLeft = element.scrollLeft;\n        if (computedHorizontal === 1 /* AutoScrollHorizontalDirection.LEFT */) {\n            if (scrollLeft > 0) {\n                horizontalScrollDirection = 1 /* AutoScrollHorizontalDirection.LEFT */;\n            }\n        }\n        else if (element.scrollWidth - scrollLeft > element.clientWidth) {\n            horizontalScrollDirection = 2 /* AutoScrollHorizontalDirection.RIGHT */;\n        }\n    }\n    return [verticalScrollDirection, horizontalScrollDirection];\n}\n\n/** Event options that can be used to bind an active, capturing event. */\nconst activeCapturingEventOptions = normalizePassiveListenerOptions({\n    passive: false,\n    capture: true,\n});\n/**\n * Service that keeps track of all the drag item and drop container\n * instances, and manages global event listeners on the `document`.\n * @docs-private\n */\n// Note: this class is generic, rather than referencing CdkDrag and CdkDropList directly, in order\n// to avoid circular imports. If we were to reference them here, importing the registry into the\n// classes that are registering themselves will introduce a circular import.\nclass DragDropRegistry {\n    constructor(_ngZone, _document) {\n        this._ngZone = _ngZone;\n        /** Registered drop container instances. */\n        this._dropInstances = new Set();\n        /** Registered drag item instances. */\n        this._dragInstances = new Set();\n        /** Drag item instances that are currently being dragged. */\n        this._activeDragInstances = [];\n        /** Keeps track of the event listeners that we've bound to the `document`. */\n        this._globalListeners = new Map();\n        /**\n         * Predicate function to check if an item is being dragged.  Moved out into a property,\n         * because it'll be called a lot and we don't want to create a new function every time.\n         */\n        this._draggingPredicate = (item) => item.isDragging();\n        /**\n         * Emits the `touchmove` or `mousemove` events that are dispatched\n         * while the user is dragging a drag item instance.\n         */\n        this.pointerMove = new Subject();\n        /**\n         * Emits the `touchend` or `mouseup` events that are dispatched\n         * while the user is dragging a drag item instance.\n         */\n        this.pointerUp = new Subject();\n        /**\n         * Emits when the viewport has been scrolled while the user is dragging an item.\n         * @deprecated To be turned into a private member. Use the `scrolled` method instead.\n         * @breaking-change 13.0.0\n         */\n        this.scroll = new Subject();\n        /**\n         * Event listener that will prevent the default browser action while the user is dragging.\n         * @param event Event whose default action should be prevented.\n         */\n        this._preventDefaultWhileDragging = (event) => {\n            if (this._activeDragInstances.length > 0) {\n                event.preventDefault();\n            }\n        };\n        /** Event listener for `touchmove` that is bound even if no dragging is happening. */\n        this._persistentTouchmoveListener = (event) => {\n            if (this._activeDragInstances.length > 0) {\n                // Note that we only want to prevent the default action after dragging has actually started.\n                // Usually this is the same time at which the item is added to the `_activeDragInstances`,\n                // but it could be pushed back if the user has set up a drag delay or threshold.\n                if (this._activeDragInstances.some(this._draggingPredicate)) {\n                    event.preventDefault();\n                }\n                this.pointerMove.next(event);\n            }\n        };\n        this._document = _document;\n    }\n    /** Adds a drop container to the registry. */\n    registerDropContainer(drop) {\n        if (!this._dropInstances.has(drop)) {\n            this._dropInstances.add(drop);\n        }\n    }\n    /** Adds a drag item instance to the registry. */\n    registerDragItem(drag) {\n        this._dragInstances.add(drag);\n        // The `touchmove` event gets bound once, ahead of time, because WebKit\n        // won't preventDefault on a dynamically-added `touchmove` listener.\n        // See https://bugs.webkit.org/show_bug.cgi?id=184250.\n        if (this._dragInstances.size === 1) {\n            this._ngZone.runOutsideAngular(() => {\n                // The event handler has to be explicitly active,\n                // because newer browsers make it passive by default.\n                this._document.addEventListener('touchmove', this._persistentTouchmoveListener, activeCapturingEventOptions);\n            });\n        }\n    }\n    /** Removes a drop container from the registry. */\n    removeDropContainer(drop) {\n        this._dropInstances.delete(drop);\n    }\n    /** Removes a drag item instance from the registry. */\n    removeDragItem(drag) {\n        this._dragInstances.delete(drag);\n        this.stopDragging(drag);\n        if (this._dragInstances.size === 0) {\n            this._document.removeEventListener('touchmove', this._persistentTouchmoveListener, activeCapturingEventOptions);\n        }\n    }\n    /**\n     * Starts the dragging sequence for a drag instance.\n     * @param drag Drag instance which is being dragged.\n     * @param event Event that initiated the dragging.\n     */\n    startDragging(drag, event) {\n        // Do not process the same drag twice to avoid memory leaks and redundant listeners\n        if (this._activeDragInstances.indexOf(drag) > -1) {\n            return;\n        }\n        this._activeDragInstances.push(drag);\n        if (this._activeDragInstances.length === 1) {\n            const isTouchEvent = event.type.startsWith('touch');\n            // We explicitly bind __active__ listeners here, because newer browsers will default to\n            // passive ones for `mousemove` and `touchmove`. The events need to be active, because we\n            // use `preventDefault` to prevent the page from scrolling while the user is dragging.\n            this._globalListeners\n                .set(isTouchEvent ? 'touchend' : 'mouseup', {\n                handler: (e) => this.pointerUp.next(e),\n                options: true,\n            })\n                .set('scroll', {\n                handler: (e) => this.scroll.next(e),\n                // Use capturing so that we pick up scroll changes in any scrollable nodes that aren't\n                // the document. See https://github.com/angular/components/issues/17144.\n                options: true,\n            })\n                // Preventing the default action on `mousemove` isn't enough to disable text selection\n                // on Safari so we need to prevent the selection event as well. Alternatively this can\n                // be done by setting `user-select: none` on the `body`, however it has causes a style\n                // recalculation which can be expensive on pages with a lot of elements.\n                .set('selectstart', {\n                handler: this._preventDefaultWhileDragging,\n                options: activeCapturingEventOptions,\n            });\n            // We don't have to bind a move event for touch drag sequences, because\n            // we already have a persistent global one bound from `registerDragItem`.\n            if (!isTouchEvent) {\n                this._globalListeners.set('mousemove', {\n                    handler: (e) => this.pointerMove.next(e),\n                    options: activeCapturingEventOptions,\n                });\n            }\n            this._ngZone.runOutsideAngular(() => {\n                this._globalListeners.forEach((config, name) => {\n                    this._document.addEventListener(name, config.handler, config.options);\n                });\n            });\n        }\n    }\n    /** Stops dragging a drag item instance. */\n    stopDragging(drag) {\n        const index = this._activeDragInstances.indexOf(drag);\n        if (index > -1) {\n            this._activeDragInstances.splice(index, 1);\n            if (this._activeDragInstances.length === 0) {\n                this._clearGlobalListeners();\n            }\n        }\n    }\n    /** Gets whether a drag item instance is currently being dragged. */\n    isDragging(drag) {\n        return this._activeDragInstances.indexOf(drag) > -1;\n    }\n    /**\n     * Gets a stream that will emit when any element on the page is scrolled while an item is being\n     * dragged.\n     * @param shadowRoot Optional shadow root that the current dragging sequence started from.\n     *   Top-level listeners won't pick up events coming from the shadow DOM so this parameter can\n     *   be used to include an additional top-level listener at the shadow root level.\n     */\n    scrolled(shadowRoot) {\n        const streams = [this.scroll];\n        if (shadowRoot && shadowRoot !== this._document) {\n            // Note that this is basically the same as `fromEvent` from rxjs, but we do it ourselves,\n            // because we want to guarantee that the event is bound outside of the `NgZone`. With\n            // `fromEvent` it'll only happen if the subscription is outside the `NgZone`.\n            streams.push(new Observable((observer) => {\n                return this._ngZone.runOutsideAngular(() => {\n                    const eventOptions = true;\n                    const callback = (event) => {\n                        if (this._activeDragInstances.length) {\n                            observer.next(event);\n                        }\n                    };\n                    shadowRoot.addEventListener('scroll', callback, eventOptions);\n                    return () => {\n                        shadowRoot.removeEventListener('scroll', callback, eventOptions);\n                    };\n                });\n            }));\n        }\n        return merge(...streams);\n    }\n    ngOnDestroy() {\n        this._dragInstances.forEach(instance => this.removeDragItem(instance));\n        this._dropInstances.forEach(instance => this.removeDropContainer(instance));\n        this._clearGlobalListeners();\n        this.pointerMove.complete();\n        this.pointerUp.complete();\n    }\n    /** Clears out the global event listeners from the `document`. */\n    _clearGlobalListeners() {\n        this._globalListeners.forEach((config, name) => {\n            this._document.removeEventListener(name, config.handler, config.options);\n        });\n        this._globalListeners.clear();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: DragDropRegistry, deps: [{ token: i0.NgZone }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: DragDropRegistry, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: DragDropRegistry, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }]; } });\n\n/** Default configuration to be used when creating a `DragRef`. */\nconst DEFAULT_CONFIG = {\n    dragStartThreshold: 5,\n    pointerDirectionChangeThreshold: 5,\n};\n/**\n * Service that allows for drag-and-drop functionality to be attached to DOM elements.\n */\nclass DragDrop {\n    constructor(_document, _ngZone, _viewportRuler, _dragDropRegistry) {\n        this._document = _document;\n        this._ngZone = _ngZone;\n        this._viewportRuler = _viewportRuler;\n        this._dragDropRegistry = _dragDropRegistry;\n    }\n    /**\n     * Turns an element into a draggable item.\n     * @param element Element to which to attach the dragging functionality.\n     * @param config Object used to configure the dragging behavior.\n     */\n    createDrag(element, config = DEFAULT_CONFIG) {\n        return new DragRef(element, config, this._document, this._ngZone, this._viewportRuler, this._dragDropRegistry);\n    }\n    /**\n     * Turns an element into a drop list.\n     * @param element Element to which to attach the drop list functionality.\n     */\n    createDropList(element) {\n        return new DropListRef(element, this._dragDropRegistry, this._document, this._ngZone, this._viewportRuler);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: DragDrop, deps: [{ token: DOCUMENT }, { token: i0.NgZone }, { token: i1.ViewportRuler }, { token: DragDropRegistry }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: DragDrop, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: DragDrop, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i0.NgZone }, { type: i1.ViewportRuler }, { type: DragDropRegistry }]; } });\n\n/**\n * Injection token that can be used for a `CdkDrag` to provide itself as a parent to the\n * drag-specific child directive (`CdkDragHandle`, `CdkDragPreview` etc.). Used primarily\n * to avoid circular imports.\n * @docs-private\n */\nconst CDK_DRAG_PARENT = new InjectionToken('CDK_DRAG_PARENT');\n\n/**\n * Asserts that a particular node is an element.\n * @param node Node to be checked.\n * @param name Name to attach to the error message.\n */\nfunction assertElementNode(node, name) {\n    if (node.nodeType !== 1) {\n        throw Error(`${name} must be attached to an element node. ` + `Currently attached to \"${node.nodeName}\".`);\n    }\n}\n\n/**\n * Injection token that can be used to reference instances of `CdkDragHandle`. It serves as\n * alternative token to the actual `CdkDragHandle` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DRAG_HANDLE = new InjectionToken('CdkDragHandle');\n/** Handle that can be used to drag a CdkDrag instance. */\nclass CdkDragHandle {\n    /** Whether starting to drag through this handle is disabled. */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n        this._stateChanges.next(this);\n    }\n    constructor(element, parentDrag) {\n        this.element = element;\n        /** Emits when the state of the handle has changed. */\n        this._stateChanges = new Subject();\n        this._disabled = false;\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            assertElementNode(element.nativeElement, 'cdkDragHandle');\n        }\n        this._parentDrag = parentDrag;\n    }\n    ngOnDestroy() {\n        this._stateChanges.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkDragHandle, deps: [{ token: i0.ElementRef }, { token: CDK_DRAG_PARENT, optional: true, skipSelf: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkDragHandle, isStandalone: true, selector: \"[cdkDragHandle]\", inputs: { disabled: [\"cdkDragHandleDisabled\", \"disabled\"] }, host: { classAttribute: \"cdk-drag-handle\" }, providers: [{ provide: CDK_DRAG_HANDLE, useExisting: CdkDragHandle }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkDragHandle, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkDragHandle]',\n                    standalone: true,\n                    host: {\n                        'class': 'cdk-drag-handle',\n                    },\n                    providers: [{ provide: CDK_DRAG_HANDLE, useExisting: CdkDragHandle }],\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CDK_DRAG_PARENT]\n                }, {\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }] }]; }, propDecorators: { disabled: [{\n                type: Input,\n                args: ['cdkDragHandleDisabled']\n            }] } });\n\n/**\n * Injection token that can be used to reference instances of `CdkDragPlaceholder`. It serves as\n * alternative token to the actual `CdkDragPlaceholder` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DRAG_PLACEHOLDER = new InjectionToken('CdkDragPlaceholder');\n/**\n * Element that will be used as a template for the placeholder of a CdkDrag when\n * it is being dragged. The placeholder is displayed in place of the element being dragged.\n */\nclass CdkDragPlaceholder {\n    constructor(templateRef) {\n        this.templateRef = templateRef;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkDragPlaceholder, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkDragPlaceholder, isStandalone: true, selector: \"ng-template[cdkDragPlaceholder]\", inputs: { data: \"data\" }, providers: [{ provide: CDK_DRAG_PLACEHOLDER, useExisting: CdkDragPlaceholder }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkDragPlaceholder, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[cdkDragPlaceholder]',\n                    standalone: true,\n                    providers: [{ provide: CDK_DRAG_PLACEHOLDER, useExisting: CdkDragPlaceholder }],\n                }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }]; }, propDecorators: { data: [{\n                type: Input\n            }] } });\n\n/**\n * Injection token that can be used to reference instances of `CdkDragPreview`. It serves as\n * alternative token to the actual `CdkDragPreview` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DRAG_PREVIEW = new InjectionToken('CdkDragPreview');\n/**\n * Element that will be used as a template for the preview\n * of a CdkDrag when it is being dragged.\n */\nclass CdkDragPreview {\n    /** Whether the preview should preserve the same size as the item that is being dragged. */\n    get matchSize() {\n        return this._matchSize;\n    }\n    set matchSize(value) {\n        this._matchSize = coerceBooleanProperty(value);\n    }\n    constructor(templateRef) {\n        this.templateRef = templateRef;\n        this._matchSize = false;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkDragPreview, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkDragPreview, isStandalone: true, selector: \"ng-template[cdkDragPreview]\", inputs: { data: \"data\", matchSize: \"matchSize\" }, providers: [{ provide: CDK_DRAG_PREVIEW, useExisting: CdkDragPreview }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkDragPreview, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[cdkDragPreview]',\n                    standalone: true,\n                    providers: [{ provide: CDK_DRAG_PREVIEW, useExisting: CdkDragPreview }],\n                }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }]; }, propDecorators: { data: [{\n                type: Input\n            }], matchSize: [{\n                type: Input\n            }] } });\n\n/**\n * Injection token that can be used to configure the\n * behavior of the drag&drop-related components.\n */\nconst CDK_DRAG_CONFIG = new InjectionToken('CDK_DRAG_CONFIG');\n\nconst DRAG_HOST_CLASS = 'cdk-drag';\n/**\n * Injection token that can be used to reference instances of `CdkDropList`. It serves as\n * alternative token to the actual `CdkDropList` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DROP_LIST = new InjectionToken('CdkDropList');\n/** Element that can be moved inside a CdkDropList container. */\nclass CdkDrag {\n    static { this._dragInstances = []; }\n    /** Whether starting to drag this element is disabled. */\n    get disabled() {\n        return this._disabled || (this.dropContainer && this.dropContainer.disabled);\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n        this._dragRef.disabled = this._disabled;\n    }\n    constructor(\n    /** Element that the draggable is attached to. */\n    element, \n    /** Droppable container that the draggable is a part of. */\n    dropContainer, \n    /**\n     * @deprecated `_document` parameter no longer being used and will be removed.\n     * @breaking-change 12.0.0\n     */\n    _document, _ngZone, _viewContainerRef, config, _dir, dragDrop, _changeDetectorRef, _selfHandle, _parentDrag) {\n        this.element = element;\n        this.dropContainer = dropContainer;\n        this._ngZone = _ngZone;\n        this._viewContainerRef = _viewContainerRef;\n        this._dir = _dir;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._selfHandle = _selfHandle;\n        this._parentDrag = _parentDrag;\n        this._destroyed = new Subject();\n        /** Emits when the user starts dragging the item. */\n        this.started = new EventEmitter();\n        /** Emits when the user has released a drag item, before any animations have started. */\n        this.released = new EventEmitter();\n        /** Emits when the user stops dragging an item in the container. */\n        this.ended = new EventEmitter();\n        /** Emits when the user has moved the item into a new container. */\n        this.entered = new EventEmitter();\n        /** Emits when the user removes the item its container by dragging it into another container. */\n        this.exited = new EventEmitter();\n        /** Emits when the user drops the item inside a container. */\n        this.dropped = new EventEmitter();\n        /**\n         * Emits as the user is dragging the item. Use with caution,\n         * because this event will fire for every pixel that the user has dragged.\n         */\n        this.moved = new Observable((observer) => {\n            const subscription = this._dragRef.moved\n                .pipe(map(movedEvent => ({\n                source: this,\n                pointerPosition: movedEvent.pointerPosition,\n                event: movedEvent.event,\n                delta: movedEvent.delta,\n                distance: movedEvent.distance,\n            })))\n                .subscribe(observer);\n            return () => {\n                subscription.unsubscribe();\n            };\n        });\n        this._dragRef = dragDrop.createDrag(element, {\n            dragStartThreshold: config && config.dragStartThreshold != null ? config.dragStartThreshold : 5,\n            pointerDirectionChangeThreshold: config && config.pointerDirectionChangeThreshold != null\n                ? config.pointerDirectionChangeThreshold\n                : 5,\n            zIndex: config?.zIndex,\n        });\n        this._dragRef.data = this;\n        // We have to keep track of the drag instances in order to be able to match an element to\n        // a drag instance. We can't go through the global registry of `DragRef`, because the root\n        // element could be different.\n        CdkDrag._dragInstances.push(this);\n        if (config) {\n            this._assignDefaults(config);\n        }\n        // Note that usually the container is assigned when the drop list is picks up the item, but in\n        // some cases (mainly transplanted views with OnPush, see #18341) we may end up in a situation\n        // where there are no items on the first change detection pass, but the items get picked up as\n        // soon as the user triggers another pass by dragging. This is a problem, because the item would\n        // have to switch from standalone mode to drag mode in the middle of the dragging sequence which\n        // is too late since the two modes save different kinds of information. We work around it by\n        // assigning the drop container both from here and the list.\n        if (dropContainer) {\n            this._dragRef._withDropContainer(dropContainer._dropListRef);\n            dropContainer.addItem(this);\n        }\n        this._syncInputs(this._dragRef);\n        this._handleEvents(this._dragRef);\n    }\n    /**\n     * Returns the element that is being used as a placeholder\n     * while the current element is being dragged.\n     */\n    getPlaceholderElement() {\n        return this._dragRef.getPlaceholderElement();\n    }\n    /** Returns the root draggable element. */\n    getRootElement() {\n        return this._dragRef.getRootElement();\n    }\n    /** Resets a standalone drag item to its initial position. */\n    reset() {\n        this._dragRef.reset();\n    }\n    /**\n     * Gets the pixel coordinates of the draggable outside of a drop container.\n     */\n    getFreeDragPosition() {\n        return this._dragRef.getFreeDragPosition();\n    }\n    /**\n     * Sets the current position in pixels the draggable outside of a drop container.\n     * @param value New position to be set.\n     */\n    setFreeDragPosition(value) {\n        this._dragRef.setFreeDragPosition(value);\n    }\n    ngAfterViewInit() {\n        // Normally this isn't in the zone, but it can cause major performance regressions for apps\n        // using `zone-patch-rxjs` because it'll trigger a change detection when it unsubscribes.\n        this._ngZone.runOutsideAngular(() => {\n            // We need to wait for the zone to stabilize, in order for the reference\n            // element to be in the proper place in the DOM. This is mostly relevant\n            // for draggable elements inside portals since they get stamped out in\n            // their original DOM position and then they get transferred to the portal.\n            this._ngZone.onStable.pipe(take(1), takeUntil(this._destroyed)).subscribe(() => {\n                this._updateRootElement();\n                this._setupHandlesListener();\n                if (this.freeDragPosition) {\n                    this._dragRef.setFreeDragPosition(this.freeDragPosition);\n                }\n            });\n        });\n    }\n    ngOnChanges(changes) {\n        const rootSelectorChange = changes['rootElementSelector'];\n        const positionChange = changes['freeDragPosition'];\n        // We don't have to react to the first change since it's being\n        // handled in `ngAfterViewInit` where it needs to be deferred.\n        if (rootSelectorChange && !rootSelectorChange.firstChange) {\n            this._updateRootElement();\n        }\n        // Skip the first change since it's being handled in `ngAfterViewInit`.\n        if (positionChange && !positionChange.firstChange && this.freeDragPosition) {\n            this._dragRef.setFreeDragPosition(this.freeDragPosition);\n        }\n    }\n    ngOnDestroy() {\n        if (this.dropContainer) {\n            this.dropContainer.removeItem(this);\n        }\n        const index = CdkDrag._dragInstances.indexOf(this);\n        if (index > -1) {\n            CdkDrag._dragInstances.splice(index, 1);\n        }\n        // Unnecessary in most cases, but used to avoid extra change detections with `zone-paths-rxjs`.\n        this._ngZone.runOutsideAngular(() => {\n            this._destroyed.next();\n            this._destroyed.complete();\n            this._dragRef.dispose();\n        });\n    }\n    /** Syncs the root element with the `DragRef`. */\n    _updateRootElement() {\n        const element = this.element.nativeElement;\n        let rootElement = element;\n        if (this.rootElementSelector) {\n            rootElement =\n                element.closest !== undefined\n                    ? element.closest(this.rootElementSelector)\n                    : // Comment tag doesn't have closest method, so use parent's one.\n                        element.parentElement?.closest(this.rootElementSelector);\n        }\n        if (rootElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            assertElementNode(rootElement, 'cdkDrag');\n        }\n        this._dragRef.withRootElement(rootElement || element);\n    }\n    /** Gets the boundary element, based on the `boundaryElement` value. */\n    _getBoundaryElement() {\n        const boundary = this.boundaryElement;\n        if (!boundary) {\n            return null;\n        }\n        if (typeof boundary === 'string') {\n            return this.element.nativeElement.closest(boundary);\n        }\n        return coerceElement(boundary);\n    }\n    /** Syncs the inputs of the CdkDrag with the options of the underlying DragRef. */\n    _syncInputs(ref) {\n        ref.beforeStarted.subscribe(() => {\n            if (!ref.isDragging()) {\n                const dir = this._dir;\n                const dragStartDelay = this.dragStartDelay;\n                const placeholder = this._placeholderTemplate\n                    ? {\n                        template: this._placeholderTemplate.templateRef,\n                        context: this._placeholderTemplate.data,\n                        viewContainer: this._viewContainerRef,\n                    }\n                    : null;\n                const preview = this._previewTemplate\n                    ? {\n                        template: this._previewTemplate.templateRef,\n                        context: this._previewTemplate.data,\n                        matchSize: this._previewTemplate.matchSize,\n                        viewContainer: this._viewContainerRef,\n                    }\n                    : null;\n                ref.disabled = this.disabled;\n                ref.lockAxis = this.lockAxis;\n                ref.dragStartDelay =\n                    typeof dragStartDelay === 'object' && dragStartDelay\n                        ? dragStartDelay\n                        : coerceNumberProperty(dragStartDelay);\n                ref.constrainPosition = this.constrainPosition;\n                ref.previewClass = this.previewClass;\n                ref\n                    .withBoundaryElement(this._getBoundaryElement())\n                    .withPlaceholderTemplate(placeholder)\n                    .withPreviewTemplate(preview)\n                    .withPreviewContainer(this.previewContainer || 'global');\n                if (dir) {\n                    ref.withDirection(dir.value);\n                }\n            }\n        });\n        // This only needs to be resolved once.\n        ref.beforeStarted.pipe(take(1)).subscribe(() => {\n            // If we managed to resolve a parent through DI, use it.\n            if (this._parentDrag) {\n                ref.withParent(this._parentDrag._dragRef);\n                return;\n            }\n            // Otherwise fall back to resolving the parent by looking up the DOM. This can happen if\n            // the item was projected into another item by something like `ngTemplateOutlet`.\n            let parent = this.element.nativeElement.parentElement;\n            while (parent) {\n                if (parent.classList.contains(DRAG_HOST_CLASS)) {\n                    ref.withParent(CdkDrag._dragInstances.find(drag => {\n                        return drag.element.nativeElement === parent;\n                    })?._dragRef || null);\n                    break;\n                }\n                parent = parent.parentElement;\n            }\n        });\n    }\n    /** Handles the events from the underlying `DragRef`. */\n    _handleEvents(ref) {\n        ref.started.subscribe(startEvent => {\n            this.started.emit({ source: this, event: startEvent.event });\n            // Since all of these events run outside of change detection,\n            // we need to ensure that everything is marked correctly.\n            this._changeDetectorRef.markForCheck();\n        });\n        ref.released.subscribe(releaseEvent => {\n            this.released.emit({ source: this, event: releaseEvent.event });\n        });\n        ref.ended.subscribe(endEvent => {\n            this.ended.emit({\n                source: this,\n                distance: endEvent.distance,\n                dropPoint: endEvent.dropPoint,\n                event: endEvent.event,\n            });\n            // Since all of these events run outside of change detection,\n            // we need to ensure that everything is marked correctly.\n            this._changeDetectorRef.markForCheck();\n        });\n        ref.entered.subscribe(enterEvent => {\n            this.entered.emit({\n                container: enterEvent.container.data,\n                item: this,\n                currentIndex: enterEvent.currentIndex,\n            });\n        });\n        ref.exited.subscribe(exitEvent => {\n            this.exited.emit({\n                container: exitEvent.container.data,\n                item: this,\n            });\n        });\n        ref.dropped.subscribe(dropEvent => {\n            this.dropped.emit({\n                previousIndex: dropEvent.previousIndex,\n                currentIndex: dropEvent.currentIndex,\n                previousContainer: dropEvent.previousContainer.data,\n                container: dropEvent.container.data,\n                isPointerOverContainer: dropEvent.isPointerOverContainer,\n                item: this,\n                distance: dropEvent.distance,\n                dropPoint: dropEvent.dropPoint,\n                event: dropEvent.event,\n            });\n        });\n    }\n    /** Assigns the default input values based on a provided config object. */\n    _assignDefaults(config) {\n        const { lockAxis, dragStartDelay, constrainPosition, previewClass, boundaryElement, draggingDisabled, rootElementSelector, previewContainer, } = config;\n        this.disabled = draggingDisabled == null ? false : draggingDisabled;\n        this.dragStartDelay = dragStartDelay || 0;\n        if (lockAxis) {\n            this.lockAxis = lockAxis;\n        }\n        if (constrainPosition) {\n            this.constrainPosition = constrainPosition;\n        }\n        if (previewClass) {\n            this.previewClass = previewClass;\n        }\n        if (boundaryElement) {\n            this.boundaryElement = boundaryElement;\n        }\n        if (rootElementSelector) {\n            this.rootElementSelector = rootElementSelector;\n        }\n        if (previewContainer) {\n            this.previewContainer = previewContainer;\n        }\n    }\n    /** Sets up the listener that syncs the handles with the drag ref. */\n    _setupHandlesListener() {\n        // Listen for any newly-added handles.\n        this._handles.changes\n            .pipe(startWith(this._handles), \n        // Sync the new handles with the DragRef.\n        tap((handles) => {\n            const childHandleElements = handles\n                .filter(handle => handle._parentDrag === this)\n                .map(handle => handle.element);\n            // Usually handles are only allowed to be a descendant of the drag element, but if\n            // the consumer defined a different drag root, we should allow the drag element\n            // itself to be a handle too.\n            if (this._selfHandle && this.rootElementSelector) {\n                childHandleElements.push(this.element);\n            }\n            this._dragRef.withHandles(childHandleElements);\n        }), \n        // Listen if the state of any of the handles changes.\n        switchMap((handles) => {\n            return merge(...handles.map(item => {\n                return item._stateChanges.pipe(startWith(item));\n            }));\n        }), takeUntil(this._destroyed))\n            .subscribe(handleInstance => {\n            // Enabled/disable the handle that changed in the DragRef.\n            const dragRef = this._dragRef;\n            const handle = handleInstance.element.nativeElement;\n            handleInstance.disabled ? dragRef.disableHandle(handle) : dragRef.enableHandle(handle);\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkDrag, deps: [{ token: i0.ElementRef }, { token: CDK_DROP_LIST, optional: true, skipSelf: true }, { token: DOCUMENT }, { token: i0.NgZone }, { token: i0.ViewContainerRef }, { token: CDK_DRAG_CONFIG, optional: true }, { token: i1$1.Directionality, optional: true }, { token: DragDrop }, { token: i0.ChangeDetectorRef }, { token: CDK_DRAG_HANDLE, optional: true, self: true }, { token: CDK_DRAG_PARENT, optional: true, skipSelf: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkDrag, isStandalone: true, selector: \"[cdkDrag]\", inputs: { data: [\"cdkDragData\", \"data\"], lockAxis: [\"cdkDragLockAxis\", \"lockAxis\"], rootElementSelector: [\"cdkDragRootElement\", \"rootElementSelector\"], boundaryElement: [\"cdkDragBoundary\", \"boundaryElement\"], dragStartDelay: [\"cdkDragStartDelay\", \"dragStartDelay\"], freeDragPosition: [\"cdkDragFreeDragPosition\", \"freeDragPosition\"], disabled: [\"cdkDragDisabled\", \"disabled\"], constrainPosition: [\"cdkDragConstrainPosition\", \"constrainPosition\"], previewClass: [\"cdkDragPreviewClass\", \"previewClass\"], previewContainer: [\"cdkDragPreviewContainer\", \"previewContainer\"] }, outputs: { started: \"cdkDragStarted\", released: \"cdkDragReleased\", ended: \"cdkDragEnded\", entered: \"cdkDragEntered\", exited: \"cdkDragExited\", dropped: \"cdkDragDropped\", moved: \"cdkDragMoved\" }, host: { properties: { \"class.cdk-drag-disabled\": \"disabled\", \"class.cdk-drag-dragging\": \"_dragRef.isDragging()\" }, classAttribute: \"cdk-drag\" }, providers: [{ provide: CDK_DRAG_PARENT, useExisting: CdkDrag }], queries: [{ propertyName: \"_previewTemplate\", first: true, predicate: CDK_DRAG_PREVIEW, descendants: true }, { propertyName: \"_placeholderTemplate\", first: true, predicate: CDK_DRAG_PLACEHOLDER, descendants: true }, { propertyName: \"_handles\", predicate: CDK_DRAG_HANDLE, descendants: true }], exportAs: [\"cdkDrag\"], usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkDrag, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkDrag]',\n                    exportAs: 'cdkDrag',\n                    standalone: true,\n                    host: {\n                        'class': DRAG_HOST_CLASS,\n                        '[class.cdk-drag-disabled]': 'disabled',\n                        '[class.cdk-drag-dragging]': '_dragRef.isDragging()',\n                    },\n                    providers: [{ provide: CDK_DRAG_PARENT, useExisting: CdkDrag }],\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CDK_DROP_LIST]\n                }, {\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i0.NgZone }, { type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [CDK_DRAG_CONFIG]\n                }] }, { type: i1$1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: DragDrop }, { type: i0.ChangeDetectorRef }, { type: CdkDragHandle, decorators: [{\n                    type: Optional\n                }, {\n                    type: Self\n                }, {\n                    type: Inject,\n                    args: [CDK_DRAG_HANDLE]\n                }] }, { type: CdkDrag, decorators: [{\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }, {\n                    type: Inject,\n                    args: [CDK_DRAG_PARENT]\n                }] }]; }, propDecorators: { _handles: [{\n                type: ContentChildren,\n                args: [CDK_DRAG_HANDLE, { descendants: true }]\n            }], _previewTemplate: [{\n                type: ContentChild,\n                args: [CDK_DRAG_PREVIEW]\n            }], _placeholderTemplate: [{\n                type: ContentChild,\n                args: [CDK_DRAG_PLACEHOLDER]\n            }], data: [{\n                type: Input,\n                args: ['cdkDragData']\n            }], lockAxis: [{\n                type: Input,\n                args: ['cdkDragLockAxis']\n            }], rootElementSelector: [{\n                type: Input,\n                args: ['cdkDragRootElement']\n            }], boundaryElement: [{\n                type: Input,\n                args: ['cdkDragBoundary']\n            }], dragStartDelay: [{\n                type: Input,\n                args: ['cdkDragStartDelay']\n            }], freeDragPosition: [{\n                type: Input,\n                args: ['cdkDragFreeDragPosition']\n            }], disabled: [{\n                type: Input,\n                args: ['cdkDragDisabled']\n            }], constrainPosition: [{\n                type: Input,\n                args: ['cdkDragConstrainPosition']\n            }], previewClass: [{\n                type: Input,\n                args: ['cdkDragPreviewClass']\n            }], previewContainer: [{\n                type: Input,\n                args: ['cdkDragPreviewContainer']\n            }], started: [{\n                type: Output,\n                args: ['cdkDragStarted']\n            }], released: [{\n                type: Output,\n                args: ['cdkDragReleased']\n            }], ended: [{\n                type: Output,\n                args: ['cdkDragEnded']\n            }], entered: [{\n                type: Output,\n                args: ['cdkDragEntered']\n            }], exited: [{\n                type: Output,\n                args: ['cdkDragExited']\n            }], dropped: [{\n                type: Output,\n                args: ['cdkDragDropped']\n            }], moved: [{\n                type: Output,\n                args: ['cdkDragMoved']\n            }] } });\n\n/**\n * Injection token that can be used to reference instances of `CdkDropListGroup`. It serves as\n * alternative token to the actual `CdkDropListGroup` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DROP_LIST_GROUP = new InjectionToken('CdkDropListGroup');\n/**\n * Declaratively connects sibling `cdkDropList` instances together. All of the `cdkDropList`\n * elements that are placed inside a `cdkDropListGroup` will be connected to each other\n * automatically. Can be used as an alternative to the `cdkDropListConnectedTo` input\n * from `cdkDropList`.\n */\nclass CdkDropListGroup {\n    constructor() {\n        /** Drop lists registered inside the group. */\n        this._items = new Set();\n        this._disabled = false;\n    }\n    /** Whether starting a dragging sequence from inside this group is disabled. */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n    }\n    ngOnDestroy() {\n        this._items.clear();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkDropListGroup, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkDropListGroup, isStandalone: true, selector: \"[cdkDropListGroup]\", inputs: { disabled: [\"cdkDropListGroupDisabled\", \"disabled\"] }, providers: [{ provide: CDK_DROP_LIST_GROUP, useExisting: CdkDropListGroup }], exportAs: [\"cdkDropListGroup\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkDropListGroup, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkDropListGroup]',\n                    exportAs: 'cdkDropListGroup',\n                    standalone: true,\n                    providers: [{ provide: CDK_DROP_LIST_GROUP, useExisting: CdkDropListGroup }],\n                }]\n        }], propDecorators: { disabled: [{\n                type: Input,\n                args: ['cdkDropListGroupDisabled']\n            }] } });\n\n/** Counter used to generate unique ids for drop zones. */\nlet _uniqueIdCounter = 0;\n/** Container that wraps a set of draggable items. */\nclass CdkDropList {\n    /** Keeps track of the drop lists that are currently on the page. */\n    static { this._dropLists = []; }\n    /** Whether starting a dragging sequence from this container is disabled. */\n    get disabled() {\n        return this._disabled || (!!this._group && this._group.disabled);\n    }\n    set disabled(value) {\n        // Usually we sync the directive and ref state right before dragging starts, in order to have\n        // a single point of failure and to avoid having to use setters for everything. `disabled` is\n        // a special case, because it can prevent the `beforeStarted` event from firing, which can lock\n        // the user in a disabled state, so we also need to sync it as it's being set.\n        this._dropListRef.disabled = this._disabled = coerceBooleanProperty(value);\n    }\n    constructor(\n    /** Element that the drop list is attached to. */\n    element, dragDrop, _changeDetectorRef, _scrollDispatcher, _dir, _group, config) {\n        this.element = element;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._scrollDispatcher = _scrollDispatcher;\n        this._dir = _dir;\n        this._group = _group;\n        /** Emits when the list has been destroyed. */\n        this._destroyed = new Subject();\n        /**\n         * Other draggable containers that this container is connected to and into which the\n         * container's items can be transferred. Can either be references to other drop containers,\n         * or their unique IDs.\n         */\n        this.connectedTo = [];\n        /**\n         * Unique ID for the drop zone. Can be used as a reference\n         * in the `connectedTo` of another `CdkDropList`.\n         */\n        this.id = `cdk-drop-list-${_uniqueIdCounter++}`;\n        /**\n         * Function that is used to determine whether an item\n         * is allowed to be moved into a drop container.\n         */\n        this.enterPredicate = () => true;\n        /** Functions that is used to determine whether an item can be sorted into a particular index. */\n        this.sortPredicate = () => true;\n        /** Emits when the user drops an item inside the container. */\n        this.dropped = new EventEmitter();\n        /**\n         * Emits when the user has moved a new drag item into this container.\n         */\n        this.entered = new EventEmitter();\n        /**\n         * Emits when the user removes an item from the container\n         * by dragging it into another container.\n         */\n        this.exited = new EventEmitter();\n        /** Emits as the user is swapping items while actively dragging. */\n        this.sorted = new EventEmitter();\n        /**\n         * Keeps track of the items that are registered with this container. Historically we used to\n         * do this with a `ContentChildren` query, however queries don't handle transplanted views very\n         * well which means that we can't handle cases like dragging the headers of a `mat-table`\n         * correctly. What we do instead is to have the items register themselves with the container\n         * and then we sort them based on their position in the DOM.\n         */\n        this._unsortedItems = new Set();\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            assertElementNode(element.nativeElement, 'cdkDropList');\n        }\n        this._dropListRef = dragDrop.createDropList(element);\n        this._dropListRef.data = this;\n        if (config) {\n            this._assignDefaults(config);\n        }\n        this._dropListRef.enterPredicate = (drag, drop) => {\n            return this.enterPredicate(drag.data, drop.data);\n        };\n        this._dropListRef.sortPredicate = (index, drag, drop) => {\n            return this.sortPredicate(index, drag.data, drop.data);\n        };\n        this._setupInputSyncSubscription(this._dropListRef);\n        this._handleEvents(this._dropListRef);\n        CdkDropList._dropLists.push(this);\n        if (_group) {\n            _group._items.add(this);\n        }\n    }\n    /** Registers an items with the drop list. */\n    addItem(item) {\n        this._unsortedItems.add(item);\n        if (this._dropListRef.isDragging()) {\n            this._syncItemsWithRef();\n        }\n    }\n    /** Removes an item from the drop list. */\n    removeItem(item) {\n        this._unsortedItems.delete(item);\n        if (this._dropListRef.isDragging()) {\n            this._syncItemsWithRef();\n        }\n    }\n    /** Gets the registered items in the list, sorted by their position in the DOM. */\n    getSortedItems() {\n        return Array.from(this._unsortedItems).sort((a, b) => {\n            const documentPosition = a._dragRef\n                .getVisibleElement()\n                .compareDocumentPosition(b._dragRef.getVisibleElement());\n            // `compareDocumentPosition` returns a bitmask so we have to use a bitwise operator.\n            // https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition\n            // tslint:disable-next-line:no-bitwise\n            return documentPosition & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1;\n        });\n    }\n    ngOnDestroy() {\n        const index = CdkDropList._dropLists.indexOf(this);\n        if (index > -1) {\n            CdkDropList._dropLists.splice(index, 1);\n        }\n        if (this._group) {\n            this._group._items.delete(this);\n        }\n        this._unsortedItems.clear();\n        this._dropListRef.dispose();\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /** Syncs the inputs of the CdkDropList with the options of the underlying DropListRef. */\n    _setupInputSyncSubscription(ref) {\n        if (this._dir) {\n            this._dir.change\n                .pipe(startWith(this._dir.value), takeUntil(this._destroyed))\n                .subscribe(value => ref.withDirection(value));\n        }\n        ref.beforeStarted.subscribe(() => {\n            const siblings = coerceArray(this.connectedTo).map(drop => {\n                if (typeof drop === 'string') {\n                    const correspondingDropList = CdkDropList._dropLists.find(list => list.id === drop);\n                    if (!correspondingDropList && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                        console.warn(`CdkDropList could not find connected drop list with id \"${drop}\"`);\n                    }\n                    return correspondingDropList;\n                }\n                return drop;\n            });\n            if (this._group) {\n                this._group._items.forEach(drop => {\n                    if (siblings.indexOf(drop) === -1) {\n                        siblings.push(drop);\n                    }\n                });\n            }\n            // Note that we resolve the scrollable parents here so that we delay the resolution\n            // as long as possible, ensuring that the element is in its final place in the DOM.\n            if (!this._scrollableParentsResolved) {\n                const scrollableParents = this._scrollDispatcher\n                    .getAncestorScrollContainers(this.element)\n                    .map(scrollable => scrollable.getElementRef().nativeElement);\n                this._dropListRef.withScrollableParents(scrollableParents);\n                // Only do this once since it involves traversing the DOM and the parents\n                // shouldn't be able to change without the drop list being destroyed.\n                this._scrollableParentsResolved = true;\n            }\n            ref.disabled = this.disabled;\n            ref.lockAxis = this.lockAxis;\n            ref.sortingDisabled = coerceBooleanProperty(this.sortingDisabled);\n            ref.autoScrollDisabled = coerceBooleanProperty(this.autoScrollDisabled);\n            ref.autoScrollStep = coerceNumberProperty(this.autoScrollStep, 2);\n            ref\n                .connectedTo(siblings.filter(drop => drop && drop !== this).map(list => list._dropListRef))\n                .withOrientation(this.orientation);\n        });\n    }\n    /** Handles events from the underlying DropListRef. */\n    _handleEvents(ref) {\n        ref.beforeStarted.subscribe(() => {\n            this._syncItemsWithRef();\n            this._changeDetectorRef.markForCheck();\n        });\n        ref.entered.subscribe(event => {\n            this.entered.emit({\n                container: this,\n                item: event.item.data,\n                currentIndex: event.currentIndex,\n            });\n        });\n        ref.exited.subscribe(event => {\n            this.exited.emit({\n                container: this,\n                item: event.item.data,\n            });\n            this._changeDetectorRef.markForCheck();\n        });\n        ref.sorted.subscribe(event => {\n            this.sorted.emit({\n                previousIndex: event.previousIndex,\n                currentIndex: event.currentIndex,\n                container: this,\n                item: event.item.data,\n            });\n        });\n        ref.dropped.subscribe(dropEvent => {\n            this.dropped.emit({\n                previousIndex: dropEvent.previousIndex,\n                currentIndex: dropEvent.currentIndex,\n                previousContainer: dropEvent.previousContainer.data,\n                container: dropEvent.container.data,\n                item: dropEvent.item.data,\n                isPointerOverContainer: dropEvent.isPointerOverContainer,\n                distance: dropEvent.distance,\n                dropPoint: dropEvent.dropPoint,\n                event: dropEvent.event,\n            });\n            // Mark for check since all of these events run outside of change\n            // detection and we're not guaranteed for something else to have triggered it.\n            this._changeDetectorRef.markForCheck();\n        });\n        merge(ref.receivingStarted, ref.receivingStopped).subscribe(() => this._changeDetectorRef.markForCheck());\n    }\n    /** Assigns the default input values based on a provided config object. */\n    _assignDefaults(config) {\n        const { lockAxis, draggingDisabled, sortingDisabled, listAutoScrollDisabled, listOrientation } = config;\n        this.disabled = draggingDisabled == null ? false : draggingDisabled;\n        this.sortingDisabled = sortingDisabled == null ? false : sortingDisabled;\n        this.autoScrollDisabled = listAutoScrollDisabled == null ? false : listAutoScrollDisabled;\n        this.orientation = listOrientation || 'vertical';\n        if (lockAxis) {\n            this.lockAxis = lockAxis;\n        }\n    }\n    /** Syncs up the registered drag items with underlying drop list ref. */\n    _syncItemsWithRef() {\n        this._dropListRef.withItems(this.getSortedItems().map(item => item._dragRef));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkDropList, deps: [{ token: i0.ElementRef }, { token: DragDrop }, { token: i0.ChangeDetectorRef }, { token: i1.ScrollDispatcher }, { token: i1$1.Directionality, optional: true }, { token: CDK_DROP_LIST_GROUP, optional: true, skipSelf: true }, { token: CDK_DRAG_CONFIG, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"16.1.1\", type: CdkDropList, isStandalone: true, selector: \"[cdkDropList], cdk-drop-list\", inputs: { connectedTo: [\"cdkDropListConnectedTo\", \"connectedTo\"], data: [\"cdkDropListData\", \"data\"], orientation: [\"cdkDropListOrientation\", \"orientation\"], id: \"id\", lockAxis: [\"cdkDropListLockAxis\", \"lockAxis\"], disabled: [\"cdkDropListDisabled\", \"disabled\"], sortingDisabled: [\"cdkDropListSortingDisabled\", \"sortingDisabled\"], enterPredicate: [\"cdkDropListEnterPredicate\", \"enterPredicate\"], sortPredicate: [\"cdkDropListSortPredicate\", \"sortPredicate\"], autoScrollDisabled: [\"cdkDropListAutoScrollDisabled\", \"autoScrollDisabled\"], autoScrollStep: [\"cdkDropListAutoScrollStep\", \"autoScrollStep\"] }, outputs: { dropped: \"cdkDropListDropped\", entered: \"cdkDropListEntered\", exited: \"cdkDropListExited\", sorted: \"cdkDropListSorted\" }, host: { properties: { \"attr.id\": \"id\", \"class.cdk-drop-list-disabled\": \"disabled\", \"class.cdk-drop-list-dragging\": \"_dropListRef.isDragging()\", \"class.cdk-drop-list-receiving\": \"_dropListRef.isReceiving()\" }, classAttribute: \"cdk-drop-list\" }, providers: [\n            // Prevent child drop lists from picking up the same group as their parent.\n            { provide: CDK_DROP_LIST_GROUP, useValue: undefined },\n            { provide: CDK_DROP_LIST, useExisting: CdkDropList },\n        ], exportAs: [\"cdkDropList\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: CdkDropList, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkDropList], cdk-drop-list',\n                    exportAs: 'cdkDropList',\n                    standalone: true,\n                    providers: [\n                        // Prevent child drop lists from picking up the same group as their parent.\n                        { provide: CDK_DROP_LIST_GROUP, useValue: undefined },\n                        { provide: CDK_DROP_LIST, useExisting: CdkDropList },\n                    ],\n                    host: {\n                        'class': 'cdk-drop-list',\n                        '[attr.id]': 'id',\n                        '[class.cdk-drop-list-disabled]': 'disabled',\n                        '[class.cdk-drop-list-dragging]': '_dropListRef.isDragging()',\n                        '[class.cdk-drop-list-receiving]': '_dropListRef.isReceiving()',\n                    },\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: DragDrop }, { type: i0.ChangeDetectorRef }, { type: i1.ScrollDispatcher }, { type: i1$1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: CdkDropListGroup, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [CDK_DROP_LIST_GROUP]\n                }, {\n                    type: SkipSelf\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [CDK_DRAG_CONFIG]\n                }] }]; }, propDecorators: { connectedTo: [{\n                type: Input,\n                args: ['cdkDropListConnectedTo']\n            }], data: [{\n                type: Input,\n                args: ['cdkDropListData']\n            }], orientation: [{\n                type: Input,\n                args: ['cdkDropListOrientation']\n            }], id: [{\n                type: Input\n            }], lockAxis: [{\n                type: Input,\n                args: ['cdkDropListLockAxis']\n            }], disabled: [{\n                type: Input,\n                args: ['cdkDropListDisabled']\n            }], sortingDisabled: [{\n                type: Input,\n                args: ['cdkDropListSortingDisabled']\n            }], enterPredicate: [{\n                type: Input,\n                args: ['cdkDropListEnterPredicate']\n            }], sortPredicate: [{\n                type: Input,\n                args: ['cdkDropListSortPredicate']\n            }], autoScrollDisabled: [{\n                type: Input,\n                args: ['cdkDropListAutoScrollDisabled']\n            }], autoScrollStep: [{\n                type: Input,\n                args: ['cdkDropListAutoScrollStep']\n            }], dropped: [{\n                type: Output,\n                args: ['cdkDropListDropped']\n            }], entered: [{\n                type: Output,\n                args: ['cdkDropListEntered']\n            }], exited: [{\n                type: Output,\n                args: ['cdkDropListExited']\n            }], sorted: [{\n                type: Output,\n                args: ['cdkDropListSorted']\n            }] } });\n\nconst DRAG_DROP_DIRECTIVES = [\n    CdkDropList,\n    CdkDropListGroup,\n    CdkDrag,\n    CdkDragHandle,\n    CdkDragPreview,\n    CdkDragPlaceholder,\n];\nclass DragDropModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: DragDropModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.1.1\", ngImport: i0, type: DragDropModule, imports: [CdkDropList,\n            CdkDropListGroup,\n            CdkDrag,\n            CdkDragHandle,\n            CdkDragPreview,\n            CdkDragPlaceholder], exports: [CdkScrollableModule, CdkDropList,\n            CdkDropListGroup,\n            CdkDrag,\n            CdkDragHandle,\n            CdkDragPreview,\n            CdkDragPlaceholder] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: DragDropModule, providers: [DragDrop], imports: [CdkScrollableModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.1.1\", ngImport: i0, type: DragDropModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: DRAG_DROP_DIRECTIVES,\n                    exports: [CdkScrollableModule, ...DRAG_DROP_DIRECTIVES],\n                    providers: [DragDrop],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CDK_DRAG_CONFIG, CDK_DRAG_HANDLE, CDK_DRAG_PARENT, CDK_DRAG_PLACEHOLDER, CDK_DRAG_PREVIEW, CDK_DROP_LIST, CDK_DROP_LIST_GROUP, CdkDrag, CdkDragHandle, CdkDragPlaceholder, CdkDragPreview, CdkDropList, CdkDropListGroup, DragDrop, DragDropModule, DragDropRegistry, DragRef, DropListRef, copyArrayItem, moveItemInArray, transferArrayItem };\n", "/**\n * Contains the list of OpenAPI data types\n * as defined by https://swagger.io/docs/specification/data-models/data-types/\n * @public\n */\nvar SchemaType;\n(function (SchemaType) {\n    /** String type. */\n    SchemaType[\"STRING\"] = \"string\";\n    /** Number type. */\n    SchemaType[\"NUMBER\"] = \"number\";\n    /** Integer type. */\n    SchemaType[\"INTEGER\"] = \"integer\";\n    /** Boolean type. */\n    SchemaType[\"BOOLEAN\"] = \"boolean\";\n    /** Array type. */\n    SchemaType[\"ARRAY\"] = \"array\";\n    /** Object type. */\n    SchemaType[\"OBJECT\"] = \"object\";\n})(SchemaType || (SchemaType = {}));\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @public\n */\nvar ExecutableCodeLanguage;\n(function (ExecutableCodeLanguage) {\n    ExecutableCodeLanguage[\"LANGUAGE_UNSPECIFIED\"] = \"language_unspecified\";\n    ExecutableCodeLanguage[\"PYTHON\"] = \"python\";\n})(ExecutableCodeLanguage || (ExecutableCodeLanguage = {}));\n/**\n * Possible outcomes of code execution.\n * @public\n */\nvar Outcome;\n(function (Outcome) {\n    /**\n     * Unspecified status. This value should not be used.\n     */\n    Outcome[\"OUTCOME_UNSPECIFIED\"] = \"outcome_unspecified\";\n    /**\n     * Code execution completed successfully.\n     */\n    Outcome[\"OUTCOME_OK\"] = \"outcome_ok\";\n    /**\n     * Code execution finished but with a failure. `stderr` should contain the\n     * reason.\n     */\n    Outcome[\"OUTCOME_FAILED\"] = \"outcome_failed\";\n    /**\n     * Code execution ran for too long, and was cancelled. There may or may not\n     * be a partial output present.\n     */\n    Outcome[\"OUTCOME_DEADLINE_EXCEEDED\"] = \"outcome_deadline_exceeded\";\n})(Outcome || (Outcome = {}));\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Possible roles.\n * @public\n */\nconst POSSIBLE_ROLES = [\"user\", \"model\", \"function\", \"system\"];\n/**\n * Harm categories that would cause prompts or candidates to be blocked.\n * @public\n */\nvar HarmCategory;\n(function (HarmCategory) {\n    HarmCategory[\"HARM_CATEGORY_UNSPECIFIED\"] = \"HARM_CATEGORY_UNSPECIFIED\";\n    HarmCategory[\"HARM_CATEGORY_HATE_SPEECH\"] = \"HARM_CATEGORY_HATE_SPEECH\";\n    HarmCategory[\"HARM_CATEGORY_SEXUALLY_EXPLICIT\"] = \"HARM_CATEGORY_SEXUALLY_EXPLICIT\";\n    HarmCategory[\"HARM_CATEGORY_HARASSMENT\"] = \"HARM_CATEGORY_HARASSMENT\";\n    HarmCategory[\"HARM_CATEGORY_DANGEROUS_CONTENT\"] = \"HARM_CATEGORY_DANGEROUS_CONTENT\";\n    HarmCategory[\"HARM_CATEGORY_CIVIC_INTEGRITY\"] = \"HARM_CATEGORY_CIVIC_INTEGRITY\";\n})(HarmCategory || (HarmCategory = {}));\n/**\n * Threshold above which a prompt or candidate will be blocked.\n * @public\n */\nvar HarmBlockThreshold;\n(function (HarmBlockThreshold) {\n    /** Threshold is unspecified. */\n    HarmBlockThreshold[\"HARM_BLOCK_THRESHOLD_UNSPECIFIED\"] = \"HARM_BLOCK_THRESHOLD_UNSPECIFIED\";\n    /** Content with NEGLIGIBLE will be allowed. */\n    HarmBlockThreshold[\"BLOCK_LOW_AND_ABOVE\"] = \"BLOCK_LOW_AND_ABOVE\";\n    /** Content with NEGLIGIBLE and LOW will be allowed. */\n    HarmBlockThreshold[\"BLOCK_MEDIUM_AND_ABOVE\"] = \"BLOCK_MEDIUM_AND_ABOVE\";\n    /** Content with NEGLIGIBLE, LOW, and MEDIUM will be allowed. */\n    HarmBlockThreshold[\"BLOCK_ONLY_HIGH\"] = \"BLOCK_ONLY_HIGH\";\n    /** All content will be allowed. */\n    HarmBlockThreshold[\"BLOCK_NONE\"] = \"BLOCK_NONE\";\n})(HarmBlockThreshold || (HarmBlockThreshold = {}));\n/**\n * Probability that a prompt or candidate matches a harm category.\n * @public\n */\nvar HarmProbability;\n(function (HarmProbability) {\n    /** Probability is unspecified. */\n    HarmProbability[\"HARM_PROBABILITY_UNSPECIFIED\"] = \"HARM_PROBABILITY_UNSPECIFIED\";\n    /** Content has a negligible chance of being unsafe. */\n    HarmProbability[\"NEGLIGIBLE\"] = \"NEGLIGIBLE\";\n    /** Content has a low chance of being unsafe. */\n    HarmProbability[\"LOW\"] = \"LOW\";\n    /** Content has a medium chance of being unsafe. */\n    HarmProbability[\"MEDIUM\"] = \"MEDIUM\";\n    /** Content has a high chance of being unsafe. */\n    HarmProbability[\"HIGH\"] = \"HIGH\";\n})(HarmProbability || (HarmProbability = {}));\n/**\n * Reason that a prompt was blocked.\n * @public\n */\nvar BlockReason;\n(function (BlockReason) {\n    // A blocked reason was not specified.\n    BlockReason[\"BLOCKED_REASON_UNSPECIFIED\"] = \"BLOCKED_REASON_UNSPECIFIED\";\n    // Content was blocked by safety settings.\n    BlockReason[\"SAFETY\"] = \"SAFETY\";\n    // Content was blocked, but the reason is uncategorized.\n    BlockReason[\"OTHER\"] = \"OTHER\";\n})(BlockReason || (BlockReason = {}));\n/**\n * Reason that a candidate finished.\n * @public\n */\nvar FinishReason;\n(function (FinishReason) {\n    // Default value. This value is unused.\n    FinishReason[\"FINISH_REASON_UNSPECIFIED\"] = \"FINISH_REASON_UNSPECIFIED\";\n    // Natural stop point of the model or provided stop sequence.\n    FinishReason[\"STOP\"] = \"STOP\";\n    // The maximum number of tokens as specified in the request was reached.\n    FinishReason[\"MAX_TOKENS\"] = \"MAX_TOKENS\";\n    // The candidate content was flagged for safety reasons.\n    FinishReason[\"SAFETY\"] = \"SAFETY\";\n    // The candidate content was flagged for recitation reasons.\n    FinishReason[\"RECITATION\"] = \"RECITATION\";\n    // The candidate content was flagged for using an unsupported language.\n    FinishReason[\"LANGUAGE\"] = \"LANGUAGE\";\n    // Token generation stopped because the content contains forbidden terms.\n    FinishReason[\"BLOCKLIST\"] = \"BLOCKLIST\";\n    // Token generation stopped for potentially containing prohibited content.\n    FinishReason[\"PROHIBITED_CONTENT\"] = \"PROHIBITED_CONTENT\";\n    // Token generation stopped because the content potentially contains Sensitive Personally Identifiable Information (SPII).\n    FinishReason[\"SPII\"] = \"SPII\";\n    // The function call generated by the model is invalid.\n    FinishReason[\"MALFORMED_FUNCTION_CALL\"] = \"MALFORMED_FUNCTION_CALL\";\n    // Unknown reason.\n    FinishReason[\"OTHER\"] = \"OTHER\";\n})(FinishReason || (FinishReason = {}));\n/**\n * Task type for embedding content.\n * @public\n */\nvar TaskType;\n(function (TaskType) {\n    TaskType[\"TASK_TYPE_UNSPECIFIED\"] = \"TASK_TYPE_UNSPECIFIED\";\n    TaskType[\"RETRIEVAL_QUERY\"] = \"RETRIEVAL_QUERY\";\n    TaskType[\"RETRIEVAL_DOCUMENT\"] = \"RETRIEVAL_DOCUMENT\";\n    TaskType[\"SEMANTIC_SIMILARITY\"] = \"SEMANTIC_SIMILARITY\";\n    TaskType[\"CLASSIFICATION\"] = \"CLASSIFICATION\";\n    TaskType[\"CLUSTERING\"] = \"CLUSTERING\";\n})(TaskType || (TaskType = {}));\n/**\n * @public\n */\nvar FunctionCallingMode;\n(function (FunctionCallingMode) {\n    // Unspecified function calling mode. This value should not be used.\n    FunctionCallingMode[\"MODE_UNSPECIFIED\"] = \"MODE_UNSPECIFIED\";\n    // Default model behavior, model decides to predict either a function call\n    // or a natural language repspose.\n    FunctionCallingMode[\"AUTO\"] = \"AUTO\";\n    // Model is constrained to always predicting a function call only.\n    // If \"allowed_function_names\" are set, the predicted function call will be\n    // limited to any one of \"allowed_function_names\", else the predicted\n    // function call will be any one of the provided \"function_declarations\".\n    FunctionCallingMode[\"ANY\"] = \"ANY\";\n    // Model will not predict any function call. Model behavior is same as when\n    // not passing any function declarations.\n    FunctionCallingMode[\"NONE\"] = \"NONE\";\n})(FunctionCallingMode || (FunctionCallingMode = {}));\n/**\n * The mode of the predictor to be used in dynamic retrieval.\n * @public\n */\nvar DynamicRetrievalMode;\n(function (DynamicRetrievalMode) {\n    // Unspecified function calling mode. This value should not be used.\n    DynamicRetrievalMode[\"MODE_UNSPECIFIED\"] = \"MODE_UNSPECIFIED\";\n    // Run retrieval only when system decides it is necessary.\n    DynamicRetrievalMode[\"MODE_DYNAMIC\"] = \"MODE_DYNAMIC\";\n})(DynamicRetrievalMode || (DynamicRetrievalMode = {}));\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Basic error type for this SDK.\n * @public\n */\nclass GoogleGenerativeAIError extends Error {\n    constructor(message) {\n        super(`[GoogleGenerativeAI Error]: ${message}`);\n    }\n}\n/**\n * Errors in the contents of a response from the model. This includes parsing\n * errors, or responses including a safety block reason.\n * @public\n */\nclass GoogleGenerativeAIResponseError extends GoogleGenerativeAIError {\n    constructor(message, response) {\n        super(message);\n        this.response = response;\n    }\n}\n/**\n * Error class covering HTTP errors when calling the server. Includes HTTP\n * status, statusText, and optional details, if provided in the server response.\n * @public\n */\nclass GoogleGenerativeAIFetchError extends GoogleGenerativeAIError {\n    constructor(message, status, statusText, errorDetails) {\n        super(message);\n        this.status = status;\n        this.statusText = statusText;\n        this.errorDetails = errorDetails;\n    }\n}\n/**\n * Errors in the contents of a request originating from user input.\n * @public\n */\nclass GoogleGenerativeAIRequestInputError extends GoogleGenerativeAIError {\n}\n/**\n * Error thrown when a request is aborted, either due to a timeout or\n * intentional cancellation by the user.\n * @public\n */\nclass GoogleGenerativeAIAbortError extends GoogleGenerativeAIError {\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst DEFAULT_BASE_URL = \"https://generativelanguage.googleapis.com\";\nconst DEFAULT_API_VERSION = \"v1beta\";\n/**\n * We can't `require` package.json if this runs on web. We will use rollup to\n * swap in the version number here at build time.\n */\nconst PACKAGE_VERSION = \"0.24.1\";\nconst PACKAGE_LOG_HEADER = \"genai-js\";\nvar Task;\n(function (Task) {\n    Task[\"GENERATE_CONTENT\"] = \"generateContent\";\n    Task[\"STREAM_GENERATE_CONTENT\"] = \"streamGenerateContent\";\n    Task[\"COUNT_TOKENS\"] = \"countTokens\";\n    Task[\"EMBED_CONTENT\"] = \"embedContent\";\n    Task[\"BATCH_EMBED_CONTENTS\"] = \"batchEmbedContents\";\n})(Task || (Task = {}));\nclass RequestUrl {\n    constructor(model, task, apiKey, stream, requestOptions) {\n        this.model = model;\n        this.task = task;\n        this.apiKey = apiKey;\n        this.stream = stream;\n        this.requestOptions = requestOptions;\n    }\n    toString() {\n        var _a, _b;\n        const apiVersion = ((_a = this.requestOptions) === null || _a === void 0 ? void 0 : _a.apiVersion) || DEFAULT_API_VERSION;\n        const baseUrl = ((_b = this.requestOptions) === null || _b === void 0 ? void 0 : _b.baseUrl) || DEFAULT_BASE_URL;\n        let url = `${baseUrl}/${apiVersion}/${this.model}:${this.task}`;\n        if (this.stream) {\n            url += \"?alt=sse\";\n        }\n        return url;\n    }\n}\n/**\n * Simple, but may become more complex if we add more versions to log.\n */\nfunction getClientHeaders(requestOptions) {\n    const clientHeaders = [];\n    if (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.apiClient) {\n        clientHeaders.push(requestOptions.apiClient);\n    }\n    clientHeaders.push(`${PACKAGE_LOG_HEADER}/${PACKAGE_VERSION}`);\n    return clientHeaders.join(\" \");\n}\nasync function getHeaders(url) {\n    var _a;\n    const headers = new Headers();\n    headers.append(\"Content-Type\", \"application/json\");\n    headers.append(\"x-goog-api-client\", getClientHeaders(url.requestOptions));\n    headers.append(\"x-goog-api-key\", url.apiKey);\n    let customHeaders = (_a = url.requestOptions) === null || _a === void 0 ? void 0 : _a.customHeaders;\n    if (customHeaders) {\n        if (!(customHeaders instanceof Headers)) {\n            try {\n                customHeaders = new Headers(customHeaders);\n            }\n            catch (e) {\n                throw new GoogleGenerativeAIRequestInputError(`unable to convert customHeaders value ${JSON.stringify(customHeaders)} to Headers: ${e.message}`);\n            }\n        }\n        for (const [headerName, headerValue] of customHeaders.entries()) {\n            if (headerName === \"x-goog-api-key\") {\n                throw new GoogleGenerativeAIRequestInputError(`Cannot set reserved header name ${headerName}`);\n            }\n            else if (headerName === \"x-goog-api-client\") {\n                throw new GoogleGenerativeAIRequestInputError(`Header name ${headerName} can only be set using the apiClient field`);\n            }\n            headers.append(headerName, headerValue);\n        }\n    }\n    return headers;\n}\nasync function constructModelRequest(model, task, apiKey, stream, body, requestOptions) {\n    const url = new RequestUrl(model, task, apiKey, stream, requestOptions);\n    return {\n        url: url.toString(),\n        fetchOptions: Object.assign(Object.assign({}, buildFetchOptions(requestOptions)), { method: \"POST\", headers: await getHeaders(url), body }),\n    };\n}\nasync function makeModelRequest(model, task, apiKey, stream, body, requestOptions = {}, \n// Allows this to be stubbed for tests\nfetchFn = fetch) {\n    const { url, fetchOptions } = await constructModelRequest(model, task, apiKey, stream, body, requestOptions);\n    return makeRequest(url, fetchOptions, fetchFn);\n}\nasync function makeRequest(url, fetchOptions, fetchFn = fetch) {\n    let response;\n    try {\n        response = await fetchFn(url, fetchOptions);\n    }\n    catch (e) {\n        handleResponseError(e, url);\n    }\n    if (!response.ok) {\n        await handleResponseNotOk(response, url);\n    }\n    return response;\n}\nfunction handleResponseError(e, url) {\n    let err = e;\n    if (err.name === \"AbortError\") {\n        err = new GoogleGenerativeAIAbortError(`Request aborted when fetching ${url.toString()}: ${e.message}`);\n        err.stack = e.stack;\n    }\n    else if (!(e instanceof GoogleGenerativeAIFetchError ||\n        e instanceof GoogleGenerativeAIRequestInputError)) {\n        err = new GoogleGenerativeAIError(`Error fetching from ${url.toString()}: ${e.message}`);\n        err.stack = e.stack;\n    }\n    throw err;\n}\nasync function handleResponseNotOk(response, url) {\n    let message = \"\";\n    let errorDetails;\n    try {\n        const json = await response.json();\n        message = json.error.message;\n        if (json.error.details) {\n            message += ` ${JSON.stringify(json.error.details)}`;\n            errorDetails = json.error.details;\n        }\n    }\n    catch (e) {\n        // ignored\n    }\n    throw new GoogleGenerativeAIFetchError(`Error fetching from ${url.toString()}: [${response.status} ${response.statusText}] ${message}`, response.status, response.statusText, errorDetails);\n}\n/**\n * Generates the request options to be passed to the fetch API.\n * @param requestOptions - The user-defined request options.\n * @returns The generated request options.\n */\nfunction buildFetchOptions(requestOptions) {\n    const fetchOptions = {};\n    if ((requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.signal) !== undefined || (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeout) >= 0) {\n        const controller = new AbortController();\n        if ((requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeout) >= 0) {\n            setTimeout(() => controller.abort(), requestOptions.timeout);\n        }\n        if (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.signal) {\n            requestOptions.signal.addEventListener(\"abort\", () => {\n                controller.abort();\n            });\n        }\n        fetchOptions.signal = controller.signal;\n    }\n    return fetchOptions;\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Adds convenience helper methods to a response object, including stream\n * chunks (as long as each chunk is a complete GenerateContentResponse JSON).\n */\nfunction addHelpers(response) {\n    response.text = () => {\n        if (response.candidates && response.candidates.length > 0) {\n            if (response.candidates.length > 1) {\n                console.warn(`This response had ${response.candidates.length} ` +\n                    `candidates. Returning text from the first candidate only. ` +\n                    `Access response.candidates directly to use the other candidates.`);\n            }\n            if (hadBadFinishReason(response.candidates[0])) {\n                throw new GoogleGenerativeAIResponseError(`${formatBlockErrorMessage(response)}`, response);\n            }\n            return getText(response);\n        }\n        else if (response.promptFeedback) {\n            throw new GoogleGenerativeAIResponseError(`Text not available. ${formatBlockErrorMessage(response)}`, response);\n        }\n        return \"\";\n    };\n    /**\n     * TODO: remove at next major version\n     */\n    response.functionCall = () => {\n        if (response.candidates && response.candidates.length > 0) {\n            if (response.candidates.length > 1) {\n                console.warn(`This response had ${response.candidates.length} ` +\n                    `candidates. Returning function calls from the first candidate only. ` +\n                    `Access response.candidates directly to use the other candidates.`);\n            }\n            if (hadBadFinishReason(response.candidates[0])) {\n                throw new GoogleGenerativeAIResponseError(`${formatBlockErrorMessage(response)}`, response);\n            }\n            console.warn(`response.functionCall() is deprecated. ` +\n                `Use response.functionCalls() instead.`);\n            return getFunctionCalls(response)[0];\n        }\n        else if (response.promptFeedback) {\n            throw new GoogleGenerativeAIResponseError(`Function call not available. ${formatBlockErrorMessage(response)}`, response);\n        }\n        return undefined;\n    };\n    response.functionCalls = () => {\n        if (response.candidates && response.candidates.length > 0) {\n            if (response.candidates.length > 1) {\n                console.warn(`This response had ${response.candidates.length} ` +\n                    `candidates. Returning function calls from the first candidate only. ` +\n                    `Access response.candidates directly to use the other candidates.`);\n            }\n            if (hadBadFinishReason(response.candidates[0])) {\n                throw new GoogleGenerativeAIResponseError(`${formatBlockErrorMessage(response)}`, response);\n            }\n            return getFunctionCalls(response);\n        }\n        else if (response.promptFeedback) {\n            throw new GoogleGenerativeAIResponseError(`Function call not available. ${formatBlockErrorMessage(response)}`, response);\n        }\n        return undefined;\n    };\n    return response;\n}\n/**\n * Returns all text found in all parts of first candidate.\n */\nfunction getText(response) {\n    var _a, _b, _c, _d;\n    const textStrings = [];\n    if ((_b = (_a = response.candidates) === null || _a === void 0 ? void 0 : _a[0].content) === null || _b === void 0 ? void 0 : _b.parts) {\n        for (const part of (_d = (_c = response.candidates) === null || _c === void 0 ? void 0 : _c[0].content) === null || _d === void 0 ? void 0 : _d.parts) {\n            if (part.text) {\n                textStrings.push(part.text);\n            }\n            if (part.executableCode) {\n                textStrings.push(\"\\n```\" +\n                    part.executableCode.language +\n                    \"\\n\" +\n                    part.executableCode.code +\n                    \"\\n```\\n\");\n            }\n            if (part.codeExecutionResult) {\n                textStrings.push(\"\\n```\\n\" + part.codeExecutionResult.output + \"\\n```\\n\");\n            }\n        }\n    }\n    if (textStrings.length > 0) {\n        return textStrings.join(\"\");\n    }\n    else {\n        return \"\";\n    }\n}\n/**\n * Returns functionCall of first candidate.\n */\nfunction getFunctionCalls(response) {\n    var _a, _b, _c, _d;\n    const functionCalls = [];\n    if ((_b = (_a = response.candidates) === null || _a === void 0 ? void 0 : _a[0].content) === null || _b === void 0 ? void 0 : _b.parts) {\n        for (const part of (_d = (_c = response.candidates) === null || _c === void 0 ? void 0 : _c[0].content) === null || _d === void 0 ? void 0 : _d.parts) {\n            if (part.functionCall) {\n                functionCalls.push(part.functionCall);\n            }\n        }\n    }\n    if (functionCalls.length > 0) {\n        return functionCalls;\n    }\n    else {\n        return undefined;\n    }\n}\nconst badFinishReasons = [\n    FinishReason.RECITATION,\n    FinishReason.SAFETY,\n    FinishReason.LANGUAGE,\n];\nfunction hadBadFinishReason(candidate) {\n    return (!!candidate.finishReason &&\n        badFinishReasons.includes(candidate.finishReason));\n}\nfunction formatBlockErrorMessage(response) {\n    var _a, _b, _c;\n    let message = \"\";\n    if ((!response.candidates || response.candidates.length === 0) &&\n        response.promptFeedback) {\n        message += \"Response was blocked\";\n        if ((_a = response.promptFeedback) === null || _a === void 0 ? void 0 : _a.blockReason) {\n            message += ` due to ${response.promptFeedback.blockReason}`;\n        }\n        if ((_b = response.promptFeedback) === null || _b === void 0 ? void 0 : _b.blockReasonMessage) {\n            message += `: ${response.promptFeedback.blockReasonMessage}`;\n        }\n    }\n    else if ((_c = response.candidates) === null || _c === void 0 ? void 0 : _c[0]) {\n        const firstCandidate = response.candidates[0];\n        if (hadBadFinishReason(firstCandidate)) {\n            message += `Candidate was blocked due to ${firstCandidate.finishReason}`;\n            if (firstCandidate.finishMessage) {\n                message += `: ${firstCandidate.finishMessage}`;\n            }\n        }\n    }\n    return message;\n}\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\n\r\nfunction __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nfunction __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst responseLineRE = /^data\\: (.*)(?:\\n\\n|\\r\\r|\\r\\n\\r\\n)/;\n/**\n * Process a response.body stream from the backend and return an\n * iterator that provides one complete GenerateContentResponse at a time\n * and a promise that resolves with a single aggregated\n * GenerateContentResponse.\n *\n * @param response - Response from a fetch call\n */\nfunction processStream(response) {\n    const inputStream = response.body.pipeThrough(new TextDecoderStream(\"utf8\", { fatal: true }));\n    const responseStream = getResponseStream(inputStream);\n    const [stream1, stream2] = responseStream.tee();\n    return {\n        stream: generateResponseSequence(stream1),\n        response: getResponsePromise(stream2),\n    };\n}\nasync function getResponsePromise(stream) {\n    const allResponses = [];\n    const reader = stream.getReader();\n    while (true) {\n        const { done, value } = await reader.read();\n        if (done) {\n            return addHelpers(aggregateResponses(allResponses));\n        }\n        allResponses.push(value);\n    }\n}\nfunction generateResponseSequence(stream) {\n    return __asyncGenerator(this, arguments, function* generateResponseSequence_1() {\n        const reader = stream.getReader();\n        while (true) {\n            const { value, done } = yield __await(reader.read());\n            if (done) {\n                break;\n            }\n            yield yield __await(addHelpers(value));\n        }\n    });\n}\n/**\n * Reads a raw stream from the fetch response and join incomplete\n * chunks, returning a new stream that provides a single complete\n * GenerateContentResponse in each iteration.\n */\nfunction getResponseStream(inputStream) {\n    const reader = inputStream.getReader();\n    const stream = new ReadableStream({\n        start(controller) {\n            let currentText = \"\";\n            return pump();\n            function pump() {\n                return reader\n                    .read()\n                    .then(({ value, done }) => {\n                    if (done) {\n                        if (currentText.trim()) {\n                            controller.error(new GoogleGenerativeAIError(\"Failed to parse stream\"));\n                            return;\n                        }\n                        controller.close();\n                        return;\n                    }\n                    currentText += value;\n                    let match = currentText.match(responseLineRE);\n                    let parsedResponse;\n                    while (match) {\n                        try {\n                            parsedResponse = JSON.parse(match[1]);\n                        }\n                        catch (e) {\n                            controller.error(new GoogleGenerativeAIError(`Error parsing JSON response: \"${match[1]}\"`));\n                            return;\n                        }\n                        controller.enqueue(parsedResponse);\n                        currentText = currentText.substring(match[0].length);\n                        match = currentText.match(responseLineRE);\n                    }\n                    return pump();\n                })\n                    .catch((e) => {\n                    let err = e;\n                    err.stack = e.stack;\n                    if (err.name === \"AbortError\") {\n                        err = new GoogleGenerativeAIAbortError(\"Request aborted when reading from the stream\");\n                    }\n                    else {\n                        err = new GoogleGenerativeAIError(\"Error reading from the stream\");\n                    }\n                    throw err;\n                });\n            }\n        },\n    });\n    return stream;\n}\n/**\n * Aggregates an array of `GenerateContentResponse`s into a single\n * GenerateContentResponse.\n */\nfunction aggregateResponses(responses) {\n    const lastResponse = responses[responses.length - 1];\n    const aggregatedResponse = {\n        promptFeedback: lastResponse === null || lastResponse === void 0 ? void 0 : lastResponse.promptFeedback,\n    };\n    for (const response of responses) {\n        if (response.candidates) {\n            let candidateIndex = 0;\n            for (const candidate of response.candidates) {\n                if (!aggregatedResponse.candidates) {\n                    aggregatedResponse.candidates = [];\n                }\n                if (!aggregatedResponse.candidates[candidateIndex]) {\n                    aggregatedResponse.candidates[candidateIndex] = {\n                        index: candidateIndex,\n                    };\n                }\n                // Keep overwriting, the last one will be final\n                aggregatedResponse.candidates[candidateIndex].citationMetadata =\n                    candidate.citationMetadata;\n                aggregatedResponse.candidates[candidateIndex].groundingMetadata =\n                    candidate.groundingMetadata;\n                aggregatedResponse.candidates[candidateIndex].finishReason =\n                    candidate.finishReason;\n                aggregatedResponse.candidates[candidateIndex].finishMessage =\n                    candidate.finishMessage;\n                aggregatedResponse.candidates[candidateIndex].safetyRatings =\n                    candidate.safetyRatings;\n                /**\n                 * Candidates should always have content and parts, but this handles\n                 * possible malformed responses.\n                 */\n                if (candidate.content && candidate.content.parts) {\n                    if (!aggregatedResponse.candidates[candidateIndex].content) {\n                        aggregatedResponse.candidates[candidateIndex].content = {\n                            role: candidate.content.role || \"user\",\n                            parts: [],\n                        };\n                    }\n                    const newPart = {};\n                    for (const part of candidate.content.parts) {\n                        if (part.text) {\n                            newPart.text = part.text;\n                        }\n                        if (part.functionCall) {\n                            newPart.functionCall = part.functionCall;\n                        }\n                        if (part.executableCode) {\n                            newPart.executableCode = part.executableCode;\n                        }\n                        if (part.codeExecutionResult) {\n                            newPart.codeExecutionResult = part.codeExecutionResult;\n                        }\n                        if (Object.keys(newPart).length === 0) {\n                            newPart.text = \"\";\n                        }\n                        aggregatedResponse.candidates[candidateIndex].content.parts.push(newPart);\n                    }\n                }\n            }\n            candidateIndex++;\n        }\n        if (response.usageMetadata) {\n            aggregatedResponse.usageMetadata = response.usageMetadata;\n        }\n    }\n    return aggregatedResponse;\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function generateContentStream(apiKey, model, params, requestOptions) {\n    const response = await makeModelRequest(model, Task.STREAM_GENERATE_CONTENT, apiKey, \n    /* stream */ true, JSON.stringify(params), requestOptions);\n    return processStream(response);\n}\nasync function generateContent(apiKey, model, params, requestOptions) {\n    const response = await makeModelRequest(model, Task.GENERATE_CONTENT, apiKey, \n    /* stream */ false, JSON.stringify(params), requestOptions);\n    const responseJson = await response.json();\n    const enhancedResponse = addHelpers(responseJson);\n    return {\n        response: enhancedResponse,\n    };\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction formatSystemInstruction(input) {\n    // null or undefined\n    if (input == null) {\n        return undefined;\n    }\n    else if (typeof input === \"string\") {\n        return { role: \"system\", parts: [{ text: input }] };\n    }\n    else if (input.text) {\n        return { role: \"system\", parts: [input] };\n    }\n    else if (input.parts) {\n        if (!input.role) {\n            return { role: \"system\", parts: input.parts };\n        }\n        else {\n            return input;\n        }\n    }\n}\nfunction formatNewContent(request) {\n    let newParts = [];\n    if (typeof request === \"string\") {\n        newParts = [{ text: request }];\n    }\n    else {\n        for (const partOrString of request) {\n            if (typeof partOrString === \"string\") {\n                newParts.push({ text: partOrString });\n            }\n            else {\n                newParts.push(partOrString);\n            }\n        }\n    }\n    return assignRoleToPartsAndValidateSendMessageRequest(newParts);\n}\n/**\n * When multiple Part types (i.e. FunctionResponsePart and TextPart) are\n * passed in a single Part array, we may need to assign different roles to each\n * part. Currently only FunctionResponsePart requires a role other than 'user'.\n * @private\n * @param parts Array of parts to pass to the model\n * @returns Array of content items\n */\nfunction assignRoleToPartsAndValidateSendMessageRequest(parts) {\n    const userContent = { role: \"user\", parts: [] };\n    const functionContent = { role: \"function\", parts: [] };\n    let hasUserContent = false;\n    let hasFunctionContent = false;\n    for (const part of parts) {\n        if (\"functionResponse\" in part) {\n            functionContent.parts.push(part);\n            hasFunctionContent = true;\n        }\n        else {\n            userContent.parts.push(part);\n            hasUserContent = true;\n        }\n    }\n    if (hasUserContent && hasFunctionContent) {\n        throw new GoogleGenerativeAIError(\"Within a single message, FunctionResponse cannot be mixed with other type of part in the request for sending chat message.\");\n    }\n    if (!hasUserContent && !hasFunctionContent) {\n        throw new GoogleGenerativeAIError(\"No content is provided for sending chat message.\");\n    }\n    if (hasUserContent) {\n        return userContent;\n    }\n    return functionContent;\n}\nfunction formatCountTokensInput(params, modelParams) {\n    var _a;\n    let formattedGenerateContentRequest = {\n        model: modelParams === null || modelParams === void 0 ? void 0 : modelParams.model,\n        generationConfig: modelParams === null || modelParams === void 0 ? void 0 : modelParams.generationConfig,\n        safetySettings: modelParams === null || modelParams === void 0 ? void 0 : modelParams.safetySettings,\n        tools: modelParams === null || modelParams === void 0 ? void 0 : modelParams.tools,\n        toolConfig: modelParams === null || modelParams === void 0 ? void 0 : modelParams.toolConfig,\n        systemInstruction: modelParams === null || modelParams === void 0 ? void 0 : modelParams.systemInstruction,\n        cachedContent: (_a = modelParams === null || modelParams === void 0 ? void 0 : modelParams.cachedContent) === null || _a === void 0 ? void 0 : _a.name,\n        contents: [],\n    };\n    const containsGenerateContentRequest = params.generateContentRequest != null;\n    if (params.contents) {\n        if (containsGenerateContentRequest) {\n            throw new GoogleGenerativeAIRequestInputError(\"CountTokensRequest must have one of contents or generateContentRequest, not both.\");\n        }\n        formattedGenerateContentRequest.contents = params.contents;\n    }\n    else if (containsGenerateContentRequest) {\n        formattedGenerateContentRequest = Object.assign(Object.assign({}, formattedGenerateContentRequest), params.generateContentRequest);\n    }\n    else {\n        // Array or string\n        const content = formatNewContent(params);\n        formattedGenerateContentRequest.contents = [content];\n    }\n    return { generateContentRequest: formattedGenerateContentRequest };\n}\nfunction formatGenerateContentInput(params) {\n    let formattedRequest;\n    if (params.contents) {\n        formattedRequest = params;\n    }\n    else {\n        // Array or string\n        const content = formatNewContent(params);\n        formattedRequest = { contents: [content] };\n    }\n    if (params.systemInstruction) {\n        formattedRequest.systemInstruction = formatSystemInstruction(params.systemInstruction);\n    }\n    return formattedRequest;\n}\nfunction formatEmbedContentInput(params) {\n    if (typeof params === \"string\" || Array.isArray(params)) {\n        const content = formatNewContent(params);\n        return { content };\n    }\n    return params;\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// https://ai.google.dev/api/rest/v1beta/Content#part\nconst VALID_PART_FIELDS = [\n    \"text\",\n    \"inlineData\",\n    \"functionCall\",\n    \"functionResponse\",\n    \"executableCode\",\n    \"codeExecutionResult\",\n];\nconst VALID_PARTS_PER_ROLE = {\n    user: [\"text\", \"inlineData\"],\n    function: [\"functionResponse\"],\n    model: [\"text\", \"functionCall\", \"executableCode\", \"codeExecutionResult\"],\n    // System instructions shouldn't be in history anyway.\n    system: [\"text\"],\n};\nfunction validateChatHistory(history) {\n    let prevContent = false;\n    for (const currContent of history) {\n        const { role, parts } = currContent;\n        if (!prevContent && role !== \"user\") {\n            throw new GoogleGenerativeAIError(`First content should be with role 'user', got ${role}`);\n        }\n        if (!POSSIBLE_ROLES.includes(role)) {\n            throw new GoogleGenerativeAIError(`Each item should include role field. Got ${role} but valid roles are: ${JSON.stringify(POSSIBLE_ROLES)}`);\n        }\n        if (!Array.isArray(parts)) {\n            throw new GoogleGenerativeAIError(\"Content should have 'parts' property with an array of Parts\");\n        }\n        if (parts.length === 0) {\n            throw new GoogleGenerativeAIError(\"Each Content should have at least one part\");\n        }\n        const countFields = {\n            text: 0,\n            inlineData: 0,\n            functionCall: 0,\n            functionResponse: 0,\n            fileData: 0,\n            executableCode: 0,\n            codeExecutionResult: 0,\n        };\n        for (const part of parts) {\n            for (const key of VALID_PART_FIELDS) {\n                if (key in part) {\n                    countFields[key] += 1;\n                }\n            }\n        }\n        const validParts = VALID_PARTS_PER_ROLE[role];\n        for (const key of VALID_PART_FIELDS) {\n            if (!validParts.includes(key) && countFields[key] > 0) {\n                throw new GoogleGenerativeAIError(`Content with role '${role}' can't contain '${key}' part`);\n            }\n        }\n        prevContent = true;\n    }\n}\n/**\n * Returns true if the response is valid (could be appended to the history), flase otherwise.\n */\nfunction isValidResponse(response) {\n    var _a;\n    if (response.candidates === undefined || response.candidates.length === 0) {\n        return false;\n    }\n    const content = (_a = response.candidates[0]) === null || _a === void 0 ? void 0 : _a.content;\n    if (content === undefined) {\n        return false;\n    }\n    if (content.parts === undefined || content.parts.length === 0) {\n        return false;\n    }\n    for (const part of content.parts) {\n        if (part === undefined || Object.keys(part).length === 0) {\n            return false;\n        }\n        if (part.text !== undefined && part.text === \"\") {\n            return false;\n        }\n    }\n    return true;\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Do not log a message for this error.\n */\nconst SILENT_ERROR = \"SILENT_ERROR\";\n/**\n * ChatSession class that enables sending chat messages and stores\n * history of sent and received messages so far.\n *\n * @public\n */\nclass ChatSession {\n    constructor(apiKey, model, params, _requestOptions = {}) {\n        this.model = model;\n        this.params = params;\n        this._requestOptions = _requestOptions;\n        this._history = [];\n        this._sendPromise = Promise.resolve();\n        this._apiKey = apiKey;\n        if (params === null || params === void 0 ? void 0 : params.history) {\n            validateChatHistory(params.history);\n            this._history = params.history;\n        }\n    }\n    /**\n     * Gets the chat history so far. Blocked prompts are not added to history.\n     * Blocked candidates are not added to history, nor are the prompts that\n     * generated them.\n     */\n    async getHistory() {\n        await this._sendPromise;\n        return this._history;\n    }\n    /**\n     * Sends a chat message and receives a non-streaming\n     * {@link GenerateContentResult}.\n     *\n     * Fields set in the optional {@link SingleRequestOptions} parameter will\n     * take precedence over the {@link RequestOptions} values provided to\n     * {@link GoogleGenerativeAI.getGenerativeModel }.\n     */\n    async sendMessage(request, requestOptions = {}) {\n        var _a, _b, _c, _d, _e, _f;\n        await this._sendPromise;\n        const newContent = formatNewContent(request);\n        const generateContentRequest = {\n            safetySettings: (_a = this.params) === null || _a === void 0 ? void 0 : _a.safetySettings,\n            generationConfig: (_b = this.params) === null || _b === void 0 ? void 0 : _b.generationConfig,\n            tools: (_c = this.params) === null || _c === void 0 ? void 0 : _c.tools,\n            toolConfig: (_d = this.params) === null || _d === void 0 ? void 0 : _d.toolConfig,\n            systemInstruction: (_e = this.params) === null || _e === void 0 ? void 0 : _e.systemInstruction,\n            cachedContent: (_f = this.params) === null || _f === void 0 ? void 0 : _f.cachedContent,\n            contents: [...this._history, newContent],\n        };\n        const chatSessionRequestOptions = Object.assign(Object.assign({}, this._requestOptions), requestOptions);\n        let finalResult;\n        // Add onto the chain.\n        this._sendPromise = this._sendPromise\n            .then(() => generateContent(this._apiKey, this.model, generateContentRequest, chatSessionRequestOptions))\n            .then((result) => {\n            var _a;\n            if (isValidResponse(result.response)) {\n                this._history.push(newContent);\n                const responseContent = Object.assign({ parts: [], \n                    // Response seems to come back without a role set.\n                    role: \"model\" }, (_a = result.response.candidates) === null || _a === void 0 ? void 0 : _a[0].content);\n                this._history.push(responseContent);\n            }\n            else {\n                const blockErrorMessage = formatBlockErrorMessage(result.response);\n                if (blockErrorMessage) {\n                    console.warn(`sendMessage() was unsuccessful. ${blockErrorMessage}. Inspect response object for details.`);\n                }\n            }\n            finalResult = result;\n        })\n            .catch((e) => {\n            // Resets _sendPromise to avoid subsequent calls failing and throw error.\n            this._sendPromise = Promise.resolve();\n            throw e;\n        });\n        await this._sendPromise;\n        return finalResult;\n    }\n    /**\n     * Sends a chat message and receives the response as a\n     * {@link GenerateContentStreamResult} containing an iterable stream\n     * and a response promise.\n     *\n     * Fields set in the optional {@link SingleRequestOptions} parameter will\n     * take precedence over the {@link RequestOptions} values provided to\n     * {@link GoogleGenerativeAI.getGenerativeModel }.\n     */\n    async sendMessageStream(request, requestOptions = {}) {\n        var _a, _b, _c, _d, _e, _f;\n        await this._sendPromise;\n        const newContent = formatNewContent(request);\n        const generateContentRequest = {\n            safetySettings: (_a = this.params) === null || _a === void 0 ? void 0 : _a.safetySettings,\n            generationConfig: (_b = this.params) === null || _b === void 0 ? void 0 : _b.generationConfig,\n            tools: (_c = this.params) === null || _c === void 0 ? void 0 : _c.tools,\n            toolConfig: (_d = this.params) === null || _d === void 0 ? void 0 : _d.toolConfig,\n            systemInstruction: (_e = this.params) === null || _e === void 0 ? void 0 : _e.systemInstruction,\n            cachedContent: (_f = this.params) === null || _f === void 0 ? void 0 : _f.cachedContent,\n            contents: [...this._history, newContent],\n        };\n        const chatSessionRequestOptions = Object.assign(Object.assign({}, this._requestOptions), requestOptions);\n        const streamPromise = generateContentStream(this._apiKey, this.model, generateContentRequest, chatSessionRequestOptions);\n        // Add onto the chain.\n        this._sendPromise = this._sendPromise\n            .then(() => streamPromise)\n            // This must be handled to avoid unhandled rejection, but jump\n            // to the final catch block with a label to not log this error.\n            .catch((_ignored) => {\n            throw new Error(SILENT_ERROR);\n        })\n            .then((streamResult) => streamResult.response)\n            .then((response) => {\n            if (isValidResponse(response)) {\n                this._history.push(newContent);\n                const responseContent = Object.assign({}, response.candidates[0].content);\n                // Response seems to come back without a role set.\n                if (!responseContent.role) {\n                    responseContent.role = \"model\";\n                }\n                this._history.push(responseContent);\n            }\n            else {\n                const blockErrorMessage = formatBlockErrorMessage(response);\n                if (blockErrorMessage) {\n                    console.warn(`sendMessageStream() was unsuccessful. ${blockErrorMessage}. Inspect response object for details.`);\n                }\n            }\n        })\n            .catch((e) => {\n            // Errors in streamPromise are already catchable by the user as\n            // streamPromise is returned.\n            // Avoid duplicating the error message in logs.\n            if (e.message !== SILENT_ERROR) {\n                // Users do not have access to _sendPromise to catch errors\n                // downstream from streamPromise, so they should not throw.\n                console.error(e);\n            }\n        });\n        return streamPromise;\n    }\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function countTokens(apiKey, model, params, singleRequestOptions) {\n    const response = await makeModelRequest(model, Task.COUNT_TOKENS, apiKey, false, JSON.stringify(params), singleRequestOptions);\n    return response.json();\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nasync function embedContent(apiKey, model, params, requestOptions) {\n    const response = await makeModelRequest(model, Task.EMBED_CONTENT, apiKey, false, JSON.stringify(params), requestOptions);\n    return response.json();\n}\nasync function batchEmbedContents(apiKey, model, params, requestOptions) {\n    const requestsWithModel = params.requests.map((request) => {\n        return Object.assign(Object.assign({}, request), { model });\n    });\n    const response = await makeModelRequest(model, Task.BATCH_EMBED_CONTENTS, apiKey, false, JSON.stringify({ requests: requestsWithModel }), requestOptions);\n    return response.json();\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Class for generative model APIs.\n * @public\n */\nclass GenerativeModel {\n    constructor(apiKey, modelParams, _requestOptions = {}) {\n        this.apiKey = apiKey;\n        this._requestOptions = _requestOptions;\n        if (modelParams.model.includes(\"/\")) {\n            // Models may be named \"models/model-name\" or \"tunedModels/model-name\"\n            this.model = modelParams.model;\n        }\n        else {\n            // If path is not included, assume it's a non-tuned model.\n            this.model = `models/${modelParams.model}`;\n        }\n        this.generationConfig = modelParams.generationConfig || {};\n        this.safetySettings = modelParams.safetySettings || [];\n        this.tools = modelParams.tools;\n        this.toolConfig = modelParams.toolConfig;\n        this.systemInstruction = formatSystemInstruction(modelParams.systemInstruction);\n        this.cachedContent = modelParams.cachedContent;\n    }\n    /**\n     * Makes a single non-streaming call to the model\n     * and returns an object containing a single {@link GenerateContentResponse}.\n     *\n     * Fields set in the optional {@link SingleRequestOptions} parameter will\n     * take precedence over the {@link RequestOptions} values provided to\n     * {@link GoogleGenerativeAI.getGenerativeModel }.\n     */\n    async generateContent(request, requestOptions = {}) {\n        var _a;\n        const formattedParams = formatGenerateContentInput(request);\n        const generativeModelRequestOptions = Object.assign(Object.assign({}, this._requestOptions), requestOptions);\n        return generateContent(this.apiKey, this.model, Object.assign({ generationConfig: this.generationConfig, safetySettings: this.safetySettings, tools: this.tools, toolConfig: this.toolConfig, systemInstruction: this.systemInstruction, cachedContent: (_a = this.cachedContent) === null || _a === void 0 ? void 0 : _a.name }, formattedParams), generativeModelRequestOptions);\n    }\n    /**\n     * Makes a single streaming call to the model and returns an object\n     * containing an iterable stream that iterates over all chunks in the\n     * streaming response as well as a promise that returns the final\n     * aggregated response.\n     *\n     * Fields set in the optional {@link SingleRequestOptions} parameter will\n     * take precedence over the {@link RequestOptions} values provided to\n     * {@link GoogleGenerativeAI.getGenerativeModel }.\n     */\n    async generateContentStream(request, requestOptions = {}) {\n        var _a;\n        const formattedParams = formatGenerateContentInput(request);\n        const generativeModelRequestOptions = Object.assign(Object.assign({}, this._requestOptions), requestOptions);\n        return generateContentStream(this.apiKey, this.model, Object.assign({ generationConfig: this.generationConfig, safetySettings: this.safetySettings, tools: this.tools, toolConfig: this.toolConfig, systemInstruction: this.systemInstruction, cachedContent: (_a = this.cachedContent) === null || _a === void 0 ? void 0 : _a.name }, formattedParams), generativeModelRequestOptions);\n    }\n    /**\n     * Gets a new {@link ChatSession} instance which can be used for\n     * multi-turn chats.\n     */\n    startChat(startChatParams) {\n        var _a;\n        return new ChatSession(this.apiKey, this.model, Object.assign({ generationConfig: this.generationConfig, safetySettings: this.safetySettings, tools: this.tools, toolConfig: this.toolConfig, systemInstruction: this.systemInstruction, cachedContent: (_a = this.cachedContent) === null || _a === void 0 ? void 0 : _a.name }, startChatParams), this._requestOptions);\n    }\n    /**\n     * Counts the tokens in the provided request.\n     *\n     * Fields set in the optional {@link SingleRequestOptions} parameter will\n     * take precedence over the {@link RequestOptions} values provided to\n     * {@link GoogleGenerativeAI.getGenerativeModel }.\n     */\n    async countTokens(request, requestOptions = {}) {\n        const formattedParams = formatCountTokensInput(request, {\n            model: this.model,\n            generationConfig: this.generationConfig,\n            safetySettings: this.safetySettings,\n            tools: this.tools,\n            toolConfig: this.toolConfig,\n            systemInstruction: this.systemInstruction,\n            cachedContent: this.cachedContent,\n        });\n        const generativeModelRequestOptions = Object.assign(Object.assign({}, this._requestOptions), requestOptions);\n        return countTokens(this.apiKey, this.model, formattedParams, generativeModelRequestOptions);\n    }\n    /**\n     * Embeds the provided content.\n     *\n     * Fields set in the optional {@link SingleRequestOptions} parameter will\n     * take precedence over the {@link RequestOptions} values provided to\n     * {@link GoogleGenerativeAI.getGenerativeModel }.\n     */\n    async embedContent(request, requestOptions = {}) {\n        const formattedParams = formatEmbedContentInput(request);\n        const generativeModelRequestOptions = Object.assign(Object.assign({}, this._requestOptions), requestOptions);\n        return embedContent(this.apiKey, this.model, formattedParams, generativeModelRequestOptions);\n    }\n    /**\n     * Embeds an array of {@link EmbedContentRequest}s.\n     *\n     * Fields set in the optional {@link SingleRequestOptions} parameter will\n     * take precedence over the {@link RequestOptions} values provided to\n     * {@link GoogleGenerativeAI.getGenerativeModel }.\n     */\n    async batchEmbedContents(batchEmbedContentRequest, requestOptions = {}) {\n        const generativeModelRequestOptions = Object.assign(Object.assign({}, this._requestOptions), requestOptions);\n        return batchEmbedContents(this.apiKey, this.model, batchEmbedContentRequest, generativeModelRequestOptions);\n    }\n}\n\n/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Top-level class for this SDK\n * @public\n */\nclass GoogleGenerativeAI {\n    constructor(apiKey) {\n        this.apiKey = apiKey;\n    }\n    /**\n     * Gets a {@link GenerativeModel} instance for the provided model name.\n     */\n    getGenerativeModel(modelParams, requestOptions) {\n        if (!modelParams.model) {\n            throw new GoogleGenerativeAIError(`Must provide a model name. ` +\n                `Example: genai.getGenerativeModel({ model: 'my-model-name' })`);\n        }\n        return new GenerativeModel(this.apiKey, modelParams, requestOptions);\n    }\n    /**\n     * Creates a {@link GenerativeModel} instance from provided content cache.\n     */\n    getGenerativeModelFromCachedContent(cachedContent, modelParams, requestOptions) {\n        if (!cachedContent.name) {\n            throw new GoogleGenerativeAIRequestInputError(\"Cached content must contain a `name` field.\");\n        }\n        if (!cachedContent.model) {\n            throw new GoogleGenerativeAIRequestInputError(\"Cached content must contain a `model` field.\");\n        }\n        /**\n         * Not checking tools and toolConfig for now as it would require a deep\n         * equality comparison and isn't likely to be a common case.\n         */\n        const disallowedDuplicates = [\"model\", \"systemInstruction\"];\n        for (const key of disallowedDuplicates) {\n            if ((modelParams === null || modelParams === void 0 ? void 0 : modelParams[key]) &&\n                cachedContent[key] &&\n                (modelParams === null || modelParams === void 0 ? void 0 : modelParams[key]) !== cachedContent[key]) {\n                if (key === \"model\") {\n                    const modelParamsComp = modelParams.model.startsWith(\"models/\")\n                        ? modelParams.model.replace(\"models/\", \"\")\n                        : modelParams.model;\n                    const cachedContentComp = cachedContent.model.startsWith(\"models/\")\n                        ? cachedContent.model.replace(\"models/\", \"\")\n                        : cachedContent.model;\n                    if (modelParamsComp === cachedContentComp) {\n                        continue;\n                    }\n                }\n                throw new GoogleGenerativeAIRequestInputError(`Different value for \"${key}\" specified in modelParams` +\n                    ` (${modelParams[key]}) and cachedContent (${cachedContent[key]})`);\n            }\n        }\n        const modelParamsFromCache = Object.assign(Object.assign({}, modelParams), { model: cachedContent.model, tools: cachedContent.tools, toolConfig: cachedContent.toolConfig, systemInstruction: cachedContent.systemInstruction, cachedContent });\n        return new GenerativeModel(this.apiKey, modelParamsFromCache, requestOptions);\n    }\n}\n\nexport { BlockReason, ChatSession, DynamicRetrievalMode, ExecutableCodeLanguage, FinishReason, FunctionCallingMode, GenerativeModel, GoogleGenerativeAI, GoogleGenerativeAIAbortError, GoogleGenerativeAIError, GoogleGenerativeAIFetchError, GoogleGenerativeAIRequestInputError, GoogleGenerativeAIResponseError, HarmBlockThreshold, HarmCategory, HarmProbability, Outcome, POSSIBLE_ROLES, SchemaType, TaskType };\n"], "names": ["GoogleGenerativeAI", "Observable", "from", "of", "map", "catchError", "environment", "AiService", "constructor", "apiAvailable", "<PERSON><PERSON><PERSON><PERSON>", "geminiApiKey", "genAI", "model", "getGenerativeModel", "console", "log", "error", "generateContent", "Promise", "resolve", "response", "text", "generateProjectTasks", "projectTitle", "memberCount", "teamMembers", "effectiveMemberCount", "Math", "max", "fallbackD<PERSON>", "createFallbackTaskData", "isApiAvailable", "observer", "setTimeout", "next", "complete", "teamMembersInfo", "length", "member", "index", "memberName", "name", "firstName", "lastName", "memberRole", "role", "join", "prompt", "pipe", "result", "textResult", "jsonMatch", "match", "JSON", "parse", "warn", "markApiAsUnavailable", "memberNames", "for<PERSON>ach", "push", "toLowerCase", "includes", "ecommerceEntities", "description", "assignedTo", "tasks", "title", "priority", "status", "entities", "slice", "moduleTypes", "Array", "_", "i", "askProjectQuestion", "question", "projectContext", "fallbackResponses", "getRandomFallbackResponse", "randomIndex", "floor", "random", "factory", "ɵfac", "providedIn", "throwError", "tap", "EquipeService", "http", "apiUrl", "urlBackend", "getEquipes", "get", "data", "handleError", "getEquipe", "id", "addEquipe", "equipe", "post", "updateEquipe", "put", "deleteEquipe", "delete", "addMembreToEquipe", "teamId", "membre", "memberData", "userId", "removeMembreFromEquipe", "membreId", "getTeamMembers", "team", "members", "memberId", "user", "_id", "errorMessage", "ErrorEvent", "message", "statusText", "url", "Error", "i0", "ɵɵinject", "i1", "HttpClient", "MembreService", "getMembres", "getMembre", "addMembre", "deleteMembre", "BehaviorSubject", "NotificationService", "notificationSubject", "getNotifications", "asObservable", "showSuccess", "timeout", "show", "type", "showError", "showInfo", "showWarning", "notification", "value", "clear", "TaskService", "getTasks", "getTasksByTeam", "getTask", "createTask", "task", "updateTask", "deleteTask", "updateTaskStatus", "patch", "Injectable", "Inject", "InjectionToken", "Directive", "Optional", "SkipSelf", "Input", "EventEmitter", "Self", "ContentChildren", "ContentChild", "Output", "NgModule", "DOCUMENT", "CdkScrollableModule", "_getEventTarget", "normalizePassiveListenerOptions", "_getShadowRoot", "coerceBooleanProperty", "coerceElement", "coerceNumberProperty", "coerce<PERSON><PERSON><PERSON>", "isFakeTouchstartFromScreenReader", "isFakeMousedownFromScreenReader", "Subject", "Subscription", "interval", "animationFrameScheduler", "merge", "takeUntil", "take", "startWith", "switchMap", "i1$1", "extendStyles", "dest", "source", "importantProperties", "key", "hasOwnProperty", "setProperty", "has", "removeProperty", "toggleNativeDragInteractions", "element", "enable", "userSelect", "style", "toggleVisibility", "position", "top", "opacity", "left", "combineTransforms", "transform", "initialTransform", "parseCssTimeUnitsToMs", "multiplier", "indexOf", "parseFloat", "getTransformTransitionDurationInMs", "computedStyle", "getComputedStyle", "transitionedProperties", "parseCssPropertyValue", "property", "find", "prop", "propertyIndex", "rawDurations", "rawDelays", "getPropertyValue", "split", "part", "trim", "getMutableClientRect", "clientRect", "getBoundingClientRect", "right", "bottom", "width", "height", "x", "y", "isInsideClientRect", "adjustClientRect", "isPointerNearClientRect", "rect", "threshold", "pointerX", "pointerY", "xThreshold", "yT<PERSON><PERSON>old", "ParentPositionTracker", "_document", "positions", "Map", "cache", "elements", "set", "scrollPosition", "getViewportScrollPosition", "scrollTop", "scrollLeft", "handleScroll", "event", "target", "cachedPosition", "newTop", "newLeft", "viewportScrollPosition", "topDifference", "leftDifference", "node", "contains", "window", "scrollY", "scrollX", "deepCloneNode", "clone", "cloneNode", "descendantsWithId", "querySelectorAll", "nodeName", "removeAttribute", "transferCanvasData", "transferInputData", "transferData", "selector", "callback", "descendantElements", "cloneElements", "cloneUniqueId", "context", "getContext", "drawImage", "passiveEventListenerOptions", "passive", "activeEventListenerOptions", "MOUSE_EVENT_IGNORE_TIME", "dragImportantProperties", "Set", "DragRef", "disabled", "_disabled", "_dropContainer", "newValue", "_toggleNativeDragInteractions", "_handles", "handle", "_config", "_ngZone", "_viewportRuler", "_dragDropRegistry", "_passiveTransform", "_activeTransform", "_hasStartedDragging", "_moveEvents", "_pointerMoveSubscription", "EMPTY", "_pointerUpSubscription", "_scrollSubscription", "_resizeSubscription", "_boundaryElement", "_nativeInteractionsEnabled", "_<PERSON><PERSON><PERSON><PERSON>", "_direction", "dragStartDelay", "beforeStarted", "started", "released", "ended", "entered", "exited", "dropped", "moved", "_pointerDown", "targetHandle", "_getTar<PERSON><PERSON><PERSON>le", "_initializeDragSequence", "_rootElement", "_pointerMove", "pointerPosition", "_getPointerPositionOnPage", "distanceX", "abs", "_pickupPositionOnPage", "distanceY", "isOverThreshold", "dragStartThreshold", "isDelayElapsed", "Date", "now", "_dragStartTime", "_getDragStartDelay", "container", "_endDragSequence", "isDragging", "isReceiving", "preventDefault", "run", "_startDragSequence", "constrainedPointerPosition", "_getConstrainedPointerPosition", "_hasMoved", "_lastKnownPointerPosition", "_updatePointerDirectionDelta", "_updateActiveDropContainer", "offset", "constrainPosition", "_initialClientRect", "activeTransform", "_applyRootElementTransform", "observers", "distance", "_getDragDistance", "delta", "_pointerDirectionDelta", "_pointerUp", "_nativeDragStart", "withRootElement", "with<PERSON><PERSON>nt", "parentDragRef", "_parentPositions", "registerDragItem", "getPlaceholderElement", "_placeholder", "getRootElement", "getVisibleElement", "<PERSON><PERSON><PERSON><PERSON>", "handles", "<PERSON><PERSON><PERSON><PERSON>", "add", "withPreviewTemplate", "template", "_previewTemplate", "withPlaceholderTemplate", "_placeholderTemplate", "rootElement", "_removeRootElementListeners", "runOutsideAngular", "addEventListener", "_initialTransform", "undefined", "SVGElement", "_ownerSVGElement", "ownerSVGElement", "withBoundaryElement", "boundaryElement", "unsubscribe", "change", "subscribe", "_containInsideBoundaryOnResize", "parent", "_parentDragRef", "dispose", "remove", "_anchor", "_destroyPreview", "_destroyPlaceholder", "removeDragItem", "_removeSubscriptions", "reset", "disable<PERSON><PERSON><PERSON>", "enableHandle", "withDirection", "direction", "_withDropContainer", "getFreeDragPosition", "setFreeDragPosition", "withPreviewContainer", "_previewContainer", "_sortFromLastPointerPosition", "_preview", "_previewRef", "destroy", "_placeholderRef", "stopDragging", "webkitTapHighlightColor", "_rootElementTapHighlight", "_stopScrolling", "_animatePreviewToPlaceholder", "then", "_cleanupDragArtifacts", "_cleanupCachedDimensions", "dropPoint", "isTouchEvent", "_lastTouchEventTime", "dropContainer", "parentNode", "placeholder", "_createPlaceholderElement", "anchor", "createComment", "shadowRoot", "insertBefore", "_createPreviewElement", "body", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "_getPreviewInsertionPoint", "start", "_initialContainer", "_initialIndex", "getItemIndex", "getScrollableParents", "referenceElement", "stopPropagation", "isTouchSequence", "isAuxiliaryMouseButton", "button", "isSyntheticEvent", "isFakeEvent", "draggable", "rootStyles", "pointer<PERSON><PERSON>", "pointerUp", "scrolled", "scrollEvent", "_updateOnScroll", "_boundaryRect", "previewTemplate", "_pickupPositionInElement", "matchSize", "_getPointerPositionInElement", "_pointerPositionAtLastDirectionChange", "startDragging", "_previewRect", "currentIndex", "isPointerOverContainer", "_isOverContainer", "item", "previousIndex", "previousContainer", "drop", "rawX", "rawY", "newContainer", "_getSiblingContainerFromPosition", "exit", "enter", "sortingDisabled", "_startScrollingIfNecessary", "_sortItem", "_applyPreviewTransform", "previewConfig", "previewClass", "preview", "rootRect", "viewRef", "viewContainer", "createEmbeddedView", "detectChanges", "getRootNode", "matchElementSize", "getTransform", "zIndex", "classList", "setAttribute", "isArray", "className", "placeholder<PERSON><PERSON><PERSON>", "duration", "handler", "propertyName", "removeEventListener", "clearTimeout", "placeholder<PERSON><PERSON><PERSON>g", "placeholderTemplate", "pointerEvents", "elementRect", "handleElement", "referenceRect", "point", "targetTouches", "_getViewportScrollPosition", "pageX", "pageY", "touches", "changedTouches", "svgMatrix", "getScreenCTM", "svgPoint", "createSVGPoint", "matrixTransform", "inverse", "dropContainerLock", "lockAxis", "pickupX", "pickupY", "boundaryRect", "previewWidth", "previewHeight", "_getPreviewRect", "minY", "maxY", "minX", "maxX", "clamp$1", "pointerPositionOnPage", "positionSinceLastChange", "changeX", "changeY", "pointerDirectionChangeThreshold", "shouldEnable", "styles", "currentPosition", "pickupPosition", "leftOverflow", "rightOverflow", "topOverflow", "bottomOverflow", "touch", "mouse", "scrollDifference", "_cachedShadowRoot", "initialParent", "previewContainer", "documentRef", "fullscreenElement", "webkitFullscreenElement", "mozFullScreenElement", "msFullscreenElement", "round", "min", "rootNodes", "nodeType", "ELEMENT_NODE", "wrapper", "createElement", "sourceRect", "moveItemInArray", "array", "fromIndex", "toIndex", "clamp", "to", "transferArrayItem", "currentArray", "targetArray", "targetIndex", "splice", "copyArrayItem", "SingleAxisSortStrategy", "_element", "_itemPositions", "orientation", "_previousSwap", "drag", "overlaps", "items", "withItems", "sort", "pointer<PERSON><PERSON><PERSON>", "siblings", "newIndex", "_getItemIndexFromPointerPosition", "isHorizontal", "findIndex", "currentItem", "siblingAtNewPosition", "newPosition", "itemOffset", "_getItemOffsetPx", "siblingOffset", "_getSiblingOffsetPx", "oldOrder", "sibling", "isDraggedItem", "elementToOffset", "activeDraggables", "_activeDraggables", "newPositionReference", "_shouldEnterAsFirstChild", "parentElement", "_cacheItemPositions", "withSortPredicate", "predicate", "_sortPredicate", "p", "getActiveItemsSnapshot", "reverse", "updateOnScroll", "elementToMeasure", "a", "b", "immediateSibling", "end", "itemPositions", "reversed", "lastItemRect", "firstItemRect", "DROP_PROXIMITY_THRESHOLD", "SCROLL_PROXIMITY_THRESHOLD", "DropListRef", "autoScrollDisabled", "autoScrollStep", "enterPredicate", "sortPredicate", "sorted", "receivingStarted", "receivingStopped", "_isDragging", "_draggables", "_siblings", "_activeSiblings", "_viewportScrollSubscription", "_verticalScrollDirection", "_horizontalScrollDirection", "_stopScrollTimers", "_startScrollInterval", "_scrollNode", "scrollStep", "scrollBy", "withScrollableParents", "registerDropContainer", "_sortStrategy", "removeDropContainer", "_draggingStarted", "_notifyReceivingSiblings", "_cacheParentPositions", "_reset", "previousItems", "draggedItems", "filter", "every", "connectedTo", "withOrientation", "_scrollableElements", "size", "_clientRect", "scrollNode", "verticalScrollDirection", "horizontalScrollDirection", "getElementScrollDirections", "getViewportSize", "getVerticalScrollDirection", "getHorizontalScrollDirection", "_initialScrollSnap", "msScrollSnapType", "scrollSnapType", "_listenToScrollEvents", "_stopReceiving", "_canReceive", "elementFromPoint", "nativeElement", "_startReceiving", "activeSiblings", "initiator", "receiver", "computedVertical", "computedHorizontal", "scrollHeight", "clientHeight", "scrollWidth", "clientWidth", "activeCapturingEventOptions", "capture", "DragDropRegistry", "_dropInstances", "_dragInstances", "_activeDragInstances", "_globalListeners", "_draggingPredicate", "scroll", "_preventDefaultWhileDragging", "_persistentTouchmoveListener", "some", "startsWith", "e", "options", "config", "_clearGlobalListeners", "streams", "eventOptions", "ngOnDestroy", "instance", "DragDropRegistry_Factory", "t", "NgZone", "ɵprov", "ɵɵdefineInjectable", "token", "ngDevMode", "ɵsetClassMetadata", "args", "decorators", "DEFAULT_CONFIG", "DragDrop", "createDrag", "createDropList", "DragDrop_Factory", "ViewportRuler", "CDK_DRAG_PARENT", "assertElementNode", "CDK_DRAG_HANDLE", "CdkDragHandle", "_stateChanges", "parentDrag", "_parentDrag", "CdkDragHandle_Factory", "ɵɵdirectiveInject", "ElementRef", "ɵdir", "ɵɵdefineDirective", "selectors", "hostAttrs", "inputs", "standalone", "features", "ɵɵProvidersFeature", "provide", "useExisting", "host", "providers", "CDK_DRAG_PLACEHOLDER", "CdkDragPlaceholder", "templateRef", "CdkDragPlaceholder_Factory", "TemplateRef", "CDK_DRAG_PREVIEW", "CdkDragPreview", "_matchSize", "CdkDragPreview_Factory", "CDK_DRAG_CONFIG", "DRAG_HOST_CLASS", "CDK_DROP_LIST", "CdkDrag", "_dragRef", "_viewContainerRef", "_dir", "dragDrop", "_changeDetectorRef", "_selfHandle", "_destroyed", "subscription", "movedEvent", "_assignDefaults", "_dropListRef", "addItem", "_syncInputs", "_handleEvents", "ngAfterViewInit", "onStable", "_updateRootElement", "_setupHandlesListener", "freeDragPosition", "ngOnChanges", "changes", "rootSelectorChange", "positionChange", "firstChange", "removeItem", "rootElementSelector", "closest", "_getBoundaryElement", "boundary", "ref", "dir", "startEvent", "emit", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "releaseEvent", "endEvent", "enterEvent", "exitEvent", "dropEvent", "draggingDisabled", "childHandleElements", "handleInstance", "dragRef", "CdkDrag_Factory", "ViewContainerRef", "Directionality", "ChangeDetectorRef", "contentQueries", "CdkDrag_ContentQueries", "rf", "ctx", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostVars", "hostBindings", "CdkDrag_HostBindings", "ɵɵclassProp", "outputs", "exportAs", "ɵɵNgOnChangesFeature", "descendants", "CDK_DROP_LIST_GROUP", "CdkDropListGroup", "_items", "CdkDropListGroup_Factory", "_uniqueIdCounter", "CdkDropList", "_dropLists", "_group", "_scrollDispatcher", "_unsortedItems", "_setupInputSyncSubscription", "_syncItemsWithRef", "getSortedItems", "documentPosition", "compareDocumentPosition", "Node", "DOCUMENT_POSITION_FOLLOWING", "correspondingDropList", "list", "_scrollableParentsResolved", "scrollableParents", "getAncestorScrollContainers", "scrollable", "getElementRef", "listAutoScrollDisabled", "listOrientation", "CdkDropList_Factory", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CdkDropList_HostBindings", "ɵɵattribute", "useValue", "DRAG_DROP_DIRECTIVES", "DragDropModule", "DragDropModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "SchemaType", "ExecutableCodeLanguage", "Outcome", "POSSIBLE_ROLES", "HarmCategory", "HarmBlockThreshold", "HarmProbability", "BlockReason", "FinishReason", "TaskType", "FunctionCallingMode", "DynamicRetrievalMode", "GoogleGenerativeAIError", "GoogleGenerativeAIResponseError", "GoogleGenerativeAIFetchError", "errorDetails", "GoogleGenerativeAIRequestInputError", "GoogleGenerativeAIAbortError", "DEFAULT_BASE_URL", "DEFAULT_API_VERSION", "PACKAGE_VERSION", "PACKAGE_LOG_HEADER", "Task", "RequestUrl", "stream", "requestOptions", "toString", "_a", "_b", "apiVersion", "baseUrl", "getClientHeaders", "clientHeaders", "apiClient", "getHeaders", "_x", "_getHeaders", "apply", "arguments", "_asyncToGenerator", "headers", "Headers", "append", "customHeaders", "stringify", "headerName", "headerValue", "entries", "constructModelRequest", "_x2", "_x3", "_x4", "_x5", "_x6", "_x7", "_constructModelRequest", "fetchOptions", "Object", "assign", "buildFetchOptions", "method", "makeModelRequest", "_x8", "_x9", "_x0", "_x1", "_x10", "_makeModelRequest", "fetchFn", "fetch", "makeRequest", "_x11", "_x12", "_makeRequest", "handleResponseError", "ok", "handleResponseNotOk", "err", "stack", "_x13", "_x14", "_handleResponseNotOk", "json", "details", "signal", "controller", "AbortController", "abort", "addHelpers", "candidates", "hadBadFinishReason", "formatBlockErrorMessage", "getText", "promptFeedback", "functionCall", "getFunctionCalls", "functionCalls", "_c", "_d", "textStrings", "content", "parts", "executableCode", "language", "code", "codeExecutionResult", "output", "badFinishReasons", "RECITATION", "SAFETY", "LANGUAGE", "candidate", "finishReason", "blockReason", "blockReasonMessage", "firstCandidate", "finishMessage", "__await", "v", "__asyncGenerator", "thisArg", "_arguments", "generator", "Symbol", "asyncIterator", "TypeError", "g", "q", "verb", "n", "resume", "step", "settle", "r", "fulfill", "reject", "f", "shift", "SuppressedError", "suppressed", "responseLineRE", "processStream", "inputStream", "pipeThrough", "TextDecoderStream", "fatal", "responseStream", "getResponseStream", "stream1", "stream2", "tee", "generateResponseSequence", "getResponsePromise", "_x15", "_getResponsePromise", "allResponses", "reader", "<PERSON><PERSON><PERSON><PERSON>", "done", "read", "aggregateResponses", "generateResponseSequence_1", "ReadableStream", "currentText", "pump", "close", "parsedResponse", "enqueue", "substring", "catch", "responses", "lastResponse", "aggregatedResponse", "candidateIndex", "citationMetadata", "groundingMetadata", "safetyRatings", "newPart", "keys", "usageMetadata", "generateContentStream", "_x16", "_x17", "_x18", "_x19", "_generateContentStream", "params", "STREAM_GENERATE_CONTENT", "_x20", "_x21", "_x22", "_x23", "_generateContent", "GENERATE_CONTENT", "responseJson", "enhancedResponse", "formatSystemInstruction", "input", "formatNewContent", "request", "newParts", "partOrString", "assignRoleToPartsAndValidateSendMessageRequest", "userContent", "functionContent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasFunctionContent", "formatCountTokensInput", "modelParams", "formattedGenerateContentRequest", "generationConfig", "safetySettings", "tools", "toolConfig", "systemInstruction", "cachedContent", "contents", "containsGenerateContentRequest", "generateContentRequest", "formatGenerateContentInput", "formattedRequest", "formatEmbedContentInput", "VALID_PART_FIELDS", "VALID_PARTS_PER_ROLE", "function", "system", "validateChatHistory", "history", "prevContent", "currContent", "countFields", "inlineData", "functionResponse", "fileData", "validParts", "isValidResponse", "SILENT_ERROR", "ChatSession", "_requestOptions", "_history", "_sendPromise", "_api<PERSON><PERSON>", "getHistory", "_this", "sendMessage", "_x24", "_this2", "_e", "_f", "newContent", "chatSessionRequestOptions", "finalResult", "responseContent", "blockErrorMessage", "sendMessageStream", "_x25", "_this3", "streamPromise", "_ignored", "streamResult", "countTokens", "_x26", "_x27", "_x28", "_x29", "_countTokens", "singleRequestOptions", "COUNT_TOKENS", "embedContent", "_x30", "_x31", "_x32", "_x33", "_embedContent", "EMBED_CONTENT", "batchEmbedContents", "_x34", "_x35", "_x36", "_x37", "_batchEmbedContents", "requestsWithModel", "requests", "BATCH_EMBED_CONTENTS", "GenerativeModel", "_x38", "_this4", "formattedParams", "generativeModelRequestOptions", "_x39", "_this5", "startChat", "startChatParams", "_x40", "_this6", "_x41", "_this7", "_x42", "_this8", "batchEmbedContentRequest", "getGenerativeModelFromCachedContent", "disallowedDuplicates", "modelParamsComp", "replace", "cachedContentComp", "modelParamsFromCache"], "sourceRoot": "webpack:///", "x_google_ignoreList": [5, 6]}