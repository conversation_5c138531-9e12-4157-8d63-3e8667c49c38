{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class MessagesListComponent {\n  static {\n    this.ɵfac = function MessagesListComponent_Factory(t) {\n      return new (t || MessagesListComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MessagesListComponent,\n      selectors: [[\"app-messages-list\"]],\n      decls: 0,\n      vars: 0,\n      template: function MessagesListComponent_Template(rf, ctx) {},\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJtZXNzYWdlcy1saXN0LmNvbXBvbmVudC5jc3MifQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvbWVzc2FnZXMvbWVzc2FnZXMtbGlzdC9tZXNzYWdlcy1saXN0LmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUNBLDRLQUE0SyIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["MessagesListComponent", "selectors", "decls", "vars", "template", "MessagesListComponent_Template", "rf", "ctx", "styles"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\messages-list\\messages-list.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-messages-list',\n  templateUrl: './messages-list.component.html',\n  styleUrls: ['./messages-list.component.css'],\n})\nexport class MessagesListComponent {\n  \n\n}\n"], "mappings": ";AAOA,OAAM,MAAOA,qBAAqB;;;uBAArBA,qBAAqB;IAAA;EAAA;;;YAArBA,qBAAqB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}