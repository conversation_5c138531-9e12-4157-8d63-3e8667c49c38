{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport const THEMES = {\n  dark: {\n    name: 'dark',\n    displayName: 'Sombre',\n    colors: {\n      primary: '#3b82f6',\n      secondary: '#6366f1',\n      accent: '#8b5cf6',\n      background: '#111827',\n      surface: '#1f2937',\n      text: '#ffffff',\n      textSecondary: '#9ca3af',\n      border: '#374151',\n      success: '#10b981',\n      warning: '#f59e0b',\n      error: '#ef4444'\n    },\n    gradients: {\n      primary: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n      secondary: 'linear-gradient(135deg, #6366f1 0%, #4f46e5 100%)',\n      accent: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)'\n    }\n  },\n  neon: {\n    name: 'neon',\n    displayName: 'Néon',\n    colors: {\n      primary: '#00ffff',\n      secondary: '#ff00ff',\n      accent: '#ffff00',\n      background: '#0a0a0a',\n      surface: '#1a1a1a',\n      text: '#ffffff',\n      textSecondary: '#cccccc',\n      border: '#333333',\n      success: '#00ff00',\n      warning: '#ff8800',\n      error: '#ff0040'\n    },\n    gradients: {\n      primary: 'linear-gradient(135deg, #00ffff 0%, #0080ff 100%)',\n      secondary: 'linear-gradient(135deg, #ff00ff 0%, #8000ff 100%)',\n      accent: 'linear-gradient(135deg, #ffff00 0%, #ff8000 100%)'\n    }\n  },\n  purple: {\n    name: 'purple',\n    displayName: 'Violet',\n    colors: {\n      primary: '#8b5cf6',\n      secondary: '#a855f7',\n      accent: '#c084fc',\n      background: '#1e1b4b',\n      surface: '#312e81',\n      text: '#ffffff',\n      textSecondary: '#c7d2fe',\n      border: '#4c1d95',\n      success: '#22c55e',\n      warning: '#eab308',\n      error: '#ef4444'\n    },\n    gradients: {\n      primary: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n      secondary: 'linear-gradient(135deg, #a855f7 0%, #9333ea 100%)',\n      accent: 'linear-gradient(135deg, #c084fc 0%, #a855f7 100%)'\n    }\n  },\n  ocean: {\n    name: 'ocean',\n    displayName: 'Océan',\n    colors: {\n      primary: '#0ea5e9',\n      secondary: '#06b6d4',\n      accent: '#22d3ee',\n      background: '#0c4a6e',\n      surface: '#075985',\n      text: '#ffffff',\n      textSecondary: '#bae6fd',\n      border: '#0369a1',\n      success: '#059669',\n      warning: '#d97706',\n      error: '#dc2626'\n    },\n    gradients: {\n      primary: 'linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%)',\n      secondary: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',\n      accent: 'linear-gradient(135deg, #22d3ee 0%, #06b6d4 100%)'\n    }\n  }\n};\nexport class ThemeService {\n  constructor() {\n    this.currentTheme = new BehaviorSubject(THEMES['dark']);\n    this.currentTheme$ = this.currentTheme.asObservable();\n    this.loadThemeFromStorage();\n    this.applyTheme(this.currentTheme.value);\n  }\n  setTheme(themeName) {\n    const theme = THEMES[themeName];\n    if (theme) {\n      this.currentTheme.next(theme);\n      this.applyTheme(theme);\n      this.saveThemeToStorage(themeName);\n    }\n  }\n  getCurrentTheme() {\n    return this.currentTheme.value;\n  }\n  getAvailableThemes() {\n    return Object.values(THEMES);\n  }\n  applyTheme(theme) {\n    const root = document.documentElement;\n    // Appliquer les couleurs CSS custom properties\n    Object.entries(theme.colors).forEach(([key, value]) => {\n      root.style.setProperty(`--color-${key}`, value);\n    });\n    // Appliquer les gradients\n    Object.entries(theme.gradients).forEach(([key, value]) => {\n      root.style.setProperty(`--gradient-${key}`, value);\n    });\n    // Ajouter la classe de thème au body\n    document.body.className = document.body.className.replace(/theme-\\w+/g, '');\n    document.body.classList.add(`theme-${theme.name}`);\n  }\n  saveThemeToStorage(themeName) {\n    try {\n      localStorage.setItem('selectedTheme', themeName);\n    } catch (error) {\n      console.warn('Could not save theme to localStorage:', error);\n    }\n  }\n  loadThemeFromStorage() {\n    try {\n      const savedTheme = localStorage.getItem('selectedTheme');\n      if (savedTheme && THEMES[savedTheme]) {\n        this.currentTheme.next(THEMES[savedTheme]);\n      }\n    } catch (error) {\n      console.warn('Could not load theme from localStorage:', error);\n    }\n  }\n  // Méthodes utilitaires pour les composants\n  getPrimaryColor() {\n    return this.currentTheme.value.colors.primary;\n  }\n  getSecondaryColor() {\n    return this.currentTheme.value.colors.secondary;\n  }\n  getAccentColor() {\n    return this.currentTheme.value.colors.accent;\n  }\n  getPrimaryGradient() {\n    return this.currentTheme.value.gradients.primary;\n  }\n  isTheme(themeName) {\n    return this.currentTheme.value.name === themeName;\n  }\n  // Méthode pour basculer entre les thèmes\n  toggleTheme() {\n    const currentTheme = this.currentTheme.value;\n    if (currentTheme.name === 'dark') {\n      this.setTheme('light');\n    } else {\n      this.setTheme('dark');\n    }\n  }\n  // Alias pour la compatibilité\n  toggleDarkMode() {\n    this.toggleTheme();\n  }\n  static {\n    this.ɵfac = function ThemeService_Factory(t) {\n      return new (t || ThemeService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ThemeService,\n      factory: ThemeService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "THEMES", "dark", "name", "displayName", "colors", "primary", "secondary", "accent", "background", "surface", "text", "textSecondary", "border", "success", "warning", "error", "gradients", "neon", "purple", "ocean", "ThemeService", "constructor", "currentTheme", "currentTheme$", "asObservable", "loadThemeFromStorage", "applyTheme", "value", "setTheme", "themeName", "theme", "next", "saveThemeToStorage", "getCurrentTheme", "getAvailableThemes", "Object", "values", "root", "document", "documentElement", "entries", "for<PERSON>ach", "key", "style", "setProperty", "body", "className", "replace", "classList", "add", "localStorage", "setItem", "console", "warn", "savedTheme", "getItem", "getPrimaryColor", "getSecondaryColor", "getAccentColor", "getPrimaryGradient", "isTheme", "toggleTheme", "toggleDarkMode", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\services\\theme.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\n\nexport interface Theme {\n  name: string;\n  displayName: string;\n  colors: {\n    primary: string;\n    secondary: string;\n    accent: string;\n    background: string;\n    surface: string;\n    text: string;\n    textSecondary: string;\n    border: string;\n    success: string;\n    warning: string;\n    error: string;\n  };\n  gradients: {\n    primary: string;\n    secondary: string;\n    accent: string;\n  };\n}\n\nexport const THEMES: { [key: string]: Theme } = {\n  dark: {\n    name: 'dark',\n    displayName: 'Sombre',\n    colors: {\n      primary: '#3b82f6',\n      secondary: '#6366f1',\n      accent: '#8b5cf6',\n      background: '#111827',\n      surface: '#1f2937',\n      text: '#ffffff',\n      textSecondary: '#9ca3af',\n      border: '#374151',\n      success: '#10b981',\n      warning: '#f59e0b',\n      error: '#ef4444',\n    },\n    gradients: {\n      primary: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n      secondary: 'linear-gradient(135deg, #6366f1 0%, #4f46e5 100%)',\n      accent: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n    },\n  },\n  neon: {\n    name: 'neon',\n    displayName: 'Néon',\n    colors: {\n      primary: '#00ffff',\n      secondary: '#ff00ff',\n      accent: '#ffff00',\n      background: '#0a0a0a',\n      surface: '#1a1a1a',\n      text: '#ffffff',\n      textSecondary: '#cccccc',\n      border: '#333333',\n      success: '#00ff00',\n      warning: '#ff8800',\n      error: '#ff0040',\n    },\n    gradients: {\n      primary: 'linear-gradient(135deg, #00ffff 0%, #0080ff 100%)',\n      secondary: 'linear-gradient(135deg, #ff00ff 0%, #8000ff 100%)',\n      accent: 'linear-gradient(135deg, #ffff00 0%, #ff8000 100%)',\n    },\n  },\n  purple: {\n    name: 'purple',\n    displayName: 'Violet',\n    colors: {\n      primary: '#8b5cf6',\n      secondary: '#a855f7',\n      accent: '#c084fc',\n      background: '#1e1b4b',\n      surface: '#312e81',\n      text: '#ffffff',\n      textSecondary: '#c7d2fe',\n      border: '#4c1d95',\n      success: '#22c55e',\n      warning: '#eab308',\n      error: '#ef4444',\n    },\n    gradients: {\n      primary: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n      secondary: 'linear-gradient(135deg, #a855f7 0%, #9333ea 100%)',\n      accent: 'linear-gradient(135deg, #c084fc 0%, #a855f7 100%)',\n    },\n  },\n  ocean: {\n    name: 'ocean',\n    displayName: 'Océan',\n    colors: {\n      primary: '#0ea5e9',\n      secondary: '#06b6d4',\n      accent: '#22d3ee',\n      background: '#0c4a6e',\n      surface: '#075985',\n      text: '#ffffff',\n      textSecondary: '#bae6fd',\n      border: '#0369a1',\n      success: '#059669',\n      warning: '#d97706',\n      error: '#dc2626',\n    },\n    gradients: {\n      primary: 'linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%)',\n      secondary: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',\n      accent: 'linear-gradient(135deg, #22d3ee 0%, #06b6d4 100%)',\n    },\n  },\n};\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class ThemeService {\n  private currentTheme = new BehaviorSubject<Theme>(THEMES['dark']);\n  public currentTheme$ = this.currentTheme.asObservable();\n\n  constructor() {\n    this.loadThemeFromStorage();\n    this.applyTheme(this.currentTheme.value);\n  }\n\n  setTheme(themeName: string): void {\n    const theme = THEMES[themeName];\n    if (theme) {\n      this.currentTheme.next(theme);\n      this.applyTheme(theme);\n      this.saveThemeToStorage(themeName);\n    }\n  }\n\n  getCurrentTheme(): Theme {\n    return this.currentTheme.value;\n  }\n\n  getAvailableThemes(): Theme[] {\n    return Object.values(THEMES);\n  }\n\n  private applyTheme(theme: Theme): void {\n    const root = document.documentElement;\n\n    // Appliquer les couleurs CSS custom properties\n    Object.entries(theme.colors).forEach(([key, value]) => {\n      root.style.setProperty(`--color-${key}`, value);\n    });\n\n    // Appliquer les gradients\n    Object.entries(theme.gradients).forEach(([key, value]) => {\n      root.style.setProperty(`--gradient-${key}`, value);\n    });\n\n    // Ajouter la classe de thème au body\n    document.body.className = document.body.className.replace(/theme-\\w+/g, '');\n    document.body.classList.add(`theme-${theme.name}`);\n  }\n\n  private saveThemeToStorage(themeName: string): void {\n    try {\n      localStorage.setItem('selectedTheme', themeName);\n    } catch (error) {\n      console.warn('Could not save theme to localStorage:', error);\n    }\n  }\n\n  private loadThemeFromStorage(): void {\n    try {\n      const savedTheme = localStorage.getItem('selectedTheme');\n      if (savedTheme && THEMES[savedTheme]) {\n        this.currentTheme.next(THEMES[savedTheme]);\n      }\n    } catch (error) {\n      console.warn('Could not load theme from localStorage:', error);\n    }\n  }\n\n  // Méthodes utilitaires pour les composants\n  getPrimaryColor(): string {\n    return this.currentTheme.value.colors.primary;\n  }\n\n  getSecondaryColor(): string {\n    return this.currentTheme.value.colors.secondary;\n  }\n\n  getAccentColor(): string {\n    return this.currentTheme.value.colors.accent;\n  }\n\n  getPrimaryGradient(): string {\n    return this.currentTheme.value.gradients.primary;\n  }\n\n  isTheme(themeName: string): boolean {\n    return this.currentTheme.value.name === themeName;\n  }\n\n  // Méthode pour basculer entre les thèmes\n  toggleTheme(): void {\n    const currentTheme = this.currentTheme.value;\n    if (currentTheme.name === 'dark') {\n      this.setTheme('light');\n    } else {\n      this.setTheme('dark');\n    }\n  }\n\n  // Alias pour la compatibilité\n  toggleDarkMode(): void {\n    this.toggleTheme();\n  }\n}\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,MAAM;;AAyBtC,OAAO,MAAMC,MAAM,GAA6B;EAC9CC,IAAI,EAAE;IACJC,IAAI,EAAE,MAAM;IACZC,WAAW,EAAE,QAAQ;IACrBC,MAAM,EAAE;MACNC,OAAO,EAAE,SAAS;MAClBC,SAAS,EAAE,SAAS;MACpBC,MAAM,EAAE,SAAS;MACjBC,UAAU,EAAE,SAAS;MACrBC,OAAO,EAAE,SAAS;MAClBC,IAAI,EAAE,SAAS;MACfC,aAAa,EAAE,SAAS;MACxBC,MAAM,EAAE,SAAS;MACjBC,OAAO,EAAE,SAAS;MAClBC,OAAO,EAAE,SAAS;MAClBC,KAAK,EAAE;KACR;IACDC,SAAS,EAAE;MACTX,OAAO,EAAE,mDAAmD;MAC5DC,SAAS,EAAE,mDAAmD;MAC9DC,MAAM,EAAE;;GAEX;EACDU,IAAI,EAAE;IACJf,IAAI,EAAE,MAAM;IACZC,WAAW,EAAE,MAAM;IACnBC,MAAM,EAAE;MACNC,OAAO,EAAE,SAAS;MAClBC,SAAS,EAAE,SAAS;MACpBC,MAAM,EAAE,SAAS;MACjBC,UAAU,EAAE,SAAS;MACrBC,OAAO,EAAE,SAAS;MAClBC,IAAI,EAAE,SAAS;MACfC,aAAa,EAAE,SAAS;MACxBC,MAAM,EAAE,SAAS;MACjBC,OAAO,EAAE,SAAS;MAClBC,OAAO,EAAE,SAAS;MAClBC,KAAK,EAAE;KACR;IACDC,SAAS,EAAE;MACTX,OAAO,EAAE,mDAAmD;MAC5DC,SAAS,EAAE,mDAAmD;MAC9DC,MAAM,EAAE;;GAEX;EACDW,MAAM,EAAE;IACNhB,IAAI,EAAE,QAAQ;IACdC,WAAW,EAAE,QAAQ;IACrBC,MAAM,EAAE;MACNC,OAAO,EAAE,SAAS;MAClBC,SAAS,EAAE,SAAS;MACpBC,MAAM,EAAE,SAAS;MACjBC,UAAU,EAAE,SAAS;MACrBC,OAAO,EAAE,SAAS;MAClBC,IAAI,EAAE,SAAS;MACfC,aAAa,EAAE,SAAS;MACxBC,MAAM,EAAE,SAAS;MACjBC,OAAO,EAAE,SAAS;MAClBC,OAAO,EAAE,SAAS;MAClBC,KAAK,EAAE;KACR;IACDC,SAAS,EAAE;MACTX,OAAO,EAAE,mDAAmD;MAC5DC,SAAS,EAAE,mDAAmD;MAC9DC,MAAM,EAAE;;GAEX;EACDY,KAAK,EAAE;IACLjB,IAAI,EAAE,OAAO;IACbC,WAAW,EAAE,OAAO;IACpBC,MAAM,EAAE;MACNC,OAAO,EAAE,SAAS;MAClBC,SAAS,EAAE,SAAS;MACpBC,MAAM,EAAE,SAAS;MACjBC,UAAU,EAAE,SAAS;MACrBC,OAAO,EAAE,SAAS;MAClBC,IAAI,EAAE,SAAS;MACfC,aAAa,EAAE,SAAS;MACxBC,MAAM,EAAE,SAAS;MACjBC,OAAO,EAAE,SAAS;MAClBC,OAAO,EAAE,SAAS;MAClBC,KAAK,EAAE;KACR;IACDC,SAAS,EAAE;MACTX,OAAO,EAAE,mDAAmD;MAC5DC,SAAS,EAAE,mDAAmD;MAC9DC,MAAM,EAAE;;;CAGb;AAKD,OAAM,MAAOa,YAAY;EAIvBC,YAAA;IAHQ,KAAAC,YAAY,GAAG,IAAIvB,eAAe,CAAQC,MAAM,CAAC,MAAM,CAAC,CAAC;IAC1D,KAAAuB,aAAa,GAAG,IAAI,CAACD,YAAY,CAACE,YAAY,EAAE;IAGrD,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,UAAU,CAAC,IAAI,CAACJ,YAAY,CAACK,KAAK,CAAC;EAC1C;EAEAC,QAAQA,CAACC,SAAiB;IACxB,MAAMC,KAAK,GAAG9B,MAAM,CAAC6B,SAAS,CAAC;IAC/B,IAAIC,KAAK,EAAE;MACT,IAAI,CAACR,YAAY,CAACS,IAAI,CAACD,KAAK,CAAC;MAC7B,IAAI,CAACJ,UAAU,CAACI,KAAK,CAAC;MACtB,IAAI,CAACE,kBAAkB,CAACH,SAAS,CAAC;;EAEtC;EAEAI,eAAeA,CAAA;IACb,OAAO,IAAI,CAACX,YAAY,CAACK,KAAK;EAChC;EAEAO,kBAAkBA,CAAA;IAChB,OAAOC,MAAM,CAACC,MAAM,CAACpC,MAAM,CAAC;EAC9B;EAEQ0B,UAAUA,CAACI,KAAY;IAC7B,MAAMO,IAAI,GAAGC,QAAQ,CAACC,eAAe;IAErC;IACAJ,MAAM,CAACK,OAAO,CAACV,KAAK,CAAC1B,MAAM,CAAC,CAACqC,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEf,KAAK,CAAC,KAAI;MACpDU,IAAI,CAACM,KAAK,CAACC,WAAW,CAAC,WAAWF,GAAG,EAAE,EAAEf,KAAK,CAAC;IACjD,CAAC,CAAC;IAEF;IACAQ,MAAM,CAACK,OAAO,CAACV,KAAK,CAACd,SAAS,CAAC,CAACyB,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEf,KAAK,CAAC,KAAI;MACvDU,IAAI,CAACM,KAAK,CAACC,WAAW,CAAC,cAAcF,GAAG,EAAE,EAAEf,KAAK,CAAC;IACpD,CAAC,CAAC;IAEF;IACAW,QAAQ,CAACO,IAAI,CAACC,SAAS,GAAGR,QAAQ,CAACO,IAAI,CAACC,SAAS,CAACC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;IAC3ET,QAAQ,CAACO,IAAI,CAACG,SAAS,CAACC,GAAG,CAAC,SAASnB,KAAK,CAAC5B,IAAI,EAAE,CAAC;EACpD;EAEQ8B,kBAAkBA,CAACH,SAAiB;IAC1C,IAAI;MACFqB,YAAY,CAACC,OAAO,CAAC,eAAe,EAAEtB,SAAS,CAAC;KACjD,CAAC,OAAOd,KAAK,EAAE;MACdqC,OAAO,CAACC,IAAI,CAAC,uCAAuC,EAAEtC,KAAK,CAAC;;EAEhE;EAEQU,oBAAoBA,CAAA;IAC1B,IAAI;MACF,MAAM6B,UAAU,GAAGJ,YAAY,CAACK,OAAO,CAAC,eAAe,CAAC;MACxD,IAAID,UAAU,IAAItD,MAAM,CAACsD,UAAU,CAAC,EAAE;QACpC,IAAI,CAAChC,YAAY,CAACS,IAAI,CAAC/B,MAAM,CAACsD,UAAU,CAAC,CAAC;;KAE7C,CAAC,OAAOvC,KAAK,EAAE;MACdqC,OAAO,CAACC,IAAI,CAAC,yCAAyC,EAAEtC,KAAK,CAAC;;EAElE;EAEA;EACAyC,eAAeA,CAAA;IACb,OAAO,IAAI,CAAClC,YAAY,CAACK,KAAK,CAACvB,MAAM,CAACC,OAAO;EAC/C;EAEAoD,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACnC,YAAY,CAACK,KAAK,CAACvB,MAAM,CAACE,SAAS;EACjD;EAEAoD,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACpC,YAAY,CAACK,KAAK,CAACvB,MAAM,CAACG,MAAM;EAC9C;EAEAoD,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACrC,YAAY,CAACK,KAAK,CAACX,SAAS,CAACX,OAAO;EAClD;EAEAuD,OAAOA,CAAC/B,SAAiB;IACvB,OAAO,IAAI,CAACP,YAAY,CAACK,KAAK,CAACzB,IAAI,KAAK2B,SAAS;EACnD;EAEA;EACAgC,WAAWA,CAAA;IACT,MAAMvC,YAAY,GAAG,IAAI,CAACA,YAAY,CAACK,KAAK;IAC5C,IAAIL,YAAY,CAACpB,IAAI,KAAK,MAAM,EAAE;MAChC,IAAI,CAAC0B,QAAQ,CAAC,OAAO,CAAC;KACvB,MAAM;MACL,IAAI,CAACA,QAAQ,CAAC,MAAM,CAAC;;EAEzB;EAEA;EACAkC,cAAcA,CAAA;IACZ,IAAI,CAACD,WAAW,EAAE;EACpB;;;uBAjGWzC,YAAY;IAAA;EAAA;;;aAAZA,YAAY;MAAA2C,OAAA,EAAZ3C,YAAY,CAAA4C,IAAA;MAAAC,UAAA,EAFX;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}