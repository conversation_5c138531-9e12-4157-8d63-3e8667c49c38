{"version": 3, "file": "src_app_views_admin_reunions_reunions_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICWEA,6DAAA,EAA8C;IAA9CA,4DAAA,aAA8C;IAC5CA,uDAAA,aAA4F;IAC9FA,0DAAA,EAAM;;;;;;IAGNA,6DAAA,EAAgG;IAAhGA,4DAAA,aAAgG;IAC9FA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAAQ,MAAA,CAAAC,KAAA,MACF;;;;;IAoDMT,4DAAA,aAA2E;IACzEA,oDAAA,GACF;IAAAA,0DAAA,EAAK;;;;IADHA,uDAAA,GACF;IADEA,gEAAA,MAAAW,cAAA,CAAAC,QAAA,QAAAD,cAAA,CAAAE,KAAA,OACF;;;;;IAcJb,4DAAA,cAAuC;IACgBA,oDAAA,YAAK;IAAAA,0DAAA,EAAK;IAC/DA,4DAAA,cAA+B;IAC7BA,4DAAA,EAA8F;IAA9FA,4DAAA,cAA8F;IAC5FA,uDAAA,eAA+J;IAEjKA,0DAAA,EAAM;IACNA,6DAAA,EAA4B;IAA5BA,4DAAA,eAA4B;IAAAA,oDAAA,GAAkB;IAAAA,0DAAA,EAAO;;;;IAAzBA,uDAAA,GAAkB;IAAlBA,+DAAA,CAAAgB,MAAA,CAAAC,OAAA,CAAAC,IAAA,CAAkB;;;;;IAKlDlB,4DAAA,cAA4C;IACWA,oDAAA,kBAAW;IAAAA,0DAAA,EAAK;IACrEA,4DAAA,YAAsG;IACpGA,4DAAA,EAAgF;IAAhFA,4DAAA,cAAgF;IAC9EA,uDAAA,eAA+M;IACjNA,0DAAA,EAAM;IACNA,oDAAA,kCACF;IAAAA,0DAAA,EAAI;;;;IALDA,uDAAA,GAA0B;IAA1BA,wDAAA,SAAAoB,MAAA,CAAAH,OAAA,CAAAI,SAAA,EAAArB,2DAAA,CAA0B;;;;;;;IA/EjCA,6DAAA,EAA2E;IAA3EA,4DAAA,cAA2E;IAIxBA,oDAAA,GAAmB;IAAAA,0DAAA,EAAK;IACrEA,uDAAA,YAA0E;;IAC5EA,0DAAA,EAAM;IACNA,4DAAA,cAA4B;IAClBA,wDAAA,mBAAAwB,8DAAA;MAAAxB,2DAAA,CAAA0B,GAAA;MAAA,MAAAC,MAAA,GAAA3B,2DAAA;MAAA,OAASA,yDAAA,CAAA2B,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAE7B9B,4DAAA,EAAgF;IAAhFA,4DAAA,cAAgF;IAC9EA,uDAAA,gBAAmM;IACrMA,0DAAA,EAAM;IACNA,oDAAA,kBACF;IAAAA,0DAAA,EAAS;IACTA,6DAAA,EAC6G;IAD7GA,4DAAA,kBAC6G;IADrGA,wDAAA,mBAAA+B,+DAAA;MAAA/B,2DAAA,CAAA0B,GAAA;MAAA,MAAAM,MAAA,GAAAhC,2DAAA;MAAA,OAASA,yDAAA,CAAAgC,MAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAE/BjC,4DAAA,EAAgF;IAAhFA,4DAAA,eAAgF;IAC9EA,uDAAA,gBAAyM;IAC3MA,0DAAA,EAAM;IACNA,oDAAA,mBACF;IAAAA,0DAAA,EAAS;IAKbA,6DAAA,EAAkB;IAAlBA,4DAAA,eAAkB;IAEdA,4DAAA,EAA8F;IAA9FA,4DAAA,eAA8F;IAC5FA,uDAAA,gBAAmK;IACrKA,0DAAA,EAAM;IACNA,6DAAA,EAA4B;IAA5BA,4DAAA,gBAA4B;IAC1BA,oDAAA,IACF;;IAAAA,0DAAA,EAAO;IAKXA,4DAAA,eAAkB;IACqCA,oDAAA,sBAAS;IAAAA,0DAAA,EAAK;IACnEA,4DAAA,eAA+B;IACDA,oDAAA,IAAgE;IAAAA,0DAAA,EAAO;IAKvGA,4DAAA,eAAkB;IACqCA,oDAAA,qBAAa;IAAAA,0DAAA,EAAK;IACvEA,4DAAA,cAA2B;IACzBA,wDAAA,KAAAmC,2CAAA,iBAEK;IACPnC,0DAAA,EAAK;IAIPA,4DAAA,eAAkB;IACqCA,oDAAA,iBAAS;IAAAA,0DAAA,EAAK;IACnEA,4DAAA,eAA2B;IACtBA,oDAAA,IAA6B;IAAAA,0DAAA,EAAI;IACpCA,4DAAA,SAAG;IAAAA,oDAAA,IAA+G;;;IAAAA,0DAAA,EAAI;IAK1HA,wDAAA,KAAAoC,4CAAA,kBASM;IAGNpC,wDAAA,KAAAqC,4CAAA,kBAQM;IACRrC,0DAAA,EAAM;;;;IAlF6CA,uDAAA,GAAmB;IAAnBA,+DAAA,CAAAsC,MAAA,CAAArB,OAAA,CAAAsB,KAAA,CAAmB;IAChDvC,uDAAA,GAAqD;IAArDA,wDAAA,cAAAA,yDAAA,QAAAsC,MAAA,CAAArB,OAAA,CAAAwB,WAAA,GAAAzC,4DAAA,CAAqD;IA2BnEA,uDAAA,IACF;IADEA,gEAAA,MAAAA,yDAAA,SAAAsC,MAAA,CAAArB,OAAA,CAAA4B,IAAA,qBAAAP,MAAA,CAAArB,OAAA,CAAA6B,UAAA,SAAAR,MAAA,CAAArB,OAAA,CAAA8B,QAAA,MACF;IAQ4B/C,uDAAA,GAAgE;IAAhEA,gEAAA,KAAAsC,MAAA,CAAArB,OAAA,CAAA+B,QAAA,kBAAAV,MAAA,CAAArB,OAAA,CAAA+B,QAAA,CAAApC,QAAA,QAAA0B,MAAA,CAAArB,OAAA,CAAA+B,QAAA,kBAAAV,MAAA,CAAArB,OAAA,CAAA+B,QAAA,CAAAnC,KAAA,MAAgE;IAQhEb,uDAAA,GAAuB;IAAvBA,wDAAA,YAAAsC,MAAA,CAAArB,OAAA,CAAAgC,YAAA,CAAuB;IAUhDjD,uDAAA,GAA6B;IAA7BA,+DAAA,CAAAsC,MAAA,CAAArB,OAAA,CAAAiC,QAAA,kBAAAZ,MAAA,CAAArB,OAAA,CAAAiC,QAAA,CAAAX,KAAA,CAA6B;IAC7BvC,uDAAA,GAA+G;IAA/GA,gEAAA,QAAAA,yDAAA,SAAAsC,MAAA,CAAArB,OAAA,CAAAiC,QAAA,kBAAAZ,MAAA,CAAArB,OAAA,CAAAiC,QAAA,CAAAC,SAAA,yBAAAnD,yDAAA,SAAAsC,MAAA,CAAArB,OAAA,CAAAiC,QAAA,kBAAAZ,MAAA,CAAArB,OAAA,CAAAiC,QAAA,CAAAE,OAAA,oBAA+G;IAKhHpD,uDAAA,GAAkB;IAAlBA,wDAAA,SAAAsC,MAAA,CAAArB,OAAA,CAAAC,IAAA,CAAkB;IAYlBlB,uDAAA,GAAuB;IAAvBA,wDAAA,SAAAsC,MAAA,CAAArB,OAAA,CAAAI,SAAA,CAAuB;;;ADvF3B,MAAOgC,sBAAsB;EAKjCC,YACUC,KAAqB,EACtBC,MAAc,EACbC,cAA8B,EAC9BC,SAAuB,EACvBC,YAA0B;IAJ1B,KAAAJ,KAAK,GAALA,KAAK;IACN,KAAAC,MAAM,GAANA,MAAM;IACL,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,YAAY,GAAZA,YAAY;IATtB,KAAA1C,OAAO,GAAQ,IAAI;IACnB,KAAA2C,OAAO,GAAG,IAAI;IACd,KAAAnD,KAAK,GAAkB,IAAI;EAQxB;EAEHoD,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEAA,kBAAkBA,CAAA;IAChB,MAAMC,EAAE,GAAG,IAAI,CAACR,KAAK,CAACS,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACjD,IAAI,CAACH,EAAE,EAAE;MACP,IAAI,CAACtD,KAAK,GAAG,0BAA0B;MACvC,IAAI,CAACmD,OAAO,GAAG,KAAK;MACpB;;IAGF,IAAI,CAACH,cAAc,CAACU,cAAc,CAACJ,EAAE,CAAC,CAACK,SAAS,CAAC;MAC/CC,IAAI,EAAGC,QAAa,IAAI;QACtB,IAAI,CAACrD,OAAO,GAAGqD,QAAQ,CAACrD,OAAO;QAC/B,IAAI,CAAC2C,OAAO,GAAG,KAAK;MACtB,CAAC;MACDnD,KAAK,EAAG8D,GAAQ,IAAI;QAClB,IAAI,CAAC9D,KAAK,GAAG8D,GAAG,CAAC9D,KAAK,EAAE+D,OAAO,IAAI,2BAA2B;QAC9D,IAAI,CAACZ,OAAO,GAAG,KAAK;QACpBa,OAAO,CAAChE,KAAK,CAAC,SAAS,EAAE8D,GAAG,CAAC;MAC/B;KACD,CAAC;EACJ;EAEAG,iBAAiBA,CAACjC,WAAmB;IACnC,IAAI,CAACA,WAAW,EAAE,OAAO,IAAI,CAACiB,SAAS,CAACiB,uBAAuB,CAAC,EAAE,CAAC;IAEnE;IACA,MAAMC,aAAa,GAAGnC,WAAW,CAACoC,OAAO,CACvC,4BAA4B,EAC5B,wEAAwE,CACzE;IAED;IACA,OAAO,IAAI,CAACnB,SAAS,CAACiB,uBAAuB,CAACC,aAAa,CAAC;EAC9D;EAEA9C,WAAWA,CAAA;IACT,IAAI,IAAI,CAACb,OAAO,EAAE;MAChB,IAAI,CAACuC,MAAM,CAACsB,QAAQ,CAAC,CAAC,gBAAgB,EAAE,IAAI,CAAC7D,OAAO,CAAC8D,GAAG,CAAC,CAAC;;EAE9D;EAEA;;;EAGA9C,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAAChB,OAAO,EAAE;IAEnB,IAAI+D,OAAO,CAAC,mFAAmF,CAAC,EAAE;MAChG,IAAI,CAACvB,cAAc,CAACxB,aAAa,CAAC,IAAI,CAAChB,OAAO,CAAC8D,GAAG,CAAC,CAACX,SAAS,CAAC;QAC5DC,IAAI,EAAGC,QAAQ,IAAI;UACjBG,OAAO,CAACQ,GAAG,CAAC,gCAAgC,EAAEX,QAAQ,CAAC;UAEvD;UACA,IAAI,CAACX,YAAY,CAACuB,OAAO,CACvB,mBAAmB,EACnB,wCAAwC,CACzC;UAED;UACA,IAAI,CAAC1B,MAAM,CAACsB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;QACrC,CAAC;QACDrE,KAAK,EAAGA,KAAK,IAAI;UACfgE,OAAO,CAAChE,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;UAEtD;UACA,IAAIA,KAAK,CAAC0E,MAAM,KAAK,GAAG,EAAE;YACxB,IAAI,CAACxB,YAAY,CAACyB,YAAY,CAAC,yBAAyB,EAAE3E,KAAK,CAAC0E,MAAM,CAAC;WACxE,MAAM,IAAI1E,KAAK,CAAC0E,MAAM,KAAK,GAAG,EAAE;YAC/B,IAAI,CAACxB,YAAY,CAAClD,KAAK,CACrB,cAAc,EACd,qDAAqD,CACtD;WACF,MAAM;YACL,MAAM4E,YAAY,GAAG5E,KAAK,CAACA,KAAK,EAAE+D,OAAO,IAAI,6CAA6C;YAC1F,IAAI,CAACb,YAAY,CAAClD,KAAK,CACrB,uBAAuB,EACvB4E,YAAY,EACZ,IAAI,CACL;;QAEL;OACD,CAAC;;EAEN;;;uBAnGWhC,sBAAsB,EAAArD,+DAAA,CAAAuF,2DAAA,GAAAvF,+DAAA,CAAAuF,mDAAA,GAAAvF,+DAAA,CAAA0F,yEAAA,GAAA1F,+DAAA,CAAA4F,mEAAA,GAAA5F,+DAAA,CAAA8F,qEAAA;IAAA;EAAA;;;YAAtBzC,sBAAsB;MAAA2C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXnCtG,4DAAA,aAAyC;UAE/BA,wDAAA,mBAAAwG,wDAAA;YAAA,OAASD,GAAA,CAAA/C,MAAA,CAAAsB,QAAA,EAAiB,WAAW,EAAE;UAAA,EAAC;UAE9C9E,4DAAA,EAAqG;UAArGA,4DAAA,aAAqG;UACnGA,uDAAA,cAA0L;UAC5LA,0DAAA,EAAM;UACNA,oDAAA,iCACF;UAAAA,0DAAA,EAAS;UAGTA,wDAAA,IAAAyG,qCAAA,iBAEM;UAGNzG,wDAAA,IAAA0G,qCAAA,iBAEM;UAGN1G,wDAAA,IAAA2G,qCAAA,mBAsFM;UACR3G,0DAAA,EAAM;;;UAjGEA,uDAAA,GAAa;UAAbA,wDAAA,SAAAuG,GAAA,CAAA3C,OAAA,CAAa;UAKb5D,uDAAA,GAAW;UAAXA,wDAAA,SAAAuG,GAAA,CAAA9F,KAAA,CAAW;UAKXT,uDAAA,GAAyB;UAAzBA,wDAAA,UAAAuG,GAAA,CAAA3C,OAAA,IAAA2C,GAAA,CAAAtF,OAAA,CAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpBmC;;;;;;;;;;;;;ICUhEjB,4DAAA,cAAgG;IAC9FA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAA6G,MAAA,CAAApG,KAAA,CAAA+D,OAAA,mCACF;;;;;IAWIxE,4DAAA,cACyD;IACvDA,uDAAA,YAA8C;IAC9CA,oDAAA,iCACF;IAAAA,0DAAA,EAAM;;;;;IA4BFA,4DAAA,cACyD;IACvDA,uDAAA,YAA8C;IAC9CA,oDAAA,gCACF;IAAAA,0DAAA,EAAM;;;;;IAUNA,4DAAA,cACyD;IACvDA,uDAAA,YAA8C;IAC9CA,oDAAA,8CACF;IAAAA,0DAAA,EAAM;;;;;IAUNA,4DAAA,cACyD;IACvDA,uDAAA,YAA8C;IAC9CA,oDAAA,uCACF;IAAAA,0DAAA,EAAM;;;;;IAoDRA,4DAAA,cAA2I;IACzIA,uDAAA,YAAuC;IACvCA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAA8G,OAAA,CAAAC,sBAAA,CAAAtE,WAAA,MACF;;;;;IAhBFzC,4DAAA,cACsI;IAGhIA,uDAAA,YAAwD;IACxDA,oDAAA,GACF;IAAAA,0DAAA,EAAO;IACPA,4DAAA,eAAsG;IACpGA,uDAAA,YAAiC;IACjCA,oDAAA,GAEF;;;IAAAA,0DAAA,EAAO;IAETA,wDAAA,KAAAgH,2CAAA,kBAGM;IACRhH,0DAAA,EAAM;;;;IAZAA,uDAAA,GACF;IADEA,gEAAA,MAAAoB,MAAA,CAAA2F,sBAAA,CAAAxE,KAAA,MACF;IAGEvC,uDAAA,GAEF;IAFEA,gEAAA,MAAAA,yDAAA,OAAAoB,MAAA,CAAA2F,sBAAA,CAAA5D,SAAA,wBAAAnD,yDAAA,OAAAoB,MAAA,CAAA2F,sBAAA,CAAA3D,OAAA,qBAEF;IAEIpD,uDAAA,GAAwC;IAAxCA,wDAAA,SAAAoB,MAAA,CAAA2F,sBAAA,CAAAtE,WAAA,CAAwC;;;;;IAS9CzC,4DAAA,iBAAkE;IAAAA,oDAAA,GAAoB;IAAAA,0DAAA,EAAS;;;;IAApDA,wDAAA,UAAAiH,YAAA,CAAAlC,GAAA,CAAsB;IAAC/E,uDAAA,GAAoB;IAApBA,+DAAA,CAAAiH,YAAA,CAAA1E,KAAA,CAAoB;;;;;IAsBpFvC,4DAAA,iBAAmE;IACjEA,uDAAA,YAAgC;IAAAA,oDAAA,GAClC;IAAAA,0DAAA,EAAS;;;;IAF0BA,wDAAA,UAAAkH,QAAA,CAAAnC,GAAA,CAAkB;IACnB/E,uDAAA,GAClC;IADkCA,gEAAA,KAAAkH,QAAA,CAAAtG,QAAA,MAClC;;;;;IAHFZ,qEAAA,GAA4B;IAC1BA,wDAAA,IAAAoH,sDAAA,qBAES;IACXpH,mEAAA,EAAe;;;;IAHYA,uDAAA,GAAQ;IAARA,wDAAA,YAAA2B,MAAA,CAAA2F,KAAA,CAAQ;;;;;IAqBrCtH,uDAAA,YAAsD;;;;;IACtDA,uDAAA,YAAgE;;;ADhLlE,MAAOuH,oBAAoB;EAU/BjE,YACUkE,EAAe,EACfjE,KAAqB,EACrBC,MAAc,EACdC,cAA8B,EAC9BgE,WAAwB,EACxBC,eAAgC,EAChC/D,YAA0B,EAC1BgE,WAA4B,EAC5BC,WAAwB;IARxB,KAAAJ,EAAE,GAAFA,EAAE;IACF,KAAAjE,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAgE,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAA/D,YAAY,GAAZA,YAAY;IACZ,KAAAgE,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IAhBrB,KAAAnH,KAAK,GAAQ,IAAI;IACjB,KAAAoH,YAAY,GAAG,KAAK;IACpB,KAAAP,KAAK,GAAW,EAAE;IAClB,KAAAQ,SAAS,GAAe,EAAE;IAC1B,KAAAf,sBAAsB,GAAoB,IAAI;IAC9C,KAAAgB,OAAO,GAAG,KAAK;EAYZ;EAEHlE,QAAQA,CAAA;IACN,IAAI,CAACmE,SAAS,GAAG,IAAI,CAACzE,KAAK,CAACS,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAE;IACxD,IAAI,CAAC+D,aAAa,EAAE;IACpB,IAAI,CAACC,QAAQ,EAAE;IACf,IAAI,CAACC,UAAU,EAAE;IACjB,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAJ,aAAaA,CAAA;IACX,IAAI,CAACF,OAAO,GAAG,IAAI,CAACH,WAAW,CAACG,OAAO,EAAE;IACzCtD,OAAO,CAACQ,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC8C,OAAO,CAAC;EACpD;EAEAG,QAAQA,CAAA;IACN,IAAI,CAACI,WAAW,GAAG,IAAI,CAACd,EAAE,CAACe,KAAK,CAAC;MAC/BhG,KAAK,EAAE,CAAC,EAAE,EAAEqE,sDAAU,CAAC4B,QAAQ,CAAC;MAChC/F,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBI,IAAI,EAAE,CAAC,EAAE,EAAE+D,sDAAU,CAAC4B,QAAQ,CAAC;MAC/B1F,UAAU,EAAE,CAAC,EAAE,EAAE8D,sDAAU,CAAC4B,QAAQ,CAAC;MACrCzF,QAAQ,EAAE,CAAC,EAAE,EAAE6D,sDAAU,CAAC4B,QAAQ,CAAC;MACnCtH,IAAI,EAAE,CAAC,EAAE,CAAC;MACVG,SAAS,EAAE,CAAC,EAAE,CAAC;MACf6B,QAAQ,EAAE,CAAC,EAAE,EAAE0D,sDAAU,CAAC4B,QAAQ,CAAC;MACnCvF,YAAY,EAAE,CAAC,EAAE;KAClB,CAAC;EACJ;EAEAkF,UAAUA,CAAA;IACR,IAAI,CAACV,WAAW,CAACgB,WAAW,EAAE,CAACrE,SAAS,CAAEkD,KAAS,IAAI;MACrD,IAAI,CAACA,KAAK,GAAGA,KAAK;IACpB,CAAC,CAAC;EACJ;EAEAc,cAAcA,CAAA;IACZ,MAAMM,MAAM,GAAG,IAAI,CAACf,WAAW,CAACgB,gBAAgB,EAAE;IAClD,IAAI,CAACD,MAAM,EAAE;IAEb;IACA,MAAME,mBAAmB,GAAG,IAAI,CAACb,OAAO,GACpC,IAAI,CAACL,eAAe,CAACmB,oBAAoB,EAAE,GAC3C,IAAI,CAACnB,eAAe,CAACoB,kBAAkB,CAACJ,MAAM,CAAC;IAEnDE,mBAAmB,CAACxE,SAAS,CAAC;MAC5BC,IAAI,EAAGC,QAAa,IAAI;QACtB;QACA,IAAI,IAAI,CAACyD,OAAO,EAAE;UAChB,IAAI,CAACD,SAAS,GAAGxD,QAAQ,CAACyE,IAAI,IAAI,EAAE;UACpCtE,OAAO,CAACQ,GAAG,CAAC,0CAA0C,EAAE,IAAI,CAAC6C,SAAS,CAAC;SACxE,MAAM;UACL,IAAI,CAACA,SAAS,GAAGxD,QAAQ,CAACwD,SAAS,IAAI,EAAE;UACzCrD,OAAO,CAACQ,GAAG,CAAC,qCAAqC,EAAE,IAAI,CAAC6C,SAAS,CAAC;;MAEtE,CAAC;MACDrH,KAAK,EAAG8D,GAAG,IAAI;QACbE,OAAO,CAAChE,KAAK,CAAC,gCAAgC,EAAE8D,GAAG,CAAC;QACpD,IAAI,CAACZ,YAAY,CAAClD,KAAK,CACrB,QAAQ,EACR,uCAAuC,CACxC;MACH;KACD,CAAC;EACJ;EAEA4H,WAAWA,CAAA;IACT,IAAI,CAAC5E,cAAc,CAACU,cAAc,CAAC,IAAI,CAAC6D,SAAS,CAAC,CAAC5D,SAAS,CAAC;MAC3DC,IAAI,EAAGpD,OAAY,IAAI;QACrB;QACA,IAAI,CAAC8F,sBAAsB,GAAG9F,OAAO,CAACA,OAAO,CAACiC,QAAQ;QAEtD,IAAI,CAACoF,WAAW,CAACU,UAAU,CAAC;UAC1BzG,KAAK,EAAEtB,OAAO,CAACA,OAAO,CAACsB,KAAK;UAC5BE,WAAW,EAAExB,OAAO,CAACA,OAAO,CAACwB,WAAW;UACxCI,IAAI,EAAE5B,OAAO,CAACA,OAAO,CAAC4B,IAAI,EAAEoG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UACzCnG,UAAU,EAAE7B,OAAO,CAACA,OAAO,CAAC6B,UAAU;UACtCC,QAAQ,EAAE9B,OAAO,CAACA,OAAO,CAAC8B,QAAQ;UAClC7B,IAAI,EAAED,OAAO,CAACA,OAAO,CAACC,IAAI;UAC1BG,SAAS,EAAEJ,OAAO,CAACA,OAAO,CAACI,SAAS;UACpC6B,QAAQ,EAAEjC,OAAO,CAACA,OAAO,CAACiC,QAAQ,EAAEa,EAAE,IAAI9C,OAAO,CAACA,OAAO,CAACiC,QAAQ,EAAE6B,GAAG;UACvE9B,YAAY,EAAEhC,OAAO,CAACA,OAAO,CAACgC,YAAY,EAAEiG,GAAG,CAAEC,CAAK,IAAKA,CAAC,CAACpE,GAAG;SACjE,CAAC;QAEF;QACA,IAAI,CAACuD,WAAW,CAACpE,GAAG,CAAC,UAAU,CAAC,EAAEkF,OAAO,EAAE;QAE3C3E,OAAO,CAACQ,GAAG,CAAC,qBAAqB,EAAEhE,OAAO,CAACA,OAAO,CAAC;QACnDwD,OAAO,CAACQ,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAC8B,sBAAsB,CAAC;MACjE,CAAC;MACDtG,KAAK,EAAG8D,GAAG,IAAI;QACbE,OAAO,CAAChE,KAAK,CAAC,0CAA0C,EAAE8D,GAAG,CAAC;QAC9D,IAAIA,GAAG,CAACY,MAAM,KAAK,GAAG,EAAE;UACtB,IAAI,CAACxB,YAAY,CAACyB,YAAY,CAAC,yBAAyB,EAAEb,GAAG,CAACY,MAAM,CAAC;SACtE,MAAM,IAAIZ,GAAG,CAACY,MAAM,KAAK,GAAG,EAAE;UAC7B,IAAI,CAACxB,YAAY,CAAClD,KAAK,CACrB,qBAAqB,EACrB,sDAAsD,CACvD;SACF,MAAM;UACL,MAAM4E,YAAY,GAAGd,GAAG,CAAC9D,KAAK,EAAE+D,OAAO,IAAI,yCAAyC;UACpF,IAAI,CAACb,YAAY,CAAClD,KAAK,CACrB,sBAAsB,EACtB4E,YAAY,CACb;;MAEL;KACD,CAAC;EACJ;EAEAgE,QAAQA,CAAA;IACN,IAAI,IAAI,CAACf,WAAW,CAACgB,OAAO,EAAE;MAC5B,IAAI,CAAC3F,YAAY,CAAC4F,OAAO,CACvB,qBAAqB,EACrB,gEAAgE,CACjE;MACD;;IAGF;IACA,IAAI,CAAC,IAAI,CAACC,2BAA2B,EAAE,EAAE;MACvC;;IAGF,IAAI,CAAC3B,YAAY,GAAG,IAAI;IACxB,MAAM5G,OAAO,GAAQ,IAAI,CAACqH,WAAW,CAACmB,KAAK;IAE3C;IACA,IAAI,IAAI,CAAC1C,sBAAsB,EAAE;MAC/B9F,OAAO,CAACiC,QAAQ,GAAG,IAAI,CAAC6D,sBAAsB,CAAChC,GAAG,IAAI,IAAI,CAACgC,sBAAsB,CAAChD,EAAE;;IAGtFU,OAAO,CAACQ,GAAG,CAAC,2CAA2C,EAAEhE,OAAO,CAAC;IAEjE,IAAI,CAACwC,cAAc,CAACiG,aAAa,CAAC,IAAI,CAAC1B,SAAS,EAAE/G,OAAO,CAAC,CAACmD,SAAS,CAAC;MACnEC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACwD,YAAY,GAAG,KAAK;QACzB,IAAI,CAAClE,YAAY,CAACuB,OAAO,CACvB,qBAAqB,EACrB,uCAAuC,CACxC;QACD,IAAI,CAAC1B,MAAM,CAACsB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;MACrC,CAAC;MACDrE,KAAK,EAAG8D,GAAG,IAAI;QACb,IAAI,CAACsD,YAAY,GAAG,KAAK;QACzBpD,OAAO,CAAChE,KAAK,CAAC,8CAA8C,EAAE8D,GAAG,CAAC;QAElE,IAAIA,GAAG,CAACY,MAAM,KAAK,GAAG,EAAE;UACtB,IAAI,CAACxB,YAAY,CAACyB,YAAY,CAAC,wBAAwB,EAAEb,GAAG,CAACY,MAAM,CAAC;SACrE,MAAM,IAAIZ,GAAG,CAACY,MAAM,KAAK,GAAG,EAAE;UAC7B,IAAI,CAACxB,YAAY,CAAClD,KAAK,CACrB,cAAc,EACd,sDAAsD,CACvD;SACF,MAAM;UACL,MAAM4E,YAAY,GAAGd,GAAG,CAAC9D,KAAK,EAAE+D,OAAO,IAAI,6CAA6C;UACxF,IAAI,CAACb,YAAY,CAAClD,KAAK,CACrB,uBAAuB,EACvB4E,YAAY,EACZ,IAAI,CACL;;MAEL;KACD,CAAC;EACJ;EAEAsE,SAASA,CAAA;IACP,IAAI,CAACnG,MAAM,CAACsB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;EACrC;EAEA;;;EAGA0E,2BAA2BA,CAAA;IACzB,MAAMI,SAAS,GAAG,IAAI,CAACtB,WAAW,CAACmB,KAAK;IACxC,MAAMI,WAAW,GAAGD,SAAS,CAAC/G,IAAI;IAClC,MAAMiH,UAAU,GAAGF,SAAS,CAAC1G,QAAQ;IAErC,IAAI,CAAC2G,WAAW,IAAI,CAACC,UAAU,EAAE;MAC/B,OAAO,IAAI,CAAC,CAAC;;IAGf;IACA,MAAMC,gBAAgB,GAAG,IAAI,CAACjC,SAAS,CAACkC,IAAI,CAACb,CAAC,IAAIA,CAAC,CAACpE,GAAG,KAAK+E,UAAU,CAAC;IACvE,IAAI,CAACC,gBAAgB,EAAE;MACrBtF,OAAO,CAACwF,IAAI,CAAC,0FAA0F,CAAC;MACxG;MACA;MACA,IAAI,CAACvC,eAAe,CAACwC,eAAe,CAACJ,UAAU,CAAC,CAAC1F,SAAS,CAAC;QACzDC,IAAI,EAAGC,QAAa,IAAI;UACtB,MAAMpB,QAAQ,GAAGoB,QAAQ,CAACpB,QAAQ;UAClC,IAAIA,QAAQ,EAAE;YACZ;YACA,IAAI,CAAC4E,SAAS,CAACqC,IAAI,CAACjH,QAAQ,CAAC;YAC7BuB,OAAO,CAACQ,GAAG,CAAC,kDAAkD,EAAE/B,QAAQ,CAAC;;QAE7E,CAAC;QACDzC,KAAK,EAAG8D,GAAG,IAAI;UACbE,OAAO,CAAChE,KAAK,CAAC,+CAA+C,EAAE8D,GAAG,CAAC;UACnE,IAAI,CAACZ,YAAY,CAAClD,KAAK,CACrB,sBAAsB,EACtB,0FAA0F,CAC3F;QACH;OACD,CAAC;MACF;MACA,OAAO,IAAI;;IAGb;IACA,MAAM2J,cAAc,GAAG,IAAIC,IAAI,CAACR,WAAW,CAAC;IAC5C,MAAMS,iBAAiB,GAAG,IAAID,IAAI,CAACN,gBAAgB,CAAC5G,SAAS,CAAC;IAC9D,MAAMoH,eAAe,GAAG,IAAIF,IAAI,CAACN,gBAAgB,CAAC3G,OAAO,CAAC;IAE1D;IACAgH,cAAc,CAACI,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACnCF,iBAAiB,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtCD,eAAe,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAEpC,IAAIJ,cAAc,GAAGE,iBAAiB,IAAIF,cAAc,GAAGG,eAAe,EAAE;MAC1E,IAAI,CAAC5G,YAAY,CAAClD,KAAK,CACrB,eAAe,EACf,qDAAqD6J,iBAAiB,CAACG,kBAAkB,CAAC,OAAO,CAAC,UAAUF,eAAe,CAACE,kBAAkB,CAAC,OAAO,CAAC,0BAA0BV,gBAAgB,CAACxH,KAAK,IAAI,EAC3M,KAAK,CACN;MACD,OAAO,KAAK;;IAGd,OAAO,IAAI;EACb;;;uBAzPWgF,oBAAoB,EAAAvH,+DAAA,CAAAuF,uDAAA,GAAAvF,+DAAA,CAAA0F,2DAAA,GAAA1F,+DAAA,CAAA0F,mDAAA,GAAA1F,+DAAA,CAAA4F,yEAAA,GAAA5F,+DAAA,CAAA8F,mEAAA,GAAA9F,+DAAA,CAAA4K,2EAAA,GAAA5K,+DAAA,CAAA8K,qEAAA,GAAA9K,+DAAA,CAAA+K,2EAAA,GAAA/K,+DAAA,CAAAiL,mEAAA;IAAA;EAAA;;;YAApB1D,oBAAoB;MAAAvB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA+E,8BAAA7E,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjBjCtG,4DAAA,aAAmD;UAI7CA,uDAAA,WAAgD;UAChDA,oDAAA,iCACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,WAAgC;UAAAA,oDAAA,sDAAqC;UAAAA,0DAAA,EAAI;UAG3EA,4DAAA,cAA+G;UAA/EA,wDAAA,sBAAAoL,uDAAA;YAAA,OAAY7E,GAAA,CAAA8C,QAAA,EAAU;UAAA,EAAC;UACrDrJ,wDAAA,IAAAqL,mCAAA,iBAEM;UAENrL,4DAAA,aAAoC;UAI9BA,uDAAA,aAA+C;UAC/CA,oDAAA,iBACF;UAAAA,0DAAA,EAAQ;UACRA,uDAAA,iBACkL;UAClLA,wDAAA,KAAAsL,oCAAA,kBAIM;UACRtL,0DAAA,EAAM;UAGNA,4DAAA,cAAsB;UAElBA,uDAAA,aAAsD;UACtDA,oDAAA,qBACF;UAAAA,0DAAA,EAAQ;UACRA,uDAAA,oBAE6D;UAC/DA,0DAAA,EAAM;UAGNA,4DAAA,eAA4F;UAExFA,uDAAA,aAAwD;UACxDA,oDAAA,uBACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,eAAmD;UAG7CA,uDAAA,aAAkD;UAClDA,oDAAA,gBACF;UAAAA,0DAAA,EAAQ;UACRA,uDAAA,iBAC4K;UAC5KA,wDAAA,KAAAuL,oCAAA,kBAIM;UACRvL,0DAAA,EAAM;UAENA,4DAAA,WAAK;UAEDA,uDAAA,aAA+C;UAC/CA,oDAAA,+BACF;UAAAA,0DAAA,EAAQ;UACRA,uDAAA,iBAC+K;UAC/KA,wDAAA,KAAAwL,oCAAA,kBAIM;UACRxL,0DAAA,EAAM;UAENA,4DAAA,WAAK;UAEDA,uDAAA,aAA6C;UAC7CA,oDAAA,wBACF;UAAAA,0DAAA,EAAQ;UACRA,uDAAA,iBACyK;UACzKA,wDAAA,KAAAyL,oCAAA,kBAIM;UACRzL,0DAAA,EAAM;UAKVA,4DAAA,eAAkG;UAE9FA,uDAAA,aAA0D;UAC1DA,oDAAA,sBACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,eAAmD;UAG7CA,uDAAA,aAA0D;UAC1DA,oDAAA,sBACF;UAAAA,0DAAA,EAAQ;UACRA,uDAAA,iBAEyD;UAC3DA,0DAAA,EAAM;UAENA,4DAAA,WAAK;UAEDA,uDAAA,aAA+C;UAC/CA,oDAAA,oBACF;UAAAA,0DAAA,EAAQ;UACRA,uDAAA,iBAEiD;UACnDA,0DAAA,EAAM;UAKVA,4DAAA,WAAK;UACmEA,oDAAA,kBAAU;UAAAA,0DAAA,EAAQ;UAGxFA,wDAAA,KAAA0L,oCAAA,oBAiBM;UAGN1L,4DAAA,kBAAgE;UAC7CA,oDAAA,qCAAwB;UAAAA,0DAAA,EAAS;UAClDA,wDAAA,KAAA2L,uCAAA,qBAA+F;UACjG3L,0DAAA,EAAS;UAETA,4DAAA,eAAiH;UAC/GA,uDAAA,aAAgD;UAChDA,4DAAA,gBAA0B;UAAAA,oDAAA,gGAAoE;UAAAA,0DAAA,EAAO;UAKzGA,4DAAA,eAAkG;UAE9FA,uDAAA,aAAkD;UAClDA,oDAAA,sBACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,iBAA+D;UAC7DA,uDAAA,aAAyD;UACzDA,oDAAA,4CACF;UAAAA,0DAAA,EAAQ;UACRA,4DAAA,kBAC4M;UAC1MA,wDAAA,KAAA4L,6CAAA,2BAIe;UACjB5L,0DAAA,EAAS;UACTA,4DAAA,aAAyC;UACvCA,uDAAA,aAAuC;UACvCA,oDAAA,+EACF;UAAAA,0DAAA,EAAI;UAKRA,4DAAA,eAAgG;UACxEA,wDAAA,mBAAA6L,uDAAA;YAAA,OAAStF,GAAA,CAAAoD,SAAA,EAAW;UAAA,EAAC;UAEzC3J,uDAAA,aAAiC;UACjCA,oDAAA,iBACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBACqQ;UACnQA,wDAAA,KAAA8L,kCAAA,gBAAsD;UACtD9L,wDAAA,KAAA+L,kCAAA,gBAAgE;UAChE/L,oDAAA,IACF;UAAAA,0DAAA,EAAS;;;;;;;UAzLPA,uDAAA,GAAyB;UAAzBA,wDAAA,cAAAuG,GAAA,CAAA+B,WAAA,CAAyB;UACvBtI,uDAAA,GAAW;UAAXA,wDAAA,SAAAuG,GAAA,CAAA9F,KAAA,CAAW;UAaPT,uDAAA,GAA4E;UAA5EA,wDAAA,WAAAgM,OAAA,GAAAzF,GAAA,CAAA+B,WAAA,CAAApE,GAAA,4BAAA8H,OAAA,CAAA1C,OAAA,OAAA0C,OAAA,GAAAzF,GAAA,CAAA+B,WAAA,CAAApE,GAAA,4BAAA8H,OAAA,CAAAC,OAAA,EAA4E;UAgCxEjM,uDAAA,IAA0E;UAA1EA,wDAAA,WAAAkM,OAAA,GAAA3F,GAAA,CAAA+B,WAAA,CAAApE,GAAA,2BAAAgI,OAAA,CAAA5C,OAAA,OAAA4C,OAAA,GAAA3F,GAAA,CAAA+B,WAAA,CAAApE,GAAA,2BAAAgI,OAAA,CAAAD,OAAA,EAA0E;UAc1EjM,uDAAA,GAAsF;UAAtFA,wDAAA,WAAAmM,OAAA,GAAA5F,GAAA,CAAA+B,WAAA,CAAApE,GAAA,iCAAAiI,OAAA,CAAA7C,OAAA,OAAA6C,OAAA,GAAA5F,GAAA,CAAA+B,WAAA,CAAApE,GAAA,iCAAAiI,OAAA,CAAAF,OAAA,EAAsF;UActFjM,uDAAA,GAAkF;UAAlFA,wDAAA,WAAAoM,OAAA,GAAA7F,GAAA,CAAA+B,WAAA,CAAApE,GAAA,+BAAAkI,OAAA,CAAA9C,OAAA,OAAA8C,OAAA,GAAA7F,GAAA,CAAA+B,WAAA,CAAApE,GAAA,+BAAAkI,OAAA,CAAAH,OAAA,EAAkF;UA2CtFjM,uDAAA,IAA4B;UAA5BA,wDAAA,SAAAuG,GAAA,CAAAQ,sBAAA,CAA4B;UAsBH/G,uDAAA,GAAY;UAAZA,wDAAA,YAAAuG,GAAA,CAAAuB,SAAA,CAAY;UAqB1B9H,uDAAA,IAAW;UAAXA,wDAAA,SAAAuG,GAAA,CAAAe,KAAA,CAAW;UAoBRtH,uDAAA,GAAgD;UAAhDA,wDAAA,aAAAuG,GAAA,CAAA+B,WAAA,CAAAgB,OAAA,IAAA/C,GAAA,CAAAsB,YAAA,CAAgD;UAEvC7H,uDAAA,GAAmB;UAAnBA,wDAAA,UAAAuG,GAAA,CAAAsB,YAAA,CAAmB;UACR7H,uDAAA,GAAkB;UAAlBA,wDAAA,SAAAuG,GAAA,CAAAsB,YAAA,CAAkB;UAC1D7H,uDAAA,GACF;UADEA,gEAAA,MAAAuG,GAAA,CAAAsB,YAAA,8DACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClM8D;AAUA;;;;;;;;;;;;ICEhE7H,4DAAA,cAAgG;IAC9FA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAA6G,MAAA,CAAApG,KAAA,CAAA+D,OAAA,mCACF;;;;;IACAxE,4DAAA,cAA+G;IAC7GA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAAQ,MAAA,CAAA+L,cAAA,MACF;;;;;IAYIvM,4DAAA,cACyD;IACvDA,uDAAA,YAA8C;IAC9CA,oDAAA,iCACF;IAAAA,0DAAA,EAAM;;;;;IA4BFA,4DAAA,cACyD;IACvDA,uDAAA,YAA8C;IAC9CA,oDAAA,gCACF;IAAAA,0DAAA,EAAM;;;;;IAUNA,4DAAA,cACyD;IACvDA,uDAAA,YAA8C;IAC9CA,oDAAA,8CACF;IAAAA,0DAAA,EAAM;;;;;IAUNA,4DAAA,cACyD;IACvDA,uDAAA,YAA8C;IAC9CA,oDAAA,uCACF;IAAAA,0DAAA,EAAM;;;;;IA0BJA,4DAAA,eAAqE;IACnEA,4DAAA,EAAyE;IAAzEA,4DAAA,cAAyE;IACvEA,uDAAA,iBAAkG;IAEpGA,0DAAA,EAAM;IACNA,oDAAA,6BACF;IAAAA,0DAAA,EAAO;;;;;IAQTA,4DAAA,cAA4H;IAC1HA,uDAAA,YAAgD;IAChDA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAA2B,MAAA,CAAA6K,cAAA,MACF;;;;;IAGAxM,4DAAA,cAC2G;IACzGA,uDAAA,YAAwC;IACxCA,oDAAA,wBACF;IAAAA,0DAAA,EAAM;;;;;IAkBRA,4DAAA,iBAAkE;IAAAA,oDAAA,GAAoB;IAAAA,0DAAA,EAAS;;;;IAApDA,wDAAA,UAAAyM,YAAA,CAAA1H,GAAA,CAAsB;IAAC/E,uDAAA,GAAoB;IAApBA,+DAAA,CAAAyM,YAAA,CAAAlK,KAAA,CAAoB;;;;;IAExFvC,4DAAA,cACyD;IACvDA,uDAAA,YAA8C;IAC9CA,oDAAA,oCACF;IAAAA,0DAAA,EAAM;;;;;IAlBRA,4DAAA,cAA2H;IAEvHA,uDAAA,YAAwD;IACxDA,oDAAA,iBACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,gBAA6E;IAC3EA,uDAAA,YAAgD;IAChDA,oDAAA,wCACF;IAAAA,0DAAA,EAAQ;IACRA,4DAAA,iBACmL;IAChKA,oDAAA,gCAAyB;IAAAA,0DAAA,EAAS;IACnDA,wDAAA,KAAA0M,8CAAA,qBAA+F;IACjG1M,0DAAA,EAAS;IACTA,wDAAA,KAAA2M,2CAAA,kBAIM;IACR3M,0DAAA,EAAM;;;;;IAP2BA,uDAAA,IAAY;IAAZA,wDAAA,YAAAgC,MAAA,CAAA8F,SAAA,CAAY;IAErC9H,uDAAA,GAAkF;IAAlFA,wDAAA,WAAA4M,OAAA,GAAA5K,MAAA,CAAAsG,WAAA,CAAApE,GAAA,+BAAA0I,OAAA,CAAAtD,OAAA,OAAAsD,OAAA,GAAA5K,MAAA,CAAAsG,WAAA,CAAApE,GAAA,+BAAA0I,OAAA,CAAAX,OAAA,EAAkF;;;;;IAwBxFjM,4DAAA,cAAqI;IACnIA,uDAAA,YAAuC;IACvCA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAA6M,OAAA,CAAA9C,gBAAA,CAAAtH,WAAA,MACF;;;;;IAnBFzC,4DAAA,cAA4J;IAExJA,uDAAA,YAA0D;IAC1DA,oDAAA,uCACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,cAA+C;IAE3CA,uDAAA,YAAwD;IACxDA,oDAAA,GACF;IAAAA,0DAAA,EAAO;IACPA,4DAAA,eAAsG;IACpGA,uDAAA,YAAiC;IACjCA,oDAAA,IAEF;;;IAAAA,0DAAA,EAAO;IAETA,wDAAA,KAAA8M,2CAAA,kBAGM;IACR9M,0DAAA,EAAM;;;;IAZAA,uDAAA,GACF;IADEA,gEAAA,MAAA8G,OAAA,CAAAiD,gBAAA,CAAAxH,KAAA,MACF;IAGEvC,uDAAA,GAEF;IAFEA,gEAAA,MAAAA,yDAAA,QAAA8G,OAAA,CAAAiD,gBAAA,CAAA5G,SAAA,wBAAAnD,yDAAA,QAAA8G,OAAA,CAAAiD,gBAAA,CAAA3G,OAAA,qBAEF;IAEIpD,uDAAA,GAAkC;IAAlCA,wDAAA,SAAA8G,OAAA,CAAAiD,gBAAA,CAAAtH,WAAA,CAAkC;;;;;IAmBpCzC,4DAAA,iBAAmE;IACjEA,uDAAA,YAAgC;IAAAA,oDAAA,GAClC;IAAAA,0DAAA,EAAS;;;;IAF0BA,wDAAA,UAAA+M,QAAA,CAAAhI,GAAA,CAAkB;IACnB/E,uDAAA,GAClC;IADkCA,gEAAA,KAAA+M,QAAA,CAAAnM,QAAA,MAClC;;;;;IAHFZ,qEAAA,GAA8C;IAC5CA,wDAAA,IAAAgN,sDAAA,qBAES;IACXhN,mEAAA,EAAe;;;;IAHYA,uDAAA,GAAQ;IAARA,wDAAA,YAAAiN,SAAA,CAAQ;;;;;IAqBrCjN,uDAAA,YAA8E;;;;;IAC9EA,uDAAA,YAAgE;;;;;IAChEA,uDAAA,YAA8D;;;ADnNhE,MAAOkN,oBAAoB;EAe/B5J,YACUkE,EAAe,EACf/D,cAA8B,EAC9BiE,eAAgC,EAChCD,WAAwB,EACxBlE,KAAqB,EACrBC,MAAc,EACdmE,WAA4B,EAC5BhE,YAA0B;IAP1B,KAAA6D,EAAE,GAAFA,EAAE;IACF,KAAA/D,cAAc,GAAdA,cAAc;IACd,KAAAiE,eAAe,GAAfA,eAAe;IACf,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAlE,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAmE,WAAW,GAAXA,WAAW;IACX,KAAAhE,YAAY,GAAZA,YAAY;IArBtB,KAAAmE,SAAS,GAAe,EAAE;IAE1B,KAAAlE,OAAO,GAAG,IAAI;IACd,KAAAiE,YAAY,GAAG,KAAK;IACpB,KAAApH,KAAK,GAAQ,IAAI;IACjB,KAAA8L,cAAc,GAAkB,IAAI;IACpC,KAAAY,UAAU,GAAG,KAAK;IAClB,KAAAC,gBAAgB,GAAkB,IAAI;IACtC,KAAAC,iBAAiB,GAAkB,IAAI;IACvC,KAAAtD,gBAAgB,GAAoB,IAAI;IACxC,KAAAyC,cAAc,GAAkB,IAAI;IACpC,KAAAc,mBAAmB,GAAG,KAAK;IAYzB,IAAI,CAAChF,WAAW,GAAG,IAAI,CAACd,EAAE,CAACe,KAAK,CAAC;MAC/BhG,KAAK,EAAE,CAAC,EAAE,EAAEqE,sDAAU,CAAC4B,QAAQ,CAAC;MAChC/F,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBI,IAAI,EAAE,CAAC,EAAE,EAAE+D,sDAAU,CAAC4B,QAAQ,CAAC;MAC/B1F,UAAU,EAAE,CAAC,EAAE,EAAE8D,sDAAU,CAAC4B,QAAQ,CAAC;MACrCzF,QAAQ,EAAE,CAAC,EAAE,EAAE6D,sDAAU,CAAC4B,QAAQ,CAAC;MACnCtH,IAAI,EAAE,CAAC,EAAE,CAAC;MACVG,SAAS,EAAE,CAAC,EAAE,CAAC;MACf6B,QAAQ,EAAE,CAAC,EAAE,EAAE0D,sDAAU,CAAC4B,QAAQ,CAAC;MACnCvF,YAAY,EAAE,CAAC,EAAE;KAClB,CAAC;IAGF,IAAI,CAACsK,MAAM,GAAG,IAAI,CAAC9F,WAAW,CAACgB,WAAW,EAAE;EAC9C;EAEA5E,QAAQA,CAAA;IACN,IAAI,CAAC2J,aAAa,EAAE;IACpB,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,wBAAwB,EAAE;EACjC;EAEAF,aAAaA,CAAA;IACX,MAAMzF,SAAS,GAAG,IAAI,CAACzE,KAAK,CAACS,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACxD,IAAI8D,SAAS,EAAE;MACb,IAAI,CAACmF,UAAU,GAAG,IAAI;MACtB,IAAI,CAACC,gBAAgB,GAAGpF,SAAS;MACjC,IAAI,CAACK,WAAW,CAACL,SAAS,CAAC;;EAE/B;EAEAwF,aAAaA,CAAA;IACX,MAAM9E,MAAM,GAAG,IAAI,CAACf,WAAW,CAACgB,gBAAgB,EAAE;IAClD,IAAI,CAACD,MAAM,EAAE;IAEb,IAAI,CAAChB,eAAe,CAACoB,kBAAkB,CAACJ,MAAM,CAAC,CAACtE,SAAS,CAAC;MACxDC,IAAI,EAAGC,QAAY,IAAI;QACrB,IAAI,CAACwD,SAAS,GAAGxD,QAAQ,CAACwD,SAAS,IAAI,EAAE;QACzCrD,OAAO,CAACQ,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC6C,SAAS,CAAC;QACpDrD,OAAO,CAACQ,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC6C,SAAS,CAAC,CAAC,CAAC,CAAC;MACxD,CAAC;MACDrH,KAAK,EAAG8D,GAAG,IAAI;QACb,IAAI,CAAC9D,KAAK,GAAG8D,GAAG;QAChBE,OAAO,CAAChE,KAAK,CAAC,gCAAgC,EAAE8D,GAAG,CAAC;MACtD;KACD,CAAC;EACJ;EAEA8D,WAAWA,CAACtE,EAAU;IACpB,IAAI,CAACN,cAAc,CAACU,cAAc,CAACJ,EAAE,CAAC,CAACK,SAAS,CAAC;MAC/CC,IAAI,EAAGpD,OAAO,IAAI;QAChB,IAAI,CAACqH,WAAW,CAACU,UAAU,CAAC;UAC1BzG,KAAK,EAAEtB,OAAO,CAACsB,KAAK;UACpBE,WAAW,EAAExB,OAAO,CAACwB,WAAW;UAChCU,SAAS,EAAE,IAAI,CAACyK,kBAAkB,CAAC3M,OAAO,CAACkC,SAAS,CAAC;UACrDC,OAAO,EAAE,IAAI,CAACwK,kBAAkB,CAAC3M,OAAO,CAACmC,OAAO,CAAC;UACjDlC,IAAI,EAAED,OAAO,CAACC,IAAI;UAClBG,SAAS,EAAEJ,OAAO,CAACI,SAAS;UAC5ByI,UAAU,EAAE7I,OAAO,CAAC6I,UAAU;UAC9B7G,YAAY,EAAEhC,OAAO,CAACgC;SACvB,CAAC;QACF,IAAI,CAACW,OAAO,GAAG,KAAK;MACtB,CAAC;MACDnD,KAAK,EAAG8D,GAAG,IAAI;QACb,IAAI,CAAC9D,KAAK,GAAG8D,GAAG;QAChB,IAAI,CAACX,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAgK,kBAAkBA,CAAC/K,IAAmB;IACpC,OAAO,IAAIwH,IAAI,CAACxH,IAAI,CAAC,CAACgL,WAAW,EAAE,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EACpD;;EAEAJ,kBAAkBA,CAAA;IAChB,MAAM5D,UAAU,GAAG,IAAI,CAACvG,KAAK,CAACS,QAAQ,CAAC+J,aAAa,CAAC7J,GAAG,CAAC,YAAY,CAAC;IACtE,IAAI4F,UAAU,EAAE;MACd,IAAI,CAACuD,iBAAiB,GAAGvD,UAAU;MAEnC;MACA,IAAI,CAACxB,WAAW,CAACU,UAAU,CAAC;QAC1B9F,QAAQ,EAAE4G;OACX,CAAC;MAEF;MACA,IAAI,CAACpC,eAAe,CAACwC,eAAe,CAACJ,UAAU,CAAC,CAAC1F,SAAS,CAAC;QACzDC,IAAI,EAAGC,QAAa,IAAI;UACtB,IAAI,CAACyF,gBAAgB,GAAGzF,QAAQ,CAACpB,QAAQ;UACzC;UACA,IAAI,IAAI,CAAC6G,gBAAgB,IAAI,CAAC,IAAI,CAACjC,SAAS,CAACkC,IAAI,CAACb,CAAC,IAAIA,CAAC,CAACpE,GAAG,KAAK+E,UAAU,CAAC,EAAE;YAC5E,IAAI,CAAChC,SAAS,CAACqC,IAAI,CAAC,IAAI,CAACJ,gBAAgB,CAAC;YAC1CtF,OAAO,CAACQ,GAAG,CAAC,sDAAsD,EAAE,IAAI,CAAC8E,gBAAgB,CAAC;;QAE9F,CAAC;QACDtJ,KAAK,EAAG8D,GAAG,IAAI;UACbE,OAAO,CAAChE,KAAK,CAAC,6CAA6C,EAAE8D,GAAG,CAAC;UACjE,IAAI,CAACZ,YAAY,CAAClD,KAAK,CACrB,sBAAsB,EACtB,0FAA0F,CAC3F;QACH;OACD,CAAC;;EAEN;EAEA4I,QAAQA,CAAA;IACN,IAAI,IAAI,CAACf,WAAW,CAACgB,OAAO,IAAI,CAAC,IAAI,CAAC0E,SAAS,EAAE,EAAE;MACjD,IAAI,CAACrK,YAAY,CAAC4F,OAAO,CACvB,qBAAqB,EACrB,gEAAgE,CACjE;MACD;;IAGF;IACA,IAAI,CAAC,IAAI,CAACC,2BAA2B,EAAE,EAAE;MACvC;;IAGF,IAAI,CAAC3B,YAAY,GAAG,IAAI;IACxB,IAAI,CAACpH,KAAK,GAAG,IAAI;IACjB,IAAI,CAAC8L,cAAc,GAAG,IAAI;IAC1B,MAAM3C,SAAS,GAAG,IAAI,CAACtB,WAAW,CAACmB,KAAK;IAExC,MAAM5G,IAAI,GAAG+G,SAAS,CAAC/G,IAAI,CAAC,CAAC;IAC7B,MAAMC,UAAU,GAAG8G,SAAS,CAAC9G,UAAU,CAAC,CAAC;IACzC,MAAMC,QAAQ,GAAG6G,SAAS,CAAC7G,QAAQ;IAEnC,MAAMkL,WAAW,GAAQ;MACvB1L,KAAK,EAAEqH,SAAS,CAACrH,KAAK;MACtBE,WAAW,EAAEmH,SAAS,CAACnH,WAAW;MAClCI,IAAI,EAAEA,IAAI;MACVC,UAAU,EAAEA,UAAU;MACtBC,QAAQ,EAAEA,QAAQ;MAClB7B,IAAI,EAAE0I,SAAS,CAAC1I,IAAI;MACpBG,SAAS,EAAEuI,SAAS,CAACvI,SAAS;MAC9B6B,QAAQ,EAAE0G,SAAS,CAAC1G,QAAQ;MAC5BD,YAAY,EAAE2G,SAAS,CAAC3G,YAAY,IAAI;KACzC;IAEDwB,OAAO,CAACQ,GAAG,CAAC,qCAAqC,EAAEgJ,WAAW,CAAC;IAC/DxJ,OAAO,CAACQ,GAAG,CAAC,6BAA6B,EAAE2E,SAAS,CAAC1G,QAAQ,CAAC;IAC9DuB,OAAO,CAACQ,GAAG,CAAC,yBAAyB,EAAE,OAAO2E,SAAS,CAAC1G,QAAQ,CAAC;IACjEuB,OAAO,CAACQ,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAAC6C,SAAS,CAAC;IAExD,IAAI,CAACrE,cAAc,CAACyK,aAAa,CAACD,WAAW,CAAC,CAAC7J,SAAS,CAAC;MACvDC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACwD,YAAY,GAAG,KAAK;QAEzB,IAAI,CAAClE,YAAY,CAACuB,OAAO,CACvB,eAAe,EACf,oCAAoC,CACrC;QAED;QACA,IAAI,CAACiJ,SAAS,EAAE;QAEhB;QACA,IAAI,CAAC3K,MAAM,CAACsB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;MACrC,CAAC;MACDrE,KAAK,EAAG8D,GAAG,IAAI;QACb,IAAI,CAACsD,YAAY,GAAG,KAAK;QACzBpD,OAAO,CAAChE,KAAK,CAAC,2CAA2C,EAAE8D,GAAG,CAAC;QAE/D,IAAIA,GAAG,CAACY,MAAM,KAAK,GAAG,EAAE;UACtB,IAAI,CAACxB,YAAY,CAACyB,YAAY,CAAC,mBAAmB,EAAEb,GAAG,CAACY,MAAM,CAAC;SAChE,MAAM,IAAIZ,GAAG,CAACY,MAAM,KAAK,GAAG,EAAE;UAC7B,IAAI,CAACxB,YAAY,CAAClD,KAAK,CACrB,cAAc,EACd,iDAAiD,CAClD;SACF,MAAM;UACL,MAAM4E,YAAY,GAAGd,GAAG,CAAC9D,KAAK,EAAE+D,OAAO,IAAI,0CAA0C;UACrF,IAAI,CAACb,YAAY,CAAClD,KAAK,CACrB,oBAAoB,EACpB4E,YAAY,EACZ,IAAI,CACL;;MAEL;KACD,CAAC;EACJ;EAEA8I,SAASA,CAAA;IACP;IACA,IAAI,CAAC7F,WAAW,CAAC8F,KAAK,CAAC;MACrB7L,KAAK,EAAE,EAAE;MACTE,WAAW,EAAE,EAAE;MACfI,IAAI,EAAE,EAAE;MACRC,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAE,EAAE;MACZ7B,IAAI,EAAE,EAAE;MACRG,SAAS,EAAE,EAAE;MACb6B,QAAQ,EAAE,EAAE;MACZD,YAAY,EAAE;KACf,CAAC;IAEF;IACA,IAAI,CAACqF,WAAW,CAAC+F,cAAc,EAAE;IACjC,IAAI,CAAC/F,WAAW,CAACgG,eAAe,EAAE;EACpC;EAGA3E,SAASA,CAAA;IACP,IAAI,CAACnG,MAAM,CAACsB,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;EACrC;EAEA;;;EAGA6I,wBAAwBA,CAAA;IACtB,IAAI,CAACrF,WAAW,CAACpE,GAAG,CAAC,WAAW,CAAC,EAAEqK,YAAY,CAC5CC,IAAI,CACHnC,4DAAY,CAAC,GAAG,CAAC;IAAE;IACnBC,oEAAoB,EAAE,CAAC;KACxB,CACAlI,SAAS,CAACqF,KAAK,IAAG;MACjB,IAAIA,KAAK,IAAIA,KAAK,CAACgF,IAAI,EAAE,KAAK,EAAE,EAAE;QAChC,IAAI,CAACC,wBAAwB,CAACjF,KAAK,CAACgF,IAAI,EAAE,CAAC;OAC5C,MAAM;QACL,IAAI,CAACjC,cAAc,GAAG,IAAI;;IAE9B,CAAC,CAAC;EACN;EAEA;;;;EAIAkC,wBAAwBA,CAACrN,SAAiB;IACxC,IAAI,CAACA,SAAS,IAAIA,SAAS,CAACoN,IAAI,EAAE,KAAK,EAAE,EAAE;MACzC,IAAI,CAACjC,cAAc,GAAG,IAAI;MAC1B;;IAGF,IAAI,CAACc,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACd,cAAc,GAAG,IAAI;IAE1B;IACA,IAAI,CAAC/I,cAAc,CAACiL,wBAAwB,CAACrN,SAAS,EAAE,IAAI,CAAC+L,gBAAgB,IAAIuB,SAAS,CAAC,CAACvK,SAAS,CAAC;MACpGC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACgJ,mBAAmB,GAAG,KAAK;QAEhC,IAAIhJ,QAAQ,CAACY,OAAO,IAAI,CAACZ,QAAQ,CAACsK,QAAQ,EAAE;UAC1C,IAAI,CAACpC,cAAc,GAAG,4CAA4ClI,QAAQ,CAACuK,YAAY,EAAEtM,KAAK,GAAG;SAClG,MAAM;UACL,IAAI,CAACiK,cAAc,GAAG,IAAI;;MAE9B,CAAC;MACD/L,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC6M,mBAAmB,GAAG,KAAK;QAChC7I,OAAO,CAAChE,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;QACrE,IAAI,CAAC+L,cAAc,GAAG,wCAAwC;MAChE;KACD,CAAC;EACJ;EAEA;;;EAGAwB,SAASA,CAAA;IACP,OAAO,IAAI,CAAC1F,WAAW,CAACwG,KAAK,IAAI,CAAC,IAAI,CAACtC,cAAc,IAAI,CAAC,IAAI,CAACc,mBAAmB;EACpF;EAEA;;;EAGA9D,2BAA2BA,CAAA;IACzB,MAAMI,SAAS,GAAG,IAAI,CAACtB,WAAW,CAACmB,KAAK;IACxC,MAAMI,WAAW,GAAGD,SAAS,CAAC/G,IAAI;IAClC,MAAMiH,UAAU,GAAGF,SAAS,CAAC1G,QAAQ;IAErC,IAAI,CAAC2G,WAAW,IAAI,CAACC,UAAU,EAAE;MAC/B,OAAO,IAAI,CAAC,CAAC;;IAGf;IACA,IAAIC,gBAAgB,GAAG,IAAI,CAACjC,SAAS,CAACkC,IAAI,CAACb,CAAC,IAAIA,CAAC,CAACpE,GAAG,KAAK+E,UAAU,CAAC;IAErE,IAAI,CAACC,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAAChF,GAAG,KAAK+E,UAAU,EAAE;MAC1FC,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;;IAG1C,IAAI,CAACA,gBAAgB,EAAE;MACrBtF,OAAO,CAACwF,IAAI,CAAC,yCAAyC,EAAEH,UAAU,CAAC;MACnErF,OAAO,CAACQ,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAAC6C,SAAS,CAACoB,GAAG,CAACC,CAAC,KAAK;QAAEpF,EAAE,EAAEoF,CAAC,CAACpE,GAAG;QAAExC,KAAK,EAAE4G,CAAC,CAAC5G;MAAK,CAAE,CAAC,CAAC,CAAC;MAClGkC,OAAO,CAACQ,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC8E,gBAAgB,CAAC;MAE3D;MACA,OAAO,IAAI;;IAGb;IACA,MAAMK,cAAc,GAAG,IAAIC,IAAI,CAACR,WAAW,CAAC;IAC5C,MAAMS,iBAAiB,GAAG,IAAID,IAAI,CAACN,gBAAgB,CAAC5G,SAAS,CAAC;IAC9D,MAAMoH,eAAe,GAAG,IAAIF,IAAI,CAACN,gBAAgB,CAAC3G,OAAO,CAAC;IAE1D;IACAgH,cAAc,CAACI,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACnCF,iBAAiB,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtCD,eAAe,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAEpC,IAAIJ,cAAc,GAAGE,iBAAiB,IAAIF,cAAc,GAAGG,eAAe,EAAE;MAC1E,IAAI,CAAC5G,YAAY,CAAClD,KAAK,CACrB,eAAe,EACf,qDAAqD6J,iBAAiB,CAACG,kBAAkB,CAAC,OAAO,CAAC,UAAUF,eAAe,CAACE,kBAAkB,CAAC,OAAO,CAAC,0BAA0BV,gBAAgB,CAACxH,KAAK,IAAI,EAC3M,KAAK,CACN;MACD,OAAO,KAAK;;IAGd,OAAO,IAAI;EACb;;;uBAlVW2K,oBAAoB,EAAAlN,+DAAA,CAAAuF,uDAAA,GAAAvF,+DAAA,CAAA0F,4EAAA,GAAA1F,+DAAA,CAAA4F,8EAAA,GAAA5F,+DAAA,CAAA8F,mEAAA,GAAA9F,+DAAA,CAAA4K,2DAAA,GAAA5K,+DAAA,CAAA4K,mDAAA,GAAA5K,+DAAA,CAAA8K,8EAAA,GAAA9K,+DAAA,CAAA+K,wEAAA;IAAA;EAAA;;;YAApBmC,oBAAoB;MAAAlH,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA2I,8BAAAzI,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnBjCtG,4DAAA,aAAmD;UAI7CA,uDAAA,WAAuD;UACvDA,oDAAA,GACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,WAAgC;UAC9BA,oDAAA,GACF;UAAAA,0DAAA,EAAI;UAGNA,4DAAA,cAA+G;UAA/EA,wDAAA,sBAAAgP,uDAAA;YAAA,OAAYzI,GAAA,CAAA8C,QAAA,EAAU;UAAA,EAAC;UACrDrJ,wDAAA,IAAAiP,mCAAA,iBAEM;UACNjP,wDAAA,IAAAkP,mCAAA,iBAEM;UAENlP,4DAAA,cAAoC;UAI9BA,uDAAA,aAA+C;UAC/CA,oDAAA,iBACF;UAAAA,0DAAA,EAAQ;UACRA,uDAAA,iBAE6C;UAC7CA,wDAAA,KAAAmP,oCAAA,kBAIM;UACRnP,0DAAA,EAAM;UAGNA,4DAAA,cAAsB;UAElBA,uDAAA,aAAsD;UACtDA,oDAAA,qBACF;UAAAA,0DAAA,EAAQ;UACRA,uDAAA,oBAE6D;UAC/DA,0DAAA,EAAM;UAGNA,4DAAA,eAA4F;UAExFA,uDAAA,aAAwD;UACxDA,oDAAA,uBACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,eAAmD;UAG7CA,uDAAA,aAAkD;UAClDA,oDAAA,gBACF;UAAAA,0DAAA,EAAQ;UACRA,uDAAA,iBAC4K;UAC5KA,wDAAA,KAAAoP,oCAAA,kBAIM;UACRpP,0DAAA,EAAM;UAENA,4DAAA,WAAK;UAEDA,uDAAA,aAA+C;UAC/CA,oDAAA,+BACF;UAAAA,0DAAA,EAAQ;UACRA,uDAAA,iBAC+K;UAC/KA,wDAAA,KAAAqP,oCAAA,kBAIM;UACRrP,0DAAA,EAAM;UAENA,4DAAA,WAAK;UAEDA,uDAAA,aAA6C;UAC7CA,oDAAA,wBACF;UAAAA,0DAAA,EAAQ;UACRA,uDAAA,iBACyK;UACzKA,wDAAA,KAAAsP,oCAAA,kBAIM;UACRtP,0DAAA,EAAM;UAKVA,4DAAA,eAAkG;UAE9FA,uDAAA,aAA0D;UAC1DA,oDAAA,sBACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,eAAmD;UAG7CA,uDAAA,aAA0D;UAC1DA,oDAAA,sBACF;UAAAA,0DAAA,EAAQ;UACRA,uDAAA,iBAEyD;UAC3DA,0DAAA,EAAM;UAENA,4DAAA,WAAK;UAEDA,uDAAA,aAA+C;UAC/CA,oDAAA,oBACA;UAAAA,wDAAA,KAAAuP,qCAAA,mBAMO;UACTvP,0DAAA,EAAQ;UACRA,uDAAA,iBAGiD;UAGjDA,wDAAA,KAAAwP,oCAAA,kBAGM;UAGNxP,wDAAA,KAAAyP,oCAAA,kBAIM;UACRzP,0DAAA,EAAM;UAKVA,wDAAA,KAAA0P,oCAAA,mBAmBM;UAGN1P,wDAAA,KAAA2P,oCAAA,oBAoBM;UAGN3P,4DAAA,eAAkG;UAE9FA,uDAAA,aAAkD;UAClDA,oDAAA,sBACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,iBAA+D;UAC7DA,uDAAA,aAAyD;UACzDA,oDAAA,4CACF;UAAAA,0DAAA,EAAQ;UACRA,4DAAA,kBAC4M;UAC1MA,wDAAA,KAAA4P,6CAAA,2BAIe;;UACjB5P,0DAAA,EAAS;UACTA,4DAAA,aAAyC;UACvCA,uDAAA,aAAuC;UACvCA,oDAAA,+EACF;UAAAA,0DAAA,EAAI;UAKRA,4DAAA,eAAgG;UACxEA,wDAAA,mBAAA6P,uDAAA;YAAA,OAAStJ,GAAA,CAAAoD,SAAA,EAAW;UAAA,EAAC;UAEzC3J,uDAAA,aAAiC;UACjCA,oDAAA,iBACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBACqQ;UACnQA,wDAAA,KAAA8P,kCAAA,gBAA8E;UAC9E9P,wDAAA,KAAA+P,kCAAA,gBAAgE;UAChE/P,wDAAA,KAAAgQ,kCAAA,gBAA8D;UAC9DhQ,oDAAA,IACF;UAAAA,0DAAA,EAAS;;;;;;;;UAnOTA,uDAAA,GACF;UADEA,gEAAA,MAAAuG,GAAA,CAAA4G,UAAA,6DACF;UAEEnN,uDAAA,GACF;UADEA,gEAAA,MAAAuG,GAAA,CAAA4G,UAAA,0HACF;UAGInN,uDAAA,GAAyB;UAAzBA,wDAAA,cAAAuG,GAAA,CAAA+B,WAAA,CAAyB;UACvBtI,uDAAA,GAAW;UAAXA,wDAAA,SAAAuG,GAAA,CAAA9F,KAAA,CAAW;UAGXT,uDAAA,GAAoB;UAApBA,wDAAA,SAAAuG,GAAA,CAAAgG,cAAA,CAAoB;UAchBvM,uDAAA,GAA4E;UAA5EA,wDAAA,WAAAoM,OAAA,GAAA7F,GAAA,CAAA+B,WAAA,CAAApE,GAAA,4BAAAkI,OAAA,CAAA9C,OAAA,OAAA8C,OAAA,GAAA7F,GAAA,CAAA+B,WAAA,CAAApE,GAAA,4BAAAkI,OAAA,CAAAH,OAAA,EAA4E;UAgCxEjM,uDAAA,IAA0E;UAA1EA,wDAAA,WAAAiQ,OAAA,GAAA1J,GAAA,CAAA+B,WAAA,CAAApE,GAAA,2BAAA+L,OAAA,CAAA3G,OAAA,OAAA2G,OAAA,GAAA1J,GAAA,CAAA+B,WAAA,CAAApE,GAAA,2BAAA+L,OAAA,CAAAhE,OAAA,EAA0E;UAc1EjM,uDAAA,GAAsF;UAAtFA,wDAAA,WAAAkQ,OAAA,GAAA3J,GAAA,CAAA+B,WAAA,CAAApE,GAAA,iCAAAgM,OAAA,CAAA5G,OAAA,OAAA4G,OAAA,GAAA3J,GAAA,CAAA+B,WAAA,CAAApE,GAAA,iCAAAgM,OAAA,CAAAjE,OAAA,EAAsF;UActFjM,uDAAA,GAAkF;UAAlFA,wDAAA,WAAAmQ,OAAA,GAAA5J,GAAA,CAAA+B,WAAA,CAAApE,GAAA,+BAAAiM,OAAA,CAAA7G,OAAA,OAAA6G,OAAA,GAAA5J,GAAA,CAAA+B,WAAA,CAAApE,GAAA,+BAAAiM,OAAA,CAAAlE,OAAA,EAAkF;UA8B/EjM,uDAAA,IAAyB;UAAzBA,wDAAA,SAAAuG,GAAA,CAAA+G,mBAAA,CAAyB;UAS3BtN,uDAAA,GAC6H;UAD7HA,wDAAA,qHAAAuG,GAAA,CAAAiG,cAAA,sGAC6H;UAI9HxM,uDAAA,GAAoB;UAApBA,wDAAA,SAAAuG,GAAA,CAAAiG,cAAA,CAAoB;UAMpBxM,uDAAA,GAAyI;UAAzIA,wDAAA,UAAAuG,GAAA,CAAAiG,cAAA,KAAAjG,GAAA,CAAA+G,mBAAA,MAAA+C,QAAA,GAAA9J,GAAA,CAAA+B,WAAA,CAAApE,GAAA,gCAAAmM,QAAA,CAAA5G,KAAA,OAAA4G,QAAA,GAAA9J,GAAA,CAAA+B,WAAA,CAAApE,GAAA,gCAAAmM,QAAA,CAAA5G,KAAA,CAAAgF,IAAA,WAAyI;UAU/IzO,uDAAA,GAAwB;UAAxBA,wDAAA,UAAAuG,GAAA,CAAA8G,iBAAA,CAAwB;UAsBxBrN,uDAAA,GAA2C;UAA3CA,wDAAA,SAAAuG,GAAA,CAAA8G,iBAAA,IAAA9G,GAAA,CAAAwD,gBAAA,CAA2C;UAkC9B/J,uDAAA,GAAqB;UAArBA,wDAAA,SAAAA,yDAAA,SAAAuG,GAAA,CAAAgH,MAAA,EAAqB;UAoBlBvN,uDAAA,GAAyC;UAAzCA,wDAAA,cAAAuG,GAAA,CAAAyH,SAAA,MAAAzH,GAAA,CAAAsB,YAAA,CAAyC;UAEhC7H,uDAAA,GAA2C;UAA3CA,wDAAA,UAAAuG,GAAA,CAAAsB,YAAA,KAAAtB,GAAA,CAAA+G,mBAAA,CAA2C;UAChCtN,uDAAA,GAAkB;UAAlBA,wDAAA,SAAAuG,GAAA,CAAAsB,YAAA,CAAkB;UAC3B7H,uDAAA,GAAyB;UAAzBA,wDAAA,SAAAuG,GAAA,CAAA+G,mBAAA,CAAyB;UACxDtN,uDAAA,GACF;UADEA,gEAAA,MAAAuG,GAAA,CAAAsB,YAAA,yBAAAtB,GAAA,CAAA+G,mBAAA,8DACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClOoF;;;;;;;;;;;;;IC8B5EtN,4DAAA,iBAAqK;IAA1IA,wDAAA,mBAAA4Q,sEAAA;MAAA5Q,2DAAA,CAAA6Q,IAAA;MAAA,MAAA7O,MAAA,GAAAhC,2DAAA;MAAA,OAASA,yDAAA,CAAAgC,MAAA,CAAA8O,WAAA,EAAa;IAAA,EAAC;IAChD9Q,4DAAA,EAA2E;IAA3EA,4DAAA,cAA2E;IACzEA,uDAAA,eAAiG;IACnGA,0DAAA,EAAM;;;;;IAgBNA,4DAAA,iBAAuE;IAAAA,oDAAA,GAAoB;IAAAA,0DAAA,EAAS;;;;IAAnDA,wDAAA,UAAAiH,YAAA,CAAAlD,EAAA,CAAqB;IAAC/D,uDAAA,GAAoB;IAApBA,+DAAA,CAAAiH,YAAA,CAAA1E,KAAA,CAAoB;;;;;;;;;;;;IAtCvGvC,6DAAA,EAC0E;IAD1EA,4DAAA,cAC0E;IAM9DA,4DAAA,EAA2E;IAA3EA,4DAAA,cAA2E;IACzEA,uDAAA,cAAwH;IAC1HA,0DAAA,EAAM;IAERA,6DAAA,EAOC;IAPDA,4DAAA,gBAOC;IAJCA,wDAAA,2BAAA+Q,oEAAAC,MAAA;MAAAhR,2DAAA,CAAAiR,IAAA;MAAA,MAAAC,OAAA,GAAAlR,2DAAA;MAAA,OAAAA,yDAAA,CAAAkR,OAAA,CAAAC,UAAA,GAAAH,MAAA;IAAA,EAAwB,mBAAAI,4DAAA;MAAApR,2DAAA,CAAAiR,IAAA;MAAA,MAAAI,OAAA,GAAArR,2DAAA;MAAA,OACfA,yDAAA,CAAAqR,OAAA,CAAAC,cAAA,EAAgB;IAAA,EADD;IAH1BtR,0DAAA,EAOC;IACDA,wDAAA,IAAAuR,6CAAA,qBAIS;IACXvR,0DAAA,EAAM;IAIVA,4DAAA,eAAsB;IAKdA,wDAAA,2BAAAwR,sEAAAR,MAAA;MAAAhR,2DAAA,CAAAiR,IAAA;MAAA,MAAAQ,OAAA,GAAAzR,2DAAA;MAAA,OAAAA,yDAAA,CAAAyR,OAAA,CAAA1H,gBAAA,GAAAiH,MAAA;IAAA,EAA8B,oBAAAU,+DAAA;MAAA1R,2DAAA,CAAAiR,IAAA;MAAA,MAAAU,OAAA,GAAA3R,2DAAA;MAAA,OACpBA,yDAAA,CAAA2R,OAAA,CAAAL,cAAA,EAAgB;IAAA,EADI;IAI9BtR,4DAAA,kBAAiB;IAAAA,oDAAA,0BAAkB;IAAAA,0DAAA,EAAS;IAC5CA,wDAAA,KAAA4R,8CAAA,qBAAoG;IACtG5R,0DAAA,EAAS;IACTA,4DAAA,eAAmF;IACjFA,4DAAA,EAA2F;IAA3FA,4DAAA,eAA2F;IACzFA,uDAAA,gBAA2F;IAC7FA,0DAAA,EAAM;;;;IA1CbA,wDAAA,YAAAA,6DAAA,IAAA8R,GAAA,EAAAjL,MAAA,CAAAkL,aAAA,EAAoE;IAa7D/R,uDAAA,GAAwB;IAAxBA,wDAAA,YAAA6G,MAAA,CAAAsK,UAAA,CAAwB;IAKjBnR,uDAAA,GAAgB;IAAhBA,wDAAA,SAAA6G,MAAA,CAAAsK,UAAA,CAAgB;IAcvBnR,uDAAA,GAA8B;IAA9BA,wDAAA,YAAA6G,MAAA,CAAAkD,gBAAA,CAA8B;IAKD/J,uDAAA,GAAkB;IAAlBA,wDAAA,YAAA6G,MAAA,CAAAmL,eAAA,CAAkB;;;;;;IAa3DhS,6DAAA,EAAkG;IAAlGA,4DAAA,cAAkG;IAChGA,4DAAA,EAA6F;IAA7FA,4DAAA,cAA6F;IAC3FA,uDAAA,eAAuG;IACzGA,0DAAA,EAAM;IACNA,6DAAA,EAAM;IAANA,4DAAA,WAAM;IAAAA,oDAAA,gCAAe;IAAAA,4DAAA,eAAyC;IAAAA,oDAAA,gCAAoB;IAAAA,0DAAA,EAAO;IAACA,oDAAA,sCAAyB;IAAAA,0DAAA,EAAO;;;;;IAK1HA,4DAAA,eAAiE;IAC/DA,oDAAA,kEACF;IAAAA,0DAAA,EAAO;;;;;IACPA,4DAAA,WAA0C;IACxCA,oDAAA,GACF;IAAAA,0DAAA,EAAO;;;;IADLA,uDAAA,GACF;IADEA,gEAAA,MAAA6M,OAAA,CAAAoF,gBAAA,CAAAC,MAAA,sCACF;;;;;;IANFlS,6DAAA,EAA+E;IAA/EA,4DAAA,cAA+E;IAC7EA,wDAAA,IAAAmS,2CAAA,mBAEO;IACPnS,wDAAA,IAAAoS,2CAAA,mBAEO;IACTpS,0DAAA,EAAM;;;;IANGA,uDAAA,GAAmC;IAAnCA,wDAAA,SAAAsC,MAAA,CAAA2P,gBAAA,CAAAC,MAAA,OAAmC;IAGnClS,uDAAA,GAAiC;IAAjCA,wDAAA,SAAAsC,MAAA,CAAA2P,gBAAA,CAAAC,MAAA,KAAiC;;;;;;IAM5ClS,6DAAA,EAA+C;IAA/CA,4DAAA,cAA+C;IAC7CA,uDAAA,cAAiH;IACjHA,4DAAA,YAA4C;IAAAA,oDAAA,yCAA6B;IAAAA,0DAAA,EAAI;;;;;;IAG/EA,6DAAA,EAAiK;IAAjKA,4DAAA,cAAiK;IAE7JA,4DAAA,EAA6F;IAA7FA,4DAAA,cAA6F;IAC3FA,uDAAA,eAA8H;IAChIA,0DAAA,EAAM;IACNA,6DAAA,EAAG;IAAHA,4DAAA,QAAG;IAAAA,oDAAA,GAA2D;IAAAA,0DAAA,EAAI;;;;IAA/DA,uDAAA,GAA2D;IAA3DA,gEAAA,kDAAAgB,MAAA,CAAAP,KAAA,CAAA+D,OAAA,KAA2D;;;;;;IAIlExE,6DAAA,EAAqH;IAArHA,4DAAA,cAAqH;IAEjHA,4DAAA,EAAqG;IAArGA,4DAAA,cAAqG;IACnGA,uDAAA,eAAmK;IACrKA,0DAAA,EAAM;IACNA,6DAAA,EAAmD;IAAnDA,4DAAA,aAAmD;IAAAA,oDAAA,sCAAqB;IAAAA,0DAAA,EAAK;IAC7EA,4DAAA,YAA8B;IAAAA,oDAAA,yFAAmE;IAAAA,0DAAA,EAAI;;;;IANhBA,yDAAA,cAAAoB,MAAA,CAAAwC,OAAA,CAA2B;;;;;IAuB1G5D,4DAAA,eACkG;IAChGA,oDAAA,kCACF;IAAAA,0DAAA,EAAO;;;;;IAiCbA,4DAAA,cAAgF;IAE5EA,4DAAA,EAA8F;IAA9FA,4DAAA,cAA8F;IAC5FA,uDAAA,eAAmV;IACrVA,0DAAA,EAAM;IACNA,6DAAA,EAAQ;IAARA,4DAAA,aAAQ;IAAAA,oDAAA,0BAAmB;IAAAA,0DAAA,EAAS;IAAAA,oDAAA,GACtC;IAAAA,0DAAA,EAAM;;;;IADgCA,uDAAA,GACtC;IADsCA,gEAAA,KAAAsS,WAAA,CAAArP,YAAA,CAAAiP,MAAA,MACtC;;;;;IAYFlS,4DAAA,cAAoD;IAEhDA,4DAAA,EAAgF;IAAhFA,4DAAA,cAAgF;IAC9EA,uDAAA,eAA+M;IACjNA,0DAAA,EAAM;IACNA,oDAAA,0CACF;IAAAA,0DAAA,EAAI;;;;IALDA,uDAAA,GAA8B;IAA9BA,mEAAA,SAAAsS,WAAA,CAAAjR,SAAA,EAAArB,2DAAA,CAA8B;;;;;;;;;IAnErCA,4DAAA,cAK8C;IAKsDA,oDAAA,GAAmB;IAAAA,0DAAA,EAAI;IAEnHA,wDAAA,IAAAwS,iDAAA,mBAGO;IACTxS,0DAAA,EAAM;IACNA,uDAAA,YAAkF;;IAClFA,4DAAA,eAA0D;IACxDA,4DAAA,EAAgG;IAAhGA,4DAAA,eAAgG;IAC9FA,uDAAA,gBAAwH;IAC1HA,0DAAA,EAAM;IACNA,6DAAA,EAAM;IAANA,4DAAA,YAAM;IAAAA,oDAAA,IAA0F;;IAAAA,0DAAA,EAAO;IAG3GA,4DAAA,eAAwC;IAEpCA,oDAAA,IACF;;IAAAA,0DAAA,EAAO;IACPA,4DAAA,kBAEqC;IAF7BA,wDAAA,mBAAAyS,oEAAAzB,MAAA;MAAA,MAAA0B,WAAA,GAAA1S,2DAAA,CAAA2S,IAAA;MAAA,MAAAL,WAAA,GAAAI,WAAA,CAAAE,SAAA;MAAA,MAAAC,OAAA,GAAA7S,2DAAA;MAAS6S,OAAA,CAAA5Q,aAAA,CAAAqQ,WAAA,CAAAvN,GAAA,IAAAuN,WAAA,CAAAvO,EAAA,CAAwC;MAAA,OAAE/D,yDAAA,CAAAgR,MAAA,CAAA8B,eAAA,EAAwB;IAAA,EAAE;IAGnF9S,4DAAA,EAA8G;IAA9GA,4DAAA,eAA8G;IAC5GA,uDAAA,gBAAyM;IAC3MA,0DAAA,EAAM;IAMZA,6DAAA,EAA0D;IAA1DA,4DAAA,eAA0D;IACxDA,4DAAA,EAA8F;IAA9FA,4DAAA,eAA8F;IAC5FA,uDAAA,gBAAgJ;IAClJA,0DAAA,EAAM;IACNA,6DAAA,EAAM;IAANA,4DAAA,YAAM;IAAQA,oDAAA,sBAAS;IAAAA,0DAAA,EAAS;IAACA,oDAAA,IAA+B;IAAAA,0DAAA,EAAO;IAIzEA,wDAAA,KAAA+S,iDAAA,kBAOM;IAGN/S,4DAAA,eAA0D;IACxDA,4DAAA,EAA8F;IAA9FA,4DAAA,eAA8F;IAC5FA,uDAAA,gBAAmK;IACrKA,0DAAA,EAAM;IACNA,6DAAA,EAAM;IAANA,4DAAA,YAAM;IAAQA,oDAAA,iBAAS;IAAAA,0DAAA,EAAS;IAACA,oDAAA,IAA4B;IAAAA,0DAAA,EAAO;IAItEA,wDAAA,KAAAgT,iDAAA,kBAOM;IAENhT,4DAAA,eAAkF;IAE9EA,4DAAA,EAAgF;IAAhFA,4DAAA,eAAgF;IAC9EA,uDAAA,gBAA+J;IAEjKA,0DAAA,EAAM;IACNA,oDAAA,IACF;IAAAA,0DAAA,EAAM;IACNA,6DAAA,EAAyC;IAAzCA,4DAAA,eAAyC;IACpCA,wDAAA,mBAAAiT,+DAAA;MAAA,MAAAP,WAAA,GAAA1S,2DAAA,CAAA2S,IAAA;MAAA,MAAAL,WAAA,GAAAI,WAAA,CAAAE,SAAA;MAAA,MAAAM,OAAA,GAAAlT,2DAAA;MAAA,OAASA,yDAAA,CAAAkT,OAAA,CAAApR,WAAA,CAAAwQ,WAAA,CAAAvN,GAAA,IAAAuN,WAAA,CAAAvO,EAAA,CAAsC;IAAA,EAAC;IAEjD/D,4DAAA,EAAgF;IAAhFA,4DAAA,eAAgF;IAC9EA,uDAAA,gBAAmM;IACrMA,0DAAA,EAAM;IACNA,oDAAA,kBACF;IAAAA,0DAAA,EAAI;;;;;;IArFLA,yDAAA,oBAAAoT,KAAA,cAAwC;IAHxCpT,yDAAA,aAAAqT,OAAA,CAAAC,YAAA,CAA+B,eAAAD,OAAA,CAAAE,sBAAA,CAAAjB,WAAA,qBAAAe,OAAA,CAAAE,sBAAA,CAAAjB,WAAA;IAQvBtS,uDAAA,GAAwD;IAAxDA,wDAAA,eAAAA,6DAAA,KAAAwT,GAAA,EAAAlB,WAAA,CAAAvN,GAAA,EAAwD;IAA+B/E,uDAAA,GAAmB;IAAnBA,+DAAA,CAAAsS,WAAA,CAAA/P,KAAA,CAAmB;IAExGvC,uDAAA,GAAqC;IAArCA,wDAAA,SAAAqT,OAAA,CAAAE,sBAAA,CAAAjB,WAAA,EAAqC;IAKtBtS,uDAAA,GAAqD;IAArDA,wDAAA,cAAAA,yDAAA,QAAAsS,WAAA,CAAA7P,WAAA,GAAAzC,4DAAA,CAAqD;IAKrEA,uDAAA,GAA0F;IAA1FA,gEAAA,KAAAA,yDAAA,SAAAsS,WAAA,CAAAzP,IAAA,6BAAAyP,WAAA,CAAAxP,UAAA,SAAAwP,WAAA,CAAAvP,QAAA,KAA0F;IAI5F/C,uDAAA,GAAwF;IAAxFA,wDAAA,iDAAAqT,OAAA,CAAAI,cAAA,CAAAnB,WAAA,CAAAoB,MAAA,EAAwF;IAC5F1T,uDAAA,GACF;IADEA,gEAAA,MAAAA,yDAAA,SAAAsS,WAAA,CAAAoB,MAAA,OACF;IAgB+B1T,uDAAA,IAA+B;IAA/BA,gEAAA,MAAAsS,WAAA,CAAAtP,QAAA,CAAApC,QAAA,KAA+B;IAI5DZ,uDAAA,GAAqC;IAArCA,wDAAA,SAAAsS,WAAA,CAAArP,YAAA,CAAAiP,MAAA,KAAqC;IAcRlS,uDAAA,GAA4B;IAA5BA,gEAAA,MAAAsS,WAAA,CAAApP,QAAA,CAAAX,KAAA,KAA4B;IAIzDvC,uDAAA,GAAuB;IAAvBA,wDAAA,SAAAsS,WAAA,CAAAjR,SAAA,CAAuB;IAezBrB,uDAAA,GACF;IADEA,gEAAA,MAAAsS,WAAA,CAAApR,IAAA,uCACF;;;;;;IAnFNlB,6DAAA,EAA2F;IAA3FA,4DAAA,cAA2F;IACzFA,wDAAA,IAAA2T,0CAAA,oBA6FM;IACR3T,0DAAA,EAAM;;;;IA9FqBA,uDAAA,GAAmE;IAAnEA,wDAAA,YAAA4T,MAAA,CAAAzC,UAAA,IAAAyC,MAAA,CAAA7J,gBAAA,GAAA6J,MAAA,CAAA3B,gBAAA,GAAA2B,MAAA,CAAAC,QAAA,CAAmE;;;AD/E1F,MAAOC,oBAAoB;EAa/B;EACA,IAAIC,SAASA,CAAA;IACX,OAAO,IAAI,CAACpM,WAAW,CAACqM,kBAAkB,EAAE,KAAK,OAAO,GACpD,qBAAqB,GACrB,cAAc;EACpB;EAEA1Q,YACUG,cAA8B,EAC9BD,MAAc,EACdmE,WAA4B,EAC5BjE,SAAuB,EACvBC,YAA0B;IAJ1B,KAAAF,cAAc,GAAdA,cAAc;IACd,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAmE,WAAW,GAAXA,WAAW;IACX,KAAAjE,SAAS,GAATA,SAAS;IACT,KAAAC,YAAY,GAAZA,YAAY;IAxBtB,KAAAkQ,QAAQ,GAAU,EAAE;IACpB,KAAA5B,gBAAgB,GAAU,EAAE;IAC5B,KAAArO,OAAO,GAAG,IAAI;IAEd,KAAA0P,YAAY,GAAG,KAAK,CAAC,CAAC;IAEtB;IACA,KAAAvB,aAAa,GAAG,KAAK;IACrB,KAAAZ,UAAU,GAAG,EAAE;IACf,KAAApH,gBAAgB,GAAG,EAAE;IACrB,KAAAiI,eAAe,GAAU,EAAE;EAexB;EAEHnO,QAAQA,CAAA;IACN,IAAI,CAACoQ,YAAY,EAAE;IAEnB;IACAxP,OAAO,CAACQ,GAAG,CAAC,gCAAgC,CAAC;IAC7C;EACF;;EAEAiP,eAAeA,CAAA;IACb;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACb,YAAY,GAAG,IAAI;IAC1B,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;;;EAGAc,eAAeA,CAAA;IACb,IAAI,CAACrC,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;IAExC;IACA,IAAI,CAAC,IAAI,CAACA,aAAa,EAAE;MACvB,IAAI,CAACjB,WAAW,EAAE;;EAEtB;EAEA;;;EAGAA,WAAWA,CAAA;IACT,IAAI,CAACK,UAAU,GAAG,EAAE;IACpB,IAAI,CAACpH,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACuH,cAAc,EAAE;EACvB;EAEA;;;EAGAA,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACH,UAAU,IAAI,CAAC,IAAI,CAACpH,gBAAgB,EAAE;MAC9C;MACA,IAAI,CAACkI,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAAC4B,QAAQ,CAAC;MAC1C;;IAGF;IACA,IAAI,CAAC5B,gBAAgB,GAAG,IAAI,CAAC4B,QAAQ,CAACQ,MAAM,CAACpT,OAAO,IAAG;MACrD;MACA,MAAMqT,iBAAiB,GAAG,CAAC,IAAI,CAACnD,UAAU,IACvClQ,OAAO,CAACsB,KAAK,IAAItB,OAAO,CAACsB,KAAK,CAACgS,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACrD,UAAU,CAACoD,WAAW,EAAE,CAAE,IACrFtT,OAAO,CAACwB,WAAW,IAAIxB,OAAO,CAACwB,WAAW,CAAC8R,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACrD,UAAU,CAACoD,WAAW,EAAE,CAAE;MAEpG;MACA,MAAME,eAAe,GAAG,CAAC,IAAI,CAAC1K,gBAAgB,IAC3C9I,OAAO,CAACiC,QAAQ,IAAIjC,OAAO,CAACiC,QAAQ,CAAC6B,GAAG,KAAK,IAAI,CAACgF,gBAAiB;MAEtE;MACA,OAAOuK,iBAAiB,IAAIG,eAAe;IAC7C,CAAC,CAAC;EACJ;EAIAR,YAAYA,CAAA;IACV,IAAI,CAACrQ,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC0P,YAAY,GAAG,KAAK,CAAC,CAAC;IAE3B,MAAM5K,MAAM,GAAG,IAAI,CAACf,WAAW,CAACgB,gBAAgB,EAAE;IAClD,MAAM+L,QAAQ,GAAG,IAAI,CAAC/M,WAAW,CAACqM,kBAAkB,EAAE;IAEtD,IAAI,CAACtL,MAAM,EAAE;MACX,IAAI,CAACjI,KAAK,GAAG,0BAA0B;MACvC,IAAI,CAACmD,OAAO,GAAG,KAAK;MACpB;;IAGF;IACA;IACA,MAAM+Q,iBAAiB,GAAGD,QAAQ,KAAK,OAAO,GAC1C,IAAI,CAACjR,cAAc,CAACmR,mBAAmB,EAAE,GACzC,IAAI,CAACnR,cAAc,CAACoR,qBAAqB,CAACnM,MAAM,CAAC;IAErDiM,iBAAiB,CAACvQ,SAAS,CAAC;MAC1BC,IAAI,EAAGC,QAAa,IAAI;QACtBG,OAAO,CAACQ,GAAG,CAAC,oBAAoB,EAAEX,QAAQ,CAAC;QAE3C;QACA,IAAI,CAAC7D,KAAK,GAAG,IAAI;QAEjB;QACA0T,UAAU,CAAC,MAAK;UACd;UACA,IAAIN,QAAQ,GAAGa,QAAQ,KAAK,OAAO,GAC9BpQ,QAAQ,CAACyE,IAAI,IAAIzE,QAAQ,CAACuP,QAAQ,IAAI,EAAE,GACxCvP,QAAQ,CAACuP,QAAQ,IAAI,EAAG;UAE7BpP,OAAO,CAACQ,GAAG,CAAC,iCAAiC,EAAE4O,QAAQ,CAAC;UACxDpP,OAAO,CAACQ,GAAG,CAAC,mCAAmC,EAAE4O,QAAQ,CAAC,CAAC,CAAC,CAAC;UAE7D;UACAA,QAAQ,GAAG,IAAI,CAACiB,kCAAkC,CAACjB,QAAQ,CAAC;UAE5D;UACA,IAAI,CAACA,QAAQ,GAAG,IAAI,CAACkB,mCAAmC,CAAClB,QAAQ,CAAC;UAElE;UACA,IAAI,CAAC5B,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAAC4B,QAAQ,CAAC;UAE1C;UACA,IAAI,CAACmB,sBAAsB,EAAE;UAE7B,IAAI,CAACpR,OAAO,GAAG,KAAK;UAEpB;UACAuQ,UAAU,CAAC,MAAK;YACd,IAAI,CAACb,YAAY,GAAG,IAAI;UAC1B,CAAC,EAAE,GAAG,CAAC;QACT,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;MACX,CAAC;;MACD7S,KAAK,EAAGA,KAAU,IAAI;QACpBgE,OAAO,CAAChE,KAAK,CAAC,mBAAmB,EAAEwU,IAAI,CAACC,SAAS,CAACzU,KAAK,CAAC,CAAC;QACzD,IAAI,CAACA,KAAK,GAAG,2CAA2CA,KAAK,CAAC+D,OAAO,IAAI/D,KAAK,CAAC0U,UAAU,IAAI,iBAAiB,EAAE;QAChH,IAAI,CAACvR,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEA6P,cAAcA,CAACC,MAAc;IAC3B,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,OAAO,2BAA2B;MACpD,KAAK,UAAU;QAAE,OAAO,+BAA+B;MACvD,KAAK,UAAU;QAAE,OAAO,6BAA6B;MACrD,KAAK,SAAS;QAAE,OAAO,yBAAyB;MAChD;QAAS,OAAO,2BAA2B;;EAE/C;EAEA5R,WAAWA,CAACiC,EAAS;IACnBU,OAAO,CAACQ,GAAG,CAAClB,EAAE,CAAC;IACb,IAAI,IAAI,CAAC8P,QAAQ,EAAE;MACnB,IAAI,CAACrQ,MAAM,CAACsB,QAAQ,CAAC,CAAC,oBAAoB,EAAEf,EAAE,CAAC,CAAC;;EAGpD;EAEA;;;;EAIA9B,aAAaA,CAAC8B,EAAU;IACtBU,OAAO,CAACQ,GAAG,CAAC,qDAAqD,EAAElB,EAAE,CAAC;IAEtE,IAAIiB,OAAO,CAAC,oDAAoD,CAAC,EAAE;MACjE,MAAM0P,QAAQ,GAAG,IAAI,CAAC/M,WAAW,CAACqM,kBAAkB,EAAE;MACtDvP,OAAO,CAACQ,GAAG,CAAC,sBAAsB,EAAEyP,QAAQ,CAAC;MAE7C;MACA,MAAMU,gBAAgB,GAAGV,QAAQ,KAAK,OAAO,GACzC,IAAI,CAACjR,cAAc,CAAC4R,kBAAkB,CAACtR,EAAE,CAAC,GAC1C,IAAI,CAACN,cAAc,CAACxB,aAAa,CAAC8B,EAAE,CAAC;MAEzCU,OAAO,CAACQ,GAAG,CAAC,0CAA0C,CAAC;MAEvDmQ,gBAAgB,CAAChR,SAAS,CAAC;QACzBC,IAAI,EAAGC,QAAQ,IAAI;UACjBG,OAAO,CAACQ,GAAG,CAAC,kCAAkC,EAAEX,QAAQ,CAAC;UACzD,IAAI,CAACgR,wBAAwB,CAACvR,EAAE,CAAC;QACnC,CAAC;QACDtD,KAAK,EAAGA,KAAK,IAAI;UACfgE,OAAO,CAAChE,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;UACxDgE,OAAO,CAAChE,KAAK,CAAC,0BAA0B,EAAE;YACxC0E,MAAM,EAAE1E,KAAK,CAAC0E,MAAM;YACpBgQ,UAAU,EAAE1U,KAAK,CAAC0U,UAAU;YAC5B3Q,OAAO,EAAE/D,KAAK,CAACA,KAAK,EAAE+D,OAAO;YAC7B+Q,SAAS,EAAE9U;WACZ,CAAC;UAEF;UACA;UACA,IAAIA,KAAK,CAAC0E,MAAM,KAAK,CAAC,IAAI1E,KAAK,CAAC0E,MAAM,KAAK,GAAG,EAAE;YAC9CV,OAAO,CAACQ,GAAG,CAAC,uGAAuG,CAAC;YACpH,IAAI,CAACqQ,wBAAwB,CAACvR,EAAE,CAAC;YACjC;;UAGF;UACA;UACA,IAAItD,KAAK,CAAC0E,MAAM,IAAI,GAAG,EAAE;YACvBV,OAAO,CAACQ,GAAG,CAAC,sEAAsE,CAAC;YACnFkP,UAAU,CAAC,MAAK;cACd,IAAI,CAACF,YAAY,EAAE;YACrB,CAAC,EAAE,IAAI,CAAC;;UAGV;UACA,IAAIxT,KAAK,CAAC0E,MAAM,KAAK,GAAG,EAAE;YACxB,IAAI,CAACxB,YAAY,CAACyB,YAAY,CAAC,yBAAyB,EAAE3E,KAAK,CAAC0E,MAAM,CAAC;WACxE,MAAM,IAAI1E,KAAK,CAAC0E,MAAM,KAAK,GAAG,EAAE;YAC/B,IAAI,CAACxB,YAAY,CAAClD,KAAK,CACrB,cAAc,EACd,qDAAqD,CACtD;WACF,MAAM;YACL,MAAM4E,YAAY,GAAG5E,KAAK,CAACA,KAAK,EAAE+D,OAAO,IAAI,6CAA6C;YAC1F,IAAI,CAACb,YAAY,CAAClD,KAAK,CACrB,uBAAuB,EACvB4E,YAAY,EACZ,IAAI,CACL;;QAEL;OACD,CAAC;KACH,MAAM;MACLZ,OAAO,CAACQ,GAAG,CAAC,0CAA0C,CAAC;;EAE3D;EAEQqQ,wBAAwBA,CAACvR,EAAU;IACzCU,OAAO,CAACQ,GAAG,CAAC,qDAAqD,EAAElB,EAAE,CAAC;IAEtE;IACA,MAAMyR,YAAY,GAAG,IAAI,CAAC3B,QAAQ,CAAC3B,MAAM;IACzC,IAAI,CAAC2B,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACQ,MAAM,CAACpT,OAAO,IAC1CA,OAAO,CAAC8D,GAAG,KAAKhB,EAAE,IAAI9C,OAAO,CAAC8C,EAAE,KAAKA,EAAE,CACxC;IACD,IAAI,CAACkO,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACoC,MAAM,CAACpT,OAAO,IAC1DA,OAAO,CAAC8D,GAAG,KAAKhB,EAAE,IAAI9C,OAAO,CAAC8C,EAAE,KAAKA,EAAE,CACxC;IAED,MAAM0R,UAAU,GAAG,IAAI,CAAC5B,QAAQ,CAAC3B,MAAM;IACvCzN,OAAO,CAACQ,GAAG,CAAC,kCAAkCuQ,YAAY,YAAYC,UAAU,EAAE,CAAC;IAEnF;IACA,IAAI,CAACT,sBAAsB,EAAE;IAE7B;IACA,IAAI,CAACrR,YAAY,CAACuB,OAAO,CACvB,mBAAmB,EACnB,wCAAwC,CACzC;IAEDT,OAAO,CAACQ,GAAG,CAAC,iDAAiD,CAAC;IAE9D;IACA,IAAI,CAACgP,YAAY,EAAE;EACrB;EAEAvP,iBAAiBA,CAACjC,WAAmB;IACnC,IAAI,CAACA,WAAW,EAAE,OAAO,IAAI,CAACiB,SAAS,CAACiB,uBAAuB,CAAC,EAAE,CAAC;IAEnE;IACA,MAAMC,aAAa,GAAGnC,WAAW,CAACoC,OAAO,CACvC,4BAA4B,EAC5B,wEAAwE,CACzE;IAED;IACA,OAAO,IAAI,CAACnB,SAAS,CAACiB,uBAAuB,CAACC,aAAa,CAAC;EAC9D;EAEA;;;;;EAKA2O,sBAAsBA,CAACtS,OAAY;IACjC,IAAI,CAACA,OAAO,CAACwB,WAAW,EAAE,OAAO,KAAK;IAEtC;IACA,MAAMiT,QAAQ,GAAG,CACf,uBAAuB,EACvB,uBAAuB,EACvB,cAAc,EACd,kBAAkB,EAClB,2BAA2B,EAC3B,2BAA2B,CAC5B;IAED;IACA,OAAOA,QAAQ,CAACC,IAAI,CAACC,OAAO,IAAIA,OAAO,CAACC,IAAI,CAAC5U,OAAO,CAACwB,WAAW,CAAC,CAAC;EACpE;EAEA;;;;;EAKAsS,mCAAmCA,CAAClB,QAAe;IACjD,IAAI,CAACA,QAAQ,IAAI,CAACA,QAAQ,CAAC3B,MAAM,EAAE,OAAO,EAAE;IAE5CzN,OAAO,CAACQ,GAAG,CAAC,iCAAiC,EAAE4O,QAAQ,CAAC3B,MAAM,CAAC;IAE/D;IACA2B,QAAQ,CAACiC,OAAO,CAAC,CAAC7U,OAAO,EAAE8U,KAAK,KAAI;MAClC,MAAMC,WAAW,GAAG,IAAI,CAACzC,sBAAsB,CAACtS,OAAO,CAAC;MACxDwD,OAAO,CAACQ,GAAG,CAAC,WAAW8Q,KAAK,GAAG,CAAC,aAAa9U,OAAO,CAACsB,KAAK,kBAAkBtB,OAAO,CAACwB,WAAW,2BAA2BuT,WAAW,EAAE,CAAC;IAC1I,CAAC,CAAC;IAEF;IACA,MAAMC,cAAc,GAAG,CAAC,GAAGpC,QAAQ,CAAC,CAACqC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACjD,MAAMC,uBAAuB,GAAG,IAAI,CAAC9C,sBAAsB,CAAC4C,CAAC,CAAC;MAC9D,MAAMG,uBAAuB,GAAG,IAAI,CAAC/C,sBAAsB,CAAC6C,CAAC,CAAC;MAE9D,IAAIC,uBAAuB,IAAI,CAACC,uBAAuB,EAAE;QACvD,OAAO,CAAC,CAAC,CAAC,CAAC;;;MAEb,IAAI,CAACD,uBAAuB,IAAIC,uBAAuB,EAAE;QACvD,OAAO,CAAC,CAAC,CAAC;;MAGZ;MACA,OAAO,IAAIjM,IAAI,CAAC+L,CAAC,CAACvT,IAAI,CAAC,CAAC0T,OAAO,EAAE,GAAG,IAAIlM,IAAI,CAAC8L,CAAC,CAACtT,IAAI,CAAC,CAAC0T,OAAO,EAAE;IAChE,CAAC,CAAC;IAEF9R,OAAO,CAACQ,GAAG,CAAC,iCAAiC,CAAC;IAC9CgR,cAAc,CAACH,OAAO,CAAC,CAAC7U,OAAO,EAAE8U,KAAK,KAAI;MACxC,MAAMC,WAAW,GAAG,IAAI,CAACzC,sBAAsB,CAACtS,OAAO,CAAC;MACxDwD,OAAO,CAACQ,GAAG,CAAC,YAAY8Q,KAAK,GAAG,CAAC,aAAa9U,OAAO,CAACsB,KAAK,2BAA2ByT,WAAW,EAAE,CAAC;IACtG,CAAC,CAAC;IAEF,OAAOC,cAAc;EACvB;EAEA;;;;;EAKA;;;EAGAjB,sBAAsBA,CAAA;IACpB;IACA,MAAMwB,YAAY,GAAG,IAAIC,GAAG,EAAE;IAE9B;IACA,IAAI,CAAC5C,QAAQ,CAACiC,OAAO,CAAC7U,OAAO,IAAG;MAC9B,IAAIA,OAAO,CAACiC,QAAQ,IAAIjC,OAAO,CAACiC,QAAQ,CAAC6B,GAAG,EAAE;QAC5C;QACA,IAAI,CAACyR,YAAY,CAACE,GAAG,CAACzV,OAAO,CAACiC,QAAQ,CAAC6B,GAAG,CAAC,EAAE;UAC3CyR,YAAY,CAACG,GAAG,CAAC1V,OAAO,CAACiC,QAAQ,CAAC6B,GAAG,EAAE;YACrChB,EAAE,EAAE9C,OAAO,CAACiC,QAAQ,CAAC6B,GAAG;YACxBxC,KAAK,EAAEtB,OAAO,CAACiC,QAAQ,CAACX;WACzB,CAAC;;;IAGR,CAAC,CAAC;IAEF;IACA,IAAI,CAACyP,eAAe,GAAG4E,KAAK,CAACC,IAAI,CAACL,YAAY,CAACM,MAAM,EAAE,CAAC;IAExD;IACA,IAAI,CAAC9E,eAAe,CAACkE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC5T,KAAK,CAACwU,aAAa,CAACX,CAAC,CAAC7T,KAAK,CAAC,CAAC;EACrE;EAEA;;;EAGAuS,kCAAkCA,CAACjB,QAAe;IAChD,IAAI,CAACA,QAAQ,IAAIA,QAAQ,CAAC3B,MAAM,KAAK,CAAC,EAAE,OAAO2B,QAAQ;IAEvD;IACA,MAAMmD,yBAAyB,GAAGnD,QAAQ,CAAC8B,IAAI,CAAC1U,OAAO,IAAI,IAAI,CAACsS,sBAAsB,CAACtS,OAAO,CAAC,CAAC;IAEhG;IACA,IAAI,CAAC+V,yBAAyB,EAAE;MAC9BvS,OAAO,CAACQ,GAAG,CAAC,yEAAyE,CAAC;MAEtF;MACA,IAAI4O,QAAQ,CAAC3B,MAAM,GAAG,CAAC,EAAE;QACvB,MAAMjR,OAAO,GAAG4S,QAAQ,CAAC,CAAC,CAAC;QAC3B5S,OAAO,CAACwB,WAAW,GAAGxB,OAAO,CAACwB,WAAW,GACrCxB,OAAO,CAACwB,WAAW,GAAG,yBAAyB,GAC/C,wBAAwB;QAC5BgC,OAAO,CAACQ,GAAG,CAAC,iDAAiDhE,OAAO,CAACsB,KAAK,EAAE,CAAC;;MAG/E;MACA,IAAIsR,QAAQ,CAAC3B,MAAM,IAAI,CAAC,EAAE;QACxB,MAAMjR,OAAO,GAAG4S,QAAQ,CAAC,CAAC,CAAC;QAC3B5S,OAAO,CAACwB,WAAW,GAAGxB,OAAO,CAACwB,WAAW,GACrCxB,OAAO,CAACwB,WAAW,GAAG,yBAAyB,GAC/C,wBAAwB;QAC5BgC,OAAO,CAACQ,GAAG,CAAC,iDAAiDhE,OAAO,CAACsB,KAAK,EAAE,CAAC;;;IAIjF,OAAOsR,QAAQ;EACjB;;;uBAjaWC,oBAAoB,EAAA9T,+DAAA,CAAAuF,4EAAA,GAAAvF,+DAAA,CAAA0F,mDAAA,GAAA1F,+DAAA,CAAA4F,8EAAA,GAAA5F,+DAAA,CAAA8F,mEAAA,GAAA9F,+DAAA,CAAA4K,wEAAA;IAAA;EAAA;;;YAApBkJ,oBAAoB;MAAA9N,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA6Q,8BAAA3Q,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChCjCtG,4DAAA,aAAmE;UAGLA,oDAAA,GAAe;UAAAA,0DAAA,EAAK;UAG5EA,4DAAA,aAAsB;UACZA,wDAAA,mBAAAkX,sDAAA;YAAA,OAAS3Q,GAAA,CAAA6N,eAAA,EAAiB;UAAA,EAAC;UACjCpU,4DAAA,EAAgG;UAAhGA,4DAAA,aAAgG;UAC9FA,uDAAA,cAAwH;UAC1HA,0DAAA,EAAM;UACNA,oDAAA,mBACF;UAAAA,0DAAA,EAAS;UAKbA,wDAAA,KAAAmX,oCAAA,kBAiDM;UAENnX,wDAAA,KAAAoX,oCAAA,iBAKM;UAGNpX,wDAAA,KAAAqX,oCAAA,kBAOM;UACRrX,0DAAA,EAAM;UAENA,wDAAA,KAAAsX,oCAAA,kBAGM;UAENtX,wDAAA,KAAAuX,oCAAA,kBAOM;UAENvX,wDAAA,KAAAwX,oCAAA,kBAQM;UAENxX,wDAAA,KAAAyX,oCAAA,kBA+FM;UACRzX,0DAAA,EAAM;;;UA3MwDA,uDAAA,GAAe;UAAfA,+DAAA,CAAAuG,GAAA,CAAAwN,SAAA,CAAe;UAcnE/T,uDAAA,GAAmB;UAAnBA,wDAAA,SAAAuG,GAAA,CAAAwL,aAAA,CAAmB;UAmDnB/R,uDAAA,GAAqC;UAArCA,wDAAA,UAAAuG,GAAA,CAAA3C,OAAA,IAAA2C,GAAA,CAAAsN,QAAA,CAAA3B,MAAA,KAAqC;UAQrClS,uDAAA,GAAoC;UAApCA,wDAAA,SAAAuG,GAAA,CAAA4K,UAAA,IAAA5K,GAAA,CAAAwD,gBAAA,CAAoC;UAUtC/J,uDAAA,GAAa;UAAbA,wDAAA,SAAAuG,GAAA,CAAA3C,OAAA,CAAa;UAKb5D,uDAAA,GAAW;UAAXA,wDAAA,SAAAuG,GAAA,CAAA9F,KAAA,CAAW;UASXT,uDAAA,GAAuC;UAAvCA,wDAAA,UAAAuG,GAAA,CAAA3C,OAAA,IAAA2C,GAAA,CAAAsN,QAAA,CAAA3B,MAAA,OAAuC;UAUvClS,uDAAA,GAAqC;UAArCA,wDAAA,UAAAuG,GAAA,CAAA3C,OAAA,IAAA2C,GAAA,CAAAsN,QAAA,CAAA3B,MAAA,KAAqC;;;;;;mBDjG/B,CACV5B,4DAAO,CAAC,QAAQ,EAAE,CAChBC,+DAAU,CAAC,QAAQ,EAAE,CACnBC,0DAAK,CAAC;UAAEkH,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAkB,CAAE,CAAC,EACpDlH,4DAAO,CAAC,eAAe,EAAED,0DAAK,CAAC;UAAEkH,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAe,CAAE,CAAC,CAAC,CAC5E,CAAC,CACH,CAAC,EACFrH,4DAAO,CAAC,aAAa,EAAE,CACrBC,+DAAU,CAAC,QAAQ,EAAE,CACnBG,0DAAK,CAAC,QAAQ,EAAE,CACdF,0DAAK,CAAC;UAAEkH,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAkB,CAAE,CAAC,EACpDhH,4DAAO,CAAC,OAAO,EAAE,CACfF,4DAAO,CAAC,eAAe,EAAED,0DAAK,CAAC;UAAEkH,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAe,CAAE,CAAC,CAAC,CAC5E,CAAC,CACH,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC,CACvB,CAAC,CACH,CAAC;MACH;IAAA;EAAA;;;;;;;;;;;;;;;;;;;;;AE7BoD;AACsB;AACA;AACM;AACR;;;AAE3E,MAAME,MAAM,GAAW,CAEnB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAElE,sFAAoBA;AAAA,CAAC,EAC5C;EAAEiE,IAAI,EAAE,iBAAiB;EAAEC,SAAS,EAAE9K,sFAAoBA;AAAA,CAAC,EAC3D;EAAE6K,IAAI,EAAE,oBAAoB;EAAEC,SAAS,EAAE3U,4FAAsBA;AAAA,CAAE,EACjE;EAAE0U,IAAI,EAAE,cAAc;EAAEC,SAAS,EAAEzQ,sFAAoBA;AAAA,CAAE,CAE1D;AAMG,MAAO0Q,qBAAqB;;;uBAArBA,qBAAqB;IAAA;EAAA;;;YAArBA;IAAqB;EAAA;;;gBAHtBJ,yDAAY,CAACK,QAAQ,CAACJ,MAAM,CAAC,EAC7BD,yDAAY;IAAA;EAAA;;;sHAEXI,qBAAqB;IAAAE,OAAA,GAAA5S,yDAAA;IAAA6S,OAAA,GAFtBP,yDAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;ACjBiC;AAES;AACW;AACM;AACN;AAC9B;AACmB;AAER;AACmB;;AAmBvE,MAAOa,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA;IAAc;EAAA;;;iBAFd,CAACJ,qDAAQ,CAAC;MAAAH,OAAA,GAPnBE,yDAAY,EACZJ,2EAAqB,EACrBJ,yDAAY,EACZU,uDAAW,EACXC,+DAAmB,EACnBC,4DAAW;IAAA;EAAA;;;sHAIFC,cAAc;IAAAC,YAAA,GAfvB7E,sFAAoB,EACpBzQ,4FAAsB,EACtB6J,sFAAoB,EACpB3F,sFAAoB;IAAA4Q,OAAA,GAGpBE,yDAAY,EACZJ,2EAAqB,EACrBJ,yDAAY,EACZU,uDAAW,EACXC,+DAAmB,EACnBC,4DAAW;EAAA;AAAA", "sources": ["./src/app/views/admin/reunions/reunion-detail/reunion-detail.component.ts", "./src/app/views/admin/reunions/reunion-detail/reunion-detail.component.html", "./src/app/views/admin/reunions/reunion-edit/reunion-edit.component.ts", "./src/app/views/admin/reunions/reunion-edit/reunion-edit.component.html", "./src/app/views/admin/reunions/reunion-form/reunion-form.component.ts", "./src/app/views/admin/reunions/reunion-form/reunion-form.component.html", "./src/app/views/admin/reunions/reunion-list/reunion-list.component.ts", "./src/app/views/admin/reunions/reunion-list/reunion-list.component.html", "./src/app/views/admin/reunions/reunions-routing.module.ts", "./src/app/views/admin/reunions/reunions.module.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { ReunionService } from '@app/services/reunion.service';\nimport { DomSanitizer, SafeHtml } from '@angular/platform-browser';\nimport { ToastService } from '@app/services/toast.service';\n\n@Component({\n  selector: 'app-reunion-detail',\n  templateUrl: './reunion-detail.component.html',\n  styleUrls: ['./reunion-detail.component.css']\n})\nexport class ReunionDetailComponent implements OnInit {\n  reunion: any = null;\n  loading = true;\n  error: string | null = null;\n\n  constructor(\n    private route: ActivatedRoute,\n    public router: Router,\n    private reunionService: ReunionService,\n    private sanitizer: DomSanitizer,\n    private toastService: ToastService\n  ) {}\n\n  ngOnInit(): void {\n    this.loadReunionDetails();\n  }\n\n  loadReunionDetails(): void {\n    const id = this.route.snapshot.paramMap.get('id');\n    if (!id) {\n      this.error = 'ID de réunion non fourni';\n      this.loading = false;\n      return;\n    }\n\n    this.reunionService.getReunionById(id).subscribe({\n      next: (response: any) => {\n        this.reunion = response.reunion;\n        this.loading = false;\n      },\n      error: (err: any) => {\n        this.error = err.error?.message || 'Erreur lors du chargement';\n        this.loading = false;\n        console.error('Erreur:', err);\n      }\n    });\n  }\n\n  formatDescription(description: string): SafeHtml {\n    if (!description) return this.sanitizer.bypassSecurityTrustHtml('');\n\n    // Recherche la chaîne \"(presence obligatoire)\" (insensible à la casse) et la remplace par une version en rouge\n    const formattedText = description.replace(\n      /\\(presence obligatoire\\)/gi,\n      '<span class=\"text-red-600 font-semibold\">(presence obligatoire)</span>'\n    );\n\n    // Sanitize le HTML pour éviter les problèmes de sécurité\n    return this.sanitizer.bypassSecurityTrustHtml(formattedText);\n  }\n\n  editReunion(): void {\n    if (this.reunion) {\n      this.router.navigate(['/reunions/edit', this.reunion._id]);\n    }\n  }\n\n  /**\n   * Supprime la réunion après confirmation\n   */\n  deleteReunion(): void {\n    if (!this.reunion) return;\n\n    if (confirm('Êtes-vous sûr de vouloir supprimer cette réunion ? Cette action est irréversible.')) {\n      this.reunionService.deleteReunion(this.reunion._id).subscribe({\n        next: (response) => {\n          console.log('Réunion supprimée avec succès:', response);\n\n          // Afficher le toast de succès\n          this.toastService.success(\n            'Réunion supprimée',\n            'La réunion a été supprimée avec succès'\n          );\n\n          // Rediriger vers la liste des réunions\n          this.router.navigate(['/reunions']);\n        },\n        error: (error) => {\n          console.error('Erreur lors de la suppression:', error);\n\n          // Gestion spécifique des erreurs d'autorisation\n          if (error.status === 403) {\n            this.toastService.accessDenied('supprimer cette réunion', error.status);\n          } else if (error.status === 401) {\n            this.toastService.error(\n              'Non autorisé',\n              'Vous devez être connecté pour supprimer une réunion'\n            );\n          } else {\n            const errorMessage = error.error?.message || 'Erreur lors de la suppression de la réunion';\n            this.toastService.error(\n              'Erreur de suppression',\n              errorMessage,\n              8000\n            );\n          }\n        }\n      });\n    }\n  }\n}", "<div class=\"container mx-auto px-4 py-6\">\n  <!-- Bouton retour -->\n  <button (click)=\"router.navigate(['/reunions'])\"\n          class=\"mb-4 flex items-center text-purple-600 hover:text-purple-800\">\n    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-1\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n      <path fill-rule=\"evenodd\" d=\"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\" clip-rule=\"evenodd\" />\n    </svg>\n    Retour aux réunions\n  </button>\n\n  <!-- Chargement -->\n  <div *ngIf=\"loading\" class=\"text-center py-8\">\n    <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto\"></div>\n  </div>\n\n  <!-- Erreur -->\n  <div *ngIf=\"error\" class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n    {{ error }}\n  </div>\n\n  <!-- Détails de la réunion -->\n  <div *ngIf=\"!loading && reunion\" class=\"bg-white rounded-lg shadow-md p-6\">\n    <!-- Titre de la réunion -->\n    <div class=\"flex justify-between items-start mb-4\">\n      <div>\n        <h1 class=\"text-2xl font-bold text-gray-800\">{{ reunion.titre }}</h1>\n        <p class=\"mt-1\" [innerHTML]=\"reunion.description | highlightPresence\"></p>\n      </div>\n      <div class=\"flex space-x-2\">\n        <button (click)=\"editReunion()\"\n                class=\"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors flex items-center\">\n          <svg class=\"h-4 w-4 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n          </svg>\n          Modifier\n        </button>\n        <button (click)=\"deleteReunion()\"\n                class=\"px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors flex items-center\">\n          <svg class=\"h-4 w-4 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n          </svg>\n          Supprimer\n        </button>\n      </div>\n    </div>\n\n    <!-- Date et heure -->\n    <div class=\"mb-6\">\n      <div class=\"flex items-center mb-2\">\n        <svg class=\"h-5 w-5 text-gray-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n        </svg>\n        <span class=\"text-gray-700\">\n          {{ reunion.date | date:'fullDate' }}, {{ reunion.heureDebut }} - {{ reunion.heureFin }}\n        </span>\n      </div>\n    </div>\n\n    <!-- Créateur -->\n    <div class=\"mb-4\">\n      <h2 class=\"text-lg font-semibold mb-2 text-gray-800\">Créateur:</h2>\n      <div class=\"flex items-center\">\n        <span class=\"text-gray-700\">{{ reunion.createur?.username }} ({{ reunion.createur?.email }})</span>\n      </div>\n    </div>\n\n    <!-- Participants -->\n    <div class=\"mb-6\">\n      <h2 class=\"text-lg font-semibold mb-2 text-gray-800\">Participants:</h2>\n      <ul class=\"list-disc pl-5\">\n        <li *ngFor=\"let participant of reunion.participants\" class=\"text-gray-700\">\n          {{ participant.username }} ({{ participant.email }})\n        </li>\n      </ul>\n    </div>\n\n    <!-- Planning -->\n    <div class=\"mb-6\">\n      <h2 class=\"text-lg font-semibold mb-2 text-gray-800\">Planning:</h2>\n      <div class=\"text-gray-700\">\n        <p>{{ reunion.planning?.titre }}</p>\n        <p>Du {{ reunion.planning?.dateDebut | date:'mediumDate' }} au {{ reunion.planning?.dateFin | date:'mediumDate' }}</p>\n      </div>\n    </div>\n\n    <!-- Lieu -->\n    <div *ngIf=\"reunion.lieu\" class=\"mb-6\">\n      <h2 class=\"text-lg font-semibold mb-2 text-gray-800\">Lieu:</h2>\n      <div class=\"flex items-center\">\n        <svg class=\"h-5 w-5 text-gray-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n        </svg>\n        <span class=\"text-gray-700\">{{ reunion.lieu }}</span>\n      </div>\n    </div>\n\n    <!-- Lien Visio -->\n    <div *ngIf=\"reunion.lienVisio\" class=\"mb-6\">\n      <h2 class=\"text-lg font-semibold mb-2 text-gray-800\">Lien Visio:</h2>\n      <a [href]=\"reunion.lienVisio\" class=\"text-blue-600 hover:underline flex items-center\" target=\"_blank\">\n        <svg class=\"h-5 w-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\" />\n        </svg>\n        Rejoindre la réunion\n      </a>\n    </div>\n  </div>\n</div>", "import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport {ReunionService} from \"@app/services/reunion.service\";\nimport {DataService} from \"@app/services/data.service\";\nimport {PlanningService} from \"@app/services/planning.service\";\nimport {Planning} from \"@app/models/planning.model\";\nimport {User} from \"@app/models/user.model\";\nimport {ToastService} from \"@app/services/toast.service\";\nimport {AuthuserService} from \"@app/services/authuser.service\";\nimport {RoleService} from \"@app/services/role.service\";\n\n@Component({\n  selector: 'app-reunion-edit',\n  templateUrl: './reunion-edit.component.html',\n  styleUrls: ['./reunion-edit.component.css']\n})\nexport class ReunionEditComponent implements OnInit {\n  reunionForm!: FormGroup;\n  reunionId!: string;\n  error: any = null;\n  isSubmitting = false;\n  users: User[] = [];\n  plannings: Planning[] = [];\n  currentReunionPlanning: Planning | null = null;\n  isAdmin = false;\n\n  constructor(\n    private fb: FormBuilder,\n    private route: ActivatedRoute,\n    private router: Router,\n    private reunionService: ReunionService,\n    private userService: DataService,\n    private planningService: PlanningService,\n    private toastService: ToastService,\n    private authService: AuthuserService,\n    private roleService: RoleService\n  ) {}\n\n  ngOnInit(): void {\n    this.reunionId = this.route.snapshot.paramMap.get('id')!;\n    this.checkUserRole();\n    this.initForm();\n    this.fetchUsers();\n    this.fetchPlannings();\n    this.loadReunion();\n  }\n\n  checkUserRole(): void {\n    this.isAdmin = this.roleService.isAdmin();\n    console.log('🔍 Utilisateur admin:', this.isAdmin);\n  }\n\n  initForm(): void {\n    this.reunionForm = this.fb.group({\n      titre: ['', Validators.required],\n      description: [''],\n      date: ['', Validators.required],\n      heureDebut: ['', Validators.required],\n      heureFin: ['', Validators.required],\n      lieu: [''],\n      lienVisio: [''],\n      planning: ['', Validators.required],\n      participants: [[]]\n    });\n  }\n\n  fetchUsers(): void {\n    this.userService.getAllUsers().subscribe((users:any) => {\n      this.users = users;\n    });\n  }\n\n  fetchPlannings(): void {\n    const userId = this.authService.getCurrentUserId();\n    if (!userId) return;\n\n    // Si admin, récupérer tous les plannings, sinon seulement ceux de l'utilisateur\n    const planningsObservable = this.isAdmin\n      ? this.planningService.getAllPlanningsAdmin()\n      : this.planningService.getPlanningsByUser(userId);\n\n    planningsObservable.subscribe({\n      next: (response: any) => {\n        // Adapter la réponse selon l'endpoint utilisé\n        if (this.isAdmin) {\n          this.plannings = response.data || [];\n          console.log('🔍 Tous les plannings (admin) récupérés:', this.plannings);\n        } else {\n          this.plannings = response.plannings || [];\n          console.log('🔍 Plannings utilisateur récupérés:', this.plannings);\n        }\n      },\n      error: (err) => {\n        console.error('❌ Erreur chargement plannings:', err);\n        this.toastService.error(\n          'Erreur',\n          'Impossible de récupérer les plannings'\n        );\n      }\n    });\n  }\n\n  loadReunion(): void {\n    this.reunionService.getReunionById(this.reunionId).subscribe({\n      next: (reunion: any) => {\n        // Stocker le planning actuel de la réunion\n        this.currentReunionPlanning = reunion.reunion.planning;\n\n        this.reunionForm.patchValue({\n          titre: reunion.reunion.titre,\n          description: reunion.reunion.description,\n          date: reunion.reunion.date?.split('T')[0],\n          heureDebut: reunion.reunion.heureDebut,\n          heureFin: reunion.reunion.heureFin,\n          lieu: reunion.reunion.lieu,\n          lienVisio: reunion.reunion.lienVisio,\n          planning: reunion.reunion.planning?.id || reunion.reunion.planning?._id,\n          participants: reunion.reunion.participants?.map((p:any) => p._id)\n        });\n\n        // Désactiver le champ planning en mode édition\n        this.reunionForm.get('planning')?.disable();\n\n        console.log('🔍 Réunion chargée:', reunion.reunion);\n        console.log('🔍 Planning actuel:', this.currentReunionPlanning);\n      },\n      error: (err) => {\n        console.error('Erreur lors du chargement de la réunion:', err);\n        if (err.status === 403) {\n          this.toastService.accessDenied('accéder à cette réunion', err.status);\n        } else if (err.status === 404) {\n          this.toastService.error(\n            'Réunion introuvable',\n            'La réunion demandée n\\'existe pas ou a été supprimée'\n          );\n        } else {\n          const errorMessage = err.error?.message || 'Erreur lors du chargement de la réunion';\n          this.toastService.error(\n            'Erreur de chargement',\n            errorMessage\n          );\n        }\n      }\n    });\n  }\n\n  onSubmit(): void {\n    if (this.reunionForm.invalid) {\n      this.toastService.warning(\n        'Formulaire invalide',\n        'Veuillez corriger les erreurs avant de soumettre le formulaire'\n      );\n      return;\n    }\n\n    // Validation de la date par rapport au planning\n    if (!this.validateDateInPlanningRange()) {\n      return;\n    }\n\n    this.isSubmitting = true;\n    const reunion: any = this.reunionForm.value;\n\n    // Inclure le planning même s'il est désactivé\n    if (this.currentReunionPlanning) {\n      reunion.planning = this.currentReunionPlanning._id || this.currentReunionPlanning.id;\n    }\n\n    console.log('🔍 Données de la réunion à mettre à jour:', reunion);\n\n    this.reunionService.updateReunion(this.reunionId, reunion).subscribe({\n      next: () => {\n        this.isSubmitting = false;\n        this.toastService.success(\n          'Réunion mise à jour',\n          'La réunion a été modifiée avec succès'\n        );\n        this.router.navigate(['/reunions']);\n      },\n      error: (err) => {\n        this.isSubmitting = false;\n        console.error('Erreur lors de la mise à jour de la réunion:', err);\n\n        if (err.status === 403) {\n          this.toastService.accessDenied('modifier cette réunion', err.status);\n        } else if (err.status === 401) {\n          this.toastService.error(\n            'Non autorisé',\n            'Vous devez être connecté pour effectuer cette action'\n          );\n        } else {\n          const errorMessage = err.error?.message || 'Erreur lors de la mise à jour de la réunion';\n          this.toastService.error(\n            'Erreur de mise à jour',\n            errorMessage,\n            8000\n          );\n        }\n      }\n    });\n  }\n\n  goReunion(): void {\n    this.router.navigate(['/reunions']);\n  }\n\n  /**\n   * Valide que la date de la réunion est dans l'intervalle du planning sélectionné\n   */\n  validateDateInPlanningRange(): boolean {\n    const formValue = this.reunionForm.value;\n    const reunionDate = formValue.date;\n    const planningId = formValue.planning;\n\n    if (!reunionDate || !planningId) {\n      return true; // Si pas de date ou planning, laisser la validation backend gérer\n    }\n\n    // Trouver le planning sélectionné\n    const selectedPlanning = this.plannings.find(p => p._id === planningId);\n    if (!selectedPlanning) {\n      console.warn('⚠️ Planning non trouvé dans la liste locale, tentative de récupération depuis le serveur');\n      // Si le planning n'est pas trouvé dans la liste locale, essayer de le récupérer\n      // Cela peut arriver si l'utilisateur modifie une réunion d'un planning dont il n'est que participant\n      this.planningService.getPlanningById(planningId).subscribe({\n        next: (response: any) => {\n          const planning = response.planning;\n          if (planning) {\n            // Ajouter le planning à la liste locale pour éviter de futures requêtes\n            this.plannings.push(planning);\n            console.log('✅ Planning récupéré et ajouté à la liste locale:', planning);\n          }\n        },\n        error: (err) => {\n          console.error('❌ Erreur lors de la récupération du planning:', err);\n          this.toastService.error(\n            'Planning introuvable',\n            'Le planning sélectionné n\\'existe pas ou vous n\\'avez pas les permissions pour y accéder'\n          );\n        }\n      });\n      // Pour cette validation, on retourne true et on laisse le backend gérer\n      return true;\n    }\n\n    // Convertir les dates pour comparaison\n    const reunionDateObj = new Date(reunionDate);\n    const planningDateDebut = new Date(selectedPlanning.dateDebut);\n    const planningDateFin = new Date(selectedPlanning.dateFin);\n\n    // Comparer seulement les dates (sans les heures)\n    reunionDateObj.setHours(0, 0, 0, 0);\n    planningDateDebut.setHours(0, 0, 0, 0);\n    planningDateFin.setHours(0, 0, 0, 0);\n\n    if (reunionDateObj < planningDateDebut || reunionDateObj > planningDateFin) {\n      this.toastService.error(\n        'Date invalide',\n        `La date de la réunion doit être comprise entre le ${planningDateDebut.toLocaleDateString('fr-FR')} et le ${planningDateFin.toLocaleDateString('fr-FR')} (période du planning \"${selectedPlanning.titre}\")`,\n        10000\n      );\n      return false;\n    }\n\n    return true;\n  }\n}", "<div class=\"container mx-auto px-4 py-6 max-w-3xl\">\n  <!-- En-tête avec gradient coloré -->\n  <div class=\"bg-gradient-to-r from-purple-600 to-indigo-600 rounded-t-lg p-6 text-white mb-0\">\n    <h1 class=\"text-2xl font-bold flex items-center\">\n      <i class=\"fas fa-edit mr-3 text-purple-200\"></i>\n      Modifier la Réunion\n    </h1>\n    <p class=\"text-purple-100 mt-2\">Modifiez les détails de votre réunion</p>\n  </div>\n\n  <form [formGroup]=\"reunionForm\" (ngSubmit)=\"onSubmit()\" class=\"bg-white rounded-b-lg shadow-lg p-6 border-t-0\">\n    <div *ngIf=\"error\" class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n      {{ error.message || 'Une erreur est survenue' }}\n    </div>\n\n    <div class=\"grid grid-cols-1 gap-6\">\n      <!-- Titre -->\n      <div class=\"relative\">\n        <label for=\"titre\" class=\"block text-sm font-medium text-purple-700 mb-2\">\n          <i class=\"fas fa-tag mr-2 text-purple-500\"></i>\n          Titre *\n        </label>\n        <input id=\"titre\" type=\"text\" formControlName=\"titre\"\n               class=\"mt-1 block w-full rounded-lg border-2 border-purple-200 shadow-sm focus:border-purple-500 focus:ring-purple-500 focus:ring-2 transition-all duration-200 px-4 py-3\">\n        <div *ngIf=\"reunionForm.get('titre')?.invalid && reunionForm.get('titre')?.touched\"\n             class=\"text-red-500 text-sm mt-2 flex items-center\">\n          <i class=\"fas fa-exclamation-circle mr-1\"></i>\n          Le titre est obligatoire\n        </div>\n      </div>\n\n      <!-- Description -->\n      <div class=\"relative\">\n        <label for=\"description\" class=\"block text-sm font-medium text-indigo-700 mb-2\">\n          <i class=\"fas fa-align-left mr-2 text-indigo-500\"></i>\n          Description\n        </label>\n        <textarea id=\"description\" formControlName=\"description\" rows=\"3\"\n                  class=\"mt-1 block w-full rounded-lg border-2 border-indigo-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 focus:ring-2 transition-all duration-200 px-4 py-3\"\n                  placeholder=\"Décrivez votre réunion...\"></textarea>\n      </div>\n\n      <!-- Date and Time -->\n      <div class=\"bg-gradient-to-r from-blue-50 to-cyan-50 p-4 rounded-lg border border-blue-200\">\n        <h3 class=\"text-lg font-semibold text-blue-800 mb-4 flex items-center\">\n          <i class=\"fas fa-calendar-clock mr-2 text-blue-600\"></i>\n          Planification\n        </h3>\n        <div class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div>\n            <label for=\"date\" class=\"block text-sm font-medium text-blue-700 mb-2\">\n              <i class=\"fas fa-calendar mr-2 text-blue-500\"></i>\n              Date *\n            </label>\n            <input id=\"date\" type=\"date\" formControlName=\"date\"\n                   class=\"mt-1 block w-full rounded-lg border-2 border-blue-200 shadow-sm focus:border-blue-500 focus:ring-blue-500 focus:ring-2 transition-all duration-200 px-4 py-3\">\n            <div *ngIf=\"reunionForm.get('date')?.invalid && reunionForm.get('date')?.touched\"\n                 class=\"text-red-500 text-sm mt-2 flex items-center\">\n              <i class=\"fas fa-exclamation-circle mr-1\"></i>\n              La date est obligatoire\n            </div>\n          </div>\n\n          <div>\n            <label for=\"heureDebut\" class=\"block text-sm font-medium text-green-700 mb-2\">\n              <i class=\"fas fa-play mr-2 text-green-500\"></i>\n              Heure de début *\n            </label>\n            <input id=\"heureDebut\" type=\"time\" formControlName=\"heureDebut\"\n                   class=\"mt-1 block w-full rounded-lg border-2 border-green-200 shadow-sm focus:border-green-500 focus:ring-green-500 focus:ring-2 transition-all duration-200 px-4 py-3\">\n            <div *ngIf=\"reunionForm.get('heureDebut')?.invalid && reunionForm.get('heureDebut')?.touched\"\n                 class=\"text-red-500 text-sm mt-2 flex items-center\">\n              <i class=\"fas fa-exclamation-circle mr-1\"></i>\n              L'heure de début est obligatoire\n            </div>\n          </div>\n\n          <div>\n            <label for=\"heureFin\" class=\"block text-sm font-medium text-red-700 mb-2\">\n              <i class=\"fas fa-stop mr-2 text-red-500\"></i>\n              Heure de fin *\n            </label>\n            <input id=\"heureFin\" type=\"time\" formControlName=\"heureFin\"\n                   class=\"mt-1 block w-full rounded-lg border-2 border-red-200 shadow-sm focus:border-red-500 focus:ring-red-500 focus:ring-2 transition-all duration-200 px-4 py-3\">\n            <div *ngIf=\"reunionForm.get('heureFin')?.invalid && reunionForm.get('heureFin')?.touched\"\n                 class=\"text-red-500 text-sm mt-2 flex items-center\">\n              <i class=\"fas fa-exclamation-circle mr-1\"></i>\n              L'heure de fin est obligatoire\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Lieu / Lien visio -->\n      <div class=\"bg-gradient-to-r from-orange-50 to-yellow-50 p-4 rounded-lg border border-orange-200\">\n        <h3 class=\"text-lg font-semibold text-orange-800 mb-4 flex items-center\">\n          <i class=\"fas fa-map-marker-alt mr-2 text-orange-600\"></i>\n          Localisation\n        </h3>\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div>\n            <label for=\"lieu\" class=\"block text-sm font-medium text-orange-700 mb-2\">\n              <i class=\"fas fa-map-marker-alt mr-2 text-orange-500\"></i>\n              Lieu / Salle\n            </label>\n            <input id=\"lieu\" type=\"text\" formControlName=\"lieu\"\n                   class=\"mt-1 block w-full rounded-lg border-2 border-orange-200 shadow-sm focus:border-orange-500 focus:ring-orange-500 focus:ring-2 transition-all duration-200 px-4 py-3\"\n                   placeholder=\"Salle 101, Bureau A, Google Meet...\">\n          </div>\n\n          <div>\n            <label for=\"lienVisio\" class=\"block text-sm font-medium text-cyan-700 mb-2\">\n              <i class=\"fas fa-video mr-2 text-cyan-500\"></i>\n              Lien Visio\n            </label>\n            <input id=\"lienVisio\" type=\"url\" formControlName=\"lienVisio\"\n                   class=\"mt-1 block w-full rounded-lg border-2 border-cyan-200 shadow-sm focus:border-cyan-500 focus:ring-cyan-500 focus:ring-2 transition-all duration-200 px-4 py-3\"\n                   placeholder=\"https://meet.google.com/...\">\n          </div>\n        </div>\n      </div>\n\n      <!-- Planning (désactivé en mode édition) -->\n      <div>\n        <label for=\"planning\" class=\"block text-sm font-medium text-gray-700\">Planning *</label>\n\n        <!-- Affichage du planning actuel (non-cliquable) -->\n        <div *ngIf=\"currentReunionPlanning\"\n             class=\"mt-1 block w-full px-4 py-3 bg-gradient-to-r from-purple-50 to-indigo-50 border-2 border-purple-200 rounded-lg shadow-sm\">\n          <div class=\"flex items-center justify-between\">\n            <span class=\"font-semibold text-purple-800 text-lg\">\n              <i class=\"fas fa-calendar-alt mr-2 text-purple-600\"></i>\n              {{ currentReunionPlanning.titre }}\n            </span>\n            <span class=\"text-sm font-medium text-red-600 bg-red-50 px-2 py-1 rounded-full border border-red-200\">\n              <i class=\"fas fa-clock mr-1\"></i>\n              {{ currentReunionPlanning.dateDebut | date:'dd/MM/yyyy' }} -\n              {{ currentReunionPlanning.dateFin | date:'dd/MM/yyyy' }}\n            </span>\n          </div>\n          <div *ngIf=\"currentReunionPlanning.description\" class=\"text-sm text-indigo-700 mt-2 bg-indigo-50 p-2 rounded border-l-4 border-indigo-300\">\n            <i class=\"fas fa-info-circle mr-1\"></i>\n            {{ currentReunionPlanning.description }}\n          </div>\n        </div>\n\n        <!-- Select caché pour maintenir la validation du formulaire -->\n        <select id=\"planning\" formControlName=\"planning\" class=\"hidden\">\n          <option value=\"\">Sélectionnez un planning</option>\n          <option *ngFor=\"let planning of plannings\" [value]=\"planning._id\">{{ planning.titre }}</option>\n        </select>\n\n        <div class=\"text-sm text-purple-600 mt-3 bg-purple-50 p-3 rounded-lg border border-purple-200 flex items-center\">\n          <i class=\"fas fa-lock mr-2 text-purple-500\"></i>\n          <span class=\"font-medium\">Le planning ne peut pas être modifié lors de l'édition d'une réunion</span>\n        </div>\n      </div>\n\n      <!-- Participants -->\n      <div class=\"bg-gradient-to-r from-emerald-50 to-teal-50 p-4 rounded-lg border border-emerald-200\">\n        <h3 class=\"text-lg font-semibold text-emerald-800 mb-4 flex items-center\">\n          <i class=\"fas fa-users mr-2 text-emerald-600\"></i>\n          Participants\n        </h3>\n        <label class=\"block text-sm font-medium text-emerald-700 mb-2\">\n          <i class=\"fas fa-user-friends mr-2 text-emerald-500\"></i>\n          Sélectionnez les participants\n        </label>\n        <select formControlName=\"participants\" multiple\n                class=\"mt-1 block w-full px-4 py-3 border-2 border-emerald-200 rounded-lg shadow-sm focus:ring-emerald-500 focus:border-emerald-500 focus:ring-2 transition-all duration-200 text-sm min-h-[120px]\">\n          <ng-container *ngIf=\"users\">\n            <option *ngFor=\"let user of users\" [value]=\"user._id\" class=\"py-2\">\n              <i class=\"fas fa-user mr-2\"></i>{{ user.username }}\n            </option>\n          </ng-container>\n        </select>\n        <p class=\"text-xs text-emerald-600 mt-2\">\n          <i class=\"fas fa-info-circle mr-1\"></i>\n          Maintenez Ctrl (ou Cmd) pour sélectionner plusieurs participants\n        </p>\n      </div>\n    </div>\n\n    <!-- Boutons d'action avec design amélioré -->\n    <div class=\"mt-8 flex justify-end space-x-4 bg-gray-50 p-4 rounded-lg border-t border-gray-200\">\n      <button type=\"button\" (click)=\"goReunion()\"\n              class=\"px-6 py-3 border-2 border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-100 hover:border-gray-400 transition-all duration-200 flex items-center\">\n        <i class=\"fas fa-times mr-2\"></i>\n        Annuler\n      </button>\n      <button type=\"submit\" [disabled]=\"reunionForm.invalid || isSubmitting\"\n              class=\"px-6 py-3 rounded-lg text-sm font-medium text-white bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center shadow-lg\">\n        <i class=\"fas fa-save mr-2\" *ngIf=\"!isSubmitting\"></i>\n        <i class=\"fas fa-spinner fa-spin mr-2\" *ngIf=\"isSubmitting\"></i>\n        {{ isSubmitting ? 'Enregistrement...' : 'Enregistrer les modifications' }}\n      </button>\n    </div>\n  </form>\n</div>", "import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { ReunionService } from 'src/app/services/reunion.service';\nimport { PlanningService } from 'src/app/services/planning.service';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { Reunion } from 'src/app/models/reunion.model';\nimport { Planning } from 'src/app/models/planning.model';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport {Observable} from \"rxjs\";\nimport {User} from \"@app/models/user.model\";\nimport {DataService} from \"@app/services/data.service\";\nimport { debounceTime, distinctUntilChanged } from 'rxjs/operators';\nimport { ToastService } from 'src/app/services/toast.service';\n\n@Component({\n  selector: 'app-reunion-form',\n  templateUrl: './reunion-form.component.html',\n  styleUrls: ['./reunion-form.component.css']\n})\nexport class ReunionFormComponent implements OnInit {\n  reunionForm: FormGroup;\n  plannings: Planning[] = [];\n  users$: Observable<User[]>;\n  loading = true;\n  isSubmitting = false;\n  error: any = null;\n  successMessage: string | null = null;\n  isEditMode = false;\n  currentReunionId: string | null = null;\n  planningIdFromUrl: string | null = null;\n  selectedPlanning: Planning | null = null;\n  lienVisioError: string | null = null;\n  isCheckingLienVisio = false;\n\n  constructor(\n    private fb: FormBuilder,\n    private reunionService: ReunionService,\n    private planningService: PlanningService,\n    private userService: DataService,\n    private route: ActivatedRoute,\n    private router: Router,\n    private authService: AuthuserService,\n    private toastService: ToastService\n  ) {\n    this.reunionForm = this.fb.group({\n      titre: ['', Validators.required],\n      description: [''],\n      date: ['', Validators.required],\n      heureDebut: ['', Validators.required],\n      heureFin: ['', Validators.required],\n      lieu: [''],\n      lienVisio: [''],\n      planning: ['', Validators.required],\n      participants: [[]]\n    });\n\n\n    this.users$ = this.userService.getAllUsers();\n  }\n\n  ngOnInit(): void {\n    this.loadPlannings();\n    this.checkEditMode();\n    this.checkPlanningParam();\n    this.setupLienVisioValidation();\n  }\n\n  checkEditMode(): void {\n    const reunionId = this.route.snapshot.paramMap.get('id');\n    if (reunionId) {\n      this.isEditMode = true;\n      this.currentReunionId = reunionId;\n      this.loadReunion(reunionId);\n    }\n  }\n\n  loadPlannings(): void {\n    const userId = this.authService.getCurrentUserId();\n    if (!userId) return;\n\n    this.planningService.getPlanningsByUser(userId).subscribe({\n      next: (response:any) => {\n        this.plannings = response.plannings || [];\n        console.log('🔍 Plannings chargés:', this.plannings);\n        console.log('🔍 Premier planning:', this.plannings[0]);\n      },\n      error: (err) => {\n        this.error = err;\n        console.error('❌ Erreur chargement plannings:', err);\n      }\n    });\n  }\n\n  loadReunion(id: string): void {\n    this.reunionService.getReunionById(id).subscribe({\n      next: (reunion) => {\n        this.reunionForm.patchValue({\n          titre: reunion.titre,\n          description: reunion.description,\n          dateDebut: this.formatDateForInput(reunion.dateDebut),\n          dateFin: this.formatDateForInput(reunion.dateFin),\n          lieu: reunion.lieu,\n          lienVisio: reunion.lienVisio,\n          planningId: reunion.planningId,\n          participants: reunion.participants\n        });\n        this.loading = false;\n      },\n      error: (err) => {\n        this.error = err;\n        this.loading = false;\n      }\n    });\n  }\n\n  formatDateForInput(date: Date | string): string {\n    return new Date(date).toISOString().slice(0, 16); // yyyy-MM-ddTHH:mm\n  }\n\n  checkPlanningParam(): void {\n    const planningId = this.route.snapshot.queryParamMap.get('planningId');\n    if (planningId) {\n      this.planningIdFromUrl = planningId;\n\n      // Si un ID de planning est fourni dans les paramètres de requête, le sélectionner automatiquement\n      this.reunionForm.patchValue({\n        planning: planningId\n      });\n\n      // Récupérer les détails du planning pour l'affichage\n      this.planningService.getPlanningById(planningId).subscribe({\n        next: (response: any) => {\n          this.selectedPlanning = response.planning;\n          // Ajouter le planning à la liste locale pour la validation\n          if (this.selectedPlanning && !this.plannings.find(p => p._id === planningId)) {\n            this.plannings.push(this.selectedPlanning);\n            console.log('✅ Planning ajouté à la liste locale pour validation:', this.selectedPlanning);\n          }\n        },\n        error: (err) => {\n          console.error('Erreur lors de la récupération du planning:', err);\n          this.toastService.error(\n            'Planning introuvable',\n            'Le planning sélectionné n\\'existe pas ou vous n\\'avez pas les permissions pour y accéder'\n          );\n        }\n      });\n    }\n  }\n\n  onSubmit(): void {\n    if (this.reunionForm.invalid || !this.canSubmit()) {\n      this.toastService.warning(\n        'Formulaire invalide',\n        'Veuillez corriger les erreurs avant de soumettre le formulaire'\n      );\n      return;\n    }\n\n    // Validation de la date par rapport au planning\n    if (!this.validateDateInPlanningRange()) {\n      return;\n    }\n\n    this.isSubmitting = true;\n    this.error = null;\n    this.successMessage = null;\n    const formValue = this.reunionForm.value;\n\n    const date = formValue.date; // already in yyyy-MM-dd format from input[type=date]\n    const heureDebut = formValue.heureDebut; // already in HH:mm format from input[type=time]\n    const heureFin = formValue.heureFin;\n\n    const reunionData: any = {\n      titre: formValue.titre,\n      description: formValue.description,\n      date: date,\n      heureDebut: heureDebut,\n      heureFin: heureFin,\n      lieu: formValue.lieu,\n      lienVisio: formValue.lienVisio,\n      planning: formValue.planning,\n      participants: formValue.participants || []\n    };\n\n    console.log('🔍 Données de la réunion à envoyer:', reunionData);\n    console.log('🔍 Planning ID sélectionné:', formValue.planning);\n    console.log('🔍 Type du planning ID:', typeof formValue.planning);\n    console.log('🔍 Plannings disponibles:', this.plannings);\n\n    this.reunionService.createReunion(reunionData).subscribe({\n      next: () => {\n        this.isSubmitting = false;\n\n        this.toastService.success(\n          'Réunion créée',\n          'La réunion a été créée avec succès'\n        );\n\n        // Réinitialiser le formulaire pour permettre la création d'une nouvelle réunion\n        this.resetForm();\n\n        // Redirection immédiate\n        this.router.navigate(['/reunions']);\n      },\n      error: (err) => {\n        this.isSubmitting = false;\n        console.error('Erreur lors de la création de la réunion:', err);\n\n        if (err.status === 403) {\n          this.toastService.accessDenied('créer une réunion', err.status);\n        } else if (err.status === 401) {\n          this.toastService.error(\n            'Non autorisé',\n            'Vous devez être connecté pour créer une réunion'\n          );\n        } else {\n          const errorMessage = err.error?.message || 'Erreur lors de la création de la réunion';\n          this.toastService.error(\n            'Erreur de création',\n            errorMessage,\n            8000\n          );\n        }\n      }\n    });\n  }\n\n  resetForm(): void {\n    // Reset the form to its initial state\n    this.reunionForm.reset({\n      titre: '',\n      description: '',\n      date: '',\n      heureDebut: '',\n      heureFin: '',\n      lieu: '',\n      lienVisio: '',\n      planning: '',\n      participants: []\n    });\n\n    // Mark the form as pristine and untouched to reset validation states\n    this.reunionForm.markAsPristine();\n    this.reunionForm.markAsUntouched();\n  }\n\n\n  goReunion(): void {\n    this.router.navigate(['/reunions']);\n  }\n\n  /**\n   * Configure la validation en temps réel du lien visio avec debounce\n   */\n  setupLienVisioValidation(): void {\n    this.reunionForm.get('lienVisio')?.valueChanges\n      .pipe(\n        debounceTime(500), // Attendre 500ms après la dernière saisie\n        distinctUntilChanged() // Ne déclencher que si la valeur a changé\n      )\n      .subscribe(value => {\n        if (value && value.trim() !== '') {\n          this.checkLienVisioUniqueness(value.trim());\n        } else {\n          this.lienVisioError = null;\n        }\n      });\n  }\n\n  /**\n   * Vérifie l'unicité du lien visio\n   * @param lienVisio Le lien à vérifier\n   */\n  checkLienVisioUniqueness(lienVisio: string): void {\n    if (!lienVisio || lienVisio.trim() === '') {\n      this.lienVisioError = null;\n      return;\n    }\n\n    this.isCheckingLienVisio = true;\n    this.lienVisioError = null;\n\n    // Utiliser la nouvelle route dédiée pour vérifier l'unicité\n    this.reunionService.checkLienVisioUniqueness(lienVisio, this.currentReunionId || undefined).subscribe({\n      next: (response) => {\n        this.isCheckingLienVisio = false;\n\n        if (response.success && !response.isUnique) {\n          this.lienVisioError = `Ce lien est déjà utilisé par la réunion \"${response.conflictWith?.titre}\"`;\n        } else {\n          this.lienVisioError = null;\n        }\n      },\n      error: (error) => {\n        this.isCheckingLienVisio = false;\n        console.error('Erreur lors de la vérification du lien visio:', error);\n        this.lienVisioError = 'Erreur lors de la vérification du lien';\n      }\n    });\n  }\n\n  /**\n   * Vérifie si le formulaire peut être soumis\n   */\n  canSubmit(): boolean {\n    return this.reunionForm.valid && !this.lienVisioError && !this.isCheckingLienVisio;\n  }\n\n  /**\n   * Valide que la date de la réunion est dans l'intervalle du planning sélectionné\n   */\n  validateDateInPlanningRange(): boolean {\n    const formValue = this.reunionForm.value;\n    const reunionDate = formValue.date;\n    const planningId = formValue.planning;\n\n    if (!reunionDate || !planningId) {\n      return true; // Si pas de date ou planning, laisser la validation backend gérer\n    }\n\n    // Chercher d'abord dans la liste locale, puis dans selectedPlanning\n    let selectedPlanning = this.plannings.find(p => p._id === planningId);\n\n    if (!selectedPlanning && this.selectedPlanning && this.selectedPlanning._id === planningId) {\n      selectedPlanning = this.selectedPlanning;\n    }\n\n    if (!selectedPlanning) {\n      console.warn('⚠️ Planning non trouvé pour validation:', planningId);\n      console.log('📋 Plannings disponibles:', this.plannings.map(p => ({ id: p._id, titre: p.titre })));\n      console.log('🎯 Selected planning:', this.selectedPlanning);\n\n      // Ne pas bloquer si le planning n'est pas trouvé - laisser le backend valider\n      return true;\n    }\n\n    // Convertir les dates pour comparaison\n    const reunionDateObj = new Date(reunionDate);\n    const planningDateDebut = new Date(selectedPlanning.dateDebut);\n    const planningDateFin = new Date(selectedPlanning.dateFin);\n\n    // Comparer seulement les dates (sans les heures)\n    reunionDateObj.setHours(0, 0, 0, 0);\n    planningDateDebut.setHours(0, 0, 0, 0);\n    planningDateFin.setHours(0, 0, 0, 0);\n\n    if (reunionDateObj < planningDateDebut || reunionDateObj > planningDateFin) {\n      this.toastService.error(\n        'Date invalide',\n        `La date de la réunion doit être comprise entre le ${planningDateDebut.toLocaleDateString('fr-FR')} et le ${planningDateFin.toLocaleDateString('fr-FR')} (période du planning \"${selectedPlanning.titre}\")`,\n        10000\n      );\n      return false;\n    }\n\n    return true;\n  }\n}", "<div class=\"container mx-auto px-4 py-6 max-w-3xl\">\n  <!-- En-tête avec gradient coloré -->\n  <div class=\"bg-gradient-to-r from-purple-600 to-indigo-600 rounded-t-lg p-6 text-white mb-0\">\n    <h1 class=\"text-2xl font-bold flex items-center\">\n      <i class=\"fas fa-plus-circle mr-3 text-purple-200\"></i>\n      {{ isEditMode ? 'Modifier la Réunion' : 'Nouvelle Réunion' }}\n    </h1>\n    <p class=\"text-purple-100 mt-2\">\n      {{ isEditMode ? 'Modifiez les détails de votre réunion' : 'Créez une nouvelle réunion pour votre équipe' }}\n    </p>\n  </div>\n\n  <form [formGroup]=\"reunionForm\" (ngSubmit)=\"onSubmit()\" class=\"bg-white rounded-b-lg shadow-lg p-6 border-t-0\">\n    <div *ngIf=\"error\" class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n      {{ error.message || 'Une erreur est survenue' }}\n    </div>\n    <div *ngIf=\"successMessage\" class=\"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4\">\n      {{ successMessage }}\n    </div>\n\n    <div class=\"grid grid-cols-1 gap-6\">\n      <!-- Titre -->\n      <div class=\"relative\">\n        <label for=\"titre\" class=\"block text-sm font-medium text-purple-700 mb-2\">\n          <i class=\"fas fa-tag mr-2 text-purple-500\"></i>\n          Titre *\n        </label>\n        <input id=\"titre\" type=\"text\" formControlName=\"titre\"\n               class=\"mt-1 block w-full rounded-lg border-2 border-purple-200 shadow-sm focus:border-purple-500 focus:ring-purple-500 focus:ring-2 transition-all duration-200 px-4 py-3\"\n               placeholder=\"Nom de votre réunion...\">\n        <div *ngIf=\"reunionForm.get('titre')?.invalid && reunionForm.get('titre')?.touched\"\n             class=\"text-red-500 text-sm mt-2 flex items-center\">\n          <i class=\"fas fa-exclamation-circle mr-1\"></i>\n          Le titre est obligatoire\n        </div>\n      </div>\n\n      <!-- Description -->\n      <div class=\"relative\">\n        <label for=\"description\" class=\"block text-sm font-medium text-indigo-700 mb-2\">\n          <i class=\"fas fa-align-left mr-2 text-indigo-500\"></i>\n          Description\n        </label>\n        <textarea id=\"description\" formControlName=\"description\" rows=\"3\"\n                  class=\"mt-1 block w-full rounded-lg border-2 border-indigo-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 focus:ring-2 transition-all duration-200 px-4 py-3\"\n                  placeholder=\"Décrivez votre réunion...\"></textarea>\n      </div>\n\n      <!-- Date and Time -->\n      <div class=\"bg-gradient-to-r from-blue-50 to-cyan-50 p-4 rounded-lg border border-blue-200\">\n        <h3 class=\"text-lg font-semibold text-blue-800 mb-4 flex items-center\">\n          <i class=\"fas fa-calendar-clock mr-2 text-blue-600\"></i>\n          Planification\n        </h3>\n        <div class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div>\n            <label for=\"date\" class=\"block text-sm font-medium text-blue-700 mb-2\">\n              <i class=\"fas fa-calendar mr-2 text-blue-500\"></i>\n              Date *\n            </label>\n            <input id=\"date\" type=\"date\" formControlName=\"date\"\n                   class=\"mt-1 block w-full rounded-lg border-2 border-blue-200 shadow-sm focus:border-blue-500 focus:ring-blue-500 focus:ring-2 transition-all duration-200 px-4 py-3\">\n            <div *ngIf=\"reunionForm.get('date')?.invalid && reunionForm.get('date')?.touched\"\n                 class=\"text-red-500 text-sm mt-2 flex items-center\">\n              <i class=\"fas fa-exclamation-circle mr-1\"></i>\n              La date est obligatoire\n            </div>\n          </div>\n\n          <div>\n            <label for=\"heureDebut\" class=\"block text-sm font-medium text-green-700 mb-2\">\n              <i class=\"fas fa-play mr-2 text-green-500\"></i>\n              Heure de début *\n            </label>\n            <input id=\"heureDebut\" type=\"time\" formControlName=\"heureDebut\"\n                   class=\"mt-1 block w-full rounded-lg border-2 border-green-200 shadow-sm focus:border-green-500 focus:ring-green-500 focus:ring-2 transition-all duration-200 px-4 py-3\">\n            <div *ngIf=\"reunionForm.get('heureDebut')?.invalid && reunionForm.get('heureDebut')?.touched\"\n                 class=\"text-red-500 text-sm mt-2 flex items-center\">\n              <i class=\"fas fa-exclamation-circle mr-1\"></i>\n              L'heure de début est obligatoire\n            </div>\n          </div>\n\n          <div>\n            <label for=\"heureFin\" class=\"block text-sm font-medium text-red-700 mb-2\">\n              <i class=\"fas fa-stop mr-2 text-red-500\"></i>\n              Heure de fin *\n            </label>\n            <input id=\"heureFin\" type=\"time\" formControlName=\"heureFin\"\n                   class=\"mt-1 block w-full rounded-lg border-2 border-red-200 shadow-sm focus:border-red-500 focus:ring-red-500 focus:ring-2 transition-all duration-200 px-4 py-3\">\n            <div *ngIf=\"reunionForm.get('heureFin')?.invalid && reunionForm.get('heureFin')?.touched\"\n                 class=\"text-red-500 text-sm mt-2 flex items-center\">\n              <i class=\"fas fa-exclamation-circle mr-1\"></i>\n              L'heure de fin est obligatoire\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Lieu / Lien visio -->\n      <div class=\"bg-gradient-to-r from-orange-50 to-yellow-50 p-4 rounded-lg border border-orange-200\">\n        <h3 class=\"text-lg font-semibold text-orange-800 mb-4 flex items-center\">\n          <i class=\"fas fa-map-marker-alt mr-2 text-orange-600\"></i>\n          Localisation\n        </h3>\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div>\n            <label for=\"lieu\" class=\"block text-sm font-medium text-orange-700 mb-2\">\n              <i class=\"fas fa-map-marker-alt mr-2 text-orange-500\"></i>\n              Lieu / Salle\n            </label>\n            <input id=\"lieu\" type=\"text\" formControlName=\"lieu\"\n                   class=\"mt-1 block w-full rounded-lg border-2 border-orange-200 shadow-sm focus:border-orange-500 focus:ring-orange-500 focus:ring-2 transition-all duration-200 px-4 py-3\"\n                   placeholder=\"Salle 101, Bureau A, Google Meet...\">\n          </div>\n\n          <div>\n            <label for=\"lienVisio\" class=\"block text-sm font-medium text-cyan-700 mb-2\">\n              <i class=\"fas fa-video mr-2 text-cyan-500\"></i>\n              Lien Visio\n              <span *ngIf=\"isCheckingLienVisio\" class=\"ml-2 text-xs text-cyan-500\">\n                <svg class=\"inline h-3 w-3 animate-spin\" fill=\"none\" viewBox=\"0 0 24 24\">\n                  <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n                  <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                </svg>\n                Vérification...\n              </span>\n            </label>\n            <input id=\"lienVisio\" type=\"url\" formControlName=\"lienVisio\"\n                   [class]=\"'mt-1 block w-full rounded-lg shadow-sm focus:ring-cyan-500 focus:ring-2 transition-all duration-200 px-4 py-3 ' +\n                           (lienVisioError ? 'border-2 border-red-300 focus:border-red-500' : 'border-2 border-cyan-200 focus:border-cyan-500')\"\n                   placeholder=\"https://meet.google.com/...\">\n\n            <!-- Message d'erreur pour l'unicité du lien -->\n            <div *ngIf=\"lienVisioError\" class=\"text-red-500 text-sm mt-2 flex items-center bg-red-50 p-2 rounded border border-red-200\">\n              <i class=\"fas fa-exclamation-triangle mr-2\"></i>\n              {{ lienVisioError }}\n            </div>\n\n            <!-- Message de succès -->\n            <div *ngIf=\"!lienVisioError && !isCheckingLienVisio && reunionForm.get('lienVisio')?.value && reunionForm.get('lienVisio')?.value.trim() !== ''\"\n                 class=\"text-green-600 text-sm mt-2 flex items-center bg-green-50 p-2 rounded border border-green-200\">\n              <i class=\"fas fa-check-circle mr-2\"></i>\n              Lien disponible\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Planning - affiché seulement si pas de planningId dans l'URL -->\n      <div *ngIf=\"!planningIdFromUrl\" class=\"bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg border border-purple-200\">\n        <h3 class=\"text-lg font-semibold text-purple-800 mb-4 flex items-center\">\n          <i class=\"fas fa-calendar-alt mr-2 text-purple-600\"></i>\n          Planning\n        </h3>\n        <label for=\"planning\" class=\"block text-sm font-medium text-purple-700 mb-2\">\n          <i class=\"fas fa-list mr-2 text-purple-500\"></i>\n          Sélectionnez un planning *\n        </label>\n        <select id=\"planning\" formControlName=\"planning\"\n                class=\"mt-1 block w-full rounded-lg border-2 border-purple-200 shadow-sm focus:border-purple-500 focus:ring-purple-500 focus:ring-2 transition-all duration-200 px-4 py-3\">\n          <option value=\"\">Choisissez un planning...</option>\n          <option *ngFor=\"let planning of plannings\" [value]=\"planning._id\">{{ planning.titre }}</option>\n        </select>\n        <div *ngIf=\"reunionForm.get('planning')?.invalid && reunionForm.get('planning')?.touched\"\n             class=\"text-red-500 text-sm mt-2 flex items-center\">\n          <i class=\"fas fa-exclamation-circle mr-1\"></i>\n          Le planning est obligatoire\n        </div>\n      </div>\n\n      <!-- Planning info - affiché quand planningId est dans l'URL -->\n      <div *ngIf=\"planningIdFromUrl && selectedPlanning\" class=\"bg-gradient-to-r from-purple-50 to-indigo-50 p-4 rounded-lg border-2 border-purple-200 shadow-sm\">\n        <h3 class=\"text-lg font-semibold text-purple-800 mb-3 flex items-center\">\n          <i class=\"fas fa-calendar-check mr-2 text-purple-600\"></i>\n          Planning sélectionné\n        </h3>\n        <div class=\"flex items-center justify-between\">\n          <span class=\"font-semibold text-purple-800 text-lg\">\n            <i class=\"fas fa-calendar-alt mr-2 text-purple-600\"></i>\n            {{ selectedPlanning.titre }}\n          </span>\n          <span class=\"text-sm font-medium text-red-600 bg-red-50 px-2 py-1 rounded-full border border-red-200\">\n            <i class=\"fas fa-clock mr-1\"></i>\n            {{ selectedPlanning.dateDebut | date:'dd/MM/yyyy' }} -\n            {{ selectedPlanning.dateFin | date:'dd/MM/yyyy' }}\n          </span>\n        </div>\n        <div *ngIf=\"selectedPlanning.description\" class=\"text-sm text-indigo-700 mt-2 bg-indigo-50 p-2 rounded border-l-4 border-indigo-300\">\n          <i class=\"fas fa-info-circle mr-1\"></i>\n          {{ selectedPlanning.description }}\n        </div>\n      </div>\n\n      <!-- Participants -->\n      <div class=\"bg-gradient-to-r from-emerald-50 to-teal-50 p-4 rounded-lg border border-emerald-200\">\n        <h3 class=\"text-lg font-semibold text-emerald-800 mb-4 flex items-center\">\n          <i class=\"fas fa-users mr-2 text-emerald-600\"></i>\n          Participants\n        </h3>\n        <label class=\"block text-sm font-medium text-emerald-700 mb-2\">\n          <i class=\"fas fa-user-friends mr-2 text-emerald-500\"></i>\n          Sélectionnez les participants\n        </label>\n        <select formControlName=\"participants\" multiple\n                class=\"mt-1 block w-full px-4 py-3 border-2 border-emerald-200 rounded-lg shadow-sm focus:ring-emerald-500 focus:border-emerald-500 focus:ring-2 transition-all duration-200 text-sm min-h-[120px]\">\n          <ng-container *ngIf=\"users$ | async as users\">\n            <option *ngFor=\"let user of users\" [value]=\"user._id\" class=\"py-2\">\n              <i class=\"fas fa-user mr-2\"></i>{{ user.username }}\n            </option>\n          </ng-container>\n        </select>\n        <p class=\"text-xs text-emerald-600 mt-2\">\n          <i class=\"fas fa-info-circle mr-1\"></i>\n          Maintenez Ctrl (ou Cmd) pour sélectionner plusieurs participants\n        </p>\n      </div>\n    </div>\n\n    <!-- Boutons d'action avec design amélioré -->\n    <div class=\"mt-8 flex justify-end space-x-4 bg-gray-50 p-4 rounded-lg border-t border-gray-200\">\n      <button type=\"button\" (click)=\"goReunion()\"\n              class=\"px-6 py-3 border-2 border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-100 hover:border-gray-400 transition-all duration-200 flex items-center\">\n        <i class=\"fas fa-times mr-2\"></i>\n        Annuler\n      </button>\n      <button type=\"submit\" [disabled]=\"!canSubmit() || isSubmitting\"\n              class=\"px-6 py-3 rounded-lg text-sm font-medium text-white bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center shadow-lg\">\n        <i class=\"fas fa-save mr-2\" *ngIf=\"!isSubmitting && !isCheckingLienVisio\"></i>\n        <i class=\"fas fa-spinner fa-spin mr-2\" *ngIf=\"isSubmitting\"></i>\n        <i class=\"fas fa-search mr-2\" *ngIf=\"isCheckingLienVisio\"></i>\n        {{ isSubmitting ? 'Enregistrement...' : (isCheckingLienVisio ? 'Vérification...' : 'Créer la réunion') }}\n      </button>\n    </div>\n  </form>\n</div>", "import { Component, OnInit, AfterViewInit } from '@angular/core';\nimport { ReunionService }  from 'src/app/services/reunion.service';\nimport { Reunion } from 'src/app/models/reunion.model';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport { Router } from \"@angular/router\";\nimport { DomSanitizer, SafeHtml } from '@angular/platform-browser';\nimport { trigger, transition, style, animate, query, stagger } from '@angular/animations';\nimport { ToastService } from 'src/app/services/toast.service';\n\n@Component({\n  selector: 'app-reunion-list',\n  templateUrl: './reunion-list.component.html',\n  styleUrls: ['./reunion-list.component.css'],\n  animations: [\n    trigger('fadeIn', [\n      transition(':enter', [\n        style({ opacity: 0, transform: 'translateY(20px)' }),\n        animate('0.4s ease-out', style({ opacity: 1, transform: 'translateY(0)' }))\n      ])\n    ]),\n    trigger('staggerList', [\n      transition('* => *', [\n        query(':enter', [\n          style({ opacity: 0, transform: 'translateY(30px)' }),\n          stagger('100ms', [\n            animate('0.5s ease-out', style({ opacity: 1, transform: 'translateY(0)' }))\n          ])\n        ], { optional: true })\n      ])\n    ])\n  ]\n})\nexport class ReunionListComponent implements OnInit, AfterViewInit {\n  reunions: any[] = [];\n  filteredReunions: any[] = [];\n  loading = true;\n  error: any;\n  animateItems = false; // Contrôle l'animation des éléments de la liste\n\n  // Propriétés pour la recherche\n  showSearchBar = false;\n  searchTerm = '';\n  selectedPlanning = '';\n  uniquePlannings: any[] = [];\n\n  // Propriété pour le titre de la page\n  get pageTitle(): string {\n    return this.authService.getCurrentUserRole() === 'admin'\n      ? 'Toutes les Réunions'\n      : 'Mes Réunions';\n  }\n\n  constructor(\n    private reunionService: ReunionService,\n    private router: Router,\n    private authService: AuthuserService,\n    private sanitizer: DomSanitizer,\n    private toastService: ToastService\n  ) {}\n\n  ngOnInit(): void {\n    this.loadReunions();\n\n    // Test du service de toast\n    console.log('🧪 Test du service de toast...');\n    // this.toastService.success('Test', 'Le service de toast fonctionne !');\n  }\n\n  ngAfterViewInit(): void {\n    // Activer les animations après un court délai pour permettre le rendu initial\n    setTimeout(() => {\n      this.animateItems = true;\n    }, 100);\n  }\n\n  /**\n   * Affiche ou masque la barre de recherche\n   */\n  toggleSearchBar(): void {\n    this.showSearchBar = !this.showSearchBar;\n\n    // Si on ferme la barre de recherche, réinitialiser les filtres\n    if (!this.showSearchBar) {\n      this.clearSearch();\n    }\n  }\n\n  /**\n   * Réinitialise les critères de recherche\n   */\n  clearSearch(): void {\n    this.searchTerm = '';\n    this.selectedPlanning = '';\n    this.searchReunions();\n  }\n\n  /**\n   * Filtre les réunions selon les critères de recherche\n   */\n  searchReunions(): void {\n    if (!this.searchTerm && !this.selectedPlanning) {\n      // Si aucun critère de recherche, afficher toutes les réunions\n      this.filteredReunions = [...this.reunions];\n      return;\n    }\n\n    // Filtrer les réunions selon les critères\n    this.filteredReunions = this.reunions.filter(reunion => {\n      // Vérifier le titre et la description si searchTerm est défini\n      const matchesSearchTerm = !this.searchTerm ||\n        (reunion.titre && reunion.titre.toLowerCase().includes(this.searchTerm.toLowerCase())) ||\n        (reunion.description && reunion.description.toLowerCase().includes(this.searchTerm.toLowerCase()));\n\n      // Vérifier le planning si selectedPlanning est défini\n      const matchesPlanning = !this.selectedPlanning ||\n        (reunion.planning && reunion.planning._id === this.selectedPlanning);\n\n      // La réunion doit correspondre aux deux critères (si définis)\n      return matchesSearchTerm && matchesPlanning;\n    });\n  }\n\n\n\n  loadReunions(): void {\n    this.loading = true;\n    this.animateItems = false; // Réinitialiser l'animation\n\n    const userId = this.authService.getCurrentUserId();\n    const userRole = this.authService.getCurrentUserRole();\n\n    if (!userId) {\n      this.error = \"Utilisateur non connecté\";\n      this.loading = false;\n      return;\n    }\n\n    // Si l'utilisateur est admin, récupérer toutes les réunions\n    // Sinon, récupérer seulement ses réunions\n    const reunionObservable = userRole === 'admin'\n      ? this.reunionService.getAllReunionsAdmin()\n      : this.reunionService.getProchainesReunions(userId);\n\n    reunionObservable.subscribe({\n      next: (response: any) => {\n        console.log('Réunions chargées:', response);\n\n        // Réinitialiser les erreurs\n        this.error = null;\n\n        // Attribuer les données après un court délai pour l'animation\n        setTimeout(() => {\n          // Récupérer les réunions selon la structure de réponse\n          let reunions = userRole === 'admin'\n            ? (response.data || response.reunions || [])\n            : (response.reunions || []);\n\n          console.log('Réunions récupérées pour admin:', reunions);\n          console.log('Structure de la première réunion:', reunions[0]);\n\n          // Pour le test : ajouter \"présence obligatoire\" à certaines réunions si aucune n'en a\n          reunions = this.ajouterPresenceObligatoirePourTest(reunions);\n\n          // Trier les réunions : celles avec \"Présence Obligatoire\" en premier\n          this.reunions = this.trierReunionsParPresenceObligatoire(reunions);\n\n          // Initialiser les réunions filtrées avec toutes les réunions\n          this.filteredReunions = [...this.reunions];\n\n          // Extraire les plannings uniques pour le filtre\n          this.extractUniquePlannings();\n\n          this.loading = false;\n\n          // Activer les animations après un court délai\n          setTimeout(() => {\n            this.animateItems = true;\n          }, 100);\n        }, 300); // Délai pour une meilleure expérience visuelle\n      },\n      error: (error: any) => {\n        console.error('Erreur détaillée:', JSON.stringify(error));\n        this.error = `Erreur lors du chargement des réunions: ${error.message || error.statusText || 'Erreur inconnue'}`;\n        this.loading = false;\n      }\n    });\n  }\n\n  getStatutClass(statut: string): string {\n    switch (statut) {\n      case 'planifiee': return 'bg-blue-100 text-blue-800';\n      case 'en_cours': return 'bg-yellow-100 text-yellow-800';\n      case 'terminee': return 'bg-green-100 text-green-800';\n      case 'annulee': return 'bg-red-100 text-red-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  }\n\n  editReunion(id:string) {\n    console.log(id)\n      if (this.reunions) {\n      this.router.navigate(['/reunions/modifier', id]);\n\n  }\n  }\n\n  /**\n   * Supprime une réunion après confirmation\n   * @param id ID de la réunion à supprimer\n   */\n  deleteReunion(id: string): void {\n    console.log('🗑️ Tentative de suppression de la réunion avec ID:', id);\n\n    if (confirm('Êtes-vous sûr de vouloir supprimer cette réunion ?')) {\n      const userRole = this.authService.getCurrentUserRole();\n      console.log('👤 Rôle utilisateur:', userRole);\n\n      // Utiliser la méthode appropriée selon le rôle\n      const deleteObservable = userRole === 'admin'\n        ? this.reunionService.forceDeleteReunion(id)\n        : this.reunionService.deleteReunion(id);\n\n      console.log('🚀 Envoi de la requête de suppression...');\n\n      deleteObservable.subscribe({\n        next: (response) => {\n          console.log('✅ Réunion supprimée avec succès:', response);\n          this.handleSuccessfulDeletion(id);\n        },\n        error: (error) => {\n          console.error('❌ Erreur lors de la suppression:', error);\n          console.error('📋 Détails de l\\'erreur:', {\n            status: error.status,\n            statusText: error.statusText,\n            message: error.error?.message,\n            fullError: error\n          });\n\n          // Si c'est une erreur 200 mal interprétée ou une erreur de CORS,\n          // on considère que la suppression a réussi\n          if (error.status === 0 || error.status === 200) {\n            console.log('🔄 Erreur probablement liée à CORS ou réponse mal formatée, on considère la suppression comme réussie');\n            this.handleSuccessfulDeletion(id);\n            return;\n          }\n\n          // Pour les autres erreurs, on vérifie quand même si la suppression a eu lieu\n          // en rechargeant la liste après un délai\n          if (error.status >= 500) {\n            console.log('🔄 Erreur serveur, vérification de la suppression dans 2 secondes...');\n            setTimeout(() => {\n              this.loadReunions();\n            }, 2000);\n          }\n\n          // Gestion spécifique des erreurs d'autorisation\n          if (error.status === 403) {\n            this.toastService.accessDenied('supprimer cette réunion', error.status);\n          } else if (error.status === 401) {\n            this.toastService.error(\n              'Non autorisé',\n              'Vous devez être connecté pour supprimer une réunion'\n            );\n          } else {\n            const errorMessage = error.error?.message || 'Erreur lors de la suppression de la réunion';\n            this.toastService.error(\n              'Erreur de suppression',\n              errorMessage,\n              8000\n            );\n          }\n        }\n      });\n    } else {\n      console.log('❌ Suppression annulée par l\\'utilisateur');\n    }\n  }\n\n  private handleSuccessfulDeletion(id: string): void {\n    console.log('🎯 Traitement de la suppression réussie pour l\\'ID:', id);\n\n    // Retirer la réunion de la liste locale (gérer _id et id)\n    const initialCount = this.reunions.length;\n    this.reunions = this.reunions.filter(reunion =>\n      reunion._id !== id && reunion.id !== id\n    );\n    this.filteredReunions = this.filteredReunions.filter(reunion =>\n      reunion._id !== id && reunion.id !== id\n    );\n\n    const finalCount = this.reunions.length;\n    console.log(`📊 Réunions avant suppression: ${initialCount}, après: ${finalCount}`);\n\n    // Mettre à jour la liste des plannings uniques\n    this.extractUniquePlannings();\n\n    // Afficher le toast de succès\n    this.toastService.success(\n      'Réunion supprimée',\n      'La réunion a été supprimée avec succès'\n    );\n\n    console.log('🎉 Toast de succès affiché et liste mise à jour');\n\n    // Recharger complètement la liste pour s'assurer de la mise à jour\n    this.loadReunions();\n  }\n\n  formatDescription(description: string): SafeHtml {\n    if (!description) return this.sanitizer.bypassSecurityTrustHtml('');\n\n    // Recherche la chaîne \"(presence obligatoire)\" (insensible à la casse) et la remplace par une version en rouge\n    const formattedText = description.replace(\n      /\\(presence obligatoire\\)/gi,\n      '<span class=\"text-red-600 font-semibold\">(presence obligatoire)</span>'\n    );\n\n    // Sanitize le HTML pour éviter les problèmes de sécurité\n    return this.sanitizer.bypassSecurityTrustHtml(formattedText);\n  }\n\n  /**\n   * Vérifie si une réunion contient \"Présence Obligatoire\" dans sa description\n   * @param reunion La réunion à vérifier\n   * @returns true si la réunion a une présence obligatoire, false sinon\n   */\n  hasPresenceObligatoire(reunion: any): boolean {\n    if (!reunion.description) return false;\n\n    // Recherche différentes variations de \"présence obligatoire\" (insensible à la casse)\n    const patterns = [\n      /presence obligatoire/i,\n      /présence obligatoire/i,\n      /obligatoire/i,\n      /\\(obligatoire\\)/i,\n      /\\(presence obligatoire\\)/i,\n      /\\(présence obligatoire\\)/i\n    ];\n\n    // Retourne true si l'une des expressions est trouvée\n    return patterns.some(pattern => pattern.test(reunion.description));\n  }\n\n  /**\n   * Trie les réunions en mettant celles avec \"Présence Obligatoire\" en premier\n   * @param reunions Liste des réunions à trier\n   * @returns Liste triée des réunions\n   */\n  trierReunionsParPresenceObligatoire(reunions: any[]): any[] {\n    if (!reunions || !reunions.length) return [];\n\n    console.log('Avant tri - Nombre de réunions:', reunions.length);\n\n    // Vérifier chaque réunion pour la présence obligatoire\n    reunions.forEach((reunion, index) => {\n      const hasPresence = this.hasPresenceObligatoire(reunion);\n      console.log(`Réunion ${index + 1} - Titre: ${reunion.titre}, Description: ${reunion.description}, Présence Obligatoire: ${hasPresence}`);\n    });\n\n    // Trier les réunions : celles avec \"Présence Obligatoire\" en premier\n    const reunionsTriees = [...reunions].sort((a, b) => {\n      const aHasPresenceObligatoire = this.hasPresenceObligatoire(a);\n      const bHasPresenceObligatoire = this.hasPresenceObligatoire(b);\n\n      if (aHasPresenceObligatoire && !bHasPresenceObligatoire) {\n        return -1; // a vient avant b\n      }\n      if (!aHasPresenceObligatoire && bHasPresenceObligatoire) {\n        return 1; // b vient avant a\n      }\n\n      // Si les deux ont ou n'ont pas \"Présence Obligatoire\", trier par date\n      return new Date(b.date).getTime() - new Date(a.date).getTime();\n    });\n\n    console.log('Après tri - Ordre des réunions:');\n    reunionsTriees.forEach((reunion, index) => {\n      const hasPresence = this.hasPresenceObligatoire(reunion);\n      console.log(`Position ${index + 1} - Titre: ${reunion.titre}, Présence Obligatoire: ${hasPresence}`);\n    });\n\n    return reunionsTriees;\n  }\n\n  /**\n   * Méthode temporaire pour ajouter \"présence obligatoire\" à certaines réunions pour tester le tri\n   * @param reunions Liste des réunions\n   * @returns Liste des réunions avec certaines marquées comme \"présence obligatoire\"\n   */\n  /**\n   * Extrait les plannings uniques à partir des réunions pour le filtre\n   */\n  extractUniquePlannings(): void {\n    // Map pour stocker les plannings uniques par ID\n    const planningsMap = new Map();\n\n    // Parcourir toutes les réunions\n    this.reunions.forEach(reunion => {\n      if (reunion.planning && reunion.planning._id) {\n        // Ajouter le planning au Map s'il n'existe pas déjà\n        if (!planningsMap.has(reunion.planning._id)) {\n          planningsMap.set(reunion.planning._id, {\n            id: reunion.planning._id,\n            titre: reunion.planning.titre\n          });\n        }\n      }\n    });\n\n    // Convertir le Map en tableau\n    this.uniquePlannings = Array.from(planningsMap.values());\n\n    // Trier les plannings par titre\n    this.uniquePlannings.sort((a, b) => a.titre.localeCompare(b.titre));\n  }\n\n  /**\n   * Méthode temporaire pour ajouter \"présence obligatoire\" à certaines réunions pour tester le tri\n   */\n  ajouterPresenceObligatoirePourTest(reunions: any[]): any[] {\n    if (!reunions || reunions.length === 0) return reunions;\n\n    // Vérifier si au moins une réunion a déjà \"présence obligatoire\"\n    const hasAnyPresenceObligatoire = reunions.some(reunion => this.hasPresenceObligatoire(reunion));\n\n    // Si aucune réunion n'a \"présence obligatoire\", en ajouter à certaines pour le test\n    if (!hasAnyPresenceObligatoire) {\n      console.log('Aucune réunion avec présence obligatoire trouvée, ajout pour le test...');\n\n      // Ajouter \"présence obligatoire\" à la première réunion si elle existe\n      if (reunions.length > 0) {\n        const reunion = reunions[0];\n        reunion.description = reunion.description\n          ? reunion.description + ' (présence obligatoire)'\n          : '(présence obligatoire)';\n        console.log(`Ajout de \"présence obligatoire\" à la réunion: ${reunion.titre}`);\n      }\n\n      // Si au moins 3 réunions, ajouter aussi à la troisième\n      if (reunions.length >= 3) {\n        const reunion = reunions[2];\n        reunion.description = reunion.description\n          ? reunion.description + ' (présence obligatoire)'\n          : '(présence obligatoire)';\n        console.log(`Ajout de \"présence obligatoire\" à la réunion: ${reunion.titre}`);\n      }\n    }\n\n    return reunions;\n  }\n}", "<div class=\"container mx-auto px-4 py-6 page-container page-enter\">\n  <div class=\"flex flex-col mb-8\">\n    <div class=\"flex justify-between items-center\">\n      <h1 class=\"text-2xl font-bold text-gray-800 page-title\">{{ pageTitle }}</h1>\n\n      <!-- Bouton de recherche -->\n      <div class=\"relative\">\n        <button (click)=\"toggleSearchBar()\" class=\"search-button px-4 py-2 bg-purple-200 text-purple-800 rounded-md hover:bg-purple-300 transition-colors transform hover:scale-105 duration-200 flex items-center shadow-sm border border-purple-300\">\n          <svg class=\"h-5 w-5 mr-2 text-purple-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n          </svg>\n          Rechercher\n        </button>\n      </div>\n    </div>\n\n    <!-- Barre de recherche -->\n    <div *ngIf=\"showSearchBar\" class=\"mt-4 bg-white p-4 rounded-lg shadow-md transition-all duration-300 animate-fadeIn\"\n         [ngClass]=\"{'animate__animated animate__fadeInDown': showSearchBar}\">\n      <div class=\"flex flex-col md:flex-row gap-4\">\n        <div class=\"flex-1\">\n          <div class=\"relative\">\n            <div class=\"flex items-center\">\n              <div class=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-purple-400\">\n                <svg class=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                </svg>\n              </div>\n              <input\n                type=\"text\"\n                id=\"searchTerm\"\n                [(ngModel)]=\"searchTerm\"\n                (input)=\"searchReunions()\"\n                class=\"w-full pl-10 pr-10 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-300 focus:border-purple-400 transition-all duration-300\"\n                placeholder=\"Rechercher par titre ou description\"\n              >\n              <button *ngIf=\"searchTerm\" (click)=\"clearSearch()\" class=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-purple-600 transition-colors\">\n                <svg class=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n                </svg>\n              </button>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"md:w-1/3\">\n          <div class=\"relative\">\n            <div class=\"flex items-center\">\n              <select\n                id=\"planningFilter\"\n                [(ngModel)]=\"selectedPlanning\"\n                (change)=\"searchReunions()\"\n                class=\"w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-300 focus:border-purple-400 transition-all duration-300 appearance-none\"\n              >\n                <option value=\"\">Tous les plannings</option>\n                <option *ngFor=\"let planning of uniquePlannings\" [value]=\"planning.id\">{{ planning.titre }}</option>\n              </select>\n              <div class=\"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\">\n                <svg class=\"h-5 w-5 text-purple-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 9l-7 7-7-7\" />\n                </svg>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <div *ngIf=\"!loading && reunions.length > 0\" class=\"mt-2 text-sm text-gray-600 flex items-center\">\n      <svg class=\"h-4 w-4 mr-2 text-red-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n      </svg>\n      <span>Les réunions à <span class=\"font-semibold text-red-600\">présence obligatoire</span> sont affichées en premier</span>\n    </div>\n\n    <!-- Résultats de recherche -->\n    <div *ngIf=\"searchTerm || selectedPlanning\" class=\"mt-2 text-sm text-gray-600\">\n      <span *ngIf=\"filteredReunions.length === 0\" class=\"text-red-500\">\n        Aucune réunion ne correspond à votre recherche.\n      </span>\n      <span *ngIf=\"filteredReunions.length > 0\">\n        {{ filteredReunions.length }} réunion(s) trouvée(s)\n      </span>\n    </div>\n  </div>\n\n  <div *ngIf=\"loading\" class=\"text-center py-12\">\n    <div class=\"loading-spinner rounded-full h-16 w-16 border-4 border-purple-200 border-t-purple-600 mx-auto\"></div>\n    <p class=\"mt-4 text-gray-600 animate-pulse\">Chargement de vos réunions...</p>\n  </div>\n\n  <div *ngIf=\"error\" class=\"bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md mb-6 shadow-md transform transition-all duration-500 hover:shadow-lg\">\n    <div class=\"flex items-center\">\n      <svg class=\"h-6 w-6 mr-3 text-red-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n      </svg>\n      <p>Erreur lors du chargement des réunions: {{ error.message }}</p>\n    </div>\n  </div>\n\n  <div *ngIf=\"!loading && reunions.length === 0\" class=\"text-center py-12 empty-container\" [class.animated]=\"!loading\">\n    <div class=\"bg-white rounded-lg shadow-md p-8 max-w-md mx-auto\">\n      <svg class=\"mx-auto h-16 w-16 text-purple-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n      </svg>\n      <h3 class=\"mt-4 text-xl font-medium text-gray-900\">Aucune réunion prévue</h3>\n      <p class=\"mt-2 text-gray-500\">Vous pouvez créer des réunions depuis la page détail d'un planning.</p>\n    </div>\n  </div>\n\n  <div *ngIf=\"!loading && reunions.length > 0\" class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n    <div *ngFor=\"let reunion of (searchTerm || selectedPlanning ? filteredReunions : reunions); let i = index\"\n         class=\"bg-white rounded-lg shadow-md p-5 hover:shadow-xl transition-all duration-300 reunion-card\"\n         [class.animated]=\"animateItems\"\n         [class.border-l-4]=\"hasPresenceObligatoire(reunion)\"\n         [class.border-red-500]=\"hasPresenceObligatoire(reunion)\"\n         [style.animation-delay]=\"i * 100 + 'ms'\">\n      <div class=\"flex justify-between items-start\">\n        <div class=\"flex-1\">\n          <div class=\"flex items-center\">\n            <h3 class=\"text-lg font-semibold text-gray-800 hover:text-purple-600 transition-colors\">\n              <a [routerLink]=\"['/reunions/reunionDetails', reunion._id]\" class=\"hover:text-purple-600\">{{ reunion.titre }}</a>\n            </h3>\n            <span *ngIf=\"hasPresenceObligatoire(reunion)\"\n                  class=\"ml-2 px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full font-bold animate-pulse\">\n              Présence Obligatoire\n            </span>\n          </div>\n          <p class=\"text-sm mt-1\" [innerHTML]=\"reunion.description | highlightPresence\"></p>\n          <div class=\"mt-3 flex items-center text-sm text-gray-500\">\n            <svg class=\"h-4 w-4 mr-2 text-purple-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n            <span>{{ reunion.date | date:'mediumDate' }} • {{ reunion.heureDebut }} - {{ reunion.heureFin }}</span>\n          </div>\n        </div>\n        <div class=\"flex items-start space-x-2\">\n          <span [class]=\"'px-3 py-1 text-xs rounded-full font-medium ' + getStatutClass(reunion.statut)\">\n            {{ reunion.statut | titlecase }}\n          </span>\n          <button (click)=\"deleteReunion(reunion._id || reunion.id); $event.stopPropagation();\"\n                  class=\"text-red-500 hover:text-red-700 transition-colors duration-300 p-1 rounded-full hover:bg-red-50\"\n                  title=\"Supprimer la réunion\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n            </svg>\n          </button>\n        </div>\n      </div>\n\n      <!-- Creator Info -->\n      <div class=\"mt-3 text-sm text-gray-600 flex items-center\">\n        <svg class=\"h-4 w-4 mr-2 text-gray-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n        </svg>\n        <span><strong>Créateur:</strong> {{ reunion.createur.username }}</span>\n      </div>\n\n      <!-- Participants -->\n      <div *ngIf=\"reunion.participants.length > 0\" class=\"mt-3 text-sm text-gray-600\">\n        <div class=\"flex items-center\">\n          <svg class=\"h-4 w-4 mr-2 text-gray-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n          </svg>\n          <strong>Participants:&nbsp;</strong>{{ reunion.participants.length }}\n        </div>\n      </div>\n\n      <!-- Planning Info -->\n      <div class=\"mt-3 text-sm text-gray-600 flex items-center\">\n        <svg class=\"h-4 w-4 mr-2 text-gray-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n        </svg>\n        <span><strong>Planning:</strong> {{ reunion.planning.titre }}</span>\n      </div>\n\n      <!-- Lien Visio -->\n      <div *ngIf=\"reunion.lienVisio\" class=\"mt-3 text-sm\">\n        <a href=\"{{ reunion.lienVisio }}\" class=\"text-purple-600 hover:text-purple-800 flex items-center transition-colors\" target=\"_blank\">\n          <svg class=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z\" />\n          </svg>\n          Rejoindre la visioconférence\n        </a>\n      </div>\n\n      <div class=\"mt-4 pt-3 border-t border-gray-100 flex justify-between items-center\">\n        <div class=\"flex items-center text-sm text-gray-500\">\n          <svg class=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n          </svg>\n          {{ reunion.lieu || 'Lieu non spécifié' }}\n        </div>\n        <div class=\"flex items-center space-x-2\">\n          <a (click)=\"editReunion(reunion._id || reunion.id)\"\n             class=\"px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-all duration-300 transform hover:scale-105 flex items-center\">\n            <svg class=\"h-4 w-4 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n            </svg>\n            Modifier\n          </a>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>", "import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { ReunionListComponent } from './reunion-list/reunion-list.component';\nimport { ReunionFormComponent } from './reunion-form/reunion-form.component';\nimport { ReunionDetailComponent } from './reunion-detail/reunion-detail.component';\nimport {ReunionEditComponent} from \"./reunion-edit/reunion-edit.component\";\n\nconst routes: Routes = [\n\n    { path: '', component: ReunionListComponent},\n    { path: 'nouvelleReunion', component: ReunionFormComponent},\n    { path: 'reunionDetails/:id', component: ReunionDetailComponent },\n    { path: 'modifier/:id', component: ReunionEditComponent }\n\n  ];\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule]\n})\nexport class ReunionsRoutingModule { }", "import { NgModule } from '@angular/core';\nimport { CommonModule, DatePipe } from '@angular/common';\n\nimport { ReunionsRoutingModule } from './reunions-routing.module';\nimport { ReunionListComponent } from './reunion-list/reunion-list.component';\nimport { ReunionDetailComponent } from './reunion-detail/reunion-detail.component';\nimport { ReunionFormComponent } from './reunion-form/reunion-form.component';\nimport { RouterModule } from '@angular/router';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\n\nimport { PipesModule } from '../../../pipes/pipes.module';\nimport { ReunionEditComponent } from './reunion-edit/reunion-edit.component';\n\n@NgModule({\n  declarations: [\n    ReunionListComponent,\n    ReunionDetailComponent,\n    ReunionFormComponent,\n    ReunionEditComponent,\n  ],\n  imports: [\n    CommonModule,\n    ReunionsRoutingModule,\n    RouterModule,\n    FormsModule,\n    ReactiveFormsModule,\n    PipesModule,\n  ],\n  providers: [DatePipe],\n})\nexport class ReunionsModule {}\n"], "names": ["i0", "ɵɵnamespaceHTML", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "error", "ɵɵtextInterpolate2", "participant_r6", "username", "email", "ɵɵnamespaceSVG", "ɵɵtextInterpolate", "ctx_r4", "reunion", "lieu", "ɵɵproperty", "ctx_r5", "lienVisio", "ɵɵsanitizeUrl", "ɵɵlistener", "ReunionDetailComponent_div_7_Template_button_click_8_listener", "ɵɵrestoreView", "_r8", "ctx_r7", "ɵɵnextContext", "ɵɵresetView", "editReunion", "ReunionDetailComponent_div_7_Template_button_click_12_listener", "ctx_r9", "deleteReunion", "ɵɵtemplate", "ReunionDetailComponent_div_7_li_33_Template", "ReunionDetailComponent_div_7_div_44_Template", "ReunionDetailComponent_div_7_div_45_Template", "ctx_r2", "titre", "ɵɵpipeBind1", "description", "ɵɵsanitizeHtml", "ɵɵtextInterpolate3", "ɵɵpipeBind2", "date", "heureDebut", "heure<PERSON>in", "<PERSON>ur", "participants", "planning", "dateDebut", "dateFin", "ReunionDetailComponent", "constructor", "route", "router", "reunionService", "sanitizer", "toastService", "loading", "ngOnInit", "loadReunionDetails", "id", "snapshot", "paramMap", "get", "getReunionById", "subscribe", "next", "response", "err", "message", "console", "formatDescription", "bypassSecurityTrustHtml", "formattedText", "replace", "navigate", "_id", "confirm", "log", "success", "status", "accessDenied", "errorMessage", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "ReunionService", "i3", "Dom<PERSON><PERSON><PERSON>zer", "i4", "ToastService", "selectors", "decls", "vars", "consts", "template", "ReunionDetailComponent_Template", "rf", "ctx", "ReunionDetailComponent_Template_button_click_1_listener", "ReunionDetailComponent_div_5_Template", "ReunionDetailComponent_div_6_Template", "ReunionDetailComponent_div_7_Template", "Validators", "ctx_r0", "ctx_r10", "currentReunionPlanning", "ReunionEditComponent_div_62_div_10_Template", "planning_r11", "user_r13", "ɵɵelementContainerStart", "ReunionEditComponent_ng_container_79_option_1_Template", "ɵɵelementContainerEnd", "users", "ReunionEditComponent", "fb", "userService", "planningService", "authService", "roleService", "isSubmitting", "plannings", "isAdmin", "reunionId", "checkUserRole", "initForm", "fetchUsers", "fetchPlannings", "loadReunion", "reunionForm", "group", "required", "getAllUsers", "userId", "getCurrentUserId", "planningsObservable", "getAllPlanningsAdmin", "getPlanningsByUser", "data", "patchValue", "split", "map", "p", "disable", "onSubmit", "invalid", "warning", "validateDateInPlanningRange", "value", "updateReunion", "goReunion", "formValue", "reunionDate", "planningId", "selectedPlanning", "find", "warn", "getPlanningById", "push", "reunionDateObj", "Date", "planningDateDebut", "planningDateFin", "setHours", "toLocaleDateString", "FormBuilder", "DataService", "i5", "PlanningService", "i6", "i7", "AuthuserService", "i8", "RoleService", "ReunionEditComponent_Template", "ReunionEditComponent_Template_form_ngSubmit_7_listener", "ReunionEditComponent_div_8_Template", "ReunionEditComponent_div_15_Template", "ReunionEditComponent_div_31_Template", "ReunionEditComponent_div_37_Template", "ReunionEditComponent_div_43_Template", "ReunionEditComponent_div_62_Template", "ReunionEditComponent_option_66_Template", "ReunionEditComponent_ng_container_79_Template", "ReunionEditComponent_Template_button_click_84_listener", "ReunionEditComponent_i_88_Template", "ReunionEditComponent_i_89_Template", "tmp_2_0", "touched", "tmp_3_0", "tmp_4_0", "tmp_5_0", "debounceTime", "distinctUntilChanged", "successMessage", "lienVisioError", "planning_r17", "ReunionFormComponent_div_63_option_10_Template", "ReunionFormComponent_div_63_div_11_Template", "tmp_1_0", "ctx_r18", "ReunionFormComponent_div_64_div_13_Template", "user_r21", "ReunionFormComponent_ng_container_73_option_1_Template", "users_r19", "ReunionFormComponent", "isEditMode", "currentReunionId", "planningIdFromUrl", "isCheckingLienVisio", "users$", "loadPlannings", "checkEditMode", "checkPlanningParam", "setupLienVisioValidation", "formatDateForInput", "toISOString", "slice", "queryParamMap", "canSubmit", "reunionData", "createReunion", "resetForm", "reset", "mark<PERSON><PERSON>ristine", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "valueChanges", "pipe", "trim", "checkLienVisioUniqueness", "undefined", "isUnique", "conflictWith", "valid", "ReunionFormComponent_Template", "ReunionFormComponent_Template_form_ngSubmit_7_listener", "ReunionFormComponent_div_8_Template", "ReunionFormComponent_div_9_Template", "ReunionFormComponent_div_16_Template", "ReunionFormComponent_div_32_Template", "ReunionFormComponent_div_38_Template", "ReunionFormComponent_div_44_Template", "ReunionFormComponent_span_59_Template", "ReunionFormComponent_div_61_Template", "ReunionFormComponent_div_62_Template", "ReunionFormComponent_div_63_Template", "ReunionFormComponent_div_64_Template", "ReunionFormComponent_ng_container_73_Template", "ReunionFormComponent_Template_button_click_79_listener", "ReunionFormComponent_i_83_Template", "ReunionFormComponent_i_84_Template", "ReunionFormComponent_i_85_Template", "tmp_6_0", "tmp_7_0", "tmp_8_0", "ɵɵclassMap", "tmp_12_0", "trigger", "transition", "style", "animate", "query", "stagger", "ReunionListComponent_div_10_button_9_Template_button_click_0_listener", "_r10", "clearSearch", "ReunionListComponent_div_10_Template_input_ngModelChange_8_listener", "$event", "_r13", "ctx_r12", "searchTerm", "ReunionListComponent_div_10_Template_input_input_8_listener", "ctx_r14", "searchReunions", "ReunionListComponent_div_10_button_9_Template", "ReunionListComponent_div_10_Template_select_ngModelChange_13_listener", "ctx_r15", "ReunionListComponent_div_10_Template_select_change_13_listener", "ctx_r16", "ReunionListComponent_div_10_option_16_Template", "ɵɵpureFunction1", "_c0", "showSearchBar", "uniquePlannings", "filteredReunions", "length", "ReunionListComponent_div_12_span_1_Template", "ReunionListComponent_div_12_span_2_Template", "ɵɵclassProp", "reunion_r20", "ɵɵpropertyInterpolate", "ReunionListComponent_div_16_div_1_span_7_Template", "ReunionListComponent_div_16_div_1_Template_button_click_20_listener", "restoredCtx", "_r28", "$implicit", "ctx_r27", "stopPropagation", "ReunionListComponent_div_16_div_1_div_30_Template", "ReunionListComponent_div_16_div_1_div_38_Template", "ReunionListComponent_div_16_div_1_Template_a_click_46_listener", "ctx_r29", "ɵɵstyleProp", "i_r21", "ctx_r19", "animateItems", "hasPresenceObligatoire", "_c1", "getStatutClass", "statut", "ReunionListComponent_div_16_div_1_Template", "ctx_r6", "reunions", "ReunionListComponent", "pageTitle", "getCurrentUserRole", "loadReunions", "ngAfterViewInit", "setTimeout", "toggleSearchBar", "filter", "matchesSearchTerm", "toLowerCase", "includes", "matchesPlanning", "userRole", "reunionObservable", "getAllReunionsAdmin", "getProchainesReunions", "ajouterPresenceObligatoirePourTest", "trierReunionsParPresenceObligatoire", "extractUniquePlannings", "JSON", "stringify", "statusText", "deleteObservable", "forceDeleteReunion", "handleSuccessfulDeletion", "fullError", "initialCount", "finalCount", "patterns", "some", "pattern", "test", "for<PERSON>ach", "index", "hasPresence", "reunionsTriees", "sort", "a", "b", "aHasPresenceObligatoire", "bHasPresenceObligatoire", "getTime", "planningsMap", "Map", "has", "set", "Array", "from", "values", "localeCompare", "hasAnyPresenceObligatoire", "ReunionListComponent_Template", "ReunionListComponent_Template_button_click_6_listener", "ReunionListComponent_div_10_Template", "ReunionListComponent_div_11_Template", "ReunionListComponent_div_12_Template", "ReunionListComponent_div_13_Template", "ReunionListComponent_div_14_Template", "ReunionListComponent_div_15_Template", "ReunionListComponent_div_16_Template", "opacity", "transform", "optional", "RouterModule", "routes", "path", "component", "ReunionsRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "exports", "CommonModule", "DatePipe", "FormsModule", "ReactiveFormsModule", "PipesModule", "ReunionsModule", "declarations"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}