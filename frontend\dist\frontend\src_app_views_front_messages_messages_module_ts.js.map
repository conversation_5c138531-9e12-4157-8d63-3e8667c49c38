{"version": 3, "file": "src_app_views_front_messages_messages_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAC8C;;;;;;;AA4LxC,MAAOC,qBAAqB;EAchCC,YACUC,cAA8B,EAC9BC,eAAgC,EAChCC,YAA0B,EAC1BC,YAA0B;IAH1B,KAAAH,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,YAAY,GAAZA,YAAY;IAjBtB,KAAAC,MAAM,GAAiB;MACrBC,OAAO,EAAE,UAAU;MACnBC,QAAQ,EAAE,QAAQ;MAClBC,QAAQ,EAAE,UAAU;MACpBC,SAAS,EAAE,UAAU;MACrBC,QAAQ,EAAE,UAAiB;MAC3BC,KAAK,EAAE,eAAe;MACtBC,SAAS,EAAE,IAAIC,IAAI;KACpB;IAED,KAAAC,UAAU,GAAG,KAAK;EAQf;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACC,YAAY,GAAGnB,8CAAQ,CAAC,KAAK,CAAC,CAACoB,SAAS,CAAC,MAAK;MACjD,IAAI,CAACF,iBAAiB,EAAE;IAC1B,CAAC,CAAC;IAEF;IACA,IAAI,CAACb,YAAY,CAACgB,aAAa,CAACD,SAAS,CAACP,KAAK,IAAG;MAChD,IAAI,CAACN,MAAM,CAACM,KAAK,GAAGA,KAAK,CAACS,WAAW;IACvC,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACJ,YAAY,EAAEK,WAAW,EAAE;EAClC;EAEMN,iBAAiBA,CAAA;IAAA,IAAAO,KAAA;IAAA,OAAAC,kKAAA;MACrBD,KAAI,CAACT,UAAU,GAAG,IAAI;MACtBS,KAAI,CAAClB,MAAM,CAACO,SAAS,GAAG,IAAIC,IAAI,EAAE;MAElC;MACA,IAAI;QACF,MAAMU,KAAI,CAACrB,eAAe,CAACuB,QAAQ,EAAE,CAACC,SAAS,EAAE;QACjDH,KAAI,CAAClB,MAAM,CAACK,QAAQ,GAAG,WAAW;OACnC,CAAC,MAAM;QACNa,KAAI,CAAClB,MAAM,CAACK,QAAQ,GAAG,aAAa;;MAGtC;MACA,IAAI;QACF,MAAMa,KAAI,CAACtB,cAAc,CAAC0B,gBAAgB,EAAE,CAACD,SAAS,EAAE;QACxDH,KAAI,CAAClB,MAAM,CAACC,OAAO,GAAG,QAAQ;QAC9BiB,KAAI,CAAClB,MAAM,CAACG,QAAQ,GAAG,QAAQ;QAC/Be,KAAI,CAAClB,MAAM,CAACI,SAAS,GAAG,QAAQ;OACjC,CAAC,MAAM;QACNc,KAAI,CAAClB,MAAM,CAACC,OAAO,GAAG,SAAS;QAC/BiB,KAAI,CAAClB,MAAM,CAACG,QAAQ,GAAG,SAAS;QAChCe,KAAI,CAAClB,MAAM,CAACI,SAAS,GAAG,SAAS;;MAGnCc,KAAI,CAACT,UAAU,GAAG,KAAK;IAAC;EAC1B;EAEAc,aAAaA,CAACvB,MAAc;IAC1B,QAAQA,MAAM;MACZ,KAAK,QAAQ;QAAE,OAAO,UAAU;MAChC,KAAK,SAAS;QAAE,OAAO,YAAY;MACnC,KAAK,UAAU;QAAE,OAAO,iBAAiB;MACzC;QAAS,OAAO,SAAS;;EAE7B;EAEAwB,YAAYA,CAAA;IACV,IAAI,CAAC3B,eAAe,CAACuB,QAAQ,EAAE,CAACP,SAAS,CAAC;MACxCY,IAAI,EAAGC,KAAK,IAAI;QACd,IAAI,CAAC3B,YAAY,CAAC4B,WAAW,CAAC,GAAGD,KAAK,CAACE,MAAM,+BAA+B,CAAC;MAC/E,CAAC;MACDC,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAC9B,YAAY,CAAC+B,SAAS,CAAC,+CAA+C,CAAC;MAC9E;KACD,CAAC;EACJ;EAEAC,UAAUA,CAAA;IACR,MAAMC,MAAM,GAAG,IAAI,CAAClC,YAAY,CAACmC,kBAAkB,EAAE;IACrD,IAAI,CAAClC,YAAY,CAACmC,QAAQ,CAAC,GAAGF,MAAM,CAACJ,MAAM,qBAAqB,CAAC;EACnE;EAEAO,iBAAiBA,CAAA;IACf,IAAI,CAACpC,YAAY,CAAC4B,WAAW,CAAC,+BAA+B,CAAC;IAC9DS,UAAU,CAAC,MAAK;MACd,IAAI,CAACrC,YAAY,CAACmC,QAAQ,CAAC,6BAA6B,CAAC;IAC3D,CAAC,EAAE,IAAI,CAAC;IACRE,UAAU,CAAC,MAAK;MACd,IAAI,CAACrC,YAAY,CAACsC,WAAW,CAAC,+BAA+B,CAAC;IAChE,CAAC,EAAE,IAAI,CAAC;EACV;;;uBAnGW3C,qBAAqB,EAAA4C,+DAAA,CAAAE,qEAAA,GAAAF,+DAAA,CAAAI,wEAAA,GAAAJ,+DAAA,CAAAM,iEAAA,GAAAN,+DAAA,CAAAQ,iEAAA;IAAA;EAAA;;;YAArBpD,qBAAqB;MAAAsD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAzK9BhB,4DAAA,aAAmF;UAG7EA,uDAAA,WAAmD;UACnDA,oDAAA,kCACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,gBAIC;UAHCA,wDAAA,mBAAAuB,uDAAA;YAAA,OAASN,GAAA,CAAA5C,iBAAA,EAAmB;UAAA,EAAC;UAI7B2B,uDAAA,WAAiE;UACjEA,oDAAA,GACF;UAAAA,0DAAA,EAAS;UAGXA,4DAAA,aAAmD;UAIjBA,oDAAA,eAAO;UAAAA,0DAAA,EAAO;UAC1CA,4DAAA,eAA+B;UAC7BA,uDAAA,eAMO;UACPA,4DAAA,gBAE8D;UAC5DA,oDAAA,IACF;UAAAA,0DAAA,EAAO;UAMbA,4DAAA,cAAyB;UAEOA,oDAAA,gBAAQ;UAAAA,0DAAA,EAAO;UAC3CA,4DAAA,eAA+B;UAC7BA,uDAAA,eAMO;UACPA,4DAAA,gBAE+D;UAC7DA,oDAAA,IACF;UAAAA,0DAAA,EAAO;UAMbA,4DAAA,cAAyB;UAEOA,oDAAA,4BAAe;UAAAA,0DAAA,EAAO;UAClDA,4DAAA,eAA+B;UAC7BA,uDAAA,eAMO;UACPA,4DAAA,gBAE+D;UAC7DA,oDAAA,IACF;UAAAA,0DAAA,EAAO;UAMbA,4DAAA,cAAyB;UAEOA,oDAAA,iBAAS;UAAAA,0DAAA,EAAO;UAC5CA,4DAAA,eAA+B;UAC7BA,uDAAA,eAMO;UACPA,4DAAA,gBAEgE;UAC9DA,oDAAA,IACF;UAAAA,0DAAA,EAAO;UAOfA,4DAAA,eAEgE;UAEhCA,oDAAA,4BAAe;UAAAA,0DAAA,EAAO;UAClDA,4DAAA,gBAC+D;UAC7DA,oDAAA,IACF;UAAAA,0DAAA,EAAO;UAETA,4DAAA,aAAsC;UACpCA,oDAAA,IAGF;UAAAA,0DAAA,EAAI;UAINA,4DAAA,eAA0C;UAEVA,oDAAA,yBAAY;UAAAA,0DAAA,EAAO;UAC/CA,4DAAA,gBAAoC;UAAAA,oDAAA,IAAkB;UAAAA,0DAAA,EAAO;UAKjEA,4DAAA,eAAoD;UAClDA,oDAAA,IACF;;UAAAA,0DAAA,EAAM;UAGNA,4DAAA,eAAwD;UAEpDA,wDAAA,mBAAAwB,wDAAA;YAAA,OAASP,GAAA,CAAA/B,YAAA,EAAc;UAAA,EAAC;UAGxBc,uDAAA,aAAoC;UACpCA,oDAAA,2BACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAGC;UAFCA,wDAAA,mBAAAyB,wDAAA;YAAA,OAASR,GAAA,CAAAxB,UAAA,EAAY;UAAA,EAAC;UAGtBO,uDAAA,aAAmC;UACnCA,oDAAA,0BACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAGC;UAFCA,wDAAA,mBAAA0B,wDAAA;YAAA,OAAST,GAAA,CAAApB,iBAAA,EAAmB;UAAA,EAAC;UAG7BG,uDAAA,aAAgC;UAChCA,oDAAA,qBACF;UAAAA,0DAAA,EAAS;;;UAlJPA,uDAAA,GAAuB;UAAvBA,wDAAA,aAAAiB,GAAA,CAAA9C,UAAA,CAAuB;UAES6B,uDAAA,GAA4B;UAA5BA,yDAAA,YAAAiB,GAAA,CAAA9C,UAAA,CAA4B;UAC5D6B,uDAAA,GACF;UADEA,gEAAA,MAAAiB,GAAA,CAAA9C,UAAA,8CACF;UAWQ6B,uDAAA,GAAkD;UAAlDA,yDAAA,iBAAAiB,GAAA,CAAAvD,MAAA,CAAAC,OAAA,cAAkD,eAAAsD,GAAA,CAAAvD,MAAA,CAAAC,OAAA,iCAAAsD,GAAA,CAAAvD,MAAA,CAAAC,OAAA,kCAAAsD,GAAA,CAAAvD,MAAA,CAAAC,OAAA;UAK9BqC,uDAAA,GAAoD;UAApDA,yDAAA,mBAAAiB,GAAA,CAAAvD,MAAA,CAAAC,OAAA,cAAoD,iBAAAsD,GAAA,CAAAvD,MAAA,CAAAC,OAAA,mCAAAsD,GAAA,CAAAvD,MAAA,CAAAC,OAAA;UAGxEqC,uDAAA,GACF;UADEA,gEAAA,MAAAiB,GAAA,CAAAhC,aAAA,CAAAgC,GAAA,CAAAvD,MAAA,CAAAC,OAAA,OACF;UAYEqC,uDAAA,GAAmD;UAAnDA,yDAAA,iBAAAiB,GAAA,CAAAvD,MAAA,CAAAE,QAAA,cAAmD,eAAAqD,GAAA,CAAAvD,MAAA,CAAAE,QAAA,iCAAAqD,GAAA,CAAAvD,MAAA,CAAAE,QAAA,kCAAAqD,GAAA,CAAAvD,MAAA,CAAAE,QAAA;UAK/BoC,uDAAA,GAAqD;UAArDA,yDAAA,mBAAAiB,GAAA,CAAAvD,MAAA,CAAAE,QAAA,cAAqD,iBAAAqD,GAAA,CAAAvD,MAAA,CAAAE,QAAA,mCAAAqD,GAAA,CAAAvD,MAAA,CAAAE,QAAA;UAGzEoC,uDAAA,GACF;UADEA,gEAAA,MAAAiB,GAAA,CAAAhC,aAAA,CAAAgC,GAAA,CAAAvD,MAAA,CAAAE,QAAA,OACF;UAYEoC,uDAAA,GAAmD;UAAnDA,yDAAA,iBAAAiB,GAAA,CAAAvD,MAAA,CAAAG,QAAA,cAAmD,eAAAoD,GAAA,CAAAvD,MAAA,CAAAG,QAAA,iCAAAoD,GAAA,CAAAvD,MAAA,CAAAG,QAAA,kCAAAoD,GAAA,CAAAvD,MAAA,CAAAG,QAAA;UAK/BmC,uDAAA,GAAqD;UAArDA,yDAAA,mBAAAiB,GAAA,CAAAvD,MAAA,CAAAG,QAAA,cAAqD,iBAAAoD,GAAA,CAAAvD,MAAA,CAAAG,QAAA,mCAAAoD,GAAA,CAAAvD,MAAA,CAAAG,QAAA;UAGzEmC,uDAAA,GACF;UADEA,gEAAA,MAAAiB,GAAA,CAAAhC,aAAA,CAAAgC,GAAA,CAAAvD,MAAA,CAAAG,QAAA,OACF;UAYEmC,uDAAA,GAAoD;UAApDA,yDAAA,iBAAAiB,GAAA,CAAAvD,MAAA,CAAAI,SAAA,cAAoD,eAAAmD,GAAA,CAAAvD,MAAA,CAAAI,SAAA,iCAAAmD,GAAA,CAAAvD,MAAA,CAAAI,SAAA,kCAAAmD,GAAA,CAAAvD,MAAA,CAAAI,SAAA;UAKhCkC,uDAAA,GAAsD;UAAtDA,yDAAA,mBAAAiB,GAAA,CAAAvD,MAAA,CAAAI,SAAA,cAAsD,iBAAAmD,GAAA,CAAAvD,MAAA,CAAAI,SAAA,mCAAAmD,GAAA,CAAAvD,MAAA,CAAAI,SAAA;UAG1EkC,uDAAA,GACF;UADEA,gEAAA,MAAAiB,GAAA,CAAAhC,aAAA,CAAAgC,GAAA,CAAAvD,MAAA,CAAAI,SAAA,OACF;UAQHkC,uDAAA,GAA0D;UAA1DA,yDAAA,qBAAAiB,GAAA,CAAAvD,MAAA,CAAAK,QAAA,iBAA0D,mBAAAkD,GAAA,CAAAvD,MAAA,CAAAK,QAAA;UAIrCiC,uDAAA,GAAwD;UAAxDA,yDAAA,mBAAAiB,GAAA,CAAAvD,MAAA,CAAAK,QAAA,iBAAwD,iBAAAkD,GAAA,CAAAvD,MAAA,CAAAK,QAAA;UAE5EiC,uDAAA,GACF;UADEA,gEAAA,MAAAiB,GAAA,CAAAvD,MAAA,CAAAK,QAAA,wDACF;UAGAiC,uDAAA,GAGF;UAHEA,gEAAA,MAAAiB,GAAA,CAAAvD,MAAA,CAAAK,QAAA,8HAGF;UAOsCiC,uDAAA,GAAkB;UAAlBA,+DAAA,CAAAiB,GAAA,CAAAvD,MAAA,CAAAM,KAAA,CAAkB;UAMxDgC,uDAAA,GACF;UADEA,gEAAA,wCAAAA,yDAAA,SAAAiB,GAAA,CAAAvD,MAAA,CAAAO,SAAA,iBACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrJ6D;AACb;AAQrB;AAUG;;;;AAGpC;;;;AAOM,MAAO8E,WAAW;EA0CtB1F,YAAoB2F,MAAc,EAAUC,MAAqB;IAA7C,KAAAD,MAAM,GAANA,MAAM;IAAkB,KAAAC,MAAM,GAANA,MAAM;IAzClD;IACQ,KAAAC,UAAU,GAAG,IAAIjB,iDAAe,CAAc,IAAI,CAAC;IACnD,KAAAkB,YAAY,GAAG,IAAIlB,iDAAe,CAAsB,IAAI,CAAC;IAC7D,KAAAmB,WAAW,GAAG,IAAInB,iDAAe,CAAoB,IAAI,CAAC;IAElE;IACO,KAAAoB,WAAW,GAAG,IAAI,CAACH,UAAU,CAACI,YAAY,EAAE;IAC5C,KAAAC,aAAa,GAAG,IAAI,CAACJ,YAAY,CAACG,YAAY,EAAE;IAChD,KAAAE,YAAY,GAAG,IAAI,CAACJ,WAAW,CAACE,YAAY,EAAE;IAErD;IACQ,KAAAG,aAAa,GAAkB,IAAI;IACnC,KAAAC,SAAS,GAMF,MAAM;IAErB;IACQ,KAAAC,MAAM,GAAwC,EAAE;IAChD,KAAAC,SAAS,GAA+B,EAAE;IAElD;IACQ,KAAAC,cAAc,GAA6B,IAAI;IAC/C,KAAAC,WAAW,GAAuB,IAAI;IACtC,KAAAC,YAAY,GAAuB,IAAI;IACvC,KAAAC,iBAAiB,GAA4B,IAAI;IACjD,KAAAC,kBAAkB,GAA4B,IAAI;IAClD,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,cAAc,GAAG,IAAI;IAE7B;IACiB,KAAAC,SAAS,GAAqB;MAC7CC,UAAU,EAAE,CACV;QAAEC,IAAI,EAAE;MAA8B,CAAE,EACxC;QAAEA,IAAI,EAAE;MAA+B,CAAE;KAE5C;IAGC,IAAI,CAACrB,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,wCAAwC,CAAC;IACzE,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACzB,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,wCAAwC,CAAC;EAC3E;EAEA7F,WAAWA,CAAA;IACT,IAAI,CAACuE,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,8BAA8B,CAAC;IAC/D,IAAI,CAACI,OAAO,EAAE;EAChB;EAEA;EAEA;;;EAGAC,YAAYA,CACVC,WAAmB,EACnBC,QAAkB,EAClBC,cAAuB;IAEvB,IAAI,CAAC9B,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,qBAAqB,EAAE;MACrDM,WAAW;MACXC;KACD,CAAC;IAEF,IAAI,IAAI,CAACpB,SAAS,KAAK,MAAM,EAAE;MAC7B,OAAOxB,gDAAU,CAAC,MAAM,IAAI8C,KAAK,CAAC,qCAAqC,CAAC,CAAC;;IAG3E,IAAI,CAACC,YAAY,CAAC,YAAY,CAAC;IAC/B,MAAMC,MAAM,GAAG,IAAI,CAACC,cAAc,EAAE;IAEpC,OAAO,IAAI,CAACnC,MAAM,CACfoC,MAAM,CAAyB;MAC9BC,QAAQ,EAAE9C,4EAAsB;MAChC+C,SAAS,EAAE;QACTT,WAAW;QACXC,QAAQ;QACRI,MAAM;QACNH;QACA;;KAEH,CAAC,CACDQ,IAAI,CACHpD,mDAAG,CAAEqD,MAAM,IAAI;MACb,MAAMC,IAAI,GAAGD,MAAM,CAACE,IAAI,EAAEd,YAAY;MACtC,IAAI,CAACa,IAAI,EAAE,MAAM,IAAIT,KAAK,CAAC,yBAAyB,CAAC;MAErD,IAAI,CAACW,mBAAmB,CAACF,IAAI,CAAC;MAC9B,OAAOA,IAAI;IACb,CAAC,CAAC,EACFrD,0DAAU,CAAE7C,KAAK,IAAI;MACnB,IAAI,CAAC0D,MAAM,CAAC1D,KAAK,CAAC,aAAa,EAAE,wBAAwB,EAAEA,KAAK,CAAC;MACjE,IAAI,CAAC0F,YAAY,CAAC,MAAM,CAAC;MACzB,OAAO/C,gDAAU,CAAC,MAAM3C,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAqG,UAAUA,CAACH,IAAkB;IAC3B,IAAI,CAACxC,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,mBAAmB,EAAEkB,IAAI,CAACI,EAAE,CAAC;IAE7D,IAAI,CAACJ,IAAI,EAAE;MACT,OAAOvD,gDAAU,CAAC,MAAM,IAAI8C,KAAK,CAAC,mBAAmB,CAAC,CAAC;;IAGzD,IAAI,CAACC,YAAY,CAAC,YAAY,CAAC;IAE/B,OAAO,IAAI,CAACjC,MAAM,CACfoC,MAAM,CAAuB;MAC5BC,QAAQ,EAAE7C,0EAAoB;MAC9B8C,SAAS,EAAE;QACTJ,MAAM,EAAEO,IAAI,CAACI;QACb;;KAEH,CAAC,CACDN,IAAI,CACHpD,mDAAG,CAAEqD,MAAM,IAAI;MACb,MAAMM,YAAY,GAAGN,MAAM,CAACE,IAAI,EAAEE,UAAU;MAC5C,IAAI,CAACE,YAAY,EAAE,MAAM,IAAId,KAAK,CAAC,uBAAuB,CAAC;MAE3D,IAAI,CAACe,kBAAkB,CAACD,YAAY,CAAC;MACrC,OAAOA,YAAY;IACrB,CAAC,CAAC,EACF1D,0DAAU,CAAE7C,KAAK,IAAI;MACnB,IAAI,CAAC0D,MAAM,CAAC1D,KAAK,CAAC,aAAa,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MAChE,IAAI,CAAC0F,YAAY,CAAC,MAAM,CAAC;MACzB,OAAO/C,gDAAU,CAAC,MAAM3C,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAyG,UAAUA,CAACd,MAAc,EAAEe,MAAe;IACxC,IAAI,CAAChD,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,mBAAmB,EAAEW,MAAM,CAAC;IAE5D,IAAI,CAACD,YAAY,CAAC,QAAQ,CAAC;IAE3B,OAAO,IAAI,CAACjC,MAAM,CACfoC,MAAM,CAA8B;MACnCC,QAAQ,EAAE5C,0EAAoB;MAC9B6C,SAAS,EAAE;QAAEJ,MAAM;QAAEe,MAAM,EAAEA,MAAM,IAAI;MAAe;KACvD,CAAC,CACDV,IAAI,CACHpD,mDAAG,CAAEqD,MAAM,IAAI;MACb,MAAMU,OAAO,GAAGV,MAAM,CAACE,IAAI,EAAEM,UAAU;MACvC,IAAI,CAACE,OAAO,EAAE,MAAM,IAAIlB,KAAK,CAAC,uBAAuB,CAAC;MAEtD,IAAI,CAACmB,eAAe,EAAE;MACtB,OAAOD,OAAO;IAChB,CAAC,CAAC,EACF9D,0DAAU,CAAE7C,KAAK,IAAI;MACnB,IAAI,CAAC0D,MAAM,CAAC1D,KAAK,CAAC,aAAa,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MAChE,IAAI,CAAC4G,eAAe,EAAE,CAAC,CAAC;MACxB,OAAOjE,gDAAU,CAAC,MAAM3C,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAEA;;;EAGA6G,OAAOA,CAAClB,MAAc;IACpB,IAAI,CAACjC,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,iBAAiB,EAAEW,MAAM,CAAC;IAE1D,IAAI,CAACD,YAAY,CAAC,QAAQ,CAAC;IAE3B,OAAO,IAAI,CAACjC,MAAM,CACfoC,MAAM,CAA2B;MAChCC,QAAQ,EAAE3C,uEAAiB;MAC3B4C,SAAS,EAAE;QAAEJ;MAAM;KACpB,CAAC,CACDK,IAAI,CACHpD,mDAAG,CAAEqD,MAAM,IAAI;MACb,MAAMU,OAAO,GAAGV,MAAM,CAACE,IAAI,EAAEU,OAAO;MACpC,IAAI,CAACF,OAAO,EAAE,MAAM,IAAIlB,KAAK,CAAC,oBAAoB,CAAC;MAEnD,IAAI,CAACmB,eAAe,EAAE;MACtB,OAAOD,OAAO;IAChB,CAAC,CAAC,EACF9D,0DAAU,CAAE7C,KAAK,IAAI;MACnB,IAAI,CAAC0D,MAAM,CAAC1D,KAAK,CAAC,aAAa,EAAE,oBAAoB,EAAEA,KAAK,CAAC;MAC7D,IAAI,CAAC4G,eAAe,EAAE,CAAC,CAAC;MACxB,OAAOjE,gDAAU,CAAC,MAAM3C,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAEA;EAEA,IAAI8G,WAAWA,CAAA;IACb,OAAO,IAAI,CAACnD,UAAU,CAACoD,KAAK;EAC9B;EAEA,IAAIC,mBAAmBA,CAAA;IACrB,OAAO,IAAI,CAACpD,YAAY,CAACmD,KAAK;EAChC;EAEA,IAAIE,YAAYA,CAAA;IACd,OAAO,IAAI,CAAC9C,SAAS,KAAK,WAAW;EACvC;EAEA,IAAI+C,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAAC/C,SAAS,KAAK,MAAM;EAClC;EAEA;EAEQyB,cAAcA,CAAA;IACpB,OAAO,QAAQjH,IAAI,CAACwI,GAAG,EAAE,IAAIC,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;EACxE;EAEQ7B,YAAYA,CAAC8B,KAA4B;IAC/C,IAAI,CAAC9D,MAAM,CAAC+D,KAAK,CACf,aAAa,EACb,eAAe,IAAI,CAACtD,SAAS,MAAMqD,KAAK,EAAE,CAC3C;IACD,IAAI,CAACrD,SAAS,GAAGqD,KAAK;EACxB;EAEQpB,mBAAmBA,CAACF,IAAU;IACpC,IAAI,CAACxC,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,8BAA8B,EAAEkB,IAAI,CAACI,EAAE,CAAC;IACxE,IAAI,CAACpC,aAAa,GAAGgC,IAAI,CAACI,EAAE;IAC5B,IAAI,CAAC3C,UAAU,CAAC/D,IAAI,CAACsG,IAAI,CAAC;IAC1B,IAAI,CAACR,YAAY,CAAC,SAAS,CAAC;IAC5B,IAAI,CAACgC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAC3B,IAAI,CAACC,sBAAsB,CAACzB,IAAI,CAAC0B,IAAI,CAAC;EACxC;EAEQpB,kBAAkBA,CAACN,IAAU;IACnC,IAAI,CAACxC,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,6BAA6B,EAAEkB,IAAI,CAACI,EAAE,CAAC;IACvE,IAAI,CAAC3C,UAAU,CAAC/D,IAAI,CAACsG,IAAI,CAAC;IAC1B,IAAI,CAACtC,YAAY,CAAChE,IAAI,CAAC,IAAI,CAAC;IAC5B,IAAI,CAAC8F,YAAY,CAAC,WAAW,CAAC;IAC9B,IAAI,CAACmC,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACH,IAAI,CAAC,gBAAgB,CAAC;EAC7B;EAEQd,eAAeA,CAAA;IACrB,IAAI,CAAClD,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,yBAAyB,CAAC;IAC1D,IAAI,CAACU,YAAY,CAAC,MAAM,CAAC;IACzB,IAAI,CAACxB,aAAa,GAAG,IAAI;IACzB,IAAI,CAACP,UAAU,CAAC/D,IAAI,CAAC,IAAI,CAAC;IAC1B,IAAI,CAACgE,YAAY,CAAChE,IAAI,CAAC,IAAI,CAAC;IAC5B,IAAI,CAACkI,aAAa,EAAE;IACpB,IAAI,CAACJ,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACK,aAAa,EAAE;EACtB;EAEQC,kBAAkBA,CAAC9B,IAAkB;IAC3C,IAAI,CAACxC,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,yBAAyB,EAAEkB,IAAI,CAACI,EAAE,CAAC;IACnE,IAAI,CAACpC,aAAa,GAAGgC,IAAI,CAACI,EAAE;IAC5B,IAAI,CAAC1C,YAAY,CAAChE,IAAI,CAACsG,IAAI,CAAC;IAC5B,IAAI,CAACR,YAAY,CAAC,SAAS,CAAC;IAC5B,IAAI,CAACgC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAC3B,IAAI,CAACO,sBAAsB,CAAC/B,IAAI,CAAC;EACnC;EAEQgC,sBAAsBA,CAAChC,IAAU;IACvC,IAAI,CAACxC,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,sBAAsB,EAAEkB,IAAI,CAAC/H,MAAM,CAAC;IAEpE,IAAI+H,IAAI,CAACI,EAAE,KAAK,IAAI,CAACpC,aAAa,EAAE;MAClC,IAAI,CAACP,UAAU,CAAC/D,IAAI,CAACsG,IAAI,CAAC;MAE1B,QAAQA,IAAI,CAAC/H,MAAM;QACjB,KAAK4E,6DAAU,CAACoF,SAAS;UACvB,IAAI,CAACzC,YAAY,CAAC,WAAW,CAAC;UAC9B,IAAI,CAACmC,IAAI,CAAC,UAAU,CAAC;UACrB,IAAI,CAACH,IAAI,CAAC,gBAAgB,CAAC;UAC3B;QACF,KAAK3E,6DAAU,CAACqF,KAAK;QACrB,KAAKrF,6DAAU,CAACsF,QAAQ;UACtB,IAAI,CAACzB,eAAe,EAAE;UACtB;;;EAGR;EAEQ0B,gBAAgBA,CAACC,MAAkB;IACzC,IAAI,CAAC7E,MAAM,CAAC+D,KAAK,CAAC,aAAa,EAAE,uBAAuB,EAAEc,MAAM,CAACX,IAAI,CAAC;IACtE,IAAI,CAAC/D,WAAW,CAACjE,IAAI,CAAC2I,MAAM,CAAC;IAC7B;EACF;EAEA;EAEQtD,gBAAgBA,CAAA;IACtB,IAAI,CAACvB,MAAM,CAAC+D,KAAK,CAAC,aAAa,EAAE,wBAAwB,CAAC;IAC1D,IAAI,CAACe,qBAAqB,EAAE;EAC9B;EAEQA,qBAAqBA,CAAA;IAC3B,IAAI,CAACC,oBAAoB,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC;IAC/D,IAAI,CAACA,oBAAoB,CACvB,gBAAgB,EAChB,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EACxB,GAAG,EACH,KAAK,CACN;IACD,IAAI,CAACA,oBAAoB,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC;EAC1E;EAEQA,oBAAoBA,CAC1BC,IAAY,EACZC,WAAqB,EACrBC,QAAgB,EAChBC,IAAa;IAEb,IAAI;MACF,MAAMC,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAC1CD,MAAc,CAACE,kBAAkB,EAAC,CAAE;MACvC,MAAMC,UAAU,GAAGJ,YAAY,CAACI,UAAU;MAC1C,MAAMC,UAAU,GAAGD,UAAU,GAAGN,QAAQ;MACxC,MAAMQ,MAAM,GAAGN,YAAY,CAACO,YAAY,CAAC,CAAC,EAAEF,UAAU,EAAED,UAAU,CAAC;MACnE,MAAMI,WAAW,GAAGF,MAAM,CAACG,cAAc,CAAC,CAAC,CAAC;MAE5C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,UAAU,EAAEK,CAAC,EAAE,EAAE;QACnC,IAAIC,MAAM,GAAG,CAAC;QACdd,WAAW,CAACe,OAAO,CAAEC,IAAI,IAAI;UAC3B,MAAMC,SAAS,GAAG,GAAG,GAAGjB,WAAW,CAAC5I,MAAM;UAC1C,MAAM8J,KAAK,GAAIL,CAAC,GAAGN,UAAU,GAAIS,IAAI,GAAG,CAAC,GAAGvC,IAAI,CAAC0C,EAAE;UACnDL,MAAM,IAAIrC,IAAI,CAAC2C,GAAG,CAACF,KAAK,CAAC,GAAGD,SAAS;QACvC,CAAC,CAAC;QACF,MAAMI,QAAQ,GAAG5C,IAAI,CAAC2C,GAAG,CAAEP,CAAC,GAAGL,UAAU,GAAI/B,IAAI,CAAC0C,EAAE,CAAC;QACrDR,WAAW,CAACE,CAAC,CAAC,GAAGC,MAAM,GAAGO,QAAQ;;MAGpC,MAAMC,KAAK,GAAG,IAAIC,KAAK,EAAE;MACzBD,KAAK,CAACpB,IAAI,GAAGA,IAAI;MAChBoB,KAAa,CAACE,UAAU,GAAG,MAAK;QAC/B,MAAMC,MAAM,GAAGtB,YAAY,CAACuB,kBAAkB,EAAE;QAChDD,MAAM,CAAChB,MAAM,GAAGA,MAAM;QACtBgB,MAAM,CAACvB,IAAI,GAAGA,IAAI;QAClBuB,MAAM,CAACE,OAAO,CAACxB,YAAY,CAACyB,WAAW,CAAC;QACxCH,MAAM,CAACI,KAAK,EAAE;QACd,IAAI,CAAC3B,IAAI,EAAE;UACTtI,UAAU,CAAC,MAAK;YACd,IAAI,CAAC8D,SAAS,CAACqE,IAAI,CAAC,GAAG,KAAK;UAC9B,CAAC,EAAEE,QAAQ,GAAG,IAAI,CAAC;;QAErB,OAAOwB,MAAM;MACf,CAAC;MAED,IAAI,CAAChG,MAAM,CAACsE,IAAI,CAAC,GAAGuB,KAAK;MACzB,IAAI,CAAC5F,SAAS,CAACqE,IAAI,CAAC,GAAG,KAAK;KAC7B,CAAC,OAAO1I,KAAK,EAAE;MACd,IAAI,CAAC0D,MAAM,CAAC1D,KAAK,CACf,aAAa,EACb,yBAAyB0I,IAAI,IAAI,EACjC1I,KAAK,CACN;;EAEL;EAEQkF,uBAAuBA,CAAA;IAC7B,IAAI,CAACxB,MAAM,CAAC+D,KAAK,CAAC,aAAa,EAAE,+BAA+B,CAAC;IACjE,IAAI,CAACgD,wBAAwB,EAAE;IAC/B,IAAI,CAACC,4BAA4B,EAAE;IACnC,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEQF,wBAAwBA,CAAA;IAC9B,IAAI,CAAChH,MAAM,CACRzE,SAAS,CAAiC;MACzC4L,KAAK,EAAExH,gFAA0B;MACjCyH,WAAW,EAAE;KACd,CAAC,CACD7L,SAAS,CAAC;MACTY,IAAI,EAAEA,CAAC;QAAEuG,IAAI;QAAE2E;MAAM,CAAE,KAAI;QACzB,IAAI3E,IAAI,EAAEvC,YAAY,EAAE;UACtB,IAAI,CAACoE,kBAAkB,CAAC7B,IAAI,CAACvC,YAAY,CAAC;;QAE5C,IAAIkH,MAAM,EAAE;UACV,IAAI,CAACpH,MAAM,CAAC1D,KAAK,CACf,aAAa,EACb,oCAAoC,EACpC8K,MAAM,CACP;;MAEL,CAAC;MACD9K,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC0D,MAAM,CAAC1D,KAAK,CACf,aAAa,EACb,sCAAsC,EACtCA,KAAK,CACN;QACDO,UAAU,CAAC,MAAM,IAAI,CAACkK,wBAAwB,EAAE,EAAE,IAAI,CAAC;MACzD;KACD,CAAC;EACN;EAEQC,4BAA4BA,CAAA;IAClC,IAAI,CAACjH,MAAM,CACRzE,SAAS,CAA8B;MACtC4L,KAAK,EAAEvH,sFAAgC;MACvCwH,WAAW,EAAE;KACd,CAAC,CACD7L,SAAS,CAAC;MACTY,IAAI,EAAEA,CAAC;QAAEuG,IAAI;QAAE2E;MAAM,CAAE,KAAI;QACzB,IAAI3E,IAAI,EAAE4E,iBAAiB,EAAE;UAC3B,IAAI,CAAC7C,sBAAsB,CAAC/B,IAAI,CAAC4E,iBAAiB,CAAC;;QAErD,IAAID,MAAM,EAAE;UACV,IAAI,CAACpH,MAAM,CAAC1D,KAAK,CACf,aAAa,EACb,kCAAkC,EAClC8K,MAAM,CACP;;MAEL,CAAC;MACD9K,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC0D,MAAM,CAAC1D,KAAK,CACf,aAAa,EACb,oCAAoC,EACpCA,KAAK,CACN;QACDO,UAAU,CAAC,MAAM,IAAI,CAACmK,4BAA4B,EAAE,EAAE,IAAI,CAAC;MAC7D;KACD,CAAC;EACN;EAEQC,sBAAsBA,CAAA;IAC5B,IAAI,CAAClH,MAAM,CACRzE,SAAS,CAA6B;MACrC4L,KAAK,EAAEtH,8EAAwB;MAC/BuH,WAAW,EAAE;KACd,CAAC,CACD7L,SAAS,CAAC;MACTY,IAAI,EAAEA,CAAC;QAAEuG,IAAI;QAAE2E;MAAM,CAAE,KAAI;QACzB,IAAI3E,IAAI,EAAE6E,UAAU,EAAE;UACpB,IAAI,CAAC1C,gBAAgB,CAACnC,IAAI,CAAC6E,UAAU,CAAC;;QAExC,IAAIF,MAAM,EAAE;UACV,IAAI,CAACpH,MAAM,CAAC1D,KAAK,CACf,aAAa,EACb,kCAAkC,EAClC8K,MAAM,CACP;;MAEL,CAAC;MACD9K,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC0D,MAAM,CAAC1D,KAAK,CACf,aAAa,EACb,oCAAoC,EACpCA,KAAK,CACN;QACDO,UAAU,CAAC,MAAM,IAAI,CAACoK,sBAAsB,EAAE,EAAE,IAAI,CAAC;MACvD;KACD,CAAC;EACN;EAEQxF,gBAAgBA,CAAA;IACtB,IAAI,CAACzB,MAAM,CAAC+D,KAAK,CAAC,aAAa,EAAE,wBAAwB,CAAC;IAC1D,IAAI,CAACwD,oBAAoB,EAAE;EAC7B;EAEA;EAEQvD,IAAIA,CAACgB,IAAY,EAAEG,IAAA,GAAgB,KAAK;IAC9C,IAAI;MACF,MAAMqC,KAAK,GAAG,IAAI,CAAC9G,MAAM,CAACsE,IAAI,CAAC;MAC/B,IAAI,CAACwC,KAAK,IAAI,IAAI,CAAC7G,SAAS,CAACqE,IAAI,CAAC,EAAE;MAEpC,IAAKwC,KAAa,CAACf,UAAU,EAAE;QAC5Be,KAAa,CAACC,aAAa,GAAID,KAAa,CAACf,UAAU,EAAE;QAC1D,IAAI,CAAC9F,SAAS,CAACqE,IAAI,CAAC,GAAG,IAAI;;KAE9B,CAAC,OAAO1I,KAAK,EAAE;MACd,IAAI,CAAC0D,MAAM,CAAC1D,KAAK,CAAC,aAAa,EAAE,wBAAwB0I,IAAI,IAAI,EAAE1I,KAAK,CAAC;;EAE7E;EAEQ6H,IAAIA,CAACa,IAAY;IACvB,IAAI;MACF,MAAMwC,KAAK,GAAG,IAAI,CAAC9G,MAAM,CAACsE,IAAI,CAAC;MAC/B,IAAI,CAACwC,KAAK,IAAI,CAAC,IAAI,CAAC7G,SAAS,CAACqE,IAAI,CAAC,EAAE;MAErC,IAAKwC,KAAa,CAACC,aAAa,EAAE;QAC/BD,KAAa,CAACC,aAAa,CAACtD,IAAI,EAAE;QAClCqD,KAAa,CAACC,aAAa,GAAG,IAAI;;MAErC,IAAI,CAAC9G,SAAS,CAACqE,IAAI,CAAC,GAAG,KAAK;KAC7B,CAAC,OAAO1I,KAAK,EAAE;MACd,IAAI,CAAC0D,MAAM,CAAC1D,KAAK,CACf,aAAa,EACb,yBAAyB0I,IAAI,IAAI,EACjC1I,KAAK,CACN;;EAEL;EAEQ8H,aAAaA,CAAA;IACnBsD,MAAM,CAACC,IAAI,CAAC,IAAI,CAACjH,MAAM,CAAC,CAACsF,OAAO,CAAEhB,IAAI,IAAK,IAAI,CAACb,IAAI,CAACa,IAAI,CAAC,CAAC;EAC7D;EAEA;EAEQuC,oBAAoBA,CAAA;IAC1B,IAAI;MACF,IAAI,CAAC3G,cAAc,GAAG,IAAIgH,iBAAiB,CAAC,IAAI,CAACzG,SAAS,CAAC;MAC3D,IAAI,CAACnB,MAAM,CAAC+D,KAAK,CAAC,aAAa,EAAE,qCAAqC,CAAC;MAEvE,IAAI,CAACnD,cAAc,CAACiH,cAAc,GAAIC,KAAK,IAAI;QAC7C,IAAIA,KAAK,CAACC,SAAS,IAAI,IAAI,CAACvH,aAAa,EAAE;UACzC,IAAI,CAACwH,UAAU,CAAC,eAAe,EAAEC,IAAI,CAACC,SAAS,CAACJ,KAAK,CAACC,SAAS,CAAC,CAAC;;MAErE,CAAC;MAED,IAAI,CAACnH,cAAc,CAACuH,OAAO,GAAIL,KAAK,IAAI;QACtC,IAAI,CAAC9H,MAAM,CAACsB,IAAI,CACd,aAAa,EACb,wBAAwB,EACxBwG,KAAK,CAACM,KAAK,CAACC,IAAI,CACjB;QACD,IAAI,CAACvH,YAAY,GAAGgH,KAAK,CAACQ,OAAO,CAAC,CAAC,CAAC;QACpC,IAAI,CAACC,kBAAkB,EAAE;MAC3B,CAAC;MAED,IAAI,CAAC3H,cAAc,CAAC4H,uBAAuB,GAAG,MAAK;QACjD,MAAM1E,KAAK,GAAG,IAAI,CAAClD,cAAc,EAAE6H,eAAe;QAClD,IAAI,CAACzI,MAAM,CAAC+D,KAAK,CAAC,aAAa,EAAE,2BAA2B,EAAED,KAAK,CAAC;QAEpE,IAAIA,KAAK,KAAK,WAAW,EAAE;UACzB,IAAI,CAAC9D,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,iCAAiC,CAAC;UAClE,IAAI,CAACU,YAAY,CAAC,WAAW,CAAC;SAC/B,MAAM,IAAI8B,KAAK,KAAK,QAAQ,EAAE;UAC7B,IAAI,CAAC9D,MAAM,CAAC1D,KAAK,CAAC,aAAa,EAAE,4BAA4B,CAAC;UAC9D,IAAI,CAAC4G,eAAe,EAAE;;MAE1B,CAAC;KACF,CAAC,OAAO5G,KAAK,EAAE;MACd,IAAI,CAAC0D,MAAM,CAAC1D,KAAK,CAAC,aAAa,EAAE,gCAAgC,EAAEA,KAAK,CAAC;;EAE7E;EAEc2H,sBAAsBA,CAACpC,QAAkB;IAAA,IAAAlG,KAAA;IAAA,OAAAC,kKAAA;MACrD,IAAI;QACFD,KAAI,CAACqE,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,iCAAiC,CAAC;QAClE,MAAMoH,MAAM,SAAS/M,KAAI,CAACgN,YAAY,CAAC9G,QAAQ,CAAC;QAChDlG,KAAI,CAACiN,8BAA8B,CAACF,MAAM,CAAC;QAC3C/M,KAAI,CAACkN,iBAAiB,EAAE;OACzB,CAAC,OAAOvM,KAAK,EAAE;QACdX,KAAI,CAACqE,MAAM,CAAC1D,KAAK,CACf,aAAa,EACb,qCAAqC,EACrCA,KAAK,CACN;;IACF;EACH;EAEciI,sBAAsBA,CAAC/B,IAAkB;IAAA,IAAAsG,MAAA;IAAA,OAAAlN,kKAAA;MACrD,IAAI;QACFkN,MAAI,CAAC9I,MAAM,CAAC+D,KAAK,CAAC,aAAa,EAAE,oCAAoC,CAAC;QACtE,IAAI,CAAC+E,MAAI,CAAClI,cAAc,EAAE;UACxBkI,MAAI,CAACvB,oBAAoB,EAAE;;QAE7B,MAAMmB,MAAM,SAASI,MAAI,CAACH,YAAY,CAACnG,IAAI,CAAC0B,IAAI,CAAC;QACjD4E,MAAI,CAACF,8BAA8B,CAACF,MAAM,CAAC;OAC5C,CAAC,OAAOpM,KAAK,EAAE;QACdwM,MAAI,CAAC9I,MAAM,CAAC1D,KAAK,CACf,aAAa,EACb,oCAAoC,EACpCA,KAAK,CACN;;IACF;EACH;EAEcqM,YAAYA,CAAC9G,QAAkB;IAAA,IAAAkH,MAAA;IAAA,OAAAnN,kKAAA;MAC3C,MAAMoN,WAAW,GAA2B;QAC1CzC,KAAK,EAAE,IAAI;QACX0C,KAAK,EAAEpH,QAAQ,KAAKzC,2DAAQ,CAAC8J;OAC9B;MAED,IAAI;QACF,MAAMR,MAAM,SAASS,SAAS,CAACC,YAAY,CAACT,YAAY,CAACK,WAAW,CAAC;QACrED,MAAI,CAAClI,WAAW,GAAG6H,MAAM;QACzB,OAAOA,MAAM;OACd,CAAC,OAAOpM,KAAK,EAAE;QACdyM,MAAI,CAAC/I,MAAM,CAAC1D,KAAK,CAAC,aAAa,EAAE,2BAA2B,EAAEA,KAAK,CAAC;QACpE,MAAMA,KAAK;;IACZ;EACH;EAEQsM,8BAA8BA,CAACF,MAAmB;IACxD,IAAI,CAAC,IAAI,CAAC9H,cAAc,EAAE;IAE1B8H,MAAM,CAACW,SAAS,EAAE,CAACrD,OAAO,CAAEoC,KAAK,IAAI;MACnC,IAAI,CAACxH,cAAe,CAAC0I,QAAQ,CAAClB,KAAK,EAAEM,MAAM,CAAC;IAC9C,CAAC,CAAC;EACJ;EAEQG,iBAAiBA,CAAA;IACvB,IAAI,IAAI,CAAC9H,iBAAiB,IAAI,IAAI,CAACF,WAAW,EAAE;MAC9C,IAAI,CAACE,iBAAiB,CAACwI,SAAS,GAAG,IAAI,CAAC1I,WAAW;;EAEvD;EAEQ0H,kBAAkBA,CAAA;IACxB,IAAI,IAAI,CAACvH,kBAAkB,IAAI,IAAI,CAACF,YAAY,EAAE;MAChD,IAAI,CAACE,kBAAkB,CAACuI,SAAS,GAAG,IAAI,CAACzI,YAAY;;EAEzD;EAEQkH,UAAUA,CAACwB,UAAkB,EAAEC,UAAkB;IACvD,IAAI,CAAC,IAAI,CAACjJ,aAAa,EAAE;IAEzB,IAAI,CAACT,MAAM,CACRoC,MAAM,CAAC;MACNC,QAAQ,EAAEvC,+EAAyB;MACnCwC,SAAS,EAAE;QACTJ,MAAM,EAAE,IAAI,CAACzB,aAAa;QAC1BgJ,UAAU;QACVC;;KAEH,CAAC,CACDnO,SAAS,CAAC;MACTY,IAAI,EAAEA,CAAA,KACJ,IAAI,CAAC8D,MAAM,CAAC+D,KAAK,CAAC,aAAa,EAAE,cAAc,EAAEyF,UAAU,CAAC;MAC9DlN,KAAK,EAAGA,KAAK,IACX,IAAI,CAAC0D,MAAM,CAAC1D,KAAK,CAAC,aAAa,EAAE,uBAAuB,EAAEA,KAAK;KAClE,CAAC;EACN;EAEA;EAEQ+H,aAAaA,CAAA;IACnB,IAAI,CAACrE,MAAM,CAAC+D,KAAK,CAAC,aAAa,EAAE,8BAA8B,CAAC;IAEhE,IAAI,IAAI,CAAClD,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACwI,SAAS,EAAE,CAACrD,OAAO,CAAEoC,KAAK,IAAKA,KAAK,CAACjE,IAAI,EAAE,CAAC;MAC7D,IAAI,CAACtD,WAAW,GAAG,IAAI;;IAGzB,IAAI,IAAI,CAACC,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,GAAG,IAAI;;IAG1B,IAAI,IAAI,CAACF,cAAc,EAAE;MACvB,IAAI,CAACA,cAAc,CAAC8I,KAAK,EAAE;MAC3B,IAAI,CAAC9I,cAAc,GAAG,IAAI;;IAG5B,IAAI,IAAI,CAACG,iBAAiB,EAAE;MAC1B,IAAI,CAACA,iBAAiB,CAACwI,SAAS,GAAG,IAAI;;IAGzC,IAAI,IAAI,CAACvI,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAACuI,SAAS,GAAG,IAAI;;IAG1C;IACA,IAAI,CAAChC,oBAAoB,EAAE;EAC7B;EAEQ7F,OAAOA,CAAA;IACb,IAAI,CAAC0C,aAAa,EAAE;IACpB,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACpE,UAAU,CAAC0J,QAAQ,EAAE;IAC1B,IAAI,CAACzJ,YAAY,CAACyJ,QAAQ,EAAE;IAC5B,IAAI,CAACxJ,WAAW,CAACwJ,QAAQ,EAAE;EAC7B;EAEA;EAEA;;;EAGAC,mBAAmBA,CACjBC,UAA4B,EAC5BC,WAA6B;IAE7B,IAAI,CAAC/I,iBAAiB,GAAG8I,UAAU;IACnC,IAAI,CAAC7I,kBAAkB,GAAG8I,WAAW;IAErC,IAAI,IAAI,CAACjJ,WAAW,EAAE;MACpB,IAAI,CAACgI,iBAAiB,EAAE;;IAE1B,IAAI,IAAI,CAAC/H,YAAY,EAAE;MACrB,IAAI,CAACyH,kBAAkB,EAAE;;EAE7B;EAEA;;;EAGAwB,WAAWA,CAAA;IACT,IAAI,CAAC9I,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C,IAAI,IAAI,CAACJ,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACmJ,cAAc,EAAE,CAAChE,OAAO,CAAEoC,KAAK,IAAI;QAClDA,KAAK,CAAC6B,OAAO,GAAG,IAAI,CAAChJ,cAAc;MACrC,CAAC,CAAC;;IAEJ,OAAO,IAAI,CAACA,cAAc;EAC5B;EAEA;;;EAGAiJ,WAAWA,CAAA;IACT,IAAI,CAAChJ,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C,IAAI,IAAI,CAACL,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACsJ,cAAc,EAAE,CAACnE,OAAO,CAAEoC,KAAK,IAAI;QAClDA,KAAK,CAAC6B,OAAO,GAAG,IAAI,CAAC/I,cAAc;MACrC,CAAC,CAAC;;IAEJ,OAAO,IAAI,CAACA,cAAc;EAC5B;EAEA;;;EAGAkJ,gBAAgBA,CACdP,UAA4B,EAC5BC,WAA6B;IAE7B,IAAI,CAACF,mBAAmB,CAACC,UAAU,EAAEC,WAAW,CAAC;EACnD;EAEA;;;EAGA,IAAIO,YAAYA,CAAA;IACd,OAAO,IAAI,CAACpJ,cAAc;EAC5B;EAEA;;;EAGA,IAAIqJ,YAAYA,CAAA;IACd,OAAO,IAAI,CAACpJ,cAAc;EAC5B;EAEA;;;EAGA,IAAIqJ,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAAC1J,WAAW;EACzB;EAEA;;;EAGA,IAAI2J,iBAAiBA,CAAA;IACnB,OAAO,IAAI,CAAC1J,YAAY;EAC1B;EAEA;;;EAGA2J,YAAYA,CAAA;IACV,IAAI,CAACzK,MAAM,CAAC+D,KAAK,CACf,aAAa,EACb,8CAA8C,CAC/C;EACH;EAEA;;;EAGA2G,aAAaA,CAAA;IACX,IAAI,CAAC1K,MAAM,CAAC+D,KAAK,CAAC,aAAa,EAAE,kBAAkB,CAAC;IACpD,IAAI,CAACK,aAAa,EAAE;EACtB;;;uBApwBWtE,WAAW,EAAA/C,sDAAA,CAAAE,kDAAA,GAAAF,sDAAA,CAAAI,0DAAA;IAAA;EAAA;;;aAAX2C,WAAW;MAAAgL,OAAA,EAAXhL,WAAW,CAAAiL,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClBoD;AAShD;AAWkB;;;;;;;;;;;;;;;ICMhCjO,4DAAA,WAA2C;IACzCA,oDAAA,GACF;IAAAA,0DAAA,EAAO;;;;IADLA,uDAAA,GACF;IADEA,gEAAA,MAAAwO,MAAA,CAAAC,oBAAA,CAAAC,YAAA,kBAAAF,MAAA,CAAAC,oBAAA,CAAAC,YAAA,CAAApP,MAAA,mBACF;;;;;IACAU,4DAAA,WACG;IAAAA,oDAAA,eAAQ;IAAAA,0DAAA,EACV;;;;;IACDA,4DAAA,WACG;IAAAA,oDAAA,iBAAU;IAAAA,0DAAA,EACZ;;;;;;IAOLA,4DAAA,iBAKC;IAHCA,wDAAA,mBAAA2O,sEAAA;MAAA3O,2DAAA,CAAA6O,IAAA;MAAA,MAAAC,OAAA,GAAA9O,2DAAA;MAAA,OAASA,yDAAA,CAAA8O,OAAA,CAAAG,cAAA,EAAgB;IAAA,EAAC;IAI1BjP,uDAAA,YAA4B;IAC9BA,0DAAA,EAAS;;;;;;IAGTA,4DAAA,iBAKC;IAHCA,wDAAA,mBAAAkP,sEAAA;MAAAlP,2DAAA,CAAAmP,IAAA;MAAA,MAAAC,OAAA,GAAApP,2DAAA;MAAA,OAASA,yDAAA,CAAAoP,OAAA,CAAAC,cAAA,EAAgB;IAAA,EAAC;IAI1BrP,uDAAA,YAA4B;IAC9BA,0DAAA,EAAS;;;;;IAcXA,4DAAA,cAAwD;IACtDA,uDAAA,cAEO;IACTA,0DAAA,EAAM;;;;;IASJA,uDAAA,cAKE;;;;IAHAA,wDAAA,QAAAsP,WAAA,CAAAC,MAAA,CAAAC,KAAA,yCAAAxP,2DAAA,CAAmE,QAAAsP,WAAA,CAAAC,MAAA,CAAAG,QAAA;;;;;IAYnE1P,4DAAA,cAGC;IACCA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAAsP,WAAA,CAAAC,MAAA,kBAAAD,WAAA,CAAAC,MAAA,CAAAG,QAAA,MACF;;;;;IAGA1P,4DAAA,cAAwD;IAEpDA,oDAAA,GACF;IAAAA,0DAAA,EAAM;IACNA,4DAAA,cAA4C;IAC1CA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IAJJA,uDAAA,GACF;IADEA,gEAAA,0BAAAsP,WAAA,CAAAK,OAAA,CAAAJ,MAAA,kBAAAD,WAAA,CAAAK,OAAA,CAAAJ,MAAA,CAAAG,QAAA,MACF;IAEE1P,uDAAA,GACF;IADEA,gEAAA,MAAAsP,WAAA,CAAAK,OAAA,CAAAC,OAAA,MACF;;;;;IAMA5P,4DAAA,cAA2D;IACzDA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAAsP,WAAA,CAAAM,OAAA,MACF;;;;;IAUE5P,4DAAA,cAAuD;IACrDA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAAsP,WAAA,CAAAM,OAAA,MACF;;;;;;IATF5P,4DAAA,cAA6D;IAKzDA,wDAAA,mBAAA6P,sEAAA;MAAA7P,2DAAA,CAAA8P,IAAA;MAAA,MAAAR,WAAA,GAAAtP,2DAAA,GAAA+P,SAAA;MAAA,MAAAC,OAAA,GAAAhQ,2DAAA;MAAA,OAASA,yDAAA,CAAAgQ,OAAA,CAAAC,eAAA,CAAAX,WAAA,CAAAY,WAAA,kBAAAZ,WAAA,CAAAY,WAAA,CAAsC,CAAC,EAAE;IAAA,EAAC;IAJrDlQ,0DAAA,EAKE;IACFA,wDAAA,IAAAoQ,sDAAA,kBAEM;IACRpQ,0DAAA,EAAM;;;;IARFA,uDAAA,GAAqC;IAArCA,wDAAA,QAAAsP,WAAA,CAAAY,WAAA,kBAAAZ,WAAA,CAAAY,WAAA,qBAAAZ,WAAA,CAAAY,WAAA,IAAAG,GAAA,EAAArQ,2DAAA,CAAqC,QAAAsP,WAAA,CAAAY,WAAA,kBAAAZ,WAAA,CAAAY,WAAA,qBAAAZ,WAAA,CAAAY,WAAA,IAAAjI,IAAA;IAKjCjI,uDAAA,GAAqB;IAArBA,wDAAA,SAAAsP,WAAA,CAAAM,OAAA,CAAqB;;;;;;IAM7B5P,4DAAA,cAA2D;IACzDA,uDAAA,YAAqC;IACrCA,4DAAA,cAAuB;IACEA,oDAAA,GAAoC;IAAAA,0DAAA,EAAM;IACjEA,4DAAA,cAAuB;IACrBA,oDAAA,GACF;IAAAA,0DAAA,EAAM;IAERA,4DAAA,iBAGC;IADCA,wDAAA,mBAAAsQ,yEAAA;MAAAtQ,2DAAA,CAAAuQ,IAAA;MAAA,MAAAjB,WAAA,GAAAtP,2DAAA,GAAA+P,SAAA;MAAA,MAAAS,OAAA,GAAAxQ,2DAAA;MAAA,OAASA,yDAAA,CAAAwQ,OAAA,CAAAC,YAAA,CAAAnB,WAAA,CAAAY,WAAA,kBAAAZ,WAAA,CAAAY,WAAA,CAAmC,CAAC,EAAE;IAAA,EAAC;IAEhDlQ,uDAAA,YAA+B;IACjCA,0DAAA,EAAS;;;;;IAVgBA,uDAAA,GAAoC;IAApCA,+DAAA,CAAAsP,WAAA,CAAAY,WAAA,kBAAAZ,WAAA,CAAAY,WAAA,qBAAAZ,WAAA,CAAAY,WAAA,IAAAjI,IAAA,CAAoC;IAEzDjI,uDAAA,GACF;IADEA,gEAAA,MAAA0Q,OAAA,CAAAC,cAAA,CAAArB,WAAA,CAAAY,WAAA,kBAAAZ,WAAA,CAAAY,WAAA,qBAAAZ,WAAA,CAAAY,WAAA,IAAAU,IAAA,OACF;;;;;;IAWJ5Q,4DAAA,cAAqE;IACpCA,wDAAA,mBAAA6Q,yEAAA;MAAA7Q,2DAAA,CAAA8Q,IAAA;MAAA,MAAAxB,WAAA,GAAAtP,2DAAA,GAAA+P,SAAA;MAAA,MAAAgB,OAAA,GAAA/Q,2DAAA;MAAA,OAASA,yDAAA,CAAA+Q,OAAA,CAAAC,gBAAA,CAAA1B,WAAA,CAAyB;IAAA,EAAC;IAChEtP,uDAAA,YAA8C;IAChDA,0DAAA,EAAS;IACTA,4DAAA,cAA4B;IAC1BA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAAiR,OAAA,CAAAC,cAAA,CAAA5B,WAAA,CAAAY,WAAA,kBAAAZ,WAAA,CAAAY,WAAA,qBAAAZ,WAAA,CAAAY,WAAA,IAAA/H,QAAA,OACF;;;;;IAUAnI,4DAAA,cAAuD;IACrDA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAAsP,WAAA,CAAAM,OAAA,MACF;;;;;IARF5P,4DAAA,cAA6D;IAC3DA,uDAAA,gBAIS;IACTA,wDAAA,IAAAmR,uDAAA,kBAEM;IACRnR,0DAAA,EAAM;;;;IAPFA,uDAAA,GAAqC;IAArCA,wDAAA,QAAAsP,WAAA,CAAAY,WAAA,kBAAAZ,WAAA,CAAAY,WAAA,qBAAAZ,WAAA,CAAAY,WAAA,IAAAG,GAAA,EAAArQ,2DAAA,CAAqC;IAIjCA,uDAAA,GAAqB;IAArBA,wDAAA,SAAAsP,WAAA,CAAAM,OAAA,CAAqB;;;;;;IAW7B5P,4DAAA,eAIC;IADCA,wDAAA,mBAAAoR,+EAAA;MAAA,MAAAC,WAAA,GAAArR,2DAAA,CAAAsR,IAAA;MAAA,MAAAC,YAAA,GAAAF,WAAA,CAAAtB,SAAA;MAAA,MAAAT,WAAA,GAAAtP,2DAAA,IAAA+P,SAAA;MAAA,MAAAyB,OAAA,GAAAxR,2DAAA;MAAA,OAASA,yDAAA,CAAAwR,OAAA,CAAAC,cAAA,CAAAnC,WAAA,EAAAiC,YAAA,CAAAG,KAAA,CAAuC;IAAA,EAAC;IAEjD1R,oDAAA,GACF;IAAAA,0DAAA,EAAO;;;;IADLA,uDAAA,GACF;IADEA,gEAAA,MAAAuR,YAAA,CAAAG,KAAA,OAAAH,YAAA,CAAAK,KAAA,MACF;;;;;IAVF5R,4DAAA,cAGC;IACCA,wDAAA,IAAA6R,wDAAA,mBAMO;IACT7R,0DAAA,EAAM;;;;IANmBA,uDAAA,GAAoB;IAApBA,wDAAA,YAAAsP,WAAA,CAAAwC,SAAA,CAAoB;;;;;IAwBvC9R,uDAAA,aAIK;;;;;IAGLA,uDAAA,aAIK;;;;;IAGLA,uDAAA,aAIK;;;;;IAGLA,uDAAA,aAIK;;;;;;IAGLA,4DAAA,aAKC;IADCA,wDAAA,mBAAA+R,yEAAA;MAAA/R,2DAAA,CAAAgS,IAAA;MAAA,MAAA1C,WAAA,GAAAtP,2DAAA,IAAA+P,SAAA;MAAA,MAAAkC,OAAA,GAAAjS,2DAAA;MAAA,OAASA,yDAAA,CAAAiS,OAAA,CAAAC,YAAA,CAAA5C,WAAA,CAAqB;IAAA,EAAC;IAChCtP,0DAAA,EAAI;;;;;IAGLA,uDAAA,aAGK;;;;;IAhDTA,4DAAA,cAGC;IAMGA,wDAAA,IAAAmS,qDAAA,gBAIK;IAGLnS,wDAAA,IAAAoS,qDAAA,gBAIK;IAGLpS,wDAAA,IAAAqS,qDAAA,gBAIK;IAGLrS,wDAAA,IAAAsS,qDAAA,gBAIK;IAGLtS,wDAAA,IAAAuS,qDAAA,gBAKK;IAGLvS,wDAAA,IAAAwS,qDAAA,gBAGK;IACPxS,0DAAA,EAAM;;;;;IA5CJA,uDAAA,GAAsC;IAAtCA,wDAAA,aAAAyS,OAAA,CAAAC,gBAAA,CAAApD,WAAA,EAAsC;IAKnCtP,uDAAA,GAAuB;IAAvBA,wDAAA,2BAAuB;IAOvBA,uDAAA,GAAoB;IAApBA,wDAAA,wBAAoB;IAOpBA,uDAAA,GAAyB;IAAzBA,wDAAA,6BAAyB;IAOzBA,uDAAA,GAAoB;IAApBA,wDAAA,wBAAoB;IAOpBA,uDAAA,GAAsB;IAAtBA,wDAAA,0BAAsB;;;;;;IAoD7BA,4DAAA,kBAKC;IAFCA,wDAAA,mBAAA2S,mFAAA;MAAA,MAAAtB,WAAA,GAAArR,2DAAA,CAAA4S,IAAA;MAAA,MAAAC,SAAA,GAAAxB,WAAA,CAAAtB,SAAA;MAAA,MAAAT,WAAA,GAAAtP,2DAAA,IAAA+P,SAAA;MAAA,MAAA+C,OAAA,GAAA9S,2DAAA;MAAA,OAASA,yDAAA,CAAA8S,OAAA,CAAArB,cAAA,CAAAnC,WAAA,EAAAuD,SAAA,CAA8B;IAAA,EAAC;IAGxC7S,oDAAA,GACF;IAAAA,0DAAA,EAAS;;;;IAHPA,wDAAA,gCAAA6S,SAAA,CAAgC;IAEhC7S,uDAAA,GACF;IADEA,gEAAA,MAAA6S,SAAA,MACF;;;;;IAXF7S,4DAAA,eAGC;IACCA,wDAAA,IAAA+S,0DAAA,sBAOS;IACX/S,0DAAA,EAAM;;;;IAPgBA,uDAAA,GAAc;IAAdA,wDAAA,YAAAgT,OAAA,CAAAC,WAAA,CAAc;;;;;;IAjNxCjT,4DAAA,cAIC;IAECA,wDAAA,IAAAkT,gDAAA,kBAKE;IAGFlT,4DAAA,cAIC;IAECA,wDAAA,IAAAmT,gDAAA,kBAKM;IAGNnT,wDAAA,IAAAoT,gDAAA,kBAOM;IAGNpT,4DAAA,cAA+B;IAE7BA,wDAAA,IAAAqT,gDAAA,kBAEM;IAGNrT,wDAAA,IAAAsT,gDAAA,kBAUM;IAGNtT,wDAAA,IAAAuT,gDAAA,kBAcM;IAGNvT,wDAAA,IAAAwT,gDAAA,kBAOM;IAGNxT,wDAAA,KAAAyT,iDAAA,kBASM;IACRzT,0DAAA,EAAM;IAGNA,wDAAA,KAAA0T,iDAAA,kBAWM;IAGN1T,4DAAA,eAAoD;IAEhDA,oDAAA,IACF;IAAAA,0DAAA,EAAO;IAGPA,wDAAA,KAAA2T,iDAAA,kBAkDM;IACR3T,0DAAA,EAAM;IAGNA,4DAAA,eAEC;IAIGA,wDAAA,mBAAA4T,oEAAA;MAAA,MAAAvC,WAAA,GAAArR,2DAAA,CAAA6T,IAAA;MAAA,MAAAvE,WAAA,GAAA+B,WAAA,CAAAtB,SAAA;MAAA,MAAA+D,OAAA,GAAA9T,2DAAA;MAAA,OAASA,yDAAA,CAAA8T,OAAA,CAAAC,oBAAA,CAAAzE,WAAA,CAA6B;IAAA,EAAC;IAGvCtP,uDAAA,aAAoC;IACtCA,0DAAA,EAAS;IAGTA,4DAAA,kBAIC;IAFCA,wDAAA,mBAAAgU,oEAAA;MAAA,MAAA3C,WAAA,GAAArR,2DAAA,CAAA6T,IAAA;MAAA,MAAAvE,WAAA,GAAA+B,WAAA,CAAAtB,SAAA;MAAA,MAAAkE,OAAA,GAAAjU,2DAAA;MAAA,OAASA,yDAAA,CAAAiU,OAAA,CAAAC,UAAA,CAAA5E,WAAA,CAAmB;IAAA,EAAC;IAG7BtP,uDAAA,aAAoC;IACtCA,0DAAA,EAAS;IAGTA,4DAAA,kBAIC;IAFCA,wDAAA,mBAAAmU,oEAAA;MAAA,MAAA9C,WAAA,GAAArR,2DAAA,CAAA6T,IAAA;MAAA,MAAAvE,WAAA,GAAA+B,WAAA,CAAAtB,SAAA;MAAA,MAAAqE,OAAA,GAAApU,2DAAA;MAAA,OAASA,yDAAA,CAAAoU,OAAA,CAAAC,eAAA,CAAA/E,WAAA,CAAwB;IAAA,EAAC;IAGlCtP,uDAAA,aAAyC;IAC3CA,0DAAA,EAAS;IAIXA,wDAAA,KAAAsU,iDAAA,kBAYM;IACRtU,0DAAA,EAAM;;;;;IAtNNA,yDAAA,eAAAuU,MAAA,CAAAC,WAAA,CAAAlF,WAAA,EAAyC;IAItCtP,uDAAA,GAA6C;IAA7CA,wDAAA,UAAAuU,MAAA,CAAAC,WAAA,CAAAlF,WAAA,KAAAA,WAAA,CAAAC,MAAA,CAA6C;IAS9CvP,uDAAA,GAAyC;IAAzCA,yDAAA,eAAAuU,MAAA,CAAAC,WAAA,CAAAlF,WAAA,EAAyC,mBAAAiF,MAAA,CAAAC,WAAA,CAAAlF,WAAA;IAKtCtP,uDAAA,GAA2D;IAA3DA,wDAAA,SAAAuU,MAAA,CAAA9F,oBAAA,CAAAgG,OAAA,KAAAF,MAAA,CAAAC,WAAA,CAAAlF,WAAA,EAA2D;IAOxDtP,uDAAA,GAAqB;IAArBA,wDAAA,SAAAsP,WAAA,CAAAK,OAAA,CAAqB;IAUtB3P,uDAAA,GAAyB;IAAzBA,wDAAA,aAAAsP,WAAA,CAAAnI,IAAA,CAAyB;IAEtBnH,uDAAA,GAA8B;IAA9BA,wDAAA,iBAAAuU,MAAA,CAAAhG,WAAA,CAAAmG,IAAA,CAA8B;IAK9B1U,uDAAA,GAA+B;IAA/BA,wDAAA,iBAAAuU,MAAA,CAAAhG,WAAA,CAAAoG,KAAA,CAA+B;IAa/B3U,uDAAA,GAA8B;IAA9BA,wDAAA,iBAAAuU,MAAA,CAAAhG,WAAA,CAAAqG,IAAA,CAA8B;IAiB9B5U,uDAAA,GAAuC;IAAvCA,wDAAA,iBAAAuU,MAAA,CAAAhG,WAAA,CAAAsG,aAAA,CAAuC;IAUvC7U,uDAAA,GAA+B;IAA/BA,wDAAA,iBAAAuU,MAAA,CAAAhG,WAAA,CAAApC,KAAA,CAA+B;IAcpCnM,uDAAA,GAAuD;IAAvDA,wDAAA,SAAAsP,WAAA,CAAAwC,SAAA,IAAAxC,WAAA,CAAAwC,SAAA,CAAAxS,MAAA,KAAuD;IAetDU,uDAAA,GACF;IADEA,gEAAA,MAAAuU,MAAA,CAAAO,iBAAA,CAAAxF,WAAA,CAAAyF,SAAA,OACF;IAIG/U,uDAAA,GAA0B;IAA1BA,wDAAA,SAAAuU,MAAA,CAAAC,WAAA,CAAAlF,WAAA,EAA0B;IAsF5BtP,uDAAA,GAA0C;IAA1CA,wDAAA,SAAAuU,MAAA,CAAAS,qBAAA,KAAA1F,WAAA,CAAAzJ,EAAA,CAA0C;;;;;IAgBjD7F,4DAAA,eAA6D;IAEzDA,uDAAA,eAA8B;IAGhCA,0DAAA,EAAM;IACNA,4DAAA,WAAM;IAAAA,oDAAA,GAAqB;IAAAA,0DAAA,EAAO;;;;IAA5BA,uDAAA,GAAqB;IAArBA,+DAAA,CAAAiV,OAAA,CAAAC,aAAA,GAAqB;;;;;;IAS7BlV,4DAAA,eAA8C;IAItCA,oDAAA,GACF;IAAAA,0DAAA,EAAM;IACNA,4DAAA,eAAwB;IAAAA,oDAAA,GAAwB;IAAAA,0DAAA,EAAM;IAExDA,4DAAA,kBAAuE;IAA/DA,wDAAA,mBAAAmV,mEAAA;MAAAnV,2DAAA,CAAAoV,IAAA;MAAA,MAAAC,OAAA,GAAArV,2DAAA;MAAA,OAASA,yDAAA,CAAAqV,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAC7BtV,uDAAA,aAA4B;IAC9BA,0DAAA,EAAS;;;;IANLA,uDAAA,GACF;IADEA,gEAAA,0BAAAuV,OAAA,CAAAC,UAAA,CAAAjG,MAAA,kBAAAgG,OAAA,CAAAC,UAAA,CAAAjG,MAAA,CAAAG,QAAA,MACF;IACwB1P,uDAAA,GAAwB;IAAxBA,+DAAA,CAAAuV,OAAA,CAAAC,UAAA,CAAA5F,OAAA,CAAwB;;;;;;IAStD5P,4DAAA,eAAkD;IAGPA,oDAAA,8BAAuB;IAAAA,0DAAA,EAAM;IAClEA,4DAAA,eAAwB;IAAAA,oDAAA,GAA4B;IAAAA,0DAAA,EAAM;IAE5DA,4DAAA,kBAGC;IAFCA,wDAAA,mBAAAyV,mEAAA;MAAAzV,2DAAA,CAAA0V,IAAA;MAAA,MAAAC,OAAA,GAAA3V,2DAAA;MAAA,OAASA,yDAAA,CAAA2V,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAGzB5V,uDAAA,aAA4B;IAC9BA,0DAAA,EAAS;;;;IAPiBA,uDAAA,GAA4B;IAA5BA,+DAAA,CAAA6V,OAAA,CAAAC,cAAA,CAAAlG,OAAA,CAA4B;;;;;;IActD5P,4DAAA,eAGC;IACCA,uDAAA,aAAyC;IACzCA,4DAAA,gBAAmD;IAAAA,oDAAA,GAEjD;IAAAA,0DAAA,EAAO;IACTA,4DAAA,kBAGC;IAFCA,wDAAA,mBAAA+V,yEAAA;MAAA,MAAA1E,WAAA,GAAArR,2DAAA,CAAAgW,IAAA;MAAA,MAAAC,KAAA,GAAA5E,WAAA,CAAA6E,KAAA;MAAA,MAAAC,OAAA,GAAAnW,2DAAA;MAAA,OAASA,yDAAA,CAAAmW,OAAA,CAAAC,kBAAA,CAAAH,KAAA,CAAqB;IAAA,EAAC;IAG/BjW,uDAAA,aAAoC;IACtCA,0DAAA,EAAS;;;;IAR0CA,uDAAA,GAEjD;IAFiDA,+DAAA,CAAAqW,QAAA,CAAApO,IAAA,CAEjD;;;;;IATRjI,4DAAA,eAAmD;IAE/CA,wDAAA,IAAAsW,gDAAA,mBAcM;IACRtW,0DAAA,EAAM;;;;IAdeA,uDAAA,GAAkB;IAAlBA,wDAAA,YAAAuW,OAAA,CAAAC,aAAA,CAAkB;;;;;;IAkBzCxW,4DAAA,eAA0D;IACxDA,uDAAA,aAA4C;IAC5CA,4DAAA,gBAA6B;IAAAA,oDAAA,GAE3B;IAAAA,0DAAA,EAAO;IACTA,4DAAA,kBAGC;IAFCA,wDAAA,mBAAAyW,mEAAA;MAAAzW,2DAAA,CAAA0W,IAAA;MAAA,MAAAC,OAAA,GAAA3W,2DAAA;MAAA,OAASA,yDAAA,CAAA2W,OAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IAG9B5W,uDAAA,aAA2B;IAC7BA,0DAAA,EAAS;IACTA,4DAAA,kBAGC;IAFCA,wDAAA,mBAAA6W,mEAAA;MAAA7W,2DAAA,CAAA0W,IAAA;MAAA,MAAAI,OAAA,GAAA9W,2DAAA;MAAA,OAASA,yDAAA,CAAA8W,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAGhC/W,uDAAA,aAA4B;IAC9BA,0DAAA,EAAS;;;;IAdoBA,uDAAA,GAE3B;IAF2BA,+DAAA,CAAAgX,OAAA,CAAA9F,cAAA,CAAA8F,OAAA,CAAAC,iBAAA,EAE3B;;;;;;IA8BEjX,4DAAA,eAGC;IAEGA,wDAAA,mBAAAkX,mEAAA;MAAAlX,2DAAA,CAAAmX,IAAA;MAAA,MAAAC,OAAA,GAAApX,2DAAA;MAAA,OAASA,yDAAA,CAAAoX,OAAA,CAAAC,gBAAA,EAAkB;IAAA,EAAC;IAG5BrX,uDAAA,aAAyC;IACzCA,4DAAA,gBAAiC;IAAAA,oDAAA,cAAO;IAAAA,0DAAA,EAAO;IAEjDA,4DAAA,kBAGC;IAFCA,wDAAA,mBAAAsX,mEAAA;MAAAtX,2DAAA,CAAAmX,IAAA;MAAA,MAAAI,OAAA,GAAAvX,2DAAA;MAAA,OAASA,yDAAA,CAAAuX,OAAA,CAAAF,gBAAA,EAAkB;IAAA,EAAC;IAG5BrX,uDAAA,aAA2C;IAC3CA,4DAAA,gBAAiC;IAAAA,oDAAA,YAAK;IAAAA,0DAAA,EAAO;IAE/CA,4DAAA,kBAGC;IAFCA,wDAAA,mBAAAwX,mEAAA;MAAAxX,2DAAA,CAAAmX,IAAA;MAAA,MAAAM,OAAA,GAAAzX,2DAAA;MAAAyX,OAAA,CAAAC,iBAAA,GAA6B,IAAI;MAAA,OAAA1X,yDAAA,CAAAyX,OAAA,CAAAE,kBAAA,GAAuB,KAAK;IAAA,EAAC;IAG9D3X,uDAAA,cAA8C;IAC9CA,4DAAA,iBAAiC;IAAAA,oDAAA,aAAK;IAAAA,0DAAA,EAAO;IAE/CA,4DAAA,mBAGC;IAFCA,wDAAA,mBAAA4X,oEAAA;MAAA5X,2DAAA,CAAAmX,IAAA;MAAA,MAAAU,OAAA,GAAA7X,2DAAA;MAAA6X,OAAA,CAAAC,kBAAA,GAA8B,IAAI;MAAA,OAAA9X,yDAAA,CAAA6X,OAAA,CAAAF,kBAAA,GAAuB,KAAK;IAAA,EAAC;IAG/D3X,uDAAA,cAAqD;IACrDA,4DAAA,iBAAiC;IAAAA,oDAAA,oBAAY;IAAAA,0DAAA,EAAO;;;;;;IA0B1DA,4DAAA,kBAOC;IAFCA,wDAAA,uBAAA+X,0EAAA;MAAA/X,2DAAA,CAAAgY,KAAA;MAAA,MAAAC,OAAA,GAAAjY,2DAAA;MAAA,OAAaA,yDAAA,CAAAiY,OAAA,CAAAC,mBAAA,EAAqB;IAAA,EAAC;IAGnClY,uDAAA,aAAiC;IACnCA,0DAAA,EAAS;;;;;;IAGTA,4DAAA,kBAMC;IAHCA,wDAAA,mBAAAmY,sEAAA;MAAAnY,2DAAA,CAAAoY,KAAA;MAAA,MAAAC,QAAA,GAAArY,2DAAA;MAAA,OAASA,yDAAA,CAAAqY,QAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAIvBtY,uDAAA,aAA6C;IAC/CA,0DAAA,EAAS;;;;IAJPA,wDAAA,cAAAuY,OAAA,CAAAC,cAAA,GAA8B;;;;;;IAhexCxY,4DAAA,aAAyD;IAMnDA,uDAAA,aAaE;IAEFA,4DAAA,aAA0B;IAEtBA,oDAAA,GAKF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,WAGC;IACCA,wDAAA,IAAAyY,0CAAA,kBAEO;IACPzY,wDAAA,IAAA0Y,0CAAA,kBAEC;IACD1Y,wDAAA,KAAA2Y,2CAAA,kBAEC;IACH3Y,0DAAA,EAAI;IAIRA,4DAAA,cAA0B;IAExBA,wDAAA,KAAA4Y,6CAAA,qBAOS;IAGT5Y,wDAAA,KAAA6Y,6CAAA,qBAOS;IAGT7Y,4DAAA,kBAA2C;IACzCA,uDAAA,aAAiC;IACnCA,0DAAA,EAAS;IAObA,4DAAA,mBAAmD;IAEjDA,wDAAA,KAAA8Y,0CAAA,kBAIM;IAGN9Y,wDAAA,KAAA+Y,0CAAA,oBA0NM;IAGN/Y,wDAAA,KAAAgZ,0CAAA,kBAOM;IACRhZ,0DAAA,EAAM;IAKNA,4DAAA,eAAqC;IAEnCA,wDAAA,KAAAiZ,0CAAA,kBAYM;IAGNjZ,wDAAA,KAAAkZ,0CAAA,kBAaM;IAGNlZ,wDAAA,KAAAmZ,0CAAA,kBAkBM;IAGNnZ,wDAAA,KAAAoZ,0CAAA,kBAiBM;IAGNpZ,4DAAA,eAA2B;IAOnBA,wDAAA,mBAAAqZ,6DAAA;MAAArZ,2DAAA,CAAAsZ,KAAA;MAAA,MAAAC,QAAA,GAAAvZ,2DAAA;MAAA,OAASA,yDAAA,CAAAuZ,QAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAGhCxZ,uDAAA,aAAgC;IAClCA,0DAAA,EAAS;IAGTA,wDAAA,KAAAyZ,0CAAA,mBAgCM;IACRzZ,0DAAA,EAAM;IAGNA,4DAAA,kBAAsE;IAA5CA,wDAAA,mBAAA0Z,6DAAA;MAAA1Z,2DAAA,CAAAsZ,KAAA;MAAA,MAAAK,QAAA,GAAA3Z,2DAAA;MAAA,OAASA,yDAAA,CAAA2Z,QAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IACrD5Z,uDAAA,aAA4B;IAC9BA,0DAAA,EAAS;IAIXA,4DAAA,wBASC;IAPCA,wDAAA,2BAAA6Z,uEAAAC,MAAA;MAAA9Z,2DAAA,CAAAsZ,KAAA;MAAA,MAAAS,QAAA,GAAA/Z,2DAAA;MAAA,OAAAA,yDAAA,CAAA+Z,QAAA,CAAAC,cAAA,GAAAF,MAAA;IAAA,EAA4B,qBAAAG,iEAAAH,MAAA;MAAA9Z,2DAAA,CAAAsZ,KAAA;MAAA,MAAAY,QAAA,GAAAla,2DAAA;MAAA,OACjBA,yDAAA,CAAAka,QAAA,CAAAC,UAAA,CAAAL,MAAA,CAAkB;IAAA,EADD,mBAAAM,+DAAA;MAAApa,2DAAA,CAAAsZ,KAAA;MAAA,MAAAe,QAAA,GAAAra,2DAAA;MAAA,OAEnBA,yDAAA,CAAAqa,QAAA,CAAAC,QAAA,EAAU;IAAA,EAFS;IAO7Bta,0DAAA,EAAW;IAGZA,4DAAA,eAA2B;IAEzBA,wDAAA,KAAAua,6CAAA,qBASS;IAGTva,wDAAA,KAAAwa,6CAAA,qBAQS;IACXxa,0DAAA,EAAM;IAIRA,4DAAA,qBAOE;IAFAA,wDAAA,oBAAAya,6DAAAX,MAAA;MAAA9Z,2DAAA,CAAAsZ,KAAA;MAAA,MAAAoB,QAAA,GAAA1a,2DAAA;MAAA,OAAUA,yDAAA,CAAA0a,QAAA,CAAAC,cAAA,CAAAb,MAAA,CAAsB;IAAA,EAAC;IALnC9Z,0DAAA,EAOE;;;;IA9dEA,uDAAA,GAAqE;IAArEA,yDAAA,YAAA4a,MAAA,CAAAnM,oBAAA,CAAAgG,OAAA,IAAAmG,MAAA,CAAAC,iBAAA,GAAqE;IAXrE7a,wDAAA,QAAA4a,MAAA,CAAAnM,oBAAA,CAAAgG,OAAA,GAAAmG,MAAA,CAAAnM,oBAAA,CAAAqM,UAAA,GAAAF,MAAA,CAAAG,kBAAA,IAAA/a,2DAAA,CAIC,QAAA4a,MAAA,CAAAnM,oBAAA,CAAAgG,OAAA,GAAAmG,MAAA,CAAAnM,oBAAA,CAAAuM,SAAA,GAAAJ,MAAA,CAAAK,gBAAA;IAYCjb,uDAAA,GAKF;IALEA,gEAAA,MAAA4a,MAAA,CAAAnM,oBAAA,CAAAgG,OAAA,GAAAmG,MAAA,CAAAnM,oBAAA,CAAAuM,SAAA,GAAAJ,MAAA,CAAAK,gBAAA,QAKF;IAGEjb,uDAAA,GAAqE;IAArEA,yDAAA,YAAA4a,MAAA,CAAAnM,oBAAA,CAAAgG,OAAA,IAAAmG,MAAA,CAAAC,iBAAA,GAAqE;IAE9D7a,uDAAA,GAAkC;IAAlCA,wDAAA,SAAA4a,MAAA,CAAAnM,oBAAA,CAAAgG,OAAA,CAAkC;IAGlCzU,uDAAA,GAA0D;IAA1DA,wDAAA,UAAA4a,MAAA,CAAAnM,oBAAA,CAAAgG,OAAA,IAAAmG,MAAA,CAAAC,iBAAA,GAA0D;IAG1D7a,uDAAA,GAA2D;IAA3DA,wDAAA,UAAA4a,MAAA,CAAAnM,oBAAA,CAAAgG,OAAA,KAAAmG,MAAA,CAAAC,iBAAA,GAA2D;IAanE7a,uDAAA,GAAmC;IAAnCA,wDAAA,UAAA4a,MAAA,CAAAnM,oBAAA,CAAAgG,OAAA,CAAmC;IAUnCzU,uDAAA,GAAmC;IAAnCA,wDAAA,UAAA4a,MAAA,CAAAnM,oBAAA,CAAAgG,OAAA,CAAmC;IAiBlCzU,uDAAA,GAAe;IAAfA,wDAAA,SAAA4a,MAAA,CAAAM,SAAA,CAAe;IAQClb,uDAAA,GAAa;IAAbA,wDAAA,YAAA4a,MAAA,CAAAO,QAAA,CAAa,iBAAAP,MAAA,CAAAQ,gBAAA;IA4N7Bpb,uDAAA,GAA4B;IAA5BA,wDAAA,SAAA4a,MAAA,CAAAS,WAAA,CAAA/b,MAAA,KAA4B;IAe5BU,uDAAA,GAAgB;IAAhBA,wDAAA,SAAA4a,MAAA,CAAApF,UAAA,CAAgB;IAehBxV,uDAAA,GAAoB;IAApBA,wDAAA,SAAA4a,MAAA,CAAA9E,cAAA,CAAoB;IAgBpB9V,uDAAA,GAA8B;IAA9BA,wDAAA,SAAA4a,MAAA,CAAApE,aAAA,CAAAlX,MAAA,KAA8B;IAqB9BU,uDAAA,GAAiB;IAAjBA,wDAAA,SAAA4a,MAAA,CAAAU,WAAA,CAAiB;IAmCdtb,uDAAA,GAAwB;IAAxBA,wDAAA,SAAA4a,MAAA,CAAAjD,kBAAA,CAAwB;IA2C7B3X,uDAAA,GAA4B;IAA5BA,wDAAA,YAAA4a,MAAA,CAAAZ,cAAA,CAA4B,aAAAY,MAAA,CAAAU,WAAA;IAazBtb,uDAAA,GAEA;IAFAA,wDAAA,UAAA4a,MAAA,CAAAZ,cAAA,CAAAuB,IAAA,OAAAX,MAAA,CAAApE,aAAA,CAAAlX,MAAA,KAAAsb,MAAA,CAAAU,WAAA,CAEA;IAUAtb,uDAAA,GAAmD;IAAnDA,wDAAA,SAAA4a,MAAA,CAAAZ,cAAA,CAAAuB,IAAA,MAAAX,MAAA,CAAApE,aAAA,CAAAlX,MAAA,CAAmD;;;;;IA0B9DU,4DAAA,eAGC;IAOOA,uDAAA,aAAmD;IACrDA,0DAAA,EAAM;IACNA,4DAAA,cAA+C;IAAAA,oDAAA,yBAAkB;IAAAA,0DAAA,EAAK;IACtEA,4DAAA,aAAyB;IAAAA,oDAAA,oDAAwC;IAAAA,0DAAA,EAAI;IAIvEA,4DAAA,eAA4B;IAKtBA,uDAAA,cAA8C;IAChDA,0DAAA,EAAM;IACNA,4DAAA,WAAK;IACgCA,oDAAA,mCAAsB;IAAAA,0DAAA,EAAK;IAC9DA,4DAAA,cAAiC;IAC/BA,oDAAA,4DACF;IAAAA,0DAAA,EAAI;IAIRA,4DAAA,gBAAmD;IAI/CA,uDAAA,cAA+C;IACjDA,0DAAA,EAAM;IACNA,4DAAA,WAAK;IACgCA,oDAAA,+BAAkB;IAAAA,0DAAA,EAAK;IAC1DA,4DAAA,cAAiC;IAAAA,oDAAA,gDAA8B;IAAAA,0DAAA,EAAI;IAIvEA,4DAAA,gBAAmD;IAI/CA,uDAAA,cAA8C;IAChDA,0DAAA,EAAM;IACNA,4DAAA,WAAK;IACgCA,oDAAA,2BAAmB;IAAAA,0DAAA,EAAK;IAC3DA,4DAAA,cAAiC;IAAAA,oDAAA,wCAA2B;IAAAA,0DAAA,EAAI;IAMtEA,4DAAA,gBAAoE;IAChBA,oDAAA,2BAAmB;IAAAA,0DAAA,EAAK;IAC1EA,4DAAA,gBAA6C;IACxCA,oDAAA,iEAA+C;IAAAA,0DAAA,EAAI;IACtDA,4DAAA,SAAG;IAAAA,oDAAA,0EAAwD;IAAAA,0DAAA,EAAI;IAC/DA,4DAAA,SAAG;IAAAA,oDAAA,4DAA+C;IAAAA,0DAAA,EAAI;IAK1DA,uDAAA,yBAAuC;IACzCA,0DAAA,EAAM;;;AD1hBF,MAAOwb,oBAAoB;EAsD/Bne,YACUC,cAA8B,EAC9Bme,WAAwB,EACxBhe,YAA0B,EAC1Bie,KAAqB,EACrBC,MAAc,EACdC,GAAsB,EACtBC,MAAc;IANd,KAAAve,cAAc,GAAdA,cAAc;IACd,KAAAme,WAAW,GAAXA,WAAW;IACX,KAAAhe,YAAY,GAAZA,YAAY;IACZ,KAAAie,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,MAAM,GAANA,MAAM;IAvDhB;IACA,KAAAC,WAAW,GAAgB,IAAI;IAC/B,KAAArN,oBAAoB,GAAwB,IAAI;IAChD,KAAA0M,QAAQ,GAAc,EAAE;IACxB,KAAAD,SAAS,GAAG,KAAK;IACjB,KAAAa,QAAQ,GAAG,KAAK;IAChB,KAAAV,WAAW,GAAW,EAAE;IAExB;IACA,KAAAW,WAAW,GAAG,CAAC;IACf,KAAAC,eAAe,GAAG,IAAI;IACtB,KAAAC,mBAAmB,GAAG,KAAK;IAE3B;IACA,KAAAlC,cAAc,GAAG,EAAE;IACnB,KAAAxD,aAAa,GAAW,EAAE;IAC1B,KAAA8E,WAAW,GAAG,KAAK;IACnB,KAAArE,iBAAiB,GAAG,CAAC;IAErB;IACA,KAAAkF,eAAe,GAAG,KAAK;IACvB,KAAAxE,kBAAkB,GAAG,KAAK;IAC1B,KAAA3C,qBAAqB,GAAkB,IAAI;IAC3C,KAAA0C,iBAAiB,GAAG,KAAK;IACzB,KAAAI,kBAAkB,GAAG,KAAK;IAC1B,KAAAtC,UAAU,GAAmB,IAAI;IACjC,KAAAM,cAAc,GAAmB,IAAI;IAErC;IACA,KAAA7C,WAAW,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAElD;IACA,KAAAmJ,WAAW,GAAG,EAAE;IAChB,KAAAC,aAAa,GAAc,EAAE;IAC7B,KAAAC,iBAAiB,GAAG,KAAK;IAEzB;IACQ,KAAAC,aAAa,GAAmB,EAAE;IAI1C;IACQ,KAAAC,eAAe,GAAG,IAAIva,iDAAe,CAAgB,IAAI,CAAC;IAElE;IACS,KAAAsM,WAAW,GAAGA,8DAAW;IACzB,KAAAlM,QAAQ,GAAGA,2DAAQ;EAUzB;EAEHjE,QAAQA,CAAA;IACN,IAAI,CAACqe,mBAAmB,EAAE;IAC1B,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAle,WAAWA,CAAA;IACT,IAAI,CAACiG,OAAO,EAAE;EAChB;EAEA;EACA;EACA;EAEQ8X,mBAAmBA,CAAA;IACzB;IACA,IAAI,CAACX,WAAW,GAAG,IAAI,CAACL,WAAW,CAACoB,cAAc,EAAE;IAEpD,IAAI,CAAC,IAAI,CAACf,WAAW,EAAE;MACrB,IAAI,CAACH,MAAM,CAACmB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAChC;;IAGF;IACA,IAAI,CAACpB,KAAK,CAACqB,MAAM,CAACxe,SAAS,CAAEwe,MAAM,IAAI;MACrC,MAAMhY,cAAc,GAAGgY,MAAM,CAAC,gBAAgB,CAAC;MAC/C,IAAIhY,cAAc,EAAE;QAClB,IAAI,CAACyX,eAAe,CAACrd,IAAI,CAAC4F,cAAc,CAAC;;IAE7C,CAAC,CAAC;EACJ;EAEQ2X,kBAAkBA,CAAA;IACxB;IACA,MAAMM,eAAe,GAAG,IAAI,CAACR,eAAe,CACzCjX,IAAI,CACH+I,sDAAM,CAAEzI,EAAE,IAAK,CAAC,CAACA,EAAE,CAAC,EACpBsI,oEAAoB,EAAE,EACtBE,oDAAG,CAAC,MAAK;MACP,IAAI,CAAC6M,SAAS,GAAG,IAAI;MACrB,IAAI,CAACC,QAAQ,GAAG,EAAE;MAClB,IAAI,CAACa,WAAW,GAAG,CAAC;MACpB,IAAI,CAACC,eAAe,GAAG,IAAI;IAC7B,CAAC,CAAC,EACF7N,0DAAS,CAAErJ,cAAc,IACvB,IAAI,CAACzH,cAAc,CAAC2f,eAAe,CAAClY,cAAe,EAAE,EAAE,EAAE,CAAC,CAAC,CAC5D,EACD3C,2DAAU,CAAE7C,KAAK,IAAI;MACnB2d,OAAO,CAAC3d,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACrE,IAAI,CAAC9B,YAAY,CAAC+B,SAAS,CACzB,8CAA8C,CAC/C;MACD,OAAO0O,yCAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH,CACA3P,SAAS,CAAE4e,YAAY,IAAI;MAC1B,IAAI,CAACjC,SAAS,GAAG,KAAK;MACtB,IAAIiC,YAAY,EAAE;QAChB,IAAI,CAAC1O,oBAAoB,GAAG0O,YAAY;QACxC,IAAI,CAAChC,QAAQ,GAAGgC,YAAY,CAAChC,QAAQ,IAAI,EAAE;QAC3C,IAAI,CAACyB,cAAc,EAAE;QACrB,IAAI,CAACQ,kBAAkB,EAAE;;MAE3B,IAAI,CAACxB,GAAG,CAACyB,aAAa,EAAE;IAC1B,CAAC,CAAC;IAEJ;IACA,MAAMC,WAAW,GAAG,IAAI,CAAChgB,cAAc,CACpCigB,mBAAmB,EAAE,CACrBhf,SAAS,CAAEif,OAAO,IAAI;MACrB,IACEA,OAAO,IACP,IAAI,CAAC/O,oBAAoB,IACzB+O,OAAO,CAACzY,cAAc,KAAK,IAAI,CAAC0J,oBAAoB,CAAC5I,EAAE,EACvD;QACA,IAAI,CAAC4X,aAAa,CAACD,OAAO,CAAC;QAC3B,IAAI,CAACZ,cAAc,EAAE;QACrB,IAAI,CAACc,iBAAiB,CAACF,OAAO,CAAC;;IAEnC,CAAC,CAAC;IAEJ;IACA,MAAMG,SAAS,GAAG,IAAI,CAACrgB,cAAc,CAClCsgB,2BAA2B,EAAE,CAC7Brf,SAAS,CAAEwM,KAAK,IAAI;MACnB,IACEA,KAAK,IACL,IAAI,CAAC0D,oBAAoB,IACzB1D,KAAK,CAAChG,cAAc,KAAK,IAAI,CAAC0J,oBAAoB,CAAC5I,EAAE,EACrD;QACA,IAAI,CAACgY,qBAAqB,CAAC9S,KAAK,CAAC;;IAErC,CAAC,CAAC;IAEJ,IAAI,CAACwR,aAAa,CAACuB,IAAI,CAACd,eAAe,EAAEM,WAAW,EAAEK,SAAS,CAAC;EAClE;EAEQhZ,OAAOA,CAAA;IACb,IAAI,CAAC4X,aAAa,CAACtT,OAAO,CAAE8U,GAAG,IAAKA,GAAG,CAACpf,WAAW,EAAE,CAAC;IACtD,IAAI,IAAI,CAACqf,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;IAElC,IAAI,IAAI,CAACE,iBAAiB,EAAE;MAC1BC,aAAa,CAAC,IAAI,CAACD,iBAAiB,CAAC;;IAEvC,IAAI,CAACE,UAAU,EAAE;EACnB;EAEA;EACA;EACA;EAEA9F,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACE,cAAc,EAAE,EAAE;MAC1B;;IAGF,MAAM5I,OAAO,GAAG,IAAI,CAACoK,cAAc,CAACuB,IAAI,EAAE;IAC1C,MAAM8C,KAAK,GAAG,IAAI,CAAC7H,aAAa;IAEhC;IACA,IAAI,CAACwD,cAAc,GAAG,EAAE;IACxB,IAAI,CAACxD,aAAa,GAAG,EAAE;IACvB,IAAI,CAAChB,UAAU,GAAG,IAAI;IACtB,IAAI,CAAC4I,UAAU,EAAE;IAEjB,IAAI,IAAI,CAACtI,cAAc,EAAE;MACvB,IAAI,CAACwI,aAAa,CAAC1O,OAAO,CAAC;MAC3B;;IAGF;IACA,IAAIA,OAAO,IAAIyO,KAAK,CAAC/e,MAAM,GAAG,CAAC,EAAE;MAC/B,IAAI,CAACif,cAAc,CAAC3O,OAAO,EAAEyO,KAAK,CAAC;;EAEvC;EAEA7F,cAAcA,CAAA;IACZ,MAAMgG,UAAU,GAAG,IAAI,CAACxE,cAAc,CAACuB,IAAI,EAAE,CAACjc,MAAM,GAAG,CAAC;IACxD,MAAMmf,QAAQ,GAAG,IAAI,CAACjI,aAAa,CAAClX,MAAM,GAAG,CAAC;IAC9C,MAAMof,eAAe,GAAG,CAAC,CAAC,IAAI,CAACjQ,oBAAoB;IAEnD,OAAOiQ,eAAe,KAAKF,UAAU,IAAIC,QAAQ,CAAC;EACpD;EAEQF,cAAcA,CAAC3O,OAAe,EAAEyO,KAAa;IACnD,IAAI,CAAC,IAAI,CAAC5P,oBAAoB,IAAI,CAAC,IAAI,CAACqN,WAAW,EAAE;IAErD,MAAMjX,WAAW,GAAG,IAAI,CAAC8Z,cAAc,EAAE;IACzC,IAAI,CAAC9Z,WAAW,EAAE;IAElB;IACA,MAAM+Z,WAAW,GAAY;MAC3B/Y,EAAE,EAAE,QAAQ3H,IAAI,CAACwI,GAAG,EAAE,EAAE;MACxBkJ,OAAO;MACPzI,IAAI,EACFkX,KAAK,CAAC/e,MAAM,GAAG,CAAC,GAAG,IAAI,CAACuf,kBAAkB,CAACR,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG9P,8DAAW,CAACmG,IAAI;MACzEK,SAAS,EAAE,IAAI7W,IAAI,EAAE;MACrBqR,MAAM,EAAE,IAAI,CAACuM,WAAW;MACxBgD,SAAS,EAAE,IAAI;MACf/Z,cAAc,EAAE,IAAI,CAAC0J,oBAAoB,CAAC5I;KAC3C;IAED,IAAI,CAAC4X,aAAa,CAACmB,WAAW,CAAC;IAC/B,IAAI,CAAChC,cAAc,EAAE;IAErB;IACA,MAAMmC,cAAc,GAClBV,KAAK,CAAC/e,MAAM,GAAG,CAAC,GACZ,IAAI,CAAChC,cAAc,CAACgb,WAAW,CAACzT,WAAW,EAAE+K,OAAO,EAAEyO,KAAK,CAAC,CAAC,CAAC,CAAC,GAC/D,IAAI,CAAC/gB,cAAc,CAACgb,WAAW,CAACzT,WAAW,EAAE+K,OAAO,CAAC;IAE3DmP,cAAc,CAACxgB,SAAS,CAAC;MACvBY,IAAI,EAAG6f,WAAW,IAAI;QACpB,IAAI,CAACC,uBAAuB,CAACL,WAAW,CAAC/Y,EAAG,EAAEmZ,WAAW,CAAC;QAC1D,IAAI,CAACvhB,YAAY,CAAC4B,WAAW,CAAC,gBAAgB,CAAC;MACjD,CAAC;MACDE,KAAK,EAAGA,KAAK,IAAI;QACf2d,OAAO,CAAC3d,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1D,IAAI,CAAC2f,kBAAkB,CAACN,WAAW,CAAC/Y,EAAG,CAAC;QACxC,IAAI,CAACpI,YAAY,CAAC+B,SAAS,CAAC,mCAAmC,CAAC;MAClE;KACD,CAAC;EACJ;EAEQ8e,aAAaA,CAACa,UAAkB;IACtC,IAAI,CAAC,IAAI,CAACrJ,cAAc,EAAE;IAE1B,IAAI,CAACxY,cAAc,CAChB8hB,WAAW,CAAC,IAAI,CAACtJ,cAAc,CAACjQ,EAAG,EAAEsZ,UAAU,CAAC,CAChD5gB,SAAS,CAAC;MACTY,IAAI,EAAGkgB,cAAc,IAAI;QACvB,IAAI,CAACC,mBAAmB,CAACD,cAAc,CAAC;QACxC,IAAI,CAACvJ,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACrY,YAAY,CAAC4B,WAAW,CAAC,iBAAiB,CAAC;MAClD,CAAC;MACDE,KAAK,EAAGA,KAAK,IAAI;QACf2d,OAAO,CAAC3d,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;QAClE,IAAI,CAAC9B,YAAY,CAAC+B,SAAS,CACzB,2CAA2C,CAC5C;MACH;KACD,CAAC;EACN;EAEA+f,aAAaA,CAAC/B,OAAgB;IAC5B,IAAI,CAACA,OAAO,CAAC3X,EAAE,IAAI,CAAC,IAAI,CAAC2Z,gBAAgB,CAAChC,OAAO,CAAC,EAAE;IAEpD,IAAIiC,OAAO,CAAC,iDAAiD,CAAC,EAAE;MAC9D,IAAI,CAACniB,cAAc,CAACiiB,aAAa,CAAC/B,OAAO,CAAC3X,EAAE,CAAC,CAACtH,SAAS,CAAC;QACtDY,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACugB,qBAAqB,CAAClC,OAAO,CAAC3X,EAAG,CAAC;UACvC,IAAI,CAACpI,YAAY,CAAC4B,WAAW,CAAC,kBAAkB,CAAC;QACnD,CAAC;QACDE,KAAK,EAAGA,KAAK,IAAI;UACf2d,OAAO,CAAC3d,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;UACjE,IAAI,CAAC9B,YAAY,CAAC+B,SAAS,CACzB,0CAA0C,CAC3C;QACH;OACD,CAAC;;EAEN;EAEA;EACA;EACA;EAEAmb,cAAcA,CAAC5P,KAAU;IACvB,MAAMsT,KAAK,GAAGtT,KAAK,CAAC4U,MAAM,CAACtB,KAAK;IAChC,IAAIA,KAAK,IAAIA,KAAK,CAAC/e,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAI,CAACkX,aAAa,GAAGoJ,KAAK,CAACC,IAAI,CAACxB,KAAK,CAAC;MACtC,IAAI,CAAC1G,kBAAkB,GAAG,KAAK;MAE/B;MACA,IAAI,IAAI,CAACqC,cAAc,CAACuB,IAAI,EAAE,KAAK,EAAE,EAAE;QACrC,IAAI,CAACjD,WAAW,EAAE;;;EAGxB;EAEAlC,kBAAkBA,CAACF,KAAa;IAC9B,IAAI,CAACM,aAAa,CAACsJ,MAAM,CAAC5J,KAAK,EAAE,CAAC,CAAC;EACrC;EAEAmB,gBAAgBA,CAAA;IACd,IAAI,CAAC0I,SAAS,CAACC,aAAa,CAACC,KAAK,EAAE;EACtC;EAEA;EACA;EACA;EAEM/H,mBAAmBA,CAAA;IAAA,IAAAtZ,KAAA;IAAA,OAAAC,kKAAA;MACvB,IAAI;QACF,MAAM8M,MAAM,SAASS,SAAS,CAACC,YAAY,CAACT,YAAY,CAAC;UAAEpC,KAAK,EAAE;QAAI,CAAE,CAAC;QACzE5K,KAAI,CAAC0c,WAAW,GAAG,IAAI;QACvB1c,KAAI,CAACqY,iBAAiB,GAAG,CAAC;QAE1B;QACArY,KAAI,CAACsf,iBAAiB,GAAGgC,WAAW,CAAC,MAAK;UACxCthB,KAAI,CAACqY,iBAAiB,EAAE;QAC1B,CAAC,EAAE,IAAI,CAAC;QAER;QACA;OACD,CAAC,OAAO1X,KAAK,EAAE;QACd2d,OAAO,CAAC3d,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7DX,KAAI,CAACnB,YAAY,CAAC+B,SAAS,CAAC,oCAAoC,CAAC;;IAClE;EACH;EAEAoX,kBAAkBA,CAAA;IAChB,IAAI,CAAC0E,WAAW,GAAG,KAAK;IACxB,IAAI,IAAI,CAAC4C,iBAAiB,EAAE;MAC1BC,aAAa,CAAC,IAAI,CAACD,iBAAiB,CAAC;;IAGvC;IACA;EACF;;EAEAnH,oBAAoBA,CAAA;IAClB,IAAI,CAACuE,WAAW,GAAG,KAAK;IACxB,IAAI,CAACrE,iBAAiB,GAAG,CAAC;IAC1B,IAAI,IAAI,CAACiH,iBAAiB,EAAE;MAC1BC,aAAa,CAAC,IAAI,CAACD,iBAAiB,CAAC;;EAEzC;EAEA;EACA;EACA;EAEAjP,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACR,oBAAoB,EAAE;IAEhC,MAAM5J,WAAW,GAAG,IAAI,CAAC8Z,cAAc,EAAE;IACzC,IAAI,CAAC9Z,WAAW,EAAE;IAElB,IAAI,CAACvH,cAAc,CAACsH,YAAY,CAACC,WAAW,EAAExC,2DAAQ,CAAC8d,KAAK,CAAC,CAAC5hB,SAAS,CAAC;MACtEY,IAAI,EAAGsG,IAAI,IAAI;QACb,IAAI,CAAChI,YAAY,CAAC4B,WAAW,CAAC,oBAAoB,CAAC;QACnD;MACF,CAAC;;MACDE,KAAK,EAAGA,KAAK,IAAI;QACf2d,OAAO,CAAC3d,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QAC/D,IAAI,CAAC9B,YAAY,CAAC+B,SAAS,CAAC,wBAAwB,CAAC;MACvD;KACD,CAAC;EACJ;EAEA6P,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACZ,oBAAoB,EAAE;IAEhC,MAAM5J,WAAW,GAAG,IAAI,CAAC8Z,cAAc,EAAE;IACzC,IAAI,CAAC9Z,WAAW,EAAE;IAElB,IAAI,CAACvH,cAAc,CAACsH,YAAY,CAACC,WAAW,EAAExC,2DAAQ,CAAC8J,KAAK,CAAC,CAAC5N,SAAS,CAAC;MACtEY,IAAI,EAAGsG,IAAI,IAAI;QACb,IAAI,CAAChI,YAAY,CAAC4B,WAAW,CAAC,oBAAoB,CAAC;QACnD;MACF,CAAC;;MACDE,KAAK,EAAGA,KAAK,IAAI;QACf2d,OAAO,CAAC3d,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;QACrE,IAAI,CAAC9B,YAAY,CAAC+B,SAAS,CAAC,8BAA8B,CAAC;MAC7D;KACD,CAAC;EACJ;EAEA;EACA;EACA;EAEA8a,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAAC7L,oBAAoB,IAAI,IAAI,CAACsN,QAAQ,EAAE;IAEjD,IAAI,CAACA,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACze,cAAc,CAAC8iB,WAAW,CAAC,IAAI,CAAC3R,oBAAoB,CAAC5I,EAAG,CAAC,CAACtH,SAAS,EAAE;IAE1E;IACA,IAAI,IAAI,CAACyf,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;IAGlC,IAAI,CAACA,aAAa,GAAGle,UAAU,CAAC,MAAK;MACnC,IAAI,CAACse,UAAU,EAAE;IACnB,CAAC,EAAE,IAAI,CAAC;EACV;EAEAA,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACrC,QAAQ,IAAI,CAAC,IAAI,CAACtN,oBAAoB,EAAE;IAElD,IAAI,CAACsN,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACze,cAAc,CAAC8gB,UAAU,CAAC,IAAI,CAAC3P,oBAAoB,CAAC5I,EAAG,CAAC,CAACtH,SAAS,EAAE;IAEzE,IAAI,IAAI,CAACyf,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;EAEpC;EAEA;EACA;EACA;EAEQW,cAAcA,CAAA;IACpB,IAAI,CAAC,IAAI,CAAClQ,oBAAoB,IAAI,CAAC,IAAI,CAACqN,WAAW,EAAE,OAAO,IAAI;IAEhE,MAAMpN,YAAY,GAAG,IAAI,CAACD,oBAAoB,CAACC,YAAY,IAAI,EAAE;IACjE,MAAM2R,aAAa,GAAG,IAAI,CAACvE,WAAW,CAACjW,EAAE,IAAI,IAAI,CAACiW,WAAW,CAACwE,GAAG;IAEjE,MAAMC,SAAS,GAAG7R,YAAY,CAAC8R,IAAI,CAChCC,CAAC,IAAK,CAACA,CAAC,CAAC5a,EAAE,IAAI4a,CAAC,CAACH,GAAG,MAAMD,aAAa,CACzC;IAED,OAAOE,SAAS,GAAGA,SAAS,CAAC1a,EAAE,IAAI0a,SAAS,CAACD,GAAI,GAAG,IAAI;EAC1D;EAEQzB,kBAAkBA,CAAC6B,IAAU;IACnC,MAAMvZ,IAAI,GAAGuZ,IAAI,CAACvZ,IAAI,CAACwZ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACpC,QAAQxZ,IAAI;MACV,KAAK,OAAO;QACV,OAAOoH,8DAAW,CAACoG,KAAK;MAC1B,KAAK,OAAO;QACV,OAAOpG,8DAAW,CAACpC,KAAK;MAC1B,KAAK,OAAO;QACV,OAAOoC,8DAAW,CAAC4R,KAAK;MAC1B;QACE,OAAO5R,8DAAW,CAACqG,IAAI;;EAE7B;EAEQ6I,aAAaA,CAACD,OAAgB;IACpC,IAAI,CAACrC,QAAQ,CAAC2C,IAAI,CAACN,OAAO,CAAC;IAC3B,IAAI,CAAC5B,GAAG,CAACyB,aAAa,EAAE;EAC1B;EAEQ4B,uBAAuBA,CAAC2B,MAAc,EAAEC,WAAoB;IAClE,MAAM3K,KAAK,GAAG,IAAI,CAACiF,QAAQ,CAAC2F,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAAClb,EAAE,KAAK+a,MAAM,CAAC;IAC7D,IAAI1K,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,IAAI,CAACiF,QAAQ,CAACjF,KAAK,CAAC,GAAG2K,WAAW;MAClC,IAAI,CAACjF,GAAG,CAACyB,aAAa,EAAE;;EAE5B;EAEQ6B,kBAAkBA,CAAC8B,SAAiB;IAC1C,MAAMxD,OAAO,GAAG,IAAI,CAACrC,QAAQ,CAACqF,IAAI,CAAEO,CAAC,IAAKA,CAAC,CAAClb,EAAE,KAAKmb,SAAS,CAAC;IAC7D,IAAIxD,OAAO,EAAE;MACXA,OAAO,CAACsB,SAAS,GAAG,KAAK;MACzBtB,OAAO,CAACyD,OAAO,GAAG,IAAI;MACtB,IAAI,CAACrF,GAAG,CAACyB,aAAa,EAAE;;EAE5B;EAEQiC,mBAAmBA,CAACD,cAAuB;IACjD,MAAMnJ,KAAK,GAAG,IAAI,CAACiF,QAAQ,CAAC2F,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAAClb,EAAE,KAAKwZ,cAAc,CAACxZ,EAAE,CAAC;IACxE,IAAIqQ,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,IAAI,CAACiF,QAAQ,CAACjF,KAAK,CAAC,GAAGmJ,cAAc;MACrC,IAAI,CAACzD,GAAG,CAACyB,aAAa,EAAE;;EAE5B;EAEQqC,qBAAqBA,CAACsB,SAAiB;IAC7C,IAAI,CAAC7F,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC7M,MAAM,CAAEyS,CAAC,IAAKA,CAAC,CAAClb,EAAE,KAAKmb,SAAS,CAAC;IAC/D,IAAI,CAACpF,GAAG,CAACyB,aAAa,EAAE;EAC1B;EAEQmC,gBAAgBA,CAAChC,OAAgB;IACvC,IAAI,CAAC,IAAI,CAAC1B,WAAW,IAAI,CAAC0B,OAAO,CAACjO,MAAM,EAAE,OAAO,KAAK;IAEtD,MAAM8Q,aAAa,GAAG,IAAI,CAACvE,WAAW,CAACjW,EAAE,IAAI,IAAI,CAACiW,WAAW,CAACwE,GAAG;IACjE,MAAMY,QAAQ,GAAG1D,OAAO,CAACjO,MAAM,CAAC1J,EAAE,IAAI2X,OAAO,CAACjO,MAAM,CAAC+Q,GAAG;IAExD,OAAOD,aAAa,KAAKa,QAAQ;EACnC;EAEQrD,qBAAqBA,CAAC9S,KAAU;IACtC,IAAI,CAAC,IAAI,CAAC+Q,WAAW,EAAE;IAEvB,MAAMuE,aAAa,GAAG,IAAI,CAACvE,WAAW,CAACjW,EAAE,IAAI,IAAI,CAACiW,WAAW,CAACwE,GAAG;IAEjE,IAAIvV,KAAK,CAACoW,MAAM,KAAKd,aAAa,EAAE,OAAO,CAAC;IAE5C,IAAItV,KAAK,CAACgR,QAAQ,EAAE;MAClB;MACA,MAAMqF,IAAI,GAAG,IAAI,CAAC3S,oBAAoB,EAAEC,YAAY,EAAE8R,IAAI,CACvDC,CAAC,IAAK,CAACA,CAAC,CAAC5a,EAAE,IAAI4a,CAAC,CAACH,GAAG,MAAMvV,KAAK,CAACoW,MAAM,CACxC;MACD,IACEC,IAAI,IACJ,CAAC,IAAI,CAAC/F,WAAW,CAACmF,IAAI,CAAEa,CAAC,IAAK,CAACA,CAAC,CAACxb,EAAE,IAAIwb,CAAC,CAACf,GAAG,MAAMvV,KAAK,CAACoW,MAAM,CAAC,EAC/D;QACA,IAAI,CAAC9F,WAAW,CAACyC,IAAI,CAACsD,IAAI,CAAC;;KAE9B,MAAM;MACL;MACA,IAAI,CAAC/F,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC/M,MAAM,CACvC+S,CAAC,IAAK,CAACA,CAAC,CAACxb,EAAE,IAAIwb,CAAC,CAACf,GAAG,MAAMvV,KAAK,CAACoW,MAAM,CACxC;;IAGH,IAAI,CAACvF,GAAG,CAACyB,aAAa,EAAE;EAC1B;EAEQD,kBAAkBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACjC,QAAQ,CAAC7b,MAAM,IAAI,CAAC,IAAI,CAACwc,WAAW,EAAE;IAEhD,MAAMwF,cAAc,GAAG,IAAI,CAACnG,QAAQ,CAAC7M,MAAM,CACxCyS,CAAC,IACA,CAACA,CAAC,CAACQ,MAAM,IACTR,CAAC,CAACxR,MAAM,IACR,CAACwR,CAAC,CAACxR,MAAM,CAAC1J,EAAE,IAAIkb,CAAC,CAACxR,MAAM,CAAC+Q,GAAG,OACzB,IAAI,CAACxE,WAAY,CAACjW,EAAE,IAAI,IAAI,CAACiW,WAAY,CAACwE,GAAG,CAAC,CACpD;IAEDgB,cAAc,CAACrY,OAAO,CAAEuU,OAAO,IAAI;MACjC,IAAIA,OAAO,CAAC3X,EAAE,EAAE;QACd,IAAI,CAAC6X,iBAAiB,CAACF,OAAO,CAAC;;IAEnC,CAAC,CAAC;EACJ;EAEQE,iBAAiBA,CAACF,OAAgB;IACxC,IAAI,CAACA,OAAO,CAAC3X,EAAE,IAAI2X,OAAO,CAAC+D,MAAM,EAAE;IAEnC,IAAI,CAACjkB,cAAc,CAACogB,iBAAiB,CAACF,OAAO,CAAC3X,EAAE,CAAC,CAACtH,SAAS,CAAC;MAC1DY,IAAI,EAAGkgB,cAAc,IAAI;QACvB,IAAI,CAACC,mBAAmB,CAACD,cAAc,CAAC;MAC1C,CAAC;MACD9f,KAAK,EAAGA,KAAK,IAAI;QACf2d,OAAO,CAAC3d,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MAC3D;KACD,CAAC;EACJ;EAEQqd,cAAcA,CAAA;IACpB,IAAI,CAACf,MAAM,CAAC2F,iBAAiB,CAAC,MAAK;MACjC1hB,UAAU,CAAC,MAAK;QACd,IAAI,IAAI,CAAC2hB,iBAAiB,EAAE;UAC1B,MAAMC,OAAO,GAAG,IAAI,CAACD,iBAAiB,CAACzB,aAAa;UACpD0B,OAAO,CAACC,SAAS,GAAGD,OAAO,CAACE,YAAY;;MAE5C,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACJ;EAEA;EACA;EACA;EAEA9M,iBAAiBA,CAACC,SAAwB;IACxC,MAAM8M,IAAI,GAAG,IAAI3jB,IAAI,CAAC6W,SAAS,CAAC;IAChC,MAAMrO,GAAG,GAAG,IAAIxI,IAAI,EAAE;IACtB,MAAM4jB,WAAW,GAAG,CAACpb,GAAG,CAACqb,OAAO,EAAE,GAAGF,IAAI,CAACE,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAEvE,IAAID,WAAW,GAAG,EAAE,EAAE;MACpB,OAAOD,IAAI,CAACG,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;OACT,CAAC;KACH,MAAM;MACL,OAAOL,IAAI,CAACM,kBAAkB,CAAC,OAAO,EAAE;QACtCC,GAAG,EAAE,SAAS;QACdC,KAAK,EAAE;OACR,CAAC;;EAEN;EAEA;;;EAGA3P,gBAAgBA,CAAC8K,OAAgB;IAC/B,IAAIA,OAAO,CAACsB,SAAS,EAAE,OAAO,SAAS;IACvC,IAAItB,OAAO,CAACyD,OAAO,EAAE,OAAO,QAAQ;IACpC,IAAIzD,OAAO,CAAC9f,MAAM,EAAE,OAAO8f,OAAO,CAAC9f,MAAM;IACzC,IAAI8f,OAAO,CAAC+D,MAAM,EAAE,OAAO,MAAM;IACjC,IAAI/D,OAAO,CAAC8E,WAAW,EAAE,OAAO,WAAW;IAC3C,OAAO,MAAM;EACf;EAEA9N,WAAWA,CAACgJ,OAAgB;IAC1B,IAAI,CAAC,IAAI,CAAC1B,WAAW,IAAI,CAAC0B,OAAO,CAACjO,MAAM,EAAE,OAAO,KAAK;IAEtD,MAAM8Q,aAAa,GAAG,IAAI,CAACvE,WAAW,CAACjW,EAAE,IAAI,IAAI,CAACiW,WAAW,CAACwE,GAAG;IACjE,MAAMY,QAAQ,GAAG1D,OAAO,CAACjO,MAAM,CAAC1J,EAAE,IAAI2X,OAAO,CAACjO,MAAM,CAAC+Q,GAAG;IAExD,OAAOD,aAAa,KAAKa,QAAQ;EACnC;EAEAhM,aAAaA,CAAA;IACX,IAAI,IAAI,CAACmG,WAAW,CAAC/b,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAE5C,IAAI,IAAI,CAAC+b,WAAW,CAAC/b,MAAM,KAAK,CAAC,EAAE;MACjC,OAAO,GAAG,IAAI,CAAC+b,WAAW,CAAC,CAAC,CAAC,CAAC3L,QAAQ,2BAA2B;KAClE,MAAM;MACL,OAAO,GAAG,IAAI,CAAC2L,WAAW,CAAC/b,MAAM,sCAAsC;;EAE3E;EAEA6a,UAAUA,CAACpP,KAAoB;IAC7B,IAAIA,KAAK,CAACwX,GAAG,KAAK,OAAO,IAAI,CAACxX,KAAK,CAACyX,QAAQ,EAAE;MAC5CzX,KAAK,CAAC0X,cAAc,EAAE;MACtB,IAAI,CAACnK,WAAW,EAAE;KACnB,MAAM;MACL,IAAI,CAACgC,QAAQ,EAAE;;EAEnB;EAEAV,iBAAiBA,CAAA;IACf,IAAI,CAACuC,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEA3C,oBAAoBA,CAAA;IAClB,IAAI,CAAC7B,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;EACpD;EAEA+K,mBAAmBA,CAAClF,OAAgB;IAClC,IAAI,CAAC1H,cAAc,GAAG0H,OAAO;IAC7B,IAAI,CAACxD,cAAc,GAAGwD,OAAO,CAAC5N,OAAO,IAAI,EAAE;IAC3C,IAAI,CAAC+S,YAAY,CAAC3C,aAAa,CAAC4C,KAAK,EAAE;EACzC;EAEAhN,aAAaA,CAAA;IACX,IAAI,CAACE,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACkE,cAAc,GAAG,EAAE;EAC1B;EAEA9F,UAAUA,CAACsJ,OAAgB;IACzB,IAAI,CAAChI,UAAU,GAAGgI,OAAO;IACzB,IAAI,CAACmF,YAAY,CAAC3C,aAAa,CAAC4C,KAAK,EAAE;EACzC;EAEAtN,WAAWA,CAAA;IACT,IAAI,CAACE,UAAU,GAAG,IAAI;EACxB;EAEA;EACA;EACA;EAEAyF,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACxM,oBAAoB,IAAI,CAAC,IAAI,CAACqN,WAAW,EAAE,OAAO,EAAE;IAE9D,MAAMpN,YAAY,GAAG,IAAI,CAACD,oBAAoB,CAACC,YAAY,IAAI,EAAE;IACjE,MAAM2R,aAAa,GAAG,IAAI,CAACvE,WAAW,CAACjW,EAAE,IAAI,IAAI,CAACiW,WAAW,CAACwE,GAAG;IAEjE,MAAMC,SAAS,GAAG7R,YAAY,CAAC8R,IAAI,CAChCC,CAAC,IAAK,CAACA,CAAC,CAAC5a,EAAE,IAAI4a,CAAC,CAACH,GAAG,MAAMD,aAAa,CACzC;IAED,OAAOE,SAAS,EAAE7Q,QAAQ,IAAI,qBAAqB;EACrD;EAEAqL,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACtM,oBAAoB,IAAI,CAAC,IAAI,CAACqN,WAAW,EACjD,OAAO,mCAAmC;IAE5C,MAAMpN,YAAY,GAAG,IAAI,CAACD,oBAAoB,CAACC,YAAY,IAAI,EAAE;IACjE,MAAM2R,aAAa,GAAG,IAAI,CAACvE,WAAW,CAACjW,EAAE,IAAI,IAAI,CAACiW,WAAW,CAACwE,GAAG;IAEjE,MAAMC,SAAS,GAAG7R,YAAY,CAAC8R,IAAI,CAChCC,CAAC,IAAK,CAACA,CAAC,CAAC5a,EAAE,IAAI4a,CAAC,CAACH,GAAG,MAAMD,aAAa,CACzC;IAED,OAAOE,SAAS,EAAE/Q,KAAK,IAAI,mCAAmC;EAChE;EAEAqL,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACpM,oBAAoB,IAAI,CAAC,IAAI,CAACqN,WAAW,EAAE,OAAO,KAAK;IAEjE,MAAMpN,YAAY,GAAG,IAAI,CAACD,oBAAoB,CAACC,YAAY,IAAI,EAAE;IACjE,MAAM2R,aAAa,GAAG,IAAI,CAACvE,WAAW,CAACjW,EAAE,IAAI,IAAI,CAACiW,WAAW,CAACwE,GAAG;IAEjE,MAAMC,SAAS,GAAG7R,YAAY,CAAC8R,IAAI,CAChCC,CAAC,IAAK,CAACA,CAAC,CAAC5a,EAAE,IAAI4a,CAAC,CAACH,GAAG,MAAMD,aAAa,CACzC;IAED,OAAOE,SAAS,EAAEsC,QAAQ,IAAI,KAAK;EACrC;EAEAzH,gBAAgBA,CAAClF,KAAa,EAAEsH,OAAgB;IAC9C,OAAOA,OAAO,CAAC3X,EAAE,IAAI2X,OAAO,CAAC8C,GAAG,IAAIpK,KAAK,CAACrP,QAAQ,EAAE;EACtD;EAEAoJ,eAAeA,CAAC6S,UAAkC;IAChD,IAAI,CAACA,UAAU,EAAEzS,GAAG,EAAE;IAEtB;IACA/H,MAAM,CAACya,IAAI,CAACD,UAAU,CAACzS,GAAG,EAAE,QAAQ,CAAC;EACvC;EAEAM,cAAcA,CAACC,IAAwB;IACrC,IAAI,CAACA,IAAI,EAAE,OAAO,KAAK;IAEvB,MAAMoS,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACrC,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIC,QAAQ,GAAGtS,IAAI;IAEnB,OAAOsS,QAAQ,IAAI,IAAI,IAAID,SAAS,GAAGD,KAAK,CAAC1jB,MAAM,GAAG,CAAC,EAAE;MACvD4jB,QAAQ,IAAI,IAAI;MAChBD,SAAS,EAAE;;IAGb,OAAO,GAAGC,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,IAAIH,KAAK,CAACC,SAAS,CAAC,EAAE;EACrD;EAEAxS,YAAYA,CAACqS,UAAkC;IAC7C,IAAI,CAACA,UAAU,EAAEzS,GAAG,EAAE;IAEtB,MAAM+S,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGT,UAAU,CAACzS,GAAG;IAC1B+S,IAAI,CAACI,QAAQ,GAAGV,UAAU,CAAC7a,IAAI,IAAI,MAAM;IACzCob,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;IAC/BA,IAAI,CAACnD,KAAK,EAAE;IACZoD,QAAQ,CAACI,IAAI,CAACE,WAAW,CAACP,IAAI,CAAC;EACjC;EAEApS,gBAAgBA,CAACwM,OAAgB;IAC/B,IAAI,CAACA,OAAO,CAACtN,WAAW,GAAG,CAAC,CAAC,EAAEG,GAAG,EAAE;IAEpC,IAAI,CAAC/S,cAAc,CAACsmB,SAAS,CAACpG,OAAO,CAACtN,WAAW,CAAC,CAAC,CAAC,CAACG,GAAG,CAAC,CAACwT,KAAK,CAAEtkB,KAAK,IAAI;MACxE2d,OAAO,CAAC3d,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;MACnE,IAAI,CAAC9B,YAAY,CAAC+B,SAAS,CAAC,4CAA4C,CAAC;IAC3E,CAAC,CAAC;EACJ;EAEA0R,cAAcA,CAAC/I,QAA4B;IACzC,IAAI,CAACA,QAAQ,EAAE,OAAO,MAAM;IAE5B,MAAM2b,OAAO,GAAGnd,IAAI,CAACod,KAAK,CAAC5b,QAAQ,GAAG,EAAE,CAAC;IACzC,MAAM6b,OAAO,GAAG7b,QAAQ,GAAG,EAAE;IAE7B,OAAO,GAAG2b,OAAO,IAAIE,OAAO,CAACnd,QAAQ,EAAE,CAACod,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D;EAEA5P,eAAeA,CAACmJ,OAAgB;IAC9B;IACA;IACA,MAAM0G,OAAO,GAAG,EAAE;IAElB,IAAI,IAAI,CAAC1E,gBAAgB,CAAChC,OAAO,CAAC,EAAE;MAClC0G,OAAO,CAACpG,IAAI,CAAC,WAAW,CAAC;;IAG3B,IAAI,IAAI,CAACtJ,WAAW,CAACgJ,OAAO,CAAC,EAAE;MAC7B0G,OAAO,CAACpG,IAAI,CAAC,UAAU,CAAC;;IAG1BoG,OAAO,CAACpG,IAAI,CAAC,UAAU,EAAE,YAAY,EAAE,QAAQ,CAAC;IAEhD;IACAZ,OAAO,CAACiH,GAAG,CAAC,sCAAsC,EAAED,OAAO,CAAC;EAC9D;EAEA;;;EAGAhS,YAAYA,CAACsL,OAAgB;IAC3B,IAAI,CAACA,OAAO,CAACyD,OAAO,IAAI,CAAC,IAAI,CAACxS,oBAAoB,EAAE;IAEpD,MAAM5J,WAAW,GAAG,IAAI,CAAC8Z,cAAc,EAAE;IACzC,IAAI,CAAC9Z,WAAW,EAAE;IAElB;IACA2Y,OAAO,CAACyD,OAAO,GAAG,KAAK;IACvBzD,OAAO,CAACsB,SAAS,GAAG,IAAI;IAExB;IACA,MAAMC,cAAc,GAAGvB,OAAO,CAACtN,WAAW,EAAE5Q,MAAM,GAC9C,IAAI,CAAChC,cAAc,CAACgb,WAAW,CAC7BzT,WAAW,EACX2Y,OAAO,CAAC5N,OAAO,IAAI,EAAE,EACrBwU,SAAS,CACV,GACD,IAAI,CAAC9mB,cAAc,CAACgb,WAAW,CAACzT,WAAW,EAAE2Y,OAAO,CAAC5N,OAAO,IAAI,EAAE,CAAC;IAEvEmP,cAAc,CAACxgB,SAAS,CAAC;MACvBY,IAAI,EAAG6f,WAAW,IAAI;QACpB;QACA,IAAI,CAACC,uBAAuB,CAACzB,OAAO,CAAC3X,EAAG,EAAEmZ,WAAW,CAAC;QACtD,IAAI,CAACvhB,YAAY,CAAC4B,WAAW,CAAC,6BAA6B,CAAC;MAC9D,CAAC;MACDE,KAAK,EAAGA,KAAK,IAAI;QACf2d,OAAO,CAAC3d,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzDie,OAAO,CAACsB,SAAS,GAAG,KAAK;QACzBtB,OAAO,CAACyD,OAAO,GAAG,IAAI;QACtB,IAAI,CAACxjB,YAAY,CAAC+B,SAAS,CAAC,4BAA4B,CAAC;MAC3D;KACD,CAAC;EACJ;EAEA;;;EAGAuU,oBAAoBA,CAACyJ,OAAgB;IACnC,MAAMwD,SAAS,GAAGxD,OAAO,CAAC3X,EAAE,IAAI2X,OAAO,CAAC8C,GAAG;IAC3C,IAAI,IAAI,CAACtL,qBAAqB,KAAKgM,SAAS,EAAE;MAC5C,IAAI,CAAChM,qBAAqB,GAAG,IAAI;KAClC,MAAM;MACL,IAAI,CAACA,qBAAqB,GAAGgM,SAAS,IAAI,IAAI;;EAElD;EAEA;;;EAGAvP,cAAcA,CAAC+L,OAAgB,EAAE9L,KAAa;IAC5C,IAAI,CAAC,IAAI,CAACoK,WAAW,IAAI,CAAC0B,OAAO,CAAC3X,EAAE,EAAE;IAEtC,IAAI,CAACvI,cAAc,CAACmU,cAAc,CAAC+L,OAAO,CAAC3X,EAAE,EAAE6L,KAAK,CAAC,CAACnT,SAAS,CAAC;MAC9DY,IAAI,EAAGkgB,cAAc,IAAI;QACvB;QACA,MAAMnJ,KAAK,GAAG,IAAI,CAACiF,QAAQ,CAAC2F,SAAS,CAClCC,CAAC,IAAK,CAACA,CAAC,CAAClb,EAAE,IAAIkb,CAAC,CAACT,GAAG,OAAO9C,OAAO,CAAC3X,EAAE,IAAI2X,OAAO,CAAC8C,GAAG,CAAC,CACvD;QACD,IAAIpK,KAAK,KAAK,CAAC,CAAC,EAAE;UAChB,IAAI,CAACiF,QAAQ,CAACjF,KAAK,CAAC,GAAGmJ,cAAc;;QAGvC;QACA,IAAI,CAACrK,qBAAqB,GAAG,IAAI;QAEjC,IAAI,CAACvX,YAAY,CAAC4B,WAAW,CAAC,YAAYqS,KAAK,UAAU,CAAC;MAC5D,CAAC;MACDnS,KAAK,EAAGA,KAAK,IAAI;QACf2d,OAAO,CAAC3d,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAC9D,IAAI,CAAC9B,YAAY,CAAC+B,SAAS,CAAC,uCAAuC,CAAC;MACtE;KACD,CAAC;EACJ;EAEA;;;EAGA6kB,wBAAwBA,CAACC,SAAc;IACrC,IAAI,CAAC5M,iBAAiB,GAAG,KAAK;IAE9B,IAAI,CAAC,IAAI,CAACjJ,oBAAoB,IAAI,CAAC6V,SAAS,EAAE;IAE9C,MAAMzf,WAAW,GAAG,IAAI,CAAC8Z,cAAc,EAAE;IACzC,IAAI,CAAC9Z,WAAW,EAAE;IAElB;IACA,MAAM0f,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEH,SAAS,CAACI,IAAI,EAAE,oBAAoB,CAAC;IAC9DH,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEH,SAAS,CAACnc,QAAQ,CAACtB,QAAQ,EAAE,CAAC;IAC1D,IAAIyd,SAAS,CAACK,QAAQ,EAAE;MACtBJ,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEvZ,IAAI,CAACC,SAAS,CAACmZ,SAAS,CAACK,QAAQ,CAAC,CAAC;;IAGjE;IACA,IAAI,CAACrnB,cAAc,CAACsnB,gBAAgB,CAAC/f,WAAW,EAAE0f,QAAQ,CAAC,CAAChmB,SAAS,CAAC;MACpEY,IAAI,EAAG6f,WAAgB,IAAI;QACzB,IAAI,CAAC7D,QAAQ,CAAC2C,IAAI,CAACkB,WAAW,CAAC;QAC/B,IAAI,CAACpC,cAAc,EAAE;QACrB,IAAI,CAACnf,YAAY,CAAC4B,WAAW,CAAC,sBAAsB,CAAC;MACvD,CAAC;MACDE,KAAK,EAAGA,KAAU,IAAI;QACpB2d,OAAO,CAAC3d,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;QAChE,IAAI,CAAC9B,YAAY,CAAC+B,SAAS,CAAC,yCAAyC,CAAC;MACxE;KACD,CAAC;EACJ;EAEA;;;EAGAqlB,kBAAkBA,CAACC,QAAa;IAC9B,IAAI,CAAChN,kBAAkB,GAAG,KAAK;IAE/B,IAAI,CAAC,IAAI,CAACrJ,oBAAoB,IAAI,CAACqW,QAAQ,EAAE;IAE7C,MAAMjgB,WAAW,GAAG,IAAI,CAAC8Z,cAAc,EAAE;IACzC,IAAI,CAAC9Z,WAAW,EAAE;IAElB;IACA,MAAMkgB,eAAe,GAAG;MACtB5d,IAAI,EAAE,UAAU;MAChB6d,QAAQ,EAAEF,QAAQ,CAACE,QAAQ;MAC3BC,SAAS,EAAEH,QAAQ,CAACG,SAAS;MAC7BC,OAAO,EAAEJ,QAAQ,CAACI,OAAO;MACzBC,MAAM,EAAEL,QAAQ,CAACK;KAClB;IAED;IACA,IAAI,CAAC7nB,cAAc,CAChB8nB,mBAAmB,CAACvgB,WAAW,EAAEkgB,eAAe,CAAC,CACjDxmB,SAAS,CAAC;MACTY,IAAI,EAAG6f,WAAgB,IAAI;QACzB,IAAI,CAAC7D,QAAQ,CAAC2C,IAAI,CAACkB,WAAW,CAAC;QAC/B,IAAI,CAACpC,cAAc,EAAE;QACrB,IAAI,CAACnf,YAAY,CAAC4B,WAAW,CAAC,uBAAuB,CAAC;MACxD,CAAC;MACDE,KAAK,EAAGA,KAAU,IAAI;QACpB2d,OAAO,CAAC3d,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QAC/D,IAAI,CAAC9B,YAAY,CAAC+B,SAAS,CAAC,wCAAwC,CAAC;MACvE;KACD,CAAC;EACN;;;uBA75BWgc,oBAAoB,EAAAxb,+DAAA,CAAAE,qEAAA,GAAAF,+DAAA,CAAAI,+DAAA,GAAAJ,+DAAA,CAAAM,iEAAA,GAAAN,+DAAA,CAAAQ,4DAAA,GAAAR,+DAAA,CAAAQ,oDAAA,GAAAR,+DAAA,CAAAA,4DAAA,GAAAA,+DAAA,CAAAA,iDAAA;IAAA;EAAA;;;YAApBwb,oBAAoB;MAAA9a,SAAA;MAAAglB,SAAA,WAAAC,2BAAA3kB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;UClCjChB,wDAAA,IAAA4lB,mCAAA,mBAkfM;UAKN5lB,wDAAA,IAAA6lB,mCAAA,kBAsEM;;;UA7jBuB7lB,wDAAA,SAAAiB,GAAA,CAAAwN,oBAAA,CAA0B;UAwfpDzO,uDAAA,GAA2B;UAA3BA,wDAAA,UAAAiB,GAAA,CAAAwN,oBAAA,CAA2B;;;;;;;;;;;;;;;;;;;;;;;ACrfxB,MAAOqX,qBAAqB;;;uBAArBA,qBAAqB;IAAA;EAAA;;;YAArBA,qBAAqB;MAAAplB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAE,QAAA,WAAAilB,+BAAA/kB,EAAA,EAAAC,GAAA;MAAA+kB,MAAA;IAAA;EAAA;;;;;;;;;;;;;;;;;;;ACNqB;AACsB;AAGM;;;AAEnF,MAAMG,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH,4FAAsB;EACjCI,QAAQ,EAAE;EACR;EACA;IACEF,IAAI,EAAE,EAAE;IACRC,SAAS,EAAE7K,sFAAoB;IAC/B9V,IAAI,EAAE;MAAE6gB,KAAK,EAAE;IAAU;GAC1B;EACD;EACA;IACEH,IAAI,EAAE,iBAAiB;IACvBC,SAAS,EAAE7K,sFAAoB;IAC/B9V,IAAI,EAAE;MAAE6gB,KAAK,EAAE;IAAM;GACtB;CAEJ,CACF;AAMK,MAAOC,qBAAqB;;;uBAArBA,qBAAqB;IAAA;EAAA;;;YAArBA;IAAqB;EAAA;;;gBAHtBP,yDAAY,CAACQ,QAAQ,CAACN,MAAM,CAAC,EAC7BF,yDAAY;IAAA;EAAA;;;sHAEXO,qBAAqB;IAAAE,OAAA,GAAAxmB,yDAAA;IAAAymB,OAAA,GAFtBV,yDAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7BuB;AACA;AAEmB;AACA;AACpB;AAC+B;AACG;AACZ;AACe;AACe;AAEzB;AACP;;AAClE;AAqBM,MAAOiB,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA;IAAc;EAAA;;;iBAFd,CAACD,mFAAiB,EAAE9mB,4EAAc,CAAC;MAAAumB,OAAA,GAR5CE,yDAAY,EACZJ,2EAAqB,EACrBK,wDAAW,EACXC,gEAAmB,EACnBC,yDAAY,EACZd,0DAAY;IAAA;EAAA;;;sHAKHiB,cAAc;IAAAC,YAAA,GAjBvB3L,sFAAoB,EACpBsK,yFAAqB,EACrBkB,6EAAiB,EACjBd,4FAAsB,EACtB9oB,oGAAqB;IAAAspB,OAAA,GAGrBE,yDAAY,EACZJ,2EAAqB,EACrBK,wDAAW,EACXC,gEAAmB,EACnBC,yDAAY,EACZd,0DAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7B0C;AACrB;AAOyB;AAEN;;;;;;;;;;;;;ICwIlDjmB,4DAAA,cAGC;IAEIA,oDAAA,GAC4B;IAAAA,0DAAA,EAC9B;IACDA,4DAAA,WAAM;IAAAA,oDAAA,GAA2C;IAAAA,0DAAA,EAAO;;;;IAHrDA,uDAAA,GAC4B;IAD5BA,gEAAA,kBAAA4a,MAAA,CAAAxb,KAAA,CAAAE,MAAA,WAAAsb,MAAA,CAAA2M,UAAA,kBAC4B;IAEzBvnB,uDAAA,GAA2C;IAA3CA,gEAAA,UAAA4a,MAAA,CAAAoB,WAAA,WAAApB,MAAA,CAAA4M,UAAA,KAA2C;;;;;IAcrDxnB,4DAAA,cAA2E;IACzEA,uDAAA,cAA6C;IAC7CA,4DAAA,cAAqC;IAAAA,oDAAA,qCAA8B;IAAAA,0DAAA,EAAM;;;;;IAI3EA,4DAAA,cAA2E;IAEvEA,uDAAA,YAA4B;IAC9BA,0DAAA,EAAM;IACNA,4DAAA,aAAmC;IAAAA,oDAAA,oCAAwB;IAAAA,0DAAA,EAAK;IAChEA,4DAAA,YAAiC;IAC/BA,oDAAA,mEACF;IAAAA,0DAAA,EAAI;;;;;IAeEA,uDAAA,eAGQ;;;;;;IAYVA,4DAAA,iBAKC;IAHCA,wDAAA,mBAAAynB,wEAAA;MAAAznB,2DAAA,CAAA0nB,IAAA;MAAA,MAAAC,OAAA,GAAA3nB,2DAAA,GAAA+P,SAAA;MAAA,MAAAwF,OAAA,GAAAvV,2DAAA;MAAA,OAASA,yDAAA,CAAAuV,OAAA,CAAAtG,cAAA,CAAA0Y,OAAA,CAAA9hB,EAAA,IAAA8hB,OAAA,CAAArH,GAAA,CAAmC;IAAA,EAAC;IAI7CtgB,uDAAA,YAA4B;IAC9BA,0DAAA,EAAS;;;;;;IACTA,4DAAA,iBAKC;IAHCA,wDAAA,mBAAA4nB,wEAAA;MAAA5nB,2DAAA,CAAA6nB,IAAA;MAAA,MAAAF,OAAA,GAAA3nB,2DAAA,GAAA+P,SAAA;MAAA,MAAAiH,OAAA,GAAAhX,2DAAA;MAAA,OAASA,yDAAA,CAAAgX,OAAA,CAAA3H,cAAA,CAAAsY,OAAA,CAAA9hB,EAAA,IAAA8hB,OAAA,CAAArH,GAAA,CAAmC;IAAA,EAAC;IAI7CtgB,uDAAA,YAA4B;IAC9BA,0DAAA,EAAS;;;;;;IAxCbA,4DAAA,aAA4D;IAGxDA,wDAAA,mBAAA8nB,2DAAA;MAAA,MAAAzW,WAAA,GAAArR,2DAAA,CAAA+nB,IAAA;MAAA,MAAAJ,OAAA,GAAAtW,WAAA,CAAAtB,SAAA;MAAA,MAAAiY,OAAA,GAAAhoB,2DAAA;MAAA,OAASA,yDAAA,CAAAgoB,OAAA,CAAAC,iBAAA,CAAAN,OAAA,CAAA9hB,EAAA,IAAA8hB,OAAA,CAAArH,GAAA,CAAsC;IAAA,EAAC;IAEhDtgB,4DAAA,cAA+B;IAC7BA,uDAAA,cAGE;IACFA,wDAAA,IAAAkoB,4CAAA,mBAGQ;IACVloB,0DAAA,EAAM;IACNA,4DAAA,cAAkC;IAE9BA,oDAAA,GACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,YAAiC;IAAAA,oDAAA,GAAgB;IAAAA,0DAAA,EAAI;IAKzDA,4DAAA,eAAqC;IACnCA,wDAAA,KAAAmoB,+CAAA,qBAOS;IACTnoB,wDAAA,KAAAooB,+CAAA,qBAOS;IACXpoB,0DAAA,EAAM;;;;IAlCAA,uDAAA,GAAwD;IAAxDA,wDAAA,QAAA2nB,OAAA,CAAAnY,KAAA,wCAAAxP,2DAAA,CAAwD;IAIvDA,uDAAA,GAAmB;IAAnBA,wDAAA,SAAA2nB,OAAA,CAAA9E,QAAA,CAAmB;IAMpB7iB,uDAAA,GACF;IADEA,gEAAA,MAAA2nB,OAAA,CAAAjY,QAAA,MACF;IACiC1P,uDAAA,GAAgB;IAAhBA,+DAAA,CAAA2nB,OAAA,CAAAU,KAAA,CAAgB;IAOhDroB,uDAAA,GAAmB;IAAnBA,wDAAA,SAAA2nB,OAAA,CAAA9E,QAAA,CAAmB;IAQnB7iB,uDAAA,GAAmB;IAAnBA,wDAAA,SAAA2nB,OAAA,CAAA9E,QAAA,CAAmB;;;;;IAnC5B7iB,4DAAA,aAA2D;IACzDA,wDAAA,IAAAsoB,qCAAA,kBA0CK;IACPtoB,0DAAA,EAAK;;;;IA3CkBA,uDAAA,GAAQ;IAARA,wDAAA,YAAAuoB,MAAA,CAAAnpB,KAAA,CAAQ;;;;;IA8C/BY,4DAAA,cAAyE;IAErEA,uDAAA,cAAsE;IAGxEA,0DAAA,EAAM;IACNA,4DAAA,cAAqC;IACnCA,oDAAA,6CACF;IAAAA,0DAAA,EAAM;;;;;;IAIRA,4DAAA,cAA4E;IAClEA,wDAAA,mBAAAwoB,0DAAA;MAAAxoB,2DAAA,CAAAyoB,IAAA;MAAA,MAAAC,OAAA,GAAA1oB,2DAAA;MAAA,OAASA,yDAAA,CAAA0oB,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAC9B3oB,uDAAA,YAAwC;IACxCA,oDAAA,oCACF;IAAAA,0DAAA,EAAS;;;ADtOT,MAAOgnB,iBAAiB;EA8B5B3pB,YACU8C,cAA8B,EAC9ByoB,WAAwB,EACzBjN,MAAc,EACdD,KAAqB,EACpBD,WAA4B,EAC5Bhe,YAA0B,EAC1BwF,MAAqB,EACrBzF,YAA0B;IAP1B,KAAA2C,cAAc,GAAdA,cAAc;IACd,KAAAyoB,WAAW,GAAXA,WAAW;IACZ,KAAAjN,MAAM,GAANA,MAAM;IACN,KAAAD,KAAK,GAALA,KAAK;IACJ,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAhe,YAAY,GAAZA,YAAY;IACZ,KAAAwF,MAAM,GAANA,MAAM;IACN,KAAAzF,YAAY,GAAZA,YAAY;IArCtB,KAAA4B,KAAK,GAAW,EAAE;IAClB,KAAAypB,OAAO,GAAG,IAAI;IACd,KAAAxI,aAAa,GAAkB,IAAI;IAGnC;IACA,KAAArE,WAAW,GAAG,CAAC;IACf,KAAA8M,QAAQ,GAAG,EAAE;IACb,KAAAvB,UAAU,GAAG,CAAC;IACd,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAuB,WAAW,GAAG,KAAK;IACnB,KAAAC,eAAe,GAAG,KAAK;IAEvB;IACA,KAAAC,MAAM,GAAG,UAAU;IACnB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,UAAU,GAAG,IAAI7B,qDAAS,CAAC;MACzBlL,WAAW,EAAE,IAAIiL,uDAAW,CAAC,EAAE,CAAC;MAChCxE,QAAQ,EAAE,IAAIwE,uDAAW,CAAiB,IAAI;KAC/C,CAAC;IAEF;IACA,KAAA+B,kBAAkB,GAAG,IAAI;IACzB,KAAAC,mBAAmB,GAAG,KAAK,CAAC,CAAC;IAGrB,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAA/M,aAAa,GAAiB,IAAI6K,8CAAY,EAAE;IAYtD,IAAI,CAACmC,WAAW,GAAG,IAAI,CAAC/rB,YAAY,CAACgB,aAAa,CAAC+G,IAAI,CACrDpD,oDAAG,CAAEnE,KAAK,IAAKA,KAAK,CAACiK,IAAI,KAAK,MAAM,CAAC,CACtC;EACH;EAEA7J,QAAQA,CAAA;IACN,IAAI,CAACiiB,aAAa,GAAG,IAAI,CAAC5E,WAAW,CAAC+N,gBAAgB,EAAE;IACxD,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,SAAS,EAAE;EAClB;EAEQF,oBAAoBA,CAAA;IAC1B;IACA,MAAMG,SAAS,GAAG,IAAI,CAACT,UAAU,CAC9BU,GAAG,CAAC,aAAa,CAAE,CACnBC,YAAY,CAACvrB,SAAS,CAAC,MAAK;MAC3B,IAAI,CAACwrB,eAAe,EAAE;MACtB,IAAI,CAACJ,SAAS,EAAE;IAClB,CAAC,CAAC;IAEJ,IAAI,CAACpN,aAAa,CAACyN,GAAG,CAACJ,SAAS,CAAC;IAEjC;IACA,MAAMK,SAAS,GAAG,IAAI,CAACd,UAAU,CAC9BU,GAAG,CAAC,UAAU,CAAE,CAChBC,YAAY,CAACvrB,SAAS,CAAC,MAAK;MAC3B,IAAI,CAACwrB,eAAe,EAAE;MACtB,IAAI,CAACJ,SAAS,EAAE;IAClB,CAAC,CAAC;IAEJ,IAAI,CAACpN,aAAa,CAACyN,GAAG,CAACC,SAAS,CAAC;EACnC;EAEQP,gBAAgBA,CAAA;IACtB,IAAI,IAAI,CAACN,kBAAkB,EAAE;MAC3B,IAAI,CAACc,uBAAuB,GAAG/sB,+CAAQ,CACrC,IAAI,CAACksB,mBAAmB,CACzB,CAAC9qB,SAAS,CAAC,MAAK;QACf,IAAI,CAAC,IAAI,CAACsqB,OAAO,IAAI,CAAC,IAAI,CAACM,UAAU,CAACU,GAAG,CAAC,aAAa,CAAC,EAAEvjB,KAAK,EAAE;UAC/D,IAAI,CAACqjB,SAAS,CAAC,IAAI,CAAC;;MAExB,CAAC,CAAC;;EAEN;EAEAQ,iBAAiBA,CAAA;IACf,IAAI,CAACf,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;IAElD,IAAI,IAAI,CAACA,kBAAkB,EAAE;MAC3B,IAAI,CAACM,gBAAgB,EAAE;KACxB,MAAM,IAAI,IAAI,CAACQ,uBAAuB,EAAE;MACvC,IAAI,CAACA,uBAAuB,CAACvrB,WAAW,EAAE;MAC1C,IAAI,CAACurB,uBAAuB,GAAG9F,SAAS;;EAE5C;EAEA2F,eAAeA,CAAA;IACb,IAAI,CAAC/N,WAAW,GAAG,CAAC;EACtB;EAEA;EACA,IAAII,WAAWA,CAAA;IACb,OAAO,IAAI,CAAC+M,UAAU,CAACU,GAAG,CAAC,aAAa,CAAC,EAAEvjB,KAAK,IAAI,EAAE;EACxD;EAEA;EACA,IAAI8V,WAAWA,CAAC9V,KAAa;IAC3B,IAAI,CAAC6iB,UAAU,CAACU,GAAG,CAAC,aAAa,CAAC,EAAEO,QAAQ,CAAC9jB,KAAK,CAAC;EACrD;EAEA;EACA+jB,IAAIA,CAACC,IAAS;IACZ,OAAOA,IAAI;EACb;EAEAX,SAASA,CAACY,YAAY,GAAG,KAAK;IAC5B,IAAI,IAAI,CAACjB,WAAW,EAAE;IAEtB,IAAI,CAACT,OAAO,GAAG,IAAI;IAEnB,MAAMzM,WAAW,GAAG,IAAI,CAAC+M,UAAU,CAACU,GAAG,CAAC,aAAa,CAAC,EAAEvjB,KAAK,IAAI,EAAE;IACnE,MAAMuc,QAAQ,GAAG,IAAI,CAACsG,UAAU,CAACU,GAAG,CAAC,UAAU,CAAC,EAAEvjB,KAAK;IAEvD,MAAMyX,GAAG,GAAG,IAAI,CAAC5d,cAAc,CAACqqB,WAAW,CACzCD,YAAY,EACZnO,WAAW,EACX,IAAI,CAACJ,WAAW,EAChB,IAAI,CAAC8M,QAAQ,EACb,IAAI,CAACG,MAAM,EACX,IAAI,CAACC,SAAS,EACdrG,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAGuB,SAAS,CACrC,CAAC7lB,SAAS,CAAC;MACVY,IAAI,EAAGC,KAAK,IAAI;QACd,IAAI,CAACwgB,KAAK,CAAC6K,OAAO,CAACrrB,KAAK,CAAC,EAAE;UACzB,IAAI,CAACA,KAAK,GAAG,EAAE;UACf,IAAI,CAACypB,OAAO,GAAG,KAAK;UACpB,IAAI,CAACS,WAAW,GAAG,KAAK;UACxB,IAAI,CAAC7rB,YAAY,CAAC+B,SAAS,CAAC,oCAAoC,CAAC;UACjE;;QAGF;QACA,IAAI,IAAI,CAACwc,WAAW,KAAK,CAAC,EAAE;UAC1B;UACA,IAAI,CAAC5c,KAAK,GAAGA,KAAK,CAACkP,MAAM,CAAE8S,IAAI,IAAI;YACjC,IAAI,CAACA,IAAI,EAAE,OAAO,KAAK;YACvB,MAAMD,MAAM,GAAGC,IAAI,CAACvb,EAAE,IAAIub,IAAI,CAACd,GAAG;YAClC,OAAOa,MAAM,KAAK,IAAI,CAACd,aAAa;UACtC,CAAC,CAAC;SACH,MAAM;UACL;UACA,MAAMqK,QAAQ,GAAGtrB,KAAK,CAACkP,MAAM,CAAEqc,OAAO,IAAI;YACxC,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;YAC1B,MAAMxJ,MAAM,GAAGwJ,OAAO,CAAC9kB,EAAE,IAAI8kB,OAAO,CAACrK,GAAG;YACxC,OACEa,MAAM,KAAK,IAAI,CAACd,aAAa,IAC7B,CAAC,IAAI,CAACjhB,KAAK,CAACwrB,IAAI,CACbC,YAAY,IACX,CAACA,YAAY,CAAChlB,EAAE,IAAIglB,YAAY,CAACvK,GAAG,MAAMa,MAAM,CACnD;UAEL,CAAC,CAAC;UAEF,IAAI,CAAC/hB,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK,EAAE,GAAGsrB,QAAQ,CAAC;;QAG3C;QACA,MAAMI,UAAU,GAAG,IAAI,CAAC3qB,cAAc,CAAC4qB,qBAAqB;QAC5D,IAAI,CAACxD,UAAU,GAAGuD,UAAU,CAACE,UAAU;QACvC,IAAI,CAACxD,UAAU,GAAGsD,UAAU,CAACtD,UAAU;QACvC,IAAI,CAACuB,WAAW,GAAG+B,UAAU,CAAC/B,WAAW;QACzC,IAAI,CAACC,eAAe,GAAG8B,UAAU,CAAC9B,eAAe;QAEjD,IAAI,CAACH,OAAO,GAAG,KAAK;QACpB,IAAI,CAACS,WAAW,GAAG,KAAK;MAC1B,CAAC;MACD/pB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACspB,OAAO,GAAG,KAAK;QACpB,IAAI,CAACS,WAAW,GAAG,KAAK;QACxB,IAAI,CAAC7rB,YAAY,CAAC+B,SAAS,CACzB,yBAAyBD,KAAK,CAACie,OAAO,IAAI,eAAe,EAAE,CAC5D;QAED,IAAI,IAAI,CAACxB,WAAW,KAAK,CAAC,EAAE;UAC1B,IAAI,CAAC5c,KAAK,GAAG,EAAE;;MAEnB,CAAC;MACDwN,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAACic,OAAO,GAAG,KAAK;QACpB,IAAI,CAACS,WAAW,GAAG,KAAK;MAC1B;KACD,CAAC;IAEF,IAAI,CAAC/M,aAAa,CAACyN,GAAG,CAACjM,GAAG,CAAC;EAC7B;EAEAkK,iBAAiBA,CAAC9G,MAA0B;IAC1C,IAAI,CAACA,MAAM,EAAE;MACX,IAAI,CAAC1jB,YAAY,CAAC+B,SAAS,CACzB,+CAA+C,CAChD;MACD;;IAGF,IAAI,CAAC/B,YAAY,CAACmC,QAAQ,CAAC,0BAA0B,CAAC;IAEtD,IAAI,CAACO,cAAc,CAAC8qB,kBAAkB,CAAC9J,MAAM,CAAC,CAAC5iB,SAAS,CAAC;MACvDY,IAAI,EAAGge,YAAY,IAAI;QACrB,IAAI,CAACA,YAAY,IAAI,CAACA,YAAY,CAACtX,EAAE,EAAE;UACrC,IAAI,CAACpI,YAAY,CAAC+B,SAAS,CACzB,iDAAiD,CAClD;UACD;;QAGF,IAAI,CAACmc,MAAM,CACRmB,QAAQ,CAAC,CAAC,8BAA8B,EAAEK,YAAY,CAACtX,EAAE,CAAC,CAAC,CAC3DqlB,IAAI,CAAEhlB,OAAO,IAAI;UAChB,IAAI,CAACA,OAAO,EAAE;YACZ,IAAI,CAACzI,YAAY,CAAC+B,SAAS,CAAC,6BAA6B,CAAC;;QAE9D,CAAC,CAAC;MACN,CAAC;MACDD,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC9B,YAAY,CAAC+B,SAAS,CACzB,kCAAkCD,KAAK,CAACie,OAAO,IAAI,eAAe,EAAE,CACrE;MACH;KACD,CAAC;EACJ;EAEAvO,cAAcA,CAACkS,MAAc;IAC3B,IAAI,CAACA,MAAM,EAAE;IAEb,IAAI,CAACyH,WAAW,CAAChkB,YAAY,CAACuc,MAAM,EAAE9e,kEAAQ,CAAC8d,KAAK,CAAC,CAAC5hB,SAAS,CAAC;MAC9DY,IAAI,EAAGsG,IAAU,IAAI;QACnB,IAAI,CAAChI,YAAY,CAAC4B,WAAW,CAAC,sBAAsB,CAAC;MACvD,CAAC;MACDE,KAAK,EAAGA,KAAU,IAAI;QACpB,IAAI,CAAC9B,YAAY,CAAC+B,SAAS,CAAC,+BAA+B,CAAC;MAC9D;KACD,CAAC;EACJ;EAEA6P,cAAcA,CAAC8R,MAAc;IAC3B,IAAI,CAACA,MAAM,EAAE;IAEb,IAAI,CAACyH,WAAW,CAAChkB,YAAY,CAACuc,MAAM,EAAE9e,kEAAQ,CAAC8J,KAAK,CAAC,CAAC5N,SAAS,CAAC;MAC9DY,IAAI,EAAGsG,IAAU,IAAI;QACnB,IAAI,CAAChI,YAAY,CAAC4B,WAAW,CAAC,sBAAsB,CAAC;MACvD,CAAC;MACDE,KAAK,EAAGA,KAAU,IAAI;QACpB,IAAI,CAAC9B,YAAY,CAAC+B,SAAS,CAAC,+BAA+B,CAAC;MAC9D;KACD,CAAC;EACJ;EAEAmpB,YAAYA,CAAA;IACV,IAAI,IAAI,CAACI,WAAW,IAAI,CAAC,IAAI,CAACF,OAAO,EAAE;MACrC,IAAI,CAACS,WAAW,GAAG,IAAI;MACvB,IAAI,CAACtN,WAAW,EAAE;MAClB,IAAI,CAAC2N,SAAS,EAAE;;EAEpB;EAEAwB,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACnC,eAAe,IAAI,CAAC,IAAI,CAACH,OAAO,EAAE;MACzC,IAAI,CAACS,WAAW,GAAG,IAAI;MACvB,IAAI,CAACtN,WAAW,EAAE;MAClB,IAAI,CAAC2N,SAAS,EAAE;;EAEpB;EAEAyB,YAAYA,CAAA;IACV,IAAI,CAACrB,eAAe,EAAE;IACtB,IAAI,CAACJ,SAAS,CAAC,IAAI,CAAC;EACtB;EAEA0B,YAAYA,CAAA;IACV,IAAI,CAAClC,UAAU,CAACmC,KAAK,CAAC;MACpBlP,WAAW,EAAE,EAAE;MACfyG,QAAQ,EAAE;KACX,CAAC;IACF,IAAI,CAACkH,eAAe,EAAE;IACtB,IAAI,CAACJ,SAAS,CAAC,IAAI,CAAC;EACtB;EAEA4B,eAAeA,CAACC,KAAa;IAC3B,IAAI,IAAI,CAACvC,MAAM,KAAKuC,KAAK,EAAE;MACzB;MACA,IAAI,CAACtC,SAAS,GAAG,IAAI,CAACA,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK;KAC3D,MAAM;MACL;MACA,IAAI,CAACD,MAAM,GAAGuC,KAAK;MACnB,IAAI,CAACtC,SAAS,GAAG,KAAK;;IAGxB,IAAI,CAACa,eAAe,EAAE;IACtB,IAAI,CAACJ,SAAS,CAAC,IAAI,CAAC;EACtB;EAEA;;;EAGA8B,qBAAqBA,CAAA;IACnB,IAAI,CAAC9P,MAAM,CAACmB,QAAQ,CAAC,CAAC,yBAAyB,CAAC,CAAC;EACnD;EAEApe,WAAWA,CAAA;IACT,IAAI,CAAC6d,aAAa,CAAC5d,WAAW,EAAE;IAChC,IAAI,IAAI,CAACurB,uBAAuB,EAAE;MAChC,IAAI,CAACA,uBAAuB,CAACvrB,WAAW,EAAE;;EAE9C;;;uBA1TWqoB,iBAAiB,EAAAhnB,+DAAA,CAAAE,4EAAA,GAAAF,+DAAA,CAAAI,sEAAA,GAAAJ,+DAAA,CAAAM,oDAAA,GAAAN,+DAAA,CAAAM,4DAAA,GAAAN,+DAAA,CAAAQ,8EAAA,GAAAR,+DAAA,CAAA2rB,wEAAA,GAAA3rB,+DAAA,CAAA4rB,0EAAA,GAAA5rB,+DAAA,CAAA6rB,qEAAA;IAAA;EAAA;;;YAAjB7E,iBAAiB;MAAAtmB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAgrB,2BAAA9qB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCpB9BhB,4DAAA,aAGC;;UAECA,4DAAA,aAAkE;UAEhEA,uDAAA,aAEO;UAcPA,4DAAA,aAAuD;UAEnDA,uDAAA,aAA6C;UAW/CA,0DAAA,EAAM;UAIRA,4DAAA,cAAyE;UACvEA,uDAAA,eAAqE;UACvEA,0DAAA,EAAM;UAGRA,4DAAA,eAAqC;UAEJA,oDAAA,6BAAqB;UAAAA,0DAAA,EAAK;UACvDA,4DAAA,eAA4B;UAExBA,wDAAA,mBAAA+rB,oDAAA;YAAA,OAAS9qB,GAAA,CAAAmqB,YAAA,EAAc;UAAA,EAAC;UAIxBprB,uDAAA,aAA+B;UACjCA,0DAAA,EAAS;UACTA,4DAAA,kBAGC;UAFCA,wDAAA,mBAAAgsB,oDAAA;YAAA,OAAS/qB,GAAA,CAAAwqB,qBAAA,EAAuB;UAAA,EAAC;UAGjCzrB,uDAAA,aAAiC;UACnCA,0DAAA,EAAS;UAKbA,4DAAA,eAAuB;UAKjBA,wDAAA,2BAAAisB,2DAAAnS,MAAA;YAAA,OAAA7Y,GAAA,CAAAmb,WAAA,GAAAtC,MAAA;UAAA,EAAsC;UAFxC9Z,0DAAA,EAME;UACFA,uDAAA,aAEK;UACPA,0DAAA,EAAM;UAGNA,4DAAA,eAA+C;UAUrCA,wDAAA,oBAAAksB,oDAAApS,MAAA;YAAA,IAAAqS,OAAA;YAAA,QAAAA,OAAA,GAERlrB,GAAA,CAAAkoB,UAAA,CAAAU,GAAA,CACD,UAAU,CAAC,mBADVsC,OAAA,CAAA/B,QAAA,CAAAtQ,MAAA,CAAA6F,MAAA,CAAAyM,OAAA,GAEF,IAAI,GAAG,IAAI,CACjB;UAAA,EADiB;UATHpsB,0DAAA,EAUE;UACFA,uDAAA,gBAAmD;UACrDA,0DAAA,EAAQ;UACRA,4DAAA,iBACG;UAAAA,oDAAA,2BAAmB;UAAAA,0DAAA,EACrB;UAIHA,4DAAA,eAAyC;UACRA,oDAAA,kBAAU;UAAAA,0DAAA,EAAO;UAChDA,4DAAA,kBAGC;UAFCA,wDAAA,oBAAAqsB,qDAAAvS,MAAA;YAAA,OAAU7Y,GAAA,CAAAsqB,eAAA,CAAAzR,MAAA,CAAA6F,MAAA,CAAArZ,KAAA,CAA0C;UAAA,EAAC;UAGrDtG,4DAAA,kBAA4D;UAC1DA,oDAAA,aACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAAsD;UACpDA,oDAAA,eACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAAgE;UAC9DA,oDAAA,qCACF;UAAAA,0DAAA,EAAS;UAEXA,4DAAA,kBASC;UARCA,wDAAA,mBAAAssB,oDAAA;YAAArrB,GAAA,CAAAioB,SAAA,GAAAjoB,GAAA,CAAAioB,SAAA,KAC6C,KAAK,GAC/D,MAAM,GAAG,KAAK;YAAA,OACdjoB,GAAA,CAAA0oB,SAAA,CAAU,IAAI,CACf;UAAA,EADe;UAMD3pB,uDAAA,SAIK;UACPA,0DAAA,EAAS;UAKbA,4DAAA,kBAAiE;UAAzDA,wDAAA,mBAAAusB,oDAAA;YAAA,OAAStrB,GAAA,CAAAoqB,YAAA,EAAc;UAAA,EAAC;UAC9BrrB,oDAAA,6BACF;UAAAA,0DAAA,EAAS;UAIXA,wDAAA,KAAAwsB,iCAAA,kBASM;UACRxsB,0DAAA,EAAM;UAIRA,4DAAA,eAMC;UAJCA,wDAAA,oBAAAysB,kDAAA3S,MAAA;YAAA,OAAAA,MAAA,CAAA6F,MAAA,CAAAgC,SAAA,GAAA7H,MAAA,CAAA6F,MAAA,CAAA+M,YAAA,IAAA5S,MAAA,CAAA6F,MAAA,CAAAiC,YAAA,GAE8C,GAAG,IAAI3gB,GAAA,CAAA0nB,YAAA,EAEzD;UAAA,EADK;UAGD3oB,wDAAA,KAAA2sB,iCAAA,kBAGM;UAGN3sB,wDAAA,KAAA4sB,iCAAA,kBAQM;UAGN5sB,wDAAA,KAAA6sB,gCAAA,iBA4CK;UAGL7sB,wDAAA,KAAA8sB,iCAAA,kBASM;UAGN9sB,wDAAA,KAAA+sB,iCAAA,kBAKM;UACR/sB,0DAAA,EAAM;;;;UA1PNA,yDAAA,SAAAA,yDAAA,QAAAiB,GAAA,CAAAsoB,WAAA,EAAkC;UAoE1BvpB,uDAAA,IAAuB;UAAvBA,wDAAA,YAAAiB,GAAA,CAAAmb,WAAA,CAAuB;UAqBjBpc,uDAAA,GAAsD;UAAtDA,wDAAA,cAAAitB,OAAA,GAAAhsB,GAAA,CAAAkoB,UAAA,CAAAU,GAAA,+BAAAoD,OAAA,CAAA3mB,KAAA,WAAsD;UAqBhDtG,uDAAA,GAAkC;UAAlCA,wDAAA,aAAAiB,GAAA,CAAAgoB,MAAA,gBAAkC;UAGlCjpB,uDAAA,GAA+B;UAA/BA,wDAAA,aAAAiB,GAAA,CAAAgoB,MAAA,aAA+B;UAG/BjpB,uDAAA,GAAoC;UAApCA,wDAAA,aAAAiB,GAAA,CAAAgoB,MAAA,kBAAoC;UAU5CjpB,uDAAA,GAEC;UAFDA,wDAAA,UAAAiB,GAAA,CAAAioB,SAAA,0DAEC;UAGClpB,uDAAA,GAEC;UAFDA,wDAAA,CAAAiB,GAAA,CAAAioB,SAAA,mDAEC;UAcRlpB,uDAAA,GAAoB;UAApBA,wDAAA,SAAAiB,GAAA,CAAAsmB,UAAA,KAAoB;UAqBnBvnB,uDAAA,GAA8B;UAA9BA,wDAAA,SAAAiB,GAAA,CAAA4nB,OAAA,KAAA5nB,GAAA,CAAA7B,KAAA,CAAAE,MAAA,CAA8B;UAM9BU,uDAAA,GAAoC;UAApCA,wDAAA,UAAAiB,GAAA,CAAA4nB,OAAA,IAAA5nB,GAAA,CAAA7B,KAAA,CAAAE,MAAA,OAAoC;UAWrCU,uDAAA,GAAsB;UAAtBA,wDAAA,SAAAiB,GAAA,CAAA7B,KAAA,CAAAE,MAAA,KAAsB;UA+CrBU,uDAAA,GAAiC;UAAjCA,wDAAA,SAAAiB,GAAA,CAAA4nB,OAAA,IAAA5nB,GAAA,CAAA7B,KAAA,CAAAE,MAAA,KAAiC;UAYjCU,uDAAA,GAA6B;UAA7BA,wDAAA,SAAAiB,GAAA,CAAA8nB,WAAA,KAAA9nB,GAAA,CAAA4nB,OAAA,CAA6B", "sources": ["./src/app/components/system-status/system-status.component.ts", "./src/app/services/call.service.ts", "./src/app/views/front/messages/message-chat/message-chat.component.ts", "./src/app/views/front/messages/message-chat/message-chat.component.html", "./src/app/views/front/messages/messages-list/messages-list.component.ts", "./src/app/views/front/messages/messages-routing.module.ts", "./src/app/views/front/messages/messages.module.ts", "./src/app/views/front/messages/user-list/user-list.component.ts", "./src/app/views/front/messages/user-list/user-list.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { Subscription, interval } from 'rxjs';\nimport { MessageService } from '../../services/message.service';\nimport { MockDataService } from '../../services/mock-data.service';\nimport { ThemeService } from '../../services/theme.service';\nimport { ToastService } from '../../services/toast.service';\n\ninterface SystemStatus {\n  backend: 'online' | 'offline' | 'checking';\n  frontend: 'online' | 'offline' | 'checking';\n  database: 'online' | 'offline' | 'checking';\n  websocket: 'online' | 'offline' | 'checking';\n  mockData: 'available' | 'unavailable';\n  theme: string;\n  lastCheck: Date;\n}\n\n@Component({\n  selector: 'app-system-status',\n  template: `\n    <div class=\"system-status-panel bg-gray-800 rounded-lg p-6 border border-gray-700\">\n      <div class=\"flex items-center justify-between mb-4\">\n        <h3 class=\"text-lg font-semibold text-white flex items-center\">\n          <i class=\"fas fa-heartbeat text-blue-400 mr-2\"></i>\n          État du système\n        </h3>\n        <button \n          (click)=\"checkSystemStatus()\"\n          class=\"px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors\"\n          [disabled]=\"isChecking\"\n        >\n          <i class=\"fas fa-sync-alt mr-1\" [class.fa-spin]=\"isChecking\"></i>\n          {{ isChecking ? 'Vérification...' : 'Actualiser' }}\n        </button>\n      </div>\n\n      <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n        <!-- Backend Status -->\n        <div class=\"status-item\">\n          <div class=\"flex items-center justify-between\">\n            <span class=\"text-gray-300\">Backend</span>\n            <div class=\"flex items-center\">\n              <div \n                class=\"w-3 h-3 rounded-full mr-2\"\n                [class.bg-green-500]=\"status.backend === 'online'\"\n                [class.bg-red-500]=\"status.backend === 'offline'\"\n                [class.bg-yellow-500]=\"status.backend === 'checking'\"\n                [class.animate-pulse]=\"status.backend === 'checking'\"\n              ></div>\n              <span class=\"text-sm\" [class.text-green-400]=\"status.backend === 'online'\"\n                    [class.text-red-400]=\"status.backend === 'offline'\"\n                    [class.text-yellow-400]=\"status.backend === 'checking'\">\n                {{ getStatusText(status.backend) }}\n              </span>\n            </div>\n          </div>\n        </div>\n\n        <!-- Frontend Status -->\n        <div class=\"status-item\">\n          <div class=\"flex items-center justify-between\">\n            <span class=\"text-gray-300\">Frontend</span>\n            <div class=\"flex items-center\">\n              <div \n                class=\"w-3 h-3 rounded-full mr-2\"\n                [class.bg-green-500]=\"status.frontend === 'online'\"\n                [class.bg-red-500]=\"status.frontend === 'offline'\"\n                [class.bg-yellow-500]=\"status.frontend === 'checking'\"\n                [class.animate-pulse]=\"status.frontend === 'checking'\"\n              ></div>\n              <span class=\"text-sm\" [class.text-green-400]=\"status.frontend === 'online'\"\n                    [class.text-red-400]=\"status.frontend === 'offline'\"\n                    [class.text-yellow-400]=\"status.frontend === 'checking'\">\n                {{ getStatusText(status.frontend) }}\n              </span>\n            </div>\n          </div>\n        </div>\n\n        <!-- Database Status -->\n        <div class=\"status-item\">\n          <div class=\"flex items-center justify-between\">\n            <span class=\"text-gray-300\">Base de données</span>\n            <div class=\"flex items-center\">\n              <div \n                class=\"w-3 h-3 rounded-full mr-2\"\n                [class.bg-green-500]=\"status.database === 'online'\"\n                [class.bg-red-500]=\"status.database === 'offline'\"\n                [class.bg-yellow-500]=\"status.database === 'checking'\"\n                [class.animate-pulse]=\"status.database === 'checking'\"\n              ></div>\n              <span class=\"text-sm\" [class.text-green-400]=\"status.database === 'online'\"\n                    [class.text-red-400]=\"status.database === 'offline'\"\n                    [class.text-yellow-400]=\"status.database === 'checking'\">\n                {{ getStatusText(status.database) }}\n              </span>\n            </div>\n          </div>\n        </div>\n\n        <!-- WebSocket Status -->\n        <div class=\"status-item\">\n          <div class=\"flex items-center justify-between\">\n            <span class=\"text-gray-300\">WebSocket</span>\n            <div class=\"flex items-center\">\n              <div \n                class=\"w-3 h-3 rounded-full mr-2\"\n                [class.bg-green-500]=\"status.websocket === 'online'\"\n                [class.bg-red-500]=\"status.websocket === 'offline'\"\n                [class.bg-yellow-500]=\"status.websocket === 'checking'\"\n                [class.animate-pulse]=\"status.websocket === 'checking'\"\n              ></div>\n              <span class=\"text-sm\" [class.text-green-400]=\"status.websocket === 'online'\"\n                    [class.text-red-400]=\"status.websocket === 'offline'\"\n                    [class.text-yellow-400]=\"status.websocket === 'checking'\">\n                {{ getStatusText(status.websocket) }}\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Mock Data Status -->\n      <div class=\"mt-4 p-3 bg-gray-700 rounded border-l-4\" \n           [class.border-green-500]=\"status.mockData === 'available'\"\n           [class.border-red-500]=\"status.mockData === 'unavailable'\">\n        <div class=\"flex items-center justify-between\">\n          <span class=\"text-gray-300\">Données de test</span>\n          <span class=\"text-sm\" [class.text-green-400]=\"status.mockData === 'available'\"\n                [class.text-red-400]=\"status.mockData === 'unavailable'\">\n            {{ status.mockData === 'available' ? 'Disponibles' : 'Indisponibles' }}\n          </span>\n        </div>\n        <p class=\"text-xs text-gray-400 mt-1\">\n          {{ status.mockData === 'available' ? \n             'Le mode démo est actif avec des données de test' : \n             'Aucune donnée de test disponible' }}\n        </p>\n      </div>\n\n      <!-- Theme Status -->\n      <div class=\"mt-4 p-3 bg-gray-700 rounded\">\n        <div class=\"flex items-center justify-between\">\n          <span class=\"text-gray-300\">Thème actuel</span>\n          <span class=\"text-sm text-blue-400\">{{ status.theme }}</span>\n        </div>\n      </div>\n\n      <!-- Last Check -->\n      <div class=\"mt-4 text-xs text-gray-500 text-center\">\n        Dernière vérification : {{ status.lastCheck | date:'medium' }}\n      </div>\n\n      <!-- Test Actions -->\n      <div class=\"mt-6 grid grid-cols-1 md:grid-cols-3 gap-2\">\n        <button \n          (click)=\"testMockData()\"\n          class=\"px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded text-sm transition-colors\"\n        >\n          <i class=\"fas fa-database mr-1\"></i>\n          Test données\n        </button>\n        <button \n          (click)=\"testThemes()\"\n          class=\"px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded text-sm transition-colors\"\n        >\n          <i class=\"fas fa-palette mr-1\"></i>\n          Test thèmes\n        </button>\n        <button \n          (click)=\"testNotifications()\"\n          class=\"px-3 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded text-sm transition-colors\"\n        >\n          <i class=\"fas fa-bell mr-1\"></i>\n          Test notifs\n        </button>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .status-item {\n      @apply p-3 bg-gray-700 rounded;\n    }\n    \n    .status-item:hover {\n      @apply bg-gray-600;\n    }\n  `]\n})\nexport class SystemStatusComponent implements OnInit, OnDestroy {\n  status: SystemStatus = {\n    backend: 'checking',\n    frontend: 'online', // Frontend is obviously online if this component is running\n    database: 'checking',\n    websocket: 'checking',\n    mockData: 'checking' as any,\n    theme: 'Chargement...',\n    lastCheck: new Date()\n  };\n\n  isChecking = false;\n  private subscription?: Subscription;\n\n  constructor(\n    private messageService: MessageService,\n    private mockDataService: MockDataService,\n    private themeService: ThemeService,\n    private toastService: ToastService\n  ) {}\n\n  ngOnInit(): void {\n    this.checkSystemStatus();\n    \n    // Auto-refresh every 30 seconds\n    this.subscription = interval(30000).subscribe(() => {\n      this.checkSystemStatus();\n    });\n\n    // Listen to theme changes\n    this.themeService.currentTheme$.subscribe(theme => {\n      this.status.theme = theme.displayName;\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.subscription?.unsubscribe();\n  }\n\n  async checkSystemStatus(): Promise<void> {\n    this.isChecking = true;\n    this.status.lastCheck = new Date();\n\n    // Check mock data availability\n    try {\n      await this.mockDataService.getUsers().toPromise();\n      this.status.mockData = 'available';\n    } catch {\n      this.status.mockData = 'unavailable';\n    }\n\n    // Check backend connectivity\n    try {\n      await this.messageService.getConversations().toPromise();\n      this.status.backend = 'online';\n      this.status.database = 'online';\n      this.status.websocket = 'online';\n    } catch {\n      this.status.backend = 'offline';\n      this.status.database = 'offline';\n      this.status.websocket = 'offline';\n    }\n\n    this.isChecking = false;\n  }\n\n  getStatusText(status: string): string {\n    switch (status) {\n      case 'online': return 'En ligne';\n      case 'offline': return 'Hors ligne';\n      case 'checking': return 'Vérification...';\n      default: return 'Inconnu';\n    }\n  }\n\n  testMockData(): void {\n    this.mockDataService.getUsers().subscribe({\n      next: (users) => {\n        this.toastService.showSuccess(`${users.length} utilisateurs de test chargés`);\n      },\n      error: () => {\n        this.toastService.showError('Erreur lors du chargement des données de test');\n      }\n    });\n  }\n\n  testThemes(): void {\n    const themes = this.themeService.getAvailableThemes();\n    this.toastService.showInfo(`${themes.length} thèmes disponibles`);\n  }\n\n  testNotifications(): void {\n    this.toastService.showSuccess('Test de notification réussi !');\n    setTimeout(() => {\n      this.toastService.showInfo('Notification d\\'information');\n    }, 1000);\n    setTimeout(() => {\n      this.toastService.showWarning('Notification d\\'avertissement');\n    }, 2000);\n  }\n}\n", "import { Injectable, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';\nimport { Apollo } from 'apollo-angular';\nimport { BehaviorSubject, Observable, throwError, of } from 'rxjs';\nimport { map, catchError, tap } from 'rxjs/operators';\nimport {\n  Call,\n  CallType,\n  CallStatus,\n  IncomingCall,\n  CallSuccess,\n  CallSignal,\n} from '../models/message.model';\nimport {\n  INITIATE_CALL_MUTATION,\n  ACCEPT_CALL_MUTATION,\n  REJECT_CALL_MUTATION,\n  END_CALL_MUTATION,\n  INCOMING_CALL_SUBSCRIPTION,\n  CALL_STATUS_CHANGED_SUBSCRIPTION,\n  CALL_SIGNAL_SUBSCRIPTION,\n  SEND_CALL_SIGNAL_MUTATION,\n} from '../graphql/message.graphql';\nimport { LoggerService } from './logger.service';\n\n/**\n * Service unifié pour la gestion des appels vidéo/audio\n * Gère l'état des appels, WebRTC, et la synchronisation\n */\n@Injectable({\n  providedIn: 'root',\n})\nexport class CallService implements OnD<PERSON>roy {\n  // ===== ÉTAT PRINCIPAL =====\n  private activeCall = new BehaviorSubject<Call | null>(null);\n  private incomingCall = new BehaviorSubject<IncomingCall | null>(null);\n  private callSignals = new BehaviorSubject<CallSignal | null>(null);\n\n  // Observables publics\n  public activeCall$ = this.activeCall.asObservable();\n  public incomingCall$ = this.incomingCall.asObservable();\n  public callSignals$ = this.callSignals.asObservable();\n\n  // ===== ÉTAT DES APPELS =====\n  private currentCallId: string | null = null;\n  private callState:\n    | 'idle'\n    | 'initiating'\n    | 'ringing'\n    | 'connecting'\n    | 'connected'\n    | 'ending' = 'idle';\n\n  // ===== GESTION AUDIO =====\n  private sounds: { [key: string]: HTMLAudioElement } = {};\n  private isPlaying: { [key: string]: boolean } = {};\n\n  // ===== WEBRTC =====\n  private peerConnection: RTCPeerConnection | null = null;\n  private localStream: MediaStream | null = null;\n  private remoteStream: MediaStream | null = null;\n  private localVideoElement: HTMLVideoElement | null = null;\n  private remoteVideoElement: HTMLVideoElement | null = null;\n  private isAudioEnabled = true;\n  private isVideoEnabled = true;\n\n  // Configuration WebRTC\n  private readonly rtcConfig: RTCConfiguration = {\n    iceServers: [\n      { urls: 'stun:stun.l.google.com:19302' },\n      { urls: 'stun:stun1.l.google.com:19302' },\n    ],\n  };\n\n  constructor(private apollo: Apollo, private logger: LoggerService) {\n    this.logger.info('CallService', '🚀 Initializing unified CallService...');\n    this.initializeSounds();\n    this.initializeSubscriptions();\n    this.initializeWebRTC();\n    this.logger.info('CallService', '✅ CallService initialized successfully');\n  }\n\n  ngOnDestroy(): void {\n    this.logger.info('CallService', '🔄 Destroying CallService...');\n    this.cleanup();\n  }\n\n  // ===== MÉTHODES PUBLIQUES PRINCIPALES =====\n\n  /**\n   * Initie un appel\n   */\n  initiateCall(\n    recipientId: string,\n    callType: CallType,\n    conversationId?: string\n  ): Observable<Call> {\n    this.logger.info('CallService', '📞 Initiating call:', {\n      recipientId,\n      callType,\n    });\n\n    if (this.callState !== 'idle') {\n      return throwError(() => new Error('Another call is already in progress'));\n    }\n\n    this.setCallState('initiating');\n    const callId = this.generateCallId();\n\n    return this.apollo\n      .mutate<{ initiateCall: Call }>({\n        mutation: INITIATE_CALL_MUTATION,\n        variables: {\n          recipientId,\n          callType,\n          callId,\n          conversationId,\n          // ✅ offer est maintenant optionnel - sera généré par WebRTC plus tard\n        },\n      })\n      .pipe(\n        map((result) => {\n          const call = result.data?.initiateCall;\n          if (!call) throw new Error('Failed to initiate call');\n\n          this.handleCallInitiated(call);\n          return call;\n        }),\n        catchError((error) => {\n          this.logger.error('CallService', 'Error initiating call:', error);\n          this.setCallState('idle');\n          return throwError(() => error);\n        })\n      );\n  }\n\n  /**\n   * Accepte un appel entrant\n   */\n  acceptCall(call: IncomingCall): Observable<Call> {\n    this.logger.info('CallService', '✅ Accepting call:', call.id);\n\n    if (!call) {\n      return throwError(() => new Error('No call to accept'));\n    }\n\n    this.setCallState('connecting');\n\n    return this.apollo\n      .mutate<{ acceptCall: Call }>({\n        mutation: ACCEPT_CALL_MUTATION,\n        variables: {\n          callId: call.id,\n          // ✅ answer est maintenant optionnel - sera généré par WebRTC plus tard\n        },\n      })\n      .pipe(\n        map((result) => {\n          const acceptedCall = result.data?.acceptCall;\n          if (!acceptedCall) throw new Error('Failed to accept call');\n\n          this.handleCallAccepted(acceptedCall);\n          return acceptedCall;\n        }),\n        catchError((error) => {\n          this.logger.error('CallService', 'Error accepting call:', error);\n          this.setCallState('idle');\n          return throwError(() => error);\n        })\n      );\n  }\n\n  /**\n   * Rejette un appel\n   */\n  rejectCall(callId: string, reason?: string): Observable<CallSuccess> {\n    this.logger.info('CallService', '❌ Rejecting call:', callId);\n\n    this.setCallState('ending');\n\n    return this.apollo\n      .mutate<{ rejectCall: CallSuccess }>({\n        mutation: REJECT_CALL_MUTATION,\n        variables: { callId, reason: reason || 'User rejected' },\n      })\n      .pipe(\n        map((result) => {\n          const success = result.data?.rejectCall;\n          if (!success) throw new Error('Failed to reject call');\n\n          this.handleCallEnded();\n          return success;\n        }),\n        catchError((error) => {\n          this.logger.error('CallService', 'Error rejecting call:', error);\n          this.handleCallEnded(); // Nettoyer même en cas d'erreur\n          return throwError(() => error);\n        })\n      );\n  }\n\n  /**\n   * Termine un appel\n   */\n  endCall(callId: string): Observable<CallSuccess> {\n    this.logger.info('CallService', '🔚 Ending call:', callId);\n\n    this.setCallState('ending');\n\n    return this.apollo\n      .mutate<{ endCall: CallSuccess }>({\n        mutation: END_CALL_MUTATION,\n        variables: { callId },\n      })\n      .pipe(\n        map((result) => {\n          const success = result.data?.endCall;\n          if (!success) throw new Error('Failed to end call');\n\n          this.handleCallEnded();\n          return success;\n        }),\n        catchError((error) => {\n          this.logger.error('CallService', 'Error ending call:', error);\n          this.handleCallEnded(); // Nettoyer même en cas d'erreur\n          return throwError(() => error);\n        })\n      );\n  }\n\n  // ===== GETTERS PUBLICS =====\n\n  get currentCall(): Call | null {\n    return this.activeCall.value;\n  }\n\n  get currentIncomingCall(): IncomingCall | null {\n    return this.incomingCall.value;\n  }\n\n  get isCallActive(): boolean {\n    return this.callState === 'connected';\n  }\n\n  get isCallInProgress(): boolean {\n    return this.callState !== 'idle';\n  }\n\n  // ===== MÉTHODES PRIVÉES =====\n\n  private generateCallId(): string {\n    return `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  private setCallState(state: typeof this.callState): void {\n    this.logger.debug(\n      'CallService',\n      `Call state: ${this.callState} → ${state}`\n    );\n    this.callState = state;\n  }\n\n  private handleCallInitiated(call: Call): void {\n    this.logger.info('CallService', 'Call initiated successfully:', call.id);\n    this.currentCallId = call.id;\n    this.activeCall.next(call);\n    this.setCallState('ringing');\n    this.play('ringtone', true);\n    this.startOutgoingCallMedia(call.type);\n  }\n\n  private handleCallAccepted(call: Call): void {\n    this.logger.info('CallService', 'Call accepted successfully:', call.id);\n    this.activeCall.next(call);\n    this.incomingCall.next(null);\n    this.setCallState('connected');\n    this.stop('ringtone');\n    this.play('call-connected');\n  }\n\n  private handleCallEnded(): void {\n    this.logger.info('CallService', 'Call ended, cleaning up');\n    this.setCallState('idle');\n    this.currentCallId = null;\n    this.activeCall.next(null);\n    this.incomingCall.next(null);\n    this.stopAllSounds();\n    this.play('call-end');\n    this.cleanupWebRTC();\n  }\n\n  private handleIncomingCall(call: IncomingCall): void {\n    this.logger.info('CallService', 'Incoming call received:', call.id);\n    this.currentCallId = call.id;\n    this.incomingCall.next(call);\n    this.setCallState('ringing');\n    this.play('ringtone', true);\n    this.prepareForIncomingCall(call);\n  }\n\n  private handleCallStatusChange(call: Call): void {\n    this.logger.info('CallService', 'Call status changed:', call.status);\n\n    if (call.id === this.currentCallId) {\n      this.activeCall.next(call);\n\n      switch (call.status) {\n        case CallStatus.CONNECTED:\n          this.setCallState('connected');\n          this.stop('ringtone');\n          this.play('call-connected');\n          break;\n        case CallStatus.ENDED:\n        case CallStatus.REJECTED:\n          this.handleCallEnded();\n          break;\n      }\n    }\n  }\n\n  private handleCallSignal(signal: CallSignal): void {\n    this.logger.debug('CallService', 'Call signal received:', signal.type);\n    this.callSignals.next(signal);\n    // Traitement WebRTC des signaux sera ajouté ici\n  }\n\n  // ===== INITIALISATION =====\n\n  private initializeSounds(): void {\n    this.logger.debug('CallService', 'Initializing sounds...');\n    this.createSyntheticSounds();\n  }\n\n  private createSyntheticSounds(): void {\n    this.createSyntheticSound('ringtone', [440, 554.37], 1.5, true);\n    this.createSyntheticSound(\n      'call-connected',\n      [523.25, 659.25, 783.99],\n      0.8,\n      false\n    );\n    this.createSyntheticSound('call-end', [392, 329.63, 261.63], 1.2, false);\n  }\n\n  private createSyntheticSound(\n    name: string,\n    frequencies: number[],\n    duration: number,\n    loop: boolean\n  ): void {\n    try {\n      const audioContext = new (window.AudioContext ||\n        (window as any).webkitAudioContext)();\n      const sampleRate = audioContext.sampleRate;\n      const frameCount = sampleRate * duration;\n      const buffer = audioContext.createBuffer(1, frameCount, sampleRate);\n      const channelData = buffer.getChannelData(0);\n\n      for (let i = 0; i < frameCount; i++) {\n        let sample = 0;\n        frequencies.forEach((freq) => {\n          const amplitude = 0.3 / frequencies.length;\n          const phase = (i / sampleRate) * freq * 2 * Math.PI;\n          sample += Math.sin(phase) * amplitude;\n        });\n        const envelope = Math.sin((i / frameCount) * Math.PI);\n        channelData[i] = sample * envelope;\n      }\n\n      const audio = new Audio();\n      audio.loop = loop;\n      (audio as any).customPlay = () => {\n        const source = audioContext.createBufferSource();\n        source.buffer = buffer;\n        source.loop = loop;\n        source.connect(audioContext.destination);\n        source.start();\n        if (!loop) {\n          setTimeout(() => {\n            this.isPlaying[name] = false;\n          }, duration * 1000);\n        }\n        return source;\n      };\n\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n    } catch (error) {\n      this.logger.error(\n        'CallService',\n        `Error creating sound '${name}':`,\n        error\n      );\n    }\n  }\n\n  private initializeSubscriptions(): void {\n    this.logger.debug('CallService', 'Initializing subscriptions...');\n    this.subscribeToIncomingCalls();\n    this.subscribeToCallStatusChanges();\n    this.subscribeToCallSignals();\n  }\n\n  private subscribeToIncomingCalls(): void {\n    this.apollo\n      .subscribe<{ incomingCall: IncomingCall }>({\n        query: INCOMING_CALL_SUBSCRIPTION,\n        errorPolicy: 'all',\n      })\n      .subscribe({\n        next: ({ data, errors }) => {\n          if (data?.incomingCall) {\n            this.handleIncomingCall(data.incomingCall);\n          }\n          if (errors) {\n            this.logger.error(\n              'CallService',\n              'Incoming call subscription errors:',\n              errors\n            );\n          }\n        },\n        error: (error) => {\n          this.logger.error(\n            'CallService',\n            'Error in incoming call subscription:',\n            error\n          );\n          setTimeout(() => this.subscribeToIncomingCalls(), 5000);\n        },\n      });\n  }\n\n  private subscribeToCallStatusChanges(): void {\n    this.apollo\n      .subscribe<{ callStatusChanged: Call }>({\n        query: CALL_STATUS_CHANGED_SUBSCRIPTION,\n        errorPolicy: 'all',\n      })\n      .subscribe({\n        next: ({ data, errors }) => {\n          if (data?.callStatusChanged) {\n            this.handleCallStatusChange(data.callStatusChanged);\n          }\n          if (errors) {\n            this.logger.error(\n              'CallService',\n              'Call status subscription errors:',\n              errors\n            );\n          }\n        },\n        error: (error) => {\n          this.logger.error(\n            'CallService',\n            'Error in call status subscription:',\n            error\n          );\n          setTimeout(() => this.subscribeToCallStatusChanges(), 5000);\n        },\n      });\n  }\n\n  private subscribeToCallSignals(): void {\n    this.apollo\n      .subscribe<{ callSignal: CallSignal }>({\n        query: CALL_SIGNAL_SUBSCRIPTION,\n        errorPolicy: 'all',\n      })\n      .subscribe({\n        next: ({ data, errors }) => {\n          if (data?.callSignal) {\n            this.handleCallSignal(data.callSignal);\n          }\n          if (errors) {\n            this.logger.error(\n              'CallService',\n              'Call signal subscription errors:',\n              errors\n            );\n          }\n        },\n        error: (error) => {\n          this.logger.error(\n            'CallService',\n            'Error in call signal subscription:',\n            error\n          );\n          setTimeout(() => this.subscribeToCallSignals(), 5000);\n        },\n      });\n  }\n\n  private initializeWebRTC(): void {\n    this.logger.debug('CallService', 'Initializing WebRTC...');\n    this.createPeerConnection();\n  }\n\n  // ===== GESTION AUDIO =====\n\n  private play(name: string, loop: boolean = false): void {\n    try {\n      const sound = this.sounds[name];\n      if (!sound || this.isPlaying[name]) return;\n\n      if ((sound as any).customPlay) {\n        (sound as any).currentSource = (sound as any).customPlay();\n        this.isPlaying[name] = true;\n      }\n    } catch (error) {\n      this.logger.error('CallService', `Error playing sound '${name}':`, error);\n    }\n  }\n\n  private stop(name: string): void {\n    try {\n      const sound = this.sounds[name];\n      if (!sound || !this.isPlaying[name]) return;\n\n      if ((sound as any).currentSource) {\n        (sound as any).currentSource.stop();\n        (sound as any).currentSource = null;\n      }\n      this.isPlaying[name] = false;\n    } catch (error) {\n      this.logger.error(\n        'CallService',\n        `Error stopping sound '${name}':`,\n        error\n      );\n    }\n  }\n\n  private stopAllSounds(): void {\n    Object.keys(this.sounds).forEach((name) => this.stop(name));\n  }\n\n  // ===== WEBRTC =====\n\n  private createPeerConnection(): void {\n    try {\n      this.peerConnection = new RTCPeerConnection(this.rtcConfig);\n      this.logger.debug('CallService', 'PeerConnection created successfully');\n\n      this.peerConnection.onicecandidate = (event) => {\n        if (event.candidate && this.currentCallId) {\n          this.sendSignal('ice-candidate', JSON.stringify(event.candidate));\n        }\n      };\n\n      this.peerConnection.ontrack = (event) => {\n        this.logger.info(\n          'CallService',\n          'Remote track received:',\n          event.track.kind\n        );\n        this.remoteStream = event.streams[0];\n        this.attachRemoteStream();\n      };\n\n      this.peerConnection.onconnectionstatechange = () => {\n        const state = this.peerConnection?.connectionState;\n        this.logger.debug('CallService', 'Connection state changed:', state);\n\n        if (state === 'connected') {\n          this.logger.info('CallService', '✅ WebRTC connection established');\n          this.setCallState('connected');\n        } else if (state === 'failed') {\n          this.logger.error('CallService', '❌ WebRTC connection failed');\n          this.handleCallEnded();\n        }\n      };\n    } catch (error) {\n      this.logger.error('CallService', 'Error creating PeerConnection:', error);\n    }\n  }\n\n  private async startOutgoingCallMedia(callType: CallType): Promise<void> {\n    try {\n      this.logger.info('CallService', '🎥 Starting outgoing call media');\n      const stream = await this.getUserMedia(callType);\n      this.addLocalStreamToPeerConnection(stream);\n      this.attachLocalStream();\n    } catch (error) {\n      this.logger.error(\n        'CallService',\n        'Error starting outgoing call media:',\n        error\n      );\n    }\n  }\n\n  private async prepareForIncomingCall(call: IncomingCall): Promise<void> {\n    try {\n      this.logger.debug('CallService', 'Preparing WebRTC for incoming call');\n      if (!this.peerConnection) {\n        this.createPeerConnection();\n      }\n      const stream = await this.getUserMedia(call.type);\n      this.addLocalStreamToPeerConnection(stream);\n    } catch (error) {\n      this.logger.error(\n        'CallService',\n        'Error preparing for incoming call:',\n        error\n      );\n    }\n  }\n\n  private async getUserMedia(callType: CallType): Promise<MediaStream> {\n    const constraints: MediaStreamConstraints = {\n      audio: true,\n      video: callType === CallType.VIDEO,\n    };\n\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia(constraints);\n      this.localStream = stream;\n      return stream;\n    } catch (error) {\n      this.logger.error('CallService', 'Error getting user media:', error);\n      throw error;\n    }\n  }\n\n  private addLocalStreamToPeerConnection(stream: MediaStream): void {\n    if (!this.peerConnection) return;\n\n    stream.getTracks().forEach((track) => {\n      this.peerConnection!.addTrack(track, stream);\n    });\n  }\n\n  private attachLocalStream(): void {\n    if (this.localVideoElement && this.localStream) {\n      this.localVideoElement.srcObject = this.localStream;\n    }\n  }\n\n  private attachRemoteStream(): void {\n    if (this.remoteVideoElement && this.remoteStream) {\n      this.remoteVideoElement.srcObject = this.remoteStream;\n    }\n  }\n\n  private sendSignal(signalType: string, signalData: string): void {\n    if (!this.currentCallId) return;\n\n    this.apollo\n      .mutate({\n        mutation: SEND_CALL_SIGNAL_MUTATION,\n        variables: {\n          callId: this.currentCallId,\n          signalType,\n          signalData,\n        },\n      })\n      .subscribe({\n        next: () =>\n          this.logger.debug('CallService', 'Signal sent:', signalType),\n        error: (error) =>\n          this.logger.error('CallService', 'Error sending signal:', error),\n      });\n  }\n\n  // ===== NETTOYAGE =====\n\n  private cleanupWebRTC(): void {\n    this.logger.debug('CallService', 'Cleaning up WebRTC resources');\n\n    if (this.localStream) {\n      this.localStream.getTracks().forEach((track) => track.stop());\n      this.localStream = null;\n    }\n\n    if (this.remoteStream) {\n      this.remoteStream = null;\n    }\n\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n\n    if (this.localVideoElement) {\n      this.localVideoElement.srcObject = null;\n    }\n\n    if (this.remoteVideoElement) {\n      this.remoteVideoElement.srcObject = null;\n    }\n\n    // Recréer une nouvelle PeerConnection pour les futurs appels\n    this.createPeerConnection();\n  }\n\n  private cleanup(): void {\n    this.stopAllSounds();\n    this.cleanupWebRTC();\n    this.activeCall.complete();\n    this.incomingCall.complete();\n    this.callSignals.complete();\n  }\n\n  // ===== MÉTHODES PUBLIQUES UTILITAIRES =====\n\n  /**\n   * Attache les éléments vidéo pour l'affichage\n   */\n  attachVideoElements(\n    localVideo: HTMLVideoElement,\n    remoteVideo: HTMLVideoElement\n  ): void {\n    this.localVideoElement = localVideo;\n    this.remoteVideoElement = remoteVideo;\n\n    if (this.localStream) {\n      this.attachLocalStream();\n    }\n    if (this.remoteStream) {\n      this.attachRemoteStream();\n    }\n  }\n\n  /**\n   * Active/désactive l'audio\n   */\n  toggleAudio(): boolean {\n    this.isAudioEnabled = !this.isAudioEnabled;\n    if (this.localStream) {\n      this.localStream.getAudioTracks().forEach((track) => {\n        track.enabled = this.isAudioEnabled;\n      });\n    }\n    return this.isAudioEnabled;\n  }\n\n  /**\n   * Active/désactive la vidéo\n   */\n  toggleVideo(): boolean {\n    this.isVideoEnabled = !this.isVideoEnabled;\n    if (this.localStream) {\n      this.localStream.getVideoTracks().forEach((track) => {\n        track.enabled = this.isVideoEnabled;\n      });\n    }\n    return this.isVideoEnabled;\n  }\n\n  /**\n   * Méthode de compatibilité pour setVideoElements\n   */\n  setVideoElements(\n    localVideo: HTMLVideoElement,\n    remoteVideo: HTMLVideoElement\n  ): void {\n    this.attachVideoElements(localVideo, remoteVideo);\n  }\n\n  /**\n   * Obtient l'état audio actuel\n   */\n  get audioEnabled(): boolean {\n    return this.isAudioEnabled;\n  }\n\n  /**\n   * Obtient l'état vidéo actuel\n   */\n  get videoEnabled(): boolean {\n    return this.isVideoEnabled;\n  }\n\n  /**\n   * Obtient le stream local\n   */\n  get localMediaStream(): MediaStream | null {\n    return this.localStream;\n  }\n\n  /**\n   * Obtient le stream distant\n   */\n  get remoteMediaStream(): MediaStream | null {\n    return this.remoteStream;\n  }\n\n  /**\n   * Active les sons (méthode de compatibilité)\n   */\n  enableSounds(): void {\n    this.logger.debug(\n      'CallService',\n      'Sounds are always enabled in unified service'\n    );\n  }\n\n  /**\n   * Désactive les sons (méthode de compatibilité)\n   */\n  disableSounds(): void {\n    this.logger.debug('CallService', 'Disabling sounds');\n    this.stopAllSounds();\n  }\n}\n", "import {\r\n  Compo<PERSON>,\r\n  On<PERSON>nit,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  AfterViewInit,\r\n  ViewChild,\r\n  ElementRef,\r\n  ChangeDetectorRef,\r\n  NgZone,\r\n} from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { Subscription, BehaviorSubject, combineLatest, of } from 'rxjs';\r\nimport {\r\n  debounceTime,\r\n  distinctUntilChanged,\r\n  switchMap,\r\n  catchError,\r\n  tap,\r\n  filter,\r\n  map,\r\n} from 'rxjs/operators';\r\nimport { MessageService } from '../../../../services/message.service';\r\nimport { AuthService } from '../../../../services/auth.service';\r\nimport { ToastService } from '../../../../services/toast.service';\r\nimport {\r\n  Message,\r\n  Conversation,\r\n  User,\r\n  MessageType,\r\n  CallType,\r\n  Attachment,\r\n} from '../../../../models/message.model';\r\n\r\n@Component({\r\n  selector: 'app-message-chat',\r\n  templateUrl: './message-chat.component.html',\r\n  styleUrls: ['./message-chat.component.css'],\r\n})\r\nexport class MessageChatComponent implements OnInit, AfterViewInit, OnDestroy {\r\n  @ViewChild('messagesContainer') messagesContainer!: ElementRef;\r\n  @ViewChild('messageInput') messageInput!: ElementRef;\r\n  @ViewChild('fileInput') fileInput!: ElementRef;\r\n  @ViewChild('voiceRecorder') voiceRecorder!: ElementRef;\r\n\r\n  // État du composant\r\n  currentUser: User | null = null;\r\n  selectedConversation: Conversation | null = null;\r\n  messages: Message[] = [];\r\n  isLoading = false;\r\n  isTyping = false;\r\n  typingUsers: User[] = [];\r\n\r\n  // Pagination\r\n  currentPage = 1;\r\n  hasMoreMessages = true;\r\n  loadingMoreMessages = false;\r\n\r\n  // Formulaire de message\r\n  messageContent = '';\r\n  selectedFiles: File[] = [];\r\n  isRecording = false;\r\n  recordingDuration = 0;\r\n\r\n  // États UI\r\n  showEmojiPicker = false;\r\n  showAttachmentMenu = false;\r\n  showQuickReactionsFor: string | null = null;\r\n  showVoiceRecorder = false;\r\n  showLocationPicker = false;\r\n  replyingTo: Message | null = null;\r\n  editingMessage: Message | null = null;\r\n\r\n  // Emojis pour les réactions rapides\r\n  quickEmojis = ['👍', '❤️', '😂', '😮', '😢', '😡'];\r\n\r\n  // Recherche\r\n  searchQuery = '';\r\n  searchResults: Message[] = [];\r\n  showSearchResults = false;\r\n\r\n  // Subscriptions\r\n  private subscriptions: Subscription[] = [];\r\n  private typingTimeout: any;\r\n  private recordingInterval: any;\r\n\r\n  // Observables\r\n  private conversationId$ = new BehaviorSubject<string | null>(null);\r\n\r\n  // Constantes\r\n  readonly MessageType = MessageType;\r\n  readonly CallType = CallType;\r\n\r\n  constructor(\r\n    private messageService: MessageService,\r\n    private authService: AuthService,\r\n    private toastService: ToastService,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private cdr: ChangeDetectorRef,\r\n    private ngZone: NgZone\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.initializeComponent();\r\n    this.setupSubscriptions();\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.scrollToBottom();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.cleanup();\r\n  }\r\n\r\n  // ============================================================================\r\n  // MÉTHODES D'INITIALISATION\r\n  // ============================================================================\r\n\r\n  private initializeComponent(): void {\r\n    // Récupérer l'utilisateur actuel\r\n    this.currentUser = this.authService.getCurrentUser();\r\n\r\n    if (!this.currentUser) {\r\n      this.router.navigate(['/login']);\r\n      return;\r\n    }\r\n\r\n    // Écouter les changements de route pour la conversation\r\n    this.route.params.subscribe((params) => {\r\n      const conversationId = params['conversationId'];\r\n      if (conversationId) {\r\n        this.conversationId$.next(conversationId);\r\n      }\r\n    });\r\n  }\r\n\r\n  private setupSubscriptions(): void {\r\n    // Subscription pour charger la conversation\r\n    const conversationSub = this.conversationId$\r\n      .pipe(\r\n        filter((id) => !!id),\r\n        distinctUntilChanged(),\r\n        tap(() => {\r\n          this.isLoading = true;\r\n          this.messages = [];\r\n          this.currentPage = 1;\r\n          this.hasMoreMessages = true;\r\n        }),\r\n        switchMap((conversationId) =>\r\n          this.messageService.getConversation(conversationId!, 25, 1)\r\n        ),\r\n        catchError((error) => {\r\n          console.error('Erreur lors du chargement de la conversation:', error);\r\n          this.toastService.showError(\r\n            'Erreur lors du chargement de la conversation'\r\n          );\r\n          return of(null);\r\n        })\r\n      )\r\n      .subscribe((conversation) => {\r\n        this.isLoading = false;\r\n        if (conversation) {\r\n          this.selectedConversation = conversation;\r\n          this.messages = conversation.messages || [];\r\n          this.scrollToBottom();\r\n          this.markMessagesAsRead();\r\n        }\r\n        this.cdr.detectChanges();\r\n      });\r\n\r\n    // Subscription pour les nouveaux messages\r\n    const messagesSub = this.messageService\r\n      .subscribeToMessages()\r\n      .subscribe((message) => {\r\n        if (\r\n          message &&\r\n          this.selectedConversation &&\r\n          message.conversationId === this.selectedConversation.id\r\n        ) {\r\n          this.addNewMessage(message);\r\n          this.scrollToBottom();\r\n          this.markMessageAsRead(message);\r\n        }\r\n      });\r\n\r\n    // Subscription pour les indicateurs de frappe\r\n    const typingSub = this.messageService\r\n      .subscribeToTypingIndicators()\r\n      .subscribe((event) => {\r\n        if (\r\n          event &&\r\n          this.selectedConversation &&\r\n          event.conversationId === this.selectedConversation.id\r\n        ) {\r\n          this.handleTypingIndicator(event);\r\n        }\r\n      });\r\n\r\n    this.subscriptions.push(conversationSub, messagesSub, typingSub);\r\n  }\r\n\r\n  private cleanup(): void {\r\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\r\n    if (this.typingTimeout) {\r\n      clearTimeout(this.typingTimeout);\r\n    }\r\n    if (this.recordingInterval) {\r\n      clearInterval(this.recordingInterval);\r\n    }\r\n    this.stopTyping();\r\n  }\r\n\r\n  // ============================================================================\r\n  // MÉTHODES DE GESTION DES MESSAGES\r\n  // ============================================================================\r\n\r\n  sendMessage(): void {\r\n    if (!this.canSendMessage()) {\r\n      return;\r\n    }\r\n\r\n    const content = this.messageContent.trim();\r\n    const files = this.selectedFiles;\r\n\r\n    // Réinitialiser le formulaire\r\n    this.messageContent = '';\r\n    this.selectedFiles = [];\r\n    this.replyingTo = null;\r\n    this.stopTyping();\r\n\r\n    if (this.editingMessage) {\r\n      this.updateMessage(content);\r\n      return;\r\n    }\r\n\r\n    // Envoyer le message\r\n    if (content || files.length > 0) {\r\n      this.sendNewMessage(content, files);\r\n    }\r\n  }\r\n\r\n  canSendMessage(): boolean {\r\n    const hasContent = this.messageContent.trim().length > 0;\r\n    const hasFiles = this.selectedFiles.length > 0;\r\n    const hasConversation = !!this.selectedConversation;\r\n\r\n    return hasConversation && (hasContent || hasFiles);\r\n  }\r\n\r\n  private sendNewMessage(content: string, files: File[]): void {\r\n    if (!this.selectedConversation || !this.currentUser) return;\r\n\r\n    const recipientId = this.getRecipientId();\r\n    if (!recipientId) return;\r\n\r\n    // Créer un message temporaire pour l'affichage immédiat\r\n    const tempMessage: Message = {\r\n      id: `temp-${Date.now()}`,\r\n      content,\r\n      type:\r\n        files.length > 0 ? this.getFileMessageType(files[0]) : MessageType.TEXT,\r\n      timestamp: new Date(),\r\n      sender: this.currentUser,\r\n      isPending: true,\r\n      conversationId: this.selectedConversation.id,\r\n    };\r\n\r\n    this.addNewMessage(tempMessage);\r\n    this.scrollToBottom();\r\n\r\n    // Envoyer le message via le service\r\n    const sendObservable =\r\n      files.length > 0\r\n        ? this.messageService.sendMessage(recipientId, content, files[0])\r\n        : this.messageService.sendMessage(recipientId, content);\r\n\r\n    sendObservable.subscribe({\r\n      next: (sentMessage) => {\r\n        this.replaceTemporaryMessage(tempMessage.id!, sentMessage);\r\n        this.toastService.showSuccess('Message envoyé');\r\n      },\r\n      error: (error) => {\r\n        console.error(\"Erreur lors de l'envoi du message:\", error);\r\n        this.markMessageAsError(tempMessage.id!);\r\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\r\n      },\r\n    });\r\n  }\r\n\r\n  private updateMessage(newContent: string): void {\r\n    if (!this.editingMessage) return;\r\n\r\n    this.messageService\r\n      .editMessage(this.editingMessage.id!, newContent)\r\n      .subscribe({\r\n        next: (updatedMessage) => {\r\n          this.updateMessageInList(updatedMessage);\r\n          this.editingMessage = null;\r\n          this.toastService.showSuccess('Message modifié');\r\n        },\r\n        error: (error) => {\r\n          console.error('Erreur lors de la modification du message:', error);\r\n          this.toastService.showError(\r\n            'Erreur lors de la modification du message'\r\n          );\r\n        },\r\n      });\r\n  }\r\n\r\n  deleteMessage(message: Message): void {\r\n    if (!message.id || !this.canDeleteMessage(message)) return;\r\n\r\n    if (confirm('Êtes-vous sûr de vouloir supprimer ce message ?')) {\r\n      this.messageService.deleteMessage(message.id).subscribe({\r\n        next: () => {\r\n          this.removeMessageFromList(message.id!);\r\n          this.toastService.showSuccess('Message supprimé');\r\n        },\r\n        error: (error) => {\r\n          console.error('Erreur lors de la suppression du message:', error);\r\n          this.toastService.showError(\r\n            'Erreur lors de la suppression du message'\r\n          );\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  // ============================================================================\r\n  // MÉTHODES DE GESTION DES FICHIERS ET MÉDIAS\r\n  // ============================================================================\r\n\r\n  onFileSelected(event: any): void {\r\n    const files = event.target.files;\r\n    if (files && files.length > 0) {\r\n      this.selectedFiles = Array.from(files);\r\n      this.showAttachmentMenu = false;\r\n\r\n      // Auto-envoyer si c'est juste un fichier sans texte\r\n      if (this.messageContent.trim() === '') {\r\n        this.sendMessage();\r\n      }\r\n    }\r\n  }\r\n\r\n  removeSelectedFile(index: number): void {\r\n    this.selectedFiles.splice(index, 1);\r\n  }\r\n\r\n  openFileSelector(): void {\r\n    this.fileInput.nativeElement.click();\r\n  }\r\n\r\n  // ============================================================================\r\n  // MÉTHODES D'ENREGISTREMENT VOCAL\r\n  // ============================================================================\r\n\r\n  async startVoiceRecording(): Promise<void> {\r\n    try {\r\n      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\r\n      this.isRecording = true;\r\n      this.recordingDuration = 0;\r\n\r\n      // Démarrer le compteur de durée\r\n      this.recordingInterval = setInterval(() => {\r\n        this.recordingDuration++;\r\n      }, 1000);\r\n\r\n      // Ici, vous pouvez implémenter l'enregistrement audio\r\n      // avec MediaRecorder API\r\n    } catch (error) {\r\n      console.error(\"Erreur lors de l'accès au microphone:\", error);\r\n      this.toastService.showError(\"Impossible d'accéder au microphone\");\r\n    }\r\n  }\r\n\r\n  stopVoiceRecording(): void {\r\n    this.isRecording = false;\r\n    if (this.recordingInterval) {\r\n      clearInterval(this.recordingInterval);\r\n    }\r\n\r\n    // Ici, vous pouvez traiter l'enregistrement et l'envoyer\r\n    // comme message vocal\r\n  }\r\n\r\n  cancelVoiceRecording(): void {\r\n    this.isRecording = false;\r\n    this.recordingDuration = 0;\r\n    if (this.recordingInterval) {\r\n      clearInterval(this.recordingInterval);\r\n    }\r\n  }\r\n\r\n  // ============================================================================\r\n  // MÉTHODES D'APPELS AUDIO/VIDÉO\r\n  // ============================================================================\r\n\r\n  startAudioCall(): void {\r\n    if (!this.selectedConversation) return;\r\n\r\n    const recipientId = this.getRecipientId();\r\n    if (!recipientId) return;\r\n\r\n    this.messageService.initiateCall(recipientId, CallType.AUDIO).subscribe({\r\n      next: (call) => {\r\n        this.toastService.showSuccess('Appel audio initié');\r\n        // Rediriger vers l'interface d'appel\r\n      },\r\n      error: (error) => {\r\n        console.error(\"Erreur lors de l'initiation de l'appel:\", error);\r\n        this.toastService.showError(\"Erreur lors de l'appel\");\r\n      },\r\n    });\r\n  }\r\n\r\n  startVideoCall(): void {\r\n    if (!this.selectedConversation) return;\r\n\r\n    const recipientId = this.getRecipientId();\r\n    if (!recipientId) return;\r\n\r\n    this.messageService.initiateCall(recipientId, CallType.VIDEO).subscribe({\r\n      next: (call) => {\r\n        this.toastService.showSuccess('Appel vidéo initié');\r\n        // Rediriger vers l'interface d'appel\r\n      },\r\n      error: (error) => {\r\n        console.error(\"Erreur lors de l'initiation de l'appel vidéo:\", error);\r\n        this.toastService.showError(\"Erreur lors de l'appel vidéo\");\r\n      },\r\n    });\r\n  }\r\n\r\n  // ============================================================================\r\n  // MÉTHODES DE GESTION DE LA FRAPPE\r\n  // ============================================================================\r\n\r\n  onTyping(): void {\r\n    if (!this.selectedConversation || this.isTyping) return;\r\n\r\n    this.isTyping = true;\r\n    this.messageService.startTyping(this.selectedConversation.id!).subscribe();\r\n\r\n    // Arrêter la frappe après 3 secondes d'inactivité\r\n    if (this.typingTimeout) {\r\n      clearTimeout(this.typingTimeout);\r\n    }\r\n\r\n    this.typingTimeout = setTimeout(() => {\r\n      this.stopTyping();\r\n    }, 3000);\r\n  }\r\n\r\n  stopTyping(): void {\r\n    if (!this.isTyping || !this.selectedConversation) return;\r\n\r\n    this.isTyping = false;\r\n    this.messageService.stopTyping(this.selectedConversation.id!).subscribe();\r\n\r\n    if (this.typingTimeout) {\r\n      clearTimeout(this.typingTimeout);\r\n    }\r\n  }\r\n\r\n  // ============================================================================\r\n  // MÉTHODES UTILITAIRES\r\n  // ============================================================================\r\n\r\n  private getRecipientId(): string | null {\r\n    if (!this.selectedConversation || !this.currentUser) return null;\r\n\r\n    const participants = this.selectedConversation.participants || [];\r\n    const currentUserId = this.currentUser.id || this.currentUser._id;\r\n\r\n    const recipient = participants.find(\r\n      (p) => (p.id || p._id) !== currentUserId\r\n    );\r\n\r\n    return recipient ? recipient.id || recipient._id! : null;\r\n  }\r\n\r\n  private getFileMessageType(file: File): MessageType {\r\n    const type = file.type.split('/')[0];\r\n    switch (type) {\r\n      case 'image':\r\n        return MessageType.IMAGE;\r\n      case 'video':\r\n        return MessageType.VIDEO;\r\n      case 'audio':\r\n        return MessageType.AUDIO;\r\n      default:\r\n        return MessageType.FILE;\r\n    }\r\n  }\r\n\r\n  private addNewMessage(message: Message): void {\r\n    this.messages.push(message);\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  private replaceTemporaryMessage(tempId: string, realMessage: Message): void {\r\n    const index = this.messages.findIndex((m) => m.id === tempId);\r\n    if (index !== -1) {\r\n      this.messages[index] = realMessage;\r\n      this.cdr.detectChanges();\r\n    }\r\n  }\r\n\r\n  private markMessageAsError(messageId: string): void {\r\n    const message = this.messages.find((m) => m.id === messageId);\r\n    if (message) {\r\n      message.isPending = false;\r\n      message.isError = true;\r\n      this.cdr.detectChanges();\r\n    }\r\n  }\r\n\r\n  private updateMessageInList(updatedMessage: Message): void {\r\n    const index = this.messages.findIndex((m) => m.id === updatedMessage.id);\r\n    if (index !== -1) {\r\n      this.messages[index] = updatedMessage;\r\n      this.cdr.detectChanges();\r\n    }\r\n  }\r\n\r\n  private removeMessageFromList(messageId: string): void {\r\n    this.messages = this.messages.filter((m) => m.id !== messageId);\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  private canDeleteMessage(message: Message): boolean {\r\n    if (!this.currentUser || !message.sender) return false;\r\n\r\n    const currentUserId = this.currentUser.id || this.currentUser._id;\r\n    const senderId = message.sender.id || message.sender._id;\r\n\r\n    return currentUserId === senderId;\r\n  }\r\n\r\n  private handleTypingIndicator(event: any): void {\r\n    if (!this.currentUser) return;\r\n\r\n    const currentUserId = this.currentUser.id || this.currentUser._id;\r\n\r\n    if (event.userId === currentUserId) return; // Ignorer ses propres indicateurs\r\n\r\n    if (event.isTyping) {\r\n      // Ajouter l'utilisateur à la liste des utilisateurs en train de taper\r\n      const user = this.selectedConversation?.participants?.find(\r\n        (p) => (p.id || p._id) === event.userId\r\n      );\r\n      if (\r\n        user &&\r\n        !this.typingUsers.find((u) => (u.id || u._id) === event.userId)\r\n      ) {\r\n        this.typingUsers.push(user);\r\n      }\r\n    } else {\r\n      // Retirer l'utilisateur de la liste\r\n      this.typingUsers = this.typingUsers.filter(\r\n        (u) => (u.id || u._id) !== event.userId\r\n      );\r\n    }\r\n\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  private markMessagesAsRead(): void {\r\n    if (!this.messages.length || !this.currentUser) return;\r\n\r\n    const unreadMessages = this.messages.filter(\r\n      (m) =>\r\n        !m.isRead &&\r\n        m.sender &&\r\n        (m.sender.id || m.sender._id) !==\r\n          (this.currentUser!.id || this.currentUser!._id)\r\n    );\r\n\r\n    unreadMessages.forEach((message) => {\r\n      if (message.id) {\r\n        this.markMessageAsRead(message);\r\n      }\r\n    });\r\n  }\r\n\r\n  private markMessageAsRead(message: Message): void {\r\n    if (!message.id || message.isRead) return;\r\n\r\n    this.messageService.markMessageAsRead(message.id).subscribe({\r\n      next: (updatedMessage) => {\r\n        this.updateMessageInList(updatedMessage);\r\n      },\r\n      error: (error) => {\r\n        console.error('Erreur lors du marquage comme lu:', error);\r\n      },\r\n    });\r\n  }\r\n\r\n  private scrollToBottom(): void {\r\n    this.ngZone.runOutsideAngular(() => {\r\n      setTimeout(() => {\r\n        if (this.messagesContainer) {\r\n          const element = this.messagesContainer.nativeElement;\r\n          element.scrollTop = element.scrollHeight;\r\n        }\r\n      }, 100);\r\n    });\r\n  }\r\n\r\n  // ============================================================================\r\n  // MÉTHODES PUBLIQUES POUR LE TEMPLATE\r\n  // ============================================================================\r\n\r\n  formatMessageTime(timestamp: Date | string): string {\r\n    const date = new Date(timestamp);\r\n    const now = new Date();\r\n    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\r\n\r\n    if (diffInHours < 24) {\r\n      return date.toLocaleTimeString('fr-FR', {\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n      });\r\n    } else {\r\n      return date.toLocaleDateString('fr-FR', {\r\n        day: '2-digit',\r\n        month: '2-digit',\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Obtient le statut d'un message pour l'affichage\r\n   */\r\n  getMessageStatus(message: Message): string {\r\n    if (message.isPending) return 'SENDING';\r\n    if (message.isError) return 'FAILED';\r\n    if (message.status) return message.status;\r\n    if (message.isRead) return 'READ';\r\n    if (message.isDelivered) return 'DELIVERED';\r\n    return 'SENT';\r\n  }\r\n\r\n  isMyMessage(message: Message): boolean {\r\n    if (!this.currentUser || !message.sender) return false;\r\n\r\n    const currentUserId = this.currentUser.id || this.currentUser._id;\r\n    const senderId = message.sender.id || message.sender._id;\r\n\r\n    return currentUserId === senderId;\r\n  }\r\n\r\n  getTypingText(): string {\r\n    if (this.typingUsers.length === 0) return '';\r\n\r\n    if (this.typingUsers.length === 1) {\r\n      return `${this.typingUsers[0].username} est en train d'écrire...`;\r\n    } else {\r\n      return `${this.typingUsers.length} personnes sont en train d'écrire...`;\r\n    }\r\n  }\r\n\r\n  onKeyPress(event: KeyboardEvent): void {\r\n    if (event.key === 'Enter' && !event.shiftKey) {\r\n      event.preventDefault();\r\n      this.sendMessage();\r\n    } else {\r\n      this.onTyping();\r\n    }\r\n  }\r\n\r\n  toggleEmojiPicker(): void {\r\n    this.showEmojiPicker = !this.showEmojiPicker;\r\n  }\r\n\r\n  toggleAttachmentMenu(): void {\r\n    this.showAttachmentMenu = !this.showAttachmentMenu;\r\n  }\r\n\r\n  startEditingMessage(message: Message): void {\r\n    this.editingMessage = message;\r\n    this.messageContent = message.content || '';\r\n    this.messageInput.nativeElement.focus();\r\n  }\r\n\r\n  cancelEditing(): void {\r\n    this.editingMessage = null;\r\n    this.messageContent = '';\r\n  }\r\n\r\n  setReplyTo(message: Message): void {\r\n    this.replyingTo = message;\r\n    this.messageInput.nativeElement.focus();\r\n  }\r\n\r\n  cancelReply(): void {\r\n    this.replyingTo = null;\r\n  }\r\n\r\n  // ============================================================================\r\n  // MÉTHODES POUR LE TEMPLATE (MANQUANTES)\r\n  // ============================================================================\r\n\r\n  getRecipientName(): string {\r\n    if (!this.selectedConversation || !this.currentUser) return '';\r\n\r\n    const participants = this.selectedConversation.participants || [];\r\n    const currentUserId = this.currentUser.id || this.currentUser._id;\r\n\r\n    const recipient = participants.find(\r\n      (p) => (p.id || p._id) !== currentUserId\r\n    );\r\n\r\n    return recipient?.username || 'Utilisateur inconnu';\r\n  }\r\n\r\n  getRecipientAvatar(): string {\r\n    if (!this.selectedConversation || !this.currentUser)\r\n      return '/assets/images/default-avatar.png';\r\n\r\n    const participants = this.selectedConversation.participants || [];\r\n    const currentUserId = this.currentUser.id || this.currentUser._id;\r\n\r\n    const recipient = participants.find(\r\n      (p) => (p.id || p._id) !== currentUserId\r\n    );\r\n\r\n    return recipient?.image || '/assets/images/default-avatar.png';\r\n  }\r\n\r\n  isRecipientOnline(): boolean {\r\n    if (!this.selectedConversation || !this.currentUser) return false;\r\n\r\n    const participants = this.selectedConversation.participants || [];\r\n    const currentUserId = this.currentUser.id || this.currentUser._id;\r\n\r\n    const recipient = participants.find(\r\n      (p) => (p.id || p._id) !== currentUserId\r\n    );\r\n\r\n    return recipient?.isOnline || false;\r\n  }\r\n\r\n  trackByMessageId(index: number, message: Message): string {\r\n    return message.id || message._id || index.toString();\r\n  }\r\n\r\n  openImageViewer(attachment: Attachment | undefined): void {\r\n    if (!attachment?.url) return;\r\n\r\n    // Ouvrir l'image dans une nouvelle fenêtre ou modal\r\n    window.open(attachment.url, '_blank');\r\n  }\r\n\r\n  formatFileSize(size: number | undefined): string {\r\n    if (!size) return '0 B';\r\n\r\n    const units = ['B', 'KB', 'MB', 'GB'];\r\n    let unitIndex = 0;\r\n    let fileSize = size;\r\n\r\n    while (fileSize >= 1024 && unitIndex < units.length - 1) {\r\n      fileSize /= 1024;\r\n      unitIndex++;\r\n    }\r\n\r\n    return `${fileSize.toFixed(1)} ${units[unitIndex]}`;\r\n  }\r\n\r\n  downloadFile(attachment: Attachment | undefined): void {\r\n    if (!attachment?.url) return;\r\n\r\n    const link = document.createElement('a');\r\n    link.href = attachment.url;\r\n    link.download = attachment.name || 'file';\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n  }\r\n\r\n  playVoiceMessage(message: Message): void {\r\n    if (!message.attachments?.[0]?.url) return;\r\n\r\n    this.messageService.playAudio(message.attachments[0].url).catch((error) => {\r\n      console.error('Erreur lors de la lecture du message vocal:', error);\r\n      this.toastService.showError('Erreur lors de la lecture du message vocal');\r\n    });\r\n  }\r\n\r\n  formatDuration(duration: number | undefined): string {\r\n    if (!duration) return '0:00';\r\n\r\n    const minutes = Math.floor(duration / 60);\r\n    const seconds = duration % 60;\r\n\r\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\r\n  }\r\n\r\n  showMessageMenu(message: Message): void {\r\n    // Ici, vous pouvez implémenter un menu contextuel\r\n    // Pour l'instant, on affiche juste les options disponibles\r\n    const actions = [];\r\n\r\n    if (this.canDeleteMessage(message)) {\r\n      actions.push('Supprimer');\r\n    }\r\n\r\n    if (this.isMyMessage(message)) {\r\n      actions.push('Modifier');\r\n    }\r\n\r\n    actions.push('Répondre', 'Transférer', 'Réagir');\r\n\r\n    // Vous pouvez implémenter un vrai menu contextuel ici\r\n    console.log('Actions disponibles pour ce message:', actions);\r\n  }\r\n\r\n  /**\r\n   * Réessaie d'envoyer un message qui a échoué\r\n   */\r\n  retryMessage(message: Message): void {\r\n    if (!message.isError || !this.selectedConversation) return;\r\n\r\n    const recipientId = this.getRecipientId();\r\n    if (!recipientId) return;\r\n\r\n    // Marquer le message comme en cours d'envoi\r\n    message.isError = false;\r\n    message.isPending = true;\r\n\r\n    // Réessayer l'envoi\r\n    const sendObservable = message.attachments?.length\r\n      ? this.messageService.sendMessage(\r\n          recipientId,\r\n          message.content || '',\r\n          undefined\r\n        )\r\n      : this.messageService.sendMessage(recipientId, message.content || '');\r\n\r\n    sendObservable.subscribe({\r\n      next: (sentMessage) => {\r\n        // Remplacer le message temporaire par le message envoyé\r\n        this.replaceTemporaryMessage(message.id!, sentMessage);\r\n        this.toastService.showSuccess('Message renvoyé avec succès');\r\n      },\r\n      error: (error) => {\r\n        console.error('Erreur lors du renvoi du message:', error);\r\n        message.isPending = false;\r\n        message.isError = true;\r\n        this.toastService.showError('Échec du renvoi du message');\r\n      },\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Affiche/masque les réactions rapides pour un message\r\n   */\r\n  toggleQuickReactions(message: Message): void {\r\n    const messageId = message.id || message._id;\r\n    if (this.showQuickReactionsFor === messageId) {\r\n      this.showQuickReactionsFor = null;\r\n    } else {\r\n      this.showQuickReactionsFor = messageId || null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Ajoute une réaction à un message\r\n   */\r\n  reactToMessage(message: Message, emoji: string): void {\r\n    if (!this.currentUser || !message.id) return;\r\n\r\n    this.messageService.reactToMessage(message.id, emoji).subscribe({\r\n      next: (updatedMessage) => {\r\n        // Mettre à jour le message dans la liste\r\n        const index = this.messages.findIndex(\r\n          (m) => (m.id || m._id) === (message.id || message._id)\r\n        );\r\n        if (index !== -1) {\r\n          this.messages[index] = updatedMessage;\r\n        }\r\n\r\n        // Masquer les réactions rapides\r\n        this.showQuickReactionsFor = null;\r\n\r\n        this.toastService.showSuccess(`Réaction ${emoji} ajoutée`);\r\n      },\r\n      error: (error) => {\r\n        console.error(\"Erreur lors de l'ajout de la réaction:\", error);\r\n        this.toastService.showError(\"Erreur lors de l'ajout de la réaction\");\r\n      },\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Gère la completion d'un enregistrement vocal\r\n   */\r\n  onVoiceRecordingComplete(recording: any): void {\r\n    this.showVoiceRecorder = false;\r\n\r\n    if (!this.selectedConversation || !recording) return;\r\n\r\n    const recipientId = this.getRecipientId();\r\n    if (!recipientId) return;\r\n\r\n    // Créer un FormData pour envoyer le fichier audio\r\n    const formData = new FormData();\r\n    formData.append('audio', recording.blob, 'voice-message.webm');\r\n    formData.append('duration', recording.duration.toString());\r\n    if (recording.waveform) {\r\n      formData.append('waveform', JSON.stringify(recording.waveform));\r\n    }\r\n\r\n    // Envoyer le message vocal\r\n    this.messageService.sendVoiceMessage(recipientId, formData).subscribe({\r\n      next: (sentMessage: any) => {\r\n        this.messages.push(sentMessage);\r\n        this.scrollToBottom();\r\n        this.toastService.showSuccess('Message vocal envoyé');\r\n      },\r\n      error: (error: any) => {\r\n        console.error(\"Erreur lors de l'envoi du message vocal:\", error);\r\n        this.toastService.showError(\"Erreur lors de l'envoi du message vocal\");\r\n      },\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Gère la sélection d'une localisation\r\n   */\r\n  onLocationSelected(location: any): void {\r\n    this.showLocationPicker = false;\r\n\r\n    if (!this.selectedConversation || !location) return;\r\n\r\n    const recipientId = this.getRecipientId();\r\n    if (!recipientId) return;\r\n\r\n    // Créer le contenu du message de localisation\r\n    const locationMessage = {\r\n      type: 'location',\r\n      latitude: location.latitude,\r\n      longitude: location.longitude,\r\n      address: location.address,\r\n      mapUrl: location.mapUrl,\r\n    };\r\n\r\n    // Envoyer le message de localisation\r\n    this.messageService\r\n      .sendLocationMessage(recipientId, locationMessage)\r\n      .subscribe({\r\n        next: (sentMessage: any) => {\r\n          this.messages.push(sentMessage);\r\n          this.scrollToBottom();\r\n          this.toastService.showSuccess('Localisation partagée');\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Erreur lors du partage de localisation:', error);\r\n          this.toastService.showError('Erreur lors du partage de localisation');\r\n        },\r\n      });\r\n  }\r\n}\r\n", "<!-- ============================================================================\r\n     COMPOSANT MESSAGE CHAT - INTERFACE WHATSAPP PROFESSIONNELLE\r\n     ============================================================================ -->\r\n\r\n<div class=\"chat-container\" *ngIf=\"selectedConversation\">\r\n  <!-- ========================================================================\r\n       EN-TÊTE DU CHAT\r\n       ======================================================================== -->\r\n  <div class=\"chat-header\">\r\n    <div class=\"user-info\">\r\n      <img\r\n        [src]=\"\r\n          selectedConversation.isGroup\r\n            ? selectedConversation.groupPhoto\r\n            : getRecipientAvatar()\r\n        \"\r\n        [alt]=\"\r\n          selectedConversation.isGroup\r\n            ? selectedConversation.groupName\r\n            : getRecipientName()\r\n        \"\r\n        class=\"user-avatar\"\r\n        [class.online]=\"!selectedConversation.isGroup && isRecipientOnline()\"\r\n      />\r\n\r\n      <div class=\"user-details\">\r\n        <h3>\r\n          {{\r\n            selectedConversation.isGroup\r\n              ? selectedConversation.groupName\r\n              : getRecipientName()\r\n          }}\r\n        </h3>\r\n        <p\r\n          class=\"user-status\"\r\n          [class.online]=\"!selectedConversation.isGroup && isRecipientOnline()\"\r\n        >\r\n          <span *ngIf=\"selectedConversation.isGroup\">\r\n            {{ selectedConversation.participants?.length }} participants\r\n          </span>\r\n          <span *ngIf=\"!selectedConversation.isGroup && isRecipientOnline()\"\r\n            >En ligne</span\r\n          >\r\n          <span *ngIf=\"!selectedConversation.isGroup && !isRecipientOnline()\"\r\n            >Hors ligne</span\r\n          >\r\n        </p>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"chat-actions\">\r\n      <!-- Bouton d'appel audio -->\r\n      <button\r\n        class=\"action-btn\"\r\n        (click)=\"startAudioCall()\"\r\n        title=\"Appel audio\"\r\n        *ngIf=\"!selectedConversation.isGroup\"\r\n      >\r\n        <i class=\"fas fa-phone\"></i>\r\n      </button>\r\n\r\n      <!-- Bouton d'appel vidéo -->\r\n      <button\r\n        class=\"action-btn\"\r\n        (click)=\"startVideoCall()\"\r\n        title=\"Appel vidéo\"\r\n        *ngIf=\"!selectedConversation.isGroup\"\r\n      >\r\n        <i class=\"fas fa-video\"></i>\r\n      </button>\r\n\r\n      <!-- Menu d'options -->\r\n      <button class=\"action-btn\" title=\"Options\">\r\n        <i class=\"fas fa-ellipsis-v\"></i>\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- ========================================================================\r\n       ZONE DES MESSAGES\r\n       ======================================================================== -->\r\n  <div class=\"messages-container\" #messagesContainer>\r\n    <!-- Indicateur de chargement -->\r\n    <div *ngIf=\"isLoading\" class=\"flex justify-center py-4\">\r\n      <div\r\n        class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"\r\n      ></div>\r\n    </div>\r\n\r\n    <!-- Messages -->\r\n    <div\r\n      *ngFor=\"let message of messages; trackBy: trackByMessageId\"\r\n      class=\"message\"\r\n      [class.my-message]=\"isMyMessage(message)\"\r\n    >\r\n      <!-- Avatar de l'expéditeur (seulement pour les messages des autres) -->\r\n      <img\r\n        *ngIf=\"!isMyMessage(message) && message.sender\"\r\n        [src]=\"message.sender.image || '/assets/images/default-avatar.png'\"\r\n        [alt]=\"message.sender.username\"\r\n        class=\"message-avatar\"\r\n      />\r\n\r\n      <!-- Contenu du message -->\r\n      <div\r\n        class=\"message-content\"\r\n        [class.my-message]=\"isMyMessage(message)\"\r\n        [class.other-message]=\"!isMyMessage(message)\"\r\n      >\r\n        <!-- Nom de l'expéditeur (pour les groupes) -->\r\n        <div\r\n          *ngIf=\"selectedConversation.isGroup && !isMyMessage(message)\"\r\n          class=\"text-xs text-blue-400 mb-1 font-medium\"\r\n        >\r\n          {{ message.sender?.username }}\r\n        </div>\r\n\r\n        <!-- Message de réponse -->\r\n        <div *ngIf=\"message.replyTo\" class=\"reply-preview mb-2\">\r\n          <div class=\"text-xs text-gray-400\">\r\n            Réponse à {{ message.replyTo.sender?.username }}\r\n          </div>\r\n          <div class=\"text-sm text-gray-300 truncate\">\r\n            {{ message.replyTo.content }}\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Contenu selon le type de message -->\r\n        <div [ngSwitch]=\"message.type\">\r\n          <!-- Message texte -->\r\n          <div *ngSwitchCase=\"MessageType.TEXT\" class=\"message-text\">\r\n            {{ message.content }}\r\n          </div>\r\n\r\n          <!-- Message image -->\r\n          <div *ngSwitchCase=\"MessageType.IMAGE\" class=\"message-image\">\r\n            <img\r\n              [src]=\"message.attachments?.[0]?.url\"\r\n              [alt]=\"message.attachments?.[0]?.name\"\r\n              class=\"message-image\"\r\n              (click)=\"openImageViewer(message.attachments?.[0])\"\r\n            />\r\n            <div *ngIf=\"message.content\" class=\"message-text mt-2\">\r\n              {{ message.content }}\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Message fichier -->\r\n          <div *ngSwitchCase=\"MessageType.FILE\" class=\"message-file\">\r\n            <i class=\"file-icon fas fa-file\"></i>\r\n            <div class=\"file-info\">\r\n              <div class=\"file-name\">{{ message.attachments?.[0]?.name }}</div>\r\n              <div class=\"file-size\">\r\n                {{ formatFileSize(message.attachments?.[0]?.size) }}\r\n              </div>\r\n            </div>\r\n            <button\r\n              class=\"text-blue-400 hover:text-blue-300\"\r\n              (click)=\"downloadFile(message.attachments?.[0])\"\r\n            >\r\n              <i class=\"fas fa-download\"></i>\r\n            </button>\r\n          </div>\r\n\r\n          <!-- Message vocal -->\r\n          <div *ngSwitchCase=\"MessageType.VOICE_MESSAGE\" class=\"voice-message\">\r\n            <button class=\"voice-play-btn\" (click)=\"playVoiceMessage(message)\">\r\n              <i class=\"fas fa-play text-white text-xs\"></i>\r\n            </button>\r\n            <div class=\"voice-duration\">\r\n              {{ formatDuration(message.attachments?.[0]?.duration) }}\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Message vidéo -->\r\n          <div *ngSwitchCase=\"MessageType.VIDEO\" class=\"message-video\">\r\n            <video\r\n              [src]=\"message.attachments?.[0]?.url\"\r\n              controls\r\n              class=\"max-w-xs rounded-lg\"\r\n            ></video>\r\n            <div *ngIf=\"message.content\" class=\"message-text mt-2\">\r\n              {{ message.content }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Réactions -->\r\n        <div\r\n          *ngIf=\"message.reactions && message.reactions.length > 0\"\r\n          class=\"flex flex-wrap gap-1 mt-2\"\r\n        >\r\n          <span\r\n            *ngFor=\"let reaction of message.reactions\"\r\n            class=\"text-xs bg-gray-600 rounded-full px-2 py-1 cursor-pointer\"\r\n            (click)=\"reactToMessage(message, reaction.emoji)\"\r\n          >\r\n            {{ reaction.emoji }} {{ reaction.count }}\r\n          </span>\r\n        </div>\r\n\r\n        <!-- Heure et statut -->\r\n        <div class=\"flex items-center justify-between mt-1\">\r\n          <span class=\"message-time\">\r\n            {{ formatMessageTime(message.timestamp!) }}\r\n          </span>\r\n\r\n          <!-- Statut du message amélioré (seulement pour mes messages) -->\r\n          <div\r\n            *ngIf=\"isMyMessage(message)\"\r\n            class=\"message-status flex items-center space-x-1\"\r\n          >\r\n            <div\r\n              [ngSwitch]=\"getMessageStatus(message)\"\r\n              class=\"flex items-center\"\r\n            >\r\n              <!-- En cours d'envoi -->\r\n              <i\r\n                *ngSwitchCase=\"'SENDING'\"\r\n                class=\"fas fa-clock text-gray-400 animate-pulse text-xs\"\r\n                title=\"Envoi en cours...\"\r\n              ></i>\r\n\r\n              <!-- Envoyé -->\r\n              <i\r\n                *ngSwitchCase=\"'SENT'\"\r\n                class=\"fas fa-check text-gray-400 text-xs\"\r\n                title=\"Envoyé\"\r\n              ></i>\r\n\r\n              <!-- Livré -->\r\n              <i\r\n                *ngSwitchCase=\"'DELIVERED'\"\r\n                class=\"fas fa-check-double text-gray-400 text-xs\"\r\n                title=\"Livré\"\r\n              ></i>\r\n\r\n              <!-- Lu -->\r\n              <i\r\n                *ngSwitchCase=\"'read'\"\r\n                class=\"fas fa-check-double text-blue-400 text-xs\"\r\n                title=\"Lu\"\r\n              ></i>\r\n\r\n              <!-- Échec -->\r\n              <i\r\n                *ngSwitchCase=\"'FAILED'\"\r\n                class=\"fas fa-exclamation-triangle text-red-400 text-xs cursor-pointer\"\r\n                title=\"Échec d'envoi - Cliquer pour réessayer\"\r\n                (click)=\"retryMessage(message)\"\r\n              ></i>\r\n\r\n              <!-- Par défaut -->\r\n              <i\r\n                *ngSwitchDefault\r\n                class=\"fas fa-check text-gray-400 text-xs\"\r\n              ></i>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Menu contextuel du message -->\r\n        <div\r\n          class=\"message-menu absolute top-0 right-0 hidden group-hover:flex items-center space-x-1\"\r\n        >\r\n          <!-- Bouton réaction rapide -->\r\n          <button\r\n            class=\"text-gray-400 hover:text-yellow-400 p-1 transition-colors\"\r\n            (click)=\"toggleQuickReactions(message)\"\r\n            title=\"Réagir\"\r\n          >\r\n            <i class=\"fas fa-smile text-xs\"></i>\r\n          </button>\r\n\r\n          <!-- Bouton répondre -->\r\n          <button\r\n            class=\"text-gray-400 hover:text-blue-400 p-1 transition-colors\"\r\n            (click)=\"setReplyTo(message)\"\r\n            title=\"Répondre\"\r\n          >\r\n            <i class=\"fas fa-reply text-xs\"></i>\r\n          </button>\r\n\r\n          <!-- Bouton menu -->\r\n          <button\r\n            class=\"text-gray-400 hover:text-white p-1 transition-colors\"\r\n            (click)=\"showMessageMenu(message)\"\r\n            title=\"Plus d'options\"\r\n          >\r\n            <i class=\"fas fa-ellipsis-h text-xs\"></i>\r\n          </button>\r\n        </div>\r\n\r\n        <!-- Réactions rapides -->\r\n        <div\r\n          *ngIf=\"showQuickReactionsFor === message.id\"\r\n          class=\"quick-reactions absolute -top-12 right-0 bg-gray-800 rounded-lg shadow-lg p-2 flex space-x-1 border border-gray-600\"\r\n        >\r\n          <button\r\n            *ngFor=\"let emoji of quickEmojis\"\r\n            class=\"hover:bg-gray-700 rounded p-1 transition-colors text-lg\"\r\n            (click)=\"reactToMessage(message, emoji)\"\r\n            [title]=\"'Réagir avec ' + emoji\"\r\n          >\r\n            {{ emoji }}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Indicateur de frappe -->\r\n    <div *ngIf=\"typingUsers.length > 0\" class=\"typing-indicator\">\r\n      <div class=\"typing-dots\">\r\n        <div class=\"typing-dot\"></div>\r\n        <div class=\"typing-dot\"></div>\r\n        <div class=\"typing-dot\"></div>\r\n      </div>\r\n      <span>{{ getTypingText() }}</span>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- ========================================================================\r\n       ZONE DE SAISIE DES MESSAGES\r\n       ======================================================================== -->\r\n  <div class=\"message-input-container\">\r\n    <!-- Aperçu de réponse -->\r\n    <div *ngIf=\"replyingTo\" class=\"reply-preview\">\r\n      <div class=\"reply-header\">\r\n        <div>\r\n          <div class=\"text-xs text-blue-400\">\r\n            Réponse à {{ replyingTo.sender?.username }}\r\n          </div>\r\n          <div class=\"reply-text\">{{ replyingTo.content }}</div>\r\n        </div>\r\n        <button (click)=\"cancelReply()\" class=\"text-gray-400 hover:text-white\">\r\n          <i class=\"fas fa-times\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Aperçu de modification -->\r\n    <div *ngIf=\"editingMessage\" class=\"reply-preview\">\r\n      <div class=\"reply-header\">\r\n        <div>\r\n          <div class=\"text-xs text-yellow-400\">Modification du message</div>\r\n          <div class=\"reply-text\">{{ editingMessage.content }}</div>\r\n        </div>\r\n        <button\r\n          (click)=\"cancelEditing()\"\r\n          class=\"text-gray-400 hover:text-white\"\r\n        >\r\n          <i class=\"fas fa-times\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Fichiers sélectionnés -->\r\n    <div *ngIf=\"selectedFiles.length > 0\" class=\"mb-3\">\r\n      <div class=\"flex flex-wrap gap-2\">\r\n        <div\r\n          *ngFor=\"let file of selectedFiles; let i = index\"\r\n          class=\"flex items-center space-x-2 bg-gray-700 rounded-lg p-2\"\r\n        >\r\n          <i class=\"fas fa-file text-blue-400\"></i>\r\n          <span class=\"text-sm text-white truncate max-w-32\">{{\r\n            file.name\r\n          }}</span>\r\n          <button\r\n            (click)=\"removeSelectedFile(i)\"\r\n            class=\"text-red-400 hover:text-red-300\"\r\n          >\r\n            <i class=\"fas fa-times text-xs\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Indicateur d'enregistrement vocal -->\r\n    <div *ngIf=\"isRecording\" class=\"recording-indicator mb-3\">\r\n      <i class=\"fas fa-microphone text-white\"></i>\r\n      <span class=\"recording-time\">{{\r\n        formatDuration(recordingDuration)\r\n      }}</span>\r\n      <button\r\n        (click)=\"stopVoiceRecording()\"\r\n        class=\"text-white hover:text-gray-300\"\r\n      >\r\n        <i class=\"fas fa-stop\"></i>\r\n      </button>\r\n      <button\r\n        (click)=\"cancelVoiceRecording()\"\r\n        class=\"text-white hover:text-gray-300 ml-2\"\r\n      >\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </div>\r\n\r\n    <!-- Zone de saisie principale -->\r\n    <div class=\"input-wrapper\">\r\n      <!-- Boutons d'actions à gauche -->\r\n      <div class=\"input-actions\">\r\n        <!-- Bouton pièce jointe -->\r\n        <div class=\"relative\">\r\n          <button\r\n            class=\"input-btn\"\r\n            (click)=\"toggleAttachmentMenu()\"\r\n            title=\"Pièce jointe\"\r\n          >\r\n            <i class=\"fas fa-paperclip\"></i>\r\n          </button>\r\n\r\n          <!-- Menu des pièces jointes -->\r\n          <div\r\n            *ngIf=\"showAttachmentMenu\"\r\n            class=\"absolute bottom-full left-0 mb-2 bg-gray-800 rounded-lg shadow-lg p-2 space-y-1\"\r\n          >\r\n            <button\r\n              (click)=\"openFileSelector()\"\r\n              class=\"flex items-center space-x-2 w-full p-2 hover:bg-gray-700 rounded text-left\"\r\n            >\r\n              <i class=\"fas fa-file text-blue-400\"></i>\r\n              <span class=\"text-white text-sm\">Fichier</span>\r\n            </button>\r\n            <button\r\n              (click)=\"openFileSelector()\"\r\n              class=\"flex items-center space-x-2 w-full p-2 hover:bg-gray-700 rounded text-left\"\r\n            >\r\n              <i class=\"fas fa-image text-green-400\"></i>\r\n              <span class=\"text-white text-sm\">Image</span>\r\n            </button>\r\n            <button\r\n              (click)=\"showVoiceRecorder = true; showAttachmentMenu = false\"\r\n              class=\"flex items-center space-x-2 w-full p-2 hover:bg-gray-700 rounded text-left\"\r\n            >\r\n              <i class=\"fas fa-microphone text-red-400\"></i>\r\n              <span class=\"text-white text-sm\">Audio</span>\r\n            </button>\r\n            <button\r\n              (click)=\"showLocationPicker = true; showAttachmentMenu = false\"\r\n              class=\"flex items-center space-x-2 w-full p-2 hover:bg-gray-700 rounded text-left\"\r\n            >\r\n              <i class=\"fas fa-map-marker-alt text-purple-400\"></i>\r\n              <span class=\"text-white text-sm\">Localisation</span>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Bouton emoji -->\r\n        <button class=\"input-btn\" (click)=\"toggleEmojiPicker()\" title=\"Emoji\">\r\n          <i class=\"fas fa-smile\"></i>\r\n        </button>\r\n      </div>\r\n\r\n      <!-- Champ de saisie -->\r\n      <textarea\r\n        #messageInput\r\n        [(ngModel)]=\"messageContent\"\r\n        (keydown)=\"onKeyPress($event)\"\r\n        (input)=\"onTyping()\"\r\n        placeholder=\"Tapez votre message...\"\r\n        class=\"message-input\"\r\n        rows=\"1\"\r\n        [disabled]=\"isRecording\"\r\n      ></textarea>\r\n\r\n      <!-- Boutons d'actions à droite -->\r\n      <div class=\"input-actions\">\r\n        <!-- Bouton enregistrement vocal (si pas de texte) -->\r\n        <button\r\n          *ngIf=\"\r\n            !messageContent.trim() && !selectedFiles.length && !isRecording\r\n          \"\r\n          class=\"input-btn\"\r\n          (mousedown)=\"startVoiceRecording()\"\r\n          title=\"Message vocal\"\r\n        >\r\n          <i class=\"fas fa-microphone\"></i>\r\n        </button>\r\n\r\n        <!-- Bouton d'envoi (si du texte ou des fichiers) -->\r\n        <button\r\n          *ngIf=\"messageContent.trim() || selectedFiles.length\"\r\n          class=\"send-btn\"\r\n          (click)=\"sendMessage()\"\r\n          [disabled]=\"!canSendMessage()\"\r\n          title=\"Envoyer\"\r\n        >\r\n          <i class=\"fas fa-paper-plane text-white\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Input file caché -->\r\n    <input\r\n      #fileInput\r\n      type=\"file\"\r\n      multiple\r\n      class=\"hidden\"\r\n      (change)=\"onFileSelected($event)\"\r\n      accept=\"image/*,video/*,audio/*,.pdf,.doc,.docx,.txt\"\r\n    />\r\n  </div>\r\n</div>\r\n\r\n<!-- ============================================================================\r\n     MESSAGE DE BIENVENUE (AUCUNE CONVERSATION SÉLECTIONNÉE)\r\n     ============================================================================ -->\r\n<div\r\n  *ngIf=\"!selectedConversation\"\r\n  class=\"flex items-center justify-center h-full bg-gray-900 text-gray-400\"\r\n>\r\n  <div class=\"text-center max-w-md mx-auto p-8\">\r\n    <!-- Logo/Icône principale -->\r\n    <div class=\"mb-8\">\r\n      <div\r\n        class=\"w-24 h-24 mx-auto bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mb-4\"\r\n      >\r\n        <i class=\"fas fa-comments text-3xl text-white\"></i>\r\n      </div>\r\n      <h1 class=\"text-3xl font-bold text-white mb-2\">DevBridge Messages</h1>\r\n      <p class=\"text-gray-400\">Messagerie professionnelle en temps réel</p>\r\n    </div>\r\n\r\n    <!-- Fonctionnalités -->\r\n    <div class=\"space-y-4 mb-8\">\r\n      <div class=\"flex items-center space-x-3 text-left\">\r\n        <div\r\n          class=\"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center\"\r\n        >\r\n          <i class=\"fas fa-bolt text-white text-sm\"></i>\r\n        </div>\r\n        <div>\r\n          <h4 class=\"text-white font-medium\">Messages en temps réel</h4>\r\n          <p class=\"text-sm text-gray-400\">\r\n            Conversations instantanées avec notifications\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"flex items-center space-x-3 text-left\">\r\n        <div\r\n          class=\"w-10 h-10 bg-green-600 rounded-full flex items-center justify-center\"\r\n        >\r\n          <i class=\"fas fa-phone text-white text-sm\"></i>\r\n        </div>\r\n        <div>\r\n          <h4 class=\"text-white font-medium\">Appels audio/vidéo</h4>\r\n          <p class=\"text-sm text-gray-400\">Communication directe intégrée</p>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"flex items-center space-x-3 text-left\">\r\n        <div\r\n          class=\"w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center\"\r\n        >\r\n          <i class=\"fas fa-file text-white text-sm\"></i>\r\n        </div>\r\n        <div>\r\n          <h4 class=\"text-white font-medium\">Partage de fichiers</h4>\r\n          <p class=\"text-sm text-gray-400\">Images, documents et médias</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Instructions -->\r\n    <div class=\"bg-gray-800 rounded-lg p-6 border border-gray-700 mb-6\">\r\n      <h3 class=\"text-lg font-semibold text-white mb-3\">Comment commencer ?</h3>\r\n      <div class=\"space-y-2 text-sm text-gray-300\">\r\n        <p>• Sélectionnez une conversation dans la sidebar</p>\r\n        <p>• Ou cliquez sur un contact pour démarrer une discussion</p>\r\n        <p>• Utilisez la recherche pour trouver rapidement</p>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- System Status Component -->\r\n    <app-system-status></app-system-status>\r\n  </div>\r\n</div>\r\n\r\n<!-- ============================================================================\r\n     MODALS POUR LES NOUVELLES FONCTIONNALITÉS (TEMPORAIREMENT DÉSACTIVÉS)\r\n     ============================================================================ -->\r\n\r\n<!-- TODO: Réactiver après avoir créé les composants\r\n<div *ngIf=\"showVoiceRecorder\" class=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n  <div class=\"bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4\">\r\n    <app-voice-recorder\r\n      (recordingComplete)=\"onVoiceRecordingComplete($event)\"\r\n      (recordingCancelled)=\"showVoiceRecorder = false\">\r\n    </app-voice-recorder>\r\n  </div>\r\n</div>\r\n\r\n<div *ngIf=\"showLocationPicker\" class=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n  <div class=\"max-w-md w-full mx-4\">\r\n    <app-location-picker\r\n      (locationSelected)=\"onLocationSelected($event)\"\r\n      (cancelled)=\"showLocationPicker = false\">\r\n    </app-location-picker>\r\n  </div>\r\n</div>\r\n-->\r\n", "import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-messages-list',\n  templateUrl: './messages-list.component.html',\n  styleUrls: ['./messages-list.component.css'],\n})\nexport class MessagesListComponent {\n  \n\n}\n", "import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { MessageChatComponent } from './message-chat/message-chat.component';\nimport { MessagesListComponent } from './messages-list/messages-list.component';\nimport { UserListComponent } from './user-list/user-list.component';\nimport { MessageLayoutComponent } from './message-layout/message-layout.component';\n\nconst routes: Routes = [\n  {\n    path: '',\n    component: MessageLayoutComponent,\n    children: [\n      // Route par défaut - affiche le layout sans conversation sélectionnée\n      {\n        path: '',\n        component: MessageChatComponent,\n        data: { title: 'Messages' },\n      },\n      // Route pour une conversation spécifique\n      {\n        path: ':conversationId',\n        component: MessageChatComponent,\n        data: { title: 'Chat' },\n      },\n    ],\n  },\n];\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule],\n})\nexport class MessagesRoutingModule {}\n", "import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n\nimport { MessagesRoutingModule } from './messages-routing.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ApolloModule } from 'apollo-angular';\nimport { MessageChatComponent } from './message-chat/message-chat.component';\nimport { MessagesListComponent } from './messages-list/messages-list.component';\nimport { UserListComponent } from './user-list/user-list.component';\nimport { MessageLayoutComponent } from './message-layout/message-layout.component';\nimport { SystemStatusComponent } from '../../../components/system-status/system-status.component';\n\nimport { UserStatusService } from 'src/app/services/user-status.service';\nimport { MessageService } from 'src/app/services/message.service';\n// import { SharedComponentsModule } from '../../../shared/components/shared-components.module';\n\n@NgModule({\n  declarations: [\n    MessageChatComponent,\n    MessagesListComponent,\n    UserListComponent,\n    MessageLayoutComponent,\n    SystemStatusComponent,\n  ],\n  imports: [\n    CommonModule,\n    MessagesRoutingModule,\n    FormsModule,\n    ReactiveFormsModule,\n    ApolloModule,\n    RouterModule,\n    // SharedComponentsModule,\n  ],\n  providers: [UserStatusService, MessageService],\n})\nexport class MessagesModule {}\n", "// user-list.component.ts\nimport { Component, OnInit, OnDestroy } from '@angular/core';\nimport { Subscription, interval, Observable } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { User } from 'src/app/models/user.model';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport { ToastService } from 'src/app/services/toast.service';\nimport { MessageService } from 'src/app/services/message.service';\nimport { CallService } from 'src/app/services/call.service';\nimport { CallType, Call } from 'src/app/models/message.model';\nimport { LoggerService } from 'src/app/services/logger.service';\nimport { FormControl, FormGroup } from '@angular/forms';\nimport { ThemeService } from '@app/services/theme.service';\n\n@Component({\n  selector: 'app-user-list',\n  templateUrl: './user-list.component.html',\n  styleUrls: ['./user-list.component.css'],\n})\nexport class UserListComponent implements OnInit, OnDestroy {\n  users: User[] = [];\n  loading = true;\n  currentUserId: string | null = null;\n  isDarkMode$: Observable<boolean>;\n\n  // Pagination\n  currentPage = 1;\n  pageSize = 10;\n  totalUsers = 0;\n  totalPages = 0;\n  hasNextPage = false;\n  hasPreviousPage = false;\n\n  // Sorting and filtering\n  sortBy = 'username';\n  sortOrder = 'asc';\n  filterForm = new FormGroup({\n    searchQuery: new FormControl(''),\n    isOnline: new FormControl<boolean | null>(null),\n  });\n\n  // Auto-refresh\n  autoRefreshEnabled = true;\n  autoRefreshInterval = 30000; // 30 seconds\n  private autoRefreshSubscription?: Subscription;\n\n  private loadingMore = false;\n  private subscriptions: Subscription = new Subscription();\n\n  constructor(\n    private MessageService: MessageService,\n    private callService: CallService,\n    public router: Router,\n    public route: ActivatedRoute,\n    private authService: AuthuserService,\n    private toastService: ToastService,\n    private logger: LoggerService,\n    private themeService: ThemeService\n  ) {\n    this.isDarkMode$ = this.themeService.currentTheme$.pipe(\n      map((theme) => theme.name === 'dark')\n    );\n  }\n\n  ngOnInit(): void {\n    this.currentUserId = this.authService.getCurrentUserId();\n    this.setupFilterListeners();\n    this.setupAutoRefresh();\n    this.loadUsers();\n  }\n\n  private setupFilterListeners(): void {\n    // Subscribe to search query changes\n    const searchSub = this.filterForm\n      .get('searchQuery')!\n      .valueChanges.subscribe(() => {\n        this.resetPagination();\n        this.loadUsers();\n      });\n\n    this.subscriptions.add(searchSub);\n\n    // Subscribe to online status filter changes\n    const onlineSub = this.filterForm\n      .get('isOnline')!\n      .valueChanges.subscribe(() => {\n        this.resetPagination();\n        this.loadUsers();\n      });\n\n    this.subscriptions.add(onlineSub);\n  }\n\n  private setupAutoRefresh(): void {\n    if (this.autoRefreshEnabled) {\n      this.autoRefreshSubscription = interval(\n        this.autoRefreshInterval\n      ).subscribe(() => {\n        if (!this.loading && !this.filterForm.get('searchQuery')?.value) {\n          this.loadUsers(true);\n        }\n      });\n    }\n  }\n\n  toggleAutoRefresh(): void {\n    this.autoRefreshEnabled = !this.autoRefreshEnabled;\n\n    if (this.autoRefreshEnabled) {\n      this.setupAutoRefresh();\n    } else if (this.autoRefreshSubscription) {\n      this.autoRefreshSubscription.unsubscribe();\n      this.autoRefreshSubscription = undefined;\n    }\n  }\n\n  resetPagination(): void {\n    this.currentPage = 1;\n  }\n\n  // Get searchQuery from the form\n  get searchQuery(): string {\n    return this.filterForm.get('searchQuery')?.value || '';\n  }\n\n  // Set searchQuery in the form\n  set searchQuery(value: string) {\n    this.filterForm.get('searchQuery')?.setValue(value);\n  }\n\n  // Helper function for template type casting\n  $any(item: any): any {\n    return item;\n  }\n\n  loadUsers(forceRefresh = false): void {\n    if (this.loadingMore) return;\n\n    this.loading = true;\n\n    const searchQuery = this.filterForm.get('searchQuery')?.value || '';\n    const isOnline = this.filterForm.get('isOnline')?.value;\n\n    const sub = this.MessageService.getAllUsers(\n      forceRefresh,\n      searchQuery,\n      this.currentPage,\n      this.pageSize,\n      this.sortBy,\n      this.sortOrder,\n      isOnline === true ? true : undefined\n    ).subscribe({\n      next: (users) => {\n        if (!Array.isArray(users)) {\n          this.users = [];\n          this.loading = false;\n          this.loadingMore = false;\n          this.toastService.showError('Failed to load users: Invalid data');\n          return;\n        }\n\n        // If first page, replace users array; otherwise append\n        if (this.currentPage === 1) {\n          // Filter out current user\n          this.users = users.filter((user) => {\n            if (!user) return false;\n            const userId = user.id || user._id;\n            return userId !== this.currentUserId;\n          });\n        } else {\n          // Append new users to existing array, avoiding duplicates and filtering out current user\n          const newUsers = users.filter((newUser) => {\n            if (!newUser) return false;\n            const userId = newUser.id || newUser._id;\n            return (\n              userId !== this.currentUserId &&\n              !this.users.some(\n                (existingUser) =>\n                  (existingUser.id || existingUser._id) === userId\n              )\n            );\n          });\n\n          this.users = [...this.users, ...newUsers];\n        }\n\n        // Update pagination metadata from service\n        const pagination = this.MessageService.currentUserPagination;\n        this.totalUsers = pagination.totalCount;\n        this.totalPages = pagination.totalPages;\n        this.hasNextPage = pagination.hasNextPage;\n        this.hasPreviousPage = pagination.hasPreviousPage;\n\n        this.loading = false;\n        this.loadingMore = false;\n      },\n      error: (error) => {\n        this.loading = false;\n        this.loadingMore = false;\n        this.toastService.showError(\n          `Failed to load users: ${error.message || 'Unknown error'}`\n        );\n\n        if (this.currentPage === 1) {\n          this.users = [];\n        }\n      },\n      complete: () => {\n        this.loading = false;\n        this.loadingMore = false;\n      },\n    });\n\n    this.subscriptions.add(sub);\n  }\n\n  startConversation(userId: string | undefined) {\n    if (!userId) {\n      this.toastService.showError(\n        'Cannot start conversation with undefined user'\n      );\n      return;\n    }\n\n    this.toastService.showInfo('Creating conversation...');\n\n    this.MessageService.createConversation(userId).subscribe({\n      next: (conversation) => {\n        if (!conversation || !conversation.id) {\n          this.toastService.showError(\n            'Failed to create conversation: Invalid response'\n          );\n          return;\n        }\n\n        this.router\n          .navigate(['/messages/conversations/chat', conversation.id])\n          .then((success) => {\n            if (!success) {\n              this.toastService.showError('Failed to open conversation');\n            }\n          });\n      },\n      error: (error) => {\n        this.toastService.showError(\n          `Failed to create conversation: ${error.message || 'Unknown error'}`\n        );\n      },\n    });\n  }\n\n  startAudioCall(userId: string): void {\n    if (!userId) return;\n\n    this.callService.initiateCall(userId, CallType.AUDIO).subscribe({\n      next: (call: Call) => {\n        this.toastService.showSuccess('Audio call initiated');\n      },\n      error: (error: any) => {\n        this.toastService.showError('Failed to initiate audio call');\n      },\n    });\n  }\n\n  startVideoCall(userId: string): void {\n    if (!userId) return;\n\n    this.callService.initiateCall(userId, CallType.VIDEO).subscribe({\n      next: (call: Call) => {\n        this.toastService.showSuccess('Video call initiated');\n      },\n      error: (error: any) => {\n        this.toastService.showError('Failed to initiate video call');\n      },\n    });\n  }\n\n  loadNextPage(): void {\n    if (this.hasNextPage && !this.loading) {\n      this.loadingMore = true;\n      this.currentPage++;\n      this.loadUsers();\n    }\n  }\n\n  loadPreviousPage(): void {\n    if (this.hasPreviousPage && !this.loading) {\n      this.loadingMore = true;\n      this.currentPage--;\n      this.loadUsers();\n    }\n  }\n\n  refreshUsers(): void {\n    this.resetPagination();\n    this.loadUsers(true);\n  }\n\n  clearFilters(): void {\n    this.filterForm.reset({\n      searchQuery: '',\n      isOnline: null,\n    });\n    this.resetPagination();\n    this.loadUsers(true);\n  }\n\n  changeSortOrder(field: string): void {\n    if (this.sortBy === field) {\n      // Toggle sort order if clicking the same field\n      this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';\n    } else {\n      // Set new sort field with default ascending order\n      this.sortBy = field;\n      this.sortOrder = 'asc';\n    }\n\n    this.resetPagination();\n    this.loadUsers(true);\n  }\n\n  /**\n   * Navigue vers la liste des conversations\n   */\n  goBackToConversations(): void {\n    this.router.navigate(['/messages/conversations']);\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.unsubscribe();\n    if (this.autoRefreshSubscription) {\n      this.autoRefreshSubscription.unsubscribe();\n    }\n  }\n}\n", "<div\n  class=\"flex flex-col h-full futuristic-users-container\"\n  [class.dark]=\"isDarkMode$ | async\"\n>\n  <!-- Background decorative elements -->\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n    <!-- Gradient orbs -->\n    <div\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/10 dark:to-transparent blur-3xl\"\n    ></div>\n    <div\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/10 dark:to-transparent blur-3xl\"\n    ></div>\n\n    <!-- Additional glow effects for dark mode -->\n    <div\n      class=\"absolute top-[40%] right-[30%] w-40 h-40 rounded-full bg-gradient-to-br from-transparent to-transparent dark:from-[#00f7ff]/5 dark:to-transparent blur-3xl opacity-0 dark:opacity-100\"\n    ></div>\n    <div\n      class=\"absolute bottom-[60%] left-[25%] w-32 h-32 rounded-full bg-gradient-to-tl from-transparent to-transparent dark:from-[#00f7ff]/5 dark:to-transparent blur-3xl opacity-0 dark:opacity-100\"\n    ></div>\n\n    <!-- Grid pattern for light mode -->\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-0\">\n      <div class=\"h-full grid grid-cols-12\">\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n      </div>\n    </div>\n\n    <!-- Horizontal scan line effect for dark mode -->\n    <div class=\"absolute inset-0 opacity-0 dark:opacity-100 overflow-hidden\">\n      <div class=\"h-px w-full bg-[#00f7ff]/20 absolute animate-scan\"></div>\n    </div>\n  </div>\n  <!-- En-tête -->\n  <div class=\"futuristic-users-header\">\n    <div class=\"flex justify-between items-center mb-4\">\n      <h1 class=\"futuristic-title\">Nouvelle Conversation</h1>\n      <div class=\"flex space-x-2\">\n        <button\n          (click)=\"refreshUsers()\"\n          class=\"futuristic-action-button\"\n          title=\"Rafraîchir la liste\"\n        >\n          <i class=\"fas fa-sync-alt\"></i>\n        </button>\n        <button\n          (click)=\"goBackToConversations()\"\n          class=\"futuristic-action-button\"\n        >\n          <i class=\"fas fa-arrow-left\"></i>\n        </button>\n      </div>\n    </div>\n\n    <!-- Recherche et filtres -->\n    <div class=\"space-y-3\">\n      <!-- Recherche -->\n      <div class=\"relative\">\n        <input\n          [ngModel]=\"searchQuery\"\n          (ngModelChange)=\"searchQuery = $event\"\n          type=\"text\"\n          placeholder=\"Rechercher des utilisateurs...\"\n          class=\"w-full pl-10 pr-4 py-2 rounded-lg futuristic-input-field\"\n        />\n        <i\n          class=\"fas fa-search absolute left-3 top-3 text-[#6d6870] dark:text-[#a0a0a0]\"\n        ></i>\n      </div>\n\n      <!-- Filtres -->\n      <div class=\"flex items-center justify-between\">\n        <div class=\"flex items-center space-x-4\">\n          <!-- Filtre en ligne -->\n          <div class=\"flex items-center space-x-2\">\n            <label class=\"futuristic-checkbox-container\">\n              <input\n                type=\"checkbox\"\n                id=\"onlineFilter\"\n                class=\"futuristic-checkbox\"\n                [checked]=\"filterForm.get('isOnline')?.value === true\"\n                (change)=\"\n                  filterForm\n                    .get('isOnline')\n                    ?.setValue($any($event.target).checked ? true : null)\n                \"\n              />\n              <span class=\"futuristic-checkbox-checkmark\"></span>\n            </label>\n            <label for=\"onlineFilter\" class=\"futuristic-label\"\n              >En ligne uniquement</label\n            >\n          </div>\n\n          <!-- Options de tri -->\n          <div class=\"flex items-center space-x-2\">\n            <span class=\"futuristic-label\">Trier par:</span>\n            <select\n              (change)=\"changeSortOrder($any($event.target).value)\"\n              class=\"futuristic-select\"\n            >\n              <option [selected]=\"sortBy === 'username'\" value=\"username\">\n                Nom\n              </option>\n              <option [selected]=\"sortBy === 'email'\" value=\"email\">\n                Email\n              </option>\n              <option [selected]=\"sortBy === 'lastActive'\" value=\"lastActive\">\n                Dernière activité\n              </option>\n            </select>\n            <button\n              (click)=\"\n                sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';\n                loadUsers(true)\n              \"\n              class=\"futuristic-sort-button\"\n              [title]=\"\n                sortOrder === 'asc' ? 'Ordre croissant' : 'Ordre décroissant'\n              \"\n            >\n              <i\n                [class]=\"\n                  sortOrder === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down'\n                \"\n              ></i>\n            </button>\n          </div>\n        </div>\n\n        <!-- Effacer les filtres -->\n        <button (click)=\"clearFilters()\" class=\"futuristic-clear-button\">\n          Effacer les filtres\n        </button>\n      </div>\n\n      <!-- Info pagination -->\n      <div\n        *ngIf=\"totalUsers > 0\"\n        class=\"flex justify-between items-center futuristic-pagination-info\"\n      >\n        <span\n          >Affichage de {{ users.length }} sur\n          {{ totalUsers }} utilisateurs</span\n        >\n        <span>Page {{ currentPage }} sur {{ totalPages }}</span>\n      </div>\n    </div>\n  </div>\n\n  <!-- Liste des utilisateurs -->\n  <div\n    class=\"futuristic-users-list\"\n    (scroll)=\"\n      $any($event.target).scrollTop + $any($event.target).clientHeight >=\n        $any($event.target).scrollHeight - 200 && loadNextPage()\n    \"\n  >\n    <!-- État de chargement -->\n    <div *ngIf=\"loading && !users.length\" class=\"futuristic-loading-container\">\n      <div class=\"futuristic-loading-circle\"></div>\n      <div class=\"futuristic-loading-text\">Chargement des utilisateurs...</div>\n    </div>\n\n    <!-- État vide -->\n    <div *ngIf=\"!loading && users.length === 0\" class=\"futuristic-empty-state\">\n      <div class=\"futuristic-empty-icon\">\n        <i class=\"fas fa-users\"></i>\n      </div>\n      <h3 class=\"futuristic-empty-title\">Aucun utilisateur trouvé</h3>\n      <p class=\"futuristic-empty-text\">\n        Essayez un autre terme de recherche ou effacez les filtres\n      </p>\n    </div>\n\n    <!-- Liste des utilisateurs -->\n    <ul *ngIf=\"users.length > 0\" class=\"futuristic-users-grid\">\n      <li *ngFor=\"let user of users\" class=\"futuristic-user-card\">\n        <div\n          class=\"futuristic-user-content\"\n          (click)=\"startConversation(user.id || user._id)\"\n        >\n          <div class=\"futuristic-avatar\">\n            <img\n              [src]=\"user.image || 'assets/images/default-avatar.png'\"\n              alt=\"User avatar\"\n            />\n            <span\n              *ngIf=\"user.isOnline\"\n              class=\"futuristic-online-indicator\"\n            ></span>\n          </div>\n          <div class=\"futuristic-user-info\">\n            <h3 class=\"futuristic-username\">\n              {{ user.username }}\n            </h3>\n            <p class=\"futuristic-user-email\">{{ user.email }}</p>\n          </div>\n        </div>\n\n        <!-- Boutons d'appel -->\n        <div class=\"futuristic-call-buttons\">\n          <button\n            *ngIf=\"user.isOnline\"\n            (click)=\"startAudioCall(user.id || user._id)\"\n            class=\"futuristic-call-button\"\n            title=\"Appel audio\"\n          >\n            <i class=\"fas fa-phone\"></i>\n          </button>\n          <button\n            *ngIf=\"user.isOnline\"\n            (click)=\"startVideoCall(user.id || user._id)\"\n            class=\"futuristic-call-button\"\n            title=\"Appel vidéo\"\n          >\n            <i class=\"fas fa-video\"></i>\n          </button>\n        </div>\n      </li>\n    </ul>\n\n    <!-- Indicateur de chargement supplémentaire -->\n    <div *ngIf=\"loading && users.length > 0\" class=\"futuristic-loading-more\">\n      <div class=\"futuristic-loading-dots\">\n        <div class=\"futuristic-loading-dot\" style=\"animation-delay: 0s\"></div>\n        <div class=\"futuristic-loading-dot\" style=\"animation-delay: 0.2s\"></div>\n        <div class=\"futuristic-loading-dot\" style=\"animation-delay: 0.4s\"></div>\n      </div>\n      <div class=\"futuristic-loading-text\">\n        Chargement de plus d'utilisateurs...\n      </div>\n    </div>\n\n    <!-- Bouton de chargement supplémentaire -->\n    <div *ngIf=\"hasNextPage && !loading\" class=\"futuristic-load-more-container\">\n      <button (click)=\"loadNextPage()\" class=\"futuristic-load-more-button\">\n        <i class=\"fas fa-chevron-down mr-2\"></i>\n        Charger plus d'utilisateurs\n      </button>\n    </div>\n  </div>\n</div>\n"], "names": ["interval", "SystemStatusComponent", "constructor", "messageService", "mockDataService", "themeService", "toastService", "status", "backend", "frontend", "database", "websocket", "mockData", "theme", "<PERSON><PERSON><PERSON><PERSON>", "Date", "isChecking", "ngOnInit", "checkSystemStatus", "subscription", "subscribe", "currentTheme$", "displayName", "ngOnDestroy", "unsubscribe", "_this", "_asyncToGenerator", "getUsers", "to<PERSON>romise", "getConversations", "getStatusText", "testMockData", "next", "users", "showSuccess", "length", "error", "showError", "testThemes", "themes", "getAvailableThemes", "showInfo", "testNotifications", "setTimeout", "showWarning", "i0", "ɵɵdirectiveInject", "i1", "MessageService", "i2", "MockDataService", "i3", "ThemeService", "i4", "ToastService", "selectors", "decls", "vars", "consts", "template", "SystemStatusComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "SystemStatusComponent_Template_button_click_5_listener", "SystemStatusComponent_Template_button_click_59_listener", "SystemStatusComponent_Template_button_click_62_listener", "SystemStatusComponent_Template_button_click_65_listener", "ɵɵadvance", "ɵɵproperty", "ɵɵclassProp", "ɵɵtextInterpolate1", "ɵɵtextInterpolate", "ɵɵpipeBind2", "BehaviorSubject", "throwError", "map", "catchError", "CallType", "CallStatus", "INITIATE_CALL_MUTATION", "ACCEPT_CALL_MUTATION", "REJECT_CALL_MUTATION", "END_CALL_MUTATION", "INCOMING_CALL_SUBSCRIPTION", "CALL_STATUS_CHANGED_SUBSCRIPTION", "CALL_SIGNAL_SUBSCRIPTION", "SEND_CALL_SIGNAL_MUTATION", "CallService", "apollo", "logger", "activeCall", "incomingCall", "callSignals", "activeCall$", "asObservable", "incomingCall$", "callSignals$", "currentCallId", "callState", "sounds", "isPlaying", "peerConnection", "localStream", "remoteStream", "localVideoElement", "remoteVideoElement", "isAudioEnabled", "isVideoEnabled", "rtcConfig", "iceServers", "urls", "info", "initializeSounds", "initializeSubscriptions", "initializeWebRTC", "cleanup", "initiateCall", "recipientId", "callType", "conversationId", "Error", "setCallState", "callId", "generateCallId", "mutate", "mutation", "variables", "pipe", "result", "call", "data", "handleCallInitiated", "acceptCall", "id", "acceptedCall", "handleCallAccepted", "rejectCall", "reason", "success", "handleCallEnded", "endCall", "currentCall", "value", "currentIncomingCall", "isCallActive", "isCallInProgress", "now", "Math", "random", "toString", "substr", "state", "debug", "play", "startOutgoingCallMedia", "type", "stop", "stopAllSounds", "cleanupWebRTC", "handleIncomingCall", "prepareForIncomingCall", "handleCallStatusChange", "CONNECTED", "ENDED", "REJECTED", "handleCallSignal", "signal", "createSyntheticSounds", "createSyntheticSound", "name", "frequencies", "duration", "loop", "audioContext", "window", "AudioContext", "webkitAudioContext", "sampleRate", "frameCount", "buffer", "createBuffer", "channelData", "getChannelData", "i", "sample", "for<PERSON>ach", "freq", "amplitude", "phase", "PI", "sin", "envelope", "audio", "Audio", "customPlay", "source", "createBufferSource", "connect", "destination", "start", "subscribeToIncomingCalls", "subscribeToCallStatusChanges", "subscribeToCallSignals", "query", "errorPolicy", "errors", "callStatusChanged", "callSignal", "createPeerConnection", "sound", "currentSource", "Object", "keys", "RTCPeerConnection", "onicecandidate", "event", "candidate", "sendSignal", "JSON", "stringify", "ontrack", "track", "kind", "streams", "attachRemoteStream", "onconnectionstatechange", "connectionState", "stream", "getUserMedia", "addLocalStreamToPeerConnection", "attachLocalStream", "_this2", "_this3", "constraints", "video", "VIDEO", "navigator", "mediaDevices", "getTracks", "addTrack", "srcObject", "signalType", "signalData", "close", "complete", "attachVideoElements", "localVideo", "remoteVideo", "toggleAudio", "getAudioTracks", "enabled", "toggleVideo", "getVideoTracks", "setVideoElements", "audioEnabled", "videoEnabled", "localMediaStream", "remoteMediaStream", "enableSounds", "disableSounds", "ɵɵinject", "Apollo", "LoggerService", "factory", "ɵfac", "providedIn", "of", "distinctUntilChanged", "switchMap", "tap", "filter", "MessageType", "ctx_r2", "selectedConversation", "participants", "MessageChatComponent_div_0_button_12_Template_button_click_0_listener", "ɵɵrestoreView", "_r21", "ctx_r20", "ɵɵnextContext", "ɵɵresetView", "startAudioCall", "MessageChatComponent_div_0_button_13_Template_button_click_0_listener", "_r23", "ctx_r22", "startVideoCall", "message_r24", "sender", "image", "ɵɵsanitizeUrl", "username", "replyTo", "content", "MessageChatComponent_div_0_div_19_div_7_Template_img_click_1_listener", "_r44", "$implicit", "ctx_r42", "openImageViewer", "attachments", "ɵɵtemplate", "MessageChatComponent_div_0_div_19_div_7_div_2_Template", "url", "MessageChatComponent_div_0_div_19_div_8_Template_button_click_7_listener", "_r48", "ctx_r46", "downloadFile", "ctx_r30", "formatFileSize", "size", "MessageChatComponent_div_0_div_19_div_9_Template_button_click_1_listener", "_r52", "ctx_r50", "playVoiceMessage", "ctx_r31", "formatDuration", "MessageChatComponent_div_0_div_19_div_10_div_2_Template", "MessageChatComponent_div_0_div_19_div_11_span_1_Template_span_click_0_listener", "restoredCtx", "_r61", "reaction_r58", "ctx_r59", "reactToMessage", "emoji", "ɵɵtextInterpolate2", "count", "MessageChatComponent_div_0_div_19_div_11_span_1_Template", "reactions", "MessageChatComponent_div_0_div_19_div_15_i_6_Template_i_click_0_listener", "_r71", "ctx_r69", "retryMessage", "MessageChatComponent_div_0_div_19_div_15_i_2_Template", "MessageChatComponent_div_0_div_19_div_15_i_3_Template", "MessageChatComponent_div_0_div_19_div_15_i_4_Template", "MessageChatComponent_div_0_div_19_div_15_i_5_Template", "MessageChatComponent_div_0_div_19_div_15_i_6_Template", "MessageChatComponent_div_0_div_19_div_15_i_7_Template", "ctx_r34", "getMessageStatus", "MessageChatComponent_div_0_div_19_div_23_button_1_Template_button_click_0_listener", "_r77", "emoji_r74", "ctx_r75", "MessageChatComponent_div_0_div_19_div_23_button_1_Template", "ctx_r35", "quickEmojis", "MessageChatComponent_div_0_div_19_img_1_Template", "MessageChatComponent_div_0_div_19_div_3_Template", "MessageChatComponent_div_0_div_19_div_4_Template", "MessageChatComponent_div_0_div_19_div_6_Template", "MessageChatComponent_div_0_div_19_div_7_Template", "MessageChatComponent_div_0_div_19_div_8_Template", "MessageChatComponent_div_0_div_19_div_9_Template", "MessageChatComponent_div_0_div_19_div_10_Template", "MessageChatComponent_div_0_div_19_div_11_Template", "MessageChatComponent_div_0_div_19_div_15_Template", "MessageChatComponent_div_0_div_19_Template_button_click_17_listener", "_r79", "ctx_r78", "toggleQuickReactions", "MessageChatComponent_div_0_div_19_Template_button_click_19_listener", "ctx_r80", "setReplyTo", "MessageChatComponent_div_0_div_19_Template_button_click_21_listener", "ctx_r81", "showMessageMenu", "MessageChatComponent_div_0_div_19_div_23_Template", "ctx_r9", "isMyMessage", "isGroup", "TEXT", "IMAGE", "FILE", "VOICE_MESSAGE", "formatMessageTime", "timestamp", "showQuickReactionsFor", "ctx_r10", "getTypingText", "MessageChatComponent_div_0_div_22_Template_button_click_7_listener", "_r83", "ctx_r82", "cancelReply", "ctx_r11", "replyingTo", "MessageChatComponent_div_0_div_23_Template_button_click_7_listener", "_r85", "ctx_r84", "cancelEditing", "ctx_r12", "editingMessage", "MessageChatComponent_div_0_div_24_div_2_Template_button_click_4_listener", "_r90", "i_r88", "index", "ctx_r89", "removeSelectedFile", "file_r87", "MessageChatComponent_div_0_div_24_div_2_Template", "ctx_r13", "selectedFiles", "MessageChatComponent_div_0_div_25_Template_button_click_4_listener", "_r92", "ctx_r91", "stopVoiceRecording", "MessageChatComponent_div_0_div_25_Template_button_click_6_listener", "ctx_r93", "cancelVoiceRecording", "ctx_r14", "recordingDuration", "MessageChatComponent_div_0_div_31_Template_button_click_1_listener", "_r95", "ctx_r94", "openFileSelector", "MessageChatComponent_div_0_div_31_Template_button_click_5_listener", "ctx_r96", "MessageChatComponent_div_0_div_31_Template_button_click_9_listener", "ctx_r97", "showVoiceRecorder", "showAttachmentMenu", "MessageChatComponent_div_0_div_31_Template_button_click_13_listener", "ctx_r98", "showLocationPicker", "MessageChatComponent_div_0_button_37_Template_button_mousedown_0_listener", "_r100", "ctx_r99", "startVoiceRecording", "MessageChatComponent_div_0_button_38_Template_button_click_0_listener", "_r102", "ctx_r101", "sendMessage", "ctx_r18", "canSendMessage", "MessageChatComponent_div_0_span_8_Template", "MessageChatComponent_div_0_span_9_Template", "MessageChatComponent_div_0_span_10_Template", "MessageChatComponent_div_0_button_12_Template", "MessageChatComponent_div_0_button_13_Template", "MessageChatComponent_div_0_div_18_Template", "MessageChatComponent_div_0_div_19_Template", "MessageChatComponent_div_0_div_20_Template", "MessageChatComponent_div_0_div_22_Template", "MessageChatComponent_div_0_div_23_Template", "MessageChatComponent_div_0_div_24_Template", "MessageChatComponent_div_0_div_25_Template", "MessageChatComponent_div_0_Template_button_click_29_listener", "_r104", "ctx_r103", "toggleAttachmentMenu", "MessageChatComponent_div_0_div_31_Template", "MessageChatComponent_div_0_Template_button_click_32_listener", "ctx_r105", "toggleEmojiPicker", "MessageChatComponent_div_0_Template_textarea_ngModelChange_34_listener", "$event", "ctx_r106", "messageContent", "MessageChatComponent_div_0_Template_textarea_keydown_34_listener", "ctx_r107", "onKeyPress", "MessageChatComponent_div_0_Template_textarea_input_34_listener", "ctx_r108", "onTyping", "MessageChatComponent_div_0_button_37_Template", "MessageChatComponent_div_0_button_38_Template", "MessageChatComponent_div_0_Template_input_change_39_listener", "ctx_r109", "onFileSelected", "ctx_r0", "isRecipientOnline", "groupPhoto", "getRecipientAvatar", "groupName", "getRecipientName", "isLoading", "messages", "trackByMessageId", "typingUsers", "isRecording", "trim", "MessageChatComponent", "authService", "route", "router", "cdr", "ngZone", "currentUser", "isTyping", "currentPage", "hasMoreMessages", "loadingMoreMessages", "showEmojiPicker", "searchQuery", "searchResults", "showSearchResults", "subscriptions", "conversationId$", "initializeComponent", "setupSubscriptions", "ngAfterViewInit", "scrollToBottom", "getCurrentUser", "navigate", "params", "conversationSub", "getConversation", "console", "conversation", "markMessagesAsRead", "detectChanges", "messagesSub", "subscribeToMessages", "message", "addNewMessage", "markMessageAsRead", "typingSub", "subscribeToTypingIndicators", "handleTypingIndicator", "push", "sub", "typingTimeout", "clearTimeout", "recordingInterval", "clearInterval", "stopTyping", "files", "updateMessage", "sendNewMessage", "<PERSON><PERSON><PERSON><PERSON>", "hasFiles", "hasConversation", "getRecipientId", "tempMessage", "getFileMessageType", "isPending", "sendObservable", "sentMessage", "replaceTemporaryMessage", "markMessageAsError", "newContent", "editMessage", "updatedMessage", "updateMessageInList", "deleteMessage", "canDeleteMessage", "confirm", "removeMessageFromList", "target", "Array", "from", "splice", "fileInput", "nativeElement", "click", "setInterval", "AUDIO", "startTyping", "currentUserId", "_id", "recipient", "find", "p", "file", "split", "tempId", "realMessage", "findIndex", "m", "messageId", "isError", "senderId", "userId", "user", "u", "unreadMessages", "isRead", "runOutsideAngular", "messagesContainer", "element", "scrollTop", "scrollHeight", "date", "diffInHours", "getTime", "toLocaleTimeString", "hour", "minute", "toLocaleDateString", "day", "month", "isDelivered", "key", "shift<PERSON>ey", "preventDefault", "startEditingMessage", "messageInput", "focus", "isOnline", "attachment", "open", "units", "unitIndex", "fileSize", "toFixed", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "playAudio", "catch", "minutes", "floor", "seconds", "padStart", "actions", "log", "undefined", "onVoiceRecordingComplete", "recording", "formData", "FormData", "append", "blob", "waveform", "sendVoiceMessage", "onLocationSelected", "location", "locationMessage", "latitude", "longitude", "address", "mapUrl", "sendLocationMessage", "AuthService", "ActivatedRoute", "Router", "ChangeDetectorRef", "NgZone", "viewQuery", "MessageChatComponent_Query", "MessageChatComponent_div_0_Template", "MessageChatComponent_div_1_Template", "MessagesListComponent", "MessagesListComponent_Template", "styles", "RouterModule", "MessageLayoutComponent", "routes", "path", "component", "children", "title", "MessagesRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "exports", "CommonModule", "FormsModule", "ReactiveFormsModule", "ApolloModule", "UserListComponent", "UserStatusService", "MessagesModule", "declarations", "Subscription", "FormControl", "FormGroup", "totalUsers", "totalPages", "UserListComponent_ul_61_li_1_button_11_Template_button_click_0_listener", "_r13", "user_r7", "UserListComponent_ul_61_li_1_button_12_Template_button_click_0_listener", "_r16", "UserListComponent_ul_61_li_1_Template_div_click_1_listener", "_r18", "ctx_r17", "startConversation", "UserListComponent_ul_61_li_1_span_4_Template", "UserListComponent_ul_61_li_1_button_11_Template", "UserListComponent_ul_61_li_1_button_12_Template", "email", "UserListComponent_ul_61_li_1_Template", "ctx_r3", "UserListComponent_div_63_Template_button_click_1_listener", "_r20", "ctx_r19", "loadNextPage", "callService", "loading", "pageSize", "hasNextPage", "hasPreviousPage", "sortBy", "sortOrder", "filterForm", "autoRefreshEnabled", "autoRefreshInterval", "loadingMore", "isDarkMode$", "getCurrentUserId", "setupFilterListeners", "setupAutoRefresh", "loadUsers", "searchSub", "get", "valueChanges", "resetPagination", "add", "onlineSub", "autoRefreshSubscription", "toggleAutoRefresh", "setValue", "$any", "item", "forceRefresh", "getAllUsers", "isArray", "newUsers", "newUser", "some", "existingUser", "pagination", "currentUserPagination", "totalCount", "createConversation", "then", "loadPreviousPage", "refreshUsers", "clearFilters", "reset", "changeSortOrder", "field", "goBackToConversations", "AuthuserService", "i5", "i6", "i7", "UserListComponent_Template", "UserListComponent_Template_button_click_27_listener", "UserListComponent_Template_button_click_29_listener", "UserListComponent_Template_input_ngModelChange_33_listener", "UserListComponent_Template_input_change_39_listener", "tmp_b_0", "checked", "UserListComponent_Template_select_change_46_listener", "UserListComponent_Template_button_click_53_listener", "UserListComponent_Template_button_click_55_listener", "UserListComponent_div_57_Template", "UserListComponent_Template_div_scroll_58_listener", "clientHeight", "UserListComponent_div_59_Template", "UserListComponent_div_60_Template", "UserListComponent_ul_61_Template", "UserListComponent_div_62_Template", "UserListComponent_div_63_Template", "ɵɵpipeBind1", "tmp_2_0", "ɵɵclassMap"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}