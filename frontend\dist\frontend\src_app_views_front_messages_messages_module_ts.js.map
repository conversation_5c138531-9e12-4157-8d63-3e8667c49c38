{"version": 3, "file": "src_app_views_front_messages_messages_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAC8C;;;;;;;AA4LxC,MAAOC,qBAAqB;EAchCC,YACUC,cAA8B,EAC9BC,eAAgC,EAChCC,YAA0B,EAC1BC,YAA0B;IAH1B,KAAAH,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,YAAY,GAAZA,YAAY;IAjBtB,KAAAC,MAAM,GAAiB;MACrBC,OAAO,EAAE,UAAU;MACnBC,QAAQ,EAAE,QAAQ;MAClBC,QAAQ,EAAE,UAAU;MACpBC,SAAS,EAAE,UAAU;MACrBC,QAAQ,EAAE,UAAiB;MAC3BC,KAAK,EAAE,eAAe;MACtBC,SAAS,EAAE,IAAIC,IAAI;KACpB;IAED,KAAAC,UAAU,GAAG,KAAK;EAQf;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACC,YAAY,GAAGnB,8CAAQ,CAAC,KAAK,CAAC,CAACoB,SAAS,CAAC,MAAK;MACjD,IAAI,CAACF,iBAAiB,EAAE;IAC1B,CAAC,CAAC;IAEF;IACA,IAAI,CAACb,YAAY,CAACgB,aAAa,CAACD,SAAS,CAACP,KAAK,IAAG;MAChD,IAAI,CAACN,MAAM,CAACM,KAAK,GAAGA,KAAK,CAACS,WAAW;IACvC,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACJ,YAAY,EAAEK,WAAW,EAAE;EAClC;EAEMN,iBAAiBA,CAAA;IAAA,IAAAO,KAAA;IAAA,OAAAC,kKAAA;MACrBD,KAAI,CAACT,UAAU,GAAG,IAAI;MACtBS,KAAI,CAAClB,MAAM,CAACO,SAAS,GAAG,IAAIC,IAAI,EAAE;MAElC;MACA,IAAI;QACF,MAAMU,KAAI,CAACrB,eAAe,CAACuB,QAAQ,EAAE,CAACC,SAAS,EAAE;QACjDH,KAAI,CAAClB,MAAM,CAACK,QAAQ,GAAG,WAAW;OACnC,CAAC,MAAM;QACNa,KAAI,CAAClB,MAAM,CAACK,QAAQ,GAAG,aAAa;;MAGtC;MACA,IAAI;QACF,MAAMa,KAAI,CAACtB,cAAc,CAAC0B,gBAAgB,EAAE,CAACD,SAAS,EAAE;QACxDH,KAAI,CAAClB,MAAM,CAACC,OAAO,GAAG,QAAQ;QAC9BiB,KAAI,CAAClB,MAAM,CAACG,QAAQ,GAAG,QAAQ;QAC/Be,KAAI,CAAClB,MAAM,CAACI,SAAS,GAAG,QAAQ;OACjC,CAAC,MAAM;QACNc,KAAI,CAAClB,MAAM,CAACC,OAAO,GAAG,SAAS;QAC/BiB,KAAI,CAAClB,MAAM,CAACG,QAAQ,GAAG,SAAS;QAChCe,KAAI,CAAClB,MAAM,CAACI,SAAS,GAAG,SAAS;;MAGnCc,KAAI,CAACT,UAAU,GAAG,KAAK;IAAC;EAC1B;EAEAc,aAAaA,CAACvB,MAAc;IAC1B,QAAQA,MAAM;MACZ,KAAK,QAAQ;QAAE,OAAO,UAAU;MAChC,KAAK,SAAS;QAAE,OAAO,YAAY;MACnC,KAAK,UAAU;QAAE,OAAO,iBAAiB;MACzC;QAAS,OAAO,SAAS;;EAE7B;EAEAwB,YAAYA,CAAA;IACV,IAAI,CAAC3B,eAAe,CAACuB,QAAQ,EAAE,CAACP,SAAS,CAAC;MACxCY,IAAI,EAAGC,KAAK,IAAI;QACd,IAAI,CAAC3B,YAAY,CAAC4B,WAAW,CAAC,GAAGD,KAAK,CAACE,MAAM,+BAA+B,CAAC;MAC/E,CAAC;MACDC,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAC9B,YAAY,CAAC+B,SAAS,CAAC,+CAA+C,CAAC;MAC9E;KACD,CAAC;EACJ;EAEAC,UAAUA,CAAA;IACR,MAAMC,MAAM,GAAG,IAAI,CAAClC,YAAY,CAACmC,kBAAkB,EAAE;IACrD,IAAI,CAAClC,YAAY,CAACmC,QAAQ,CAAC,GAAGF,MAAM,CAACJ,MAAM,qBAAqB,CAAC;EACnE;EAEAO,iBAAiBA,CAAA;IACf,IAAI,CAACpC,YAAY,CAAC4B,WAAW,CAAC,+BAA+B,CAAC;IAC9DS,UAAU,CAAC,MAAK;MACd,IAAI,CAACrC,YAAY,CAACmC,QAAQ,CAAC,6BAA6B,CAAC;IAC3D,CAAC,EAAE,IAAI,CAAC;IACRE,UAAU,CAAC,MAAK;MACd,IAAI,CAACrC,YAAY,CAACsC,WAAW,CAAC,+BAA+B,CAAC;IAChE,CAAC,EAAE,IAAI,CAAC;EACV;;;uBAnGW3C,qBAAqB,EAAA4C,+DAAA,CAAAE,qEAAA,GAAAF,+DAAA,CAAAI,wEAAA,GAAAJ,+DAAA,CAAAM,iEAAA,GAAAN,+DAAA,CAAAQ,iEAAA;IAAA;EAAA;;;YAArBpD,qBAAqB;MAAAsD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAzK9BhB,4DAAA,aAAmF;UAG7EA,uDAAA,WAAmD;UACnDA,oDAAA,kCACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,gBAIC;UAHCA,wDAAA,mBAAAuB,uDAAA;YAAA,OAASN,GAAA,CAAA5C,iBAAA,EAAmB;UAAA,EAAC;UAI7B2B,uDAAA,WAAiE;UACjEA,oDAAA,GACF;UAAAA,0DAAA,EAAS;UAGXA,4DAAA,aAAmD;UAIjBA,oDAAA,eAAO;UAAAA,0DAAA,EAAO;UAC1CA,4DAAA,eAA+B;UAC7BA,uDAAA,eAMO;UACPA,4DAAA,gBAE8D;UAC5DA,oDAAA,IACF;UAAAA,0DAAA,EAAO;UAMbA,4DAAA,cAAyB;UAEOA,oDAAA,gBAAQ;UAAAA,0DAAA,EAAO;UAC3CA,4DAAA,eAA+B;UAC7BA,uDAAA,eAMO;UACPA,4DAAA,gBAE+D;UAC7DA,oDAAA,IACF;UAAAA,0DAAA,EAAO;UAMbA,4DAAA,cAAyB;UAEOA,oDAAA,4BAAe;UAAAA,0DAAA,EAAO;UAClDA,4DAAA,eAA+B;UAC7BA,uDAAA,eAMO;UACPA,4DAAA,gBAE+D;UAC7DA,oDAAA,IACF;UAAAA,0DAAA,EAAO;UAMbA,4DAAA,cAAyB;UAEOA,oDAAA,iBAAS;UAAAA,0DAAA,EAAO;UAC5CA,4DAAA,eAA+B;UAC7BA,uDAAA,eAMO;UACPA,4DAAA,gBAEgE;UAC9DA,oDAAA,IACF;UAAAA,0DAAA,EAAO;UAOfA,4DAAA,eAEgE;UAEhCA,oDAAA,4BAAe;UAAAA,0DAAA,EAAO;UAClDA,4DAAA,gBAC+D;UAC7DA,oDAAA,IACF;UAAAA,0DAAA,EAAO;UAETA,4DAAA,aAAsC;UACpCA,oDAAA,IAGF;UAAAA,0DAAA,EAAI;UAINA,4DAAA,eAA0C;UAEVA,oDAAA,yBAAY;UAAAA,0DAAA,EAAO;UAC/CA,4DAAA,gBAAoC;UAAAA,oDAAA,IAAkB;UAAAA,0DAAA,EAAO;UAKjEA,4DAAA,eAAoD;UAClDA,oDAAA,IACF;;UAAAA,0DAAA,EAAM;UAGNA,4DAAA,eAAwD;UAEpDA,wDAAA,mBAAAwB,wDAAA;YAAA,OAASP,GAAA,CAAA/B,YAAA,EAAc;UAAA,EAAC;UAGxBc,uDAAA,aAAoC;UACpCA,oDAAA,2BACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAGC;UAFCA,wDAAA,mBAAAyB,wDAAA;YAAA,OAASR,GAAA,CAAAxB,UAAA,EAAY;UAAA,EAAC;UAGtBO,uDAAA,aAAmC;UACnCA,oDAAA,0BACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAGC;UAFCA,wDAAA,mBAAA0B,wDAAA;YAAA,OAAST,GAAA,CAAApB,iBAAA,EAAmB;UAAA,EAAC;UAG7BG,uDAAA,aAAgC;UAChCA,oDAAA,qBACF;UAAAA,0DAAA,EAAS;;;UAlJPA,uDAAA,GAAuB;UAAvBA,wDAAA,aAAAiB,GAAA,CAAA9C,UAAA,CAAuB;UAES6B,uDAAA,GAA4B;UAA5BA,yDAAA,YAAAiB,GAAA,CAAA9C,UAAA,CAA4B;UAC5D6B,uDAAA,GACF;UADEA,gEAAA,MAAAiB,GAAA,CAAA9C,UAAA,8CACF;UAWQ6B,uDAAA,GAAkD;UAAlDA,yDAAA,iBAAAiB,GAAA,CAAAvD,MAAA,CAAAC,OAAA,cAAkD,eAAAsD,GAAA,CAAAvD,MAAA,CAAAC,OAAA,iCAAAsD,GAAA,CAAAvD,MAAA,CAAAC,OAAA,kCAAAsD,GAAA,CAAAvD,MAAA,CAAAC,OAAA;UAK9BqC,uDAAA,GAAoD;UAApDA,yDAAA,mBAAAiB,GAAA,CAAAvD,MAAA,CAAAC,OAAA,cAAoD,iBAAAsD,GAAA,CAAAvD,MAAA,CAAAC,OAAA,mCAAAsD,GAAA,CAAAvD,MAAA,CAAAC,OAAA;UAGxEqC,uDAAA,GACF;UADEA,gEAAA,MAAAiB,GAAA,CAAAhC,aAAA,CAAAgC,GAAA,CAAAvD,MAAA,CAAAC,OAAA,OACF;UAYEqC,uDAAA,GAAmD;UAAnDA,yDAAA,iBAAAiB,GAAA,CAAAvD,MAAA,CAAAE,QAAA,cAAmD,eAAAqD,GAAA,CAAAvD,MAAA,CAAAE,QAAA,iCAAAqD,GAAA,CAAAvD,MAAA,CAAAE,QAAA,kCAAAqD,GAAA,CAAAvD,MAAA,CAAAE,QAAA;UAK/BoC,uDAAA,GAAqD;UAArDA,yDAAA,mBAAAiB,GAAA,CAAAvD,MAAA,CAAAE,QAAA,cAAqD,iBAAAqD,GAAA,CAAAvD,MAAA,CAAAE,QAAA,mCAAAqD,GAAA,CAAAvD,MAAA,CAAAE,QAAA;UAGzEoC,uDAAA,GACF;UADEA,gEAAA,MAAAiB,GAAA,CAAAhC,aAAA,CAAAgC,GAAA,CAAAvD,MAAA,CAAAE,QAAA,OACF;UAYEoC,uDAAA,GAAmD;UAAnDA,yDAAA,iBAAAiB,GAAA,CAAAvD,MAAA,CAAAG,QAAA,cAAmD,eAAAoD,GAAA,CAAAvD,MAAA,CAAAG,QAAA,iCAAAoD,GAAA,CAAAvD,MAAA,CAAAG,QAAA,kCAAAoD,GAAA,CAAAvD,MAAA,CAAAG,QAAA;UAK/BmC,uDAAA,GAAqD;UAArDA,yDAAA,mBAAAiB,GAAA,CAAAvD,MAAA,CAAAG,QAAA,cAAqD,iBAAAoD,GAAA,CAAAvD,MAAA,CAAAG,QAAA,mCAAAoD,GAAA,CAAAvD,MAAA,CAAAG,QAAA;UAGzEmC,uDAAA,GACF;UADEA,gEAAA,MAAAiB,GAAA,CAAAhC,aAAA,CAAAgC,GAAA,CAAAvD,MAAA,CAAAG,QAAA,OACF;UAYEmC,uDAAA,GAAoD;UAApDA,yDAAA,iBAAAiB,GAAA,CAAAvD,MAAA,CAAAI,SAAA,cAAoD,eAAAmD,GAAA,CAAAvD,MAAA,CAAAI,SAAA,iCAAAmD,GAAA,CAAAvD,MAAA,CAAAI,SAAA,kCAAAmD,GAAA,CAAAvD,MAAA,CAAAI,SAAA;UAKhCkC,uDAAA,GAAsD;UAAtDA,yDAAA,mBAAAiB,GAAA,CAAAvD,MAAA,CAAAI,SAAA,cAAsD,iBAAAmD,GAAA,CAAAvD,MAAA,CAAAI,SAAA,mCAAAmD,GAAA,CAAAvD,MAAA,CAAAI,SAAA;UAG1EkC,uDAAA,GACF;UADEA,gEAAA,MAAAiB,GAAA,CAAAhC,aAAA,CAAAgC,GAAA,CAAAvD,MAAA,CAAAI,SAAA,OACF;UAQHkC,uDAAA,GAA0D;UAA1DA,yDAAA,qBAAAiB,GAAA,CAAAvD,MAAA,CAAAK,QAAA,iBAA0D,mBAAAkD,GAAA,CAAAvD,MAAA,CAAAK,QAAA;UAIrCiC,uDAAA,GAAwD;UAAxDA,yDAAA,mBAAAiB,GAAA,CAAAvD,MAAA,CAAAK,QAAA,iBAAwD,iBAAAkD,GAAA,CAAAvD,MAAA,CAAAK,QAAA;UAE5EiC,uDAAA,GACF;UADEA,gEAAA,MAAAiB,GAAA,CAAAvD,MAAA,CAAAK,QAAA,wDACF;UAGAiC,uDAAA,GAGF;UAHEA,gEAAA,MAAAiB,GAAA,CAAAvD,MAAA,CAAAK,QAAA,8HAGF;UAOsCiC,uDAAA,GAAkB;UAAlBA,+DAAA,CAAAiB,GAAA,CAAAvD,MAAA,CAAAM,KAAA,CAAkB;UAMxDgC,uDAAA,GACF;UADEA,gEAAA,wCAAAA,yDAAA,SAAAiB,GAAA,CAAAvD,MAAA,CAAAO,SAAA,iBACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrJ6D;AACb;AAQrB;AAUG;;;;AAGpC;;;;AAOM,MAAO8E,WAAW;EA0CtB1F,YAAoB2F,MAAc,EAAUC,MAAqB;IAA7C,KAAAD,MAAM,GAANA,MAAM;IAAkB,KAAAC,MAAM,GAANA,MAAM;IAzClD;IACQ,KAAAC,UAAU,GAAG,IAAIjB,iDAAe,CAAc,IAAI,CAAC;IACnD,KAAAkB,YAAY,GAAG,IAAIlB,iDAAe,CAAsB,IAAI,CAAC;IAC7D,KAAAmB,WAAW,GAAG,IAAInB,iDAAe,CAAoB,IAAI,CAAC;IAElE;IACO,KAAAoB,WAAW,GAAG,IAAI,CAACH,UAAU,CAACI,YAAY,EAAE;IAC5C,KAAAC,aAAa,GAAG,IAAI,CAACJ,YAAY,CAACG,YAAY,EAAE;IAChD,KAAAE,YAAY,GAAG,IAAI,CAACJ,WAAW,CAACE,YAAY,EAAE;IAErD;IACQ,KAAAG,aAAa,GAAkB,IAAI;IACnC,KAAAC,SAAS,GAMF,MAAM;IAErB;IACQ,KAAAC,MAAM,GAAwC,EAAE;IAChD,KAAAC,SAAS,GAA+B,EAAE;IAElD;IACQ,KAAAC,cAAc,GAA6B,IAAI;IAC/C,KAAAC,WAAW,GAAuB,IAAI;IACtC,KAAAC,YAAY,GAAuB,IAAI;IACvC,KAAAC,iBAAiB,GAA4B,IAAI;IACjD,KAAAC,kBAAkB,GAA4B,IAAI;IAClD,KAAAC,cAAc,GAAG,IAAI;IACrB,KAAAC,cAAc,GAAG,IAAI;IAE7B;IACiB,KAAAC,SAAS,GAAqB;MAC7CC,UAAU,EAAE,CACV;QAAEC,IAAI,EAAE;MAA8B,CAAE,EACxC;QAAEA,IAAI,EAAE;MAA+B,CAAE;KAE5C;IAGC,IAAI,CAACrB,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,wCAAwC,CAAC;IACzE,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACzB,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,wCAAwC,CAAC;EAC3E;EAEA7F,WAAWA,CAAA;IACT,IAAI,CAACuE,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,8BAA8B,CAAC;IAC/D,IAAI,CAACI,OAAO,EAAE;EAChB;EAEA;EAEA;;;EAGAC,YAAYA,CACVC,WAAmB,EACnBC,QAAkB,EAClBC,cAAuB;IAEvB,IAAI,CAAC9B,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,qBAAqB,EAAE;MACrDM,WAAW;MACXC;KACD,CAAC;IAEF,IAAI,IAAI,CAACpB,SAAS,KAAK,MAAM,EAAE;MAC7B,OAAOxB,gDAAU,CAAC,MAAM,IAAI8C,KAAK,CAAC,qCAAqC,CAAC,CAAC;;IAG3E,IAAI,CAACC,YAAY,CAAC,YAAY,CAAC;IAC/B,MAAMC,MAAM,GAAG,IAAI,CAACC,cAAc,EAAE;IAEpC,OAAO,IAAI,CAACnC,MAAM,CACfoC,MAAM,CAAyB;MAC9BC,QAAQ,EAAE9C,4EAAsB;MAChC+C,SAAS,EAAE;QACTT,WAAW;QACXC,QAAQ;QACRI,MAAM;QACNH;QACA;;KAEH,CAAC,CACDQ,IAAI,CACHpD,mDAAG,CAAEqD,MAAM,IAAI;MACb,MAAMC,IAAI,GAAGD,MAAM,CAACE,IAAI,EAAEd,YAAY;MACtC,IAAI,CAACa,IAAI,EAAE,MAAM,IAAIT,KAAK,CAAC,yBAAyB,CAAC;MAErD,IAAI,CAACW,mBAAmB,CAACF,IAAI,CAAC;MAC9B,OAAOA,IAAI;IACb,CAAC,CAAC,EACFrD,0DAAU,CAAE7C,KAAK,IAAI;MACnB,IAAI,CAAC0D,MAAM,CAAC1D,KAAK,CAAC,aAAa,EAAE,wBAAwB,EAAEA,KAAK,CAAC;MACjE,IAAI,CAAC0F,YAAY,CAAC,MAAM,CAAC;MACzB,OAAO/C,gDAAU,CAAC,MAAM3C,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAqG,UAAUA,CAACH,IAAkB;IAC3B,IAAI,CAACxC,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,mBAAmB,EAAEkB,IAAI,CAACI,EAAE,CAAC;IAE7D,IAAI,CAACJ,IAAI,EAAE;MACT,OAAOvD,gDAAU,CAAC,MAAM,IAAI8C,KAAK,CAAC,mBAAmB,CAAC,CAAC;;IAGzD,IAAI,CAACC,YAAY,CAAC,YAAY,CAAC;IAE/B,OAAO,IAAI,CAACjC,MAAM,CACfoC,MAAM,CAAuB;MAC5BC,QAAQ,EAAE7C,0EAAoB;MAC9B8C,SAAS,EAAE;QACTJ,MAAM,EAAEO,IAAI,CAACI;QACb;;KAEH,CAAC,CACDN,IAAI,CACHpD,mDAAG,CAAEqD,MAAM,IAAI;MACb,MAAMM,YAAY,GAAGN,MAAM,CAACE,IAAI,EAAEE,UAAU;MAC5C,IAAI,CAACE,YAAY,EAAE,MAAM,IAAId,KAAK,CAAC,uBAAuB,CAAC;MAE3D,IAAI,CAACe,kBAAkB,CAACD,YAAY,CAAC;MACrC,OAAOA,YAAY;IACrB,CAAC,CAAC,EACF1D,0DAAU,CAAE7C,KAAK,IAAI;MACnB,IAAI,CAAC0D,MAAM,CAAC1D,KAAK,CAAC,aAAa,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MAChE,IAAI,CAAC0F,YAAY,CAAC,MAAM,CAAC;MACzB,OAAO/C,gDAAU,CAAC,MAAM3C,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAyG,UAAUA,CAACd,MAAc,EAAEe,MAAe;IACxC,IAAI,CAAChD,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,mBAAmB,EAAEW,MAAM,CAAC;IAE5D,IAAI,CAACD,YAAY,CAAC,QAAQ,CAAC;IAE3B,OAAO,IAAI,CAACjC,MAAM,CACfoC,MAAM,CAA8B;MACnCC,QAAQ,EAAE5C,0EAAoB;MAC9B6C,SAAS,EAAE;QAAEJ,MAAM;QAAEe,MAAM,EAAEA,MAAM,IAAI;MAAe;KACvD,CAAC,CACDV,IAAI,CACHpD,mDAAG,CAAEqD,MAAM,IAAI;MACb,MAAMU,OAAO,GAAGV,MAAM,CAACE,IAAI,EAAEM,UAAU;MACvC,IAAI,CAACE,OAAO,EAAE,MAAM,IAAIlB,KAAK,CAAC,uBAAuB,CAAC;MAEtD,IAAI,CAACmB,eAAe,EAAE;MACtB,OAAOD,OAAO;IAChB,CAAC,CAAC,EACF9D,0DAAU,CAAE7C,KAAK,IAAI;MACnB,IAAI,CAAC0D,MAAM,CAAC1D,KAAK,CAAC,aAAa,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MAChE,IAAI,CAAC4G,eAAe,EAAE,CAAC,CAAC;MACxB,OAAOjE,gDAAU,CAAC,MAAM3C,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAEA;;;EAGA6G,OAAOA,CAAClB,MAAc;IACpB,IAAI,CAACjC,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,iBAAiB,EAAEW,MAAM,CAAC;IAE1D,IAAI,CAACD,YAAY,CAAC,QAAQ,CAAC;IAE3B,OAAO,IAAI,CAACjC,MAAM,CACfoC,MAAM,CAA2B;MAChCC,QAAQ,EAAE3C,uEAAiB;MAC3B4C,SAAS,EAAE;QAAEJ;MAAM;KACpB,CAAC,CACDK,IAAI,CACHpD,mDAAG,CAAEqD,MAAM,IAAI;MACb,MAAMU,OAAO,GAAGV,MAAM,CAACE,IAAI,EAAEU,OAAO;MACpC,IAAI,CAACF,OAAO,EAAE,MAAM,IAAIlB,KAAK,CAAC,oBAAoB,CAAC;MAEnD,IAAI,CAACmB,eAAe,EAAE;MACtB,OAAOD,OAAO;IAChB,CAAC,CAAC,EACF9D,0DAAU,CAAE7C,KAAK,IAAI;MACnB,IAAI,CAAC0D,MAAM,CAAC1D,KAAK,CAAC,aAAa,EAAE,oBAAoB,EAAEA,KAAK,CAAC;MAC7D,IAAI,CAAC4G,eAAe,EAAE,CAAC,CAAC;MACxB,OAAOjE,gDAAU,CAAC,MAAM3C,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACL;EAEA;EAEA,IAAI8G,WAAWA,CAAA;IACb,OAAO,IAAI,CAACnD,UAAU,CAACoD,KAAK;EAC9B;EAEA,IAAIC,mBAAmBA,CAAA;IACrB,OAAO,IAAI,CAACpD,YAAY,CAACmD,KAAK;EAChC;EAEA,IAAIE,YAAYA,CAAA;IACd,OAAO,IAAI,CAAC9C,SAAS,KAAK,WAAW;EACvC;EAEA,IAAI+C,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAAC/C,SAAS,KAAK,MAAM;EAClC;EAEA;EAEQyB,cAAcA,CAAA;IACpB,OAAO,QAAQjH,IAAI,CAACwI,GAAG,EAAE,IAAIC,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;EACxE;EAEQ7B,YAAYA,CAAC8B,KAA4B;IAC/C,IAAI,CAAC9D,MAAM,CAAC+D,KAAK,CACf,aAAa,EACb,eAAe,IAAI,CAACtD,SAAS,MAAMqD,KAAK,EAAE,CAC3C;IACD,IAAI,CAACrD,SAAS,GAAGqD,KAAK;EACxB;EAEQpB,mBAAmBA,CAACF,IAAU;IACpC,IAAI,CAACxC,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,8BAA8B,EAAEkB,IAAI,CAACI,EAAE,CAAC;IACxE,IAAI,CAACpC,aAAa,GAAGgC,IAAI,CAACI,EAAE;IAC5B,IAAI,CAAC3C,UAAU,CAAC/D,IAAI,CAACsG,IAAI,CAAC;IAC1B,IAAI,CAACR,YAAY,CAAC,SAAS,CAAC;IAC5B,IAAI,CAACgC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAC3B,IAAI,CAACC,sBAAsB,CAACzB,IAAI,CAAC0B,IAAI,CAAC;EACxC;EAEQpB,kBAAkBA,CAACN,IAAU;IACnC,IAAI,CAACxC,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,6BAA6B,EAAEkB,IAAI,CAACI,EAAE,CAAC;IACvE,IAAI,CAAC3C,UAAU,CAAC/D,IAAI,CAACsG,IAAI,CAAC;IAC1B,IAAI,CAACtC,YAAY,CAAChE,IAAI,CAAC,IAAI,CAAC;IAC5B,IAAI,CAAC8F,YAAY,CAAC,WAAW,CAAC;IAC9B,IAAI,CAACmC,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACH,IAAI,CAAC,gBAAgB,CAAC;EAC7B;EAEQd,eAAeA,CAAA;IACrB,IAAI,CAAClD,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,yBAAyB,CAAC;IAC1D,IAAI,CAACU,YAAY,CAAC,MAAM,CAAC;IACzB,IAAI,CAACxB,aAAa,GAAG,IAAI;IACzB,IAAI,CAACP,UAAU,CAAC/D,IAAI,CAAC,IAAI,CAAC;IAC1B,IAAI,CAACgE,YAAY,CAAChE,IAAI,CAAC,IAAI,CAAC;IAC5B,IAAI,CAACkI,aAAa,EAAE;IACpB,IAAI,CAACJ,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACK,aAAa,EAAE;EACtB;EAEQC,kBAAkBA,CAAC9B,IAAkB;IAC3C,IAAI,CAACxC,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,yBAAyB,EAAEkB,IAAI,CAACI,EAAE,CAAC;IACnE,IAAI,CAACpC,aAAa,GAAGgC,IAAI,CAACI,EAAE;IAC5B,IAAI,CAAC1C,YAAY,CAAChE,IAAI,CAACsG,IAAI,CAAC;IAC5B,IAAI,CAACR,YAAY,CAAC,SAAS,CAAC;IAC5B,IAAI,CAACgC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAC3B,IAAI,CAACO,sBAAsB,CAAC/B,IAAI,CAAC;EACnC;EAEQgC,sBAAsBA,CAAChC,IAAU;IACvC,IAAI,CAACxC,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,sBAAsB,EAAEkB,IAAI,CAAC/H,MAAM,CAAC;IAEpE,IAAI+H,IAAI,CAACI,EAAE,KAAK,IAAI,CAACpC,aAAa,EAAE;MAClC,IAAI,CAACP,UAAU,CAAC/D,IAAI,CAACsG,IAAI,CAAC;MAE1B,QAAQA,IAAI,CAAC/H,MAAM;QACjB,KAAK4E,6DAAU,CAACoF,SAAS;UACvB,IAAI,CAACzC,YAAY,CAAC,WAAW,CAAC;UAC9B,IAAI,CAACmC,IAAI,CAAC,UAAU,CAAC;UACrB,IAAI,CAACH,IAAI,CAAC,gBAAgB,CAAC;UAC3B;QACF,KAAK3E,6DAAU,CAACqF,KAAK;QACrB,KAAKrF,6DAAU,CAACsF,QAAQ;UACtB,IAAI,CAACzB,eAAe,EAAE;UACtB;;;EAGR;EAEQ0B,gBAAgBA,CAACC,MAAkB;IACzC,IAAI,CAAC7E,MAAM,CAAC+D,KAAK,CAAC,aAAa,EAAE,uBAAuB,EAAEc,MAAM,CAACX,IAAI,CAAC;IACtE,IAAI,CAAC/D,WAAW,CAACjE,IAAI,CAAC2I,MAAM,CAAC;IAC7B;EACF;EAEA;EAEQtD,gBAAgBA,CAAA;IACtB,IAAI,CAACvB,MAAM,CAAC+D,KAAK,CAAC,aAAa,EAAE,wBAAwB,CAAC;IAC1D,IAAI,CAACe,qBAAqB,EAAE;EAC9B;EAEQA,qBAAqBA,CAAA;IAC3B,IAAI,CAACC,oBAAoB,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC;IAC/D,IAAI,CAACA,oBAAoB,CACvB,gBAAgB,EAChB,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EACxB,GAAG,EACH,KAAK,CACN;IACD,IAAI,CAACA,oBAAoB,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC;EAC1E;EAEQA,oBAAoBA,CAC1BC,IAAY,EACZC,WAAqB,EACrBC,QAAgB,EAChBC,IAAa;IAEb,IAAI;MACF,MAAMC,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAC1CD,MAAc,CAACE,kBAAkB,EAAC,CAAE;MACvC,MAAMC,UAAU,GAAGJ,YAAY,CAACI,UAAU;MAC1C,MAAMC,UAAU,GAAGD,UAAU,GAAGN,QAAQ;MACxC,MAAMQ,MAAM,GAAGN,YAAY,CAACO,YAAY,CAAC,CAAC,EAAEF,UAAU,EAAED,UAAU,CAAC;MACnE,MAAMI,WAAW,GAAGF,MAAM,CAACG,cAAc,CAAC,CAAC,CAAC;MAE5C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,UAAU,EAAEK,CAAC,EAAE,EAAE;QACnC,IAAIC,MAAM,GAAG,CAAC;QACdd,WAAW,CAACe,OAAO,CAAEC,IAAI,IAAI;UAC3B,MAAMC,SAAS,GAAG,GAAG,GAAGjB,WAAW,CAAC5I,MAAM;UAC1C,MAAM8J,KAAK,GAAIL,CAAC,GAAGN,UAAU,GAAIS,IAAI,GAAG,CAAC,GAAGvC,IAAI,CAAC0C,EAAE;UACnDL,MAAM,IAAIrC,IAAI,CAAC2C,GAAG,CAACF,KAAK,CAAC,GAAGD,SAAS;QACvC,CAAC,CAAC;QACF,MAAMI,QAAQ,GAAG5C,IAAI,CAAC2C,GAAG,CAAEP,CAAC,GAAGL,UAAU,GAAI/B,IAAI,CAAC0C,EAAE,CAAC;QACrDR,WAAW,CAACE,CAAC,CAAC,GAAGC,MAAM,GAAGO,QAAQ;;MAGpC,MAAMC,KAAK,GAAG,IAAIC,KAAK,EAAE;MACzBD,KAAK,CAACpB,IAAI,GAAGA,IAAI;MAChBoB,KAAa,CAACE,UAAU,GAAG,MAAK;QAC/B,MAAMC,MAAM,GAAGtB,YAAY,CAACuB,kBAAkB,EAAE;QAChDD,MAAM,CAAChB,MAAM,GAAGA,MAAM;QACtBgB,MAAM,CAACvB,IAAI,GAAGA,IAAI;QAClBuB,MAAM,CAACE,OAAO,CAACxB,YAAY,CAACyB,WAAW,CAAC;QACxCH,MAAM,CAACI,KAAK,EAAE;QACd,IAAI,CAAC3B,IAAI,EAAE;UACTtI,UAAU,CAAC,MAAK;YACd,IAAI,CAAC8D,SAAS,CAACqE,IAAI,CAAC,GAAG,KAAK;UAC9B,CAAC,EAAEE,QAAQ,GAAG,IAAI,CAAC;;QAErB,OAAOwB,MAAM;MACf,CAAC;MAED,IAAI,CAAChG,MAAM,CAACsE,IAAI,CAAC,GAAGuB,KAAK;MACzB,IAAI,CAAC5F,SAAS,CAACqE,IAAI,CAAC,GAAG,KAAK;KAC7B,CAAC,OAAO1I,KAAK,EAAE;MACd,IAAI,CAAC0D,MAAM,CAAC1D,KAAK,CACf,aAAa,EACb,yBAAyB0I,IAAI,IAAI,EACjC1I,KAAK,CACN;;EAEL;EAEQkF,uBAAuBA,CAAA;IAC7B,IAAI,CAACxB,MAAM,CAAC+D,KAAK,CAAC,aAAa,EAAE,+BAA+B,CAAC;IACjE,IAAI,CAACgD,wBAAwB,EAAE;IAC/B,IAAI,CAACC,4BAA4B,EAAE;IACnC,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEQF,wBAAwBA,CAAA;IAC9B,IAAI,CAAChH,MAAM,CACRzE,SAAS,CAAiC;MACzC4L,KAAK,EAAExH,gFAA0B;MACjCyH,WAAW,EAAE;KACd,CAAC,CACD7L,SAAS,CAAC;MACTY,IAAI,EAAEA,CAAC;QAAEuG,IAAI;QAAE2E;MAAM,CAAE,KAAI;QACzB,IAAI3E,IAAI,EAAEvC,YAAY,EAAE;UACtB,IAAI,CAACoE,kBAAkB,CAAC7B,IAAI,CAACvC,YAAY,CAAC;;QAE5C,IAAIkH,MAAM,EAAE;UACV,IAAI,CAACpH,MAAM,CAAC1D,KAAK,CACf,aAAa,EACb,oCAAoC,EACpC8K,MAAM,CACP;;MAEL,CAAC;MACD9K,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC0D,MAAM,CAAC1D,KAAK,CACf,aAAa,EACb,sCAAsC,EACtCA,KAAK,CACN;QACDO,UAAU,CAAC,MAAM,IAAI,CAACkK,wBAAwB,EAAE,EAAE,IAAI,CAAC;MACzD;KACD,CAAC;EACN;EAEQC,4BAA4BA,CAAA;IAClC,IAAI,CAACjH,MAAM,CACRzE,SAAS,CAA8B;MACtC4L,KAAK,EAAEvH,sFAAgC;MACvCwH,WAAW,EAAE;KACd,CAAC,CACD7L,SAAS,CAAC;MACTY,IAAI,EAAEA,CAAC;QAAEuG,IAAI;QAAE2E;MAAM,CAAE,KAAI;QACzB,IAAI3E,IAAI,EAAE4E,iBAAiB,EAAE;UAC3B,IAAI,CAAC7C,sBAAsB,CAAC/B,IAAI,CAAC4E,iBAAiB,CAAC;;QAErD,IAAID,MAAM,EAAE;UACV,IAAI,CAACpH,MAAM,CAAC1D,KAAK,CACf,aAAa,EACb,kCAAkC,EAClC8K,MAAM,CACP;;MAEL,CAAC;MACD9K,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC0D,MAAM,CAAC1D,KAAK,CACf,aAAa,EACb,oCAAoC,EACpCA,KAAK,CACN;QACDO,UAAU,CAAC,MAAM,IAAI,CAACmK,4BAA4B,EAAE,EAAE,IAAI,CAAC;MAC7D;KACD,CAAC;EACN;EAEQC,sBAAsBA,CAAA;IAC5B,IAAI,CAAClH,MAAM,CACRzE,SAAS,CAA6B;MACrC4L,KAAK,EAAEtH,8EAAwB;MAC/BuH,WAAW,EAAE;KACd,CAAC,CACD7L,SAAS,CAAC;MACTY,IAAI,EAAEA,CAAC;QAAEuG,IAAI;QAAE2E;MAAM,CAAE,KAAI;QACzB,IAAI3E,IAAI,EAAE6E,UAAU,EAAE;UACpB,IAAI,CAAC1C,gBAAgB,CAACnC,IAAI,CAAC6E,UAAU,CAAC;;QAExC,IAAIF,MAAM,EAAE;UACV,IAAI,CAACpH,MAAM,CAAC1D,KAAK,CACf,aAAa,EACb,kCAAkC,EAClC8K,MAAM,CACP;;MAEL,CAAC;MACD9K,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC0D,MAAM,CAAC1D,KAAK,CACf,aAAa,EACb,oCAAoC,EACpCA,KAAK,CACN;QACDO,UAAU,CAAC,MAAM,IAAI,CAACoK,sBAAsB,EAAE,EAAE,IAAI,CAAC;MACvD;KACD,CAAC;EACN;EAEQxF,gBAAgBA,CAAA;IACtB,IAAI,CAACzB,MAAM,CAAC+D,KAAK,CAAC,aAAa,EAAE,wBAAwB,CAAC;IAC1D,IAAI,CAACwD,oBAAoB,EAAE;EAC7B;EAEA;EAEQvD,IAAIA,CAACgB,IAAY,EAAEG,IAAA,GAAgB,KAAK;IAC9C,IAAI;MACF,MAAMqC,KAAK,GAAG,IAAI,CAAC9G,MAAM,CAACsE,IAAI,CAAC;MAC/B,IAAI,CAACwC,KAAK,IAAI,IAAI,CAAC7G,SAAS,CAACqE,IAAI,CAAC,EAAE;MAEpC,IAAKwC,KAAa,CAACf,UAAU,EAAE;QAC5Be,KAAa,CAACC,aAAa,GAAID,KAAa,CAACf,UAAU,EAAE;QAC1D,IAAI,CAAC9F,SAAS,CAACqE,IAAI,CAAC,GAAG,IAAI;;KAE9B,CAAC,OAAO1I,KAAK,EAAE;MACd,IAAI,CAAC0D,MAAM,CAAC1D,KAAK,CAAC,aAAa,EAAE,wBAAwB0I,IAAI,IAAI,EAAE1I,KAAK,CAAC;;EAE7E;EAEQ6H,IAAIA,CAACa,IAAY;IACvB,IAAI;MACF,MAAMwC,KAAK,GAAG,IAAI,CAAC9G,MAAM,CAACsE,IAAI,CAAC;MAC/B,IAAI,CAACwC,KAAK,IAAI,CAAC,IAAI,CAAC7G,SAAS,CAACqE,IAAI,CAAC,EAAE;MAErC,IAAKwC,KAAa,CAACC,aAAa,EAAE;QAC/BD,KAAa,CAACC,aAAa,CAACtD,IAAI,EAAE;QAClCqD,KAAa,CAACC,aAAa,GAAG,IAAI;;MAErC,IAAI,CAAC9G,SAAS,CAACqE,IAAI,CAAC,GAAG,KAAK;KAC7B,CAAC,OAAO1I,KAAK,EAAE;MACd,IAAI,CAAC0D,MAAM,CAAC1D,KAAK,CACf,aAAa,EACb,yBAAyB0I,IAAI,IAAI,EACjC1I,KAAK,CACN;;EAEL;EAEQ8H,aAAaA,CAAA;IACnBsD,MAAM,CAACC,IAAI,CAAC,IAAI,CAACjH,MAAM,CAAC,CAACsF,OAAO,CAAEhB,IAAI,IAAK,IAAI,CAACb,IAAI,CAACa,IAAI,CAAC,CAAC;EAC7D;EAEA;EAEQuC,oBAAoBA,CAAA;IAC1B,IAAI;MACF,IAAI,CAAC3G,cAAc,GAAG,IAAIgH,iBAAiB,CAAC,IAAI,CAACzG,SAAS,CAAC;MAC3D,IAAI,CAACnB,MAAM,CAAC+D,KAAK,CAAC,aAAa,EAAE,qCAAqC,CAAC;MAEvE,IAAI,CAACnD,cAAc,CAACiH,cAAc,GAAIC,KAAK,IAAI;QAC7C,IAAIA,KAAK,CAACC,SAAS,IAAI,IAAI,CAACvH,aAAa,EAAE;UACzC,IAAI,CAACwH,UAAU,CAAC,eAAe,EAAEC,IAAI,CAACC,SAAS,CAACJ,KAAK,CAACC,SAAS,CAAC,CAAC;;MAErE,CAAC;MAED,IAAI,CAACnH,cAAc,CAACuH,OAAO,GAAIL,KAAK,IAAI;QACtC,IAAI,CAAC9H,MAAM,CAACsB,IAAI,CACd,aAAa,EACb,wBAAwB,EACxBwG,KAAK,CAACM,KAAK,CAACC,IAAI,CACjB;QACD,IAAI,CAACvH,YAAY,GAAGgH,KAAK,CAACQ,OAAO,CAAC,CAAC,CAAC;QACpC,IAAI,CAACC,kBAAkB,EAAE;MAC3B,CAAC;MAED,IAAI,CAAC3H,cAAc,CAAC4H,uBAAuB,GAAG,MAAK;QACjD,MAAM1E,KAAK,GAAG,IAAI,CAAClD,cAAc,EAAE6H,eAAe;QAClD,IAAI,CAACzI,MAAM,CAAC+D,KAAK,CAAC,aAAa,EAAE,2BAA2B,EAAED,KAAK,CAAC;QAEpE,IAAIA,KAAK,KAAK,WAAW,EAAE;UACzB,IAAI,CAAC9D,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,iCAAiC,CAAC;UAClE,IAAI,CAACU,YAAY,CAAC,WAAW,CAAC;SAC/B,MAAM,IAAI8B,KAAK,KAAK,QAAQ,EAAE;UAC7B,IAAI,CAAC9D,MAAM,CAAC1D,KAAK,CAAC,aAAa,EAAE,4BAA4B,CAAC;UAC9D,IAAI,CAAC4G,eAAe,EAAE;;MAE1B,CAAC;KACF,CAAC,OAAO5G,KAAK,EAAE;MACd,IAAI,CAAC0D,MAAM,CAAC1D,KAAK,CAAC,aAAa,EAAE,gCAAgC,EAAEA,KAAK,CAAC;;EAE7E;EAEc2H,sBAAsBA,CAACpC,QAAkB;IAAA,IAAAlG,KAAA;IAAA,OAAAC,kKAAA;MACrD,IAAI;QACFD,KAAI,CAACqE,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,iCAAiC,CAAC;QAClE,MAAMoH,MAAM,SAAS/M,KAAI,CAACgN,YAAY,CAAC9G,QAAQ,CAAC;QAChDlG,KAAI,CAACiN,8BAA8B,CAACF,MAAM,CAAC;QAC3C/M,KAAI,CAACkN,iBAAiB,EAAE;OACzB,CAAC,OAAOvM,KAAK,EAAE;QACdX,KAAI,CAACqE,MAAM,CAAC1D,KAAK,CACf,aAAa,EACb,qCAAqC,EACrCA,KAAK,CACN;;IACF;EACH;EAEciI,sBAAsBA,CAAC/B,IAAkB;IAAA,IAAAsG,MAAA;IAAA,OAAAlN,kKAAA;MACrD,IAAI;QACFkN,MAAI,CAAC9I,MAAM,CAAC+D,KAAK,CAAC,aAAa,EAAE,oCAAoC,CAAC;QACtE,IAAI,CAAC+E,MAAI,CAAClI,cAAc,EAAE;UACxBkI,MAAI,CAACvB,oBAAoB,EAAE;;QAE7B,MAAMmB,MAAM,SAASI,MAAI,CAACH,YAAY,CAACnG,IAAI,CAAC0B,IAAI,CAAC;QACjD4E,MAAI,CAACF,8BAA8B,CAACF,MAAM,CAAC;OAC5C,CAAC,OAAOpM,KAAK,EAAE;QACdwM,MAAI,CAAC9I,MAAM,CAAC1D,KAAK,CACf,aAAa,EACb,oCAAoC,EACpCA,KAAK,CACN;;IACF;EACH;EAEcqM,YAAYA,CAAC9G,QAAkB;IAAA,IAAAkH,MAAA;IAAA,OAAAnN,kKAAA;MAC3C,MAAMoN,WAAW,GAA2B;QAC1CzC,KAAK,EAAE,IAAI;QACX0C,KAAK,EAAEpH,QAAQ,KAAKzC,2DAAQ,CAAC8J;OAC9B;MAED,IAAI;QACF,MAAMR,MAAM,SAASS,SAAS,CAACC,YAAY,CAACT,YAAY,CAACK,WAAW,CAAC;QACrED,MAAI,CAAClI,WAAW,GAAG6H,MAAM;QACzB,OAAOA,MAAM;OACd,CAAC,OAAOpM,KAAK,EAAE;QACdyM,MAAI,CAAC/I,MAAM,CAAC1D,KAAK,CAAC,aAAa,EAAE,2BAA2B,EAAEA,KAAK,CAAC;QACpE,MAAMA,KAAK;;IACZ;EACH;EAEQsM,8BAA8BA,CAACF,MAAmB;IACxD,IAAI,CAAC,IAAI,CAAC9H,cAAc,EAAE;IAE1B8H,MAAM,CAACW,SAAS,EAAE,CAACrD,OAAO,CAAEoC,KAAK,IAAI;MACnC,IAAI,CAACxH,cAAe,CAAC0I,QAAQ,CAAClB,KAAK,EAAEM,MAAM,CAAC;IAC9C,CAAC,CAAC;EACJ;EAEQG,iBAAiBA,CAAA;IACvB,IAAI,IAAI,CAAC9H,iBAAiB,IAAI,IAAI,CAACF,WAAW,EAAE;MAC9C,IAAI,CAACE,iBAAiB,CAACwI,SAAS,GAAG,IAAI,CAAC1I,WAAW;;EAEvD;EAEQ0H,kBAAkBA,CAAA;IACxB,IAAI,IAAI,CAACvH,kBAAkB,IAAI,IAAI,CAACF,YAAY,EAAE;MAChD,IAAI,CAACE,kBAAkB,CAACuI,SAAS,GAAG,IAAI,CAACzI,YAAY;;EAEzD;EAEQkH,UAAUA,CAACwB,UAAkB,EAAEC,UAAkB;IACvD,IAAI,CAAC,IAAI,CAACjJ,aAAa,EAAE;IAEzB,IAAI,CAACT,MAAM,CACRoC,MAAM,CAAC;MACNC,QAAQ,EAAEvC,+EAAyB;MACnCwC,SAAS,EAAE;QACTJ,MAAM,EAAE,IAAI,CAACzB,aAAa;QAC1BgJ,UAAU;QACVC;;KAEH,CAAC,CACDnO,SAAS,CAAC;MACTY,IAAI,EAAEA,CAAA,KACJ,IAAI,CAAC8D,MAAM,CAAC+D,KAAK,CAAC,aAAa,EAAE,cAAc,EAAEyF,UAAU,CAAC;MAC9DlN,KAAK,EAAGA,KAAK,IACX,IAAI,CAAC0D,MAAM,CAAC1D,KAAK,CAAC,aAAa,EAAE,uBAAuB,EAAEA,KAAK;KAClE,CAAC;EACN;EAEA;EAEQ+H,aAAaA,CAAA;IACnB,IAAI,CAACrE,MAAM,CAAC+D,KAAK,CAAC,aAAa,EAAE,8BAA8B,CAAC;IAEhE,IAAI,IAAI,CAAClD,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACwI,SAAS,EAAE,CAACrD,OAAO,CAAEoC,KAAK,IAAKA,KAAK,CAACjE,IAAI,EAAE,CAAC;MAC7D,IAAI,CAACtD,WAAW,GAAG,IAAI;;IAGzB,IAAI,IAAI,CAACC,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,GAAG,IAAI;;IAG1B,IAAI,IAAI,CAACF,cAAc,EAAE;MACvB,IAAI,CAACA,cAAc,CAAC8I,KAAK,EAAE;MAC3B,IAAI,CAAC9I,cAAc,GAAG,IAAI;;IAG5B,IAAI,IAAI,CAACG,iBAAiB,EAAE;MAC1B,IAAI,CAACA,iBAAiB,CAACwI,SAAS,GAAG,IAAI;;IAGzC,IAAI,IAAI,CAACvI,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAACuI,SAAS,GAAG,IAAI;;IAG1C;IACA,IAAI,CAAChC,oBAAoB,EAAE;EAC7B;EAEQ7F,OAAOA,CAAA;IACb,IAAI,CAAC0C,aAAa,EAAE;IACpB,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACpE,UAAU,CAAC0J,QAAQ,EAAE;IAC1B,IAAI,CAACzJ,YAAY,CAACyJ,QAAQ,EAAE;IAC5B,IAAI,CAACxJ,WAAW,CAACwJ,QAAQ,EAAE;EAC7B;EAEA;EAEA;;;EAGAC,mBAAmBA,CACjBC,UAA4B,EAC5BC,WAA6B;IAE7B,IAAI,CAAC/I,iBAAiB,GAAG8I,UAAU;IACnC,IAAI,CAAC7I,kBAAkB,GAAG8I,WAAW;IAErC,IAAI,IAAI,CAACjJ,WAAW,EAAE;MACpB,IAAI,CAACgI,iBAAiB,EAAE;;IAE1B,IAAI,IAAI,CAAC/H,YAAY,EAAE;MACrB,IAAI,CAACyH,kBAAkB,EAAE;;EAE7B;EAEA;;;EAGAwB,WAAWA,CAAA;IACT,IAAI,CAAC9I,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C,IAAI,IAAI,CAACJ,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACmJ,cAAc,EAAE,CAAChE,OAAO,CAAEoC,KAAK,IAAI;QAClDA,KAAK,CAAC6B,OAAO,GAAG,IAAI,CAAChJ,cAAc;MACrC,CAAC,CAAC;;IAEJ,OAAO,IAAI,CAACA,cAAc;EAC5B;EAEA;;;EAGAiJ,WAAWA,CAAA;IACT,IAAI,CAAChJ,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C,IAAI,IAAI,CAACL,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACsJ,cAAc,EAAE,CAACnE,OAAO,CAAEoC,KAAK,IAAI;QAClDA,KAAK,CAAC6B,OAAO,GAAG,IAAI,CAAC/I,cAAc;MACrC,CAAC,CAAC;;IAEJ,OAAO,IAAI,CAACA,cAAc;EAC5B;EAEA;;;EAGAkJ,gBAAgBA,CACdP,UAA4B,EAC5BC,WAA6B;IAE7B,IAAI,CAACF,mBAAmB,CAACC,UAAU,EAAEC,WAAW,CAAC;EACnD;EAEA;;;EAGA,IAAIO,YAAYA,CAAA;IACd,OAAO,IAAI,CAACpJ,cAAc;EAC5B;EAEA;;;EAGA,IAAIqJ,YAAYA,CAAA;IACd,OAAO,IAAI,CAACpJ,cAAc;EAC5B;EAEA;;;EAGA,IAAIqJ,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAAC1J,WAAW;EACzB;EAEA;;;EAGA,IAAI2J,iBAAiBA,CAAA;IACnB,OAAO,IAAI,CAAC1J,YAAY;EAC1B;EAEA;;;EAGA2J,YAAYA,CAAA;IACV,IAAI,CAACzK,MAAM,CAAC+D,KAAK,CACf,aAAa,EACb,8CAA8C,CAC/C;EACH;EAEA;;;EAGA2G,aAAaA,CAAA;IACX,IAAI,CAAC1K,MAAM,CAAC+D,KAAK,CAAC,aAAa,EAAE,kBAAkB,CAAC;IACpD,IAAI,CAACK,aAAa,EAAE;EACtB;;;uBApwBWtE,WAAW,EAAA/C,sDAAA,CAAAE,kDAAA,GAAAF,sDAAA,CAAAI,0DAAA;IAAA;EAAA;;;aAAX2C,WAAW;MAAAgL,OAAA,EAAXhL,WAAW,CAAAiL,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClBoD;AAShD;AAWkB;;;;;;;;;;;;;;;ICMhCjO,4DAAA,WAA2C;IACzCA,oDAAA,GACF;IAAAA,0DAAA,EAAO;;;;IADLA,uDAAA,GACF;IADEA,gEAAA,MAAAwO,MAAA,CAAAC,oBAAA,CAAAC,YAAA,kBAAAF,MAAA,CAAAC,oBAAA,CAAAC,YAAA,CAAApP,MAAA,mBACF;;;;;IACAU,4DAAA,WACG;IAAAA,oDAAA,eAAQ;IAAAA,0DAAA,EACV;;;;;IACDA,4DAAA,WACG;IAAAA,oDAAA,iBAAU;IAAAA,0DAAA,EACZ;;;;;;IAOLA,4DAAA,iBAKC;IAHCA,wDAAA,mBAAA2O,sEAAA;MAAA3O,2DAAA,CAAA6O,IAAA;MAAA,MAAAC,OAAA,GAAA9O,2DAAA;MAAA,OAASA,yDAAA,CAAA8O,OAAA,CAAAG,cAAA,EAAgB;IAAA,EAAC;IAI1BjP,uDAAA,YAA4B;IAC9BA,0DAAA,EAAS;;;;;;IAGTA,4DAAA,iBAKC;IAHCA,wDAAA,mBAAAkP,sEAAA;MAAAlP,2DAAA,CAAAmP,IAAA;MAAA,MAAAC,OAAA,GAAApP,2DAAA;MAAA,OAASA,yDAAA,CAAAoP,OAAA,CAAAC,cAAA,EAAgB;IAAA,EAAC;IAI1BrP,uDAAA,YAA4B;IAC9BA,0DAAA,EAAS;;;;;IAcXA,4DAAA,cAAwD;IACtDA,uDAAA,cAEO;IACTA,0DAAA,EAAM;;;;;IASJA,uDAAA,cAKE;;;;IAHAA,wDAAA,QAAAsP,WAAA,CAAAC,MAAA,CAAAC,KAAA,yCAAAxP,2DAAA,CAAmE,QAAAsP,WAAA,CAAAC,MAAA,CAAAG,QAAA;;;;;IAYnE1P,4DAAA,cAGC;IACCA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAAsP,WAAA,CAAAC,MAAA,kBAAAD,WAAA,CAAAC,MAAA,CAAAG,QAAA,MACF;;;;;IAGA1P,4DAAA,cAAwD;IAEpDA,oDAAA,GACF;IAAAA,0DAAA,EAAM;IACNA,4DAAA,cAA4C;IAC1CA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IAJJA,uDAAA,GACF;IADEA,gEAAA,0BAAAsP,WAAA,CAAAK,OAAA,CAAAJ,MAAA,kBAAAD,WAAA,CAAAK,OAAA,CAAAJ,MAAA,CAAAG,QAAA,MACF;IAEE1P,uDAAA,GACF;IADEA,gEAAA,MAAAsP,WAAA,CAAAK,OAAA,CAAAC,OAAA,MACF;;;;;IAMA5P,4DAAA,cAA2D;IACzDA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAAsP,WAAA,CAAAM,OAAA,MACF;;;;;IAUE5P,4DAAA,cAAuD;IACrDA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAAsP,WAAA,CAAAM,OAAA,MACF;;;;;;IATF5P,4DAAA,cAA6D;IAKzDA,wDAAA,mBAAA6P,sEAAA;MAAA7P,2DAAA,CAAA8P,IAAA;MAAA,MAAAR,WAAA,GAAAtP,2DAAA,GAAA+P,SAAA;MAAA,MAAAC,OAAA,GAAAhQ,2DAAA;MAAA,OAASA,yDAAA,CAAAgQ,OAAA,CAAAC,eAAA,CAAAX,WAAA,CAAAY,WAAA,kBAAAZ,WAAA,CAAAY,WAAA,CAAsC,CAAC,EAAE;IAAA,EAAC;IAJrDlQ,0DAAA,EAKE;IACFA,wDAAA,IAAAoQ,sDAAA,kBAEM;IACRpQ,0DAAA,EAAM;;;;IARFA,uDAAA,GAAqC;IAArCA,wDAAA,QAAAsP,WAAA,CAAAY,WAAA,kBAAAZ,WAAA,CAAAY,WAAA,qBAAAZ,WAAA,CAAAY,WAAA,IAAAG,GAAA,EAAArQ,2DAAA,CAAqC,QAAAsP,WAAA,CAAAY,WAAA,kBAAAZ,WAAA,CAAAY,WAAA,qBAAAZ,WAAA,CAAAY,WAAA,IAAAjI,IAAA;IAKjCjI,uDAAA,GAAqB;IAArBA,wDAAA,SAAAsP,WAAA,CAAAM,OAAA,CAAqB;;;;;;IAM7B5P,4DAAA,cAA2D;IACzDA,uDAAA,YAAqC;IACrCA,4DAAA,cAAuB;IACEA,oDAAA,GAAoC;IAAAA,0DAAA,EAAM;IACjEA,4DAAA,cAAuB;IACrBA,oDAAA,GACF;IAAAA,0DAAA,EAAM;IAERA,4DAAA,iBAGC;IADCA,wDAAA,mBAAAsQ,yEAAA;MAAAtQ,2DAAA,CAAAuQ,IAAA;MAAA,MAAAjB,WAAA,GAAAtP,2DAAA,GAAA+P,SAAA;MAAA,MAAAS,OAAA,GAAAxQ,2DAAA;MAAA,OAASA,yDAAA,CAAAwQ,OAAA,CAAAC,YAAA,CAAAnB,WAAA,CAAAY,WAAA,kBAAAZ,WAAA,CAAAY,WAAA,CAAmC,CAAC,EAAE;IAAA,EAAC;IAEhDlQ,uDAAA,YAA+B;IACjCA,0DAAA,EAAS;;;;;IAVgBA,uDAAA,GAAoC;IAApCA,+DAAA,CAAAsP,WAAA,CAAAY,WAAA,kBAAAZ,WAAA,CAAAY,WAAA,qBAAAZ,WAAA,CAAAY,WAAA,IAAAjI,IAAA,CAAoC;IAEzDjI,uDAAA,GACF;IADEA,gEAAA,MAAA0Q,OAAA,CAAAC,cAAA,CAAArB,WAAA,CAAAY,WAAA,kBAAAZ,WAAA,CAAAY,WAAA,qBAAAZ,WAAA,CAAAY,WAAA,IAAAU,IAAA,OACF;;;;;;IAWJ5Q,4DAAA,cAAqE;IACpCA,wDAAA,mBAAA6Q,yEAAA;MAAA7Q,2DAAA,CAAA8Q,IAAA;MAAA,MAAAxB,WAAA,GAAAtP,2DAAA,GAAA+P,SAAA;MAAA,MAAAgB,OAAA,GAAA/Q,2DAAA;MAAA,OAASA,yDAAA,CAAA+Q,OAAA,CAAAC,gBAAA,CAAA1B,WAAA,CAAyB;IAAA,EAAC;IAChEtP,uDAAA,YAA8C;IAChDA,0DAAA,EAAS;IACTA,4DAAA,cAA4B;IAC1BA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAAiR,OAAA,CAAAC,cAAA,CAAA5B,WAAA,CAAAY,WAAA,kBAAAZ,WAAA,CAAAY,WAAA,qBAAAZ,WAAA,CAAAY,WAAA,IAAA/H,QAAA,OACF;;;;;IAUAnI,4DAAA,cAAuD;IACrDA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAAsP,WAAA,CAAAM,OAAA,MACF;;;;;IARF5P,4DAAA,cAA6D;IAC3DA,uDAAA,gBAIS;IACTA,wDAAA,IAAAmR,uDAAA,kBAEM;IACRnR,0DAAA,EAAM;;;;IAPFA,uDAAA,GAAqC;IAArCA,wDAAA,QAAAsP,WAAA,CAAAY,WAAA,kBAAAZ,WAAA,CAAAY,WAAA,qBAAAZ,WAAA,CAAAY,WAAA,IAAAG,GAAA,EAAArQ,2DAAA,CAAqC;IAIjCA,uDAAA,GAAqB;IAArBA,wDAAA,SAAAsP,WAAA,CAAAM,OAAA,CAAqB;;;;;;IAW7B5P,4DAAA,eAIC;IADCA,wDAAA,mBAAAoR,+EAAA;MAAA,MAAAC,WAAA,GAAArR,2DAAA,CAAAsR,IAAA;MAAA,MAAAC,YAAA,GAAAF,WAAA,CAAAtB,SAAA;MAAA,MAAAT,WAAA,GAAAtP,2DAAA,IAAA+P,SAAA;MAAA,MAAAyB,OAAA,GAAAxR,2DAAA;MAAA,OAASA,yDAAA,CAAAwR,OAAA,CAAAC,cAAA,CAAAnC,WAAA,EAAAiC,YAAA,CAAAG,KAAA,CAAuC;IAAA,EAAC;IAEjD1R,oDAAA,GACF;IAAAA,0DAAA,EAAO;;;;IADLA,uDAAA,GACF;IADEA,gEAAA,MAAAuR,YAAA,CAAAG,KAAA,OAAAH,YAAA,CAAAK,KAAA,MACF;;;;;IAVF5R,4DAAA,cAGC;IACCA,wDAAA,IAAA6R,wDAAA,mBAMO;IACT7R,0DAAA,EAAM;;;;IANmBA,uDAAA,GAAoB;IAApBA,wDAAA,YAAAsP,WAAA,CAAAwC,SAAA,CAAoB;;;;;IAsBzC9R,uDAAA,YAAsD;;;;;IACtDA,uDAAA,YAAmE;;;;;IACnEA,uDAAA,YAGK;;;;;IACLA,uDAAA,YAGK;;;;;IAhBPA,4DAAA,cAMC;IACCA,wDAAA,IAAA+R,qDAAA,gBAAsD;IACtD/R,wDAAA,IAAAgS,qDAAA,gBAAmE;IACnEhS,wDAAA,IAAAiS,qDAAA,gBAGK;IACLjS,wDAAA,IAAAkS,qDAAA,gBAGK;IACPlS,0DAAA,EAAM;;;;IAdJA,yDAAA,SAAAsP,WAAA,CAAA6C,MAAA,CAA6B,YAAA7C,WAAA,CAAA8C,SAAA,WAAA9C,WAAA,CAAA+C,OAAA;IAIzBrS,uDAAA,GAAuB;IAAvBA,wDAAA,SAAAsP,WAAA,CAAA8C,SAAA,CAAuB;IACvBpS,uDAAA,GAAqB;IAArBA,wDAAA,SAAAsP,WAAA,CAAA+C,OAAA,CAAqB;IAEtBrS,uDAAA,GAA8D;IAA9DA,wDAAA,UAAAsP,WAAA,CAAA8C,SAAA,KAAA9C,WAAA,CAAA+C,OAAA,IAAA/C,WAAA,CAAA6C,MAAA,CAA8D;IAI9DnS,uDAAA,GAA+D;IAA/DA,wDAAA,UAAAsP,WAAA,CAAA8C,SAAA,KAAA9C,WAAA,CAAA+C,OAAA,KAAA/C,WAAA,CAAA6C,MAAA,CAA+D;;;;;;IApI1EnS,4DAAA,cAIC;IAECA,wDAAA,IAAAsS,gDAAA,kBAKE;IAGFtS,4DAAA,cAIC;IAECA,wDAAA,IAAAuS,gDAAA,kBAKM;IAGNvS,wDAAA,IAAAwS,gDAAA,kBAOM;IAGNxS,4DAAA,cAA+B;IAE7BA,wDAAA,IAAAyS,gDAAA,kBAEM;IAGNzS,wDAAA,IAAA0S,gDAAA,kBAUM;IAGN1S,wDAAA,IAAA2S,gDAAA,kBAcM;IAGN3S,wDAAA,IAAA4S,gDAAA,kBAOM;IAGN5S,wDAAA,KAAA6S,iDAAA,kBASM;IACR7S,0DAAA,EAAM;IAGNA,wDAAA,KAAA8S,iDAAA,kBAWM;IAGN9S,4DAAA,eAAoD;IAEhDA,oDAAA,IACF;IAAAA,0DAAA,EAAO;IAGPA,wDAAA,KAAA+S,iDAAA,mBAiBM;IACR/S,0DAAA,EAAM;IAGNA,4DAAA,eAEC;IAGGA,wDAAA,mBAAAgT,oEAAA;MAAA,MAAA3B,WAAA,GAAArR,2DAAA,CAAAiT,IAAA;MAAA,MAAA3D,WAAA,GAAA+B,WAAA,CAAAtB,SAAA;MAAA,MAAAmD,OAAA,GAAAlT,2DAAA;MAAA,OAASA,yDAAA,CAAAkT,OAAA,CAAAC,eAAA,CAAA7D,WAAA,CAAwB;IAAA,EAAC;IAElCtP,uDAAA,aAAyC;IAC3CA,0DAAA,EAAS;;;;;IAhJbA,yDAAA,eAAAoT,MAAA,CAAAC,WAAA,CAAA/D,WAAA,EAAyC;IAItCtP,uDAAA,GAA6C;IAA7CA,wDAAA,UAAAoT,MAAA,CAAAC,WAAA,CAAA/D,WAAA,KAAAA,WAAA,CAAAC,MAAA,CAA6C;IAS9CvP,uDAAA,GAAyC;IAAzCA,yDAAA,eAAAoT,MAAA,CAAAC,WAAA,CAAA/D,WAAA,EAAyC,mBAAA8D,MAAA,CAAAC,WAAA,CAAA/D,WAAA;IAKtCtP,uDAAA,GAA2D;IAA3DA,wDAAA,SAAAoT,MAAA,CAAA3E,oBAAA,CAAA6E,OAAA,KAAAF,MAAA,CAAAC,WAAA,CAAA/D,WAAA,EAA2D;IAOxDtP,uDAAA,GAAqB;IAArBA,wDAAA,SAAAsP,WAAA,CAAAK,OAAA,CAAqB;IAUtB3P,uDAAA,GAAyB;IAAzBA,wDAAA,aAAAsP,WAAA,CAAAnI,IAAA,CAAyB;IAEtBnH,uDAAA,GAA8B;IAA9BA,wDAAA,iBAAAoT,MAAA,CAAA7E,WAAA,CAAAgF,IAAA,CAA8B;IAK9BvT,uDAAA,GAA+B;IAA/BA,wDAAA,iBAAAoT,MAAA,CAAA7E,WAAA,CAAAiF,KAAA,CAA+B;IAa/BxT,uDAAA,GAA8B;IAA9BA,wDAAA,iBAAAoT,MAAA,CAAA7E,WAAA,CAAAkF,IAAA,CAA8B;IAiB9BzT,uDAAA,GAAuC;IAAvCA,wDAAA,iBAAAoT,MAAA,CAAA7E,WAAA,CAAAmF,aAAA,CAAuC;IAUvC1T,uDAAA,GAA+B;IAA/BA,wDAAA,iBAAAoT,MAAA,CAAA7E,WAAA,CAAApC,KAAA,CAA+B;IAcpCnM,uDAAA,GAAuD;IAAvDA,wDAAA,SAAAsP,WAAA,CAAAwC,SAAA,IAAAxC,WAAA,CAAAwC,SAAA,CAAAxS,MAAA,KAAuD;IAetDU,uDAAA,GACF;IADEA,gEAAA,MAAAoT,MAAA,CAAAO,iBAAA,CAAArE,WAAA,CAAAsE,SAAA,OACF;IAIG5T,uDAAA,GAA0B;IAA1BA,wDAAA,SAAAoT,MAAA,CAAAC,WAAA,CAAA/D,WAAA,EAA0B;;;;;IAkCnCtP,4DAAA,cAA6D;IAEzDA,uDAAA,cAA8B;IAGhCA,0DAAA,EAAM;IACNA,4DAAA,WAAM;IAAAA,oDAAA,GAAqB;IAAAA,0DAAA,EAAO;;;;IAA5BA,uDAAA,GAAqB;IAArBA,+DAAA,CAAA6T,OAAA,CAAAC,aAAA,GAAqB;;;;;;IAS7B9T,4DAAA,cAA8C;IAItCA,oDAAA,GACF;IAAAA,0DAAA,EAAM;IACNA,4DAAA,eAAwB;IAAAA,oDAAA,GAAwB;IAAAA,0DAAA,EAAM;IAExDA,4DAAA,kBAAuE;IAA/DA,wDAAA,mBAAA+T,mEAAA;MAAA/T,2DAAA,CAAAgU,IAAA;MAAA,MAAAC,OAAA,GAAAjU,2DAAA;MAAA,OAASA,yDAAA,CAAAiU,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAC7BlU,uDAAA,aAA4B;IAC9BA,0DAAA,EAAS;;;;IANLA,uDAAA,GACF;IADEA,gEAAA,0BAAAmU,OAAA,CAAAC,UAAA,CAAA7E,MAAA,kBAAA4E,OAAA,CAAAC,UAAA,CAAA7E,MAAA,CAAAG,QAAA,MACF;IACwB1P,uDAAA,GAAwB;IAAxBA,+DAAA,CAAAmU,OAAA,CAAAC,UAAA,CAAAxE,OAAA,CAAwB;;;;;;IAStD5P,4DAAA,cAAkD;IAGPA,oDAAA,8BAAuB;IAAAA,0DAAA,EAAM;IAClEA,4DAAA,eAAwB;IAAAA,oDAAA,GAA4B;IAAAA,0DAAA,EAAM;IAE5DA,4DAAA,kBAGC;IAFCA,wDAAA,mBAAAqU,mEAAA;MAAArU,2DAAA,CAAAsU,IAAA;MAAA,MAAAC,OAAA,GAAAvU,2DAAA;MAAA,OAASA,yDAAA,CAAAuU,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAGzBxU,uDAAA,aAA4B;IAC9BA,0DAAA,EAAS;;;;IAPiBA,uDAAA,GAA4B;IAA5BA,+DAAA,CAAAyU,OAAA,CAAAC,cAAA,CAAA9E,OAAA,CAA4B;;;;;;IActD5P,4DAAA,eAGC;IACCA,uDAAA,aAAyC;IACzCA,4DAAA,gBAAmD;IAAAA,oDAAA,GAEjD;IAAAA,0DAAA,EAAO;IACTA,4DAAA,kBAGC;IAFCA,wDAAA,mBAAA2U,yEAAA;MAAA,MAAAtD,WAAA,GAAArR,2DAAA,CAAA4U,IAAA;MAAA,MAAAC,KAAA,GAAAxD,WAAA,CAAAyD,KAAA;MAAA,MAAAC,OAAA,GAAA/U,2DAAA;MAAA,OAASA,yDAAA,CAAA+U,OAAA,CAAAC,kBAAA,CAAAH,KAAA,CAAqB;IAAA,EAAC;IAG/B7U,uDAAA,aAAoC;IACtCA,0DAAA,EAAS;;;;IAR0CA,uDAAA,GAEjD;IAFiDA,+DAAA,CAAAiV,QAAA,CAAAhN,IAAA,CAEjD;;;;;IATRjI,4DAAA,eAAmD;IAE/CA,wDAAA,IAAAkV,gDAAA,mBAcM;IACRlV,0DAAA,EAAM;;;;IAdeA,uDAAA,GAAkB;IAAlBA,wDAAA,YAAAmV,OAAA,CAAAC,aAAA,CAAkB;;;;;;IAkBzCpV,4DAAA,eAA0D;IACxDA,uDAAA,aAA4C;IAC5CA,4DAAA,gBAA6B;IAAAA,oDAAA,GAE3B;IAAAA,0DAAA,EAAO;IACTA,4DAAA,kBAGC;IAFCA,wDAAA,mBAAAqV,mEAAA;MAAArV,2DAAA,CAAAsV,IAAA;MAAA,MAAAC,OAAA,GAAAvV,2DAAA;MAAA,OAASA,yDAAA,CAAAuV,OAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IAG9BxV,uDAAA,aAA2B;IAC7BA,0DAAA,EAAS;IACTA,4DAAA,kBAGC;IAFCA,wDAAA,mBAAAyV,mEAAA;MAAAzV,2DAAA,CAAAsV,IAAA;MAAA,MAAAI,OAAA,GAAA1V,2DAAA;MAAA,OAASA,yDAAA,CAAA0V,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAGhC3V,uDAAA,aAA4B;IAC9BA,0DAAA,EAAS;;;;IAdoBA,uDAAA,GAE3B;IAF2BA,+DAAA,CAAA4V,OAAA,CAAA1E,cAAA,CAAA0E,OAAA,CAAAC,iBAAA,EAE3B;;;;;;IA8BE7V,4DAAA,eAGC;IAEGA,wDAAA,mBAAA8V,mEAAA;MAAA9V,2DAAA,CAAA+V,IAAA;MAAA,MAAAC,OAAA,GAAAhW,2DAAA;MAAA,OAASA,yDAAA,CAAAgW,OAAA,CAAAC,gBAAA,EAAkB;IAAA,EAAC;IAG5BjW,uDAAA,aAAyC;IACzCA,4DAAA,gBAAiC;IAAAA,oDAAA,cAAO;IAAAA,0DAAA,EAAO;IAEjDA,4DAAA,kBAGC;IAFCA,wDAAA,mBAAAkW,mEAAA;MAAAlW,2DAAA,CAAA+V,IAAA;MAAA,MAAAI,OAAA,GAAAnW,2DAAA;MAAA,OAASA,yDAAA,CAAAmW,OAAA,CAAAF,gBAAA,EAAkB;IAAA,EAAC;IAG5BjW,uDAAA,aAA2C;IAC3CA,4DAAA,gBAAiC;IAAAA,oDAAA,YAAK;IAAAA,0DAAA,EAAO;;;;;;IA0BnDA,4DAAA,kBAOC;IAFCA,wDAAA,uBAAAoW,0EAAA;MAAApW,2DAAA,CAAAqW,IAAA;MAAA,MAAAC,OAAA,GAAAtW,2DAAA;MAAA,OAAaA,yDAAA,CAAAsW,OAAA,CAAAC,mBAAA,EAAqB;IAAA,EAAC;IAGnCvW,uDAAA,aAAiC;IACnCA,0DAAA,EAAS;;;;;;IAGTA,4DAAA,kBAMC;IAHCA,wDAAA,mBAAAwW,sEAAA;MAAAxW,2DAAA,CAAAyW,IAAA;MAAA,MAAAC,OAAA,GAAA1W,2DAAA;MAAA,OAASA,yDAAA,CAAA0W,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAIvB3W,uDAAA,aAA6C;IAC/CA,0DAAA,EAAS;;;;IAJPA,wDAAA,cAAA4W,OAAA,CAAAC,cAAA,GAA8B;;;;;;IA9YxC7W,4DAAA,aAAyD;IAMnDA,uDAAA,aAaE;IAEFA,4DAAA,aAA0B;IAEtBA,oDAAA,GAKF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,WAGC;IACCA,wDAAA,IAAA8W,0CAAA,kBAEO;IACP9W,wDAAA,IAAA+W,0CAAA,kBAEC;IACD/W,wDAAA,KAAAgX,2CAAA,kBAEC;IACHhX,0DAAA,EAAI;IAIRA,4DAAA,cAA0B;IAExBA,wDAAA,KAAAiX,6CAAA,qBAOS;IAGTjX,wDAAA,KAAAkX,6CAAA,qBAOS;IAGTlX,4DAAA,kBAA2C;IACzCA,uDAAA,aAAiC;IACnCA,0DAAA,EAAS;IAObA,4DAAA,mBAAmD;IAEjDA,wDAAA,KAAAmX,0CAAA,kBAIM;IAGNnX,wDAAA,KAAAoX,0CAAA,oBAsJM;IAGNpX,wDAAA,KAAAqX,0CAAA,kBAOM;IACRrX,0DAAA,EAAM;IAKNA,4DAAA,eAAqC;IAEnCA,wDAAA,KAAAsX,0CAAA,kBAYM;IAGNtX,wDAAA,KAAAuX,0CAAA,kBAaM;IAGNvX,wDAAA,KAAAwX,0CAAA,kBAkBM;IAGNxX,wDAAA,KAAAyX,0CAAA,kBAiBM;IAGNzX,4DAAA,eAA2B;IAOnBA,wDAAA,mBAAA0X,6DAAA;MAAA1X,2DAAA,CAAA2X,IAAA;MAAA,MAAAC,OAAA,GAAA5X,2DAAA;MAAA,OAASA,yDAAA,CAAA4X,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAGhC7X,uDAAA,aAAgC;IAClCA,0DAAA,EAAS;IAGTA,wDAAA,KAAA8X,0CAAA,kBAkBM;IACR9X,0DAAA,EAAM;IAGNA,4DAAA,kBAAsE;IAA5CA,wDAAA,mBAAA+X,6DAAA;MAAA/X,2DAAA,CAAA2X,IAAA;MAAA,MAAAK,OAAA,GAAAhY,2DAAA;MAAA,OAASA,yDAAA,CAAAgY,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IACrDjY,uDAAA,aAA4B;IAC9BA,0DAAA,EAAS;IAIXA,4DAAA,wBASC;IAPCA,wDAAA,2BAAAkY,uEAAAC,MAAA;MAAAnY,2DAAA,CAAA2X,IAAA;MAAA,MAAAS,OAAA,GAAApY,2DAAA;MAAA,OAAAA,yDAAA,CAAAoY,OAAA,CAAAC,cAAA,GAAAF,MAAA;IAAA,EAA4B,qBAAAG,iEAAAH,MAAA;MAAAnY,2DAAA,CAAA2X,IAAA;MAAA,MAAAY,OAAA,GAAAvY,2DAAA;MAAA,OACjBA,yDAAA,CAAAuY,OAAA,CAAAC,UAAA,CAAAL,MAAA,CAAkB;IAAA,EADD,mBAAAM,+DAAA;MAAAzY,2DAAA,CAAA2X,IAAA;MAAA,MAAAe,OAAA,GAAA1Y,2DAAA;MAAA,OAEnBA,yDAAA,CAAA0Y,OAAA,CAAAC,QAAA,EAAU;IAAA,EAFS;IAO7B3Y,0DAAA,EAAW;IAGZA,4DAAA,eAA2B;IAEzBA,wDAAA,KAAA4Y,6CAAA,qBASS;IAGT5Y,wDAAA,KAAA6Y,6CAAA,qBAQS;IACX7Y,0DAAA,EAAM;IAIRA,4DAAA,qBAOE;IAFAA,wDAAA,oBAAA8Y,6DAAAX,MAAA;MAAAnY,2DAAA,CAAA2X,IAAA;MAAA,MAAAoB,OAAA,GAAA/Y,2DAAA;MAAA,OAAUA,yDAAA,CAAA+Y,OAAA,CAAAC,cAAA,CAAAb,MAAA,CAAsB;IAAA,EAAC;IALnCnY,0DAAA,EAOE;;;;IA5YEA,uDAAA,GAAqE;IAArEA,yDAAA,YAAAiZ,MAAA,CAAAxK,oBAAA,CAAA6E,OAAA,IAAA2F,MAAA,CAAAC,iBAAA,GAAqE;IAXrElZ,wDAAA,QAAAiZ,MAAA,CAAAxK,oBAAA,CAAA6E,OAAA,GAAA2F,MAAA,CAAAxK,oBAAA,CAAA0K,UAAA,GAAAF,MAAA,CAAAG,kBAAA,IAAApZ,2DAAA,CAIC,QAAAiZ,MAAA,CAAAxK,oBAAA,CAAA6E,OAAA,GAAA2F,MAAA,CAAAxK,oBAAA,CAAA4K,SAAA,GAAAJ,MAAA,CAAAK,gBAAA;IAYCtZ,uDAAA,GAKF;IALEA,gEAAA,MAAAiZ,MAAA,CAAAxK,oBAAA,CAAA6E,OAAA,GAAA2F,MAAA,CAAAxK,oBAAA,CAAA4K,SAAA,GAAAJ,MAAA,CAAAK,gBAAA,QAKF;IAGEtZ,uDAAA,GAAqE;IAArEA,yDAAA,YAAAiZ,MAAA,CAAAxK,oBAAA,CAAA6E,OAAA,IAAA2F,MAAA,CAAAC,iBAAA,GAAqE;IAE9DlZ,uDAAA,GAAkC;IAAlCA,wDAAA,SAAAiZ,MAAA,CAAAxK,oBAAA,CAAA6E,OAAA,CAAkC;IAGlCtT,uDAAA,GAA0D;IAA1DA,wDAAA,UAAAiZ,MAAA,CAAAxK,oBAAA,CAAA6E,OAAA,IAAA2F,MAAA,CAAAC,iBAAA,GAA0D;IAG1DlZ,uDAAA,GAA2D;IAA3DA,wDAAA,UAAAiZ,MAAA,CAAAxK,oBAAA,CAAA6E,OAAA,KAAA2F,MAAA,CAAAC,iBAAA,GAA2D;IAanElZ,uDAAA,GAAmC;IAAnCA,wDAAA,UAAAiZ,MAAA,CAAAxK,oBAAA,CAAA6E,OAAA,CAAmC;IAUnCtT,uDAAA,GAAmC;IAAnCA,wDAAA,UAAAiZ,MAAA,CAAAxK,oBAAA,CAAA6E,OAAA,CAAmC;IAiBlCtT,uDAAA,GAAe;IAAfA,wDAAA,SAAAiZ,MAAA,CAAAM,SAAA,CAAe;IAQCvZ,uDAAA,GAAa;IAAbA,wDAAA,YAAAiZ,MAAA,CAAAO,QAAA,CAAa,iBAAAP,MAAA,CAAAQ,gBAAA;IAwJ7BzZ,uDAAA,GAA4B;IAA5BA,wDAAA,SAAAiZ,MAAA,CAAAS,WAAA,CAAApa,MAAA,KAA4B;IAe5BU,uDAAA,GAAgB;IAAhBA,wDAAA,SAAAiZ,MAAA,CAAA7E,UAAA,CAAgB;IAehBpU,uDAAA,GAAoB;IAApBA,wDAAA,SAAAiZ,MAAA,CAAAvE,cAAA,CAAoB;IAgBpB1U,uDAAA,GAA8B;IAA9BA,wDAAA,SAAAiZ,MAAA,CAAA7D,aAAA,CAAA9V,MAAA,KAA8B;IAqB9BU,uDAAA,GAAiB;IAAjBA,wDAAA,SAAAiZ,MAAA,CAAAU,WAAA,CAAiB;IAmCd3Z,uDAAA,GAAwB;IAAxBA,wDAAA,SAAAiZ,MAAA,CAAAW,kBAAA,CAAwB;IA6B7B5Z,uDAAA,GAA4B;IAA5BA,wDAAA,YAAAiZ,MAAA,CAAAZ,cAAA,CAA4B,aAAAY,MAAA,CAAAU,WAAA;IAazB3Z,uDAAA,GAEA;IAFAA,wDAAA,UAAAiZ,MAAA,CAAAZ,cAAA,CAAAwB,IAAA,OAAAZ,MAAA,CAAA7D,aAAA,CAAA9V,MAAA,KAAA2Z,MAAA,CAAAU,WAAA,CAEA;IAUA3Z,uDAAA,GAAmD;IAAnDA,wDAAA,SAAAiZ,MAAA,CAAAZ,cAAA,CAAAwB,IAAA,MAAAZ,MAAA,CAAA7D,aAAA,CAAA9V,MAAA,CAAmD;;;;;IA0B9DU,4DAAA,eAGC;IAOOA,uDAAA,aAAmD;IACrDA,0DAAA,EAAM;IACNA,4DAAA,cAA+C;IAAAA,oDAAA,yBAAkB;IAAAA,0DAAA,EAAK;IACtEA,4DAAA,aAAyB;IAAAA,oDAAA,oDAAwC;IAAAA,0DAAA,EAAI;IAIvEA,4DAAA,eAA4B;IAKtBA,uDAAA,cAA8C;IAChDA,0DAAA,EAAM;IACNA,4DAAA,WAAK;IACgCA,oDAAA,mCAAsB;IAAAA,0DAAA,EAAK;IAC9DA,4DAAA,cAAiC;IAC/BA,oDAAA,4DACF;IAAAA,0DAAA,EAAI;IAIRA,4DAAA,gBAAmD;IAI/CA,uDAAA,cAA+C;IACjDA,0DAAA,EAAM;IACNA,4DAAA,WAAK;IACgCA,oDAAA,+BAAkB;IAAAA,0DAAA,EAAK;IAC1DA,4DAAA,cAAiC;IAAAA,oDAAA,gDAA8B;IAAAA,0DAAA,EAAI;IAIvEA,4DAAA,gBAAmD;IAI/CA,uDAAA,cAA8C;IAChDA,0DAAA,EAAM;IACNA,4DAAA,WAAK;IACgCA,oDAAA,2BAAmB;IAAAA,0DAAA,EAAK;IAC3DA,4DAAA,cAAiC;IAAAA,oDAAA,wCAA2B;IAAAA,0DAAA,EAAI;IAMtEA,4DAAA,gBAAoE;IAChBA,oDAAA,2BAAmB;IAAAA,0DAAA,EAAK;IAC1EA,4DAAA,gBAA6C;IACxCA,oDAAA,iEAA+C;IAAAA,0DAAA,EAAI;IACtDA,4DAAA,SAAG;IAAAA,oDAAA,0EAAwD;IAAAA,0DAAA,EAAI;IAC/DA,4DAAA,SAAG;IAAAA,oDAAA,4DAA+C;IAAAA,0DAAA,EAAI;IAK1DA,uDAAA,yBAAuC;IACzCA,0DAAA,EAAM;;;ADxcF,MAAO8Z,oBAAoB;EAgD/Bzc,YACUC,cAA8B,EAC9Byc,WAAwB,EACxBtc,YAA0B,EAC1Buc,KAAqB,EACrBC,MAAc,EACdC,GAAsB,EACtBC,MAAc;IANd,KAAA7c,cAAc,GAAdA,cAAc;IACd,KAAAyc,WAAW,GAAXA,WAAW;IACX,KAAAtc,YAAY,GAAZA,YAAY;IACZ,KAAAuc,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,MAAM,GAANA,MAAM;IAjDhB;IACA,KAAAC,WAAW,GAAgB,IAAI;IAC/B,KAAA3L,oBAAoB,GAAwB,IAAI;IAChD,KAAA+K,QAAQ,GAAc,EAAE;IACxB,KAAAD,SAAS,GAAG,KAAK;IACjB,KAAAc,QAAQ,GAAG,KAAK;IAChB,KAAAX,WAAW,GAAW,EAAE;IAExB;IACA,KAAAY,WAAW,GAAG,CAAC;IACf,KAAAC,eAAe,GAAG,IAAI;IACtB,KAAAC,mBAAmB,GAAG,KAAK;IAE3B;IACA,KAAAnC,cAAc,GAAG,EAAE;IACnB,KAAAjD,aAAa,GAAW,EAAE;IAC1B,KAAAuE,WAAW,GAAG,KAAK;IACnB,KAAA9D,iBAAiB,GAAG,CAAC;IAErB;IACA,KAAA4E,eAAe,GAAG,KAAK;IACvB,KAAAb,kBAAkB,GAAG,KAAK;IAC1B,KAAAxF,UAAU,GAAmB,IAAI;IACjC,KAAAM,cAAc,GAAmB,IAAI;IAErC;IACA,KAAAgG,WAAW,GAAG,EAAE;IAChB,KAAAC,aAAa,GAAc,EAAE;IAC7B,KAAAC,iBAAiB,GAAG,KAAK;IAEzB;IACQ,KAAAC,aAAa,GAAmB,EAAE;IAI1C;IACQ,KAAAC,eAAe,GAAG,IAAI7Y,iDAAe,CAAgB,IAAI,CAAC;IAElE;IACS,KAAAsM,WAAW,GAAGA,8DAAW;IACzB,KAAAlM,QAAQ,GAAGA,2DAAQ;EAUzB;EAEHjE,QAAQA,CAAA;IACN,IAAI,CAAC2c,mBAAmB,EAAE;IAC1B,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAxc,WAAWA,CAAA;IACT,IAAI,CAACiG,OAAO,EAAE;EAChB;EAEA;EACA;EACA;EAEQoW,mBAAmBA,CAAA;IACzB;IACA,IAAI,CAACX,WAAW,GAAG,IAAI,CAACL,WAAW,CAACoB,cAAc,EAAE;IAEpD,IAAI,CAAC,IAAI,CAACf,WAAW,EAAE;MACrB,IAAI,CAACH,MAAM,CAACmB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAChC;;IAGF;IACA,IAAI,CAACpB,KAAK,CAACqB,MAAM,CAAC9c,SAAS,CAAE8c,MAAM,IAAI;MACrC,MAAMtW,cAAc,GAAGsW,MAAM,CAAC,gBAAgB,CAAC;MAC/C,IAAItW,cAAc,EAAE;QAClB,IAAI,CAAC+V,eAAe,CAAC3b,IAAI,CAAC4F,cAAc,CAAC;;IAE7C,CAAC,CAAC;EACJ;EAEQiW,kBAAkBA,CAAA;IACxB;IACA,MAAMM,eAAe,GAAG,IAAI,CAACR,eAAe,CACzCvV,IAAI,CACH+I,sDAAM,CAAEzI,EAAE,IAAK,CAAC,CAACA,EAAE,CAAC,EACpBsI,oEAAoB,EAAE,EACtBE,oDAAG,CAAC,MAAK;MACP,IAAI,CAACkL,SAAS,GAAG,IAAI;MACrB,IAAI,CAACC,QAAQ,GAAG,EAAE;MAClB,IAAI,CAACc,WAAW,GAAG,CAAC;MACpB,IAAI,CAACC,eAAe,GAAG,IAAI;IAC7B,CAAC,CAAC,EACFnM,0DAAS,CAAErJ,cAAc,IACvB,IAAI,CAACzH,cAAc,CAACie,eAAe,CAACxW,cAAe,EAAE,EAAE,EAAE,CAAC,CAAC,CAC5D,EACD3C,2DAAU,CAAE7C,KAAK,IAAI;MACnBic,OAAO,CAACjc,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACrE,IAAI,CAAC9B,YAAY,CAAC+B,SAAS,CACzB,8CAA8C,CAC/C;MACD,OAAO0O,yCAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH,CACA3P,SAAS,CAAEkd,YAAY,IAAI;MAC1B,IAAI,CAAClC,SAAS,GAAG,KAAK;MACtB,IAAIkC,YAAY,EAAE;QAChB,IAAI,CAAChN,oBAAoB,GAAGgN,YAAY;QACxC,IAAI,CAACjC,QAAQ,GAAGiC,YAAY,CAACjC,QAAQ,IAAI,EAAE;QAC3C,IAAI,CAAC0B,cAAc,EAAE;QACrB,IAAI,CAACQ,kBAAkB,EAAE;;MAE3B,IAAI,CAACxB,GAAG,CAACyB,aAAa,EAAE;IAC1B,CAAC,CAAC;IAEJ;IACA,MAAMC,WAAW,GAAG,IAAI,CAACte,cAAc,CACpCue,mBAAmB,EAAE,CACrBtd,SAAS,CAAEud,OAAO,IAAI;MACrB,IACEA,OAAO,IACP,IAAI,CAACrN,oBAAoB,IACzBqN,OAAO,CAAC/W,cAAc,KAAK,IAAI,CAAC0J,oBAAoB,CAAC5I,EAAE,EACvD;QACA,IAAI,CAACkW,aAAa,CAACD,OAAO,CAAC;QAC3B,IAAI,CAACZ,cAAc,EAAE;QACrB,IAAI,CAACc,iBAAiB,CAACF,OAAO,CAAC;;IAEnC,CAAC,CAAC;IAEJ;IACA,MAAMG,SAAS,GAAG,IAAI,CAAC3e,cAAc,CAClC4e,2BAA2B,EAAE,CAC7B3d,SAAS,CAAEwM,KAAK,IAAI;MACnB,IACEA,KAAK,IACL,IAAI,CAAC0D,oBAAoB,IACzB1D,KAAK,CAAChG,cAAc,KAAK,IAAI,CAAC0J,oBAAoB,CAAC5I,EAAE,EACrD;QACA,IAAI,CAACsW,qBAAqB,CAACpR,KAAK,CAAC;;IAErC,CAAC,CAAC;IAEJ,IAAI,CAAC8P,aAAa,CAACuB,IAAI,CAACd,eAAe,EAAEM,WAAW,EAAEK,SAAS,CAAC;EAClE;EAEQtX,OAAOA,CAAA;IACb,IAAI,CAACkW,aAAa,CAAC5R,OAAO,CAAEoT,GAAG,IAAKA,GAAG,CAAC1d,WAAW,EAAE,CAAC;IACtD,IAAI,IAAI,CAAC2d,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;IAElC,IAAI,IAAI,CAACE,iBAAiB,EAAE;MAC1BC,aAAa,CAAC,IAAI,CAACD,iBAAiB,CAAC;;IAEvC,IAAI,CAACE,UAAU,EAAE;EACnB;EAEA;EACA;EACA;EAEA/F,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACE,cAAc,EAAE,EAAE;MAC1B;;IAGF,MAAMjH,OAAO,GAAG,IAAI,CAACyI,cAAc,CAACwB,IAAI,EAAE;IAC1C,MAAM8C,KAAK,GAAG,IAAI,CAACvH,aAAa;IAEhC;IACA,IAAI,CAACiD,cAAc,GAAG,EAAE;IACxB,IAAI,CAACjD,aAAa,GAAG,EAAE;IACvB,IAAI,CAAChB,UAAU,GAAG,IAAI;IACtB,IAAI,CAACsI,UAAU,EAAE;IAEjB,IAAI,IAAI,CAAChI,cAAc,EAAE;MACvB,IAAI,CAACkI,aAAa,CAAChN,OAAO,CAAC;MAC3B;;IAGF;IACA,IAAIA,OAAO,IAAI+M,KAAK,CAACrd,MAAM,GAAG,CAAC,EAAE;MAC/B,IAAI,CAACud,cAAc,CAACjN,OAAO,EAAE+M,KAAK,CAAC;;EAEvC;EAEA9F,cAAcA,CAAA;IACZ,MAAMiG,UAAU,GAAG,IAAI,CAACzE,cAAc,CAACwB,IAAI,EAAE,CAACva,MAAM,GAAG,CAAC;IACxD,MAAMyd,QAAQ,GAAG,IAAI,CAAC3H,aAAa,CAAC9V,MAAM,GAAG,CAAC;IAC9C,MAAM0d,eAAe,GAAG,CAAC,CAAC,IAAI,CAACvO,oBAAoB;IAEnD,OAAOuO,eAAe,KAAKF,UAAU,IAAIC,QAAQ,CAAC;EACpD;EAEQF,cAAcA,CAACjN,OAAe,EAAE+M,KAAa;IACnD,IAAI,CAAC,IAAI,CAAClO,oBAAoB,IAAI,CAAC,IAAI,CAAC2L,WAAW,EAAE;IAErD,MAAMvV,WAAW,GAAG,IAAI,CAACoY,cAAc,EAAE;IACzC,IAAI,CAACpY,WAAW,EAAE;IAElB;IACA,MAAMqY,WAAW,GAAY;MAC3BrX,EAAE,EAAE,QAAQ3H,IAAI,CAACwI,GAAG,EAAE,EAAE;MACxBkJ,OAAO;MACPzI,IAAI,EACFwV,KAAK,CAACrd,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC6d,kBAAkB,CAACR,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGpO,8DAAW,CAACgF,IAAI;MACzEK,SAAS,EAAE,IAAI1V,IAAI,EAAE;MACrBqR,MAAM,EAAE,IAAI,CAAC6K,WAAW;MACxBhI,SAAS,EAAE,IAAI;MACfrN,cAAc,EAAE,IAAI,CAAC0J,oBAAoB,CAAC5I;KAC3C;IAED,IAAI,CAACkW,aAAa,CAACmB,WAAW,CAAC;IAC/B,IAAI,CAAChC,cAAc,EAAE;IAErB;IACA,MAAMkC,cAAc,GAClBT,KAAK,CAACrd,MAAM,GAAG,CAAC,GACZ,IAAI,CAAChC,cAAc,CAACqZ,WAAW,CAAC9R,WAAW,EAAE+K,OAAO,EAAE+M,KAAK,CAAC,CAAC,CAAC,CAAC,GAC/D,IAAI,CAACrf,cAAc,CAACqZ,WAAW,CAAC9R,WAAW,EAAE+K,OAAO,CAAC;IAE3DwN,cAAc,CAAC7e,SAAS,CAAC;MACvBY,IAAI,EAAGke,WAAW,IAAI;QACpB,IAAI,CAACC,uBAAuB,CAACJ,WAAW,CAACrX,EAAG,EAAEwX,WAAW,CAAC;QAC1D,IAAI,CAAC5f,YAAY,CAAC4B,WAAW,CAAC,gBAAgB,CAAC;MACjD,CAAC;MACDE,KAAK,EAAGA,KAAK,IAAI;QACfic,OAAO,CAACjc,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1D,IAAI,CAACge,kBAAkB,CAACL,WAAW,CAACrX,EAAG,CAAC;QACxC,IAAI,CAACpI,YAAY,CAAC+B,SAAS,CAAC,mCAAmC,CAAC;MAClE;KACD,CAAC;EACJ;EAEQod,aAAaA,CAACY,UAAkB;IACtC,IAAI,CAAC,IAAI,CAAC9I,cAAc,EAAE;IAE1B,IAAI,CAACpX,cAAc,CAChBmgB,WAAW,CAAC,IAAI,CAAC/I,cAAc,CAAC7O,EAAG,EAAE2X,UAAU,CAAC,CAChDjf,SAAS,CAAC;MACTY,IAAI,EAAGue,cAAc,IAAI;QACvB,IAAI,CAACC,mBAAmB,CAACD,cAAc,CAAC;QACxC,IAAI,CAAChJ,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACjX,YAAY,CAAC4B,WAAW,CAAC,iBAAiB,CAAC;MAClD,CAAC;MACDE,KAAK,EAAGA,KAAK,IAAI;QACfic,OAAO,CAACjc,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;QAClE,IAAI,CAAC9B,YAAY,CAAC+B,SAAS,CACzB,2CAA2C,CAC5C;MACH;KACD,CAAC;EACN;EAEAoe,aAAaA,CAAC9B,OAAgB;IAC5B,IAAI,CAACA,OAAO,CAACjW,EAAE,IAAI,CAAC,IAAI,CAACgY,gBAAgB,CAAC/B,OAAO,CAAC,EAAE;IAEpD,IAAIgC,OAAO,CAAC,iDAAiD,CAAC,EAAE;MAC9D,IAAI,CAACxgB,cAAc,CAACsgB,aAAa,CAAC9B,OAAO,CAACjW,EAAE,CAAC,CAACtH,SAAS,CAAC;QACtDY,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAAC4e,qBAAqB,CAACjC,OAAO,CAACjW,EAAG,CAAC;UACvC,IAAI,CAACpI,YAAY,CAAC4B,WAAW,CAAC,kBAAkB,CAAC;QACnD,CAAC;QACDE,KAAK,EAAGA,KAAK,IAAI;UACfic,OAAO,CAACjc,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;UACjE,IAAI,CAAC9B,YAAY,CAAC+B,SAAS,CACzB,0CAA0C,CAC3C;QACH;OACD,CAAC;;EAEN;EAEAiS,cAAcA,CAACqK,OAAgB,EAAEpK,KAAa;IAC5C,IAAI,CAACoK,OAAO,CAACjW,EAAE,EAAE;IAEjB,IAAI,CAACvI,cAAc,CAACmU,cAAc,CAACqK,OAAO,CAACjW,EAAE,EAAE6L,KAAK,CAAC,CAACnT,SAAS,CAAC;MAC9DY,IAAI,EAAGue,cAAc,IAAI;QACvB,IAAI,CAACC,mBAAmB,CAACD,cAAc,CAAC;MAC1C,CAAC;MACDne,KAAK,EAAGA,KAAK,IAAI;QACfic,OAAO,CAACjc,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAC9D,IAAI,CAAC9B,YAAY,CAAC+B,SAAS,CAAC,4BAA4B,CAAC;MAC3D;KACD,CAAC;EACJ;EAEA;EACA;EACA;EAEAwZ,cAAcA,CAACjO,KAAU;IACvB,MAAM4R,KAAK,GAAG5R,KAAK,CAACiT,MAAM,CAACrB,KAAK;IAChC,IAAIA,KAAK,IAAIA,KAAK,CAACrd,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAI,CAAC8V,aAAa,GAAG6I,KAAK,CAACC,IAAI,CAACvB,KAAK,CAAC;MACtC,IAAI,CAAC/C,kBAAkB,GAAG,KAAK;MAE/B;MACA,IAAI,IAAI,CAACvB,cAAc,CAACwB,IAAI,EAAE,KAAK,EAAE,EAAE;QACrC,IAAI,CAAClD,WAAW,EAAE;;;EAGxB;EAEA3B,kBAAkBA,CAACF,KAAa;IAC9B,IAAI,CAACM,aAAa,CAAC+I,MAAM,CAACrJ,KAAK,EAAE,CAAC,CAAC;EACrC;EAEAmB,gBAAgBA,CAAA;IACd,IAAI,CAACmI,SAAS,CAACC,aAAa,CAACC,KAAK,EAAE;EACtC;EAEA;EACA;EACA;EAEM/H,mBAAmBA,CAAA;IAAA,IAAA3X,KAAA;IAAA,OAAAC,kKAAA;MACvB,IAAI;QACF,MAAM8M,MAAM,SAASS,SAAS,CAACC,YAAY,CAACT,YAAY,CAAC;UAAEpC,KAAK,EAAE;QAAI,CAAE,CAAC;QACzE5K,KAAI,CAAC+a,WAAW,GAAG,IAAI;QACvB/a,KAAI,CAACiX,iBAAiB,GAAG,CAAC;QAE1B;QACAjX,KAAI,CAAC4d,iBAAiB,GAAG+B,WAAW,CAAC,MAAK;UACxC3f,KAAI,CAACiX,iBAAiB,EAAE;QAC1B,CAAC,EAAE,IAAI,CAAC;QAER;QACA;OACD,CAAC,OAAOtW,KAAK,EAAE;QACdic,OAAO,CAACjc,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7DX,KAAI,CAACnB,YAAY,CAAC+B,SAAS,CAAC,oCAAoC,CAAC;;IAClE;EACH;EAEAgW,kBAAkBA,CAAA;IAChB,IAAI,CAACmE,WAAW,GAAG,KAAK;IACxB,IAAI,IAAI,CAAC6C,iBAAiB,EAAE;MAC1BC,aAAa,CAAC,IAAI,CAACD,iBAAiB,CAAC;;IAGvC;IACA;EACF;;EAEA7G,oBAAoBA,CAAA;IAClB,IAAI,CAACgE,WAAW,GAAG,KAAK;IACxB,IAAI,CAAC9D,iBAAiB,GAAG,CAAC;IAC1B,IAAI,IAAI,CAAC2G,iBAAiB,EAAE;MAC1BC,aAAa,CAAC,IAAI,CAACD,iBAAiB,CAAC;;EAEzC;EAEA;EACA;EACA;EAEAvN,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACR,oBAAoB,EAAE;IAEhC,MAAM5J,WAAW,GAAG,IAAI,CAACoY,cAAc,EAAE;IACzC,IAAI,CAACpY,WAAW,EAAE;IAElB,IAAI,CAACvH,cAAc,CAACsH,YAAY,CAACC,WAAW,EAAExC,2DAAQ,CAACmc,KAAK,CAAC,CAACjgB,SAAS,CAAC;MACtEY,IAAI,EAAGsG,IAAI,IAAI;QACb,IAAI,CAAChI,YAAY,CAAC4B,WAAW,CAAC,oBAAoB,CAAC;QACnD;MACF,CAAC;;MACDE,KAAK,EAAGA,KAAK,IAAI;QACfic,OAAO,CAACjc,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QAC/D,IAAI,CAAC9B,YAAY,CAAC+B,SAAS,CAAC,wBAAwB,CAAC;MACvD;KACD,CAAC;EACJ;EAEA6P,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACZ,oBAAoB,EAAE;IAEhC,MAAM5J,WAAW,GAAG,IAAI,CAACoY,cAAc,EAAE;IACzC,IAAI,CAACpY,WAAW,EAAE;IAElB,IAAI,CAACvH,cAAc,CAACsH,YAAY,CAACC,WAAW,EAAExC,2DAAQ,CAAC8J,KAAK,CAAC,CAAC5N,SAAS,CAAC;MACtEY,IAAI,EAAGsG,IAAI,IAAI;QACb,IAAI,CAAChI,YAAY,CAAC4B,WAAW,CAAC,oBAAoB,CAAC;QACnD;MACF,CAAC;;MACDE,KAAK,EAAGA,KAAK,IAAI;QACfic,OAAO,CAACjc,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;QACrE,IAAI,CAAC9B,YAAY,CAAC+B,SAAS,CAAC,8BAA8B,CAAC;MAC7D;KACD,CAAC;EACJ;EAEA;EACA;EACA;EAEAmZ,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAAClK,oBAAoB,IAAI,IAAI,CAAC4L,QAAQ,EAAE;IAEjD,IAAI,CAACA,QAAQ,GAAG,IAAI;IACpB,IAAI,CAAC/c,cAAc,CAACmhB,WAAW,CAAC,IAAI,CAAChQ,oBAAoB,CAAC5I,EAAG,CAAC,CAACtH,SAAS,EAAE;IAE1E;IACA,IAAI,IAAI,CAAC+d,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;IAGlC,IAAI,CAACA,aAAa,GAAGxc,UAAU,CAAC,MAAK;MACnC,IAAI,CAAC4c,UAAU,EAAE;IACnB,CAAC,EAAE,IAAI,CAAC;EACV;EAEAA,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACrC,QAAQ,IAAI,CAAC,IAAI,CAAC5L,oBAAoB,EAAE;IAElD,IAAI,CAAC4L,QAAQ,GAAG,KAAK;IACrB,IAAI,CAAC/c,cAAc,CAACof,UAAU,CAAC,IAAI,CAACjO,oBAAoB,CAAC5I,EAAG,CAAC,CAACtH,SAAS,EAAE;IAEzE,IAAI,IAAI,CAAC+d,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;EAEpC;EAEA;EACA;EACA;EAEQW,cAAcA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACxO,oBAAoB,IAAI,CAAC,IAAI,CAAC2L,WAAW,EAAE,OAAO,IAAI;IAEhE,MAAM1L,YAAY,GAAG,IAAI,CAACD,oBAAoB,CAACC,YAAY,IAAI,EAAE;IACjE,MAAMgQ,aAAa,GAAG,IAAI,CAACtE,WAAW,CAACvU,EAAE,IAAI,IAAI,CAACuU,WAAW,CAACuE,GAAG;IAEjE,MAAMC,SAAS,GAAGlQ,YAAY,CAACmQ,IAAI,CAChCC,CAAC,IAAK,CAACA,CAAC,CAACjZ,EAAE,IAAIiZ,CAAC,CAACH,GAAG,MAAMD,aAAa,CACzC;IAED,OAAOE,SAAS,GAAGA,SAAS,CAAC/Y,EAAE,IAAI+Y,SAAS,CAACD,GAAI,GAAG,IAAI;EAC1D;EAEQxB,kBAAkBA,CAAC4B,IAAU;IACnC,MAAM5X,IAAI,GAAG4X,IAAI,CAAC5X,IAAI,CAAC6X,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACpC,QAAQ7X,IAAI;MACV,KAAK,OAAO;QACV,OAAOoH,8DAAW,CAACiF,KAAK;MAC1B,KAAK,OAAO;QACV,OAAOjF,8DAAW,CAACpC,KAAK;MAC1B,KAAK,OAAO;QACV,OAAOoC,8DAAW,CAACiQ,KAAK;MAC1B;QACE,OAAOjQ,8DAAW,CAACkF,IAAI;;EAE7B;EAEQsI,aAAaA,CAACD,OAAgB;IACpC,IAAI,CAACtC,QAAQ,CAAC4C,IAAI,CAACN,OAAO,CAAC;IAC3B,IAAI,CAAC5B,GAAG,CAACyB,aAAa,EAAE;EAC1B;EAEQ2B,uBAAuBA,CAAC2B,MAAc,EAAEC,WAAoB;IAClE,MAAMpK,KAAK,GAAG,IAAI,CAAC0E,QAAQ,CAAC2F,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAACvZ,EAAE,KAAKoZ,MAAM,CAAC;IAC7D,IAAInK,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,IAAI,CAAC0E,QAAQ,CAAC1E,KAAK,CAAC,GAAGoK,WAAW;MAClC,IAAI,CAAChF,GAAG,CAACyB,aAAa,EAAE;;EAE5B;EAEQ4B,kBAAkBA,CAAC8B,SAAiB;IAC1C,MAAMvD,OAAO,GAAG,IAAI,CAACtC,QAAQ,CAACqF,IAAI,CAAEO,CAAC,IAAKA,CAAC,CAACvZ,EAAE,KAAKwZ,SAAS,CAAC;IAC7D,IAAIvD,OAAO,EAAE;MACXA,OAAO,CAAC1J,SAAS,GAAG,KAAK;MACzB0J,OAAO,CAACzJ,OAAO,GAAG,IAAI;MACtB,IAAI,CAAC6H,GAAG,CAACyB,aAAa,EAAE;;EAE5B;EAEQgC,mBAAmBA,CAACD,cAAuB;IACjD,MAAM5I,KAAK,GAAG,IAAI,CAAC0E,QAAQ,CAAC2F,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAACvZ,EAAE,KAAK6X,cAAc,CAAC7X,EAAE,CAAC;IACxE,IAAIiP,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,IAAI,CAAC0E,QAAQ,CAAC1E,KAAK,CAAC,GAAG4I,cAAc;MACrC,IAAI,CAACxD,GAAG,CAACyB,aAAa,EAAE;;EAE5B;EAEQoC,qBAAqBA,CAACsB,SAAiB;IAC7C,IAAI,CAAC7F,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAClL,MAAM,CAAE8Q,CAAC,IAAKA,CAAC,CAACvZ,EAAE,KAAKwZ,SAAS,CAAC;IAC/D,IAAI,CAACnF,GAAG,CAACyB,aAAa,EAAE;EAC1B;EAEQkC,gBAAgBA,CAAC/B,OAAgB;IACvC,IAAI,CAAC,IAAI,CAAC1B,WAAW,IAAI,CAAC0B,OAAO,CAACvM,MAAM,EAAE,OAAO,KAAK;IAEtD,MAAMmP,aAAa,GAAG,IAAI,CAACtE,WAAW,CAACvU,EAAE,IAAI,IAAI,CAACuU,WAAW,CAACuE,GAAG;IACjE,MAAMW,QAAQ,GAAGxD,OAAO,CAACvM,MAAM,CAAC1J,EAAE,IAAIiW,OAAO,CAACvM,MAAM,CAACoP,GAAG;IAExD,OAAOD,aAAa,KAAKY,QAAQ;EACnC;EAEQnD,qBAAqBA,CAACpR,KAAU;IACtC,IAAI,CAAC,IAAI,CAACqP,WAAW,EAAE;IAEvB,MAAMsE,aAAa,GAAG,IAAI,CAACtE,WAAW,CAACvU,EAAE,IAAI,IAAI,CAACuU,WAAW,CAACuE,GAAG;IAEjE,IAAI5T,KAAK,CAACwU,MAAM,KAAKb,aAAa,EAAE,OAAO,CAAC;IAE5C,IAAI3T,KAAK,CAACsP,QAAQ,EAAE;MAClB;MACA,MAAMmF,IAAI,GAAG,IAAI,CAAC/Q,oBAAoB,EAAEC,YAAY,EAAEmQ,IAAI,CACvDC,CAAC,IAAK,CAACA,CAAC,CAACjZ,EAAE,IAAIiZ,CAAC,CAACH,GAAG,MAAM5T,KAAK,CAACwU,MAAM,CACxC;MACD,IACEC,IAAI,IACJ,CAAC,IAAI,CAAC9F,WAAW,CAACmF,IAAI,CAAEY,CAAC,IAAK,CAACA,CAAC,CAAC5Z,EAAE,IAAI4Z,CAAC,CAACd,GAAG,MAAM5T,KAAK,CAACwU,MAAM,CAAC,EAC/D;QACA,IAAI,CAAC7F,WAAW,CAAC0C,IAAI,CAACoD,IAAI,CAAC;;KAE9B,MAAM;MACL;MACA,IAAI,CAAC9F,WAAW,GAAG,IAAI,CAACA,WAAW,CAACpL,MAAM,CACvCmR,CAAC,IAAK,CAACA,CAAC,CAAC5Z,EAAE,IAAI4Z,CAAC,CAACd,GAAG,MAAM5T,KAAK,CAACwU,MAAM,CACxC;;IAGH,IAAI,CAACrF,GAAG,CAACyB,aAAa,EAAE;EAC1B;EAEQD,kBAAkBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAAClC,QAAQ,CAACla,MAAM,IAAI,CAAC,IAAI,CAAC8a,WAAW,EAAE;IAEhD,MAAMsF,cAAc,GAAG,IAAI,CAAClG,QAAQ,CAAClL,MAAM,CACxC8Q,CAAC,IACA,CAACA,CAAC,CAACjN,MAAM,IACTiN,CAAC,CAAC7P,MAAM,IACR,CAAC6P,CAAC,CAAC7P,MAAM,CAAC1J,EAAE,IAAIuZ,CAAC,CAAC7P,MAAM,CAACoP,GAAG,OACzB,IAAI,CAACvE,WAAY,CAACvU,EAAE,IAAI,IAAI,CAACuU,WAAY,CAACuE,GAAG,CAAC,CACpD;IAEDe,cAAc,CAACzW,OAAO,CAAE6S,OAAO,IAAI;MACjC,IAAIA,OAAO,CAACjW,EAAE,EAAE;QACd,IAAI,CAACmW,iBAAiB,CAACF,OAAO,CAAC;;IAEnC,CAAC,CAAC;EACJ;EAEQE,iBAAiBA,CAACF,OAAgB;IACxC,IAAI,CAACA,OAAO,CAACjW,EAAE,IAAIiW,OAAO,CAAC3J,MAAM,EAAE;IAEnC,IAAI,CAAC7U,cAAc,CAAC0e,iBAAiB,CAACF,OAAO,CAACjW,EAAE,CAAC,CAACtH,SAAS,CAAC;MAC1DY,IAAI,EAAGue,cAAc,IAAI;QACvB,IAAI,CAACC,mBAAmB,CAACD,cAAc,CAAC;MAC1C,CAAC;MACDne,KAAK,EAAGA,KAAK,IAAI;QACfic,OAAO,CAACjc,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MAC3D;KACD,CAAC;EACJ;EAEQ2b,cAAcA,CAAA;IACpB,IAAI,CAACf,MAAM,CAACwF,iBAAiB,CAAC,MAAK;MACjC7f,UAAU,CAAC,MAAK;QACd,IAAI,IAAI,CAAC8f,iBAAiB,EAAE;UAC1B,MAAMC,OAAO,GAAG,IAAI,CAACD,iBAAiB,CAACvB,aAAa;UACpDwB,OAAO,CAACC,SAAS,GAAGD,OAAO,CAACE,YAAY;;MAE5C,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACJ;EAEA;EACA;EACA;EAEApM,iBAAiBA,CAACC,SAAwB;IACxC,MAAMoM,IAAI,GAAG,IAAI9hB,IAAI,CAAC0V,SAAS,CAAC;IAChC,MAAMlN,GAAG,GAAG,IAAIxI,IAAI,EAAE;IACtB,MAAM+hB,WAAW,GAAG,CAACvZ,GAAG,CAACwZ,OAAO,EAAE,GAAGF,IAAI,CAACE,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAEvE,IAAID,WAAW,GAAG,EAAE,EAAE;MACpB,OAAOD,IAAI,CAACG,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;OACT,CAAC;KACH,MAAM;MACL,OAAOL,IAAI,CAACM,kBAAkB,CAAC,OAAO,EAAE;QACtCC,GAAG,EAAE,SAAS;QACdC,KAAK,EAAE;OACR,CAAC;;EAEN;EAEAnN,WAAWA,CAACyI,OAAgB;IAC1B,IAAI,CAAC,IAAI,CAAC1B,WAAW,IAAI,CAAC0B,OAAO,CAACvM,MAAM,EAAE,OAAO,KAAK;IAEtD,MAAMmP,aAAa,GAAG,IAAI,CAACtE,WAAW,CAACvU,EAAE,IAAI,IAAI,CAACuU,WAAW,CAACuE,GAAG;IACjE,MAAMW,QAAQ,GAAGxD,OAAO,CAACvM,MAAM,CAAC1J,EAAE,IAAIiW,OAAO,CAACvM,MAAM,CAACoP,GAAG;IAExD,OAAOD,aAAa,KAAKY,QAAQ;EACnC;EAEAxL,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC4F,WAAW,CAACpa,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAE5C,IAAI,IAAI,CAACoa,WAAW,CAACpa,MAAM,KAAK,CAAC,EAAE;MACjC,OAAO,GAAG,IAAI,CAACoa,WAAW,CAAC,CAAC,CAAC,CAAChK,QAAQ,2BAA2B;KAClE,MAAM;MACL,OAAO,GAAG,IAAI,CAACgK,WAAW,CAACpa,MAAM,sCAAsC;;EAE3E;EAEAkZ,UAAUA,CAACzN,KAAoB;IAC7B,IAAIA,KAAK,CAAC0V,GAAG,KAAK,OAAO,IAAI,CAAC1V,KAAK,CAAC2V,QAAQ,EAAE;MAC5C3V,KAAK,CAAC4V,cAAc,EAAE;MACtB,IAAI,CAAChK,WAAW,EAAE;KACnB,MAAM;MACL,IAAI,CAACgC,QAAQ,EAAE;;EAEnB;EAEAV,iBAAiBA,CAAA;IACf,IAAI,CAACwC,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEA5C,oBAAoBA,CAAA;IAClB,IAAI,CAAC+B,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;EACpD;EAEAgH,mBAAmBA,CAAC9E,OAAgB;IAClC,IAAI,CAACpH,cAAc,GAAGoH,OAAO;IAC7B,IAAI,CAACzD,cAAc,GAAGyD,OAAO,CAAClM,OAAO,IAAI,EAAE;IAC3C,IAAI,CAACiR,YAAY,CAACxC,aAAa,CAACyC,KAAK,EAAE;EACzC;EAEAtM,aAAaA,CAAA;IACX,IAAI,CAACE,cAAc,GAAG,IAAI;IAC1B,IAAI,CAAC2D,cAAc,GAAG,EAAE;EAC1B;EAEA0I,UAAUA,CAACjF,OAAgB;IACzB,IAAI,CAAC1H,UAAU,GAAG0H,OAAO;IACzB,IAAI,CAAC+E,YAAY,CAACxC,aAAa,CAACyC,KAAK,EAAE;EACzC;EAEA5M,WAAWA,CAAA;IACT,IAAI,CAACE,UAAU,GAAG,IAAI;EACxB;EAEA;EACA;EACA;EAEAkF,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAAC7K,oBAAoB,IAAI,CAAC,IAAI,CAAC2L,WAAW,EAAE,OAAO,EAAE;IAE9D,MAAM1L,YAAY,GAAG,IAAI,CAACD,oBAAoB,CAACC,YAAY,IAAI,EAAE;IACjE,MAAMgQ,aAAa,GAAG,IAAI,CAACtE,WAAW,CAACvU,EAAE,IAAI,IAAI,CAACuU,WAAW,CAACuE,GAAG;IAEjE,MAAMC,SAAS,GAAGlQ,YAAY,CAACmQ,IAAI,CAChCC,CAAC,IAAK,CAACA,CAAC,CAACjZ,EAAE,IAAIiZ,CAAC,CAACH,GAAG,MAAMD,aAAa,CACzC;IAED,OAAOE,SAAS,EAAElP,QAAQ,IAAI,qBAAqB;EACrD;EAEA0J,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAAC3K,oBAAoB,IAAI,CAAC,IAAI,CAAC2L,WAAW,EACjD,OAAO,mCAAmC;IAE5C,MAAM1L,YAAY,GAAG,IAAI,CAACD,oBAAoB,CAACC,YAAY,IAAI,EAAE;IACjE,MAAMgQ,aAAa,GAAG,IAAI,CAACtE,WAAW,CAACvU,EAAE,IAAI,IAAI,CAACuU,WAAW,CAACuE,GAAG;IAEjE,MAAMC,SAAS,GAAGlQ,YAAY,CAACmQ,IAAI,CAChCC,CAAC,IAAK,CAACA,CAAC,CAACjZ,EAAE,IAAIiZ,CAAC,CAACH,GAAG,MAAMD,aAAa,CACzC;IAED,OAAOE,SAAS,EAAEpP,KAAK,IAAI,mCAAmC;EAChE;EAEA0J,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACzK,oBAAoB,IAAI,CAAC,IAAI,CAAC2L,WAAW,EAAE,OAAO,KAAK;IAEjE,MAAM1L,YAAY,GAAG,IAAI,CAACD,oBAAoB,CAACC,YAAY,IAAI,EAAE;IACjE,MAAMgQ,aAAa,GAAG,IAAI,CAACtE,WAAW,CAACvU,EAAE,IAAI,IAAI,CAACuU,WAAW,CAACuE,GAAG;IAEjE,MAAMC,SAAS,GAAGlQ,YAAY,CAACmQ,IAAI,CAChCC,CAAC,IAAK,CAACA,CAAC,CAACjZ,EAAE,IAAIiZ,CAAC,CAACH,GAAG,MAAMD,aAAa,CACzC;IAED,OAAOE,SAAS,EAAEoC,QAAQ,IAAI,KAAK;EACrC;EAEAvH,gBAAgBA,CAAC3E,KAAa,EAAEgH,OAAgB;IAC9C,OAAOA,OAAO,CAACjW,EAAE,IAAIiW,OAAO,CAAC6C,GAAG,IAAI7J,KAAK,CAACjO,QAAQ,EAAE;EACtD;EAEAoJ,eAAeA,CAACgR,UAAkC;IAChD,IAAI,CAACA,UAAU,EAAE5Q,GAAG,EAAE;IAEtB;IACA/H,MAAM,CAAC4Y,IAAI,CAACD,UAAU,CAAC5Q,GAAG,EAAE,QAAQ,CAAC;EACvC;EAEAM,cAAcA,CAACC,IAAwB;IACrC,IAAI,CAACA,IAAI,EAAE,OAAO,KAAK;IAEvB,MAAMuQ,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACrC,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIC,QAAQ,GAAGzQ,IAAI;IAEnB,OAAOyQ,QAAQ,IAAI,IAAI,IAAID,SAAS,GAAGD,KAAK,CAAC7hB,MAAM,GAAG,CAAC,EAAE;MACvD+hB,QAAQ,IAAI,IAAI;MAChBD,SAAS,EAAE;;IAGb,OAAO,GAAGC,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,IAAIH,KAAK,CAACC,SAAS,CAAC,EAAE;EACrD;EAEA3Q,YAAYA,CAACwQ,UAAkC;IAC7C,IAAI,CAACA,UAAU,EAAE5Q,GAAG,EAAE;IAEtB,MAAMkR,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGT,UAAU,CAAC5Q,GAAG;IAC1BkR,IAAI,CAACI,QAAQ,GAAGV,UAAU,CAAChZ,IAAI,IAAI,MAAM;IACzCuZ,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;IAC/BA,IAAI,CAACjD,KAAK,EAAE;IACZkD,QAAQ,CAACI,IAAI,CAACE,WAAW,CAACP,IAAI,CAAC;EACjC;EAEAvQ,gBAAgBA,CAAC8K,OAAgB;IAC/B,IAAI,CAACA,OAAO,CAAC5L,WAAW,GAAG,CAAC,CAAC,EAAEG,GAAG,EAAE;IAEpC,IAAI,CAAC/S,cAAc,CAACykB,SAAS,CAACjG,OAAO,CAAC5L,WAAW,CAAC,CAAC,CAAC,CAACG,GAAG,CAAC,CAAC2R,KAAK,CAAEziB,KAAK,IAAI;MACxEic,OAAO,CAACjc,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;MACnE,IAAI,CAAC9B,YAAY,CAAC+B,SAAS,CAAC,4CAA4C,CAAC;IAC3E,CAAC,CAAC;EACJ;EAEA0R,cAAcA,CAAC/I,QAA4B;IACzC,IAAI,CAACA,QAAQ,EAAE,OAAO,MAAM;IAE5B,MAAM8Z,OAAO,GAAGtb,IAAI,CAACub,KAAK,CAAC/Z,QAAQ,GAAG,EAAE,CAAC;IACzC,MAAMga,OAAO,GAAGha,QAAQ,GAAG,EAAE;IAE7B,OAAO,GAAG8Z,OAAO,IAAIE,OAAO,CAACtb,QAAQ,EAAE,CAACub,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D;EAEAjP,eAAeA,CAAC2I,OAAgB;IAC9B;IACA;IACA,MAAMuG,OAAO,GAAG,EAAE;IAElB,IAAI,IAAI,CAACxE,gBAAgB,CAAC/B,OAAO,CAAC,EAAE;MAClCuG,OAAO,CAACjG,IAAI,CAAC,WAAW,CAAC;;IAG3B,IAAI,IAAI,CAAC/I,WAAW,CAACyI,OAAO,CAAC,EAAE;MAC7BuG,OAAO,CAACjG,IAAI,CAAC,UAAU,CAAC;;IAG1BiG,OAAO,CAACjG,IAAI,CAAC,UAAU,EAAE,YAAY,EAAE,QAAQ,CAAC;IAEhD;IACAZ,OAAO,CAAC8G,GAAG,CAAC,sCAAsC,EAAED,OAAO,CAAC;EAC9D;;;uBAvwBWvI,oBAAoB,EAAA9Z,+DAAA,CAAAE,qEAAA,GAAAF,+DAAA,CAAAI,+DAAA,GAAAJ,+DAAA,CAAAM,iEAAA,GAAAN,+DAAA,CAAAQ,4DAAA,GAAAR,+DAAA,CAAAQ,oDAAA,GAAAR,+DAAA,CAAAA,4DAAA,GAAAA,+DAAA,CAAAA,iDAAA;IAAA;EAAA;;;YAApB8Z,oBAAoB;MAAApZ,SAAA;MAAAkiB,SAAA,WAAAC,2BAAA7hB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;UClCjChB,wDAAA,IAAA8iB,mCAAA,mBAgaM;UAKN9iB,wDAAA,IAAA+iB,mCAAA,kBAsEM;;;UA3euB/iB,wDAAA,SAAAiB,GAAA,CAAAwN,oBAAA,CAA0B;UAsapDzO,uDAAA,GAA2B;UAA3BA,wDAAA,UAAAiB,GAAA,CAAAwN,oBAAA,CAA2B;;;;;;;;;;;;;;;;;;;;;;;ACnaxB,MAAOuU,qBAAqB;;;uBAArBA,qBAAqB;IAAA;EAAA;;;YAArBA,qBAAqB;MAAAtiB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAE,QAAA,WAAAmiB,+BAAAjiB,EAAA,EAAAC,GAAA;MAAAiiB,MAAA;IAAA;EAAA;;;;;;;;;;;;;;;;;;;ACNqB;AACsB;AAGM;;;AAEnF,MAAMG,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH,4FAAsB;EACjCI,QAAQ,EAAE;EACR;EACA;IACEF,IAAI,EAAE,EAAE;IACRC,SAAS,EAAEzJ,sFAAoB;IAC/BpU,IAAI,EAAE;MAAE+d,KAAK,EAAE;IAAU;GAC1B;EACD;EACA;IACEH,IAAI,EAAE,iBAAiB;IACvBC,SAAS,EAAEzJ,sFAAoB;IAC/BpU,IAAI,EAAE;MAAE+d,KAAK,EAAE;IAAM;GACtB;CAEJ,CACF;AAMK,MAAOC,qBAAqB;;;uBAArBA,qBAAqB;IAAA;EAAA;;;YAArBA;IAAqB;EAAA;;;gBAHtBP,yDAAY,CAACQ,QAAQ,CAACN,MAAM,CAAC,EAC7BF,yDAAY;IAAA;EAAA;;;sHAEXO,qBAAqB;IAAAE,OAAA,GAAA1jB,yDAAA;IAAA2jB,OAAA,GAFtBV,yDAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7BuB;AACA;AAEmB;AACA;AACpB;AAC+B;AACG;AACZ;AACe;AACe;AAEzB;AACP;;AAoB5D,MAAOiB,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA;IAAc;EAAA;;;iBAFd,CAACD,mFAAiB,EAAEhkB,4EAAc,CAAC;MAAAyjB,OAAA,GAP5CE,yDAAY,EACZJ,2EAAqB,EACrBK,wDAAW,EACXC,gEAAmB,EACnBC,yDAAY,EACZd,0DAAY;IAAA;EAAA;;;sHAIHiB,cAAc;IAAAC,YAAA,GAhBvBvK,sFAAoB,EACpBkJ,yFAAqB,EACrBkB,6EAAiB,EACjBd,4FAAsB,EACtBhmB,oGAAqB;IAAAwmB,OAAA,GAGrBE,yDAAY,EACZJ,2EAAqB,EACrBK,wDAAW,EACXC,gEAAmB,EACnBC,yDAAY,EACZd,0DAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5B0C;AACrB;AAOyB;AAEN;;;;;;;;;;;;;ICwIlDnjB,4DAAA,cAGC;IAEIA,oDAAA,GAC4B;IAAAA,0DAAA,EAC9B;IACDA,4DAAA,WAAM;IAAAA,oDAAA,GAA2C;IAAAA,0DAAA,EAAO;;;;IAHrDA,uDAAA,GAC4B;IAD5BA,gEAAA,kBAAAiZ,MAAA,CAAA7Z,KAAA,CAAAE,MAAA,WAAA2Z,MAAA,CAAAwL,UAAA,kBAC4B;IAEzBzkB,uDAAA,GAA2C;IAA3CA,gEAAA,UAAAiZ,MAAA,CAAAqB,WAAA,WAAArB,MAAA,CAAAyL,UAAA,KAA2C;;;;;IAcrD1kB,4DAAA,cAA2E;IACzEA,uDAAA,cAA6C;IAC7CA,4DAAA,cAAqC;IAAAA,oDAAA,qCAA8B;IAAAA,0DAAA,EAAM;;;;;IAI3EA,4DAAA,cAA2E;IAEvEA,uDAAA,YAA4B;IAC9BA,0DAAA,EAAM;IACNA,4DAAA,aAAmC;IAAAA,oDAAA,oCAAwB;IAAAA,0DAAA,EAAK;IAChEA,4DAAA,YAAiC;IAC/BA,oDAAA,mEACF;IAAAA,0DAAA,EAAI;;;;;IAeEA,uDAAA,eAGQ;;;;;;IAYVA,4DAAA,iBAKC;IAHCA,wDAAA,mBAAA2kB,wEAAA;MAAA3kB,2DAAA,CAAA4kB,IAAA;MAAA,MAAAC,OAAA,GAAA7kB,2DAAA,GAAA+P,SAAA;MAAA,MAAAoE,OAAA,GAAAnU,2DAAA;MAAA,OAASA,yDAAA,CAAAmU,OAAA,CAAAlF,cAAA,CAAA4V,OAAA,CAAAhf,EAAA,IAAAgf,OAAA,CAAAlG,GAAA,CAAmC;IAAA,EAAC;IAI7C3e,uDAAA,YAA4B;IAC9BA,0DAAA,EAAS;;;;;;IACTA,4DAAA,iBAKC;IAHCA,wDAAA,mBAAA8kB,wEAAA;MAAA9kB,2DAAA,CAAA+kB,IAAA;MAAA,MAAAF,OAAA,GAAA7kB,2DAAA,GAAA+P,SAAA;MAAA,MAAA6F,OAAA,GAAA5V,2DAAA;MAAA,OAASA,yDAAA,CAAA4V,OAAA,CAAAvG,cAAA,CAAAwV,OAAA,CAAAhf,EAAA,IAAAgf,OAAA,CAAAlG,GAAA,CAAmC;IAAA,EAAC;IAI7C3e,uDAAA,YAA4B;IAC9BA,0DAAA,EAAS;;;;;;IAxCbA,4DAAA,aAA4D;IAGxDA,wDAAA,mBAAAglB,2DAAA;MAAA,MAAA3T,WAAA,GAAArR,2DAAA,CAAAilB,IAAA;MAAA,MAAAJ,OAAA,GAAAxT,WAAA,CAAAtB,SAAA;MAAA,MAAAmV,OAAA,GAAAllB,2DAAA;MAAA,OAASA,yDAAA,CAAAklB,OAAA,CAAAC,iBAAA,CAAAN,OAAA,CAAAhf,EAAA,IAAAgf,OAAA,CAAAlG,GAAA,CAAsC;IAAA,EAAC;IAEhD3e,4DAAA,cAA+B;IAC7BA,uDAAA,cAGE;IACFA,wDAAA,IAAAolB,4CAAA,mBAGQ;IACVplB,0DAAA,EAAM;IACNA,4DAAA,cAAkC;IAE9BA,oDAAA,GACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,YAAiC;IAAAA,oDAAA,GAAgB;IAAAA,0DAAA,EAAI;IAKzDA,4DAAA,eAAqC;IACnCA,wDAAA,KAAAqlB,+CAAA,qBAOS;IACTrlB,wDAAA,KAAAslB,+CAAA,qBAOS;IACXtlB,0DAAA,EAAM;;;;IAlCAA,uDAAA,GAAwD;IAAxDA,wDAAA,QAAA6kB,OAAA,CAAArV,KAAA,wCAAAxP,2DAAA,CAAwD;IAIvDA,uDAAA,GAAmB;IAAnBA,wDAAA,SAAA6kB,OAAA,CAAA7D,QAAA,CAAmB;IAMpBhhB,uDAAA,GACF;IADEA,gEAAA,MAAA6kB,OAAA,CAAAnV,QAAA,MACF;IACiC1P,uDAAA,GAAgB;IAAhBA,+DAAA,CAAA6kB,OAAA,CAAAU,KAAA,CAAgB;IAOhDvlB,uDAAA,GAAmB;IAAnBA,wDAAA,SAAA6kB,OAAA,CAAA7D,QAAA,CAAmB;IAQnBhhB,uDAAA,GAAmB;IAAnBA,wDAAA,SAAA6kB,OAAA,CAAA7D,QAAA,CAAmB;;;;;IAnC5BhhB,4DAAA,aAA2D;IACzDA,wDAAA,IAAAwlB,qCAAA,kBA0CK;IACPxlB,0DAAA,EAAK;;;;IA3CkBA,uDAAA,GAAQ;IAARA,wDAAA,YAAAylB,MAAA,CAAArmB,KAAA,CAAQ;;;;;IA8C/BY,4DAAA,cAAyE;IAErEA,uDAAA,cAAsE;IAGxEA,0DAAA,EAAM;IACNA,4DAAA,cAAqC;IACnCA,oDAAA,6CACF;IAAAA,0DAAA,EAAM;;;;;;IAIRA,4DAAA,cAA4E;IAClEA,wDAAA,mBAAA0lB,0DAAA;MAAA1lB,2DAAA,CAAA2lB,IAAA;MAAA,MAAAC,OAAA,GAAA5lB,2DAAA;MAAA,OAASA,yDAAA,CAAA4lB,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAC9B7lB,uDAAA,YAAwC;IACxCA,oDAAA,oCACF;IAAAA,0DAAA,EAAS;;;ADtOT,MAAOkkB,iBAAiB;EA8B5B7mB,YACU8C,cAA8B,EAC9B2lB,WAAwB,EACzB7L,MAAc,EACdD,KAAqB,EACpBD,WAA4B,EAC5Btc,YAA0B,EAC1BwF,MAAqB,EACrBzF,YAA0B;IAP1B,KAAA2C,cAAc,GAAdA,cAAc;IACd,KAAA2lB,WAAW,GAAXA,WAAW;IACZ,KAAA7L,MAAM,GAANA,MAAM;IACN,KAAAD,KAAK,GAALA,KAAK;IACJ,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAtc,YAAY,GAAZA,YAAY;IACZ,KAAAwF,MAAM,GAANA,MAAM;IACN,KAAAzF,YAAY,GAAZA,YAAY;IArCtB,KAAA4B,KAAK,GAAW,EAAE;IAClB,KAAA2mB,OAAO,GAAG,IAAI;IACd,KAAArH,aAAa,GAAkB,IAAI;IAGnC;IACA,KAAApE,WAAW,GAAG,CAAC;IACf,KAAA0L,QAAQ,GAAG,EAAE;IACb,KAAAvB,UAAU,GAAG,CAAC;IACd,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAuB,WAAW,GAAG,KAAK;IACnB,KAAAC,eAAe,GAAG,KAAK;IAEvB;IACA,KAAAC,MAAM,GAAG,UAAU;IACnB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,UAAU,GAAG,IAAI7B,qDAAS,CAAC;MACzB9J,WAAW,EAAE,IAAI6J,uDAAW,CAAC,EAAE,CAAC;MAChCvD,QAAQ,EAAE,IAAIuD,uDAAW,CAAiB,IAAI;KAC/C,CAAC;IAEF;IACA,KAAA+B,kBAAkB,GAAG,IAAI;IACzB,KAAAC,mBAAmB,GAAG,KAAK,CAAC,CAAC;IAGrB,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAA3L,aAAa,GAAiB,IAAIyJ,8CAAY,EAAE;IAYtD,IAAI,CAACmC,WAAW,GAAG,IAAI,CAACjpB,YAAY,CAACgB,aAAa,CAAC+G,IAAI,CACrDpD,oDAAG,CAAEnE,KAAK,IAAKA,KAAK,CAACiK,IAAI,KAAK,MAAM,CAAC,CACtC;EACH;EAEA7J,QAAQA,CAAA;IACN,IAAI,CAACsgB,aAAa,GAAG,IAAI,CAAC3E,WAAW,CAAC2M,gBAAgB,EAAE;IACxD,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,SAAS,EAAE;EAClB;EAEQF,oBAAoBA,CAAA;IAC1B;IACA,MAAMG,SAAS,GAAG,IAAI,CAACT,UAAU,CAC9BU,GAAG,CAAC,aAAa,CAAE,CACnBC,YAAY,CAACzoB,SAAS,CAAC,MAAK;MAC3B,IAAI,CAAC0oB,eAAe,EAAE;MACtB,IAAI,CAACJ,SAAS,EAAE;IAClB,CAAC,CAAC;IAEJ,IAAI,CAAChM,aAAa,CAACqM,GAAG,CAACJ,SAAS,CAAC;IAEjC;IACA,MAAMK,SAAS,GAAG,IAAI,CAACd,UAAU,CAC9BU,GAAG,CAAC,UAAU,CAAE,CAChBC,YAAY,CAACzoB,SAAS,CAAC,MAAK;MAC3B,IAAI,CAAC0oB,eAAe,EAAE;MACtB,IAAI,CAACJ,SAAS,EAAE;IAClB,CAAC,CAAC;IAEJ,IAAI,CAAChM,aAAa,CAACqM,GAAG,CAACC,SAAS,CAAC;EACnC;EAEQP,gBAAgBA,CAAA;IACtB,IAAI,IAAI,CAACN,kBAAkB,EAAE;MAC3B,IAAI,CAACc,uBAAuB,GAAGjqB,+CAAQ,CACrC,IAAI,CAACopB,mBAAmB,CACzB,CAAChoB,SAAS,CAAC,MAAK;QACf,IAAI,CAAC,IAAI,CAACwnB,OAAO,IAAI,CAAC,IAAI,CAACM,UAAU,CAACU,GAAG,CAAC,aAAa,CAAC,EAAEzgB,KAAK,EAAE;UAC/D,IAAI,CAACugB,SAAS,CAAC,IAAI,CAAC;;MAExB,CAAC,CAAC;;EAEN;EAEAQ,iBAAiBA,CAAA;IACf,IAAI,CAACf,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;IAElD,IAAI,IAAI,CAACA,kBAAkB,EAAE;MAC3B,IAAI,CAACM,gBAAgB,EAAE;KACxB,MAAM,IAAI,IAAI,CAACQ,uBAAuB,EAAE;MACvC,IAAI,CAACA,uBAAuB,CAACzoB,WAAW,EAAE;MAC1C,IAAI,CAACyoB,uBAAuB,GAAGE,SAAS;;EAE5C;EAEAL,eAAeA,CAAA;IACb,IAAI,CAAC3M,WAAW,GAAG,CAAC;EACtB;EAEA;EACA,IAAII,WAAWA,CAAA;IACb,OAAO,IAAI,CAAC2L,UAAU,CAACU,GAAG,CAAC,aAAa,CAAC,EAAEzgB,KAAK,IAAI,EAAE;EACxD;EAEA;EACA,IAAIoU,WAAWA,CAACpU,KAAa;IAC3B,IAAI,CAAC+f,UAAU,CAACU,GAAG,CAAC,aAAa,CAAC,EAAEQ,QAAQ,CAACjhB,KAAK,CAAC;EACrD;EAEA;EACAkhB,IAAIA,CAACC,IAAS;IACZ,OAAOA,IAAI;EACb;EAEAZ,SAASA,CAACa,YAAY,GAAG,KAAK;IAC5B,IAAI,IAAI,CAAClB,WAAW,EAAE;IAEtB,IAAI,CAACT,OAAO,GAAG,IAAI;IAEnB,MAAMrL,WAAW,GAAG,IAAI,CAAC2L,UAAU,CAACU,GAAG,CAAC,aAAa,CAAC,EAAEzgB,KAAK,IAAI,EAAE;IACnE,MAAM0a,QAAQ,GAAG,IAAI,CAACqF,UAAU,CAACU,GAAG,CAAC,UAAU,CAAC,EAAEzgB,KAAK;IAEvD,MAAM+V,GAAG,GAAG,IAAI,CAAClc,cAAc,CAACwnB,WAAW,CACzCD,YAAY,EACZhN,WAAW,EACX,IAAI,CAACJ,WAAW,EAChB,IAAI,CAAC0L,QAAQ,EACb,IAAI,CAACG,MAAM,EACX,IAAI,CAACC,SAAS,EACdpF,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAGsG,SAAS,CACrC,CAAC/oB,SAAS,CAAC;MACVY,IAAI,EAAGC,KAAK,IAAI;QACd,IAAI,CAAC6e,KAAK,CAAC2J,OAAO,CAACxoB,KAAK,CAAC,EAAE;UACzB,IAAI,CAACA,KAAK,GAAG,EAAE;UACf,IAAI,CAAC2mB,OAAO,GAAG,KAAK;UACpB,IAAI,CAACS,WAAW,GAAG,KAAK;UACxB,IAAI,CAAC/oB,YAAY,CAAC+B,SAAS,CAAC,oCAAoC,CAAC;UACjE;;QAGF;QACA,IAAI,IAAI,CAAC8a,WAAW,KAAK,CAAC,EAAE;UAC1B;UACA,IAAI,CAAClb,KAAK,GAAGA,KAAK,CAACkP,MAAM,CAAEkR,IAAI,IAAI;YACjC,IAAI,CAACA,IAAI,EAAE,OAAO,KAAK;YACvB,MAAMD,MAAM,GAAGC,IAAI,CAAC3Z,EAAE,IAAI2Z,IAAI,CAACb,GAAG;YAClC,OAAOY,MAAM,KAAK,IAAI,CAACb,aAAa;UACtC,CAAC,CAAC;SACH,MAAM;UACL;UACA,MAAMmJ,QAAQ,GAAGzoB,KAAK,CAACkP,MAAM,CAAEwZ,OAAO,IAAI;YACxC,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;YAC1B,MAAMvI,MAAM,GAAGuI,OAAO,CAACjiB,EAAE,IAAIiiB,OAAO,CAACnJ,GAAG;YACxC,OACEY,MAAM,KAAK,IAAI,CAACb,aAAa,IAC7B,CAAC,IAAI,CAACtf,KAAK,CAAC2oB,IAAI,CACbC,YAAY,IACX,CAACA,YAAY,CAACniB,EAAE,IAAImiB,YAAY,CAACrJ,GAAG,MAAMY,MAAM,CACnD;UAEL,CAAC,CAAC;UAEF,IAAI,CAACngB,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK,EAAE,GAAGyoB,QAAQ,CAAC;;QAG3C;QACA,MAAMI,UAAU,GAAG,IAAI,CAAC9nB,cAAc,CAAC+nB,qBAAqB;QAC5D,IAAI,CAACzD,UAAU,GAAGwD,UAAU,CAACE,UAAU;QACvC,IAAI,CAACzD,UAAU,GAAGuD,UAAU,CAACvD,UAAU;QACvC,IAAI,CAACuB,WAAW,GAAGgC,UAAU,CAAChC,WAAW;QACzC,IAAI,CAACC,eAAe,GAAG+B,UAAU,CAAC/B,eAAe;QAEjD,IAAI,CAACH,OAAO,GAAG,KAAK;QACpB,IAAI,CAACS,WAAW,GAAG,KAAK;MAC1B,CAAC;MACDjnB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACwmB,OAAO,GAAG,KAAK;QACpB,IAAI,CAACS,WAAW,GAAG,KAAK;QACxB,IAAI,CAAC/oB,YAAY,CAAC+B,SAAS,CACzB,yBAAyBD,KAAK,CAACuc,OAAO,IAAI,eAAe,EAAE,CAC5D;QAED,IAAI,IAAI,CAACxB,WAAW,KAAK,CAAC,EAAE;UAC1B,IAAI,CAAClb,KAAK,GAAG,EAAE;;MAEnB,CAAC;MACDwN,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAACmZ,OAAO,GAAG,KAAK;QACpB,IAAI,CAACS,WAAW,GAAG,KAAK;MAC1B;KACD,CAAC;IAEF,IAAI,CAAC3L,aAAa,CAACqM,GAAG,CAAC7K,GAAG,CAAC;EAC7B;EAEA8I,iBAAiBA,CAAC5F,MAA0B;IAC1C,IAAI,CAACA,MAAM,EAAE;MACX,IAAI,CAAC9hB,YAAY,CAAC+B,SAAS,CACzB,+CAA+C,CAChD;MACD;;IAGF,IAAI,CAAC/B,YAAY,CAACmC,QAAQ,CAAC,0BAA0B,CAAC;IAEtD,IAAI,CAACO,cAAc,CAACioB,kBAAkB,CAAC7I,MAAM,CAAC,CAAChhB,SAAS,CAAC;MACvDY,IAAI,EAAGsc,YAAY,IAAI;QACrB,IAAI,CAACA,YAAY,IAAI,CAACA,YAAY,CAAC5V,EAAE,EAAE;UACrC,IAAI,CAACpI,YAAY,CAAC+B,SAAS,CACzB,iDAAiD,CAClD;UACD;;QAGF,IAAI,CAACya,MAAM,CACRmB,QAAQ,CAAC,CAAC,8BAA8B,EAAEK,YAAY,CAAC5V,EAAE,CAAC,CAAC,CAC3DwiB,IAAI,CAAEniB,OAAO,IAAI;UAChB,IAAI,CAACA,OAAO,EAAE;YACZ,IAAI,CAACzI,YAAY,CAAC+B,SAAS,CAAC,6BAA6B,CAAC;;QAE9D,CAAC,CAAC;MACN,CAAC;MACDD,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC9B,YAAY,CAAC+B,SAAS,CACzB,kCAAkCD,KAAK,CAACuc,OAAO,IAAI,eAAe,EAAE,CACrE;MACH;KACD,CAAC;EACJ;EAEA7M,cAAcA,CAACsQ,MAAc;IAC3B,IAAI,CAACA,MAAM,EAAE;IAEb,IAAI,CAACuG,WAAW,CAAClhB,YAAY,CAAC2a,MAAM,EAAEld,kEAAQ,CAACmc,KAAK,CAAC,CAACjgB,SAAS,CAAC;MAC9DY,IAAI,EAAGsG,IAAU,IAAI;QACnB,IAAI,CAAChI,YAAY,CAAC4B,WAAW,CAAC,sBAAsB,CAAC;MACvD,CAAC;MACDE,KAAK,EAAGA,KAAU,IAAI;QACpB,IAAI,CAAC9B,YAAY,CAAC+B,SAAS,CAAC,+BAA+B,CAAC;MAC9D;KACD,CAAC;EACJ;EAEA6P,cAAcA,CAACkQ,MAAc;IAC3B,IAAI,CAACA,MAAM,EAAE;IAEb,IAAI,CAACuG,WAAW,CAAClhB,YAAY,CAAC2a,MAAM,EAAEld,kEAAQ,CAAC8J,KAAK,CAAC,CAAC5N,SAAS,CAAC;MAC9DY,IAAI,EAAGsG,IAAU,IAAI;QACnB,IAAI,CAAChI,YAAY,CAAC4B,WAAW,CAAC,sBAAsB,CAAC;MACvD,CAAC;MACDE,KAAK,EAAGA,KAAU,IAAI;QACpB,IAAI,CAAC9B,YAAY,CAAC+B,SAAS,CAAC,+BAA+B,CAAC;MAC9D;KACD,CAAC;EACJ;EAEAqmB,YAAYA,CAAA;IACV,IAAI,IAAI,CAACI,WAAW,IAAI,CAAC,IAAI,CAACF,OAAO,EAAE;MACrC,IAAI,CAACS,WAAW,GAAG,IAAI;MACvB,IAAI,CAAClM,WAAW,EAAE;MAClB,IAAI,CAACuM,SAAS,EAAE;;EAEpB;EAEAyB,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACpC,eAAe,IAAI,CAAC,IAAI,CAACH,OAAO,EAAE;MACzC,IAAI,CAACS,WAAW,GAAG,IAAI;MACvB,IAAI,CAAClM,WAAW,EAAE;MAClB,IAAI,CAACuM,SAAS,EAAE;;EAEpB;EAEA0B,YAAYA,CAAA;IACV,IAAI,CAACtB,eAAe,EAAE;IACtB,IAAI,CAACJ,SAAS,CAAC,IAAI,CAAC;EACtB;EAEA2B,YAAYA,CAAA;IACV,IAAI,CAACnC,UAAU,CAACoC,KAAK,CAAC;MACpB/N,WAAW,EAAE,EAAE;MACfsG,QAAQ,EAAE;KACX,CAAC;IACF,IAAI,CAACiG,eAAe,EAAE;IACtB,IAAI,CAACJ,SAAS,CAAC,IAAI,CAAC;EACtB;EAEA6B,eAAeA,CAACC,KAAa;IAC3B,IAAI,IAAI,CAACxC,MAAM,KAAKwC,KAAK,EAAE;MACzB;MACA,IAAI,CAACvC,SAAS,GAAG,IAAI,CAACA,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK;KAC3D,MAAM;MACL;MACA,IAAI,CAACD,MAAM,GAAGwC,KAAK;MACnB,IAAI,CAACvC,SAAS,GAAG,KAAK;;IAGxB,IAAI,CAACa,eAAe,EAAE;IACtB,IAAI,CAACJ,SAAS,CAAC,IAAI,CAAC;EACtB;EAEA;;;EAGA+B,qBAAqBA,CAAA;IACnB,IAAI,CAAC3O,MAAM,CAACmB,QAAQ,CAAC,CAAC,yBAAyB,CAAC,CAAC;EACnD;EAEA1c,WAAWA,CAAA;IACT,IAAI,CAACmc,aAAa,CAAClc,WAAW,EAAE;IAChC,IAAI,IAAI,CAACyoB,uBAAuB,EAAE;MAChC,IAAI,CAACA,uBAAuB,CAACzoB,WAAW,EAAE;;EAE9C;;;uBA1TWulB,iBAAiB,EAAAlkB,+DAAA,CAAAE,4EAAA,GAAAF,+DAAA,CAAAI,sEAAA,GAAAJ,+DAAA,CAAAM,oDAAA,GAAAN,+DAAA,CAAAM,4DAAA,GAAAN,+DAAA,CAAAQ,8EAAA,GAAAR,+DAAA,CAAA8oB,wEAAA,GAAA9oB,+DAAA,CAAA+oB,0EAAA,GAAA/oB,+DAAA,CAAAgpB,qEAAA;IAAA;EAAA;;;YAAjB9E,iBAAiB;MAAAxjB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAmoB,2BAAAjoB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCpB9BhB,4DAAA,aAGC;;UAECA,4DAAA,aAAkE;UAEhEA,uDAAA,aAEO;UAcPA,4DAAA,aAAuD;UAEnDA,uDAAA,aAA6C;UAW/CA,0DAAA,EAAM;UAIRA,4DAAA,cAAyE;UACvEA,uDAAA,eAAqE;UACvEA,0DAAA,EAAM;UAGRA,4DAAA,eAAqC;UAEJA,oDAAA,6BAAqB;UAAAA,0DAAA,EAAK;UACvDA,4DAAA,eAA4B;UAExBA,wDAAA,mBAAAkpB,oDAAA;YAAA,OAASjoB,GAAA,CAAAsnB,YAAA,EAAc;UAAA,EAAC;UAIxBvoB,uDAAA,aAA+B;UACjCA,0DAAA,EAAS;UACTA,4DAAA,kBAGC;UAFCA,wDAAA,mBAAAmpB,oDAAA;YAAA,OAASloB,GAAA,CAAA2nB,qBAAA,EAAuB;UAAA,EAAC;UAGjC5oB,uDAAA,aAAiC;UACnCA,0DAAA,EAAS;UAKbA,4DAAA,eAAuB;UAKjBA,wDAAA,2BAAAopB,2DAAAjR,MAAA;YAAA,OAAAlX,GAAA,CAAAyZ,WAAA,GAAAvC,MAAA;UAAA,EAAsC;UAFxCnY,0DAAA,EAME;UACFA,uDAAA,aAEK;UACPA,0DAAA,EAAM;UAGNA,4DAAA,eAA+C;UAUrCA,wDAAA,oBAAAqpB,oDAAAlR,MAAA;YAAA,IAAAmR,OAAA;YAAA,QAAAA,OAAA,GAERroB,GAAA,CAAAolB,UAAA,CAAAU,GAAA,CACD,UAAU,CAAC,mBADVuC,OAAA,CAAA/B,QAAA,CAAApP,MAAA,CAAA6F,MAAA,CAAAuL,OAAA,GAEF,IAAI,GAAG,IAAI,CACjB;UAAA,EADiB;UATHvpB,0DAAA,EAUE;UACFA,uDAAA,gBAAmD;UACrDA,0DAAA,EAAQ;UACRA,4DAAA,iBACG;UAAAA,oDAAA,2BAAmB;UAAAA,0DAAA,EACrB;UAIHA,4DAAA,eAAyC;UACRA,oDAAA,kBAAU;UAAAA,0DAAA,EAAO;UAChDA,4DAAA,kBAGC;UAFCA,wDAAA,oBAAAwpB,qDAAArR,MAAA;YAAA,OAAUlX,GAAA,CAAAynB,eAAA,CAAAvQ,MAAA,CAAA6F,MAAA,CAAA1X,KAAA,CAA0C;UAAA,EAAC;UAGrDtG,4DAAA,kBAA4D;UAC1DA,oDAAA,aACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAAsD;UACpDA,oDAAA,eACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAAgE;UAC9DA,oDAAA,qCACF;UAAAA,0DAAA,EAAS;UAEXA,4DAAA,kBASC;UARCA,wDAAA,mBAAAypB,oDAAA;YAAAxoB,GAAA,CAAAmlB,SAAA,GAAAnlB,GAAA,CAAAmlB,SAAA,KAC6C,KAAK,GAC/D,MAAM,GAAG,KAAK;YAAA,OACdnlB,GAAA,CAAA4lB,SAAA,CAAU,IAAI,CACf;UAAA,EADe;UAMD7mB,uDAAA,SAIK;UACPA,0DAAA,EAAS;UAKbA,4DAAA,kBAAiE;UAAzDA,wDAAA,mBAAA0pB,oDAAA;YAAA,OAASzoB,GAAA,CAAAunB,YAAA,EAAc;UAAA,EAAC;UAC9BxoB,oDAAA,6BACF;UAAAA,0DAAA,EAAS;UAIXA,wDAAA,KAAA2pB,iCAAA,kBASM;UACR3pB,0DAAA,EAAM;UAIRA,4DAAA,eAMC;UAJCA,wDAAA,oBAAA4pB,kDAAAzR,MAAA;YAAA,OAAAA,MAAA,CAAA6F,MAAA,CAAA8B,SAAA,GAAA3H,MAAA,CAAA6F,MAAA,CAAA6L,YAAA,IAAA1R,MAAA,CAAA6F,MAAA,CAAA+B,YAAA,GAE8C,GAAG,IAAI9e,GAAA,CAAA4kB,YAAA,EAEzD;UAAA,EADK;UAGD7lB,wDAAA,KAAA8pB,iCAAA,kBAGM;UAGN9pB,wDAAA,KAAA+pB,iCAAA,kBAQM;UAGN/pB,wDAAA,KAAAgqB,gCAAA,iBA4CK;UAGLhqB,wDAAA,KAAAiqB,iCAAA,kBASM;UAGNjqB,wDAAA,KAAAkqB,iCAAA,kBAKM;UACRlqB,0DAAA,EAAM;;;;UA1PNA,yDAAA,SAAAA,yDAAA,QAAAiB,GAAA,CAAAwlB,WAAA,EAAkC;UAoE1BzmB,uDAAA,IAAuB;UAAvBA,wDAAA,YAAAiB,GAAA,CAAAyZ,WAAA,CAAuB;UAqBjB1a,uDAAA,GAAsD;UAAtDA,wDAAA,cAAAoqB,OAAA,GAAAnpB,GAAA,CAAAolB,UAAA,CAAAU,GAAA,+BAAAqD,OAAA,CAAA9jB,KAAA,WAAsD;UAqBhDtG,uDAAA,GAAkC;UAAlCA,wDAAA,aAAAiB,GAAA,CAAAklB,MAAA,gBAAkC;UAGlCnmB,uDAAA,GAA+B;UAA/BA,wDAAA,aAAAiB,GAAA,CAAAklB,MAAA,aAA+B;UAG/BnmB,uDAAA,GAAoC;UAApCA,wDAAA,aAAAiB,GAAA,CAAAklB,MAAA,kBAAoC;UAU5CnmB,uDAAA,GAEC;UAFDA,wDAAA,UAAAiB,GAAA,CAAAmlB,SAAA,0DAEC;UAGCpmB,uDAAA,GAEC;UAFDA,wDAAA,CAAAiB,GAAA,CAAAmlB,SAAA,mDAEC;UAcRpmB,uDAAA,GAAoB;UAApBA,wDAAA,SAAAiB,GAAA,CAAAwjB,UAAA,KAAoB;UAqBnBzkB,uDAAA,GAA8B;UAA9BA,wDAAA,SAAAiB,GAAA,CAAA8kB,OAAA,KAAA9kB,GAAA,CAAA7B,KAAA,CAAAE,MAAA,CAA8B;UAM9BU,uDAAA,GAAoC;UAApCA,wDAAA,UAAAiB,GAAA,CAAA8kB,OAAA,IAAA9kB,GAAA,CAAA7B,KAAA,CAAAE,MAAA,OAAoC;UAWrCU,uDAAA,GAAsB;UAAtBA,wDAAA,SAAAiB,GAAA,CAAA7B,KAAA,CAAAE,MAAA,KAAsB;UA+CrBU,uDAAA,GAAiC;UAAjCA,wDAAA,SAAAiB,GAAA,CAAA8kB,OAAA,IAAA9kB,GAAA,CAAA7B,KAAA,CAAAE,MAAA,KAAiC;UAYjCU,uDAAA,GAA6B;UAA7BA,wDAAA,SAAAiB,GAAA,CAAAglB,WAAA,KAAAhlB,GAAA,CAAA8kB,OAAA,CAA6B", "sources": ["./src/app/components/system-status/system-status.component.ts", "./src/app/services/call.service.ts", "./src/app/views/front/messages/message-chat/message-chat.component.ts", "./src/app/views/front/messages/message-chat/message-chat.component.html", "./src/app/views/front/messages/messages-list/messages-list.component.ts", "./src/app/views/front/messages/messages-routing.module.ts", "./src/app/views/front/messages/messages.module.ts", "./src/app/views/front/messages/user-list/user-list.component.ts", "./src/app/views/front/messages/user-list/user-list.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { Subscription, interval } from 'rxjs';\nimport { MessageService } from '../../services/message.service';\nimport { MockDataService } from '../../services/mock-data.service';\nimport { ThemeService } from '../../services/theme.service';\nimport { ToastService } from '../../services/toast.service';\n\ninterface SystemStatus {\n  backend: 'online' | 'offline' | 'checking';\n  frontend: 'online' | 'offline' | 'checking';\n  database: 'online' | 'offline' | 'checking';\n  websocket: 'online' | 'offline' | 'checking';\n  mockData: 'available' | 'unavailable';\n  theme: string;\n  lastCheck: Date;\n}\n\n@Component({\n  selector: 'app-system-status',\n  template: `\n    <div class=\"system-status-panel bg-gray-800 rounded-lg p-6 border border-gray-700\">\n      <div class=\"flex items-center justify-between mb-4\">\n        <h3 class=\"text-lg font-semibold text-white flex items-center\">\n          <i class=\"fas fa-heartbeat text-blue-400 mr-2\"></i>\n          État du système\n        </h3>\n        <button \n          (click)=\"checkSystemStatus()\"\n          class=\"px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors\"\n          [disabled]=\"isChecking\"\n        >\n          <i class=\"fas fa-sync-alt mr-1\" [class.fa-spin]=\"isChecking\"></i>\n          {{ isChecking ? 'Vérification...' : 'Actualiser' }}\n        </button>\n      </div>\n\n      <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n        <!-- Backend Status -->\n        <div class=\"status-item\">\n          <div class=\"flex items-center justify-between\">\n            <span class=\"text-gray-300\">Backend</span>\n            <div class=\"flex items-center\">\n              <div \n                class=\"w-3 h-3 rounded-full mr-2\"\n                [class.bg-green-500]=\"status.backend === 'online'\"\n                [class.bg-red-500]=\"status.backend === 'offline'\"\n                [class.bg-yellow-500]=\"status.backend === 'checking'\"\n                [class.animate-pulse]=\"status.backend === 'checking'\"\n              ></div>\n              <span class=\"text-sm\" [class.text-green-400]=\"status.backend === 'online'\"\n                    [class.text-red-400]=\"status.backend === 'offline'\"\n                    [class.text-yellow-400]=\"status.backend === 'checking'\">\n                {{ getStatusText(status.backend) }}\n              </span>\n            </div>\n          </div>\n        </div>\n\n        <!-- Frontend Status -->\n        <div class=\"status-item\">\n          <div class=\"flex items-center justify-between\">\n            <span class=\"text-gray-300\">Frontend</span>\n            <div class=\"flex items-center\">\n              <div \n                class=\"w-3 h-3 rounded-full mr-2\"\n                [class.bg-green-500]=\"status.frontend === 'online'\"\n                [class.bg-red-500]=\"status.frontend === 'offline'\"\n                [class.bg-yellow-500]=\"status.frontend === 'checking'\"\n                [class.animate-pulse]=\"status.frontend === 'checking'\"\n              ></div>\n              <span class=\"text-sm\" [class.text-green-400]=\"status.frontend === 'online'\"\n                    [class.text-red-400]=\"status.frontend === 'offline'\"\n                    [class.text-yellow-400]=\"status.frontend === 'checking'\">\n                {{ getStatusText(status.frontend) }}\n              </span>\n            </div>\n          </div>\n        </div>\n\n        <!-- Database Status -->\n        <div class=\"status-item\">\n          <div class=\"flex items-center justify-between\">\n            <span class=\"text-gray-300\">Base de données</span>\n            <div class=\"flex items-center\">\n              <div \n                class=\"w-3 h-3 rounded-full mr-2\"\n                [class.bg-green-500]=\"status.database === 'online'\"\n                [class.bg-red-500]=\"status.database === 'offline'\"\n                [class.bg-yellow-500]=\"status.database === 'checking'\"\n                [class.animate-pulse]=\"status.database === 'checking'\"\n              ></div>\n              <span class=\"text-sm\" [class.text-green-400]=\"status.database === 'online'\"\n                    [class.text-red-400]=\"status.database === 'offline'\"\n                    [class.text-yellow-400]=\"status.database === 'checking'\">\n                {{ getStatusText(status.database) }}\n              </span>\n            </div>\n          </div>\n        </div>\n\n        <!-- WebSocket Status -->\n        <div class=\"status-item\">\n          <div class=\"flex items-center justify-between\">\n            <span class=\"text-gray-300\">WebSocket</span>\n            <div class=\"flex items-center\">\n              <div \n                class=\"w-3 h-3 rounded-full mr-2\"\n                [class.bg-green-500]=\"status.websocket === 'online'\"\n                [class.bg-red-500]=\"status.websocket === 'offline'\"\n                [class.bg-yellow-500]=\"status.websocket === 'checking'\"\n                [class.animate-pulse]=\"status.websocket === 'checking'\"\n              ></div>\n              <span class=\"text-sm\" [class.text-green-400]=\"status.websocket === 'online'\"\n                    [class.text-red-400]=\"status.websocket === 'offline'\"\n                    [class.text-yellow-400]=\"status.websocket === 'checking'\">\n                {{ getStatusText(status.websocket) }}\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Mock Data Status -->\n      <div class=\"mt-4 p-3 bg-gray-700 rounded border-l-4\" \n           [class.border-green-500]=\"status.mockData === 'available'\"\n           [class.border-red-500]=\"status.mockData === 'unavailable'\">\n        <div class=\"flex items-center justify-between\">\n          <span class=\"text-gray-300\">Données de test</span>\n          <span class=\"text-sm\" [class.text-green-400]=\"status.mockData === 'available'\"\n                [class.text-red-400]=\"status.mockData === 'unavailable'\">\n            {{ status.mockData === 'available' ? 'Disponibles' : 'Indisponibles' }}\n          </span>\n        </div>\n        <p class=\"text-xs text-gray-400 mt-1\">\n          {{ status.mockData === 'available' ? \n             'Le mode démo est actif avec des données de test' : \n             'Aucune donnée de test disponible' }}\n        </p>\n      </div>\n\n      <!-- Theme Status -->\n      <div class=\"mt-4 p-3 bg-gray-700 rounded\">\n        <div class=\"flex items-center justify-between\">\n          <span class=\"text-gray-300\">Thème actuel</span>\n          <span class=\"text-sm text-blue-400\">{{ status.theme }}</span>\n        </div>\n      </div>\n\n      <!-- Last Check -->\n      <div class=\"mt-4 text-xs text-gray-500 text-center\">\n        Dernière vérification : {{ status.lastCheck | date:'medium' }}\n      </div>\n\n      <!-- Test Actions -->\n      <div class=\"mt-6 grid grid-cols-1 md:grid-cols-3 gap-2\">\n        <button \n          (click)=\"testMockData()\"\n          class=\"px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded text-sm transition-colors\"\n        >\n          <i class=\"fas fa-database mr-1\"></i>\n          Test données\n        </button>\n        <button \n          (click)=\"testThemes()\"\n          class=\"px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded text-sm transition-colors\"\n        >\n          <i class=\"fas fa-palette mr-1\"></i>\n          Test thèmes\n        </button>\n        <button \n          (click)=\"testNotifications()\"\n          class=\"px-3 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded text-sm transition-colors\"\n        >\n          <i class=\"fas fa-bell mr-1\"></i>\n          Test notifs\n        </button>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .status-item {\n      @apply p-3 bg-gray-700 rounded;\n    }\n    \n    .status-item:hover {\n      @apply bg-gray-600;\n    }\n  `]\n})\nexport class SystemStatusComponent implements OnInit, OnDestroy {\n  status: SystemStatus = {\n    backend: 'checking',\n    frontend: 'online', // Frontend is obviously online if this component is running\n    database: 'checking',\n    websocket: 'checking',\n    mockData: 'checking' as any,\n    theme: 'Chargement...',\n    lastCheck: new Date()\n  };\n\n  isChecking = false;\n  private subscription?: Subscription;\n\n  constructor(\n    private messageService: MessageService,\n    private mockDataService: MockDataService,\n    private themeService: ThemeService,\n    private toastService: ToastService\n  ) {}\n\n  ngOnInit(): void {\n    this.checkSystemStatus();\n    \n    // Auto-refresh every 30 seconds\n    this.subscription = interval(30000).subscribe(() => {\n      this.checkSystemStatus();\n    });\n\n    // Listen to theme changes\n    this.themeService.currentTheme$.subscribe(theme => {\n      this.status.theme = theme.displayName;\n    });\n  }\n\n  ngOnDestroy(): void {\n    this.subscription?.unsubscribe();\n  }\n\n  async checkSystemStatus(): Promise<void> {\n    this.isChecking = true;\n    this.status.lastCheck = new Date();\n\n    // Check mock data availability\n    try {\n      await this.mockDataService.getUsers().toPromise();\n      this.status.mockData = 'available';\n    } catch {\n      this.status.mockData = 'unavailable';\n    }\n\n    // Check backend connectivity\n    try {\n      await this.messageService.getConversations().toPromise();\n      this.status.backend = 'online';\n      this.status.database = 'online';\n      this.status.websocket = 'online';\n    } catch {\n      this.status.backend = 'offline';\n      this.status.database = 'offline';\n      this.status.websocket = 'offline';\n    }\n\n    this.isChecking = false;\n  }\n\n  getStatusText(status: string): string {\n    switch (status) {\n      case 'online': return 'En ligne';\n      case 'offline': return 'Hors ligne';\n      case 'checking': return 'Vérification...';\n      default: return 'Inconnu';\n    }\n  }\n\n  testMockData(): void {\n    this.mockDataService.getUsers().subscribe({\n      next: (users) => {\n        this.toastService.showSuccess(`${users.length} utilisateurs de test chargés`);\n      },\n      error: () => {\n        this.toastService.showError('Erreur lors du chargement des données de test');\n      }\n    });\n  }\n\n  testThemes(): void {\n    const themes = this.themeService.getAvailableThemes();\n    this.toastService.showInfo(`${themes.length} thèmes disponibles`);\n  }\n\n  testNotifications(): void {\n    this.toastService.showSuccess('Test de notification réussi !');\n    setTimeout(() => {\n      this.toastService.showInfo('Notification d\\'information');\n    }, 1000);\n    setTimeout(() => {\n      this.toastService.showWarning('Notification d\\'avertissement');\n    }, 2000);\n  }\n}\n", "import { Injectable, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';\nimport { Apollo } from 'apollo-angular';\nimport { BehaviorSubject, Observable, throwError, of } from 'rxjs';\nimport { map, catchError, tap } from 'rxjs/operators';\nimport {\n  Call,\n  CallType,\n  CallStatus,\n  IncomingCall,\n  CallSuccess,\n  CallSignal,\n} from '../models/message.model';\nimport {\n  INITIATE_CALL_MUTATION,\n  ACCEPT_CALL_MUTATION,\n  REJECT_CALL_MUTATION,\n  END_CALL_MUTATION,\n  INCOMING_CALL_SUBSCRIPTION,\n  CALL_STATUS_CHANGED_SUBSCRIPTION,\n  CALL_SIGNAL_SUBSCRIPTION,\n  SEND_CALL_SIGNAL_MUTATION,\n} from '../graphql/message.graphql';\nimport { LoggerService } from './logger.service';\n\n/**\n * Service unifié pour la gestion des appels vidéo/audio\n * Gère l'état des appels, WebRTC, et la synchronisation\n */\n@Injectable({\n  providedIn: 'root',\n})\nexport class CallService implements OnD<PERSON>roy {\n  // ===== ÉTAT PRINCIPAL =====\n  private activeCall = new BehaviorSubject<Call | null>(null);\n  private incomingCall = new BehaviorSubject<IncomingCall | null>(null);\n  private callSignals = new BehaviorSubject<CallSignal | null>(null);\n\n  // Observables publics\n  public activeCall$ = this.activeCall.asObservable();\n  public incomingCall$ = this.incomingCall.asObservable();\n  public callSignals$ = this.callSignals.asObservable();\n\n  // ===== ÉTAT DES APPELS =====\n  private currentCallId: string | null = null;\n  private callState:\n    | 'idle'\n    | 'initiating'\n    | 'ringing'\n    | 'connecting'\n    | 'connected'\n    | 'ending' = 'idle';\n\n  // ===== GESTION AUDIO =====\n  private sounds: { [key: string]: HTMLAudioElement } = {};\n  private isPlaying: { [key: string]: boolean } = {};\n\n  // ===== WEBRTC =====\n  private peerConnection: RTCPeerConnection | null = null;\n  private localStream: MediaStream | null = null;\n  private remoteStream: MediaStream | null = null;\n  private localVideoElement: HTMLVideoElement | null = null;\n  private remoteVideoElement: HTMLVideoElement | null = null;\n  private isAudioEnabled = true;\n  private isVideoEnabled = true;\n\n  // Configuration WebRTC\n  private readonly rtcConfig: RTCConfiguration = {\n    iceServers: [\n      { urls: 'stun:stun.l.google.com:19302' },\n      { urls: 'stun:stun1.l.google.com:19302' },\n    ],\n  };\n\n  constructor(private apollo: Apollo, private logger: LoggerService) {\n    this.logger.info('CallService', '🚀 Initializing unified CallService...');\n    this.initializeSounds();\n    this.initializeSubscriptions();\n    this.initializeWebRTC();\n    this.logger.info('CallService', '✅ CallService initialized successfully');\n  }\n\n  ngOnDestroy(): void {\n    this.logger.info('CallService', '🔄 Destroying CallService...');\n    this.cleanup();\n  }\n\n  // ===== MÉTHODES PUBLIQUES PRINCIPALES =====\n\n  /**\n   * Initie un appel\n   */\n  initiateCall(\n    recipientId: string,\n    callType: CallType,\n    conversationId?: string\n  ): Observable<Call> {\n    this.logger.info('CallService', '📞 Initiating call:', {\n      recipientId,\n      callType,\n    });\n\n    if (this.callState !== 'idle') {\n      return throwError(() => new Error('Another call is already in progress'));\n    }\n\n    this.setCallState('initiating');\n    const callId = this.generateCallId();\n\n    return this.apollo\n      .mutate<{ initiateCall: Call }>({\n        mutation: INITIATE_CALL_MUTATION,\n        variables: {\n          recipientId,\n          callType,\n          callId,\n          conversationId,\n          // ✅ offer est maintenant optionnel - sera généré par WebRTC plus tard\n        },\n      })\n      .pipe(\n        map((result) => {\n          const call = result.data?.initiateCall;\n          if (!call) throw new Error('Failed to initiate call');\n\n          this.handleCallInitiated(call);\n          return call;\n        }),\n        catchError((error) => {\n          this.logger.error('CallService', 'Error initiating call:', error);\n          this.setCallState('idle');\n          return throwError(() => error);\n        })\n      );\n  }\n\n  /**\n   * Accepte un appel entrant\n   */\n  acceptCall(call: IncomingCall): Observable<Call> {\n    this.logger.info('CallService', '✅ Accepting call:', call.id);\n\n    if (!call) {\n      return throwError(() => new Error('No call to accept'));\n    }\n\n    this.setCallState('connecting');\n\n    return this.apollo\n      .mutate<{ acceptCall: Call }>({\n        mutation: ACCEPT_CALL_MUTATION,\n        variables: {\n          callId: call.id,\n          // ✅ answer est maintenant optionnel - sera généré par WebRTC plus tard\n        },\n      })\n      .pipe(\n        map((result) => {\n          const acceptedCall = result.data?.acceptCall;\n          if (!acceptedCall) throw new Error('Failed to accept call');\n\n          this.handleCallAccepted(acceptedCall);\n          return acceptedCall;\n        }),\n        catchError((error) => {\n          this.logger.error('CallService', 'Error accepting call:', error);\n          this.setCallState('idle');\n          return throwError(() => error);\n        })\n      );\n  }\n\n  /**\n   * Rejette un appel\n   */\n  rejectCall(callId: string, reason?: string): Observable<CallSuccess> {\n    this.logger.info('CallService', '❌ Rejecting call:', callId);\n\n    this.setCallState('ending');\n\n    return this.apollo\n      .mutate<{ rejectCall: CallSuccess }>({\n        mutation: REJECT_CALL_MUTATION,\n        variables: { callId, reason: reason || 'User rejected' },\n      })\n      .pipe(\n        map((result) => {\n          const success = result.data?.rejectCall;\n          if (!success) throw new Error('Failed to reject call');\n\n          this.handleCallEnded();\n          return success;\n        }),\n        catchError((error) => {\n          this.logger.error('CallService', 'Error rejecting call:', error);\n          this.handleCallEnded(); // Nettoyer même en cas d'erreur\n          return throwError(() => error);\n        })\n      );\n  }\n\n  /**\n   * Termine un appel\n   */\n  endCall(callId: string): Observable<CallSuccess> {\n    this.logger.info('CallService', '🔚 Ending call:', callId);\n\n    this.setCallState('ending');\n\n    return this.apollo\n      .mutate<{ endCall: CallSuccess }>({\n        mutation: END_CALL_MUTATION,\n        variables: { callId },\n      })\n      .pipe(\n        map((result) => {\n          const success = result.data?.endCall;\n          if (!success) throw new Error('Failed to end call');\n\n          this.handleCallEnded();\n          return success;\n        }),\n        catchError((error) => {\n          this.logger.error('CallService', 'Error ending call:', error);\n          this.handleCallEnded(); // Nettoyer même en cas d'erreur\n          return throwError(() => error);\n        })\n      );\n  }\n\n  // ===== GETTERS PUBLICS =====\n\n  get currentCall(): Call | null {\n    return this.activeCall.value;\n  }\n\n  get currentIncomingCall(): IncomingCall | null {\n    return this.incomingCall.value;\n  }\n\n  get isCallActive(): boolean {\n    return this.callState === 'connected';\n  }\n\n  get isCallInProgress(): boolean {\n    return this.callState !== 'idle';\n  }\n\n  // ===== MÉTHODES PRIVÉES =====\n\n  private generateCallId(): string {\n    return `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  private setCallState(state: typeof this.callState): void {\n    this.logger.debug(\n      'CallService',\n      `Call state: ${this.callState} → ${state}`\n    );\n    this.callState = state;\n  }\n\n  private handleCallInitiated(call: Call): void {\n    this.logger.info('CallService', 'Call initiated successfully:', call.id);\n    this.currentCallId = call.id;\n    this.activeCall.next(call);\n    this.setCallState('ringing');\n    this.play('ringtone', true);\n    this.startOutgoingCallMedia(call.type);\n  }\n\n  private handleCallAccepted(call: Call): void {\n    this.logger.info('CallService', 'Call accepted successfully:', call.id);\n    this.activeCall.next(call);\n    this.incomingCall.next(null);\n    this.setCallState('connected');\n    this.stop('ringtone');\n    this.play('call-connected');\n  }\n\n  private handleCallEnded(): void {\n    this.logger.info('CallService', 'Call ended, cleaning up');\n    this.setCallState('idle');\n    this.currentCallId = null;\n    this.activeCall.next(null);\n    this.incomingCall.next(null);\n    this.stopAllSounds();\n    this.play('call-end');\n    this.cleanupWebRTC();\n  }\n\n  private handleIncomingCall(call: IncomingCall): void {\n    this.logger.info('CallService', 'Incoming call received:', call.id);\n    this.currentCallId = call.id;\n    this.incomingCall.next(call);\n    this.setCallState('ringing');\n    this.play('ringtone', true);\n    this.prepareForIncomingCall(call);\n  }\n\n  private handleCallStatusChange(call: Call): void {\n    this.logger.info('CallService', 'Call status changed:', call.status);\n\n    if (call.id === this.currentCallId) {\n      this.activeCall.next(call);\n\n      switch (call.status) {\n        case CallStatus.CONNECTED:\n          this.setCallState('connected');\n          this.stop('ringtone');\n          this.play('call-connected');\n          break;\n        case CallStatus.ENDED:\n        case CallStatus.REJECTED:\n          this.handleCallEnded();\n          break;\n      }\n    }\n  }\n\n  private handleCallSignal(signal: CallSignal): void {\n    this.logger.debug('CallService', 'Call signal received:', signal.type);\n    this.callSignals.next(signal);\n    // Traitement WebRTC des signaux sera ajouté ici\n  }\n\n  // ===== INITIALISATION =====\n\n  private initializeSounds(): void {\n    this.logger.debug('CallService', 'Initializing sounds...');\n    this.createSyntheticSounds();\n  }\n\n  private createSyntheticSounds(): void {\n    this.createSyntheticSound('ringtone', [440, 554.37], 1.5, true);\n    this.createSyntheticSound(\n      'call-connected',\n      [523.25, 659.25, 783.99],\n      0.8,\n      false\n    );\n    this.createSyntheticSound('call-end', [392, 329.63, 261.63], 1.2, false);\n  }\n\n  private createSyntheticSound(\n    name: string,\n    frequencies: number[],\n    duration: number,\n    loop: boolean\n  ): void {\n    try {\n      const audioContext = new (window.AudioContext ||\n        (window as any).webkitAudioContext)();\n      const sampleRate = audioContext.sampleRate;\n      const frameCount = sampleRate * duration;\n      const buffer = audioContext.createBuffer(1, frameCount, sampleRate);\n      const channelData = buffer.getChannelData(0);\n\n      for (let i = 0; i < frameCount; i++) {\n        let sample = 0;\n        frequencies.forEach((freq) => {\n          const amplitude = 0.3 / frequencies.length;\n          const phase = (i / sampleRate) * freq * 2 * Math.PI;\n          sample += Math.sin(phase) * amplitude;\n        });\n        const envelope = Math.sin((i / frameCount) * Math.PI);\n        channelData[i] = sample * envelope;\n      }\n\n      const audio = new Audio();\n      audio.loop = loop;\n      (audio as any).customPlay = () => {\n        const source = audioContext.createBufferSource();\n        source.buffer = buffer;\n        source.loop = loop;\n        source.connect(audioContext.destination);\n        source.start();\n        if (!loop) {\n          setTimeout(() => {\n            this.isPlaying[name] = false;\n          }, duration * 1000);\n        }\n        return source;\n      };\n\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n    } catch (error) {\n      this.logger.error(\n        'CallService',\n        `Error creating sound '${name}':`,\n        error\n      );\n    }\n  }\n\n  private initializeSubscriptions(): void {\n    this.logger.debug('CallService', 'Initializing subscriptions...');\n    this.subscribeToIncomingCalls();\n    this.subscribeToCallStatusChanges();\n    this.subscribeToCallSignals();\n  }\n\n  private subscribeToIncomingCalls(): void {\n    this.apollo\n      .subscribe<{ incomingCall: IncomingCall }>({\n        query: INCOMING_CALL_SUBSCRIPTION,\n        errorPolicy: 'all',\n      })\n      .subscribe({\n        next: ({ data, errors }) => {\n          if (data?.incomingCall) {\n            this.handleIncomingCall(data.incomingCall);\n          }\n          if (errors) {\n            this.logger.error(\n              'CallService',\n              'Incoming call subscription errors:',\n              errors\n            );\n          }\n        },\n        error: (error) => {\n          this.logger.error(\n            'CallService',\n            'Error in incoming call subscription:',\n            error\n          );\n          setTimeout(() => this.subscribeToIncomingCalls(), 5000);\n        },\n      });\n  }\n\n  private subscribeToCallStatusChanges(): void {\n    this.apollo\n      .subscribe<{ callStatusChanged: Call }>({\n        query: CALL_STATUS_CHANGED_SUBSCRIPTION,\n        errorPolicy: 'all',\n      })\n      .subscribe({\n        next: ({ data, errors }) => {\n          if (data?.callStatusChanged) {\n            this.handleCallStatusChange(data.callStatusChanged);\n          }\n          if (errors) {\n            this.logger.error(\n              'CallService',\n              'Call status subscription errors:',\n              errors\n            );\n          }\n        },\n        error: (error) => {\n          this.logger.error(\n            'CallService',\n            'Error in call status subscription:',\n            error\n          );\n          setTimeout(() => this.subscribeToCallStatusChanges(), 5000);\n        },\n      });\n  }\n\n  private subscribeToCallSignals(): void {\n    this.apollo\n      .subscribe<{ callSignal: CallSignal }>({\n        query: CALL_SIGNAL_SUBSCRIPTION,\n        errorPolicy: 'all',\n      })\n      .subscribe({\n        next: ({ data, errors }) => {\n          if (data?.callSignal) {\n            this.handleCallSignal(data.callSignal);\n          }\n          if (errors) {\n            this.logger.error(\n              'CallService',\n              'Call signal subscription errors:',\n              errors\n            );\n          }\n        },\n        error: (error) => {\n          this.logger.error(\n            'CallService',\n            'Error in call signal subscription:',\n            error\n          );\n          setTimeout(() => this.subscribeToCallSignals(), 5000);\n        },\n      });\n  }\n\n  private initializeWebRTC(): void {\n    this.logger.debug('CallService', 'Initializing WebRTC...');\n    this.createPeerConnection();\n  }\n\n  // ===== GESTION AUDIO =====\n\n  private play(name: string, loop: boolean = false): void {\n    try {\n      const sound = this.sounds[name];\n      if (!sound || this.isPlaying[name]) return;\n\n      if ((sound as any).customPlay) {\n        (sound as any).currentSource = (sound as any).customPlay();\n        this.isPlaying[name] = true;\n      }\n    } catch (error) {\n      this.logger.error('CallService', `Error playing sound '${name}':`, error);\n    }\n  }\n\n  private stop(name: string): void {\n    try {\n      const sound = this.sounds[name];\n      if (!sound || !this.isPlaying[name]) return;\n\n      if ((sound as any).currentSource) {\n        (sound as any).currentSource.stop();\n        (sound as any).currentSource = null;\n      }\n      this.isPlaying[name] = false;\n    } catch (error) {\n      this.logger.error(\n        'CallService',\n        `Error stopping sound '${name}':`,\n        error\n      );\n    }\n  }\n\n  private stopAllSounds(): void {\n    Object.keys(this.sounds).forEach((name) => this.stop(name));\n  }\n\n  // ===== WEBRTC =====\n\n  private createPeerConnection(): void {\n    try {\n      this.peerConnection = new RTCPeerConnection(this.rtcConfig);\n      this.logger.debug('CallService', 'PeerConnection created successfully');\n\n      this.peerConnection.onicecandidate = (event) => {\n        if (event.candidate && this.currentCallId) {\n          this.sendSignal('ice-candidate', JSON.stringify(event.candidate));\n        }\n      };\n\n      this.peerConnection.ontrack = (event) => {\n        this.logger.info(\n          'CallService',\n          'Remote track received:',\n          event.track.kind\n        );\n        this.remoteStream = event.streams[0];\n        this.attachRemoteStream();\n      };\n\n      this.peerConnection.onconnectionstatechange = () => {\n        const state = this.peerConnection?.connectionState;\n        this.logger.debug('CallService', 'Connection state changed:', state);\n\n        if (state === 'connected') {\n          this.logger.info('CallService', '✅ WebRTC connection established');\n          this.setCallState('connected');\n        } else if (state === 'failed') {\n          this.logger.error('CallService', '❌ WebRTC connection failed');\n          this.handleCallEnded();\n        }\n      };\n    } catch (error) {\n      this.logger.error('CallService', 'Error creating PeerConnection:', error);\n    }\n  }\n\n  private async startOutgoingCallMedia(callType: CallType): Promise<void> {\n    try {\n      this.logger.info('CallService', '🎥 Starting outgoing call media');\n      const stream = await this.getUserMedia(callType);\n      this.addLocalStreamToPeerConnection(stream);\n      this.attachLocalStream();\n    } catch (error) {\n      this.logger.error(\n        'CallService',\n        'Error starting outgoing call media:',\n        error\n      );\n    }\n  }\n\n  private async prepareForIncomingCall(call: IncomingCall): Promise<void> {\n    try {\n      this.logger.debug('CallService', 'Preparing WebRTC for incoming call');\n      if (!this.peerConnection) {\n        this.createPeerConnection();\n      }\n      const stream = await this.getUserMedia(call.type);\n      this.addLocalStreamToPeerConnection(stream);\n    } catch (error) {\n      this.logger.error(\n        'CallService',\n        'Error preparing for incoming call:',\n        error\n      );\n    }\n  }\n\n  private async getUserMedia(callType: CallType): Promise<MediaStream> {\n    const constraints: MediaStreamConstraints = {\n      audio: true,\n      video: callType === CallType.VIDEO,\n    };\n\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia(constraints);\n      this.localStream = stream;\n      return stream;\n    } catch (error) {\n      this.logger.error('CallService', 'Error getting user media:', error);\n      throw error;\n    }\n  }\n\n  private addLocalStreamToPeerConnection(stream: MediaStream): void {\n    if (!this.peerConnection) return;\n\n    stream.getTracks().forEach((track) => {\n      this.peerConnection!.addTrack(track, stream);\n    });\n  }\n\n  private attachLocalStream(): void {\n    if (this.localVideoElement && this.localStream) {\n      this.localVideoElement.srcObject = this.localStream;\n    }\n  }\n\n  private attachRemoteStream(): void {\n    if (this.remoteVideoElement && this.remoteStream) {\n      this.remoteVideoElement.srcObject = this.remoteStream;\n    }\n  }\n\n  private sendSignal(signalType: string, signalData: string): void {\n    if (!this.currentCallId) return;\n\n    this.apollo\n      .mutate({\n        mutation: SEND_CALL_SIGNAL_MUTATION,\n        variables: {\n          callId: this.currentCallId,\n          signalType,\n          signalData,\n        },\n      })\n      .subscribe({\n        next: () =>\n          this.logger.debug('CallService', 'Signal sent:', signalType),\n        error: (error) =>\n          this.logger.error('CallService', 'Error sending signal:', error),\n      });\n  }\n\n  // ===== NETTOYAGE =====\n\n  private cleanupWebRTC(): void {\n    this.logger.debug('CallService', 'Cleaning up WebRTC resources');\n\n    if (this.localStream) {\n      this.localStream.getTracks().forEach((track) => track.stop());\n      this.localStream = null;\n    }\n\n    if (this.remoteStream) {\n      this.remoteStream = null;\n    }\n\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n\n    if (this.localVideoElement) {\n      this.localVideoElement.srcObject = null;\n    }\n\n    if (this.remoteVideoElement) {\n      this.remoteVideoElement.srcObject = null;\n    }\n\n    // Recréer une nouvelle PeerConnection pour les futurs appels\n    this.createPeerConnection();\n  }\n\n  private cleanup(): void {\n    this.stopAllSounds();\n    this.cleanupWebRTC();\n    this.activeCall.complete();\n    this.incomingCall.complete();\n    this.callSignals.complete();\n  }\n\n  // ===== MÉTHODES PUBLIQUES UTILITAIRES =====\n\n  /**\n   * Attache les éléments vidéo pour l'affichage\n   */\n  attachVideoElements(\n    localVideo: HTMLVideoElement,\n    remoteVideo: HTMLVideoElement\n  ): void {\n    this.localVideoElement = localVideo;\n    this.remoteVideoElement = remoteVideo;\n\n    if (this.localStream) {\n      this.attachLocalStream();\n    }\n    if (this.remoteStream) {\n      this.attachRemoteStream();\n    }\n  }\n\n  /**\n   * Active/désactive l'audio\n   */\n  toggleAudio(): boolean {\n    this.isAudioEnabled = !this.isAudioEnabled;\n    if (this.localStream) {\n      this.localStream.getAudioTracks().forEach((track) => {\n        track.enabled = this.isAudioEnabled;\n      });\n    }\n    return this.isAudioEnabled;\n  }\n\n  /**\n   * Active/désactive la vidéo\n   */\n  toggleVideo(): boolean {\n    this.isVideoEnabled = !this.isVideoEnabled;\n    if (this.localStream) {\n      this.localStream.getVideoTracks().forEach((track) => {\n        track.enabled = this.isVideoEnabled;\n      });\n    }\n    return this.isVideoEnabled;\n  }\n\n  /**\n   * Méthode de compatibilité pour setVideoElements\n   */\n  setVideoElements(\n    localVideo: HTMLVideoElement,\n    remoteVideo: HTMLVideoElement\n  ): void {\n    this.attachVideoElements(localVideo, remoteVideo);\n  }\n\n  /**\n   * Obtient l'état audio actuel\n   */\n  get audioEnabled(): boolean {\n    return this.isAudioEnabled;\n  }\n\n  /**\n   * Obtient l'état vidéo actuel\n   */\n  get videoEnabled(): boolean {\n    return this.isVideoEnabled;\n  }\n\n  /**\n   * Obtient le stream local\n   */\n  get localMediaStream(): MediaStream | null {\n    return this.localStream;\n  }\n\n  /**\n   * Obtient le stream distant\n   */\n  get remoteMediaStream(): MediaStream | null {\n    return this.remoteStream;\n  }\n\n  /**\n   * Active les sons (méthode de compatibilité)\n   */\n  enableSounds(): void {\n    this.logger.debug(\n      'CallService',\n      'Sounds are always enabled in unified service'\n    );\n  }\n\n  /**\n   * Désactive les sons (méthode de compatibilité)\n   */\n  disableSounds(): void {\n    this.logger.debug('CallService', 'Disabling sounds');\n    this.stopAllSounds();\n  }\n}\n", "import {\r\n  Compo<PERSON>,\r\n  On<PERSON>nit,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  AfterViewInit,\r\n  ViewChild,\r\n  ElementRef,\r\n  ChangeDetectorRef,\r\n  NgZone,\r\n} from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { Subscription, BehaviorSubject, combineLatest, of } from 'rxjs';\r\nimport {\r\n  debounceTime,\r\n  distinctUntilChanged,\r\n  switchMap,\r\n  catchError,\r\n  tap,\r\n  filter,\r\n  map,\r\n} from 'rxjs/operators';\r\nimport { MessageService } from '../../../../services/message.service';\r\nimport { AuthService } from '../../../../services/auth.service';\r\nimport { ToastService } from '../../../../services/toast.service';\r\nimport {\r\n  Message,\r\n  Conversation,\r\n  User,\r\n  MessageType,\r\n  CallType,\r\n  Attachment,\r\n} from '../../../../models/message.model';\r\n\r\n@Component({\r\n  selector: 'app-message-chat',\r\n  templateUrl: './message-chat.component.html',\r\n  styleUrls: ['./message-chat.component.css'],\r\n})\r\nexport class MessageChatComponent implements OnInit, AfterViewInit, OnDestroy {\r\n  @ViewChild('messagesContainer') messagesContainer!: ElementRef;\r\n  @ViewChild('messageInput') messageInput!: ElementRef;\r\n  @ViewChild('fileInput') fileInput!: ElementRef;\r\n  @ViewChild('voiceRecorder') voiceRecorder!: ElementRef;\r\n\r\n  // État du composant\r\n  currentUser: User | null = null;\r\n  selectedConversation: Conversation | null = null;\r\n  messages: Message[] = [];\r\n  isLoading = false;\r\n  isTyping = false;\r\n  typingUsers: User[] = [];\r\n\r\n  // Pagination\r\n  currentPage = 1;\r\n  hasMoreMessages = true;\r\n  loadingMoreMessages = false;\r\n\r\n  // Formulaire de message\r\n  messageContent = '';\r\n  selectedFiles: File[] = [];\r\n  isRecording = false;\r\n  recordingDuration = 0;\r\n\r\n  // États UI\r\n  showEmojiPicker = false;\r\n  showAttachmentMenu = false;\r\n  replyingTo: Message | null = null;\r\n  editingMessage: Message | null = null;\r\n\r\n  // Recherche\r\n  searchQuery = '';\r\n  searchResults: Message[] = [];\r\n  showSearchResults = false;\r\n\r\n  // Subscriptions\r\n  private subscriptions: Subscription[] = [];\r\n  private typingTimeout: any;\r\n  private recordingInterval: any;\r\n\r\n  // Observables\r\n  private conversationId$ = new BehaviorSubject<string | null>(null);\r\n\r\n  // Constantes\r\n  readonly MessageType = MessageType;\r\n  readonly CallType = CallType;\r\n\r\n  constructor(\r\n    private messageService: MessageService,\r\n    private authService: AuthService,\r\n    private toastService: ToastService,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private cdr: ChangeDetectorRef,\r\n    private ngZone: NgZone\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.initializeComponent();\r\n    this.setupSubscriptions();\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.scrollToBottom();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.cleanup();\r\n  }\r\n\r\n  // ============================================================================\r\n  // MÉTHODES D'INITIALISATION\r\n  // ============================================================================\r\n\r\n  private initializeComponent(): void {\r\n    // Récupérer l'utilisateur actuel\r\n    this.currentUser = this.authService.getCurrentUser();\r\n\r\n    if (!this.currentUser) {\r\n      this.router.navigate(['/login']);\r\n      return;\r\n    }\r\n\r\n    // Écouter les changements de route pour la conversation\r\n    this.route.params.subscribe((params) => {\r\n      const conversationId = params['conversationId'];\r\n      if (conversationId) {\r\n        this.conversationId$.next(conversationId);\r\n      }\r\n    });\r\n  }\r\n\r\n  private setupSubscriptions(): void {\r\n    // Subscription pour charger la conversation\r\n    const conversationSub = this.conversationId$\r\n      .pipe(\r\n        filter((id) => !!id),\r\n        distinctUntilChanged(),\r\n        tap(() => {\r\n          this.isLoading = true;\r\n          this.messages = [];\r\n          this.currentPage = 1;\r\n          this.hasMoreMessages = true;\r\n        }),\r\n        switchMap((conversationId) =>\r\n          this.messageService.getConversation(conversationId!, 25, 1)\r\n        ),\r\n        catchError((error) => {\r\n          console.error('Erreur lors du chargement de la conversation:', error);\r\n          this.toastService.showError(\r\n            'Erreur lors du chargement de la conversation'\r\n          );\r\n          return of(null);\r\n        })\r\n      )\r\n      .subscribe((conversation) => {\r\n        this.isLoading = false;\r\n        if (conversation) {\r\n          this.selectedConversation = conversation;\r\n          this.messages = conversation.messages || [];\r\n          this.scrollToBottom();\r\n          this.markMessagesAsRead();\r\n        }\r\n        this.cdr.detectChanges();\r\n      });\r\n\r\n    // Subscription pour les nouveaux messages\r\n    const messagesSub = this.messageService\r\n      .subscribeToMessages()\r\n      .subscribe((message) => {\r\n        if (\r\n          message &&\r\n          this.selectedConversation &&\r\n          message.conversationId === this.selectedConversation.id\r\n        ) {\r\n          this.addNewMessage(message);\r\n          this.scrollToBottom();\r\n          this.markMessageAsRead(message);\r\n        }\r\n      });\r\n\r\n    // Subscription pour les indicateurs de frappe\r\n    const typingSub = this.messageService\r\n      .subscribeToTypingIndicators()\r\n      .subscribe((event) => {\r\n        if (\r\n          event &&\r\n          this.selectedConversation &&\r\n          event.conversationId === this.selectedConversation.id\r\n        ) {\r\n          this.handleTypingIndicator(event);\r\n        }\r\n      });\r\n\r\n    this.subscriptions.push(conversationSub, messagesSub, typingSub);\r\n  }\r\n\r\n  private cleanup(): void {\r\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\r\n    if (this.typingTimeout) {\r\n      clearTimeout(this.typingTimeout);\r\n    }\r\n    if (this.recordingInterval) {\r\n      clearInterval(this.recordingInterval);\r\n    }\r\n    this.stopTyping();\r\n  }\r\n\r\n  // ============================================================================\r\n  // MÉTHODES DE GESTION DES MESSAGES\r\n  // ============================================================================\r\n\r\n  sendMessage(): void {\r\n    if (!this.canSendMessage()) {\r\n      return;\r\n    }\r\n\r\n    const content = this.messageContent.trim();\r\n    const files = this.selectedFiles;\r\n\r\n    // Réinitialiser le formulaire\r\n    this.messageContent = '';\r\n    this.selectedFiles = [];\r\n    this.replyingTo = null;\r\n    this.stopTyping();\r\n\r\n    if (this.editingMessage) {\r\n      this.updateMessage(content);\r\n      return;\r\n    }\r\n\r\n    // Envoyer le message\r\n    if (content || files.length > 0) {\r\n      this.sendNewMessage(content, files);\r\n    }\r\n  }\r\n\r\n  canSendMessage(): boolean {\r\n    const hasContent = this.messageContent.trim().length > 0;\r\n    const hasFiles = this.selectedFiles.length > 0;\r\n    const hasConversation = !!this.selectedConversation;\r\n\r\n    return hasConversation && (hasContent || hasFiles);\r\n  }\r\n\r\n  private sendNewMessage(content: string, files: File[]): void {\r\n    if (!this.selectedConversation || !this.currentUser) return;\r\n\r\n    const recipientId = this.getRecipientId();\r\n    if (!recipientId) return;\r\n\r\n    // Créer un message temporaire pour l'affichage immédiat\r\n    const tempMessage: Message = {\r\n      id: `temp-${Date.now()}`,\r\n      content,\r\n      type:\r\n        files.length > 0 ? this.getFileMessageType(files[0]) : MessageType.TEXT,\r\n      timestamp: new Date(),\r\n      sender: this.currentUser,\r\n      isPending: true,\r\n      conversationId: this.selectedConversation.id,\r\n    };\r\n\r\n    this.addNewMessage(tempMessage);\r\n    this.scrollToBottom();\r\n\r\n    // Envoyer le message via le service\r\n    const sendObservable =\r\n      files.length > 0\r\n        ? this.messageService.sendMessage(recipientId, content, files[0])\r\n        : this.messageService.sendMessage(recipientId, content);\r\n\r\n    sendObservable.subscribe({\r\n      next: (sentMessage) => {\r\n        this.replaceTemporaryMessage(tempMessage.id!, sentMessage);\r\n        this.toastService.showSuccess('Message envoyé');\r\n      },\r\n      error: (error) => {\r\n        console.error(\"Erreur lors de l'envoi du message:\", error);\r\n        this.markMessageAsError(tempMessage.id!);\r\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\r\n      },\r\n    });\r\n  }\r\n\r\n  private updateMessage(newContent: string): void {\r\n    if (!this.editingMessage) return;\r\n\r\n    this.messageService\r\n      .editMessage(this.editingMessage.id!, newContent)\r\n      .subscribe({\r\n        next: (updatedMessage) => {\r\n          this.updateMessageInList(updatedMessage);\r\n          this.editingMessage = null;\r\n          this.toastService.showSuccess('Message modifié');\r\n        },\r\n        error: (error) => {\r\n          console.error('Erreur lors de la modification du message:', error);\r\n          this.toastService.showError(\r\n            'Erreur lors de la modification du message'\r\n          );\r\n        },\r\n      });\r\n  }\r\n\r\n  deleteMessage(message: Message): void {\r\n    if (!message.id || !this.canDeleteMessage(message)) return;\r\n\r\n    if (confirm('Êtes-vous sûr de vouloir supprimer ce message ?')) {\r\n      this.messageService.deleteMessage(message.id).subscribe({\r\n        next: () => {\r\n          this.removeMessageFromList(message.id!);\r\n          this.toastService.showSuccess('Message supprimé');\r\n        },\r\n        error: (error) => {\r\n          console.error('Erreur lors de la suppression du message:', error);\r\n          this.toastService.showError(\r\n            'Erreur lors de la suppression du message'\r\n          );\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  reactToMessage(message: Message, emoji: string): void {\r\n    if (!message.id) return;\r\n\r\n    this.messageService.reactToMessage(message.id, emoji).subscribe({\r\n      next: (updatedMessage) => {\r\n        this.updateMessageInList(updatedMessage);\r\n      },\r\n      error: (error) => {\r\n        console.error('Erreur lors de la réaction au message:', error);\r\n        this.toastService.showError('Erreur lors de la réaction');\r\n      },\r\n    });\r\n  }\r\n\r\n  // ============================================================================\r\n  // MÉTHODES DE GESTION DES FICHIERS ET MÉDIAS\r\n  // ============================================================================\r\n\r\n  onFileSelected(event: any): void {\r\n    const files = event.target.files;\r\n    if (files && files.length > 0) {\r\n      this.selectedFiles = Array.from(files);\r\n      this.showAttachmentMenu = false;\r\n\r\n      // Auto-envoyer si c'est juste un fichier sans texte\r\n      if (this.messageContent.trim() === '') {\r\n        this.sendMessage();\r\n      }\r\n    }\r\n  }\r\n\r\n  removeSelectedFile(index: number): void {\r\n    this.selectedFiles.splice(index, 1);\r\n  }\r\n\r\n  openFileSelector(): void {\r\n    this.fileInput.nativeElement.click();\r\n  }\r\n\r\n  // ============================================================================\r\n  // MÉTHODES D'ENREGISTREMENT VOCAL\r\n  // ============================================================================\r\n\r\n  async startVoiceRecording(): Promise<void> {\r\n    try {\r\n      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\r\n      this.isRecording = true;\r\n      this.recordingDuration = 0;\r\n\r\n      // Démarrer le compteur de durée\r\n      this.recordingInterval = setInterval(() => {\r\n        this.recordingDuration++;\r\n      }, 1000);\r\n\r\n      // Ici, vous pouvez implémenter l'enregistrement audio\r\n      // avec MediaRecorder API\r\n    } catch (error) {\r\n      console.error(\"Erreur lors de l'accès au microphone:\", error);\r\n      this.toastService.showError(\"Impossible d'accéder au microphone\");\r\n    }\r\n  }\r\n\r\n  stopVoiceRecording(): void {\r\n    this.isRecording = false;\r\n    if (this.recordingInterval) {\r\n      clearInterval(this.recordingInterval);\r\n    }\r\n\r\n    // Ici, vous pouvez traiter l'enregistrement et l'envoyer\r\n    // comme message vocal\r\n  }\r\n\r\n  cancelVoiceRecording(): void {\r\n    this.isRecording = false;\r\n    this.recordingDuration = 0;\r\n    if (this.recordingInterval) {\r\n      clearInterval(this.recordingInterval);\r\n    }\r\n  }\r\n\r\n  // ============================================================================\r\n  // MÉTHODES D'APPELS AUDIO/VIDÉO\r\n  // ============================================================================\r\n\r\n  startAudioCall(): void {\r\n    if (!this.selectedConversation) return;\r\n\r\n    const recipientId = this.getRecipientId();\r\n    if (!recipientId) return;\r\n\r\n    this.messageService.initiateCall(recipientId, CallType.AUDIO).subscribe({\r\n      next: (call) => {\r\n        this.toastService.showSuccess('Appel audio initié');\r\n        // Rediriger vers l'interface d'appel\r\n      },\r\n      error: (error) => {\r\n        console.error(\"Erreur lors de l'initiation de l'appel:\", error);\r\n        this.toastService.showError(\"Erreur lors de l'appel\");\r\n      },\r\n    });\r\n  }\r\n\r\n  startVideoCall(): void {\r\n    if (!this.selectedConversation) return;\r\n\r\n    const recipientId = this.getRecipientId();\r\n    if (!recipientId) return;\r\n\r\n    this.messageService.initiateCall(recipientId, CallType.VIDEO).subscribe({\r\n      next: (call) => {\r\n        this.toastService.showSuccess('Appel vidéo initié');\r\n        // Rediriger vers l'interface d'appel\r\n      },\r\n      error: (error) => {\r\n        console.error(\"Erreur lors de l'initiation de l'appel vidéo:\", error);\r\n        this.toastService.showError(\"Erreur lors de l'appel vidéo\");\r\n      },\r\n    });\r\n  }\r\n\r\n  // ============================================================================\r\n  // MÉTHODES DE GESTION DE LA FRAPPE\r\n  // ============================================================================\r\n\r\n  onTyping(): void {\r\n    if (!this.selectedConversation || this.isTyping) return;\r\n\r\n    this.isTyping = true;\r\n    this.messageService.startTyping(this.selectedConversation.id!).subscribe();\r\n\r\n    // Arrêter la frappe après 3 secondes d'inactivité\r\n    if (this.typingTimeout) {\r\n      clearTimeout(this.typingTimeout);\r\n    }\r\n\r\n    this.typingTimeout = setTimeout(() => {\r\n      this.stopTyping();\r\n    }, 3000);\r\n  }\r\n\r\n  stopTyping(): void {\r\n    if (!this.isTyping || !this.selectedConversation) return;\r\n\r\n    this.isTyping = false;\r\n    this.messageService.stopTyping(this.selectedConversation.id!).subscribe();\r\n\r\n    if (this.typingTimeout) {\r\n      clearTimeout(this.typingTimeout);\r\n    }\r\n  }\r\n\r\n  // ============================================================================\r\n  // MÉTHODES UTILITAIRES\r\n  // ============================================================================\r\n\r\n  private getRecipientId(): string | null {\r\n    if (!this.selectedConversation || !this.currentUser) return null;\r\n\r\n    const participants = this.selectedConversation.participants || [];\r\n    const currentUserId = this.currentUser.id || this.currentUser._id;\r\n\r\n    const recipient = participants.find(\r\n      (p) => (p.id || p._id) !== currentUserId\r\n    );\r\n\r\n    return recipient ? recipient.id || recipient._id! : null;\r\n  }\r\n\r\n  private getFileMessageType(file: File): MessageType {\r\n    const type = file.type.split('/')[0];\r\n    switch (type) {\r\n      case 'image':\r\n        return MessageType.IMAGE;\r\n      case 'video':\r\n        return MessageType.VIDEO;\r\n      case 'audio':\r\n        return MessageType.AUDIO;\r\n      default:\r\n        return MessageType.FILE;\r\n    }\r\n  }\r\n\r\n  private addNewMessage(message: Message): void {\r\n    this.messages.push(message);\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  private replaceTemporaryMessage(tempId: string, realMessage: Message): void {\r\n    const index = this.messages.findIndex((m) => m.id === tempId);\r\n    if (index !== -1) {\r\n      this.messages[index] = realMessage;\r\n      this.cdr.detectChanges();\r\n    }\r\n  }\r\n\r\n  private markMessageAsError(messageId: string): void {\r\n    const message = this.messages.find((m) => m.id === messageId);\r\n    if (message) {\r\n      message.isPending = false;\r\n      message.isError = true;\r\n      this.cdr.detectChanges();\r\n    }\r\n  }\r\n\r\n  private updateMessageInList(updatedMessage: Message): void {\r\n    const index = this.messages.findIndex((m) => m.id === updatedMessage.id);\r\n    if (index !== -1) {\r\n      this.messages[index] = updatedMessage;\r\n      this.cdr.detectChanges();\r\n    }\r\n  }\r\n\r\n  private removeMessageFromList(messageId: string): void {\r\n    this.messages = this.messages.filter((m) => m.id !== messageId);\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  private canDeleteMessage(message: Message): boolean {\r\n    if (!this.currentUser || !message.sender) return false;\r\n\r\n    const currentUserId = this.currentUser.id || this.currentUser._id;\r\n    const senderId = message.sender.id || message.sender._id;\r\n\r\n    return currentUserId === senderId;\r\n  }\r\n\r\n  private handleTypingIndicator(event: any): void {\r\n    if (!this.currentUser) return;\r\n\r\n    const currentUserId = this.currentUser.id || this.currentUser._id;\r\n\r\n    if (event.userId === currentUserId) return; // Ignorer ses propres indicateurs\r\n\r\n    if (event.isTyping) {\r\n      // Ajouter l'utilisateur à la liste des utilisateurs en train de taper\r\n      const user = this.selectedConversation?.participants?.find(\r\n        (p) => (p.id || p._id) === event.userId\r\n      );\r\n      if (\r\n        user &&\r\n        !this.typingUsers.find((u) => (u.id || u._id) === event.userId)\r\n      ) {\r\n        this.typingUsers.push(user);\r\n      }\r\n    } else {\r\n      // Retirer l'utilisateur de la liste\r\n      this.typingUsers = this.typingUsers.filter(\r\n        (u) => (u.id || u._id) !== event.userId\r\n      );\r\n    }\r\n\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  private markMessagesAsRead(): void {\r\n    if (!this.messages.length || !this.currentUser) return;\r\n\r\n    const unreadMessages = this.messages.filter(\r\n      (m) =>\r\n        !m.isRead &&\r\n        m.sender &&\r\n        (m.sender.id || m.sender._id) !==\r\n          (this.currentUser!.id || this.currentUser!._id)\r\n    );\r\n\r\n    unreadMessages.forEach((message) => {\r\n      if (message.id) {\r\n        this.markMessageAsRead(message);\r\n      }\r\n    });\r\n  }\r\n\r\n  private markMessageAsRead(message: Message): void {\r\n    if (!message.id || message.isRead) return;\r\n\r\n    this.messageService.markMessageAsRead(message.id).subscribe({\r\n      next: (updatedMessage) => {\r\n        this.updateMessageInList(updatedMessage);\r\n      },\r\n      error: (error) => {\r\n        console.error('Erreur lors du marquage comme lu:', error);\r\n      },\r\n    });\r\n  }\r\n\r\n  private scrollToBottom(): void {\r\n    this.ngZone.runOutsideAngular(() => {\r\n      setTimeout(() => {\r\n        if (this.messagesContainer) {\r\n          const element = this.messagesContainer.nativeElement;\r\n          element.scrollTop = element.scrollHeight;\r\n        }\r\n      }, 100);\r\n    });\r\n  }\r\n\r\n  // ============================================================================\r\n  // MÉTHODES PUBLIQUES POUR LE TEMPLATE\r\n  // ============================================================================\r\n\r\n  formatMessageTime(timestamp: Date | string): string {\r\n    const date = new Date(timestamp);\r\n    const now = new Date();\r\n    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\r\n\r\n    if (diffInHours < 24) {\r\n      return date.toLocaleTimeString('fr-FR', {\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n      });\r\n    } else {\r\n      return date.toLocaleDateString('fr-FR', {\r\n        day: '2-digit',\r\n        month: '2-digit',\r\n      });\r\n    }\r\n  }\r\n\r\n  isMyMessage(message: Message): boolean {\r\n    if (!this.currentUser || !message.sender) return false;\r\n\r\n    const currentUserId = this.currentUser.id || this.currentUser._id;\r\n    const senderId = message.sender.id || message.sender._id;\r\n\r\n    return currentUserId === senderId;\r\n  }\r\n\r\n  getTypingText(): string {\r\n    if (this.typingUsers.length === 0) return '';\r\n\r\n    if (this.typingUsers.length === 1) {\r\n      return `${this.typingUsers[0].username} est en train d'écrire...`;\r\n    } else {\r\n      return `${this.typingUsers.length} personnes sont en train d'écrire...`;\r\n    }\r\n  }\r\n\r\n  onKeyPress(event: KeyboardEvent): void {\r\n    if (event.key === 'Enter' && !event.shiftKey) {\r\n      event.preventDefault();\r\n      this.sendMessage();\r\n    } else {\r\n      this.onTyping();\r\n    }\r\n  }\r\n\r\n  toggleEmojiPicker(): void {\r\n    this.showEmojiPicker = !this.showEmojiPicker;\r\n  }\r\n\r\n  toggleAttachmentMenu(): void {\r\n    this.showAttachmentMenu = !this.showAttachmentMenu;\r\n  }\r\n\r\n  startEditingMessage(message: Message): void {\r\n    this.editingMessage = message;\r\n    this.messageContent = message.content || '';\r\n    this.messageInput.nativeElement.focus();\r\n  }\r\n\r\n  cancelEditing(): void {\r\n    this.editingMessage = null;\r\n    this.messageContent = '';\r\n  }\r\n\r\n  setReplyTo(message: Message): void {\r\n    this.replyingTo = message;\r\n    this.messageInput.nativeElement.focus();\r\n  }\r\n\r\n  cancelReply(): void {\r\n    this.replyingTo = null;\r\n  }\r\n\r\n  // ============================================================================\r\n  // MÉTHODES POUR LE TEMPLATE (MANQUANTES)\r\n  // ============================================================================\r\n\r\n  getRecipientName(): string {\r\n    if (!this.selectedConversation || !this.currentUser) return '';\r\n\r\n    const participants = this.selectedConversation.participants || [];\r\n    const currentUserId = this.currentUser.id || this.currentUser._id;\r\n\r\n    const recipient = participants.find(\r\n      (p) => (p.id || p._id) !== currentUserId\r\n    );\r\n\r\n    return recipient?.username || 'Utilisateur inconnu';\r\n  }\r\n\r\n  getRecipientAvatar(): string {\r\n    if (!this.selectedConversation || !this.currentUser)\r\n      return '/assets/images/default-avatar.png';\r\n\r\n    const participants = this.selectedConversation.participants || [];\r\n    const currentUserId = this.currentUser.id || this.currentUser._id;\r\n\r\n    const recipient = participants.find(\r\n      (p) => (p.id || p._id) !== currentUserId\r\n    );\r\n\r\n    return recipient?.image || '/assets/images/default-avatar.png';\r\n  }\r\n\r\n  isRecipientOnline(): boolean {\r\n    if (!this.selectedConversation || !this.currentUser) return false;\r\n\r\n    const participants = this.selectedConversation.participants || [];\r\n    const currentUserId = this.currentUser.id || this.currentUser._id;\r\n\r\n    const recipient = participants.find(\r\n      (p) => (p.id || p._id) !== currentUserId\r\n    );\r\n\r\n    return recipient?.isOnline || false;\r\n  }\r\n\r\n  trackByMessageId(index: number, message: Message): string {\r\n    return message.id || message._id || index.toString();\r\n  }\r\n\r\n  openImageViewer(attachment: Attachment | undefined): void {\r\n    if (!attachment?.url) return;\r\n\r\n    // Ouvrir l'image dans une nouvelle fenêtre ou modal\r\n    window.open(attachment.url, '_blank');\r\n  }\r\n\r\n  formatFileSize(size: number | undefined): string {\r\n    if (!size) return '0 B';\r\n\r\n    const units = ['B', 'KB', 'MB', 'GB'];\r\n    let unitIndex = 0;\r\n    let fileSize = size;\r\n\r\n    while (fileSize >= 1024 && unitIndex < units.length - 1) {\r\n      fileSize /= 1024;\r\n      unitIndex++;\r\n    }\r\n\r\n    return `${fileSize.toFixed(1)} ${units[unitIndex]}`;\r\n  }\r\n\r\n  downloadFile(attachment: Attachment | undefined): void {\r\n    if (!attachment?.url) return;\r\n\r\n    const link = document.createElement('a');\r\n    link.href = attachment.url;\r\n    link.download = attachment.name || 'file';\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n  }\r\n\r\n  playVoiceMessage(message: Message): void {\r\n    if (!message.attachments?.[0]?.url) return;\r\n\r\n    this.messageService.playAudio(message.attachments[0].url).catch((error) => {\r\n      console.error('Erreur lors de la lecture du message vocal:', error);\r\n      this.toastService.showError('Erreur lors de la lecture du message vocal');\r\n    });\r\n  }\r\n\r\n  formatDuration(duration: number | undefined): string {\r\n    if (!duration) return '0:00';\r\n\r\n    const minutes = Math.floor(duration / 60);\r\n    const seconds = duration % 60;\r\n\r\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\r\n  }\r\n\r\n  showMessageMenu(message: Message): void {\r\n    // Ici, vous pouvez implémenter un menu contextuel\r\n    // Pour l'instant, on affiche juste les options disponibles\r\n    const actions = [];\r\n\r\n    if (this.canDeleteMessage(message)) {\r\n      actions.push('Supprimer');\r\n    }\r\n\r\n    if (this.isMyMessage(message)) {\r\n      actions.push('Modifier');\r\n    }\r\n\r\n    actions.push('Répondre', 'Transférer', 'Réagir');\r\n\r\n    // Vous pouvez implémenter un vrai menu contextuel ici\r\n    console.log('Actions disponibles pour ce message:', actions);\r\n  }\r\n}\r\n", "<!-- ============================================================================\r\n     COMPOSANT MESSAGE CHAT - INTERFACE WHATSAPP PROFESSIONNELLE\r\n     ============================================================================ -->\r\n\r\n<div class=\"chat-container\" *ngIf=\"selectedConversation\">\r\n  <!-- ========================================================================\r\n       EN-TÊTE DU CHAT\r\n       ======================================================================== -->\r\n  <div class=\"chat-header\">\r\n    <div class=\"user-info\">\r\n      <img\r\n        [src]=\"\r\n          selectedConversation.isGroup\r\n            ? selectedConversation.groupPhoto\r\n            : getRecipientAvatar()\r\n        \"\r\n        [alt]=\"\r\n          selectedConversation.isGroup\r\n            ? selectedConversation.groupName\r\n            : getRecipientName()\r\n        \"\r\n        class=\"user-avatar\"\r\n        [class.online]=\"!selectedConversation.isGroup && isRecipientOnline()\"\r\n      />\r\n\r\n      <div class=\"user-details\">\r\n        <h3>\r\n          {{\r\n            selectedConversation.isGroup\r\n              ? selectedConversation.groupName\r\n              : getRecipientName()\r\n          }}\r\n        </h3>\r\n        <p\r\n          class=\"user-status\"\r\n          [class.online]=\"!selectedConversation.isGroup && isRecipientOnline()\"\r\n        >\r\n          <span *ngIf=\"selectedConversation.isGroup\">\r\n            {{ selectedConversation.participants?.length }} participants\r\n          </span>\r\n          <span *ngIf=\"!selectedConversation.isGroup && isRecipientOnline()\"\r\n            >En ligne</span\r\n          >\r\n          <span *ngIf=\"!selectedConversation.isGroup && !isRecipientOnline()\"\r\n            >Hors ligne</span\r\n          >\r\n        </p>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"chat-actions\">\r\n      <!-- Bouton d'appel audio -->\r\n      <button\r\n        class=\"action-btn\"\r\n        (click)=\"startAudioCall()\"\r\n        title=\"Appel audio\"\r\n        *ngIf=\"!selectedConversation.isGroup\"\r\n      >\r\n        <i class=\"fas fa-phone\"></i>\r\n      </button>\r\n\r\n      <!-- Bouton d'appel vidéo -->\r\n      <button\r\n        class=\"action-btn\"\r\n        (click)=\"startVideoCall()\"\r\n        title=\"Appel vidéo\"\r\n        *ngIf=\"!selectedConversation.isGroup\"\r\n      >\r\n        <i class=\"fas fa-video\"></i>\r\n      </button>\r\n\r\n      <!-- Menu d'options -->\r\n      <button class=\"action-btn\" title=\"Options\">\r\n        <i class=\"fas fa-ellipsis-v\"></i>\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- ========================================================================\r\n       ZONE DES MESSAGES\r\n       ======================================================================== -->\r\n  <div class=\"messages-container\" #messagesContainer>\r\n    <!-- Indicateur de chargement -->\r\n    <div *ngIf=\"isLoading\" class=\"flex justify-center py-4\">\r\n      <div\r\n        class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"\r\n      ></div>\r\n    </div>\r\n\r\n    <!-- Messages -->\r\n    <div\r\n      *ngFor=\"let message of messages; trackBy: trackByMessageId\"\r\n      class=\"message\"\r\n      [class.my-message]=\"isMyMessage(message)\"\r\n    >\r\n      <!-- Avatar de l'expéditeur (seulement pour les messages des autres) -->\r\n      <img\r\n        *ngIf=\"!isMyMessage(message) && message.sender\"\r\n        [src]=\"message.sender.image || '/assets/images/default-avatar.png'\"\r\n        [alt]=\"message.sender.username\"\r\n        class=\"message-avatar\"\r\n      />\r\n\r\n      <!-- Contenu du message -->\r\n      <div\r\n        class=\"message-content\"\r\n        [class.my-message]=\"isMyMessage(message)\"\r\n        [class.other-message]=\"!isMyMessage(message)\"\r\n      >\r\n        <!-- Nom de l'expéditeur (pour les groupes) -->\r\n        <div\r\n          *ngIf=\"selectedConversation.isGroup && !isMyMessage(message)\"\r\n          class=\"text-xs text-blue-400 mb-1 font-medium\"\r\n        >\r\n          {{ message.sender?.username }}\r\n        </div>\r\n\r\n        <!-- Message de réponse -->\r\n        <div *ngIf=\"message.replyTo\" class=\"reply-preview mb-2\">\r\n          <div class=\"text-xs text-gray-400\">\r\n            Réponse à {{ message.replyTo.sender?.username }}\r\n          </div>\r\n          <div class=\"text-sm text-gray-300 truncate\">\r\n            {{ message.replyTo.content }}\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Contenu selon le type de message -->\r\n        <div [ngSwitch]=\"message.type\">\r\n          <!-- Message texte -->\r\n          <div *ngSwitchCase=\"MessageType.TEXT\" class=\"message-text\">\r\n            {{ message.content }}\r\n          </div>\r\n\r\n          <!-- Message image -->\r\n          <div *ngSwitchCase=\"MessageType.IMAGE\" class=\"message-image\">\r\n            <img\r\n              [src]=\"message.attachments?.[0]?.url\"\r\n              [alt]=\"message.attachments?.[0]?.name\"\r\n              class=\"message-image\"\r\n              (click)=\"openImageViewer(message.attachments?.[0])\"\r\n            />\r\n            <div *ngIf=\"message.content\" class=\"message-text mt-2\">\r\n              {{ message.content }}\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Message fichier -->\r\n          <div *ngSwitchCase=\"MessageType.FILE\" class=\"message-file\">\r\n            <i class=\"file-icon fas fa-file\"></i>\r\n            <div class=\"file-info\">\r\n              <div class=\"file-name\">{{ message.attachments?.[0]?.name }}</div>\r\n              <div class=\"file-size\">\r\n                {{ formatFileSize(message.attachments?.[0]?.size) }}\r\n              </div>\r\n            </div>\r\n            <button\r\n              class=\"text-blue-400 hover:text-blue-300\"\r\n              (click)=\"downloadFile(message.attachments?.[0])\"\r\n            >\r\n              <i class=\"fas fa-download\"></i>\r\n            </button>\r\n          </div>\r\n\r\n          <!-- Message vocal -->\r\n          <div *ngSwitchCase=\"MessageType.VOICE_MESSAGE\" class=\"voice-message\">\r\n            <button class=\"voice-play-btn\" (click)=\"playVoiceMessage(message)\">\r\n              <i class=\"fas fa-play text-white text-xs\"></i>\r\n            </button>\r\n            <div class=\"voice-duration\">\r\n              {{ formatDuration(message.attachments?.[0]?.duration) }}\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Message vidéo -->\r\n          <div *ngSwitchCase=\"MessageType.VIDEO\" class=\"message-video\">\r\n            <video\r\n              [src]=\"message.attachments?.[0]?.url\"\r\n              controls\r\n              class=\"max-w-xs rounded-lg\"\r\n            ></video>\r\n            <div *ngIf=\"message.content\" class=\"message-text mt-2\">\r\n              {{ message.content }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Réactions -->\r\n        <div\r\n          *ngIf=\"message.reactions && message.reactions.length > 0\"\r\n          class=\"flex flex-wrap gap-1 mt-2\"\r\n        >\r\n          <span\r\n            *ngFor=\"let reaction of message.reactions\"\r\n            class=\"text-xs bg-gray-600 rounded-full px-2 py-1 cursor-pointer\"\r\n            (click)=\"reactToMessage(message, reaction.emoji)\"\r\n          >\r\n            {{ reaction.emoji }} {{ reaction.count }}\r\n          </span>\r\n        </div>\r\n\r\n        <!-- Heure et statut -->\r\n        <div class=\"flex items-center justify-between mt-1\">\r\n          <span class=\"message-time\">\r\n            {{ formatMessageTime(message.timestamp!) }}\r\n          </span>\r\n\r\n          <!-- Statut du message (seulement pour mes messages) -->\r\n          <div\r\n            *ngIf=\"isMyMessage(message)\"\r\n            class=\"message-status\"\r\n            [class.read]=\"message.isRead\"\r\n            [class.pending]=\"message.isPending\"\r\n            [class.error]=\"message.isError\"\r\n          >\r\n            <i *ngIf=\"message.isPending\" class=\"fas fa-clock\"></i>\r\n            <i *ngIf=\"message.isError\" class=\"fas fa-exclamation-triangle\"></i>\r\n            <i\r\n              *ngIf=\"!message.isPending && !message.isError && message.isRead\"\r\n              class=\"fas fa-check-double\"\r\n            ></i>\r\n            <i\r\n              *ngIf=\"!message.isPending && !message.isError && !message.isRead\"\r\n              class=\"fas fa-check\"\r\n            ></i>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Menu contextuel du message -->\r\n        <div\r\n          class=\"message-menu absolute top-0 right-0 hidden group-hover:block\"\r\n        >\r\n          <button\r\n            class=\"text-gray-400 hover:text-white p-1\"\r\n            (click)=\"showMessageMenu(message)\"\r\n          >\r\n            <i class=\"fas fa-ellipsis-h text-xs\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Indicateur de frappe -->\r\n    <div *ngIf=\"typingUsers.length > 0\" class=\"typing-indicator\">\r\n      <div class=\"typing-dots\">\r\n        <div class=\"typing-dot\"></div>\r\n        <div class=\"typing-dot\"></div>\r\n        <div class=\"typing-dot\"></div>\r\n      </div>\r\n      <span>{{ getTypingText() }}</span>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- ========================================================================\r\n       ZONE DE SAISIE DES MESSAGES\r\n       ======================================================================== -->\r\n  <div class=\"message-input-container\">\r\n    <!-- Aperçu de réponse -->\r\n    <div *ngIf=\"replyingTo\" class=\"reply-preview\">\r\n      <div class=\"reply-header\">\r\n        <div>\r\n          <div class=\"text-xs text-blue-400\">\r\n            Réponse à {{ replyingTo.sender?.username }}\r\n          </div>\r\n          <div class=\"reply-text\">{{ replyingTo.content }}</div>\r\n        </div>\r\n        <button (click)=\"cancelReply()\" class=\"text-gray-400 hover:text-white\">\r\n          <i class=\"fas fa-times\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Aperçu de modification -->\r\n    <div *ngIf=\"editingMessage\" class=\"reply-preview\">\r\n      <div class=\"reply-header\">\r\n        <div>\r\n          <div class=\"text-xs text-yellow-400\">Modification du message</div>\r\n          <div class=\"reply-text\">{{ editingMessage.content }}</div>\r\n        </div>\r\n        <button\r\n          (click)=\"cancelEditing()\"\r\n          class=\"text-gray-400 hover:text-white\"\r\n        >\r\n          <i class=\"fas fa-times\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Fichiers sélectionnés -->\r\n    <div *ngIf=\"selectedFiles.length > 0\" class=\"mb-3\">\r\n      <div class=\"flex flex-wrap gap-2\">\r\n        <div\r\n          *ngFor=\"let file of selectedFiles; let i = index\"\r\n          class=\"flex items-center space-x-2 bg-gray-700 rounded-lg p-2\"\r\n        >\r\n          <i class=\"fas fa-file text-blue-400\"></i>\r\n          <span class=\"text-sm text-white truncate max-w-32\">{{\r\n            file.name\r\n          }}</span>\r\n          <button\r\n            (click)=\"removeSelectedFile(i)\"\r\n            class=\"text-red-400 hover:text-red-300\"\r\n          >\r\n            <i class=\"fas fa-times text-xs\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Indicateur d'enregistrement vocal -->\r\n    <div *ngIf=\"isRecording\" class=\"recording-indicator mb-3\">\r\n      <i class=\"fas fa-microphone text-white\"></i>\r\n      <span class=\"recording-time\">{{\r\n        formatDuration(recordingDuration)\r\n      }}</span>\r\n      <button\r\n        (click)=\"stopVoiceRecording()\"\r\n        class=\"text-white hover:text-gray-300\"\r\n      >\r\n        <i class=\"fas fa-stop\"></i>\r\n      </button>\r\n      <button\r\n        (click)=\"cancelVoiceRecording()\"\r\n        class=\"text-white hover:text-gray-300 ml-2\"\r\n      >\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </div>\r\n\r\n    <!-- Zone de saisie principale -->\r\n    <div class=\"input-wrapper\">\r\n      <!-- Boutons d'actions à gauche -->\r\n      <div class=\"input-actions\">\r\n        <!-- Bouton pièce jointe -->\r\n        <div class=\"relative\">\r\n          <button\r\n            class=\"input-btn\"\r\n            (click)=\"toggleAttachmentMenu()\"\r\n            title=\"Pièce jointe\"\r\n          >\r\n            <i class=\"fas fa-paperclip\"></i>\r\n          </button>\r\n\r\n          <!-- Menu des pièces jointes -->\r\n          <div\r\n            *ngIf=\"showAttachmentMenu\"\r\n            class=\"absolute bottom-full left-0 mb-2 bg-gray-800 rounded-lg shadow-lg p-2 space-y-1\"\r\n          >\r\n            <button\r\n              (click)=\"openFileSelector()\"\r\n              class=\"flex items-center space-x-2 w-full p-2 hover:bg-gray-700 rounded text-left\"\r\n            >\r\n              <i class=\"fas fa-file text-blue-400\"></i>\r\n              <span class=\"text-white text-sm\">Fichier</span>\r\n            </button>\r\n            <button\r\n              (click)=\"openFileSelector()\"\r\n              class=\"flex items-center space-x-2 w-full p-2 hover:bg-gray-700 rounded text-left\"\r\n            >\r\n              <i class=\"fas fa-image text-green-400\"></i>\r\n              <span class=\"text-white text-sm\">Image</span>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Bouton emoji -->\r\n        <button class=\"input-btn\" (click)=\"toggleEmojiPicker()\" title=\"Emoji\">\r\n          <i class=\"fas fa-smile\"></i>\r\n        </button>\r\n      </div>\r\n\r\n      <!-- Champ de saisie -->\r\n      <textarea\r\n        #messageInput\r\n        [(ngModel)]=\"messageContent\"\r\n        (keydown)=\"onKeyPress($event)\"\r\n        (input)=\"onTyping()\"\r\n        placeholder=\"Tapez votre message...\"\r\n        class=\"message-input\"\r\n        rows=\"1\"\r\n        [disabled]=\"isRecording\"\r\n      ></textarea>\r\n\r\n      <!-- Boutons d'actions à droite -->\r\n      <div class=\"input-actions\">\r\n        <!-- Bouton enregistrement vocal (si pas de texte) -->\r\n        <button\r\n          *ngIf=\"\r\n            !messageContent.trim() && !selectedFiles.length && !isRecording\r\n          \"\r\n          class=\"input-btn\"\r\n          (mousedown)=\"startVoiceRecording()\"\r\n          title=\"Message vocal\"\r\n        >\r\n          <i class=\"fas fa-microphone\"></i>\r\n        </button>\r\n\r\n        <!-- Bouton d'envoi (si du texte ou des fichiers) -->\r\n        <button\r\n          *ngIf=\"messageContent.trim() || selectedFiles.length\"\r\n          class=\"send-btn\"\r\n          (click)=\"sendMessage()\"\r\n          [disabled]=\"!canSendMessage()\"\r\n          title=\"Envoyer\"\r\n        >\r\n          <i class=\"fas fa-paper-plane text-white\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Input file caché -->\r\n    <input\r\n      #fileInput\r\n      type=\"file\"\r\n      multiple\r\n      class=\"hidden\"\r\n      (change)=\"onFileSelected($event)\"\r\n      accept=\"image/*,video/*,audio/*,.pdf,.doc,.docx,.txt\"\r\n    />\r\n  </div>\r\n</div>\r\n\r\n<!-- ============================================================================\r\n     MESSAGE DE BIENVENUE (AUCUNE CONVERSATION SÉLECTIONNÉE)\r\n     ============================================================================ -->\r\n<div\r\n  *ngIf=\"!selectedConversation\"\r\n  class=\"flex items-center justify-center h-full bg-gray-900 text-gray-400\"\r\n>\r\n  <div class=\"text-center max-w-md mx-auto p-8\">\r\n    <!-- Logo/Icône principale -->\r\n    <div class=\"mb-8\">\r\n      <div\r\n        class=\"w-24 h-24 mx-auto bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mb-4\"\r\n      >\r\n        <i class=\"fas fa-comments text-3xl text-white\"></i>\r\n      </div>\r\n      <h1 class=\"text-3xl font-bold text-white mb-2\">DevBridge Messages</h1>\r\n      <p class=\"text-gray-400\">Messagerie professionnelle en temps réel</p>\r\n    </div>\r\n\r\n    <!-- Fonctionnalités -->\r\n    <div class=\"space-y-4 mb-8\">\r\n      <div class=\"flex items-center space-x-3 text-left\">\r\n        <div\r\n          class=\"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center\"\r\n        >\r\n          <i class=\"fas fa-bolt text-white text-sm\"></i>\r\n        </div>\r\n        <div>\r\n          <h4 class=\"text-white font-medium\">Messages en temps réel</h4>\r\n          <p class=\"text-sm text-gray-400\">\r\n            Conversations instantanées avec notifications\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"flex items-center space-x-3 text-left\">\r\n        <div\r\n          class=\"w-10 h-10 bg-green-600 rounded-full flex items-center justify-center\"\r\n        >\r\n          <i class=\"fas fa-phone text-white text-sm\"></i>\r\n        </div>\r\n        <div>\r\n          <h4 class=\"text-white font-medium\">Appels audio/vidéo</h4>\r\n          <p class=\"text-sm text-gray-400\">Communication directe intégrée</p>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"flex items-center space-x-3 text-left\">\r\n        <div\r\n          class=\"w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center\"\r\n        >\r\n          <i class=\"fas fa-file text-white text-sm\"></i>\r\n        </div>\r\n        <div>\r\n          <h4 class=\"text-white font-medium\">Partage de fichiers</h4>\r\n          <p class=\"text-sm text-gray-400\">Images, documents et médias</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Instructions -->\r\n    <div class=\"bg-gray-800 rounded-lg p-6 border border-gray-700 mb-6\">\r\n      <h3 class=\"text-lg font-semibold text-white mb-3\">Comment commencer ?</h3>\r\n      <div class=\"space-y-2 text-sm text-gray-300\">\r\n        <p>• Sélectionnez une conversation dans la sidebar</p>\r\n        <p>• Ou cliquez sur un contact pour démarrer une discussion</p>\r\n        <p>• Utilisez la recherche pour trouver rapidement</p>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- System Status Component -->\r\n    <app-system-status></app-system-status>\r\n  </div>\r\n</div>\r\n", "import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-messages-list',\n  templateUrl: './messages-list.component.html',\n  styleUrls: ['./messages-list.component.css'],\n})\nexport class MessagesListComponent {\n  \n\n}\n", "import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { MessageChatComponent } from './message-chat/message-chat.component';\nimport { MessagesListComponent } from './messages-list/messages-list.component';\nimport { UserListComponent } from './user-list/user-list.component';\nimport { MessageLayoutComponent } from './message-layout/message-layout.component';\n\nconst routes: Routes = [\n  {\n    path: '',\n    component: MessageLayoutComponent,\n    children: [\n      // Route par défaut - affiche le layout sans conversation sélectionnée\n      {\n        path: '',\n        component: MessageChatComponent,\n        data: { title: 'Messages' },\n      },\n      // Route pour une conversation spécifique\n      {\n        path: ':conversationId',\n        component: MessageChatComponent,\n        data: { title: 'Chat' },\n      },\n    ],\n  },\n];\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule],\n})\nexport class MessagesRoutingModule {}\n", "import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n\nimport { MessagesRoutingModule } from './messages-routing.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ApolloModule } from 'apollo-angular';\nimport { MessageChatComponent } from './message-chat/message-chat.component';\nimport { MessagesListComponent } from './messages-list/messages-list.component';\nimport { UserListComponent } from './user-list/user-list.component';\nimport { MessageLayoutComponent } from './message-layout/message-layout.component';\nimport { SystemStatusComponent } from '../../../components/system-status/system-status.component';\n\nimport { UserStatusService } from 'src/app/services/user-status.service';\nimport { MessageService } from 'src/app/services/message.service';\n\n@NgModule({\n  declarations: [\n    MessageChatComponent,\n    MessagesListComponent,\n    UserListComponent,\n    MessageLayoutComponent,\n    SystemStatusComponent,\n  ],\n  imports: [\n    CommonModule,\n    MessagesRoutingModule,\n    FormsModule,\n    ReactiveFormsModule,\n    ApolloModule,\n    RouterModule,\n  ],\n  providers: [UserStatusService, MessageService],\n})\nexport class MessagesModule {}\n", "// user-list.component.ts\nimport { Component, OnInit, OnDestroy } from '@angular/core';\nimport { Subscription, interval, Observable } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { User } from 'src/app/models/user.model';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport { ToastService } from 'src/app/services/toast.service';\nimport { MessageService } from 'src/app/services/message.service';\nimport { CallService } from 'src/app/services/call.service';\nimport { CallType, Call } from 'src/app/models/message.model';\nimport { LoggerService } from 'src/app/services/logger.service';\nimport { FormControl, FormGroup } from '@angular/forms';\nimport { ThemeService } from '@app/services/theme.service';\n\n@Component({\n  selector: 'app-user-list',\n  templateUrl: './user-list.component.html',\n  styleUrls: ['./user-list.component.css'],\n})\nexport class UserListComponent implements OnInit, OnDestroy {\n  users: User[] = [];\n  loading = true;\n  currentUserId: string | null = null;\n  isDarkMode$: Observable<boolean>;\n\n  // Pagination\n  currentPage = 1;\n  pageSize = 10;\n  totalUsers = 0;\n  totalPages = 0;\n  hasNextPage = false;\n  hasPreviousPage = false;\n\n  // Sorting and filtering\n  sortBy = 'username';\n  sortOrder = 'asc';\n  filterForm = new FormGroup({\n    searchQuery: new FormControl(''),\n    isOnline: new FormControl<boolean | null>(null),\n  });\n\n  // Auto-refresh\n  autoRefreshEnabled = true;\n  autoRefreshInterval = 30000; // 30 seconds\n  private autoRefreshSubscription?: Subscription;\n\n  private loadingMore = false;\n  private subscriptions: Subscription = new Subscription();\n\n  constructor(\n    private MessageService: MessageService,\n    private callService: CallService,\n    public router: Router,\n    public route: ActivatedRoute,\n    private authService: AuthuserService,\n    private toastService: ToastService,\n    private logger: LoggerService,\n    private themeService: ThemeService\n  ) {\n    this.isDarkMode$ = this.themeService.currentTheme$.pipe(\n      map((theme) => theme.name === 'dark')\n    );\n  }\n\n  ngOnInit(): void {\n    this.currentUserId = this.authService.getCurrentUserId();\n    this.setupFilterListeners();\n    this.setupAutoRefresh();\n    this.loadUsers();\n  }\n\n  private setupFilterListeners(): void {\n    // Subscribe to search query changes\n    const searchSub = this.filterForm\n      .get('searchQuery')!\n      .valueChanges.subscribe(() => {\n        this.resetPagination();\n        this.loadUsers();\n      });\n\n    this.subscriptions.add(searchSub);\n\n    // Subscribe to online status filter changes\n    const onlineSub = this.filterForm\n      .get('isOnline')!\n      .valueChanges.subscribe(() => {\n        this.resetPagination();\n        this.loadUsers();\n      });\n\n    this.subscriptions.add(onlineSub);\n  }\n\n  private setupAutoRefresh(): void {\n    if (this.autoRefreshEnabled) {\n      this.autoRefreshSubscription = interval(\n        this.autoRefreshInterval\n      ).subscribe(() => {\n        if (!this.loading && !this.filterForm.get('searchQuery')?.value) {\n          this.loadUsers(true);\n        }\n      });\n    }\n  }\n\n  toggleAutoRefresh(): void {\n    this.autoRefreshEnabled = !this.autoRefreshEnabled;\n\n    if (this.autoRefreshEnabled) {\n      this.setupAutoRefresh();\n    } else if (this.autoRefreshSubscription) {\n      this.autoRefreshSubscription.unsubscribe();\n      this.autoRefreshSubscription = undefined;\n    }\n  }\n\n  resetPagination(): void {\n    this.currentPage = 1;\n  }\n\n  // Get searchQuery from the form\n  get searchQuery(): string {\n    return this.filterForm.get('searchQuery')?.value || '';\n  }\n\n  // Set searchQuery in the form\n  set searchQuery(value: string) {\n    this.filterForm.get('searchQuery')?.setValue(value);\n  }\n\n  // Helper function for template type casting\n  $any(item: any): any {\n    return item;\n  }\n\n  loadUsers(forceRefresh = false): void {\n    if (this.loadingMore) return;\n\n    this.loading = true;\n\n    const searchQuery = this.filterForm.get('searchQuery')?.value || '';\n    const isOnline = this.filterForm.get('isOnline')?.value;\n\n    const sub = this.MessageService.getAllUsers(\n      forceRefresh,\n      searchQuery,\n      this.currentPage,\n      this.pageSize,\n      this.sortBy,\n      this.sortOrder,\n      isOnline === true ? true : undefined\n    ).subscribe({\n      next: (users) => {\n        if (!Array.isArray(users)) {\n          this.users = [];\n          this.loading = false;\n          this.loadingMore = false;\n          this.toastService.showError('Failed to load users: Invalid data');\n          return;\n        }\n\n        // If first page, replace users array; otherwise append\n        if (this.currentPage === 1) {\n          // Filter out current user\n          this.users = users.filter((user) => {\n            if (!user) return false;\n            const userId = user.id || user._id;\n            return userId !== this.currentUserId;\n          });\n        } else {\n          // Append new users to existing array, avoiding duplicates and filtering out current user\n          const newUsers = users.filter((newUser) => {\n            if (!newUser) return false;\n            const userId = newUser.id || newUser._id;\n            return (\n              userId !== this.currentUserId &&\n              !this.users.some(\n                (existingUser) =>\n                  (existingUser.id || existingUser._id) === userId\n              )\n            );\n          });\n\n          this.users = [...this.users, ...newUsers];\n        }\n\n        // Update pagination metadata from service\n        const pagination = this.MessageService.currentUserPagination;\n        this.totalUsers = pagination.totalCount;\n        this.totalPages = pagination.totalPages;\n        this.hasNextPage = pagination.hasNextPage;\n        this.hasPreviousPage = pagination.hasPreviousPage;\n\n        this.loading = false;\n        this.loadingMore = false;\n      },\n      error: (error) => {\n        this.loading = false;\n        this.loadingMore = false;\n        this.toastService.showError(\n          `Failed to load users: ${error.message || 'Unknown error'}`\n        );\n\n        if (this.currentPage === 1) {\n          this.users = [];\n        }\n      },\n      complete: () => {\n        this.loading = false;\n        this.loadingMore = false;\n      },\n    });\n\n    this.subscriptions.add(sub);\n  }\n\n  startConversation(userId: string | undefined) {\n    if (!userId) {\n      this.toastService.showError(\n        'Cannot start conversation with undefined user'\n      );\n      return;\n    }\n\n    this.toastService.showInfo('Creating conversation...');\n\n    this.MessageService.createConversation(userId).subscribe({\n      next: (conversation) => {\n        if (!conversation || !conversation.id) {\n          this.toastService.showError(\n            'Failed to create conversation: Invalid response'\n          );\n          return;\n        }\n\n        this.router\n          .navigate(['/messages/conversations/chat', conversation.id])\n          .then((success) => {\n            if (!success) {\n              this.toastService.showError('Failed to open conversation');\n            }\n          });\n      },\n      error: (error) => {\n        this.toastService.showError(\n          `Failed to create conversation: ${error.message || 'Unknown error'}`\n        );\n      },\n    });\n  }\n\n  startAudioCall(userId: string): void {\n    if (!userId) return;\n\n    this.callService.initiateCall(userId, CallType.AUDIO).subscribe({\n      next: (call: Call) => {\n        this.toastService.showSuccess('Audio call initiated');\n      },\n      error: (error: any) => {\n        this.toastService.showError('Failed to initiate audio call');\n      },\n    });\n  }\n\n  startVideoCall(userId: string): void {\n    if (!userId) return;\n\n    this.callService.initiateCall(userId, CallType.VIDEO).subscribe({\n      next: (call: Call) => {\n        this.toastService.showSuccess('Video call initiated');\n      },\n      error: (error: any) => {\n        this.toastService.showError('Failed to initiate video call');\n      },\n    });\n  }\n\n  loadNextPage(): void {\n    if (this.hasNextPage && !this.loading) {\n      this.loadingMore = true;\n      this.currentPage++;\n      this.loadUsers();\n    }\n  }\n\n  loadPreviousPage(): void {\n    if (this.hasPreviousPage && !this.loading) {\n      this.loadingMore = true;\n      this.currentPage--;\n      this.loadUsers();\n    }\n  }\n\n  refreshUsers(): void {\n    this.resetPagination();\n    this.loadUsers(true);\n  }\n\n  clearFilters(): void {\n    this.filterForm.reset({\n      searchQuery: '',\n      isOnline: null,\n    });\n    this.resetPagination();\n    this.loadUsers(true);\n  }\n\n  changeSortOrder(field: string): void {\n    if (this.sortBy === field) {\n      // Toggle sort order if clicking the same field\n      this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';\n    } else {\n      // Set new sort field with default ascending order\n      this.sortBy = field;\n      this.sortOrder = 'asc';\n    }\n\n    this.resetPagination();\n    this.loadUsers(true);\n  }\n\n  /**\n   * Navigue vers la liste des conversations\n   */\n  goBackToConversations(): void {\n    this.router.navigate(['/messages/conversations']);\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.unsubscribe();\n    if (this.autoRefreshSubscription) {\n      this.autoRefreshSubscription.unsubscribe();\n    }\n  }\n}\n", "<div\n  class=\"flex flex-col h-full futuristic-users-container\"\n  [class.dark]=\"isDarkMode$ | async\"\n>\n  <!-- Background decorative elements -->\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n    <!-- Gradient orbs -->\n    <div\n      class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/10 dark:to-transparent blur-3xl\"\n    ></div>\n    <div\n      class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#00f7ff]/10 dark:to-transparent blur-3xl\"\n    ></div>\n\n    <!-- Additional glow effects for dark mode -->\n    <div\n      class=\"absolute top-[40%] right-[30%] w-40 h-40 rounded-full bg-gradient-to-br from-transparent to-transparent dark:from-[#00f7ff]/5 dark:to-transparent blur-3xl opacity-0 dark:opacity-100\"\n    ></div>\n    <div\n      class=\"absolute bottom-[60%] left-[25%] w-32 h-32 rounded-full bg-gradient-to-tl from-transparent to-transparent dark:from-[#00f7ff]/5 dark:to-transparent blur-3xl opacity-0 dark:opacity-100\"\n    ></div>\n\n    <!-- Grid pattern for light mode -->\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-0\">\n      <div class=\"h-full grid grid-cols-12\">\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n        <div class=\"border-r border-[#4f5fad]\"></div>\n      </div>\n    </div>\n\n    <!-- Horizontal scan line effect for dark mode -->\n    <div class=\"absolute inset-0 opacity-0 dark:opacity-100 overflow-hidden\">\n      <div class=\"h-px w-full bg-[#00f7ff]/20 absolute animate-scan\"></div>\n    </div>\n  </div>\n  <!-- En-tête -->\n  <div class=\"futuristic-users-header\">\n    <div class=\"flex justify-between items-center mb-4\">\n      <h1 class=\"futuristic-title\">Nouvelle Conversation</h1>\n      <div class=\"flex space-x-2\">\n        <button\n          (click)=\"refreshUsers()\"\n          class=\"futuristic-action-button\"\n          title=\"Rafraîchir la liste\"\n        >\n          <i class=\"fas fa-sync-alt\"></i>\n        </button>\n        <button\n          (click)=\"goBackToConversations()\"\n          class=\"futuristic-action-button\"\n        >\n          <i class=\"fas fa-arrow-left\"></i>\n        </button>\n      </div>\n    </div>\n\n    <!-- Recherche et filtres -->\n    <div class=\"space-y-3\">\n      <!-- Recherche -->\n      <div class=\"relative\">\n        <input\n          [ngModel]=\"searchQuery\"\n          (ngModelChange)=\"searchQuery = $event\"\n          type=\"text\"\n          placeholder=\"Rechercher des utilisateurs...\"\n          class=\"w-full pl-10 pr-4 py-2 rounded-lg futuristic-input-field\"\n        />\n        <i\n          class=\"fas fa-search absolute left-3 top-3 text-[#6d6870] dark:text-[#a0a0a0]\"\n        ></i>\n      </div>\n\n      <!-- Filtres -->\n      <div class=\"flex items-center justify-between\">\n        <div class=\"flex items-center space-x-4\">\n          <!-- Filtre en ligne -->\n          <div class=\"flex items-center space-x-2\">\n            <label class=\"futuristic-checkbox-container\">\n              <input\n                type=\"checkbox\"\n                id=\"onlineFilter\"\n                class=\"futuristic-checkbox\"\n                [checked]=\"filterForm.get('isOnline')?.value === true\"\n                (change)=\"\n                  filterForm\n                    .get('isOnline')\n                    ?.setValue($any($event.target).checked ? true : null)\n                \"\n              />\n              <span class=\"futuristic-checkbox-checkmark\"></span>\n            </label>\n            <label for=\"onlineFilter\" class=\"futuristic-label\"\n              >En ligne uniquement</label\n            >\n          </div>\n\n          <!-- Options de tri -->\n          <div class=\"flex items-center space-x-2\">\n            <span class=\"futuristic-label\">Trier par:</span>\n            <select\n              (change)=\"changeSortOrder($any($event.target).value)\"\n              class=\"futuristic-select\"\n            >\n              <option [selected]=\"sortBy === 'username'\" value=\"username\">\n                Nom\n              </option>\n              <option [selected]=\"sortBy === 'email'\" value=\"email\">\n                Email\n              </option>\n              <option [selected]=\"sortBy === 'lastActive'\" value=\"lastActive\">\n                Dernière activité\n              </option>\n            </select>\n            <button\n              (click)=\"\n                sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';\n                loadUsers(true)\n              \"\n              class=\"futuristic-sort-button\"\n              [title]=\"\n                sortOrder === 'asc' ? 'Ordre croissant' : 'Ordre décroissant'\n              \"\n            >\n              <i\n                [class]=\"\n                  sortOrder === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down'\n                \"\n              ></i>\n            </button>\n          </div>\n        </div>\n\n        <!-- Effacer les filtres -->\n        <button (click)=\"clearFilters()\" class=\"futuristic-clear-button\">\n          Effacer les filtres\n        </button>\n      </div>\n\n      <!-- Info pagination -->\n      <div\n        *ngIf=\"totalUsers > 0\"\n        class=\"flex justify-between items-center futuristic-pagination-info\"\n      >\n        <span\n          >Affichage de {{ users.length }} sur\n          {{ totalUsers }} utilisateurs</span\n        >\n        <span>Page {{ currentPage }} sur {{ totalPages }}</span>\n      </div>\n    </div>\n  </div>\n\n  <!-- Liste des utilisateurs -->\n  <div\n    class=\"futuristic-users-list\"\n    (scroll)=\"\n      $any($event.target).scrollTop + $any($event.target).clientHeight >=\n        $any($event.target).scrollHeight - 200 && loadNextPage()\n    \"\n  >\n    <!-- État de chargement -->\n    <div *ngIf=\"loading && !users.length\" class=\"futuristic-loading-container\">\n      <div class=\"futuristic-loading-circle\"></div>\n      <div class=\"futuristic-loading-text\">Chargement des utilisateurs...</div>\n    </div>\n\n    <!-- État vide -->\n    <div *ngIf=\"!loading && users.length === 0\" class=\"futuristic-empty-state\">\n      <div class=\"futuristic-empty-icon\">\n        <i class=\"fas fa-users\"></i>\n      </div>\n      <h3 class=\"futuristic-empty-title\">Aucun utilisateur trouvé</h3>\n      <p class=\"futuristic-empty-text\">\n        Essayez un autre terme de recherche ou effacez les filtres\n      </p>\n    </div>\n\n    <!-- Liste des utilisateurs -->\n    <ul *ngIf=\"users.length > 0\" class=\"futuristic-users-grid\">\n      <li *ngFor=\"let user of users\" class=\"futuristic-user-card\">\n        <div\n          class=\"futuristic-user-content\"\n          (click)=\"startConversation(user.id || user._id)\"\n        >\n          <div class=\"futuristic-avatar\">\n            <img\n              [src]=\"user.image || 'assets/images/default-avatar.png'\"\n              alt=\"User avatar\"\n            />\n            <span\n              *ngIf=\"user.isOnline\"\n              class=\"futuristic-online-indicator\"\n            ></span>\n          </div>\n          <div class=\"futuristic-user-info\">\n            <h3 class=\"futuristic-username\">\n              {{ user.username }}\n            </h3>\n            <p class=\"futuristic-user-email\">{{ user.email }}</p>\n          </div>\n        </div>\n\n        <!-- Boutons d'appel -->\n        <div class=\"futuristic-call-buttons\">\n          <button\n            *ngIf=\"user.isOnline\"\n            (click)=\"startAudioCall(user.id || user._id)\"\n            class=\"futuristic-call-button\"\n            title=\"Appel audio\"\n          >\n            <i class=\"fas fa-phone\"></i>\n          </button>\n          <button\n            *ngIf=\"user.isOnline\"\n            (click)=\"startVideoCall(user.id || user._id)\"\n            class=\"futuristic-call-button\"\n            title=\"Appel vidéo\"\n          >\n            <i class=\"fas fa-video\"></i>\n          </button>\n        </div>\n      </li>\n    </ul>\n\n    <!-- Indicateur de chargement supplémentaire -->\n    <div *ngIf=\"loading && users.length > 0\" class=\"futuristic-loading-more\">\n      <div class=\"futuristic-loading-dots\">\n        <div class=\"futuristic-loading-dot\" style=\"animation-delay: 0s\"></div>\n        <div class=\"futuristic-loading-dot\" style=\"animation-delay: 0.2s\"></div>\n        <div class=\"futuristic-loading-dot\" style=\"animation-delay: 0.4s\"></div>\n      </div>\n      <div class=\"futuristic-loading-text\">\n        Chargement de plus d'utilisateurs...\n      </div>\n    </div>\n\n    <!-- Bouton de chargement supplémentaire -->\n    <div *ngIf=\"hasNextPage && !loading\" class=\"futuristic-load-more-container\">\n      <button (click)=\"loadNextPage()\" class=\"futuristic-load-more-button\">\n        <i class=\"fas fa-chevron-down mr-2\"></i>\n        Charger plus d'utilisateurs\n      </button>\n    </div>\n  </div>\n</div>\n"], "names": ["interval", "SystemStatusComponent", "constructor", "messageService", "mockDataService", "themeService", "toastService", "status", "backend", "frontend", "database", "websocket", "mockData", "theme", "<PERSON><PERSON><PERSON><PERSON>", "Date", "isChecking", "ngOnInit", "checkSystemStatus", "subscription", "subscribe", "currentTheme$", "displayName", "ngOnDestroy", "unsubscribe", "_this", "_asyncToGenerator", "getUsers", "to<PERSON>romise", "getConversations", "getStatusText", "testMockData", "next", "users", "showSuccess", "length", "error", "showError", "testThemes", "themes", "getAvailableThemes", "showInfo", "testNotifications", "setTimeout", "showWarning", "i0", "ɵɵdirectiveInject", "i1", "MessageService", "i2", "MockDataService", "i3", "ThemeService", "i4", "ToastService", "selectors", "decls", "vars", "consts", "template", "SystemStatusComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "SystemStatusComponent_Template_button_click_5_listener", "SystemStatusComponent_Template_button_click_59_listener", "SystemStatusComponent_Template_button_click_62_listener", "SystemStatusComponent_Template_button_click_65_listener", "ɵɵadvance", "ɵɵproperty", "ɵɵclassProp", "ɵɵtextInterpolate1", "ɵɵtextInterpolate", "ɵɵpipeBind2", "BehaviorSubject", "throwError", "map", "catchError", "CallType", "CallStatus", "INITIATE_CALL_MUTATION", "ACCEPT_CALL_MUTATION", "REJECT_CALL_MUTATION", "END_CALL_MUTATION", "INCOMING_CALL_SUBSCRIPTION", "CALL_STATUS_CHANGED_SUBSCRIPTION", "CALL_SIGNAL_SUBSCRIPTION", "SEND_CALL_SIGNAL_MUTATION", "CallService", "apollo", "logger", "activeCall", "incomingCall", "callSignals", "activeCall$", "asObservable", "incomingCall$", "callSignals$", "currentCallId", "callState", "sounds", "isPlaying", "peerConnection", "localStream", "remoteStream", "localVideoElement", "remoteVideoElement", "isAudioEnabled", "isVideoEnabled", "rtcConfig", "iceServers", "urls", "info", "initializeSounds", "initializeSubscriptions", "initializeWebRTC", "cleanup", "initiateCall", "recipientId", "callType", "conversationId", "Error", "setCallState", "callId", "generateCallId", "mutate", "mutation", "variables", "pipe", "result", "call", "data", "handleCallInitiated", "acceptCall", "id", "acceptedCall", "handleCallAccepted", "rejectCall", "reason", "success", "handleCallEnded", "endCall", "currentCall", "value", "currentIncomingCall", "isCallActive", "isCallInProgress", "now", "Math", "random", "toString", "substr", "state", "debug", "play", "startOutgoingCallMedia", "type", "stop", "stopAllSounds", "cleanupWebRTC", "handleIncomingCall", "prepareForIncomingCall", "handleCallStatusChange", "CONNECTED", "ENDED", "REJECTED", "handleCallSignal", "signal", "createSyntheticSounds", "createSyntheticSound", "name", "frequencies", "duration", "loop", "audioContext", "window", "AudioContext", "webkitAudioContext", "sampleRate", "frameCount", "buffer", "createBuffer", "channelData", "getChannelData", "i", "sample", "for<PERSON>ach", "freq", "amplitude", "phase", "PI", "sin", "envelope", "audio", "Audio", "customPlay", "source", "createBufferSource", "connect", "destination", "start", "subscribeToIncomingCalls", "subscribeToCallStatusChanges", "subscribeToCallSignals", "query", "errorPolicy", "errors", "callStatusChanged", "callSignal", "createPeerConnection", "sound", "currentSource", "Object", "keys", "RTCPeerConnection", "onicecandidate", "event", "candidate", "sendSignal", "JSON", "stringify", "ontrack", "track", "kind", "streams", "attachRemoteStream", "onconnectionstatechange", "connectionState", "stream", "getUserMedia", "addLocalStreamToPeerConnection", "attachLocalStream", "_this2", "_this3", "constraints", "video", "VIDEO", "navigator", "mediaDevices", "getTracks", "addTrack", "srcObject", "signalType", "signalData", "close", "complete", "attachVideoElements", "localVideo", "remoteVideo", "toggleAudio", "getAudioTracks", "enabled", "toggleVideo", "getVideoTracks", "setVideoElements", "audioEnabled", "videoEnabled", "localMediaStream", "remoteMediaStream", "enableSounds", "disableSounds", "ɵɵinject", "Apollo", "LoggerService", "factory", "ɵfac", "providedIn", "of", "distinctUntilChanged", "switchMap", "tap", "filter", "MessageType", "ctx_r2", "selectedConversation", "participants", "MessageChatComponent_div_0_button_12_Template_button_click_0_listener", "ɵɵrestoreView", "_r21", "ctx_r20", "ɵɵnextContext", "ɵɵresetView", "startAudioCall", "MessageChatComponent_div_0_button_13_Template_button_click_0_listener", "_r23", "ctx_r22", "startVideoCall", "message_r24", "sender", "image", "ɵɵsanitizeUrl", "username", "replyTo", "content", "MessageChatComponent_div_0_div_19_div_7_Template_img_click_1_listener", "_r43", "$implicit", "ctx_r41", "openImageViewer", "attachments", "ɵɵtemplate", "MessageChatComponent_div_0_div_19_div_7_div_2_Template", "url", "MessageChatComponent_div_0_div_19_div_8_Template_button_click_7_listener", "_r47", "ctx_r45", "downloadFile", "ctx_r30", "formatFileSize", "size", "MessageChatComponent_div_0_div_19_div_9_Template_button_click_1_listener", "_r51", "ctx_r49", "playVoiceMessage", "ctx_r31", "formatDuration", "MessageChatComponent_div_0_div_19_div_10_div_2_Template", "MessageChatComponent_div_0_div_19_div_11_span_1_Template_span_click_0_listener", "restoredCtx", "_r60", "reaction_r57", "ctx_r58", "reactToMessage", "emoji", "ɵɵtextInterpolate2", "count", "MessageChatComponent_div_0_div_19_div_11_span_1_Template", "reactions", "MessageChatComponent_div_0_div_19_div_15_i_1_Template", "MessageChatComponent_div_0_div_19_div_15_i_2_Template", "MessageChatComponent_div_0_div_19_div_15_i_3_Template", "MessageChatComponent_div_0_div_19_div_15_i_4_Template", "isRead", "isPending", "isError", "MessageChatComponent_div_0_div_19_img_1_Template", "MessageChatComponent_div_0_div_19_div_3_Template", "MessageChatComponent_div_0_div_19_div_4_Template", "MessageChatComponent_div_0_div_19_div_6_Template", "MessageChatComponent_div_0_div_19_div_7_Template", "MessageChatComponent_div_0_div_19_div_8_Template", "MessageChatComponent_div_0_div_19_div_9_Template", "MessageChatComponent_div_0_div_19_div_10_Template", "MessageChatComponent_div_0_div_19_div_11_Template", "MessageChatComponent_div_0_div_19_div_15_Template", "MessageChatComponent_div_0_div_19_Template_button_click_17_listener", "_r68", "ctx_r67", "showMessageMenu", "ctx_r9", "isMyMessage", "isGroup", "TEXT", "IMAGE", "FILE", "VOICE_MESSAGE", "formatMessageTime", "timestamp", "ctx_r10", "getTypingText", "MessageChatComponent_div_0_div_22_Template_button_click_7_listener", "_r70", "ctx_r69", "cancelReply", "ctx_r11", "replyingTo", "MessageChatComponent_div_0_div_23_Template_button_click_7_listener", "_r72", "ctx_r71", "cancelEditing", "ctx_r12", "editingMessage", "MessageChatComponent_div_0_div_24_div_2_Template_button_click_4_listener", "_r77", "i_r75", "index", "ctx_r76", "removeSelectedFile", "file_r74", "MessageChatComponent_div_0_div_24_div_2_Template", "ctx_r13", "selectedFiles", "MessageChatComponent_div_0_div_25_Template_button_click_4_listener", "_r79", "ctx_r78", "stopVoiceRecording", "MessageChatComponent_div_0_div_25_Template_button_click_6_listener", "ctx_r80", "cancelVoiceRecording", "ctx_r14", "recordingDuration", "MessageChatComponent_div_0_div_31_Template_button_click_1_listener", "_r82", "ctx_r81", "openFileSelector", "MessageChatComponent_div_0_div_31_Template_button_click_5_listener", "ctx_r83", "MessageChatComponent_div_0_button_37_Template_button_mousedown_0_listener", "_r85", "ctx_r84", "startVoiceRecording", "MessageChatComponent_div_0_button_38_Template_button_click_0_listener", "_r87", "ctx_r86", "sendMessage", "ctx_r18", "canSendMessage", "MessageChatComponent_div_0_span_8_Template", "MessageChatComponent_div_0_span_9_Template", "MessageChatComponent_div_0_span_10_Template", "MessageChatComponent_div_0_button_12_Template", "MessageChatComponent_div_0_button_13_Template", "MessageChatComponent_div_0_div_18_Template", "MessageChatComponent_div_0_div_19_Template", "MessageChatComponent_div_0_div_20_Template", "MessageChatComponent_div_0_div_22_Template", "MessageChatComponent_div_0_div_23_Template", "MessageChatComponent_div_0_div_24_Template", "MessageChatComponent_div_0_div_25_Template", "MessageChatComponent_div_0_Template_button_click_29_listener", "_r89", "ctx_r88", "toggleAttachmentMenu", "MessageChatComponent_div_0_div_31_Template", "MessageChatComponent_div_0_Template_button_click_32_listener", "ctx_r90", "toggleEmojiPicker", "MessageChatComponent_div_0_Template_textarea_ngModelChange_34_listener", "$event", "ctx_r91", "messageContent", "MessageChatComponent_div_0_Template_textarea_keydown_34_listener", "ctx_r92", "onKeyPress", "MessageChatComponent_div_0_Template_textarea_input_34_listener", "ctx_r93", "onTyping", "MessageChatComponent_div_0_button_37_Template", "MessageChatComponent_div_0_button_38_Template", "MessageChatComponent_div_0_Template_input_change_39_listener", "ctx_r94", "onFileSelected", "ctx_r0", "isRecipientOnline", "groupPhoto", "getRecipientAvatar", "groupName", "getRecipientName", "isLoading", "messages", "trackByMessageId", "typingUsers", "isRecording", "showAttachmentMenu", "trim", "MessageChatComponent", "authService", "route", "router", "cdr", "ngZone", "currentUser", "isTyping", "currentPage", "hasMoreMessages", "loadingMoreMessages", "showEmojiPicker", "searchQuery", "searchResults", "showSearchResults", "subscriptions", "conversationId$", "initializeComponent", "setupSubscriptions", "ngAfterViewInit", "scrollToBottom", "getCurrentUser", "navigate", "params", "conversationSub", "getConversation", "console", "conversation", "markMessagesAsRead", "detectChanges", "messagesSub", "subscribeToMessages", "message", "addNewMessage", "markMessageAsRead", "typingSub", "subscribeToTypingIndicators", "handleTypingIndicator", "push", "sub", "typingTimeout", "clearTimeout", "recordingInterval", "clearInterval", "stopTyping", "files", "updateMessage", "sendNewMessage", "<PERSON><PERSON><PERSON><PERSON>", "hasFiles", "hasConversation", "getRecipientId", "tempMessage", "getFileMessageType", "sendObservable", "sentMessage", "replaceTemporaryMessage", "markMessageAsError", "newContent", "editMessage", "updatedMessage", "updateMessageInList", "deleteMessage", "canDeleteMessage", "confirm", "removeMessageFromList", "target", "Array", "from", "splice", "fileInput", "nativeElement", "click", "setInterval", "AUDIO", "startTyping", "currentUserId", "_id", "recipient", "find", "p", "file", "split", "tempId", "realMessage", "findIndex", "m", "messageId", "senderId", "userId", "user", "u", "unreadMessages", "runOutsideAngular", "messagesContainer", "element", "scrollTop", "scrollHeight", "date", "diffInHours", "getTime", "toLocaleTimeString", "hour", "minute", "toLocaleDateString", "day", "month", "key", "shift<PERSON>ey", "preventDefault", "startEditingMessage", "messageInput", "focus", "setReplyTo", "isOnline", "attachment", "open", "units", "unitIndex", "fileSize", "toFixed", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "playAudio", "catch", "minutes", "floor", "seconds", "padStart", "actions", "log", "AuthService", "ActivatedRoute", "Router", "ChangeDetectorRef", "NgZone", "viewQuery", "MessageChatComponent_Query", "MessageChatComponent_div_0_Template", "MessageChatComponent_div_1_Template", "MessagesListComponent", "MessagesListComponent_Template", "styles", "RouterModule", "MessageLayoutComponent", "routes", "path", "component", "children", "title", "MessagesRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "exports", "CommonModule", "FormsModule", "ReactiveFormsModule", "ApolloModule", "UserListComponent", "UserStatusService", "MessagesModule", "declarations", "Subscription", "FormControl", "FormGroup", "totalUsers", "totalPages", "UserListComponent_ul_61_li_1_button_11_Template_button_click_0_listener", "_r13", "user_r7", "UserListComponent_ul_61_li_1_button_12_Template_button_click_0_listener", "_r16", "UserListComponent_ul_61_li_1_Template_div_click_1_listener", "_r18", "ctx_r17", "startConversation", "UserListComponent_ul_61_li_1_span_4_Template", "UserListComponent_ul_61_li_1_button_11_Template", "UserListComponent_ul_61_li_1_button_12_Template", "email", "UserListComponent_ul_61_li_1_Template", "ctx_r3", "UserListComponent_div_63_Template_button_click_1_listener", "_r20", "ctx_r19", "loadNextPage", "callService", "loading", "pageSize", "hasNextPage", "hasPreviousPage", "sortBy", "sortOrder", "filterForm", "autoRefreshEnabled", "autoRefreshInterval", "loadingMore", "isDarkMode$", "getCurrentUserId", "setupFilterListeners", "setupAutoRefresh", "loadUsers", "searchSub", "get", "valueChanges", "resetPagination", "add", "onlineSub", "autoRefreshSubscription", "toggleAutoRefresh", "undefined", "setValue", "$any", "item", "forceRefresh", "getAllUsers", "isArray", "newUsers", "newUser", "some", "existingUser", "pagination", "currentUserPagination", "totalCount", "createConversation", "then", "loadPreviousPage", "refreshUsers", "clearFilters", "reset", "changeSortOrder", "field", "goBackToConversations", "AuthuserService", "i5", "i6", "i7", "UserListComponent_Template", "UserListComponent_Template_button_click_27_listener", "UserListComponent_Template_button_click_29_listener", "UserListComponent_Template_input_ngModelChange_33_listener", "UserListComponent_Template_input_change_39_listener", "tmp_b_0", "checked", "UserListComponent_Template_select_change_46_listener", "UserListComponent_Template_button_click_53_listener", "UserListComponent_Template_button_click_55_listener", "UserListComponent_div_57_Template", "UserListComponent_Template_div_scroll_58_listener", "clientHeight", "UserListComponent_div_59_Template", "UserListComponent_div_60_Template", "UserListComponent_ul_61_Template", "UserListComponent_div_62_Template", "UserListComponent_div_63_Template", "ɵɵpipeBind1", "tmp_2_0", "ɵɵclassMap"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}