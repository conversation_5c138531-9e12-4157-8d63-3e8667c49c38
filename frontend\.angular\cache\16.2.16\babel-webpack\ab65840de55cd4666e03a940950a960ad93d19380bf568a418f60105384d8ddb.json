{"ast": null, "code": "import { of } from 'rxjs';\nimport { delay } from 'rxjs/operators';\nimport { MessageType } from '../models/message.model';\nimport * as i0 from \"@angular/core\";\nexport let MockDataService = /*#__PURE__*/(() => {\n  class MockDataService {\n    constructor() {\n      // Utilisateurs de test\n      this.mockUsers = [{\n        id: '1',\n        username: '<PERSON>',\n        email: '<EMAIL>',\n        image: '/assets/images/avatars/alice.jpg',\n        isOnline: true,\n        role: 'developer'\n      }, {\n        id: '2',\n        username: '<PERSON>',\n        email: '<EMAIL>',\n        image: '/assets/images/avatars/bob.jpg',\n        isOnline: false,\n        role: 'designer'\n      }, {\n        id: '3',\n        username: '<PERSON>',\n        email: '<EMAIL>',\n        image: '/assets/images/avatars/claire.jpg',\n        isOnline: true,\n        role: 'manager'\n      }, {\n        id: '4',\n        username: '<PERSON>',\n        email: '<EMAIL>',\n        image: '/assets/images/avatars/david.jpg',\n        isOnline: true,\n        role: 'developer'\n      }, {\n        id: '5',\n        username: '<PERSON>',\n        email: '<EMAIL>',\n        image: '/assets/images/avatars/emma.jpg',\n        isOnline: false,\n        role: 'tester'\n      }];\n      // Messages de test\n      this.mockMessages = [{\n        id: '1',\n        content: 'Salut ! Comment ça va ?',\n        type: MessageType.TEXT,\n        timestamp: new Date(Date.now() - 3600000),\n        sender: this.mockUsers[1],\n        isRead: true,\n        conversationId: 'conv1'\n      }, {\n        id: '2',\n        content: 'Ça va bien merci ! Et toi ?',\n        type: MessageType.TEXT,\n        timestamp: new Date(Date.now() - 3500000),\n        sender: this.mockUsers[0],\n        isRead: true,\n        conversationId: 'conv1'\n      }, {\n        id: '3',\n        content: 'Super ! Tu as vu le nouveau design ?',\n        type: MessageType.TEXT,\n        timestamp: new Date(Date.now() - 1800000),\n        sender: this.mockUsers[1],\n        isRead: false,\n        conversationId: 'conv1'\n      }];\n      // Conversations de test\n      this.mockConversations = [{\n        id: 'conv1',\n        participants: [this.mockUsers[0], this.mockUsers[1]],\n        lastMessage: this.mockMessages[2],\n        unreadCount: 1,\n        isGroup: false,\n        createdAt: new Date(Date.now() - 86400000) // 1 day ago\n      }, {\n        id: 'conv2',\n        participants: [this.mockUsers[0], this.mockUsers[2]],\n        lastMessage: {\n          id: '4',\n          content: 'Réunion à 14h ?',\n          type: MessageType.TEXT,\n          timestamp: new Date(Date.now() - 7200000),\n          sender: this.mockUsers[2],\n          isRead: true,\n          conversationId: 'conv2'\n        },\n        unreadCount: 0,\n        isGroup: false,\n        createdAt: new Date(Date.now() - 172800000) // 2 days ago\n      }, {\n        id: 'conv3',\n        participants: [this.mockUsers[0], this.mockUsers[1], this.mockUsers[2], this.mockUsers[3]],\n        lastMessage: {\n          id: '5',\n          content: 'Nouveau projet lancé ! 🚀',\n          type: MessageType.TEXT,\n          timestamp: new Date(Date.now() - 10800000),\n          sender: this.mockUsers[3],\n          isRead: false,\n          conversationId: 'conv3'\n        },\n        unreadCount: 3,\n        isGroup: true,\n        groupName: 'Équipe DevBridge',\n        groupPhoto: '/assets/images/groups/team.jpg',\n        createdAt: new Date(Date.now() - 259200000) // 3 days ago\n      }];\n      // Notifications de test\n      this.mockNotifications = [{\n        id: 'notif1',\n        type: NotificationType.NEW_MESSAGE,\n        content: 'Nouveau message de Bob Dupont',\n        timestamp: new Date(Date.now() - 1800000),\n        isRead: false,\n        userId: '1'\n      }, {\n        id: 'notif2',\n        type: NotificationType.MESSAGE_REACTION,\n        content: 'Alice a réagi à votre message avec ❤️',\n        timestamp: new Date(Date.now() - 3600000),\n        isRead: true,\n        userId: '1'\n      }, {\n        id: 'notif3',\n        type: NotificationType.GROUP_INVITE,\n        content: 'Vous avez été ajouté au groupe \"Équipe DevBridge\"',\n        timestamp: new Date(Date.now() - 7200000),\n        isRead: false,\n        userId: '1'\n      }];\n    }\n    // ============================================================================\n    // MÉTHODES PUBLIQUES POUR LES TESTS\n    // ============================================================================\n    /**\n     * Récupère tous les utilisateurs\n     */\n    getUsers() {\n      return of(this.mockUsers).pipe(delay(500)); // Simule la latence réseau\n    }\n    /**\n     * Récupère toutes les conversations\n     */\n    getConversations() {\n      return of(this.mockConversations).pipe(delay(300));\n    }\n    /**\n     * Récupère une conversation par ID\n     */\n    getConversation(id) {\n      const conversation = this.mockConversations.find(c => c.id === id);\n      return of(conversation || null).pipe(delay(200));\n    }\n    /**\n     * Récupère les messages d'une conversation\n     */\n    getMessages(conversationId) {\n      const messages = this.mockMessages.filter(m => m.conversationId === conversationId);\n      return of(messages).pipe(delay(300));\n    }\n    /**\n     * Récupère toutes les notifications\n     */\n    getNotifications() {\n      return of(this.mockNotifications).pipe(delay(200));\n    }\n    /**\n     * Simule l'envoi d'un message\n     */\n    sendMessage(content, conversationId, senderId) {\n      const newMessage = {\n        id: `msg_${Date.now()}`,\n        content,\n        type: MessageType.TEXT,\n        timestamp: new Date(),\n        sender: this.mockUsers.find(u => u.id === senderId) || this.mockUsers[0],\n        isRead: false,\n        conversationId\n      };\n      // Ajouter le message à la liste\n      this.mockMessages.push(newMessage);\n      // Mettre à jour la conversation\n      const conversation = this.mockConversations.find(c => c.id === conversationId);\n      if (conversation) {\n        conversation.lastMessage = newMessage;\n      }\n      return of(newMessage).pipe(delay(100));\n    }\n    /**\n     * Simule la création d'une conversation\n     */\n    createConversation(userId, currentUserId) {\n      const otherUser = this.mockUsers.find(u => u.id === userId);\n      const currentUser = this.mockUsers.find(u => u.id === currentUserId);\n      if (!otherUser || !currentUser) {\n        throw new Error('Utilisateur non trouvé');\n      }\n      const newConversation = {\n        id: `conv_${Date.now()}`,\n        participants: [currentUser, otherUser],\n        unreadCount: 0,\n        isGroup: false,\n        createdAt: new Date()\n      };\n      this.mockConversations.unshift(newConversation);\n      return of(newConversation).pipe(delay(200));\n    }\n    /**\n     * Récupère l'utilisateur actuel (pour les tests)\n     */\n    getCurrentUser() {\n      return this.mockUsers[0]; // Alice comme utilisateur actuel\n    }\n    /**\n     * Simule la recherche d'utilisateurs\n     */\n    searchUsers(query) {\n      const results = this.mockUsers.filter(user => user.username.toLowerCase().includes(query.toLowerCase()) || user.email.toLowerCase().includes(query.toLowerCase()));\n      return of(results).pipe(delay(300));\n    }\n    /**\n     * Simule la recherche de conversations\n     */\n    searchConversations(query) {\n      const results = this.mockConversations.filter(conv => {\n        if (conv.isGroup) {\n          return conv.groupName?.toLowerCase().includes(query.toLowerCase());\n        } else {\n          return conv.participants?.some(p => p.username.toLowerCase().includes(query.toLowerCase()));\n        }\n      });\n      return of(results).pipe(delay(300));\n    }\n    static {\n      this.ɵfac = function MockDataService_Factory(t) {\n        return new (t || MockDataService)();\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: MockDataService,\n        factory: MockDataService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return MockDataService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}