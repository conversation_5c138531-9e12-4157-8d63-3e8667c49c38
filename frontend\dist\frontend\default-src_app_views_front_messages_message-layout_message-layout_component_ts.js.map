{"version": 3, "file": "default-src_app_views_front_messages_message-layout_message-layout_component_ts.js", "mappings": ";;;;;;;;;;;;;;;;;AACuD;AAChB;AAQN;;AAK3B,MAAOG,eAAe;EAkK1BC,YAAA;IAjKA;IACQ,KAAAC,SAAS,GAAW,CAC1B;MACEC,EAAE,EAAE,GAAG;MACPC,GAAG,EAAE,GAAG;MACRC,QAAQ,EAAE,cAAc;MACxBC,KAAK,EAAE,qBAAqB;MAC5BC,KAAK,EAAE,kCAAkC;MACzCC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE;KACP,EACD;MACEP,EAAE,EAAE,GAAG;MACPC,GAAG,EAAE,GAAG;MACRC,QAAQ,EAAE,YAAY;MACtBC,KAAK,EAAE,mBAAmB;MAC1BC,KAAK,EAAE,gCAAgC;MACvCC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE;KACP,EACD;MACEP,EAAE,EAAE,GAAG;MACPC,GAAG,EAAE,GAAG;MACRC,QAAQ,EAAE,iBAAiB;MAC3BC,KAAK,EAAE,sBAAsB;MAC7BC,KAAK,EAAE,mCAAmC;MAC1CC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE;KACP,EACD;MACEP,EAAE,EAAE,GAAG;MACPC,GAAG,EAAE,GAAG;MACRC,QAAQ,EAAE,YAAY;MACtBC,KAAK,EAAE,qBAAqB;MAC5BC,KAAK,EAAE,kCAAkC;MACzCC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE;KACP,EACD;MACEP,EAAE,EAAE,GAAG;MACPC,GAAG,EAAE,GAAG;MACRC,QAAQ,EAAE,aAAa;MACvBC,KAAK,EAAE,oBAAoB;MAC3BC,KAAK,EAAE,iCAAiC;MACxCC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE;KACP,CACF;IAED;IACQ,KAAAC,YAAY,GAAc,CAChC;MACER,EAAE,EAAE,GAAG;MACPS,OAAO,EAAE,yBAAyB;MAClCC,IAAI,EAAEd,8DAAW,CAACe,IAAI;MACtBC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,OAAO,CAAC;MACzCC,MAAM,EAAE,IAAI,CAAChB,SAAS,CAAC,CAAC,CAAC;MACzBiB,MAAM,EAAE,IAAI;MACZC,cAAc,EAAE;KACjB,EACD;MACEjB,EAAE,EAAE,GAAG;MACPS,OAAO,EAAE,6BAA6B;MACtCC,IAAI,EAAEd,8DAAW,CAACe,IAAI;MACtBC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,OAAO,CAAC;MACzCC,MAAM,EAAE,IAAI,CAAChB,SAAS,CAAC,CAAC,CAAC;MACzBiB,MAAM,EAAE,IAAI;MACZC,cAAc,EAAE;KACjB,EACD;MACEjB,EAAE,EAAE,GAAG;MACPS,OAAO,EAAE,sCAAsC;MAC/CC,IAAI,EAAEd,8DAAW,CAACe,IAAI;MACtBC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,OAAO,CAAC;MACzCC,MAAM,EAAE,IAAI,CAAChB,SAAS,CAAC,CAAC,CAAC;MACzBiB,MAAM,EAAE,KAAK;MACbC,cAAc,EAAE;KACjB,CACF;IAED;IACQ,KAAAC,iBAAiB,GAAmB,CAC1C;MACElB,EAAE,EAAE,OAAO;MACXmB,YAAY,EAAE,CAAC,IAAI,CAACpB,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,CAACA,SAAS,CAAC,CAAC,CAAC,CAAC;MACpDqB,WAAW,EAAE,IAAI,CAACZ,YAAY,CAAC,CAAC,CAAC;MACjCa,WAAW,EAAE,CAAC;MACdC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,IAAIV,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,QAAQ,CAAC,CAAE;KAC7C,EACD;MACEd,EAAE,EAAE,OAAO;MACXmB,YAAY,EAAE,CAAC,IAAI,CAACpB,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,CAACA,SAAS,CAAC,CAAC,CAAC,CAAC;MACpDqB,WAAW,EAAE;QACXpB,EAAE,EAAE,GAAG;QACPS,OAAO,EAAE,iBAAiB;QAC1BC,IAAI,EAAEd,8DAAW,CAACe,IAAI;QACtBC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,OAAO,CAAC;QACzCC,MAAM,EAAE,IAAI,CAAChB,SAAS,CAAC,CAAC,CAAC;QACzBiB,MAAM,EAAE,IAAI;QACZC,cAAc,EAAE;OACjB;MACDI,WAAW,EAAE,CAAC;MACdC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,IAAIV,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAE;KAC9C,EACD;MACEd,EAAE,EAAE,OAAO;MACXmB,YAAY,EAAE,CACZ,IAAI,CAACpB,SAAS,CAAC,CAAC,CAAC,EACjB,IAAI,CAACA,SAAS,CAAC,CAAC,CAAC,EACjB,IAAI,CAACA,SAAS,CAAC,CAAC,CAAC,EACjB,IAAI,CAACA,SAAS,CAAC,CAAC,CAAC,CAClB;MACDqB,WAAW,EAAE;QACXpB,EAAE,EAAE,GAAG;QACPS,OAAO,EAAE,2BAA2B;QACpCC,IAAI,EAAEd,8DAAW,CAACe,IAAI;QACtBC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,QAAQ,CAAC;QAC1CC,MAAM,EAAE,IAAI,CAAChB,SAAS,CAAC,CAAC,CAAC;QACzBiB,MAAM,EAAE,KAAK;QACbC,cAAc,EAAE;OACjB;MACDI,WAAW,EAAE,CAAC;MACdC,OAAO,EAAE,IAAI;MACbE,SAAS,EAAE,kBAAkB;MAC7BC,UAAU,EAAE,gCAAgC;MAC5CF,SAAS,EAAE,IAAIV,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAE;KAC9C,CACF;IAED;IACQ,KAAAY,iBAAiB,GAAmB,CAC1C;MACE1B,EAAE,EAAE,QAAQ;MACZU,IAAI,EAAE,aAAoB;MAC1BD,OAAO,EAAE,+BAA+B;MACxCG,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,OAAO,CAAC;MACzCE,MAAM,EAAE;KACT,EACD;MACEhB,EAAE,EAAE,QAAQ;MACZU,IAAI,EAAE,kBAAyB;MAC/BD,OAAO,EAAE,uCAAuC;MAChDG,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,OAAO,CAAC;MACzCE,MAAM,EAAE;KACT,EACD;MACEhB,EAAE,EAAE,QAAQ;MACZU,IAAI,EAAE,cAAqB;MAC3BD,OAAO,EAAE,mDAAmD;MAC5DG,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,OAAO,CAAC;MACzCE,MAAM,EAAE;KACT,CACF;EAEc;EAEf;EACA;EACA;EAEA;;;EAGAW,QAAQA,CAAA;IACN,OAAOjC,wCAAE,CAAC,IAAI,CAACK,SAAS,CAAC,CAAC6B,IAAI,CAACjC,qDAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC9C;EAEA;;;EAGAkC,gBAAgBA,CAAA;IACd,OAAOnC,wCAAE,CAAC,IAAI,CAACwB,iBAAiB,CAAC,CAACU,IAAI,CAACjC,qDAAK,CAAC,GAAG,CAAC,CAAC;EACpD;EAEA;;;EAGAmC,eAAeA,CAAC9B,EAAU;IACxB,MAAM+B,YAAY,GAAG,IAAI,CAACb,iBAAiB,CAACc,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACjC,EAAE,KAAKA,EAAE,CAAC;IACpE,OAAON,wCAAE,CAACqC,YAAY,IAAI,IAAI,CAAC,CAACH,IAAI,CAACjC,qDAAK,CAAC,GAAG,CAAC,CAAC;EAClD;EAEA;;;EAGAuC,WAAWA,CAACjB,cAAsB;IAChC,MAAMkB,QAAQ,GAAG,IAAI,CAAC3B,YAAY,CAAC4B,MAAM,CACtCC,CAAC,IAAKA,CAAC,CAACpB,cAAc,KAAKA,cAAc,CAC3C;IACD,OAAOvB,wCAAE,CAACyC,QAAQ,CAAC,CAACP,IAAI,CAACjC,qDAAK,CAAC,GAAG,CAAC,CAAC;EACtC;EAEA;;;EAGA2C,gBAAgBA,CAAA;IACd,OAAO5C,wCAAE,CAAC,IAAI,CAACgC,iBAAiB,CAAC,CAACE,IAAI,CAACjC,qDAAK,CAAC,GAAG,CAAC,CAAC;EACpD;EAEA;;;EAGA4C,WAAWA,CACT9B,OAAe,EACfQ,cAAsB,EACtBuB,QAAgB;IAEhB,MAAMC,UAAU,GAAY;MAC1BzC,EAAE,EAAE,OAAOa,IAAI,CAACC,GAAG,EAAE,EAAE;MACvBL,OAAO;MACPC,IAAI,EAAEd,8DAAW,CAACe,IAAI;MACtBC,SAAS,EAAE,IAAIC,IAAI,EAAE;MACrBE,MAAM,EACJ,IAAI,CAAChB,SAAS,CAACiC,IAAI,CAAEU,CAAC,IAAKA,CAAC,CAAC1C,EAAE,KAAKwC,QAAQ,CAAC,IAAI,IAAI,CAACzC,SAAS,CAAC,CAAC,CAAC;MACpEiB,MAAM,EAAE,KAAK;MACbC;KACD;IAED;IACA,IAAI,CAACT,YAAY,CAACmC,IAAI,CAACF,UAAU,CAAC;IAElC;IACA,MAAMV,YAAY,GAAG,IAAI,CAACb,iBAAiB,CAACc,IAAI,CAC7CC,CAAC,IAAKA,CAAC,CAACjC,EAAE,KAAKiB,cAAc,CAC/B;IACD,IAAIc,YAAY,EAAE;MAChBA,YAAY,CAACX,WAAW,GAAGqB,UAAU;;IAGvC,OAAO/C,wCAAE,CAAC+C,UAAU,CAAC,CAACb,IAAI,CAACjC,qDAAK,CAAC,GAAG,CAAC,CAAC;EACxC;EAEA;;;EAGAiD,kBAAkBA,CAChBC,MAAc,EACdC,aAAqB;IAErB,MAAMC,SAAS,GAAG,IAAI,CAAChD,SAAS,CAACiC,IAAI,CAAEU,CAAC,IAAKA,CAAC,CAAC1C,EAAE,KAAK6C,MAAM,CAAC;IAC7D,MAAMG,WAAW,GAAG,IAAI,CAACjD,SAAS,CAACiC,IAAI,CAAEU,CAAC,IAAKA,CAAC,CAAC1C,EAAE,KAAK8C,aAAa,CAAC;IAEtE,IAAI,CAACC,SAAS,IAAI,CAACC,WAAW,EAAE;MAC9B,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAAC;;IAG3C,MAAMC,eAAe,GAAiB;MACpClD,EAAE,EAAE,QAAQa,IAAI,CAACC,GAAG,EAAE,EAAE;MACxBK,YAAY,EAAE,CAAC6B,WAAW,EAAED,SAAS,CAAC;MACtC1B,WAAW,EAAE,CAAC;MACdC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,IAAIV,IAAI;KACpB;IAED,IAAI,CAACK,iBAAiB,CAACiC,OAAO,CAACD,eAAe,CAAC;IAC/C,OAAOxD,wCAAE,CAACwD,eAAe,CAAC,CAACtB,IAAI,CAACjC,qDAAK,CAAC,GAAG,CAAC,CAAC;EAC7C;EAEA;;;EAGAyD,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACrD,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B;EAEA;;;EAGAsD,WAAWA,CAACC,KAAa;IACvB,MAAMC,OAAO,GAAG,IAAI,CAACxD,SAAS,CAACqC,MAAM,CAClCoB,IAAI,IACHA,IAAI,CAACtD,QAAQ,CAACuD,WAAW,EAAE,CAACC,QAAQ,CAACJ,KAAK,CAACG,WAAW,EAAE,CAAC,IACzDD,IAAI,CAACrD,KAAK,CAACsD,WAAW,EAAE,CAACC,QAAQ,CAACJ,KAAK,CAACG,WAAW,EAAE,CAAC,CACzD;IACD,OAAO/D,wCAAE,CAAC6D,OAAO,CAAC,CAAC3B,IAAI,CAACjC,qDAAK,CAAC,GAAG,CAAC,CAAC;EACrC;EAEA;;;EAGAgE,mBAAmBA,CAACL,KAAa;IAC/B,MAAMC,OAAO,GAAG,IAAI,CAACrC,iBAAiB,CAACkB,MAAM,CAAEwB,IAAI,IAAI;MACrD,IAAIA,IAAI,CAACtC,OAAO,EAAE;QAChB,OAAOsC,IAAI,CAACpC,SAAS,EAAEiC,WAAW,EAAE,CAACC,QAAQ,CAACJ,KAAK,CAACG,WAAW,EAAE,CAAC;OACnE,MAAM;QACL,OAAOG,IAAI,CAACzC,YAAY,EAAE0C,IAAI,CAAEC,CAAC,IAC/BA,CAAC,CAAC5D,QAAQ,CAACuD,WAAW,EAAE,CAACC,QAAQ,CAACJ,KAAK,CAACG,WAAW,EAAE,CAAC,CACvD;;IAEL,CAAC,CAAC;IACF,OAAO/D,wCAAE,CAAC6D,OAAO,CAAC,CAAC3B,IAAI,CAACjC,qDAAK,CAAC,GAAG,CAAC,CAAC;EACrC;;;uBA3SWE,eAAe;IAAA;EAAA;;;aAAfA,eAAe;MAAAkE,OAAA,EAAflE,eAAe,CAAAmE,IAAA;MAAAC,UAAA,EAFd;IAAM;EAAA;;;;;;;;;;;;;;;;;ACZmB;;AAMjC,MAAOE,YAAY;EAKvBrE,YAAA;IAJQ,KAAAsE,aAAa,GAAG,IAAIF,iDAAe,CAAU,EAAE,CAAC;IACxD,KAAAG,OAAO,GAAG,IAAI,CAACD,aAAa,CAACE,YAAY,EAAE;IACnC,KAAAC,SAAS,GAAG,CAAC;EAEN;EACPC,UAAUA,CAAA;IAChB,OAAOC,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;EAChD;EAEQC,QAAQA,CAACC,KAAwB;IACvC,MAAMC,QAAQ,GAAU;MACtB,GAAGD,KAAK;MACR9E,EAAE,EAAE,IAAI,CAACwE,UAAU,EAAE;MACrBQ,QAAQ,EAAEF,KAAK,CAACE,QAAQ,IAAI;KAC7B;IAED,MAAMC,aAAa,GAAG,IAAI,CAACb,aAAa,CAACc,KAAK;IAC9C,IAAI,CAACd,aAAa,CAACe,IAAI,CAAC,CAAC,GAAGF,aAAa,EAAEF,QAAQ,CAAC,CAAC;IAErD;IACA,IAAIA,QAAQ,CAACC,QAAQ,IAAID,QAAQ,CAACC,QAAQ,GAAG,CAAC,EAAE;MAC9CI,UAAU,CAAC,MAAK;QACd,IAAI,CAACC,WAAW,CAACN,QAAQ,CAAC/E,EAAE,CAAC;MAC/B,CAAC,EAAE+E,QAAQ,CAACC,QAAQ,CAAC;;EAEzB;EACAM,IAAIA,CACFC,OAAe,EACf7E,IAAA,GAAiD,MAAM,EACvDsE,QAAQ,GAAG,IAAI;IAEf,MAAMhF,EAAE,GAAG,IAAI,CAACwE,UAAU,EAAE;IAC5B,MAAMM,KAAK,GAAU;MAAE9E,EAAE;MAAEU,IAAI;MAAE8E,KAAK,EAAE,EAAE;MAAED,OAAO;MAAEP;IAAQ,CAAE;IAC/D,MAAMC,aAAa,GAAG,IAAI,CAACb,aAAa,CAACc,KAAK;IAC9C,IAAI,CAACd,aAAa,CAACe,IAAI,CAAC,CAAC,GAAGF,aAAa,EAAEH,KAAK,CAAC,CAAC;IAElD,IAAIE,QAAQ,GAAG,CAAC,EAAE;MAChBI,UAAU,CAAC,MAAM,IAAI,CAACK,OAAO,CAACzF,EAAE,CAAC,EAAEgF,QAAQ,CAAC;;EAEhD;EAEAU,WAAWA,CAACH,OAAe,EAAEP,QAAQ,GAAG,IAAI;IAC1C,IAAI,CAACM,IAAI,CAACC,OAAO,EAAE,SAAS,EAAEP,QAAQ,CAAC;EACzC;EAEAW,SAASA,CAACJ,OAAe,EAAEP,QAAQ,GAAG,IAAI;IACxC,IAAI,CAACM,IAAI,CAACC,OAAO,EAAE,OAAO,EAAEP,QAAQ,CAAC;EACvC;EAEAY,WAAWA,CAACL,OAAe,EAAEP,QAAQ,GAAG,IAAI;IAC1C,IAAI,CAACM,IAAI,CAACC,OAAO,EAAE,SAAS,EAAEP,QAAQ,CAAC;EACzC;EAEAa,QAAQA,CAACN,OAAe,EAAEP,QAAQ,GAAG,IAAI;IACvC,IAAI,CAACM,IAAI,CAACC,OAAO,EAAE,MAAM,EAAEP,QAAQ,CAAC;EACtC;EAEAS,OAAOA,CAACzF,EAAU;IAChB,MAAMiF,aAAa,GAAG,IAAI,CAACb,aAAa,CAACc,KAAK,CAAC9C,MAAM,CAAE0D,CAAC,IAAKA,CAAC,CAAC9F,EAAE,KAAKA,EAAE,CAAC;IACzE,IAAI,CAACoE,aAAa,CAACe,IAAI,CAACF,aAAa,CAAC;EACxC;EACAc,OAAOA,CAACP,KAAa,EAAED,OAAe,EAAEP,QAAiB;IACvD,IAAI,CAACH,QAAQ,CAAC;MACZnE,IAAI,EAAE,SAAS;MACf8E,KAAK;MACLD,OAAO;MACPP,QAAQ;MACRgB,IAAI,EAAE;KACP,CAAC;EACJ;EACAC,KAAKA,CACHT,KAAa,EACbD,OAAe,EACfP,QAAiB,EACjBkB,MAAwB;IAExB,IAAI,CAACrB,QAAQ,CAAC;MACZnE,IAAI,EAAE,OAAO;MACb8E,KAAK;MACLD,OAAO;MACPP,QAAQ,EAAEA,QAAQ,IAAI,IAAI;MAC1BgB,IAAI,EAAE,UAAU;MAChBE;KACD,CAAC;EACJ;EAEAC,OAAOA,CAACX,KAAa,EAAED,OAAe,EAAEP,QAAiB;IACvD,IAAI,CAACH,QAAQ,CAAC;MACZnE,IAAI,EAAE,SAAS;MACf8E,KAAK;MACLD,OAAO;MACPP,QAAQ;MACRgB,IAAI,EAAE;KACP,CAAC;EACJ;EACA;EACAI,YAAYA,CAACF,MAAA,GAAiB,wBAAwB,EAAEG,IAAa;IACnE,MAAMC,QAAQ,GAAGD,IAAI,GAAG,WAAWA,IAAI,GAAG,GAAG,EAAE;IAC/C,IAAI,CAACJ,KAAK,CACR,cAAc,EACd,oDAAoDC,MAAM,GAAGI,QAAQ,EAAE,EACvE,IAAI,EACJ;MACEC,KAAK,EAAE,sBAAsB;MAC7BC,OAAO,EAAEA,CAAA,KAAK;QACZ;QACAC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACtD;KACD,CACF;EACH;EAEAC,iBAAiBA,CAACC,QAAA,GAAmB,iBAAiB;IACpD,IAAI,CAACX,KAAK,CACR,qBAAqB,EACrB,2DAA2DW,QAAQ,EAAE,EACrE,IAAI,CACL;EACH;EAEAvB,WAAWA,CAACrF,EAAU;IACpB,MAAMiF,aAAa,GAAG,IAAI,CAACb,aAAa,CAACc,KAAK;IAC9C,IAAI,CAACd,aAAa,CAACe,IAAI,CAACF,aAAa,CAAC7C,MAAM,CAAE0C,KAAK,IAAKA,KAAK,CAAC9E,EAAE,KAAKA,EAAE,CAAC,CAAC;EAC3E;EACA6G,KAAKA,CAAA;IACH,IAAI,CAACzC,aAAa,CAACe,IAAI,CAAC,EAAE,CAAC;EAC7B;;;uBA/HWhB,YAAY;IAAA;EAAA;;;aAAZA,YAAY;MAAAJ,OAAA,EAAZI,YAAY,CAAAH,IAAA;MAAAC,UAAA,EAFX;IAAM;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;ACGiC;;;;;;;;;;;;;ICsDrC6C,uDAAA,YAGK;;;;;;IAfPA,4DAAA,cAIC;IADCA,wDAAA,mBAAAI,kEAAA;MAAA,MAAAC,WAAA,GAAAL,2DAAA,CAAAO,IAAA;MAAA,MAAAC,QAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,OAAA,GAAAV,2DAAA;MAAA,OAASA,yDAAA,CAAAU,OAAA,CAAAG,WAAA,CAAAL,QAAA,CAAAM,IAAA,CAAuB;IAAA,EAAC;IAEjCd,uDAAA,cAKO;IACPA,4DAAA,eAAiC;IAAAA,oDAAA,GAAuB;IAAAA,0DAAA,EAAO;IAC/DA,wDAAA,IAAAkB,gDAAA,gBAGK;IACPlB,0DAAA,EAAM;;;;;IATFA,uDAAA,GAA4C;IAA5CA,yDAAA,eAAAQ,QAAA,CAAAa,SAAA,CAAAC,OAAA,CAA4C;IAC5CtB,yDAAA,kBAAAwB,MAAA,CAAAC,YAAA,kBAAAD,MAAA,CAAAC,YAAA,CAAAX,IAAA,MAAAN,QAAA,CAAAM,IAAA,CAAwD,qBAAAU,MAAA,CAAAC,YAAA,kBAAAD,MAAA,CAAAC,YAAA,CAAAX,IAAA,MAAAN,QAAA,CAAAM,IAAA;IAGzBd,uDAAA,GAAuB;IAAvBA,+DAAA,CAAAQ,QAAA,CAAAmB,WAAA,CAAuB;IAErD3B,uDAAA,GAAuC;IAAvCA,wDAAA,UAAAwB,MAAA,CAAAC,YAAA,kBAAAD,MAAA,CAAAC,YAAA,CAAAX,IAAA,MAAAN,QAAA,CAAAM,IAAA,CAAuC;;;;;IApB9Cd,4DAAA,cAGC;IAEGA,oDAAA,8BACF;IAAAA,0DAAA,EAAM;IACNA,wDAAA,IAAA6B,4CAAA,kBAgBM;IACR7B,0DAAA,EAAM;;;;IAhBgBA,uDAAA,GAAkB;IAAlBA,wDAAA,YAAA8B,MAAA,CAAAC,eAAA,CAAkB;;;;;;IAwC5C/B,4DAAA,iBAIC;IAFCA,wDAAA,mBAAAgC,kEAAA;MAAAhC,2DAAA,CAAAiC,IAAA;MAAA,MAAAC,OAAA,GAAAlC,2DAAA;MAAA,OAASA,yDAAA,CAAAkC,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAGvBnC,uDAAA,YAA4B;IAC9BA,0DAAA,EAAS;;;;;IA2CTA,4DAAA,eAGC;IACCA,oDAAA,GACF;IAAAA,0DAAA,EAAO;;;;IADLA,uDAAA,GACF;IADEA,gEAAA,MAAAqC,MAAA,CAAAC,aAAA,CAAAC,MAAA,cAAAF,MAAA,CAAAC,aAAA,CAAAC,MAAA,MACF;;;;;;IAkBEvC,4DAAA,cAIC;IADCA,wDAAA,mBAAAwC,wEAAA;MAAA,MAAAnC,WAAA,GAAAL,2DAAA,CAAAyC,IAAA;MAAA,MAAAC,UAAA,GAAArC,WAAA,CAAAI,SAAA;MAAA,MAAAkC,OAAA,GAAA3C,2DAAA;MAAA,OAASA,yDAAA,CAAA2C,OAAA,CAAAC,kBAAA,CAAAF,UAAA,CAA0B;IAAA,EAAC;IAEpC1C,4DAAA,aAAyC;IACvCA,uDAAA,cAIE;IACFA,4DAAA,cAA4B;IAExBA,oDAAA,GACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,YAA0C;IACxCA,oDAAA,GACF;IAAAA,0DAAA,EAAI;;;;;IAVJA,uDAAA,GAAqC;IAArCA,wDAAA,QAAA6C,OAAA,CAAAC,qBAAA,CAAAJ,UAAA,GAAA1C,2DAAA,CAAqC,QAAA6C,OAAA,CAAAG,mBAAA,CAAAN,UAAA;IAMnC1C,uDAAA,GACF;IADEA,gEAAA,MAAA6C,OAAA,CAAAG,mBAAA,CAAAN,UAAA,OACF;IAEE1C,uDAAA,GACF;IADEA,gEAAA,MAAA6C,OAAA,CAAAI,qBAAA,CAAAP,UAAA,OACF;;;;;IAxBR1C,4DAAA,cAGC;IAEGA,oDAAA,GACF;IAAAA,0DAAA,EAAM;IACNA,wDAAA,IAAAkD,kDAAA,kBAoBM;IACRlD,0DAAA,EAAM;;;;IAvBFA,uDAAA,GACF;IADEA,gEAAA,mCAAAmD,OAAA,CAAAC,aAAA,CAAAb,MAAA,OACF;IAEqBvC,uDAAA,GAAgB;IAAhBA,wDAAA,YAAAmD,OAAA,CAAAC,aAAA,CAAgB;;;;;IAuBvCpD,4DAAA,cAGC;IACCA,uDAAA,YAA2C;IAC3CA,4DAAA,QAAG;IAAAA,oDAAA,sCAAqB;IAAAA,0DAAA,EAAI;;;;;IAM5BA,4DAAA,cAGC;IACCA,uDAAA,cAEO;IACPA,4DAAA,YAA8B;IAAAA,oDAAA,sCAA+B;IAAAA,0DAAA,EAAI;;;;;IAuB7DA,uDAAA,cAGO;;;;;IAsBLA,4DAAA,eAGC;IACCA,oDAAA,GAKF;IAAAA,0DAAA,EAAO;;;;;IALLA,uDAAA,GAKF;IALEA,gEAAA,MAAAqD,OAAA,CAAAC,cAAA,CAAAC,gBAAA,iBAAAF,OAAA,CAAAC,cAAA,CAAAC,gBAAA,OAKF;;;;;;IArDRvD,4DAAA,cAUC;IADCA,wDAAA,mBAAAwD,wEAAA;MAAA,MAAAnD,WAAA,GAAAL,2DAAA,CAAAyD,IAAA;MAAA,MAAAF,gBAAA,GAAAlD,WAAA,CAAAI,SAAA;MAAA,MAAAiD,OAAA,GAAA1D,2DAAA;MAAA,OAASA,yDAAA,CAAA0D,OAAA,CAAAd,kBAAA,CAAAW,gBAAA,CAAgC;IAAA,EAAC;IAE1CvD,4DAAA,aAAyC;IAGrCA,uDAAA,cAIE;IACFA,wDAAA,IAAA2D,wDAAA,kBAGO;IACT3D,0DAAA,EAAM;IAGNA,4DAAA,cAA4B;IAGtBA,oDAAA,GACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,eAAoC;IAClCA,oDAAA,IAGF;IAAAA,0DAAA,EAAO;IAGTA,4DAAA,eAAoD;IAEhDA,oDAAA,IACF;IAAAA,0DAAA,EAAI;IAGJA,wDAAA,KAAA4D,0DAAA,mBASO;IACT5D,0DAAA,EAAM;;;;;IAhDVA,yDAAA,gBAAA6D,OAAA,CAAAC,sBAAA,KAAAP,gBAAA,CAAArK,EAAA,CAAgE,eAAA2K,OAAA,CAAAC,sBAAA,KAAAP,gBAAA,CAAArK,EAAA,qBAAA2K,OAAA,CAAAC,sBAAA,KAAAP,gBAAA,CAAArK,EAAA;IAS1D8G,uDAAA,GAA2C;IAA3CA,wDAAA,QAAA6D,OAAA,CAAAf,qBAAA,CAAAS,gBAAA,GAAAvD,2DAAA,CAA2C,QAAA6D,OAAA,CAAAb,mBAAA,CAAAO,gBAAA;IAK1CvD,uDAAA,GAA4E;IAA5EA,wDAAA,UAAAuD,gBAAA,CAAA/I,OAAA,IAAAqJ,OAAA,CAAAE,YAAA,CAAAR,gBAAA,CAAAlJ,YAAA,kBAAAkJ,gBAAA,CAAAlJ,YAAA,KAA4E;IAS3E2F,uDAAA,GACF;IADEA,gEAAA,MAAA6D,OAAA,CAAAb,mBAAA,CAAAO,gBAAA,OACF;IAEEvD,uDAAA,GAGF;IAHEA,gEAAA,MAAA6D,OAAA,CAAAG,qBAAA,CAAAT,gBAAA,CAAAjJ,WAAA,kBAAAiJ,gBAAA,CAAAjJ,WAAA,CAAAR,SAAA,OAGF;IAKEkG,uDAAA,GACF;IADEA,gEAAA,MAAA6D,OAAA,CAAAZ,qBAAA,CAAAM,gBAAA,OACF;IAIGvD,uDAAA,GAAsC;IAAtCA,wDAAA,SAAA6D,OAAA,CAAAP,cAAA,CAAAC,gBAAA,MAAsC;;;;;IAqB7CvD,4DAAA,WAAsC;IAAAA,oDAAA,mBAAY;IAAAA,0DAAA,EAAO;;;;;IACzDA,4DAAA,WAAqC;IAAAA,oDAAA,oBAAa;IAAAA,0DAAA,EAAO;;;;;;IAP7DA,4DAAA,cAA0D;IAEtDA,wDAAA,mBAAAiE,2EAAA;MAAAjE,2DAAA,CAAAkE,IAAA;MAAA,MAAAC,OAAA,GAAAnE,2DAAA;MAAA,OAASA,yDAAA,CAAAmE,OAAA,CAAAC,qBAAA,EAAuB;IAAA,EAAC;IAIjCpE,wDAAA,IAAAqE,yDAAA,mBAAyD;IACzDrE,wDAAA,IAAAsE,yDAAA,mBAAyD;IAC3DtE,0DAAA,EAAS;;;;IALPA,uDAAA,GAAmC;IAAnCA,wDAAA,aAAAuE,OAAA,CAAAC,sBAAA,CAAmC;IAG5BxE,uDAAA,GAA6B;IAA7BA,wDAAA,UAAAuE,OAAA,CAAAC,sBAAA,CAA6B;IAC7BxE,uDAAA,GAA4B;IAA5BA,wDAAA,SAAAuE,OAAA,CAAAC,sBAAA,CAA4B;;;;;IAKvCxE,4DAAA,cAGC;IACCA,uDAAA,YAA6C;IAC7CA,4DAAA,QAAG;IAAAA,oDAAA,0BAAmB;IAAAA,0DAAA,EAAI;IAC1BA,4DAAA,YAAwB;IACtBA,oDAAA,mEACF;IAAAA,0DAAA,EAAI;;;;;IA7FRA,4DAAA,UAA0B;IAExBA,wDAAA,IAAAyE,kDAAA,kBAQM;IAGNzE,wDAAA,IAAA0E,kDAAA,oBAyDM;IAGN1E,wDAAA,IAAA2E,kDAAA,kBASM;IAGN3E,wDAAA,IAAA4E,kDAAA,kBASM;IACR5E,0DAAA,EAAM;;;;IA5FDA,uDAAA,GAA0D;IAA1DA,wDAAA,SAAA6E,OAAA,CAAAL,sBAAA,IAAAK,OAAA,CAAAC,aAAA,CAAAvC,MAAA,OAA0D;IAYzBvC,uDAAA,GACjB;IADiBA,wDAAA,YAAA6E,OAAA,CAAAC,aAAA,CACjB,iBAAAD,OAAA,CAAAE,qBAAA;IAyDb/E,uDAAA,GAA0B;IAA1BA,wDAAA,SAAA6E,OAAA,CAAAG,oBAAA,CAA0B;IAa7BhF,uDAAA,GAA2D;IAA3DA,wDAAA,SAAA6E,OAAA,CAAAC,aAAA,CAAAvC,MAAA,WAAAsC,OAAA,CAAAL,sBAAA,CAA2D;;;;;IAhIlExE,4DAAA,cAAsE;IAEpEA,wDAAA,IAAAiF,4CAAA,kBA4BM;IAGNjF,wDAAA,IAAAkF,4CAAA,kBAMM;IAGNlF,wDAAA,IAAAmF,4CAAA,kBA+FM;IACRnF,0DAAA,EAAM;;;;IAvIDA,uDAAA,GAA6C;IAA7CA,wDAAA,SAAAoF,MAAA,CAAAC,WAAA,IAAAD,MAAA,CAAAhC,aAAA,CAAAb,MAAA,KAA6C;IA+B7CvC,uDAAA,GAA+C;IAA/CA,wDAAA,SAAAoF,MAAA,CAAAC,WAAA,IAAAD,MAAA,CAAAhC,aAAA,CAAAb,MAAA,OAA+C;IAQ5CvC,uDAAA,GAAkB;IAAlBA,wDAAA,UAAAoF,MAAA,CAAAC,WAAA,CAAkB;;;;;IA0HhBrF,uDAAA,cAGO;;;;;IAVXA,4DAAA,aAAgE;IAE5DA,uDAAA,cAIE;IACFA,wDAAA,IAAAsF,8DAAA,kBAGO;IACTtF,0DAAA,EAAM;IACNA,4DAAA,cAA4B;IAExBA,oDAAA,GACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,YAA0C;IAAAA,oDAAA,GAAkB;IAAAA,0DAAA,EAAI;IAElEA,4DAAA,cAA2B;IACzBA,uDAAA,aAA8B;IAChCA,0DAAA,EAAM;;;;;IAjBFA,uDAAA,GAA2D;IAA3DA,wDAAA,QAAAuF,UAAA,CAAAjM,KAAA,yCAAA0G,2DAAA,CAA2D,QAAAuF,UAAA,CAAAnM,QAAA;IAK1D4G,uDAAA,GAA0B;IAA1BA,wDAAA,SAAAwF,OAAA,CAAAzB,YAAA,CAAAwB,UAAA,EAA0B;IAM3BvF,uDAAA,GACF;IADEA,gEAAA,MAAAuF,UAAA,CAAAnM,QAAA,MACF;IAC0C4G,uDAAA,GAAkB;IAAlBA,+DAAA,CAAAuF,UAAA,CAAAlM,KAAA,CAAkB;;;;;;IArBlE2G,4DAAA,cAIC;IADCA,wDAAA,mBAAAyF,wEAAA;MAAA,MAAApF,WAAA,GAAAL,2DAAA,CAAA0F,IAAA;MAAA,MAAAH,UAAA,GAAAlF,WAAA,CAAAI,SAAA;MAAA,MAAAkF,OAAA,GAAA3F,2DAAA;MAAA,OAASA,yDAAA,CAAA2F,OAAA,CAAAC,MAAA,CAAAL,UAAA,CAAc,GAAGI,OAAA,CAAAE,yBAAA,CAAAN,UAAA,CAAiC,GAAG,IAAI;IAAA,EAAC;IAEnEvF,wDAAA,IAAA8F,wDAAA,mBAqBM;IACR9F,0DAAA,EAAM;;;;;IAtBsCA,uDAAA,GAAoB;IAApBA,wDAAA,SAAA+F,OAAA,CAAAH,MAAA,CAAAL,UAAA,EAAoB;;;;;IAZlEvF,4DAAA,cAGC;IAEGA,oDAAA,GACF;IAAAA,0DAAA,EAAM;IACNA,wDAAA,IAAAgG,kDAAA,kBA2BM;IACRhG,0DAAA,EAAM;;;;IA9BFA,uDAAA,GACF;IADEA,gEAAA,mCAAAiG,OAAA,CAAA7C,aAAA,CAAAb,MAAA,OACF;IAEqBvC,uDAAA,GAAgB;IAAhBA,wDAAA,YAAAiG,OAAA,CAAA7C,aAAA,CAAgB;;;;;IA8BvCpD,4DAAA,cAGC;IACCA,uDAAA,YAA2C;IAC3CA,4DAAA,QAAG;IAAAA,oDAAA,oCAAwB;IAAAA,0DAAA,EAAI;;;;;IAM/BA,4DAAA,cAGC;IACCA,uDAAA,cAEO;IACPA,4DAAA,YAA8B;IAAAA,oDAAA,qCAA8B;IAAAA,0DAAA,EAAI;;;;;IAiB5DA,uDAAA,cAGO;;;;;IASPA,4DAAA,YAAmD;IACjDA,oDAAA,GACF;IAAAA,0DAAA,EAAI;;;;IADFA,uDAAA,GACF;IADEA,gEAAA,MAAAkG,QAAA,CAAAzM,IAAA,MACF;;;;;;IA3BNuG,4DAAA,cAIC;IADCA,wDAAA,mBAAAmG,wEAAA;MAAA,MAAA9F,WAAA,GAAAL,2DAAA,CAAAoG,IAAA;MAAA,MAAAF,QAAA,GAAA7F,WAAA,CAAAI,SAAA;MAAA,MAAA4F,OAAA,GAAArG,2DAAA;MAAA,OAASA,yDAAA,CAAAqG,OAAA,CAAAR,yBAAA,CAAAK,QAAA,CAA+B;IAAA,EAAC;IAEzClG,4DAAA,aAAyC;IAGrCA,uDAAA,cAIE;IACFA,wDAAA,IAAAsG,wDAAA,kBAGO;IACTtG,0DAAA,EAAM;IAGNA,4DAAA,cAA4B;IAExBA,oDAAA,GACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,YAA0C;IAAAA,oDAAA,GAAgB;IAAAA,0DAAA,EAAI;IAC9DA,wDAAA,KAAAuG,uDAAA,gBAEI;IACNvG,0DAAA,EAAM;IAGNA,4DAAA,eAAwB;IAQpBA,oDAAA,IACF;IAAAA,0DAAA,EAAM;IACNA,4DAAA,eAAgC;IAC9BA,uDAAA,aAA8B;IAChCA,0DAAA,EAAM;;;;;IAlCJA,uDAAA,GAAyD;IAAzDA,wDAAA,QAAAkG,QAAA,CAAA5M,KAAA,yCAAA0G,2DAAA,CAAyD,QAAAkG,QAAA,CAAA9M,QAAA;IAKxD4G,uDAAA,GAAwB;IAAxBA,wDAAA,SAAAwG,OAAA,CAAAzC,YAAA,CAAAmC,QAAA,EAAwB;IAQzBlG,uDAAA,GACF;IADEA,gEAAA,MAAAkG,QAAA,CAAA9M,QAAA,MACF;IAC0C4G,uDAAA,GAAgB;IAAhBA,+DAAA,CAAAkG,QAAA,CAAA7M,KAAA,CAAgB;IACxB2G,uDAAA,GAAe;IAAfA,wDAAA,SAAAkG,QAAA,CAAAzM,IAAA,CAAe;IAS/CuG,uDAAA,GAAyC;IAAzCA,yDAAA,iBAAAwG,OAAA,CAAAzC,YAAA,CAAAmC,QAAA,EAAyC,mBAAAM,OAAA,CAAAzC,YAAA,CAAAmC,QAAA,mBAAAM,OAAA,CAAAzC,YAAA,CAAAmC,QAAA,qBAAAM,OAAA,CAAAzC,YAAA,CAAAmC,QAAA;IAKzClG,uDAAA,GACF;IADEA,gEAAA,MAAAwG,OAAA,CAAAzC,YAAA,CAAAmC,QAAA,mCACF;;;;;IAeFlG,4DAAA,WAA8B;IAAAA,oDAAA,mBAAY;IAAAA,0DAAA,EAAO;;;;;IACjDA,4DAAA,WAA6B;IAAAA,oDAAA,oBAAa;IAAAA,0DAAA,EAAO;;;;;;IAPrDA,4DAAA,cAAkD;IAE9CA,wDAAA,mBAAAyG,2EAAA;MAAAzG,2DAAA,CAAA0G,IAAA;MAAA,MAAAC,OAAA,GAAA3G,2DAAA;MAAA,OAASA,yDAAA,CAAA2G,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAIzB5G,wDAAA,IAAA6G,yDAAA,mBAAiD;IACjD7G,wDAAA,IAAA8G,yDAAA,mBAAiD;IACnD9G,0DAAA,EAAS;;;;IALPA,uDAAA,GAA2B;IAA3BA,wDAAA,aAAA+G,OAAA,CAAAC,cAAA,CAA2B;IAGpBhH,uDAAA,GAAqB;IAArBA,wDAAA,UAAA+G,OAAA,CAAAC,cAAA,CAAqB;IACrBhH,uDAAA,GAAoB;IAApBA,wDAAA,SAAA+G,OAAA,CAAAC,cAAA,CAAoB;;;;;IAK/BhH,4DAAA,cAGC;IACCA,uDAAA,YAA0C;IAC1CA,4DAAA,QAAG;IAAAA,oDAAA,oCAAwB;IAAAA,0DAAA,EAAI;;;;;IA/EnCA,4DAAA,UAA0B;IAExBA,wDAAA,IAAAiH,kDAAA,kBAQM;IAGNjH,wDAAA,IAAAkH,kDAAA,oBA8CM;IAGNlH,wDAAA,IAAAmH,kDAAA,kBASM;IAGNnH,wDAAA,IAAAoH,kDAAA,kBAMM;IACRpH,0DAAA,EAAM;;;;IA9EDA,uDAAA,GAA0C;IAA1CA,wDAAA,SAAAqH,OAAA,CAAAL,cAAA,IAAAK,OAAA,CAAAC,KAAA,CAAA/E,MAAA,OAA0C;IAW1BvC,uDAAA,GAAU;IAAVA,wDAAA,YAAAqH,OAAA,CAAAC,KAAA,CAAU,iBAAAD,OAAA,CAAAE,aAAA;IAgDvBvH,uDAAA,GAAkB;IAAlBA,wDAAA,SAAAqH,OAAA,CAAAG,YAAA,CAAkB;IAarBxH,uDAAA,GAA2C;IAA3CA,wDAAA,SAAAqH,OAAA,CAAAC,KAAA,CAAA/E,MAAA,WAAA8E,OAAA,CAAAL,cAAA,CAA2C;;;;;IA5HlDhH,4DAAA,cAAsD;IAEpDA,wDAAA,IAAAyH,4CAAA,kBAmCM;IAGNzH,wDAAA,IAAA0H,4CAAA,kBAMM;IAGN1H,wDAAA,IAAA2H,4CAAA,kBAiFM;IACR3H,0DAAA,EAAM;;;;IAhIDA,uDAAA,GAA6C;IAA7CA,wDAAA,SAAA4H,MAAA,CAAAvC,WAAA,IAAAuC,MAAA,CAAAxE,aAAA,CAAAb,MAAA,KAA6C;IAsC7CvC,uDAAA,GAA+C;IAA/CA,wDAAA,SAAA4H,MAAA,CAAAvC,WAAA,IAAAuC,MAAA,CAAAxE,aAAA,CAAAb,MAAA,OAA+C;IAQ5CvC,uDAAA,GAAkB;IAAlBA,wDAAA,UAAA4H,MAAA,CAAAvC,WAAA,CAAkB;;;;;IAyFxBrF,4DAAA,cAGC;IACCA,uDAAA,cAEO;IACPA,4DAAA,YAA8B;IAAAA,oDAAA,sCAA+B;IAAAA,0DAAA,EAAI;;;;;IAkD/DA,uDAAA,eAGO;;;;;;IAjDXA,4DAAA,cAQC;IADCA,wDAAA,mBAAA6H,kEAAA;MAAA,MAAAxH,WAAA,GAAAL,2DAAA,CAAA8H,IAAA;MAAA,MAAAC,gBAAA,GAAA1H,WAAA,CAAAI,SAAA;MAAA,MAAAuH,OAAA,GAAAhI,2DAAA;MAAA,OAASA,yDAAA,CAAAgI,OAAA,CAAAC,sBAAA,CAAAF,gBAAA,CAAoC;IAAA,EAAC;IAE9C/H,4DAAA,cAAwC;IAUpCA,uDAAA,YAUK;IACPA,0DAAA,EAAM;IAGNA,4DAAA,cAA4B;IAExBA,oDAAA,GACF;IAAAA,0DAAA,EAAK;IACLA,4DAAA,YAAsC;IACpCA,oDAAA,GACF;IAAAA,0DAAA,EAAI;IACJA,4DAAA,YAAsC;IACpCA,oDAAA,IACF;IAAAA,0DAAA,EAAI;IAINA,wDAAA,KAAAkI,mDAAA,kBAGO;IACTlI,0DAAA,EAAM;;;;;IA5CNA,yDAAA,iBAAA+H,gBAAA,CAAA7N,MAAA,CAA0C;IAOtC8F,uDAAA,GAAyD;IAAzDA,yDAAA,gBAAA+H,gBAAA,CAAAnO,IAAA,mBAAyD,iBAAAmO,gBAAA,CAAAnO,IAAA,wCAAAmO,gBAAA,CAAAnO,IAAA,sCAAAmO,gBAAA,CAAAnO,IAAA,uCAAAmO,gBAAA,CAAAnO,IAAA;IAQvDoG,uDAAA,GAAwD;IAAxDA,yDAAA,eAAA+H,gBAAA,CAAAnO,IAAA,mBAAwD,iBAAAmO,gBAAA,CAAAnO,IAAA,mCAAAmO,gBAAA,CAAAnO,IAAA,iCAAAmO,gBAAA,CAAAnO,IAAA,oDAAAmO,gBAAA,CAAAnO,IAAA;IAcxDoG,uDAAA,GACF;IADEA,gEAAA,MAAAmI,OAAA,CAAAC,oBAAA,CAAAL,gBAAA,OACF;IAEE/H,uDAAA,GACF;IADEA,gEAAA,MAAA+H,gBAAA,CAAApO,OAAA,MACF;IAEEqG,uDAAA,GACF;IADEA,gEAAA,MAAAmI,OAAA,CAAAnE,qBAAA,CAAA+D,gBAAA,CAAAjO,SAAA,OACF;IAKCkG,uDAAA,GAA0B;IAA1BA,wDAAA,UAAA+H,gBAAA,CAAA7N,MAAA,CAA0B;;;;;IAOjC8F,4DAAA,cAGC;IACCA,uDAAA,aAAyC;IACzCA,4DAAA,QAAG;IAAAA,oDAAA,0BAAmB;IAAAA,0DAAA,EAAI;IAC1BA,4DAAA,YAAwB;IACtBA,oDAAA,8EACF;IAAAA,0DAAA,EAAI;;;;;IA3ERA,4DAAA,cAAsE;IAEpEA,wDAAA,IAAAqI,4CAAA,kBAQM;IAGNrI,wDAAA,IAAAsI,4CAAA,oBAmDM;IAGNtI,wDAAA,IAAAuI,4CAAA,kBASM;IACRvI,0DAAA,EAAM;;;;IA1EDA,uDAAA,GAA0D;IAA1DA,wDAAA,SAAAwI,MAAA,CAAAC,sBAAA,IAAAD,MAAA,CAAAlG,aAAA,CAAAC,MAAA,OAA0D;IAY3BvC,uDAAA,GACjB;IADiBA,wDAAA,YAAAwI,MAAA,CAAAlG,aAAA,CACjB,iBAAAkG,MAAA,CAAAE,qBAAA;IAoDd1I,uDAAA,GAA2D;IAA3DA,wDAAA,SAAAwI,MAAA,CAAAlG,aAAA,CAAAC,MAAA,WAAAiG,MAAA,CAAAC,sBAAA,CAA2D;;;AD1dhE,MAAOE,sBAAsB;EAyCjC3P,YACU4P,cAA8B,EAC9BC,WAAwB,EACxBC,YAA0B,EAC1BC,YAA0B,EAC1BC,eAAgC,EAChCC,KAAqB,EACrBC,MAAc,EACdC,GAAsB;IAPtB,KAAAP,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IA9Cb;IACA,KAAAjN,WAAW,GAAgB,IAAI;IAC/B,KAAA4I,aAAa,GAAmB,EAAE;IAClC,KAAAwC,KAAK,GAAW,EAAE;IAClB,KAAAhF,aAAa,GAAmB,EAAE;IAElC;IACA,KAAA8G,SAAS,GAAgD,eAAe;IACxE,KAAAtF,sBAAsB,GAAkB,IAAI;IAC5C,KAAAuF,gBAAgB,GAAG,KAAK;IACxB,KAAAhE,WAAW,GAAG,KAAK;IAEnB;IACA,KAAA5D,YAAY,GAAiB,IAAI;IACjC,KAAAM,eAAe,GAAY,EAAE;IAC7B,KAAAuH,iBAAiB,GAAG,KAAK;IAEzB;IACA,KAAAC,WAAW,GAAG,EAAE;IAChB,KAAAnG,aAAa,GAA4B,EAAE;IAE3C;IACA,KAAAoB,sBAAsB,GAAG,KAAK;IAC9B,KAAAwC,cAAc,GAAG,KAAK;IACtB,KAAAyB,sBAAsB,GAAG,KAAK;IAE9B;IACA,KAAAe,iBAAiB,GAAG,CAAC;IACrB,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAzE,oBAAoB,GAAG,IAAI;IAC3B,KAAAwC,YAAY,GAAG,IAAI;IAEnB;IACQ,KAAAkC,aAAa,GAAmB,EAAE;IAE1C;IACQ,KAAAC,YAAY,GAAG,IAAIvM,iDAAe,CAAS,EAAE,CAAC;EAWnD;EAEHwM,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,eAAe,EAAE;EACxB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACN,aAAa,CAACO,OAAO,CAAEC,GAAG,IAAKA,GAAG,CAACC,WAAW,EAAE,CAAC;EACxD;EAEA;EACA;EACA;EAEQN,mBAAmBA,CAAA;IACzB;IACA,IAAI,CAAC3N,WAAW,GAAG,IAAI,CAAC2M,WAAW,CAACvM,cAAc,EAAE;IAEpD,IAAI,CAAC,IAAI,CAACJ,WAAW,EAAE;MACrB,IAAI,CAACgN,MAAM,CAACkB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAChC;;IAGF;IACA,IAAI,CAAC3I,YAAY,GAAG,IAAI,CAACsH,YAAY,CAACsB,eAAe,EAAE;IACvD,IAAI,CAACtI,eAAe,GAAG,IAAI,CAACgH,YAAY,CAACuB,kBAAkB,EAAE;IAE7D;IACA,IAAI,CAACrB,KAAK,CAACsB,MAAM,CAACC,SAAS,CAAED,MAAM,IAAI;MACrC,MAAMpQ,cAAc,GAAGoQ,MAAM,CAAC,gBAAgB,CAAC;MAC/C,IAAIpQ,cAAc,EAAE;QAClB,IAAI,CAAC2J,sBAAsB,GAAG3J,cAAc;QAC5C,IAAI,CAACsQ,0BAA0B,CAACtQ,cAAc,CAAC;;IAEnD,CAAC,CAAC;EACJ;EAEQ2P,kBAAkBA,CAAA;IACxB;IACA,MAAMY,WAAW,GAAG,IAAI,CAAC9B,cAAc,CACpC+B,mBAAmB,EAAE,CACrBH,SAAS,CAAE/L,OAAO,IAAI;MACrB,IAAIA,OAAO,EAAE;QACX,IAAI,CAACmM,gBAAgB,CAACnM,OAAO,CAAC;;IAElC,CAAC,CAAC;IAEJ;IACA,MAAMoM,gBAAgB,GAAG,IAAI,CAACjC,cAAc,CACzCkC,wBAAwB,EAAE,CAC1BN,SAAS,CAAEO,YAAY,IAAI;MAC1B,IAAIA,YAAY,EAAE;QAChB,IAAI,CAACC,qBAAqB,CAACD,YAAY,CAAC;;IAE5C,CAAC,CAAC;IAEJ;IACA,MAAME,SAAS,GAAG,IAAI,CAACtB,YAAY,CAACa,SAAS,CAAEhO,KAAK,IAAI;MACtD,IAAI,CAAC0O,aAAa,CAAC1O,KAAK,CAAC;IAC3B,CAAC,CAAC;IAEF;IACA,MAAM2O,QAAQ,GAAG,IAAI,CAACpC,YAAY,CAACqC,aAAa,CAACZ,SAAS,CAAEa,KAAK,IAAI;MACnE,IAAI,CAAC5J,YAAY,GAAG4J,KAAK;MACzB,IAAI,CAAClC,GAAG,CAACmC,aAAa,EAAE;IAC1B,CAAC,CAAC;IAEF,IAAI,CAAC5B,aAAa,CAAC7N,IAAI,CAAC6O,WAAW,EAAEG,gBAAgB,EAAEI,SAAS,EAAEE,QAAQ,CAAC;EAC7E;EAEQpB,eAAeA,CAAA;IACrB,IAAI,CAACwB,iBAAiB,EAAE;IACxB,IAAI,CAACC,SAAS,EAAE;IAChB,IAAI,CAACC,iBAAiB,EAAE;IAExB;IACA,IAAI,CAAC,IAAI,CAACvP,WAAW,EAAE;MACrB,IAAI,CAACA,WAAW,GAAG,IAAI,CAAC8M,eAAe,CAAC1M,cAAc,EAAE;;EAE5D;EAEA;EACA;EACA;EAEAiP,iBAAiBA,CAACG,IAAA,GAAe,CAAC;IAChC,IAAI,IAAI,CAAClH,sBAAsB,EAAE;IAEjC,IAAI,CAACA,sBAAsB,GAAG,IAAI;IAElC,IAAI,CAACoE,cAAc,CAAC7N,gBAAgB,EAAE,CAACyP,SAAS,CAAC;MAC/CnM,IAAI,EAAGyG,aAAa,IAAI;QACtB,IAAI4G,IAAI,KAAK,CAAC,EAAE;UACd,IAAI,CAAC5G,aAAa,GAAGA,aAAa;SACnC,MAAM;UACL,IAAI,CAACA,aAAa,CAACjJ,IAAI,CAAC,GAAGiJ,aAAa,CAAC;;QAG3C,IAAI,CAAC0E,iBAAiB,GAAGkC,IAAI;QAC7B,IAAI,CAAC1G,oBAAoB,GAAGF,aAAa,CAACvC,MAAM,KAAK,EAAE;QACvD,IAAI,CAACiC,sBAAsB,GAAG,KAAK;QACnC,IAAI,CAAC2E,GAAG,CAACmC,aAAa,EAAE;MAC1B,CAAC;MACDnM,KAAK,EAAGA,KAAK,IAAI;QACfQ,OAAO,CAACgM,IAAI,CACV,kEAAkE,EAClExM,KAAK,CACN;QACD;QACA,IAAI,CAAC6J,eAAe,CAACjO,gBAAgB,EAAE,CAACyP,SAAS,CAAC;UAChDnM,IAAI,EAAGyG,aAAa,IAAI;YACtB,IAAI4G,IAAI,KAAK,CAAC,EAAE;cACd,IAAI,CAAC5G,aAAa,GAAGA,aAAa;aACnC,MAAM;cACL,IAAI,CAACA,aAAa,CAACjJ,IAAI,CAAC,GAAGiJ,aAAa,CAAC;;YAE3C,IAAI,CAAC0E,iBAAiB,GAAGkC,IAAI;YAC7B,IAAI,CAAC1G,oBAAoB,GAAG,KAAK,CAAC,CAAC;YACnC,IAAI,CAACR,sBAAsB,GAAG,KAAK;YACnC,IAAI,CAAC2E,GAAG,CAACmC,aAAa,EAAE;YACxB,IAAII,IAAI,KAAK,CAAC,EAAE;cACd,IAAI,CAAC5C,YAAY,CAAC/J,QAAQ,CACxB,sCAAsC,CACvC;;UAEL,CAAC;UACDI,KAAK,EAAGyM,SAAS,IAAI;YACnBjM,OAAO,CAACR,KAAK,CACX,gDAAgD,EAChDyM,SAAS,CACV;YACD,IAAI,CAACpH,sBAAsB,GAAG,KAAK;YACnC,IAAI,CAACsE,YAAY,CAACjK,SAAS,CACzB,6CAA6C,CAC9C;UACH;SACD,CAAC;MACJ;KACD,CAAC;EACJ;EAEA2M,SAASA,CAACE,IAAA,GAAe,CAAC;IACxB,IAAI,IAAI,CAAC1E,cAAc,EAAE;IAEzB,IAAI,CAACA,cAAc,GAAG,IAAI;IAE1B,IAAI,CAAC4B,cAAc,CAACiD,WAAW,CAAC,KAAK,EAAE,EAAE,EAAEH,IAAI,EAAE,EAAE,CAAC,CAAClB,SAAS,CAAC;MAC7DnM,IAAI,EAAGiJ,KAAK,IAAI;QACd,IAAIoE,IAAI,KAAK,CAAC,EAAE;UACd,IAAI,CAACpE,KAAK,GAAGA,KAAK;SACnB,MAAM;UACL,IAAI,CAACA,KAAK,CAACzL,IAAI,CAAC,GAAGyL,KAAK,CAAC;;QAG3B,IAAI,CAACmC,SAAS,GAAGiC,IAAI;QACrB,IAAI,CAAClE,YAAY,GAAGF,KAAK,CAAC/E,MAAM,KAAK,EAAE;QACvC,IAAI,CAACyE,cAAc,GAAG,KAAK;QAC3B,IAAI,CAACmC,GAAG,CAACmC,aAAa,EAAE;MAC1B,CAAC;MACDnM,KAAK,EAAGA,KAAK,IAAI;QACfQ,OAAO,CAACgM,IAAI,CACV,kEAAkE,EAClExM,KAAK,CACN;QACD;QACA,IAAI,CAAC6J,eAAe,CAACnO,QAAQ,EAAE,CAAC2P,SAAS,CAAC;UACxCnM,IAAI,EAAGiJ,KAAK,IAAI;YACd,IAAIoE,IAAI,KAAK,CAAC,EAAE;cACd,IAAI,CAACpE,KAAK,GAAGA,KAAK;aACnB,MAAM;cACL,IAAI,CAACA,KAAK,CAACzL,IAAI,CAAC,GAAGyL,KAAK,CAAC;;YAE3B,IAAI,CAACmC,SAAS,GAAGiC,IAAI;YACrB,IAAI,CAAClE,YAAY,GAAG,KAAK,CAAC,CAAC;YAC3B,IAAI,CAACR,cAAc,GAAG,KAAK;YAC3B,IAAI,CAACmC,GAAG,CAACmC,aAAa,EAAE;UAC1B,CAAC;UACDnM,KAAK,EAAGyM,SAAS,IAAI;YACnBjM,OAAO,CAACR,KAAK,CACX,gDAAgD,EAChDyM,SAAS,CACV;YACD,IAAI,CAAC5E,cAAc,GAAG,KAAK;YAC3B,IAAI,CAAC8B,YAAY,CAACjK,SAAS,CACzB,4CAA4C,CAC7C;UACH;SACD,CAAC;MACJ;KACD,CAAC;EACJ;EAEA4M,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAAChD,sBAAsB,EAAE;IAEjC,IAAI,CAACA,sBAAsB,GAAG,IAAI;IAElC,IAAI,CAACG,cAAc,CAACpN,gBAAgB,EAAE,CAACgP,SAAS,CAAC;MAC/CnM,IAAI,EAAGiE,aAAa,IAAI;QACtB,IAAI,CAACA,aAAa,GAAGA,aAAa;QAClC,IAAI,CAACmG,sBAAsB,GAAG,KAAK;QACnC,IAAI,CAACU,GAAG,CAACmC,aAAa,EAAE;MAC1B,CAAC;MACDnM,KAAK,EAAGA,KAAK,IAAI;QACfQ,OAAO,CAACgM,IAAI,CACV,kEAAkE,EAClExM,KAAK,CACN;QACD;QACA,IAAI,CAAC6J,eAAe,CAACxN,gBAAgB,EAAE,CAACgP,SAAS,CAAC;UAChDnM,IAAI,EAAGiE,aAAa,IAAI;YACtB,IAAI,CAACA,aAAa,GAAGA,aAAa;YAClC,IAAI,CAACmG,sBAAsB,GAAG,KAAK;YACnC,IAAI,CAACU,GAAG,CAACmC,aAAa,EAAE;UAC1B,CAAC;UACDnM,KAAK,EAAGyM,SAAS,IAAI;YACnBjM,OAAO,CAACR,KAAK,CACX,gDAAgD,EAChDyM,SAAS,CACV;YACD,IAAI,CAACnD,sBAAsB,GAAG,KAAK;YACnC,IAAI,CAACK,YAAY,CAACjK,SAAS,CACzB,6CAA6C,CAC9C;UACH;SACD,CAAC;MACJ;KACD,CAAC;EACJ;EAEA;EACA;EACA;EAEQ+L,gBAAgBA,CAACnM,OAAgB;IACvC;IACA,MAAMqN,iBAAiB,GAAG,IAAI,CAAChH,aAAa,CAACiH,SAAS,CACnDjP,IAAI,IAAKA,IAAI,CAAC5D,EAAE,KAAKuF,OAAO,CAACtE,cAAc,CAC7C;IAED,IAAI2R,iBAAiB,KAAK,CAAC,CAAC,EAAE;MAC5B;MACA,IAAI,CAAChH,aAAa,CAACgH,iBAAiB,CAAC,CAACxR,WAAW,GAAGmE,OAAO;MAE3D;MACA,MAAMxD,YAAY,GAAG,IAAI,CAAC6J,aAAa,CAACkH,MAAM,CAACF,iBAAiB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACvE,IAAI,CAAChH,aAAa,CAACzI,OAAO,CAACpB,YAAY,CAAC;MAExC,IAAI,CAACkO,GAAG,CAACmC,aAAa,EAAE;;EAE5B;EAEQN,qBAAqBA,CAACD,YAA0B;IACtD;IACA,IAAI,CAACzI,aAAa,CAACjG,OAAO,CAAC0O,YAAY,CAAC;IACxC,IAAI,CAAC5B,GAAG,CAACmC,aAAa,EAAE;IAExB;IACA,IAAI,IAAI,CAAClC,SAAS,KAAK,eAAe,EAAE;MACtC,IAAI,CAACN,YAAY,CAAC/J,QAAQ,CAAC,6BAA6B,CAAC;;EAE7D;EAEQ0L,0BAA0BA,CAACtQ,cAAsB;IACvD;IACA,IAAI,CAAC2J,sBAAsB,GAAG3J,cAAc;IAC5C,IAAI,CAACgP,GAAG,CAACmC,aAAa,EAAE;EAC1B;EAEA;EACA;EACA;EAEAW,SAASA,CAACC,GAAgD;IACxD,IAAI,CAAC9C,SAAS,GAAG8C,GAAG;IACpB,IAAI,CAAC3C,WAAW,GAAG,EAAE;IACrB,IAAI,CAACnG,aAAa,GAAG,EAAE;IACvB,IAAI,CAACiC,WAAW,GAAG,KAAK;IAExB;IACA,QAAQ6G,GAAG;MACT,KAAK,eAAe;QAClB,IAAI,IAAI,CAACpH,aAAa,CAACvC,MAAM,KAAK,CAAC,EAAE;UACnC,IAAI,CAACgJ,iBAAiB,EAAE;;QAE1B;MACF,KAAK,OAAO;QACV,IAAI,IAAI,CAACjE,KAAK,CAAC/E,MAAM,KAAK,CAAC,EAAE;UAC3B,IAAI,CAACiJ,SAAS,EAAE;;QAElB;MACF,KAAK,eAAe;QAClB,IAAI,IAAI,CAAClJ,aAAa,CAACC,MAAM,KAAK,CAAC,EAAE;UACnC,IAAI,CAACkJ,iBAAiB,EAAE;;QAE1B;;EAEN;EAEA7I,kBAAkBA,CAAC3H,YAA0B;IAC3C,IAAI,CAACA,YAAY,CAAC/B,EAAE,EAAE;IAEtB,IAAI,CAAC4K,sBAAsB,GAAG7I,YAAY,CAAC/B,EAAE;IAC7C,IAAI,CAACgQ,MAAM,CAACkB,QAAQ,CAAC,CAAC,WAAW,EAAEnP,YAAY,CAAC/B,EAAE,CAAC,CAAC;IAEpD;IACA,IAAI,CAACmQ,gBAAgB,GAAG,KAAK;EAC/B;EAEAxD,yBAAyBA,CAACnJ,IAAU;IAClC,IAAI,CAACA,IAAI,CAACxD,EAAE,IAAI,CAACwD,IAAI,CAACvD,GAAG,EAAE;IAE3B,MAAM4C,MAAM,GAAGW,IAAI,CAACxD,EAAE,IAAIwD,IAAI,CAACvD,GAAI;IAEnC;IACA,IAAI,CAACyP,cAAc,CAACuD,uBAAuB,CAACpQ,MAAM,CAAC,CAACyO,SAAS,CAAC;MAC5DnM,IAAI,EAAGpD,YAAY,IAAI;QACrB,IAAI,CAAC2H,kBAAkB,CAAC3H,YAAY,CAAC;MACvC,CAAC;MACDkE,KAAK,EAAGA,KAAK,IAAI;QACfQ,OAAO,CAACgM,IAAI,CACV,kEAAkE,EAClExM,KAAK,CACN;QACD;QACA,MAAMnD,aAAa,GAAG,IAAI,CAACE,WAAW,EAAEhD,EAAE,IAAI,GAAG;QACjD,IAAI,CAAC8P,eAAe,CACjBlN,kBAAkB,CAACC,MAAM,EAAEC,aAAa,CAAC,CACzCwO,SAAS,CAAC;UACTnM,IAAI,EAAGpD,YAAY,IAAI;YACrB,IAAI,CAAC6J,aAAa,CAACzI,OAAO,CAACpB,YAAY,CAAC;YACxC,IAAI,CAAC2H,kBAAkB,CAAC3H,YAAY,CAAC;YACrC,IAAI,CAAC6N,YAAY,CAAClK,WAAW,CAAC,gCAAgC,CAAC;UACjE,CAAC;UACDO,KAAK,EAAGyM,SAAS,IAAI;YACnBjM,OAAO,CAACR,KAAK,CACX,gDAAgD,EAChDyM,SAAS,CACV;YACD,IAAI,CAAC9C,YAAY,CAACjK,SAAS,CACzB,+CAA+C,CAChD;UACH;SACD,CAAC;MACN;KACD,CAAC;EACJ;EAEAuN,gBAAgBA,CAAA;IACd,IAAI,CAAC/C,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;EAChD;EAEA;EACA;EACA;EAEAgD,aAAaA,CAACC,KAAU;IACtB,MAAM9P,KAAK,GAAG8P,KAAK,CAACC,MAAM,CAACnO,KAAK,CAACoO,IAAI,EAAE;IACvC,IAAI,CAACjD,WAAW,GAAG/M,KAAK;IACxB,IAAI,CAACmN,YAAY,CAACtL,IAAI,CAAC7B,KAAK,CAAC;EAC/B;EAEQ0O,aAAaA,CAAC1O,KAAa;IACjC,IAAI,CAACA,KAAK,EAAE;MACV,IAAI,CAAC4G,aAAa,GAAG,EAAE;MACvB,IAAI,CAACiC,WAAW,GAAG,KAAK;MACxB;;IAGF,IAAI,CAACA,WAAW,GAAG,IAAI;IAEvB,IAAI,IAAI,CAAC+D,SAAS,KAAK,eAAe,EAAE;MACtC,IAAI,CAAChG,aAAa,GAAG,IAAI,CAAC0B,aAAa,CAACxJ,MAAM,CAAEwB,IAAI,IAClDA,IAAI,CAACtC,OAAO,GACRsC,IAAI,CAACpC,SAAS,EAAEiC,WAAW,EAAE,CAACC,QAAQ,CAACJ,KAAK,CAACG,WAAW,EAAE,CAAC,GAC3DG,IAAI,CAACzC,YAAY,EAAE0C,IAAI,CAAEC,CAAC,IACxBA,CAAC,CAAC5D,QAAQ,EAAEuD,WAAW,EAAE,CAACC,QAAQ,CAACJ,KAAK,CAACG,WAAW,EAAE,CAAC,CACxD,CACN;KACF,MAAM,IAAI,IAAI,CAACyM,SAAS,KAAK,OAAO,EAAE;MACrC,IAAI,CAAChG,aAAa,GAAG,IAAI,CAACkE,KAAK,CAAChM,MAAM,CACnCoB,IAAI,IACHA,IAAI,CAACtD,QAAQ,EAAEuD,WAAW,EAAE,CAACC,QAAQ,CAACJ,KAAK,CAACG,WAAW,EAAE,CAAC,IAC1DD,IAAI,CAACrD,KAAK,EAAEsD,WAAW,EAAE,CAACC,QAAQ,CAACJ,KAAK,CAACG,WAAW,EAAE,CAAC,CAC1D;;IAGH,IAAI,CAACwM,GAAG,CAACmC,aAAa,EAAE;EAC1B;EAEAnJ,WAAWA,CAAA;IACT,IAAI,CAACoH,WAAW,GAAG,EAAE;IACrB,IAAI,CAACnG,aAAa,GAAG,EAAE;IACvB,IAAI,CAACiC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACsE,YAAY,CAACtL,IAAI,CAAC,EAAE,CAAC;EAC5B;EAEA;EACA;EACA;EAEA+F,qBAAqBA,CAAA;IACnB,IAAI,IAAI,CAACY,oBAAoB,IAAI,CAAC,IAAI,CAACR,sBAAsB,EAAE;MAC7D,IAAI,CAAC+G,iBAAiB,CAAC,IAAI,CAAC/B,iBAAiB,GAAG,CAAC,CAAC;;EAEtD;EAEA5C,aAAaA,CAAA;IACX,IAAI,IAAI,CAACY,YAAY,IAAI,CAAC,IAAI,CAACR,cAAc,EAAE;MAC7C,IAAI,CAACwE,SAAS,CAAC,IAAI,CAAC/B,SAAS,GAAG,CAAC,CAAC;;EAEtC;EAEA;EACA;EACA;EAEAzG,mBAAmBA,CAAC/H,YAA0B;IAC5C,IAAIA,YAAY,CAACT,OAAO,EAAE;MACxB,OAAOS,YAAY,CAACP,SAAS,IAAI,iBAAiB;;IAGpD,IAAI,CAAC,IAAI,CAACwB,WAAW,EAAE,OAAO,cAAc;IAE5C,MAAMF,aAAa,GAAG,IAAI,CAACE,WAAW,CAAChD,EAAE,IAAI,IAAI,CAACgD,WAAW,CAAC/C,GAAG;IACjE,MAAMsT,gBAAgB,GAAGxR,YAAY,CAACZ,YAAY,EAAEa,IAAI,CACrD8B,CAAC,IAAK,CAACA,CAAC,CAAC9D,EAAE,IAAI8D,CAAC,CAAC7D,GAAG,MAAM6C,aAAa,CACzC;IAED,OAAOyQ,gBAAgB,EAAErT,QAAQ,IAAI,qBAAqB;EAC5D;EAEA0J,qBAAqBA,CAAC7H,YAA0B;IAC9C,IAAIA,YAAY,CAACT,OAAO,EAAE;MACxB,OAAOS,YAAY,CAACN,UAAU,IAAI,kCAAkC;;IAGtE,IAAI,CAAC,IAAI,CAACuB,WAAW,EAAE,OAAO,mCAAmC;IAEjE,MAAMF,aAAa,GAAG,IAAI,CAACE,WAAW,CAAChD,EAAE,IAAI,IAAI,CAACgD,WAAW,CAAC/C,GAAG;IACjE,MAAMsT,gBAAgB,GAAGxR,YAAY,CAACZ,YAAY,EAAEa,IAAI,CACrD8B,CAAC,IAAK,CAACA,CAAC,CAAC9D,EAAE,IAAI8D,CAAC,CAAC7D,GAAG,MAAM6C,aAAa,CACzC;IAED,OAAOyQ,gBAAgB,EAAEnT,KAAK,IAAI,mCAAmC;EACvE;EAEA2J,qBAAqBA,CAAChI,YAA0B;IAC9C,IAAI,CAACA,YAAY,CAACX,WAAW,EAAE,OAAO,eAAe;IAErD,MAAMmE,OAAO,GAAGxD,YAAY,CAACX,WAAW;IAExC,IAAImE,OAAO,CAAC7E,IAAI,KAAK,MAAM,EAAE;MAC3B,OAAO6E,OAAO,CAAC9E,OAAO,IAAI,EAAE;KAC7B,MAAM,IAAI8E,OAAO,CAAC7E,IAAI,KAAK,OAAO,EAAE;MACnC,OAAO,UAAU;KAClB,MAAM,IAAI6E,OAAO,CAAC7E,IAAI,KAAK,MAAM,EAAE;MAClC,OAAO,YAAY;KACpB,MAAM,IAAI6E,OAAO,CAAC7E,IAAI,KAAK,eAAe,EAAE;MAC3C,OAAO,kBAAkB;KAC1B,MAAM,IAAI6E,OAAO,CAAC7E,IAAI,KAAK,OAAO,EAAE;MACnC,OAAO,UAAU;;IAGnB,OAAO,SAAS;EAClB;EAEAoK,qBAAqBA,CAAClK,SAAoC;IACxD,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IAEzB,MAAM4S,IAAI,GAAG,IAAI3S,IAAI,CAACD,SAAS,CAAC;IAChC,MAAME,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAM4S,WAAW,GAAG,CAAC3S,GAAG,CAAC4S,OAAO,EAAE,GAAGF,IAAI,CAACE,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAEvE,IAAID,WAAW,GAAG,CAAC,EAAE;MACnB,OAAO,aAAa;KACrB,MAAM,IAAIA,WAAW,GAAG,EAAE,EAAE;MAC3B,OAAOD,IAAI,CAACG,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;OACT,CAAC;KACH,MAAM,IAAIJ,WAAW,GAAG,GAAG,EAAE;MAC5B;MACA,OAAOD,IAAI,CAACM,kBAAkB,CAAC,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAO,CAAE,CAAC;KAC9D,MAAM;MACL,OAAOP,IAAI,CAACM,kBAAkB,CAAC,OAAO,EAAE;QACtCE,GAAG,EAAE,SAAS;QACdC,KAAK,EAAE;OACR,CAAC;;EAEN;EAEA7J,cAAcA,CAACrI,YAA0B;IACvC,OAAOA,YAAY,CAACV,WAAW,IAAI,CAAC;EACtC;EAEAwJ,YAAYA,CAACrH,IAAU;IACrB,OAAOA,IAAI,CAACnD,QAAQ,IAAI,KAAK;EAC/B;EAEAwL,qBAAqBA,CAACqI,KAAa,EAAEnS,YAA0B;IAC7D,OAAOA,YAAY,CAAC/B,EAAE,IAAI+B,YAAY,CAAC9B,GAAG,IAAIiU,KAAK,CAACvP,QAAQ,EAAE;EAChE;EAEA0J,aAAaA,CAAC6F,KAAa,EAAE1Q,IAAU;IACrC,OAAOA,IAAI,CAACxD,EAAE,IAAIwD,IAAI,CAACvD,GAAG,IAAIiU,KAAK,CAACvP,QAAQ,EAAE;EAChD;EAEA6K,qBAAqBA,CAAC0E,KAAa,EAAErC,YAA0B;IAC7D,OAAOA,YAAY,CAAC7R,EAAE,IAAIkU,KAAK,CAACvP,QAAQ,EAAE;EAC5C;EAEAoK,sBAAsBA,CAAC8C,YAA0B;IAC/C,IAAI,CAACA,YAAY,CAAC7R,EAAE,IAAI6R,YAAY,CAAC7Q,MAAM,EAAE;IAE7C,IAAI,CAAC0O,cAAc,CAACX,sBAAsB,CAAC8C,YAAY,CAAC7R,EAAE,CAAC,CAACsR,SAAS,CAAC;MACpEnM,IAAI,EAAEA,CAAA,KAAK;QACT0M,YAAY,CAAC7Q,MAAM,GAAG,IAAI;QAC1B,IAAI,CAACiP,GAAG,CAACmC,aAAa,EAAE;MAC1B,CAAC;MACDnM,KAAK,EAAGA,KAAK,IAAI;QACfQ,OAAO,CAACR,KAAK,CACX,uDAAuD,EACvDA,KAAK,CACN;QACD,IAAI,CAAC2J,YAAY,CAACjK,SAAS,CACzB,4CAA4C,CAC7C;MACH;KACD,CAAC;EACJ;EAEA;EACA+G,MAAMA,CAACyH,IAAyB;IAC9B,OAAO,UAAU,IAAIA,IAAI,IAAI,OAAO,IAAIA,IAAI;EAC9C;EAEAC,cAAcA,CAACD,IAAyB;IACtC,OAAO,cAAc,IAAIA,IAAI,IAAI,SAAS,IAAIA,IAAI;EACpD;EAEAjF,oBAAoBA,CAAC2C,YAA0B;IAC7C,QAAQA,YAAY,CAACnR,IAAI;MACvB,KAAK,aAAa;QAChB,OAAO,iBAAiB;MAC1B,KAAK,gBAAgB;QACnB,OAAO,eAAe;MACxB,KAAK,cAAc;QACjB,OAAO,sBAAsB;MAC/B,KAAK,kBAAkB;QACrB,OAAO,uBAAuB;MAChC,KAAK,cAAc;QACjB,OAAO,gBAAgB;MACzB;QACE,OAAO,cAAc;;EAE3B;EAEA;EACA;EACA;EAEAiH,WAAWA,CAAC0M,SAAiB;IAC3B,IAAI,CAACxE,YAAY,CAACyE,QAAQ,CAACD,SAAS,CAAC;IACrC,IAAI,CAACjE,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACR,YAAY,CAAClK,WAAW,CAC3B,UAAU,IAAI,CAACmK,YAAY,CAACsB,eAAe,EAAE,CAAC1I,WAAW,YAAY,CACtE;EACH;;;uBA5mBWgH,sBAAsB,EAAA3I,+DAAA,CAAA0N,qEAAA,GAAA1N,+DAAA,CAAA4N,+DAAA,GAAA5N,+DAAA,CAAA8N,iEAAA,GAAA9N,+DAAA,CAAA+N,iEAAA,GAAA/N,+DAAA,CAAAiO,wEAAA,GAAAjO,+DAAA,CAAAkO,2DAAA,GAAAlO,+DAAA,CAAAkO,mDAAA,GAAAlO,+DAAA,CAAAA,4DAAA;IAAA;EAAA;;;YAAtB2I,sBAAsB;MAAA2F,SAAA;MAAAC,SAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;UCvBnCzO,4DAAA,aAAiE;UAavDA,uDAAA,aAIE;UACFA,4DAAA,UAAK;UAEDA,oDAAA,GACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,WAAkC;UAAAA,oDAAA,gBAAQ;UAAAA,0DAAA,EAAI;UAKlDA,4DAAA,cAAyC;UAKnCA,wDAAA,mBAAA2O,yDAAA;YAAA,OAAAD,GAAA,CAAApF,iBAAA,IAAAoF,GAAA,CAAApF,iBAAA;UAAA,EAAgD;UAGhDtJ,uDAAA,aAA4C;UAC9CA,0DAAA,EAAS;UAGTA,wDAAA,KAAA4O,sCAAA,kBAwBM;UACR5O,0DAAA,EAAM;UAGNA,4DAAA,kBAGC;UADCA,wDAAA,mBAAA6O,yDAAA;YAAA,OAASH,GAAA,CAAAtC,gBAAA,EAAkB;UAAA,EAAC;UAE5BpM,uDAAA,aAAuC;UACzCA,0DAAA,EAAS;UAKbA,4DAAA,cAAsB;UAIlBA,wDAAA,2BAAA8O,gEAAAC,MAAA;YAAA,OAAAL,GAAA,CAAAnF,WAAA,GAAAwF,MAAA;UAAA,EAAyB,mBAAAC,wDAAAD,MAAA;YAAA,OAChBL,GAAA,CAAArC,aAAA,CAAA0C,MAAA,CAAqB;UAAA,EADL;UAH3B/O,0DAAA,EAOE;UACFA,uDAAA,aAAiE;UACjEA,wDAAA,KAAAiP,yCAAA,qBAMS;UACXjP,0DAAA,EAAM;UAIRA,4DAAA,eAAgD;UAQ5CA,wDAAA,mBAAAkP,yDAAA;YAAA,OAASR,GAAA,CAAAzC,SAAA,CAAU,eAAe,CAAC;UAAA,EAAC;UAEpCjM,uDAAA,aAAoC;UACpCA,4DAAA,eAAqB;UAAAA,oDAAA,mBAAW;UAAAA,0DAAA,EAAM;UAGxCA,4DAAA,kBAQC;UADCA,wDAAA,mBAAAmP,yDAAA;YAAA,OAAST,GAAA,CAAAzC,SAAA,CAAU,OAAO,CAAC;UAAA,EAAC;UAE5BjM,uDAAA,aAAiC;UACjCA,4DAAA,eAAqB;UAAAA,oDAAA,gBAAQ;UAAAA,0DAAA,EAAM;UAGrCA,4DAAA,kBAQC;UADCA,wDAAA,mBAAAoP,yDAAA;YAAA,OAASV,GAAA,CAAAzC,SAAA,CAAU,eAAe,CAAC;UAAA,EAAC;UAEpCjM,uDAAA,aAAgC;UAChCA,4DAAA,eAAqB;UAAAA,oDAAA,qBAAa;UAAAA,0DAAA,EAAM;UACxCA,wDAAA,KAAAqP,uCAAA,mBAKO;UACTrP,0DAAA,EAAS;UAIXA,4DAAA,eAAoD;UAIlDA,wDAAA,KAAAsP,sCAAA,kBA0IM;UAKNtP,wDAAA,KAAAuP,sCAAA,kBAmIM;UAKNvP,wDAAA,KAAAwP,sCAAA,kBA6EM;UACRxP,0DAAA,EAAM;UAMRA,4DAAA,eAA+C;UAKzCA,wDAAA,mBAAAyP,yDAAA;YAAA,OAASf,GAAA,CAAAtC,gBAAA,EAAkB;UAAA,EAAC;UAE5BpM,uDAAA,aAAsC;UACxCA,0DAAA,EAAS;UAIXA,4DAAA,eAAoB;UAClBA,uDAAA,qBAA+B;UACjCA,0DAAA,EAAM;;;UAzgBNA,uDAAA,GAAkC;UAAlCA,yDAAA,YAAA0O,GAAA,CAAArF,gBAAA,CAAkC;UAQ1BrJ,uDAAA,GAAiE;UAAjEA,wDAAA,SAAA0O,GAAA,CAAAxS,WAAA,kBAAAwS,GAAA,CAAAxS,WAAA,CAAA5C,KAAA,0CAAA0G,2DAAA,CAAiE,QAAA0O,GAAA,CAAAxS,WAAA,kBAAAwS,GAAA,CAAAxS,WAAA,CAAA9C,QAAA;UAM/D4G,uDAAA,GACF;UADEA,gEAAA,MAAA0O,GAAA,CAAAxS,WAAA,kBAAAwS,GAAA,CAAAxS,WAAA,CAAA9C,QAAA,MACF;UAmBG4G,uDAAA,GAAuB;UAAvBA,wDAAA,SAAA0O,GAAA,CAAApF,iBAAA,CAAuB;UAyC5BtJ,uDAAA,GAAyB;UAAzBA,wDAAA,YAAA0O,GAAA,CAAAnF,WAAA,CAAyB;UAOxBvJ,uDAAA,GAAiB;UAAjBA,wDAAA,SAAA0O,GAAA,CAAAnF,WAAA,CAAiB;UAapBvJ,uDAAA,GAA8C;UAA9CA,yDAAA,WAAA0O,GAAA,CAAAtF,SAAA,qBAA8C,kBAAAsF,GAAA,CAAAtF,SAAA,oCAAAsF,GAAA,CAAAtF,SAAA,yCAAAsF,GAAA,CAAAtF,SAAA,uCAAAsF,GAAA,CAAAtF,SAAA;UAa9CpJ,uDAAA,GAAsC;UAAtCA,yDAAA,WAAA0O,GAAA,CAAAtF,SAAA,aAAsC,kBAAAsF,GAAA,CAAAtF,SAAA,4BAAAsF,GAAA,CAAAtF,SAAA,iCAAAsF,GAAA,CAAAtF,SAAA,+BAAAsF,GAAA,CAAAtF,SAAA;UAatCpJ,uDAAA,GAA8C;UAA9CA,yDAAA,WAAA0O,GAAA,CAAAtF,SAAA,qBAA8C,kBAAAsF,GAAA,CAAAtF,SAAA,oCAAAsF,GAAA,CAAAtF,SAAA,yCAAAsF,GAAA,CAAAtF,SAAA,uCAAAsF,GAAA,CAAAtF,SAAA;UAU3CpJ,uDAAA,GAA8B;UAA9BA,wDAAA,SAAA0O,GAAA,CAAApM,aAAA,CAAAC,MAAA,KAA8B;UAa7BvC,uDAAA,GAAmC;UAAnCA,wDAAA,SAAA0O,GAAA,CAAAtF,SAAA,qBAAmC;UA+InCpJ,uDAAA,GAA2B;UAA3BA,wDAAA,SAAA0O,GAAA,CAAAtF,SAAA,aAA2B;UAwI3BpJ,uDAAA,GAAmC;UAAnCA,wDAAA,SAAA0O,GAAA,CAAAtF,SAAA,qBAAmC;;;;;;;;;;;;;;;;;;;;;;;;ACjbK;AACZ;AACI;AACrC,SAASvQ,KAAKA,CAACgX,GAAG,EAAEC,SAAS,GAAGJ,4DAAc,EAAE;EACnD,MAAMxR,QAAQ,GAAG0R,wDAAK,CAACC,GAAG,EAAEC,SAAS,CAAC;EACtC,OAAOH,qDAAS,CAAC,MAAMzR,QAAQ,CAAC;AACpC;;;;;;;;;;;;;;;;;;;;ACN8C;AAChB;AACoB;AAClB;AACM;AACc;AAC7C,SAASyR,SAASA,CAACU,qBAAqB,EAAEC,iBAAiB,EAAE;EAChE,IAAIA,iBAAiB,EAAE;IACnB,OAAQC,MAAM,IAAKR,0DAAM,CAACO,iBAAiB,CAACxV,IAAI,CAACkV,2CAAI,CAAC,CAAC,CAAC,EAAEC,+DAAc,CAAC,CAAC,CAAC,EAAEM,MAAM,CAACzV,IAAI,CAAC6U,SAAS,CAACU,qBAAqB,CAAC,CAAC,CAAC;EAC/H;EACA,OAAOF,mDAAQ,CAAC,CAAC/R,KAAK,EAAEgP,KAAK,KAAKgD,gEAAS,CAACC,qBAAqB,CAACjS,KAAK,EAAEgP,KAAK,CAAC,CAAC,CAACtS,IAAI,CAACkV,2CAAI,CAAC,CAAC,CAAC,EAAEE,6CAAK,CAAC9R,KAAK,CAAC,CAAC,CAAC;AACjH;;;;;;;;;;;;;;;;;ACXuC;AACyB;AAC5B;AAC7B,SAAS6R,cAAcA,CAAA,EAAG;EAC7B,OAAOO,mDAAO,CAAC,CAACD,MAAM,EAAEI,UAAU,KAAK;IACnCJ,MAAM,CAAC/F,SAAS,CAACiG,6EAAwB,CAACE,UAAU,EAAED,4CAAI,CAAC,CAAC;EAChE,CAAC,CAAC;AACN", "sources": ["./src/app/services/mock-data.service.ts", "./src/app/services/toast.service.ts", "./src/app/views/front/messages/message-layout/message-layout.component.ts", "./src/app/views/front/messages/message-layout/message-layout.component.html", "./node_modules/rxjs/dist/esm/internal/operators/delay.js", "./node_modules/rxjs/dist/esm/internal/operators/delayWhen.js", "./node_modules/rxjs/dist/esm/internal/operators/ignoreElements.js"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Observable, of, BehaviorSubject } from 'rxjs';\nimport { delay } from 'rxjs/operators';\nimport {\n  User,\n  Conversation,\n  Message,\n  Notification,\n  MessageType,\n  NotificationType,\n} from '../models/message.model';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class MockDataService {\n  // Utilisateurs de test\n  private mockUsers: User[] = [\n    {\n      id: '1',\n      _id: '1',\n      username: '<PERSON>',\n      email: '<EMAIL>',\n      image: '/assets/images/avatars/alice.jpg',\n      isOnline: true,\n      isActive: true,\n      role: 'developer',\n    },\n    {\n      id: '2',\n      _id: '2',\n      username: '<PERSON>',\n      email: '<EMAIL>',\n      image: '/assets/images/avatars/bob.jpg',\n      isOnline: false,\n      isActive: true,\n      role: 'designer',\n    },\n    {\n      id: '3',\n      _id: '3',\n      username: '<PERSON>',\n      email: '<EMAIL>',\n      image: '/assets/images/avatars/claire.jpg',\n      isOnline: true,\n      isActive: true,\n      role: 'manager',\n    },\n    {\n      id: '4',\n      _id: '4',\n      username: '<PERSON>',\n      email: '<EMAIL>',\n      image: '/assets/images/avatars/david.jpg',\n      isOnline: true,\n      isActive: true,\n      role: 'developer',\n    },\n    {\n      id: '5',\n      _id: '5',\n      username: 'Emma Wilson',\n      email: '<EMAIL>',\n      image: '/assets/images/avatars/emma.jpg',\n      isOnline: false,\n      isActive: true,\n      role: 'tester',\n    },\n  ];\n\n  // Messages de test\n  private mockMessages: Message[] = [\n    {\n      id: '1',\n      content: 'Salut ! Comment ça va ?',\n      type: MessageType.TEXT,\n      timestamp: new Date(Date.now() - 3600000), // 1h ago\n      sender: this.mockUsers[1],\n      isRead: true,\n      conversationId: 'conv1',\n    },\n    {\n      id: '2',\n      content: 'Ça va bien merci ! Et toi ?',\n      type: MessageType.TEXT,\n      timestamp: new Date(Date.now() - 3500000), // 58min ago\n      sender: this.mockUsers[0], // Current user\n      isRead: true,\n      conversationId: 'conv1',\n    },\n    {\n      id: '3',\n      content: 'Super ! Tu as vu le nouveau design ?',\n      type: MessageType.TEXT,\n      timestamp: new Date(Date.now() - 1800000), // 30min ago\n      sender: this.mockUsers[1],\n      isRead: false,\n      conversationId: 'conv1',\n    },\n  ];\n\n  // Conversations de test\n  private mockConversations: Conversation[] = [\n    {\n      id: 'conv1',\n      participants: [this.mockUsers[0], this.mockUsers[1]],\n      lastMessage: this.mockMessages[2],\n      unreadCount: 1,\n      isGroup: false,\n      createdAt: new Date(Date.now() - 86400000), // 1 day ago\n    },\n    {\n      id: 'conv2',\n      participants: [this.mockUsers[0], this.mockUsers[2]],\n      lastMessage: {\n        id: '4',\n        content: 'Réunion à 14h ?',\n        type: MessageType.TEXT,\n        timestamp: new Date(Date.now() - 7200000), // 2h ago\n        sender: this.mockUsers[2],\n        isRead: true,\n        conversationId: 'conv2',\n      },\n      unreadCount: 0,\n      isGroup: false,\n      createdAt: new Date(Date.now() - 172800000), // 2 days ago\n    },\n    {\n      id: 'conv3',\n      participants: [\n        this.mockUsers[0],\n        this.mockUsers[1],\n        this.mockUsers[2],\n        this.mockUsers[3],\n      ],\n      lastMessage: {\n        id: '5',\n        content: 'Nouveau projet lancé ! 🚀',\n        type: MessageType.TEXT,\n        timestamp: new Date(Date.now() - 10800000), // 3h ago\n        sender: this.mockUsers[3],\n        isRead: false,\n        conversationId: 'conv3',\n      },\n      unreadCount: 3,\n      isGroup: true,\n      groupName: 'Équipe DevBridge',\n      groupPhoto: '/assets/images/groups/team.jpg',\n      createdAt: new Date(Date.now() - 259200000), // 3 days ago\n    },\n  ];\n\n  // Notifications de test\n  private mockNotifications: Notification[] = [\n    {\n      id: 'notif1',\n      type: 'NEW_MESSAGE' as any,\n      content: 'Nouveau message de Bob Dupont',\n      timestamp: new Date(Date.now() - 1800000), // 30min ago\n      isRead: false,\n    },\n    {\n      id: 'notif2',\n      type: 'MESSAGE_REACTION' as any,\n      content: 'Alice a réagi à votre message avec ❤️',\n      timestamp: new Date(Date.now() - 3600000), // 1h ago\n      isRead: true,\n    },\n    {\n      id: 'notif3',\n      type: 'GROUP_INVITE' as any,\n      content: 'Vous avez été ajouté au groupe \"Équipe DevBridge\"',\n      timestamp: new Date(Date.now() - 7200000), // 2h ago\n      isRead: false,\n    },\n  ];\n\n  constructor() {}\n\n  // ============================================================================\n  // MÉTHODES PUBLIQUES POUR LES TESTS\n  // ============================================================================\n\n  /**\n   * Récupère tous les utilisateurs\n   */\n  getUsers(): Observable<User[]> {\n    return of(this.mockUsers).pipe(delay(500)); // Simule la latence réseau\n  }\n\n  /**\n   * Récupère toutes les conversations\n   */\n  getConversations(): Observable<Conversation[]> {\n    return of(this.mockConversations).pipe(delay(300));\n  }\n\n  /**\n   * Récupère une conversation par ID\n   */\n  getConversation(id: string): Observable<Conversation | null> {\n    const conversation = this.mockConversations.find((c) => c.id === id);\n    return of(conversation || null).pipe(delay(200));\n  }\n\n  /**\n   * Récupère les messages d'une conversation\n   */\n  getMessages(conversationId: string): Observable<Message[]> {\n    const messages = this.mockMessages.filter(\n      (m) => m.conversationId === conversationId\n    );\n    return of(messages).pipe(delay(300));\n  }\n\n  /**\n   * Récupère toutes les notifications\n   */\n  getNotifications(): Observable<Notification[]> {\n    return of(this.mockNotifications).pipe(delay(200));\n  }\n\n  /**\n   * Simule l'envoi d'un message\n   */\n  sendMessage(\n    content: string,\n    conversationId: string,\n    senderId: string\n  ): Observable<Message> {\n    const newMessage: Message = {\n      id: `msg_${Date.now()}`,\n      content,\n      type: MessageType.TEXT,\n      timestamp: new Date(),\n      sender:\n        this.mockUsers.find((u) => u.id === senderId) || this.mockUsers[0],\n      isRead: false,\n      conversationId,\n    };\n\n    // Ajouter le message à la liste\n    this.mockMessages.push(newMessage);\n\n    // Mettre à jour la conversation\n    const conversation = this.mockConversations.find(\n      (c) => c.id === conversationId\n    );\n    if (conversation) {\n      conversation.lastMessage = newMessage;\n    }\n\n    return of(newMessage).pipe(delay(100));\n  }\n\n  /**\n   * Simule la création d'une conversation\n   */\n  createConversation(\n    userId: string,\n    currentUserId: string\n  ): Observable<Conversation> {\n    const otherUser = this.mockUsers.find((u) => u.id === userId);\n    const currentUser = this.mockUsers.find((u) => u.id === currentUserId);\n\n    if (!otherUser || !currentUser) {\n      throw new Error('Utilisateur non trouvé');\n    }\n\n    const newConversation: Conversation = {\n      id: `conv_${Date.now()}`,\n      participants: [currentUser, otherUser],\n      unreadCount: 0,\n      isGroup: false,\n      createdAt: new Date(),\n    };\n\n    this.mockConversations.unshift(newConversation);\n    return of(newConversation).pipe(delay(200));\n  }\n\n  /**\n   * Récupère l'utilisateur actuel (pour les tests)\n   */\n  getCurrentUser(): User {\n    return this.mockUsers[0]; // Alice comme utilisateur actuel\n  }\n\n  /**\n   * Simule la recherche d'utilisateurs\n   */\n  searchUsers(query: string): Observable<User[]> {\n    const results = this.mockUsers.filter(\n      (user) =>\n        user.username.toLowerCase().includes(query.toLowerCase()) ||\n        user.email.toLowerCase().includes(query.toLowerCase())\n    );\n    return of(results).pipe(delay(300));\n  }\n\n  /**\n   * Simule la recherche de conversations\n   */\n  searchConversations(query: string): Observable<Conversation[]> {\n    const results = this.mockConversations.filter((conv) => {\n      if (conv.isGroup) {\n        return conv.groupName?.toLowerCase().includes(query.toLowerCase());\n      } else {\n        return conv.participants?.some((p) =>\n          p.username.toLowerCase().includes(query.toLowerCase())\n        );\n      }\n    });\n    return of(results).pipe(delay(300));\n  }\n}\n", "import { Injectable } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\nimport { Toast } from 'src/app/models/message.model';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class ToastService {\n  private toastsSubject = new BehaviorSubject<Toast[]>([]);\n  toasts$ = this.toastsSubject.asObservable();\n  private currentId = 0;\n\n  constructor() {}\n  private generateId(): string {\n    return Math.random().toString(36).substr(2, 9);\n  }\n\n  private addToast(toast: Omit<Toast, 'id'>): void {\n    const newToast: Toast = {\n      ...toast,\n      id: this.generateId(),\n      duration: toast.duration || 5000,\n    };\n\n    const currentToasts = this.toastsSubject.value;\n    this.toastsSubject.next([...currentToasts, newToast]);\n\n    // Auto-remove toast after duration\n    if (newToast.duration && newToast.duration > 0) {\n      setTimeout(() => {\n        this.removeToast(newToast.id);\n      }, newToast.duration);\n    }\n  }\n  show(\n    message: string,\n    type: 'success' | 'error' | 'warning' | 'info' = 'info',\n    duration = 5000\n  ) {\n    const id = this.generateId();\n    const toast: Toast = { id, type, title: '', message, duration };\n    const currentToasts = this.toastsSubject.value;\n    this.toastsSubject.next([...currentToasts, toast]);\n\n    if (duration > 0) {\n      setTimeout(() => this.dismiss(id), duration);\n    }\n  }\n\n  showSuccess(message: string, duration = 3000) {\n    this.show(message, 'success', duration);\n  }\n\n  showError(message: string, duration = 5000) {\n    this.show(message, 'error', duration);\n  }\n\n  showWarning(message: string, duration = 4000) {\n    this.show(message, 'warning', duration);\n  }\n\n  showInfo(message: string, duration = 3000) {\n    this.show(message, 'info', duration);\n  }\n\n  dismiss(id: string) {\n    const currentToasts = this.toastsSubject.value.filter((t) => t.id !== id);\n    this.toastsSubject.next(currentToasts);\n  }\n  success(title: string, message: string, duration?: number): void {\n    this.addToast({\n      type: 'success',\n      title,\n      message,\n      duration,\n      icon: 'check-circle',\n    });\n  }\n  error(\n    title: string,\n    message: string,\n    duration?: number,\n    action?: Toast['action']\n  ): void {\n    this.addToast({\n      type: 'error',\n      title,\n      message,\n      duration: duration || 8000, // Longer duration for errors\n      icon: 'x-circle',\n      action,\n    });\n  }\n\n  warning(title: string, message: string, duration?: number): void {\n    this.addToast({\n      type: 'warning',\n      title,\n      message,\n      duration,\n      icon: 'exclamation-triangle',\n    });\n  }\n  // Méthodes spécifiques pour les erreurs d'autorisation\n  accessDenied(action: string = 'effectuer cette action', code?: number): void {\n    const codeText = code ? ` (Code: ${code})` : '';\n    this.error(\n      'Accès refusé',\n      `Vous n'avez pas les permissions nécessaires pour ${action}${codeText}`,\n      8000,\n      {\n        label: 'Comprendre les rôles',\n        handler: () => {\n          // Optionnel: rediriger vers une page d'aide\n          console.log(\"Redirection vers l'aide sur les rôles\");\n        },\n      }\n    );\n  }\n\n  ownershipRequired(resource: string = 'cette ressource'): void {\n    this.error(\n      'Propriétaire requis',\n      `Seul le propriétaire ou un administrateur peut modifier ${resource}`,\n      8000\n    );\n  }\n\n  removeToast(id: string): void {\n    const currentToasts = this.toastsSubject.value;\n    this.toastsSubject.next(currentToasts.filter((toast) => toast.id !== id));\n  }\n  clear() {\n    this.toastsSubject.next([]);\n  }\n}\n", "import {\n  <PERSON>mponent,\n  <PERSON><PERSON>nit,\n  <PERSON><PERSON><PERSON>roy,\n  ChangeDetectorRef,\n  ViewChild,\n  ElementRef,\n} from '@angular/core';\nimport { Subscription, BehaviorSubject } from 'rxjs';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { MessageService } from '../../../../services/message.service';\nimport { AuthService } from '../../../../services/auth.service';\nimport { ToastService } from '../../../../services/toast.service';\nimport { ThemeService, Theme } from '../../../../services/theme.service';\nimport { MockDataService } from '../../../../services/mock-data.service';\nimport {\n  User,\n  Conversation,\n  Message,\n  Notification,\n} from '../../../../models/message.model';\n\n@Component({\n  selector: 'app-message-layout',\n  templateUrl: './message-layout.component.html',\n  styleUrls: ['./message-layout.component.css'],\n})\nexport class MessageLayoutComponent implements OnInit, OnDestroy {\n  @ViewChild('searchInput') searchInput!: ElementRef;\n\n  // État du composant\n  currentUser: User | null = null;\n  conversations: Conversation[] = [];\n  users: User[] = [];\n  notifications: Notification[] = [];\n\n  // Navigation et UI\n  activeTab: 'conversations' | 'users' | 'notifications' = 'conversations';\n  selectedConversationId: string | null = null;\n  isMobileMenuOpen = false;\n  isSearching = false;\n\n  // Thème\n  currentTheme: Theme | null = null;\n  availableThemes: Theme[] = [];\n  showThemeSelector = false;\n\n  // Recherche\n  searchQuery = '';\n  searchResults: (Conversation | User)[] = [];\n\n  // États de chargement\n  isLoadingConversations = false;\n  isLoadingUsers = false;\n  isLoadingNotifications = false;\n\n  // Pagination\n  conversationsPage = 1;\n  usersPage = 1;\n  hasMoreConversations = true;\n  hasMoreUsers = true;\n\n  // Subscriptions\n  private subscriptions: Subscription[] = [];\n\n  // Observables\n  private searchQuery$ = new BehaviorSubject<string>('');\n\n  constructor(\n    private messageService: MessageService,\n    private authService: AuthService,\n    private toastService: ToastService,\n    private themeService: ThemeService,\n    private mockDataService: MockDataService,\n    private route: ActivatedRoute,\n    private router: Router,\n    private cdr: ChangeDetectorRef\n  ) {}\n\n  ngOnInit(): void {\n    this.initializeComponent();\n    this.setupSubscriptions();\n    this.loadInitialData();\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\n  }\n\n  // ============================================================================\n  // MÉTHODES D'INITIALISATION\n  // ============================================================================\n\n  private initializeComponent(): void {\n    // Récupérer l'utilisateur actuel\n    this.currentUser = this.authService.getCurrentUser();\n\n    if (!this.currentUser) {\n      this.router.navigate(['/login']);\n      return;\n    }\n\n    // Initialiser les thèmes\n    this.currentTheme = this.themeService.getCurrentTheme();\n    this.availableThemes = this.themeService.getAvailableThemes();\n\n    // Écouter les changements de route\n    this.route.params.subscribe((params) => {\n      const conversationId = params['conversationId'];\n      if (conversationId) {\n        this.selectedConversationId = conversationId;\n        this.markConversationAsSelected(conversationId);\n      }\n    });\n  }\n\n  private setupSubscriptions(): void {\n    // Subscription pour les nouveaux messages\n    const messagesSub = this.messageService\n      .subscribeToMessages()\n      .subscribe((message) => {\n        if (message) {\n          this.handleNewMessage(message);\n        }\n      });\n\n    // Subscription pour les notifications\n    const notificationsSub = this.messageService\n      .subscribeToNotifications()\n      .subscribe((notification) => {\n        if (notification) {\n          this.handleNewNotification(notification);\n        }\n      });\n\n    // Subscription pour la recherche\n    const searchSub = this.searchQuery$.subscribe((query) => {\n      this.performSearch(query);\n    });\n\n    // Subscription pour les changements de thème\n    const themeSub = this.themeService.currentTheme$.subscribe((theme) => {\n      this.currentTheme = theme;\n      this.cdr.detectChanges();\n    });\n\n    this.subscriptions.push(messagesSub, notificationsSub, searchSub, themeSub);\n  }\n\n  private loadInitialData(): void {\n    this.loadConversations();\n    this.loadUsers();\n    this.loadNotifications();\n\n    // Charger l'utilisateur actuel depuis les données de test\n    if (!this.currentUser) {\n      this.currentUser = this.mockDataService.getCurrentUser();\n    }\n  }\n\n  // ============================================================================\n  // MÉTHODES DE CHARGEMENT DES DONNÉES\n  // ============================================================================\n\n  loadConversations(page: number = 1): void {\n    if (this.isLoadingConversations) return;\n\n    this.isLoadingConversations = true;\n\n    this.messageService.getConversations().subscribe({\n      next: (conversations) => {\n        if (page === 1) {\n          this.conversations = conversations;\n        } else {\n          this.conversations.push(...conversations);\n        }\n\n        this.conversationsPage = page;\n        this.hasMoreConversations = conversations.length === 25;\n        this.isLoadingConversations = false;\n        this.cdr.detectChanges();\n      },\n      error: (error) => {\n        console.warn(\n          'Service principal indisponible, utilisation des données de test:',\n          error\n        );\n        // Fallback sur les données de test\n        this.mockDataService.getConversations().subscribe({\n          next: (conversations) => {\n            if (page === 1) {\n              this.conversations = conversations;\n            } else {\n              this.conversations.push(...conversations);\n            }\n            this.conversationsPage = page;\n            this.hasMoreConversations = false; // Pas de pagination pour les données de test\n            this.isLoadingConversations = false;\n            this.cdr.detectChanges();\n            if (page === 1) {\n              this.toastService.showInfo(\n                'Mode démo - Données de test chargées'\n              );\n            }\n          },\n          error: (mockError) => {\n            console.error(\n              'Erreur lors du chargement des données de test:',\n              mockError\n            );\n            this.isLoadingConversations = false;\n            this.toastService.showError(\n              'Erreur lors du chargement des conversations'\n            );\n          },\n        });\n      },\n    });\n  }\n\n  loadUsers(page: number = 1): void {\n    if (this.isLoadingUsers) return;\n\n    this.isLoadingUsers = true;\n\n    this.messageService.getAllUsers(false, '', page, 25).subscribe({\n      next: (users) => {\n        if (page === 1) {\n          this.users = users;\n        } else {\n          this.users.push(...users);\n        }\n\n        this.usersPage = page;\n        this.hasMoreUsers = users.length === 25;\n        this.isLoadingUsers = false;\n        this.cdr.detectChanges();\n      },\n      error: (error) => {\n        console.warn(\n          'Service principal indisponible, utilisation des données de test:',\n          error\n        );\n        // Fallback sur les données de test\n        this.mockDataService.getUsers().subscribe({\n          next: (users) => {\n            if (page === 1) {\n              this.users = users;\n            } else {\n              this.users.push(...users);\n            }\n            this.usersPage = page;\n            this.hasMoreUsers = false; // Pas de pagination pour les données de test\n            this.isLoadingUsers = false;\n            this.cdr.detectChanges();\n          },\n          error: (mockError) => {\n            console.error(\n              'Erreur lors du chargement des données de test:',\n              mockError\n            );\n            this.isLoadingUsers = false;\n            this.toastService.showError(\n              'Erreur lors du chargement des utilisateurs'\n            );\n          },\n        });\n      },\n    });\n  }\n\n  loadNotifications(): void {\n    if (this.isLoadingNotifications) return;\n\n    this.isLoadingNotifications = true;\n\n    this.messageService.getNotifications().subscribe({\n      next: (notifications) => {\n        this.notifications = notifications;\n        this.isLoadingNotifications = false;\n        this.cdr.detectChanges();\n      },\n      error: (error) => {\n        console.warn(\n          'Service principal indisponible, utilisation des données de test:',\n          error\n        );\n        // Fallback sur les données de test\n        this.mockDataService.getNotifications().subscribe({\n          next: (notifications) => {\n            this.notifications = notifications;\n            this.isLoadingNotifications = false;\n            this.cdr.detectChanges();\n          },\n          error: (mockError) => {\n            console.error(\n              'Erreur lors du chargement des données de test:',\n              mockError\n            );\n            this.isLoadingNotifications = false;\n            this.toastService.showError(\n              'Erreur lors du chargement des notifications'\n            );\n          },\n        });\n      },\n    });\n  }\n\n  // ============================================================================\n  // MÉTHODES DE GESTION DES ÉVÉNEMENTS\n  // ============================================================================\n\n  private handleNewMessage(message: Message): void {\n    // Mettre à jour la conversation correspondante\n    const conversationIndex = this.conversations.findIndex(\n      (conv) => conv.id === message.conversationId\n    );\n\n    if (conversationIndex !== -1) {\n      // Mettre à jour le dernier message\n      this.conversations[conversationIndex].lastMessage = message;\n\n      // Déplacer la conversation en haut de la liste\n      const conversation = this.conversations.splice(conversationIndex, 1)[0];\n      this.conversations.unshift(conversation);\n\n      this.cdr.detectChanges();\n    }\n  }\n\n  private handleNewNotification(notification: Notification): void {\n    // Ajouter la nouvelle notification en haut de la liste\n    this.notifications.unshift(notification);\n    this.cdr.detectChanges();\n\n    // Afficher une notification toast si ce n'est pas l'onglet actif\n    if (this.activeTab !== 'notifications') {\n      this.toastService.showInfo('Nouvelle notification reçue');\n    }\n  }\n\n  private markConversationAsSelected(conversationId: string): void {\n    // Marquer la conversation comme sélectionnée visuellement\n    this.selectedConversationId = conversationId;\n    this.cdr.detectChanges();\n  }\n\n  // ============================================================================\n  // MÉTHODES DE NAVIGATION ET UI\n  // ============================================================================\n\n  switchTab(tab: 'conversations' | 'users' | 'notifications'): void {\n    this.activeTab = tab;\n    this.searchQuery = '';\n    this.searchResults = [];\n    this.isSearching = false;\n\n    // Charger les données si nécessaire\n    switch (tab) {\n      case 'conversations':\n        if (this.conversations.length === 0) {\n          this.loadConversations();\n        }\n        break;\n      case 'users':\n        if (this.users.length === 0) {\n          this.loadUsers();\n        }\n        break;\n      case 'notifications':\n        if (this.notifications.length === 0) {\n          this.loadNotifications();\n        }\n        break;\n    }\n  }\n\n  selectConversation(conversation: Conversation): void {\n    if (!conversation.id) return;\n\n    this.selectedConversationId = conversation.id;\n    this.router.navigate(['/messages', conversation.id]);\n\n    // Fermer le menu mobile si ouvert\n    this.isMobileMenuOpen = false;\n  }\n\n  startConversationWithUser(user: User): void {\n    if (!user.id && !user._id) return;\n\n    const userId = user.id || user._id!;\n\n    // Créer ou récupérer la conversation avec cet utilisateur\n    this.messageService.createOrGetConversation(userId).subscribe({\n      next: (conversation) => {\n        this.selectConversation(conversation);\n      },\n      error: (error) => {\n        console.warn(\n          'Service principal indisponible, utilisation des données de test:',\n          error\n        );\n        // Fallback sur les données de test\n        const currentUserId = this.currentUser?.id || '1';\n        this.mockDataService\n          .createConversation(userId, currentUserId)\n          .subscribe({\n            next: (conversation) => {\n              this.conversations.unshift(conversation);\n              this.selectConversation(conversation);\n              this.toastService.showSuccess('Conversation créée (mode démo)');\n            },\n            error: (mockError) => {\n              console.error(\n                'Erreur lors de la création de la conversation:',\n                mockError\n              );\n              this.toastService.showError(\n                'Erreur lors de la création de la conversation'\n              );\n            },\n          });\n      },\n    });\n  }\n\n  toggleMobileMenu(): void {\n    this.isMobileMenuOpen = !this.isMobileMenuOpen;\n  }\n\n  // ============================================================================\n  // MÉTHODES DE RECHERCHE\n  // ============================================================================\n\n  onSearchInput(event: any): void {\n    const query = event.target.value.trim();\n    this.searchQuery = query;\n    this.searchQuery$.next(query);\n  }\n\n  private performSearch(query: string): void {\n    if (!query) {\n      this.searchResults = [];\n      this.isSearching = false;\n      return;\n    }\n\n    this.isSearching = true;\n\n    if (this.activeTab === 'conversations') {\n      this.searchResults = this.conversations.filter((conv) =>\n        conv.isGroup\n          ? conv.groupName?.toLowerCase().includes(query.toLowerCase())\n          : conv.participants?.some((p) =>\n              p.username?.toLowerCase().includes(query.toLowerCase())\n            )\n      );\n    } else if (this.activeTab === 'users') {\n      this.searchResults = this.users.filter(\n        (user) =>\n          user.username?.toLowerCase().includes(query.toLowerCase()) ||\n          user.email?.toLowerCase().includes(query.toLowerCase())\n      );\n    }\n\n    this.cdr.detectChanges();\n  }\n\n  clearSearch(): void {\n    this.searchQuery = '';\n    this.searchResults = [];\n    this.isSearching = false;\n    this.searchQuery$.next('');\n  }\n\n  // ============================================================================\n  // MÉTHODES DE PAGINATION\n  // ============================================================================\n\n  loadMoreConversations(): void {\n    if (this.hasMoreConversations && !this.isLoadingConversations) {\n      this.loadConversations(this.conversationsPage + 1);\n    }\n  }\n\n  loadMoreUsers(): void {\n    if (this.hasMoreUsers && !this.isLoadingUsers) {\n      this.loadUsers(this.usersPage + 1);\n    }\n  }\n\n  // ============================================================================\n  // MÉTHODES UTILITAIRES POUR LE TEMPLATE\n  // ============================================================================\n\n  getConversationName(conversation: Conversation): string {\n    if (conversation.isGroup) {\n      return conversation.groupName || 'Groupe sans nom';\n    }\n\n    if (!this.currentUser) return 'Conversation';\n\n    const currentUserId = this.currentUser.id || this.currentUser._id;\n    const otherParticipant = conversation.participants?.find(\n      (p) => (p.id || p._id) !== currentUserId\n    );\n\n    return otherParticipant?.username || 'Utilisateur inconnu';\n  }\n\n  getConversationAvatar(conversation: Conversation): string {\n    if (conversation.isGroup) {\n      return conversation.groupPhoto || '/assets/images/default-group.png';\n    }\n\n    if (!this.currentUser) return '/assets/images/default-avatar.png';\n\n    const currentUserId = this.currentUser.id || this.currentUser._id;\n    const otherParticipant = conversation.participants?.find(\n      (p) => (p.id || p._id) !== currentUserId\n    );\n\n    return otherParticipant?.image || '/assets/images/default-avatar.png';\n  }\n\n  getLastMessagePreview(conversation: Conversation): string {\n    if (!conversation.lastMessage) return 'Aucun message';\n\n    const message = conversation.lastMessage;\n\n    if (message.type === 'TEXT') {\n      return message.content || '';\n    } else if (message.type === 'IMAGE') {\n      return '📷 Image';\n    } else if (message.type === 'FILE') {\n      return '📎 Fichier';\n    } else if (message.type === 'VOICE_MESSAGE') {\n      return '🎤 Message vocal';\n    } else if (message.type === 'VIDEO') {\n      return '🎥 Vidéo';\n    }\n\n    return 'Message';\n  }\n\n  formatLastMessageTime(timestamp: Date | string | undefined): string {\n    if (!timestamp) return '';\n\n    const date = new Date(timestamp);\n    const now = new Date();\n    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n\n    if (diffInHours < 1) {\n      return \"À l'instant\";\n    } else if (diffInHours < 24) {\n      return date.toLocaleTimeString('fr-FR', {\n        hour: '2-digit',\n        minute: '2-digit',\n      });\n    } else if (diffInHours < 168) {\n      // 7 jours\n      return date.toLocaleDateString('fr-FR', { weekday: 'short' });\n    } else {\n      return date.toLocaleDateString('fr-FR', {\n        day: '2-digit',\n        month: '2-digit',\n      });\n    }\n  }\n\n  getUnreadCount(conversation: Conversation): number {\n    return conversation.unreadCount || 0;\n  }\n\n  isUserOnline(user: User): boolean {\n    return user.isOnline || false;\n  }\n\n  trackByConversationId(index: number, conversation: Conversation): string {\n    return conversation.id || conversation._id || index.toString();\n  }\n\n  trackByUserId(index: number, user: User): string {\n    return user.id || user._id || index.toString();\n  }\n\n  trackByNotificationId(index: number, notification: Notification): string {\n    return notification.id || index.toString();\n  }\n\n  markNotificationAsRead(notification: Notification): void {\n    if (!notification.id || notification.isRead) return;\n\n    this.messageService.markNotificationAsRead(notification.id).subscribe({\n      next: () => {\n        notification.isRead = true;\n        this.cdr.detectChanges();\n      },\n      error: (error) => {\n        console.error(\n          'Erreur lors du marquage de la notification comme lue:',\n          error\n        );\n        this.toastService.showError(\n          'Erreur lors du marquage de la notification'\n        );\n      },\n    });\n  }\n\n  // Type guards pour différencier User et Conversation dans les résultats de recherche\n  isUser(item: User | Conversation): item is User {\n    return 'username' in item && 'email' in item;\n  }\n\n  isConversation(item: User | Conversation): item is Conversation {\n    return 'participants' in item || 'isGroup' in item;\n  }\n\n  getNotificationTitle(notification: Notification): string {\n    switch (notification.type) {\n      case 'NEW_MESSAGE':\n        return 'Nouveau message';\n      case 'FRIEND_REQUEST':\n        return \"Demande d'ami\";\n      case 'GROUP_INVITE':\n        return 'Invitation de groupe';\n      case 'MESSAGE_REACTION':\n        return 'Réaction à un message';\n      case 'SYSTEM_ALERT':\n        return 'Alerte système';\n      default:\n        return 'Notification';\n    }\n  }\n\n  // ============================================================================\n  // MÉTHODES DE GESTION DES THÈMES\n  // ============================================================================\n\n  selectTheme(themeName: string): void {\n    this.themeService.setTheme(themeName);\n    this.showThemeSelector = false;\n    this.toastService.showSuccess(\n      `Thème \"${this.themeService.getCurrentTheme().displayName}\" appliqué`\n    );\n  }\n}\n", "<!-- ============================================================================\n     LAYOUT PRINCIPAL DE MESSAGERIE - STYLE WHATSAPP\n     ============================================================================ -->\n\n<div class=\"message-layout h-screen bg-gray-900 text-white flex\">\n  <!-- ========================================================================\n       SIDEBAR GAUCHE - CONVERSATIONS/UTILISATEURS/NOTIFICATIONS\n       ======================================================================== -->\n  <div\n    class=\"sidebar w-80 bg-gray-800 border-r border-gray-700 flex flex-col\"\n    [class.hidden]=\"!isMobileMenuOpen\"\n    [class.md:flex]=\"true\"\n  >\n    <!-- En-tête de la sidebar -->\n    <div class=\"sidebar-header p-4 border-b border-gray-700 bg-gray-800\">\n      <div class=\"flex items-center justify-between mb-4\">\n        <div class=\"flex items-center space-x-3\">\n          <img\n            [src]=\"currentUser?.image || '/assets/images/default-avatar.png'\"\n            [alt]=\"currentUser?.username\"\n            class=\"w-10 h-10 rounded-full border-2 border-blue-500\"\n          />\n          <div>\n            <h3 class=\"font-semibold text-white\">\n              {{ currentUser?.username }}\n            </h3>\n            <p class=\"text-sm text-green-400\">En ligne</p>\n          </div>\n        </div>\n\n        <!-- Actions de l'en-tête -->\n        <div class=\"flex items-center space-x-2\">\n          <!-- Sélecteur de thème -->\n          <div class=\"relative\">\n            <button\n              class=\"p-2 rounded-lg bg-gray-700 hover:bg-gray-600 transition-colors\"\n              (click)=\"showThemeSelector = !showThemeSelector\"\n              title=\"Changer de thème\"\n            >\n              <i class=\"fas fa-palette text-blue-400\"></i>\n            </button>\n\n            <!-- Menu des thèmes -->\n            <div\n              *ngIf=\"showThemeSelector\"\n              class=\"absolute top-full right-0 mt-2 bg-gray-800 rounded-lg shadow-lg border border-gray-700 p-2 z-50 min-w-48\"\n            >\n              <div class=\"text-xs text-gray-400 mb-2 px-2\">\n                Choisir un thème\n              </div>\n              <div\n                *ngFor=\"let theme of availableThemes\"\n                class=\"flex items-center space-x-3 p-2 hover:bg-gray-700 rounded cursor-pointer transition-colors\"\n                (click)=\"selectTheme(theme.name)\"\n              >\n                <div\n                  class=\"w-4 h-4 rounded-full border-2\"\n                  [style.background]=\"theme.gradients.primary\"\n                  [class.border-white]=\"currentTheme?.name === theme.name\"\n                  [class.border-gray-500]=\"currentTheme?.name !== theme.name\"\n                ></div>\n                <span class=\"text-white text-sm\">{{ theme.displayName }}</span>\n                <i\n                  *ngIf=\"currentTheme?.name === theme.name\"\n                  class=\"fas fa-check text-blue-400 text-xs ml-auto\"\n                ></i>\n              </div>\n            </div>\n          </div>\n\n          <!-- Bouton menu mobile -->\n          <button\n            class=\"md:hidden p-2 rounded-lg bg-gray-700 hover:bg-gray-600\"\n            (click)=\"toggleMobileMenu()\"\n          >\n            <i class=\"fas fa-times text-white\"></i>\n          </button>\n        </div>\n      </div>\n\n      <!-- Barre de recherche -->\n      <div class=\"relative\">\n        <input\n          #searchInput\n          type=\"text\"\n          [(ngModel)]=\"searchQuery\"\n          (input)=\"onSearchInput($event)\"\n          placeholder=\"Rechercher...\"\n          class=\"w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 pl-10 text-white placeholder-gray-400 focus:outline-none focus:border-blue-500\"\n        />\n        <i class=\"fas fa-search absolute left-3 top-3 text-gray-400\"></i>\n        <button\n          *ngIf=\"searchQuery\"\n          (click)=\"clearSearch()\"\n          class=\"absolute right-3 top-3 text-gray-400 hover:text-white\"\n        >\n          <i class=\"fas fa-times\"></i>\n        </button>\n      </div>\n    </div>\n\n    <!-- Onglets de navigation -->\n    <div class=\"tabs flex border-b border-gray-700\">\n      <button\n        class=\"tab flex-1 py-3 px-4 text-center transition-all duration-200\"\n        [class.active]=\"activeTab === 'conversations'\"\n        [class.text-blue-400]=\"activeTab === 'conversations'\"\n        [class.border-b-2]=\"activeTab === 'conversations'\"\n        [class.border-blue-500]=\"activeTab === 'conversations'\"\n        [class.text-gray-400]=\"activeTab !== 'conversations'\"\n        (click)=\"switchTab('conversations')\"\n      >\n        <i class=\"fas fa-comments mb-1\"></i>\n        <div class=\"text-xs\">Discussions</div>\n      </button>\n\n      <button\n        class=\"tab flex-1 py-3 px-4 text-center transition-all duration-200\"\n        [class.active]=\"activeTab === 'users'\"\n        [class.text-blue-400]=\"activeTab === 'users'\"\n        [class.border-b-2]=\"activeTab === 'users'\"\n        [class.border-blue-500]=\"activeTab === 'users'\"\n        [class.text-gray-400]=\"activeTab !== 'users'\"\n        (click)=\"switchTab('users')\"\n      >\n        <i class=\"fas fa-users mb-1\"></i>\n        <div class=\"text-xs\">Contacts</div>\n      </button>\n\n      <button\n        class=\"tab flex-1 py-3 px-4 text-center transition-all duration-200 relative\"\n        [class.active]=\"activeTab === 'notifications'\"\n        [class.text-blue-400]=\"activeTab === 'notifications'\"\n        [class.border-b-2]=\"activeTab === 'notifications'\"\n        [class.border-blue-500]=\"activeTab === 'notifications'\"\n        [class.text-gray-400]=\"activeTab !== 'notifications'\"\n        (click)=\"switchTab('notifications')\"\n      >\n        <i class=\"fas fa-bell mb-1\"></i>\n        <div class=\"text-xs\">Notifications</div>\n        <span\n          *ngIf=\"notifications.length > 0\"\n          class=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\"\n        >\n          {{ notifications.length > 9 ? \"9+\" : notifications.length }}\n        </span>\n      </button>\n    </div>\n\n    <!-- Contenu de la sidebar -->\n    <div class=\"sidebar-content flex-1 overflow-y-auto\">\n      <!-- ====================================================================\n           ONGLET CONVERSATIONS\n           ==================================================================== -->\n      <div *ngIf=\"activeTab === 'conversations'\" class=\"conversations-list\">\n        <!-- Résultats de recherche -->\n        <div\n          *ngIf=\"isSearching && searchResults.length > 0\"\n          class=\"search-results\"\n        >\n          <div class=\"p-3 text-sm text-gray-400 border-b border-gray-700\">\n            Résultats de recherche ({{ searchResults.length }})\n          </div>\n          <div\n            *ngFor=\"let result of searchResults\"\n            class=\"conversation-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors\"\n            (click)=\"selectConversation(result)\"\n          >\n            <div class=\"flex items-center space-x-3\">\n              <img\n                [src]=\"getConversationAvatar(result)\"\n                [alt]=\"getConversationName(result)\"\n                class=\"w-12 h-12 rounded-full\"\n              />\n              <div class=\"flex-1 min-w-0\">\n                <h4 class=\"font-medium text-white truncate\">\n                  {{ getConversationName(result) }}\n                </h4>\n                <p class=\"text-sm text-gray-400 truncate\">\n                  {{ getLastMessagePreview(result) }}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Message si aucun résultat -->\n        <div\n          *ngIf=\"isSearching && searchResults.length === 0\"\n          class=\"p-8 text-center text-gray-400\"\n        >\n          <i class=\"fas fa-search text-4xl mb-4\"></i>\n          <p>Aucun résultat trouvé</p>\n        </div>\n\n        <!-- Liste des conversations -->\n        <div *ngIf=\"!isSearching\">\n          <!-- Indicateur de chargement -->\n          <div\n            *ngIf=\"isLoadingConversations && conversations.length === 0\"\n            class=\"p-8 text-center\"\n          >\n            <div\n              class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto\"\n            ></div>\n            <p class=\"text-gray-400 mt-2\">Chargement des conversations...</p>\n          </div>\n\n          <!-- Conversations -->\n          <div\n            *ngFor=\"\n              let conversation of conversations;\n              trackBy: trackByConversationId\n            \"\n            class=\"conversation-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors relative\"\n            [class.bg-gray-700]=\"selectedConversationId === conversation.id\"\n            [class.border-l-4]=\"selectedConversationId === conversation.id\"\n            [class.border-blue-500]=\"selectedConversationId === conversation.id\"\n            (click)=\"selectConversation(conversation)\"\n          >\n            <div class=\"flex items-center space-x-3\">\n              <!-- Avatar avec indicateur en ligne -->\n              <div class=\"relative\">\n                <img\n                  [src]=\"getConversationAvatar(conversation)\"\n                  [alt]=\"getConversationName(conversation)\"\n                  class=\"w-12 h-12 rounded-full\"\n                />\n                <div\n                  *ngIf=\"!conversation.isGroup && isUserOnline(conversation.participants?.[0]!)\"\n                  class=\"absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-gray-800\"\n                ></div>\n              </div>\n\n              <!-- Informations de la conversation -->\n              <div class=\"flex-1 min-w-0\">\n                <div class=\"flex items-center justify-between\">\n                  <h4 class=\"font-medium text-white truncate\">\n                    {{ getConversationName(conversation) }}\n                  </h4>\n                  <span class=\"text-xs text-gray-400\">\n                    {{\n                      formatLastMessageTime(conversation.lastMessage?.timestamp)\n                    }}\n                  </span>\n                </div>\n\n                <div class=\"flex items-center justify-between mt-1\">\n                  <p class=\"text-sm text-gray-400 truncate\">\n                    {{ getLastMessagePreview(conversation) }}\n                  </p>\n\n                  <!-- Badge de messages non lus -->\n                  <span\n                    *ngIf=\"getUnreadCount(conversation) > 0\"\n                    class=\"bg-blue-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center\"\n                  >\n                    {{\n                      getUnreadCount(conversation) > 99\n                        ? \"99+\"\n                        : getUnreadCount(conversation)\n                    }}\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Bouton charger plus -->\n          <div *ngIf=\"hasMoreConversations\" class=\"p-4 text-center\">\n            <button\n              (click)=\"loadMoreConversations()\"\n              [disabled]=\"isLoadingConversations\"\n              class=\"text-blue-400 hover:text-blue-300 disabled:text-gray-500\"\n            >\n              <span *ngIf=\"!isLoadingConversations\">Charger plus</span>\n              <span *ngIf=\"isLoadingConversations\">Chargement...</span>\n            </button>\n          </div>\n\n          <!-- Message si aucune conversation -->\n          <div\n            *ngIf=\"conversations.length === 0 && !isLoadingConversations\"\n            class=\"p-8 text-center text-gray-400\"\n          >\n            <i class=\"fas fa-comments text-4xl mb-4\"></i>\n            <p>Aucune conversation</p>\n            <p class=\"text-sm mt-2\">\n              Commencez une nouvelle conversation dans l'onglet Contacts\n            </p>\n          </div>\n        </div>\n      </div>\n\n      <!-- ====================================================================\n           ONGLET UTILISATEURS/CONTACTS\n           ==================================================================== -->\n      <div *ngIf=\"activeTab === 'users'\" class=\"users-list\">\n        <!-- Résultats de recherche -->\n        <div\n          *ngIf=\"isSearching && searchResults.length > 0\"\n          class=\"search-results\"\n        >\n          <div class=\"p-3 text-sm text-gray-400 border-b border-gray-700\">\n            Résultats de recherche ({{ searchResults.length }})\n          </div>\n          <div\n            *ngFor=\"let result of searchResults\"\n            class=\"user-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors\"\n            (click)=\"isUser(result) ? startConversationWithUser(result) : null\"\n          >\n            <div class=\"flex items-center space-x-3\" *ngIf=\"isUser(result)\">\n              <div class=\"relative\">\n                <img\n                  [src]=\"result.image || '/assets/images/default-avatar.png'\"\n                  [alt]=\"result.username\"\n                  class=\"w-12 h-12 rounded-full\"\n                />\n                <div\n                  *ngIf=\"isUserOnline(result)\"\n                  class=\"absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-gray-800\"\n                ></div>\n              </div>\n              <div class=\"flex-1 min-w-0\">\n                <h4 class=\"font-medium text-white truncate\">\n                  {{ result.username }}\n                </h4>\n                <p class=\"text-sm text-gray-400 truncate\">{{ result.email }}</p>\n              </div>\n              <div class=\"text-blue-400\">\n                <i class=\"fas fa-comment\"></i>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Message si aucun résultat -->\n        <div\n          *ngIf=\"isSearching && searchResults.length === 0\"\n          class=\"p-8 text-center text-gray-400\"\n        >\n          <i class=\"fas fa-search text-4xl mb-4\"></i>\n          <p>Aucun utilisateur trouvé</p>\n        </div>\n\n        <!-- Liste des utilisateurs -->\n        <div *ngIf=\"!isSearching\">\n          <!-- Indicateur de chargement -->\n          <div\n            *ngIf=\"isLoadingUsers && users.length === 0\"\n            class=\"p-8 text-center\"\n          >\n            <div\n              class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto\"\n            ></div>\n            <p class=\"text-gray-400 mt-2\">Chargement des utilisateurs...</p>\n          </div>\n\n          <!-- Utilisateurs -->\n          <div\n            *ngFor=\"let user of users; trackBy: trackByUserId\"\n            class=\"user-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors\"\n            (click)=\"startConversationWithUser(user)\"\n          >\n            <div class=\"flex items-center space-x-3\">\n              <!-- Avatar avec indicateur en ligne -->\n              <div class=\"relative\">\n                <img\n                  [src]=\"user.image || '/assets/images/default-avatar.png'\"\n                  [alt]=\"user.username\"\n                  class=\"w-12 h-12 rounded-full\"\n                />\n                <div\n                  *ngIf=\"isUserOnline(user)\"\n                  class=\"absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-gray-800\"\n                ></div>\n              </div>\n\n              <!-- Informations de l'utilisateur -->\n              <div class=\"flex-1 min-w-0\">\n                <h4 class=\"font-medium text-white truncate\">\n                  {{ user.username }}\n                </h4>\n                <p class=\"text-sm text-gray-400 truncate\">{{ user.email }}</p>\n                <p class=\"text-xs text-gray-500\" *ngIf=\"user.role\">\n                  {{ user.role }}\n                </p>\n              </div>\n\n              <!-- Statut en ligne -->\n              <div class=\"text-right\">\n                <div\n                  class=\"text-xs px-2 py-1 rounded-full\"\n                  [class.bg-green-600]=\"isUserOnline(user)\"\n                  [class.text-green-100]=\"isUserOnline(user)\"\n                  [class.bg-gray-600]=\"!isUserOnline(user)\"\n                  [class.text-gray-300]=\"!isUserOnline(user)\"\n                >\n                  {{ isUserOnline(user) ? \"En ligne\" : \"Hors ligne\" }}\n                </div>\n                <div class=\"text-blue-400 mt-1\">\n                  <i class=\"fas fa-comment\"></i>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Bouton charger plus -->\n          <div *ngIf=\"hasMoreUsers\" class=\"p-4 text-center\">\n            <button\n              (click)=\"loadMoreUsers()\"\n              [disabled]=\"isLoadingUsers\"\n              class=\"text-blue-400 hover:text-blue-300 disabled:text-gray-500\"\n            >\n              <span *ngIf=\"!isLoadingUsers\">Charger plus</span>\n              <span *ngIf=\"isLoadingUsers\">Chargement...</span>\n            </button>\n          </div>\n\n          <!-- Message si aucun utilisateur -->\n          <div\n            *ngIf=\"users.length === 0 && !isLoadingUsers\"\n            class=\"p-8 text-center text-gray-400\"\n          >\n            <i class=\"fas fa-users text-4xl mb-4\"></i>\n            <p>Aucun utilisateur trouvé</p>\n          </div>\n        </div>\n      </div>\n\n      <!-- ====================================================================\n           ONGLET NOTIFICATIONS\n           ==================================================================== -->\n      <div *ngIf=\"activeTab === 'notifications'\" class=\"notifications-list\">\n        <!-- Indicateur de chargement -->\n        <div\n          *ngIf=\"isLoadingNotifications && notifications.length === 0\"\n          class=\"p-8 text-center\"\n        >\n          <div\n            class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto\"\n          ></div>\n          <p class=\"text-gray-400 mt-2\">Chargement des notifications...</p>\n        </div>\n\n        <!-- Notifications -->\n        <div\n          *ngFor=\"\n            let notification of notifications;\n            trackBy: trackByNotificationId\n          \"\n          class=\"notification-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors\"\n          [class.bg-gray-700]=\"!notification.isRead\"\n          (click)=\"markNotificationAsRead(notification)\"\n        >\n          <div class=\"flex items-start space-x-3\">\n            <!-- Icône de notification -->\n            <div\n              class=\"notification-icon p-2 rounded-full\"\n              [class.bg-blue-600]=\"notification.type === 'NEW_MESSAGE'\"\n              [class.bg-green-600]=\"notification.type === 'FRIEND_REQUEST'\"\n              [class.bg-yellow-600]=\"notification.type === 'GROUP_INVITE'\"\n              [class.bg-purple-600]=\"notification.type === 'MESSAGE_REACTION'\"\n              [class.bg-red-600]=\"notification.type === 'SYSTEM_ALERT'\"\n            >\n              <i\n                class=\"fas\"\n                [class.fa-message]=\"notification.type === 'NEW_MESSAGE'\"\n                [class.fa-user-plus]=\"notification.type === 'FRIEND_REQUEST'\"\n                [class.fa-users]=\"notification.type === 'GROUP_INVITE'\"\n                [class.fa-heart]=\"notification.type === 'MESSAGE_REACTION'\"\n                [class.fa-exclamation-triangle]=\"\n                  notification.type === 'SYSTEM_ALERT'\n                \"\n                class=\"text-white text-sm\"\n              ></i>\n            </div>\n\n            <!-- Contenu de la notification -->\n            <div class=\"flex-1 min-w-0\">\n              <h4 class=\"font-medium text-white truncate\">\n                {{ getNotificationTitle(notification) }}\n              </h4>\n              <p class=\"text-sm text-gray-400 mt-1\">\n                {{ notification.content }}\n              </p>\n              <p class=\"text-xs text-gray-500 mt-2\">\n                {{ formatLastMessageTime(notification.timestamp) }}\n              </p>\n            </div>\n\n            <!-- Indicateur non lu -->\n            <div\n              *ngIf=\"!notification.isRead\"\n              class=\"w-2 h-2 bg-blue-500 rounded-full\"\n            ></div>\n          </div>\n        </div>\n\n        <!-- Message si aucune notification -->\n        <div\n          *ngIf=\"notifications.length === 0 && !isLoadingNotifications\"\n          class=\"p-8 text-center text-gray-400\"\n        >\n          <i class=\"fas fa-bell text-4xl mb-4\"></i>\n          <p>Aucune notification</p>\n          <p class=\"text-sm mt-2\">\n            Vous serez notifié des nouveaux messages et événements\n          </p>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- ========================================================================\n       ZONE PRINCIPALE - CHAT OU MESSAGE DE BIENVENUE\n       ======================================================================== -->\n  <div class=\"main-content flex-1 flex flex-col\">\n    <!-- Bouton menu mobile -->\n    <div class=\"md:hidden p-4 border-b border-gray-700 bg-gray-800\">\n      <button\n        class=\"p-2 rounded-lg bg-gray-700 hover:bg-gray-600\"\n        (click)=\"toggleMobileMenu()\"\n      >\n        <i class=\"fas fa-bars text-white\"></i>\n      </button>\n    </div>\n\n    <!-- Contenu principal -->\n    <div class=\"flex-1\">\n      <router-outlet></router-outlet>\n    </div>\n  </div>\n</div>\n", "import { asyncScheduler } from '../scheduler/async';\nimport { delayWhen } from './delayWhen';\nimport { timer } from '../observable/timer';\nexport function delay(due, scheduler = asyncScheduler) {\n    const duration = timer(due, scheduler);\n    return delayWhen(() => duration);\n}\n", "import { concat } from '../observable/concat';\nimport { take } from './take';\nimport { ignoreElements } from './ignoreElements';\nimport { mapTo } from './mapTo';\nimport { mergeMap } from './mergeMap';\nimport { innerFrom } from '../observable/innerFrom';\nexport function delayWhen(delayDurationSelector, subscriptionDelay) {\n    if (subscriptionDelay) {\n        return (source) => concat(subscriptionDelay.pipe(take(1), ignoreElements()), source.pipe(delayWhen(delayDurationSelector)));\n    }\n    return mergeMap((value, index) => innerFrom(delayDurationSelector(value, index)).pipe(take(1), mapTo(value)));\n}\n", "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nexport function ignoreElements() {\n    return operate((source, subscriber) => {\n        source.subscribe(createOperatorSubscriber(subscriber, noop));\n    });\n}\n"], "names": ["of", "delay", "MessageType", "MockDataService", "constructor", "mockUsers", "id", "_id", "username", "email", "image", "isOnline", "isActive", "role", "mockMessages", "content", "type", "TEXT", "timestamp", "Date", "now", "sender", "isRead", "conversationId", "mockConversations", "participants", "lastMessage", "unreadCount", "isGroup", "createdAt", "groupName", "groupPhoto", "mockNotifications", "getUsers", "pipe", "getConversations", "getConversation", "conversation", "find", "c", "getMessages", "messages", "filter", "m", "getNotifications", "sendMessage", "senderId", "newMessage", "u", "push", "createConversation", "userId", "currentUserId", "otherUser", "currentUser", "Error", "newConversation", "unshift", "getCurrentUser", "searchUsers", "query", "results", "user", "toLowerCase", "includes", "searchConversations", "conv", "some", "p", "factory", "ɵfac", "providedIn", "BehaviorSubject", "ToastService", "toastsSubject", "toasts$", "asObservable", "currentId", "generateId", "Math", "random", "toString", "substr", "addToast", "toast", "newToast", "duration", "currentToasts", "value", "next", "setTimeout", "removeToast", "show", "message", "title", "dismiss", "showSuccess", "showError", "showWarning", "showInfo", "t", "success", "icon", "error", "action", "warning", "accessDenied", "code", "codeText", "label", "handler", "console", "log", "ownershipRequired", "resource", "clear", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵlistener", "MessageLayoutComponent_div_15_div_3_Template_div_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r11", "theme_r8", "$implicit", "ctx_r10", "ɵɵnextContext", "ɵɵresetView", "selectTheme", "name", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "MessageLayoutComponent_div_15_div_3_i_4_Template", "ɵɵadvance", "ɵɵstyleProp", "gradients", "primary", "ɵɵclassProp", "ctx_r7", "currentTheme", "ɵɵtextInterpolate", "displayName", "ɵɵproperty", "MessageLayoutComponent_div_15_div_3_Template", "ctx_r0", "availableThemes", "MessageLayoutComponent_button_22_Template_button_click_0_listener", "_r13", "ctx_r12", "clearSearch", "ɵɵtextInterpolate1", "ctx_r3", "notifications", "length", "MessageLayoutComponent_div_38_div_1_div_3_Template_div_click_0_listener", "_r20", "result_r18", "ctx_r19", "selectConversation", "ctx_r17", "getConversationAvatar", "ɵɵsanitizeUrl", "getConversationName", "getLastMessagePreview", "MessageLayoutComponent_div_38_div_1_div_3_Template", "ctx_r14", "searchResults", "ctx_r27", "getUnreadCount", "conversation_r25", "MessageLayoutComponent_div_38_div_3_div_2_Template_div_click_0_listener", "_r30", "ctx_r29", "MessageLayoutComponent_div_38_div_3_div_2_div_4_Template", "MessageLayoutComponent_div_38_div_3_div_2_span_14_Template", "ctx_r22", "selectedConversationId", "isUserOnline", "formatLastMessageTime", "MessageLayoutComponent_div_38_div_3_div_3_Template_button_click_1_listener", "_r34", "ctx_r33", "loadMoreConversations", "MessageLayoutComponent_div_38_div_3_div_3_span_2_Template", "MessageLayoutComponent_div_38_div_3_div_3_span_3_Template", "ctx_r23", "isLoadingConversations", "MessageLayoutComponent_div_38_div_3_div_1_Template", "MessageLayoutComponent_div_38_div_3_div_2_Template", "MessageLayoutComponent_div_38_div_3_div_3_Template", "MessageLayoutComponent_div_38_div_3_div_4_Template", "ctx_r16", "conversations", "trackByConversationId", "hasMoreConversations", "MessageLayoutComponent_div_38_div_1_Template", "MessageLayoutComponent_div_38_div_2_Template", "MessageLayoutComponent_div_38_div_3_Template", "ctx_r4", "isSearching", "MessageLayoutComponent_div_39_div_1_div_3_div_1_div_3_Template", "result_r39", "ctx_r40", "MessageLayoutComponent_div_39_div_1_div_3_Template_div_click_0_listener", "_r44", "ctx_r43", "isUser", "startConversationWithUser", "MessageLayoutComponent_div_39_div_1_div_3_div_1_Template", "ctx_r38", "MessageLayoutComponent_div_39_div_1_div_3_Template", "ctx_r35", "user_r49", "MessageLayoutComponent_div_39_div_3_div_2_Template_div_click_0_listener", "_r54", "ctx_r53", "MessageLayoutComponent_div_39_div_3_div_2_div_4_Template", "MessageLayoutComponent_div_39_div_3_div_2_p_10_Template", "ctx_r46", "MessageLayoutComponent_div_39_div_3_div_3_Template_button_click_1_listener", "_r58", "ctx_r57", "loadMoreUsers", "MessageLayoutComponent_div_39_div_3_div_3_span_2_Template", "MessageLayoutComponent_div_39_div_3_div_3_span_3_Template", "ctx_r47", "isLoadingUsers", "MessageLayoutComponent_div_39_div_3_div_1_Template", "MessageLayoutComponent_div_39_div_3_div_2_Template", "MessageLayoutComponent_div_39_div_3_div_3_Template", "MessageLayoutComponent_div_39_div_3_div_4_Template", "ctx_r37", "users", "trackByUserId", "hasMoreUsers", "MessageLayoutComponent_div_39_div_1_Template", "MessageLayoutComponent_div_39_div_2_Template", "MessageLayoutComponent_div_39_div_3_Template", "ctx_r5", "MessageLayoutComponent_div_40_div_2_Template_div_click_0_listener", "_r65", "notification_r62", "ctx_r64", "markNotificationAsRead", "MessageLayoutComponent_div_40_div_2_div_11_Template", "ctx_r60", "getNotificationTitle", "MessageLayoutComponent_div_40_div_1_Template", "MessageLayoutComponent_div_40_div_2_Template", "MessageLayoutComponent_div_40_div_3_Template", "ctx_r6", "isLoadingNotifications", "trackByNotificationId", "MessageLayoutComponent", "messageService", "authService", "toastService", "themeService", "mockDataService", "route", "router", "cdr", "activeTab", "isMobileMenuOpen", "showThemeSelector", "searchQuery", "conversationsPage", "usersPage", "subscriptions", "searchQuery$", "ngOnInit", "initializeComponent", "setupSubscriptions", "loadInitialData", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "navigate", "getCurrentTheme", "getAvailableThemes", "params", "subscribe", "markConversationAsSelected", "messagesSub", "subscribeToMessages", "handleNewMessage", "notificationsSub", "subscribeToNotifications", "notification", "handleNewNotification", "searchSub", "performSearch", "themeSub", "currentTheme$", "theme", "detectChanges", "loadConversations", "loadUsers", "loadNotifications", "page", "warn", "mockError", "getAllUsers", "conversationIndex", "findIndex", "splice", "switchTab", "tab", "createOrGetConversation", "toggleMobileMenu", "onSearchInput", "event", "target", "trim", "otherParticipant", "date", "diffInHours", "getTime", "toLocaleTimeString", "hour", "minute", "toLocaleDateString", "weekday", "day", "month", "index", "item", "isConversation", "themeName", "setTheme", "ɵɵdirectiveInject", "i1", "MessageService", "i2", "AuthService", "i3", "i4", "ThemeService", "i5", "i6", "ActivatedRoute", "Router", "ChangeDetectorRef", "selectors", "viewQuery", "MessageLayoutComponent_Query", "rf", "ctx", "MessageLayoutComponent_Template_button_click_13_listener", "MessageLayoutComponent_div_15_Template", "MessageLayoutComponent_Template_button_click_16_listener", "MessageLayoutComponent_Template_input_ngModelChange_19_listener", "$event", "MessageLayoutComponent_Template_input_input_19_listener", "MessageLayoutComponent_button_22_Template", "MessageLayoutComponent_Template_button_click_24_listener", "MessageLayoutComponent_Template_button_click_28_listener", "MessageLayoutComponent_Template_button_click_32_listener", "MessageLayoutComponent_span_36_Template", "MessageLayoutComponent_div_38_Template", "MessageLayoutComponent_div_39_Template", "MessageLayoutComponent_div_40_Template", "MessageLayoutComponent_Template_button_click_43_listener", "asyncScheduler", "<PERSON><PERSON>hen", "timer", "due", "scheduler", "concat", "take", "ignoreElements", "mapTo", "mergeMap", "innerFrom", "delayDurationSelector", "subscriptionDelay", "source", "operate", "createOperatorSubscriber", "noop", "subscriber"], "sourceRoot": "webpack:///", "x_google_ignoreList": [4, 5, 6]}