{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@app/services/planning.service\";\nimport * as i3 from \"@app/services/data.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@app/services/toast.service\";\nfunction PlanningEditComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.error, \" \");\n  }\n}\nfunction PlanningEditComponent_div_20_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Le titre est obligatoire\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PlanningEditComponent_div_20_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Au moins 3 caract\\u00E8res requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PlanningEditComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵelement(1, \"i\", 51);\n    i0.ɵɵtemplate(2, PlanningEditComponent_div_20_span_2_Template, 2, 0, \"span\", 52);\n    i0.ɵɵtemplate(3, PlanningEditComponent_div_20_span_3_Template, 2, 0, \"span\", 52);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r1.planningForm.get(\"titre\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r1.planningForm.get(\"titre\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"minlength\"]);\n  }\n}\nfunction PlanningEditComponent_option_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r8._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", user_r8.username, \" \");\n  }\n}\nfunction PlanningEditComponent_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵelement(1, \"i\", 51);\n    i0.ɵɵtext(2, \" Veuillez s\\u00E9lectionner au moins un participant \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PlanningEditComponent_i_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 54);\n  }\n}\nfunction PlanningEditComponent_i_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 55);\n  }\n}\nexport let PlanningEditComponent = /*#__PURE__*/(() => {\n  class PlanningEditComponent {\n    constructor(fb, planningService, userService, route, router, toastService) {\n      this.fb = fb;\n      this.planningService = planningService;\n      this.userService = userService;\n      this.route = route;\n      this.router = router;\n      this.toastService = toastService;\n      this.users$ = this.userService.getAllUsers();\n      this.error = '';\n      this.isLoading = false;\n    }\n    ngOnInit() {\n      this.planningId = this.route.snapshot.paramMap.get('id');\n      this.initForm();\n      this.loadPlanning();\n    }\n    initForm() {\n      this.planningForm = this.fb.group({\n        titre: ['', [Validators.required, Validators.minLength(3)]],\n        description: [''],\n        dateDebut: ['', Validators.required],\n        dateFin: ['', Validators.required],\n        lieu: [''],\n        participants: [[], Validators.required] // FormArray for multiple participants\n      });\n    }\n\n    loadPlanning() {\n      this.planningService.getPlanningById(this.planningId).subscribe({\n        next: response => {\n          const planning = response.planning;\n          this.planningForm.patchValue({\n            titre: planning.titre,\n            description: planning.description,\n            dateDebut: planning.dateDebut,\n            dateFin: planning.dateFin,\n            lieu: planning.lieu\n          });\n          const participantsArray = this.planningForm.get('participants');\n          participantsArray.clear();\n          planning.participants.forEach(p => {\n            participantsArray.push(this.fb.control(p._id));\n          });\n        },\n        error: err => {\n          console.error('Erreur lors du chargement du planning:', err);\n          if (err.status === 403) {\n            this.toastService.showError(\"Accès refusé : vous n'avez pas les droits pour accéder à ce planning\");\n          } else if (err.status === 404) {\n            this.toastService.showError(\"Le planning demandé n'existe pas ou a été supprimé\");\n          } else {\n            const errorMessage = err.error?.message || 'Erreur lors du chargement du planning';\n            this.toastService.showError(errorMessage);\n          }\n        }\n      });\n    }\n    onSubmit() {\n      if (this.planningForm.invalid) {\n        console.log('Formulaire invalide, soumission annulée');\n        // Marquer tous les champs comme \"touched\" pour afficher les erreurs\n        this.markFormGroupTouched();\n        this.toastService.showWarning('Veuillez corriger les erreurs avant de soumettre le formulaire');\n        return;\n      }\n      this.isLoading = true;\n      const formValue = this.planningForm.value;\n      console.log('Données du formulaire à soumettre:', formValue);\n      // Vérifier que les dates sont au bon format\n      let dateDebut = formValue.dateDebut;\n      let dateFin = formValue.dateFin;\n      // S'assurer que les dates sont des objets Date\n      if (typeof dateDebut === 'string') {\n        dateDebut = new Date(dateDebut);\n      }\n      if (typeof dateFin === 'string') {\n        dateFin = new Date(dateFin);\n      }\n      // Créer un objet avec seulement les propriétés à mettre à jour\n      // sans utiliser le type Planning complet pour éviter les erreurs de typage\n      const updatedPlanning = {\n        titre: formValue.titre,\n        description: formValue.description || '',\n        lieu: formValue.lieu || '',\n        dateDebut: dateDebut,\n        dateFin: dateFin,\n        participants: formValue.participants || []\n      };\n      console.log('Mise à jour du planning avec ID:', this.planningId);\n      console.log('Données formatées:', updatedPlanning);\n      try {\n        this.planningService.updatePlanning(this.planningId, updatedPlanning).subscribe({\n          next: response => {\n            console.log('Planning mis à jour avec succès:', response);\n            this.isLoading = false;\n            // Afficher un toast de succès\n            this.toastService.showSuccess('Le planning a été modifié avec succès');\n            // Redirection vers la page de détail du planning\n            console.log('Redirection vers la page de détail du planning:', this.planningId);\n            // Utiliser setTimeout pour s'assurer que la redirection se produit après le traitement\n            setTimeout(() => {\n              this.router.navigate(['/plannings', this.planningId]).then(navigated => console.log('Redirection réussie:', navigated), err => console.error('Erreur de redirection:', err));\n            }, 100);\n          },\n          error: err => {\n            this.isLoading = false;\n            console.error('Erreur lors de la mise à jour du planning:', err);\n            // Gestion spécifique des erreurs d'autorisation\n            if (err.status === 403) {\n              this.toastService.showError(\"Accès refusé : vous n'avez pas les droits pour modifier ce planning\");\n            } else if (err.status === 401) {\n              this.toastService.showError('Vous devez être connecté pour effectuer cette action');\n            } else {\n              // Autres erreurs\n              const errorMessage = err.error?.message || 'Erreur lors de la mise à jour du planning';\n              this.toastService.showError(errorMessage, 8000);\n            }\n            // Afficher plus de détails sur l'erreur dans la console\n            if (err.error) {\n              console.error(\"Détails de l'erreur:\", err.error);\n            }\n          }\n        });\n      } catch (e) {\n        this.isLoading = false;\n        const errorMessage = e instanceof Error ? e.message : String(e);\n        this.toastService.showError(`Exception lors de la mise à jour: ${errorMessage}`);\n        console.error('Exception lors de la mise à jour:', e);\n      }\n    }\n    // Marquer tous les champs comme \"touched\" pour déclencher l'affichage des erreurs\n    markFormGroupTouched() {\n      Object.keys(this.planningForm.controls).forEach(key => {\n        const control = this.planningForm.get(key);\n        if (control) {\n          control.markAsTouched();\n        }\n      });\n    }\n    static {\n      this.ɵfac = function PlanningEditComponent_Factory(t) {\n        return new (t || PlanningEditComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.PlanningService), i0.ɵɵdirectiveInject(i3.DataService), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.ToastService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: PlanningEditComponent,\n        selectors: [[\"app-planning-edit\"]],\n        decls: 71,\n        vars: 13,\n        consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-6\", \"max-w-3xl\"], [1, \"bg-gradient-to-r\", \"from-purple-600\", \"to-indigo-600\", \"rounded-t-lg\", \"p-6\", \"text-white\", \"mb-0\"], [1, \"text-2xl\", \"font-bold\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-edit\", \"mr-3\", \"text-purple-200\"], [1, \"text-purple-100\", \"mt-2\"], [\"novalidate\", \"\", 1, \"bg-white\", \"rounded-b-lg\", \"shadow-lg\", \"p-6\", \"border-t-0\", 3, \"formGroup\", \"ngSubmit\"], [\"class\", \"mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\", 4, \"ngIf\"], [1, \"grid\", \"grid-cols-1\", \"gap-6\"], [1, \"bg-gradient-to-r\", \"from-purple-50\", \"to-pink-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-purple-200\"], [1, \"text-lg\", \"font-semibold\", \"text-purple-800\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-info-circle\", \"mr-2\", \"text-purple-600\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-6\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-purple-700\", \"mb-2\"], [1, \"fas\", \"fa-tag\", \"mr-2\", \"text-purple-500\"], [\"type\", \"text\", \"formControlName\", \"titre\", \"placeholder\", \"Nom de votre planning...\", 1, \"mt-1\", \"block\", \"w-full\", \"px-4\", \"py-3\", \"border-2\", \"border-purple-200\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\"], [\"class\", \"text-red-500 text-sm mt-2 flex items-center\", 4, \"ngIf\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-orange-700\", \"mb-2\"], [1, \"fas\", \"fa-map-marker-alt\", \"mr-2\", \"text-orange-500\"], [\"type\", \"text\", \"formControlName\", \"lieu\", \"placeholder\", \"Salle, bureau, lieu de l'\\u00E9v\\u00E9nement...\", 1, \"mt-1\", \"block\", \"w-full\", \"px-4\", \"py-3\", \"border-2\", \"border-orange-200\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-orange-500\", \"focus:border-orange-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\"], [1, \"bg-gradient-to-r\", \"from-blue-50\", \"to-cyan-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-blue-200\"], [1, \"text-lg\", \"font-semibold\", \"text-blue-800\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-calendar-week\", \"mr-2\", \"text-blue-600\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-green-700\", \"mb-2\"], [1, \"fas\", \"fa-calendar-day\", \"mr-2\", \"text-green-500\"], [\"type\", \"date\", \"formControlName\", \"dateDebut\", 1, \"mt-1\", \"block\", \"w-full\", \"px-4\", \"py-3\", \"border-2\", \"border-green-200\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-green-500\", \"focus:border-green-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-red-700\", \"mb-2\"], [1, \"fas\", \"fa-calendar-check\", \"mr-2\", \"text-red-500\"], [\"type\", \"date\", \"formControlName\", \"dateFin\", 1, \"mt-1\", \"block\", \"w-full\", \"px-4\", \"py-3\", \"border-2\", \"border-red-200\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-red-500\", \"focus:border-red-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\"], [1, \"bg-gradient-to-r\", \"from-indigo-50\", \"to-purple-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-indigo-200\"], [1, \"text-lg\", \"font-semibold\", \"text-indigo-800\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-align-left\", \"mr-2\", \"text-indigo-600\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-indigo-700\", \"mb-2\"], [1, \"fas\", \"fa-edit\", \"mr-2\", \"text-indigo-500\"], [\"formControlName\", \"description\", \"rows\", \"4\", \"placeholder\", \"D\\u00E9crivez les objectifs, le contexte ou les d\\u00E9tails de ce planning...\", 1, \"mt-1\", \"block\", \"w-full\", \"px-4\", \"py-3\", \"border-2\", \"border-indigo-200\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-indigo-500\", \"focus:border-indigo-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\"], [1, \"bg-gradient-to-r\", \"from-emerald-50\", \"to-teal-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-emerald-200\"], [1, \"text-lg\", \"font-semibold\", \"text-emerald-800\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-users\", \"mr-2\", \"text-emerald-600\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-emerald-700\", \"mb-2\"], [1, \"fas\", \"fa-user-friends\", \"mr-2\", \"text-emerald-500\"], [\"formControlName\", \"participants\", \"multiple\", \"\", 1, \"mt-1\", \"block\", \"w-full\", \"px-4\", \"py-3\", \"border-2\", \"border-emerald-200\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-emerald-500\", \"focus:border-emerald-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"text-sm\", \"min-h-[120px]\"], [\"class\", \"py-2\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"text-xs\", \"text-emerald-600\", \"mt-2\"], [1, \"fas\", \"fa-info-circle\", \"mr-1\"], [1, \"mt-8\", \"flex\", \"justify-end\", \"space-x-4\", \"bg-gray-50\", \"p-4\", \"rounded-lg\", \"border-t\", \"border-gray-200\"], [\"type\", \"button\", \"routerLink\", \"/plannings\", 1, \"px-6\", \"py-3\", \"border-2\", \"border-gray-300\", \"rounded-lg\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"hover:bg-gray-100\", \"hover:border-gray-400\", \"transition-all\", \"duration-200\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-times\", \"mr-2\"], [\"type\", \"button\", 1, \"px-6\", \"py-3\", \"rounded-lg\", \"text-sm\", \"font-medium\", \"text-white\", \"bg-gradient-to-r\", \"from-purple-600\", \"to-indigo-600\", \"hover:from-purple-700\", \"hover:to-indigo-700\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"transition-all\", \"duration-200\", \"flex\", \"items-center\", \"shadow-lg\", 3, \"disabled\", \"click\"], [\"class\", \"fas fa-save mr-2\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner fa-spin mr-2\", 4, \"ngIf\"], [1, \"mb-4\", \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\"], [1, \"text-red-500\", \"text-sm\", \"mt-2\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-exclamation-circle\", \"mr-1\"], [4, \"ngIf\"], [1, \"py-2\", 3, \"value\"], [1, \"fas\", \"fa-save\", \"mr-2\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"mr-2\"]],\n        template: function PlanningEditComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\", 2);\n            i0.ɵɵelement(3, \"i\", 3);\n            i0.ɵɵtext(4, \" Modifier le Planning \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"p\", 4);\n            i0.ɵɵtext(6, \"Modifiez les d\\u00E9tails de votre planning\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"form\", 5);\n            i0.ɵɵlistener(\"ngSubmit\", function PlanningEditComponent_Template_form_ngSubmit_7_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵtemplate(8, PlanningEditComponent_div_8_Template, 2, 1, \"div\", 6);\n            i0.ɵɵelementStart(9, \"div\", 7)(10, \"div\", 8)(11, \"h3\", 9);\n            i0.ɵɵelement(12, \"i\", 10);\n            i0.ɵɵtext(13, \" Informations g\\u00E9n\\u00E9rales \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"div\", 11)(15, \"div\")(16, \"label\", 12);\n            i0.ɵɵelement(17, \"i\", 13);\n            i0.ɵɵtext(18, \" Titre * \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(19, \"input\", 14);\n            i0.ɵɵtemplate(20, PlanningEditComponent_div_20_Template, 4, 2, \"div\", 15);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"div\")(22, \"label\", 16);\n            i0.ɵɵelement(23, \"i\", 17);\n            i0.ɵɵtext(24, \" Lieu / Salle \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(25, \"input\", 18);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(26, \"div\", 19)(27, \"h3\", 20);\n            i0.ɵɵelement(28, \"i\", 21);\n            i0.ɵɵtext(29, \" P\\u00E9riode du planning \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(30, \"div\", 11)(31, \"div\")(32, \"label\", 22);\n            i0.ɵɵelement(33, \"i\", 23);\n            i0.ɵɵtext(34, \" Date de d\\u00E9but * \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(35, \"input\", 24);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(36, \"div\")(37, \"label\", 25);\n            i0.ɵɵelement(38, \"i\", 26);\n            i0.ɵɵtext(39, \" Date de fin * \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(40, \"input\", 27);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(41, \"div\", 28)(42, \"h3\", 29);\n            i0.ɵɵelement(43, \"i\", 30);\n            i0.ɵɵtext(44, \" Description \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(45, \"label\", 31);\n            i0.ɵɵelement(46, \"i\", 32);\n            i0.ɵɵtext(47, \" D\\u00E9crivez votre planning \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(48, \"textarea\", 33);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"div\", 34)(50, \"h3\", 35);\n            i0.ɵɵelement(51, \"i\", 36);\n            i0.ɵɵtext(52, \" Participants \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(53, \"label\", 37);\n            i0.ɵɵelement(54, \"i\", 38);\n            i0.ɵɵtext(55, \" S\\u00E9lectionnez les participants * \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(56, \"select\", 39);\n            i0.ɵɵtemplate(57, PlanningEditComponent_option_57_Template, 2, 2, \"option\", 40);\n            i0.ɵɵpipe(58, \"async\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(59, PlanningEditComponent_div_59_Template, 3, 0, \"div\", 15);\n            i0.ɵɵelementStart(60, \"p\", 41);\n            i0.ɵɵelement(61, \"i\", 42);\n            i0.ɵɵtext(62, \" Maintenez Ctrl (ou Cmd) pour s\\u00E9lectionner plusieurs participants \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(63, \"div\", 43)(64, \"button\", 44);\n            i0.ɵɵelement(65, \"i\", 45);\n            i0.ɵɵtext(66, \" Annuler \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(67, \"button\", 46);\n            i0.ɵɵlistener(\"click\", function PlanningEditComponent_Template_button_click_67_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵtemplate(68, PlanningEditComponent_i_68_Template, 1, 0, \"i\", 47);\n            i0.ɵɵtemplate(69, PlanningEditComponent_i_69_Template, 1, 0, \"i\", 48);\n            i0.ɵɵtext(70);\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            let tmp_2_0;\n            let tmp_3_0;\n            let tmp_5_0;\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"formGroup\", ctx.planningForm);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(11);\n            i0.ɵɵclassProp(\"border-red-300\", ((tmp_2_0 = ctx.planningForm.get(\"titre\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.planningForm.get(\"titre\")) == null ? null : tmp_2_0.touched));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx.planningForm.get(\"titre\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.planningForm.get(\"titre\")) == null ? null : tmp_3_0.touched));\n            i0.ɵɵadvance(37);\n            i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(58, 11, ctx.users$));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.planningForm.get(\"participants\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.planningForm.get(\"participants\")) == null ? null : tmp_5_0.touched));\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"disabled\", ctx.isLoading || ctx.planningForm.invalid);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵtextInterpolate1(\" \", ctx.isLoading ? \"Enregistrement...\" : \"Enregistrer les modifications\", \" \");\n          }\n        }\n      });\n    }\n  }\n  return PlanningEditComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}