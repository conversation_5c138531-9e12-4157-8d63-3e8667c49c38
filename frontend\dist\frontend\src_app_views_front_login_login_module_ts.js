"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([["src_app_views_front_login_login_module_ts"],{

/***/ 1645:
/*!***********************************************************!*\
  !*** ./src/app/views/front/login/login-routing.module.ts ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LoginRoutingModule: () => (/* binding */ LoginRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _login_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./login.component */ 5073);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);




const routes = [{
  path: '',
  component: _login_component__WEBPACK_IMPORTED_MODULE_0__.LoginComponent
}];
class LoginRoutingModule {
  static {
    this.ɵfac = function LoginRoutingModule_Factory(t) {
      return new (t || LoginRoutingModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineNgModule"]({
      type: LoginRoutingModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjector"]({
      imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵsetNgModuleScope"](LoginRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
  });
})();

/***/ }),

/***/ 5073:
/*!******************************************************!*\
  !*** ./src/app/views/front/login/login.component.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LoginComponent: () => (/* binding */ LoginComponent)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _services_auth_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../services/auth.service */ 4796);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 316);






function LoginComponent_div_36_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](1, "i", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](2, " Email is required ");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
function LoginComponent_div_45_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](1, "i", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](2, " Password is required ");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
function LoginComponent_div_46_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 40)(1, "div", 41)(2, "div", 42);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](3, "i", 43)(4, "div", 44);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](5, "div", 45)(6, "p", 46);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"](" ", ctx_r2.error, " ");
  }
}
function LoginComponent_div_47_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 47)(1, "div", 41)(2, "div", 48);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](3, "i", 49)(4, "div", 50);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](5, "div", 45)(6, "p", 51);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"](" ", ctx_r3.message, " ");
  }
}
function LoginComponent_span_52_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "span", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](1, "i", 53);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](2, " Sign In ");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
function LoginComponent_span_53_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "span", 54);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](1, "svg", 55);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](2, "circle", 56)(3, "path", 57);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](4, " Signing in... ");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
const _c0 = function (a0) {
  return {
    "shake-animation": a0
  };
};
class LoginComponent {
  constructor(fb, authService, router) {
    this.fb = fb;
    this.authService = authService;
    this.router = router;
    this.message = '';
    this.error = '';
    this.isLoading = false;
    this.loginForm = this.fb.group({
      email: ['', [_angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.email]],
      password: ['', _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required]
    });
  }
  onSubmit() {
    if (this.loginForm.invalid) return;
    // Reset previous error messages
    this.error = '';
    this.message = '';
    this.isLoading = true;
    this.authService.login(this.loginForm.value).subscribe({
      next: res => {
        this.isLoading = false;
        localStorage.setItem('token', res.token);
        localStorage.setItem('user', JSON.stringify(res.user));
        this.router.navigate(['/']);
      },
      error: err => {
        this.isLoading = false;
        // Handle authentication errors
        if (err.status === 401) {
          this.error = 'Email ou mot de passe incorrect. Veuillez réessayer.';
        } else if (err.status === 403) {
          this.error = "Votre compte n'est pas vérifié. Veuillez vérifier votre email.";
        } else {
          this.error = err.error?.message || 'Une erreur est survenue lors de la connexion. Veuillez réessayer.';
        }
      }
    });
  }
  static {
    this.ɵfac = function LoginComponent_Factory(t) {
      return new (t || LoginComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormBuilder), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_services_auth_service__WEBPACK_IMPORTED_MODULE_0__.AuthService), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_3__.Router));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
      type: LoginComponent,
      selectors: [["app-login"]],
      decls: 63,
      vars: 11,
      consts: [[1, "min-h-screen", "bg-[#edf1f4]", "dark:bg-[#121212]", "flex", "items-center", "justify-center", "p-4", "relative", "futuristic-layout"], [1, "absolute", "inset-0", "overflow-hidden", "pointer-events-none"], [1, "absolute", "top-[15%]", "left-[10%]", "w-64", "h-64", "rounded-full", "bg-gradient-to-br", "from-[#4f5fad]/5", "to-transparent", "dark:from-[#6d78c9]/3", "dark:to-transparent", "blur-3xl"], [1, "absolute", "bottom-[20%]", "right-[10%]", "w-80", "h-80", "rounded-full", "bg-gradient-to-tl", "from-[#4f5fad]/5", "to-transparent", "dark:from-[#6d78c9]/3", "dark:to-transparent", "blur-3xl"], [1, "absolute", "inset-0", "opacity-5", "dark:opacity-[0.03]"], [1, "h-full", "grid", "grid-cols-12"], [1, "border-r", "border-[#4f5fad]", "dark:border-[#6d78c9]"], [1, "w-full", "max-w-md", "relative", "z-10"], [1, "bg-white", "dark:bg-[#1e1e1e]", "rounded-xl", "shadow-lg", "dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]", "overflow-hidden", "backdrop-blur-sm", "border", "border-[#edf1f4]/50", "dark:border-[#2a2a2a]", "relative"], [1, "absolute", "top-0", "left-0", "right-0", "h-1", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]"], [1, "absolute", "top-0", "left-0", "right-0", "h-1", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "blur-md"], [1, "p-6", "text-center"], [1, "text-2xl", "font-bold", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "bg-clip-text", "text-transparent"], [1, "text-sm", "text-[#6d6870]", "dark:text-[#a0a0a0]", "mt-2"], [1, "p-6"], [1, "space-y-5", 3, "formGroup", "ngClass", "ngSubmit"], [1, "group"], ["for", "email", 1, "flex", "items-center", "text-sm", "font-medium", "text-[#4f5fad]", "dark:text-[#6d78c9]", "mb-2"], [1, "fas", "fa-envelope", "mr-1.5", "text-xs"], [1, "relative"], ["id", "email", "type", "email", "formControlName", "email", "required", "", "placeholder", "<EMAIL>", 1, "w-full", "px-4", "py-2.5", "text-sm", "rounded-lg", "border", "border-[#bdc6cc]", "dark:border-[#2a2a2a]", "bg-white", "dark:bg-[#1e1e1e]", "text-[#6d6870]", "dark:text-[#e0e0e0]", "focus:outline-none", "focus:border-[#4f5fad]", "dark:focus:border-[#6d78c9]", "focus:ring-2", "focus:ring-[#4f5fad]/20", "dark:focus:ring-[#6d78c9]/20", "transition-all"], [1, "absolute", "inset-y-0", "left-0", "pl-3", "flex", "items-center", "pointer-events-none", "opacity-0", "group-focus-within:opacity-100", "transition-opacity"], [1, "w-0.5", "h-4", "bg-gradient-to-b", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "rounded-full"], ["class", "text-[#ff6b69] dark:text-[#ff8785] text-xs mt-1.5 flex items-center", 4, "ngIf"], ["for", "password", 1, "flex", "items-center", "text-sm", "font-medium", "text-[#4f5fad]", "dark:text-[#6d78c9]", "mb-2"], [1, "fas", "fa-lock", "mr-1.5", "text-xs"], ["id", "password", "type", "password", "formControlName", "password", "required", "", "placeholder", "\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022", 1, "w-full", "px-4", "py-2.5", "text-sm", "rounded-lg", "border", "border-[#bdc6cc]", "dark:border-[#2a2a2a]", "bg-white", "dark:bg-[#1e1e1e]", "text-[#6d6870]", "dark:text-[#e0e0e0]", "focus:outline-none", "focus:border-[#4f5fad]", "dark:focus:border-[#6d78c9]", "focus:ring-2", "focus:ring-[#4f5fad]/20", "dark:focus:ring-[#6d78c9]/20", "transition-all"], ["class", "bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-3 backdrop-blur-sm", 4, "ngIf"], ["class", "bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-3 backdrop-blur-sm", 4, "ngIf"], ["type", "submit", 1, "w-full", "relative", "overflow-hidden", "group", "mt-6", 3, "disabled"], [1, "absolute", "inset-0", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#3d4a85]", "dark:to-[#6d78c9]", "rounded-lg", "transition-transform", "duration-300", "group-hover:scale-105", "disabled:opacity-50"], [1, "absolute", "inset-0", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#3d4a85]", "dark:to-[#6d78c9]", "rounded-lg", "opacity-0", "group-hover:opacity-100", "blur-xl", "transition-opacity", "duration-300", "disabled:opacity-0"], [1, "relative", "flex", "items-center", "justify-center", "text-white", "font-medium", "py-2.5", "px-4", "rounded-lg", "transition-all", "z-10"], ["class", "flex items-center", 4, "ngIf"], ["class", "flex items-center justify-center", 4, "ngIf"], [1, "text-center", "text-sm", "text-[#6d6870]", "dark:text-[#a0a0a0]", "space-y-2", "pt-4"], ["routerLink", "/forgot-password", 1, "text-[#4f5fad]", "dark:text-[#6d78c9]", "hover:text-[#3d4a85]", "dark:hover:text-[#4f5fad]", "transition-colors", "font-medium"], ["routerLink", "/signup", 1, "text-[#4f5fad]", "dark:text-[#6d78c9]", "hover:text-[#3d4a85]", "dark:hover:text-[#4f5fad]", "transition-colors", "font-medium"], [1, "text-[#ff6b69]", "dark:text-[#ff8785]", "text-xs", "mt-1.5", "flex", "items-center"], [1, "fas", "fa-exclamation-circle", "mr-1"], [1, "bg-[#ff6b69]/10", "dark:bg-[#ff6b69]/5", "border", "border-[#ff6b69]", "dark:border-[#ff6b69]/30", "rounded-lg", "p-3", "backdrop-blur-sm"], [1, "flex", "items-start"], [1, "text-[#ff6b69]", "dark:text-[#ff8785]", "mr-2", "text-base", "relative"], [1, "fas", "fa-exclamation-triangle"], [1, "absolute", "inset-0", "bg-[#ff6b69]/20", "dark:bg-[#ff8785]/20", "blur-xl", "rounded-full", "transform", "scale-150", "-z-10"], [1, "flex-1"], [1, "text-xs", "text-[#ff6b69]", "dark:text-[#ff8785]"], [1, "bg-[#4f5fad]/10", "dark:bg-[#6d78c9]/5", "border", "border-[#4f5fad]", "dark:border-[#6d78c9]/30", "rounded-lg", "p-3", "backdrop-blur-sm"], [1, "text-[#4f5fad]", "dark:text-[#6d78c9]", "mr-2", "text-base", "relative"], [1, "fas", "fa-check-circle"], [1, "absolute", "inset-0", "bg-[#4f5fad]/20", "dark:bg-[#6d78c9]/20", "blur-xl", "rounded-full", "transform", "scale-150", "-z-10"], [1, "text-xs", "text-[#4f5fad]", "dark:text-[#6d78c9]"], [1, "flex", "items-center"], [1, "fas", "fa-sign-in-alt", "mr-2"], [1, "flex", "items-center", "justify-center"], ["xmlns", "http://www.w3.org/2000/svg", "fill", "none", "viewBox", "0 0 24 24", 1, "animate-spin", "mr-2", "h-4", "w-4", "text-white"], ["cx", "12", "cy", "12", "r", "10", "stroke", "currentColor", "stroke-width", "4", 1, "opacity-25"], ["fill", "currentColor", "d", "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z", 1, "opacity-75"]],
      template: function LoginComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 0)(1, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](2, "div", 2)(3, "div", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](4, "div", 4)(5, "div", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](6, "div", 6)(7, "div", 6)(8, "div", 6)(9, "div", 6)(10, "div", 6)(11, "div", 6)(12, "div", 6)(13, "div", 6)(14, "div", 6)(15, "div", 6)(16, "div", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](17, "div", 7)(18, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](19, "div", 9)(20, "div", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](21, "div", 11)(22, "h1", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](23, " Welcome Back ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](24, "p", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](25, " Sign in to access your workspace ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](26, "div", 14)(27, "form", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("ngSubmit", function LoginComponent_Template_form_ngSubmit_27_listener() {
            return ctx.onSubmit();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](28, "div", 16)(29, "label", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](30, "i", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](31, " Email ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](32, "div", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](33, "input", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](34, "div", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](35, "div", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](36, LoginComponent_div_36_Template, 3, 0, "div", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](37, "div", 16)(38, "label", 24);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](39, "i", 25);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](40, " Password ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](41, "div", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](42, "input", 26);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](43, "div", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](44, "div", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](45, LoginComponent_div_45_Template, 3, 0, "div", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](46, LoginComponent_div_46_Template, 8, 1, "div", 27);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](47, LoginComponent_div_47_Template, 8, 1, "div", 28);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](48, "button", 29);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](49, "div", 30)(50, "div", 31);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](51, "span", 32);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](52, LoginComponent_span_52_Template, 3, 0, "span", 33);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](53, LoginComponent_span_53_Template, 5, 0, "span", 34);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](54, "div", 35)(55, "div");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](56, " Forgot your password? ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](57, "a", 36);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](58, " Reset it ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](59, "div");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](60, " Don't have an account? ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](61, "a", 37);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](62, " Sign up ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()()()()()()();
        }
        if (rf & 2) {
          let tmp_2_0;
          let tmp_3_0;
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](27);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("formGroup", ctx.loginForm)("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵpureFunction1"](9, _c0, ctx.error));
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](9);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ((tmp_2_0 = ctx.loginForm.get("email")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.loginForm.get("email")) == null ? null : tmp_2_0.touched));
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](9);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ((tmp_3_0 = ctx.loginForm.get("password")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.loginForm.get("password")) == null ? null : tmp_3_0.touched));
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.error);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.message);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("disabled", ctx.loginForm.invalid || ctx.isLoading);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", !ctx.isLoading);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.isLoading);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_4__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_2__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_2__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.RequiredValidator, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormControlName, _angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterLink],
      styles: ["@keyframes _ngcontent-%COMP%_shake {\n        0%,\n        100% {\n          transform: translateX(0);\n        }\n        10%,\n        30%,\n        50%,\n        70%,\n        90% {\n          transform: translateX(-5px);\n        }\n        20%,\n        40%,\n        60%,\n        80% {\n          transform: translateX(5px);\n        }\n      }\n      .shake-animation[_ngcontent-%COMP%] {\n        animation: _ngcontent-%COMP%_shake 0.6s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;\n      }\n    \n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImxvZ2luLmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO01BQ007UUFDRTs7VUFFRSx3QkFBd0I7UUFDMUI7UUFDQTs7Ozs7VUFLRSwyQkFBMkI7UUFDN0I7UUFDQTs7OztVQUlFLDBCQUEwQjtRQUM1QjtNQUNGO01BQ0E7UUFDRSwrREFBK0Q7TUFDakUiLCJmaWxlIjoibG9naW4uY29tcG9uZW50LnRzIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgICBAa2V5ZnJhbWVzIHNoYWtlIHtcbiAgICAgICAgMCUsXG4gICAgICAgIDEwMCUge1xuICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgwKTtcbiAgICAgICAgfVxuICAgICAgICAxMCUsXG4gICAgICAgIDMwJSxcbiAgICAgICAgNTAlLFxuICAgICAgICA3MCUsXG4gICAgICAgIDkwJSB7XG4gICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKC01cHgpO1xuICAgICAgICB9XG4gICAgICAgIDIwJSxcbiAgICAgICAgNDAlLFxuICAgICAgICA2MCUsXG4gICAgICAgIDgwJSB7XG4gICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKDVweCk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIC5zaGFrZS1hbmltYXRpb24ge1xuICAgICAgICBhbmltYXRpb246IHNoYWtlIDAuNnMgY3ViaWMtYmV6aWVyKDAuMzYsIDAuMDcsIDAuMTksIDAuOTcpIGJvdGg7XG4gICAgICB9XG4gICAgIl19 */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvbG9naW4vbG9naW4uY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7TUFDTTtRQUNFOztVQUVFLHdCQUF3QjtRQUMxQjtRQUNBOzs7OztVQUtFLDJCQUEyQjtRQUM3QjtRQUNBOzs7O1VBSUUsMEJBQTBCO1FBQzVCO01BQ0Y7TUFDQTtRQUNFLCtEQUErRDtNQUNqRTs7QUFFTix3OEJBQXc4QiIsInNvdXJjZXNDb250ZW50IjpbIlxuICAgICAgQGtleWZyYW1lcyBzaGFrZSB7XG4gICAgICAgIDAlLFxuICAgICAgICAxMDAlIHtcbiAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoMCk7XG4gICAgICAgIH1cbiAgICAgICAgMTAlLFxuICAgICAgICAzMCUsXG4gICAgICAgIDUwJSxcbiAgICAgICAgNzAlLFxuICAgICAgICA5MCUge1xuICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgtNXB4KTtcbiAgICAgICAgfVxuICAgICAgICAyMCUsXG4gICAgICAgIDQwJSxcbiAgICAgICAgNjAlLFxuICAgICAgICA4MCUge1xuICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCg1cHgpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICAuc2hha2UtYW5pbWF0aW9uIHtcbiAgICAgICAgYW5pbWF0aW9uOiBzaGFrZSAwLjZzIGN1YmljLWJlemllcigwLjM2LCAwLjA3LCAwLjE5LCAwLjk3KSBib3RoO1xuICAgICAgfVxuICAgICJdLCJzb3VyY2VSb290IjoiIn0= */"]
    });
  }
}

/***/ }),

/***/ 1764:
/*!***************************************************!*\
  !*** ./src/app/views/front/login/login.module.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LoginModule: () => (/* binding */ LoginModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _login_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./login-routing.module */ 1645);
/* harmony import */ var _login_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./login.component */ 5073);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 7580);





class LoginModule {
  static {
    this.ɵfac = function LoginModule_Factory(t) {
      return new (t || LoginModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineNgModule"]({
      type: LoginModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineInjector"]({
      imports: [_angular_common__WEBPACK_IMPORTED_MODULE_3__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.ReactiveFormsModule, _login_routing_module__WEBPACK_IMPORTED_MODULE_0__.LoginRoutingModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵsetNgModuleScope"](LoginModule, {
    declarations: [_login_component__WEBPACK_IMPORTED_MODULE_1__.LoginComponent],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_3__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.ReactiveFormsModule, _login_routing_module__WEBPACK_IMPORTED_MODULE_0__.LoginRoutingModule]
  });
})();

/***/ })

}]);
//# sourceMappingURL=src_app_views_front_login_login_module_ts.js.map