{"ast": null, "code": "import { CommonModule, DatePipe } from '@angular/common';\nimport { ReunionsRoutingModule } from './reunions-routing.module';\nimport { ReunionListComponent } from './reunion-list/reunion-list.component';\nimport { ReunionDetailComponent } from './reunion-detail/reunion-detail.component';\nimport { ReunionFormComponent } from './reunion-form/reunion-form.component';\nimport { RouterModule } from '@angular/router';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { PipesModule } from '@app/pipes/pipes.module';\nimport { ReunionEditComponent } from './reunion-edit/reunion-edit.component';\nimport * as i0 from \"@angular/core\";\nexport class ReunionsModule {\n  static {\n    this.ɵfac = function ReunionsModule_Factory(t) {\n      return new (t || ReunionsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ReunionsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [DatePipe],\n      imports: [CommonModule, ReunionsRoutingModule, RouterModule, FormsModule, ReactiveFormsModule, PipesModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ReunionsModule, {\n    declarations: [ReunionListComponent, ReunionDetailComponent, ReunionFormComponent, ReunionEditComponent],\n    imports: [CommonModule, ReunionsRoutingModule, RouterModule, FormsModule, ReactiveFormsModule, PipesModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "DatePipe", "ReunionsRoutingModule", "ReunionListComponent", "ReunionDetailComponent", "ReunionFormComponent", "RouterModule", "FormsModule", "ReactiveFormsModule", "PipesModule", "ReunionEditComponent", "ReunionsModule", "imports", "declarations"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\reunions\\reunions.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule, DatePipe } from '@angular/common';\n\nimport { ReunionsRoutingModule } from './reunions-routing.module';\nimport { ReunionListComponent } from './reunion-list/reunion-list.component';\nimport { ReunionDetailComponent } from './reunion-detail/reunion-detail.component';\nimport { ReunionFormComponent } from './reunion-form/reunion-form.component';\nimport { RouterModule } from '@angular/router';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\n\nimport { PipesModule } from '@app/pipes/pipes.module';\nimport { ReunionEditComponent } from './reunion-edit/reunion-edit.component';\n\n@NgModule({\n  declarations: [\n    ReunionListComponent,\n    ReunionDetailComponent,\n    ReunionFormComponent,\n    ReunionEditComponent,\n  ],\n  imports: [\n    CommonModule,\n    ReunionsRoutingModule,\n    RouterModule,\n    FormsModule,\n    ReactiveFormsModule,\n    PipesModule,\n  ],\n  providers: [DatePipe],\n})\nexport class ReunionsModule {}\n"], "mappings": "AACA,SAASA,YAAY,EAAEC,QAAQ,QAAQ,iBAAiB;AAExD,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAEjE,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,oBAAoB,QAAQ,uCAAuC;;AAmB5E,OAAM,MAAOC,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA;IAAc;EAAA;;;iBAFd,CAACV,QAAQ,CAAC;MAAAW,OAAA,GAPnBZ,YAAY,EACZE,qBAAqB,EACrBI,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,WAAW;IAAA;EAAA;;;2EAIFE,cAAc;IAAAE,YAAA,GAfvBV,oBAAoB,EACpBC,sBAAsB,EACtBC,oBAAoB,EACpBK,oBAAoB;IAAAE,OAAA,GAGpBZ,YAAY,EACZE,qBAAqB,EACrBI,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,WAAW;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}