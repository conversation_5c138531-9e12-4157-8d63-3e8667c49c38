{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { MessageChatComponent } from './message-chat/message-chat.component';\nimport { MessageLayoutComponent } from './message-layout/message-layout.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: MessageLayoutComponent,\n  children: [\n  // Route par défaut - affiche le layout sans conversation sélectionnée\n  {\n    path: '',\n    component: MessageChatComponent,\n    data: {\n      title: 'Messages'\n    }\n  },\n  // Route pour une conversation spécifique\n  {\n    path: ':conversationId',\n    component: MessageChatComponent,\n    data: {\n      title: 'Chat'\n    }\n  }]\n}];\nexport class MessagesRoutingModule {\n  static {\n    this.ɵfac = function MessagesRoutingModule_Factory(t) {\n      return new (t || MessagesRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: MessagesRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(MessagesRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "MessageChatComponent", "MessageLayoutComponent", "routes", "path", "component", "children", "data", "title", "MessagesRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\messages-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { MessageChatComponent } from './message-chat/message-chat.component';\nimport { MessagesListComponent } from './messages-list/messages-list.component';\nimport { UserListComponent } from './user-list/user-list.component';\nimport { MessageLayoutComponent } from './message-layout/message-layout.component';\n\nconst routes: Routes = [\n  {\n    path: '',\n    component: MessageLayoutComponent,\n    children: [\n      // Route par défaut - affiche le layout sans conversation sélectionnée\n      {\n        path: '',\n        component: MessageChatComponent,\n        data: { title: 'Messages' },\n      },\n      // Route pour une conversation spécifique\n      {\n        path: ':conversationId',\n        component: MessageChatComponent,\n        data: { title: 'Chat' },\n      },\n    ],\n  },\n];\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule],\n})\nexport class MessagesRoutingModule {}\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,oBAAoB,QAAQ,uCAAuC;AAG5E,SAASC,sBAAsB,QAAQ,2CAA2C;;;AAElF,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH,sBAAsB;EACjCI,QAAQ,EAAE;EACR;EACA;IACEF,IAAI,EAAE,EAAE;IACRC,SAAS,EAAEJ,oBAAoB;IAC/BM,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAU;GAC1B;EACD;EACA;IACEJ,IAAI,EAAE,iBAAiB;IACvBC,SAAS,EAAEJ,oBAAoB;IAC/BM,IAAI,EAAE;MAAEC,KAAK,EAAE;IAAM;GACtB;CAEJ,CACF;AAMD,OAAM,MAAOC,qBAAqB;;;uBAArBA,qBAAqB;IAAA;EAAA;;;YAArBA;IAAqB;EAAA;;;gBAHtBT,YAAY,CAACU,QAAQ,CAACP,MAAM,CAAC,EAC7BH,YAAY;IAAA;EAAA;;;2EAEXS,qBAAqB;IAAAE,OAAA,GAAAC,EAAA,CAAAZ,YAAA;IAAAa,OAAA,GAFtBb,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}