/* ============================================================================
   STYLES POUR LE COMPOSANT MESSAGE CHAT - THÈME SOMBRE FUTURISTE
   ============================================================================ */

.chat-container {
  @apply h-full flex flex-col bg-gray-900 text-white;
}

/* ============================================================================
   EN-TÊTE DU CHAT
   ============================================================================ */

.chat-header {
  @apply flex items-center justify-between p-4 border-b border-gray-700 bg-gray-800;
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.user-info {
  @apply flex items-center space-x-3;
}

.user-avatar {
  @apply w-10 h-10 rounded-full border-2 border-blue-500;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
}

.user-details h3 {
  @apply font-semibold text-white;
}

.user-status {
  @apply text-sm text-gray-400;
}

.user-status.online {
  @apply text-green-400;
}

.chat-actions {
  @apply flex items-center space-x-2;
}

.action-btn {
  @apply p-2 rounded-full bg-gray-700 hover:bg-gray-600 transition-all duration-200;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.action-btn:hover {
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.4);
  border-color: rgba(59, 130, 246, 0.6);
}

.action-btn i {
  @apply text-blue-400;
}

/* ============================================================================
   ZONE DES MESSAGES
   ============================================================================ */

.messages-container {
  @apply flex-1 overflow-y-auto p-4 space-y-4;
  background: linear-gradient(180deg, #111827 0%, #0f172a 100%);
  scrollbar-width: thin;
  scrollbar-color: #374151 #1f2937;
}

.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: #1f2937;
}

.messages-container::-webkit-scrollbar-thumb {
  background: #374151;
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: #4b5563;
}

/* ============================================================================
   MESSAGES
   ============================================================================ */

.message {
  @apply flex items-end space-x-2 max-w-xs md:max-w-md;
  animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message.my-message {
  @apply ml-auto flex-row-reverse space-x-reverse;
}

.message-avatar {
  @apply w-8 h-8 rounded-full border border-gray-600;
}

.message-content {
  @apply rounded-2xl px-4 py-2 max-w-full break-words;
  position: relative;
}

.message-content.my-message {
  @apply bg-blue-600 text-white;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: 0 2px 10px rgba(59, 130, 246, 0.3);
}

.message-content.other-message {
  @apply bg-gray-700 text-white border border-gray-600;
  background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
}

.message-text {
  @apply text-sm leading-relaxed;
}

.message-time {
  @apply text-xs text-gray-400 mt-1 block;
}

.message-status {
  @apply text-xs text-gray-400 mt-1;
}

.message-status.read {
  @apply text-blue-400;
}

.message-status.pending {
  @apply text-yellow-400;
}

.message-status.error {
  @apply text-red-400;
}

/* ============================================================================
   TYPES DE MESSAGES SPÉCIAUX
   ============================================================================ */

.message-image {
  @apply rounded-lg max-w-xs cursor-pointer;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.message-file {
  @apply flex items-center space-x-2 p-3 bg-gray-800 rounded-lg border border-gray-600;
}

.file-icon {
  @apply text-blue-400 text-xl;
}

.file-info {
  @apply flex-1;
}

.file-name {
  @apply text-sm font-medium text-white;
}

.file-size {
  @apply text-xs text-gray-400;
}

.voice-message {
  @apply flex items-center space-x-3 p-3 bg-gray-800 rounded-lg;
}

.voice-play-btn {
  @apply w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center cursor-pointer;
}

.voice-duration {
  @apply text-sm text-gray-400;
}

/* ============================================================================
   INDICATEURS DE FRAPPE
   ============================================================================ */

.typing-indicator {
  @apply flex items-center space-x-2 p-3 text-gray-400 text-sm;
}

.typing-dots {
  @apply flex space-x-1;
}

.typing-dot {
  @apply w-2 h-2 bg-gray-400 rounded-full;
  animation: typingPulse 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typingPulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* ============================================================================
   ZONE DE SAISIE
   ============================================================================ */

.message-input-container {
  @apply p-4 border-t border-gray-700 bg-gray-800;
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
}

.reply-preview {
  @apply mb-3 p-2 bg-gray-700 rounded-lg border-l-4 border-blue-500;
}

.reply-header {
  @apply flex items-center justify-between;
}

.reply-text {
  @apply text-sm text-gray-300 truncate;
}

.input-wrapper {
  @apply flex items-end space-x-2;
}

.message-input {
  @apply flex-1 bg-gray-700 border border-gray-600 rounded-2xl px-4 py-2 text-white placeholder-gray-400 resize-none;
  min-height: 40px;
  max-height: 120px;
  transition: all 0.2s ease;
}

.message-input:focus {
  @apply outline-none border-blue-500;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
}

.input-actions {
  @apply flex items-center space-x-1;
}

.input-btn {
  @apply p-2 rounded-full bg-gray-700 hover:bg-gray-600 transition-all duration-200;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.input-btn:hover {
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.4);
}

.input-btn i {
  @apply text-blue-400;
}

.send-btn {
  @apply p-2 rounded-full bg-blue-600 hover:bg-blue-700 transition-all duration-200;
  box-shadow: 0 2px 10px rgba(59, 130, 246, 0.3);
}

.send-btn:hover {
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.5);
}

.send-btn:disabled {
  @apply bg-gray-600 cursor-not-allowed;
  box-shadow: none;
}

/* ============================================================================
   ENREGISTREMENT VOCAL
   ============================================================================ */

.recording-indicator {
  @apply flex items-center space-x-2 p-3 bg-red-600 rounded-lg;
  animation: recordingPulse 1s infinite;
}

@keyframes recordingPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.recording-time {
  @apply text-white font-mono;
}

/* ============================================================================
   RESPONSIVE
   ============================================================================ */

@media (max-width: 768px) {
  .message {
    @apply max-w-xs;
  }
  
  .chat-header {
    @apply px-3 py-2;
  }
  
  .messages-container {
    @apply px-3;
  }
  
  .message-input-container {
    @apply px-3;
  }
}
