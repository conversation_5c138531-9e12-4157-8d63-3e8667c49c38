"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([["src_app_views_front_notifications_notifications_module_ts"],{

/***/ 3226:
/*!********************************************************************************************!*\
  !*** ./src/app/views/front/notifications/notification-list/notification-list.component.ts ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NotificationListComponent: () => (/* binding */ NotificationListComponent)
/* harmony export */ });
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rxjs */ 819);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rxjs */ 5797);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! rxjs */ 9452);
/* harmony import */ var src_app_models_message_model__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/models/message.model */ 5293);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rxjs/operators */ 271);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rxjs/operators */ 4334);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rxjs/operators */ 3900);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rxjs/operators */ 2575);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! rxjs/operators */ 1817);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! rxjs/operators */ 1567);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! rxjs/operators */ 1318);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var src_app_services_message_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/services/message.service */ 4537);
/* harmony import */ var _app_services_theme_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @app/services/theme.service */ 487);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @angular/common */ 316);








const _c0 = ["notificationContainer"];
function NotificationListComponent_div_8_div_3_Template(rf, ctx) {
  if (rf & 1) {
    const _r14 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 33)(1, "label", 34)(2, "input", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_8_div_3_Template_input_click_2_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r14);
      const ctx_r13 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r13.toggleSelectAll($event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](3, "span", 36);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("checked", ctx_r10.allSelected);
  }
}
function NotificationListComponent_div_8_button_9_Template(rf, ctx) {
  if (rf & 1) {
    const _r16 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "button", 37);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_8_button_9_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r16);
      const ctx_r15 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r15.markAllAsRead());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "i", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](2, " Tout marquer comme lu ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function NotificationListComponent_div_8_button_11_Template(rf, ctx) {
  if (rf & 1) {
    const _r18 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "button", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_8_button_11_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r18);
      const ctx_r17 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r17.deleteAllNotifications());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "i", 40);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](2, " Tout supprimer ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function NotificationListComponent_div_8_Template(rf, ctx) {
  if (rf & 1) {
    const _r20 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 23)(1, "button", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_8_Template_button_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r20);
      const ctx_r19 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r19.loadNotifications());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](2, "i", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](3, NotificationListComponent_div_8_div_3_Template, 4, 1, "div", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](4, "async");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](5, "button", 27);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_8_Template_button_click_5_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r20);
      const ctx_r21 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r21.toggleUnreadFilter());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](6, "i", 28);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](7, "button", 29);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_8_Template_button_click_7_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r20);
      const ctx_r22 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r22.toggleSound());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](8, "i", 30);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](9, NotificationListComponent_div_8_button_9_Template, 3, 0, "button", 31);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](10, "async");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](11, NotificationListComponent_div_8_button_11_Template, 3, 0, "button", 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](12, "async");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind1"](4, 9, ctx_r0.hasNotifications()));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵclassProp"]("active", ctx_r0.showOnlyUnread);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵclassProp"]("active", !ctx_r0.isSoundMuted);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpropertyInterpolate"]("title", ctx_r0.isSoundMuted ? "Activer le son" : "D\u00E9sactiver le son");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngClass", ctx_r0.isSoundMuted ? "fa-volume-mute" : "fa-volume-up");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind1"](10, 11, ctx_r0.unreadCount$) || 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind1"](12, 13, ctx_r0.hasNotifications()));
  }
}
function NotificationListComponent_div_9_Template(rf, ctx) {
  if (rf & 1) {
    const _r24 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 41)(1, "span", 42);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](3, "button", 37);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_9_Template_button_click_3_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r24);
      const ctx_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r23.markSelectedAsRead());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](4, "i", 43);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](5, " Marquer comme lu ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](6, "button", 44);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_9_Template_button_click_6_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r24);
      const ctx_r25 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r25.deleteSelectedNotifications());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](7, "i", 40);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](8, " Supprimer ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](9, "button", 45);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_9_Template_button_click_9_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r24);
      const ctx_r26 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      ctx_r26.selectedNotifications.clear();
      ctx_r26.showSelectionBar = false;
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r26.allSelected = false);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](10, "i", 46);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](11, " Annuler ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"]("", ctx_r1.selectedNotifications.size, " s\u00E9lectionn\u00E9(s)");
  }
}
function NotificationListComponent_div_10_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 47);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "div", 48);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](2, "p", 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](3, "Chargement des notifications...");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
}
function NotificationListComponent_div_11_Template(rf, ctx) {
  if (rf & 1) {
    const _r28 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 50)(1, "div", 51);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](2, "i", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](3, "div")(4, "h3", 53);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](5, "Erreur de chargement");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](6, "p", 54);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](8, "button", 55);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_11_Template_button_click_8_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r28);
      const ctx_r27 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r27.loadNotifications());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](9, " R\u00E9essayer ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](ctx_r3.getErrorMessage());
  }
}
function NotificationListComponent_div_12_Template(rf, ctx) {
  if (rf & 1) {
    const _r30 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 56)(1, "div", 57);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](2, "i", 58);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](3, "h3", 59);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](4, "Aucune notification");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](5, "p", 60);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](6, "Vous \u00EAtes \u00E0 jour !");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](7, "button", 61);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_12_Template_button_click_7_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r30);
      const ctx_r29 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r29.loadNotifications());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](8, " V\u00E9rifier les nouvelles notifications ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
}
function NotificationListComponent_div_14_ng_container_2_div_20_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 89);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const notification_r34 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", notification_r34.message == null ? null : notification_r34.message.content, " ");
  }
}
function NotificationListComponent_div_14_ng_container_2_div_21_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 90);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "i", 91);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const notification_r34 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", notification_r34.message == null ? null : notification_r34.message.attachments == null ? null : notification_r34.message.attachments.length, " pi\u00E8ce(s) jointe(s) ");
  }
}
function NotificationListComponent_div_14_ng_container_2_div_22_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](0, "div", 92);
  }
}
function NotificationListComponent_div_14_ng_container_2_button_24_Template(rf, ctx) {
  if (rf & 1) {
    const _r45 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "button", 93);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_14_ng_container_2_button_24_Template_button_click_0_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r45);
      const notification_r34 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]().$implicit;
      const ctx_r43 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
      ctx_r43.getNotificationAttachments(notification_r34.id);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"]($event.stopPropagation());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "i", 91);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function NotificationListComponent_div_14_ng_container_2_button_25_i_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](0, "i", 97);
  }
}
function NotificationListComponent_div_14_ng_container_2_button_25_i_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](0, "i", 98);
  }
}
function NotificationListComponent_div_14_ng_container_2_button_25_Template(rf, ctx) {
  if (rf & 1) {
    const _r50 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "button", 94);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_14_ng_container_2_button_25_Template_button_click_0_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r50);
      const notification_r34 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]().$implicit;
      const ctx_r48 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
      ctx_r48.joinConversation(notification_r34);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"]($event.stopPropagation());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](1, NotificationListComponent_div_14_ng_container_2_button_25_i_1_Template, 1, 0, "i", 95);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](2, NotificationListComponent_div_14_ng_container_2_button_25_i_2_Template, 1, 0, "i", 96);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r39 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("disabled", ctx_r39.loading);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx_r39.loading);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx_r39.loading);
  }
}
function NotificationListComponent_div_14_ng_container_2_button_28_Template(rf, ctx) {
  if (rf & 1) {
    const _r53 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "button", 99);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_14_ng_container_2_button_28_Template_button_click_0_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r53);
      const notification_r34 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]().$implicit;
      const ctx_r51 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
      ctx_r51.markAsRead(notification_r34.id);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"]($event.stopPropagation());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "i", 100);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function NotificationListComponent_div_14_ng_container_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r55 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](1, "div", 66)(2, "div", 67)(3, "label", 34)(4, "input", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_14_ng_container_2_Template_input_click_4_listener($event) {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r55);
      const notification_r34 = restoredCtx.$implicit;
      const ctx_r54 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r54.toggleSelection(notification_r34.id, $event));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](5, "span", 36);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](6, "div", 68);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](7, "img", 69);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](8, "div", 70)(9, "div", 71)(10, "div", 72)(11, "div", 73)(12, "span", 74);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](13);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](14, "div", 75);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](15);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](16, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](17, "div", 76)(18, "span", 77);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](19);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](20, NotificationListComponent_div_14_ng_container_2_div_20_Template, 2, 1, "div", 78);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](21, NotificationListComponent_div_14_ng_container_2_div_21_Template, 3, 1, "div", 79);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](22, NotificationListComponent_div_14_ng_container_2_div_22_Template, 1, 0, "div", 80);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](23, "div", 81);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](24, NotificationListComponent_div_14_ng_container_2_button_24_Template, 2, 0, "button", 82);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](25, NotificationListComponent_div_14_ng_container_2_button_25_Template, 3, 3, "button", 83);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](26, "button", 84);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_14_ng_container_2_Template_button_click_26_listener($event) {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r55);
      const notification_r34 = restoredCtx.$implicit;
      const ctx_r56 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
      ctx_r56.openNotificationDetails(notification_r34);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"]($event.stopPropagation());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](27, "i", 85);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](28, NotificationListComponent_div_14_ng_container_2_button_28_Template, 2, 0, "button", 86);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](29, "button", 87);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_14_ng_container_2_Template_button_click_29_listener($event) {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r55);
      const notification_r34 = restoredCtx.$implicit;
      const ctx_r57 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
      ctx_r57.deleteNotification(notification_r34.id);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"]($event.stopPropagation());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](30, "i", 88);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const notification_r34 = ctx.$implicit;
    const ctx_r32 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵclassProp"]("futuristic-notification-unread", !notification_r34.isRead)("futuristic-notification-read", notification_r34.isRead)("futuristic-notification-selected", ctx_r32.isSelected(notification_r34.id));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("checked", ctx_r32.isSelected(notification_r34.id));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("src", (notification_r34.senderId == null ? null : notification_r34.senderId.image) || "assets/images/default-avatar.png", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵsanitizeUrl"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"]((notification_r34.senderId == null ? null : notification_r34.senderId.username) || "Syst\u00E8me");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind2"](16, 17, notification_r34.timestamp, "shortTime"), " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](notification_r34.content);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", notification_r34.message == null ? null : notification_r34.message.content);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", notification_r34.message == null ? null : notification_r34.message.attachments == null ? null : notification_r34.message.attachments.length);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !notification_r34.isRead);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", notification_r34.message == null ? null : notification_r34.message.attachments == null ? null : notification_r34.message.attachments.length);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", notification_r34.type === "NEW_MESSAGE" || notification_r34.type === "GROUP_INVITE" || notification_r34.type === "MESSAGE_REACTION");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !notification_r34.isRead);
  }
}
function NotificationListComponent_div_14_div_4_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 101);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "div", 102);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](2, "p", 103);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](3, " Chargement des notifications plus anciennes... ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
}
function NotificationListComponent_div_14_Template(rf, ctx) {
  if (rf & 1) {
    const _r59 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 62, 63);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("scroll", function NotificationListComponent_div_14_Template_div_scroll_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r59);
      const _r31 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵreference"](1);
      const ctx_r58 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r58.onScroll(_r31));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](2, NotificationListComponent_div_14_ng_container_2_Template, 31, 20, "ng-container", 64);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](3, "async");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](4, NotificationListComponent_div_14_div_4_Template, 4, 0, "div", 65);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngForOf", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind1"](3, 2, ctx_r5.filteredNotifications$));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx_r5.loadingMore);
  }
}
function NotificationListComponent_div_25_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 47);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "div", 48);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](2, "p", 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](3, "Chargement des pi\u00E8ces jointes...");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
}
function NotificationListComponent_div_26_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 56)(1, "div", 57);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](2, "i", 104);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](3, "h3", 59);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](4, "Aucune pi\u00E8ce jointe");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](5, "p", 60);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](6, " Aucune pi\u00E8ce jointe n'a \u00E9t\u00E9 trouv\u00E9e pour cette notification. ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
}
function NotificationListComponent_div_27_div_1_div_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r67 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 120)(1, "img", 121);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_27_div_1_div_1_Template_img_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r67);
      const attachment_r61 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]().$implicit;
      const ctx_r65 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r65.openAttachment(attachment_r61.url));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const attachment_r61 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("src", attachment_r61.url, _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵsanitizeUrl"]);
  }
}
function NotificationListComponent_div_27_div_1_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 122);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "i");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const attachment_r61 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]().$implicit;
    const ctx_r63 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵclassMap"](ctx_r63.getFileIcon(attachment_r61.type));
  }
}
function NotificationListComponent_div_27_div_1_span_9_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "span", 123);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const attachment_r61 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]().$implicit;
    const ctx_r64 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](ctx_r64.formatFileSize(attachment_r61.size));
  }
}
function NotificationListComponent_div_27_div_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r72 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 107);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](1, NotificationListComponent_div_27_div_1_div_1_Template, 2, 1, "div", 108);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](2, NotificationListComponent_div_27_div_1_div_2_Template, 2, 2, "div", 109);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](3, "div", 110)(4, "div", 111);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](6, "div", 112)(7, "span", 113);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](9, NotificationListComponent_div_27_div_1_span_9_Template, 2, 1, "span", 114);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](10, "div", 115)(11, "button", 116);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_27_div_1_Template_button_click_11_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r72);
      const attachment_r61 = restoredCtx.$implicit;
      const ctx_r71 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r71.openAttachment(attachment_r61.url));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](12, "i", 117);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](13, "button", 118);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_27_div_1_Template_button_click_13_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r72);
      const attachment_r61 = restoredCtx.$implicit;
      const ctx_r73 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r73.downloadAttachment(attachment_r61));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](14, "i", 119);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const attachment_r61 = ctx.$implicit;
    const ctx_r60 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx_r60.isImage(attachment_r61.type));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx_r60.isImage(attachment_r61.type));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", attachment_r61.name || "Pi\u00E8ce jointe", " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](ctx_r60.getFileTypeLabel(attachment_r61.type));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", attachment_r61.size);
  }
}
function NotificationListComponent_div_27_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 105);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](1, NotificationListComponent_div_27_div_1_Template, 15, 5, "div", 106);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngForOf", ctx_r8.currentAttachments);
  }
}
function NotificationListComponent_div_36_div_19_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 147)(1, "strong");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](2, "Message original :");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r74 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", ctx_r74.currentNotification.message == null ? null : ctx_r74.currentNotification.message.content, " ");
  }
}
function NotificationListComponent_div_36_div_35_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 137)(1, "span", 138);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](2, "Lu le :");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](3, "span", 139);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](5, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r75 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind2"](5, 1, ctx_r75.currentNotification.readAt, "medium"));
  }
}
function NotificationListComponent_div_36_div_36_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 148)(1, "span", 138);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](2, "i", 149);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](3, " Note : ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](4, "span", 150);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](5, " Ouvrir les d\u00E9tails ne marque pas automatiquement comme lu ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
}
function NotificationListComponent_div_36_div_37_div_5_div_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r87 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 164)(1, "img", 121);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_36_div_37_div_5_div_1_Template_img_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r87);
      const attachment_r81 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]().$implicit;
      const ctx_r85 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](3);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r85.openAttachment(attachment_r81.url));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const attachment_r81 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("src", attachment_r81.url, _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵsanitizeUrl"]);
  }
}
function NotificationListComponent_div_36_div_37_div_5_div_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 165);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "i");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const attachment_r81 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]().$implicit;
    const ctx_r83 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵclassMap"](ctx_r83.getFileIcon(attachment_r81.type));
  }
}
function NotificationListComponent_div_36_div_37_div_5_span_9_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "span", 166);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const attachment_r81 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]().$implicit;
    const ctx_r84 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](ctx_r84.formatFileSize(attachment_r81.size));
  }
}
function NotificationListComponent_div_36_div_37_div_5_Template(rf, ctx) {
  if (rf & 1) {
    const _r92 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 153);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](1, NotificationListComponent_div_36_div_37_div_5_div_1_Template, 2, 1, "div", 154);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](2, NotificationListComponent_div_36_div_37_div_5_div_2_Template, 2, 2, "div", 155);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](3, "div", 156)(4, "div", 157);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](6, "div", 158)(7, "span", 159);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](9, NotificationListComponent_div_36_div_37_div_5_span_9_Template, 2, 1, "span", 160);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](10, "div", 161)(11, "button", 162);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_36_div_37_div_5_Template_button_click_11_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r92);
      const attachment_r81 = restoredCtx.$implicit;
      const ctx_r91 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](3);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r91.openAttachment(attachment_r81.url));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](12, "i", 117);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](13, "button", 163);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_36_div_37_div_5_Template_button_click_13_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r92);
      const attachment_r81 = restoredCtx.$implicit;
      const ctx_r93 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](3);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r93.downloadAttachment(attachment_r81));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](14, "i", 119);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const attachment_r81 = ctx.$implicit;
    const ctx_r80 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx_r80.isImage(attachment_r81.type));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx_r80.isImage(attachment_r81.type));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", attachment_r81.name || "Pi\u00E8ce jointe", " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](ctx_r80.getFileTypeLabel(attachment_r81.type));
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", attachment_r81.size);
  }
}
function NotificationListComponent_div_36_div_37_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 124)(1, "h4", 125);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](2, "i", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](4, "div", 151);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](5, NotificationListComponent_div_36_div_37_div_5_Template, 15, 5, "div", 152);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r77 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" Pi\u00E8ces jointes (", ctx_r77.currentAttachments.length, ") ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngForOf", ctx_r77.currentAttachments);
  }
}
function NotificationListComponent_div_36_button_39_i_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](0, "i", 170);
  }
}
function NotificationListComponent_div_36_button_39_i_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](0, "i", 171);
  }
}
function NotificationListComponent_div_36_button_39_Template(rf, ctx) {
  if (rf & 1) {
    const _r97 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "button", 167);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_36_button_39_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r97);
      const ctx_r96 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
      ctx_r96.joinConversation(ctx_r96.currentNotification);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r96.closeNotificationDetailsModal());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](1, NotificationListComponent_div_36_button_39_i_1_Template, 1, 0, "i", 168);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](2, NotificationListComponent_div_36_button_39_i_2_Template, 1, 0, "i", 169);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](3, " Rejoindre la conversation ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r78 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("disabled", ctx_r78.loading);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx_r78.loading);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx_r78.loading);
  }
}
function NotificationListComponent_div_36_button_40_Template(rf, ctx) {
  if (rf & 1) {
    const _r99 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "button", 172);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_36_button_40_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r99);
      const ctx_r98 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r98.markAsRead(ctx_r98.currentNotification.id));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "i", 173);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](2, " Marquer comme lu ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function NotificationListComponent_div_36_Template(rf, ctx) {
  if (rf & 1) {
    const _r101 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 19)(1, "div", 124)(2, "h4", 125);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](3, "i", 126);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](4, " Exp\u00E9diteur ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](5, "div", 127);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](6, "img", 128);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](7, "div", 129)(8, "span", 130);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](10, "span", 131);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](11);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](12, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](13, "div", 124)(14, "h4", 125);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](15, "i", 132);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](16, " Message ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](17, "div", 133);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](18);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](19, NotificationListComponent_div_36_div_19_Template, 4, 1, "div", 134);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](20, "div", 124)(21, "h4", 125);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](22, "i", 135);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](23, " Informations ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](24, "div", 136)(25, "div", 137)(26, "span", 138);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](27, "Type :");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](28, "span", 139);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](29);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](30, "div", 137)(31, "span", 138);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](32, "Statut :");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](33, "span", 139);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](34);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](35, NotificationListComponent_div_36_div_35_Template, 6, 4, "div", 140);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](36, NotificationListComponent_div_36_div_36_Template, 6, 0, "div", 141);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](37, NotificationListComponent_div_36_div_37_Template, 6, 2, "div", 142);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](38, "div", 143);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](39, NotificationListComponent_div_36_button_39_Template, 4, 3, "button", 144);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](40, NotificationListComponent_div_36_button_40_Template, 3, 0, "button", 145);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](41, "button", 44);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_div_36_Template_button_click_41_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r101);
      const ctx_r100 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      ctx_r100.deleteNotification(ctx_r100.currentNotification.id);
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r100.closeNotificationDetailsModal());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](42, "i", 146);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](43, " Supprimer ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("src", (ctx_r9.currentNotification.senderId == null ? null : ctx_r9.currentNotification.senderId.image) || "assets/images/default-avatar.png", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵsanitizeUrl"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", (ctx_r9.currentNotification.senderId == null ? null : ctx_r9.currentNotification.senderId.username) || "Syst\u00E8me", " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind2"](12, 16, ctx_r9.currentNotification.timestamp, "medium"), " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", ctx_r9.currentNotification.content, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx_r9.currentNotification.message == null ? null : ctx_r9.currentNotification.message.content);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](10);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](ctx_r9.currentNotification.type);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵclassProp"]("text-green-500", ctx_r9.currentNotification.isRead)("text-orange-500", !ctx_r9.currentNotification.isRead);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", ctx_r9.currentNotification.isRead ? "Lu" : "Non lu", " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx_r9.currentNotification.readAt);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx_r9.currentNotification.isRead);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx_r9.currentAttachments.length > 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx_r9.currentNotification.type === "NEW_MESSAGE" || ctx_r9.currentNotification.type === "GROUP_INVITE" || ctx_r9.currentNotification.type === "MESSAGE_REACTION");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx_r9.currentNotification.isRead);
  }
}
class NotificationListComponent {
  constructor(messageService, themeService, router) {
    this.messageService = messageService;
    this.themeService = themeService;
    this.router = router;
    this.loading = true;
    this.loadingMore = false;
    this.hasMoreNotifications = true;
    this.error = null;
    this.showOnlyUnread = false;
    this.isSoundMuted = false;
    // Propriétés pour la sélection multiple
    this.selectedNotifications = new Set();
    this.allSelected = false;
    this.showSelectionBar = false;
    // Propriétés pour le modal des pièces jointes
    this.showAttachmentsModal = false;
    this.loadingAttachments = false;
    this.currentAttachments = [];
    // Propriétés pour le modal des détails de notification
    this.showNotificationDetailsModal = false;
    this.currentNotification = null;
    this.destroy$ = new rxjs__WEBPACK_IMPORTED_MODULE_4__.Subject();
    this.scrollPosition$ = new rxjs__WEBPACK_IMPORTED_MODULE_5__.BehaviorSubject(0);
    this.notifications$ = this.messageService.notifications$;
    this.filteredNotifications$ = this.notifications$; // Par défaut, afficher toutes les notifications
    this.unreadCount$ = this.messageService.notificationCount$;
    this.isDarkMode$ = this.themeService.currentTheme$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.map)(theme => theme.name === 'dark'));
    // Vérifier l'état du son
    this.isSoundMuted = this.messageService.isMuted();
  }
  /**
   * Rejoint une conversation ou un groupe à partir d'une notification
   * @param notification Notification contenant les informations de la conversation ou du groupe
   */
  joinConversation(notification) {
    // Marquer la notification comme lue d'abord
    this.markAsRead(notification.id);
    // Extraire les informations pertinentes de la notification
    const conversationId = notification.conversationId || notification.metadata && notification.metadata['conversationId'] || (notification.relatedEntity && notification.relatedEntity.includes('conversation') ? notification.relatedEntity : null);
    const groupId = notification.groupId || notification.metadata && notification.metadata['groupId'] || (notification.relatedEntity && notification.relatedEntity.includes('group') ? notification.relatedEntity : null);
    // Déterminer où rediriger l'utilisateur
    if (conversationId) {
      this.router.navigate(['/messages/conversations/chat', conversationId]);
    } else if (groupId) {
      this.router.navigate(['/messages/group', groupId]);
    } else if (notification.senderId && notification.senderId.id) {
      this.loading = true;
      this.messageService.getOrCreateConversation(notification.senderId.id).subscribe({
        next: conversation => {
          this.loading = false;
          if (conversation && conversation.id) {
            this.router.navigate(['/messages/conversations/chat', conversation.id]);
          } else {
            this.router.navigate(['/messages']);
          }
        },
        error: error => {
          this.loading = false;
          this.error = error;
          this.router.navigate(['/messages']);
        }
      });
    } else {
      this.router.navigate(['/messages']);
    }
  }
  onScroll(target) {
    if (!target) return;
    const scrollPosition = target.scrollTop;
    const scrollHeight = target.scrollHeight;
    const clientHeight = target.clientHeight;
    // Si on est proche du bas (à 200px du bas)
    if (scrollHeight - scrollPosition - clientHeight < 200) {
      this.scrollPosition$.next(scrollPosition);
    }
  }
  ngOnInit() {
    // Charger la préférence de son depuis le localStorage
    const savedMutePreference = localStorage.getItem('notificationSoundMuted');
    if (savedMutePreference !== null) {
      this.isSoundMuted = savedMutePreference === 'true';
      this.messageService.setMuted(this.isSoundMuted);
    }
    this.loadNotifications();
    this.setupSubscriptions();
    this.setupInfiniteScroll();
    this.filterDeletedNotifications();
  }
  /**
   * Filtre les notifications supprimées lors du chargement initial
   */
  filterDeletedNotifications() {
    const deletedNotificationIds = this.getDeletedNotificationIds();
    if (deletedNotificationIds.size > 0) {
      this.notifications$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.take)(1)).subscribe(notifications => {
        const filteredNotifications = notifications.filter(notification => !deletedNotificationIds.has(notification.id));
        this.messageService.notifications.next(filteredNotifications);
        const unreadCount = filteredNotifications.filter(n => !n.isRead).length;
        this.messageService.notificationCount.next(unreadCount);
        this.updateNotificationCache(filteredNotifications);
      });
    }
  }
  setupInfiniteScroll() {
    // Configurer le chargement des anciennes notifications lors du défilement
    this.scrollPosition$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_8__.takeUntil)(this.destroy$), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__.debounceTime)(200),
    // Attendre 200ms après le dernier événement de défilement
    (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_10__.distinctUntilChanged)(),
    // Ne déclencher que si la position de défilement a changé
    (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_11__.filter)(() => !this.loadingMore && this.hasMoreNotifications) // Ne charger que s'il y a plus de notifications et qu'on n'est pas déjà en train de charger
    ).subscribe(() => {
      this.loadMoreNotifications();
    });
  }
  loadNotifications() {
    this.loading = true;
    this.loadingMore = false;
    this.error = null;
    this.hasMoreNotifications = true;
    const deletedNotificationIds = this.getDeletedNotificationIds();
    this.messageService.getNotifications(true).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_8__.takeUntil)(this.destroy$), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.map)(notifications => {
      if (deletedNotificationIds.size > 0) {
        return notifications.filter(notification => !deletedNotificationIds.has(notification.id));
      }
      return notifications;
    })).subscribe({
      next: notifications => {
        this.messageService.notifications.next(notifications);
        const unreadCount = notifications.filter(n => !n.isRead).length;
        this.messageService.notificationCount.next(unreadCount);
        this.loading = false;
        this.hasMoreNotifications = this.messageService.hasMoreNotifications();
      },
      error: err => {
        this.error = err;
        this.loading = false;
        this.hasMoreNotifications = false;
      }
    });
  }
  loadMoreNotifications() {
    if (this.loadingMore || !this.hasMoreNotifications) return;
    this.loadingMore = true;
    const deletedNotificationIds = this.getDeletedNotificationIds();
    this.messageService.loadMoreNotifications().pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_8__.takeUntil)(this.destroy$), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.map)(notifications => {
      if (deletedNotificationIds.size > 0) {
        return notifications.filter(notification => !deletedNotificationIds.has(notification.id));
      }
      return notifications;
    })).subscribe({
      next: notifications => {
        this.notifications$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.take)(1)).subscribe(existingNotifications => {
          const allNotifications = [...existingNotifications, ...notifications];
          this.messageService.notifications.next(allNotifications);
          const unreadCount = allNotifications.filter(n => !n.isRead).length;
          this.messageService.notificationCount.next(unreadCount);
          this.updateNotificationCache(allNotifications);
        });
        this.loadingMore = false;
        this.hasMoreNotifications = this.messageService.hasMoreNotifications();
      },
      error: err => {
        this.loadingMore = false;
        this.hasMoreNotifications = false;
      }
    });
  }
  setupSubscriptions() {
    this.messageService.subscribeToNewNotifications().pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_8__.takeUntil)(this.destroy$), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_12__.catchError)(error => {
      console.log('Notification stream error:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_13__.of)(null);
    })).subscribe();
    this.messageService.subscribeToNotificationsRead().pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_8__.takeUntil)(this.destroy$), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_12__.catchError)(error => {
      console.log('Notifications read stream error:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_13__.of)(null);
    })).subscribe();
  }
  markAsRead(notificationId) {
    if (!notificationId) {
      this.error = new Error('ID de notification invalide');
      return;
    }
    this.notifications$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.take)(1)).subscribe(notifications => {
      const notification = notifications.find(n => n.id === notificationId);
      if (notification) {
        if (notification.isRead) return;
        const updatedNotifications = notifications.map(n => n.id === notificationId ? {
          ...n,
          isRead: true,
          readAt: new Date().toISOString()
        } : n);
        this.updateUIWithNotifications(updatedNotifications);
        this.messageService.markAsRead([notificationId]).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_8__.takeUntil)(this.destroy$)).subscribe({
          next: result => {
            if (result && result.success) {
              if (this.error && this.error.message.includes('mark')) {
                this.error = null;
              }
            }
          },
          error: err => {
            const revertedNotifications = notifications.map(n => n.id === notificationId ? {
              ...n,
              isRead: false,
              readAt: undefined
            } : n);
            this.messageService.notifications.next(revertedNotifications);
            const revertedUnreadCount = revertedNotifications.filter(n => !n.isRead).length;
            this.messageService.notificationCount.next(revertedUnreadCount);
          }
        });
      } else {
        this.loadNotifications();
      }
    });
  }
  /**
   * Met à jour l'interface utilisateur avec les nouvelles notifications
   * @param notifications Notifications à afficher
   */
  updateUIWithNotifications(notifications) {
    // Mettre à jour l'interface utilisateur immédiatement
    this.messageService.notifications.next(notifications);
    // Mettre à jour le compteur de notifications non lues
    const unreadCount = notifications.filter(n => !n.isRead).length;
    this.messageService.notificationCount.next(unreadCount);
    // Mettre à jour le cache de notifications dans le service
    this.updateNotificationCache(notifications);
  }
  /**
   * Met à jour le cache de notifications dans le service
   * @param notifications Notifications à mettre à jour
   */
  updateNotificationCache(notifications) {
    notifications.forEach(notification => {
      this.messageService.updateNotificationCache?.(notification);
    });
  }
  /**
   * Réinitialise la sélection des notifications
   */
  resetSelection() {
    this.selectedNotifications.clear();
    this.allSelected = false;
    this.showSelectionBar = false;
  }
  markAllAsRead() {
    this.notifications$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.take)(1)).subscribe(notifications => {
      const unreadIds = notifications.filter(n => !n.isRead).map(n => n.id);
      if (unreadIds.length === 0) return;
      const validIds = unreadIds.filter(id => id && typeof id === 'string' && id.trim() !== '');
      if (validIds.length !== unreadIds.length) {
        this.error = new Error('Invalid notification IDs');
        return;
      }
      const updatedNotifications = notifications.map(n => validIds.includes(n.id) ? {
        ...n,
        isRead: true,
        readAt: new Date().toISOString()
      } : n);
      this.updateUIWithNotifications(updatedNotifications);
      this.messageService.markAsRead(validIds).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_8__.takeUntil)(this.destroy$)).subscribe({
        next: result => {
          if (result && result.success) {
            if (this.error && this.error.message.includes('mark')) {
              this.error = null;
            }
          }
        },
        error: err => {
          // Ne pas définir d'erreur pour éviter de perturber l'interface utilisateur
        }
      });
    });
  }
  hasNotifications() {
    return this.notifications$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.map)(notifications => notifications?.length > 0));
  }
  hasUnreadNotifications() {
    return this.unreadCount$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.map)(count => count > 0));
  }
  /**
   * Active/désactive le filtre pour n'afficher que les notifications non lues
   */
  toggleUnreadFilter() {
    this.showOnlyUnread = !this.showOnlyUnread;
    if (this.showOnlyUnread) {
      this.filteredNotifications$ = this.messageService.getUnreadNotifications();
    } else {
      this.filteredNotifications$ = this.notifications$;
    }
  }
  /**
   * Active/désactive le son des notifications
   */
  toggleSound() {
    this.isSoundMuted = !this.isSoundMuted;
    this.messageService.setMuted(this.isSoundMuted);
    if (!this.isSoundMuted) {
      setTimeout(() => {
        this.messageService.playNotificationSound();
        setTimeout(() => {
          this.messageService.playNotificationSound();
        }, 1000);
      }, 100);
    }
    localStorage.setItem('notificationSoundMuted', this.isSoundMuted.toString());
  }
  /**
   * Récupère les pièces jointes d'une notification et ouvre le modal
   * @param notificationId ID de la notification
   */
  getNotificationAttachments(notificationId) {
    if (!notificationId) return;
    this.currentAttachments = [];
    this.loadingAttachments = true;
    this.showAttachmentsModal = true;
    let notification;
    this.notifications$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.take)(1)).subscribe(notifications => {
      notification = notifications.find(n => n.id === notificationId);
    });
    if (notification && notification.message && notification.message.attachments && notification.message.attachments.length > 0) {
      this.loadingAttachments = false;
      this.currentAttachments = notification.message.attachments.map(attachment => ({
        id: '',
        url: attachment.url || '',
        type: this.convertAttachmentTypeToMessageType(attachment.type),
        name: attachment.name || '',
        size: attachment.size || 0,
        duration: 0
      }));
      return;
    }
    this.messageService.getNotificationAttachments(notificationId).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_8__.takeUntil)(this.destroy$)).subscribe({
      next: attachments => {
        this.loadingAttachments = false;
        this.currentAttachments = attachments;
      },
      error: err => {
        this.loadingAttachments = false;
      }
    });
  }
  /**
   * Ferme le modal des pièces jointes
   */
  closeAttachmentsModal() {
    this.showAttachmentsModal = false;
  }
  /**
   * Ouvre le modal des détails de notification
   * @param notification Notification à afficher
   */
  openNotificationDetails(notification) {
    this.currentNotification = notification;
    this.showNotificationDetailsModal = true;
    if (notification.message?.attachments?.length) {
      this.getNotificationAttachmentsForModal(notification.id);
    }
  }
  /**
   * Ferme le modal des détails de notification
   */
  closeNotificationDetailsModal() {
    this.showNotificationDetailsModal = false;
    this.currentNotification = null;
    this.currentAttachments = [];
  }
  /**
   * Récupère les pièces jointes d'une notification pour le modal de détails
   */
  getNotificationAttachmentsForModal(notificationId) {
    this.currentAttachments = [];
    if (this.currentNotification?.message?.attachments?.length) {
      this.currentAttachments = this.currentNotification.message.attachments.map(attachment => ({
        id: '',
        url: attachment.url || '',
        type: this.convertAttachmentTypeToMessageType(attachment.type),
        name: attachment.name || '',
        size: attachment.size || 0,
        duration: 0
      }));
    }
  }
  /**
   * Convertit AttachmentType en MessageType
   */
  convertAttachmentTypeToMessageType(type) {
    switch (type) {
      case 'IMAGE':
        return src_app_models_message_model__WEBPACK_IMPORTED_MODULE_0__.MessageType.IMAGE;
      case 'VIDEO':
        return src_app_models_message_model__WEBPACK_IMPORTED_MODULE_0__.MessageType.VIDEO;
      case 'AUDIO':
        return src_app_models_message_model__WEBPACK_IMPORTED_MODULE_0__.MessageType.AUDIO;
      case 'FILE':
        return src_app_models_message_model__WEBPACK_IMPORTED_MODULE_0__.MessageType.FILE;
      default:
        return src_app_models_message_model__WEBPACK_IMPORTED_MODULE_0__.MessageType.FILE;
    }
  }
  /**
   * Vérifie si un type de fichier est une image
   */
  isImage(type) {
    return type?.startsWith('image/') || false;
  }
  /**
   * Obtient l'icône FontAwesome correspondant au type de fichier
   * @param type Type MIME du fichier
   * @returns Classe CSS de l'icône
   */
  getFileIcon(type) {
    if (!type) return 'fas fa-file';
    if (type.startsWith('image/')) return 'fas fa-file-image';
    if (type.startsWith('video/')) return 'fas fa-file-video';
    if (type.startsWith('audio/')) return 'fas fa-file-audio';
    if (type.startsWith('text/')) return 'fas fa-file-alt';
    if (type.includes('pdf')) return 'fas fa-file-pdf';
    if (type.includes('word') || type.includes('document')) return 'fas fa-file-word';
    if (type.includes('excel') || type.includes('sheet')) return 'fas fa-file-excel';
    if (type.includes('powerpoint') || type.includes('presentation')) return 'fas fa-file-powerpoint';
    if (type.includes('zip') || type.includes('compressed')) return 'fas fa-file-archive';
    return 'fas fa-file';
  }
  /**
   * Obtient le libellé du type de fichier
   * @param type Type MIME du fichier
   * @returns Libellé du type de fichier
   */
  getFileTypeLabel(type) {
    if (!type) return 'Fichier';
    if (type.startsWith('image/')) return 'Image';
    if (type.startsWith('video/')) return 'Vidéo';
    if (type.startsWith('audio/')) return 'Audio';
    if (type.startsWith('text/')) return 'Texte';
    if (type.includes('pdf')) return 'PDF';
    if (type.includes('word') || type.includes('document')) return 'Document';
    if (type.includes('excel') || type.includes('sheet')) return 'Feuille de calcul';
    if (type.includes('powerpoint') || type.includes('presentation')) return 'Présentation';
    if (type.includes('zip') || type.includes('compressed')) return 'Archive';
    return 'Fichier';
  }
  /**
   * Formate la taille du fichier en unités lisibles
   * @param size Taille en octets
   * @returns Taille formatée (ex: "1.5 MB")
   */
  formatFileSize(size) {
    if (!size) return '';
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let i = 0;
    let formattedSize = size;
    while (formattedSize >= 1024 && i < units.length - 1) {
      formattedSize /= 1024;
      i++;
    }
    return `${formattedSize.toFixed(1)} ${units[i]}`;
  }
  /**
   * Ouvre une pièce jointe dans un nouvel onglet
   * @param url URL de la pièce jointe
   */
  openAttachment(url) {
    if (!url) return;
    window.open(url, '_blank');
  }
  /**
   * Télécharge une pièce jointe
   * @param attachment Pièce jointe à télécharger
   */
  downloadAttachment(attachment) {
    if (!attachment?.url) return;
    const link = document.createElement('a');
    link.href = attachment.url;
    link.download = attachment.name || 'attachment';
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
  acceptFriendRequest(notification) {
    this.markAsRead(notification.id);
  }
  /**
   * Supprime une notification et la stocke dans le localStorage
   * @param notificationId ID de la notification à supprimer
   */
  deleteNotification(notificationId) {
    if (!notificationId) {
      this.error = new Error('ID de notification invalide');
      return;
    }
    const deletedNotificationIds = this.getDeletedNotificationIds();
    deletedNotificationIds.add(notificationId);
    this.saveDeletedNotificationIds(deletedNotificationIds);
    this.messageService.deleteNotification(notificationId).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_8__.takeUntil)(this.destroy$)).subscribe({
      next: result => {
        if (result && result.success) {
          if (this.error && this.error.message.includes('suppression')) {
            this.error = null;
          }
        }
      },
      error: err => {
        this.error = err;
      }
    });
  }
  /**
   * Supprime toutes les notifications et les stocke dans le localStorage
   */
  deleteAllNotifications() {
    this.notifications$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.take)(1)).subscribe(notifications => {
      const deletedNotificationIds = this.getDeletedNotificationIds();
      notifications.forEach(notification => {
        deletedNotificationIds.add(notification.id);
      });
      this.saveDeletedNotificationIds(deletedNotificationIds);
      this.messageService.deleteAllNotifications().pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_8__.takeUntil)(this.destroy$)).subscribe({
        next: result => {
          if (result && result.success) {
            if (this.error && this.error.message.includes('suppression')) {
              this.error = null;
            }
          }
        },
        error: err => {
          this.error = err;
        }
      });
    });
  }
  getErrorMessage() {
    return this.error?.message || 'Unknown error occurred';
  }
  /**
   * Récupère les IDs des notifications supprimées du localStorage
   * @returns Set contenant les IDs des notifications supprimées
   */
  getDeletedNotificationIds() {
    try {
      const deletedIdsJson = localStorage.getItem('deletedNotificationIds');
      if (deletedIdsJson) {
        return new Set(JSON.parse(deletedIdsJson));
      }
      return new Set();
    } catch (error) {
      return new Set();
    }
  }
  /**
   * Sauvegarde les IDs des notifications supprimées dans le localStorage
   * @param deletedIds Set contenant les IDs des notifications supprimées
   */
  saveDeletedNotificationIds(deletedIds) {
    try {
      localStorage.setItem('deletedNotificationIds', JSON.stringify(Array.from(deletedIds)));
    } catch (error) {
      // Ignore silently
    }
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  /**
   * Sélectionne ou désélectionne une notification
   * @param notificationId ID de la notification
   * @param event Événement de la case à cocher
   */
  toggleSelection(notificationId, event) {
    event.stopPropagation(); // Empêcher la propagation de l'événement
    if (this.selectedNotifications.has(notificationId)) {
      this.selectedNotifications.delete(notificationId);
    } else {
      this.selectedNotifications.add(notificationId);
    }
    // Mettre à jour l'état de sélection globale
    this.updateSelectionState();
    // Afficher ou masquer la barre de sélection
    this.showSelectionBar = this.selectedNotifications.size > 0;
  }
  /**
   * Sélectionne ou désélectionne toutes les notifications
   * @param event Événement de la case à cocher
   */
  toggleSelectAll(event) {
    event.stopPropagation(); // Empêcher la propagation de l'événement
    this.allSelected = !this.allSelected;
    this.filteredNotifications$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.take)(1)).subscribe(notifications => {
      if (this.allSelected) {
        // Sélectionner toutes les notifications
        notifications.forEach(notification => {
          this.selectedNotifications.add(notification.id);
        });
      } else {
        // Désélectionner toutes les notifications
        this.selectedNotifications.clear();
      }
      // Afficher ou masquer la barre de sélection
      this.showSelectionBar = this.selectedNotifications.size > 0;
    });
  }
  /**
   * Met à jour l'état de sélection globale
   */
  updateSelectionState() {
    this.filteredNotifications$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.take)(1)).subscribe(notifications => {
      this.allSelected = notifications.length > 0 && this.selectedNotifications.size === notifications.length;
    });
  }
  /**
   * Supprime les notifications sélectionnées
   */
  deleteSelectedNotifications() {
    if (this.selectedNotifications.size === 0) return;
    const selectedIds = Array.from(this.selectedNotifications);
    this.notifications$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.take)(1)).subscribe(notifications => {
      const updatedNotifications = notifications.filter(notification => !this.selectedNotifications.has(notification.id));
      this.updateUIWithNotifications(updatedNotifications);
      this.resetSelection();
    });
    this.messageService.deleteMultipleNotifications(selectedIds).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_8__.takeUntil)(this.destroy$)).subscribe({
      next: result => {
        // Success handled silently
      },
      error: err => {
        // Error handled silently
      }
    });
  }
  /**
   * Marque les notifications sélectionnées comme lues
   */
  markSelectedAsRead() {
    if (this.selectedNotifications.size === 0) return;
    const selectedIds = Array.from(this.selectedNotifications);
    this.notifications$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.take)(1)).subscribe(notifications => {
      const updatedNotifications = notifications.map(notification => this.selectedNotifications.has(notification.id) ? {
        ...notification,
        isRead: true,
        readAt: new Date().toISOString()
      } : notification);
      this.updateUIWithNotifications(updatedNotifications);
      this.resetSelection();
    });
    this.messageService.markAsRead(selectedIds).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_8__.takeUntil)(this.destroy$)).subscribe({
      next: result => {
        // Success handled silently
      },
      error: err => {
        // Error handled silently
      }
    });
  }
  /**
   * Vérifie si une notification est sélectionnée
   * @param notificationId ID de la notification
   * @returns true si la notification est sélectionnée, false sinon
   */
  isSelected(notificationId) {
    return this.selectedNotifications.has(notificationId);
  }
  static {
    this.ɵfac = function NotificationListComponent_Factory(t) {
      return new (t || NotificationListComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](src_app_services_message_service__WEBPACK_IMPORTED_MODULE_1__.MessageService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_app_services_theme_service__WEBPACK_IMPORTED_MODULE_2__.ThemeService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_14__.Router));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineComponent"]({
      type: NotificationListComponent,
      selectors: [["app-notification-list"]],
      viewQuery: function NotificationListComponent_Query(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵviewQuery"](_c0, 5);
        }
        if (rf & 2) {
          let _t;
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵloadQuery"]()) && (ctx.notificationContainer = _t.first);
        }
      },
      hostBindings: function NotificationListComponent_HostBindings(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("scroll", function NotificationListComponent_scroll_HostBindingHandler($event) {
            return ctx.onScroll($event.target);
          });
        }
      },
      decls: 37,
      vars: 22,
      consts: [[1, "futuristic-notifications-container", "main-grid-container"], [1, "background-elements", "background-grid"], [1, "futuristic-notifications-card", "content-card", "relative", "z-10"], [1, "futuristic-notifications-header"], [1, "futuristic-title"], [1, "fas", "fa-bell", "mr-2"], ["class", "flex space-x-2", 4, "ngIf"], ["class", "flex space-x-2 selection-actions", 4, "ngIf"], ["class", "futuristic-loading-container", 4, "ngIf"], ["class", "futuristic-error-message", 4, "ngIf"], ["class", "futuristic-empty-state", 4, "ngIf"], ["class", "futuristic-notifications-list", 3, "scroll", 4, "ngIf"], [1, "futuristic-modal-overlay", 3, "click"], [1, "futuristic-modal-container", 3, "click"], [1, "futuristic-modal-header"], [1, "futuristic-modal-title"], [1, "fas", "fa-paperclip", "mr-2"], [1, "futuristic-modal-close", 3, "click"], [1, "fas", "fa-times"], [1, "futuristic-modal-body"], ["class", "futuristic-attachments-list", 4, "ngIf"], [1, "fas", "fa-info-circle", "mr-2"], ["class", "futuristic-modal-body", 4, "ngIf"], [1, "flex", "space-x-2"], ["title", "Rafra\u00EEchir", 1, "futuristic-action-button", 3, "click"], [1, "fas", "fa-sync-alt"], ["class", "select-all-checkbox", 4, "ngIf"], ["title", "Filtrer les non lues", 1, "futuristic-action-button", 3, "click"], [1, "fas", "fa-filter"], [1, "futuristic-action-button", 3, "title", "click"], [1, "fas", 3, "ngClass"], ["class", "futuristic-primary-button", 3, "click", 4, "ngIf"], ["class", "futuristic-danger-button", "title", "Supprimer toutes les notifications", 3, "click", 4, "ngIf"], [1, "select-all-checkbox"], [1, "futuristic-checkbox"], ["type", "checkbox", 3, "checked", "click"], [1, "checkmark"], [1, "futuristic-primary-button", 3, "click"], [1, "fas", "fa-check-double", "mr-1"], ["title", "Supprimer toutes les notifications", 1, "futuristic-danger-button", 3, "click"], [1, "fas", "fa-trash-alt", "mr-1"], [1, "flex", "space-x-2", "selection-actions"], [1, "selection-count"], [1, "fas", "fa-check", "mr-1"], [1, "futuristic-danger-button", 3, "click"], [1, "futuristic-cancel-button", 3, "click"], [1, "fas", "fa-times", "mr-1"], [1, "futuristic-loading-container"], [1, "futuristic-loading-circle"], [1, "futuristic-loading-text"], [1, "futuristic-error-message"], [1, "flex", "items-center"], [1, "fas", "fa-exclamation-triangle", "futuristic-error-icon"], [1, "futuristic-error-title"], [1, "futuristic-error-text"], [1, "futuristic-retry-button", "ml-auto", 3, "click"], [1, "futuristic-empty-state"], [1, "futuristic-empty-icon"], [1, "fas", "fa-bell-slash"], [1, "futuristic-empty-title"], [1, "futuristic-empty-text"], [1, "futuristic-check-button", 3, "click"], [1, "futuristic-notifications-list", 3, "scroll"], ["notificationContainer", ""], [4, "ngFor", "ngForOf"], ["class", "futuristic-loading-more", 4, "ngIf"], [1, "futuristic-notification-card"], [1, "notification-checkbox"], [1, "notification-avatar"], ["alt", "Avatar", "onerror", "this.src='assets/images/default-avatar.png'", 3, "src"], [1, "notification-main-content"], [1, "notification-content"], [1, "notification-header"], [1, "notification-header-top"], [1, "notification-sender"], [1, "notification-time"], [1, "notification-text-container"], [1, "notification-text"], ["class", "notification-message-preview", 4, "ngIf"], ["class", "notification-attachments-indicator", 4, "ngIf"], ["class", "unread-indicator", 4, "ngIf"], [1, "notification-actions"], ["class", "notification-action-button notification-attachment-button", "title", "Voir les pi\u00E8ces jointes", 3, "click", 4, "ngIf"], ["class", "notification-action-button notification-join-button", "title", "Rejoindre la conversation", 3, "disabled", "click", 4, "ngIf"], ["title", "Voir les d\u00E9tails (ne marque PAS comme lu automatiquement)", 1, "notification-action-button", "notification-details-button", 3, "click"], [1, "fas", "fa-info-circle"], ["class", "notification-action-button notification-read-button", "title", "Marquer cette notification comme lue", 3, "click", 4, "ngIf"], ["title", "Supprimer cette notification", 1, "notification-action-button", "notification-delete-button", 3, "click"], [1, "fas", "fa-trash-alt"], [1, "notification-message-preview"], [1, "notification-attachments-indicator"], [1, "fas", "fa-paperclip"], [1, "unread-indicator"], ["title", "Voir les pi\u00E8ces jointes", 1, "notification-action-button", "notification-attachment-button", 3, "click"], ["title", "Rejoindre la conversation", 1, "notification-action-button", "notification-join-button", 3, "disabled", "click"], ["class", "fas fa-comments", 4, "ngIf"], ["class", "fas fa-spinner fa-spin", 4, "ngIf"], [1, "fas", "fa-comments"], [1, "fas", "fa-spinner", "fa-spin"], ["title", "Marquer cette notification comme lue", 1, "notification-action-button", "notification-read-button", 3, "click"], [1, "fas", "fa-check"], [1, "futuristic-loading-more"], [1, "futuristic-loading-circle-small"], [1, "futuristic-loading-text-small"], [1, "fas", "fa-file-alt"], [1, "futuristic-attachments-list"], ["class", "futuristic-attachment-item", 4, "ngFor", "ngForOf"], [1, "futuristic-attachment-item"], ["class", "futuristic-attachment-preview", 4, "ngIf"], ["class", "futuristic-attachment-icon", 4, "ngIf"], [1, "futuristic-attachment-info"], [1, "futuristic-attachment-name"], [1, "futuristic-attachment-meta"], [1, "futuristic-attachment-type"], ["class", "futuristic-attachment-size", 4, "ngIf"], [1, "futuristic-attachment-actions"], ["title", "Ouvrir", 1, "futuristic-attachment-button", 3, "click"], [1, "fas", "fa-external-link-alt"], ["title", "T\u00E9l\u00E9charger", 1, "futuristic-attachment-button", 3, "click"], [1, "fas", "fa-download"], [1, "futuristic-attachment-preview"], ["alt", "Image", 3, "src", "click"], [1, "futuristic-attachment-icon"], [1, "futuristic-attachment-size"], [1, "notification-detail-section"], [1, "notification-detail-title"], [1, "fas", "fa-user", "mr-2"], [1, "notification-sender-info"], ["alt", "Avatar", "onerror", "this.src='assets/images/default-avatar.png'", 1, "notification-sender-avatar", 3, "src"], [1, "notification-sender-details"], [1, "notification-sender-name"], [1, "notification-timestamp"], [1, "fas", "fa-message", "mr-2"], [1, "notification-content-detail"], ["class", "notification-message-detail", 4, "ngIf"], [1, "fas", "fa-tag", "mr-2"], [1, "notification-info-grid"], [1, "notification-info-item"], [1, "notification-info-label"], [1, "notification-info-value"], ["class", "notification-info-item", 4, "ngIf"], ["class", "notification-info-item", "style", "\n              background: rgba(255, 140, 0, 0.1);\n              border: 1px solid rgba(255, 140, 0, 0.3);\n            ", 4, "ngIf"], ["class", "notification-detail-section", 4, "ngIf"], [1, "notification-detail-actions"], ["class", "futuristic-primary-button", 3, "disabled", "click", 4, "ngIf"], ["class", "futuristic-secondary-button", 3, "click", 4, "ngIf"], [1, "fas", "fa-trash-alt", "mr-2"], [1, "notification-message-detail"], [1, "notification-info-item", 2, "background", "rgba(255, 140, 0, 0.1)", "border", "1px solid rgba(255, 140, 0, 0.3)"], [1, "fas", "fa-info-circle", "mr-1"], [1, "notification-info-value", 2, "color", "#ff8c00", "font-style", "italic"], [1, "notification-attachments-grid"], ["class", "notification-attachment-item", 4, "ngFor", "ngForOf"], [1, "notification-attachment-item"], ["class", "notification-attachment-preview", 4, "ngIf"], ["class", "notification-attachment-icon", 4, "ngIf"], [1, "notification-attachment-info"], [1, "notification-attachment-name"], [1, "notification-attachment-meta"], [1, "notification-attachment-type"], ["class", "notification-attachment-size", 4, "ngIf"], [1, "notification-attachment-actions"], ["title", "Ouvrir", 1, "notification-attachment-button", 3, "click"], ["title", "T\u00E9l\u00E9charger", 1, "notification-attachment-button", 3, "click"], [1, "notification-attachment-preview"], [1, "notification-attachment-icon"], [1, "notification-attachment-size"], [1, "futuristic-primary-button", 3, "disabled", "click"], ["class", "fas fa-comments mr-2", 4, "ngIf"], ["class", "fas fa-spinner fa-spin mr-2", 4, "ngIf"], [1, "fas", "fa-comments", "mr-2"], [1, "fas", "fa-spinner", "fa-spin", "mr-2"], [1, "futuristic-secondary-button", 3, "click"], [1, "fas", "fa-check", "mr-2"]],
      template: function NotificationListComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](1, "async");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](2, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](3, "div", 2)(4, "div", 3)(5, "h2", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](6, "i", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](7, " Notifications ");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](8, NotificationListComponent_div_8_Template, 13, 15, "div", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](9, NotificationListComponent_div_9_Template, 12, 1, "div", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](10, NotificationListComponent_div_10_Template, 4, 0, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](11, NotificationListComponent_div_11_Template, 10, 1, "div", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](12, NotificationListComponent_div_12_Template, 9, 0, "div", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](13, "async");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](14, NotificationListComponent_div_14_Template, 5, 4, "div", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](15, "async");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](16, "div", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_Template_div_click_16_listener() {
            return ctx.closeAttachmentsModal();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](17, "div", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_Template_div_click_17_listener($event) {
            return $event.stopPropagation();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](18, "div", 14)(19, "h3", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](20, "i", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](21, " Pi\u00E8ces jointes ");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](22, "button", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_Template_button_click_22_listener() {
            return ctx.closeAttachmentsModal();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](23, "i", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](24, "div", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](25, NotificationListComponent_div_25_Template, 4, 0, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](26, NotificationListComponent_div_26_Template, 7, 0, "div", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](27, NotificationListComponent_div_27_Template, 2, 1, "div", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](28, "div", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_Template_div_click_28_listener() {
            return ctx.closeNotificationDetailsModal();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](29, "div", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_Template_div_click_29_listener($event) {
            return $event.stopPropagation();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](30, "div", 14)(31, "h3", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](32, "i", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](33, " D\u00E9tails de la notification ");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](34, "button", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function NotificationListComponent_Template_button_click_34_listener() {
            return ctx.closeNotificationDetailsModal();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](35, "i", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](36, NotificationListComponent_div_36_Template, 44, 19, "div", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵclassProp"]("dark", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind1"](1, 16, ctx.isDarkMode$));
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](8);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx.showSelectionBar);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.showSelectionBar);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.loading);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.error);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx.loading && !_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind1"](13, 18, ctx.hasNotifications()));
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx.loading && _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind1"](15, 20, ctx.hasNotifications()));
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵstyleProp"]("display", ctx.showAttachmentsModal ? "flex" : "none");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](9);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.loadingAttachments);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx.loadingAttachments && ctx.currentAttachments.length === 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx.loadingAttachments && ctx.currentAttachments.length > 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵstyleProp"]("display", ctx.showNotificationDetailsModal ? "flex" : "none");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](8);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.currentNotification);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_15__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_15__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_15__.NgIf, _angular_common__WEBPACK_IMPORTED_MODULE_15__.AsyncPipe, _angular_common__WEBPACK_IMPORTED_MODULE_15__.DatePipe],
      styles: ["/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJub3RpZmljYXRpb24tbGlzdC5jb21wb25lbnQuY3NzIn0= */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvbm90aWZpY2F0aW9ucy9ub3RpZmljYXRpb24tbGlzdC9ub3RpZmljYXRpb24tbGlzdC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSxnTEFBZ0wiLCJzb3VyY2VSb290IjoiIn0= */"]
    });
  }
}

/***/ }),

/***/ 2285:
/*!***************************************************************************!*\
  !*** ./src/app/views/front/notifications/notifications-routing.module.ts ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NotificationsRoutingModule: () => (/* binding */ NotificationsRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _notification_list_notification_list_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./notification-list/notification-list.component */ 3226);
/* harmony import */ var _messages_message_layout_message_layout_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../messages/message-layout/message-layout.component */ 8076);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 7580);





const routes = [{
  path: '',
  component: _messages_message_layout_message_layout_component__WEBPACK_IMPORTED_MODULE_1__.MessageLayoutComponent,
  data: {
    context: 'notifications'
  },
  children: [{
    path: '',
    component: _notification_list_notification_list_component__WEBPACK_IMPORTED_MODULE_0__.NotificationListComponent,
    data: {
      title: 'Notifications'
    }
  }]
}];
class NotificationsRoutingModule {
  static {
    this.ɵfac = function NotificationsRoutingModule_Factory(t) {
      return new (t || NotificationsRoutingModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineNgModule"]({
      type: NotificationsRoutingModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineInjector"]({
      imports: [_angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵsetNgModuleScope"](NotificationsRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterModule]
  });
})();

/***/ }),

/***/ 6532:
/*!*******************************************************************!*\
  !*** ./src/app/views/front/notifications/notifications.module.ts ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NotificationsModule: () => (/* binding */ NotificationsModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _notifications_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./notifications-routing.module */ 2285);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _notification_list_notification_list_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./notification-list/notification-list.component */ 3226);
/* harmony import */ var src_app_services_message_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/services/message.service */ 4537);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 7580);






class NotificationsModule {
  static {
    this.ɵfac = function NotificationsModule_Factory(t) {
      return new (t || NotificationsModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineNgModule"]({
      type: NotificationsModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineInjector"]({
      providers: [src_app_services_message_service__WEBPACK_IMPORTED_MODULE_2__.MessageService],
      imports: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.CommonModule, _notifications_routing_module__WEBPACK_IMPORTED_MODULE_0__.NotificationsRoutingModule, _angular_router__WEBPACK_IMPORTED_MODULE_5__.RouterModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵsetNgModuleScope"](NotificationsModule, {
    declarations: [_notification_list_notification_list_component__WEBPACK_IMPORTED_MODULE_1__.NotificationListComponent],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.CommonModule, _notifications_routing_module__WEBPACK_IMPORTED_MODULE_0__.NotificationsRoutingModule, _angular_router__WEBPACK_IMPORTED_MODULE_5__.RouterModule]
  });
})();

/***/ })

}]);
//# sourceMappingURL=src_app_views_front_notifications_notifications_module_ts.js.map