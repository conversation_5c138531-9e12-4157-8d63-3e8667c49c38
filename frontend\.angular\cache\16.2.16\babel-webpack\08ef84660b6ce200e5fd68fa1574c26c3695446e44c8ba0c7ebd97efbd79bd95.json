{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject, of } from 'rxjs';\nimport { distinctUntilChanged, switchMap, catchError, tap, filter } from 'rxjs/operators';\nimport { MessageType, CallType } from '../../../../models/message.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../services/message.service\";\nimport * as i2 from \"../../../../services/auth.service\";\nimport * as i3 from \"../../../../services/toast.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nconst _c0 = [\"messagesContainer\"];\nconst _c1 = [\"messageInput\"];\nconst _c2 = [\"fileInput\"];\nconst _c3 = [\"voiceRecorder\"];\nfunction MessageChatComponent_div_0_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.selectedConversation.participants == null ? null : ctx_r2.selectedConversation.participants.length, \" participants \");\n  }\n}\nfunction MessageChatComponent_div_0_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"En ligne\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_0_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Hors ligne\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_0_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r20.startAudioCall());\n    });\n    i0.ɵɵelement(1, \"i\", 38);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_0_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_button_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r22.startVideoCall());\n    });\n    i0.ɵɵelement(1, \"i\", 40);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_0_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵelement(1, \"div\", 42);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_0_div_19_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 61);\n  }\n  if (rf & 2) {\n    const message_r24 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", message_r24.sender.image || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", message_r24.sender.username);\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r24 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", message_r24.sender == null ? null : message_r24.sender.username, \" \");\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"div\", 64);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 65);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r24 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" R\\u00E9ponse \\u00E0 \", message_r24.replyTo.sender == null ? null : message_r24.replyTo.sender.username, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", message_r24.replyTo.content, \" \");\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r24 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", message_r24.content, \" \");\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_7_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r24 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", message_r24.content, \" \");\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r43 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 67)(1, \"img\", 68);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_div_19_div_7_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r43);\n      const message_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r41 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r41.openImageViewer(message_r24.attachments == null ? null : message_r24.attachments[0]));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, MessageChatComponent_div_0_div_19_div_7_div_2_Template, 2, 1, \"div\", 69);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r24 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", message_r24.attachments == null ? null : message_r24.attachments[0] == null ? null : message_r24.attachments[0].url, i0.ɵɵsanitizeUrl)(\"alt\", message_r24.attachments == null ? null : message_r24.attachments[0] == null ? null : message_r24.attachments[0].name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r24.content);\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r47 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelement(1, \"i\", 72);\n    i0.ɵɵelementStart(2, \"div\", 73)(3, \"div\", 74);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 75);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 76);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_div_19_div_8_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r47);\n      const message_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r45 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r45.downloadFile(message_r24.attachments == null ? null : message_r24.attachments[0]));\n    });\n    i0.ɵɵelement(8, \"i\", 77);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r24 = i0.ɵɵnextContext().$implicit;\n    const ctx_r30 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(message_r24.attachments == null ? null : message_r24.attachments[0] == null ? null : message_r24.attachments[0].name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r30.formatFileSize(message_r24.attachments == null ? null : message_r24.attachments[0] == null ? null : message_r24.attachments[0].size), \" \");\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r51 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"button\", 79);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_div_19_div_9_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r51);\n      const message_r24 = i0.ɵɵnextContext().$implicit;\n      const ctx_r49 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r49.playVoiceMessage(message_r24));\n    });\n    i0.ɵɵelement(2, \"i\", 80);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 81);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r24 = i0.ɵɵnextContext().$implicit;\n    const ctx_r31 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r31.formatDuration(message_r24.attachments == null ? null : message_r24.attachments[0] == null ? null : message_r24.attachments[0].duration), \" \");\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_10_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r24 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", message_r24.content, \" \");\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 82);\n    i0.ɵɵelement(1, \"video\", 83);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_0_div_19_div_10_div_2_Template, 2, 1, \"div\", 69);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r24 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", message_r24.attachments == null ? null : message_r24.attachments[0] == null ? null : message_r24.attachments[0].url, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r24.content);\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_11_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r60 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 86);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_div_19_div_11_span_1_Template_span_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r60);\n      const reaction_r57 = restoredCtx.$implicit;\n      const message_r24 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r58 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r58.reactToMessage(message_r24, reaction_r57.emoji));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const reaction_r57 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", reaction_r57.emoji, \" \", reaction_r57.count, \" \");\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 84);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_0_div_19_div_11_span_1_Template, 2, 2, \"span\", 85);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r24 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", message_r24.reactions);\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_15_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 92);\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_15_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 93);\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_15_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 94);\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_15_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 95);\n  }\n}\nfunction MessageChatComponent_div_0_div_19_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 87);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_0_div_19_div_15_i_1_Template, 1, 0, \"i\", 88);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_0_div_19_div_15_i_2_Template, 1, 0, \"i\", 89);\n    i0.ɵɵtemplate(3, MessageChatComponent_div_0_div_19_div_15_i_3_Template, 1, 0, \"i\", 90);\n    i0.ɵɵtemplate(4, MessageChatComponent_div_0_div_19_div_15_i_4_Template, 1, 0, \"i\", 91);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r24 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"read\", message_r24.isRead)(\"pending\", message_r24.isPending)(\"error\", message_r24.isError);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r24.isPending);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r24.isError);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !message_r24.isPending && !message_r24.isError && message_r24.isRead);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !message_r24.isPending && !message_r24.isError && !message_r24.isRead);\n  }\n}\nfunction MessageChatComponent_div_0_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r68 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵtemplate(1, MessageChatComponent_div_0_div_19_img_1_Template, 1, 2, \"img\", 44);\n    i0.ɵɵelementStart(2, \"div\", 45);\n    i0.ɵɵtemplate(3, MessageChatComponent_div_0_div_19_div_3_Template, 2, 1, \"div\", 46);\n    i0.ɵɵtemplate(4, MessageChatComponent_div_0_div_19_div_4_Template, 5, 2, \"div\", 47);\n    i0.ɵɵelementStart(5, \"div\", 48);\n    i0.ɵɵtemplate(6, MessageChatComponent_div_0_div_19_div_6_Template, 2, 1, \"div\", 49);\n    i0.ɵɵtemplate(7, MessageChatComponent_div_0_div_19_div_7_Template, 3, 3, \"div\", 50);\n    i0.ɵɵtemplate(8, MessageChatComponent_div_0_div_19_div_8_Template, 9, 2, \"div\", 51);\n    i0.ɵɵtemplate(9, MessageChatComponent_div_0_div_19_div_9_Template, 5, 1, \"div\", 52);\n    i0.ɵɵtemplate(10, MessageChatComponent_div_0_div_19_div_10_Template, 3, 2, \"div\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, MessageChatComponent_div_0_div_19_div_11_Template, 2, 1, \"div\", 54);\n    i0.ɵɵelementStart(12, \"div\", 55)(13, \"span\", 56);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, MessageChatComponent_div_0_div_19_div_15_Template, 5, 10, \"div\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 58)(17, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_div_19_Template_button_click_17_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r68);\n      const message_r24 = restoredCtx.$implicit;\n      const ctx_r67 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r67.showMessageMenu(message_r24));\n    });\n    i0.ɵɵelement(18, \"i\", 60);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const message_r24 = ctx.$implicit;\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"my-message\", ctx_r9.isMyMessage(message_r24));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.isMyMessage(message_r24) && message_r24.sender);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"my-message\", ctx_r9.isMyMessage(message_r24))(\"other-message\", !ctx_r9.isMyMessage(message_r24));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.selectedConversation.isGroup && !ctx_r9.isMyMessage(message_r24));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r24.replyTo);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitch\", message_r24.type);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r9.MessageType.TEXT);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r9.MessageType.IMAGE);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r9.MessageType.FILE);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r9.MessageType.VOICE_MESSAGE);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r9.MessageType.VIDEO);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r24.reactions && message_r24.reactions.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.formatMessageTime(message_r24.timestamp), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.isMyMessage(message_r24));\n  }\n}\nfunction MessageChatComponent_div_0_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 96)(1, \"div\", 97);\n    i0.ɵɵelement(2, \"div\", 98)(3, \"div\", 98)(4, \"div\", 98);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r10.getTypingText());\n  }\n}\nfunction MessageChatComponent_div_0_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r70 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 99)(1, \"div\", 100)(2, \"div\")(3, \"div\", 101);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 102);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 103);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_div_22_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r70);\n      const ctx_r69 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r69.cancelReply());\n    });\n    i0.ɵɵelement(8, \"i\", 104);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" R\\u00E9ponse \\u00E0 \", ctx_r11.replyingTo.sender == null ? null : ctx_r11.replyingTo.sender.username, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r11.replyingTo.content);\n  }\n}\nfunction MessageChatComponent_div_0_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r72 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 99)(1, \"div\", 100)(2, \"div\")(3, \"div\", 105);\n    i0.ɵɵtext(4, \"Modification du message\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 102);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 103);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_div_23_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r72);\n      const ctx_r71 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r71.cancelEditing());\n    });\n    i0.ɵɵelement(8, \"i\", 104);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r12.editingMessage.content);\n  }\n}\nfunction MessageChatComponent_div_0_div_24_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r77 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 109);\n    i0.ɵɵelement(1, \"i\", 110);\n    i0.ɵɵelementStart(2, \"span\", 111);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 112);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_div_24_div_2_Template_button_click_4_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r77);\n      const i_r75 = restoredCtx.index;\n      const ctx_r76 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r76.removeSelectedFile(i_r75));\n    });\n    i0.ɵɵelement(5, \"i\", 113);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r74 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(file_r74.name);\n  }\n}\nfunction MessageChatComponent_div_0_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 106)(1, \"div\", 107);\n    i0.ɵɵtemplate(2, MessageChatComponent_div_0_div_24_div_2_Template, 6, 1, \"div\", 108);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r13.selectedFiles);\n  }\n}\nfunction MessageChatComponent_div_0_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r79 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 114);\n    i0.ɵɵelement(1, \"i\", 115);\n    i0.ɵɵelementStart(2, \"span\", 116);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 117);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_div_25_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r79);\n      const ctx_r78 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r78.stopVoiceRecording());\n    });\n    i0.ɵɵelement(5, \"i\", 118);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 119);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_div_25_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r79);\n      const ctx_r80 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r80.cancelVoiceRecording());\n    });\n    i0.ɵɵelement(7, \"i\", 104);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r14.formatDuration(ctx_r14.recordingDuration));\n  }\n}\nfunction MessageChatComponent_div_0_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r82 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 120)(1, \"button\", 121);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_div_31_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r82);\n      const ctx_r81 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r81.openFileSelector());\n    });\n    i0.ɵɵelement(2, \"i\", 110);\n    i0.ɵɵelementStart(3, \"span\", 122);\n    i0.ɵɵtext(4, \"Fichier\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 121);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_div_31_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r82);\n      const ctx_r83 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r83.openFileSelector());\n    });\n    i0.ɵɵelement(6, \"i\", 123);\n    i0.ɵɵelementStart(7, \"span\", 122);\n    i0.ɵɵtext(8, \"Image\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction MessageChatComponent_div_0_button_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r85 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 124);\n    i0.ɵɵlistener(\"mousedown\", function MessageChatComponent_div_0_button_37_Template_button_mousedown_0_listener() {\n      i0.ɵɵrestoreView(_r85);\n      const ctx_r84 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r84.startVoiceRecording());\n    });\n    i0.ɵɵelement(1, \"i\", 125);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_div_0_button_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r87 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 126);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_button_38_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r87);\n      const ctx_r86 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r86.sendMessage());\n    });\n    i0.ɵɵelement(1, \"i\", 127);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r18.canSendMessage());\n  }\n}\nfunction MessageChatComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r89 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n    i0.ɵɵelement(3, \"img\", 5);\n    i0.ɵɵelementStart(4, \"div\", 6)(5, \"h3\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 7);\n    i0.ɵɵtemplate(8, MessageChatComponent_div_0_span_8_Template, 2, 1, \"span\", 8);\n    i0.ɵɵtemplate(9, MessageChatComponent_div_0_span_9_Template, 2, 0, \"span\", 8);\n    i0.ɵɵtemplate(10, MessageChatComponent_div_0_span_10_Template, 2, 0, \"span\", 8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 9);\n    i0.ɵɵtemplate(12, MessageChatComponent_div_0_button_12_Template, 2, 0, \"button\", 10);\n    i0.ɵɵtemplate(13, MessageChatComponent_div_0_button_13_Template, 2, 0, \"button\", 11);\n    i0.ɵɵelementStart(14, \"button\", 12);\n    i0.ɵɵelement(15, \"i\", 13);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 14, 15);\n    i0.ɵɵtemplate(18, MessageChatComponent_div_0_div_18_Template, 2, 0, \"div\", 16);\n    i0.ɵɵtemplate(19, MessageChatComponent_div_0_div_19_Template, 19, 18, \"div\", 17);\n    i0.ɵɵtemplate(20, MessageChatComponent_div_0_div_20_Template, 7, 1, \"div\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 19);\n    i0.ɵɵtemplate(22, MessageChatComponent_div_0_div_22_Template, 9, 2, \"div\", 20);\n    i0.ɵɵtemplate(23, MessageChatComponent_div_0_div_23_Template, 9, 1, \"div\", 20);\n    i0.ɵɵtemplate(24, MessageChatComponent_div_0_div_24_Template, 3, 1, \"div\", 21);\n    i0.ɵɵtemplate(25, MessageChatComponent_div_0_div_25_Template, 8, 1, \"div\", 22);\n    i0.ɵɵelementStart(26, \"div\", 23)(27, \"div\", 24)(28, \"div\", 25)(29, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_Template_button_click_29_listener() {\n      i0.ɵɵrestoreView(_r89);\n      const ctx_r88 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r88.toggleAttachmentMenu());\n    });\n    i0.ɵɵelement(30, \"i\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(31, MessageChatComponent_div_0_div_31_Template, 9, 0, \"div\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_0_Template_button_click_32_listener() {\n      i0.ɵɵrestoreView(_r89);\n      const ctx_r90 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r90.toggleEmojiPicker());\n    });\n    i0.ɵɵelement(33, \"i\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"textarea\", 31, 32);\n    i0.ɵɵlistener(\"ngModelChange\", function MessageChatComponent_div_0_Template_textarea_ngModelChange_34_listener($event) {\n      i0.ɵɵrestoreView(_r89);\n      const ctx_r91 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r91.messageContent = $event);\n    })(\"keydown\", function MessageChatComponent_div_0_Template_textarea_keydown_34_listener($event) {\n      i0.ɵɵrestoreView(_r89);\n      const ctx_r92 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r92.onKeyPress($event));\n    })(\"input\", function MessageChatComponent_div_0_Template_textarea_input_34_listener() {\n      i0.ɵɵrestoreView(_r89);\n      const ctx_r93 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r93.onTyping());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"div\", 24);\n    i0.ɵɵtemplate(37, MessageChatComponent_div_0_button_37_Template, 2, 0, \"button\", 33);\n    i0.ɵɵtemplate(38, MessageChatComponent_div_0_button_38_Template, 2, 1, \"button\", 34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"input\", 35, 36);\n    i0.ɵɵlistener(\"change\", function MessageChatComponent_div_0_Template_input_change_39_listener($event) {\n      i0.ɵɵrestoreView(_r89);\n      const ctx_r94 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r94.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"online\", !ctx_r0.selectedConversation.isGroup && ctx_r0.isRecipientOnline());\n    i0.ɵɵproperty(\"src\", ctx_r0.selectedConversation.isGroup ? ctx_r0.selectedConversation.groupPhoto : ctx_r0.getRecipientAvatar(), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r0.selectedConversation.isGroup ? ctx_r0.selectedConversation.groupName : ctx_r0.getRecipientName());\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.selectedConversation.isGroup ? ctx_r0.selectedConversation.groupName : ctx_r0.getRecipientName(), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"online\", !ctx_r0.selectedConversation.isGroup && ctx_r0.isRecipientOnline());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedConversation.isGroup);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.selectedConversation.isGroup && ctx_r0.isRecipientOnline());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.selectedConversation.isGroup && !ctx_r0.isRecipientOnline());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.selectedConversation.isGroup);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.selectedConversation.isGroup);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.messages)(\"ngForTrackBy\", ctx_r0.trackByMessageId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.typingUsers.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.replyingTo);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.editingMessage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedFiles.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isRecording);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showAttachmentMenu);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.messageContent)(\"disabled\", ctx_r0.isRecording);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.messageContent.trim() && !ctx_r0.selectedFiles.length && !ctx_r0.isRecording);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messageContent.trim() || ctx_r0.selectedFiles.length);\n  }\n}\nfunction MessageChatComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 128)(1, \"div\", 129)(2, \"div\", 130)(3, \"div\", 131);\n    i0.ɵɵelement(4, \"i\", 132);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h1\", 133);\n    i0.ɵɵtext(6, \"DevBridge Messages\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 134);\n    i0.ɵɵtext(8, \"Messagerie professionnelle en temps r\\u00E9el\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 135)(10, \"div\", 136)(11, \"div\", 137);\n    i0.ɵɵelement(12, \"i\", 138);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\")(14, \"h4\", 139);\n    i0.ɵɵtext(15, \"Messages en temps r\\u00E9el\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"p\", 140);\n    i0.ɵɵtext(17, \" Conversations instantan\\u00E9es avec notifications \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 136)(19, \"div\", 141);\n    i0.ɵɵelement(20, \"i\", 142);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\")(22, \"h4\", 139);\n    i0.ɵɵtext(23, \"Appels audio/vid\\u00E9o\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"p\", 140);\n    i0.ɵɵtext(25, \"Communication directe int\\u00E9gr\\u00E9e\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"div\", 136)(27, \"div\", 143);\n    i0.ɵɵelement(28, \"i\", 144);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\")(30, \"h4\", 139);\n    i0.ɵɵtext(31, \"Partage de fichiers\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"p\", 140);\n    i0.ɵɵtext(33, \"Images, documents et m\\u00E9dias\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(34, \"div\", 145)(35, \"h3\", 146);\n    i0.ɵɵtext(36, \"Comment commencer ?\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 147)(38, \"p\");\n    i0.ɵɵtext(39, \"\\u2022 S\\u00E9lectionnez une conversation dans la sidebar\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"p\");\n    i0.ɵɵtext(41, \"\\u2022 Ou cliquez sur un contact pour d\\u00E9marrer une discussion\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"p\");\n    i0.ɵɵtext(43, \"\\u2022 Utilisez la recherche pour trouver rapidement\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nexport class MessageChatComponent {\n  constructor(messageService, authService, toastService, route, router, cdr, ngZone) {\n    this.messageService = messageService;\n    this.authService = authService;\n    this.toastService = toastService;\n    this.route = route;\n    this.router = router;\n    this.cdr = cdr;\n    this.ngZone = ngZone;\n    // État du composant\n    this.currentUser = null;\n    this.selectedConversation = null;\n    this.messages = [];\n    this.isLoading = false;\n    this.isTyping = false;\n    this.typingUsers = [];\n    // Pagination\n    this.currentPage = 1;\n    this.hasMoreMessages = true;\n    this.loadingMoreMessages = false;\n    // Formulaire de message\n    this.messageContent = '';\n    this.selectedFiles = [];\n    this.isRecording = false;\n    this.recordingDuration = 0;\n    // États UI\n    this.showEmojiPicker = false;\n    this.showAttachmentMenu = false;\n    this.replyingTo = null;\n    this.editingMessage = null;\n    // Recherche\n    this.searchQuery = '';\n    this.searchResults = [];\n    this.showSearchResults = false;\n    // Subscriptions\n    this.subscriptions = [];\n    // Observables\n    this.conversationId$ = new BehaviorSubject(null);\n    // Constantes\n    this.MessageType = MessageType;\n    this.CallType = CallType;\n  }\n  ngOnInit() {\n    this.initializeComponent();\n    this.setupSubscriptions();\n  }\n  ngAfterViewInit() {\n    this.scrollToBottom();\n  }\n  ngOnDestroy() {\n    this.cleanup();\n  }\n  // ============================================================================\n  // MÉTHODES D'INITIALISATION\n  // ============================================================================\n  initializeComponent() {\n    // Récupérer l'utilisateur actuel\n    this.currentUser = this.authService.getCurrentUser();\n    if (!this.currentUser) {\n      this.router.navigate(['/login']);\n      return;\n    }\n    // Écouter les changements de route pour la conversation\n    this.route.params.subscribe(params => {\n      const conversationId = params['conversationId'];\n      if (conversationId) {\n        this.conversationId$.next(conversationId);\n      }\n    });\n  }\n  setupSubscriptions() {\n    // Subscription pour charger la conversation\n    const conversationSub = this.conversationId$.pipe(filter(id => !!id), distinctUntilChanged(), tap(() => {\n      this.isLoading = true;\n      this.messages = [];\n      this.currentPage = 1;\n      this.hasMoreMessages = true;\n    }), switchMap(conversationId => this.messageService.getConversation(conversationId, 25, 1)), catchError(error => {\n      console.error('Erreur lors du chargement de la conversation:', error);\n      this.toastService.showError('Erreur lors du chargement de la conversation');\n      return of(null);\n    })).subscribe(conversation => {\n      this.isLoading = false;\n      if (conversation) {\n        this.selectedConversation = conversation;\n        this.messages = conversation.messages || [];\n        this.scrollToBottom();\n        this.markMessagesAsRead();\n      }\n      this.cdr.detectChanges();\n    });\n    // Subscription pour les nouveaux messages\n    const messagesSub = this.messageService.subscribeToMessages().subscribe(message => {\n      if (message && this.selectedConversation && message.conversationId === this.selectedConversation.id) {\n        this.addNewMessage(message);\n        this.scrollToBottom();\n        this.markMessageAsRead(message);\n      }\n    });\n    // Subscription pour les indicateurs de frappe\n    const typingSub = this.messageService.subscribeToTypingIndicators().subscribe(event => {\n      if (event && this.selectedConversation && event.conversationId === this.selectedConversation.id) {\n        this.handleTypingIndicator(event);\n      }\n    });\n    this.subscriptions.push(conversationSub, messagesSub, typingSub);\n  }\n  cleanup() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n    if (this.recordingInterval) {\n      clearInterval(this.recordingInterval);\n    }\n    this.stopTyping();\n  }\n  // ============================================================================\n  // MÉTHODES DE GESTION DES MESSAGES\n  // ============================================================================\n  sendMessage() {\n    if (!this.canSendMessage()) {\n      return;\n    }\n    const content = this.messageContent.trim();\n    const files = this.selectedFiles;\n    // Réinitialiser le formulaire\n    this.messageContent = '';\n    this.selectedFiles = [];\n    this.replyingTo = null;\n    this.stopTyping();\n    if (this.editingMessage) {\n      this.updateMessage(content);\n      return;\n    }\n    // Envoyer le message\n    if (content || files.length > 0) {\n      this.sendNewMessage(content, files);\n    }\n  }\n  canSendMessage() {\n    const hasContent = this.messageContent.trim().length > 0;\n    const hasFiles = this.selectedFiles.length > 0;\n    const hasConversation = !!this.selectedConversation;\n    return hasConversation && (hasContent || hasFiles);\n  }\n  sendNewMessage(content, files) {\n    if (!this.selectedConversation || !this.currentUser) return;\n    const recipientId = this.getRecipientId();\n    if (!recipientId) return;\n    // Créer un message temporaire pour l'affichage immédiat\n    const tempMessage = {\n      id: `temp-${Date.now()}`,\n      content,\n      type: files.length > 0 ? this.getFileMessageType(files[0]) : MessageType.TEXT,\n      timestamp: new Date(),\n      sender: this.currentUser,\n      isPending: true,\n      conversationId: this.selectedConversation.id\n    };\n    this.addNewMessage(tempMessage);\n    this.scrollToBottom();\n    // Envoyer le message via le service\n    const sendObservable = files.length > 0 ? this.messageService.sendMessageWithFile(this.currentUser.id || this.currentUser._id, recipientId, content, files[0]) : this.messageService.sendMessage(this.currentUser.id || this.currentUser._id, recipientId, content);\n    sendObservable.subscribe({\n      next: sentMessage => {\n        this.replaceTemporaryMessage(tempMessage.id, sentMessage);\n        this.toastService.showSuccess('Message envoyé');\n      },\n      error: error => {\n        console.error(\"Erreur lors de l'envoi du message:\", error);\n        this.markMessageAsError(tempMessage.id);\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\n      }\n    });\n  }\n  updateMessage(newContent) {\n    if (!this.editingMessage) return;\n    this.messageService.editMessage(this.editingMessage.id, newContent).subscribe({\n      next: updatedMessage => {\n        this.updateMessageInList(updatedMessage);\n        this.editingMessage = null;\n        this.toastService.showSuccess('Message modifié');\n      },\n      error: error => {\n        console.error('Erreur lors de la modification du message:', error);\n        this.toastService.showError('Erreur lors de la modification du message');\n      }\n    });\n  }\n  deleteMessage(message) {\n    if (!message.id || !this.canDeleteMessage(message)) return;\n    if (confirm('Êtes-vous sûr de vouloir supprimer ce message ?')) {\n      this.messageService.deleteMessage(message.id).subscribe({\n        next: () => {\n          this.removeMessageFromList(message.id);\n          this.toastService.showSuccess('Message supprimé');\n        },\n        error: error => {\n          console.error('Erreur lors de la suppression du message:', error);\n          this.toastService.showError('Erreur lors de la suppression du message');\n        }\n      });\n    }\n  }\n  reactToMessage(message, emoji) {\n    if (!message.id) return;\n    this.messageService.reactToMessage(message.id, emoji).subscribe({\n      next: updatedMessage => {\n        this.updateMessageInList(updatedMessage);\n      },\n      error: error => {\n        console.error('Erreur lors de la réaction au message:', error);\n        this.toastService.showError('Erreur lors de la réaction');\n      }\n    });\n  }\n  // ============================================================================\n  // MÉTHODES DE GESTION DES FICHIERS ET MÉDIAS\n  // ============================================================================\n  onFileSelected(event) {\n    const files = event.target.files;\n    if (files && files.length > 0) {\n      this.selectedFiles = Array.from(files);\n      this.showAttachmentMenu = false;\n      // Auto-envoyer si c'est juste un fichier sans texte\n      if (this.messageContent.trim() === '') {\n        this.sendMessage();\n      }\n    }\n  }\n  removeSelectedFile(index) {\n    this.selectedFiles.splice(index, 1);\n  }\n  openFileSelector() {\n    this.fileInput.nativeElement.click();\n  }\n  // ============================================================================\n  // MÉTHODES D'ENREGISTREMENT VOCAL\n  // ============================================================================\n  startVoiceRecording() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const stream = yield navigator.mediaDevices.getUserMedia({\n          audio: true\n        });\n        _this.isRecording = true;\n        _this.recordingDuration = 0;\n        // Démarrer le compteur de durée\n        _this.recordingInterval = setInterval(() => {\n          _this.recordingDuration++;\n        }, 1000);\n        // Ici, vous pouvez implémenter l'enregistrement audio\n        // avec MediaRecorder API\n      } catch (error) {\n        console.error(\"Erreur lors de l'accès au microphone:\", error);\n        _this.toastService.showError(\"Impossible d'accéder au microphone\");\n      }\n    })();\n  }\n  stopVoiceRecording() {\n    this.isRecording = false;\n    if (this.recordingInterval) {\n      clearInterval(this.recordingInterval);\n    }\n    // Ici, vous pouvez traiter l'enregistrement et l'envoyer\n    // comme message vocal\n  }\n\n  cancelVoiceRecording() {\n    this.isRecording = false;\n    this.recordingDuration = 0;\n    if (this.recordingInterval) {\n      clearInterval(this.recordingInterval);\n    }\n  }\n  // ============================================================================\n  // MÉTHODES D'APPELS AUDIO/VIDÉO\n  // ============================================================================\n  startAudioCall() {\n    if (!this.selectedConversation) return;\n    const recipientId = this.getRecipientId();\n    if (!recipientId) return;\n    this.messageService.initiateCall(recipientId, CallType.AUDIO).subscribe({\n      next: call => {\n        this.toastService.showSuccess('Appel audio initié');\n        // Rediriger vers l'interface d'appel\n      },\n\n      error: error => {\n        console.error(\"Erreur lors de l'initiation de l'appel:\", error);\n        this.toastService.showError(\"Erreur lors de l'appel\");\n      }\n    });\n  }\n  startVideoCall() {\n    if (!this.selectedConversation) return;\n    const recipientId = this.getRecipientId();\n    if (!recipientId) return;\n    this.messageService.initiateCall(recipientId, CallType.VIDEO).subscribe({\n      next: call => {\n        this.toastService.showSuccess('Appel vidéo initié');\n        // Rediriger vers l'interface d'appel\n      },\n\n      error: error => {\n        console.error(\"Erreur lors de l'initiation de l'appel vidéo:\", error);\n        this.toastService.showError(\"Erreur lors de l'appel vidéo\");\n      }\n    });\n  }\n  // ============================================================================\n  // MÉTHODES DE GESTION DE LA FRAPPE\n  // ============================================================================\n  onTyping() {\n    if (!this.selectedConversation || this.isTyping) return;\n    this.isTyping = true;\n    this.messageService.startTyping(this.selectedConversation.id).subscribe();\n    // Arrêter la frappe après 3 secondes d'inactivité\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n    this.typingTimeout = setTimeout(() => {\n      this.stopTyping();\n    }, 3000);\n  }\n  stopTyping() {\n    if (!this.isTyping || !this.selectedConversation) return;\n    this.isTyping = false;\n    this.messageService.stopTyping(this.selectedConversation.id).subscribe();\n    if (this.typingTimeout) {\n      clearTimeout(this.typingTimeout);\n    }\n  }\n  // ============================================================================\n  // MÉTHODES UTILITAIRES\n  // ============================================================================\n  getRecipientId() {\n    if (!this.selectedConversation || !this.currentUser) return null;\n    const participants = this.selectedConversation.participants || [];\n    const currentUserId = this.currentUser.id || this.currentUser._id;\n    const recipient = participants.find(p => (p.id || p._id) !== currentUserId);\n    return recipient ? recipient.id || recipient._id : null;\n  }\n  getFileMessageType(file) {\n    const type = file.type.split('/')[0];\n    switch (type) {\n      case 'image':\n        return MessageType.IMAGE;\n      case 'video':\n        return MessageType.VIDEO;\n      case 'audio':\n        return MessageType.AUDIO;\n      default:\n        return MessageType.FILE;\n    }\n  }\n  addNewMessage(message) {\n    this.messages.push(message);\n    this.cdr.detectChanges();\n  }\n  replaceTemporaryMessage(tempId, realMessage) {\n    const index = this.messages.findIndex(m => m.id === tempId);\n    if (index !== -1) {\n      this.messages[index] = realMessage;\n      this.cdr.detectChanges();\n    }\n  }\n  markMessageAsError(messageId) {\n    const message = this.messages.find(m => m.id === messageId);\n    if (message) {\n      message.isPending = false;\n      message.isError = true;\n      this.cdr.detectChanges();\n    }\n  }\n  updateMessageInList(updatedMessage) {\n    const index = this.messages.findIndex(m => m.id === updatedMessage.id);\n    if (index !== -1) {\n      this.messages[index] = updatedMessage;\n      this.cdr.detectChanges();\n    }\n  }\n  removeMessageFromList(messageId) {\n    this.messages = this.messages.filter(m => m.id !== messageId);\n    this.cdr.detectChanges();\n  }\n  canDeleteMessage(message) {\n    if (!this.currentUser || !message.sender) return false;\n    const currentUserId = this.currentUser.id || this.currentUser._id;\n    const senderId = message.sender.id || message.sender._id;\n    return currentUserId === senderId;\n  }\n  handleTypingIndicator(event) {\n    if (!this.currentUser) return;\n    const currentUserId = this.currentUser.id || this.currentUser._id;\n    if (event.userId === currentUserId) return; // Ignorer ses propres indicateurs\n    if (event.isTyping) {\n      // Ajouter l'utilisateur à la liste des utilisateurs en train de taper\n      const user = this.selectedConversation?.participants?.find(p => (p.id || p._id) === event.userId);\n      if (user && !this.typingUsers.find(u => (u.id || u._id) === event.userId)) {\n        this.typingUsers.push(user);\n      }\n    } else {\n      // Retirer l'utilisateur de la liste\n      this.typingUsers = this.typingUsers.filter(u => (u.id || u._id) !== event.userId);\n    }\n    this.cdr.detectChanges();\n  }\n  markMessagesAsRead() {\n    if (!this.messages.length || !this.currentUser) return;\n    const unreadMessages = this.messages.filter(m => !m.isRead && m.sender && (m.sender.id || m.sender._id) !== (this.currentUser.id || this.currentUser._id));\n    unreadMessages.forEach(message => {\n      if (message.id) {\n        this.markMessageAsRead(message);\n      }\n    });\n  }\n  markMessageAsRead(message) {\n    if (!message.id || message.isRead) return;\n    this.messageService.markMessageAsRead(message.id).subscribe({\n      next: updatedMessage => {\n        this.updateMessageInList(updatedMessage);\n      },\n      error: error => {\n        console.error('Erreur lors du marquage comme lu:', error);\n      }\n    });\n  }\n  scrollToBottom() {\n    this.ngZone.runOutsideAngular(() => {\n      setTimeout(() => {\n        if (this.messagesContainer) {\n          const element = this.messagesContainer.nativeElement;\n          element.scrollTop = element.scrollHeight;\n        }\n      }, 100);\n    });\n  }\n  // ============================================================================\n  // MÉTHODES PUBLIQUES POUR LE TEMPLATE\n  // ============================================================================\n  formatMessageTime(timestamp) {\n    const date = new Date(timestamp);\n    const now = new Date();\n    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n    if (diffInHours < 24) {\n      return date.toLocaleTimeString('fr-FR', {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    } else {\n      return date.toLocaleDateString('fr-FR', {\n        day: '2-digit',\n        month: '2-digit'\n      });\n    }\n  }\n  isMyMessage(message) {\n    if (!this.currentUser || !message.sender) return false;\n    const currentUserId = this.currentUser.id || this.currentUser._id;\n    const senderId = message.sender.id || message.sender._id;\n    return currentUserId === senderId;\n  }\n  getTypingText() {\n    if (this.typingUsers.length === 0) return '';\n    if (this.typingUsers.length === 1) {\n      return `${this.typingUsers[0].username} est en train d'écrire...`;\n    } else {\n      return `${this.typingUsers.length} personnes sont en train d'écrire...`;\n    }\n  }\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    } else {\n      this.onTyping();\n    }\n  }\n  toggleEmojiPicker() {\n    this.showEmojiPicker = !this.showEmojiPicker;\n  }\n  toggleAttachmentMenu() {\n    this.showAttachmentMenu = !this.showAttachmentMenu;\n  }\n  startEditingMessage(message) {\n    this.editingMessage = message;\n    this.messageContent = message.content || '';\n    this.messageInput.nativeElement.focus();\n  }\n  cancelEditing() {\n    this.editingMessage = null;\n    this.messageContent = '';\n  }\n  setReplyTo(message) {\n    this.replyingTo = message;\n    this.messageInput.nativeElement.focus();\n  }\n  cancelReply() {\n    this.replyingTo = null;\n  }\n  // ============================================================================\n  // MÉTHODES POUR LE TEMPLATE (MANQUANTES)\n  // ============================================================================\n  getRecipientName() {\n    if (!this.selectedConversation || !this.currentUser) return '';\n    const participants = this.selectedConversation.participants || [];\n    const currentUserId = this.currentUser.id || this.currentUser._id;\n    const recipient = participants.find(p => (p.id || p._id) !== currentUserId);\n    return recipient?.username || 'Utilisateur inconnu';\n  }\n  getRecipientAvatar() {\n    if (!this.selectedConversation || !this.currentUser) return '/assets/images/default-avatar.png';\n    const participants = this.selectedConversation.participants || [];\n    const currentUserId = this.currentUser.id || this.currentUser._id;\n    const recipient = participants.find(p => (p.id || p._id) !== currentUserId);\n    return recipient?.image || '/assets/images/default-avatar.png';\n  }\n  isRecipientOnline() {\n    if (!this.selectedConversation || !this.currentUser) return false;\n    const participants = this.selectedConversation.participants || [];\n    const currentUserId = this.currentUser.id || this.currentUser._id;\n    const recipient = participants.find(p => (p.id || p._id) !== currentUserId);\n    return recipient?.isOnline || false;\n  }\n  trackByMessageId(index, message) {\n    return message.id || message._id || index.toString();\n  }\n  openImageViewer(attachment) {\n    if (!attachment?.url) return;\n    // Ouvrir l'image dans une nouvelle fenêtre ou modal\n    window.open(attachment.url, '_blank');\n  }\n  formatFileSize(size) {\n    if (!size) return '0 B';\n    const units = ['B', 'KB', 'MB', 'GB'];\n    let unitIndex = 0;\n    let fileSize = size;\n    while (fileSize >= 1024 && unitIndex < units.length - 1) {\n      fileSize /= 1024;\n      unitIndex++;\n    }\n    return `${fileSize.toFixed(1)} ${units[unitIndex]}`;\n  }\n  downloadFile(attachment) {\n    if (!attachment?.url) return;\n    const link = document.createElement('a');\n    link.href = attachment.url;\n    link.download = attachment.name || 'file';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  }\n  playVoiceMessage(message) {\n    if (!message.attachments?.[0]?.url) return;\n    this.messageService.playAudio(message.attachments[0].url).catch(error => {\n      console.error('Erreur lors de la lecture du message vocal:', error);\n      this.toastService.showError('Erreur lors de la lecture du message vocal');\n    });\n  }\n  formatDuration(duration) {\n    if (!duration) return '0:00';\n    const minutes = Math.floor(duration / 60);\n    const seconds = duration % 60;\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n  showMessageMenu(message) {\n    // Ici, vous pouvez implémenter un menu contextuel\n    // Pour l'instant, on affiche juste les options disponibles\n    const actions = [];\n    if (this.canDeleteMessage(message)) {\n      actions.push('Supprimer');\n    }\n    if (this.isMyMessage(message)) {\n      actions.push('Modifier');\n    }\n    actions.push('Répondre', 'Transférer', 'Réagir');\n    // Vous pouvez implémenter un vrai menu contextuel ici\n    console.log('Actions disponibles pour ce message:', actions);\n  }\n  static {\n    this.ɵfac = function MessageChatComponent_Factory(t) {\n      return new (t || MessageChatComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.ToastService), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MessageChatComponent,\n      selectors: [[\"app-message-chat\"]],\n      viewQuery: function MessageChatComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n          i0.ɵɵviewQuery(_c3, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messageInput = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.voiceRecorder = _t.first);\n        }\n      },\n      decls: 2,\n      vars: 2,\n      consts: [[\"class\", \"chat-container\", 4, \"ngIf\"], [\"class\", \"flex items-center justify-center h-full bg-gray-900 text-gray-400\", 4, \"ngIf\"], [1, \"chat-container\"], [1, \"chat-header\"], [1, \"user-info\"], [1, \"user-avatar\", 3, \"src\", \"alt\"], [1, \"user-details\"], [1, \"user-status\"], [4, \"ngIf\"], [1, \"chat-actions\"], [\"class\", \"action-btn\", \"title\", \"Appel audio\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"action-btn\", \"title\", \"Appel vid\\u00E9o\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"Options\", 1, \"action-btn\"], [1, \"fas\", \"fa-ellipsis-v\"], [1, \"messages-container\"], [\"messagesContainer\", \"\"], [\"class\", \"flex justify-center py-4\", 4, \"ngIf\"], [\"class\", \"message\", 3, \"my-message\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"typing-indicator\", 4, \"ngIf\"], [1, \"message-input-container\"], [\"class\", \"reply-preview\", 4, \"ngIf\"], [\"class\", \"mb-3\", 4, \"ngIf\"], [\"class\", \"recording-indicator mb-3\", 4, \"ngIf\"], [1, \"input-wrapper\"], [1, \"input-actions\"], [1, \"relative\"], [\"title\", \"Pi\\u00E8ce jointe\", 1, \"input-btn\", 3, \"click\"], [1, \"fas\", \"fa-paperclip\"], [\"class\", \"absolute bottom-full left-0 mb-2 bg-gray-800 rounded-lg shadow-lg p-2 space-y-1\", 4, \"ngIf\"], [\"title\", \"Emoji\", 1, \"input-btn\", 3, \"click\"], [1, \"fas\", \"fa-smile\"], [\"placeholder\", \"Tapez votre message...\", \"rows\", \"1\", 1, \"message-input\", 3, \"ngModel\", \"disabled\", \"ngModelChange\", \"keydown\", \"input\"], [\"messageInput\", \"\"], [\"class\", \"input-btn\", \"title\", \"Message vocal\", 3, \"mousedown\", 4, \"ngIf\"], [\"class\", \"send-btn\", \"title\", \"Envoyer\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"type\", \"file\", \"multiple\", \"\", \"accept\", \"image/*,video/*,audio/*,.pdf,.doc,.docx,.txt\", 1, \"hidden\", 3, \"change\"], [\"fileInput\", \"\"], [\"title\", \"Appel audio\", 1, \"action-btn\", 3, \"click\"], [1, \"fas\", \"fa-phone\"], [\"title\", \"Appel vid\\u00E9o\", 1, \"action-btn\", 3, \"click\"], [1, \"fas\", \"fa-video\"], [1, \"flex\", \"justify-center\", \"py-4\"], [1, \"animate-spin\", \"rounded-full\", \"h-8\", \"w-8\", \"border-b-2\", \"border-blue-500\"], [1, \"message\"], [\"class\", \"message-avatar\", 3, \"src\", \"alt\", 4, \"ngIf\"], [1, \"message-content\"], [\"class\", \"text-xs text-blue-400 mb-1 font-medium\", 4, \"ngIf\"], [\"class\", \"reply-preview mb-2\", 4, \"ngIf\"], [3, \"ngSwitch\"], [\"class\", \"message-text\", 4, \"ngSwitchCase\"], [\"class\", \"message-image\", 4, \"ngSwitchCase\"], [\"class\", \"message-file\", 4, \"ngSwitchCase\"], [\"class\", \"voice-message\", 4, \"ngSwitchCase\"], [\"class\", \"message-video\", 4, \"ngSwitchCase\"], [\"class\", \"flex flex-wrap gap-1 mt-2\", 4, \"ngIf\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mt-1\"], [1, \"message-time\"], [\"class\", \"message-status\", 3, \"read\", \"pending\", \"error\", 4, \"ngIf\"], [1, \"message-menu\", \"absolute\", \"top-0\", \"right-0\", \"hidden\", \"group-hover:block\"], [1, \"text-gray-400\", \"hover:text-white\", \"p-1\", 3, \"click\"], [1, \"fas\", \"fa-ellipsis-h\", \"text-xs\"], [1, \"message-avatar\", 3, \"src\", \"alt\"], [1, \"text-xs\", \"text-blue-400\", \"mb-1\", \"font-medium\"], [1, \"reply-preview\", \"mb-2\"], [1, \"text-xs\", \"text-gray-400\"], [1, \"text-sm\", \"text-gray-300\", \"truncate\"], [1, \"message-text\"], [1, \"message-image\"], [1, \"message-image\", 3, \"src\", \"alt\", \"click\"], [\"class\", \"message-text mt-2\", 4, \"ngIf\"], [1, \"message-text\", \"mt-2\"], [1, \"message-file\"], [1, \"file-icon\", \"fas\", \"fa-file\"], [1, \"file-info\"], [1, \"file-name\"], [1, \"file-size\"], [1, \"text-blue-400\", \"hover:text-blue-300\", 3, \"click\"], [1, \"fas\", \"fa-download\"], [1, \"voice-message\"], [1, \"voice-play-btn\", 3, \"click\"], [1, \"fas\", \"fa-play\", \"text-white\", \"text-xs\"], [1, \"voice-duration\"], [1, \"message-video\"], [\"controls\", \"\", 1, \"max-w-xs\", \"rounded-lg\", 3, \"src\"], [1, \"flex\", \"flex-wrap\", \"gap-1\", \"mt-2\"], [\"class\", \"text-xs bg-gray-600 rounded-full px-2 py-1 cursor-pointer\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"text-xs\", \"bg-gray-600\", \"rounded-full\", \"px-2\", \"py-1\", \"cursor-pointer\", 3, \"click\"], [1, \"message-status\"], [\"class\", \"fas fa-clock\", 4, \"ngIf\"], [\"class\", \"fas fa-exclamation-triangle\", 4, \"ngIf\"], [\"class\", \"fas fa-check-double\", 4, \"ngIf\"], [\"class\", \"fas fa-check\", 4, \"ngIf\"], [1, \"fas\", \"fa-clock\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"fas\", \"fa-check-double\"], [1, \"fas\", \"fa-check\"], [1, \"typing-indicator\"], [1, \"typing-dots\"], [1, \"typing-dot\"], [1, \"reply-preview\"], [1, \"reply-header\"], [1, \"text-xs\", \"text-blue-400\"], [1, \"reply-text\"], [1, \"text-gray-400\", \"hover:text-white\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"text-xs\", \"text-yellow-400\"], [1, \"mb-3\"], [1, \"flex\", \"flex-wrap\", \"gap-2\"], [\"class\", \"flex items-center space-x-2 bg-gray-700 rounded-lg p-2\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"bg-gray-700\", \"rounded-lg\", \"p-2\"], [1, \"fas\", \"fa-file\", \"text-blue-400\"], [1, \"text-sm\", \"text-white\", \"truncate\", \"max-w-32\"], [1, \"text-red-400\", \"hover:text-red-300\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"text-xs\"], [1, \"recording-indicator\", \"mb-3\"], [1, \"fas\", \"fa-microphone\", \"text-white\"], [1, \"recording-time\"], [1, \"text-white\", \"hover:text-gray-300\", 3, \"click\"], [1, \"fas\", \"fa-stop\"], [1, \"text-white\", \"hover:text-gray-300\", \"ml-2\", 3, \"click\"], [1, \"absolute\", \"bottom-full\", \"left-0\", \"mb-2\", \"bg-gray-800\", \"rounded-lg\", \"shadow-lg\", \"p-2\", \"space-y-1\"], [1, \"flex\", \"items-center\", \"space-x-2\", \"w-full\", \"p-2\", \"hover:bg-gray-700\", \"rounded\", \"text-left\", 3, \"click\"], [1, \"text-white\", \"text-sm\"], [1, \"fas\", \"fa-image\", \"text-green-400\"], [\"title\", \"Message vocal\", 1, \"input-btn\", 3, \"mousedown\"], [1, \"fas\", \"fa-microphone\"], [\"title\", \"Envoyer\", 1, \"send-btn\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-paper-plane\", \"text-white\"], [1, \"flex\", \"items-center\", \"justify-center\", \"h-full\", \"bg-gray-900\", \"text-gray-400\"], [1, \"text-center\", \"max-w-md\", \"mx-auto\", \"p-8\"], [1, \"mb-8\"], [1, \"w-24\", \"h-24\", \"mx-auto\", \"bg-gradient-to-br\", \"from-blue-500\", \"to-purple-600\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"mb-4\"], [1, \"fas\", \"fa-comments\", \"text-3xl\", \"text-white\"], [1, \"text-3xl\", \"font-bold\", \"text-white\", \"mb-2\"], [1, \"text-gray-400\"], [1, \"space-y-4\", \"mb-8\"], [1, \"flex\", \"items-center\", \"space-x-3\", \"text-left\"], [1, \"w-10\", \"h-10\", \"bg-blue-600\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-bolt\", \"text-white\", \"text-sm\"], [1, \"text-white\", \"font-medium\"], [1, \"text-sm\", \"text-gray-400\"], [1, \"w-10\", \"h-10\", \"bg-green-600\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-phone\", \"text-white\", \"text-sm\"], [1, \"w-10\", \"h-10\", \"bg-purple-600\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\"], [1, \"fas\", \"fa-file\", \"text-white\", \"text-sm\"], [1, \"bg-gray-800\", \"rounded-lg\", \"p-6\", \"border\", \"border-gray-700\"], [1, \"text-lg\", \"font-semibold\", \"text-white\", \"mb-3\"], [1, \"space-y-2\", \"text-sm\", \"text-gray-300\"]],\n      template: function MessageChatComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, MessageChatComponent_div_0_Template, 41, 25, \"div\", 0);\n          i0.ɵɵtemplate(1, MessageChatComponent_div_1_Template, 44, 0, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedConversation);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.selectedConversation);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i5.NgSwitch, i5.NgSwitchCase, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgModel],\n      styles: [\"\\n\\n\\n\\n\\n.chat-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 100%;\\n  flex-direction: column;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n\\n\\n\\n\\n\\n\\n.chat-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  border-bottom-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\\n  padding: 1rem;\\n  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.user-info[_ngcontent-%COMP%]    > [_ngcontent-%COMP%]:not([hidden])    ~ [_ngcontent-%COMP%]:not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n\\n.user-avatar[_ngcontent-%COMP%] {\\n  height: 2.5rem;\\n  width: 2.5rem;\\n  border-radius: 9999px;\\n  border-width: 2px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\\n  box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);\\n}\\n\\n.user-details[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n\\n.user-status[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\n\\n.user-status.online[_ngcontent-%COMP%] {\\n  --tw-text-opacity: 1;\\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\\n}\\n\\n.chat-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.chat-actions[_ngcontent-%COMP%]    > [_ngcontent-%COMP%]:not([hidden])    ~ [_ngcontent-%COMP%]:not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  border-radius: 9999px;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n  padding: 0.5rem;\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n}\\n\\n.action-btn[_ngcontent-%COMP%]:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  border: 1px solid rgba(59, 130, 246, 0.3);\\n}\\n\\n.action-btn[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 0 15px rgba(59, 130, 246, 0.4);\\n  border-color: rgba(59, 130, 246, 0.6);\\n}\\n\\n.action-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  --tw-text-opacity: 1;\\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\\n}\\n\\n\\n\\n\\n\\n\\n.messages-container[_ngcontent-%COMP%] {\\n  flex: 1 1 0%;\\n}\\n\\n.messages-container[_ngcontent-%COMP%]    > [_ngcontent-%COMP%]:not([hidden])    ~ [_ngcontent-%COMP%]:not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\n\\n.messages-container[_ngcontent-%COMP%] {\\n  overflow-y: auto;\\n  padding: 1rem;\\n  background: linear-gradient(180deg, #111827 0%, #0f172a 100%);\\n  scrollbar-width: thin;\\n  scrollbar-color: #374151 #1f2937;\\n}\\n\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #1f2937;\\n}\\n\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #374151;\\n  border-radius: 3px;\\n}\\n\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #4b5563;\\n}\\n\\n\\n\\n\\n\\n\\n.message[_ngcontent-%COMP%] {\\n  display: flex;\\n  max-width: 20rem;\\n  align-items: flex-end;\\n}\\n\\n.message[_ngcontent-%COMP%]    > [_ngcontent-%COMP%]:not([hidden])    ~ [_ngcontent-%COMP%]:not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n\\n@media (min-width: 768px) {\\n\\n  .message[_ngcontent-%COMP%] {\\n    max-width: 28rem;\\n  }\\n}\\n\\n.message[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_messageSlideIn 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_messageSlideIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n.message.my-message[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  flex-direction: row-reverse;\\n}\\n\\n.message.my-message[_ngcontent-%COMP%]    > [_ngcontent-%COMP%]:not([hidden])    ~ [_ngcontent-%COMP%]:not([hidden]) {\\n  --tw-space-x-reverse: 1;\\n}\\n\\n.message-avatar[_ngcontent-%COMP%] {\\n  height: 2rem;\\n  width: 2rem;\\n  border-radius: 9999px;\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\\n}\\n\\n.message-content[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  overflow-wrap: break-word;\\n  border-radius: 1rem;\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n  position: relative;\\n}\\n\\n.message-content.my-message[_ngcontent-%COMP%] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\\n  box-shadow: 0 2px 10px rgba(59, 130, 246, 0.3);\\n}\\n\\n.message-content.other-message[_ngcontent-%COMP%] {\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n  background: linear-gradient(135deg, #374151 0%, #1f2937 100%);\\n}\\n\\n.message-text[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  line-height: 1.625;\\n}\\n\\n.message-time[_ngcontent-%COMP%] {\\n  margin-top: 0.25rem;\\n  display: block;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\n\\n.message-status[_ngcontent-%COMP%] {\\n  margin-top: 0.25rem;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\n\\n.message-status.read[_ngcontent-%COMP%] {\\n  --tw-text-opacity: 1;\\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\\n}\\n\\n.message-status.pending[_ngcontent-%COMP%] {\\n  --tw-text-opacity: 1;\\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\\n}\\n\\n.message-status.error[_ngcontent-%COMP%] {\\n  --tw-text-opacity: 1;\\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\\n}\\n\\n\\n\\n\\n\\n\\n.message-image[_ngcontent-%COMP%] {\\n  max-width: 20rem;\\n  cursor: pointer;\\n  border-radius: 0.5rem;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);\\n}\\n\\n.message-file[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.message-file[_ngcontent-%COMP%]    > [_ngcontent-%COMP%]:not([hidden])    ~ [_ngcontent-%COMP%]:not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n\\n.message-file[_ngcontent-%COMP%] {\\n  border-radius: 0.5rem;\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\\n  padding: 0.75rem;\\n}\\n\\n.file-icon[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n  --tw-text-opacity: 1;\\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\\n}\\n\\n.file-info[_ngcontent-%COMP%] {\\n  flex: 1 1 0%;\\n}\\n\\n.file-name[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  font-weight: 500;\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n\\n.file-size[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\n\\n.voice-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.voice-message[_ngcontent-%COMP%]    > [_ngcontent-%COMP%]:not([hidden])    ~ [_ngcontent-%COMP%]:not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n\\n.voice-message[_ngcontent-%COMP%] {\\n  border-radius: 0.5rem;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\\n  padding: 0.75rem;\\n}\\n\\n.voice-play-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 2rem;\\n  width: 2rem;\\n  cursor: pointer;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 9999px;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\\n}\\n\\n.voice-duration[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\n\\n\\n\\n\\n\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%]    > [_ngcontent-%COMP%]:not([hidden])    ~ [_ngcontent-%COMP%]:not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  padding: 0.75rem;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\n\\n.typing-dots[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n\\n.typing-dots[_ngcontent-%COMP%]    > [_ngcontent-%COMP%]:not([hidden])    ~ [_ngcontent-%COMP%]:not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n\\n.typing-dot[_ngcontent-%COMP%] {\\n  height: 0.5rem;\\n  width: 0.5rem;\\n  border-radius: 9999px;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));\\n  animation: _ngcontent-%COMP%_typingPulse 1.4s infinite ease-in-out;\\n}\\n\\n.typing-dot[_ngcontent-%COMP%]:nth-child(1) {\\n  animation-delay: -0.32s;\\n}\\n\\n.typing-dot[_ngcontent-%COMP%]:nth-child(2) {\\n  animation-delay: -0.16s;\\n}\\n\\n@keyframes _ngcontent-%COMP%_typingPulse {\\n  0%, 80%, 100% {\\n    transform: scale(0.8);\\n    opacity: 0.5;\\n  }\\n  40% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n.message-input-container[_ngcontent-%COMP%] {\\n  border-top-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\\n  padding: 1rem;\\n  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);\\n}\\n\\n.reply-preview[_ngcontent-%COMP%] {\\n  margin-bottom: 0.75rem;\\n  border-radius: 0.5rem;\\n  border-left-width: 4px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n  padding: 0.5rem;\\n}\\n\\n.reply-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n\\n.reply-text[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n  --tw-text-opacity: 1;\\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\\n}\\n\\n.input-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-end;\\n}\\n\\n.input-wrapper[_ngcontent-%COMP%]    > [_ngcontent-%COMP%]:not([hidden])    ~ [_ngcontent-%COMP%]:not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n\\n.message-input[_ngcontent-%COMP%] {\\n  flex: 1 1 0%;\\n  resize: none;\\n  border-radius: 1rem;\\n  border-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n\\n.message-input[_ngcontent-%COMP%]::placeholder {\\n  --tw-placeholder-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));\\n}\\n\\n.message-input[_ngcontent-%COMP%] {\\n  min-height: 40px;\\n  max-height: 120px;\\n  transition: all 0.2s ease;\\n}\\n\\n.message-input[_ngcontent-%COMP%]:focus {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);\\n}\\n\\n.input-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.input-actions[_ngcontent-%COMP%]    > [_ngcontent-%COMP%]:not([hidden])    ~ [_ngcontent-%COMP%]:not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n\\n.input-btn[_ngcontent-%COMP%] {\\n  border-radius: 9999px;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n  padding: 0.5rem;\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n}\\n\\n.input-btn[_ngcontent-%COMP%]:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));\\n}\\n\\n.input-btn[_ngcontent-%COMP%] {\\n  border: 1px solid rgba(59, 130, 246, 0.3);\\n}\\n\\n.input-btn[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 0 10px rgba(59, 130, 246, 0.4);\\n}\\n\\n.input-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  --tw-text-opacity: 1;\\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\\n}\\n\\n.send-btn[_ngcontent-%COMP%] {\\n  border-radius: 9999px;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\\n  padding: 0.5rem;\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n}\\n\\n.send-btn[_ngcontent-%COMP%]:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));\\n}\\n\\n.send-btn[_ngcontent-%COMP%] {\\n  box-shadow: 0 2px 10px rgba(59, 130, 246, 0.3);\\n}\\n\\n.send-btn[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.5);\\n}\\n\\n.send-btn[_ngcontent-%COMP%]:disabled {\\n  cursor: not-allowed;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));\\n  box-shadow: none;\\n}\\n\\n\\n\\n\\n\\n\\n.recording-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.recording-indicator[_ngcontent-%COMP%]    > [_ngcontent-%COMP%]:not([hidden])    ~ [_ngcontent-%COMP%]:not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n\\n.recording-indicator[_ngcontent-%COMP%] {\\n  border-radius: 0.5rem;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\\n  padding: 0.75rem;\\n  animation: _ngcontent-%COMP%_recordingPulse 1s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_recordingPulse {\\n  0%, 100% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0.7;\\n  }\\n}\\n\\n.recording-time[_ngcontent-%COMP%] {\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace;\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n\\n\\n\\n\\n\\n\\n@media (max-width: 768px) {\\n  .message[_ngcontent-%COMP%] {\\n    max-width: 20rem;\\n  }\\n  \\n  .chat-header[_ngcontent-%COMP%] {\\n    padding-left: 0.75rem;\\n    padding-right: 0.75rem;\\n    padding-top: 0.5rem;\\n    padding-bottom: 0.5rem;\\n  }\\n  \\n  .messages-container[_ngcontent-%COMP%] {\\n    padding-left: 0.75rem;\\n    padding-right: 0.75rem;\\n  }\\n  \\n  .message-input-container[_ngcontent-%COMP%] {\\n    padding-left: 0.75rem;\\n    padding-right: 0.75rem;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "of", "distinctUntilChanged", "switchMap", "catchError", "tap", "filter", "MessageType", "CallType", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r2", "selectedConversation", "participants", "length", "ɵɵlistener", "MessageChatComponent_div_0_button_12_Template_button_click_0_listener", "ɵɵrestoreView", "_r21", "ctx_r20", "ɵɵnextContext", "ɵɵresetView", "startAudioCall", "ɵɵelement", "MessageChatComponent_div_0_button_13_Template_button_click_0_listener", "_r23", "ctx_r22", "startVideoCall", "ɵɵproperty", "message_r24", "sender", "image", "ɵɵsanitizeUrl", "username", "replyTo", "content", "MessageChatComponent_div_0_div_19_div_7_Template_img_click_1_listener", "_r43", "$implicit", "ctx_r41", "openImageViewer", "attachments", "ɵɵtemplate", "MessageChatComponent_div_0_div_19_div_7_div_2_Template", "url", "name", "MessageChatComponent_div_0_div_19_div_8_Template_button_click_7_listener", "_r47", "ctx_r45", "downloadFile", "ɵɵtextInterpolate", "ctx_r30", "formatFileSize", "size", "MessageChatComponent_div_0_div_19_div_9_Template_button_click_1_listener", "_r51", "ctx_r49", "playVoiceMessage", "ctx_r31", "formatDuration", "duration", "MessageChatComponent_div_0_div_19_div_10_div_2_Template", "MessageChatComponent_div_0_div_19_div_11_span_1_Template_span_click_0_listener", "restoredCtx", "_r60", "reaction_r57", "ctx_r58", "reactToMessage", "emoji", "ɵɵtextInterpolate2", "count", "MessageChatComponent_div_0_div_19_div_11_span_1_Template", "reactions", "MessageChatComponent_div_0_div_19_div_15_i_1_Template", "MessageChatComponent_div_0_div_19_div_15_i_2_Template", "MessageChatComponent_div_0_div_19_div_15_i_3_Template", "MessageChatComponent_div_0_div_19_div_15_i_4_Template", "ɵɵclassProp", "isRead", "isPending", "isError", "MessageChatComponent_div_0_div_19_img_1_Template", "MessageChatComponent_div_0_div_19_div_3_Template", "MessageChatComponent_div_0_div_19_div_4_Template", "MessageChatComponent_div_0_div_19_div_6_Template", "MessageChatComponent_div_0_div_19_div_7_Template", "MessageChatComponent_div_0_div_19_div_8_Template", "MessageChatComponent_div_0_div_19_div_9_Template", "MessageChatComponent_div_0_div_19_div_10_Template", "MessageChatComponent_div_0_div_19_div_11_Template", "MessageChatComponent_div_0_div_19_div_15_Template", "MessageChatComponent_div_0_div_19_Template_button_click_17_listener", "_r68", "ctx_r67", "showMessageMenu", "ctx_r9", "isMyMessage", "isGroup", "type", "TEXT", "IMAGE", "FILE", "VOICE_MESSAGE", "VIDEO", "formatMessageTime", "timestamp", "ctx_r10", "getTypingText", "MessageChatComponent_div_0_div_22_Template_button_click_7_listener", "_r70", "ctx_r69", "cancelReply", "ctx_r11", "replyingTo", "MessageChatComponent_div_0_div_23_Template_button_click_7_listener", "_r72", "ctx_r71", "cancelEditing", "ctx_r12", "editingMessage", "MessageChatComponent_div_0_div_24_div_2_Template_button_click_4_listener", "_r77", "i_r75", "index", "ctx_r76", "removeSelectedFile", "file_r74", "MessageChatComponent_div_0_div_24_div_2_Template", "ctx_r13", "selectedFiles", "MessageChatComponent_div_0_div_25_Template_button_click_4_listener", "_r79", "ctx_r78", "stopVoiceRecording", "MessageChatComponent_div_0_div_25_Template_button_click_6_listener", "ctx_r80", "cancelVoiceRecording", "ctx_r14", "recordingDuration", "MessageChatComponent_div_0_div_31_Template_button_click_1_listener", "_r82", "ctx_r81", "openFileSelector", "MessageChatComponent_div_0_div_31_Template_button_click_5_listener", "ctx_r83", "MessageChatComponent_div_0_button_37_Template_button_mousedown_0_listener", "_r85", "ctx_r84", "startVoiceRecording", "MessageChatComponent_div_0_button_38_Template_button_click_0_listener", "_r87", "ctx_r86", "sendMessage", "ctx_r18", "canSendMessage", "MessageChatComponent_div_0_span_8_Template", "MessageChatComponent_div_0_span_9_Template", "MessageChatComponent_div_0_span_10_Template", "MessageChatComponent_div_0_button_12_Template", "MessageChatComponent_div_0_button_13_Template", "MessageChatComponent_div_0_div_18_Template", "MessageChatComponent_div_0_div_19_Template", "MessageChatComponent_div_0_div_20_Template", "MessageChatComponent_div_0_div_22_Template", "MessageChatComponent_div_0_div_23_Template", "MessageChatComponent_div_0_div_24_Template", "MessageChatComponent_div_0_div_25_Template", "MessageChatComponent_div_0_Template_button_click_29_listener", "_r89", "ctx_r88", "toggleAttachmentMenu", "MessageChatComponent_div_0_div_31_Template", "MessageChatComponent_div_0_Template_button_click_32_listener", "ctx_r90", "toggleEmojiPicker", "MessageChatComponent_div_0_Template_textarea_ngModelChange_34_listener", "$event", "ctx_r91", "messageContent", "MessageChatComponent_div_0_Template_textarea_keydown_34_listener", "ctx_r92", "onKeyPress", "MessageChatComponent_div_0_Template_textarea_input_34_listener", "ctx_r93", "onTyping", "MessageChatComponent_div_0_button_37_Template", "MessageChatComponent_div_0_button_38_Template", "MessageChatComponent_div_0_Template_input_change_39_listener", "ctx_r94", "onFileSelected", "ctx_r0", "isRecipientOnline", "groupPhoto", "getRecipientAvatar", "groupName", "getRecipientName", "isLoading", "messages", "trackByMessageId", "typingUsers", "isRecording", "showAttachmentMenu", "trim", "MessageChatComponent", "constructor", "messageService", "authService", "toastService", "route", "router", "cdr", "ngZone", "currentUser", "isTyping", "currentPage", "hasMoreMessages", "loadingMoreMessages", "showEmojiPicker", "searchQuery", "searchResults", "showSearchResults", "subscriptions", "conversationId$", "ngOnInit", "initializeComponent", "setupSubscriptions", "ngAfterViewInit", "scrollToBottom", "ngOnDestroy", "cleanup", "getCurrentUser", "navigate", "params", "subscribe", "conversationId", "next", "conversationSub", "pipe", "id", "getConversation", "error", "console", "showError", "conversation", "markMessagesAsRead", "detectChanges", "messagesSub", "subscribeToMessages", "message", "addNewMessage", "markMessageAsRead", "typingSub", "subscribeToTypingIndicators", "event", "handleTypingIndicator", "push", "for<PERSON>ach", "sub", "unsubscribe", "typingTimeout", "clearTimeout", "recordingInterval", "clearInterval", "stopTyping", "files", "updateMessage", "sendNewMessage", "<PERSON><PERSON><PERSON><PERSON>", "hasFiles", "hasConversation", "recipientId", "getRecipientId", "tempMessage", "Date", "now", "getFileMessageType", "sendObservable", "sendMessageWithFile", "_id", "sentMessage", "replaceTemporaryMessage", "showSuccess", "markMessageAsError", "newContent", "editMessage", "updatedMessage", "updateMessageInList", "deleteMessage", "canDeleteMessage", "confirm", "removeMessageFromList", "target", "Array", "from", "splice", "fileInput", "nativeElement", "click", "_this", "_asyncToGenerator", "stream", "navigator", "mediaDevices", "getUserMedia", "audio", "setInterval", "initiateCall", "AUDIO", "call", "startTyping", "setTimeout", "currentUserId", "recipient", "find", "p", "file", "split", "tempId", "realMessage", "findIndex", "m", "messageId", "senderId", "userId", "user", "u", "unreadMessages", "runOutsideAngular", "messagesContainer", "element", "scrollTop", "scrollHeight", "date", "diffInHours", "getTime", "toLocaleTimeString", "hour", "minute", "toLocaleDateString", "day", "month", "key", "shift<PERSON>ey", "preventDefault", "startEditingMessage", "messageInput", "focus", "setReplyTo", "isOnline", "toString", "attachment", "window", "open", "units", "unitIndex", "fileSize", "toFixed", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "playAudio", "catch", "minutes", "Math", "floor", "seconds", "padStart", "actions", "log", "ɵɵdirectiveInject", "i1", "MessageService", "i2", "AuthService", "i3", "ToastService", "i4", "ActivatedRoute", "Router", "ChangeDetectorRef", "NgZone", "selectors", "viewQuery", "MessageChatComponent_Query", "rf", "ctx", "MessageChatComponent_div_0_Template", "MessageChatComponent_div_1_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.html"], "sourcesContent": ["import {\r\n  Compo<PERSON>,\r\n  On<PERSON>nit,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  AfterViewInit,\r\n  ViewChild,\r\n  ElementRef,\r\n  ChangeDetectorRef,\r\n  NgZone,\r\n} from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { Subscription, BehaviorSubject, combineLatest, of } from 'rxjs';\r\nimport {\r\n  debounceTime,\r\n  distinctUntilChanged,\r\n  switchMap,\r\n  catchError,\r\n  tap,\r\n  filter,\r\n  map,\r\n} from 'rxjs/operators';\r\nimport { MessageService } from '../../../../services/message.service';\r\nimport { AuthService } from '../../../../services/auth.service';\r\nimport { ToastService } from '../../../../services/toast.service';\r\nimport {\r\n  Message,\r\n  Conversation,\r\n  User,\r\n  MessageType,\r\n  CallType,\r\n  Attachment,\r\n} from '../../../../models/message.model';\r\n\r\n@Component({\r\n  selector: 'app-message-chat',\r\n  templateUrl: './message-chat.component.html',\r\n  styleUrls: ['./message-chat.component.css'],\r\n})\r\nexport class MessageChatComponent implements OnInit, AfterViewInit, OnDestroy {\r\n  @ViewChild('messagesContainer') messagesContainer!: ElementRef;\r\n  @ViewChild('messageInput') messageInput!: ElementRef;\r\n  @ViewChild('fileInput') fileInput!: ElementRef;\r\n  @ViewChild('voiceRecorder') voiceRecorder!: ElementRef;\r\n\r\n  // État du composant\r\n  currentUser: User | null = null;\r\n  selectedConversation: Conversation | null = null;\r\n  messages: Message[] = [];\r\n  isLoading = false;\r\n  isTyping = false;\r\n  typingUsers: User[] = [];\r\n\r\n  // Pagination\r\n  currentPage = 1;\r\n  hasMoreMessages = true;\r\n  loadingMoreMessages = false;\r\n\r\n  // Formulaire de message\r\n  messageContent = '';\r\n  selectedFiles: File[] = [];\r\n  isRecording = false;\r\n  recordingDuration = 0;\r\n\r\n  // États UI\r\n  showEmojiPicker = false;\r\n  showAttachmentMenu = false;\r\n  replyingTo: Message | null = null;\r\n  editingMessage: Message | null = null;\r\n\r\n  // Recherche\r\n  searchQuery = '';\r\n  searchResults: Message[] = [];\r\n  showSearchResults = false;\r\n\r\n  // Subscriptions\r\n  private subscriptions: Subscription[] = [];\r\n  private typingTimeout: any;\r\n  private recordingInterval: any;\r\n\r\n  // Observables\r\n  private conversationId$ = new BehaviorSubject<string | null>(null);\r\n\r\n  // Constantes\r\n  readonly MessageType = MessageType;\r\n  readonly CallType = CallType;\r\n\r\n  constructor(\r\n    private messageService: MessageService,\r\n    private authService: AuthService,\r\n    private toastService: ToastService,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private cdr: ChangeDetectorRef,\r\n    private ngZone: NgZone\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.initializeComponent();\r\n    this.setupSubscriptions();\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.scrollToBottom();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.cleanup();\r\n  }\r\n\r\n  // ============================================================================\r\n  // MÉTHODES D'INITIALISATION\r\n  // ============================================================================\r\n\r\n  private initializeComponent(): void {\r\n    // Récupérer l'utilisateur actuel\r\n    this.currentUser = this.authService.getCurrentUser();\r\n\r\n    if (!this.currentUser) {\r\n      this.router.navigate(['/login']);\r\n      return;\r\n    }\r\n\r\n    // Écouter les changements de route pour la conversation\r\n    this.route.params.subscribe((params) => {\r\n      const conversationId = params['conversationId'];\r\n      if (conversationId) {\r\n        this.conversationId$.next(conversationId);\r\n      }\r\n    });\r\n  }\r\n\r\n  private setupSubscriptions(): void {\r\n    // Subscription pour charger la conversation\r\n    const conversationSub = this.conversationId$\r\n      .pipe(\r\n        filter((id) => !!id),\r\n        distinctUntilChanged(),\r\n        tap(() => {\r\n          this.isLoading = true;\r\n          this.messages = [];\r\n          this.currentPage = 1;\r\n          this.hasMoreMessages = true;\r\n        }),\r\n        switchMap((conversationId) =>\r\n          this.messageService.getConversation(conversationId!, 25, 1)\r\n        ),\r\n        catchError((error) => {\r\n          console.error('Erreur lors du chargement de la conversation:', error);\r\n          this.toastService.showError(\r\n            'Erreur lors du chargement de la conversation'\r\n          );\r\n          return of(null);\r\n        })\r\n      )\r\n      .subscribe((conversation) => {\r\n        this.isLoading = false;\r\n        if (conversation) {\r\n          this.selectedConversation = conversation;\r\n          this.messages = conversation.messages || [];\r\n          this.scrollToBottom();\r\n          this.markMessagesAsRead();\r\n        }\r\n        this.cdr.detectChanges();\r\n      });\r\n\r\n    // Subscription pour les nouveaux messages\r\n    const messagesSub = this.messageService\r\n      .subscribeToMessages()\r\n      .subscribe((message) => {\r\n        if (\r\n          message &&\r\n          this.selectedConversation &&\r\n          message.conversationId === this.selectedConversation.id\r\n        ) {\r\n          this.addNewMessage(message);\r\n          this.scrollToBottom();\r\n          this.markMessageAsRead(message);\r\n        }\r\n      });\r\n\r\n    // Subscription pour les indicateurs de frappe\r\n    const typingSub = this.messageService\r\n      .subscribeToTypingIndicators()\r\n      .subscribe((event) => {\r\n        if (\r\n          event &&\r\n          this.selectedConversation &&\r\n          event.conversationId === this.selectedConversation.id\r\n        ) {\r\n          this.handleTypingIndicator(event);\r\n        }\r\n      });\r\n\r\n    this.subscriptions.push(conversationSub, messagesSub, typingSub);\r\n  }\r\n\r\n  private cleanup(): void {\r\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\r\n    if (this.typingTimeout) {\r\n      clearTimeout(this.typingTimeout);\r\n    }\r\n    if (this.recordingInterval) {\r\n      clearInterval(this.recordingInterval);\r\n    }\r\n    this.stopTyping();\r\n  }\r\n\r\n  // ============================================================================\r\n  // MÉTHODES DE GESTION DES MESSAGES\r\n  // ============================================================================\r\n\r\n  sendMessage(): void {\r\n    if (!this.canSendMessage()) {\r\n      return;\r\n    }\r\n\r\n    const content = this.messageContent.trim();\r\n    const files = this.selectedFiles;\r\n\r\n    // Réinitialiser le formulaire\r\n    this.messageContent = '';\r\n    this.selectedFiles = [];\r\n    this.replyingTo = null;\r\n    this.stopTyping();\r\n\r\n    if (this.editingMessage) {\r\n      this.updateMessage(content);\r\n      return;\r\n    }\r\n\r\n    // Envoyer le message\r\n    if (content || files.length > 0) {\r\n      this.sendNewMessage(content, files);\r\n    }\r\n  }\r\n\r\n  canSendMessage(): boolean {\r\n    const hasContent = this.messageContent.trim().length > 0;\r\n    const hasFiles = this.selectedFiles.length > 0;\r\n    const hasConversation = !!this.selectedConversation;\r\n\r\n    return hasConversation && (hasContent || hasFiles);\r\n  }\r\n\r\n  private sendNewMessage(content: string, files: File[]): void {\r\n    if (!this.selectedConversation || !this.currentUser) return;\r\n\r\n    const recipientId = this.getRecipientId();\r\n    if (!recipientId) return;\r\n\r\n    // Créer un message temporaire pour l'affichage immédiat\r\n    const tempMessage: Message = {\r\n      id: `temp-${Date.now()}`,\r\n      content,\r\n      type:\r\n        files.length > 0 ? this.getFileMessageType(files[0]) : MessageType.TEXT,\r\n      timestamp: new Date(),\r\n      sender: this.currentUser,\r\n      isPending: true,\r\n      conversationId: this.selectedConversation.id,\r\n    };\r\n\r\n    this.addNewMessage(tempMessage);\r\n    this.scrollToBottom();\r\n\r\n    // Envoyer le message via le service\r\n    const sendObservable =\r\n      files.length > 0\r\n        ? this.messageService.sendMessageWithFile(\r\n            this.currentUser.id || this.currentUser._id!,\r\n            recipientId,\r\n            content,\r\n            files[0]\r\n          )\r\n        : this.messageService.sendMessage(\r\n            this.currentUser.id || this.currentUser._id!,\r\n            recipientId,\r\n            content\r\n          );\r\n\r\n    sendObservable.subscribe({\r\n      next: (sentMessage) => {\r\n        this.replaceTemporaryMessage(tempMessage.id!, sentMessage);\r\n        this.toastService.showSuccess('Message envoyé');\r\n      },\r\n      error: (error) => {\r\n        console.error(\"Erreur lors de l'envoi du message:\", error);\r\n        this.markMessageAsError(tempMessage.id!);\r\n        this.toastService.showError(\"Erreur lors de l'envoi du message\");\r\n      },\r\n    });\r\n  }\r\n\r\n  private updateMessage(newContent: string): void {\r\n    if (!this.editingMessage) return;\r\n\r\n    this.messageService\r\n      .editMessage(this.editingMessage.id!, newContent)\r\n      .subscribe({\r\n        next: (updatedMessage) => {\r\n          this.updateMessageInList(updatedMessage);\r\n          this.editingMessage = null;\r\n          this.toastService.showSuccess('Message modifié');\r\n        },\r\n        error: (error) => {\r\n          console.error('Erreur lors de la modification du message:', error);\r\n          this.toastService.showError(\r\n            'Erreur lors de la modification du message'\r\n          );\r\n        },\r\n      });\r\n  }\r\n\r\n  deleteMessage(message: Message): void {\r\n    if (!message.id || !this.canDeleteMessage(message)) return;\r\n\r\n    if (confirm('Êtes-vous sûr de vouloir supprimer ce message ?')) {\r\n      this.messageService.deleteMessage(message.id).subscribe({\r\n        next: () => {\r\n          this.removeMessageFromList(message.id!);\r\n          this.toastService.showSuccess('Message supprimé');\r\n        },\r\n        error: (error) => {\r\n          console.error('Erreur lors de la suppression du message:', error);\r\n          this.toastService.showError(\r\n            'Erreur lors de la suppression du message'\r\n          );\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  reactToMessage(message: Message, emoji: string): void {\r\n    if (!message.id) return;\r\n\r\n    this.messageService.reactToMessage(message.id, emoji).subscribe({\r\n      next: (updatedMessage) => {\r\n        this.updateMessageInList(updatedMessage);\r\n      },\r\n      error: (error) => {\r\n        console.error('Erreur lors de la réaction au message:', error);\r\n        this.toastService.showError('Erreur lors de la réaction');\r\n      },\r\n    });\r\n  }\r\n\r\n  // ============================================================================\r\n  // MÉTHODES DE GESTION DES FICHIERS ET MÉDIAS\r\n  // ============================================================================\r\n\r\n  onFileSelected(event: any): void {\r\n    const files = event.target.files;\r\n    if (files && files.length > 0) {\r\n      this.selectedFiles = Array.from(files);\r\n      this.showAttachmentMenu = false;\r\n\r\n      // Auto-envoyer si c'est juste un fichier sans texte\r\n      if (this.messageContent.trim() === '') {\r\n        this.sendMessage();\r\n      }\r\n    }\r\n  }\r\n\r\n  removeSelectedFile(index: number): void {\r\n    this.selectedFiles.splice(index, 1);\r\n  }\r\n\r\n  openFileSelector(): void {\r\n    this.fileInput.nativeElement.click();\r\n  }\r\n\r\n  // ============================================================================\r\n  // MÉTHODES D'ENREGISTREMENT VOCAL\r\n  // ============================================================================\r\n\r\n  async startVoiceRecording(): Promise<void> {\r\n    try {\r\n      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\r\n      this.isRecording = true;\r\n      this.recordingDuration = 0;\r\n\r\n      // Démarrer le compteur de durée\r\n      this.recordingInterval = setInterval(() => {\r\n        this.recordingDuration++;\r\n      }, 1000);\r\n\r\n      // Ici, vous pouvez implémenter l'enregistrement audio\r\n      // avec MediaRecorder API\r\n    } catch (error) {\r\n      console.error(\"Erreur lors de l'accès au microphone:\", error);\r\n      this.toastService.showError(\"Impossible d'accéder au microphone\");\r\n    }\r\n  }\r\n\r\n  stopVoiceRecording(): void {\r\n    this.isRecording = false;\r\n    if (this.recordingInterval) {\r\n      clearInterval(this.recordingInterval);\r\n    }\r\n\r\n    // Ici, vous pouvez traiter l'enregistrement et l'envoyer\r\n    // comme message vocal\r\n  }\r\n\r\n  cancelVoiceRecording(): void {\r\n    this.isRecording = false;\r\n    this.recordingDuration = 0;\r\n    if (this.recordingInterval) {\r\n      clearInterval(this.recordingInterval);\r\n    }\r\n  }\r\n\r\n  // ============================================================================\r\n  // MÉTHODES D'APPELS AUDIO/VIDÉO\r\n  // ============================================================================\r\n\r\n  startAudioCall(): void {\r\n    if (!this.selectedConversation) return;\r\n\r\n    const recipientId = this.getRecipientId();\r\n    if (!recipientId) return;\r\n\r\n    this.messageService.initiateCall(recipientId, CallType.AUDIO).subscribe({\r\n      next: (call) => {\r\n        this.toastService.showSuccess('Appel audio initié');\r\n        // Rediriger vers l'interface d'appel\r\n      },\r\n      error: (error) => {\r\n        console.error(\"Erreur lors de l'initiation de l'appel:\", error);\r\n        this.toastService.showError(\"Erreur lors de l'appel\");\r\n      },\r\n    });\r\n  }\r\n\r\n  startVideoCall(): void {\r\n    if (!this.selectedConversation) return;\r\n\r\n    const recipientId = this.getRecipientId();\r\n    if (!recipientId) return;\r\n\r\n    this.messageService.initiateCall(recipientId, CallType.VIDEO).subscribe({\r\n      next: (call) => {\r\n        this.toastService.showSuccess('Appel vidéo initié');\r\n        // Rediriger vers l'interface d'appel\r\n      },\r\n      error: (error) => {\r\n        console.error(\"Erreur lors de l'initiation de l'appel vidéo:\", error);\r\n        this.toastService.showError(\"Erreur lors de l'appel vidéo\");\r\n      },\r\n    });\r\n  }\r\n\r\n  // ============================================================================\r\n  // MÉTHODES DE GESTION DE LA FRAPPE\r\n  // ============================================================================\r\n\r\n  onTyping(): void {\r\n    if (!this.selectedConversation || this.isTyping) return;\r\n\r\n    this.isTyping = true;\r\n    this.messageService.startTyping(this.selectedConversation.id!).subscribe();\r\n\r\n    // Arrêter la frappe après 3 secondes d'inactivité\r\n    if (this.typingTimeout) {\r\n      clearTimeout(this.typingTimeout);\r\n    }\r\n\r\n    this.typingTimeout = setTimeout(() => {\r\n      this.stopTyping();\r\n    }, 3000);\r\n  }\r\n\r\n  stopTyping(): void {\r\n    if (!this.isTyping || !this.selectedConversation) return;\r\n\r\n    this.isTyping = false;\r\n    this.messageService.stopTyping(this.selectedConversation.id!).subscribe();\r\n\r\n    if (this.typingTimeout) {\r\n      clearTimeout(this.typingTimeout);\r\n    }\r\n  }\r\n\r\n  // ============================================================================\r\n  // MÉTHODES UTILITAIRES\r\n  // ============================================================================\r\n\r\n  private getRecipientId(): string | null {\r\n    if (!this.selectedConversation || !this.currentUser) return null;\r\n\r\n    const participants = this.selectedConversation.participants || [];\r\n    const currentUserId = this.currentUser.id || this.currentUser._id;\r\n\r\n    const recipient = participants.find(\r\n      (p) => (p.id || p._id) !== currentUserId\r\n    );\r\n\r\n    return recipient ? recipient.id || recipient._id! : null;\r\n  }\r\n\r\n  private getFileMessageType(file: File): MessageType {\r\n    const type = file.type.split('/')[0];\r\n    switch (type) {\r\n      case 'image':\r\n        return MessageType.IMAGE;\r\n      case 'video':\r\n        return MessageType.VIDEO;\r\n      case 'audio':\r\n        return MessageType.AUDIO;\r\n      default:\r\n        return MessageType.FILE;\r\n    }\r\n  }\r\n\r\n  private addNewMessage(message: Message): void {\r\n    this.messages.push(message);\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  private replaceTemporaryMessage(tempId: string, realMessage: Message): void {\r\n    const index = this.messages.findIndex((m) => m.id === tempId);\r\n    if (index !== -1) {\r\n      this.messages[index] = realMessage;\r\n      this.cdr.detectChanges();\r\n    }\r\n  }\r\n\r\n  private markMessageAsError(messageId: string): void {\r\n    const message = this.messages.find((m) => m.id === messageId);\r\n    if (message) {\r\n      message.isPending = false;\r\n      message.isError = true;\r\n      this.cdr.detectChanges();\r\n    }\r\n  }\r\n\r\n  private updateMessageInList(updatedMessage: Message): void {\r\n    const index = this.messages.findIndex((m) => m.id === updatedMessage.id);\r\n    if (index !== -1) {\r\n      this.messages[index] = updatedMessage;\r\n      this.cdr.detectChanges();\r\n    }\r\n  }\r\n\r\n  private removeMessageFromList(messageId: string): void {\r\n    this.messages = this.messages.filter((m) => m.id !== messageId);\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  private canDeleteMessage(message: Message): boolean {\r\n    if (!this.currentUser || !message.sender) return false;\r\n\r\n    const currentUserId = this.currentUser.id || this.currentUser._id;\r\n    const senderId = message.sender.id || message.sender._id;\r\n\r\n    return currentUserId === senderId;\r\n  }\r\n\r\n  private handleTypingIndicator(event: any): void {\r\n    if (!this.currentUser) return;\r\n\r\n    const currentUserId = this.currentUser.id || this.currentUser._id;\r\n\r\n    if (event.userId === currentUserId) return; // Ignorer ses propres indicateurs\r\n\r\n    if (event.isTyping) {\r\n      // Ajouter l'utilisateur à la liste des utilisateurs en train de taper\r\n      const user = this.selectedConversation?.participants?.find(\r\n        (p) => (p.id || p._id) === event.userId\r\n      );\r\n      if (\r\n        user &&\r\n        !this.typingUsers.find((u) => (u.id || u._id) === event.userId)\r\n      ) {\r\n        this.typingUsers.push(user);\r\n      }\r\n    } else {\r\n      // Retirer l'utilisateur de la liste\r\n      this.typingUsers = this.typingUsers.filter(\r\n        (u) => (u.id || u._id) !== event.userId\r\n      );\r\n    }\r\n\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  private markMessagesAsRead(): void {\r\n    if (!this.messages.length || !this.currentUser) return;\r\n\r\n    const unreadMessages = this.messages.filter(\r\n      (m) =>\r\n        !m.isRead &&\r\n        m.sender &&\r\n        (m.sender.id || m.sender._id) !==\r\n          (this.currentUser!.id || this.currentUser!._id)\r\n    );\r\n\r\n    unreadMessages.forEach((message) => {\r\n      if (message.id) {\r\n        this.markMessageAsRead(message);\r\n      }\r\n    });\r\n  }\r\n\r\n  private markMessageAsRead(message: Message): void {\r\n    if (!message.id || message.isRead) return;\r\n\r\n    this.messageService.markMessageAsRead(message.id).subscribe({\r\n      next: (updatedMessage) => {\r\n        this.updateMessageInList(updatedMessage);\r\n      },\r\n      error: (error) => {\r\n        console.error('Erreur lors du marquage comme lu:', error);\r\n      },\r\n    });\r\n  }\r\n\r\n  private scrollToBottom(): void {\r\n    this.ngZone.runOutsideAngular(() => {\r\n      setTimeout(() => {\r\n        if (this.messagesContainer) {\r\n          const element = this.messagesContainer.nativeElement;\r\n          element.scrollTop = element.scrollHeight;\r\n        }\r\n      }, 100);\r\n    });\r\n  }\r\n\r\n  // ============================================================================\r\n  // MÉTHODES PUBLIQUES POUR LE TEMPLATE\r\n  // ============================================================================\r\n\r\n  formatMessageTime(timestamp: Date | string): string {\r\n    const date = new Date(timestamp);\r\n    const now = new Date();\r\n    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\r\n\r\n    if (diffInHours < 24) {\r\n      return date.toLocaleTimeString('fr-FR', {\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n      });\r\n    } else {\r\n      return date.toLocaleDateString('fr-FR', {\r\n        day: '2-digit',\r\n        month: '2-digit',\r\n      });\r\n    }\r\n  }\r\n\r\n  isMyMessage(message: Message): boolean {\r\n    if (!this.currentUser || !message.sender) return false;\r\n\r\n    const currentUserId = this.currentUser.id || this.currentUser._id;\r\n    const senderId = message.sender.id || message.sender._id;\r\n\r\n    return currentUserId === senderId;\r\n  }\r\n\r\n  getTypingText(): string {\r\n    if (this.typingUsers.length === 0) return '';\r\n\r\n    if (this.typingUsers.length === 1) {\r\n      return `${this.typingUsers[0].username} est en train d'écrire...`;\r\n    } else {\r\n      return `${this.typingUsers.length} personnes sont en train d'écrire...`;\r\n    }\r\n  }\r\n\r\n  onKeyPress(event: KeyboardEvent): void {\r\n    if (event.key === 'Enter' && !event.shiftKey) {\r\n      event.preventDefault();\r\n      this.sendMessage();\r\n    } else {\r\n      this.onTyping();\r\n    }\r\n  }\r\n\r\n  toggleEmojiPicker(): void {\r\n    this.showEmojiPicker = !this.showEmojiPicker;\r\n  }\r\n\r\n  toggleAttachmentMenu(): void {\r\n    this.showAttachmentMenu = !this.showAttachmentMenu;\r\n  }\r\n\r\n  startEditingMessage(message: Message): void {\r\n    this.editingMessage = message;\r\n    this.messageContent = message.content || '';\r\n    this.messageInput.nativeElement.focus();\r\n  }\r\n\r\n  cancelEditing(): void {\r\n    this.editingMessage = null;\r\n    this.messageContent = '';\r\n  }\r\n\r\n  setReplyTo(message: Message): void {\r\n    this.replyingTo = message;\r\n    this.messageInput.nativeElement.focus();\r\n  }\r\n\r\n  cancelReply(): void {\r\n    this.replyingTo = null;\r\n  }\r\n\r\n  // ============================================================================\r\n  // MÉTHODES POUR LE TEMPLATE (MANQUANTES)\r\n  // ============================================================================\r\n\r\n  getRecipientName(): string {\r\n    if (!this.selectedConversation || !this.currentUser) return '';\r\n\r\n    const participants = this.selectedConversation.participants || [];\r\n    const currentUserId = this.currentUser.id || this.currentUser._id;\r\n\r\n    const recipient = participants.find(\r\n      (p) => (p.id || p._id) !== currentUserId\r\n    );\r\n\r\n    return recipient?.username || 'Utilisateur inconnu';\r\n  }\r\n\r\n  getRecipientAvatar(): string {\r\n    if (!this.selectedConversation || !this.currentUser)\r\n      return '/assets/images/default-avatar.png';\r\n\r\n    const participants = this.selectedConversation.participants || [];\r\n    const currentUserId = this.currentUser.id || this.currentUser._id;\r\n\r\n    const recipient = participants.find(\r\n      (p) => (p.id || p._id) !== currentUserId\r\n    );\r\n\r\n    return recipient?.image || '/assets/images/default-avatar.png';\r\n  }\r\n\r\n  isRecipientOnline(): boolean {\r\n    if (!this.selectedConversation || !this.currentUser) return false;\r\n\r\n    const participants = this.selectedConversation.participants || [];\r\n    const currentUserId = this.currentUser.id || this.currentUser._id;\r\n\r\n    const recipient = participants.find(\r\n      (p) => (p.id || p._id) !== currentUserId\r\n    );\r\n\r\n    return recipient?.isOnline || false;\r\n  }\r\n\r\n  trackByMessageId(index: number, message: Message): string {\r\n    return message.id || message._id || index.toString();\r\n  }\r\n\r\n  openImageViewer(attachment: Attachment | undefined): void {\r\n    if (!attachment?.url) return;\r\n\r\n    // Ouvrir l'image dans une nouvelle fenêtre ou modal\r\n    window.open(attachment.url, '_blank');\r\n  }\r\n\r\n  formatFileSize(size: number | undefined): string {\r\n    if (!size) return '0 B';\r\n\r\n    const units = ['B', 'KB', 'MB', 'GB'];\r\n    let unitIndex = 0;\r\n    let fileSize = size;\r\n\r\n    while (fileSize >= 1024 && unitIndex < units.length - 1) {\r\n      fileSize /= 1024;\r\n      unitIndex++;\r\n    }\r\n\r\n    return `${fileSize.toFixed(1)} ${units[unitIndex]}`;\r\n  }\r\n\r\n  downloadFile(attachment: Attachment | undefined): void {\r\n    if (!attachment?.url) return;\r\n\r\n    const link = document.createElement('a');\r\n    link.href = attachment.url;\r\n    link.download = attachment.name || 'file';\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n  }\r\n\r\n  playVoiceMessage(message: Message): void {\r\n    if (!message.attachments?.[0]?.url) return;\r\n\r\n    this.messageService.playAudio(message.attachments[0].url).catch((error) => {\r\n      console.error('Erreur lors de la lecture du message vocal:', error);\r\n      this.toastService.showError('Erreur lors de la lecture du message vocal');\r\n    });\r\n  }\r\n\r\n  formatDuration(duration: number | undefined): string {\r\n    if (!duration) return '0:00';\r\n\r\n    const minutes = Math.floor(duration / 60);\r\n    const seconds = duration % 60;\r\n\r\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\r\n  }\r\n\r\n  showMessageMenu(message: Message): void {\r\n    // Ici, vous pouvez implémenter un menu contextuel\r\n    // Pour l'instant, on affiche juste les options disponibles\r\n    const actions = [];\r\n\r\n    if (this.canDeleteMessage(message)) {\r\n      actions.push('Supprimer');\r\n    }\r\n\r\n    if (this.isMyMessage(message)) {\r\n      actions.push('Modifier');\r\n    }\r\n\r\n    actions.push('Répondre', 'Transférer', 'Réagir');\r\n\r\n    // Vous pouvez implémenter un vrai menu contextuel ici\r\n    console.log('Actions disponibles pour ce message:', actions);\r\n  }\r\n}\r\n", "<!-- ============================================================================\r\n     COMPOSANT MESSAGE CHAT - INTERFACE WHATSAPP PROFESSIONNELLE\r\n     ============================================================================ -->\r\n\r\n<div class=\"chat-container\" *ngIf=\"selectedConversation\">\r\n  <!-- ========================================================================\r\n       EN-TÊTE DU CHAT\r\n       ======================================================================== -->\r\n  <div class=\"chat-header\">\r\n    <div class=\"user-info\">\r\n      <img\r\n        [src]=\"\r\n          selectedConversation.isGroup\r\n            ? selectedConversation.groupPhoto\r\n            : getRecipientAvatar()\r\n        \"\r\n        [alt]=\"\r\n          selectedConversation.isGroup\r\n            ? selectedConversation.groupName\r\n            : getRecipientName()\r\n        \"\r\n        class=\"user-avatar\"\r\n        [class.online]=\"!selectedConversation.isGroup && isRecipientOnline()\"\r\n      />\r\n\r\n      <div class=\"user-details\">\r\n        <h3>\r\n          {{\r\n            selectedConversation.isGroup\r\n              ? selectedConversation.groupName\r\n              : getRecipientName()\r\n          }}\r\n        </h3>\r\n        <p\r\n          class=\"user-status\"\r\n          [class.online]=\"!selectedConversation.isGroup && isRecipientOnline()\"\r\n        >\r\n          <span *ngIf=\"selectedConversation.isGroup\">\r\n            {{ selectedConversation.participants?.length }} participants\r\n          </span>\r\n          <span *ngIf=\"!selectedConversation.isGroup && isRecipientOnline()\"\r\n            >En ligne</span\r\n          >\r\n          <span *ngIf=\"!selectedConversation.isGroup && !isRecipientOnline()\"\r\n            >Hors ligne</span\r\n          >\r\n        </p>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"chat-actions\">\r\n      <!-- Bouton d'appel audio -->\r\n      <button\r\n        class=\"action-btn\"\r\n        (click)=\"startAudioCall()\"\r\n        title=\"Appel audio\"\r\n        *ngIf=\"!selectedConversation.isGroup\"\r\n      >\r\n        <i class=\"fas fa-phone\"></i>\r\n      </button>\r\n\r\n      <!-- Bouton d'appel vidéo -->\r\n      <button\r\n        class=\"action-btn\"\r\n        (click)=\"startVideoCall()\"\r\n        title=\"Appel vidéo\"\r\n        *ngIf=\"!selectedConversation.isGroup\"\r\n      >\r\n        <i class=\"fas fa-video\"></i>\r\n      </button>\r\n\r\n      <!-- Menu d'options -->\r\n      <button class=\"action-btn\" title=\"Options\">\r\n        <i class=\"fas fa-ellipsis-v\"></i>\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- ========================================================================\r\n       ZONE DES MESSAGES\r\n       ======================================================================== -->\r\n  <div class=\"messages-container\" #messagesContainer>\r\n    <!-- Indicateur de chargement -->\r\n    <div *ngIf=\"isLoading\" class=\"flex justify-center py-4\">\r\n      <div\r\n        class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"\r\n      ></div>\r\n    </div>\r\n\r\n    <!-- Messages -->\r\n    <div\r\n      *ngFor=\"let message of messages; trackBy: trackByMessageId\"\r\n      class=\"message\"\r\n      [class.my-message]=\"isMyMessage(message)\"\r\n    >\r\n      <!-- Avatar de l'expéditeur (seulement pour les messages des autres) -->\r\n      <img\r\n        *ngIf=\"!isMyMessage(message) && message.sender\"\r\n        [src]=\"message.sender.image || '/assets/images/default-avatar.png'\"\r\n        [alt]=\"message.sender.username\"\r\n        class=\"message-avatar\"\r\n      />\r\n\r\n      <!-- Contenu du message -->\r\n      <div\r\n        class=\"message-content\"\r\n        [class.my-message]=\"isMyMessage(message)\"\r\n        [class.other-message]=\"!isMyMessage(message)\"\r\n      >\r\n        <!-- Nom de l'expéditeur (pour les groupes) -->\r\n        <div\r\n          *ngIf=\"selectedConversation.isGroup && !isMyMessage(message)\"\r\n          class=\"text-xs text-blue-400 mb-1 font-medium\"\r\n        >\r\n          {{ message.sender?.username }}\r\n        </div>\r\n\r\n        <!-- Message de réponse -->\r\n        <div *ngIf=\"message.replyTo\" class=\"reply-preview mb-2\">\r\n          <div class=\"text-xs text-gray-400\">\r\n            Réponse à {{ message.replyTo.sender?.username }}\r\n          </div>\r\n          <div class=\"text-sm text-gray-300 truncate\">\r\n            {{ message.replyTo.content }}\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Contenu selon le type de message -->\r\n        <div [ngSwitch]=\"message.type\">\r\n          <!-- Message texte -->\r\n          <div *ngSwitchCase=\"MessageType.TEXT\" class=\"message-text\">\r\n            {{ message.content }}\r\n          </div>\r\n\r\n          <!-- Message image -->\r\n          <div *ngSwitchCase=\"MessageType.IMAGE\" class=\"message-image\">\r\n            <img\r\n              [src]=\"message.attachments?.[0]?.url\"\r\n              [alt]=\"message.attachments?.[0]?.name\"\r\n              class=\"message-image\"\r\n              (click)=\"openImageViewer(message.attachments?.[0])\"\r\n            />\r\n            <div *ngIf=\"message.content\" class=\"message-text mt-2\">\r\n              {{ message.content }}\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Message fichier -->\r\n          <div *ngSwitchCase=\"MessageType.FILE\" class=\"message-file\">\r\n            <i class=\"file-icon fas fa-file\"></i>\r\n            <div class=\"file-info\">\r\n              <div class=\"file-name\">{{ message.attachments?.[0]?.name }}</div>\r\n              <div class=\"file-size\">\r\n                {{ formatFileSize(message.attachments?.[0]?.size) }}\r\n              </div>\r\n            </div>\r\n            <button\r\n              class=\"text-blue-400 hover:text-blue-300\"\r\n              (click)=\"downloadFile(message.attachments?.[0])\"\r\n            >\r\n              <i class=\"fas fa-download\"></i>\r\n            </button>\r\n          </div>\r\n\r\n          <!-- Message vocal -->\r\n          <div *ngSwitchCase=\"MessageType.VOICE_MESSAGE\" class=\"voice-message\">\r\n            <button class=\"voice-play-btn\" (click)=\"playVoiceMessage(message)\">\r\n              <i class=\"fas fa-play text-white text-xs\"></i>\r\n            </button>\r\n            <div class=\"voice-duration\">\r\n              {{ formatDuration(message.attachments?.[0]?.duration) }}\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Message vidéo -->\r\n          <div *ngSwitchCase=\"MessageType.VIDEO\" class=\"message-video\">\r\n            <video\r\n              [src]=\"message.attachments?.[0]?.url\"\r\n              controls\r\n              class=\"max-w-xs rounded-lg\"\r\n            ></video>\r\n            <div *ngIf=\"message.content\" class=\"message-text mt-2\">\r\n              {{ message.content }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Réactions -->\r\n        <div\r\n          *ngIf=\"message.reactions && message.reactions.length > 0\"\r\n          class=\"flex flex-wrap gap-1 mt-2\"\r\n        >\r\n          <span\r\n            *ngFor=\"let reaction of message.reactions\"\r\n            class=\"text-xs bg-gray-600 rounded-full px-2 py-1 cursor-pointer\"\r\n            (click)=\"reactToMessage(message, reaction.emoji)\"\r\n          >\r\n            {{ reaction.emoji }} {{ reaction.count }}\r\n          </span>\r\n        </div>\r\n\r\n        <!-- Heure et statut -->\r\n        <div class=\"flex items-center justify-between mt-1\">\r\n          <span class=\"message-time\">\r\n            {{ formatMessageTime(message.timestamp!) }}\r\n          </span>\r\n\r\n          <!-- Statut du message (seulement pour mes messages) -->\r\n          <div\r\n            *ngIf=\"isMyMessage(message)\"\r\n            class=\"message-status\"\r\n            [class.read]=\"message.isRead\"\r\n            [class.pending]=\"message.isPending\"\r\n            [class.error]=\"message.isError\"\r\n          >\r\n            <i *ngIf=\"message.isPending\" class=\"fas fa-clock\"></i>\r\n            <i *ngIf=\"message.isError\" class=\"fas fa-exclamation-triangle\"></i>\r\n            <i\r\n              *ngIf=\"!message.isPending && !message.isError && message.isRead\"\r\n              class=\"fas fa-check-double\"\r\n            ></i>\r\n            <i\r\n              *ngIf=\"!message.isPending && !message.isError && !message.isRead\"\r\n              class=\"fas fa-check\"\r\n            ></i>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Menu contextuel du message -->\r\n        <div\r\n          class=\"message-menu absolute top-0 right-0 hidden group-hover:block\"\r\n        >\r\n          <button\r\n            class=\"text-gray-400 hover:text-white p-1\"\r\n            (click)=\"showMessageMenu(message)\"\r\n          >\r\n            <i class=\"fas fa-ellipsis-h text-xs\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Indicateur de frappe -->\r\n    <div *ngIf=\"typingUsers.length > 0\" class=\"typing-indicator\">\r\n      <div class=\"typing-dots\">\r\n        <div class=\"typing-dot\"></div>\r\n        <div class=\"typing-dot\"></div>\r\n        <div class=\"typing-dot\"></div>\r\n      </div>\r\n      <span>{{ getTypingText() }}</span>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- ========================================================================\r\n       ZONE DE SAISIE DES MESSAGES\r\n       ======================================================================== -->\r\n  <div class=\"message-input-container\">\r\n    <!-- Aperçu de réponse -->\r\n    <div *ngIf=\"replyingTo\" class=\"reply-preview\">\r\n      <div class=\"reply-header\">\r\n        <div>\r\n          <div class=\"text-xs text-blue-400\">\r\n            Réponse à {{ replyingTo.sender?.username }}\r\n          </div>\r\n          <div class=\"reply-text\">{{ replyingTo.content }}</div>\r\n        </div>\r\n        <button (click)=\"cancelReply()\" class=\"text-gray-400 hover:text-white\">\r\n          <i class=\"fas fa-times\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Aperçu de modification -->\r\n    <div *ngIf=\"editingMessage\" class=\"reply-preview\">\r\n      <div class=\"reply-header\">\r\n        <div>\r\n          <div class=\"text-xs text-yellow-400\">Modification du message</div>\r\n          <div class=\"reply-text\">{{ editingMessage.content }}</div>\r\n        </div>\r\n        <button\r\n          (click)=\"cancelEditing()\"\r\n          class=\"text-gray-400 hover:text-white\"\r\n        >\r\n          <i class=\"fas fa-times\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Fichiers sélectionnés -->\r\n    <div *ngIf=\"selectedFiles.length > 0\" class=\"mb-3\">\r\n      <div class=\"flex flex-wrap gap-2\">\r\n        <div\r\n          *ngFor=\"let file of selectedFiles; let i = index\"\r\n          class=\"flex items-center space-x-2 bg-gray-700 rounded-lg p-2\"\r\n        >\r\n          <i class=\"fas fa-file text-blue-400\"></i>\r\n          <span class=\"text-sm text-white truncate max-w-32\">{{\r\n            file.name\r\n          }}</span>\r\n          <button\r\n            (click)=\"removeSelectedFile(i)\"\r\n            class=\"text-red-400 hover:text-red-300\"\r\n          >\r\n            <i class=\"fas fa-times text-xs\"></i>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Indicateur d'enregistrement vocal -->\r\n    <div *ngIf=\"isRecording\" class=\"recording-indicator mb-3\">\r\n      <i class=\"fas fa-microphone text-white\"></i>\r\n      <span class=\"recording-time\">{{\r\n        formatDuration(recordingDuration)\r\n      }}</span>\r\n      <button\r\n        (click)=\"stopVoiceRecording()\"\r\n        class=\"text-white hover:text-gray-300\"\r\n      >\r\n        <i class=\"fas fa-stop\"></i>\r\n      </button>\r\n      <button\r\n        (click)=\"cancelVoiceRecording()\"\r\n        class=\"text-white hover:text-gray-300 ml-2\"\r\n      >\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </div>\r\n\r\n    <!-- Zone de saisie principale -->\r\n    <div class=\"input-wrapper\">\r\n      <!-- Boutons d'actions à gauche -->\r\n      <div class=\"input-actions\">\r\n        <!-- Bouton pièce jointe -->\r\n        <div class=\"relative\">\r\n          <button\r\n            class=\"input-btn\"\r\n            (click)=\"toggleAttachmentMenu()\"\r\n            title=\"Pièce jointe\"\r\n          >\r\n            <i class=\"fas fa-paperclip\"></i>\r\n          </button>\r\n\r\n          <!-- Menu des pièces jointes -->\r\n          <div\r\n            *ngIf=\"showAttachmentMenu\"\r\n            class=\"absolute bottom-full left-0 mb-2 bg-gray-800 rounded-lg shadow-lg p-2 space-y-1\"\r\n          >\r\n            <button\r\n              (click)=\"openFileSelector()\"\r\n              class=\"flex items-center space-x-2 w-full p-2 hover:bg-gray-700 rounded text-left\"\r\n            >\r\n              <i class=\"fas fa-file text-blue-400\"></i>\r\n              <span class=\"text-white text-sm\">Fichier</span>\r\n            </button>\r\n            <button\r\n              (click)=\"openFileSelector()\"\r\n              class=\"flex items-center space-x-2 w-full p-2 hover:bg-gray-700 rounded text-left\"\r\n            >\r\n              <i class=\"fas fa-image text-green-400\"></i>\r\n              <span class=\"text-white text-sm\">Image</span>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Bouton emoji -->\r\n        <button class=\"input-btn\" (click)=\"toggleEmojiPicker()\" title=\"Emoji\">\r\n          <i class=\"fas fa-smile\"></i>\r\n        </button>\r\n      </div>\r\n\r\n      <!-- Champ de saisie -->\r\n      <textarea\r\n        #messageInput\r\n        [(ngModel)]=\"messageContent\"\r\n        (keydown)=\"onKeyPress($event)\"\r\n        (input)=\"onTyping()\"\r\n        placeholder=\"Tapez votre message...\"\r\n        class=\"message-input\"\r\n        rows=\"1\"\r\n        [disabled]=\"isRecording\"\r\n      ></textarea>\r\n\r\n      <!-- Boutons d'actions à droite -->\r\n      <div class=\"input-actions\">\r\n        <!-- Bouton enregistrement vocal (si pas de texte) -->\r\n        <button\r\n          *ngIf=\"\r\n            !messageContent.trim() && !selectedFiles.length && !isRecording\r\n          \"\r\n          class=\"input-btn\"\r\n          (mousedown)=\"startVoiceRecording()\"\r\n          title=\"Message vocal\"\r\n        >\r\n          <i class=\"fas fa-microphone\"></i>\r\n        </button>\r\n\r\n        <!-- Bouton d'envoi (si du texte ou des fichiers) -->\r\n        <button\r\n          *ngIf=\"messageContent.trim() || selectedFiles.length\"\r\n          class=\"send-btn\"\r\n          (click)=\"sendMessage()\"\r\n          [disabled]=\"!canSendMessage()\"\r\n          title=\"Envoyer\"\r\n        >\r\n          <i class=\"fas fa-paper-plane text-white\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Input file caché -->\r\n    <input\r\n      #fileInput\r\n      type=\"file\"\r\n      multiple\r\n      class=\"hidden\"\r\n      (change)=\"onFileSelected($event)\"\r\n      accept=\"image/*,video/*,audio/*,.pdf,.doc,.docx,.txt\"\r\n    />\r\n  </div>\r\n</div>\r\n\r\n<!-- ============================================================================\r\n     MESSAGE DE BIENVENUE (AUCUNE CONVERSATION SÉLECTIONNÉE)\r\n     ============================================================================ -->\r\n<div\r\n  *ngIf=\"!selectedConversation\"\r\n  class=\"flex items-center justify-center h-full bg-gray-900 text-gray-400\"\r\n>\r\n  <div class=\"text-center max-w-md mx-auto p-8\">\r\n    <!-- Logo/Icône principale -->\r\n    <div class=\"mb-8\">\r\n      <div\r\n        class=\"w-24 h-24 mx-auto bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mb-4\"\r\n      >\r\n        <i class=\"fas fa-comments text-3xl text-white\"></i>\r\n      </div>\r\n      <h1 class=\"text-3xl font-bold text-white mb-2\">DevBridge Messages</h1>\r\n      <p class=\"text-gray-400\">Messagerie professionnelle en temps réel</p>\r\n    </div>\r\n\r\n    <!-- Fonctionnalités -->\r\n    <div class=\"space-y-4 mb-8\">\r\n      <div class=\"flex items-center space-x-3 text-left\">\r\n        <div\r\n          class=\"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center\"\r\n        >\r\n          <i class=\"fas fa-bolt text-white text-sm\"></i>\r\n        </div>\r\n        <div>\r\n          <h4 class=\"text-white font-medium\">Messages en temps réel</h4>\r\n          <p class=\"text-sm text-gray-400\">\r\n            Conversations instantanées avec notifications\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"flex items-center space-x-3 text-left\">\r\n        <div\r\n          class=\"w-10 h-10 bg-green-600 rounded-full flex items-center justify-center\"\r\n        >\r\n          <i class=\"fas fa-phone text-white text-sm\"></i>\r\n        </div>\r\n        <div>\r\n          <h4 class=\"text-white font-medium\">Appels audio/vidéo</h4>\r\n          <p class=\"text-sm text-gray-400\">Communication directe intégrée</p>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"flex items-center space-x-3 text-left\">\r\n        <div\r\n          class=\"w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center\"\r\n        >\r\n          <i class=\"fas fa-file text-white text-sm\"></i>\r\n        </div>\r\n        <div>\r\n          <h4 class=\"text-white font-medium\">Partage de fichiers</h4>\r\n          <p class=\"text-sm text-gray-400\">Images, documents et médias</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Instructions -->\r\n    <div class=\"bg-gray-800 rounded-lg p-6 border border-gray-700\">\r\n      <h3 class=\"text-lg font-semibold text-white mb-3\">Comment commencer ?</h3>\r\n      <div class=\"space-y-2 text-sm text-gray-300\">\r\n        <p>• Sélectionnez une conversation dans la sidebar</p>\r\n        <p>• Ou cliquez sur un contact pour démarrer une discussion</p>\r\n        <p>• Utilisez la recherche pour trouver rapidement</p>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": ";AAWA,SAAuBA,eAAe,EAAiBC,EAAE,QAAQ,MAAM;AACvE,SAEEC,oBAAoB,EACpBC,SAAS,EACTC,UAAU,EACVC,GAAG,EACHC,MAAM,QAED,gBAAgB;AAIvB,SAIEC,WAAW,EACXC,QAAQ,QAEH,kCAAkC;;;;;;;;;;;;;;ICM/BC,EAAA,CAAAC,cAAA,WAA2C;IACzCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,oBAAA,CAAAC,YAAA,kBAAAF,MAAA,CAAAC,oBAAA,CAAAC,YAAA,CAAAC,MAAA,mBACF;;;;;IACAT,EAAA,CAAAC,cAAA,WACG;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EACV;;;;;IACDH,EAAA,CAAAC,cAAA,WACG;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EACZ;;;;;;IAOLH,EAAA,CAAAC,cAAA,iBAKC;IAHCD,EAAA,CAAAU,UAAA,mBAAAC,sEAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAF,OAAA,CAAAG,cAAA,EAAgB;IAAA,EAAC;IAI1BjB,EAAA,CAAAkB,SAAA,YAA4B;IAC9BlB,EAAA,CAAAG,YAAA,EAAS;;;;;;IAGTH,EAAA,CAAAC,cAAA,iBAKC;IAHCD,EAAA,CAAAU,UAAA,mBAAAS,sEAAA;MAAAnB,EAAA,CAAAY,aAAA,CAAAQ,IAAA;MAAA,MAAAC,OAAA,GAAArB,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAK,OAAA,CAAAC,cAAA,EAAgB;IAAA,EAAC;IAI1BtB,EAAA,CAAAkB,SAAA,YAA4B;IAC9BlB,EAAA,CAAAG,YAAA,EAAS;;;;;IAcXH,EAAA,CAAAC,cAAA,cAAwD;IACtDD,EAAA,CAAAkB,SAAA,cAEO;IACTlB,EAAA,CAAAG,YAAA,EAAM;;;;;IASJH,EAAA,CAAAkB,SAAA,cAKE;;;;IAHAlB,EAAA,CAAAuB,UAAA,QAAAC,WAAA,CAAAC,MAAA,CAAAC,KAAA,yCAAA1B,EAAA,CAAA2B,aAAA,CAAmE,QAAAH,WAAA,CAAAC,MAAA,CAAAG,QAAA;;;;;IAYnE5B,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAmB,WAAA,CAAAC,MAAA,kBAAAD,WAAA,CAAAC,MAAA,CAAAG,QAAA,MACF;;;;;IAGA5B,EAAA,CAAAC,cAAA,cAAwD;IAEpDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA4C;IAC1CD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAJJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,0BAAAmB,WAAA,CAAAK,OAAA,CAAAJ,MAAA,kBAAAD,WAAA,CAAAK,OAAA,CAAAJ,MAAA,CAAAG,QAAA,MACF;IAEE5B,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAmB,WAAA,CAAAK,OAAA,CAAAC,OAAA,MACF;;;;;IAMA9B,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAmB,WAAA,CAAAM,OAAA,MACF;;;;;IAUE9B,EAAA,CAAAC,cAAA,cAAuD;IACrDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAmB,WAAA,CAAAM,OAAA,MACF;;;;;;IATF9B,EAAA,CAAAC,cAAA,cAA6D;IAKzDD,EAAA,CAAAU,UAAA,mBAAAqB,sEAAA;MAAA/B,EAAA,CAAAY,aAAA,CAAAoB,IAAA;MAAA,MAAAR,WAAA,GAAAxB,EAAA,CAAAe,aAAA,GAAAkB,SAAA;MAAA,MAAAC,OAAA,GAAAlC,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAkB,OAAA,CAAAC,eAAA,CAAAX,WAAA,CAAAY,WAAA,kBAAAZ,WAAA,CAAAY,WAAA,CAAsC,CAAC,EAAE;IAAA,EAAC;IAJrDpC,EAAA,CAAAG,YAAA,EAKE;IACFH,EAAA,CAAAqC,UAAA,IAAAC,sDAAA,kBAEM;IACRtC,EAAA,CAAAG,YAAA,EAAM;;;;IARFH,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAuB,UAAA,QAAAC,WAAA,CAAAY,WAAA,kBAAAZ,WAAA,CAAAY,WAAA,qBAAAZ,WAAA,CAAAY,WAAA,IAAAG,GAAA,EAAAvC,EAAA,CAAA2B,aAAA,CAAqC,QAAAH,WAAA,CAAAY,WAAA,kBAAAZ,WAAA,CAAAY,WAAA,qBAAAZ,WAAA,CAAAY,WAAA,IAAAI,IAAA;IAKjCxC,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAuB,UAAA,SAAAC,WAAA,CAAAM,OAAA,CAAqB;;;;;;IAM7B9B,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAAkB,SAAA,YAAqC;IACrClB,EAAA,CAAAC,cAAA,cAAuB;IACED,EAAA,CAAAE,MAAA,GAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACjEH,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,iBAGC;IADCD,EAAA,CAAAU,UAAA,mBAAA+B,yEAAA;MAAAzC,EAAA,CAAAY,aAAA,CAAA8B,IAAA;MAAA,MAAAlB,WAAA,GAAAxB,EAAA,CAAAe,aAAA,GAAAkB,SAAA;MAAA,MAAAU,OAAA,GAAA3C,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAA2B,OAAA,CAAAC,YAAA,CAAApB,WAAA,CAAAY,WAAA,kBAAAZ,WAAA,CAAAY,WAAA,CAAmC,CAAC,EAAE;IAAA,EAAC;IAEhDpC,EAAA,CAAAkB,SAAA,YAA+B;IACjClB,EAAA,CAAAG,YAAA,EAAS;;;;;IAVgBH,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAA6C,iBAAA,CAAArB,WAAA,CAAAY,WAAA,kBAAAZ,WAAA,CAAAY,WAAA,qBAAAZ,WAAA,CAAAY,WAAA,IAAAI,IAAA,CAAoC;IAEzDxC,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAyC,OAAA,CAAAC,cAAA,CAAAvB,WAAA,CAAAY,WAAA,kBAAAZ,WAAA,CAAAY,WAAA,qBAAAZ,WAAA,CAAAY,WAAA,IAAAY,IAAA,OACF;;;;;;IAWJhD,EAAA,CAAAC,cAAA,cAAqE;IACpCD,EAAA,CAAAU,UAAA,mBAAAuC,yEAAA;MAAAjD,EAAA,CAAAY,aAAA,CAAAsC,IAAA;MAAA,MAAA1B,WAAA,GAAAxB,EAAA,CAAAe,aAAA,GAAAkB,SAAA;MAAA,MAAAkB,OAAA,GAAAnD,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAmC,OAAA,CAAAC,gBAAA,CAAA5B,WAAA,CAAyB;IAAA,EAAC;IAChExB,EAAA,CAAAkB,SAAA,YAA8C;IAChDlB,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAgD,OAAA,CAAAC,cAAA,CAAA9B,WAAA,CAAAY,WAAA,kBAAAZ,WAAA,CAAAY,WAAA,qBAAAZ,WAAA,CAAAY,WAAA,IAAAmB,QAAA,OACF;;;;;IAUAvD,EAAA,CAAAC,cAAA,cAAuD;IACrDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAmB,WAAA,CAAAM,OAAA,MACF;;;;;IARF9B,EAAA,CAAAC,cAAA,cAA6D;IAC3DD,EAAA,CAAAkB,SAAA,gBAIS;IACTlB,EAAA,CAAAqC,UAAA,IAAAmB,uDAAA,kBAEM;IACRxD,EAAA,CAAAG,YAAA,EAAM;;;;IAPFH,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAuB,UAAA,QAAAC,WAAA,CAAAY,WAAA,kBAAAZ,WAAA,CAAAY,WAAA,qBAAAZ,WAAA,CAAAY,WAAA,IAAAG,GAAA,EAAAvC,EAAA,CAAA2B,aAAA,CAAqC;IAIjC3B,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAuB,UAAA,SAAAC,WAAA,CAAAM,OAAA,CAAqB;;;;;;IAW7B9B,EAAA,CAAAC,cAAA,eAIC;IADCD,EAAA,CAAAU,UAAA,mBAAA+C,+EAAA;MAAA,MAAAC,WAAA,GAAA1D,EAAA,CAAAY,aAAA,CAAA+C,IAAA;MAAA,MAAAC,YAAA,GAAAF,WAAA,CAAAzB,SAAA;MAAA,MAAAT,WAAA,GAAAxB,EAAA,CAAAe,aAAA,IAAAkB,SAAA;MAAA,MAAA4B,OAAA,GAAA7D,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAA6C,OAAA,CAAAC,cAAA,CAAAtC,WAAA,EAAAoC,YAAA,CAAAG,KAAA,CAAuC;IAAA,EAAC;IAEjD/D,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAgE,kBAAA,MAAAJ,YAAA,CAAAG,KAAA,OAAAH,YAAA,CAAAK,KAAA,MACF;;;;;IAVFjE,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAqC,UAAA,IAAA6B,wDAAA,mBAMO;IACTlE,EAAA,CAAAG,YAAA,EAAM;;;;IANmBH,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAuB,UAAA,YAAAC,WAAA,CAAA2C,SAAA,CAAoB;;;;;IAsBzCnE,EAAA,CAAAkB,SAAA,YAAsD;;;;;IACtDlB,EAAA,CAAAkB,SAAA,YAAmE;;;;;IACnElB,EAAA,CAAAkB,SAAA,YAGK;;;;;IACLlB,EAAA,CAAAkB,SAAA,YAGK;;;;;IAhBPlB,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAqC,UAAA,IAAA+B,qDAAA,gBAAsD;IACtDpE,EAAA,CAAAqC,UAAA,IAAAgC,qDAAA,gBAAmE;IACnErE,EAAA,CAAAqC,UAAA,IAAAiC,qDAAA,gBAGK;IACLtE,EAAA,CAAAqC,UAAA,IAAAkC,qDAAA,gBAGK;IACPvE,EAAA,CAAAG,YAAA,EAAM;;;;IAdJH,EAAA,CAAAwE,WAAA,SAAAhD,WAAA,CAAAiD,MAAA,CAA6B,YAAAjD,WAAA,CAAAkD,SAAA,WAAAlD,WAAA,CAAAmD,OAAA;IAIzB3E,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAuB,UAAA,SAAAC,WAAA,CAAAkD,SAAA,CAAuB;IACvB1E,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAuB,UAAA,SAAAC,WAAA,CAAAmD,OAAA,CAAqB;IAEtB3E,EAAA,CAAAI,SAAA,GAA8D;IAA9DJ,EAAA,CAAAuB,UAAA,UAAAC,WAAA,CAAAkD,SAAA,KAAAlD,WAAA,CAAAmD,OAAA,IAAAnD,WAAA,CAAAiD,MAAA,CAA8D;IAI9DzE,EAAA,CAAAI,SAAA,GAA+D;IAA/DJ,EAAA,CAAAuB,UAAA,UAAAC,WAAA,CAAAkD,SAAA,KAAAlD,WAAA,CAAAmD,OAAA,KAAAnD,WAAA,CAAAiD,MAAA,CAA+D;;;;;;IApI1EzE,EAAA,CAAAC,cAAA,cAIC;IAECD,EAAA,CAAAqC,UAAA,IAAAuC,gDAAA,kBAKE;IAGF5E,EAAA,CAAAC,cAAA,cAIC;IAECD,EAAA,CAAAqC,UAAA,IAAAwC,gDAAA,kBAKM;IAGN7E,EAAA,CAAAqC,UAAA,IAAAyC,gDAAA,kBAOM;IAGN9E,EAAA,CAAAC,cAAA,cAA+B;IAE7BD,EAAA,CAAAqC,UAAA,IAAA0C,gDAAA,kBAEM;IAGN/E,EAAA,CAAAqC,UAAA,IAAA2C,gDAAA,kBAUM;IAGNhF,EAAA,CAAAqC,UAAA,IAAA4C,gDAAA,kBAcM;IAGNjF,EAAA,CAAAqC,UAAA,IAAA6C,gDAAA,kBAOM;IAGNlF,EAAA,CAAAqC,UAAA,KAAA8C,iDAAA,kBASM;IACRnF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAqC,UAAA,KAAA+C,iDAAA,kBAWM;IAGNpF,EAAA,CAAAC,cAAA,eAAoD;IAEhDD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGPH,EAAA,CAAAqC,UAAA,KAAAgD,iDAAA,mBAiBM;IACRrF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAEC;IAGGD,EAAA,CAAAU,UAAA,mBAAA4E,oEAAA;MAAA,MAAA5B,WAAA,GAAA1D,EAAA,CAAAY,aAAA,CAAA2E,IAAA;MAAA,MAAA/D,WAAA,GAAAkC,WAAA,CAAAzB,SAAA;MAAA,MAAAuD,OAAA,GAAAxF,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAwE,OAAA,CAAAC,eAAA,CAAAjE,WAAA,CAAwB;IAAA,EAAC;IAElCxB,EAAA,CAAAkB,SAAA,aAAyC;IAC3ClB,EAAA,CAAAG,YAAA,EAAS;;;;;IAhJbH,EAAA,CAAAwE,WAAA,eAAAkB,MAAA,CAAAC,WAAA,CAAAnE,WAAA,EAAyC;IAItCxB,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAuB,UAAA,UAAAmE,MAAA,CAAAC,WAAA,CAAAnE,WAAA,KAAAA,WAAA,CAAAC,MAAA,CAA6C;IAS9CzB,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAwE,WAAA,eAAAkB,MAAA,CAAAC,WAAA,CAAAnE,WAAA,EAAyC,mBAAAkE,MAAA,CAAAC,WAAA,CAAAnE,WAAA;IAKtCxB,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAAuB,UAAA,SAAAmE,MAAA,CAAAnF,oBAAA,CAAAqF,OAAA,KAAAF,MAAA,CAAAC,WAAA,CAAAnE,WAAA,EAA2D;IAOxDxB,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAuB,UAAA,SAAAC,WAAA,CAAAK,OAAA,CAAqB;IAUtB7B,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAuB,UAAA,aAAAC,WAAA,CAAAqE,IAAA,CAAyB;IAEtB7F,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAuB,UAAA,iBAAAmE,MAAA,CAAA5F,WAAA,CAAAgG,IAAA,CAA8B;IAK9B9F,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAuB,UAAA,iBAAAmE,MAAA,CAAA5F,WAAA,CAAAiG,KAAA,CAA+B;IAa/B/F,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAuB,UAAA,iBAAAmE,MAAA,CAAA5F,WAAA,CAAAkG,IAAA,CAA8B;IAiB9BhG,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAuB,UAAA,iBAAAmE,MAAA,CAAA5F,WAAA,CAAAmG,aAAA,CAAuC;IAUvCjG,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAuB,UAAA,iBAAAmE,MAAA,CAAA5F,WAAA,CAAAoG,KAAA,CAA+B;IAcpClG,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAAuB,UAAA,SAAAC,WAAA,CAAA2C,SAAA,IAAA3C,WAAA,CAAA2C,SAAA,CAAA1D,MAAA,KAAuD;IAetDT,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAqF,MAAA,CAAAS,iBAAA,CAAA3E,WAAA,CAAA4E,SAAA,OACF;IAIGpG,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAuB,UAAA,SAAAmE,MAAA,CAAAC,WAAA,CAAAnE,WAAA,EAA0B;;;;;IAkCnCxB,EAAA,CAAAC,cAAA,cAA6D;IAEzDD,EAAA,CAAAkB,SAAA,cAA8B;IAGhClB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA5BH,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAA6C,iBAAA,CAAAwD,OAAA,CAAAC,aAAA,GAAqB;;;;;;IAS7BtG,EAAA,CAAAC,cAAA,cAA8C;IAItCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAExDH,EAAA,CAAAC,cAAA,kBAAuE;IAA/DD,EAAA,CAAAU,UAAA,mBAAA6F,mEAAA;MAAAvG,EAAA,CAAAY,aAAA,CAAA4F,IAAA;MAAA,MAAAC,OAAA,GAAAzG,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAyF,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAC7B1G,EAAA,CAAAkB,SAAA,aAA4B;IAC9BlB,EAAA,CAAAG,YAAA,EAAS;;;;IANLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,0BAAAsG,OAAA,CAAAC,UAAA,CAAAnF,MAAA,kBAAAkF,OAAA,CAAAC,UAAA,CAAAnF,MAAA,CAAAG,QAAA,MACF;IACwB5B,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAA6C,iBAAA,CAAA8D,OAAA,CAAAC,UAAA,CAAA9E,OAAA,CAAwB;;;;;;IAStD9B,EAAA,CAAAC,cAAA,cAAkD;IAGPD,EAAA,CAAAE,MAAA,8BAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAClEH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAE5DH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAU,UAAA,mBAAAmG,mEAAA;MAAA7G,EAAA,CAAAY,aAAA,CAAAkG,IAAA;MAAA,MAAAC,OAAA,GAAA/G,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAA+F,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAGzBhH,EAAA,CAAAkB,SAAA,aAA4B;IAC9BlB,EAAA,CAAAG,YAAA,EAAS;;;;IAPiBH,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAA6C,iBAAA,CAAAoE,OAAA,CAAAC,cAAA,CAAApF,OAAA,CAA4B;;;;;;IActD9B,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAAkB,SAAA,aAAyC;IACzClB,EAAA,CAAAC,cAAA,gBAAmD;IAAAD,EAAA,CAAAE,MAAA,GAEjD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACTH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAU,UAAA,mBAAAyG,yEAAA;MAAA,MAAAzD,WAAA,GAAA1D,EAAA,CAAAY,aAAA,CAAAwG,IAAA;MAAA,MAAAC,KAAA,GAAA3D,WAAA,CAAA4D,KAAA;MAAA,MAAAC,OAAA,GAAAvH,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAuG,OAAA,CAAAC,kBAAA,CAAAH,KAAA,CAAqB;IAAA,EAAC;IAG/BrH,EAAA,CAAAkB,SAAA,aAAoC;IACtClB,EAAA,CAAAG,YAAA,EAAS;;;;IAR0CH,EAAA,CAAAI,SAAA,GAEjD;IAFiDJ,EAAA,CAAA6C,iBAAA,CAAA4E,QAAA,CAAAjF,IAAA,CAEjD;;;;;IATRxC,EAAA,CAAAC,cAAA,eAAmD;IAE/CD,EAAA,CAAAqC,UAAA,IAAAqF,gDAAA,mBAcM;IACR1H,EAAA,CAAAG,YAAA,EAAM;;;;IAdeH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAuB,UAAA,YAAAoG,OAAA,CAAAC,aAAA,CAAkB;;;;;;IAkBzC5H,EAAA,CAAAC,cAAA,eAA0D;IACxDD,EAAA,CAAAkB,SAAA,aAA4C;IAC5ClB,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAE,MAAA,GAE3B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACTH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAU,UAAA,mBAAAmH,mEAAA;MAAA7H,EAAA,CAAAY,aAAA,CAAAkH,IAAA;MAAA,MAAAC,OAAA,GAAA/H,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAA+G,OAAA,CAAAC,kBAAA,EAAoB;IAAA,EAAC;IAG9BhI,EAAA,CAAAkB,SAAA,aAA2B;IAC7BlB,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAU,UAAA,mBAAAuH,mEAAA;MAAAjI,EAAA,CAAAY,aAAA,CAAAkH,IAAA;MAAA,MAAAI,OAAA,GAAAlI,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAkH,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAGhCnI,EAAA,CAAAkB,SAAA,aAA4B;IAC9BlB,EAAA,CAAAG,YAAA,EAAS;;;;IAdoBH,EAAA,CAAAI,SAAA,GAE3B;IAF2BJ,EAAA,CAAA6C,iBAAA,CAAAuF,OAAA,CAAA9E,cAAA,CAAA8E,OAAA,CAAAC,iBAAA,EAE3B;;;;;;IA8BErI,EAAA,CAAAC,cAAA,eAGC;IAEGD,EAAA,CAAAU,UAAA,mBAAA4H,mEAAA;MAAAtI,EAAA,CAAAY,aAAA,CAAA2H,IAAA;MAAA,MAAAC,OAAA,GAAAxI,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAwH,OAAA,CAAAC,gBAAA,EAAkB;IAAA,EAAC;IAG5BzI,EAAA,CAAAkB,SAAA,aAAyC;IACzClB,EAAA,CAAAC,cAAA,gBAAiC;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEjDH,EAAA,CAAAC,cAAA,kBAGC;IAFCD,EAAA,CAAAU,UAAA,mBAAAgI,mEAAA;MAAA1I,EAAA,CAAAY,aAAA,CAAA2H,IAAA;MAAA,MAAAI,OAAA,GAAA3I,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAA2H,OAAA,CAAAF,gBAAA,EAAkB;IAAA,EAAC;IAG5BzI,EAAA,CAAAkB,SAAA,aAA2C;IAC3ClB,EAAA,CAAAC,cAAA,gBAAiC;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IA0BnDH,EAAA,CAAAC,cAAA,kBAOC;IAFCD,EAAA,CAAAU,UAAA,uBAAAkI,0EAAA;MAAA5I,EAAA,CAAAY,aAAA,CAAAiI,IAAA;MAAA,MAAAC,OAAA,GAAA9I,EAAA,CAAAe,aAAA;MAAA,OAAaf,EAAA,CAAAgB,WAAA,CAAA8H,OAAA,CAAAC,mBAAA,EAAqB;IAAA,EAAC;IAGnC/I,EAAA,CAAAkB,SAAA,aAAiC;IACnClB,EAAA,CAAAG,YAAA,EAAS;;;;;;IAGTH,EAAA,CAAAC,cAAA,kBAMC;IAHCD,EAAA,CAAAU,UAAA,mBAAAsI,sEAAA;MAAAhJ,EAAA,CAAAY,aAAA,CAAAqI,IAAA;MAAA,MAAAC,OAAA,GAAAlJ,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAkI,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAIvBnJ,EAAA,CAAAkB,SAAA,aAA6C;IAC/ClB,EAAA,CAAAG,YAAA,EAAS;;;;IAJPH,EAAA,CAAAuB,UAAA,cAAA6H,OAAA,CAAAC,cAAA,GAA8B;;;;;;IA9YxCrJ,EAAA,CAAAC,cAAA,aAAyD;IAMnDD,EAAA,CAAAkB,SAAA,aAaE;IAEFlB,EAAA,CAAAC,cAAA,aAA0B;IAEtBD,EAAA,CAAAE,MAAA,GAKF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,WAGC;IACCD,EAAA,CAAAqC,UAAA,IAAAiH,0CAAA,kBAEO;IACPtJ,EAAA,CAAAqC,UAAA,IAAAkH,0CAAA,kBAEC;IACDvJ,EAAA,CAAAqC,UAAA,KAAAmH,2CAAA,kBAEC;IACHxJ,EAAA,CAAAG,YAAA,EAAI;IAIRH,EAAA,CAAAC,cAAA,cAA0B;IAExBD,EAAA,CAAAqC,UAAA,KAAAoH,6CAAA,qBAOS;IAGTzJ,EAAA,CAAAqC,UAAA,KAAAqH,6CAAA,qBAOS;IAGT1J,EAAA,CAAAC,cAAA,kBAA2C;IACzCD,EAAA,CAAAkB,SAAA,aAAiC;IACnClB,EAAA,CAAAG,YAAA,EAAS;IAObH,EAAA,CAAAC,cAAA,mBAAmD;IAEjDD,EAAA,CAAAqC,UAAA,KAAAsH,0CAAA,kBAIM;IAGN3J,EAAA,CAAAqC,UAAA,KAAAuH,0CAAA,oBAsJM;IAGN5J,EAAA,CAAAqC,UAAA,KAAAwH,0CAAA,kBAOM;IACR7J,EAAA,CAAAG,YAAA,EAAM;IAKNH,EAAA,CAAAC,cAAA,eAAqC;IAEnCD,EAAA,CAAAqC,UAAA,KAAAyH,0CAAA,kBAYM;IAGN9J,EAAA,CAAAqC,UAAA,KAAA0H,0CAAA,kBAaM;IAGN/J,EAAA,CAAAqC,UAAA,KAAA2H,0CAAA,kBAkBM;IAGNhK,EAAA,CAAAqC,UAAA,KAAA4H,0CAAA,kBAiBM;IAGNjK,EAAA,CAAAC,cAAA,eAA2B;IAOnBD,EAAA,CAAAU,UAAA,mBAAAwJ,6DAAA;MAAAlK,EAAA,CAAAY,aAAA,CAAAuJ,IAAA;MAAA,MAAAC,OAAA,GAAApK,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAoJ,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAGhCrK,EAAA,CAAAkB,SAAA,aAAgC;IAClClB,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAqC,UAAA,KAAAiI,0CAAA,kBAkBM;IACRtK,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,kBAAsE;IAA5CD,EAAA,CAAAU,UAAA,mBAAA6J,6DAAA;MAAAvK,EAAA,CAAAY,aAAA,CAAAuJ,IAAA;MAAA,MAAAK,OAAA,GAAAxK,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAwJ,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IACrDzK,EAAA,CAAAkB,SAAA,aAA4B;IAC9BlB,EAAA,CAAAG,YAAA,EAAS;IAIXH,EAAA,CAAAC,cAAA,wBASC;IAPCD,EAAA,CAAAU,UAAA,2BAAAgK,uEAAAC,MAAA;MAAA3K,EAAA,CAAAY,aAAA,CAAAuJ,IAAA;MAAA,MAAAS,OAAA,GAAA5K,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAA4J,OAAA,CAAAC,cAAA,GAAAF,MAAA;IAAA,EAA4B,qBAAAG,iEAAAH,MAAA;MAAA3K,EAAA,CAAAY,aAAA,CAAAuJ,IAAA;MAAA,MAAAY,OAAA,GAAA/K,EAAA,CAAAe,aAAA;MAAA,OACjBf,EAAA,CAAAgB,WAAA,CAAA+J,OAAA,CAAAC,UAAA,CAAAL,MAAA,CAAkB;IAAA,EADD,mBAAAM,+DAAA;MAAAjL,EAAA,CAAAY,aAAA,CAAAuJ,IAAA;MAAA,MAAAe,OAAA,GAAAlL,EAAA,CAAAe,aAAA;MAAA,OAEnBf,EAAA,CAAAgB,WAAA,CAAAkK,OAAA,CAAAC,QAAA,EAAU;IAAA,EAFS;IAO7BnL,EAAA,CAAAG,YAAA,EAAW;IAGZH,EAAA,CAAAC,cAAA,eAA2B;IAEzBD,EAAA,CAAAqC,UAAA,KAAA+I,6CAAA,qBASS;IAGTpL,EAAA,CAAAqC,UAAA,KAAAgJ,6CAAA,qBAQS;IACXrL,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,qBAOE;IAFAD,EAAA,CAAAU,UAAA,oBAAA4K,6DAAAX,MAAA;MAAA3K,EAAA,CAAAY,aAAA,CAAAuJ,IAAA;MAAA,MAAAoB,OAAA,GAAAvL,EAAA,CAAAe,aAAA;MAAA,OAAUf,EAAA,CAAAgB,WAAA,CAAAuK,OAAA,CAAAC,cAAA,CAAAb,MAAA,CAAsB;IAAA,EAAC;IALnC3K,EAAA,CAAAG,YAAA,EAOE;;;;IA5YEH,EAAA,CAAAI,SAAA,GAAqE;IAArEJ,EAAA,CAAAwE,WAAA,YAAAiH,MAAA,CAAAlL,oBAAA,CAAAqF,OAAA,IAAA6F,MAAA,CAAAC,iBAAA,GAAqE;IAXrE1L,EAAA,CAAAuB,UAAA,QAAAkK,MAAA,CAAAlL,oBAAA,CAAAqF,OAAA,GAAA6F,MAAA,CAAAlL,oBAAA,CAAAoL,UAAA,GAAAF,MAAA,CAAAG,kBAAA,IAAA5L,EAAA,CAAA2B,aAAA,CAIC,QAAA8J,MAAA,CAAAlL,oBAAA,CAAAqF,OAAA,GAAA6F,MAAA,CAAAlL,oBAAA,CAAAsL,SAAA,GAAAJ,MAAA,CAAAK,gBAAA;IAYC9L,EAAA,CAAAI,SAAA,GAKF;IALEJ,EAAA,CAAAK,kBAAA,MAAAoL,MAAA,CAAAlL,oBAAA,CAAAqF,OAAA,GAAA6F,MAAA,CAAAlL,oBAAA,CAAAsL,SAAA,GAAAJ,MAAA,CAAAK,gBAAA,QAKF;IAGE9L,EAAA,CAAAI,SAAA,GAAqE;IAArEJ,EAAA,CAAAwE,WAAA,YAAAiH,MAAA,CAAAlL,oBAAA,CAAAqF,OAAA,IAAA6F,MAAA,CAAAC,iBAAA,GAAqE;IAE9D1L,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAuB,UAAA,SAAAkK,MAAA,CAAAlL,oBAAA,CAAAqF,OAAA,CAAkC;IAGlC5F,EAAA,CAAAI,SAAA,GAA0D;IAA1DJ,EAAA,CAAAuB,UAAA,UAAAkK,MAAA,CAAAlL,oBAAA,CAAAqF,OAAA,IAAA6F,MAAA,CAAAC,iBAAA,GAA0D;IAG1D1L,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAAuB,UAAA,UAAAkK,MAAA,CAAAlL,oBAAA,CAAAqF,OAAA,KAAA6F,MAAA,CAAAC,iBAAA,GAA2D;IAanE1L,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAuB,UAAA,UAAAkK,MAAA,CAAAlL,oBAAA,CAAAqF,OAAA,CAAmC;IAUnC5F,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAuB,UAAA,UAAAkK,MAAA,CAAAlL,oBAAA,CAAAqF,OAAA,CAAmC;IAiBlC5F,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAuB,UAAA,SAAAkK,MAAA,CAAAM,SAAA,CAAe;IAQC/L,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAuB,UAAA,YAAAkK,MAAA,CAAAO,QAAA,CAAa,iBAAAP,MAAA,CAAAQ,gBAAA;IAwJ7BjM,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAuB,UAAA,SAAAkK,MAAA,CAAAS,WAAA,CAAAzL,MAAA,KAA4B;IAe5BT,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAuB,UAAA,SAAAkK,MAAA,CAAA7E,UAAA,CAAgB;IAehB5G,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAuB,UAAA,SAAAkK,MAAA,CAAAvE,cAAA,CAAoB;IAgBpBlH,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAuB,UAAA,SAAAkK,MAAA,CAAA7D,aAAA,CAAAnH,MAAA,KAA8B;IAqB9BT,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAuB,UAAA,SAAAkK,MAAA,CAAAU,WAAA,CAAiB;IAmCdnM,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAuB,UAAA,SAAAkK,MAAA,CAAAW,kBAAA,CAAwB;IA6B7BpM,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAuB,UAAA,YAAAkK,MAAA,CAAAZ,cAAA,CAA4B,aAAAY,MAAA,CAAAU,WAAA;IAazBnM,EAAA,CAAAI,SAAA,GAEA;IAFAJ,EAAA,CAAAuB,UAAA,UAAAkK,MAAA,CAAAZ,cAAA,CAAAwB,IAAA,OAAAZ,MAAA,CAAA7D,aAAA,CAAAnH,MAAA,KAAAgL,MAAA,CAAAU,WAAA,CAEA;IAUAnM,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAuB,UAAA,SAAAkK,MAAA,CAAAZ,cAAA,CAAAwB,IAAA,MAAAZ,MAAA,CAAA7D,aAAA,CAAAnH,MAAA,CAAmD;;;;;IA0B9DT,EAAA,CAAAC,cAAA,eAGC;IAOOD,EAAA,CAAAkB,SAAA,aAAmD;IACrDlB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA+C;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtEH,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAE,MAAA,oDAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAIvEH,EAAA,CAAAC,cAAA,eAA4B;IAKtBD,EAAA,CAAAkB,SAAA,cAA8C;IAChDlB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,WAAK;IACgCD,EAAA,CAAAE,MAAA,mCAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9DH,EAAA,CAAAC,cAAA,cAAiC;IAC/BD,EAAA,CAAAE,MAAA,4DACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAIRH,EAAA,CAAAC,cAAA,gBAAmD;IAI/CD,EAAA,CAAAkB,SAAA,cAA+C;IACjDlB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,WAAK;IACgCD,EAAA,CAAAE,MAAA,+BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1DH,EAAA,CAAAC,cAAA,cAAiC;IAAAD,EAAA,CAAAE,MAAA,gDAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAIvEH,EAAA,CAAAC,cAAA,gBAAmD;IAI/CD,EAAA,CAAAkB,SAAA,cAA8C;IAChDlB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,WAAK;IACgCD,EAAA,CAAAE,MAAA,2BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3DH,EAAA,CAAAC,cAAA,cAAiC;IAAAD,EAAA,CAAAE,MAAA,wCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAMtEH,EAAA,CAAAC,cAAA,gBAA+D;IACXD,EAAA,CAAAE,MAAA,2BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1EH,EAAA,CAAAC,cAAA,gBAA6C;IACxCD,EAAA,CAAAE,MAAA,iEAA+C;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACtDH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,0EAAwD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC/DH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,4DAA+C;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;ADlc9D,OAAM,MAAOmM,oBAAoB;EAgD/BC,YACUC,cAA8B,EAC9BC,WAAwB,EACxBC,YAA0B,EAC1BC,KAAqB,EACrBC,MAAc,EACdC,GAAsB,EACtBC,MAAc;IANd,KAAAN,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,MAAM,GAANA,MAAM;IAjDhB;IACA,KAAAC,WAAW,GAAgB,IAAI;IAC/B,KAAAxM,oBAAoB,GAAwB,IAAI;IAChD,KAAAyL,QAAQ,GAAc,EAAE;IACxB,KAAAD,SAAS,GAAG,KAAK;IACjB,KAAAiB,QAAQ,GAAG,KAAK;IAChB,KAAAd,WAAW,GAAW,EAAE;IAExB;IACA,KAAAe,WAAW,GAAG,CAAC;IACf,KAAAC,eAAe,GAAG,IAAI;IACtB,KAAAC,mBAAmB,GAAG,KAAK;IAE3B;IACA,KAAAtC,cAAc,GAAG,EAAE;IACnB,KAAAjD,aAAa,GAAW,EAAE;IAC1B,KAAAuE,WAAW,GAAG,KAAK;IACnB,KAAA9D,iBAAiB,GAAG,CAAC;IAErB;IACA,KAAA+E,eAAe,GAAG,KAAK;IACvB,KAAAhB,kBAAkB,GAAG,KAAK;IAC1B,KAAAxF,UAAU,GAAmB,IAAI;IACjC,KAAAM,cAAc,GAAmB,IAAI;IAErC;IACA,KAAAmG,WAAW,GAAG,EAAE;IAChB,KAAAC,aAAa,GAAc,EAAE;IAC7B,KAAAC,iBAAiB,GAAG,KAAK;IAEzB;IACQ,KAAAC,aAAa,GAAmB,EAAE;IAI1C;IACQ,KAAAC,eAAe,GAAG,IAAIlO,eAAe,CAAgB,IAAI,CAAC;IAElE;IACS,KAAAO,WAAW,GAAGA,WAAW;IACzB,KAAAC,QAAQ,GAAGA,QAAQ;EAUzB;EAEH2N,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACC,OAAO,EAAE;EAChB;EAEA;EACA;EACA;EAEQL,mBAAmBA,CAAA;IACzB;IACA,IAAI,CAACZ,WAAW,GAAG,IAAI,CAACN,WAAW,CAACwB,cAAc,EAAE;IAEpD,IAAI,CAAC,IAAI,CAAClB,WAAW,EAAE;MACrB,IAAI,CAACH,MAAM,CAACsB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAChC;;IAGF;IACA,IAAI,CAACvB,KAAK,CAACwB,MAAM,CAACC,SAAS,CAAED,MAAM,IAAI;MACrC,MAAME,cAAc,GAAGF,MAAM,CAAC,gBAAgB,CAAC;MAC/C,IAAIE,cAAc,EAAE;QAClB,IAAI,CAACZ,eAAe,CAACa,IAAI,CAACD,cAAc,CAAC;;IAE7C,CAAC,CAAC;EACJ;EAEQT,kBAAkBA,CAAA;IACxB;IACA,MAAMW,eAAe,GAAG,IAAI,CAACd,eAAe,CACzCe,IAAI,CACH3O,MAAM,CAAE4O,EAAE,IAAK,CAAC,CAACA,EAAE,CAAC,EACpBhP,oBAAoB,EAAE,EACtBG,GAAG,CAAC,MAAK;MACP,IAAI,CAACmM,SAAS,GAAG,IAAI;MACrB,IAAI,CAACC,QAAQ,GAAG,EAAE;MAClB,IAAI,CAACiB,WAAW,GAAG,CAAC;MACpB,IAAI,CAACC,eAAe,GAAG,IAAI;IAC7B,CAAC,CAAC,EACFxN,SAAS,CAAE2O,cAAc,IACvB,IAAI,CAAC7B,cAAc,CAACkC,eAAe,CAACL,cAAe,EAAE,EAAE,EAAE,CAAC,CAAC,CAC5D,EACD1O,UAAU,CAAEgP,KAAK,IAAI;MACnBC,OAAO,CAACD,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACrE,IAAI,CAACjC,YAAY,CAACmC,SAAS,CACzB,8CAA8C,CAC/C;MACD,OAAOrP,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH,CACA4O,SAAS,CAAEU,YAAY,IAAI;MAC1B,IAAI,CAAC/C,SAAS,GAAG,KAAK;MACtB,IAAI+C,YAAY,EAAE;QAChB,IAAI,CAACvO,oBAAoB,GAAGuO,YAAY;QACxC,IAAI,CAAC9C,QAAQ,GAAG8C,YAAY,CAAC9C,QAAQ,IAAI,EAAE;QAC3C,IAAI,CAAC8B,cAAc,EAAE;QACrB,IAAI,CAACiB,kBAAkB,EAAE;;MAE3B,IAAI,CAAClC,GAAG,CAACmC,aAAa,EAAE;IAC1B,CAAC,CAAC;IAEJ;IACA,MAAMC,WAAW,GAAG,IAAI,CAACzC,cAAc,CACpC0C,mBAAmB,EAAE,CACrBd,SAAS,CAAEe,OAAO,IAAI;MACrB,IACEA,OAAO,IACP,IAAI,CAAC5O,oBAAoB,IACzB4O,OAAO,CAACd,cAAc,KAAK,IAAI,CAAC9N,oBAAoB,CAACkO,EAAE,EACvD;QACA,IAAI,CAACW,aAAa,CAACD,OAAO,CAAC;QAC3B,IAAI,CAACrB,cAAc,EAAE;QACrB,IAAI,CAACuB,iBAAiB,CAACF,OAAO,CAAC;;IAEnC,CAAC,CAAC;IAEJ;IACA,MAAMG,SAAS,GAAG,IAAI,CAAC9C,cAAc,CAClC+C,2BAA2B,EAAE,CAC7BnB,SAAS,CAAEoB,KAAK,IAAI;MACnB,IACEA,KAAK,IACL,IAAI,CAACjP,oBAAoB,IACzBiP,KAAK,CAACnB,cAAc,KAAK,IAAI,CAAC9N,oBAAoB,CAACkO,EAAE,EACrD;QACA,IAAI,CAACgB,qBAAqB,CAACD,KAAK,CAAC;;IAErC,CAAC,CAAC;IAEJ,IAAI,CAAChC,aAAa,CAACkC,IAAI,CAACnB,eAAe,EAAEU,WAAW,EAAEK,SAAS,CAAC;EAClE;EAEQtB,OAAOA,CAAA;IACb,IAAI,CAACR,aAAa,CAACmC,OAAO,CAAEC,GAAG,IAAKA,GAAG,CAACC,WAAW,EAAE,CAAC;IACtD,IAAI,IAAI,CAACC,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;IAElC,IAAI,IAAI,CAACE,iBAAiB,EAAE;MAC1BC,aAAa,CAAC,IAAI,CAACD,iBAAiB,CAAC;;IAEvC,IAAI,CAACE,UAAU,EAAE;EACnB;EAEA;EACA;EACA;EAEA/G,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACE,cAAc,EAAE,EAAE;MAC1B;;IAGF,MAAMvH,OAAO,GAAG,IAAI,CAAC+I,cAAc,CAACwB,IAAI,EAAE;IAC1C,MAAM8D,KAAK,GAAG,IAAI,CAACvI,aAAa;IAEhC;IACA,IAAI,CAACiD,cAAc,GAAG,EAAE;IACxB,IAAI,CAACjD,aAAa,GAAG,EAAE;IACvB,IAAI,CAAChB,UAAU,GAAG,IAAI;IACtB,IAAI,CAACsJ,UAAU,EAAE;IAEjB,IAAI,IAAI,CAAChJ,cAAc,EAAE;MACvB,IAAI,CAACkJ,aAAa,CAACtO,OAAO,CAAC;MAC3B;;IAGF;IACA,IAAIA,OAAO,IAAIqO,KAAK,CAAC1P,MAAM,GAAG,CAAC,EAAE;MAC/B,IAAI,CAAC4P,cAAc,CAACvO,OAAO,EAAEqO,KAAK,CAAC;;EAEvC;EAEA9G,cAAcA,CAAA;IACZ,MAAMiH,UAAU,GAAG,IAAI,CAACzF,cAAc,CAACwB,IAAI,EAAE,CAAC5L,MAAM,GAAG,CAAC;IACxD,MAAM8P,QAAQ,GAAG,IAAI,CAAC3I,aAAa,CAACnH,MAAM,GAAG,CAAC;IAC9C,MAAM+P,eAAe,GAAG,CAAC,CAAC,IAAI,CAACjQ,oBAAoB;IAEnD,OAAOiQ,eAAe,KAAKF,UAAU,IAAIC,QAAQ,CAAC;EACpD;EAEQF,cAAcA,CAACvO,OAAe,EAAEqO,KAAa;IACnD,IAAI,CAAC,IAAI,CAAC5P,oBAAoB,IAAI,CAAC,IAAI,CAACwM,WAAW,EAAE;IAErD,MAAM0D,WAAW,GAAG,IAAI,CAACC,cAAc,EAAE;IACzC,IAAI,CAACD,WAAW,EAAE;IAElB;IACA,MAAME,WAAW,GAAY;MAC3BlC,EAAE,EAAE,QAAQmC,IAAI,CAACC,GAAG,EAAE,EAAE;MACxB/O,OAAO;MACP+D,IAAI,EACFsK,KAAK,CAAC1P,MAAM,GAAG,CAAC,GAAG,IAAI,CAACqQ,kBAAkB,CAACX,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGrQ,WAAW,CAACgG,IAAI;MACzEM,SAAS,EAAE,IAAIwK,IAAI,EAAE;MACrBnP,MAAM,EAAE,IAAI,CAACsL,WAAW;MACxBrI,SAAS,EAAE,IAAI;MACf2J,cAAc,EAAE,IAAI,CAAC9N,oBAAoB,CAACkO;KAC3C;IAED,IAAI,CAACW,aAAa,CAACuB,WAAW,CAAC;IAC/B,IAAI,CAAC7C,cAAc,EAAE;IAErB;IACA,MAAMiD,cAAc,GAClBZ,KAAK,CAAC1P,MAAM,GAAG,CAAC,GACZ,IAAI,CAAC+L,cAAc,CAACwE,mBAAmB,CACrC,IAAI,CAACjE,WAAW,CAAC0B,EAAE,IAAI,IAAI,CAAC1B,WAAW,CAACkE,GAAI,EAC5CR,WAAW,EACX3O,OAAO,EACPqO,KAAK,CAAC,CAAC,CAAC,CACT,GACD,IAAI,CAAC3D,cAAc,CAACrD,WAAW,CAC7B,IAAI,CAAC4D,WAAW,CAAC0B,EAAE,IAAI,IAAI,CAAC1B,WAAW,CAACkE,GAAI,EAC5CR,WAAW,EACX3O,OAAO,CACR;IAEPiP,cAAc,CAAC3C,SAAS,CAAC;MACvBE,IAAI,EAAG4C,WAAW,IAAI;QACpB,IAAI,CAACC,uBAAuB,CAACR,WAAW,CAAClC,EAAG,EAAEyC,WAAW,CAAC;QAC1D,IAAI,CAACxE,YAAY,CAAC0E,WAAW,CAAC,gBAAgB,CAAC;MACjD,CAAC;MACDzC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1D,IAAI,CAAC0C,kBAAkB,CAACV,WAAW,CAAClC,EAAG,CAAC;QACxC,IAAI,CAAC/B,YAAY,CAACmC,SAAS,CAAC,mCAAmC,CAAC;MAClE;KACD,CAAC;EACJ;EAEQuB,aAAaA,CAACkB,UAAkB;IACtC,IAAI,CAAC,IAAI,CAACpK,cAAc,EAAE;IAE1B,IAAI,CAACsF,cAAc,CAChB+E,WAAW,CAAC,IAAI,CAACrK,cAAc,CAACuH,EAAG,EAAE6C,UAAU,CAAC,CAChDlD,SAAS,CAAC;MACTE,IAAI,EAAGkD,cAAc,IAAI;QACvB,IAAI,CAACC,mBAAmB,CAACD,cAAc,CAAC;QACxC,IAAI,CAACtK,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACwF,YAAY,CAAC0E,WAAW,CAAC,iBAAiB,CAAC;MAClD,CAAC;MACDzC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;QAClE,IAAI,CAACjC,YAAY,CAACmC,SAAS,CACzB,2CAA2C,CAC5C;MACH;KACD,CAAC;EACN;EAEA6C,aAAaA,CAACvC,OAAgB;IAC5B,IAAI,CAACA,OAAO,CAACV,EAAE,IAAI,CAAC,IAAI,CAACkD,gBAAgB,CAACxC,OAAO,CAAC,EAAE;IAEpD,IAAIyC,OAAO,CAAC,iDAAiD,CAAC,EAAE;MAC9D,IAAI,CAACpF,cAAc,CAACkF,aAAa,CAACvC,OAAO,CAACV,EAAE,CAAC,CAACL,SAAS,CAAC;QACtDE,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACuD,qBAAqB,CAAC1C,OAAO,CAACV,EAAG,CAAC;UACvC,IAAI,CAAC/B,YAAY,CAAC0E,WAAW,CAAC,kBAAkB,CAAC;QACnD,CAAC;QACDzC,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;UACjE,IAAI,CAACjC,YAAY,CAACmC,SAAS,CACzB,0CAA0C,CAC3C;QACH;OACD,CAAC;;EAEN;EAEA/K,cAAcA,CAACqL,OAAgB,EAAEpL,KAAa;IAC5C,IAAI,CAACoL,OAAO,CAACV,EAAE,EAAE;IAEjB,IAAI,CAACjC,cAAc,CAAC1I,cAAc,CAACqL,OAAO,CAACV,EAAE,EAAE1K,KAAK,CAAC,CAACqK,SAAS,CAAC;MAC9DE,IAAI,EAAGkD,cAAc,IAAI;QACvB,IAAI,CAACC,mBAAmB,CAACD,cAAc,CAAC;MAC1C,CAAC;MACD7C,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAC9D,IAAI,CAACjC,YAAY,CAACmC,SAAS,CAAC,4BAA4B,CAAC;MAC3D;KACD,CAAC;EACJ;EAEA;EACA;EACA;EAEArD,cAAcA,CAACgE,KAAU;IACvB,MAAMW,KAAK,GAAGX,KAAK,CAACsC,MAAM,CAAC3B,KAAK;IAChC,IAAIA,KAAK,IAAIA,KAAK,CAAC1P,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAI,CAACmH,aAAa,GAAGmK,KAAK,CAACC,IAAI,CAAC7B,KAAK,CAAC;MACtC,IAAI,CAAC/D,kBAAkB,GAAG,KAAK;MAE/B;MACA,IAAI,IAAI,CAACvB,cAAc,CAACwB,IAAI,EAAE,KAAK,EAAE,EAAE;QACrC,IAAI,CAAClD,WAAW,EAAE;;;EAGxB;EAEA3B,kBAAkBA,CAACF,KAAa;IAC9B,IAAI,CAACM,aAAa,CAACqK,MAAM,CAAC3K,KAAK,EAAE,CAAC,CAAC;EACrC;EAEAmB,gBAAgBA,CAAA;IACd,IAAI,CAACyJ,SAAS,CAACC,aAAa,CAACC,KAAK,EAAE;EACtC;EAEA;EACA;EACA;EAEMrJ,mBAAmBA,CAAA;IAAA,IAAAsJ,KAAA;IAAA,OAAAC,iBAAA;MACvB,IAAI;QACF,MAAMC,MAAM,SAASC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;UAAEC,KAAK,EAAE;QAAI,CAAE,CAAC;QACzEN,KAAI,CAAClG,WAAW,GAAG,IAAI;QACvBkG,KAAI,CAAChK,iBAAiB,GAAG,CAAC;QAE1B;QACAgK,KAAI,CAACrC,iBAAiB,GAAG4C,WAAW,CAAC,MAAK;UACxCP,KAAI,CAAChK,iBAAiB,EAAE;QAC1B,CAAC,EAAE,IAAI,CAAC;QAER;QACA;OACD,CAAC,OAAOsG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7D0D,KAAI,CAAC3F,YAAY,CAACmC,SAAS,CAAC,oCAAoC,CAAC;;IAClE;EACH;EAEA7G,kBAAkBA,CAAA;IAChB,IAAI,CAACmE,WAAW,GAAG,KAAK;IACxB,IAAI,IAAI,CAAC6D,iBAAiB,EAAE;MAC1BC,aAAa,CAAC,IAAI,CAACD,iBAAiB,CAAC;;IAGvC;IACA;EACF;;EAEA7H,oBAAoBA,CAAA;IAClB,IAAI,CAACgE,WAAW,GAAG,KAAK;IACxB,IAAI,CAAC9D,iBAAiB,GAAG,CAAC;IAC1B,IAAI,IAAI,CAAC2H,iBAAiB,EAAE;MAC1BC,aAAa,CAAC,IAAI,CAACD,iBAAiB,CAAC;;EAEzC;EAEA;EACA;EACA;EAEA/O,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACV,oBAAoB,EAAE;IAEhC,MAAMkQ,WAAW,GAAG,IAAI,CAACC,cAAc,EAAE;IACzC,IAAI,CAACD,WAAW,EAAE;IAElB,IAAI,CAACjE,cAAc,CAACqG,YAAY,CAACpC,WAAW,EAAE1Q,QAAQ,CAAC+S,KAAK,CAAC,CAAC1E,SAAS,CAAC;MACtEE,IAAI,EAAGyE,IAAI,IAAI;QACb,IAAI,CAACrG,YAAY,CAAC0E,WAAW,CAAC,oBAAoB,CAAC;QACnD;MACF,CAAC;;MACDzC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QAC/D,IAAI,CAACjC,YAAY,CAACmC,SAAS,CAAC,wBAAwB,CAAC;MACvD;KACD,CAAC;EACJ;EAEAvN,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACf,oBAAoB,EAAE;IAEhC,MAAMkQ,WAAW,GAAG,IAAI,CAACC,cAAc,EAAE;IACzC,IAAI,CAACD,WAAW,EAAE;IAElB,IAAI,CAACjE,cAAc,CAACqG,YAAY,CAACpC,WAAW,EAAE1Q,QAAQ,CAACmG,KAAK,CAAC,CAACkI,SAAS,CAAC;MACtEE,IAAI,EAAGyE,IAAI,IAAI;QACb,IAAI,CAACrG,YAAY,CAAC0E,WAAW,CAAC,oBAAoB,CAAC;QACnD;MACF,CAAC;;MACDzC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;QACrE,IAAI,CAACjC,YAAY,CAACmC,SAAS,CAAC,8BAA8B,CAAC;MAC7D;KACD,CAAC;EACJ;EAEA;EACA;EACA;EAEA1D,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAAC5K,oBAAoB,IAAI,IAAI,CAACyM,QAAQ,EAAE;IAEjD,IAAI,CAACA,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACR,cAAc,CAACwG,WAAW,CAAC,IAAI,CAACzS,oBAAoB,CAACkO,EAAG,CAAC,CAACL,SAAS,EAAE;IAE1E;IACA,IAAI,IAAI,CAAC0B,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;IAGlC,IAAI,CAACA,aAAa,GAAGmD,UAAU,CAAC,MAAK;MACnC,IAAI,CAAC/C,UAAU,EAAE;IACnB,CAAC,EAAE,IAAI,CAAC;EACV;EAEAA,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAAClD,QAAQ,IAAI,CAAC,IAAI,CAACzM,oBAAoB,EAAE;IAElD,IAAI,CAACyM,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACR,cAAc,CAAC0D,UAAU,CAAC,IAAI,CAAC3P,oBAAoB,CAACkO,EAAG,CAAC,CAACL,SAAS,EAAE;IAEzE,IAAI,IAAI,CAAC0B,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;EAEpC;EAEA;EACA;EACA;EAEQY,cAAcA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACnQ,oBAAoB,IAAI,CAAC,IAAI,CAACwM,WAAW,EAAE,OAAO,IAAI;IAEhE,MAAMvM,YAAY,GAAG,IAAI,CAACD,oBAAoB,CAACC,YAAY,IAAI,EAAE;IACjE,MAAM0S,aAAa,GAAG,IAAI,CAACnG,WAAW,CAAC0B,EAAE,IAAI,IAAI,CAAC1B,WAAW,CAACkE,GAAG;IAEjE,MAAMkC,SAAS,GAAG3S,YAAY,CAAC4S,IAAI,CAChCC,CAAC,IAAK,CAACA,CAAC,CAAC5E,EAAE,IAAI4E,CAAC,CAACpC,GAAG,MAAMiC,aAAa,CACzC;IAED,OAAOC,SAAS,GAAGA,SAAS,CAAC1E,EAAE,IAAI0E,SAAS,CAAClC,GAAI,GAAG,IAAI;EAC1D;EAEQH,kBAAkBA,CAACwC,IAAU;IACnC,MAAMzN,IAAI,GAAGyN,IAAI,CAACzN,IAAI,CAAC0N,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACpC,QAAQ1N,IAAI;MACV,KAAK,OAAO;QACV,OAAO/F,WAAW,CAACiG,KAAK;MAC1B,KAAK,OAAO;QACV,OAAOjG,WAAW,CAACoG,KAAK;MAC1B,KAAK,OAAO;QACV,OAAOpG,WAAW,CAACgT,KAAK;MAC1B;QACE,OAAOhT,WAAW,CAACkG,IAAI;;EAE7B;EAEQoJ,aAAaA,CAACD,OAAgB;IACpC,IAAI,CAACnD,QAAQ,CAAC0D,IAAI,CAACP,OAAO,CAAC;IAC3B,IAAI,CAACtC,GAAG,CAACmC,aAAa,EAAE;EAC1B;EAEQmC,uBAAuBA,CAACqC,MAAc,EAAEC,WAAoB;IAClE,MAAMnM,KAAK,GAAG,IAAI,CAAC0E,QAAQ,CAAC0H,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAAClF,EAAE,KAAK+E,MAAM,CAAC;IAC7D,IAAIlM,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,IAAI,CAAC0E,QAAQ,CAAC1E,KAAK,CAAC,GAAGmM,WAAW;MAClC,IAAI,CAAC5G,GAAG,CAACmC,aAAa,EAAE;;EAE5B;EAEQqC,kBAAkBA,CAACuC,SAAiB;IAC1C,MAAMzE,OAAO,GAAG,IAAI,CAACnD,QAAQ,CAACoH,IAAI,CAAEO,CAAC,IAAKA,CAAC,CAAClF,EAAE,KAAKmF,SAAS,CAAC;IAC7D,IAAIzE,OAAO,EAAE;MACXA,OAAO,CAACzK,SAAS,GAAG,KAAK;MACzByK,OAAO,CAACxK,OAAO,GAAG,IAAI;MACtB,IAAI,CAACkI,GAAG,CAACmC,aAAa,EAAE;;EAE5B;EAEQyC,mBAAmBA,CAACD,cAAuB;IACjD,MAAMlK,KAAK,GAAG,IAAI,CAAC0E,QAAQ,CAAC0H,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAAClF,EAAE,KAAK+C,cAAc,CAAC/C,EAAE,CAAC;IACxE,IAAInH,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,IAAI,CAAC0E,QAAQ,CAAC1E,KAAK,CAAC,GAAGkK,cAAc;MACrC,IAAI,CAAC3E,GAAG,CAACmC,aAAa,EAAE;;EAE5B;EAEQ6C,qBAAqBA,CAAC+B,SAAiB;IAC7C,IAAI,CAAC5H,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACnM,MAAM,CAAE8T,CAAC,IAAKA,CAAC,CAAClF,EAAE,KAAKmF,SAAS,CAAC;IAC/D,IAAI,CAAC/G,GAAG,CAACmC,aAAa,EAAE;EAC1B;EAEQ2C,gBAAgBA,CAACxC,OAAgB;IACvC,IAAI,CAAC,IAAI,CAACpC,WAAW,IAAI,CAACoC,OAAO,CAAC1N,MAAM,EAAE,OAAO,KAAK;IAEtD,MAAMyR,aAAa,GAAG,IAAI,CAACnG,WAAW,CAAC0B,EAAE,IAAI,IAAI,CAAC1B,WAAW,CAACkE,GAAG;IACjE,MAAM4C,QAAQ,GAAG1E,OAAO,CAAC1N,MAAM,CAACgN,EAAE,IAAIU,OAAO,CAAC1N,MAAM,CAACwP,GAAG;IAExD,OAAOiC,aAAa,KAAKW,QAAQ;EACnC;EAEQpE,qBAAqBA,CAACD,KAAU;IACtC,IAAI,CAAC,IAAI,CAACzC,WAAW,EAAE;IAEvB,MAAMmG,aAAa,GAAG,IAAI,CAACnG,WAAW,CAAC0B,EAAE,IAAI,IAAI,CAAC1B,WAAW,CAACkE,GAAG;IAEjE,IAAIzB,KAAK,CAACsE,MAAM,KAAKZ,aAAa,EAAE,OAAO,CAAC;IAE5C,IAAI1D,KAAK,CAACxC,QAAQ,EAAE;MAClB;MACA,MAAM+G,IAAI,GAAG,IAAI,CAACxT,oBAAoB,EAAEC,YAAY,EAAE4S,IAAI,CACvDC,CAAC,IAAK,CAACA,CAAC,CAAC5E,EAAE,IAAI4E,CAAC,CAACpC,GAAG,MAAMzB,KAAK,CAACsE,MAAM,CACxC;MACD,IACEC,IAAI,IACJ,CAAC,IAAI,CAAC7H,WAAW,CAACkH,IAAI,CAAEY,CAAC,IAAK,CAACA,CAAC,CAACvF,EAAE,IAAIuF,CAAC,CAAC/C,GAAG,MAAMzB,KAAK,CAACsE,MAAM,CAAC,EAC/D;QACA,IAAI,CAAC5H,WAAW,CAACwD,IAAI,CAACqE,IAAI,CAAC;;KAE9B,MAAM;MACL;MACA,IAAI,CAAC7H,WAAW,GAAG,IAAI,CAACA,WAAW,CAACrM,MAAM,CACvCmU,CAAC,IAAK,CAACA,CAAC,CAACvF,EAAE,IAAIuF,CAAC,CAAC/C,GAAG,MAAMzB,KAAK,CAACsE,MAAM,CACxC;;IAGH,IAAI,CAACjH,GAAG,CAACmC,aAAa,EAAE;EAC1B;EAEQD,kBAAkBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAAC/C,QAAQ,CAACvL,MAAM,IAAI,CAAC,IAAI,CAACsM,WAAW,EAAE;IAEhD,MAAMkH,cAAc,GAAG,IAAI,CAACjI,QAAQ,CAACnM,MAAM,CACxC8T,CAAC,IACA,CAACA,CAAC,CAAClP,MAAM,IACTkP,CAAC,CAAClS,MAAM,IACR,CAACkS,CAAC,CAAClS,MAAM,CAACgN,EAAE,IAAIkF,CAAC,CAAClS,MAAM,CAACwP,GAAG,OACzB,IAAI,CAAClE,WAAY,CAAC0B,EAAE,IAAI,IAAI,CAAC1B,WAAY,CAACkE,GAAG,CAAC,CACpD;IAEDgD,cAAc,CAACtE,OAAO,CAAER,OAAO,IAAI;MACjC,IAAIA,OAAO,CAACV,EAAE,EAAE;QACd,IAAI,CAACY,iBAAiB,CAACF,OAAO,CAAC;;IAEnC,CAAC,CAAC;EACJ;EAEQE,iBAAiBA,CAACF,OAAgB;IACxC,IAAI,CAACA,OAAO,CAACV,EAAE,IAAIU,OAAO,CAAC1K,MAAM,EAAE;IAEnC,IAAI,CAAC+H,cAAc,CAAC6C,iBAAiB,CAACF,OAAO,CAACV,EAAE,CAAC,CAACL,SAAS,CAAC;MAC1DE,IAAI,EAAGkD,cAAc,IAAI;QACvB,IAAI,CAACC,mBAAmB,CAACD,cAAc,CAAC;MAC1C,CAAC;MACD7C,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MAC3D;KACD,CAAC;EACJ;EAEQb,cAAcA,CAAA;IACpB,IAAI,CAAChB,MAAM,CAACoH,iBAAiB,CAAC,MAAK;MACjCjB,UAAU,CAAC,MAAK;QACd,IAAI,IAAI,CAACkB,iBAAiB,EAAE;UAC1B,MAAMC,OAAO,GAAG,IAAI,CAACD,iBAAiB,CAAChC,aAAa;UACpDiC,OAAO,CAACC,SAAS,GAAGD,OAAO,CAACE,YAAY;;MAE5C,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACJ;EAEA;EACA;EACA;EAEAnO,iBAAiBA,CAACC,SAAwB;IACxC,MAAMmO,IAAI,GAAG,IAAI3D,IAAI,CAACxK,SAAS,CAAC;IAChC,MAAMyK,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAM4D,WAAW,GAAG,CAAC3D,GAAG,CAAC4D,OAAO,EAAE,GAAGF,IAAI,CAACE,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAEvE,IAAID,WAAW,GAAG,EAAE,EAAE;MACpB,OAAOD,IAAI,CAACG,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;OACT,CAAC;KACH,MAAM;MACL,OAAOL,IAAI,CAACM,kBAAkB,CAAC,OAAO,EAAE;QACtCC,GAAG,EAAE,SAAS;QACdC,KAAK,EAAE;OACR,CAAC;;EAEN;EAEApP,WAAWA,CAACwJ,OAAgB;IAC1B,IAAI,CAAC,IAAI,CAACpC,WAAW,IAAI,CAACoC,OAAO,CAAC1N,MAAM,EAAE,OAAO,KAAK;IAEtD,MAAMyR,aAAa,GAAG,IAAI,CAACnG,WAAW,CAAC0B,EAAE,IAAI,IAAI,CAAC1B,WAAW,CAACkE,GAAG;IACjE,MAAM4C,QAAQ,GAAG1E,OAAO,CAAC1N,MAAM,CAACgN,EAAE,IAAIU,OAAO,CAAC1N,MAAM,CAACwP,GAAG;IAExD,OAAOiC,aAAa,KAAKW,QAAQ;EACnC;EAEAvN,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC4F,WAAW,CAACzL,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAE5C,IAAI,IAAI,CAACyL,WAAW,CAACzL,MAAM,KAAK,CAAC,EAAE;MACjC,OAAO,GAAG,IAAI,CAACyL,WAAW,CAAC,CAAC,CAAC,CAACtK,QAAQ,2BAA2B;KAClE,MAAM;MACL,OAAO,GAAG,IAAI,CAACsK,WAAW,CAACzL,MAAM,sCAAsC;;EAE3E;EAEAuK,UAAUA,CAACwE,KAAoB;IAC7B,IAAIA,KAAK,CAACwF,GAAG,KAAK,OAAO,IAAI,CAACxF,KAAK,CAACyF,QAAQ,EAAE;MAC5CzF,KAAK,CAAC0F,cAAc,EAAE;MACtB,IAAI,CAAC/L,WAAW,EAAE;KACnB,MAAM;MACL,IAAI,CAACgC,QAAQ,EAAE;;EAEnB;EAEAV,iBAAiBA,CAAA;IACf,IAAI,CAAC2C,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEA/C,oBAAoBA,CAAA;IAClB,IAAI,CAAC+B,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;EACpD;EAEA+I,mBAAmBA,CAAChG,OAAgB;IAClC,IAAI,CAACjI,cAAc,GAAGiI,OAAO;IAC7B,IAAI,CAACtE,cAAc,GAAGsE,OAAO,CAACrN,OAAO,IAAI,EAAE;IAC3C,IAAI,CAACsT,YAAY,CAACjD,aAAa,CAACkD,KAAK,EAAE;EACzC;EAEArO,aAAaA,CAAA;IACX,IAAI,CAACE,cAAc,GAAG,IAAI;IAC1B,IAAI,CAAC2D,cAAc,GAAG,EAAE;EAC1B;EAEAyK,UAAUA,CAACnG,OAAgB;IACzB,IAAI,CAACvI,UAAU,GAAGuI,OAAO;IACzB,IAAI,CAACiG,YAAY,CAACjD,aAAa,CAACkD,KAAK,EAAE;EACzC;EAEA3O,WAAWA,CAAA;IACT,IAAI,CAACE,UAAU,GAAG,IAAI;EACxB;EAEA;EACA;EACA;EAEAkF,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACvL,oBAAoB,IAAI,CAAC,IAAI,CAACwM,WAAW,EAAE,OAAO,EAAE;IAE9D,MAAMvM,YAAY,GAAG,IAAI,CAACD,oBAAoB,CAACC,YAAY,IAAI,EAAE;IACjE,MAAM0S,aAAa,GAAG,IAAI,CAACnG,WAAW,CAAC0B,EAAE,IAAI,IAAI,CAAC1B,WAAW,CAACkE,GAAG;IAEjE,MAAMkC,SAAS,GAAG3S,YAAY,CAAC4S,IAAI,CAChCC,CAAC,IAAK,CAACA,CAAC,CAAC5E,EAAE,IAAI4E,CAAC,CAACpC,GAAG,MAAMiC,aAAa,CACzC;IAED,OAAOC,SAAS,EAAEvR,QAAQ,IAAI,qBAAqB;EACrD;EAEAgK,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACrL,oBAAoB,IAAI,CAAC,IAAI,CAACwM,WAAW,EACjD,OAAO,mCAAmC;IAE5C,MAAMvM,YAAY,GAAG,IAAI,CAACD,oBAAoB,CAACC,YAAY,IAAI,EAAE;IACjE,MAAM0S,aAAa,GAAG,IAAI,CAACnG,WAAW,CAAC0B,EAAE,IAAI,IAAI,CAAC1B,WAAW,CAACkE,GAAG;IAEjE,MAAMkC,SAAS,GAAG3S,YAAY,CAAC4S,IAAI,CAChCC,CAAC,IAAK,CAACA,CAAC,CAAC5E,EAAE,IAAI4E,CAAC,CAACpC,GAAG,MAAMiC,aAAa,CACzC;IAED,OAAOC,SAAS,EAAEzR,KAAK,IAAI,mCAAmC;EAChE;EAEAgK,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACnL,oBAAoB,IAAI,CAAC,IAAI,CAACwM,WAAW,EAAE,OAAO,KAAK;IAEjE,MAAMvM,YAAY,GAAG,IAAI,CAACD,oBAAoB,CAACC,YAAY,IAAI,EAAE;IACjE,MAAM0S,aAAa,GAAG,IAAI,CAACnG,WAAW,CAAC0B,EAAE,IAAI,IAAI,CAAC1B,WAAW,CAACkE,GAAG;IAEjE,MAAMkC,SAAS,GAAG3S,YAAY,CAAC4S,IAAI,CAChCC,CAAC,IAAK,CAACA,CAAC,CAAC5E,EAAE,IAAI4E,CAAC,CAACpC,GAAG,MAAMiC,aAAa,CACzC;IAED,OAAOC,SAAS,EAAEoC,QAAQ,IAAI,KAAK;EACrC;EAEAtJ,gBAAgBA,CAAC3E,KAAa,EAAE6H,OAAgB;IAC9C,OAAOA,OAAO,CAACV,EAAE,IAAIU,OAAO,CAAC8B,GAAG,IAAI3J,KAAK,CAACkO,QAAQ,EAAE;EACtD;EAEArT,eAAeA,CAACsT,UAAkC;IAChD,IAAI,CAACA,UAAU,EAAElT,GAAG,EAAE;IAEtB;IACAmT,MAAM,CAACC,IAAI,CAACF,UAAU,CAAClT,GAAG,EAAE,QAAQ,CAAC;EACvC;EAEAQ,cAAcA,CAACC,IAAwB;IACrC,IAAI,CAACA,IAAI,EAAE,OAAO,KAAK;IAEvB,MAAM4S,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACrC,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIC,QAAQ,GAAG9S,IAAI;IAEnB,OAAO8S,QAAQ,IAAI,IAAI,IAAID,SAAS,GAAGD,KAAK,CAACnV,MAAM,GAAG,CAAC,EAAE;MACvDqV,QAAQ,IAAI,IAAI;MAChBD,SAAS,EAAE;;IAGb,OAAO,GAAGC,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,IAAIH,KAAK,CAACC,SAAS,CAAC,EAAE;EACrD;EAEAjT,YAAYA,CAAC6S,UAAkC;IAC7C,IAAI,CAACA,UAAU,EAAElT,GAAG,EAAE;IAEtB,MAAMyT,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGV,UAAU,CAAClT,GAAG;IAC1ByT,IAAI,CAACI,QAAQ,GAAGX,UAAU,CAACjT,IAAI,IAAI,MAAM;IACzCyT,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;IAC/BA,IAAI,CAAC5D,KAAK,EAAE;IACZ6D,QAAQ,CAACI,IAAI,CAACE,WAAW,CAACP,IAAI,CAAC;EACjC;EAEA5S,gBAAgBA,CAAC+L,OAAgB;IAC/B,IAAI,CAACA,OAAO,CAAC/M,WAAW,GAAG,CAAC,CAAC,EAAEG,GAAG,EAAE;IAEpC,IAAI,CAACiK,cAAc,CAACgK,SAAS,CAACrH,OAAO,CAAC/M,WAAW,CAAC,CAAC,CAAC,CAACG,GAAG,CAAC,CAACkU,KAAK,CAAE9H,KAAK,IAAI;MACxEC,OAAO,CAACD,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;MACnE,IAAI,CAACjC,YAAY,CAACmC,SAAS,CAAC,4CAA4C,CAAC;IAC3E,CAAC,CAAC;EACJ;EAEAvL,cAAcA,CAACC,QAA4B;IACzC,IAAI,CAACA,QAAQ,EAAE,OAAO,MAAM;IAE5B,MAAMmT,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACrT,QAAQ,GAAG,EAAE,CAAC;IACzC,MAAMsT,OAAO,GAAGtT,QAAQ,GAAG,EAAE;IAE7B,OAAO,GAAGmT,OAAO,IAAIG,OAAO,CAACrB,QAAQ,EAAE,CAACsB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAC5D;EAEArR,eAAeA,CAAC0J,OAAgB;IAC9B;IACA;IACA,MAAM4H,OAAO,GAAG,EAAE;IAElB,IAAI,IAAI,CAACpF,gBAAgB,CAACxC,OAAO,CAAC,EAAE;MAClC4H,OAAO,CAACrH,IAAI,CAAC,WAAW,CAAC;;IAG3B,IAAI,IAAI,CAAC/J,WAAW,CAACwJ,OAAO,CAAC,EAAE;MAC7B4H,OAAO,CAACrH,IAAI,CAAC,UAAU,CAAC;;IAG1BqH,OAAO,CAACrH,IAAI,CAAC,UAAU,EAAE,YAAY,EAAE,QAAQ,CAAC;IAEhD;IACAd,OAAO,CAACoI,GAAG,CAAC,sCAAsC,EAAED,OAAO,CAAC;EAC9D;;;uBAhxBWzK,oBAAoB,EAAAtM,EAAA,CAAAiX,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAnX,EAAA,CAAAiX,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAArX,EAAA,CAAAiX,iBAAA,CAAAK,EAAA,CAAAC,YAAA,GAAAvX,EAAA,CAAAiX,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAzX,EAAA,CAAAiX,iBAAA,CAAAO,EAAA,CAAAE,MAAA,GAAA1X,EAAA,CAAAiX,iBAAA,CAAAjX,EAAA,CAAA2X,iBAAA,GAAA3X,EAAA,CAAAiX,iBAAA,CAAAjX,EAAA,CAAA4X,MAAA;IAAA;EAAA;;;YAApBtL,oBAAoB;MAAAuL,SAAA;MAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;UClCjChY,EAAA,CAAAqC,UAAA,IAAA6V,mCAAA,mBAgaM;UAKNlY,EAAA,CAAAqC,UAAA,IAAA8V,mCAAA,kBAmEM;;;UAxeuBnY,EAAA,CAAAuB,UAAA,SAAA0W,GAAA,CAAA1X,oBAAA,CAA0B;UAsapDP,EAAA,CAAAI,SAAA,GAA2B;UAA3BJ,EAAA,CAAAuB,UAAA,UAAA0W,GAAA,CAAA1X,oBAAA,CAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}