{"ast": null, "code": "import { trigger, transition, style, animate } from '@angular/animations';\nimport { map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"src/app/services/authadmin.service\";\nimport * as i3 from \"src/app/services/authuser.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@app/services/theme.service\";\nimport * as i6 from \"src/app/services/data.service\";\nfunction AdminLayoutComponent_div_83_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"div\", 87);\n    i0.ɵɵlistener(\"click\", function AdminLayoutComponent_div_83_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.toggleMobileMenu());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 88)(3, \"div\", 89)(4, \"div\", 90);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(5, \"svg\", 91);\n    i0.ɵɵelement(6, \"path\", 10)(7, \"path\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(8, \"span\", 92);\n    i0.ɵɵtext(9, \"DevBridge\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"button\", 93);\n    i0.ɵɵlistener(\"click\", function AdminLayoutComponent_div_83_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.toggleMobileMenu());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(11, \"svg\", 94);\n    i0.ɵɵelement(12, \"path\", 95);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(13, \"div\", 14)(14, \"nav\", 15)(15, \"a\", 96);\n    i0.ɵɵlistener(\"click\", function AdminLayoutComponent_div_83_Template_a_click_15_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.toggleMobileMenu());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(16, \"svg\", 97);\n    i0.ɵɵelement(17, \"path\", 98);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(18, \" Dashboard \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(19, \"a\", 99);\n    i0.ɵɵlistener(\"click\", function AdminLayoutComponent_div_83_Template_a_click_19_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.toggleMobileMenu());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(20, \"svg\", 97);\n    i0.ɵɵelement(21, \"path\", 100);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(22, \" profile \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(23, \"a\", 101);\n    i0.ɵɵlistener(\"click\", function AdminLayoutComponent_div_83_Template_a_click_23_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.toggleMobileMenu());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(24, \"svg\", 97);\n    i0.ɵɵelement(25, \"path\", 102);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(26, \" Reunions \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(27, \"a\", 103);\n    i0.ɵɵlistener(\"click\", function AdminLayoutComponent_div_83_Template_a_click_27_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.toggleMobileMenu());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(28, \"svg\", 97);\n    i0.ɵɵelement(29, \"path\", 104);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(30, \" Planning \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(31, \"a\", 105);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(32, \"svg\", 97);\n    i0.ɵɵelement(33, \"path\", 106);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(34, \" Projects \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(35, \"a\", 107);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(36, \"svg\", 97);\n    i0.ɵɵelement(37, \"path\", 108);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(38, \" Student Rendus \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(39, \"a\", 109);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(40, \"svg\", 97);\n    i0.ɵɵelement(41, \"path\", 110);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(42, \" \\u00C9valuations \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(43, \"a\", 111);\n    i0.ɵɵlistener(\"click\", function AdminLayoutComponent_div_83_Template_a_click_43_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.toggleMobileMenu());\n    });\n    i0.ɵɵelement(44, \"i\", 112);\n    i0.ɵɵtext(45, \" Equipes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"a\", 113);\n    i0.ɵɵlistener(\"click\", function AdminLayoutComponent_div_83_Template_a_click_46_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.toggleMobileMenu());\n    });\n    i0.ɵɵelement(47, \"i\", 114);\n    i0.ɵɵtext(48, \" Back Home \");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction AdminLayoutComponent_i_119_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 115);\n  }\n}\nfunction AdminLayoutComponent_i_121_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 116);\n  }\n}\nfunction AdminLayoutComponent_div_133_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 117)(1, \"a\", 118);\n    i0.ɵɵlistener(\"click\", function AdminLayoutComponent_div_133_Template_a_click_1_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.openLogoutModal());\n    });\n    i0.ɵɵelementStart(2, \"div\", 90)(3, \"div\", 8);\n    i0.ɵɵelement(4, \"i\", 119)(5, \"div\", 120);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7, \"Logout\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"@fadeIn\", undefined);\n  }\n}\nfunction AdminLayoutComponent_div_153_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 121)(1, \"div\", 122)(2, \"div\", 123);\n    i0.ɵɵelement(3, \"div\", 124);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 125)(5, \"div\", 41);\n    i0.ɵɵelement(6, \"div\", 126)(7, \"div\", 127);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 128)(9, \"div\", 129)(10, \"div\", 130);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(11, \"svg\", 131);\n    i0.ɵɵelement(12, \"path\", 132);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelement(13, \"div\", 133);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 134)(15, \"h3\", 135);\n    i0.ɵɵtext(16, \" Ready to Leave? \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 136)(18, \"p\", 137);\n    i0.ɵɵtext(19, \" Are you sure you want to logout? \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(20, \"div\", 138)(21, \"button\", 139);\n    i0.ɵɵlistener(\"click\", function AdminLayoutComponent_div_153_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.logout());\n    });\n    i0.ɵɵelement(22, \"div\", 140)(23, \"div\", 141);\n    i0.ɵɵelementStart(24, \"span\", 142);\n    i0.ɵɵelement(25, \"i\", 143);\n    i0.ɵɵtext(26, \" Logout \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"button\", 144);\n    i0.ɵɵlistener(\"click\", function AdminLayoutComponent_div_153_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.closeLogoutModal());\n    });\n    i0.ɵɵelementStart(28, \"span\", 145);\n    i0.ɵɵelement(29, \"i\", 146);\n    i0.ɵɵtext(30, \" Cancel \");\n    i0.ɵɵelementEnd()()()()()();\n  }\n}\nfunction AdminLayoutComponent_button_154_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 147);\n    i0.ɵɵlistener(\"click\", function AdminLayoutComponent_button_154_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.scrollToTop());\n    });\n    i0.ɵɵelement(1, \"div\", 148)(2, \"div\", 149);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 150);\n    i0.ɵɵelement(4, \"path\", 151);\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c0 = function () {\n  return {\n    exact: true\n  };\n};\nconst _c1 = function (a0) {\n  return {\n    \"rotate-180\": a0\n  };\n};\nexport class AdminLayoutComponent {\n  constructor(location, authAdminService, authService, router, themeService, dataService) {\n    this.location = location;\n    this.authAdminService = authAdminService;\n    this.authService = authService;\n    this.router = router;\n    this.themeService = themeService;\n    this.dataService = dataService;\n    this.username = '';\n    this.imageProfile = '';\n    this.mobileMenuOpen = false;\n    this.userMenuOpen = false;\n    this.showLogoutModal = false;\n    this.showScrollButton = false;\n    this.currentYear = new Date().getFullYear();\n    this.subscriptions = [];\n    this.loadUserProfile();\n    this.isDarkMode$ = this.themeService.currentTheme$.pipe(map(theme => theme.name === 'dark'));\n  }\n  loadUserProfile() {\n    const user = this.authAdminService.getUser();\n    this.username = user?.fullName || user?.username || '';\n    // Toujours utiliser l'image par défaut si l'image de profil est null, 'null' ou vide\n    if (user?.profileImage && user.profileImage !== 'null' && user.profileImage.trim() !== '') {\n      this.imageProfile = user.profileImage;\n    } else if (user?.image && user.image !== 'null' && user.image.trim() !== '') {\n      this.imageProfile = user.image;\n    } else {\n      this.imageProfile = 'assets/images/default-profile.png';\n    }\n    console.log('Admin layout - Image profile loaded:', this.imageProfile);\n  }\n  ngOnInit() {\n    this.checkScrollPosition();\n    // S'abonner aux changements d'image de profil\n    const profileSub = this.dataService.currentUser$.subscribe(user => {\n      if (user) {\n        this.username = user.fullName || user.username || '';\n        // Toujours utiliser l'image par défaut si l'image de profil est null, 'null' ou vide\n        if (user.profileImage && user.profileImage !== 'null' && user.profileImage.trim() !== '') {\n          this.imageProfile = user.profileImage;\n        } else if (user.image && user.image !== 'null' && user.image.trim() !== '') {\n          this.imageProfile = user.image;\n        } else {\n          this.imageProfile = 'assets/images/default-profile.png';\n        }\n        console.log('Admin layout - Image profile updated:', this.imageProfile);\n      }\n    });\n    this.subscriptions.push(profileSub);\n  }\n  ngOnDestroy() {\n    // Désabonner de tous les observables pour éviter les fuites de mémoire\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  checkScrollPosition() {\n    this.showScrollButton = window.pageYOffset > 300;\n  }\n  scrollToTop() {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  }\n  toggleMobileMenu() {\n    this.mobileMenuOpen = !this.mobileMenuOpen;\n  }\n  toggleUserMenu() {\n    this.userMenuOpen = !this.userMenuOpen;\n  }\n  openLogoutModal() {\n    this.showLogoutModal = true;\n    this.userMenuOpen = false;\n  }\n  closeLogoutModal() {\n    this.showLogoutModal = false;\n  }\n  logout() {\n    this.authService.logout().subscribe({\n      next: () => {\n        this.userMenuOpen = false;\n        this.showLogoutModal = false;\n        this.authService.clearAuthData();\n        this.authAdminService.clearAuthData();\n        setTimeout(() => {\n          this.router.navigate(['/admin/login'], {\n            queryParams: {\n              message: 'Déconnexion réussie'\n            },\n            replaceUrl: true\n          });\n        }, 100);\n      },\n      error: err => {\n        console.error('Logout error:', err);\n        this.authService.clearAuthData();\n        this.authAdminService.clearAuthData();\n        setTimeout(() => {\n          this.router.navigate(['/admin/login'], {\n            queryParams: {\n              message: 'Déconnexion effectuée'\n            },\n            replaceUrl: true\n          });\n        }, 100);\n      }\n    });\n  }\n  goBack() {\n    this.location.back();\n  }\n  toggleDarkMode() {\n    this.themeService.toggleTheme();\n  }\n  static {\n    this.ɵfac = function AdminLayoutComponent_Factory(t) {\n      return new (t || AdminLayoutComponent)(i0.ɵɵdirectiveInject(i1.Location), i0.ɵɵdirectiveInject(i2.AuthadminService), i0.ɵɵdirectiveInject(i3.AuthuserService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.ThemeService), i0.ɵɵdirectiveInject(i6.DataService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AdminLayoutComponent,\n      selectors: [[\"app-admin-layout\"]],\n      hostBindings: function AdminLayoutComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"scroll\", function AdminLayoutComponent_scroll_HostBindingHandler() {\n            return ctx.checkScrollPosition();\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      decls: 155,\n      vars: 24,\n      consts: [[1, \"flex\", \"h-screen\", \"main-grid-container\", \"futuristic-layout\"], [1, \"background-grid\"], [1, \"hidden\", \"md:flex\", \"md:flex-shrink-0\"], [1, \"flex\", \"flex-col\", \"w-64\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"border-r\", \"border-[#edf1f4]\", \"dark:border-[#2a2a2a]\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-center\", \"justify-center\", \"h-16\", \"px-4\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"-top-6\", \"-left-6\", \"w-12\", \"h-12\", \"bg-gradient-to-br\", \"from-[#4f5fad]/20\", \"to-transparent\", \"rounded-full\"], [1, \"absolute\", \"-bottom-6\", \"-right-6\", \"w-12\", \"h-12\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/20\", \"to-transparent\", \"rounded-full\"], [1, \"flex\", \"items-center\", \"relative\", \"z-10\"], [1, \"relative\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-8\", \"w-8\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"transform\", \"rotate-12\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"ml-2\", \"text-xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"flex\", \"flex-col\", \"flex-grow\", \"px-4\", \"py-4\"], [1, \"flex-1\", \"space-y-2\"], [\"routerLink\", \"/admin/dashboard\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\"], [1, \"absolute\", \"inset-0\", \"w-1\", \"bg-gradient-to-b\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\"], [1, \"relative\", \"z-10\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-th-large\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-all\", \"group-hover:scale-110\"], [\"routerLink\", \"/admin/profile\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\"], [1, \"fas\", \"fa-user-shield\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-all\", \"group-hover:scale-110\"], [\"routerLink\", \"/admin/reunions\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\"], [1, \"fas\", \"fa-users-cog\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-all\", \"group-hover:scale-110\"], [\"routerLink\", \"/admin/plannings\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\"], [1, \"far\", \"fa-calendar-check\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-all\", \"group-hover:scale-110\"], [\"routerLink\", \"/admin/projects\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\"], [1, \"fas\", \"fa-rocket\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-all\", \"group-hover:scale-110\"], [\"routerLink\", \"/admin/projects/rendus\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\"], [1, \"fas\", \"fa-file-upload\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-all\", \"group-hover:scale-110\"], [\"routerLink\", \"/admin/projects/evaluations\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\"], [1, \"fas\", \"fa-clipboard-check\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-all\", \"group-hover:scale-110\"], [\"routerLink\", \"/admin/equipes\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\"], [1, \"fas\", \"fa-users\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-all\", \"group-hover:scale-110\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [\"routerLink\", \"/\", \"routerLinkActive\", \"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\", 1, \"sidebar-nav-link\", \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-l-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-all\", 3, \"routerLinkActiveOptions\"], [1, \"fas\", \"fa-home\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"group-hover:text-[#3d4a85]\", \"dark:group-hover:text-[#4f5fad]\", \"transition-colors\"], [\"class\", \"md:hidden fixed inset-0 z-40\", 4, \"ngIf\"], [1, \"flex-1\", \"flex\", \"flex-col\", \"overflow-hidden\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]\", \"z-10\", \"border-b\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-center\", \"justify-between\", \"h-16\", \"px-4\", \"relative\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-0\", \"left-1/4\", \"w-px\", \"h-full\", \"bg-gradient-to-b\", \"from-transparent\", \"via-[#4f5fad]/10\", \"dark:via-[#6d78c9]/5\", \"to-transparent\"], [1, \"absolute\", \"top-0\", \"right-1/3\", \"w-px\", \"h-full\", \"bg-gradient-to-b\", \"from-transparent\", \"via-[#4f5fad]/5\", \"dark:via-[#6d78c9]/3\", \"to-transparent\"], [1, \"md:hidden\", \"flex\", \"items-center\", \"justify-center\", \"h-8\", \"w-8\", \"rounded-md\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"focus:outline-none\", \"transition-colors\", \"relative\", \"group\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"rounded-md\", \"blur-md\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"relative\", \"z-10\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 6h16M4 12h16M4 18h16\"], [1, \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"ml-2\", \"relative\", \"z-10\"], [1, \"hidden\", \"md:flex\", \"items-center\", \"px-3\", \"py-1\", \"rounded-md\", \"text-sm\", \"font-medium\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"transition-all\", \"duration-200\", \"relative\", \"group\", \"overflow-hidden\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"mr-1\", \"group-hover:scale-110\", \"transition-transform\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10 19l-7-7m0 0l7-7m-7 7h18\"], [1, \"flex-1\", \"max-w-md\", \"ml-4\", \"md:ml-6\"], [1, \"relative\", \"group\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"group-focus-within:text-[#4f5fad]\", \"dark:group-focus-within:text-[#6d78c9]\", \"transition-colors\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"], [\"type\", \"text\", \"placeholder\", \"Search...\", 1, \"block\", \"w-full\", \"pl-10\", \"pr-3\", \"py-2\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"rounded-md\", \"leading-5\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"placeholder-[#bdc6cc]\", \"dark:placeholder-[#6d6870]\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-[#4f5fad]\", \"dark:focus:ring-[#6d78c9]\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"transition-all\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\", \"opacity-0\", \"group-focus-within:opacity-100\", \"transition-opacity\"], [1, \"w-0.5\", \"h-4\", \"bg-gradient-to-b\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"rounded-full\"], [1, \"ml-4\", \"flex\", \"items-center\", \"md:ml-6\"], [\"aria-label\", \"Toggle dark mode\", 1, \"flex\", \"items-center\", \"justify-center\", \"h-8\", \"w-8\", \"rounded-full\", \"bg-[#edf1f4]\", \"dark:bg-[#2a2a2a]\", \"hover:bg-[#dce4ec]\", \"dark:hover:bg-[#3a3a3a]\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"mr-3\", \"transition-all\", \"duration-300\", \"relative\", \"overflow-hidden\", \"group\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"rounded-full\", \"overflow-hidden\"], [1, \"absolute\", \"inset-0\", \"rounded-full\", \"border\", \"border-[#4f5fad]/20\", \"dark:border-[#6d78c9]/20\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\"], [1, \"absolute\", \"-inset-1\", \"bg-gradient-to-r\", \"from-[#4f5fad]/0\", \"via-[#4f5fad]/30\", \"to-[#4f5fad]/0\", \"dark:from-[#6d78c9]/0\", \"dark:via-[#6d78c9]/30\", \"dark:to-[#6d78c9]/0\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-sm\", \"animate-shine\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/10\", \"dark:bg-[#6d78c9]/10\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"blur-md\"], [1, \"relative\", \"z-10\", \"transition-all\", \"duration-500\", \"ease-in-out\", 3, \"ngClass\"], [\"class\", \"far fa-moon group-hover:scale-110 transition-transform\", 4, \"ngIf\"], [\"class\", \"far fa-sun group-hover:scale-110 transition-transform\", 4, \"ngIf\"], [1, \"flex\", \"items-center\", \"text-sm\", \"rounded-full\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-[#4f5fad]\", \"dark:focus:ring-[#6d78c9]\", \"transition-all\", \"group\", 3, \"click\"], [1, \"sr-only\"], [1, \"hidden\", \"md:inline-block\", \"mr-2\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"group-hover:text-[#4f5fad]\", \"dark:group-hover:text-[#6d78c9]\", \"transition-colors\"], [1, \"h-8\", \"w-8\", \"rounded-full\", \"overflow-hidden\", \"flex\", \"items-center\", \"justify-center\", \"border-2\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\", \"group-hover:border-[#3d4a85]\", \"dark:group-hover:border-[#4f5fad]\", \"transition-colors\", \"relative\"], [\"alt\", \"Profile\", 1, \"h-full\", \"w-full\", \"object-cover\", 3, \"src\"], [\"class\", \"origin-top-right absolute right-0 mt-2 w-48 rounded-lg shadow-lg dark:shadow-[0_10px_25px_rgba(0,0,0,0.3)] bg-white dark:bg-[#1e1e1e] border border-[#edf1f4]/50 dark:border-[#2a2a2a] py-1 z-50 backdrop-blur-sm\", 4, \"ngIf\"], [1, \"flex-1\", \"overflow-y-auto\", \"bg-[#edf1f4]\", \"dark:bg-[#121212]\", \"p-4\", \"md:p-6\", \"relative\"], [1, \"absolute\", \"top-[15%]\", \"left-[10%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[20%]\", \"right-[10%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"relative\", \"z-10\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"border-t\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"py-4\", \"relative\", \"overflow-hidden\"], [1, \"container\", \"mx-auto\", \"px-4\", \"text-center\", \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"relative\", \"z-10\"], [1, \"flex\", \"items-center\", \"justify-center\"], [1, \"relative\", \"mr-2\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-4\", \"w-4\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\"], [\"class\", \"fixed inset-0 overflow-y-auto z-50\", 4, \"ngIf\"], [\"class\", \"fixed bottom-6 right-6 p-3 rounded-full shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#4f5fad] dark:focus:ring-[#6d78c9] overflow-hidden group\", 3, \"click\", 4, \"ngIf\"], [1, \"md:hidden\", \"fixed\", \"inset-0\", \"z-40\"], [1, \"fixed\", \"inset-0\", \"bg-gray-600\", \"bg-opacity-75\", 3, \"click\"], [1, \"relative\", \"flex\", \"flex-col\", \"w-72\", \"bg-white\", \"h-full\"], [1, \"flex\", \"items-center\", \"justify-between\", \"h-16\", \"px-4\"], [1, \"flex\", \"items-center\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-8\", \"w-8\", \"text-[#4f5fad]\"], [1, \"ml-2\", \"text-xl\", \"font-bold\", \"text-[#4f5fad]\"], [1, \"text-[#6d6870]\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-6\", \"w-6\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M6 18L18 6M6 6l12 12\"], [\"routerLink\", \"/admin/dashboard\", 1, \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-md\", \"text-[#6d6870]\", \"hover:bg-[#edf1f4]\", \"hover:text-[#4f5fad]\", \"transition-colors\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"mr-3\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z\"], [\"routerLink\", \"/admin/profile\", 1, \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-md\", \"text-[#6d6870]\", \"hover:bg-[#edf1f4]\", \"hover:text-[#4f5fad]\", \"transition-colors\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z\"], [\"routerLink\", \"/admin/reunions\", 1, \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-md\", \"text-[#6d6870]\", \"hover:bg-[#edf1f4]\", \"hover:text-[#4f5fad]\", \"transition-colors\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [\"routerLink\", \"/admin/plannings\", 1, \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-md\", \"text-[#6d6870]\", \"hover:bg-[#edf1f4]\", \"hover:text-[#4f5fad]\", \"transition-colors\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"], [\"routerLink\", \"/admin/projects\", \"routerLinkActive\", \"bg-[#edf1f4] text-[#4f5fad] font-medium\", 1, \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-md\", \"text-[#6d6870]\", \"hover:bg-[#edf1f4]\", \"hover:text-[#4f5fad]\", \"transition-colors\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z\"], [\"routerLink\", \"/admin/projects/rendus\", \"routerLinkActive\", \"bg-[#edf1f4] text-[#4f5fad] font-medium\", 1, \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-md\", \"text-[#6d6870]\", \"hover:bg-[#edf1f4]\", \"hover:text-[#4f5fad]\", \"transition-colors\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\"], [\"routerLink\", \"/admin/projects/evaluations\", \"routerLinkActive\", \"bg-[#edf1f4] text-[#4f5fad] font-medium\", 1, \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-md\", \"text-[#6d6870]\", \"hover:bg-[#edf1f4]\", \"hover:text-[#4f5fad]\", \"transition-colors\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"], [\"routerLink\", \"/admin/equipes\", 1, \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-md\", \"text-[#6d6870]\", \"hover:bg-[#edf1f4]\", \"hover:text-[#4f5fad]\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-users\", \"h-5\", \"w-5\", \"mr-3\"], [\"routerLink\", \"/\", 1, \"group\", \"flex\", \"items-center\", \"px-4\", \"py-3\", \"text-sm\", \"font-medium\", \"rounded-md\", \"text-[#6d6870]\", \"hover:bg-[#edf1f4]\", \"hover:text-[#4f5fad]\", \"transition-colors\", 3, \"click\"], [1, \"fas\", \"fa-home\", \"h-5\", \"w-5\", \"mr-3\", \"text-[#4f5fad]\"], [1, \"far\", \"fa-moon\", \"group-hover:scale-110\", \"transition-transform\"], [1, \"far\", \"fa-sun\", \"group-hover:scale-110\", \"transition-transform\"], [1, \"origin-top-right\", \"absolute\", \"right-0\", \"mt-2\", \"w-48\", \"rounded-lg\", \"shadow-lg\", \"dark:shadow-[0_10px_25px_rgba(0,0,0,0.3)]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"py-1\", \"z-50\", \"backdrop-blur-sm\"], [1, \"block\", \"px-4\", \"py-2\", \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"hover:text-[#4f5fad]\", \"dark:hover:text-[#6d78c9]\", \"transition-colors\", \"group\", \"cursor-pointer\", 3, \"click\"], [1, \"fas\", \"fa-sign-out-alt\", \"mr-2\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"group-hover:scale-110\", \"transition-transform\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff8785]/20\", \"opacity-0\", \"group-hover:opacity-100\", \"transition-opacity\", \"blur-md\", \"rounded-full\"], [1, \"fixed\", \"inset-0\", \"overflow-y-auto\", \"z-50\"], [1, \"flex\", \"items-center\", \"justify-center\", \"min-h-screen\", \"pt-4\", \"px-4\", \"pb-20\", \"text-center\", \"sm:block\", \"sm:p-0\"], [\"aria-hidden\", \"true\", 1, \"fixed\", \"inset-0\", \"transition-opacity\"], [1, \"absolute\", \"inset-0\", \"bg-black/50\", \"dark:bg-black/70\", \"backdrop-blur-sm\"], [1, \"inline-block\", \"align-bottom\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-lg\", \"text-left\", \"overflow-hidden\", \"shadow-xl\", \"dark:shadow-[0_10px_25px_rgba(0,0,0,0.3)]\", \"transform\", \"transition-all\", \"sm:my-8\", \"sm:align-middle\", \"sm:max-w-lg\", \"sm:w-full\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"relative\"], [1, \"absolute\", \"top-[10%]\", \"left-[5%]\", \"w-32\", \"h-32\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/5\", \"to-transparent\", \"dark:from-[#6d78c9]/3\", \"dark:to-transparent\", \"blur-2xl\"], [1, \"absolute\", \"bottom-[10%]\", \"right-[5%]\", \"w-40\", \"h-40\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#ff6b69]/5\", \"to-transparent\", \"dark:from-[#ff8785]/3\", \"dark:to-transparent\", \"blur-2xl\"], [1, \"bg-white\", \"dark:bg-[#1e1e1e]\", \"px-4\", \"pt-5\", \"pb-4\", \"sm:p-6\", \"sm:pb-4\", \"relative\", \"z-10\"], [1, \"sm:flex\", \"sm:items-start\"], [1, \"mx-auto\", \"flex-shrink-0\", \"flex\", \"items-center\", \"justify-center\", \"h-12\", \"w-12\", \"rounded-full\", \"bg-[#ff6b69]/10\", \"dark:bg-[#ff6b69]/5\", \"sm:mx-0\", \"sm:h-10\", \"sm:w-10\", \"relative\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-6\", \"w-6\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff8785]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"mt-3\", \"text-center\", \"sm:mt-0\", \"sm:ml-4\", \"sm:text-left\"], [1, \"text-lg\", \"leading-6\", \"font-medium\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"mt-2\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"bg-[#edf1f4]\", \"dark:bg-[#161616]\", \"px-4\", \"py-3\", \"sm:px-6\", \"sm:flex\", \"sm:flex-row-reverse\", \"border-t\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"relative\", \"z-10\"], [\"type\", \"button\", 1, \"w-full\", \"inline-flex\", \"justify-center\", \"rounded-md\", \"px-4\", \"py-2\", \"text-base\", \"font-medium\", \"text-white\", \"sm:ml-3\", \"sm:w-auto\", \"sm:text-sm\", \"relative\", \"overflow-hidden\", \"group\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#ff6b69]\", \"to-[#ff8785]\", \"dark:from-[#ff6b69]\", \"dark:to-[#ff8785]\", \"rounded-md\", \"transition-transform\", \"duration-300\", \"group-hover:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#ff6b69]\", \"to-[#ff8785]\", \"dark:from-[#ff6b69]\", \"dark:to-[#ff8785]\", \"rounded-md\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-xl\", \"transition-opacity\", \"duration-300\"], [1, \"relative\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-sign-out-alt\", \"mr-1.5\"], [\"type\", \"button\", 1, \"mt-3\", \"w-full\", \"inline-flex\", \"justify-center\", \"rounded-md\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"px-4\", \"py-2\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-base\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"hover:bg-[#edf1f4]\", \"dark:hover:bg-[#2a2a2a]\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-[#4f5fad]\", \"dark:focus:ring-[#6d78c9]\", \"sm:mt-0\", \"sm:ml-3\", \"sm:w-auto\", \"sm:text-sm\", \"transition-all\", \"group\", 3, \"click\"], [1, \"relative\", \"flex\", \"items-center\", \"group-hover:text-[#4f5fad]\", \"dark:group-hover:text-[#6d78c9]\", \"transition-colors\"], [1, \"fas\", \"fa-times\", \"mr-1.5\"], [1, \"fixed\", \"bottom-6\", \"right-6\", \"p-3\", \"rounded-full\", \"shadow-lg\", \"focus:outline-none\", \"focus:ring-2\", \"focus:ring-offset-2\", \"focus:ring-[#4f5fad]\", \"dark:focus:ring-[#6d78c9]\", \"overflow-hidden\", \"group\", 3, \"click\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-full\", \"transition-transform\", \"duration-300\", \"group-hover:scale-110\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-full\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-xl\", \"transition-opacity\", \"duration-300\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-5\", \"w-5\", \"text-white\", \"relative\", \"z-10\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M5 10l7-7m0 0l7 7m-7-7v18\"]],\n      template: function AdminLayoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵpipe(1, \"async\");\n          i0.ɵɵelement(2, \"div\", 1);\n          i0.ɵɵelementStart(3, \"div\", 2)(4, \"div\", 3)(5, \"div\", 4);\n          i0.ɵɵelement(6, \"div\", 5)(7, \"div\", 6);\n          i0.ɵɵelementStart(8, \"div\", 7)(9, \"div\", 8);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(10, \"svg\", 9);\n          i0.ɵɵelement(11, \"path\", 10)(12, \"path\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelement(13, \"div\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"span\", 13);\n          i0.ɵɵtext(15, \"DevBridge\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"div\", 14)(17, \"nav\", 15)(18, \"a\", 16);\n          i0.ɵɵelement(19, \"span\", 17);\n          i0.ɵɵelementStart(20, \"div\", 18)(21, \"div\", 8);\n          i0.ɵɵelement(22, \"i\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"span\", 8);\n          i0.ɵɵtext(24, \"Dashboard\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(25, \"a\", 20);\n          i0.ɵɵelement(26, \"span\", 17);\n          i0.ɵɵelementStart(27, \"div\", 18)(28, \"div\", 8);\n          i0.ɵɵelement(29, \"i\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"span\", 8);\n          i0.ɵɵtext(31, \"Profile\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(32, \"a\", 22);\n          i0.ɵɵelement(33, \"span\", 17);\n          i0.ɵɵelementStart(34, \"div\", 18)(35, \"div\", 8);\n          i0.ɵɵelement(36, \"i\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"span\", 8);\n          i0.ɵɵtext(38, \"Reunions\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(39, \"a\", 24);\n          i0.ɵɵelement(40, \"span\", 17);\n          i0.ɵɵelementStart(41, \"div\", 18)(42, \"div\", 8);\n          i0.ɵɵelement(43, \"i\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"span\", 8);\n          i0.ɵɵtext(45, \"Plannings\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(46, \"a\", 26);\n          i0.ɵɵelement(47, \"span\", 17);\n          i0.ɵɵelementStart(48, \"div\", 18)(49, \"div\", 8);\n          i0.ɵɵelement(50, \"i\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"span\", 8);\n          i0.ɵɵtext(52, \"Projects\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(53, \"a\", 28);\n          i0.ɵɵelement(54, \"span\", 17);\n          i0.ɵɵelementStart(55, \"div\", 18)(56, \"div\", 8);\n          i0.ɵɵelement(57, \"i\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"span\", 8);\n          i0.ɵɵtext(59, \"Student Rendus\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(60, \"a\", 30);\n          i0.ɵɵelement(61, \"span\", 17);\n          i0.ɵɵelementStart(62, \"div\", 18)(63, \"div\", 8);\n          i0.ɵɵelement(64, \"i\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"span\", 8);\n          i0.ɵɵtext(66, \"\\u00C9valuations\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(67, \"a\", 32);\n          i0.ɵɵelement(68, \"span\", 17);\n          i0.ɵɵelementStart(69, \"div\", 18)(70, \"div\", 8);\n          i0.ɵɵelement(71, \"i\", 33)(72, \"div\", 34);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"span\", 8);\n          i0.ɵɵtext(74, \"Equipes\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(75, \"a\", 35);\n          i0.ɵɵelement(76, \"span\", 17);\n          i0.ɵɵelementStart(77, \"div\", 18)(78, \"div\", 8);\n          i0.ɵɵelement(79, \"i\", 36)(80, \"div\", 34);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(81, \"span\", 8);\n          i0.ɵɵtext(82, \"Back Home\");\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵtemplate(83, AdminLayoutComponent_div_83_Template, 49, 0, \"div\", 37);\n          i0.ɵɵelementStart(84, \"div\", 38)(85, \"header\", 39)(86, \"div\", 40)(87, \"div\", 41);\n          i0.ɵɵelement(88, \"div\", 42)(89, \"div\", 43);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(90, \"button\", 44);\n          i0.ɵɵlistener(\"click\", function AdminLayoutComponent_Template_button_click_90_listener() {\n            return ctx.toggleMobileMenu();\n          });\n          i0.ɵɵelement(91, \"div\", 45);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(92, \"svg\", 46);\n          i0.ɵɵelement(93, \"path\", 47);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(94, \"span\", 48);\n          i0.ɵɵtext(95, \"Menu\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(96, \"button\", 49);\n          i0.ɵɵlistener(\"click\", function AdminLayoutComponent_Template_button_click_96_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵelement(97, \"div\", 45);\n          i0.ɵɵelementStart(98, \"div\", 18);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(99, \"svg\", 50);\n          i0.ɵɵelement(100, \"path\", 51);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(101, \"span\");\n          i0.ɵɵtext(102, \"Back\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(103, \"div\", 52)(104, \"div\", 53)(105, \"div\", 54);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(106, \"svg\", 55);\n          i0.ɵɵelement(107, \"path\", 56);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelement(108, \"input\", 57);\n          i0.ɵɵelementStart(109, \"div\", 58);\n          i0.ɵɵelement(110, \"div\", 59);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(111, \"div\", 60)(112, \"button\", 61);\n          i0.ɵɵlistener(\"click\", function AdminLayoutComponent_Template_button_click_112_listener() {\n            return ctx.toggleDarkMode();\n          });\n          i0.ɵɵelementStart(113, \"div\", 62);\n          i0.ɵɵelement(114, \"div\", 63)(115, \"div\", 64);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(116, \"div\", 65);\n          i0.ɵɵelementStart(117, \"div\", 66);\n          i0.ɵɵpipe(118, \"async\");\n          i0.ɵɵtemplate(119, AdminLayoutComponent_i_119_Template, 1, 0, \"i\", 67);\n          i0.ɵɵpipe(120, \"async\");\n          i0.ɵɵtemplate(121, AdminLayoutComponent_i_121_Template, 1, 0, \"i\", 68);\n          i0.ɵɵpipe(122, \"async\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(123, \"div\", 60)(124, \"div\", 8)(125, \"button\", 69);\n          i0.ɵɵlistener(\"click\", function AdminLayoutComponent_Template_button_click_125_listener() {\n            return ctx.toggleUserMenu();\n          });\n          i0.ɵɵelementStart(126, \"span\", 70);\n          i0.ɵɵtext(127, \"Open user menu\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(128, \"span\", 71);\n          i0.ɵɵtext(129);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(130, \"div\", 72);\n          i0.ɵɵelement(131, \"div\", 65)(132, \"img\", 73);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(133, AdminLayoutComponent_div_133_Template, 8, 1, \"div\", 74);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(134, \"main\", 75)(135, \"div\", 41);\n          i0.ɵɵelement(136, \"div\", 76)(137, \"div\", 77);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(138, \"div\", 78);\n          i0.ɵɵelement(139, \"router-outlet\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(140, \"footer\", 79)(141, \"div\", 41);\n          i0.ɵɵelement(142, \"div\", 42)(143, \"div\", 43);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(144, \"div\", 80)(145, \"div\", 81)(146, \"div\", 82);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(147, \"svg\", 83);\n          i0.ɵɵelement(148, \"path\", 10)(149, \"path\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelement(150, \"div\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(151, \"span\");\n          i0.ɵɵtext(152);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(153, AdminLayoutComponent_div_153_Template, 31, 0, \"div\", 84);\n          i0.ɵɵtemplate(154, AdminLayoutComponent_button_154_Template, 5, 0, \"button\", 85);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"dark\", i0.ɵɵpipeBind1(1, 13, ctx.isDarkMode$));\n          i0.ɵɵadvance(75);\n          i0.ɵɵproperty(\"routerLinkActiveOptions\", i0.ɵɵpureFunction0(21, _c0));\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.mobileMenuOpen);\n          i0.ɵɵadvance(34);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(22, _c1, i0.ɵɵpipeBind1(118, 15, ctx.isDarkMode$)));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !i0.ɵɵpipeBind1(120, 17, ctx.isDarkMode$));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(122, 19, ctx.isDarkMode$));\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate(ctx.username);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"src\", ctx.imageProfile, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.userMenuOpen);\n          i0.ɵɵadvance(19);\n          i0.ɵɵtextInterpolate1(\"\\u00A9 \", ctx.currentYear, \" DevBridge. All rights reserved.\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showLogoutModal);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showScrollButton);\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgIf, i4.RouterOutlet, i4.RouterLink, i4.RouterLinkActive, i1.AsyncPipe],\n      styles: [\".notification-message[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 20px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  background-color: #4caf50;\\n  color: white;\\n  padding: 15px 25px;\\n  border-radius: 4px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n  z-index: 1000;\\n  animation: _ngcontent-%COMP%_fadeInOut 5s forwards;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeInOut {\\n  0% {\\n    opacity: 0;\\n    top: 0;\\n  }\\n  10% {\\n    opacity: 1;\\n    top: 20px;\\n  }\\n  90% {\\n    opacity: 1;\\n    top: 20px;\\n  }\\n  100% {\\n    opacity: 0;\\n    top: 0;\\n  }\\n}\\n\\n.back-button[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n\\n.back-button[_ngcontent-%COMP%]:hover {\\n  transform: translateX(-2px);\\n}\\n\\n.back-button[_ngcontent-%COMP%]:active {\\n  transform: translateX(-4px);\\n}\\n\\n\\n\\n.sidebar-nav-link[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n\\n\\n.sidebar-nav-link[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  inset: 0;\\n  border-radius: 0.375rem 0 0 0.375rem;\\n  border: 2px solid rgba(79, 95, 173, 0.1);\\n  pointer-events: none;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .sidebar-nav-link[_ngcontent-%COMP%]::before {\\n  border-color: rgba(109, 120, 201, 0.1);\\n}\\n\\n\\n\\n.sidebar-nav-link.active[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  right: 0;\\n  width: 0.5rem;\\n  background: linear-gradient(to bottom, #4f5fad, #00f7ff, #4f5fad);\\n  border-radius: 0 0.375rem 0.375rem 0;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.7);\\n}\\n\\n.dark[_ngcontent-%COMP%]   .sidebar-nav-link.active[_ngcontent-%COMP%]::after {\\n  background: linear-gradient(to bottom, #6d78c9, #00f7ff, #6d78c9);\\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.7);\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    opacity: 0.7;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n  100% {\\n    opacity: 0.7;\\n  }\\n}\\n\\n\\n\\n.sidebar-nav-link.active[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  right: 0;\\n  width: 0.5rem;\\n  background: linear-gradient(to bottom, #4f5fad, #00f7ff, #4f5fad);\\n  border-radius: 0 0.375rem 0.375rem 0;\\n  filter: blur(8px);\\n  transform: scale(1.5);\\n  opacity: 0.5;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n.dark[_ngcontent-%COMP%]   .sidebar-nav-link.active[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(to bottom, #6d78c9, #00f7ff, #6d78c9);\\n}\\n\\n\\n\\n.conversation-item[_ngcontent-%COMP%], .user-item[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n  transition: all 0.3s ease;\\n}\\n\\n\\n\\n.conversation-item.active[_ngcontent-%COMP%], .conversation-item[_ngcontent-%COMP%]:hover, .user-item.active[_ngcontent-%COMP%], .user-item[_ngcontent-%COMP%]:hover {\\n  border-color: rgba(255, 193, 7, 0.5) !important;\\n}\\n\\n\\n\\n.conversation-item.active[_ngcontent-%COMP%]::after, .user-item.active[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  right: 0;\\n  width: 0.25rem;\\n  background: linear-gradient(to bottom, #ffc107, #ffeb3b, #ffc107);\\n  border-radius: 0 0.375rem 0.375rem 0;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n  box-shadow: 0 0 15px rgba(255, 193, 7, 0.7);\\n}\\n\\n\\n\\n.conversation-item.active[_ngcontent-%COMP%]::before, .user-item.active[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  right: 0;\\n  width: 0.25rem;\\n  background: linear-gradient(to bottom, #ffc107, #ffeb3b, #ffc107);\\n  border-radius: 0 0.375rem 0.375rem 0;\\n  filter: blur(8px);\\n  transform: scale(1.5);\\n  opacity: 0.5;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n\\n\\n.conversation-item.unread[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  right: 1rem;\\n  transform: translateY(-50%);\\n  width: 0.5rem;\\n  height: 0.5rem;\\n  background-color: #ffc107;\\n  border-radius: 50%;\\n  box-shadow: 0 0 10px rgba(255, 193, 7, 0.7);\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbGF5b3V0cy9hZG1pbi1sYXlvdXQvYWRtaW4tbGF5b3V0LmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxlQUFlO0VBQ2YsU0FBUztFQUNULFNBQVM7RUFDVCwyQkFBMkI7RUFDM0IseUJBQXlCO0VBQ3pCLFlBQVk7RUFDWixrQkFBa0I7RUFDbEIsa0JBQWtCO0VBQ2xCLHlDQUF5QztFQUN6QyxhQUFhO0VBQ2IsZ0NBQWdDO0FBQ2xDOztBQUVBO0VBQ0U7SUFDRSxVQUFVO0lBQ1YsTUFBTTtFQUNSO0VBQ0E7SUFDRSxVQUFVO0lBQ1YsU0FBUztFQUNYO0VBQ0E7SUFDRSxVQUFVO0lBQ1YsU0FBUztFQUNYO0VBQ0E7SUFDRSxVQUFVO0lBQ1YsTUFBTTtFQUNSO0FBQ0Y7O0FBRUE7RUFDRSx5QkFBeUI7QUFDM0I7O0FBRUE7RUFDRSwyQkFBMkI7QUFDN0I7O0FBRUE7RUFDRSwyQkFBMkI7QUFDN0I7O0FBRUEscURBQXFEO0FBQ3JEO0VBQ0Usa0JBQWtCO0VBQ2xCLGdCQUFnQjtBQUNsQjs7QUFFQSx5Q0FBeUM7QUFDekM7RUFDRSxXQUFXO0VBQ1gsa0JBQWtCO0VBQ2xCLFFBQVE7RUFDUixvQ0FBb0M7RUFDcEMsd0NBQXdDO0VBQ3hDLG9CQUFvQjtBQUN0Qjs7QUFFQTtFQUNFLHNDQUFzQztBQUN4Qzs7QUFFQSx1REFBdUQ7QUFDdkQ7RUFDRSxXQUFXO0VBQ1gsa0JBQWtCO0VBQ2xCLE1BQU07RUFDTixTQUFTO0VBQ1QsUUFBUTtFQUNSLGFBQWE7RUFDYixpRUFBaUU7RUFDakUsb0NBQW9DO0VBQ3BDLDRCQUE0QjtFQUM1QiwyQ0FBMkM7QUFDN0M7O0FBRUE7RUFDRSxpRUFBaUU7RUFDakUsMkNBQTJDO0FBQzdDOztBQUVBLDJCQUEyQjtBQUMzQjtFQUNFO0lBQ0UsWUFBWTtFQUNkO0VBQ0E7SUFDRSxVQUFVO0VBQ1o7RUFDQTtJQUNFLFlBQVk7RUFDZDtBQUNGOztBQUVBLCtCQUErQjtBQUMvQjtFQUNFLFdBQVc7RUFDWCxrQkFBa0I7RUFDbEIsTUFBTTtFQUNOLFNBQVM7RUFDVCxRQUFRO0VBQ1IsYUFBYTtFQUNiLGlFQUFpRTtFQUNqRSxvQ0FBb0M7RUFDcEMsaUJBQWlCO0VBQ2pCLHFCQUFxQjtFQUNyQixZQUFZO0VBQ1osNEJBQTRCO0FBQzlCOztBQUVBO0VBQ0UsaUVBQWlFO0FBQ25FOztBQUVBLGlFQUFpRTtBQUNqRTs7RUFFRSxrQkFBa0I7RUFDbEIsZ0JBQWdCO0VBQ2hCLHlCQUF5QjtBQUMzQjs7QUFFQSxnRUFBZ0U7QUFDaEU7Ozs7RUFJRSwrQ0FBK0M7QUFDakQ7O0FBRUEsb0VBQW9FO0FBQ3BFOztFQUVFLFdBQVc7RUFDWCxrQkFBa0I7RUFDbEIsTUFBTTtFQUNOLFNBQVM7RUFDVCxRQUFRO0VBQ1IsY0FBYztFQUNkLGlFQUFpRTtFQUNqRSxvQ0FBb0M7RUFDcEMsNEJBQTRCO0VBQzVCLDJDQUEyQztBQUM3Qzs7QUFFQSx3REFBd0Q7QUFDeEQ7O0VBRUUsV0FBVztFQUNYLGtCQUFrQjtFQUNsQixNQUFNO0VBQ04sU0FBUztFQUNULFFBQVE7RUFDUixjQUFjO0VBQ2QsaUVBQWlFO0VBQ2pFLG9DQUFvQztFQUNwQyxpQkFBaUI7RUFDakIscUJBQXFCO0VBQ3JCLFlBQVk7RUFDWiw0QkFBNEI7QUFDOUI7O0FBRUEsNENBQTRDO0FBQzVDO0VBQ0UsV0FBVztFQUNYLGtCQUFrQjtFQUNsQixRQUFRO0VBQ1IsV0FBVztFQUNYLDJCQUEyQjtFQUMzQixhQUFhO0VBQ2IsY0FBYztFQUNkLHlCQUF5QjtFQUN6QixrQkFBa0I7RUFDbEIsMkNBQTJDO0FBQzdDOztBQUVBLHcwT0FBdzBPIiwic291cmNlc0NvbnRlbnQiOlsiLm5vdGlmaWNhdGlvbi1tZXNzYWdlIHtcbiAgcG9zaXRpb246IGZpeGVkO1xuICB0b3A6IDIwcHg7XG4gIGxlZnQ6IDUwJTtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKC01MCUpO1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjNGNhZjUwO1xuICBjb2xvcjogd2hpdGU7XG4gIHBhZGRpbmc6IDE1cHggMjVweDtcbiAgYm9yZGVyLXJhZGl1czogNHB4O1xuICBib3gtc2hhZG93OiAwIDJweCAxMHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbiAgei1pbmRleDogMTAwMDtcbiAgYW5pbWF0aW9uOiBmYWRlSW5PdXQgNXMgZm9yd2FyZHM7XG59XG5cbkBrZXlmcmFtZXMgZmFkZUluT3V0IHtcbiAgMCUge1xuICAgIG9wYWNpdHk6IDA7XG4gICAgdG9wOiAwO1xuICB9XG4gIDEwJSB7XG4gICAgb3BhY2l0eTogMTtcbiAgICB0b3A6IDIwcHg7XG4gIH1cbiAgOTAlIHtcbiAgICBvcGFjaXR5OiAxO1xuICAgIHRvcDogMjBweDtcbiAgfVxuICAxMDAlIHtcbiAgICBvcGFjaXR5OiAwO1xuICAgIHRvcDogMDtcbiAgfVxufVxuXG4uYmFjay1idXR0b24ge1xuICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xufVxuXG4uYmFjay1idXR0b246aG92ZXIge1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoLTJweCk7XG59XG5cbi5iYWNrLWJ1dHRvbjphY3RpdmUge1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoLTRweCk7XG59XG5cbi8qIEVmZmV0IGZsdW8gcG91ciBsZXMgYm91dG9ucyBkZSBsYSBiYXJyZSBsYXTDg8KpcmFsZSAqL1xuLnNpZGViYXItbmF2LWxpbmsge1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIG92ZXJmbG93OiBoaWRkZW47XG59XG5cbi8qIEJvcmR1cmUgc3RhbmRhcmQgcG91ciB0b3VzIGxlcyBsaWVucyAqL1xuLnNpZGViYXItbmF2LWxpbms6OmJlZm9yZSB7XG4gIGNvbnRlbnQ6IFwiXCI7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgaW5zZXQ6IDA7XG4gIGJvcmRlci1yYWRpdXM6IDAuMzc1cmVtIDAgMCAwLjM3NXJlbTtcbiAgYm9yZGVyOiAycHggc29saWQgcmdiYSg3OSwgOTUsIDE3MywgMC4xKTtcbiAgcG9pbnRlci1ldmVudHM6IG5vbmU7XG59XG5cbi5kYXJrIC5zaWRlYmFyLW5hdi1saW5rOjpiZWZvcmUge1xuICBib3JkZXItY29sb3I6IHJnYmEoMTA5LCAxMjAsIDIwMSwgMC4xKTtcbn1cblxuLyogRWZmZXQgZmx1byBzdXIgbGUgYm9yZCBkcm9pdCBwb3VyIGxlcyBsaWVucyBhY3RpZnMgKi9cbi5zaWRlYmFyLW5hdi1saW5rLmFjdGl2ZTo6YWZ0ZXIge1xuICBjb250ZW50OiBcIlwiO1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIHRvcDogMDtcbiAgYm90dG9tOiAwO1xuICByaWdodDogMDtcbiAgd2lkdGg6IDAuNXJlbTtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KHRvIGJvdHRvbSwgIzRmNWZhZCwgIzAwZjdmZiwgIzRmNWZhZCk7XG4gIGJvcmRlci1yYWRpdXM6IDAgMC4zNzVyZW0gMC4zNzVyZW0gMDtcbiAgYW5pbWF0aW9uOiBwdWxzZSAycyBpbmZpbml0ZTtcbiAgYm94LXNoYWRvdzogMCAwIDE1cHggcmdiYSgwLCAyNDcsIDI1NSwgMC43KTtcbn1cblxuLmRhcmsgLnNpZGViYXItbmF2LWxpbmsuYWN0aXZlOjphZnRlciB7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCh0byBib3R0b20sICM2ZDc4YzksICMwMGY3ZmYsICM2ZDc4YzkpO1xuICBib3gtc2hhZG93OiAwIDAgMTVweCByZ2JhKDAsIDI0NywgMjU1LCAwLjcpO1xufVxuXG4vKiBBbmltYXRpb24gZGUgcHVsc2F0aW9uICovXG5Aa2V5ZnJhbWVzIHB1bHNlIHtcbiAgMCUge1xuICAgIG9wYWNpdHk6IDAuNztcbiAgfVxuICA1MCUge1xuICAgIG9wYWNpdHk6IDE7XG4gIH1cbiAgMTAwJSB7XG4gICAgb3BhY2l0eTogMC43O1xuICB9XG59XG5cbi8qIEVmZmV0IGRlIGx1ZXVyIHF1aSBkw4PCqWJvcmRlICovXG4uc2lkZWJhci1uYXYtbGluay5hY3RpdmU6OmJlZm9yZSB7XG4gIGNvbnRlbnQ6IFwiXCI7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgdG9wOiAwO1xuICBib3R0b206IDA7XG4gIHJpZ2h0OiAwO1xuICB3aWR0aDogMC41cmVtO1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQodG8gYm90dG9tLCAjNGY1ZmFkLCAjMDBmN2ZmLCAjNGY1ZmFkKTtcbiAgYm9yZGVyLXJhZGl1czogMCAwLjM3NXJlbSAwLjM3NXJlbSAwO1xuICBmaWx0ZXI6IGJsdXIoOHB4KTtcbiAgdHJhbnNmb3JtOiBzY2FsZSgxLjUpO1xuICBvcGFjaXR5OiAwLjU7XG4gIGFuaW1hdGlvbjogcHVsc2UgMnMgaW5maW5pdGU7XG59XG5cbi5kYXJrIC5zaWRlYmFyLW5hdi1saW5rLmFjdGl2ZTo6YmVmb3JlIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KHRvIGJvdHRvbSwgIzZkNzhjOSwgIzAwZjdmZiwgIzZkNzhjOSk7XG59XG5cbi8qIEVmZmV0IGphdW5lIHBvdXIgbGVzIGxpc3RlcyBkZSBjb252ZXJzYXRpb25zIGV0IHV0aWxpc2F0ZXVycyAqL1xuLmNvbnZlcnNhdGlvbi1pdGVtLFxuLnVzZXItaXRlbSB7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbn1cblxuLyogRWZmZXQgZGUgYm9yZHVyZSBqYXVuZSBwb3VyIGxlcyDDg8KpbMODwqltZW50cyBhY3RpZnMgb3Ugc3Vydm9sw4PCqXMgKi9cbi5jb252ZXJzYXRpb24taXRlbS5hY3RpdmUsXG4uY29udmVyc2F0aW9uLWl0ZW06aG92ZXIsXG4udXNlci1pdGVtLmFjdGl2ZSxcbi51c2VyLWl0ZW06aG92ZXIge1xuICBib3JkZXItY29sb3I6IHJnYmEoMjU1LCAxOTMsIDcsIDAuNSkgIWltcG9ydGFudDtcbn1cblxuLyogRWZmZXQgZGUgbHVldXIgamF1bmUgc3VyIGxlIGJvcmQgZHJvaXQgcG91ciBsZXMgw4PCqWzDg8KpbWVudHMgYWN0aWZzICovXG4uY29udmVyc2F0aW9uLWl0ZW0uYWN0aXZlOjphZnRlcixcbi51c2VyLWl0ZW0uYWN0aXZlOjphZnRlciB7XG4gIGNvbnRlbnQ6IFwiXCI7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgdG9wOiAwO1xuICBib3R0b206IDA7XG4gIHJpZ2h0OiAwO1xuICB3aWR0aDogMC4yNXJlbTtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KHRvIGJvdHRvbSwgI2ZmYzEwNywgI2ZmZWIzYiwgI2ZmYzEwNyk7XG4gIGJvcmRlci1yYWRpdXM6IDAgMC4zNzVyZW0gMC4zNzVyZW0gMDtcbiAgYW5pbWF0aW9uOiBwdWxzZSAycyBpbmZpbml0ZTtcbiAgYm94LXNoYWRvdzogMCAwIDE1cHggcmdiYSgyNTUsIDE5MywgNywgMC43KTtcbn1cblxuLyogRWZmZXQgZGUgbHVldXIgcXVpIGTDg8KpYm9yZGUgcG91ciBsZXMgw4PCqWzDg8KpbWVudHMgYWN0aWZzICovXG4uY29udmVyc2F0aW9uLWl0ZW0uYWN0aXZlOjpiZWZvcmUsXG4udXNlci1pdGVtLmFjdGl2ZTo6YmVmb3JlIHtcbiAgY29udGVudDogXCJcIjtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB0b3A6IDA7XG4gIGJvdHRvbTogMDtcbiAgcmlnaHQ6IDA7XG4gIHdpZHRoOiAwLjI1cmVtO1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQodG8gYm90dG9tLCAjZmZjMTA3LCAjZmZlYjNiLCAjZmZjMTA3KTtcbiAgYm9yZGVyLXJhZGl1czogMCAwLjM3NXJlbSAwLjM3NXJlbSAwO1xuICBmaWx0ZXI6IGJsdXIoOHB4KTtcbiAgdHJhbnNmb3JtOiBzY2FsZSgxLjUpO1xuICBvcGFjaXR5OiAwLjU7XG4gIGFuaW1hdGlvbjogcHVsc2UgMnMgaW5maW5pdGU7XG59XG5cbi8qIEVmZmV0IGRlIGhhbG8gcG91ciBsZXMgw4PCqWzDg8KpbWVudHMgbm9uIGx1cyAqL1xuLmNvbnZlcnNhdGlvbi1pdGVtLnVucmVhZDo6YWZ0ZXIge1xuICBjb250ZW50OiBcIlwiO1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIHRvcDogNTAlO1xuICByaWdodDogMXJlbTtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01MCUpO1xuICB3aWR0aDogMC41cmVtO1xuICBoZWlnaHQ6IDAuNXJlbTtcbiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmYzEwNztcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xuICBib3gtc2hhZG93OiAwIDAgMTBweCByZ2JhKDI1NSwgMTkzLCA3LCAwLjcpO1xufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"],\n      data: {\n        animation: [trigger('fadeIn', [transition(':enter', [style({\n          opacity: 0,\n          transform: 'translateY(-10px)'\n        }), animate('150ms ease-out', style({\n          opacity: 1,\n          transform: 'translateY(0)'\n        }))]), transition(':leave', [animate('100ms ease-in', style({\n          opacity: 0,\n          transform: 'translateY(-10px)'\n        }))])])]\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["trigger", "transition", "style", "animate", "map", "i0", "ɵɵelementStart", "ɵɵlistener", "AdminLayoutComponent_div_83_Template_div_click_1_listener", "ɵɵrestoreView", "_r7", "ctx_r6", "ɵɵnextContext", "ɵɵresetView", "toggleMobileMenu", "ɵɵelementEnd", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵnamespaceHTML", "ɵɵtext", "AdminLayoutComponent_div_83_Template_button_click_10_listener", "ctx_r8", "AdminLayoutComponent_div_83_Template_a_click_15_listener", "ctx_r9", "AdminLayoutComponent_div_83_Template_a_click_19_listener", "ctx_r10", "AdminLayoutComponent_div_83_Template_a_click_23_listener", "ctx_r11", "AdminLayoutComponent_div_83_Template_a_click_27_listener", "ctx_r12", "AdminLayoutComponent_div_83_Template_a_click_43_listener", "ctx_r13", "AdminLayoutComponent_div_83_Template_a_click_46_listener", "ctx_r14", "AdminLayoutComponent_div_133_Template_a_click_1_listener", "_r16", "ctx_r15", "openLogoutModal", "ɵɵproperty", "undefined", "AdminLayoutComponent_div_153_Template_button_click_21_listener", "_r18", "ctx_r17", "logout", "AdminLayoutComponent_div_153_Template_button_click_27_listener", "ctx_r19", "closeLogoutModal", "AdminLayoutComponent_button_154_Template_button_click_0_listener", "_r21", "ctx_r20", "scrollToTop", "AdminLayoutComponent", "constructor", "location", "authAdminService", "authService", "router", "themeService", "dataService", "username", "imageProfile", "mobileMenuOpen", "userMenuOpen", "showLogoutModal", "showScrollButton", "currentYear", "Date", "getFullYear", "subscriptions", "loadUserProfile", "isDarkMode$", "currentTheme$", "pipe", "theme", "name", "user", "getUser", "fullName", "profileImage", "trim", "image", "console", "log", "ngOnInit", "checkScrollPosition", "profileSub", "currentUser$", "subscribe", "push", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "window", "pageYOffset", "scrollTo", "top", "behavior", "toggleUserMenu", "next", "clearAuthData", "setTimeout", "navigate", "queryParams", "message", "replaceUrl", "error", "err", "goBack", "back", "toggleDarkMode", "toggleTheme", "ɵɵdirectiveInject", "i1", "Location", "i2", "AuthadminService", "i3", "AuthuserService", "i4", "Router", "i5", "ThemeService", "i6", "DataService", "selectors", "hostBindings", "AdminLayoutComponent_HostBindings", "rf", "ctx", "ɵɵresolveWindow", "ɵɵtemplate", "AdminLayoutComponent_div_83_Template", "AdminLayoutComponent_Template_button_click_90_listener", "AdminLayoutComponent_Template_button_click_96_listener", "AdminLayoutComponent_Template_button_click_112_listener", "AdminLayoutComponent_i_119_Template", "AdminLayoutComponent_i_121_Template", "AdminLayoutComponent_Template_button_click_125_listener", "AdminLayoutComponent_div_133_Template", "AdminLayoutComponent_div_153_Template", "AdminLayoutComponent_button_154_Template", "ɵɵclassProp", "ɵɵpipeBind1", "ɵɵadvance", "ɵɵpureFunction0", "_c0", "ɵɵpureFunction1", "_c1", "ɵɵtextInterpolate", "ɵɵsanitizeUrl", "ɵɵtextInterpolate1", "opacity", "transform"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\layouts\\admin-layout\\admin-layout.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\layouts\\admin-layout\\admin-layout.component.html"], "sourcesContent": ["import { Location } from '@angular/common';\nimport { Component, HostListener, OnInit, OnDestroy } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthadminService } from 'src/app/services/authadmin.service';\nimport { trigger, transition, style, animate } from '@angular/animations';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport { ThemeService } from '@app/services/theme.service';\nimport { Observable, Subscription } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { DataService } from 'src/app/services/data.service';\nimport { User } from 'src/app/models/user.model';\n@Component({\n  selector: 'app-admin-layout',\n  templateUrl: './admin-layout.component.html',\n  styleUrls: ['./admin-layout.component.css'],\n  animations: [\n    trigger('fadeIn', [\n      transition(':enter', [\n        style({ opacity: 0, transform: 'translateY(-10px)' }),\n        animate(\n          '150ms ease-out',\n          style({ opacity: 1, transform: 'translateY(0)' })\n        ),\n      ]),\n      transition(':leave', [\n        animate(\n          '100ms ease-in',\n          style({ opacity: 0, transform: 'translateY(-10px)' })\n        ),\n      ]),\n    ]),\n  ],\n})\nexport class AdminLayoutComponent implements OnInit, OnDestroy {\n  username: string = '';\n  imageProfile: string = '';\n  mobileMenuOpen = false;\n  userMenuOpen = false;\n  showLogoutModal = false;\n  showScrollButton = false;\n  currentYear = new Date().getFullYear();\n  isDarkMode$: Observable<boolean>;\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private location: Location,\n    private authAdminService: AuthadminService,\n    private authService: AuthuserService,\n    private router: Router,\n    private themeService: ThemeService,\n    private dataService: DataService\n  ) {\n    this.loadUserProfile();\n    this.isDarkMode$ = this.themeService.currentTheme$.pipe(\n      map((theme) => theme.name === 'dark')\n    );\n  }\n\n  private loadUserProfile(): void {\n    const user = this.authAdminService.getUser();\n    this.username = user?.fullName || user?.username || '';\n\n    // Toujours utiliser l'image par défaut si l'image de profil est null, 'null' ou vide\n    if (\n      user?.profileImage &&\n      user.profileImage !== 'null' &&\n      user.profileImage.trim() !== ''\n    ) {\n      this.imageProfile = user.profileImage;\n    } else if (\n      user?.image &&\n      user.image !== 'null' &&\n      user.image.trim() !== ''\n    ) {\n      this.imageProfile = user.image;\n    } else {\n      this.imageProfile = 'assets/images/default-profile.png';\n    }\n\n    console.log('Admin layout - Image profile loaded:', this.imageProfile);\n  }\n\n  ngOnInit(): void {\n    this.checkScrollPosition();\n\n    // S'abonner aux changements d'image de profil\n    const profileSub = this.dataService.currentUser$.subscribe(\n      (user: User | null) => {\n        if (user) {\n          this.username = user.fullName || user.username || '';\n\n          // Toujours utiliser l'image par défaut si l'image de profil est null, 'null' ou vide\n          if (\n            user.profileImage &&\n            user.profileImage !== 'null' &&\n            user.profileImage.trim() !== ''\n          ) {\n            this.imageProfile = user.profileImage;\n          } else if (\n            user.image &&\n            user.image !== 'null' &&\n            user.image.trim() !== ''\n          ) {\n            this.imageProfile = user.image;\n          } else {\n            this.imageProfile = 'assets/images/default-profile.png';\n          }\n\n          console.log(\n            'Admin layout - Image profile updated:',\n            this.imageProfile\n          );\n        }\n      }\n    );\n\n    this.subscriptions.push(profileSub);\n  }\n\n  ngOnDestroy(): void {\n    // Désabonner de tous les observables pour éviter les fuites de mémoire\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\n  }\n\n  @HostListener('window:scroll')\n  checkScrollPosition(): void {\n    this.showScrollButton = window.pageYOffset > 300;\n  }\n\n  scrollToTop(): void {\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  }\n\n  toggleMobileMenu(): void {\n    this.mobileMenuOpen = !this.mobileMenuOpen;\n  }\n\n  toggleUserMenu(): void {\n    this.userMenuOpen = !this.userMenuOpen;\n  }\n\n  openLogoutModal(): void {\n    this.showLogoutModal = true;\n    this.userMenuOpen = false;\n  }\n\n  closeLogoutModal(): void {\n    this.showLogoutModal = false;\n  }\n  logout(): void {\n    this.authService.logout().subscribe({\n      next: () => {\n        this.userMenuOpen = false;\n        this.showLogoutModal = false;\n        this.authService.clearAuthData();\n        this.authAdminService.clearAuthData();\n        setTimeout(() => {\n          this.router.navigate(['/admin/login'], {\n            queryParams: { message: 'Déconnexion réussie' },\n            replaceUrl: true,\n          });\n        }, 100);\n      },\n      error: (err) => {\n        console.error('Logout error:', err);\n        this.authService.clearAuthData();\n        this.authAdminService.clearAuthData();\n        setTimeout(() => {\n          this.router.navigate(['/admin/login'], {\n            queryParams: { message: 'Déconnexion effectuée' },\n            replaceUrl: true,\n          });\n        }, 100);\n      },\n    });\n  }\n\n  goBack(): void {\n    this.location.back();\n  }\n\n  toggleDarkMode(): void {\n    this.themeService.toggleTheme();\n  }\n}\n", "<div\n  class=\"flex h-screen main-grid-container futuristic-layout\"\n  [class.dark]=\"isDarkMode$ | async\"\n>\n  <!-- Background Grid -->\n  <div class=\"background-grid\"></div>\n\n  <!-- Sidebar -->\n  <div class=\"hidden md:flex md:flex-shrink-0\">\n    <div\n      class=\"flex flex-col w-64 bg-white dark:bg-[#1e1e1e] border-r border-[#edf1f4] dark:border-[#2a2a2a] backdrop-blur-sm\"\n    >\n      <div\n        class=\"flex items-center justify-center h-16 px-4 relative overflow-hidden\"\n      >\n        <!-- Decorative elements -->\n        <div\n          class=\"absolute -top-6 -left-6 w-12 h-12 bg-gradient-to-br from-[#4f5fad]/20 to-transparent rounded-full\"\n        ></div>\n        <div\n          class=\"absolute -bottom-6 -right-6 w-12 h-12 bg-gradient-to-tl from-[#4f5fad]/20 to-transparent rounded-full\"\n        ></div>\n\n        <div class=\"flex items-center relative z-10\">\n          <div class=\"relative\">\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              class=\"h-8 w-8 text-[#4f5fad] dark:text-[#6d78c9] transform rotate-12\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"2\"\n                d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n              />\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"2\"\n                d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n              />\n            </svg>\n            <!-- Glow effect -->\n            <div\n              class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\n            ></div>\n          </div>\n          <span\n            class=\"ml-2 text-xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\"\n            >DevBridge</span\n          >\n        </div>\n      </div>\n      <div class=\"flex flex-col flex-grow px-4 py-4\">\n        <nav class=\"flex-1 space-y-2\">\n          <!-- Navigation Items -->\n\n          <!-- dashboard -->\n          <a\n            routerLink=\"/admin/dashboard\"\n            routerLinkActive=\"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\"\n            class=\"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\"\n          >\n            <!-- Hover effect -->\n            <span\n              class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity\"\n            ></span>\n\n            <div class=\"relative z-10 flex items-center\">\n              <div class=\"relative\">\n                <i\n                  class=\"fas fa-th-large h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-all group-hover:scale-110\"\n                ></i>\n              </div>\n              <span class=\"relative\">Dashboard</span>\n            </div>\n          </a>\n\n          <!-- admin profile -->\n          <a\n            routerLink=\"/admin/profile\"\n            routerLinkActive=\"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\"\n            class=\"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\"\n          >\n            <!-- Hover effect -->\n            <span\n              class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity\"\n            ></span>\n\n            <div class=\"relative z-10 flex items-center\">\n              <div class=\"relative\">\n                <i\n                  class=\"fas fa-user-shield h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-all group-hover:scale-110\"\n                ></i>\n              </div>\n              <span class=\"relative\">Profile</span>\n            </div>\n          </a>\n\n          <!-- Reunions -->\n          <a\n            routerLink=\"/admin/reunions\"\n            routerLinkActive=\"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\"\n            class=\"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\"\n          >\n            <!-- Hover effect -->\n            <span\n              class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity\"\n            ></span>\n\n            <div class=\"relative z-10 flex items-center\">\n              <div class=\"relative\">\n                <i\n                  class=\"fas fa-users-cog h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-all group-hover:scale-110\"\n                ></i>\n              </div>\n              <span class=\"relative\">Reunions</span>\n            </div>\n          </a>\n\n          <!-- Planning  -->\n          <a\n            routerLink=\"/admin/plannings\"\n            routerLinkActive=\"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\"\n            class=\"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\"\n          >\n            <!-- Hover effect -->\n            <span\n              class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity\"\n            ></span>\n\n            <div class=\"relative z-10 flex items-center\">\n              <div class=\"relative\">\n                <i\n                  class=\"far fa-calendar-check h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-all group-hover:scale-110\"\n                ></i>\n              </div>\n              <span class=\"relative\">Plannings</span>\n            </div>\n          </a>\n\n          <!-- projects -->\n          <a\n            routerLink=\"/admin/projects\"\n            routerLinkActive=\"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\"\n            class=\"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\"\n          >\n            <!-- Hover effect -->\n            <span\n              class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity\"\n            ></span>\n\n            <div class=\"relative z-10 flex items-center\">\n              <div class=\"relative\">\n                <i\n                  class=\"fas fa-rocket h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-all group-hover:scale-110\"\n                ></i>\n              </div>\n              <span class=\"relative\">Projects</span>\n            </div>\n          </a>\n\n          <!-- rendus -->\n          <a\n            routerLink=\"/admin/projects/rendus\"\n            routerLinkActive=\"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\"\n            class=\"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\"\n          >\n            <!-- Hover effect -->\n            <span\n              class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity\"\n            ></span>\n\n            <div class=\"relative z-10 flex items-center\">\n              <div class=\"relative\">\n                <i\n                  class=\"fas fa-file-upload h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-all group-hover:scale-110\"\n                ></i>\n              </div>\n              <span class=\"relative\">Student Rendus</span>\n            </div>\n          </a>\n\n          <!--  évaluations -->\n          <a\n            routerLink=\"/admin/projects/evaluations\"\n            routerLinkActive=\"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\"\n            class=\"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\"\n          >\n            <!-- Hover effect -->\n            <span\n              class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity\"\n            ></span>\n\n            <div class=\"relative z-10 flex items-center\">\n              <div class=\"relative\">\n                <i\n                  class=\"fas fa-clipboard-check h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-all group-hover:scale-110\"\n                ></i>\n              </div>\n              <span class=\"relative\">Évaluations</span>\n            </div>\n          </a>\n          <!-- Equipes -->\n          <a\n            routerLink=\"/admin/equipes\"\n            routerLinkActive=\"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\"\n            class=\"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\"\n          >\n            <!-- Hover effect -->\n            <span\n              class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity\"\n            ></span>\n\n            <div class=\"relative z-10 flex items-center\">\n              <div class=\"relative\">\n                <i\n                  class=\"fas fa-users h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-all group-hover:scale-110\"\n                ></i>\n                <!-- Glow effect -->\n                <div\n                  class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\n                ></div>\n              </div>\n              <span class=\"relative\">Equipes</span>\n            </div>\n          </a>\n\n          <!-- Back Home button -->\n          <a\n            routerLink=\"/\"\n            routerLinkActive=\"active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium\"\n            [routerLinkActiveOptions]=\"{ exact: true }\"\n            class=\"sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all\"\n          >\n            <!-- Hover effect -->\n            <span\n              class=\"absolute inset-0 w-1 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] opacity-0 group-hover:opacity-100 transition-opacity\"\n            ></span>\n\n            <div class=\"relative z-10 flex items-center\">\n              <div class=\"relative\">\n                <i\n                  class=\"fas fa-home h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-colors\"\n                ></i>\n                <!-- Glow effect -->\n                <div\n                  class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\n                ></div>\n              </div>\n              <span class=\"relative\">Back Home</span>\n            </div>\n          </a>\n\n          <!-- end of bo back -->\n        </nav>\n      </div>\n    </div>\n  </div>\n  <!-- Model de template pour Mobile -->\n  <!-- Mobile sidebar -->\n  <div class=\"md:hidden fixed inset-0 z-40\" *ngIf=\"mobileMenuOpen\">\n    <div\n      class=\"fixed inset-0 bg-gray-600 bg-opacity-75\"\n      (click)=\"toggleMobileMenu()\"\n    ></div>\n    <div class=\"relative flex flex-col w-72 bg-white h-full\">\n      <div class=\"flex items-center justify-between h-16 px-4\">\n        <div class=\"flex items-center\">\n          <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            class=\"h-8 w-8 text-[#4f5fad]\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n            stroke=\"currentColor\"\n          >\n            <path\n              stroke-linecap=\"round\"\n              stroke-linejoin=\"round\"\n              stroke-width=\"2\"\n              d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n            />\n            <path\n              stroke-linecap=\"round\"\n              stroke-linejoin=\"round\"\n              stroke-width=\"2\"\n              d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n            />\n          </svg>\n          <span class=\"ml-2 text-xl font-bold text-[#4f5fad]\">DevBridge</span>\n        </div>\n        <button (click)=\"toggleMobileMenu()\" class=\"text-[#6d6870]\">\n          <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            class=\"h-6 w-6\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n            stroke=\"currentColor\"\n          >\n            <path\n              stroke-linecap=\"round\"\n              stroke-linejoin=\"round\"\n              stroke-width=\"2\"\n              d=\"M6 18L18 6M6 6l12 12\"\n            />\n          </svg>\n        </button>\n      </div>\n      <div class=\"flex flex-col flex-grow px-4 py-4\">\n        <nav class=\"flex-1 space-y-2\">\n          <!-- start of bo back -->\n\n          <!-- dashboard -->\n          <a\n            routerLink=\"/admin/dashboard\"\n            (click)=\"toggleMobileMenu()\"\n            class=\"group flex items-center px-4 py-3 text-sm font-medium rounded-md text-[#6d6870] hover:bg-[#edf1f4] hover:text-[#4f5fad] transition-colors\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              class=\"h-5 w-5 mr-3\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"2\"\n                d=\"M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z\"\n              />\n            </svg>\n            Dashboard\n          </a>\n          <!-- admin profile -->\n          <a\n            routerLink=\"/admin/profile\"\n            (click)=\"toggleMobileMenu()\"\n            class=\"group flex items-center px-4 py-3 text-sm font-medium rounded-md text-[#6d6870] hover:bg-[#edf1f4] hover:text-[#4f5fad] transition-colors\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              class=\"h-5 w-5 mr-3\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"2\"\n                d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z\"\n              />\n            </svg>\n            profile\n          </a>\n          <!-- Reunions -->\n          <a\n            routerLink=\"/admin/reunions\"\n            (click)=\"toggleMobileMenu()\"\n            class=\"group flex items-center px-4 py-3 text-sm font-medium rounded-md text-[#6d6870] hover:bg-[#edf1f4] hover:text-[#4f5fad] transition-colors\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              class=\"h-5 w-5 mr-3\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"2\"\n                d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"\n              />\n            </svg>\n            Reunions\n          </a>\n          <!-- Planning -->\n          <a\n            routerLink=\"/admin/plannings\"\n            (click)=\"toggleMobileMenu()\"\n            class=\"group flex items-center px-4 py-3 text-sm font-medium rounded-md text-[#6d6870] hover:bg-[#edf1f4] hover:text-[#4f5fad] transition-colors\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              class=\"h-5 w-5 mr-3\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"2\"\n                d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n              />\n            </svg>\n            Planning\n          </a>\n          <!-- projects -->\n          <a\n            routerLink=\"/admin/projects\"\n            routerLinkActive=\"bg-[#edf1f4] text-[#4f5fad] font-medium\"\n            class=\"group flex items-center px-4 py-3 text-sm font-medium rounded-md text-[#6d6870] hover:bg-[#edf1f4] hover:text-[#4f5fad] transition-colors\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              class=\"h-5 w-5 mr-3\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"2\"\n                d=\"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z\"\n              />\n            </svg>\n            Projects\n          </a>\n          <!-- rendus -->\n          <a\n            routerLink=\"/admin/projects/rendus\"\n            routerLinkActive=\"bg-[#edf1f4] text-[#4f5fad] font-medium\"\n            class=\"group flex items-center px-4 py-3 text-sm font-medium rounded-md text-[#6d6870] hover:bg-[#edf1f4] hover:text-[#4f5fad] transition-colors\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              class=\"h-5 w-5 mr-3\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"2\"\n                d=\"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\"\n              />\n            </svg>\n            Student Rendus\n          </a>\n          <!--  évaluations -->\n          <a\n            routerLink=\"/admin/projects/evaluations\"\n            routerLinkActive=\"bg-[#edf1f4] text-[#4f5fad] font-medium\"\n            class=\"group flex items-center px-4 py-3 text-sm font-medium rounded-md text-[#6d6870] hover:bg-[#edf1f4] hover:text-[#4f5fad] transition-colors\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              class=\"h-5 w-5 mr-3\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"2\"\n                d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"\n              />\n            </svg>\n            Évaluations\n          </a>\n          <!-- Equipes -->\n          <a\n            routerLink=\"/admin/equipes\"\n            (click)=\"toggleMobileMenu()\"\n            class=\"group flex items-center px-4 py-3 text-sm font-medium rounded-md text-[#6d6870] hover:bg-[#edf1f4] hover:text-[#4f5fad] transition-colors\"\n          >\n            <i class=\"fas fa-users h-5 w-5 mr-3\"></i>\n            Equipes\n          </a>\n\n          <!-- Back Home button -->\n          <a\n            routerLink=\"/\"\n            (click)=\"toggleMobileMenu()\"\n            class=\"group flex items-center px-4 py-3 text-sm font-medium rounded-md text-[#6d6870] hover:bg-[#edf1f4] hover:text-[#4f5fad] transition-colors\"\n          >\n            <i class=\"fas fa-home h-5 w-5 mr-3 text-[#4f5fad]\"></i>\n            Back Home\n          </a>\n\n          <!-- end of bo back -->\n        </nav>\n      </div>\n    </div>\n  </div>\n\n  <!-- Main Content -->\n  <div class=\"flex-1 flex flex-col overflow-hidden\">\n    <!-- Topbar -->\n    <header\n      class=\"bg-white dark:bg-[#1e1e1e] shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)] z-10 border-b border-[#edf1f4]/50 dark:border-[#2a2a2a] backdrop-blur-sm\"\n    >\n      <div class=\"flex items-center justify-between h-16 px-4 relative\">\n        <!-- Decorative elements -->\n        <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n          <div\n            class=\"absolute top-0 left-1/4 w-px h-full bg-gradient-to-b from-transparent via-[#4f5fad]/10 dark:via-[#6d78c9]/5 to-transparent\"\n          ></div>\n          <div\n            class=\"absolute top-0 right-1/3 w-px h-full bg-gradient-to-b from-transparent via-[#4f5fad]/5 dark:via-[#6d78c9]/3 to-transparent\"\n          ></div>\n        </div>\n\n        <!-- Mobile menu button -->\n        <button\n          (click)=\"toggleMobileMenu()\"\n          class=\"md:hidden flex items-center justify-center h-8 w-8 rounded-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] focus:outline-none transition-colors relative group\"\n        >\n          <div\n            class=\"absolute inset-0 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 opacity-0 group-hover:opacity-100 transition-opacity rounded-md blur-md\"\n          ></div>\n          <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            class=\"h-5 w-5 relative z-10\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n            stroke=\"currentColor\"\n          >\n            <path\n              stroke-linecap=\"round\"\n              stroke-linejoin=\"round\"\n              stroke-width=\"2\"\n              d=\"M4 6h16M4 12h16M4 18h16\"\n            />\n          </svg>\n          <span\n            class=\"text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] ml-2 relative z-10\"\n            >Menu</span\n          >\n        </button>\n\n        <!-- Bouton Back -->\n        <button\n          (click)=\"goBack()\"\n          class=\"hidden md:flex items-center px-3 py-1 rounded-md text-sm font-medium text-[#4f5fad] dark:text-[#6d78c9] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] transition-all duration-200 relative group overflow-hidden\"\n        >\n          <div\n            class=\"absolute inset-0 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 opacity-0 group-hover:opacity-100 transition-opacity rounded-md blur-md\"\n          ></div>\n          <div class=\"relative z-10 flex items-center\">\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              class=\"h-5 w-5 mr-1 group-hover:scale-110 transition-transform\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"2\"\n                d=\"M10 19l-7-7m0 0l7-7m-7 7h18\"\n              />\n            </svg>\n            <span>Back</span>\n          </div>\n        </button>\n\n        <!-- Search Bar -->\n        <div class=\"flex-1 max-w-md ml-4 md:ml-6\">\n          <div class=\"relative group\">\n            <div\n              class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\n            >\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                class=\"h-5 w-5 text-[#6d6870] dark:text-[#a0a0a0] group-focus-within:text-[#4f5fad] dark:group-focus-within:text-[#6d78c9] transition-colors\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke=\"currentColor\"\n              >\n                <path\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  stroke-width=\"2\"\n                  d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                />\n              </svg>\n            </div>\n            <input\n              type=\"text\"\n              class=\"block w-full pl-10 pr-3 py-2 border border-[#bdc6cc] dark:border-[#2a2a2a] rounded-md leading-5 bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] placeholder-[#bdc6cc] dark:placeholder-[#6d6870] focus:outline-none focus:ring-2 focus:ring-[#4f5fad] dark:focus:ring-[#6d78c9] focus:border-[#4f5fad] dark:focus:border-[#6d78c9] transition-all\"\n              placeholder=\"Search...\"\n            />\n            <div\n              class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none opacity-0 group-focus-within:opacity-100 transition-opacity\"\n            >\n              <div\n                class=\"w-0.5 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full\"\n              ></div>\n            </div>\n          </div>\n        </div>\n\n        <!-- User menu -->\n        <div class=\"ml-4 flex items-center md:ml-6\">\n          <!-- Bouton Dark Mode -->\n          <button\n            (click)=\"toggleDarkMode()\"\n            class=\"flex items-center justify-center h-8 w-8 rounded-full bg-[#edf1f4] dark:bg-[#2a2a2a] hover:bg-[#dce4ec] dark:hover:bg-[#3a3a3a] text-[#4f5fad] dark:text-[#6d78c9] mr-3 transition-all duration-300 relative overflow-hidden group\"\n            aria-label=\"Toggle dark mode\"\n          >\n            <!-- Animated border -->\n            <div class=\"absolute inset-0 rounded-full overflow-hidden\">\n              <div\n                class=\"absolute inset-0 rounded-full border border-[#4f5fad]/20 dark:border-[#6d78c9]/20 opacity-0 group-hover:opacity-100 transition-opacity\"\n              ></div>\n              <div\n                class=\"absolute -inset-1 bg-gradient-to-r from-[#4f5fad]/0 via-[#4f5fad]/30 to-[#4f5fad]/0 dark:from-[#6d78c9]/0 dark:via-[#6d78c9]/30 dark:to-[#6d78c9]/0 opacity-0 group-hover:opacity-100 blur-sm animate-shine\"\n              ></div>\n            </div>\n            <!-- Glow effect -->\n            <div\n              class=\"absolute inset-0 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 opacity-0 group-hover:opacity-100 transition-opacity blur-md\"\n            ></div>\n            <div\n              class=\"relative z-10 transition-all duration-500 ease-in-out\"\n              [ngClass]=\"{ 'rotate-180': isDarkMode$ | async }\"\n            >\n              <i\n                *ngIf=\"!(isDarkMode$ | async)\"\n                class=\"far fa-moon group-hover:scale-110 transition-transform\"\n              ></i>\n              <i\n                *ngIf=\"isDarkMode$ | async\"\n                class=\"far fa-sun group-hover:scale-110 transition-transform\"\n              ></i>\n            </div>\n          </button>\n        </div>\n\n        <!-- User Profile Menu -->\n        <div class=\"ml-4 flex items-center md:ml-6\">\n          <div class=\"relative\">\n            <button\n              (click)=\"toggleUserMenu()\"\n              class=\"flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-[#4f5fad] dark:focus:ring-[#6d78c9] transition-all group\"\n            >\n              <span class=\"sr-only\">Open user menu</span>\n              <span\n                class=\"hidden md:inline-block mr-2 text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] group-hover:text-[#4f5fad] dark:group-hover:text-[#6d78c9] transition-colors\"\n                >{{ username }}</span\n              >\n              <div\n                class=\"h-8 w-8 rounded-full overflow-hidden flex items-center justify-center border-2 border-[#4f5fad] dark:border-[#6d78c9] group-hover:border-[#3d4a85] dark:group-hover:border-[#4f5fad] transition-colors relative\"\n              >\n                <!-- Glow effect -->\n                <div\n                  class=\"absolute inset-0 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 opacity-0 group-hover:opacity-100 transition-opacity blur-md\"\n                ></div>\n                <img\n                  class=\"h-full w-full object-cover\"\n                  [src]=\"imageProfile\"\n                  alt=\"Profile\"\n                />\n              </div>\n            </button>\n\n            <!-- User dropdown menu -->\n            <div\n              *ngIf=\"userMenuOpen\"\n              [@fadeIn]\n              class=\"origin-top-right absolute right-0 mt-2 w-48 rounded-lg shadow-lg dark:shadow-[0_10px_25px_rgba(0,0,0,0.3)] bg-white dark:bg-[#1e1e1e] border border-[#edf1f4]/50 dark:border-[#2a2a2a] py-1 z-50 backdrop-blur-sm\"\n            >\n              <a\n                (click)=\"openLogoutModal()\"\n                class=\"block px-4 py-2 text-sm text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors group cursor-pointer\"\n              >\n                <div class=\"flex items-center\">\n                  <div class=\"relative\">\n                    <i\n                      class=\"fas fa-sign-out-alt mr-2 text-[#ff6b69] dark:text-[#ff8785] group-hover:scale-110 transition-transform\"\n                    ></i>\n                    <!-- Glow effect -->\n                    <div\n                      class=\"absolute inset-0 bg-[#ff6b69]/20 dark:bg-[#ff8785]/20 opacity-0 group-hover:opacity-100 transition-opacity blur-md rounded-full\"\n                    ></div>\n                  </div>\n                  <span>Logout</span>\n                </div>\n              </a>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n\n    <!-- Main content area -->\n    <main\n      class=\"flex-1 overflow-y-auto bg-[#edf1f4] dark:bg-[#121212] p-4 md:p-6 relative\"\n    >\n      <!-- Background decorative elements -->\n      <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        <div\n          class=\"absolute top-[15%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\n        ></div>\n        <div\n          class=\"absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-3xl\"\n        ></div>\n      </div>\n\n      <!-- Router outlet -->\n      <div class=\"relative z-10\">\n        <router-outlet></router-outlet>\n      </div>\n    </main>\n\n    <!-- Footer -->\n    <footer\n      class=\"bg-white dark:bg-[#1e1e1e] border-t border-[#edf1f4]/50 dark:border-[#2a2a2a] py-4 relative overflow-hidden\"\n    >\n      <!-- Decorative elements -->\n      <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        <div\n          class=\"absolute top-0 left-1/4 w-px h-full bg-gradient-to-b from-transparent via-[#4f5fad]/10 dark:via-[#6d78c9]/5 to-transparent\"\n        ></div>\n        <div\n          class=\"absolute top-0 right-1/3 w-px h-full bg-gradient-to-b from-transparent via-[#4f5fad]/5 dark:via-[#6d78c9]/3 to-transparent\"\n        ></div>\n      </div>\n\n      <div\n        class=\"container mx-auto px-4 text-center text-sm text-[#6d6870] dark:text-[#a0a0a0] relative z-10\"\n      >\n        <div class=\"flex items-center justify-center\">\n          <div class=\"relative mr-2\">\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              class=\"h-4 w-4 text-[#4f5fad] dark:text-[#6d78c9]\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"2\"\n                d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n              />\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"2\"\n                d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n              />\n            </svg>\n            <!-- Glow effect -->\n            <div\n              class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\n            ></div>\n          </div>\n          <span>&copy; {{ currentYear }} DevBridge. All rights reserved.</span>\n        </div>\n      </div>\n    </footer>\n  </div>\n\n  <!-- Logout Modal -->\n  <div *ngIf=\"showLogoutModal\" class=\"fixed inset-0 overflow-y-auto z-50\">\n    <div\n      class=\"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\"\n    >\n      <!-- Background overlay with blur -->\n      <div class=\"fixed inset-0 transition-opacity\" aria-hidden=\"true\">\n        <div\n          class=\"absolute inset-0 bg-black/50 dark:bg-black/70 backdrop-blur-sm\"\n        ></div>\n      </div>\n\n      <!-- Modal -->\n      <div\n        class=\"inline-block align-bottom bg-white dark:bg-[#1e1e1e] rounded-lg text-left overflow-hidden shadow-xl dark:shadow-[0_10px_25px_rgba(0,0,0,0.3)] transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full border border-[#edf1f4]/50 dark:border-[#2a2a2a] relative\"\n      >\n        <!-- Decorative elements -->\n        <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n          <div\n            class=\"absolute top-[10%] left-[5%] w-32 h-32 rounded-full bg-gradient-to-br from-[#4f5fad]/5 to-transparent dark:from-[#6d78c9]/3 dark:to-transparent blur-2xl\"\n          ></div>\n          <div\n            class=\"absolute bottom-[10%] right-[5%] w-40 h-40 rounded-full bg-gradient-to-tl from-[#ff6b69]/5 to-transparent dark:from-[#ff8785]/3 dark:to-transparent blur-2xl\"\n          ></div>\n        </div>\n\n        <div\n          class=\"bg-white dark:bg-[#1e1e1e] px-4 pt-5 pb-4 sm:p-6 sm:pb-4 relative z-10\"\n        >\n          <div class=\"sm:flex sm:items-start\">\n            <div\n              class=\"mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 sm:mx-0 sm:h-10 sm:w-10 relative\"\n            >\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                class=\"h-6 w-6 text-[#ff6b69] dark:text-[#ff8785]\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke=\"currentColor\"\n              >\n                <path\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  stroke-width=\"2\"\n                  d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\"\n                />\n              </svg>\n              <!-- Glow effect -->\n              <div\n                class=\"absolute inset-0 bg-[#ff6b69]/20 dark:bg-[#ff8785]/20 blur-xl rounded-full transform scale-150 -z-10\"\n              ></div>\n            </div>\n            <div class=\"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left\">\n              <h3\n                class=\"text-lg leading-6 font-medium bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\"\n              >\n                Ready to Leave?\n              </h3>\n              <div class=\"mt-2\">\n                <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">\n                  Are you sure you want to logout?\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div\n          class=\"bg-[#edf1f4] dark:bg-[#161616] px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse border-t border-[#edf1f4]/50 dark:border-[#2a2a2a] relative z-10\"\n        >\n          <button\n            type=\"button\"\n            (click)=\"logout()\"\n            class=\"w-full inline-flex justify-center rounded-md px-4 py-2 text-base font-medium text-white sm:ml-3 sm:w-auto sm:text-sm relative overflow-hidden group\"\n          >\n            <div\n              class=\"absolute inset-0 bg-gradient-to-r from-[#ff6b69] to-[#ff8785] dark:from-[#ff6b69] dark:to-[#ff8785] rounded-md transition-transform duration-300 group-hover:scale-105\"\n            ></div>\n            <div\n              class=\"absolute inset-0 bg-gradient-to-r from-[#ff6b69] to-[#ff8785] dark:from-[#ff6b69] dark:to-[#ff8785] rounded-md opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-300\"\n            ></div>\n            <span class=\"relative flex items-center\">\n              <i class=\"fas fa-sign-out-alt mr-1.5\"></i>\n              Logout\n            </span>\n          </button>\n          <button\n            type=\"button\"\n            (click)=\"closeLogoutModal()\"\n            class=\"mt-3 w-full inline-flex justify-center rounded-md border border-[#bdc6cc] dark:border-[#2a2a2a] px-4 py-2 bg-white dark:bg-[#1e1e1e] text-base font-medium text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] focus:outline-none focus:ring-2 focus:ring-[#4f5fad] dark:focus:ring-[#6d78c9] sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm transition-all group\"\n          >\n            <span\n              class=\"relative flex items-center group-hover:text-[#4f5fad] dark:group-hover:text-[#6d78c9] transition-colors\"\n            >\n              <i class=\"fas fa-times mr-1.5\"></i>\n              Cancel\n            </span>\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Scroll to top button -->\n  <button\n    *ngIf=\"showScrollButton\"\n    (click)=\"scrollToTop()\"\n    class=\"fixed bottom-6 right-6 p-3 rounded-full shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#4f5fad] dark:focus:ring-[#6d78c9] overflow-hidden group\"\n  >\n    <div\n      class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-full transition-transform duration-300 group-hover:scale-110\"\n    ></div>\n    <div\n      class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-full opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-300\"\n    ></div>\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      class=\"h-5 w-5 text-white relative z-10\"\n      fill=\"none\"\n      viewBox=\"0 0 24 24\"\n      stroke=\"currentColor\"\n    >\n      <path\n        stroke-linecap=\"round\"\n        stroke-linejoin=\"round\"\n        stroke-width=\"2\"\n        d=\"M5 10l7-7m0 0l7 7m-7-7v18\"\n      />\n    </svg>\n  </button>\n</div>\n"], "mappings": "AAIA,SAASA,OAAO,EAAEC,UAAU,EAAEC,KAAK,EAAEC,OAAO,QAAQ,qBAAqB;AAIzE,SAASC,GAAG,QAAQ,gBAAgB;;;;;;;;;;;ICgQlCC,EAAA,CAAAC,cAAA,cAAiE;IAG7DD,EAAA,CAAAE,UAAA,mBAAAC,0DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,gBAAA,EAAkB;IAAA,EAAC;IAC7BT,EAAA,CAAAU,YAAA,EAAM;IACPV,EAAA,CAAAC,cAAA,cAAyD;IAGnDD,EAAA,CAAAW,cAAA,EAMC;IANDX,EAAA,CAAAC,cAAA,cAMC;IACCD,EAAA,CAAAY,SAAA,eAKE;IAOJZ,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAa,eAAA,EAAoD;IAApDb,EAAA,CAAAC,cAAA,eAAoD;IAAAD,EAAA,CAAAc,MAAA,gBAAS;IAAAd,EAAA,CAAAU,YAAA,EAAO;IAEtEV,EAAA,CAAAC,cAAA,kBAA4D;IAApDD,EAAA,CAAAE,UAAA,mBAAAa,8DAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAW,MAAA,GAAAhB,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAQ,MAAA,CAAAP,gBAAA,EAAkB;IAAA,EAAC;IAClCT,EAAA,CAAAW,cAAA,EAMC;IANDX,EAAA,CAAAC,cAAA,eAMC;IACCD,EAAA,CAAAY,SAAA,gBAKE;IACJZ,EAAA,CAAAU,YAAA,EAAM;IAGVV,EAAA,CAAAa,eAAA,EAA+C;IAA/Cb,EAAA,CAAAC,cAAA,eAA+C;IAOzCD,EAAA,CAAAE,UAAA,mBAAAe,yDAAA;MAAAjB,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAa,MAAA,GAAAlB,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAU,MAAA,CAAAT,gBAAA,EAAkB;IAAA,EAAC;IAG5BT,EAAA,CAAAW,cAAA,EAMC;IANDX,EAAA,CAAAC,cAAA,eAMC;IACCD,EAAA,CAAAY,SAAA,gBAKE;IACJZ,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAc,MAAA,mBACF;IAAAd,EAAA,CAAAU,YAAA,EAAI;IAEJV,EAAA,CAAAa,eAAA,EAIC;IAJDb,EAAA,CAAAC,cAAA,aAIC;IAFCD,EAAA,CAAAE,UAAA,mBAAAiB,yDAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAe,OAAA,GAAApB,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAY,OAAA,CAAAX,gBAAA,EAAkB;IAAA,EAAC;IAG5BT,EAAA,CAAAW,cAAA,EAMC;IANDX,EAAA,CAAAC,cAAA,eAMC;IACCD,EAAA,CAAAY,SAAA,iBAKE;IACJZ,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAc,MAAA,iBACF;IAAAd,EAAA,CAAAU,YAAA,EAAI;IAEJV,EAAA,CAAAa,eAAA,EAIC;IAJDb,EAAA,CAAAC,cAAA,cAIC;IAFCD,EAAA,CAAAE,UAAA,mBAAAmB,yDAAA;MAAArB,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAiB,OAAA,GAAAtB,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAc,OAAA,CAAAb,gBAAA,EAAkB;IAAA,EAAC;IAG5BT,EAAA,CAAAW,cAAA,EAMC;IANDX,EAAA,CAAAC,cAAA,eAMC;IACCD,EAAA,CAAAY,SAAA,iBAKE;IACJZ,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAc,MAAA,kBACF;IAAAd,EAAA,CAAAU,YAAA,EAAI;IAEJV,EAAA,CAAAa,eAAA,EAIC;IAJDb,EAAA,CAAAC,cAAA,cAIC;IAFCD,EAAA,CAAAE,UAAA,mBAAAqB,yDAAA;MAAAvB,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAmB,OAAA,GAAAxB,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAgB,OAAA,CAAAf,gBAAA,EAAkB;IAAA,EAAC;IAG5BT,EAAA,CAAAW,cAAA,EAMC;IANDX,EAAA,CAAAC,cAAA,eAMC;IACCD,EAAA,CAAAY,SAAA,iBAKE;IACJZ,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAc,MAAA,kBACF;IAAAd,EAAA,CAAAU,YAAA,EAAI;IAEJV,EAAA,CAAAa,eAAA,EAIC;IAJDb,EAAA,CAAAC,cAAA,cAIC;IACCD,EAAA,CAAAW,cAAA,EAMC;IANDX,EAAA,CAAAC,cAAA,eAMC;IACCD,EAAA,CAAAY,SAAA,iBAKE;IACJZ,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAc,MAAA,kBACF;IAAAd,EAAA,CAAAU,YAAA,EAAI;IAEJV,EAAA,CAAAa,eAAA,EAIC;IAJDb,EAAA,CAAAC,cAAA,cAIC;IACCD,EAAA,CAAAW,cAAA,EAMC;IANDX,EAAA,CAAAC,cAAA,eAMC;IACCD,EAAA,CAAAY,SAAA,iBAKE;IACJZ,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAc,MAAA,wBACF;IAAAd,EAAA,CAAAU,YAAA,EAAI;IAEJV,EAAA,CAAAa,eAAA,EAIC;IAJDb,EAAA,CAAAC,cAAA,cAIC;IACCD,EAAA,CAAAW,cAAA,EAMC;IANDX,EAAA,CAAAC,cAAA,eAMC;IACCD,EAAA,CAAAY,SAAA,iBAKE;IACJZ,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAc,MAAA,0BACF;IAAAd,EAAA,CAAAU,YAAA,EAAI;IAEJV,EAAA,CAAAa,eAAA,EAIC;IAJDb,EAAA,CAAAC,cAAA,cAIC;IAFCD,EAAA,CAAAE,UAAA,mBAAAuB,yDAAA;MAAAzB,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAqB,OAAA,GAAA1B,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAkB,OAAA,CAAAjB,gBAAA,EAAkB;IAAA,EAAC;IAG5BT,EAAA,CAAAY,SAAA,cAAyC;IACzCZ,EAAA,CAAAc,MAAA,iBACF;IAAAd,EAAA,CAAAU,YAAA,EAAI;IAGJV,EAAA,CAAAC,cAAA,cAIC;IAFCD,EAAA,CAAAE,UAAA,mBAAAyB,yDAAA;MAAA3B,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAuB,OAAA,GAAA5B,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAoB,OAAA,CAAAnB,gBAAA,EAAkB;IAAA,EAAC;IAG5BT,EAAA,CAAAY,SAAA,cAAuD;IACvDZ,EAAA,CAAAc,MAAA,mBACF;IAAAd,EAAA,CAAAU,YAAA,EAAI;;;;;IA6IAV,EAAA,CAAAY,SAAA,aAGK;;;;;IACLZ,EAAA,CAAAY,SAAA,aAGK;;;;;;IAiCPZ,EAAA,CAAAC,cAAA,eAIC;IAEGD,EAAA,CAAAE,UAAA,mBAAA2B,yDAAA;MAAA7B,EAAA,CAAAI,aAAA,CAAA0B,IAAA;MAAA,MAAAC,OAAA,GAAA/B,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAuB,OAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;IAG3BhC,EAAA,CAAAC,cAAA,cAA+B;IAE3BD,EAAA,CAAAY,SAAA,aAEK;IAKPZ,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAc,MAAA,aAAM;IAAAd,EAAA,CAAAU,YAAA,EAAO;;;IAjBvBV,EAAA,CAAAiC,UAAA,YAAAC,SAAA,CAAS;;;;;;IAiGrBlC,EAAA,CAAAC,cAAA,eAAwE;IAMlED,EAAA,CAAAY,SAAA,eAEO;IACTZ,EAAA,CAAAU,YAAA,EAAM;IAGNV,EAAA,CAAAC,cAAA,eAEC;IAGGD,EAAA,CAAAY,SAAA,eAEO;IAITZ,EAAA,CAAAU,YAAA,EAAM;IAENV,EAAA,CAAAC,cAAA,eAEC;IAKKD,EAAA,CAAAW,cAAA,EAMC;IANDX,EAAA,CAAAC,cAAA,gBAMC;IACCD,EAAA,CAAAY,SAAA,iBAKE;IACJZ,EAAA,CAAAU,YAAA,EAAM;IAENV,EAAA,CAAAa,eAAA,EAEC;IAFDb,EAAA,CAAAY,SAAA,gBAEO;IACTZ,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,gBAA2D;IAIvDD,EAAA,CAAAc,MAAA,yBACF;IAAAd,EAAA,CAAAU,YAAA,EAAK;IACLV,EAAA,CAAAC,cAAA,gBAAkB;IAEdD,EAAA,CAAAc,MAAA,0CACF;IAAAd,EAAA,CAAAU,YAAA,EAAI;IAKZV,EAAA,CAAAC,cAAA,gBAEC;IAGGD,EAAA,CAAAE,UAAA,mBAAAiC,+DAAA;MAAAnC,EAAA,CAAAI,aAAA,CAAAgC,IAAA;MAAA,MAAAC,OAAA,GAAArC,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA6B,OAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IAGlBtC,EAAA,CAAAY,SAAA,gBAEO;IAIPZ,EAAA,CAAAC,cAAA,iBAAyC;IACvCD,EAAA,CAAAY,SAAA,cAA0C;IAC1CZ,EAAA,CAAAc,MAAA,gBACF;IAAAd,EAAA,CAAAU,YAAA,EAAO;IAETV,EAAA,CAAAC,cAAA,mBAIC;IAFCD,EAAA,CAAAE,UAAA,mBAAAqC,+DAAA;MAAAvC,EAAA,CAAAI,aAAA,CAAAgC,IAAA;MAAA,MAAAI,OAAA,GAAAxC,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAgC,OAAA,CAAAC,gBAAA,EAAkB;IAAA,EAAC;IAG5BzC,EAAA,CAAAC,cAAA,iBAEC;IACCD,EAAA,CAAAY,SAAA,cAAmC;IACnCZ,EAAA,CAAAc,MAAA,gBACF;IAAAd,EAAA,CAAAU,YAAA,EAAO;;;;;;IAQjBV,EAAA,CAAAC,cAAA,kBAIC;IAFCD,EAAA,CAAAE,UAAA,mBAAAwC,iEAAA;MAAA1C,EAAA,CAAAI,aAAA,CAAAuC,IAAA;MAAA,MAAAC,OAAA,GAAA5C,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAoC,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAGvB7C,EAAA,CAAAY,SAAA,eAEO;IAIPZ,EAAA,CAAAW,cAAA,EAMC;IANDX,EAAA,CAAAC,cAAA,eAMC;IACCD,EAAA,CAAAY,SAAA,gBAKE;IACJZ,EAAA,CAAAU,YAAA,EAAM;;;;;;;;;;;;;AD51BV,OAAM,MAAOoC,oBAAoB;EAW/BC,YACUC,QAAkB,EAClBC,gBAAkC,EAClCC,WAA4B,EAC5BC,MAAc,EACdC,YAA0B,EAC1BC,WAAwB;IALxB,KAAAL,QAAQ,GAARA,QAAQ;IACR,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,WAAW,GAAXA,WAAW;IAhBrB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,WAAW,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;IAE9B,KAAAC,aAAa,GAAmB,EAAE;IAUxC,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,WAAW,GAAG,IAAI,CAACb,YAAY,CAACc,aAAa,CAACC,IAAI,CACrDpE,GAAG,CAAEqE,KAAK,IAAKA,KAAK,CAACC,IAAI,KAAK,MAAM,CAAC,CACtC;EACH;EAEQL,eAAeA,CAAA;IACrB,MAAMM,IAAI,GAAG,IAAI,CAACrB,gBAAgB,CAACsB,OAAO,EAAE;IAC5C,IAAI,CAACjB,QAAQ,GAAGgB,IAAI,EAAEE,QAAQ,IAAIF,IAAI,EAAEhB,QAAQ,IAAI,EAAE;IAEtD;IACA,IACEgB,IAAI,EAAEG,YAAY,IAClBH,IAAI,CAACG,YAAY,KAAK,MAAM,IAC5BH,IAAI,CAACG,YAAY,CAACC,IAAI,EAAE,KAAK,EAAE,EAC/B;MACA,IAAI,CAACnB,YAAY,GAAGe,IAAI,CAACG,YAAY;KACtC,MAAM,IACLH,IAAI,EAAEK,KAAK,IACXL,IAAI,CAACK,KAAK,KAAK,MAAM,IACrBL,IAAI,CAACK,KAAK,CAACD,IAAI,EAAE,KAAK,EAAE,EACxB;MACA,IAAI,CAACnB,YAAY,GAAGe,IAAI,CAACK,KAAK;KAC/B,MAAM;MACL,IAAI,CAACpB,YAAY,GAAG,mCAAmC;;IAGzDqB,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAACtB,YAAY,CAAC;EACxE;EAEAuB,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;IAE1B;IACA,MAAMC,UAAU,GAAG,IAAI,CAAC3B,WAAW,CAAC4B,YAAY,CAACC,SAAS,CACvDZ,IAAiB,IAAI;MACpB,IAAIA,IAAI,EAAE;QACR,IAAI,CAAChB,QAAQ,GAAGgB,IAAI,CAACE,QAAQ,IAAIF,IAAI,CAAChB,QAAQ,IAAI,EAAE;QAEpD;QACA,IACEgB,IAAI,CAACG,YAAY,IACjBH,IAAI,CAACG,YAAY,KAAK,MAAM,IAC5BH,IAAI,CAACG,YAAY,CAACC,IAAI,EAAE,KAAK,EAAE,EAC/B;UACA,IAAI,CAACnB,YAAY,GAAGe,IAAI,CAACG,YAAY;SACtC,MAAM,IACLH,IAAI,CAACK,KAAK,IACVL,IAAI,CAACK,KAAK,KAAK,MAAM,IACrBL,IAAI,CAACK,KAAK,CAACD,IAAI,EAAE,KAAK,EAAE,EACxB;UACA,IAAI,CAACnB,YAAY,GAAGe,IAAI,CAACK,KAAK;SAC/B,MAAM;UACL,IAAI,CAACpB,YAAY,GAAG,mCAAmC;;QAGzDqB,OAAO,CAACC,GAAG,CACT,uCAAuC,EACvC,IAAI,CAACtB,YAAY,CAClB;;IAEL,CAAC,CACF;IAED,IAAI,CAACQ,aAAa,CAACoB,IAAI,CAACH,UAAU,CAAC;EACrC;EAEAI,WAAWA,CAAA;IACT;IACA,IAAI,CAACrB,aAAa,CAACsB,OAAO,CAAEC,GAAG,IAAKA,GAAG,CAACC,WAAW,EAAE,CAAC;EACxD;EAGAR,mBAAmBA,CAAA;IACjB,IAAI,CAACpB,gBAAgB,GAAG6B,MAAM,CAACC,WAAW,GAAG,GAAG;EAClD;EAEA5C,WAAWA,CAAA;IACT2C,MAAM,CAACE,QAAQ,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAQ,CAAE,CAAC;EACjD;EAEAnF,gBAAgBA,CAAA;IACd,IAAI,CAAC+C,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;EAC5C;EAEAqC,cAAcA,CAAA;IACZ,IAAI,CAACpC,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEAzB,eAAeA,CAAA;IACb,IAAI,CAAC0B,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACD,YAAY,GAAG,KAAK;EAC3B;EAEAhB,gBAAgBA,CAAA;IACd,IAAI,CAACiB,eAAe,GAAG,KAAK;EAC9B;EACApB,MAAMA,CAAA;IACJ,IAAI,CAACY,WAAW,CAACZ,MAAM,EAAE,CAAC4C,SAAS,CAAC;MAClCY,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACrC,YAAY,GAAG,KAAK;QACzB,IAAI,CAACC,eAAe,GAAG,KAAK;QAC5B,IAAI,CAACR,WAAW,CAAC6C,aAAa,EAAE;QAChC,IAAI,CAAC9C,gBAAgB,CAAC8C,aAAa,EAAE;QACrCC,UAAU,CAAC,MAAK;UACd,IAAI,CAAC7C,MAAM,CAAC8C,QAAQ,CAAC,CAAC,cAAc,CAAC,EAAE;YACrCC,WAAW,EAAE;cAAEC,OAAO,EAAE;YAAqB,CAAE;YAC/CC,UAAU,EAAE;WACb,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACDC,KAAK,EAAGC,GAAG,IAAI;QACb1B,OAAO,CAACyB,KAAK,CAAC,eAAe,EAAEC,GAAG,CAAC;QACnC,IAAI,CAACpD,WAAW,CAAC6C,aAAa,EAAE;QAChC,IAAI,CAAC9C,gBAAgB,CAAC8C,aAAa,EAAE;QACrCC,UAAU,CAAC,MAAK;UACd,IAAI,CAAC7C,MAAM,CAAC8C,QAAQ,CAAC,CAAC,cAAc,CAAC,EAAE;YACrCC,WAAW,EAAE;cAAEC,OAAO,EAAE;YAAuB,CAAE;YACjDC,UAAU,EAAE;WACb,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;MACT;KACD,CAAC;EACJ;EAEAG,MAAMA,CAAA;IACJ,IAAI,CAACvD,QAAQ,CAACwD,IAAI,EAAE;EACtB;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAACrD,YAAY,CAACsD,WAAW,EAAE;EACjC;;;uBAtJW5D,oBAAoB,EAAA9C,EAAA,CAAA2G,iBAAA,CAAAC,EAAA,CAAAC,QAAA,GAAA7G,EAAA,CAAA2G,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAA/G,EAAA,CAAA2G,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAAjH,EAAA,CAAA2G,iBAAA,CAAAO,EAAA,CAAAC,MAAA,GAAAnH,EAAA,CAAA2G,iBAAA,CAAAS,EAAA,CAAAC,YAAA,GAAArH,EAAA,CAAA2G,iBAAA,CAAAW,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAApBzE,oBAAoB;MAAA0E,SAAA;MAAAC,YAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;mBAApBC,GAAA,CAAA7C,mBAAA,EAAqB;UAAA,UAAA/E,EAAA,CAAA6H,eAAA;;;;;;;;UCjClC7H,EAAA,CAAAC,cAAA,aAGC;;UAECD,EAAA,CAAAY,SAAA,aAAmC;UAGnCZ,EAAA,CAAAC,cAAA,aAA6C;UAQvCD,EAAA,CAAAY,SAAA,aAEO;UAKPZ,EAAA,CAAAC,cAAA,aAA6C;UAEzCD,EAAA,CAAAW,cAAA,EAMC;UANDX,EAAA,CAAAC,cAAA,cAMC;UACCD,EAAA,CAAAY,SAAA,gBAKE;UAOJZ,EAAA,CAAAU,YAAA,EAAM;UAENV,EAAA,CAAAa,eAAA,EAEC;UAFDb,EAAA,CAAAY,SAAA,eAEO;UACTZ,EAAA,CAAAU,YAAA,EAAM;UACNV,EAAA,CAAAC,cAAA,gBAEG;UAAAD,EAAA,CAAAc,MAAA,iBAAS;UAAAd,EAAA,CAAAU,YAAA,EACX;UAGLV,EAAA,CAAAC,cAAA,eAA+C;UAWzCD,EAAA,CAAAY,SAAA,gBAEQ;UAERZ,EAAA,CAAAC,cAAA,eAA6C;UAEzCD,EAAA,CAAAY,SAAA,aAEK;UACPZ,EAAA,CAAAU,YAAA,EAAM;UACNV,EAAA,CAAAC,cAAA,eAAuB;UAAAD,EAAA,CAAAc,MAAA,iBAAS;UAAAd,EAAA,CAAAU,YAAA,EAAO;UAK3CV,EAAA,CAAAC,cAAA,aAIC;UAECD,EAAA,CAAAY,SAAA,gBAEQ;UAERZ,EAAA,CAAAC,cAAA,eAA6C;UAEzCD,EAAA,CAAAY,SAAA,aAEK;UACPZ,EAAA,CAAAU,YAAA,EAAM;UACNV,EAAA,CAAAC,cAAA,eAAuB;UAAAD,EAAA,CAAAc,MAAA,eAAO;UAAAd,EAAA,CAAAU,YAAA,EAAO;UAKzCV,EAAA,CAAAC,cAAA,aAIC;UAECD,EAAA,CAAAY,SAAA,gBAEQ;UAERZ,EAAA,CAAAC,cAAA,eAA6C;UAEzCD,EAAA,CAAAY,SAAA,aAEK;UACPZ,EAAA,CAAAU,YAAA,EAAM;UACNV,EAAA,CAAAC,cAAA,eAAuB;UAAAD,EAAA,CAAAc,MAAA,gBAAQ;UAAAd,EAAA,CAAAU,YAAA,EAAO;UAK1CV,EAAA,CAAAC,cAAA,aAIC;UAECD,EAAA,CAAAY,SAAA,gBAEQ;UAERZ,EAAA,CAAAC,cAAA,eAA6C;UAEzCD,EAAA,CAAAY,SAAA,aAEK;UACPZ,EAAA,CAAAU,YAAA,EAAM;UACNV,EAAA,CAAAC,cAAA,eAAuB;UAAAD,EAAA,CAAAc,MAAA,iBAAS;UAAAd,EAAA,CAAAU,YAAA,EAAO;UAK3CV,EAAA,CAAAC,cAAA,aAIC;UAECD,EAAA,CAAAY,SAAA,gBAEQ;UAERZ,EAAA,CAAAC,cAAA,eAA6C;UAEzCD,EAAA,CAAAY,SAAA,aAEK;UACPZ,EAAA,CAAAU,YAAA,EAAM;UACNV,EAAA,CAAAC,cAAA,eAAuB;UAAAD,EAAA,CAAAc,MAAA,gBAAQ;UAAAd,EAAA,CAAAU,YAAA,EAAO;UAK1CV,EAAA,CAAAC,cAAA,aAIC;UAECD,EAAA,CAAAY,SAAA,gBAEQ;UAERZ,EAAA,CAAAC,cAAA,eAA6C;UAEzCD,EAAA,CAAAY,SAAA,aAEK;UACPZ,EAAA,CAAAU,YAAA,EAAM;UACNV,EAAA,CAAAC,cAAA,eAAuB;UAAAD,EAAA,CAAAc,MAAA,sBAAc;UAAAd,EAAA,CAAAU,YAAA,EAAO;UAKhDV,EAAA,CAAAC,cAAA,aAIC;UAECD,EAAA,CAAAY,SAAA,gBAEQ;UAERZ,EAAA,CAAAC,cAAA,eAA6C;UAEzCD,EAAA,CAAAY,SAAA,aAEK;UACPZ,EAAA,CAAAU,YAAA,EAAM;UACNV,EAAA,CAAAC,cAAA,eAAuB;UAAAD,EAAA,CAAAc,MAAA,wBAAW;UAAAd,EAAA,CAAAU,YAAA,EAAO;UAI7CV,EAAA,CAAAC,cAAA,aAIC;UAECD,EAAA,CAAAY,SAAA,gBAEQ;UAERZ,EAAA,CAAAC,cAAA,eAA6C;UAEzCD,EAAA,CAAAY,SAAA,aAEK;UAKPZ,EAAA,CAAAU,YAAA,EAAM;UACNV,EAAA,CAAAC,cAAA,eAAuB;UAAAD,EAAA,CAAAc,MAAA,eAAO;UAAAd,EAAA,CAAAU,YAAA,EAAO;UAKzCV,EAAA,CAAAC,cAAA,aAKC;UAECD,EAAA,CAAAY,SAAA,gBAEQ;UAERZ,EAAA,CAAAC,cAAA,eAA6C;UAEzCD,EAAA,CAAAY,SAAA,aAEK;UAKPZ,EAAA,CAAAU,YAAA,EAAM;UACNV,EAAA,CAAAC,cAAA,eAAuB;UAAAD,EAAA,CAAAc,MAAA,iBAAS;UAAAd,EAAA,CAAAU,YAAA,EAAO;UAWnDV,EAAA,CAAA8H,UAAA,KAAAC,oCAAA,mBAqOM;UAGN/H,EAAA,CAAAC,cAAA,eAAkD;UAQ1CD,EAAA,CAAAY,SAAA,eAEO;UAITZ,EAAA,CAAAU,YAAA,EAAM;UAGNV,EAAA,CAAAC,cAAA,kBAGC;UAFCD,EAAA,CAAAE,UAAA,mBAAA8H,uDAAA;YAAA,OAASJ,GAAA,CAAAnH,gBAAA,EAAkB;UAAA,EAAC;UAG5BT,EAAA,CAAAY,SAAA,eAEO;UACPZ,EAAA,CAAAW,cAAA,EAMC;UANDX,EAAA,CAAAC,cAAA,eAMC;UACCD,EAAA,CAAAY,SAAA,gBAKE;UACJZ,EAAA,CAAAU,YAAA,EAAM;UACNV,EAAA,CAAAa,eAAA,EAEG;UAFHb,EAAA,CAAAC,cAAA,gBAEG;UAAAD,EAAA,CAAAc,MAAA,YAAI;UAAAd,EAAA,CAAAU,YAAA,EACN;UAIHV,EAAA,CAAAC,cAAA,kBAGC;UAFCD,EAAA,CAAAE,UAAA,mBAAA+H,uDAAA;YAAA,OAASL,GAAA,CAAArB,MAAA,EAAQ;UAAA,EAAC;UAGlBvG,EAAA,CAAAY,SAAA,eAEO;UACPZ,EAAA,CAAAC,cAAA,eAA6C;UAC3CD,EAAA,CAAAW,cAAA,EAMC;UANDX,EAAA,CAAAC,cAAA,eAMC;UACCD,EAAA,CAAAY,SAAA,iBAKE;UACJZ,EAAA,CAAAU,YAAA,EAAM;UACNV,EAAA,CAAAa,eAAA,EAAM;UAANb,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAc,MAAA,aAAI;UAAAd,EAAA,CAAAU,YAAA,EAAO;UAKrBV,EAAA,CAAAC,cAAA,gBAA0C;UAKpCD,EAAA,CAAAW,cAAA,EAMC;UANDX,EAAA,CAAAC,cAAA,gBAMC;UACCD,EAAA,CAAAY,SAAA,iBAKE;UACJZ,EAAA,CAAAU,YAAA,EAAM;UAERV,EAAA,CAAAa,eAAA,EAIE;UAJFb,EAAA,CAAAY,SAAA,kBAIE;UACFZ,EAAA,CAAAC,cAAA,gBAEC;UACCD,EAAA,CAAAY,SAAA,gBAEO;UACTZ,EAAA,CAAAU,YAAA,EAAM;UAKVV,EAAA,CAAAC,cAAA,gBAA4C;UAGxCD,EAAA,CAAAE,UAAA,mBAAAgI,wDAAA;YAAA,OAASN,GAAA,CAAAnB,cAAA,EAAgB;UAAA,EAAC;UAK1BzG,EAAA,CAAAC,cAAA,gBAA2D;UACzDD,EAAA,CAAAY,SAAA,gBAEO;UAITZ,EAAA,CAAAU,YAAA,EAAM;UAENV,EAAA,CAAAY,SAAA,gBAEO;UACPZ,EAAA,CAAAC,cAAA,gBAGC;;UACCD,EAAA,CAAA8H,UAAA,MAAAK,mCAAA,gBAGK;;UACLnI,EAAA,CAAA8H,UAAA,MAAAM,mCAAA,gBAGK;;UACPpI,EAAA,CAAAU,YAAA,EAAM;UAKVV,EAAA,CAAAC,cAAA,gBAA4C;UAGtCD,EAAA,CAAAE,UAAA,mBAAAmI,wDAAA;YAAA,OAAST,GAAA,CAAA/B,cAAA,EAAgB;UAAA,EAAC;UAG1B7F,EAAA,CAAAC,cAAA,iBAAsB;UAAAD,EAAA,CAAAc,MAAA,uBAAc;UAAAd,EAAA,CAAAU,YAAA,EAAO;UAC3CV,EAAA,CAAAC,cAAA,iBAEG;UAAAD,EAAA,CAAAc,MAAA,KAAc;UAAAd,EAAA,CAAAU,YAAA,EAChB;UACDV,EAAA,CAAAC,cAAA,gBAEC;UAECD,EAAA,CAAAY,SAAA,gBAEO;UAMTZ,EAAA,CAAAU,YAAA,EAAM;UAIRV,EAAA,CAAA8H,UAAA,MAAAQ,qCAAA,kBAsBM;UACRtI,EAAA,CAAAU,YAAA,EAAM;UAMZV,EAAA,CAAAC,cAAA,iBAEC;UAGGD,EAAA,CAAAY,SAAA,gBAEO;UAITZ,EAAA,CAAAU,YAAA,EAAM;UAGNV,EAAA,CAAAC,cAAA,gBAA2B;UACzBD,EAAA,CAAAY,SAAA,sBAA+B;UACjCZ,EAAA,CAAAU,YAAA,EAAM;UAIRV,EAAA,CAAAC,cAAA,mBAEC;UAGGD,EAAA,CAAAY,SAAA,gBAEO;UAITZ,EAAA,CAAAU,YAAA,EAAM;UAENV,EAAA,CAAAC,cAAA,gBAEC;UAGKD,EAAA,CAAAW,cAAA,EAMC;UANDX,EAAA,CAAAC,cAAA,gBAMC;UACCD,EAAA,CAAAY,SAAA,iBAKE;UAOJZ,EAAA,CAAAU,YAAA,EAAM;UAENV,EAAA,CAAAa,eAAA,EAEC;UAFDb,EAAA,CAAAY,SAAA,gBAEO;UACTZ,EAAA,CAAAU,YAAA,EAAM;UACNV,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAc,MAAA,KAAwD;UAAAd,EAAA,CAAAU,YAAA,EAAO;UAO7EV,EAAA,CAAA8H,UAAA,MAAAS,qCAAA,mBAmGM;UAGNvI,EAAA,CAAA8H,UAAA,MAAAU,wCAAA,qBAyBS;UACXxI,EAAA,CAAAU,YAAA,EAAM;;;UA73BJV,EAAA,CAAAyI,WAAA,SAAAzI,EAAA,CAAA0I,WAAA,QAAAd,GAAA,CAAA3D,WAAA,EAAkC;UAyOxBjE,EAAA,CAAA2I,SAAA,IAA2C;UAA3C3I,EAAA,CAAAiC,UAAA,4BAAAjC,EAAA,CAAA4I,eAAA,KAAAC,GAAA,EAA2C;UA6BV7I,EAAA,CAAA2I,SAAA,GAAoB;UAApB3I,EAAA,CAAAiC,UAAA,SAAA2F,GAAA,CAAApE,cAAA,CAAoB;UA0WnDxD,EAAA,CAAA2I,SAAA,IAAiD;UAAjD3I,EAAA,CAAAiC,UAAA,YAAAjC,EAAA,CAAA8I,eAAA,KAAAC,GAAA,EAAA/I,EAAA,CAAA0I,WAAA,UAAAd,GAAA,CAAA3D,WAAA,GAAiD;UAG9CjE,EAAA,CAAA2I,SAAA,GAA4B;UAA5B3I,EAAA,CAAAiC,UAAA,UAAAjC,EAAA,CAAA0I,WAAA,UAAAd,GAAA,CAAA3D,WAAA,EAA4B;UAI5BjE,EAAA,CAAA2I,SAAA,GAAyB;UAAzB3I,EAAA,CAAAiC,UAAA,SAAAjC,EAAA,CAAA0I,WAAA,UAAAd,GAAA,CAAA3D,WAAA,EAAyB;UAiBzBjE,EAAA,CAAA2I,SAAA,GAAc;UAAd3I,EAAA,CAAAgJ,iBAAA,CAAApB,GAAA,CAAAtE,QAAA,CAAc;UAWbtD,EAAA,CAAA2I,SAAA,GAAoB;UAApB3I,EAAA,CAAAiC,UAAA,QAAA2F,GAAA,CAAArE,YAAA,EAAAvD,EAAA,CAAAiJ,aAAA,CAAoB;UAQvBjJ,EAAA,CAAA2I,SAAA,GAAkB;UAAlB3I,EAAA,CAAAiC,UAAA,SAAA2F,GAAA,CAAAnE,YAAA,CAAkB;UA2FjBzD,EAAA,CAAA2I,SAAA,IAAwD;UAAxD3I,EAAA,CAAAkJ,kBAAA,YAAAtB,GAAA,CAAAhE,WAAA,qCAAwD;UAOhE5D,EAAA,CAAA2I,SAAA,GAAqB;UAArB3I,EAAA,CAAAiC,UAAA,SAAA2F,GAAA,CAAAlE,eAAA,CAAqB;UAuGxB1D,EAAA,CAAA2I,SAAA,GAAsB;UAAtB3I,EAAA,CAAAiC,UAAA,SAAA2F,GAAA,CAAAjE,gBAAA,CAAsB;;;;;;mBDv1Bb,CACVhE,OAAO,CAAC,QAAQ,EAAE,CAChBC,UAAU,CAAC,QAAQ,EAAE,CACnBC,KAAK,CAAC;UAAEsJ,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAmB,CAAE,CAAC,EACrDtJ,OAAO,CACL,gBAAgB,EAChBD,KAAK,CAAC;UAAEsJ,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAe,CAAE,CAAC,CAClD,CACF,CAAC,EACFxJ,UAAU,CAAC,QAAQ,EAAE,CACnBE,OAAO,CACL,eAAe,EACfD,KAAK,CAAC;UAAEsJ,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAmB,CAAE,CAAC,CACtD,CACF,CAAC,CACH,CAAC;MACH;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}