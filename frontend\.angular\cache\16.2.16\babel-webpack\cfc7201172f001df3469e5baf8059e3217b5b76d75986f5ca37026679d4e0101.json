{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@app/services/data.service\";\nimport * as i3 from \"@app/services/planning.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@app/services/toast.service\";\nfunction PlanningFormComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.errorMessage, \" \");\n  }\n}\nfunction PlanningFormComponent_div_20_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Le titre est obligatoire\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PlanningFormComponent_div_20_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Au moins 3 caract\\u00E8res requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PlanningFormComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵelement(1, \"i\", 51);\n    i0.ɵɵtemplate(2, PlanningFormComponent_div_20_span_2_Template, 2, 0, \"span\", 52);\n    i0.ɵɵtemplate(3, PlanningFormComponent_div_20_span_3_Template, 2, 0, \"span\", 52);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r1.planningForm.get(\"titre\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r1.planningForm.get(\"titre\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"minlength\"]);\n  }\n}\nfunction PlanningFormComponent_option_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r8 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r8._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", user_r8.username, \" \");\n  }\n}\nfunction PlanningFormComponent_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵelement(1, \"i\", 51);\n    i0.ɵɵtext(2, \" Veuillez s\\u00E9lectionner au moins un participant \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PlanningFormComponent_i_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 54);\n  }\n}\nfunction PlanningFormComponent_i_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 55);\n  }\n}\nexport let PlanningFormComponent = /*#__PURE__*/(() => {\n  class PlanningFormComponent {\n    constructor(fb, userService, planningService, router, toastService) {\n      this.fb = fb;\n      this.userService = userService;\n      this.planningService = planningService;\n      this.router = router;\n      this.toastService = toastService;\n      this.isLoading = false;\n      this.errorMessage = null;\n      this.users$ = this.userService.getAllUsers();\n    }\n    ngOnInit() {\n      this.planningForm = this.fb.group({\n        titre: ['', [Validators.required, Validators.minLength(3)]],\n        description: [''],\n        lieu: [''],\n        dateDebut: ['', Validators.required],\n        dateFin: ['', Validators.required],\n        participants: [[], Validators.required]\n      });\n    }\n    submit() {\n      console.log('Submit method called');\n      console.log('Form valid:', this.planningForm.valid);\n      console.log('Form values:', this.planningForm.value);\n      if (this.planningForm.valid) {\n        this.isLoading = true;\n        this.errorMessage = null;\n        // Extract form values\n        const formValues = this.planningForm.value;\n        // Create a simplified planning object with just the fields the API expects\n        const planningData = {\n          titre: formValues.titre,\n          description: formValues.description || '',\n          dateDebut: formValues.dateDebut,\n          dateFin: formValues.dateFin,\n          lieu: formValues.lieu || '',\n          participants: formValues.participants || []\n        };\n        console.log('Planning data to submit:', planningData);\n        // Call the createPlanning method to add the new planning\n        this.planningService.createPlanning(planningData).subscribe({\n          next: newPlanning => {\n            console.log('Planning created successfully:', newPlanning);\n            this.isLoading = false;\n            // Afficher un toast de succès\n            this.toastService.showSuccess('Le planning a été créé avec succès');\n            // Navigate to plannings list page after successful creation\n            this.router.navigate(['/plannings']);\n          },\n          error: error => {\n            console.error('Error creating planning:', error);\n            console.error('Error details:', error.error || error.message || error);\n            this.isLoading = false;\n            // Gestion spécifique des erreurs d'autorisation\n            if (error.status === 403) {\n              this.toastService.showError(\"Accès refusé : vous n'avez pas les droits pour créer un planning\");\n            } else if (error.status === 401) {\n              this.toastService.showError('Vous devez être connecté pour créer un planning');\n            } else {\n              // Autres erreurs\n              const errorMessage = error.error?.message || 'Une erreur est survenue lors de la création du planning';\n              this.toastService.showError(errorMessage, 8000);\n            }\n          }\n        });\n      } else {\n        console.log('Form validation errors:', this.getFormValidationErrors());\n        // Marquer tous les champs comme \"touched\" pour afficher les erreurs\n        this.markFormGroupTouched();\n        this.toastService.showWarning('Veuillez corriger les erreurs avant de soumettre le formulaire');\n      }\n    }\n    // Helper method to get form validation errors\n    getFormValidationErrors() {\n      const errors = {};\n      Object.keys(this.planningForm.controls).forEach(key => {\n        const control = this.planningForm.get(key);\n        if (control && control.errors) {\n          errors[key] = control.errors;\n        }\n      });\n      return errors;\n    }\n    // Marquer tous les champs comme \"touched\" pour déclencher l'affichage des erreurs\n    markFormGroupTouched() {\n      Object.keys(this.planningForm.controls).forEach(key => {\n        const control = this.planningForm.get(key);\n        if (control) {\n          control.markAsTouched();\n        }\n      });\n    }\n    static {\n      this.ɵfac = function PlanningFormComponent_Factory(t) {\n        return new (t || PlanningFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.DataService), i0.ɵɵdirectiveInject(i3.PlanningService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.ToastService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: PlanningFormComponent,\n        selectors: [[\"app-planning-form\"]],\n        decls: 71,\n        vars: 13,\n        consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-6\", \"max-w-3xl\"], [1, \"bg-gradient-to-r\", \"from-purple-600\", \"to-indigo-600\", \"rounded-t-lg\", \"p-6\", \"text-white\", \"mb-0\"], [1, \"text-2xl\", \"font-bold\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-calendar-plus\", \"mr-3\", \"text-purple-200\"], [1, \"text-purple-100\", \"mt-2\"], [\"novalidate\", \"\", 1, \"bg-white\", \"rounded-b-lg\", \"shadow-lg\", \"p-6\", \"border-t-0\", 3, \"formGroup\", \"ngSubmit\"], [\"class\", \"mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\", 4, \"ngIf\"], [1, \"grid\", \"grid-cols-1\", \"gap-6\"], [1, \"bg-gradient-to-r\", \"from-purple-50\", \"to-pink-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-purple-200\"], [1, \"text-lg\", \"font-semibold\", \"text-purple-800\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-info-circle\", \"mr-2\", \"text-purple-600\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-6\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-purple-700\", \"mb-2\"], [1, \"fas\", \"fa-tag\", \"mr-2\", \"text-purple-500\"], [\"type\", \"text\", \"formControlName\", \"titre\", \"placeholder\", \"Nom de votre planning...\", 1, \"mt-1\", \"block\", \"w-full\", \"px-4\", \"py-3\", \"border-2\", \"border-purple-200\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-purple-500\", \"focus:border-purple-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\"], [\"class\", \"text-red-500 text-sm mt-2 flex items-center\", 4, \"ngIf\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-orange-700\", \"mb-2\"], [1, \"fas\", \"fa-map-marker-alt\", \"mr-2\", \"text-orange-500\"], [\"type\", \"text\", \"formControlName\", \"lieu\", \"placeholder\", \"Salle, bureau, lieu de l'\\u00E9v\\u00E9nement...\", 1, \"mt-1\", \"block\", \"w-full\", \"px-4\", \"py-3\", \"border-2\", \"border-orange-200\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-orange-500\", \"focus:border-orange-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\"], [1, \"bg-gradient-to-r\", \"from-blue-50\", \"to-cyan-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-blue-200\"], [1, \"text-lg\", \"font-semibold\", \"text-blue-800\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-calendar-week\", \"mr-2\", \"text-blue-600\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-green-700\", \"mb-2\"], [1, \"fas\", \"fa-calendar-day\", \"mr-2\", \"text-green-500\"], [\"type\", \"date\", \"formControlName\", \"dateDebut\", 1, \"mt-1\", \"block\", \"w-full\", \"px-4\", \"py-3\", \"border-2\", \"border-green-200\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-green-500\", \"focus:border-green-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-red-700\", \"mb-2\"], [1, \"fas\", \"fa-calendar-check\", \"mr-2\", \"text-red-500\"], [\"type\", \"date\", \"formControlName\", \"dateFin\", 1, \"mt-1\", \"block\", \"w-full\", \"px-4\", \"py-3\", \"border-2\", \"border-red-200\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-red-500\", \"focus:border-red-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\"], [1, \"bg-gradient-to-r\", \"from-emerald-50\", \"to-teal-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-emerald-200\"], [1, \"text-lg\", \"font-semibold\", \"text-emerald-800\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-users\", \"mr-2\", \"text-emerald-600\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-emerald-700\", \"mb-2\"], [1, \"fas\", \"fa-user-friends\", \"mr-2\", \"text-emerald-500\"], [\"formControlName\", \"participants\", \"multiple\", \"\", 1, \"mt-1\", \"block\", \"w-full\", \"px-4\", \"py-3\", \"border-2\", \"border-emerald-200\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-emerald-500\", \"focus:border-emerald-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"text-sm\", \"min-h-[120px]\"], [\"class\", \"py-2\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"text-xs\", \"text-emerald-600\", \"mt-2\"], [1, \"fas\", \"fa-info-circle\", \"mr-1\"], [1, \"bg-gradient-to-r\", \"from-indigo-50\", \"to-purple-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-indigo-200\"], [1, \"text-lg\", \"font-semibold\", \"text-indigo-800\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-align-left\", \"mr-2\", \"text-indigo-600\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-indigo-700\", \"mb-2\"], [1, \"fas\", \"fa-edit\", \"mr-2\", \"text-indigo-500\"], [\"formControlName\", \"description\", \"rows\", \"4\", \"placeholder\", \"D\\u00E9crivez les objectifs, le contexte ou les d\\u00E9tails de ce planning...\", 1, \"mt-1\", \"block\", \"w-full\", \"px-4\", \"py-3\", \"border-2\", \"border-indigo-200\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-indigo-500\", \"focus:border-indigo-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\"], [1, \"mt-8\", \"flex\", \"justify-end\", \"space-x-4\", \"bg-gray-50\", \"p-4\", \"rounded-lg\", \"border-t\", \"border-gray-200\"], [\"type\", \"button\", \"routerLink\", \"/plannings\", 1, \"px-6\", \"py-3\", \"border-2\", \"border-gray-300\", \"rounded-lg\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"hover:bg-gray-100\", \"hover:border-gray-400\", \"transition-all\", \"duration-200\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-times\", \"mr-2\"], [\"type\", \"button\", 1, \"px-6\", \"py-3\", \"rounded-lg\", \"text-sm\", \"font-medium\", \"text-white\", \"bg-gradient-to-r\", \"from-purple-600\", \"to-indigo-600\", \"hover:from-purple-700\", \"hover:to-indigo-700\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"transition-all\", \"duration-200\", \"flex\", \"items-center\", \"shadow-lg\", 3, \"disabled\", \"click\"], [\"class\", \"fas fa-save mr-2\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner fa-spin mr-2\", 4, \"ngIf\"], [1, \"mb-4\", \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\"], [1, \"text-red-500\", \"text-sm\", \"mt-2\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-exclamation-circle\", \"mr-1\"], [4, \"ngIf\"], [1, \"py-2\", 3, \"value\"], [1, \"fas\", \"fa-save\", \"mr-2\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"mr-2\"]],\n        template: function PlanningFormComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\", 2);\n            i0.ɵɵelement(3, \"i\", 3);\n            i0.ɵɵtext(4, \" Nouveau Planning \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"p\", 4);\n            i0.ɵɵtext(6, \"Cr\\u00E9ez un nouveau planning pour organiser vos \\u00E9v\\u00E9nements\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"form\", 5);\n            i0.ɵɵlistener(\"ngSubmit\", function PlanningFormComponent_Template_form_ngSubmit_7_listener() {\n              return ctx.submit();\n            });\n            i0.ɵɵtemplate(8, PlanningFormComponent_div_8_Template, 2, 1, \"div\", 6);\n            i0.ɵɵelementStart(9, \"div\", 7)(10, \"div\", 8)(11, \"h3\", 9);\n            i0.ɵɵelement(12, \"i\", 10);\n            i0.ɵɵtext(13, \" Informations g\\u00E9n\\u00E9rales \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(14, \"div\", 11)(15, \"div\")(16, \"label\", 12);\n            i0.ɵɵelement(17, \"i\", 13);\n            i0.ɵɵtext(18, \" Titre * \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(19, \"input\", 14);\n            i0.ɵɵtemplate(20, PlanningFormComponent_div_20_Template, 4, 2, \"div\", 15);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"div\")(22, \"label\", 16);\n            i0.ɵɵelement(23, \"i\", 17);\n            i0.ɵɵtext(24, \" Lieu / Salle \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(25, \"input\", 18);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(26, \"div\", 19)(27, \"h3\", 20);\n            i0.ɵɵelement(28, \"i\", 21);\n            i0.ɵɵtext(29, \" P\\u00E9riode du planning \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(30, \"div\", 11)(31, \"div\")(32, \"label\", 22);\n            i0.ɵɵelement(33, \"i\", 23);\n            i0.ɵɵtext(34, \" Date de d\\u00E9but * \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(35, \"input\", 24);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(36, \"div\")(37, \"label\", 25);\n            i0.ɵɵelement(38, \"i\", 26);\n            i0.ɵɵtext(39, \" Date de fin * \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(40, \"input\", 27);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(41, \"div\", 28)(42, \"h3\", 29);\n            i0.ɵɵelement(43, \"i\", 30);\n            i0.ɵɵtext(44, \" Participants \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(45, \"label\", 31);\n            i0.ɵɵelement(46, \"i\", 32);\n            i0.ɵɵtext(47, \" S\\u00E9lectionnez les participants * \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(48, \"select\", 33);\n            i0.ɵɵtemplate(49, PlanningFormComponent_option_49_Template, 2, 2, \"option\", 34);\n            i0.ɵɵpipe(50, \"async\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(51, PlanningFormComponent_div_51_Template, 3, 0, \"div\", 15);\n            i0.ɵɵelementStart(52, \"p\", 35);\n            i0.ɵɵelement(53, \"i\", 36);\n            i0.ɵɵtext(54, \" Maintenez Ctrl (ou Cmd) pour s\\u00E9lectionner plusieurs participants \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(55, \"div\", 37)(56, \"h3\", 38);\n            i0.ɵɵelement(57, \"i\", 39);\n            i0.ɵɵtext(58, \" Description \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(59, \"label\", 40);\n            i0.ɵɵelement(60, \"i\", 41);\n            i0.ɵɵtext(61, \" D\\u00E9crivez votre planning \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(62, \"textarea\", 42);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(63, \"div\", 43)(64, \"button\", 44);\n            i0.ɵɵelement(65, \"i\", 45);\n            i0.ɵɵtext(66, \" Annuler \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(67, \"button\", 46);\n            i0.ɵɵlistener(\"click\", function PlanningFormComponent_Template_button_click_67_listener() {\n              return ctx.submit();\n            });\n            i0.ɵɵtemplate(68, PlanningFormComponent_i_68_Template, 1, 0, \"i\", 47);\n            i0.ɵɵtemplate(69, PlanningFormComponent_i_69_Template, 1, 0, \"i\", 48);\n            i0.ɵɵtext(70);\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            let tmp_2_0;\n            let tmp_3_0;\n            let tmp_5_0;\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"formGroup\", ctx.planningForm);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n            i0.ɵɵadvance(11);\n            i0.ɵɵclassProp(\"border-red-300\", ((tmp_2_0 = ctx.planningForm.get(\"titre\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.planningForm.get(\"titre\")) == null ? null : tmp_2_0.touched));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx.planningForm.get(\"titre\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.planningForm.get(\"titre\")) == null ? null : tmp_3_0.touched));\n            i0.ɵɵadvance(29);\n            i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(50, 11, ctx.users$));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.planningForm.get(\"participants\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.planningForm.get(\"participants\")) == null ? null : tmp_5_0.touched));\n            i0.ɵɵadvance(16);\n            i0.ɵɵproperty(\"disabled\", ctx.isLoading || ctx.planningForm.invalid);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵtextInterpolate1(\" \", ctx.isLoading ? \"Enregistrement...\" : \"Cr\\u00E9er le planning\", \" \");\n          }\n        }\n      });\n    }\n  }\n  return PlanningFormComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}