{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { EquipeListComponent } from './equipe-list/equipe-list.component';\nimport { EquipeFormComponent } from './equipe-form/equipe-form.component';\nimport { EquipeDetailComponent } from './equipe-detail/equipe-detail.component';\nimport { TaskListComponent } from './task-list/task-list.component';\nimport { EquipeLayoutComponent } from './equipe-layout/equipe-layout.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: EquipeLayoutComponent,\n  children: [\n  // Liste des équipes\n  {\n    path: '',\n    component: EquipeListComponent\n  }, {\n    path: 'liste',\n    component: EquipeListComponent\n  }, {\n    path: 'mes-equipes',\n    component: EquipeListComponent\n  },\n  // Formulaire pour ajouter une nouvelle équipe\n  {\n    path: 'ajouter',\n    component: EquipeFormComponent\n  }, {\n    path: 'nouveau',\n    component: EquipeFormComponent\n  },\n  // Formulaire pour modifier une équipe existante\n  {\n    path: 'modifier/:id',\n    component: EquipeFormComponent\n  },\n  // Détails d'une équipe spécifique\n  {\n    path: 'detail/:id',\n    component: EquipeDetailComponent\n  },\n  // Gestion des tâches d'une équipe\n  {\n    path: 'tasks/:id',\n    component: TaskListComponent\n  }]\n}];\nexport class EquipesRoutingModule {\n  static {\n    this.ɵfac = function EquipesRoutingModule_Factory(t) {\n      return new (t || EquipesRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: EquipesRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(EquipesRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "EquipeListComponent", "EquipeFormComponent", "EquipeDetailComponent", "TaskListComponent", "EquipeLayoutComponent", "routes", "path", "component", "children", "EquipesRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\equipes\\equipes-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { EquipeListComponent } from './equipe-list/equipe-list.component';\nimport { EquipeFormComponent } from './equipe-form/equipe-form.component';\nimport { EquipeDetailComponent } from './equipe-detail/equipe-detail.component';\nimport { TaskListComponent } from './task-list/task-list.component';\nimport { EquipeComponent } from './equipe/equipe.component';\nimport { EquipeLayoutComponent } from './equipe-layout/equipe-layout.component';\n\nconst routes: Routes = [\n  {\n    path: '',\n    component: EquipeLayoutComponent,\n    children: [\n      // Liste des équipes\n  \n      { path: '', component: EquipeListComponent },\n\n      { path: 'liste', component: EquipeListComponent },\n      { path: 'mes-equipes', component: EquipeListComponent },\n\n      // Formulaire pour ajouter une nouvelle équipe\n      { path: 'ajouter', component: EquipeFormComponent },\n      { path: 'nouveau', component: EquipeFormComponent },\n\n      // Formulaire pour modifier une équipe existante\n      { path: 'modifier/:id', component: EquipeFormComponent },\n\n      // Détails d'une équipe spécifique\n      { path: 'detail/:id', component: EquipeDetailComponent },\n\n      // Gestion des tâches d'une équipe\n      { path: 'tasks/:id', component: TaskListComponent },\n    ],\n  },\n];\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule],\n})\nexport class EquipesRoutingModule {}\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,iBAAiB,QAAQ,iCAAiC;AAEnE,SAASC,qBAAqB,QAAQ,yCAAyC;;;AAE/E,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH,qBAAqB;EAChCI,QAAQ,EAAE;EACR;EAEA;IAAEF,IAAI,EAAE,EAAE;IAAEC,SAAS,EAAEP;EAAmB,CAAE,EAE5C;IAAEM,IAAI,EAAE,OAAO;IAAEC,SAAS,EAAEP;EAAmB,CAAE,EACjD;IAAEM,IAAI,EAAE,aAAa;IAAEC,SAAS,EAAEP;EAAmB,CAAE;EAEvD;EACA;IAAEM,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAEN;EAAmB,CAAE,EACnD;IAAEK,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAEN;EAAmB,CAAE;EAEnD;EACA;IAAEK,IAAI,EAAE,cAAc;IAAEC,SAAS,EAAEN;EAAmB,CAAE;EAExD;EACA;IAAEK,IAAI,EAAE,YAAY;IAAEC,SAAS,EAAEL;EAAqB,CAAE;EAExD;EACA;IAAEI,IAAI,EAAE,WAAW;IAAEC,SAAS,EAAEJ;EAAiB,CAAE;CAEtD,CACF;AAMD,OAAM,MAAOM,oBAAoB;;;uBAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;gBAHrBV,YAAY,CAACW,QAAQ,CAACL,MAAM,CAAC,EAC7BN,YAAY;IAAA;EAAA;;;2EAEXU,oBAAoB;IAAAE,OAAA,GAAAC,EAAA,CAAAb,YAAA;IAAAc,OAAA,GAFrBd,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}