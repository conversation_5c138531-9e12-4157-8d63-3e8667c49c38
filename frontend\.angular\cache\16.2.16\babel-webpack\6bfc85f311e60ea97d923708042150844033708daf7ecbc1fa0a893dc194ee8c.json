{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class TimeAgoPipe {\n  transform(value) {\n    if (!value) return '';\n    const date = new Date(value);\n    const now = new Date();\n    const diffInMs = now.getTime() - date.getTime();\n    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));\n    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));\n    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));\n    if (diffInMinutes < 1) {\n      return 'À l\\'instant';\n    } else if (diffInMinutes < 60) {\n      return `Il y a ${diffInMinutes} min`;\n    } else if (diffInHours < 24) {\n      return `Il y a ${diffInHours}h`;\n    } else if (diffInDays < 7) {\n      return `Il y a ${diffInDays} jour${diffInDays > 1 ? 's' : ''}`;\n    } else {\n      return date.toLocaleDateString('fr-FR');\n    }\n  }\n  static {\n    this.ɵfac = function TimeAgoPipe_Factory(t) {\n      return new (t || TimeAgoPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"timeAgo\",\n      type: TimeAgoPipe,\n      pure: true\n    });\n  }\n}", "map": {"version": 3, "names": ["TimeAgoPipe", "transform", "value", "date", "Date", "now", "diffInMs", "getTime", "diffInMinutes", "Math", "floor", "diffInHours", "diffInDays", "toLocaleDateString", "pure"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\shared\\pipes\\time-ago.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\n\n@Pipe({\n  name: 'timeAgo'\n})\nexport class TimeAgoPipe implements PipeTransform {\n\n  transform(value: Date | string | number): string {\n    if (!value) return '';\n\n    const date = new Date(value);\n    const now = new Date();\n    const diffInMs = now.getTime() - date.getTime();\n    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));\n    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));\n    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));\n\n    if (diffInMinutes < 1) {\n      return 'À l\\'instant';\n    } else if (diffInMinutes < 60) {\n      return `Il y a ${diffInMinutes} min`;\n    } else if (diffInHours < 24) {\n      return `Il y a ${diffInHours}h`;\n    } else if (diffInDays < 7) {\n      return `Il y a ${diffInDays} jour${diffInDays > 1 ? 's' : ''}`;\n    } else {\n      return date.toLocaleDateString('fr-FR');\n    }\n  }\n\n}\n"], "mappings": ";AAKA,OAAM,MAAOA,WAAW;EAEtBC,SAASA,CAACC,KAA6B;IACrC,IAAI,CAACA,KAAK,EAAE,OAAO,EAAE;IAErB,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,KAAK,CAAC;IAC5B,MAAMG,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAME,QAAQ,GAAGD,GAAG,CAACE,OAAO,EAAE,GAAGJ,IAAI,CAACI,OAAO,EAAE;IAC/C,MAAMC,aAAa,GAAGC,IAAI,CAACC,KAAK,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;IACxD,MAAMK,WAAW,GAAGF,IAAI,CAACC,KAAK,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC3D,MAAMM,UAAU,GAAGH,IAAI,CAACC,KAAK,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE/D,IAAIE,aAAa,GAAG,CAAC,EAAE;MACrB,OAAO,cAAc;KACtB,MAAM,IAAIA,aAAa,GAAG,EAAE,EAAE;MAC7B,OAAO,UAAUA,aAAa,MAAM;KACrC,MAAM,IAAIG,WAAW,GAAG,EAAE,EAAE;MAC3B,OAAO,UAAUA,WAAW,GAAG;KAChC,MAAM,IAAIC,UAAU,GAAG,CAAC,EAAE;MACzB,OAAO,UAAUA,UAAU,QAAQA,UAAU,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE;KAC/D,MAAM;MACL,OAAOT,IAAI,CAACU,kBAAkB,CAAC,OAAO,CAAC;;EAE3C;;;uBAvBWb,WAAW;IAAA;EAAA;;;;YAAXA,WAAW;MAAAc,IAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}