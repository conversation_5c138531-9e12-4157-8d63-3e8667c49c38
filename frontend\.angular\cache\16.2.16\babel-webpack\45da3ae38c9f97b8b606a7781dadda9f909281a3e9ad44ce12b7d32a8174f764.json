{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { MessageChatComponent } from './message-chat/message-chat.component';\nimport { MessageLayoutComponent } from './message-layout/message-layout.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: MessageLayoutComponent,\n  children: [\n  // Route par défaut - affiche le layout sans conversation sélectionnée\n  {\n    path: '',\n    component: MessageChatComponent,\n    data: {\n      title: 'Messages'\n    }\n  },\n  // Route pour une conversation spécifique\n  {\n    path: ':conversationId',\n    component: MessageChatComponent,\n    data: {\n      title: 'Chat'\n    }\n  }]\n}];\nexport let MessagesRoutingModule = /*#__PURE__*/(() => {\n  class MessagesRoutingModule {\n    static {\n      this.ɵfac = function MessagesRoutingModule_Factory(t) {\n        return new (t || MessagesRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: MessagesRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return MessagesRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}