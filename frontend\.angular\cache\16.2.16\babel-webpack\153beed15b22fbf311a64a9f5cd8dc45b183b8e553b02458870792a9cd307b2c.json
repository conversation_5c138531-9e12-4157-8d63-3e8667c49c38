{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@app/services/reunion.service\";\nimport * as i4 from \"@app/services/data.service\";\nimport * as i5 from \"@app/services/planning.service\";\nimport * as i6 from \"@app/services/toast.service\";\nimport * as i7 from \"@app/services/authuser.service\";\nimport * as i8 from \"@app/services/role.service\";\nfunction ReunionEditComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.error.message || \"Une erreur est survenue\", \" \");\n  }\n}\nfunction ReunionEditComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵelement(1, \"i\", 64);\n    i0.ɵɵtext(2, \" Le titre est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionEditComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵelement(1, \"i\", 64);\n    i0.ɵɵtext(2, \" La date est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionEditComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵelement(1, \"i\", 64);\n    i0.ɵɵtext(2, \" L'heure de d\\u00E9but est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionEditComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵelement(1, \"i\", 64);\n    i0.ɵɵtext(2, \" L'heure de fin est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionEditComponent_div_62_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵelement(1, \"i\", 55);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.currentReunionPlanning.description, \" \");\n  }\n}\nfunction ReunionEditComponent_div_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"div\", 66)(2, \"span\", 67);\n    i0.ɵɵelement(3, \"i\", 68);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 69);\n    i0.ɵɵelement(6, \"i\", 70);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"date\");\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(10, ReunionEditComponent_div_62_div_10_Template, 3, 1, \"div\", 71);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.currentReunionPlanning.titre, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind2(8, 4, ctx_r5.currentReunionPlanning.dateDebut, \"dd/MM/yyyy\"), \" - \", i0.ɵɵpipeBind2(9, 7, ctx_r5.currentReunionPlanning.dateFin, \"dd/MM/yyyy\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.currentReunionPlanning.description);\n  }\n}\nfunction ReunionEditComponent_option_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 73);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const planning_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", planning_r11._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(planning_r11.titre);\n  }\n}\nfunction ReunionEditComponent_ng_container_79_option_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 75);\n    i0.ɵɵelement(1, \"i\", 76);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r13._id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", user_r13.username, \" \");\n  }\n}\nfunction ReunionEditComponent_ng_container_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ReunionEditComponent_ng_container_79_option_1_Template, 3, 2, \"option\", 74);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.users);\n  }\n}\nfunction ReunionEditComponent_i_88_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 77);\n  }\n}\nfunction ReunionEditComponent_i_89_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 78);\n  }\n}\nexport class ReunionEditComponent {\n  constructor(fb, route, router, reunionService, userService, planningService, toastService, authService, roleService) {\n    this.fb = fb;\n    this.route = route;\n    this.router = router;\n    this.reunionService = reunionService;\n    this.userService = userService;\n    this.planningService = planningService;\n    this.toastService = toastService;\n    this.authService = authService;\n    this.roleService = roleService;\n    this.error = null;\n    this.isSubmitting = false;\n    this.users = [];\n    this.plannings = [];\n    this.currentReunionPlanning = null;\n    this.isAdmin = false;\n  }\n  ngOnInit() {\n    this.reunionId = this.route.snapshot.paramMap.get('id');\n    this.checkUserRole();\n    this.initForm();\n    this.fetchUsers();\n    this.fetchPlannings();\n    this.loadReunion();\n  }\n  checkUserRole() {\n    this.isAdmin = this.roleService.isAdmin();\n    console.log('🔍 Utilisateur admin:', this.isAdmin);\n  }\n  initForm() {\n    this.reunionForm = this.fb.group({\n      titre: ['', Validators.required],\n      description: [''],\n      date: ['', Validators.required],\n      heureDebut: ['', Validators.required],\n      heureFin: ['', Validators.required],\n      lieu: [''],\n      lienVisio: [''],\n      planning: ['', Validators.required],\n      participants: [[]]\n    });\n  }\n  fetchUsers() {\n    this.userService.getAllUsers().subscribe(users => {\n      this.users = users;\n    });\n  }\n  fetchPlannings() {\n    const userId = this.authService.getCurrentUserId();\n    if (!userId) return;\n    // Si admin, récupérer tous les plannings, sinon seulement ceux de l'utilisateur\n    const planningsObservable = this.isAdmin ? this.planningService.getAllPlanningsAdmin() : this.planningService.getPlanningsByUser(userId);\n    planningsObservable.subscribe({\n      next: response => {\n        // Adapter la réponse selon l'endpoint utilisé\n        if (this.isAdmin) {\n          this.plannings = response.data || [];\n          console.log('🔍 Tous les plannings (admin) récupérés:', this.plannings);\n        } else {\n          this.plannings = response.plannings || [];\n          console.log('🔍 Plannings utilisateur récupérés:', this.plannings);\n        }\n      },\n      error: err => {\n        console.error('❌ Erreur chargement plannings:', err);\n        this.toastService.error('Erreur', 'Impossible de récupérer les plannings');\n      }\n    });\n  }\n  loadReunion() {\n    this.reunionService.getReunionById(this.reunionId).subscribe({\n      next: reunion => {\n        // Stocker le planning actuel de la réunion\n        this.currentReunionPlanning = reunion.reunion.planning;\n        this.reunionForm.patchValue({\n          titre: reunion.reunion.titre,\n          description: reunion.reunion.description,\n          date: reunion.reunion.date?.split('T')[0],\n          heureDebut: reunion.reunion.heureDebut,\n          heureFin: reunion.reunion.heureFin,\n          lieu: reunion.reunion.lieu,\n          lienVisio: reunion.reunion.lienVisio,\n          planning: reunion.reunion.planning?.id || reunion.reunion.planning?._id,\n          participants: reunion.reunion.participants?.map(p => p._id)\n        });\n        // Désactiver le champ planning en mode édition\n        this.reunionForm.get('planning')?.disable();\n        console.log('🔍 Réunion chargée:', reunion.reunion);\n        console.log('🔍 Planning actuel:', this.currentReunionPlanning);\n      },\n      error: err => {\n        console.error('Erreur lors du chargement de la réunion:', err);\n        if (err.status === 403) {\n          this.toastService.accessDenied('accéder à cette réunion', err.status);\n        } else if (err.status === 404) {\n          this.toastService.error('Réunion introuvable', 'La réunion demandée n\\'existe pas ou a été supprimée');\n        } else {\n          const errorMessage = err.error?.message || 'Erreur lors du chargement de la réunion';\n          this.toastService.error('Erreur de chargement', errorMessage);\n        }\n      }\n    });\n  }\n  onSubmit() {\n    if (this.reunionForm.invalid) {\n      this.toastService.warning('Formulaire invalide', 'Veuillez corriger les erreurs avant de soumettre le formulaire');\n      return;\n    }\n    // Validation de la date par rapport au planning\n    if (!this.validateDateInPlanningRange()) {\n      return;\n    }\n    this.isSubmitting = true;\n    const reunion = this.reunionForm.value;\n    // Inclure le planning même s'il est désactivé\n    if (this.currentReunionPlanning) {\n      reunion.planning = this.currentReunionPlanning._id || this.currentReunionPlanning.id;\n    }\n    console.log('🔍 Données de la réunion à mettre à jour:', reunion);\n    this.reunionService.updateReunion(this.reunionId, reunion).subscribe({\n      next: () => {\n        this.isSubmitting = false;\n        this.toastService.success('Réunion mise à jour', 'La réunion a été modifiée avec succès');\n        this.router.navigate(['/reunions']);\n      },\n      error: err => {\n        this.isSubmitting = false;\n        console.error('Erreur lors de la mise à jour de la réunion:', err);\n        if (err.status === 403) {\n          this.toastService.accessDenied('modifier cette réunion', err.status);\n        } else if (err.status === 401) {\n          this.toastService.error('Non autorisé', 'Vous devez être connecté pour effectuer cette action');\n        } else {\n          const errorMessage = err.error?.message || 'Erreur lors de la mise à jour de la réunion';\n          this.toastService.error('Erreur de mise à jour', errorMessage, 8000);\n        }\n      }\n    });\n  }\n  goReunion() {\n    this.router.navigate(['/reunions']);\n  }\n  /**\n   * Valide que la date de la réunion est dans l'intervalle du planning sélectionné\n   */\n  validateDateInPlanningRange() {\n    const formValue = this.reunionForm.value;\n    const reunionDate = formValue.date;\n    const planningId = formValue.planning;\n    if (!reunionDate || !planningId) {\n      return true; // Si pas de date ou planning, laisser la validation backend gérer\n    }\n    // Trouver le planning sélectionné\n    const selectedPlanning = this.plannings.find(p => p._id === planningId);\n    if (!selectedPlanning) {\n      console.warn('⚠️ Planning non trouvé dans la liste locale, tentative de récupération depuis le serveur');\n      // Si le planning n'est pas trouvé dans la liste locale, essayer de le récupérer\n      // Cela peut arriver si l'utilisateur modifie une réunion d'un planning dont il n'est que participant\n      this.planningService.getPlanningById(planningId).subscribe({\n        next: response => {\n          const planning = response.planning;\n          if (planning) {\n            // Ajouter le planning à la liste locale pour éviter de futures requêtes\n            this.plannings.push(planning);\n            console.log('✅ Planning récupéré et ajouté à la liste locale:', planning);\n          }\n        },\n        error: err => {\n          console.error('❌ Erreur lors de la récupération du planning:', err);\n          this.toastService.error('Planning introuvable', 'Le planning sélectionné n\\'existe pas ou vous n\\'avez pas les permissions pour y accéder');\n        }\n      });\n      // Pour cette validation, on retourne true et on laisse le backend gérer\n      return true;\n    }\n    // Convertir les dates pour comparaison\n    const reunionDateObj = new Date(reunionDate);\n    const planningDateDebut = new Date(selectedPlanning.dateDebut);\n    const planningDateFin = new Date(selectedPlanning.dateFin);\n    // Comparer seulement les dates (sans les heures)\n    reunionDateObj.setHours(0, 0, 0, 0);\n    planningDateDebut.setHours(0, 0, 0, 0);\n    planningDateFin.setHours(0, 0, 0, 0);\n    if (reunionDateObj < planningDateDebut || reunionDateObj > planningDateFin) {\n      this.toastService.error('Date invalide', `La date de la réunion doit être comprise entre le ${planningDateDebut.toLocaleDateString('fr-FR')} et le ${planningDateFin.toLocaleDateString('fr-FR')} (période du planning \"${selectedPlanning.titre}\")`, 10000);\n      return false;\n    }\n    return true;\n  }\n  static {\n    this.ɵfac = function ReunionEditComponent_Factory(t) {\n      return new (t || ReunionEditComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.ReunionService), i0.ɵɵdirectiveInject(i4.DataService), i0.ɵɵdirectiveInject(i5.PlanningService), i0.ɵɵdirectiveInject(i6.ToastService), i0.ɵɵdirectiveInject(i7.AuthuserService), i0.ɵɵdirectiveInject(i8.RoleService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ReunionEditComponent,\n      selectors: [[\"app-reunion-edit\"]],\n      decls: 91,\n      vars: 13,\n      consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-6\", \"max-w-3xl\"], [1, \"bg-gradient-to-r\", \"from-purple-600\", \"to-indigo-600\", \"rounded-t-lg\", \"p-6\", \"text-white\", \"mb-0\"], [1, \"text-2xl\", \"font-bold\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-edit\", \"mr-3\", \"text-purple-200\"], [1, \"text-purple-100\", \"mt-2\"], [1, \"bg-white\", \"rounded-b-lg\", \"shadow-lg\", \"p-6\", \"border-t-0\", 3, \"formGroup\", \"ngSubmit\"], [\"class\", \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [1, \"grid\", \"grid-cols-1\", \"gap-6\"], [1, \"relative\"], [\"for\", \"titre\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-purple-700\", \"mb-2\"], [1, \"fas\", \"fa-tag\", \"mr-2\", \"text-purple-500\"], [\"id\", \"titre\", \"type\", \"text\", \"formControlName\", \"titre\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-lg\", \"border-2\", \"border-purple-200\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"px-4\", \"py-3\"], [\"class\", \"text-red-500 text-sm mt-2 flex items-center\", 4, \"ngIf\"], [\"for\", \"description\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-indigo-700\", \"mb-2\"], [1, \"fas\", \"fa-align-left\", \"mr-2\", \"text-indigo-500\"], [\"id\", \"description\", \"formControlName\", \"description\", \"rows\", \"3\", \"placeholder\", \"D\\u00E9crivez votre r\\u00E9union...\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-lg\", \"border-2\", \"border-indigo-200\", \"shadow-sm\", \"focus:border-indigo-500\", \"focus:ring-indigo-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"px-4\", \"py-3\"], [1, \"bg-gradient-to-r\", \"from-blue-50\", \"to-cyan-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-blue-200\"], [1, \"text-lg\", \"font-semibold\", \"text-blue-800\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-calendar-clock\", \"mr-2\", \"text-blue-600\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-3\", \"gap-6\"], [\"for\", \"date\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-blue-700\", \"mb-2\"], [1, \"fas\", \"fa-calendar\", \"mr-2\", \"text-blue-500\"], [\"id\", \"date\", \"type\", \"date\", \"formControlName\", \"date\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-lg\", \"border-2\", \"border-blue-200\", \"shadow-sm\", \"focus:border-blue-500\", \"focus:ring-blue-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"px-4\", \"py-3\"], [\"for\", \"heureDebut\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-green-700\", \"mb-2\"], [1, \"fas\", \"fa-play\", \"mr-2\", \"text-green-500\"], [\"id\", \"heureDebut\", \"type\", \"time\", \"formControlName\", \"heureDebut\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-lg\", \"border-2\", \"border-green-200\", \"shadow-sm\", \"focus:border-green-500\", \"focus:ring-green-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"px-4\", \"py-3\"], [\"for\", \"heureFin\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-red-700\", \"mb-2\"], [1, \"fas\", \"fa-stop\", \"mr-2\", \"text-red-500\"], [\"id\", \"heureFin\", \"type\", \"time\", \"formControlName\", \"heureFin\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-lg\", \"border-2\", \"border-red-200\", \"shadow-sm\", \"focus:border-red-500\", \"focus:ring-red-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"px-4\", \"py-3\"], [1, \"bg-gradient-to-r\", \"from-orange-50\", \"to-yellow-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-orange-200\"], [1, \"text-lg\", \"font-semibold\", \"text-orange-800\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-map-marker-alt\", \"mr-2\", \"text-orange-600\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-6\"], [\"for\", \"lieu\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-orange-700\", \"mb-2\"], [1, \"fas\", \"fa-map-marker-alt\", \"mr-2\", \"text-orange-500\"], [\"id\", \"lieu\", \"type\", \"text\", \"formControlName\", \"lieu\", \"placeholder\", \"Salle 101, Bureau A, Google Meet...\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-lg\", \"border-2\", \"border-orange-200\", \"shadow-sm\", \"focus:border-orange-500\", \"focus:ring-orange-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"px-4\", \"py-3\"], [\"for\", \"lienVisio\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-cyan-700\", \"mb-2\"], [1, \"fas\", \"fa-video\", \"mr-2\", \"text-cyan-500\"], [\"id\", \"lienVisio\", \"type\", \"url\", \"formControlName\", \"lienVisio\", \"placeholder\", \"https://meet.google.com/...\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-lg\", \"border-2\", \"border-cyan-200\", \"shadow-sm\", \"focus:border-cyan-500\", \"focus:ring-cyan-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"px-4\", \"py-3\"], [\"for\", \"planning\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"class\", \"mt-1 block w-full px-4 py-3 bg-gradient-to-r from-purple-50 to-indigo-50 border-2 border-purple-200 rounded-lg shadow-sm\", 4, \"ngIf\"], [\"id\", \"planning\", \"formControlName\", \"planning\", 1, \"hidden\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"text-sm\", \"text-purple-600\", \"mt-3\", \"bg-purple-50\", \"p-3\", \"rounded-lg\", \"border\", \"border-purple-200\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-lock\", \"mr-2\", \"text-purple-500\"], [1, \"font-medium\"], [1, \"bg-gradient-to-r\", \"from-emerald-50\", \"to-teal-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-emerald-200\"], [1, \"text-lg\", \"font-semibold\", \"text-emerald-800\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-users\", \"mr-2\", \"text-emerald-600\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-emerald-700\", \"mb-2\"], [1, \"fas\", \"fa-user-friends\", \"mr-2\", \"text-emerald-500\"], [\"formControlName\", \"participants\", \"multiple\", \"\", 1, \"mt-1\", \"block\", \"w-full\", \"px-4\", \"py-3\", \"border-2\", \"border-emerald-200\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-emerald-500\", \"focus:border-emerald-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"text-sm\", \"min-h-[120px]\"], [4, \"ngIf\"], [1, \"text-xs\", \"text-emerald-600\", \"mt-2\"], [1, \"fas\", \"fa-info-circle\", \"mr-1\"], [1, \"mt-8\", \"flex\", \"justify-end\", \"space-x-4\", \"bg-gray-50\", \"p-4\", \"rounded-lg\", \"border-t\", \"border-gray-200\"], [\"type\", \"button\", 1, \"px-6\", \"py-3\", \"border-2\", \"border-gray-300\", \"rounded-lg\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"hover:bg-gray-100\", \"hover:border-gray-400\", \"transition-all\", \"duration-200\", \"flex\", \"items-center\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"mr-2\"], [\"type\", \"submit\", 1, \"px-6\", \"py-3\", \"rounded-lg\", \"text-sm\", \"font-medium\", \"text-white\", \"bg-gradient-to-r\", \"from-purple-600\", \"to-indigo-600\", \"hover:from-purple-700\", \"hover:to-indigo-700\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"transition-all\", \"duration-200\", \"flex\", \"items-center\", \"shadow-lg\", 3, \"disabled\"], [\"class\", \"fas fa-save mr-2\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner fa-spin mr-2\", 4, \"ngIf\"], [1, \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [1, \"text-red-500\", \"text-sm\", \"mt-2\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-exclamation-circle\", \"mr-1\"], [1, \"mt-1\", \"block\", \"w-full\", \"px-4\", \"py-3\", \"bg-gradient-to-r\", \"from-purple-50\", \"to-indigo-50\", \"border-2\", \"border-purple-200\", \"rounded-lg\", \"shadow-sm\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"font-semibold\", \"text-purple-800\", \"text-lg\"], [1, \"fas\", \"fa-calendar-alt\", \"mr-2\", \"text-purple-600\"], [1, \"text-sm\", \"font-medium\", \"text-red-600\", \"bg-red-50\", \"px-2\", \"py-1\", \"rounded-full\", \"border\", \"border-red-200\"], [1, \"fas\", \"fa-clock\", \"mr-1\"], [\"class\", \"text-sm text-indigo-700 mt-2 bg-indigo-50 p-2 rounded border-l-4 border-indigo-300\", 4, \"ngIf\"], [1, \"text-sm\", \"text-indigo-700\", \"mt-2\", \"bg-indigo-50\", \"p-2\", \"rounded\", \"border-l-4\", \"border-indigo-300\"], [3, \"value\"], [\"class\", \"py-2\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"py-2\", 3, \"value\"], [1, \"fas\", \"fa-user\", \"mr-2\"], [1, \"fas\", \"fa-save\", \"mr-2\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"mr-2\"]],\n      template: function ReunionEditComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\", 2);\n          i0.ɵɵelement(3, \"i\", 3);\n          i0.ɵɵtext(4, \" Modifier la R\\u00E9union \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 4);\n          i0.ɵɵtext(6, \"Modifiez les d\\u00E9tails de votre r\\u00E9union\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"form\", 5);\n          i0.ɵɵlistener(\"ngSubmit\", function ReunionEditComponent_Template_form_ngSubmit_7_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtemplate(8, ReunionEditComponent_div_8_Template, 2, 1, \"div\", 6);\n          i0.ɵɵelementStart(9, \"div\", 7)(10, \"div\", 8)(11, \"label\", 9);\n          i0.ɵɵelement(12, \"i\", 10);\n          i0.ɵɵtext(13, \" Titre * \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(14, \"input\", 11);\n          i0.ɵɵtemplate(15, ReunionEditComponent_div_15_Template, 3, 0, \"div\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"div\", 8)(17, \"label\", 13);\n          i0.ɵɵelement(18, \"i\", 14);\n          i0.ɵɵtext(19, \" Description \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(20, \"textarea\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\", 16)(22, \"h3\", 17);\n          i0.ɵɵelement(23, \"i\", 18);\n          i0.ɵɵtext(24, \" Planification \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"div\", 19)(26, \"div\")(27, \"label\", 20);\n          i0.ɵɵelement(28, \"i\", 21);\n          i0.ɵɵtext(29, \" Date * \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(30, \"input\", 22);\n          i0.ɵɵtemplate(31, ReunionEditComponent_div_31_Template, 3, 0, \"div\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\")(33, \"label\", 23);\n          i0.ɵɵelement(34, \"i\", 24);\n          i0.ɵɵtext(35, \" Heure de d\\u00E9but * \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(36, \"input\", 25);\n          i0.ɵɵtemplate(37, ReunionEditComponent_div_37_Template, 3, 0, \"div\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"div\")(39, \"label\", 26);\n          i0.ɵɵelement(40, \"i\", 27);\n          i0.ɵɵtext(41, \" Heure de fin * \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(42, \"input\", 28);\n          i0.ɵɵtemplate(43, ReunionEditComponent_div_43_Template, 3, 0, \"div\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(44, \"div\", 29)(45, \"h3\", 30);\n          i0.ɵɵelement(46, \"i\", 31);\n          i0.ɵɵtext(47, \" Localisation \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"div\", 32)(49, \"div\")(50, \"label\", 33);\n          i0.ɵɵelement(51, \"i\", 34);\n          i0.ɵɵtext(52, \" Lieu / Salle \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(53, \"input\", 35);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"div\")(55, \"label\", 36);\n          i0.ɵɵelement(56, \"i\", 37);\n          i0.ɵɵtext(57, \" Lien Visio \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(58, \"input\", 38);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(59, \"div\")(60, \"label\", 39);\n          i0.ɵɵtext(61, \"Planning *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(62, ReunionEditComponent_div_62_Template, 11, 10, \"div\", 40);\n          i0.ɵɵelementStart(63, \"select\", 41)(64, \"option\", 42);\n          i0.ɵɵtext(65, \"S\\u00E9lectionnez un planning\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(66, ReunionEditComponent_option_66_Template, 2, 2, \"option\", 43);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"div\", 44);\n          i0.ɵɵelement(68, \"i\", 45);\n          i0.ɵɵelementStart(69, \"span\", 46);\n          i0.ɵɵtext(70, \"Le planning ne peut pas \\u00EAtre modifi\\u00E9 lors de l'\\u00E9dition d'une r\\u00E9union\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(71, \"div\", 47)(72, \"h3\", 48);\n          i0.ɵɵelement(73, \"i\", 49);\n          i0.ɵɵtext(74, \" Participants \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"label\", 50);\n          i0.ɵɵelement(76, \"i\", 51);\n          i0.ɵɵtext(77, \" S\\u00E9lectionnez les participants \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"select\", 52);\n          i0.ɵɵtemplate(79, ReunionEditComponent_ng_container_79_Template, 2, 1, \"ng-container\", 53);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(80, \"p\", 54);\n          i0.ɵɵelement(81, \"i\", 55);\n          i0.ɵɵtext(82, \" Maintenez Ctrl (ou Cmd) pour s\\u00E9lectionner plusieurs participants \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(83, \"div\", 56)(84, \"button\", 57);\n          i0.ɵɵlistener(\"click\", function ReunionEditComponent_Template_button_click_84_listener() {\n            return ctx.goReunion();\n          });\n          i0.ɵɵelement(85, \"i\", 58);\n          i0.ɵɵtext(86, \" Annuler \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"button\", 59);\n          i0.ɵɵtemplate(88, ReunionEditComponent_i_88_Template, 1, 0, \"i\", 60);\n          i0.ɵɵtemplate(89, ReunionEditComponent_i_89_Template, 1, 0, \"i\", 61);\n          i0.ɵɵtext(90);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          let tmp_2_0;\n          let tmp_3_0;\n          let tmp_4_0;\n          let tmp_5_0;\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"formGroup\", ctx.reunionForm);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.reunionForm.get(\"titre\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.reunionForm.get(\"titre\")) == null ? null : tmp_2_0.touched));\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx.reunionForm.get(\"date\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.reunionForm.get(\"date\")) == null ? null : tmp_3_0.touched));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.reunionForm.get(\"heureDebut\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.reunionForm.get(\"heureDebut\")) == null ? null : tmp_4_0.touched));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.reunionForm.get(\"heureFin\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.reunionForm.get(\"heureFin\")) == null ? null : tmp_5_0.touched));\n          i0.ɵɵadvance(19);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentReunionPlanning);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.plannings);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngIf\", ctx.users);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"disabled\", ctx.reunionForm.invalid || ctx.isSubmitting);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isSubmitting);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isSubmitting);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \", ctx.isSubmitting ? \"Enregistrement...\" : \"Enregistrer les modifications\", \" \");\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJyZXVuaW9uLWVkaXQuY29tcG9uZW50LmNzcyJ9 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvcmV1bmlvbnMvcmV1bmlvbi1lZGl0L3JldW5pb24tZWRpdC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSx3S0FBd0siLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "error", "message", "ɵɵelement", "ctx_r10", "currentReunionPlanning", "description", "ɵɵtemplate", "ReunionEditComponent_div_62_div_10_Template", "ctx_r5", "titre", "ɵɵtextInterpolate2", "ɵɵpipeBind2", "dateDebut", "dateFin", "ɵɵproperty", "planning_r11", "_id", "ɵɵtextInterpolate", "user_r13", "username", "ɵɵelementContainerStart", "ReunionEditComponent_ng_container_79_option_1_Template", "ɵɵelementContainerEnd", "ctx_r7", "users", "ReunionEditComponent", "constructor", "fb", "route", "router", "reunionService", "userService", "planningService", "toastService", "authService", "roleService", "isSubmitting", "plannings", "isAdmin", "ngOnInit", "reunionId", "snapshot", "paramMap", "get", "checkUserRole", "initForm", "fetchUsers", "fetchPlannings", "loadReunion", "console", "log", "reunionForm", "group", "required", "date", "heureDebut", "heure<PERSON>in", "lieu", "lienVisio", "planning", "participants", "getAllUsers", "subscribe", "userId", "getCurrentUserId", "planningsObservable", "getAllPlanningsAdmin", "getPlanningsByUser", "next", "response", "data", "err", "getReunionById", "reunion", "patchValue", "split", "id", "map", "p", "disable", "status", "accessDenied", "errorMessage", "onSubmit", "invalid", "warning", "validateDateInPlanningRange", "value", "updateReunion", "success", "navigate", "goReunion", "formValue", "reunionDate", "planningId", "selectedPlanning", "find", "warn", "getPlanningById", "push", "reunionDateObj", "Date", "planningDateDebut", "planningDateFin", "setHours", "toLocaleDateString", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "ActivatedRoute", "Router", "i3", "ReunionService", "i4", "DataService", "i5", "PlanningService", "i6", "ToastService", "i7", "AuthuserService", "i8", "RoleService", "selectors", "decls", "vars", "consts", "template", "ReunionEditComponent_Template", "rf", "ctx", "ɵɵlistener", "ReunionEditComponent_Template_form_ngSubmit_7_listener", "ReunionEditComponent_div_8_Template", "ReunionEditComponent_div_15_Template", "ReunionEditComponent_div_31_Template", "ReunionEditComponent_div_37_Template", "ReunionEditComponent_div_43_Template", "ReunionEditComponent_div_62_Template", "ReunionEditComponent_option_66_Template", "ReunionEditComponent_ng_container_79_Template", "ReunionEditComponent_Template_button_click_84_listener", "ReunionEditComponent_i_88_Template", "ReunionEditComponent_i_89_Template", "tmp_2_0", "touched", "tmp_3_0", "tmp_4_0", "tmp_5_0"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\reunions\\reunion-edit\\reunion-edit.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\reunions\\reunion-edit\\reunion-edit.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport {ReunionService} from \"@app/services/reunion.service\";\nimport {DataService} from \"@app/services/data.service\";\nimport {PlanningService} from \"@app/services/planning.service\";\nimport {Planning} from \"@app/models/planning.model\";\nimport {User} from \"@app/models/user.model\";\nimport {ToastService} from \"@app/services/toast.service\";\nimport {AuthuserService} from \"@app/services/authuser.service\";\nimport {RoleService} from \"@app/services/role.service\";\n\n@Component({\n  selector: 'app-reunion-edit',\n  templateUrl: './reunion-edit.component.html',\n  styleUrls: ['./reunion-edit.component.css']\n})\nexport class ReunionEditComponent implements OnInit {\n  reunionForm!: FormGroup;\n  reunionId!: string;\n  error: any = null;\n  isSubmitting = false;\n  users: User[] = [];\n  plannings: Planning[] = [];\n  currentReunionPlanning: Planning | null = null;\n  isAdmin = false;\n\n  constructor(\n    private fb: FormBuilder,\n    private route: ActivatedRoute,\n    private router: Router,\n    private reunionService: ReunionService,\n    private userService: DataService,\n    private planningService: PlanningService,\n    private toastService: ToastService,\n    private authService: AuthuserService,\n    private roleService: RoleService\n  ) {}\n\n  ngOnInit(): void {\n    this.reunionId = this.route.snapshot.paramMap.get('id')!;\n    this.checkUserRole();\n    this.initForm();\n    this.fetchUsers();\n    this.fetchPlannings();\n    this.loadReunion();\n  }\n\n  checkUserRole(): void {\n    this.isAdmin = this.roleService.isAdmin();\n    console.log('🔍 Utilisateur admin:', this.isAdmin);\n  }\n\n  initForm(): void {\n    this.reunionForm = this.fb.group({\n      titre: ['', Validators.required],\n      description: [''],\n      date: ['', Validators.required],\n      heureDebut: ['', Validators.required],\n      heureFin: ['', Validators.required],\n      lieu: [''],\n      lienVisio: [''],\n      planning: ['', Validators.required],\n      participants: [[]]\n    });\n  }\n\n  fetchUsers(): void {\n    this.userService.getAllUsers().subscribe((users:any) => {\n      this.users = users;\n    });\n  }\n\n  fetchPlannings(): void {\n    const userId = this.authService.getCurrentUserId();\n    if (!userId) return;\n\n    // Si admin, récupérer tous les plannings, sinon seulement ceux de l'utilisateur\n    const planningsObservable = this.isAdmin\n      ? this.planningService.getAllPlanningsAdmin()\n      : this.planningService.getPlanningsByUser(userId);\n\n    planningsObservable.subscribe({\n      next: (response: any) => {\n        // Adapter la réponse selon l'endpoint utilisé\n        if (this.isAdmin) {\n          this.plannings = response.data || [];\n          console.log('🔍 Tous les plannings (admin) récupérés:', this.plannings);\n        } else {\n          this.plannings = response.plannings || [];\n          console.log('🔍 Plannings utilisateur récupérés:', this.plannings);\n        }\n      },\n      error: (err) => {\n        console.error('❌ Erreur chargement plannings:', err);\n        this.toastService.error(\n          'Erreur',\n          'Impossible de récupérer les plannings'\n        );\n      }\n    });\n  }\n\n  loadReunion(): void {\n    this.reunionService.getReunionById(this.reunionId).subscribe({\n      next: (reunion: any) => {\n        // Stocker le planning actuel de la réunion\n        this.currentReunionPlanning = reunion.reunion.planning;\n\n        this.reunionForm.patchValue({\n          titre: reunion.reunion.titre,\n          description: reunion.reunion.description,\n          date: reunion.reunion.date?.split('T')[0],\n          heureDebut: reunion.reunion.heureDebut,\n          heureFin: reunion.reunion.heureFin,\n          lieu: reunion.reunion.lieu,\n          lienVisio: reunion.reunion.lienVisio,\n          planning: reunion.reunion.planning?.id || reunion.reunion.planning?._id,\n          participants: reunion.reunion.participants?.map((p:any) => p._id)\n        });\n\n        // Désactiver le champ planning en mode édition\n        this.reunionForm.get('planning')?.disable();\n\n        console.log('🔍 Réunion chargée:', reunion.reunion);\n        console.log('🔍 Planning actuel:', this.currentReunionPlanning);\n      },\n      error: (err) => {\n        console.error('Erreur lors du chargement de la réunion:', err);\n        if (err.status === 403) {\n          this.toastService.accessDenied('accéder à cette réunion', err.status);\n        } else if (err.status === 404) {\n          this.toastService.error(\n            'Réunion introuvable',\n            'La réunion demandée n\\'existe pas ou a été supprimée'\n          );\n        } else {\n          const errorMessage = err.error?.message || 'Erreur lors du chargement de la réunion';\n          this.toastService.error(\n            'Erreur de chargement',\n            errorMessage\n          );\n        }\n      }\n    });\n  }\n\n  onSubmit(): void {\n    if (this.reunionForm.invalid) {\n      this.toastService.warning(\n        'Formulaire invalide',\n        'Veuillez corriger les erreurs avant de soumettre le formulaire'\n      );\n      return;\n    }\n\n    // Validation de la date par rapport au planning\n    if (!this.validateDateInPlanningRange()) {\n      return;\n    }\n\n    this.isSubmitting = true;\n    const reunion: any = this.reunionForm.value;\n\n    // Inclure le planning même s'il est désactivé\n    if (this.currentReunionPlanning) {\n      reunion.planning = this.currentReunionPlanning._id || this.currentReunionPlanning.id;\n    }\n\n    console.log('🔍 Données de la réunion à mettre à jour:', reunion);\n\n    this.reunionService.updateReunion(this.reunionId, reunion).subscribe({\n      next: () => {\n        this.isSubmitting = false;\n        this.toastService.success(\n          'Réunion mise à jour',\n          'La réunion a été modifiée avec succès'\n        );\n        this.router.navigate(['/reunions']);\n      },\n      error: (err) => {\n        this.isSubmitting = false;\n        console.error('Erreur lors de la mise à jour de la réunion:', err);\n\n        if (err.status === 403) {\n          this.toastService.accessDenied('modifier cette réunion', err.status);\n        } else if (err.status === 401) {\n          this.toastService.error(\n            'Non autorisé',\n            'Vous devez être connecté pour effectuer cette action'\n          );\n        } else {\n          const errorMessage = err.error?.message || 'Erreur lors de la mise à jour de la réunion';\n          this.toastService.error(\n            'Erreur de mise à jour',\n            errorMessage,\n            8000\n          );\n        }\n      }\n    });\n  }\n\n  goReunion(): void {\n    this.router.navigate(['/reunions']);\n  }\n\n  /**\n   * Valide que la date de la réunion est dans l'intervalle du planning sélectionné\n   */\n  validateDateInPlanningRange(): boolean {\n    const formValue = this.reunionForm.value;\n    const reunionDate = formValue.date;\n    const planningId = formValue.planning;\n\n    if (!reunionDate || !planningId) {\n      return true; // Si pas de date ou planning, laisser la validation backend gérer\n    }\n\n    // Trouver le planning sélectionné\n    const selectedPlanning = this.plannings.find(p => p._id === planningId);\n    if (!selectedPlanning) {\n      console.warn('⚠️ Planning non trouvé dans la liste locale, tentative de récupération depuis le serveur');\n      // Si le planning n'est pas trouvé dans la liste locale, essayer de le récupérer\n      // Cela peut arriver si l'utilisateur modifie une réunion d'un planning dont il n'est que participant\n      this.planningService.getPlanningById(planningId).subscribe({\n        next: (response: any) => {\n          const planning = response.planning;\n          if (planning) {\n            // Ajouter le planning à la liste locale pour éviter de futures requêtes\n            this.plannings.push(planning);\n            console.log('✅ Planning récupéré et ajouté à la liste locale:', planning);\n          }\n        },\n        error: (err) => {\n          console.error('❌ Erreur lors de la récupération du planning:', err);\n          this.toastService.error(\n            'Planning introuvable',\n            'Le planning sélectionné n\\'existe pas ou vous n\\'avez pas les permissions pour y accéder'\n          );\n        }\n      });\n      // Pour cette validation, on retourne true et on laisse le backend gérer\n      return true;\n    }\n\n    // Convertir les dates pour comparaison\n    const reunionDateObj = new Date(reunionDate);\n    const planningDateDebut = new Date(selectedPlanning.dateDebut);\n    const planningDateFin = new Date(selectedPlanning.dateFin);\n\n    // Comparer seulement les dates (sans les heures)\n    reunionDateObj.setHours(0, 0, 0, 0);\n    planningDateDebut.setHours(0, 0, 0, 0);\n    planningDateFin.setHours(0, 0, 0, 0);\n\n    if (reunionDateObj < planningDateDebut || reunionDateObj > planningDateFin) {\n      this.toastService.error(\n        'Date invalide',\n        `La date de la réunion doit être comprise entre le ${planningDateDebut.toLocaleDateString('fr-FR')} et le ${planningDateFin.toLocaleDateString('fr-FR')} (période du planning \"${selectedPlanning.titre}\")`,\n        10000\n      );\n      return false;\n    }\n\n    return true;\n  }\n}", "<div class=\"container mx-auto px-4 py-6 max-w-3xl\">\n  <!-- En-tête avec gradient coloré -->\n  <div class=\"bg-gradient-to-r from-purple-600 to-indigo-600 rounded-t-lg p-6 text-white mb-0\">\n    <h1 class=\"text-2xl font-bold flex items-center\">\n      <i class=\"fas fa-edit mr-3 text-purple-200\"></i>\n      Modifier la Réunion\n    </h1>\n    <p class=\"text-purple-100 mt-2\">Modifiez les détails de votre réunion</p>\n  </div>\n\n  <form [formGroup]=\"reunionForm\" (ngSubmit)=\"onSubmit()\" class=\"bg-white rounded-b-lg shadow-lg p-6 border-t-0\">\n    <div *ngIf=\"error\" class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n      {{ error.message || 'Une erreur est survenue' }}\n    </div>\n\n    <div class=\"grid grid-cols-1 gap-6\">\n      <!-- Titre -->\n      <div class=\"relative\">\n        <label for=\"titre\" class=\"block text-sm font-medium text-purple-700 mb-2\">\n          <i class=\"fas fa-tag mr-2 text-purple-500\"></i>\n          Titre *\n        </label>\n        <input id=\"titre\" type=\"text\" formControlName=\"titre\"\n               class=\"mt-1 block w-full rounded-lg border-2 border-purple-200 shadow-sm focus:border-purple-500 focus:ring-purple-500 focus:ring-2 transition-all duration-200 px-4 py-3\">\n        <div *ngIf=\"reunionForm.get('titre')?.invalid && reunionForm.get('titre')?.touched\"\n             class=\"text-red-500 text-sm mt-2 flex items-center\">\n          <i class=\"fas fa-exclamation-circle mr-1\"></i>\n          Le titre est obligatoire\n        </div>\n      </div>\n\n      <!-- Description -->\n      <div class=\"relative\">\n        <label for=\"description\" class=\"block text-sm font-medium text-indigo-700 mb-2\">\n          <i class=\"fas fa-align-left mr-2 text-indigo-500\"></i>\n          Description\n        </label>\n        <textarea id=\"description\" formControlName=\"description\" rows=\"3\"\n                  class=\"mt-1 block w-full rounded-lg border-2 border-indigo-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 focus:ring-2 transition-all duration-200 px-4 py-3\"\n                  placeholder=\"Décrivez votre réunion...\"></textarea>\n      </div>\n\n      <!-- Date and Time -->\n      <div class=\"bg-gradient-to-r from-blue-50 to-cyan-50 p-4 rounded-lg border border-blue-200\">\n        <h3 class=\"text-lg font-semibold text-blue-800 mb-4 flex items-center\">\n          <i class=\"fas fa-calendar-clock mr-2 text-blue-600\"></i>\n          Planification\n        </h3>\n        <div class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div>\n            <label for=\"date\" class=\"block text-sm font-medium text-blue-700 mb-2\">\n              <i class=\"fas fa-calendar mr-2 text-blue-500\"></i>\n              Date *\n            </label>\n            <input id=\"date\" type=\"date\" formControlName=\"date\"\n                   class=\"mt-1 block w-full rounded-lg border-2 border-blue-200 shadow-sm focus:border-blue-500 focus:ring-blue-500 focus:ring-2 transition-all duration-200 px-4 py-3\">\n            <div *ngIf=\"reunionForm.get('date')?.invalid && reunionForm.get('date')?.touched\"\n                 class=\"text-red-500 text-sm mt-2 flex items-center\">\n              <i class=\"fas fa-exclamation-circle mr-1\"></i>\n              La date est obligatoire\n            </div>\n          </div>\n\n          <div>\n            <label for=\"heureDebut\" class=\"block text-sm font-medium text-green-700 mb-2\">\n              <i class=\"fas fa-play mr-2 text-green-500\"></i>\n              Heure de début *\n            </label>\n            <input id=\"heureDebut\" type=\"time\" formControlName=\"heureDebut\"\n                   class=\"mt-1 block w-full rounded-lg border-2 border-green-200 shadow-sm focus:border-green-500 focus:ring-green-500 focus:ring-2 transition-all duration-200 px-4 py-3\">\n            <div *ngIf=\"reunionForm.get('heureDebut')?.invalid && reunionForm.get('heureDebut')?.touched\"\n                 class=\"text-red-500 text-sm mt-2 flex items-center\">\n              <i class=\"fas fa-exclamation-circle mr-1\"></i>\n              L'heure de début est obligatoire\n            </div>\n          </div>\n\n          <div>\n            <label for=\"heureFin\" class=\"block text-sm font-medium text-red-700 mb-2\">\n              <i class=\"fas fa-stop mr-2 text-red-500\"></i>\n              Heure de fin *\n            </label>\n            <input id=\"heureFin\" type=\"time\" formControlName=\"heureFin\"\n                   class=\"mt-1 block w-full rounded-lg border-2 border-red-200 shadow-sm focus:border-red-500 focus:ring-red-500 focus:ring-2 transition-all duration-200 px-4 py-3\">\n            <div *ngIf=\"reunionForm.get('heureFin')?.invalid && reunionForm.get('heureFin')?.touched\"\n                 class=\"text-red-500 text-sm mt-2 flex items-center\">\n              <i class=\"fas fa-exclamation-circle mr-1\"></i>\n              L'heure de fin est obligatoire\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Lieu / Lien visio -->\n      <div class=\"bg-gradient-to-r from-orange-50 to-yellow-50 p-4 rounded-lg border border-orange-200\">\n        <h3 class=\"text-lg font-semibold text-orange-800 mb-4 flex items-center\">\n          <i class=\"fas fa-map-marker-alt mr-2 text-orange-600\"></i>\n          Localisation\n        </h3>\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div>\n            <label for=\"lieu\" class=\"block text-sm font-medium text-orange-700 mb-2\">\n              <i class=\"fas fa-map-marker-alt mr-2 text-orange-500\"></i>\n              Lieu / Salle\n            </label>\n            <input id=\"lieu\" type=\"text\" formControlName=\"lieu\"\n                   class=\"mt-1 block w-full rounded-lg border-2 border-orange-200 shadow-sm focus:border-orange-500 focus:ring-orange-500 focus:ring-2 transition-all duration-200 px-4 py-3\"\n                   placeholder=\"Salle 101, Bureau A, Google Meet...\">\n          </div>\n\n          <div>\n            <label for=\"lienVisio\" class=\"block text-sm font-medium text-cyan-700 mb-2\">\n              <i class=\"fas fa-video mr-2 text-cyan-500\"></i>\n              Lien Visio\n            </label>\n            <input id=\"lienVisio\" type=\"url\" formControlName=\"lienVisio\"\n                   class=\"mt-1 block w-full rounded-lg border-2 border-cyan-200 shadow-sm focus:border-cyan-500 focus:ring-cyan-500 focus:ring-2 transition-all duration-200 px-4 py-3\"\n                   placeholder=\"https://meet.google.com/...\">\n          </div>\n        </div>\n      </div>\n\n      <!-- Planning (désactivé en mode édition) -->\n      <div>\n        <label for=\"planning\" class=\"block text-sm font-medium text-gray-700\">Planning *</label>\n\n        <!-- Affichage du planning actuel (non-cliquable) -->\n        <div *ngIf=\"currentReunionPlanning\"\n             class=\"mt-1 block w-full px-4 py-3 bg-gradient-to-r from-purple-50 to-indigo-50 border-2 border-purple-200 rounded-lg shadow-sm\">\n          <div class=\"flex items-center justify-between\">\n            <span class=\"font-semibold text-purple-800 text-lg\">\n              <i class=\"fas fa-calendar-alt mr-2 text-purple-600\"></i>\n              {{ currentReunionPlanning.titre }}\n            </span>\n            <span class=\"text-sm font-medium text-red-600 bg-red-50 px-2 py-1 rounded-full border border-red-200\">\n              <i class=\"fas fa-clock mr-1\"></i>\n              {{ currentReunionPlanning.dateDebut | date:'dd/MM/yyyy' }} -\n              {{ currentReunionPlanning.dateFin | date:'dd/MM/yyyy' }}\n            </span>\n          </div>\n          <div *ngIf=\"currentReunionPlanning.description\" class=\"text-sm text-indigo-700 mt-2 bg-indigo-50 p-2 rounded border-l-4 border-indigo-300\">\n            <i class=\"fas fa-info-circle mr-1\"></i>\n            {{ currentReunionPlanning.description }}\n          </div>\n        </div>\n\n        <!-- Select caché pour maintenir la validation du formulaire -->\n        <select id=\"planning\" formControlName=\"planning\" class=\"hidden\">\n          <option value=\"\">Sélectionnez un planning</option>\n          <option *ngFor=\"let planning of plannings\" [value]=\"planning._id\">{{ planning.titre }}</option>\n        </select>\n\n        <div class=\"text-sm text-purple-600 mt-3 bg-purple-50 p-3 rounded-lg border border-purple-200 flex items-center\">\n          <i class=\"fas fa-lock mr-2 text-purple-500\"></i>\n          <span class=\"font-medium\">Le planning ne peut pas être modifié lors de l'édition d'une réunion</span>\n        </div>\n      </div>\n\n      <!-- Participants -->\n      <div class=\"bg-gradient-to-r from-emerald-50 to-teal-50 p-4 rounded-lg border border-emerald-200\">\n        <h3 class=\"text-lg font-semibold text-emerald-800 mb-4 flex items-center\">\n          <i class=\"fas fa-users mr-2 text-emerald-600\"></i>\n          Participants\n        </h3>\n        <label class=\"block text-sm font-medium text-emerald-700 mb-2\">\n          <i class=\"fas fa-user-friends mr-2 text-emerald-500\"></i>\n          Sélectionnez les participants\n        </label>\n        <select formControlName=\"participants\" multiple\n                class=\"mt-1 block w-full px-4 py-3 border-2 border-emerald-200 rounded-lg shadow-sm focus:ring-emerald-500 focus:border-emerald-500 focus:ring-2 transition-all duration-200 text-sm min-h-[120px]\">\n          <ng-container *ngIf=\"users\">\n            <option *ngFor=\"let user of users\" [value]=\"user._id\" class=\"py-2\">\n              <i class=\"fas fa-user mr-2\"></i>{{ user.username }}\n            </option>\n          </ng-container>\n        </select>\n        <p class=\"text-xs text-emerald-600 mt-2\">\n          <i class=\"fas fa-info-circle mr-1\"></i>\n          Maintenez Ctrl (ou Cmd) pour sélectionner plusieurs participants\n        </p>\n      </div>\n    </div>\n\n    <!-- Boutons d'action avec design amélioré -->\n    <div class=\"mt-8 flex justify-end space-x-4 bg-gray-50 p-4 rounded-lg border-t border-gray-200\">\n      <button type=\"button\" (click)=\"goReunion()\"\n              class=\"px-6 py-3 border-2 border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-100 hover:border-gray-400 transition-all duration-200 flex items-center\">\n        <i class=\"fas fa-times mr-2\"></i>\n        Annuler\n      </button>\n      <button type=\"submit\" [disabled]=\"reunionForm.invalid || isSubmitting\"\n              class=\"px-6 py-3 rounded-lg text-sm font-medium text-white bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center shadow-lg\">\n        <i class=\"fas fa-save mr-2\" *ngIf=\"!isSubmitting\"></i>\n        <i class=\"fas fa-spinner fa-spin mr-2\" *ngIf=\"isSubmitting\"></i>\n        {{ isSubmitting ? 'Enregistrement...' : 'Enregistrer les modifications' }}\n      </button>\n    </div>\n  </form>\n</div>"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;ICU/DC,EAAA,CAAAC,cAAA,cAAgG;IAC9FD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,KAAA,CAAAC,OAAA,mCACF;;;;;IAWIR,EAAA,CAAAC,cAAA,cACyD;IACvDD,EAAA,CAAAS,SAAA,YAA8C;IAC9CT,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IA4BFH,EAAA,CAAAC,cAAA,cACyD;IACvDD,EAAA,CAAAS,SAAA,YAA8C;IAC9CT,EAAA,CAAAE,MAAA,gCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAUNH,EAAA,CAAAC,cAAA,cACyD;IACvDD,EAAA,CAAAS,SAAA,YAA8C;IAC9CT,EAAA,CAAAE,MAAA,8CACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAUNH,EAAA,CAAAC,cAAA,cACyD;IACvDD,EAAA,CAAAS,SAAA,YAA8C;IAC9CT,EAAA,CAAAE,MAAA,uCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAoDRH,EAAA,CAAAC,cAAA,cAA2I;IACzID,EAAA,CAAAS,SAAA,YAAuC;IACvCT,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAK,OAAA,CAAAC,sBAAA,CAAAC,WAAA,MACF;;;;;IAhBFZ,EAAA,CAAAC,cAAA,cACsI;IAGhID,EAAA,CAAAS,SAAA,YAAwD;IACxDT,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,eAAsG;IACpGD,EAAA,CAAAS,SAAA,YAAiC;IACjCT,EAAA,CAAAE,MAAA,GAEF;;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAa,UAAA,KAAAC,2CAAA,kBAGM;IACRd,EAAA,CAAAG,YAAA,EAAM;;;;IAZAH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAU,MAAA,CAAAJ,sBAAA,CAAAK,KAAA,MACF;IAGEhB,EAAA,CAAAI,SAAA,GAEF;IAFEJ,EAAA,CAAAiB,kBAAA,MAAAjB,EAAA,CAAAkB,WAAA,OAAAH,MAAA,CAAAJ,sBAAA,CAAAQ,SAAA,wBAAAnB,EAAA,CAAAkB,WAAA,OAAAH,MAAA,CAAAJ,sBAAA,CAAAS,OAAA,qBAEF;IAEIpB,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAqB,UAAA,SAAAN,MAAA,CAAAJ,sBAAA,CAAAC,WAAA,CAAwC;;;;;IAS9CZ,EAAA,CAAAC,cAAA,iBAAkE;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAApDH,EAAA,CAAAqB,UAAA,UAAAC,YAAA,CAAAC,GAAA,CAAsB;IAACvB,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAwB,iBAAA,CAAAF,YAAA,CAAAN,KAAA,CAAoB;;;;;IAsBpFhB,EAAA,CAAAC,cAAA,iBAAmE;IACjED,EAAA,CAAAS,SAAA,YAAgC;IAAAT,EAAA,CAAAE,MAAA,GAClC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAF0BH,EAAA,CAAAqB,UAAA,UAAAI,QAAA,CAAAF,GAAA,CAAkB;IACnBvB,EAAA,CAAAI,SAAA,GAClC;IADkCJ,EAAA,CAAAK,kBAAA,KAAAoB,QAAA,CAAAC,QAAA,MAClC;;;;;IAHF1B,EAAA,CAAA2B,uBAAA,GAA4B;IAC1B3B,EAAA,CAAAa,UAAA,IAAAe,sDAAA,qBAES;IACX5B,EAAA,CAAA6B,qBAAA,EAAe;;;;IAHY7B,EAAA,CAAAI,SAAA,GAAQ;IAARJ,EAAA,CAAAqB,UAAA,YAAAS,MAAA,CAAAC,KAAA,CAAQ;;;;;IAqBrC/B,EAAA,CAAAS,SAAA,YAAsD;;;;;IACtDT,EAAA,CAAAS,SAAA,YAAgE;;;ADhLxE,OAAM,MAAOuB,oBAAoB;EAU/BC,YACUC,EAAe,EACfC,KAAqB,EACrBC,MAAc,EACdC,cAA8B,EAC9BC,WAAwB,EACxBC,eAAgC,EAChCC,YAA0B,EAC1BC,WAA4B,EAC5BC,WAAwB;IARxB,KAAAR,EAAE,GAAFA,EAAE;IACF,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IAhBrB,KAAAnC,KAAK,GAAQ,IAAI;IACjB,KAAAoC,YAAY,GAAG,KAAK;IACpB,KAAAZ,KAAK,GAAW,EAAE;IAClB,KAAAa,SAAS,GAAe,EAAE;IAC1B,KAAAjC,sBAAsB,GAAoB,IAAI;IAC9C,KAAAkC,OAAO,GAAG,KAAK;EAYZ;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,GAAG,IAAI,CAACZ,KAAK,CAACa,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAE;IACxD,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,QAAQ,EAAE;IACf,IAAI,CAACC,UAAU,EAAE;IACjB,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAJ,aAAaA,CAAA;IACX,IAAI,CAACN,OAAO,GAAG,IAAI,CAACH,WAAW,CAACG,OAAO,EAAE;IACzCW,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACZ,OAAO,CAAC;EACpD;EAEAO,QAAQA,CAAA;IACN,IAAI,CAACM,WAAW,GAAG,IAAI,CAACxB,EAAE,CAACyB,KAAK,CAAC;MAC/B3C,KAAK,EAAE,CAAC,EAAE,EAAEjB,UAAU,CAAC6D,QAAQ,CAAC;MAChChD,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBiD,IAAI,EAAE,CAAC,EAAE,EAAE9D,UAAU,CAAC6D,QAAQ,CAAC;MAC/BE,UAAU,EAAE,CAAC,EAAE,EAAE/D,UAAU,CAAC6D,QAAQ,CAAC;MACrCG,QAAQ,EAAE,CAAC,EAAE,EAAEhE,UAAU,CAAC6D,QAAQ,CAAC;MACnCI,IAAI,EAAE,CAAC,EAAE,CAAC;MACVC,SAAS,EAAE,CAAC,EAAE,CAAC;MACfC,QAAQ,EAAE,CAAC,EAAE,EAAEnE,UAAU,CAAC6D,QAAQ,CAAC;MACnCO,YAAY,EAAE,CAAC,EAAE;KAClB,CAAC;EACJ;EAEAd,UAAUA,CAAA;IACR,IAAI,CAACf,WAAW,CAAC8B,WAAW,EAAE,CAACC,SAAS,CAAEtC,KAAS,IAAI;MACrD,IAAI,CAACA,KAAK,GAAGA,KAAK;IACpB,CAAC,CAAC;EACJ;EAEAuB,cAAcA,CAAA;IACZ,MAAMgB,MAAM,GAAG,IAAI,CAAC7B,WAAW,CAAC8B,gBAAgB,EAAE;IAClD,IAAI,CAACD,MAAM,EAAE;IAEb;IACA,MAAME,mBAAmB,GAAG,IAAI,CAAC3B,OAAO,GACpC,IAAI,CAACN,eAAe,CAACkC,oBAAoB,EAAE,GAC3C,IAAI,CAAClC,eAAe,CAACmC,kBAAkB,CAACJ,MAAM,CAAC;IAEnDE,mBAAmB,CAACH,SAAS,CAAC;MAC5BM,IAAI,EAAGC,QAAa,IAAI;QACtB;QACA,IAAI,IAAI,CAAC/B,OAAO,EAAE;UAChB,IAAI,CAACD,SAAS,GAAGgC,QAAQ,CAACC,IAAI,IAAI,EAAE;UACpCrB,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE,IAAI,CAACb,SAAS,CAAC;SACxE,MAAM;UACL,IAAI,CAACA,SAAS,GAAGgC,QAAQ,CAAChC,SAAS,IAAI,EAAE;UACzCY,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE,IAAI,CAACb,SAAS,CAAC;;MAEtE,CAAC;MACDrC,KAAK,EAAGuE,GAAG,IAAI;QACbtB,OAAO,CAACjD,KAAK,CAAC,gCAAgC,EAAEuE,GAAG,CAAC;QACpD,IAAI,CAACtC,YAAY,CAACjC,KAAK,CACrB,QAAQ,EACR,uCAAuC,CACxC;MACH;KACD,CAAC;EACJ;EAEAgD,WAAWA,CAAA;IACT,IAAI,CAAClB,cAAc,CAAC0C,cAAc,CAAC,IAAI,CAAChC,SAAS,CAAC,CAACsB,SAAS,CAAC;MAC3DM,IAAI,EAAGK,OAAY,IAAI;QACrB;QACA,IAAI,CAACrE,sBAAsB,GAAGqE,OAAO,CAACA,OAAO,CAACd,QAAQ;QAEtD,IAAI,CAACR,WAAW,CAACuB,UAAU,CAAC;UAC1BjE,KAAK,EAAEgE,OAAO,CAACA,OAAO,CAAChE,KAAK;UAC5BJ,WAAW,EAAEoE,OAAO,CAACA,OAAO,CAACpE,WAAW;UACxCiD,IAAI,EAAEmB,OAAO,CAACA,OAAO,CAACnB,IAAI,EAAEqB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UACzCpB,UAAU,EAAEkB,OAAO,CAACA,OAAO,CAAClB,UAAU;UACtCC,QAAQ,EAAEiB,OAAO,CAACA,OAAO,CAACjB,QAAQ;UAClCC,IAAI,EAAEgB,OAAO,CAACA,OAAO,CAAChB,IAAI;UAC1BC,SAAS,EAAEe,OAAO,CAACA,OAAO,CAACf,SAAS;UACpCC,QAAQ,EAAEc,OAAO,CAACA,OAAO,CAACd,QAAQ,EAAEiB,EAAE,IAAIH,OAAO,CAACA,OAAO,CAACd,QAAQ,EAAE3C,GAAG;UACvE4C,YAAY,EAAEa,OAAO,CAACA,OAAO,CAACb,YAAY,EAAEiB,GAAG,CAAEC,CAAK,IAAKA,CAAC,CAAC9D,GAAG;SACjE,CAAC;QAEF;QACA,IAAI,CAACmC,WAAW,CAACR,GAAG,CAAC,UAAU,CAAC,EAAEoC,OAAO,EAAE;QAE3C9B,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEuB,OAAO,CAACA,OAAO,CAAC;QACnDxB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAC9C,sBAAsB,CAAC;MACjE,CAAC;MACDJ,KAAK,EAAGuE,GAAG,IAAI;QACbtB,OAAO,CAACjD,KAAK,CAAC,0CAA0C,EAAEuE,GAAG,CAAC;QAC9D,IAAIA,GAAG,CAACS,MAAM,KAAK,GAAG,EAAE;UACtB,IAAI,CAAC/C,YAAY,CAACgD,YAAY,CAAC,yBAAyB,EAAEV,GAAG,CAACS,MAAM,CAAC;SACtE,MAAM,IAAIT,GAAG,CAACS,MAAM,KAAK,GAAG,EAAE;UAC7B,IAAI,CAAC/C,YAAY,CAACjC,KAAK,CACrB,qBAAqB,EACrB,sDAAsD,CACvD;SACF,MAAM;UACL,MAAMkF,YAAY,GAAGX,GAAG,CAACvE,KAAK,EAAEC,OAAO,IAAI,yCAAyC;UACpF,IAAI,CAACgC,YAAY,CAACjC,KAAK,CACrB,sBAAsB,EACtBkF,YAAY,CACb;;MAEL;KACD,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAAChC,WAAW,CAACiC,OAAO,EAAE;MAC5B,IAAI,CAACnD,YAAY,CAACoD,OAAO,CACvB,qBAAqB,EACrB,gEAAgE,CACjE;MACD;;IAGF;IACA,IAAI,CAAC,IAAI,CAACC,2BAA2B,EAAE,EAAE;MACvC;;IAGF,IAAI,CAAClD,YAAY,GAAG,IAAI;IACxB,MAAMqC,OAAO,GAAQ,IAAI,CAACtB,WAAW,CAACoC,KAAK;IAE3C;IACA,IAAI,IAAI,CAACnF,sBAAsB,EAAE;MAC/BqE,OAAO,CAACd,QAAQ,GAAG,IAAI,CAACvD,sBAAsB,CAACY,GAAG,IAAI,IAAI,CAACZ,sBAAsB,CAACwE,EAAE;;IAGtF3B,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEuB,OAAO,CAAC;IAEjE,IAAI,CAAC3C,cAAc,CAAC0D,aAAa,CAAC,IAAI,CAAChD,SAAS,EAAEiC,OAAO,CAAC,CAACX,SAAS,CAAC;MACnEM,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAChC,YAAY,GAAG,KAAK;QACzB,IAAI,CAACH,YAAY,CAACwD,OAAO,CACvB,qBAAqB,EACrB,uCAAuC,CACxC;QACD,IAAI,CAAC5D,MAAM,CAAC6D,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;MACrC,CAAC;MACD1F,KAAK,EAAGuE,GAAG,IAAI;QACb,IAAI,CAACnC,YAAY,GAAG,KAAK;QACzBa,OAAO,CAACjD,KAAK,CAAC,8CAA8C,EAAEuE,GAAG,CAAC;QAElE,IAAIA,GAAG,CAACS,MAAM,KAAK,GAAG,EAAE;UACtB,IAAI,CAAC/C,YAAY,CAACgD,YAAY,CAAC,wBAAwB,EAAEV,GAAG,CAACS,MAAM,CAAC;SACrE,MAAM,IAAIT,GAAG,CAACS,MAAM,KAAK,GAAG,EAAE;UAC7B,IAAI,CAAC/C,YAAY,CAACjC,KAAK,CACrB,cAAc,EACd,sDAAsD,CACvD;SACF,MAAM;UACL,MAAMkF,YAAY,GAAGX,GAAG,CAACvE,KAAK,EAAEC,OAAO,IAAI,6CAA6C;UACxF,IAAI,CAACgC,YAAY,CAACjC,KAAK,CACrB,uBAAuB,EACvBkF,YAAY,EACZ,IAAI,CACL;;MAEL;KACD,CAAC;EACJ;EAEAS,SAASA,CAAA;IACP,IAAI,CAAC9D,MAAM,CAAC6D,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;EACrC;EAEA;;;EAGAJ,2BAA2BA,CAAA;IACzB,MAAMM,SAAS,GAAG,IAAI,CAACzC,WAAW,CAACoC,KAAK;IACxC,MAAMM,WAAW,GAAGD,SAAS,CAACtC,IAAI;IAClC,MAAMwC,UAAU,GAAGF,SAAS,CAACjC,QAAQ;IAErC,IAAI,CAACkC,WAAW,IAAI,CAACC,UAAU,EAAE;MAC/B,OAAO,IAAI,CAAC,CAAC;;IAGf;IACA,MAAMC,gBAAgB,GAAG,IAAI,CAAC1D,SAAS,CAAC2D,IAAI,CAAClB,CAAC,IAAIA,CAAC,CAAC9D,GAAG,KAAK8E,UAAU,CAAC;IACvE,IAAI,CAACC,gBAAgB,EAAE;MACrB9C,OAAO,CAACgD,IAAI,CAAC,0FAA0F,CAAC;MACxG;MACA;MACA,IAAI,CAACjE,eAAe,CAACkE,eAAe,CAACJ,UAAU,CAAC,CAAChC,SAAS,CAAC;QACzDM,IAAI,EAAGC,QAAa,IAAI;UACtB,MAAMV,QAAQ,GAAGU,QAAQ,CAACV,QAAQ;UAClC,IAAIA,QAAQ,EAAE;YACZ;YACA,IAAI,CAACtB,SAAS,CAAC8D,IAAI,CAACxC,QAAQ,CAAC;YAC7BV,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAES,QAAQ,CAAC;;QAE7E,CAAC;QACD3D,KAAK,EAAGuE,GAAG,IAAI;UACbtB,OAAO,CAACjD,KAAK,CAAC,+CAA+C,EAAEuE,GAAG,CAAC;UACnE,IAAI,CAACtC,YAAY,CAACjC,KAAK,CACrB,sBAAsB,EACtB,0FAA0F,CAC3F;QACH;OACD,CAAC;MACF;MACA,OAAO,IAAI;;IAGb;IACA,MAAMoG,cAAc,GAAG,IAAIC,IAAI,CAACR,WAAW,CAAC;IAC5C,MAAMS,iBAAiB,GAAG,IAAID,IAAI,CAACN,gBAAgB,CAACnF,SAAS,CAAC;IAC9D,MAAM2F,eAAe,GAAG,IAAIF,IAAI,CAACN,gBAAgB,CAAClF,OAAO,CAAC;IAE1D;IACAuF,cAAc,CAACI,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACnCF,iBAAiB,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtCD,eAAe,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAEpC,IAAIJ,cAAc,GAAGE,iBAAiB,IAAIF,cAAc,GAAGG,eAAe,EAAE;MAC1E,IAAI,CAACtE,YAAY,CAACjC,KAAK,CACrB,eAAe,EACf,qDAAqDsG,iBAAiB,CAACG,kBAAkB,CAAC,OAAO,CAAC,UAAUF,eAAe,CAACE,kBAAkB,CAAC,OAAO,CAAC,0BAA0BV,gBAAgB,CAACtF,KAAK,IAAI,EAC3M,KAAK,CACN;MACD,OAAO,KAAK;;IAGd,OAAO,IAAI;EACb;;;uBAzPWgB,oBAAoB,EAAAhC,EAAA,CAAAiH,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAnH,EAAA,CAAAiH,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAArH,EAAA,CAAAiH,iBAAA,CAAAG,EAAA,CAAAE,MAAA,GAAAtH,EAAA,CAAAiH,iBAAA,CAAAM,EAAA,CAAAC,cAAA,GAAAxH,EAAA,CAAAiH,iBAAA,CAAAQ,EAAA,CAAAC,WAAA,GAAA1H,EAAA,CAAAiH,iBAAA,CAAAU,EAAA,CAAAC,eAAA,GAAA5H,EAAA,CAAAiH,iBAAA,CAAAY,EAAA,CAAAC,YAAA,GAAA9H,EAAA,CAAAiH,iBAAA,CAAAc,EAAA,CAAAC,eAAA,GAAAhI,EAAA,CAAAiH,iBAAA,CAAAgB,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAApBlG,oBAAoB;MAAAmG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjBjCzI,EAAA,CAAAC,cAAA,aAAmD;UAI7CD,EAAA,CAAAS,SAAA,WAAgD;UAChDT,EAAA,CAAAE,MAAA,iCACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,WAAgC;UAAAD,EAAA,CAAAE,MAAA,sDAAqC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAG3EH,EAAA,CAAAC,cAAA,cAA+G;UAA/ED,EAAA,CAAA2I,UAAA,sBAAAC,uDAAA;YAAA,OAAYF,GAAA,CAAAhD,QAAA,EAAU;UAAA,EAAC;UACrD1F,EAAA,CAAAa,UAAA,IAAAgI,mCAAA,iBAEM;UAEN7I,EAAA,CAAAC,cAAA,aAAoC;UAI9BD,EAAA,CAAAS,SAAA,aAA+C;UAC/CT,EAAA,CAAAE,MAAA,iBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAS,SAAA,iBACkL;UAClLT,EAAA,CAAAa,UAAA,KAAAiI,oCAAA,kBAIM;UACR9I,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,cAAsB;UAElBD,EAAA,CAAAS,SAAA,aAAsD;UACtDT,EAAA,CAAAE,MAAA,qBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAS,SAAA,oBAE6D;UAC/DT,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAA4F;UAExFD,EAAA,CAAAS,SAAA,aAAwD;UACxDT,EAAA,CAAAE,MAAA,uBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,eAAmD;UAG7CD,EAAA,CAAAS,SAAA,aAAkD;UAClDT,EAAA,CAAAE,MAAA,gBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAS,SAAA,iBAC4K;UAC5KT,EAAA,CAAAa,UAAA,KAAAkI,oCAAA,kBAIM;UACR/I,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,WAAK;UAEDD,EAAA,CAAAS,SAAA,aAA+C;UAC/CT,EAAA,CAAAE,MAAA,+BACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAS,SAAA,iBAC+K;UAC/KT,EAAA,CAAAa,UAAA,KAAAmI,oCAAA,kBAIM;UACRhJ,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,WAAK;UAEDD,EAAA,CAAAS,SAAA,aAA6C;UAC7CT,EAAA,CAAAE,MAAA,wBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAS,SAAA,iBACyK;UACzKT,EAAA,CAAAa,UAAA,KAAAoI,oCAAA,kBAIM;UACRjJ,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAC,cAAA,eAAkG;UAE9FD,EAAA,CAAAS,SAAA,aAA0D;UAC1DT,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,eAAmD;UAG7CD,EAAA,CAAAS,SAAA,aAA0D;UAC1DT,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAS,SAAA,iBAEyD;UAC3DT,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,WAAK;UAEDD,EAAA,CAAAS,SAAA,aAA+C;UAC/CT,EAAA,CAAAE,MAAA,oBACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAS,SAAA,iBAEiD;UACnDT,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAC,cAAA,WAAK;UACmED,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAGxFH,EAAA,CAAAa,UAAA,KAAAqI,oCAAA,oBAiBM;UAGNlJ,EAAA,CAAAC,cAAA,kBAAgE;UAC7CD,EAAA,CAAAE,MAAA,qCAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAClDH,EAAA,CAAAa,UAAA,KAAAsI,uCAAA,qBAA+F;UACjGnJ,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,eAAiH;UAC/GD,EAAA,CAAAS,SAAA,aAAgD;UAChDT,EAAA,CAAAC,cAAA,gBAA0B;UAAAD,EAAA,CAAAE,MAAA,gGAAoE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAKzGH,EAAA,CAAAC,cAAA,eAAkG;UAE9FD,EAAA,CAAAS,SAAA,aAAkD;UAClDT,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,iBAA+D;UAC7DD,EAAA,CAAAS,SAAA,aAAyD;UACzDT,EAAA,CAAAE,MAAA,4CACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,kBAC4M;UAC1MD,EAAA,CAAAa,UAAA,KAAAuI,6CAAA,2BAIe;UACjBpJ,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,aAAyC;UACvCD,EAAA,CAAAS,SAAA,aAAuC;UACvCT,EAAA,CAAAE,MAAA,+EACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAKRH,EAAA,CAAAC,cAAA,eAAgG;UACxED,EAAA,CAAA2I,UAAA,mBAAAU,uDAAA;YAAA,OAASX,GAAA,CAAAxC,SAAA,EAAW;UAAA,EAAC;UAEzClG,EAAA,CAAAS,SAAA,aAAiC;UACjCT,EAAA,CAAAE,MAAA,iBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBACqQ;UACnQD,EAAA,CAAAa,UAAA,KAAAyI,kCAAA,gBAAsD;UACtDtJ,EAAA,CAAAa,UAAA,KAAA0I,kCAAA,gBAAgE;UAChEvJ,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;UAzLPH,EAAA,CAAAI,SAAA,GAAyB;UAAzBJ,EAAA,CAAAqB,UAAA,cAAAqH,GAAA,CAAAhF,WAAA,CAAyB;UACvB1D,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAqB,UAAA,SAAAqH,GAAA,CAAAnI,KAAA,CAAW;UAaPP,EAAA,CAAAI,SAAA,GAA4E;UAA5EJ,EAAA,CAAAqB,UAAA,WAAAmI,OAAA,GAAAd,GAAA,CAAAhF,WAAA,CAAAR,GAAA,4BAAAsG,OAAA,CAAA7D,OAAA,OAAA6D,OAAA,GAAAd,GAAA,CAAAhF,WAAA,CAAAR,GAAA,4BAAAsG,OAAA,CAAAC,OAAA,EAA4E;UAgCxEzJ,EAAA,CAAAI,SAAA,IAA0E;UAA1EJ,EAAA,CAAAqB,UAAA,WAAAqI,OAAA,GAAAhB,GAAA,CAAAhF,WAAA,CAAAR,GAAA,2BAAAwG,OAAA,CAAA/D,OAAA,OAAA+D,OAAA,GAAAhB,GAAA,CAAAhF,WAAA,CAAAR,GAAA,2BAAAwG,OAAA,CAAAD,OAAA,EAA0E;UAc1EzJ,EAAA,CAAAI,SAAA,GAAsF;UAAtFJ,EAAA,CAAAqB,UAAA,WAAAsI,OAAA,GAAAjB,GAAA,CAAAhF,WAAA,CAAAR,GAAA,iCAAAyG,OAAA,CAAAhE,OAAA,OAAAgE,OAAA,GAAAjB,GAAA,CAAAhF,WAAA,CAAAR,GAAA,iCAAAyG,OAAA,CAAAF,OAAA,EAAsF;UActFzJ,EAAA,CAAAI,SAAA,GAAkF;UAAlFJ,EAAA,CAAAqB,UAAA,WAAAuI,OAAA,GAAAlB,GAAA,CAAAhF,WAAA,CAAAR,GAAA,+BAAA0G,OAAA,CAAAjE,OAAA,OAAAiE,OAAA,GAAAlB,GAAA,CAAAhF,WAAA,CAAAR,GAAA,+BAAA0G,OAAA,CAAAH,OAAA,EAAkF;UA2CtFzJ,EAAA,CAAAI,SAAA,IAA4B;UAA5BJ,EAAA,CAAAqB,UAAA,SAAAqH,GAAA,CAAA/H,sBAAA,CAA4B;UAsBHX,EAAA,CAAAI,SAAA,GAAY;UAAZJ,EAAA,CAAAqB,UAAA,YAAAqH,GAAA,CAAA9F,SAAA,CAAY;UAqB1B5C,EAAA,CAAAI,SAAA,IAAW;UAAXJ,EAAA,CAAAqB,UAAA,SAAAqH,GAAA,CAAA3G,KAAA,CAAW;UAoBR/B,EAAA,CAAAI,SAAA,GAAgD;UAAhDJ,EAAA,CAAAqB,UAAA,aAAAqH,GAAA,CAAAhF,WAAA,CAAAiC,OAAA,IAAA+C,GAAA,CAAA/F,YAAA,CAAgD;UAEvC3C,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAAqB,UAAA,UAAAqH,GAAA,CAAA/F,YAAA,CAAmB;UACR3C,EAAA,CAAAI,SAAA,GAAkB;UAAlBJ,EAAA,CAAAqB,UAAA,SAAAqH,GAAA,CAAA/F,YAAA,CAAkB;UAC1D3C,EAAA,CAAAI,SAAA,GACF;UADEJ,EAAA,CAAAK,kBAAA,MAAAqI,GAAA,CAAA/F,YAAA,8DACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}