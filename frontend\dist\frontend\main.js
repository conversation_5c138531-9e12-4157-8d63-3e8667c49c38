"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([["main"],{

/***/ 4114:
/*!***************************************!*\
  !*** ./src/app/app-routing.module.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AppRoutingModule: () => (/* binding */ AppRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _layouts_front_layout_front_layout_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./layouts/front-layout/front-layout.component */ 3030);
/* harmony import */ var _layouts_admin_layout_admin_layout_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./layouts/admin-layout/admin-layout.component */ 134);
/* harmony import */ var _layouts_auth_admin_layout_auth_admin_layout_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./layouts/auth-admin-layout/auth-admin-layout.component */ 4564);
/* harmony import */ var _views_guards_guardadmin_guard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./views/guards/guardadmin.guard */ 3235);
/* harmony import */ var _views_guards_guarduser_guard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./views/guards/guarduser.guard */ 6039);
/* harmony import */ var _views_guards_noguarduser_guard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./views/guards/noguarduser.guard */ 8722);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/core */ 7580);









const routes = [
// Espace Front
{
  path: '',
  component: _layouts_front_layout_front_layout_component__WEBPACK_IMPORTED_MODULE_0__.FrontLayoutComponent,
  children: [{
    path: '',
    loadChildren: () => __webpack_require__.e(/*! import() */ "src_app_views_front_home_home_module_ts").then(__webpack_require__.bind(__webpack_require__, /*! ./views/front/home/<USER>/ 3608)).then(m => m.HomeModule)
  }, {
    path: 'profile',
    loadChildren: () => __webpack_require__.e(/*! import() */ "src_app_views_front_profile_profile_module_ts").then(__webpack_require__.bind(__webpack_require__, /*! ./views/front/profile/profile.module */ 7636)).then(m => m.ProfileModule),
    canActivateChild: [_views_guards_guarduser_guard__WEBPACK_IMPORTED_MODULE_4__.guarduserGuard]
  }, {
    path: 'messages',
    loadChildren: () => Promise.all(/*! import() */[__webpack_require__.e("default-src_app_views_front_messages_message-layout_message-layout_component_ts"), __webpack_require__.e("src_app_views_front_messages_messages_module_ts")]).then(__webpack_require__.bind(__webpack_require__, /*! ./views/front/messages/messages.module */ 8534)).then(m => m.MessagesModule),
    canActivateChild: [_views_guards_guarduser_guard__WEBPACK_IMPORTED_MODULE_4__.guarduserGuard]
  },
  // Modules temporairement désactivés - trop d'erreurs complexes à corriger
  // {
  //   path: 'plannings',
  //   loadChildren: () =>
  //     import('./views/front/plannings/plannings.module').then(
  //       (m) => m.PlanningsModule
  //     ),
  //   canActivateChild: [guarduserGuard],
  // },
  // {
  //   path: 'reunions',
  //   loadChildren: () =>
  //     import('./views/front/reunions/reunions.module').then(
  //       (m) => m.ReunionsModule
  //     ),
  //   canActivateChild: [guarduserGuard],
  // },
  {
    path: 'notifications',
    loadChildren: () => Promise.all(/*! import() */[__webpack_require__.e("default-src_app_views_front_messages_message-layout_message-layout_component_ts"), __webpack_require__.e("src_app_views_front_notifications_notifications_module_ts")]).then(__webpack_require__.bind(__webpack_require__, /*! ./views/front/notifications/notifications.module */ 6532)).then(m => m.NotificationsModule),
    canActivateChild: [_views_guards_guarduser_guard__WEBPACK_IMPORTED_MODULE_4__.guarduserGuard]
  }, {
    path: 'change-password',
    loadChildren: () => __webpack_require__.e(/*! import() */ "src_app_views_front_change-password_change-password_module_ts").then(__webpack_require__.bind(__webpack_require__, /*! ./views/front/change-password/change-password.module */ 5756)).then(m => m.ChangePasswordModule),
    canActivateChild: [_views_guards_guarduser_guard__WEBPACK_IMPORTED_MODULE_4__.guarduserGuard]
  }, {
    path: 'signup',
    loadChildren: () => __webpack_require__.e(/*! import() */ "src_app_views_front_signup_signup_module_ts").then(__webpack_require__.bind(__webpack_require__, /*! ./views/front/signup/signup.module */ 4418)).then(m => m.SignupModule),
    canActivateChild: [_views_guards_noguarduser_guard__WEBPACK_IMPORTED_MODULE_5__.noguarduserGuard]
  }, {
    path: 'login',
    loadChildren: () => __webpack_require__.e(/*! import() */ "src_app_views_front_login_login_module_ts").then(__webpack_require__.bind(__webpack_require__, /*! ./views/front/login/login.module */ 1764)).then(m => m.LoginModule),
    canActivateChild: [_views_guards_noguarduser_guard__WEBPACK_IMPORTED_MODULE_5__.noguarduserGuard]
  }, {
    path: 'verify-email',
    loadChildren: () => __webpack_require__.e(/*! import() */ "src_app_views_front_verify-email_verify-email_module_ts").then(__webpack_require__.bind(__webpack_require__, /*! ./views/front/verify-email/verify-email.module */ 7942)).then(m => m.VerifyEmailModule)
  }, {
    path: 'reset-password',
    loadChildren: () => __webpack_require__.e(/*! import() */ "src_app_views_front_reset-password_reset-password_module_ts").then(__webpack_require__.bind(__webpack_require__, /*! ./views/front/reset-password/reset-password.module */ 7400)).then(m => m.ResetPasswordModule)
  }, {
    path: 'forgot-password',
    loadChildren: () => __webpack_require__.e(/*! import() */ "src_app_views_front_forgot-password_forgot-password_module_ts").then(__webpack_require__.bind(__webpack_require__, /*! ./views/front/forgot-password/forgot-password.module */ 7824)).then(m => m.ForgotPasswordModule)
  }, {
    path: 'projects',
    loadChildren: () => Promise.all(/*! import() */[__webpack_require__.e("common"), __webpack_require__.e("src_app_views_front_projects_projects_module_ts")]).then(__webpack_require__.bind(__webpack_require__, /*! ./views/front/projects/projects.module */ 6138)).then(m => m.ProjectsModule),
    canActivateChild: [_views_guards_guarduser_guard__WEBPACK_IMPORTED_MODULE_4__.guarduserGuard] // Protection pour utilisateurs authentifiés
  }, {
    path: 'equipes',
    loadChildren: () => Promise.all(/*! import() */[__webpack_require__.e("default-node_modules_angular_cdk_fesm2022_a11y_mjs-node_modules_angular_cdk_fesm2022_scrolling_mjs"), __webpack_require__.e("default-src_app_services_ai_service_ts-src_app_services_equipe_service_ts-src_app_services_me-02f595"), __webpack_require__.e("src_app_views_front_equipes_equipes_module_ts")]).then(__webpack_require__.bind(__webpack_require__, /*! ./views/front/equipes/equipes.module */ 2012)).then(m => m.EquipesModule),
    canActivateChild: [_views_guards_guarduser_guard__WEBPACK_IMPORTED_MODULE_4__.guarduserGuard] // Protection pour utilisateurs authentifiés
  }]
},
//  Espace Admin
{
  path: 'admin',
  component: _layouts_admin_layout_admin_layout_component__WEBPACK_IMPORTED_MODULE_1__.AdminLayoutComponent,
  canActivate: [_views_guards_guardadmin_guard__WEBPACK_IMPORTED_MODULE_3__.guardadminGuard],
  children: [{
    path: '',
    loadChildren: () => __webpack_require__.e(/*! import() */ "src_app_views_admin_dashboard_dashboard_module_ts").then(__webpack_require__.bind(__webpack_require__, /*! ./views/admin/dashboard/dashboard.module */ 3804)).then(m => m.DashboardModule)
  }, {
    path: 'dashboard',
    loadChildren: () => __webpack_require__.e(/*! import() */ "src_app_views_admin_dashboard_dashboard_module_ts").then(__webpack_require__.bind(__webpack_require__, /*! ./views/admin/dashboard/dashboard.module */ 3804)).then(m => m.DashboardModule)
  }, {
    path: 'userdetails/:id',
    loadChildren: () => __webpack_require__.e(/*! import() */ "src_app_views_admin_userdetails_userdetails_module_ts").then(__webpack_require__.bind(__webpack_require__, /*! ./views/admin/userdetails/userdetails.module */ 4916)).then(m => m.UserdetailsModule)
  },
  // Modules temporairement désactivés - trop d'erreurs complexes à corriger
  // {
  //   path: 'plannings',
  //   loadChildren: () =>
  //     import('./views/admin/plannings/plannings.module').then(
  //       (m) => m.PlanningsModule
  //     ),
  // },
  // {
  //   path: 'reunions',
  //   loadChildren: () =>
  //     import('./views/admin/reunions/reunions.module').then(
  //       (m) => m.ReunionsModule
  //     ),
  // },
  {
    path: 'projects',
    loadChildren: () => Promise.all(/*! import() */[__webpack_require__.e("default-node_modules_angular_cdk_fesm2022_a11y_mjs-node_modules_angular_cdk_fesm2022_scrolling_mjs"), __webpack_require__.e("common"), __webpack_require__.e("src_app_views_admin_projects_projects_module_ts")]).then(__webpack_require__.bind(__webpack_require__, /*! ./views/admin/projects/projects.module */ 3078)).then(m => m.ProjectsModule),
    canActivateChild: [_views_guards_guarduser_guard__WEBPACK_IMPORTED_MODULE_4__.guarduserGuard]
  }, {
    path: 'profile',
    loadChildren: () => __webpack_require__.e(/*! import() */ "src_app_views_admin_profile_profile_module_ts").then(__webpack_require__.bind(__webpack_require__, /*! ./views/admin/profile/profile.module */ 1952)).then(m => m.ProfileModule)
  }, {
    path: 'equipes',
    loadChildren: () => Promise.all(/*! import() */[__webpack_require__.e("default-node_modules_angular_cdk_fesm2022_a11y_mjs-node_modules_angular_cdk_fesm2022_scrolling_mjs"), __webpack_require__.e("default-src_app_services_ai_service_ts-src_app_services_equipe_service_ts-src_app_services_me-02f595"), __webpack_require__.e("src_app_views_admin_equipes_equipes_module_ts")]).then(__webpack_require__.bind(__webpack_require__, /*! ./views/admin/equipes/equipes.module */ 9464)).then(m => m.EquipesModule)
  }]
},
//  Espace Auth-admin
{
  path: 'admin/login',
  component: _layouts_auth_admin_layout_auth_admin_layout_component__WEBPACK_IMPORTED_MODULE_2__.AuthAdminLayoutComponent
}];
class AppRoutingModule {
  static {
    this.ɵfac = function AppRoutingModule_Factory(t) {
      return new (t || AppRoutingModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineNgModule"]({
      type: AppRoutingModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineInjector"]({
      imports: [_angular_router__WEBPACK_IMPORTED_MODULE_7__.RouterModule.forRoot(routes), _angular_router__WEBPACK_IMPORTED_MODULE_7__.RouterModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵsetNgModuleScope"](AppRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_7__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_7__.RouterModule]
  });
})();

/***/ }),

/***/ 92:
/*!**********************************!*\
  !*** ./src/app/app.component.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AppComponent: () => (/* binding */ AppComponent)
/* harmony export */ });
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rxjs/operators */ 271);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _services_logger_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./services/logger.service */ 4798);
/* harmony import */ var _services_theme_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./services/theme.service */ 487);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/router */ 5072);






const _c0 = function (a0) {
  return {
    dark: a0
  };
};
class AppComponent {
  constructor(logger, themeService) {
    this.logger = logger;
    this.themeService = themeService;
    this.title = 'frontend';
    // Utiliser le nouveau système de thème
    this.isDarkMode$ = this.themeService.currentTheme$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.map)(theme => theme.name === 'dark'));
  }
  ngOnInit() {
    // Activer les logs en développement
    this.logger.setLogsEnabled(true);
    // Activer les logs pour tous les composants importants
    this.logger.enableComponentLogs('MessageService');
    this.logger.enableComponentLogs('MessageChat');
    this.logger.enableComponentLogs('CallService');
    this.logger.enableComponentLogs('IncomingCall');
    this.logger.enableComponentLogs('ActiveCall');
    this.logger.enableComponentLogs('VoiceMessage');
    this.logger.enableComponentLogs('NotificationService');
    console.log('🚀 [App] Application initialized with full logging enabled');
    this.logger.info('App', 'Application started successfully');
  }
  static {
    this.ɵfac = function AppComponent_Factory(t) {
      return new (t || AppComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_services_logger_service__WEBPACK_IMPORTED_MODULE_0__.LoggerService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_services_theme_service__WEBPACK_IMPORTED_MODULE_1__.ThemeService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineComponent"]({
      type: AppComponent,
      selectors: [["app-root"]],
      decls: 3,
      vars: 5,
      consts: [[1, "app-container", 3, "ngClass"]],
      template: function AppComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](1, "async");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](2, "router-outlet");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpureFunction1"](3, _c0, _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind1"](1, 1, ctx.isDarkMode$)));
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.NgClass, _angular_router__WEBPACK_IMPORTED_MODULE_5__.RouterOutlet, _angular_common__WEBPACK_IMPORTED_MODULE_4__.AsyncPipe],
      styles: [".app-container[_ngcontent-%COMP%] {\n  min-height: 100vh;\n  width: 100%;\n}\n\n.dark[_ngcontent-%COMP%] {\n  color-scheme: dark;\n}\n\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFwcC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsaUJBQWlCO0VBQ2pCLFdBQVc7QUFDYjs7QUFFQTtFQUNFLGtCQUFrQjtBQUNwQiIsImZpbGUiOiJhcHAuY29tcG9uZW50LmNzcyIsInNvdXJjZXNDb250ZW50IjpbIi5hcHAtY29udGFpbmVyIHtcbiAgbWluLWhlaWdodDogMTAwdmg7XG4gIHdpZHRoOiAxMDAlO1xufVxuXG4uZGFyayB7XG4gIGNvbG9yLXNjaGVtZTogZGFyaztcbn1cbiJdfQ== */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxpQkFBaUI7RUFDakIsV0FBVztBQUNiOztBQUVBO0VBQ0Usa0JBQWtCO0FBQ3BCOztBQUVBLGdhQUFnYSIsInNvdXJjZXNDb250ZW50IjpbIi5hcHAtY29udGFpbmVyIHtcbiAgbWluLWhlaWdodDogMTAwdmg7XG4gIHdpZHRoOiAxMDAlO1xufVxuXG4uZGFyayB7XG4gIGNvbG9yLXNjaGVtZTogZGFyaztcbn1cbiJdLCJzb3VyY2VSb290IjoiIn0= */"]
    });
  }
}

/***/ }),

/***/ 635:
/*!*******************************!*\
  !*** ./src/app/app.module.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AppModule: () => (/* binding */ AppModule),
/* harmony export */   jwtOptionsFactory: () => (/* binding */ jwtOptionsFactory)
/* harmony export */ });
/* harmony import */ var _angular_platform_browser__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/platform-browser */ 436);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _app_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./app-routing.module */ 4114);
/* harmony import */ var _app_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./app.component */ 92);
/* harmony import */ var _layouts_layouts_module__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./layouts/layouts.module */ 7431);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/common/http */ 6443);
/* harmony import */ var _auth0_angular_jwt__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @auth0/angular-jwt */ 2389);
/* harmony import */ var src_environments_environment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/environments/environment */ 5312);
/* harmony import */ var _angular_platform_browser_animations__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @angular/platform-browser/animations */ 3835);
/* harmony import */ var _graphql_module__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./graphql.module */ 2613);
/* harmony import */ var apollo_angular__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! apollo-angular */ 7797);
/* harmony import */ var _shared_shared_module__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./shared/shared.module */ 3887);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/core */ 7580);










// import { AiChatbotModule } from './components/ai-chatbot/ai-chatbot.module';


// Modules temporairement commentés - à créer si nécessaire
// import { CallModule } from './components/call/call.module';
// import { ConnectionStatusModule } from './components/connection-status/connection-status.module';
// import { GraphqlStatusModule } from './components/graphql-status/graphql-status.module';
// import { VoiceMessageModule } from './components/voice-message/voice-message.module';



// Factory simplifiée sans injection de JwtHelperService
function jwtOptionsFactory() {
  return {
    tokenGetter: () => {
      // Supprimer les logs pour améliorer les performances
      // if (!environment.production) {
      //   console.debug('JWT token retrieved from storage');
      // }
      return localStorage.getItem('token');
    },
    allowedDomains: [new URL(src_environments_environment__WEBPACK_IMPORTED_MODULE_3__.environment.urlBackend).hostname],
    disallowedRoutes: [`${new URL(src_environments_environment__WEBPACK_IMPORTED_MODULE_3__.environment.urlBackend).origin}/users/login`]
  };
}
class AppModule {
  static {
    this.ɵfac = function AppModule_Factory(t) {
      return new (t || AppModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineNgModule"]({
      type: AppModule,
      bootstrap: [_app_component__WEBPACK_IMPORTED_MODULE_1__.AppComponent]
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineInjector"]({
      imports: [_angular_platform_browser__WEBPACK_IMPORTED_MODULE_7__.BrowserModule, _angular_common__WEBPACK_IMPORTED_MODULE_8__.CommonModule, _app_routing_module__WEBPACK_IMPORTED_MODULE_0__.AppRoutingModule, _layouts_layouts_module__WEBPACK_IMPORTED_MODULE_2__.LayoutsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.FormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.ReactiveFormsModule, _angular_common_http__WEBPACK_IMPORTED_MODULE_10__.HttpClientModule, _angular_platform_browser_animations__WEBPACK_IMPORTED_MODULE_11__.BrowserAnimationsModule,
      // AiChatbotModule,
      _auth0_angular_jwt__WEBPACK_IMPORTED_MODULE_12__.JwtModule.forRoot({
        jwtOptionsProvider: {
          provide: _auth0_angular_jwt__WEBPACK_IMPORTED_MODULE_12__.JWT_OPTIONS,
          useFactory: jwtOptionsFactory
        }
      }), _graphql_module__WEBPACK_IMPORTED_MODULE_4__.GraphQLModule, apollo_angular__WEBPACK_IMPORTED_MODULE_13__.ApolloModule,
      // CallModule,
      // ConnectionStatusModule,
      // GraphqlStatusModule,
      // VoiceMessageModule,
      _shared_shared_module__WEBPACK_IMPORTED_MODULE_5__.SharedModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵsetNgModuleScope"](AppModule, {
    declarations: [_app_component__WEBPACK_IMPORTED_MODULE_1__.AppComponent],
    imports: [_angular_platform_browser__WEBPACK_IMPORTED_MODULE_7__.BrowserModule, _angular_common__WEBPACK_IMPORTED_MODULE_8__.CommonModule, _app_routing_module__WEBPACK_IMPORTED_MODULE_0__.AppRoutingModule, _layouts_layouts_module__WEBPACK_IMPORTED_MODULE_2__.LayoutsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.FormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.ReactiveFormsModule, _angular_common_http__WEBPACK_IMPORTED_MODULE_10__.HttpClientModule, _angular_platform_browser_animations__WEBPACK_IMPORTED_MODULE_11__.BrowserAnimationsModule, _auth0_angular_jwt__WEBPACK_IMPORTED_MODULE_12__.JwtModule, _graphql_module__WEBPACK_IMPORTED_MODULE_4__.GraphQLModule, apollo_angular__WEBPACK_IMPORTED_MODULE_13__.ApolloModule,
    // CallModule,
    // ConnectionStatusModule,
    // GraphqlStatusModule,
    // VoiceMessageModule,
    _shared_shared_module__WEBPACK_IMPORTED_MODULE_5__.SharedModule]
  });
})();

/***/ }),

/***/ 2613:
/*!***********************************!*\
  !*** ./src/app/graphql.module.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   GraphQLClientService: () => (/* binding */ GraphQLClientService),
/* harmony export */   GraphQLModule: () => (/* binding */ GraphQLModule),
/* harmony export */   createApollo: () => (/* binding */ createApollo)
/* harmony export */ });
/* harmony import */ var apollo_angular__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! apollo-angular */ 7797);
/* harmony import */ var _apollo_client_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @apollo/client/core */ 3032);
/* harmony import */ var _apollo_client_core__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @apollo/client/core */ 8599);
/* harmony import */ var _apollo_client_core__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @apollo/client/core */ 5525);
/* harmony import */ var apollo_angular_http__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! apollo-angular/http */ 5761);
/* harmony import */ var _apollo_client_link_subscriptions__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @apollo/client/link/subscriptions */ 3539);
/* harmony import */ var _apollo_client_utilities__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @apollo/client/utilities */ 6898);
/* harmony import */ var _environments_environment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../environments/environment */ 5312);
/* harmony import */ var _apollo_client_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @apollo/client/core */ 9613);
/* harmony import */ var _apollo_client_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @apollo/client/core */ 9350);
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/common/http */ 6443);
/* harmony import */ var _services_authuser_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./services/authuser.service */ 9271);
/* harmony import */ var graphql_ws__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! graphql-ws */ 9130);
/* harmony import */ var graphql_ws__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(graphql_ws__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var apollo_upload_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! apollo-upload-client */ 2667);
/* harmony import */ var apollo_upload_client__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(apollo_upload_client__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _apollo_client_link_error__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @apollo/client/link/error */ 7687);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rxjs */ 5797);
/* harmony import */ var _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./graphql/message.graphql */ 8896);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/core */ 7580);

















const createUploadLink = (apollo_upload_client__WEBPACK_IMPORTED_MODULE_4___default());
// Service pour gérer le client Apollo
class GraphQLClientService {
  constructor() {
    this.client = null;
    this.clientSubject = new rxjs__WEBPACK_IMPORTED_MODULE_5__.BehaviorSubject(null);
    this.client$ = this.clientSubject.asObservable();
  }
  setClient(client) {
    this.client = client;
    this.clientSubject.next(client);
  }
  getClient() {
    return this.client;
  }
  static {
    this.ɵfac = function GraphQLClientService_Factory(t) {
      return new (t || GraphQLClientService)();
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineInjectable"]({
      token: GraphQLClientService,
      factory: GraphQLClientService.ɵfac,
      providedIn: 'root'
    });
  }
}
// Fonction pour créer le client Apollo
function createApollo(httpLink, authService) {
  // Récupérer le token à chaque requête plutôt qu'à l'initialisation
  const getToken = () => authService.getToken();
  const httpUri = `${_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend.replace('/api/', '/graphql')}`;
  const wsUri = httpUri.replace('http', 'ws');
  // Lien d'erreur pour gérer les erreurs GraphQL
  const errorLink = (0,_apollo_client_link_error__WEBPACK_IMPORTED_MODULE_7__.onError)(({
    graphQLErrors,
    networkError,
    operation,
    forward
  }) => {
    if (graphQLErrors) {
      for (const err of graphQLErrors) {
        // Conserver uniquement les erreurs importantes
        if (err.message.includes('Enum "MessageType" cannot represent value')) {
          // Ignorer cette erreur spécifique
        } else {
          console.error(`[GraphQL error]: Message: ${err.message}, Location: ${err.locations}, Path: ${err.path}`, err);
        }
        // Gérer les erreurs d'authentification
        if (err.extensions?.['code'] === 'UNAUTHENTICATED') {
          // Afficher uniquement en développement
          if (_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.production === false) {
            console.warn('Authentication token is invalid or expired');
          }
          // Rediriger vers la page de connexion ou rafraîchir le token
        }
      }
    }

    if (networkError) {
      // Filtrer certaines erreurs réseau
      const errorMessage = networkError.toString();
      if (!errorMessage.includes('cors') && !errorMessage.includes('Failed to fetch')) {
        console.error(`[Network error]: ${networkError}`);
      }
    }
    return forward(operation);
  });
  // Lien pour transformer les variables et s'assurer que les énumérations sont correctement envoyées
  const transformVariablesLink = new _apollo_client_core__WEBPACK_IMPORTED_MODULE_8__.ApolloLink((operation, forward) => {
    const operationName = operation.operationName;
    const variables = operation.variables;
    // Traitement spécial pour les mutations SendMessage
    if (operationName === 'SendMessage' && variables['type']) {
      // S'assurer que le type est bien une valeur d'énumération en majuscules
      if (typeof variables['type'] === 'string' && variables['type'].toLowerCase() === 'text') {
        variables['type'] = 'TEXT';
      }
      // Logs activés
      console.log(`[GraphQL] SendMessage operation variables after transform:`, variables);
    }
    return forward(operation);
  });
  // Lien d'authentification pour ajouter le token à chaque requête
  const authLink = new _apollo_client_core__WEBPACK_IMPORTED_MODULE_8__.ApolloLink((operation, forward) => {
    const token = getToken();
    // Log des variables de l'opération pour le débogage
    const operationName = operation.operationName;
    const variables = operation.variables;
    if (operationName === 'SendMessage') {
      console.log(`[GraphQL] SendMessage operation variables:`, variables);
    }
    operation.setContext(({
      headers = {}
    }) => ({
      headers: {
        ...headers,
        authorization: token ? `Bearer ${token}` : ''
      }
    }));
    return forward(operation);
  });
  // Créer le lien pour l'upload de fichiers
  const uploadLink = createUploadLink({
    uri: httpUri,
    headers: {
      'Apollo-Require-Preflight': 'true'
    }
    // Les en-têtes d'authentification seront ajoutés par authLink
  });
  // Créer le lien HTTP standard
  const http = httpLink.create({
    uri: httpUri,
    headers: new _angular_common_http__WEBPACK_IMPORTED_MODULE_9__.HttpHeaders({
      'Content-Type': 'application/json'
    })
    // Les en-têtes d'authentification seront ajoutés par authLink
  });
  // Combiner les liens pour gérer les uploads
  const httpLinkSplit = (0,_apollo_client_core__WEBPACK_IMPORTED_MODULE_10__.split)(({
    query,
    variables
  }) => {
    const definition = (0,_apollo_client_utilities__WEBPACK_IMPORTED_MODULE_11__.getMainDefinition)(query);
    return definition.kind === 'OperationDefinition' && definition.operation === 'mutation' && variables?.['file'] !== undefined;
  }, uploadLink, http);
  // Combiner tous les liens HTTP
  let link = (0,_apollo_client_core__WEBPACK_IMPORTED_MODULE_12__.from)([transformVariablesLink, authLink, errorLink, httpLinkSplit]);
  // Ajouter le lien WebSocket pour les souscriptions
  if (typeof window !== 'undefined') {
    try {
      const wsClient = (0,graphql_ws__WEBPACK_IMPORTED_MODULE_2__.createClient)({
        url: wsUri,
        connectionParams: () => {
          const token = getToken();
          const userId = authService.getCurrentUserId();
          if (!token) {
            // console.warn('No token available for WebSocket connection');
            return {};
          }
          // Log désactivé
          // console.debug(
          //   'Setting up WebSocket connection with token and userId:',
          //   userId
          // );
          return {
            authorization: `Bearer ${token}`,
            userId: userId
          };
        },
        shouldRetry: err => {
          // Conserver uniquement les erreurs importantes
          if (err && err.toString().includes('critical')) {
            console.error('WebSocket critical error:', err);
          }
          return true;
        },
        retryAttempts: 10,
        keepAlive: 30000,
        on: {
          connected: () => {
            /* WebSocket connected successfully */
          },
          error: err => console.error('WebSocket connection error:', err),
          closed: () => {
            /* WebSocket connection closed */
          },
          connecting: () => {
            /* WebSocket connecting... */
          },
          ping: () => {
            /* Ping/Pong events */
          }
        }
      });
      const wsLink = new _apollo_client_link_subscriptions__WEBPACK_IMPORTED_MODULE_13__.GraphQLWsLink(wsClient);
      link = (0,_apollo_client_core__WEBPACK_IMPORTED_MODULE_10__.split)(({
        query
      }) => {
        const definition = (0,_apollo_client_utilities__WEBPACK_IMPORTED_MODULE_11__.getMainDefinition)(query);
        return definition.kind === 'OperationDefinition' && definition.operation === 'subscription';
      }, wsLink, link);
    } catch (error) {
      // Afficher uniquement en développement
      if (_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.production === false) {
        console.error('WebSocket initialization failed:', error);
      }
    }
  }
  return {
    link,
    cache: new _apollo_client_core__WEBPACK_IMPORTED_MODULE_14__.InMemoryCache({
      addTypename: false,
      typePolicies: {
        // Configuration pour gérer les uploads de fichiers
        Upload: {
          merge: true
        }
      }
    }),
    typeDefs: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_3__.typeDefs,
    defaultOptions: {
      watchQuery: {
        fetchPolicy: 'cache-and-network',
        errorPolicy: 'ignore'
      },
      query: {
        fetchPolicy: 'network-only',
        errorPolicy: 'all'
      },
      mutate: {
        errorPolicy: 'all'
      }
    }
  };
}
class GraphQLModule {
  constructor(graphQLClientService, authService, httpLink) {
    this.graphQLClientService = graphQLClientService;
    this.authService = authService;
    this.httpLink = httpLink;
    // Écouter les changements d'authentification pour recréer le client Apollo si nécessaire
    this.authService.authChange$.subscribe(() => {
      // Log désactivé
      // console.log(
      //   `Auth change detected: ${type}, token: ${token ? 'present' : 'absent'}`
      // );
      // Recréer le client Apollo avec les nouvelles informations d'authentification
      const newClient = new _apollo_client_core__WEBPACK_IMPORTED_MODULE_15__.ApolloClient(createApollo(this.httpLink, this.authService));
      this.graphQLClientService.setClient(newClient);
      // console.log(`Apollo client recreated after ${type}`);
    });
  }

  static {
    this.ɵfac = function GraphQLModule_Factory(t) {
      return new (t || GraphQLModule)(_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵinject"](GraphQLClientService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵinject"](_services_authuser_service__WEBPACK_IMPORTED_MODULE_1__.AuthuserService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵinject"](apollo_angular_http__WEBPACK_IMPORTED_MODULE_16__.HttpLink));
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineNgModule"]({
      type: GraphQLModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineInjector"]({
      providers: [GraphQLClientService, {
        provide: apollo_angular__WEBPACK_IMPORTED_MODULE_17__.APOLLO_OPTIONS,
        useFactory: createApollo,
        deps: [apollo_angular_http__WEBPACK_IMPORTED_MODULE_16__.HttpLink, _services_authuser_service__WEBPACK_IMPORTED_MODULE_1__.AuthuserService]
      }],
      imports: [apollo_angular__WEBPACK_IMPORTED_MODULE_17__.ApolloModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵsetNgModuleScope"](GraphQLModule, {
    exports: [apollo_angular__WEBPACK_IMPORTED_MODULE_17__.ApolloModule]
  });
})();

/***/ }),

/***/ 8896:
/*!********************************************!*\
  !*** ./src/app/graphql/message.graphql.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ACCEPT_CALL_MUTATION: () => (/* binding */ ACCEPT_CALL_MUTATION),
/* harmony export */   ADD_GROUP_PARTICIPANTS_MUTATION: () => (/* binding */ ADD_GROUP_PARTICIPANTS_MUTATION),
/* harmony export */   CALL_DETAILS_QUERY: () => (/* binding */ CALL_DETAILS_QUERY),
/* harmony export */   CALL_HISTORY_QUERY: () => (/* binding */ CALL_HISTORY_QUERY),
/* harmony export */   CALL_SIGNAL_SUBSCRIPTION: () => (/* binding */ CALL_SIGNAL_SUBSCRIPTION),
/* harmony export */   CALL_STATS_QUERY: () => (/* binding */ CALL_STATS_QUERY),
/* harmony export */   CALL_STATUS_CHANGED_SUBSCRIPTION: () => (/* binding */ CALL_STATUS_CHANGED_SUBSCRIPTION),
/* harmony export */   CONVERSATION_UPDATED_SUBSCRIPTION: () => (/* binding */ CONVERSATION_UPDATED_SUBSCRIPTION),
/* harmony export */   CREATE_CONVERSATION_MUTATION: () => (/* binding */ CREATE_CONVERSATION_MUTATION),
/* harmony export */   CREATE_GROUP_MUTATION: () => (/* binding */ CREATE_GROUP_MUTATION),
/* harmony export */   DELETE_ALL_NOTIFICATIONS_MUTATION: () => (/* binding */ DELETE_ALL_NOTIFICATIONS_MUTATION),
/* harmony export */   DELETE_GROUP_MUTATION: () => (/* binding */ DELETE_GROUP_MUTATION),
/* harmony export */   DELETE_MESSAGE_MUTATION: () => (/* binding */ DELETE_MESSAGE_MUTATION),
/* harmony export */   DELETE_MULTIPLE_NOTIFICATIONS_MUTATION: () => (/* binding */ DELETE_MULTIPLE_NOTIFICATIONS_MUTATION),
/* harmony export */   DELETE_NOTIFICATION_MUTATION: () => (/* binding */ DELETE_NOTIFICATION_MUTATION),
/* harmony export */   EDIT_MESSAGE_MUTATION: () => (/* binding */ EDIT_MESSAGE_MUTATION),
/* harmony export */   END_CALL_MUTATION: () => (/* binding */ END_CALL_MUTATION),
/* harmony export */   FORWARD_MESSAGE_MUTATION: () => (/* binding */ FORWARD_MESSAGE_MUTATION),
/* harmony export */   GET_ALL_USER_QUERY: () => (/* binding */ GET_ALL_USER_QUERY),
/* harmony export */   GET_CONVERSATIONS_QUERY: () => (/* binding */ GET_CONVERSATIONS_QUERY),
/* harmony export */   GET_CONVERSATION_QUERY: () => (/* binding */ GET_CONVERSATION_QUERY),
/* harmony export */   GET_CURRENT_USER_QUERY: () => (/* binding */ GET_CURRENT_USER_QUERY),
/* harmony export */   GET_GROUP_QUERY: () => (/* binding */ GET_GROUP_QUERY),
/* harmony export */   GET_MESSAGES_QUERY: () => (/* binding */ GET_MESSAGES_QUERY),
/* harmony export */   GET_NOTIFICATIONS_ATTACHAMENTS: () => (/* binding */ GET_NOTIFICATIONS_ATTACHAMENTS),
/* harmony export */   GET_NOTIFICATIONS_QUERY: () => (/* binding */ GET_NOTIFICATIONS_QUERY),
/* harmony export */   GET_UNREAD_MESSAGES_QUERY: () => (/* binding */ GET_UNREAD_MESSAGES_QUERY),
/* harmony export */   GET_USER_GROUPS_QUERY: () => (/* binding */ GET_USER_GROUPS_QUERY),
/* harmony export */   GET_USER_QUERY: () => (/* binding */ GET_USER_QUERY),
/* harmony export */   GET_VOICE_MESSAGES_QUERY: () => (/* binding */ GET_VOICE_MESSAGES_QUERY),
/* harmony export */   INCOMING_CALL_SUBSCRIPTION: () => (/* binding */ INCOMING_CALL_SUBSCRIPTION),
/* harmony export */   INITIATE_CALL_MUTATION: () => (/* binding */ INITIATE_CALL_MUTATION),
/* harmony export */   LEAVE_GROUP_MUTATION: () => (/* binding */ LEAVE_GROUP_MUTATION),
/* harmony export */   MARK_AS_READ_MUTATION: () => (/* binding */ MARK_AS_READ_MUTATION),
/* harmony export */   MARK_NOTIFICATION_READ_MUTATION: () => (/* binding */ MARK_NOTIFICATION_READ_MUTATION),
/* harmony export */   MESSAGE_SENT_SUBSCRIPTION: () => (/* binding */ MESSAGE_SENT_SUBSCRIPTION),
/* harmony export */   NOTIFICATIONS_READ_SUBSCRIPTION: () => (/* binding */ NOTIFICATIONS_READ_SUBSCRIPTION),
/* harmony export */   NOTIFICATION_SUBSCRIPTION: () => (/* binding */ NOTIFICATION_SUBSCRIPTION),
/* harmony export */   PIN_MESSAGE_MUTATION: () => (/* binding */ PIN_MESSAGE_MUTATION),
/* harmony export */   REACT_TO_MESSAGE_MUTATION: () => (/* binding */ REACT_TO_MESSAGE_MUTATION),
/* harmony export */   REJECT_CALL_MUTATION: () => (/* binding */ REJECT_CALL_MUTATION),
/* harmony export */   REMOVE_GROUP_PARTICIPANTS_MUTATION: () => (/* binding */ REMOVE_GROUP_PARTICIPANTS_MUTATION),
/* harmony export */   SEARCH_MESSAGES_QUERY: () => (/* binding */ SEARCH_MESSAGES_QUERY),
/* harmony export */   SEND_CALL_SIGNAL_MUTATION: () => (/* binding */ SEND_CALL_SIGNAL_MUTATION),
/* harmony export */   SEND_MESSAGE_MUTATION: () => (/* binding */ SEND_MESSAGE_MUTATION),
/* harmony export */   SET_USER_OFFLINE_MUTATION: () => (/* binding */ SET_USER_OFFLINE_MUTATION),
/* harmony export */   SET_USER_ONLINE_MUTATION: () => (/* binding */ SET_USER_ONLINE_MUTATION),
/* harmony export */   START_TYPING_MUTATION: () => (/* binding */ START_TYPING_MUTATION),
/* harmony export */   STOP_TYPING_MUTATION: () => (/* binding */ STOP_TYPING_MUTATION),
/* harmony export */   TOGGLE_CALL_MEDIA_MUTATION: () => (/* binding */ TOGGLE_CALL_MEDIA_MUTATION),
/* harmony export */   TYPING_INDICATOR_SUBSCRIPTION: () => (/* binding */ TYPING_INDICATOR_SUBSCRIPTION),
/* harmony export */   UPDATE_GROUP_MUTATION: () => (/* binding */ UPDATE_GROUP_MUTATION),
/* harmony export */   USER_STATUS_SUBSCRIPTION: () => (/* binding */ USER_STATUS_SUBSCRIPTION),
/* harmony export */   typeDefs: () => (/* binding */ typeDefs)
/* harmony export */ });
/* harmony import */ var apollo_angular__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! apollo-angular */ 7797);

// Définir les types GraphQL
const typeDefs = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  enum MessageType {
    TEXT
    IMAGE
    FILE
    AUDIO
    VIDEO
    SYSTEM
    VOICE_MESSAGE
  }
`;
// Message Mutations
const SEND_MESSAGE_MUTATION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  mutation SendMessage(
    $receiverId: ID!
    $content: String
    $file: Upload
    $type: MessageType
    $metadata: JSON
  ) {
    sendMessage(
      receiverId: $receiverId
      content: $content
      file: $file
      type: $type
      metadata: $metadata
    ) {
      id
      content
      type
      timestamp
      isRead
      sender {
        id
        username
        image
      }
      conversation {
        id
      }
      attachments {
        url
        type
        duration
      }
      metadata
    }
  }
`;
const MARK_AS_READ_MUTATION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  mutation MarkMessageAsRead($messageId: ID!) {
    markMessageAsRead(messageId: $messageId) {
      id
      isRead
      readAt
    }
  }
`;
const EDIT_MESSAGE_MUTATION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  mutation EditMessage($messageId: ID!, $newContent: String!) {
    editMessage(messageId: $messageId, newContent: $newContent) {
      id
      content
      isEdited
      updatedAt
    }
  }
`;
const DELETE_MESSAGE_MUTATION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  mutation DeleteMessage($messageId: ID!) {
    deleteMessage(messageId: $messageId) {
      id
      isDeleted
      deletedAt
    }
  }
`;
const GET_MESSAGES_QUERY = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  query GetMessages(
    $senderId: ID!
    $receiverId: ID!
    $conversationId: ID!
    $page: Int
    $limit: Int = 25
  ) {
    getMessages(
      senderId: $senderId
      receiverId: $receiverId
      conversationId: $conversationId
      page: $page
      limit: $limit
    ) {
      id
      content
      type
      timestamp
      isRead
      status
      sender {
        id
        username
        image
      }
      attachments {
        url
        type
        name
        size
      }
    }
  }
`;
// Conversation Queries
const GET_CONVERSATIONS_QUERY = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  query GetConversations {
    getConversations {
      id
      participants {
        id
        username
        image
        isOnline
      }
      lastMessage {
        id
        content
        timestamp
        isRead
        sender {
          id
          username
        }
      }
      unreadCount
      updatedAt
    }
  }
`;
const GET_CONVERSATION_QUERY = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  query GetConversation(
    $conversationId: ID!
    $limit: Int = 10
    $offset: Int = 0
  ) {
    getConversation(conversationId: $conversationId) {
      id
      participants {
        id
        username
        image
        isOnline
      }
      messages(limit: $limit, offset: $offset) {
        id
        content
        type
        timestamp
        isRead
        sender {
          id
          username
          image
        }
        receiver {
          id
          username
          image
        }
        attachments {
          url
          type
          duration
        }
        metadata
        conversationId
      }
    }
  }
`;
// User Queries
const GET_USER_QUERY = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  query GetOneUser($id: ID!) {
    getOneUser(id: $id) {
      id
      username
      email
      image
      isOnline
      lastActive
    }
  }
`;
const GET_ALL_USER_QUERY = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  query GetAllUsers(
    $search: String
    $page: Int
    $limit: Int
    $sortBy: String
    $sortOrder: String
    $isOnline: Boolean
  ) {
    getAllUsers(
      search: $search
      page: $page
      limit: $limit
      sortBy: $sortBy
      sortOrder: $sortOrder
      isOnline: $isOnline
    ) {
      users {
        id
        username
        email
        image
        isOnline
        lastActive
      }
      totalCount
      totalPages
      currentPage
      hasNextPage
      hasPreviousPage
    }
  }
`;
// Status Mutations
const SET_USER_ONLINE_MUTATION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  mutation SetUserOnline($userId: ID!) {
    setUserOnline(userId: $userId) {
      id
      isOnline
      lastActive
    }
  }
`;
const SET_USER_OFFLINE_MUTATION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  mutation SetUserOffline($userId: ID!) {
    setUserOffline(userId: $userId) {
      id
      isOnline
      lastActive
    }
  }
`;
// Search Query
const SEARCH_MESSAGES_QUERY = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  query SearchMessages($query: String!, $conversationId: ID) {
    searchMessages(query: $query, conversationId: $conversationId) {
      id
      content
      timestamp
      sender {
        id
        username
      }
    }
  }
`;
// Unread Messages Query
const GET_UNREAD_MESSAGES_QUERY = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  query GetUnreadMessages($userId: ID!) {
    getUnreadMessages(userId: $userId) {
      id
      content
      timestamp
      sender {
        id
        username
        image
      }
      conversation {
        id
      }
    }
  }
`;
// Subscriptions
const MESSAGE_SENT_SUBSCRIPTION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  subscription MessageSent($conversationId: ID!) {
    messageSent(conversationId: $conversationId) {
      id
      content
      type
      timestamp
      isRead
      sender {
        id
        username
        image
      }
      conversation {
        id
      }
      attachments {
        url
        type
        duration
      }
      metadata
    }
  }
`;
const USER_STATUS_SUBSCRIPTION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  subscription UserStatusChanged {
    userStatusChanged {
      id
      username
      isOnline
      lastActive
    }
  }
`;
const CONVERSATION_UPDATED_SUBSCRIPTION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  subscription ConversationUpdated($conversationId: ID!) {
    conversationUpdated(conversationId: $conversationId) {
      id
      participants {
        id
        username
        image
        isOnline
      }
      lastMessage {
        id
        content
        timestamp
        isRead
        sender {
          id
          username
        }
      }
      unreadCount
      updatedAt
    }
  }
`;
// Notification Queries
const GET_NOTIFICATIONS_QUERY = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  query GetUserNotifications {
    getUserNotifications {
      id
      type
      content
      timestamp
      isRead
      senderId {
        id
        username
        image
      }
      message {
        id
        content
      }
      readAt
      relatedEntity
      metadata
    }
  }
`;
const GET_NOTIFICATIONS_ATTACHAMENTS = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  query GetNotificationAttachments($id: ID!) {
    getNotificationAttachments(notificationId: $id) {
      url
      type
      name
      size
    }
  }
`;
const MARK_NOTIFICATION_READ_MUTATION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  mutation MarkNotificationsAsRead($notificationIds: [ID!]!) {
    markNotificationsAsRead(notificationIds: $notificationIds) {
      success
      readCount
      remainingCount
    }
  }
`;
const DELETE_NOTIFICATION_MUTATION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  mutation DeleteNotification($notificationId: ID!) {
    deleteNotification(notificationId: $notificationId) {
      success
      message
    }
  }
`;
const DELETE_MULTIPLE_NOTIFICATIONS_MUTATION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  mutation DeleteMultipleNotifications($notificationIds: [ID!]!) {
    deleteMultipleNotifications(notificationIds: $notificationIds) {
      success
      count
      message
    }
  }
`;
const DELETE_ALL_NOTIFICATIONS_MUTATION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  mutation DeleteAllNotifications {
    deleteAllNotifications {
      success
      count
      message
    }
  }
`;
const NOTIFICATION_SUBSCRIPTION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  subscription NotificationReceived {
    notificationReceived {
      id
      type
      content
      timestamp
      isRead
      senderId {
        id
        username
        image
      }
      message {
        id
        content
      }
      readAt
      relatedEntity
      metadata
    }
  }
`;
const NOTIFICATIONS_READ_SUBSCRIPTION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  subscription NotificationsRead {
    notificationsRead
  }
`;
// group Queries
const GET_GROUP_QUERY = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  query GetGroup($id: ID!) {
    getGroup(id: $id) {
      id
      name
      photo
      description
      participants {
        id
        username
        email
        image
        isOnline
      }
      admins {
        id
        username
        email
        image
        isOnline
      }
      messageCount
      createdAt
      updatedAt
    }
  }
`;
const GET_USER_GROUPS_QUERY = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  query GetUserGroups($userId: ID!) {
    getUserGroups(userId: $userId) {
      id
      name
      photo
      description
      participants {
        id
        username
        image
        isOnline
      }
      admins {
        id
        username
        image
        isOnline
      }
      messageCount
      createdAt
      updatedAt
    }
  }
`;
const CREATE_GROUP_MUTATION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  mutation CreateGroup(
    $name: String!
    $participantIds: [ID!]!
    $photo: Upload
    $description: String
  ) {
    createGroup(
      name: $name
      participantIds: $participantIds
      photo: $photo
      description: $description
    ) {
      id
      name
      photo
      description
      participants {
        id
        username
        image
      }
      admins {
        id
        username
        image
      }
    }
  }
`;
const UPDATE_GROUP_MUTATION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  mutation UpdateGroup($id: ID!, $input: UpdateGroupInput!) {
    updateGroup(id: $id, input: $input) {
      id
      name
      photo
      description
      participants {
        id
        username
        image
      }
      admins {
        id
        username
        image
      }
    }
  }
`;
const DELETE_GROUP_MUTATION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  mutation DeleteGroup($id: ID!) {
    deleteGroup(id: $id) {
      success
      message
    }
  }
`;
const ADD_GROUP_PARTICIPANTS_MUTATION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  mutation AddGroupParticipants($groupId: ID!, $participantIds: [ID!]!) {
    addGroupParticipants(groupId: $groupId, participantIds: $participantIds) {
      id
      participants {
        id
        username
        image
      }
    }
  }
`;
const REMOVE_GROUP_PARTICIPANTS_MUTATION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  mutation RemoveGroupParticipants($groupId: ID!, $participantIds: [ID!]!) {
    removeGroupParticipants(
      groupId: $groupId
      participantIds: $participantIds
    ) {
      id
      participants {
        id
        username
        image
      }
    }
  }
`;
const LEAVE_GROUP_MUTATION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  mutation LeaveGroup($groupId: ID!) {
    leaveGroup(groupId: $groupId) {
      success
      message
    }
  }
`;
// Add to exports
const TYPING_INDICATOR_SUBSCRIPTION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  subscription TypingIndicator($conversationId: ID!) {
    typingIndicator(conversationId: $conversationId) {
      conversationId
      userId
      isTyping
    }
  }
`;
const START_TYPING_MUTATION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  mutation StartTyping($input: TypingInput!) {
    startTyping(input: $input)
  }
`;
const STOP_TYPING_MUTATION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  mutation StopTyping($input: TypingInput!) {
    stopTyping(input: $input)
  }
`;
const GET_CURRENT_USER_QUERY = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  query GetCurrentUser {
    getCurrentUser {
      id
      username
      email
      image
      isOnline
      lastActive
      createdAt
      updatedAt
    }
  }
`;
const REACT_TO_MESSAGE_MUTATION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  mutation ReactToMessage($messageId: ID!, $emoji: String!) {
    reactToMessage(messageId: $messageId, emoji: $emoji) {
      id
      reactions {
        userId
        emoji
        createdAt
      }
    }
  }
`;
const FORWARD_MESSAGE_MUTATION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  mutation ForwardMessage($messageId: ID!, $conversationIds: [ID!]!) {
    forwardMessage(messageId: $messageId, conversationIds: $conversationIds) {
      id
      content
      timestamp
      sender {
        id
        username
      }
      conversation {
        id
      }
    }
  }
`;
const PIN_MESSAGE_MUTATION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  mutation PinMessage($messageId: ID!, $conversationId: ID!) {
    pinMessage(messageId: $messageId, conversationId: $conversationId) {
      id
      pinned
      pinnedAt
      pinnedBy {
        id
        username
      }
    }
  }
`;
const CREATE_CONVERSATION_MUTATION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  mutation CreateConversation($userId: ID!) {
    createConversation(userId: $userId) {
      id
      participants {
        id
        username
        image
        isOnline
      }
      lastMessage {
        id
        content
        timestamp
      }
      unreadCount
      updatedAt
    }
  }
`;
// Call Queries
const CALL_HISTORY_QUERY = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  query CallHistory(
    $limit: Int
    $offset: Int
    $status: [CallStatus]
    $type: [CallType]
    $startDate: String
    $endDate: String
  ) {
    callHistory(
      limit: $limit
      offset: $offset
      status: $status
      type: $type
      startDate: $startDate
      endDate: $endDate
    ) {
      id
      caller {
        id
        username
        image
      }
      recipient {
        id
        username
        image
      }
      type
      status
      startTime
      endTime
      duration
      conversationId
    }
  }
`;
const CALL_DETAILS_QUERY = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  query CallDetails($callId: ID!) {
    callDetails(callId: $callId) {
      id
      caller {
        id
        username
        image
      }
      recipient {
        id
        username
        image
      }
      type
      status
      startTime
      endTime
      duration
      conversationId
      metadata
    }
  }
`;
const CALL_STATS_QUERY = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  query CallStats {
    callStats {
      totalCalls
      totalDuration
      missedCalls
      callsByType {
        type
        count
      }
      averageCallDuration
      mostCalledUser {
        id
        username
        image
      }
    }
  }
`;
// Call Mutations
const INITIATE_CALL_MUTATION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  mutation InitiateCall(
    $recipientId: ID!
    $callType: CallType!
    $callId: String!
    $offer: String
    $conversationId: ID
    $options: CallOptions
  ) {
    initiateCall(
      recipientId: $recipientId
      callType: $callType
      callId: $callId
      offer: $offer
      conversationId: $conversationId
      options: $options
    ) {
      id
      caller {
        id
        username
        image
      }
      recipient {
        id
        username
        image
      }
      type
      status
      startTime
      conversationId
    }
  }
`;
const SEND_CALL_SIGNAL_MUTATION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  mutation SendCallSignal(
    $callId: ID!
    $signalType: String!
    $signalData: String!
  ) {
    sendCallSignal(
      callId: $callId
      signalType: $signalType
      signalData: $signalData
    ) {
      success
      message
    }
  }
`;
const ACCEPT_CALL_MUTATION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  mutation AcceptCall($callId: ID!, $answer: String) {
    acceptCall(callId: $callId, answer: $answer) {
      id
      status
    }
  }
`;
const REJECT_CALL_MUTATION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  mutation RejectCall($callId: ID!, $reason: String) {
    rejectCall(callId: $callId, reason: $reason) {
      id
      status
    }
  }
`;
const END_CALL_MUTATION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  mutation EndCall($callId: ID!, $feedback: CallFeedbackInput) {
    endCall(callId: $callId, feedback: $feedback) {
      id
      status
      endTime
      duration
    }
  }
`;
const TOGGLE_CALL_MEDIA_MUTATION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  mutation ToggleCallMedia($callId: ID!, $video: Boolean, $audio: Boolean) {
    toggleCallMedia(callId: $callId, video: $video, audio: $audio) {
      success
      message
    }
  }
`;
// Call Subscriptions
const CALL_SIGNAL_SUBSCRIPTION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  subscription CallSignal($callId: ID) {
    callSignal(callId: $callId) {
      callId
      senderId
      type
      data
      timestamp
    }
  }
`;
const INCOMING_CALL_SUBSCRIPTION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  subscription IncomingCall {
    incomingCall {
      id
      caller {
        id
        username
        image
      }
      recipient {
        id
        username
        image
      }
      type
      conversationId
      offer
      timestamp
    }
  }
`;
const CALL_STATUS_CHANGED_SUBSCRIPTION = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  subscription CallStatusChanged($callId: ID) {
    callStatusChanged(callId: $callId) {
      id
      status
      endTime
      duration
    }
  }
`;
// Requête pour récupérer les messages vocaux
const GET_VOICE_MESSAGES_QUERY = (0,apollo_angular__WEBPACK_IMPORTED_MODULE_0__.gql)`
  query GetVoiceMessages {
    getVoiceMessages {
      id
      caller {
        id
        username
        image
      }
      recipient {
        id
        username
        image
      }
      type
      status
      startTime
      endTime
      duration
      conversationId
      metadata
    }
  }
`;

/***/ }),

/***/ 134:
/*!****************************************************************!*\
  !*** ./src/app/layouts/admin-layout/admin-layout.component.ts ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AdminLayoutComponent: () => (/* binding */ AdminLayoutComponent)
/* harmony export */ });
/* harmony import */ var _angular_animations__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/animations */ 7172);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rxjs/operators */ 271);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var src_app_services_authadmin_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/services/authadmin.service */ 4667);
/* harmony import */ var src_app_services_authuser_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/services/authuser.service */ 9271);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _app_services_theme_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @app/services/theme.service */ 487);
/* harmony import */ var src_app_services_data_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/services/data.service */ 8490);









function AdminLayoutComponent_div_83_Template(rf, ctx) {
  if (rf & 1) {
    const _r7 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 86)(1, "div", 87);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function AdminLayoutComponent_div_83_Template_div_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r7);
      const ctx_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r6.toggleMobileMenu());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](2, "div", 88)(3, "div", 89)(4, "div", 90);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](5, "svg", 91);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](6, "path", 10)(7, "path", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](8, "span", 92);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](9, "DevBridge");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](10, "button", 93);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function AdminLayoutComponent_div_83_Template_button_click_10_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r7);
      const ctx_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r8.toggleMobileMenu());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](11, "svg", 94);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](12, "path", 95);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](13, "div", 14)(14, "nav", 15)(15, "a", 96);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function AdminLayoutComponent_div_83_Template_a_click_15_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r7);
      const ctx_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r9.toggleMobileMenu());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](16, "svg", 97);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](17, "path", 98);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](18, " Dashboard ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](19, "a", 99);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function AdminLayoutComponent_div_83_Template_a_click_19_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r7);
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r10.toggleMobileMenu());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](20, "svg", 97);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](21, "path", 100);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](22, " profile ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](23, "a", 101);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function AdminLayoutComponent_div_83_Template_a_click_23_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r7);
      const ctx_r11 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r11.toggleMobileMenu());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](24, "svg", 97);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](25, "path", 102);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](26, " Reunions ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](27, "a", 103);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function AdminLayoutComponent_div_83_Template_a_click_27_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r7);
      const ctx_r12 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r12.toggleMobileMenu());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](28, "svg", 97);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](29, "path", 104);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](30, " Planning ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](31, "a", 105);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](32, "svg", 97);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](33, "path", 106);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](34, " Projects ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](35, "a", 107);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](36, "svg", 97);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](37, "path", 108);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](38, " Student Rendus ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](39, "a", 109);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](40, "svg", 97);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](41, "path", 110);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](42, " \u00C9valuations ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](43, "a", 111);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function AdminLayoutComponent_div_83_Template_a_click_43_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r7);
      const ctx_r13 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r13.toggleMobileMenu());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](44, "i", 112);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](45, " Equipes ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](46, "a", 113);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function AdminLayoutComponent_div_83_Template_a_click_46_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r7);
      const ctx_r14 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r14.toggleMobileMenu());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](47, "i", 114);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](48, " Back Home ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()()();
  }
}
function AdminLayoutComponent_i_119_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](0, "i", 115);
  }
}
function AdminLayoutComponent_i_121_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](0, "i", 116);
  }
}
function AdminLayoutComponent_div_133_Template(rf, ctx) {
  if (rf & 1) {
    const _r16 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 117)(1, "a", 118);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function AdminLayoutComponent_div_133_Template_a_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r16);
      const ctx_r15 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r15.openLogoutModal());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](2, "div", 90)(3, "div", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](4, "i", 119)(5, "div", 120);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](6, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](7, "Logout");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("@fadeIn", undefined);
  }
}
function AdminLayoutComponent_div_153_Template(rf, ctx) {
  if (rf & 1) {
    const _r18 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 121)(1, "div", 122)(2, "div", 123);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](3, "div", 124);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](4, "div", 125)(5, "div", 41);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](6, "div", 126)(7, "div", 127);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](8, "div", 128)(9, "div", 129)(10, "div", 130);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](11, "svg", 131);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](12, "path", 132);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](13, "div", 133);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](14, "div", 134)(15, "h3", 135);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](16, " Ready to Leave? ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](17, "div", 136)(18, "p", 137);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](19, " Are you sure you want to logout? ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](20, "div", 138)(21, "button", 139);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function AdminLayoutComponent_div_153_Template_button_click_21_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r18);
      const ctx_r17 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r17.logout());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](22, "div", 140)(23, "div", 141);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](24, "span", 142);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](25, "i", 143);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](26, " Logout ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](27, "button", 144);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function AdminLayoutComponent_div_153_Template_button_click_27_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r18);
      const ctx_r19 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r19.closeLogoutModal());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](28, "span", 145);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](29, "i", 146);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](30, " Cancel ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()()()();
  }
}
function AdminLayoutComponent_button_154_Template(rf, ctx) {
  if (rf & 1) {
    const _r21 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "button", 147);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function AdminLayoutComponent_button_154_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r21);
      const ctx_r20 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r20.scrollToTop());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](1, "div", 148)(2, "div", 149);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](3, "svg", 150);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](4, "path", 151);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
  }
}
const _c0 = function () {
  return {
    exact: true
  };
};
const _c1 = function (a0) {
  return {
    "rotate-180": a0
  };
};
class AdminLayoutComponent {
  constructor(location, authAdminService, authService, router, themeService, dataService) {
    this.location = location;
    this.authAdminService = authAdminService;
    this.authService = authService;
    this.router = router;
    this.themeService = themeService;
    this.dataService = dataService;
    this.username = '';
    this.imageProfile = '';
    this.mobileMenuOpen = false;
    this.userMenuOpen = false;
    this.showLogoutModal = false;
    this.showScrollButton = false;
    this.currentYear = new Date().getFullYear();
    this.subscriptions = [];
    this.loadUserProfile();
    this.isDarkMode$ = this.themeService.currentTheme$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(theme => theme.name === 'dark'));
  }
  loadUserProfile() {
    const user = this.authAdminService.getUser();
    this.username = user?.fullName || user?.username || '';
    // Toujours utiliser l'image par défaut si l'image de profil est null, 'null' ou vide
    if (user?.profileImage && user.profileImage !== 'null' && user.profileImage.trim() !== '') {
      this.imageProfile = user.profileImage;
    } else if (user?.image && user.image !== 'null' && user.image.trim() !== '') {
      this.imageProfile = user.image;
    } else {
      this.imageProfile = 'assets/images/default-profile.png';
    }
    console.log('Admin layout - Image profile loaded:', this.imageProfile);
  }
  ngOnInit() {
    this.checkScrollPosition();
    // S'abonner aux changements d'image de profil
    const profileSub = this.dataService.currentUser$.subscribe(user => {
      if (user) {
        this.username = user.fullName || user.username || '';
        // Toujours utiliser l'image par défaut si l'image de profil est null, 'null' ou vide
        if (user.profileImage && user.profileImage !== 'null' && user.profileImage.trim() !== '') {
          this.imageProfile = user.profileImage;
        } else if (user.image && user.image !== 'null' && user.image.trim() !== '') {
          this.imageProfile = user.image;
        } else {
          this.imageProfile = 'assets/images/default-profile.png';
        }
        console.log('Admin layout - Image profile updated:', this.imageProfile);
      }
    });
    this.subscriptions.push(profileSub);
  }
  ngOnDestroy() {
    // Désabonner de tous les observables pour éviter les fuites de mémoire
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }
  checkScrollPosition() {
    this.showScrollButton = window.pageYOffset > 300;
  }
  scrollToTop() {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }
  toggleMobileMenu() {
    this.mobileMenuOpen = !this.mobileMenuOpen;
  }
  toggleUserMenu() {
    this.userMenuOpen = !this.userMenuOpen;
  }
  openLogoutModal() {
    this.showLogoutModal = true;
    this.userMenuOpen = false;
  }
  closeLogoutModal() {
    this.showLogoutModal = false;
  }
  logout() {
    this.authService.logout().subscribe({
      next: () => {
        this.userMenuOpen = false;
        this.showLogoutModal = false;
        this.authService.clearAuthData();
        this.authAdminService.clearAuthData();
        setTimeout(() => {
          this.router.navigate(['/admin/login'], {
            queryParams: {
              message: 'Déconnexion réussie'
            },
            replaceUrl: true
          });
        }, 100);
      },
      error: err => {
        console.error('Logout error:', err);
        this.authService.clearAuthData();
        this.authAdminService.clearAuthData();
        setTimeout(() => {
          this.router.navigate(['/admin/login'], {
            queryParams: {
              message: 'Déconnexion effectuée'
            },
            replaceUrl: true
          });
        }, 100);
      }
    });
  }
  goBack() {
    this.location.back();
  }
  toggleDarkMode() {
    this.themeService.toggleTheme();
  }
  static {
    this.ɵfac = function AdminLayoutComponent_Factory(t) {
      return new (t || AdminLayoutComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_common__WEBPACK_IMPORTED_MODULE_6__.Location), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](src_app_services_authadmin_service__WEBPACK_IMPORTED_MODULE_0__.AuthadminService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](src_app_services_authuser_service__WEBPACK_IMPORTED_MODULE_1__.AuthuserService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_7__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_app_services_theme_service__WEBPACK_IMPORTED_MODULE_2__.ThemeService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](src_app_services_data_service__WEBPACK_IMPORTED_MODULE_3__.DataService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineComponent"]({
      type: AdminLayoutComponent,
      selectors: [["app-admin-layout"]],
      hostBindings: function AdminLayoutComponent_HostBindings(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("scroll", function AdminLayoutComponent_scroll_HostBindingHandler() {
            return ctx.checkScrollPosition();
          }, false, _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresolveWindow"]);
        }
      },
      decls: 155,
      vars: 24,
      consts: [[1, "flex", "h-screen", "main-grid-container", "futuristic-layout"], [1, "background-grid"], [1, "hidden", "md:flex", "md:flex-shrink-0"], [1, "flex", "flex-col", "w-64", "bg-white", "dark:bg-[#1e1e1e]", "border-r", "border-[#edf1f4]", "dark:border-[#2a2a2a]", "backdrop-blur-sm"], [1, "flex", "items-center", "justify-center", "h-16", "px-4", "relative", "overflow-hidden"], [1, "absolute", "-top-6", "-left-6", "w-12", "h-12", "bg-gradient-to-br", "from-[#4f5fad]/20", "to-transparent", "rounded-full"], [1, "absolute", "-bottom-6", "-right-6", "w-12", "h-12", "bg-gradient-to-tl", "from-[#4f5fad]/20", "to-transparent", "rounded-full"], [1, "flex", "items-center", "relative", "z-10"], [1, "relative"], ["xmlns", "http://www.w3.org/2000/svg", "fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "h-8", "w-8", "text-[#4f5fad]", "dark:text-[#6d78c9]", "transform", "rotate-12"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M15 12a3 3 0 11-6 0 3 3 0 016 0z"], [1, "absolute", "inset-0", "bg-[#4f5fad]/20", "dark:bg-[#6d78c9]/20", "blur-xl", "rounded-full", "transform", "scale-150", "-z-10"], [1, "ml-2", "text-xl", "font-bold", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "bg-clip-text", "text-transparent"], [1, "flex", "flex-col", "flex-grow", "px-4", "py-4"], [1, "flex-1", "space-y-2"], ["routerLink", "/admin/dashboard", "routerLinkActive", "active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium", 1, "sidebar-nav-link", "group", "flex", "items-center", "px-4", "py-3", "text-sm", "font-medium", "rounded-l-md", "text-[#6d6870]", "dark:text-[#a0a0a0]", "hover:bg-[#edf1f4]", "dark:hover:bg-[#2a2a2a]", "hover:text-[#4f5fad]", "dark:hover:text-[#6d78c9]", "transition-all"], [1, "absolute", "inset-0", "w-1", "bg-gradient-to-b", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "opacity-0", "group-hover:opacity-100", "transition-opacity"], [1, "relative", "z-10", "flex", "items-center"], [1, "fas", "fa-th-large", "h-5", "w-5", "mr-3", "text-[#4f5fad]", "dark:text-[#6d78c9]", "group-hover:text-[#3d4a85]", "dark:group-hover:text-[#4f5fad]", "transition-all", "group-hover:scale-110"], ["routerLink", "/admin/profile", "routerLinkActive", "active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium", 1, "sidebar-nav-link", "group", "flex", "items-center", "px-4", "py-3", "text-sm", "font-medium", "rounded-l-md", "text-[#6d6870]", "dark:text-[#a0a0a0]", "hover:bg-[#edf1f4]", "dark:hover:bg-[#2a2a2a]", "hover:text-[#4f5fad]", "dark:hover:text-[#6d78c9]", "transition-all"], [1, "fas", "fa-user-shield", "h-5", "w-5", "mr-3", "text-[#4f5fad]", "dark:text-[#6d78c9]", "group-hover:text-[#3d4a85]", "dark:group-hover:text-[#4f5fad]", "transition-all", "group-hover:scale-110"], ["routerLink", "/admin/reunions", "routerLinkActive", "active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium", 1, "sidebar-nav-link", "group", "flex", "items-center", "px-4", "py-3", "text-sm", "font-medium", "rounded-l-md", "text-[#6d6870]", "dark:text-[#a0a0a0]", "hover:bg-[#edf1f4]", "dark:hover:bg-[#2a2a2a]", "hover:text-[#4f5fad]", "dark:hover:text-[#6d78c9]", "transition-all"], [1, "fas", "fa-users-cog", "h-5", "w-5", "mr-3", "text-[#4f5fad]", "dark:text-[#6d78c9]", "group-hover:text-[#3d4a85]", "dark:group-hover:text-[#4f5fad]", "transition-all", "group-hover:scale-110"], ["routerLink", "/admin/plannings", "routerLinkActive", "active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium", 1, "sidebar-nav-link", "group", "flex", "items-center", "px-4", "py-3", "text-sm", "font-medium", "rounded-l-md", "text-[#6d6870]", "dark:text-[#a0a0a0]", "hover:bg-[#edf1f4]", "dark:hover:bg-[#2a2a2a]", "hover:text-[#4f5fad]", "dark:hover:text-[#6d78c9]", "transition-all"], [1, "far", "fa-calendar-check", "h-5", "w-5", "mr-3", "text-[#4f5fad]", "dark:text-[#6d78c9]", "group-hover:text-[#3d4a85]", "dark:group-hover:text-[#4f5fad]", "transition-all", "group-hover:scale-110"], ["routerLink", "/admin/projects", "routerLinkActive", "active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium", 1, "sidebar-nav-link", "group", "flex", "items-center", "px-4", "py-3", "text-sm", "font-medium", "rounded-l-md", "text-[#6d6870]", "dark:text-[#a0a0a0]", "hover:bg-[#edf1f4]", "dark:hover:bg-[#2a2a2a]", "hover:text-[#4f5fad]", "dark:hover:text-[#6d78c9]", "transition-all"], [1, "fas", "fa-rocket", "h-5", "w-5", "mr-3", "text-[#4f5fad]", "dark:text-[#6d78c9]", "group-hover:text-[#3d4a85]", "dark:group-hover:text-[#4f5fad]", "transition-all", "group-hover:scale-110"], ["routerLink", "/admin/projects/rendus", "routerLinkActive", "active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium", 1, "sidebar-nav-link", "group", "flex", "items-center", "px-4", "py-3", "text-sm", "font-medium", "rounded-l-md", "text-[#6d6870]", "dark:text-[#a0a0a0]", "hover:bg-[#edf1f4]", "dark:hover:bg-[#2a2a2a]", "hover:text-[#4f5fad]", "dark:hover:text-[#6d78c9]", "transition-all"], [1, "fas", "fa-file-upload", "h-5", "w-5", "mr-3", "text-[#4f5fad]", "dark:text-[#6d78c9]", "group-hover:text-[#3d4a85]", "dark:group-hover:text-[#4f5fad]", "transition-all", "group-hover:scale-110"], ["routerLink", "/admin/projects/evaluations", "routerLinkActive", "active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium", 1, "sidebar-nav-link", "group", "flex", "items-center", "px-4", "py-3", "text-sm", "font-medium", "rounded-l-md", "text-[#6d6870]", "dark:text-[#a0a0a0]", "hover:bg-[#edf1f4]", "dark:hover:bg-[#2a2a2a]", "hover:text-[#4f5fad]", "dark:hover:text-[#6d78c9]", "transition-all"], [1, "fas", "fa-clipboard-check", "h-5", "w-5", "mr-3", "text-[#4f5fad]", "dark:text-[#6d78c9]", "group-hover:text-[#3d4a85]", "dark:group-hover:text-[#4f5fad]", "transition-all", "group-hover:scale-110"], ["routerLink", "/admin/equipes", "routerLinkActive", "active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium", 1, "sidebar-nav-link", "group", "flex", "items-center", "px-4", "py-3", "text-sm", "font-medium", "rounded-l-md", "text-[#6d6870]", "dark:text-[#a0a0a0]", "hover:bg-[#edf1f4]", "dark:hover:bg-[#2a2a2a]", "hover:text-[#4f5fad]", "dark:hover:text-[#6d78c9]", "transition-all"], [1, "fas", "fa-users", "h-5", "w-5", "mr-3", "text-[#4f5fad]", "dark:text-[#6d78c9]", "group-hover:text-[#3d4a85]", "dark:group-hover:text-[#4f5fad]", "transition-all", "group-hover:scale-110"], [1, "absolute", "inset-0", "bg-[#4f5fad]/20", "dark:bg-[#6d78c9]/20", "opacity-0", "group-hover:opacity-100", "transition-opacity", "blur-md", "rounded-full"], ["routerLink", "/", "routerLinkActive", "active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium", 1, "sidebar-nav-link", "group", "flex", "items-center", "px-4", "py-3", "text-sm", "font-medium", "rounded-l-md", "text-[#6d6870]", "dark:text-[#a0a0a0]", "hover:bg-[#edf1f4]", "dark:hover:bg-[#2a2a2a]", "hover:text-[#4f5fad]", "dark:hover:text-[#6d78c9]", "transition-all", 3, "routerLinkActiveOptions"], [1, "fas", "fa-home", "h-5", "w-5", "mr-3", "text-[#4f5fad]", "dark:text-[#6d78c9]", "group-hover:text-[#3d4a85]", "dark:group-hover:text-[#4f5fad]", "transition-colors"], ["class", "md:hidden fixed inset-0 z-40", 4, "ngIf"], [1, "flex-1", "flex", "flex-col", "overflow-hidden"], [1, "bg-white", "dark:bg-[#1e1e1e]", "shadow-md", "dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]", "z-10", "border-b", "border-[#edf1f4]/50", "dark:border-[#2a2a2a]", "backdrop-blur-sm"], [1, "flex", "items-center", "justify-between", "h-16", "px-4", "relative"], [1, "absolute", "inset-0", "overflow-hidden", "pointer-events-none"], [1, "absolute", "top-0", "left-1/4", "w-px", "h-full", "bg-gradient-to-b", "from-transparent", "via-[#4f5fad]/10", "dark:via-[#6d78c9]/5", "to-transparent"], [1, "absolute", "top-0", "right-1/3", "w-px", "h-full", "bg-gradient-to-b", "from-transparent", "via-[#4f5fad]/5", "dark:via-[#6d78c9]/3", "to-transparent"], [1, "md:hidden", "flex", "items-center", "justify-center", "h-8", "w-8", "rounded-md", "text-[#6d6870]", "dark:text-[#a0a0a0]", "hover:bg-[#edf1f4]", "dark:hover:bg-[#2a2a2a]", "focus:outline-none", "transition-colors", "relative", "group", 3, "click"], [1, "absolute", "inset-0", "bg-[#4f5fad]/10", "dark:bg-[#6d78c9]/10", "opacity-0", "group-hover:opacity-100", "transition-opacity", "rounded-md", "blur-md"], ["xmlns", "http://www.w3.org/2000/svg", "fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "h-5", "w-5", "relative", "z-10"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M4 6h16M4 12h16M4 18h16"], [1, "text-sm", "font-medium", "text-[#4f5fad]", "dark:text-[#6d78c9]", "ml-2", "relative", "z-10"], [1, "hidden", "md:flex", "items-center", "px-3", "py-1", "rounded-md", "text-sm", "font-medium", "text-[#4f5fad]", "dark:text-[#6d78c9]", "hover:bg-[#edf1f4]", "dark:hover:bg-[#2a2a2a]", "transition-all", "duration-200", "relative", "group", "overflow-hidden", 3, "click"], ["xmlns", "http://www.w3.org/2000/svg", "fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "h-5", "w-5", "mr-1", "group-hover:scale-110", "transition-transform"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M10 19l-7-7m0 0l7-7m-7 7h18"], [1, "flex-1", "max-w-md", "ml-4", "md:ml-6"], [1, "relative", "group"], [1, "absolute", "inset-y-0", "left-0", "pl-3", "flex", "items-center", "pointer-events-none"], ["xmlns", "http://www.w3.org/2000/svg", "fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "h-5", "w-5", "text-[#6d6870]", "dark:text-[#a0a0a0]", "group-focus-within:text-[#4f5fad]", "dark:group-focus-within:text-[#6d78c9]", "transition-colors"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"], ["type", "text", "placeholder", "Search...", 1, "block", "w-full", "pl-10", "pr-3", "py-2", "border", "border-[#bdc6cc]", "dark:border-[#2a2a2a]", "rounded-md", "leading-5", "bg-white", "dark:bg-[#1e1e1e]", "text-[#6d6870]", "dark:text-[#e0e0e0]", "placeholder-[#bdc6cc]", "dark:placeholder-[#6d6870]", "focus:outline-none", "focus:ring-2", "focus:ring-[#4f5fad]", "dark:focus:ring-[#6d78c9]", "focus:border-[#4f5fad]", "dark:focus:border-[#6d78c9]", "transition-all"], [1, "absolute", "inset-y-0", "left-0", "pl-3", "flex", "items-center", "pointer-events-none", "opacity-0", "group-focus-within:opacity-100", "transition-opacity"], [1, "w-0.5", "h-4", "bg-gradient-to-b", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "rounded-full"], [1, "ml-4", "flex", "items-center", "md:ml-6"], ["aria-label", "Toggle dark mode", 1, "flex", "items-center", "justify-center", "h-8", "w-8", "rounded-full", "bg-[#edf1f4]", "dark:bg-[#2a2a2a]", "hover:bg-[#dce4ec]", "dark:hover:bg-[#3a3a3a]", "text-[#4f5fad]", "dark:text-[#6d78c9]", "mr-3", "transition-all", "duration-300", "relative", "overflow-hidden", "group", 3, "click"], [1, "absolute", "inset-0", "rounded-full", "overflow-hidden"], [1, "absolute", "inset-0", "rounded-full", "border", "border-[#4f5fad]/20", "dark:border-[#6d78c9]/20", "opacity-0", "group-hover:opacity-100", "transition-opacity"], [1, "absolute", "-inset-1", "bg-gradient-to-r", "from-[#4f5fad]/0", "via-[#4f5fad]/30", "to-[#4f5fad]/0", "dark:from-[#6d78c9]/0", "dark:via-[#6d78c9]/30", "dark:to-[#6d78c9]/0", "opacity-0", "group-hover:opacity-100", "blur-sm", "animate-shine"], [1, "absolute", "inset-0", "bg-[#4f5fad]/10", "dark:bg-[#6d78c9]/10", "opacity-0", "group-hover:opacity-100", "transition-opacity", "blur-md"], [1, "relative", "z-10", "transition-all", "duration-500", "ease-in-out", 3, "ngClass"], ["class", "far fa-moon group-hover:scale-110 transition-transform", 4, "ngIf"], ["class", "far fa-sun group-hover:scale-110 transition-transform", 4, "ngIf"], [1, "flex", "items-center", "text-sm", "rounded-full", "focus:outline-none", "focus:ring-2", "focus:ring-[#4f5fad]", "dark:focus:ring-[#6d78c9]", "transition-all", "group", 3, "click"], [1, "sr-only"], [1, "hidden", "md:inline-block", "mr-2", "text-sm", "font-medium", "text-[#6d6870]", "dark:text-[#a0a0a0]", "group-hover:text-[#4f5fad]", "dark:group-hover:text-[#6d78c9]", "transition-colors"], [1, "h-8", "w-8", "rounded-full", "overflow-hidden", "flex", "items-center", "justify-center", "border-2", "border-[#4f5fad]", "dark:border-[#6d78c9]", "group-hover:border-[#3d4a85]", "dark:group-hover:border-[#4f5fad]", "transition-colors", "relative"], ["alt", "Profile", 1, "h-full", "w-full", "object-cover", 3, "src"], ["class", "origin-top-right absolute right-0 mt-2 w-48 rounded-lg shadow-lg dark:shadow-[0_10px_25px_rgba(0,0,0,0.3)] bg-white dark:bg-[#1e1e1e] border border-[#edf1f4]/50 dark:border-[#2a2a2a] py-1 z-50 backdrop-blur-sm", 4, "ngIf"], [1, "flex-1", "overflow-y-auto", "bg-[#edf1f4]", "dark:bg-[#121212]", "p-4", "md:p-6", "relative"], [1, "absolute", "top-[15%]", "left-[10%]", "w-64", "h-64", "rounded-full", "bg-gradient-to-br", "from-[#4f5fad]/5", "to-transparent", "dark:from-[#6d78c9]/3", "dark:to-transparent", "blur-3xl"], [1, "absolute", "bottom-[20%]", "right-[10%]", "w-80", "h-80", "rounded-full", "bg-gradient-to-tl", "from-[#4f5fad]/5", "to-transparent", "dark:from-[#6d78c9]/3", "dark:to-transparent", "blur-3xl"], [1, "relative", "z-10"], [1, "bg-white", "dark:bg-[#1e1e1e]", "border-t", "border-[#edf1f4]/50", "dark:border-[#2a2a2a]", "py-4", "relative", "overflow-hidden"], [1, "container", "mx-auto", "px-4", "text-center", "text-sm", "text-[#6d6870]", "dark:text-[#a0a0a0]", "relative", "z-10"], [1, "flex", "items-center", "justify-center"], [1, "relative", "mr-2"], ["xmlns", "http://www.w3.org/2000/svg", "fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "h-4", "w-4", "text-[#4f5fad]", "dark:text-[#6d78c9]"], ["class", "fixed inset-0 overflow-y-auto z-50", 4, "ngIf"], ["class", "fixed bottom-6 right-6 p-3 rounded-full shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#4f5fad] dark:focus:ring-[#6d78c9] overflow-hidden group", 3, "click", 4, "ngIf"], [1, "md:hidden", "fixed", "inset-0", "z-40"], [1, "fixed", "inset-0", "bg-gray-600", "bg-opacity-75", 3, "click"], [1, "relative", "flex", "flex-col", "w-72", "bg-white", "h-full"], [1, "flex", "items-center", "justify-between", "h-16", "px-4"], [1, "flex", "items-center"], ["xmlns", "http://www.w3.org/2000/svg", "fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "h-8", "w-8", "text-[#4f5fad]"], [1, "ml-2", "text-xl", "font-bold", "text-[#4f5fad]"], [1, "text-[#6d6870]", 3, "click"], ["xmlns", "http://www.w3.org/2000/svg", "fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "h-6", "w-6"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M6 18L18 6M6 6l12 12"], ["routerLink", "/admin/dashboard", 1, "group", "flex", "items-center", "px-4", "py-3", "text-sm", "font-medium", "rounded-md", "text-[#6d6870]", "hover:bg-[#edf1f4]", "hover:text-[#4f5fad]", "transition-colors", 3, "click"], ["xmlns", "http://www.w3.org/2000/svg", "fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "h-5", "w-5", "mr-3"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"], ["routerLink", "/admin/profile", 1, "group", "flex", "items-center", "px-4", "py-3", "text-sm", "font-medium", "rounded-md", "text-[#6d6870]", "hover:bg-[#edf1f4]", "hover:text-[#4f5fad]", "transition-colors", 3, "click"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"], ["routerLink", "/admin/reunions", 1, "group", "flex", "items-center", "px-4", "py-3", "text-sm", "font-medium", "rounded-md", "text-[#6d6870]", "hover:bg-[#edf1f4]", "hover:text-[#4f5fad]", "transition-colors", 3, "click"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"], ["routerLink", "/admin/plannings", 1, "group", "flex", "items-center", "px-4", "py-3", "text-sm", "font-medium", "rounded-md", "text-[#6d6870]", "hover:bg-[#edf1f4]", "hover:text-[#4f5fad]", "transition-colors", 3, "click"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"], ["routerLink", "/admin/projects", "routerLinkActive", "bg-[#edf1f4] text-[#4f5fad] font-medium", 1, "group", "flex", "items-center", "px-4", "py-3", "text-sm", "font-medium", "rounded-md", "text-[#6d6870]", "hover:bg-[#edf1f4]", "hover:text-[#4f5fad]", "transition-colors"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"], ["routerLink", "/admin/projects/rendus", "routerLinkActive", "bg-[#edf1f4] text-[#4f5fad] font-medium", 1, "group", "flex", "items-center", "px-4", "py-3", "text-sm", "font-medium", "rounded-md", "text-[#6d6870]", "hover:bg-[#edf1f4]", "hover:text-[#4f5fad]", "transition-colors"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"], ["routerLink", "/admin/projects/evaluations", "routerLinkActive", "bg-[#edf1f4] text-[#4f5fad] font-medium", 1, "group", "flex", "items-center", "px-4", "py-3", "text-sm", "font-medium", "rounded-md", "text-[#6d6870]", "hover:bg-[#edf1f4]", "hover:text-[#4f5fad]", "transition-colors"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"], ["routerLink", "/admin/equipes", 1, "group", "flex", "items-center", "px-4", "py-3", "text-sm", "font-medium", "rounded-md", "text-[#6d6870]", "hover:bg-[#edf1f4]", "hover:text-[#4f5fad]", "transition-colors", 3, "click"], [1, "fas", "fa-users", "h-5", "w-5", "mr-3"], ["routerLink", "/", 1, "group", "flex", "items-center", "px-4", "py-3", "text-sm", "font-medium", "rounded-md", "text-[#6d6870]", "hover:bg-[#edf1f4]", "hover:text-[#4f5fad]", "transition-colors", 3, "click"], [1, "fas", "fa-home", "h-5", "w-5", "mr-3", "text-[#4f5fad]"], [1, "far", "fa-moon", "group-hover:scale-110", "transition-transform"], [1, "far", "fa-sun", "group-hover:scale-110", "transition-transform"], [1, "origin-top-right", "absolute", "right-0", "mt-2", "w-48", "rounded-lg", "shadow-lg", "dark:shadow-[0_10px_25px_rgba(0,0,0,0.3)]", "bg-white", "dark:bg-[#1e1e1e]", "border", "border-[#edf1f4]/50", "dark:border-[#2a2a2a]", "py-1", "z-50", "backdrop-blur-sm"], [1, "block", "px-4", "py-2", "text-sm", "text-[#6d6870]", "dark:text-[#a0a0a0]", "hover:bg-[#edf1f4]", "dark:hover:bg-[#2a2a2a]", "hover:text-[#4f5fad]", "dark:hover:text-[#6d78c9]", "transition-colors", "group", "cursor-pointer", 3, "click"], [1, "fas", "fa-sign-out-alt", "mr-2", "text-[#ff6b69]", "dark:text-[#ff8785]", "group-hover:scale-110", "transition-transform"], [1, "absolute", "inset-0", "bg-[#ff6b69]/20", "dark:bg-[#ff8785]/20", "opacity-0", "group-hover:opacity-100", "transition-opacity", "blur-md", "rounded-full"], [1, "fixed", "inset-0", "overflow-y-auto", "z-50"], [1, "flex", "items-center", "justify-center", "min-h-screen", "pt-4", "px-4", "pb-20", "text-center", "sm:block", "sm:p-0"], ["aria-hidden", "true", 1, "fixed", "inset-0", "transition-opacity"], [1, "absolute", "inset-0", "bg-black/50", "dark:bg-black/70", "backdrop-blur-sm"], [1, "inline-block", "align-bottom", "bg-white", "dark:bg-[#1e1e1e]", "rounded-lg", "text-left", "overflow-hidden", "shadow-xl", "dark:shadow-[0_10px_25px_rgba(0,0,0,0.3)]", "transform", "transition-all", "sm:my-8", "sm:align-middle", "sm:max-w-lg", "sm:w-full", "border", "border-[#edf1f4]/50", "dark:border-[#2a2a2a]", "relative"], [1, "absolute", "top-[10%]", "left-[5%]", "w-32", "h-32", "rounded-full", "bg-gradient-to-br", "from-[#4f5fad]/5", "to-transparent", "dark:from-[#6d78c9]/3", "dark:to-transparent", "blur-2xl"], [1, "absolute", "bottom-[10%]", "right-[5%]", "w-40", "h-40", "rounded-full", "bg-gradient-to-tl", "from-[#ff6b69]/5", "to-transparent", "dark:from-[#ff8785]/3", "dark:to-transparent", "blur-2xl"], [1, "bg-white", "dark:bg-[#1e1e1e]", "px-4", "pt-5", "pb-4", "sm:p-6", "sm:pb-4", "relative", "z-10"], [1, "sm:flex", "sm:items-start"], [1, "mx-auto", "flex-shrink-0", "flex", "items-center", "justify-center", "h-12", "w-12", "rounded-full", "bg-[#ff6b69]/10", "dark:bg-[#ff6b69]/5", "sm:mx-0", "sm:h-10", "sm:w-10", "relative"], ["xmlns", "http://www.w3.org/2000/svg", "fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "h-6", "w-6", "text-[#ff6b69]", "dark:text-[#ff8785]"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"], [1, "absolute", "inset-0", "bg-[#ff6b69]/20", "dark:bg-[#ff8785]/20", "blur-xl", "rounded-full", "transform", "scale-150", "-z-10"], [1, "mt-3", "text-center", "sm:mt-0", "sm:ml-4", "sm:text-left"], [1, "text-lg", "leading-6", "font-medium", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "bg-clip-text", "text-transparent"], [1, "mt-2"], [1, "text-sm", "text-[#6d6870]", "dark:text-[#a0a0a0]"], [1, "bg-[#edf1f4]", "dark:bg-[#161616]", "px-4", "py-3", "sm:px-6", "sm:flex", "sm:flex-row-reverse", "border-t", "border-[#edf1f4]/50", "dark:border-[#2a2a2a]", "relative", "z-10"], ["type", "button", 1, "w-full", "inline-flex", "justify-center", "rounded-md", "px-4", "py-2", "text-base", "font-medium", "text-white", "sm:ml-3", "sm:w-auto", "sm:text-sm", "relative", "overflow-hidden", "group", 3, "click"], [1, "absolute", "inset-0", "bg-gradient-to-r", "from-[#ff6b69]", "to-[#ff8785]", "dark:from-[#ff6b69]", "dark:to-[#ff8785]", "rounded-md", "transition-transform", "duration-300", "group-hover:scale-105"], [1, "absolute", "inset-0", "bg-gradient-to-r", "from-[#ff6b69]", "to-[#ff8785]", "dark:from-[#ff6b69]", "dark:to-[#ff8785]", "rounded-md", "opacity-0", "group-hover:opacity-100", "blur-xl", "transition-opacity", "duration-300"], [1, "relative", "flex", "items-center"], [1, "fas", "fa-sign-out-alt", "mr-1.5"], ["type", "button", 1, "mt-3", "w-full", "inline-flex", "justify-center", "rounded-md", "border", "border-[#bdc6cc]", "dark:border-[#2a2a2a]", "px-4", "py-2", "bg-white", "dark:bg-[#1e1e1e]", "text-base", "font-medium", "text-[#6d6870]", "dark:text-[#a0a0a0]", "hover:bg-[#edf1f4]", "dark:hover:bg-[#2a2a2a]", "focus:outline-none", "focus:ring-2", "focus:ring-[#4f5fad]", "dark:focus:ring-[#6d78c9]", "sm:mt-0", "sm:ml-3", "sm:w-auto", "sm:text-sm", "transition-all", "group", 3, "click"], [1, "relative", "flex", "items-center", "group-hover:text-[#4f5fad]", "dark:group-hover:text-[#6d78c9]", "transition-colors"], [1, "fas", "fa-times", "mr-1.5"], [1, "fixed", "bottom-6", "right-6", "p-3", "rounded-full", "shadow-lg", "focus:outline-none", "focus:ring-2", "focus:ring-offset-2", "focus:ring-[#4f5fad]", "dark:focus:ring-[#6d78c9]", "overflow-hidden", "group", 3, "click"], [1, "absolute", "inset-0", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#3d4a85]", "dark:to-[#6d78c9]", "rounded-full", "transition-transform", "duration-300", "group-hover:scale-110"], [1, "absolute", "inset-0", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#3d4a85]", "dark:to-[#6d78c9]", "rounded-full", "opacity-0", "group-hover:opacity-100", "blur-xl", "transition-opacity", "duration-300"], ["xmlns", "http://www.w3.org/2000/svg", "fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "h-5", "w-5", "text-white", "relative", "z-10"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M5 10l7-7m0 0l7 7m-7-7v18"]],
      template: function AdminLayoutComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipe"](1, "async");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](2, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](3, "div", 2)(4, "div", 3)(5, "div", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](6, "div", 5)(7, "div", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](8, "div", 7)(9, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](10, "svg", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](11, "path", 10)(12, "path", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](13, "div", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](14, "span", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](15, "DevBridge");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](16, "div", 14)(17, "nav", 15)(18, "a", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](19, "span", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](20, "div", 18)(21, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](22, "i", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](23, "span", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](24, "Dashboard");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](25, "a", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](26, "span", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](27, "div", 18)(28, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](29, "i", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](30, "span", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](31, "Profile");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](32, "a", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](33, "span", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](34, "div", 18)(35, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](36, "i", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](37, "span", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](38, "Reunions");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](39, "a", 24);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](40, "span", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](41, "div", 18)(42, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](43, "i", 25);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](44, "span", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](45, "Plannings");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](46, "a", 26);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](47, "span", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](48, "div", 18)(49, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](50, "i", 27);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](51, "span", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](52, "Projects");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](53, "a", 28);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](54, "span", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](55, "div", 18)(56, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](57, "i", 29);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](58, "span", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](59, "Student Rendus");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](60, "a", 30);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](61, "span", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](62, "div", 18)(63, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](64, "i", 31);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](65, "span", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](66, "\u00C9valuations");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](67, "a", 32);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](68, "span", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](69, "div", 18)(70, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](71, "i", 33)(72, "div", 34);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](73, "span", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](74, "Equipes");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](75, "a", 35);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](76, "span", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](77, "div", 18)(78, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](79, "i", 36)(80, "div", 34);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](81, "span", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](82, "Back Home");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](83, AdminLayoutComponent_div_83_Template, 49, 0, "div", 37);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](84, "div", 38)(85, "header", 39)(86, "div", 40)(87, "div", 41);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](88, "div", 42)(89, "div", 43);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](90, "button", 44);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function AdminLayoutComponent_Template_button_click_90_listener() {
            return ctx.toggleMobileMenu();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](91, "div", 45);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](92, "svg", 46);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](93, "path", 47);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](94, "span", 48);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](95, "Menu");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](96, "button", 49);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function AdminLayoutComponent_Template_button_click_96_listener() {
            return ctx.goBack();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](97, "div", 45);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](98, "div", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](99, "svg", 50);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](100, "path", 51);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](101, "span");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](102, "Back");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](103, "div", 52)(104, "div", 53)(105, "div", 54);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](106, "svg", 55);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](107, "path", 56);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](108, "input", 57);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](109, "div", 58);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](110, "div", 59);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](111, "div", 60)(112, "button", 61);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function AdminLayoutComponent_Template_button_click_112_listener() {
            return ctx.toggleDarkMode();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](113, "div", 62);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](114, "div", 63)(115, "div", 64);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](116, "div", 65);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](117, "div", 66);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipe"](118, "async");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](119, AdminLayoutComponent_i_119_Template, 1, 0, "i", 67);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipe"](120, "async");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](121, AdminLayoutComponent_i_121_Template, 1, 0, "i", 68);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipe"](122, "async");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](123, "div", 60)(124, "div", 8)(125, "button", 69);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function AdminLayoutComponent_Template_button_click_125_listener() {
            return ctx.toggleUserMenu();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](126, "span", 70);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](127, "Open user menu");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](128, "span", 71);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](129);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](130, "div", 72);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](131, "div", 65)(132, "img", 73);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](133, AdminLayoutComponent_div_133_Template, 8, 1, "div", 74);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](134, "main", 75)(135, "div", 41);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](136, "div", 76)(137, "div", 77);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](138, "div", 78);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](139, "router-outlet");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](140, "footer", 79)(141, "div", 41);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](142, "div", 42)(143, "div", 43);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](144, "div", 80)(145, "div", 81)(146, "div", 82);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](147, "svg", 83);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](148, "path", 10)(149, "path", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](150, "div", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](151, "span");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](152);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](153, AdminLayoutComponent_div_153_Template, 31, 0, "div", 84);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](154, AdminLayoutComponent_button_154_Template, 5, 0, "button", 85);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵclassProp"]("dark", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipeBind1"](1, 13, ctx.isDarkMode$));
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](75);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("routerLinkActiveOptions", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpureFunction0"](21, _c0));
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](8);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.mobileMenuOpen);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](34);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpureFunction1"](22, _c1, _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipeBind1"](118, 15, ctx.isDarkMode$)));
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", !_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipeBind1"](120, 17, ctx.isDarkMode$));
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipeBind1"](122, 19, ctx.isDarkMode$));
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](8);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](ctx.username);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("src", ctx.imageProfile, _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵsanitizeUrl"]);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.userMenuOpen);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](19);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"]("\u00A9 ", ctx.currentYear, " DevBridge. All rights reserved.");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.showLogoutModal);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.showScrollButton);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_6__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_6__.NgIf, _angular_router__WEBPACK_IMPORTED_MODULE_7__.RouterOutlet, _angular_router__WEBPACK_IMPORTED_MODULE_7__.RouterLink, _angular_router__WEBPACK_IMPORTED_MODULE_7__.RouterLinkActive, _angular_common__WEBPACK_IMPORTED_MODULE_6__.AsyncPipe],
      styles: [".notification-message[_ngcontent-%COMP%] {\n  position: fixed;\n  top: 20px;\n  left: 50%;\n  transform: translateX(-50%);\n  background-color: #4caf50;\n  color: white;\n  padding: 15px 25px;\n  border-radius: 4px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n  z-index: 1000;\n  animation: _ngcontent-%COMP%_fadeInOut 5s forwards;\n}\n\n@keyframes _ngcontent-%COMP%_fadeInOut {\n  0% {\n    opacity: 0;\n    top: 0;\n  }\n  10% {\n    opacity: 1;\n    top: 20px;\n  }\n  90% {\n    opacity: 1;\n    top: 20px;\n  }\n  100% {\n    opacity: 0;\n    top: 0;\n  }\n}\n\n.back-button[_ngcontent-%COMP%] {\n  transition: all 0.2s ease;\n}\n\n.back-button[_ngcontent-%COMP%]:hover {\n  transform: translateX(-2px);\n}\n\n.back-button[_ngcontent-%COMP%]:active {\n  transform: translateX(-4px);\n}\n\n\n\n.sidebar-nav-link[_ngcontent-%COMP%] {\n  position: relative;\n  overflow: hidden;\n}\n\n\n\n.sidebar-nav-link[_ngcontent-%COMP%]::before {\n  content: \"\";\n  position: absolute;\n  inset: 0;\n  border-radius: 0.375rem 0 0 0.375rem;\n  border: 2px solid rgba(79, 95, 173, 0.1);\n  pointer-events: none;\n}\n\n.dark[_ngcontent-%COMP%]   .sidebar-nav-link[_ngcontent-%COMP%]::before {\n  border-color: rgba(109, 120, 201, 0.1);\n}\n\n\n\n.sidebar-nav-link.active[_ngcontent-%COMP%]::after {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  width: 0.5rem;\n  background: linear-gradient(to bottom, #4f5fad, #00f7ff, #4f5fad);\n  border-radius: 0 0.375rem 0.375rem 0;\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.7);\n}\n\n.dark[_ngcontent-%COMP%]   .sidebar-nav-link.active[_ngcontent-%COMP%]::after {\n  background: linear-gradient(to bottom, #6d78c9, #00f7ff, #6d78c9);\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.7);\n}\n\n\n\n@keyframes _ngcontent-%COMP%_pulse {\n  0% {\n    opacity: 0.7;\n  }\n  50% {\n    opacity: 1;\n  }\n  100% {\n    opacity: 0.7;\n  }\n}\n\n\n\n.sidebar-nav-link.active[_ngcontent-%COMP%]::before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  width: 0.5rem;\n  background: linear-gradient(to bottom, #4f5fad, #00f7ff, #4f5fad);\n  border-radius: 0 0.375rem 0.375rem 0;\n  filter: blur(8px);\n  transform: scale(1.5);\n  opacity: 0.5;\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\n}\n\n.dark[_ngcontent-%COMP%]   .sidebar-nav-link.active[_ngcontent-%COMP%]::before {\n  background: linear-gradient(to bottom, #6d78c9, #00f7ff, #6d78c9);\n}\n\n\n\n.conversation-item[_ngcontent-%COMP%], .user-item[_ngcontent-%COMP%] {\n  position: relative;\n  overflow: hidden;\n  transition: all 0.3s ease;\n}\n\n\n\n.conversation-item.active[_ngcontent-%COMP%], .conversation-item[_ngcontent-%COMP%]:hover, .user-item.active[_ngcontent-%COMP%], .user-item[_ngcontent-%COMP%]:hover {\n  border-color: rgba(255, 193, 7, 0.5) !important;\n}\n\n\n\n.conversation-item.active[_ngcontent-%COMP%]::after, .user-item.active[_ngcontent-%COMP%]::after {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  width: 0.25rem;\n  background: linear-gradient(to bottom, #ffc107, #ffeb3b, #ffc107);\n  border-radius: 0 0.375rem 0.375rem 0;\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\n  box-shadow: 0 0 15px rgba(255, 193, 7, 0.7);\n}\n\n\n\n.conversation-item.active[_ngcontent-%COMP%]::before, .user-item.active[_ngcontent-%COMP%]::before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  width: 0.25rem;\n  background: linear-gradient(to bottom, #ffc107, #ffeb3b, #ffc107);\n  border-radius: 0 0.375rem 0.375rem 0;\n  filter: blur(8px);\n  transform: scale(1.5);\n  opacity: 0.5;\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\n}\n\n\n\n.conversation-item.unread[_ngcontent-%COMP%]::after {\n  content: \"\";\n  position: absolute;\n  top: 50%;\n  right: 1rem;\n  transform: translateY(-50%);\n  width: 0.5rem;\n  height: 0.5rem;\n  background-color: #ffc107;\n  border-radius: 50%;\n  box-shadow: 0 0 10px rgba(255, 193, 7, 0.7);\n}\n\n/*# sourceMappingURL=data:application/json;base64,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 */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"],
      data: {
        animation: [(0,_angular_animations__WEBPACK_IMPORTED_MODULE_8__.trigger)('fadeIn', [(0,_angular_animations__WEBPACK_IMPORTED_MODULE_8__.transition)(':enter', [(0,_angular_animations__WEBPACK_IMPORTED_MODULE_8__.style)({
          opacity: 0,
          transform: 'translateY(-10px)'
        }), (0,_angular_animations__WEBPACK_IMPORTED_MODULE_8__.animate)('150ms ease-out', (0,_angular_animations__WEBPACK_IMPORTED_MODULE_8__.style)({
          opacity: 1,
          transform: 'translateY(0)'
        }))]), (0,_angular_animations__WEBPACK_IMPORTED_MODULE_8__.transition)(':leave', [(0,_angular_animations__WEBPACK_IMPORTED_MODULE_8__.animate)('100ms ease-in', (0,_angular_animations__WEBPACK_IMPORTED_MODULE_8__.style)({
          opacity: 0,
          transform: 'translateY(-10px)'
        }))])])]
      }
    });
  }
}

/***/ }),

/***/ 4564:
/*!**************************************************************************!*\
  !*** ./src/app/layouts/auth-admin-layout/auth-admin-layout.component.ts ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthAdminLayoutComponent: () => (/* binding */ AuthAdminLayoutComponent)
/* harmony export */ });
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rxjs */ 819);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rxjs */ 3900);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rxjs/operators */ 271);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var src_app_services_authadmin_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/services/authadmin.service */ 4667);
/* harmony import */ var src_app_services_authuser_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/services/authuser.service */ 9271);
/* harmony import */ var src_app_services_auth_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/services/auth.service */ 4796);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _app_services_theme_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @app/services/theme.service */ 487);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/forms */ 4456);










function AuthAdminLayoutComponent_div_0_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 48)(1, "div", 49)(2, "div", 34);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](3, "i", 50)(4, "div", 51);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](5, "p", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", ctx_r0.messageFromRedirect, " ");
  }
}
function AuthAdminLayoutComponent_div_65_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 53);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](1, "i", 54);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    const _r2 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵreference"](62);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", (_r2.errors == null ? null : _r2.errors["required"]) ? "Email requis" : "Format email invalide", " ");
  }
}
function AuthAdminLayoutComponent_div_74_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 53);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](1, "i", 54);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](2, " Mot de passe requis ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
}
function AuthAdminLayoutComponent_div_75_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 55);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](1, "i", 56);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](2, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](ctx_r6.messageAuthError);
  }
}
class AuthAdminLayoutComponent {
  constructor(authAdminService, authUserService, authService, router, route, themeService) {
    this.authAdminService = authAdminService;
    this.authUserService = authUserService;
    this.authService = authService;
    this.router = router;
    this.route = route;
    this.themeService = themeService;
    this.messageAuthError = '';
    this.messageFromRedirect = '';
    this.destroy$ = new rxjs__WEBPACK_IMPORTED_MODULE_5__.Subject();
    this.checkExistingAuth();
    this.isDarkMode$ = this.themeService.currentTheme$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.map)(theme => theme.name === 'dark'));
  }
  ngOnInit() {
    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/admin/';
    this.subscribeToQueryParams();
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  checkExistingAuth() {
    if (this.authUserService.userLoggedIn()) {
      this.router.navigate(['/'], {
        queryParams: {
          message: "Vous êtes déjà connecté en tant qu'utilisateur. Veuillez vous déconnecter d'abord."
        }
      });
      return;
    }
    if (this.authAdminService.loggedIn()) {
      this.router.navigateByUrl('/admin');
    }
  }
  subscribeToQueryParams() {
    this.route.queryParams.pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_7__.takeUntil)(this.destroy$)).subscribe(params => {
      this.messageFromRedirect = params['message'] || '';
      this.clearMessageAfterDelay();
    });
  }
  clearMessageAfterDelay() {
    if (this.messageFromRedirect) {
      setTimeout(() => this.messageFromRedirect = '', 5000);
    }
  }
  loginAdmin(form) {
    if (!form.valid) {
      this.messageAuthError = 'Veuillez remplir correctement le formulaire.';
      return;
    }
    const data = form.value;
    this.authAdminService.login(data).subscribe({
      next: response => {
        this.handleLoginSuccess(response);
      },
      error: err => {
        this.handleLoginError(err);
      }
    });
  }
  handleLoginSuccess(response) {
    this.authAdminService.saveDataProfil(response.token);
    this.router.navigate([this.returnUrl]);
  }
  handleLoginError(err) {
    this.messageAuthError = err.error?.message || 'Une erreur est survenue lors de la connexion';
    // Effacer le message d'erreur après 5 secondes
    setTimeout(() => this.messageAuthError = '', 5000);
  }
  static {
    this.ɵfac = function AuthAdminLayoutComponent_Factory(t) {
      return new (t || AuthAdminLayoutComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](src_app_services_authadmin_service__WEBPACK_IMPORTED_MODULE_0__.AuthadminService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](src_app_services_authuser_service__WEBPACK_IMPORTED_MODULE_1__.AuthuserService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](src_app_services_auth_service__WEBPACK_IMPORTED_MODULE_2__.AuthService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_8__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_8__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_app_services_theme_service__WEBPACK_IMPORTED_MODULE_3__.ThemeService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineComponent"]({
      type: AuthAdminLayoutComponent,
      selectors: [["app-auth-admin-layout"]],
      decls: 81,
      vars: 8,
      consts: [["class", "fixed top-4 right-4 left-4 md:left-auto md:w-96 bg-white dark:bg-[#1e1e1e] border-l-4 border-[#4f5fad] dark:border-[#6d78c9] rounded-lg p-4 shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.3)] z-50 backdrop-blur-sm", 4, "ngIf"], [1, "min-h-screen", "main-grid-container", "flex", "items-center", "justify-center", "p-4", "relative", "overflow-hidden"], [1, "background-grid"], [1, "absolute", "top-0", "left-0", "w-full", "h-full", "overflow-hidden", "pointer-events-none"], [1, "absolute", "top-[10%]", "left-[5%]", "w-64", "h-64", "rounded-full", "bg-gradient-to-br", "from-[#4f5fad]/10", "to-transparent", "dark:from-[#6d78c9]/5", "dark:to-transparent", "blur-3xl"], [1, "absolute", "bottom-[10%]", "right-[5%]", "w-80", "h-80", "rounded-full", "bg-gradient-to-tl", "from-[#4f5fad]/10", "to-transparent", "dark:from-[#6d78c9]/5", "dark:to-transparent", "blur-3xl"], [1, "w-full", "max-w-4xl", "bg-white", "dark:bg-[#1e1e1e]", "rounded-2xl", "shadow-xl", "dark:shadow-[0_10px_30px_rgba(0,0,0,0.3)]", "overflow-hidden", "backdrop-blur-sm", "relative", "z-10", "border", "border-[#edf1f4]/50", "dark:border-[#2a2a2a]"], [1, "flex", "flex-col", "md:flex-row"], [1, "md:w-1/2", "bg-gradient-to-br", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#2a3052]", "dark:to-[#4f5fad]", "hidden", "md:flex", "items-center", "justify-center", "p-12", "relative", "overflow-hidden"], [1, "absolute", "top-0", "right-0", "w-32", "h-32", "bg-white/10", "rounded-full", "blur-3xl", "transform", "translate-x-16", "-translate-y-16"], [1, "absolute", "bottom-0", "left-0", "w-40", "h-40", "bg-white/10", "rounded-full", "blur-3xl", "transform", "-translate-x-20", "translate-y-20"], [1, "absolute", "inset-0", "opacity-10"], [1, "grid", "grid-cols-12", "h-full"], [1, "border-r", "border-white/20"], [1, "grid", "grid-rows-12", "w-full", "absolute", "top-0", "left-0", "h-full"], [1, "border-b", "border-white/20"], [1, "text-center", "text-white", "relative", "z-10"], [1, "text-3xl", "font-bold", "mb-4", "text-white"], [1, "text-white/80"], [1, "mt-8", "relative"], ["xmlns", "http://www.w3.org/2000/svg", "fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "h-24", "w-24", "mx-auto", "text-white"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "1.5", "d", "M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "1.5", "d", "M15 12a3 3 0 11-6 0 3 3 0 016 0z"], [1, "absolute", "inset-0", "bg-white/20", "blur-xl", "rounded-full", "transform", "scale-150", "-z-10"], [1, "md:w-1/2", "p-8", "md:p-12", "relative"], [1, "absolute", "top-0", "right-0", "w-20", "h-20", "bg-[#4f5fad]/5", "dark:bg-[#6d78c9]/5", "rounded-full", "blur-2xl"], [1, "absolute", "bottom-0", "left-0", "w-24", "h-24", "bg-[#4f5fad]/5", "dark:bg-[#6d78c9]/5", "rounded-full", "blur-2xl"], [1, "text-center", "mb-8", "relative"], [1, "text-2xl", "font-bold", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "bg-clip-text", "text-transparent"], [1, "text-[#6d6870]", "dark:text-[#a0a0a0]", "mt-2"], [1, "space-y-6", "relative", "z-10", 3, "ngSubmit"], ["f", "ngForm"], [1, "group"], ["for", "email", 1, "block", "text-sm", "font-medium", "text-[#6d6870]", "dark:text-[#a0a0a0]", "mb-1", "transition-colors"], [1, "relative"], ["id", "email", "type", "email", "name", "email", "ngModel", "", "required", "", "email", "", "placeholder", "<EMAIL>", 1, "w-full", "px-4", "py-3", "rounded-lg", "border", "border-[#bdc6cc]", "dark:border-[#2a2a2a]", "bg-white", "dark:bg-[#1e1e1e]", "text-[#6d6870]", "dark:text-[#e0e0e0]", "focus:border-[#4f5fad]", "dark:focus:border-[#6d78c9]", "focus:ring-2", "focus:ring-[#4f5fad]/20", "dark:focus:ring-[#6d78c9]/20", "transition-all"], ["email", "ngModel"], [1, "absolute", "inset-y-0", "left-0", "pl-3", "flex", "items-center", "pointer-events-none", "opacity-0", "group-focus-within:opacity-100", "transition-opacity"], [1, "w-0.5", "h-4", "bg-gradient-to-b", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "rounded-full"], ["class", "text-[#ff6b69] dark:text-[#ff8785] text-xs mt-1 flex items-center", 4, "ngIf"], ["for", "password", 1, "block", "text-sm", "font-medium", "text-[#6d6870]", "dark:text-[#a0a0a0]", "mb-1", "transition-colors"], ["id", "password", "type", "password", "name", "password", "ngModel", "", "required", "", "placeholder", "\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022", 1, "w-full", "px-4", "py-3", "rounded-lg", "border", "border-[#bdc6cc]", "dark:border-[#2a2a2a]", "bg-white", "dark:bg-[#1e1e1e]", "text-[#6d6870]", "dark:text-[#e0e0e0]", "focus:border-[#4f5fad]", "dark:focus:border-[#6d78c9]", "focus:ring-2", "focus:ring-[#4f5fad]/20", "dark:focus:ring-[#6d78c9]/20", "transition-all"], ["password", "ngModel"], ["class", "bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 text-[#ff6b69] dark:text-[#ff8785] p-3 rounded-lg text-sm flex items-start", 4, "ngIf"], ["type", "submit", 1, "w-full", "relative", "overflow-hidden", "group"], [1, "absolute", "inset-0", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#3d4a85]", "dark:to-[#6d78c9]", "rounded-lg", "transition-transform", "duration-300", "group-hover:scale-105"], [1, "absolute", "inset-0", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#3d4a85]", "dark:to-[#6d78c9]", "rounded-lg", "opacity-0", "group-hover:opacity-100", "blur-xl", "transition-opacity", "duration-300"], [1, "relative", "block", "text-white", "font-bold", "py-3", "px-4", "rounded-lg", "transition-all"], [1, "fixed", "top-4", "right-4", "left-4", "md:left-auto", "md:w-96", "bg-white", "dark:bg-[#1e1e1e]", "border-l-4", "border-[#4f5fad]", "dark:border-[#6d78c9]", "rounded-lg", "p-4", "shadow-md", "dark:shadow-[0_4px_20px_rgba(0,0,0,0.3)]", "z-50", "backdrop-blur-sm"], [1, "flex", "items-center"], [1, "fas", "fa-info-circle", "text-[#4f5fad]", "dark:text-[#6d78c9]", "text-lg", "mr-3"], [1, "absolute", "inset-0", "bg-[#4f5fad]/20", "dark:bg-[#6d78c9]/20", "blur-xl", "rounded-full", "transform", "scale-150", "-z-10"], [1, "text-sm", "font-medium", "text-[#6d6870]", "dark:text-[#a0a0a0]"], [1, "text-[#ff6b69]", "dark:text-[#ff8785]", "text-xs", "mt-1", "flex", "items-center"], [1, "fas", "fa-exclamation-circle", "mr-1"], [1, "bg-[#ff6b69]/10", "dark:bg-[#ff6b69]/5", "border", "border-[#ff6b69]", "dark:border-[#ff6b69]/30", "text-[#ff6b69]", "dark:text-[#ff8785]", "p-3", "rounded-lg", "text-sm", "flex", "items-start"], [1, "fas", "fa-exclamation-triangle", "mt-0.5", "mr-2"]],
      template: function AuthAdminLayoutComponent_Template(rf, ctx) {
        if (rf & 1) {
          const _r7 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵgetCurrentView"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](0, AuthAdminLayoutComponent_div_0_Template, 7, 1, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](1, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipe"](2, "async");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](3, "div", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](4, "div", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](5, "div", 4)(6, "div", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](7, "div", 6)(8, "div", 7)(9, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](10, "div", 9)(11, "div", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](12, "div", 11)(13, "div", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](14, "div", 13)(15, "div", 13)(16, "div", 13)(17, "div", 13)(18, "div", 13)(19, "div", 13)(20, "div", 13)(21, "div", 13)(22, "div", 13)(23, "div", 13)(24, "div", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](25, "div", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](26, "div", 15)(27, "div", 15)(28, "div", 15)(29, "div", 15)(30, "div", 15)(31, "div", 15)(32, "div", 15)(33, "div", 15)(34, "div", 15)(35, "div", 15)(36, "div", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](37, "div", 16)(38, "h2", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](39, " Espace Administrateur ");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](40, "p", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](41, "Gestion compl\u00E8te de votre plateforme");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](42, "div", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](43, "svg", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](44, "path", 21)(45, "path", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](46, "div", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](47, "div", 24);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](48, "div", 25)(49, "div", 26);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](50, "div", 27)(51, "h1", 28);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](52, " Connexion Admin ");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](53, "p", 29);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](54, " Acc\u00E9dez \u00E0 votre tableau de bord ");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](55, "form", 30, 31);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("ngSubmit", function AuthAdminLayoutComponent_Template_form_ngSubmit_55_listener() {
            _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r7);
            const _r1 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵreference"](56);
            return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx.loginAdmin(_r1));
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](57, "div", 32)(58, "label", 33);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](59, "Email");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](60, "div", 34);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](61, "input", 35, 36);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](63, "div", 37);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](64, "div", 38);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](65, AuthAdminLayoutComponent_div_65_Template, 3, 1, "div", 39);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](66, "div", 32)(67, "label", 40);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](68, "Mot de passe");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](69, "div", 34);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](70, "input", 41, 42);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](72, "div", 37);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](73, "div", 38);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](74, AuthAdminLayoutComponent_div_74_Template, 3, 0, "div", 39);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](75, AuthAdminLayoutComponent_div_75_Template, 4, 1, "div", 43);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](76, "button", 44);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](77, "div", 45)(78, "div", 46);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](79, "span", 47);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](80, " Se connecter ");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()()()()();
        }
        if (rf & 2) {
          const _r2 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵreference"](62);
          const _r4 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵreference"](71);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.messageFromRedirect);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵclassProp"]("dark", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipeBind1"](2, 6, ctx.isDarkMode$));
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](64);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", _r2.invalid && (_r2.dirty || _r2.touched));
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](9);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", _r4.invalid && (_r4.dirty || _r4.touched));
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.messageAuthError);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_9__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_10__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_10__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.RequiredValidator, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.EmailValidator, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.NgModel, _angular_forms__WEBPACK_IMPORTED_MODULE_10__.NgForm, _angular_common__WEBPACK_IMPORTED_MODULE_9__.AsyncPipe],
      styles: ["@charset \"UTF-8\";\n\n\n.notification[_ngcontent-%COMP%] {\n  position: fixed;\n  top: 20px;\n  right: 20px;\n  padding: 15px 25px;\n  border-radius: 8px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  z-index: 1000;\n  display: flex;\n  align-items: center;\n  max-width: 350px;\n  animation: _ngcontent-%COMP%_slideIn 0.3s ease-out forwards;\n  font-family: \"Segoe UI\", Roboto, sans-serif;\n  font-size: 14px;\n  line-height: 1.5;\n}\n\n.notification-info[_ngcontent-%COMP%] {\n  background-color: #e6f7ff;\n  color: #0052cc;\n  border-left: 4px solid #1890ff;\n}\n\n.notification-error[_ngcontent-%COMP%] {\n  background-color: #fff1f0;\n  color: #cf1322;\n  border-left: 4px solid #ff4d4f;\n}\n\n.notification-success[_ngcontent-%COMP%] {\n  background-color: #f6ffed;\n  color: #389e0d;\n  border-left: 4px solid #52c41a;\n}\n\n\n\n.notification-icon[_ngcontent-%COMP%] {\n  margin-right: 12px;\n  font-size: 18px;\n}\n\n\n\n@keyframes _ngcontent-%COMP%_slideIn {\n  from {\n    transform: translateX(100%);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0);\n    opacity: 1;\n  }\n}\n@keyframes _ngcontent-%COMP%_fadeOut {\n  to {\n    opacity: 0;\n    transform: translateY(-20px);\n  }\n}\n\n\n.notification-auto-hide[_ngcontent-%COMP%] {\n  animation: _ngcontent-%COMP%_fadeOut 0.5s ease-in 4.5s forwards;\n}\n\n/*# sourceMappingURL=data:application/json;base64,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 */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 3030:
/*!****************************************************************!*\
  !*** ./src/app/layouts/front-layout/front-layout.component.ts ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   FrontLayoutComponent: () => (/* binding */ FrontLayoutComponent)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rxjs */ 819);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rxjs/operators */ 271);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rxjs/operators */ 3900);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! rxjs/operators */ 4334);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! rxjs/operators */ 1567);
/* harmony import */ var src_environments_environment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/environments/environment */ 5312);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var src_app_services_authuser_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/services/authuser.service */ 9271);
/* harmony import */ var src_app_services_user_status_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/services/user-status.service */ 9722);
/* harmony import */ var src_app_services_message_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/services/message.service */ 4537);
/* harmony import */ var _app_services_theme_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @app/services/theme.service */ 487);
/* harmony import */ var src_app_services_data_service__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/app/services/data.service */ 8490);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @angular/common */ 316);












const _c0 = function () {
  return {
    exact: true
  };
};
function FrontLayoutComponent_a_18_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "a", 64);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](1, "span", 65);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](2, "div", 66)(3, "div", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](4, "i", 67)(5, "div", 68);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](6, "span", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](7, "Accueil");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("routerLinkActiveOptions", _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpureFunction0"](1, _c0));
  }
}
function FrontLayoutComponent_a_19_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "a", 69);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](1, "span", 65);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](2, "div", 66)(3, "div", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](4, "i", 70)(5, "div", 68);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](6, "span", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](7, "Projects");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
  }
}
function FrontLayoutComponent_a_20_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "a", 71);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](1, "span", 65);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](2, "div", 66)(3, "div", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](4, "i", 72)(5, "div", 68);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](6, "span", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](7, "Plannings");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
  }
}
function FrontLayoutComponent_a_21_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "a", 73);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](1, "span", 65);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](2, "div", 66)(3, "div", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](4, "i", 74)(5, "div", 68);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](6, "span", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](7, "R\u00E9unions");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
  }
}
function FrontLayoutComponent_a_22_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "a", 75);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](1, "span", 65);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](2, "div", 66)(3, "div", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](4, "i", 76)(5, "div", 68);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](6, "span", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](7, "Messages");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
  }
}
function FrontLayoutComponent_a_23_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "a", 77);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](1, "span", 65);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](2, "div", 66)(3, "div", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](4, "i", 78)(5, "div", 68);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](6, "span", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](7, "Equipes");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
  }
}
function FrontLayoutComponent_a_25_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "a", 79);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](1, "span", 80);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](2, "div", 66)(3, "div", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](4, "i", 81)(5, "div", 82);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](6, "span", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](7, "Go to Dashboard");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
  }
}
function FrontLayoutComponent_ng_container_47_a_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "a", 104)(1, "div", 30);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](2, "i", 105);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](3, "div", 106);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r15 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", ctx_r15.unreadNotificationsCount > 0 ? ctx_r15.unreadNotificationsCount > 99 ? "99+" : ctx_r15.unreadNotificationsCount : "0", " ");
  }
}
function FrontLayoutComponent_ng_container_47_i_10_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "i", 107);
  }
}
function FrontLayoutComponent_ng_container_47_i_12_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "i", 108);
  }
}
const _c1 = function (a0) {
  return {
    "rotate-180": a0
  };
};
function FrontLayoutComponent_ng_container_47_Template(rf, ctx) {
  if (rf & 1) {
    const _r19 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](1, "div", 83);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](2, FrontLayoutComponent_ng_container_47_a_2_Template, 5, 1, "a", 84);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](3, "button", 85);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function FrontLayoutComponent_ng_container_47_Template_button_click_3_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r19);
      const ctx_r18 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r18.toggleDarkMode());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](4, "div", 86);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](5, "div", 87)(6, "div", 88);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](7, "div", 89);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](8, "div", 90);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpipe"](9, "async");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](10, FrontLayoutComponent_ng_container_47_i_10_Template, 1, 0, "i", 91);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpipe"](11, "async");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](12, FrontLayoutComponent_ng_container_47_i_12_Template, 1, 0, "i", 92);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpipe"](13, "async");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](14, "button", 93);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function FrontLayoutComponent_ng_container_47_Template_button_click_14_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r19);
      const ctx_r20 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r20.logout());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](15, "div", 94);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](16, "div", 95)(17, "div", 96);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](18, "div", 97)(19, "i", 98);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](20, "a", 99)(21, "span", 100);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](22, "Profile");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](23, "span", 101);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](24);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](25, "div", 102);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](26, "div", 89)(27, "img", 103);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const ctx_r7 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r7.authService.userLoggedIn());
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpureFunction1"](12, _c1, _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpipeBind1"](9, 6, ctx_r7.isDarkMode$)));
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpipeBind1"](11, 8, ctx_r7.isDarkMode$));
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpipeBind1"](13, 10, ctx_r7.isDarkMode$));
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](12);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", ctx_r7.username, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("src", ctx_r7.imageProfile, _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵsanitizeUrl"]);
  }
}
function FrontLayoutComponent_ng_template_48_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 109)(1, "a", 110);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](2, "div", 111)(3, "div", 112);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](4, "span", 113);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](5, "i", 114);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](6, " Connexion ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](7, "a", 115);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](8, "div", 116)(9, "div", 117);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](10, "span", 113);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](11, "i", 118);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](12, " Inscription ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
  }
}
function FrontLayoutComponent_a_69_div_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 122);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const item_r21 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", item_r21.badge > 99 ? "99+" : item_r21.badge, " ");
  }
}
const _c2 = function (a0) {
  return {
    exact: a0
  };
};
function FrontLayoutComponent_a_69_Template(rf, ctx) {
  if (rf & 1) {
    const _r25 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "a", 119);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function FrontLayoutComponent_a_69_Template_a_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r25);
      const ctx_r24 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r24.toggleSidebar());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](1, "span", 65);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](2, "div", 120)(3, "div", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](4, "i")(5, "div", 68);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](6, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](8, FrontLayoutComponent_a_69_div_8_Template, 2, 1, "div", 121);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const item_r21 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpropertyInterpolate"]("routerLink", item_r21.route);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("routerLinkActiveOptions", _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpureFunction1"](7, _c2, item_r21.route === "/" ? true : false));
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵclassMapInterpolate1"]("", item_r21.icon, " h-5 w-5 mr-3 text-[#4f5fad] dark:text-[#6d78c9] group-hover:text-[#3d4a85] dark:group-hover:text-[#4f5fad] transition-colors");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate"](item_r21.text);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", item_r21.badge && item_r21.badge > 0);
  }
}
function FrontLayoutComponent_a_70_Template(rf, ctx) {
  if (rf & 1) {
    const _r27 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "a", 123);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function FrontLayoutComponent_a_70_Template_a_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r27);
      const ctx_r26 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r26.toggleSidebar());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](1, "span", 80);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](2, "div", 120)(3, "div", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](4, "i", 124)(5, "div", 82);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](6, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](7, "Go to Dashboard");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
  }
}
function FrontLayoutComponent_ng_container_72_Template(rf, ctx) {
  if (rf & 1) {
    const _r29 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](1, "a", 125);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function FrontLayoutComponent_ng_container_72_Template_a_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r29);
      const ctx_r28 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r28.toggleSidebar());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](2, "span", 65);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](3, "div", 66)(4, "div", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](5, "i", 126)(6, "div", 68);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](7, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](8, "Connexion");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](9, "a", 127);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function FrontLayoutComponent_ng_container_72_Template_a_click_9_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r29);
      const ctx_r30 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r30.toggleSidebar());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](10, "span", 80);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](11, "div", 66)(12, "div", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](13, "i", 128)(14, "div", 82);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](15, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](16, "Inscription");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementContainerEnd"]();
  }
}
function FrontLayoutComponent_ng_container_73_Template(rf, ctx) {
  if (rf & 1) {
    const _r32 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](1, "a", 129);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function FrontLayoutComponent_ng_container_73_Template_a_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r32);
      const ctx_r31 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r31.toggleSidebar());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](2, "span", 65);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](3, "div", 66)(4, "div", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](5, "i", 130)(6, "div", 68);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](7, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](8, "Mon Profil");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](9, "a", 131);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function FrontLayoutComponent_ng_container_73_Template_a_click_9_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵrestoreView"](_r32);
      const ctx_r33 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
      ctx_r33.logout();
      return _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresetView"](ctx_r33.toggleSidebar());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](10, "span", 132);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](11, "div", 66)(12, "div", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](13, "i", 133)(14, "div", 134);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](15, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](16, "D\u00E9connexion");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementContainerEnd"]();
  }
}
function FrontLayoutComponent_div_79_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 135)(1, "div", 136)(2, "div", 30)(3, "div", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](4, "i", 137)(5, "div", 138);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](6, "p", 139);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const ctx_r14 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", ctx_r14.messageFromRedirect, " ");
  }
}
const _c3 = function () {
  return {
    route: "/",
    icon: "fas fa-home",
    text: "Accueil"
  };
};
const _c4 = function () {
  return {
    route: "/projects",
    icon: "fas fa-rocket",
    text: "Projects"
  };
};
const _c5 = function () {
  return {
    route: "/plannings",
    icon: "far fa-calendar-check",
    text: "Plannings"
  };
};
const _c6 = function () {
  return {
    route: "/reunions",
    icon: "fas fa-users-cog",
    text: "R\u00E9unions"
  };
};
const _c7 = function () {
  return {
    route: "/messages",
    icon: "far fa-comment-dots",
    text: "Messages"
  };
};
const _c8 = function () {
  return {
    route: "/equipes",
    icon: "fas fa-users",
    text: "Equipes"
  };
};
const _c9 = function (a3) {
  return {
    route: "/notifications",
    icon: "far fa-bell",
    text: "Notifications",
    badge: a3
  };
};
const _c10 = function (a0, a1, a2, a3, a4, a5, a6) {
  return [a0, a1, a2, a3, a4, a5, a6];
};
class FrontLayoutComponent {
  constructor(authService, route, router, statusService, MessageService, themeService, dataService, cdr) {
    this.authService = authService;
    this.route = route;
    this.router = router;
    this.statusService = statusService;
    this.MessageService = MessageService;
    this.themeService = themeService;
    this.dataService = dataService;
    this.cdr = cdr;
    this.sidebarOpen = false;
    this.profileMenuOpen = false;
    this.currentUser = null;
    this.messageFromRedirect = '';
    this.unreadNotificationsCount = 0;
    this.isMobileView = false;
    this.username = '';
    this.imageProfile = '';
    this.destroy$ = new rxjs__WEBPACK_IMPORTED_MODULE_7__.Subject();
    this.MOBILE_BREAKPOINT = 768;
    this.subscriptions = [];
    this.checkViewport();
    this.loadUserProfile();
    this.isDarkMode$ = this.themeService.currentTheme$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_8__.map)(theme => theme.name === 'dark'));
  }
  loadUserProfile() {
    // Try to get user from both services for maximum reliability
    const authUser = this.authService.getCurrentUser();
    const dataUser = this.dataService.currentUserValue;
    console.log('Auth User:', authUser);
    console.log('Data User:', dataUser);
    // Prefer dataUser if available, otherwise use authUser
    const user = dataUser || authUser;
    if (user) {
      this.updateProfileDisplay(user);
      // Forcer une synchronisation complète des données utilisateur
      if (this.authService.userLoggedIn()) {
        this.dataService.syncCurrentUser().subscribe({
          next: updatedUser => {
            console.log('User profile synced:', updatedUser);
            this.updateProfileDisplay(updatedUser);
          },
          error: err => {
            console.error('Failed to sync user profile:', err);
          }
        });
      }
    } else {
      // Default values if no user is found
      this.username = '';
      this.imageProfile = 'assets/images/default-profile.png';
    }
    console.log('Front layout - Image profile loaded:', this.imageProfile);
    // Sync user data between services if needed
    if (authUser && !dataUser) {
      this.dataService.updateCurrentUser(authUser);
    } else if (!authUser && dataUser) {
      this.authService.setCurrentUser(dataUser);
    }
  }
  ngOnInit() {
    this.subscribeToQueryParams();
    this.subscribeToCurrentUser();
    this.subscribeToRouterEvents();
    this.setupNotificationSystem();
  }
  checkViewport() {
    this.isMobileView = window.innerWidth < this.MOBILE_BREAKPOINT;
    if (!this.isMobileView) {
      this.sidebarOpen = false;
    }
  }
  setupNotificationSystem() {
    console.log('🔔 LAYOUT: Setting up notification system...');
    // Approche 1: Subscription normale
    const countSubscription = this.MessageService.notificationCount$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__.takeUntil)(this.destroy$)).subscribe(count => {
      console.log('🔔 LAYOUT: Notification count updated via subscription:', count);
      this.unreadNotificationsCount = count;
      this.cdr.detectChanges();
    });
    this.subscriptions.push(countSubscription);
    // Approche 2: Événement personnalisé (solution de secours)
    const handleNotificationCountChange = event => {
      const count = event.detail.count;
      console.log('🔔 LAYOUT: Notification count updated via custom event:', count);
      this.unreadNotificationsCount = count;
      this.cdr.detectChanges();
    };
    window.addEventListener('notificationCountChanged', handleNotificationCountChange);
    // Nettoyer l'événement à la destruction
    this.subscriptions.push({
      unsubscribe: () => window.removeEventListener('notificationCountChanged', handleNotificationCountChange)
    });
    // Forcer une première lecture du compteur
    setTimeout(() => {
      this.MessageService.notificationCount$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_10__.take)(1)).subscribe(currentCount => {
        console.log('🔔 LAYOUT: Initial count read:', currentCount);
        this.unreadNotificationsCount = currentCount;
        this.cdr.detectChanges();
      });
    }, 100);
    // Charger les notifications initiales si l'utilisateur est connecté
    if (this.authService.userLoggedIn()) {
      this.MessageService.getNotifications(true).subscribe();
    }
  }
  subscribeToCurrentUser() {
    // S'abonner aux changements d'image de profil via AuthUserService
    const authProfileSub = this.authService.currentUser$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__.takeUntil)(this.destroy$)).subscribe(user => {
      this.currentUser = user;
      this.updateProfileDisplay(user);
    });
    // S'abonner aux changements d'image de profil via DataService
    const dataProfileSub = this.dataService.currentUser$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__.takeUntil)(this.destroy$)).subscribe(user => {
      if (user) {
        this.currentUser = user;
        this.updateProfileDisplay(user);
      }
    });
    this.subscriptions.push(authProfileSub, dataProfileSub);
  }
  updateProfileDisplay(user) {
    if (user) {
      this.username = user.fullName || user.username || '';
      // Vérification plus robuste pour l'image de profil
      let imageFound = false;
      // Vérifier profileImage en premier
      if (user.profileImage && user.profileImage !== 'null' && user.profileImage.trim() !== '' && user.profileImage !== 'undefined') {
        this.imageProfile = user.profileImage;
        imageFound = true;
        console.log('Using profileImage:', this.imageProfile);
      }
      // Ensuite vérifier image si profileImage n'est pas valide
      if (!imageFound && user.image && user.image !== 'null' && user.image.trim() !== '' && user.image !== 'undefined') {
        this.imageProfile = user.image;
        imageFound = true;
        console.log('Using image:', this.imageProfile);
      }
      // Vérifier si l'image est une URL relative au backend
      if (imageFound && !this.imageProfile.startsWith('http') && !this.imageProfile.startsWith('assets/')) {
        // Si c'est une URL relative au backend, ajouter le préfixe du backend
        if (this.imageProfile.startsWith('/')) {
          this.imageProfile = `${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend.replace(/\/$/, '')}${this.imageProfile}`;
        } else {
          this.imageProfile = `${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend.replace(/\/$/, '')}/${this.imageProfile}`;
        }
        console.log('Converted to absolute URL:', this.imageProfile);
      }
      // Si aucune image valide n'est trouvée, utiliser l'image par défaut
      if (!imageFound) {
        this.imageProfile = 'assets/images/default-profile.png';
        console.log('Using default image');
      }
      console.log('Front layout - Image profile updated:', this.imageProfile);
    }
  }
  subscribeToQueryParams() {
    this.route.queryParams.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__.takeUntil)(this.destroy$)).subscribe(params => {
      this.messageFromRedirect = params['message'] || '';
    });
  }
  subscribeToRouterEvents() {
    this.router.events.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_11__.filter)(event => event instanceof _angular_router__WEBPACK_IMPORTED_MODULE_12__.NavigationEnd), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__.takeUntil)(this.destroy$)).subscribe(() => {
      this.sidebarOpen = false;
      this.profileMenuOpen = false;
    });
  }
  toggleSidebar() {
    this.sidebarOpen = !this.sidebarOpen;
  }
  toggleProfileMenu() {
    this.profileMenuOpen = !this.profileMenuOpen;
  }
  toggleDarkMode() {
    this.themeService.toggleTheme();
  }
  logout() {
    this.authService.logout().subscribe({
      next: () => {
        this.profileMenuOpen = false;
        this.sidebarOpen = false;
        this.currentUser = null;
        // Reset image to default
        this.imageProfile = 'assets/images/default-profile.png';
        // Clear data in both services
        this.dataService.updateCurrentUser({});
        this.router.navigate(['/login']);
      },
      error: err => {
        console.error('Logout error:', err);
        this.authService.clearAuthData();
        this.currentUser = null;
        // Reset image to default
        this.imageProfile = 'assets/images/default-profile.png';
        // Clear data in both services
        this.dataService.updateCurrentUser({});
        this.router.navigate(['/login']);
      }
    });
  }
  ngOnDestroy() {
    // Désabonner de tous les observables pour éviter les fuites de mémoire
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.destroy$.next();
    this.destroy$.complete();
  }
  static {
    this.ɵfac = function FrontLayoutComponent_Factory(t) {
      return new (t || FrontLayoutComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](src_app_services_authuser_service__WEBPACK_IMPORTED_MODULE_1__.AuthuserService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_12__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_12__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](src_app_services_user_status_service__WEBPACK_IMPORTED_MODULE_2__.UserStatusService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](src_app_services_message_service__WEBPACK_IMPORTED_MODULE_3__.MessageService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_app_services_theme_service__WEBPACK_IMPORTED_MODULE_4__.ThemeService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](src_app_services_data_service__WEBPACK_IMPORTED_MODULE_5__.DataService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_core__WEBPACK_IMPORTED_MODULE_6__.ChangeDetectorRef));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineComponent"]({
      type: FrontLayoutComponent,
      selectors: [["app-front-layout"]],
      hostBindings: function FrontLayoutComponent_HostBindings(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("resize", function FrontLayoutComponent_resize_HostBindingHandler() {
            return ctx.checkViewport();
          }, false, _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵresolveWindow"]);
        }
      },
      decls: 82,
      vars: 40,
      consts: [[1, "flex", "h-screen", "main-grid-container", "text-[#6d6870]", "dark:text-[#a0a0a0]", "futuristic-layout"], [1, "background-grid"], [1, "hidden", "md:flex", "md:flex-shrink-0"], [1, "flex", "flex-col", "w-64", "bg-white", "dark:bg-[#1e1e1e]", "border-r", "border-[#edf1f4]", "dark:border-[#2a2a2a]", "backdrop-blur-sm"], [1, "flex", "items-center", "justify-center", "h-16", "px-4", "relative", "overflow-hidden"], [1, "absolute", "-top-6", "-left-6", "w-12", "h-12", "bg-gradient-to-br", "from-[#4f5fad]/20", "to-transparent", "rounded-full"], [1, "absolute", "-bottom-6", "-right-6", "w-12", "h-12", "bg-gradient-to-tl", "from-[#4f5fad]/20", "to-transparent", "rounded-full"], [1, "flex", "items-center", "relative", "z-10"], [1, "relative"], ["xmlns", "http://www.w3.org/2000/svg", "fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "h-8", "w-8", "text-[#4f5fad]", "dark:text-[#6d78c9]", "transform", "rotate-12"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M15 12a3 3 0 11-6 0 3 3 0 016 0z"], [1, "absolute", "inset-0", "bg-[#4f5fad]/20", "dark:bg-[#6d78c9]/20", "blur-xl", "rounded-full", "transform", "scale-150", "-z-10"], [1, "ml-2", "text-xl", "font-bold", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "bg-clip-text", "text-transparent"], [1, "flex", "flex-col", "flex-grow", "px-4", "py-4"], [1, "flex-1", "space-y-2"], ["routerLink", "/", "routerLinkActive", "active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium", "class", "sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all", 3, "routerLinkActiveOptions", 4, "ngIf"], ["routerLink", "/projects", "routerLinkActive", "active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium", "class", "sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all", 4, "ngIf"], ["routerLink", "/plannings", "routerLinkActive", "active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium", "class", "sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all", 4, "ngIf"], ["routerLink", "/reunions", "routerLinkActive", "active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium", "class", "sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all", 4, "ngIf"], ["routerLink", "/messages", "routerLinkActive", "active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium", "class", "sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all", 4, "ngIf"], ["routerLink", "/equipes", "routerLinkActive", "active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium", "class", "sidebar-nav-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-all", 4, "ngIf"], [1, "border-t", "border-[#edf1f4]", "dark:border-[#2a2a2a]", "my-2"], ["routerLink", "/admin/dashboard", "routerLinkActive", "active bg-gradient-to-r from-[#7826b5]/10 to-[#9d4edd]/10 dark:from-[#7826b5]/20 dark:to-[#9d4edd]/20 text-[#7826b5] dark:text-[#9d4edd] font-medium", "class", "sidebar-nav-link dashboard-link group flex items-center px-4 py-3 text-sm font-medium rounded-l-md text-[#6d6870] dark:text-[#a0a0a0] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] hover:text-[#7826b5] dark:hover:text-[#9d4edd] transition-all", 4, "ngIf"], [1, "fixed", "top-0", "left-0", "right-0", "bg-white", "dark:bg-[#1e1e1e]", "shadow-md", "dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]", "z-50", "backdrop-blur-sm", "border-b", "border-[#edf1f4]/50", "dark:border-[#2a2a2a]"], [1, "max-w-7xl", "mx-auto", "px-4", "sm:px-6", "lg:px-8", "relative"], [1, "absolute", "inset-0", "overflow-hidden", "pointer-events-none"], [1, "absolute", "top-0", "left-1/4", "w-px", "h-full", "bg-gradient-to-b", "from-transparent", "via-[#4f5fad]/10", "dark:via-[#6d78c9]/5", "to-transparent"], [1, "absolute", "top-0", "right-1/3", "w-px", "h-full", "bg-gradient-to-b", "from-transparent", "via-[#4f5fad]/5", "dark:via-[#6d78c9]/3", "to-transparent"], [1, "flex", "items-center", "justify-between", "h-16", "relative", "z-10"], [1, "flex", "items-center"], ["aria-label", "Toggle menu", 1, "md:hidden", "flex", "items-center", "justify-center", "h-8", "px-3", "rounded-md", "text-[#6d6870]", "dark:text-[#a0a0a0]", "hover:bg-[#edf1f4]", "dark:hover:bg-[#2a2a2a]", "focus:outline-none", "transition-colors", "relative", "group", "overflow-hidden", 3, "click"], [1, "absolute", "inset-0", "bg-[#4f5fad]/10", "dark:bg-[#6d78c9]/10", "opacity-0", "group-hover:opacity-100", "transition-opacity", "rounded-md", "blur-md"], ["fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "h-5", "w-5", "relative", "z-10"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M4 6h16M4 12h16M4 18h16"], [1, "text-sm", "font-medium", "text-[#4f5fad]", "dark:text-[#6d78c9]", "ml-2", "relative", "z-10"], ["routerLink", "/", 1, "flex-shrink-0", "flex", "items-center", "group"], [1, "text-xl", "font-bold", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "bg-clip-text", "text-transparent", "group-hover:scale-105", "transition-transform"], [1, "absolute", "inset-0", "bg-[#4f5fad]/10", "dark:bg-[#6d78c9]/10", "blur-xl", "rounded-full", "transform", "scale-150", "-z-10", "opacity-0", "group-hover:opacity-100", "transition-opacity"], [1, "ml-2", "text-sm", "text-[#6d6870]", "dark:text-[#a0a0a0]", "hidden", "md:block"], [4, "ngIf", "ngIfElse"], ["authButtons", ""], [1, "fixed", "inset-0", "z-40", "md:hidden", 3, "click"], [1, "absolute", "inset-0", "bg-black/30", "dark:bg-black/50", "backdrop-blur-sm"], [1, "fixed", "inset-y-0", "left-0", "max-w-xs", "w-full", "bg-white", "dark:bg-[#1e1e1e]", "shadow-lg", "dark:shadow-[0_0_30px_rgba(0,0,0,0.3)]", "transform", "transition-transform", "duration-300", "ease-in-out", "border-r", "border-[#edf1f4]/50", "dark:border-[#2a2a2a]", 3, "click"], [1, "flex", "flex-col", "h-full", "relative"], [1, "absolute", "top-[10%]", "left-[5%]", "w-32", "h-32", "rounded-full", "bg-gradient-to-br", "from-[#4f5fad]/5", "to-transparent", "dark:from-[#6d78c9]/3", "dark:to-transparent", "blur-2xl"], [1, "absolute", "bottom-[10%]", "right-[5%]", "w-40", "h-40", "rounded-full", "bg-gradient-to-tl", "from-[#4f5fad]/5", "to-transparent", "dark:from-[#6d78c9]/3", "dark:to-transparent", "blur-2xl"], [1, "flex", "items-center", "justify-between", "px-4", "py-3", "border-b", "border-[#edf1f4]", "dark:border-[#2a2a2a]", "relative", "z-10"], [1, "text-xl", "font-bold", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "bg-clip-text", "text-transparent"], [1, "text-sm", "text-[#6d6870]", "dark:text-[#a0a0a0]"], ["aria-label", "Close menu", 1, "text-[#6d6870]", "dark:text-[#a0a0a0]", "hover:text-[#4f5fad]", "dark:hover:text-[#6d78c9]", "p-2", "rounded-full", "hover:bg-[#edf1f4]", "dark:hover:bg-[#2a2a2a]", "transition-colors", "relative", "group", 3, "click"], [1, "absolute", "inset-0", "bg-[#4f5fad]/10", "dark:bg-[#6d78c9]/10", "opacity-0", "group-hover:opacity-100", "transition-opacity", "rounded-full", "blur-md"], [1, "fas", "fa-times", "relative", "z-10"], [1, "flex-1", "px-2", "py-4", "space-y-1", "overflow-y-auto", "bg-white", "dark:bg-[#1e1e1e]", "relative", "z-10"], ["class", "flex items-center px-3 py-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] rounded-lg transition-all relative group overflow-hidden", "routerLinkActive", "bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium border-r-2 border-[#4f5fad] dark:border-[#6d78c9]", 3, "routerLink", "routerLinkActiveOptions", "click", 4, "ngFor", "ngForOf"], ["routerLink", "/admin/dashboard", "class", "flex items-center px-3 py-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#7826b5] dark:hover:text-[#9d4edd] hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a] rounded-lg transition-all relative group overflow-hidden mt-2", 3, "click", 4, "ngIf"], [4, "ngIf"], [1, "flex-1", "flex", "flex-col", "overflow-hidden"], [1, "flex-1", "overflow-y-auto", "bg-[#edf1f4]", "dark:bg-[#121212]", "pt-16", "pb-6", "relative"], [1, "absolute", "top-[15%]", "left-[10%]", "w-64", "h-64", "rounded-full", "bg-gradient-to-br", "from-[#4f5fad]/5", "to-transparent", "dark:from-[#6d78c9]/3", "dark:to-transparent", "blur-3xl"], [1, "absolute", "bottom-[20%]", "right-[10%]", "w-80", "h-80", "rounded-full", "bg-gradient-to-tl", "from-[#4f5fad]/5", "to-transparent", "dark:from-[#6d78c9]/3", "dark:to-transparent", "blur-3xl"], ["class", "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 mt-4", 4, "ngIf"], [1, "max-w-7xl", "mx-auto", "px-4", "sm:px-6", "lg:px-8", "relative", "z-10"], ["routerLink", "/", "routerLinkActive", "active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium", 1, "sidebar-nav-link", "group", "flex", "items-center", "px-4", "py-3", "text-sm", "font-medium", "rounded-l-md", "text-[#6d6870]", "dark:text-[#a0a0a0]", "hover:bg-[#edf1f4]", "dark:hover:bg-[#2a2a2a]", "hover:text-[#4f5fad]", "dark:hover:text-[#6d78c9]", "transition-all", 3, "routerLinkActiveOptions"], [1, "absolute", "inset-0", "w-1", "bg-gradient-to-b", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "opacity-0", "group-hover:opacity-100", "transition-opacity"], [1, "relative", "z-10", "flex", "items-center"], [1, "fas", "fa-home", "h-5", "w-5", "mr-3", "text-[#4f5fad]", "dark:text-[#6d78c9]", "group-hover:text-[#3d4a85]", "dark:group-hover:text-[#4f5fad]", "transition-all", "group-hover:scale-110"], [1, "absolute", "inset-0", "bg-[#4f5fad]/20", "dark:bg-[#6d78c9]/20", "opacity-0", "group-hover:opacity-100", "transition-opacity", "blur-md", "rounded-full"], ["routerLink", "/projects", "routerLinkActive", "active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium", 1, "sidebar-nav-link", "group", "flex", "items-center", "px-4", "py-3", "text-sm", "font-medium", "rounded-l-md", "text-[#6d6870]", "dark:text-[#a0a0a0]", "hover:bg-[#edf1f4]", "dark:hover:bg-[#2a2a2a]", "hover:text-[#4f5fad]", "dark:hover:text-[#6d78c9]", "transition-all"], [1, "fas", "fa-rocket", "h-5", "w-5", "mr-3", "text-[#4f5fad]", "dark:text-[#6d78c9]", "group-hover:text-[#3d4a85]", "dark:group-hover:text-[#4f5fad]", "transition-all", "group-hover:scale-110"], ["routerLink", "/plannings", "routerLinkActive", "active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium", 1, "sidebar-nav-link", "group", "flex", "items-center", "px-4", "py-3", "text-sm", "font-medium", "rounded-l-md", "text-[#6d6870]", "dark:text-[#a0a0a0]", "hover:bg-[#edf1f4]", "dark:hover:bg-[#2a2a2a]", "hover:text-[#4f5fad]", "dark:hover:text-[#6d78c9]", "transition-all"], [1, "far", "fa-calendar-check", "h-5", "w-5", "mr-3", "text-[#4f5fad]", "dark:text-[#6d78c9]", "group-hover:text-[#3d4a85]", "dark:group-hover:text-[#4f5fad]", "transition-all", "group-hover:scale-110"], ["routerLink", "/reunions", "routerLinkActive", "active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium", 1, "sidebar-nav-link", "group", "flex", "items-center", "px-4", "py-3", "text-sm", "font-medium", "rounded-l-md", "text-[#6d6870]", "dark:text-[#a0a0a0]", "hover:bg-[#edf1f4]", "dark:hover:bg-[#2a2a2a]", "hover:text-[#4f5fad]", "dark:hover:text-[#6d78c9]", "transition-all"], [1, "fas", "fa-users-cog", "h-5", "w-5", "mr-3", "text-[#4f5fad]", "dark:text-[#6d78c9]", "group-hover:text-[#3d4a85]", "dark:group-hover:text-[#4f5fad]", "transition-all", "group-hover:scale-110"], ["routerLink", "/messages", "routerLinkActive", "active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium", 1, "sidebar-nav-link", "group", "flex", "items-center", "px-4", "py-3", "text-sm", "font-medium", "rounded-l-md", "text-[#6d6870]", "dark:text-[#a0a0a0]", "hover:bg-[#edf1f4]", "dark:hover:bg-[#2a2a2a]", "hover:text-[#4f5fad]", "dark:hover:text-[#6d78c9]", "transition-all"], [1, "far", "fa-comment-dots", "h-5", "w-5", "mr-3", "text-[#4f5fad]", "dark:text-[#6d78c9]", "group-hover:text-[#3d4a85]", "dark:group-hover:text-[#4f5fad]", "transition-all", "group-hover:scale-110"], ["routerLink", "/equipes", "routerLinkActive", "active bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium", 1, "sidebar-nav-link", "group", "flex", "items-center", "px-4", "py-3", "text-sm", "font-medium", "rounded-l-md", "text-[#6d6870]", "dark:text-[#a0a0a0]", "hover:bg-[#edf1f4]", "dark:hover:bg-[#2a2a2a]", "hover:text-[#4f5fad]", "dark:hover:text-[#6d78c9]", "transition-all"], [1, "fas", "fa-users", "h-5", "w-5", "mr-3", "text-[#4f5fad]", "dark:text-[#6d78c9]", "group-hover:text-[#3d4a85]", "dark:group-hover:text-[#4f5fad]", "transition-all", "group-hover:scale-110"], ["routerLink", "/admin/dashboard", "routerLinkActive", "active bg-gradient-to-r from-[#7826b5]/10 to-[#9d4edd]/10 dark:from-[#7826b5]/20 dark:to-[#9d4edd]/20 text-[#7826b5] dark:text-[#9d4edd] font-medium", 1, "sidebar-nav-link", "dashboard-link", "group", "flex", "items-center", "px-4", "py-3", "text-sm", "font-medium", "rounded-l-md", "text-[#6d6870]", "dark:text-[#a0a0a0]", "hover:bg-[#edf1f4]", "dark:hover:bg-[#2a2a2a]", "hover:text-[#7826b5]", "dark:hover:text-[#9d4edd]", "transition-all"], [1, "absolute", "inset-0", "w-1", "bg-gradient-to-b", "from-[#7826b5]", "to-[#9d4edd]", "dark:from-[#7826b5]", "dark:to-[#9d4edd]", "opacity-0", "group-hover:opacity-100", "transition-opacity"], [1, "fas", "fa-tachometer-alt", "h-5", "w-5", "mr-3", "text-[#7826b5]", "dark:text-[#9d4edd]", "group-hover:text-[#7826b5]", "dark:group-hover:text-[#9d4edd]", "transition-all", "group-hover:scale-110"], [1, "absolute", "inset-0", "bg-[#7826b5]/20", "dark:bg-[#9d4edd]/20", "opacity-0", "group-hover:opacity-100", "transition-opacity", "blur-md", "rounded-full"], [1, "ml-4", "flex", "items-center", "md:ml-6"], ["routerLink", "/notifications", "class", "flex items-center justify-center h-10 px-4 rounded-xl bg-white dark:bg-[#1e1e1e] border border-[#4f5fad]/20 dark:border-[#6d78c9]/20 text-[#4f5fad] dark:text-[#6d78c9] mr-2 transition-all duration-300 hover:bg-[#edf1f4] dark:hover:bg-[#2a2a2a]", "aria-label", "Notifications", 4, "ngIf"], ["aria-label", "Toggle dark mode", 1, "flex", "items-center", "justify-center", "h-9", "w-9", "rounded-xl", "bg-[#edf1f4]", "dark:bg-[#2a2a2a]", "hover:bg-[#dce4ec]", "dark:hover:bg-[#3a3a3a]", "text-[#4f5fad]", "dark:text-[#6d78c9]", "mr-3", "transition-all", "duration-300", "relative", "overflow-hidden", "group", 3, "click"], [1, "absolute", "inset-0", "rounded-xl", "overflow-hidden"], [1, "absolute", "inset-0", "rounded-xl", "border-2", "border-[#4f5fad]/30", "dark:border-[#6d78c9]/30", "opacity-100"], [1, "absolute", "-inset-1", "bg-gradient-to-r", "from-[#4f5fad]/0", "via-[#4f5fad]/30", "to-[#4f5fad]/0", "dark:from-[#6d78c9]/0", "dark:via-[#6d78c9]/30", "dark:to-[#6d78c9]/0", "opacity-0", "group-hover:opacity-100", "blur-sm", "animate-shine"], [1, "absolute", "inset-0", "bg-[#4f5fad]/10", "dark:bg-[#6d78c9]/10", "opacity-0", "group-hover:opacity-100", "transition-opacity", "blur-md"], [1, "relative", "z-10", "transition-all", "duration-500", "ease-in-out", 3, "ngClass"], ["class", "far fa-moon text-lg group-hover:scale-110 transition-transform", 4, "ngIf"], ["class", "far fa-sun text-lg group-hover:scale-110 transition-transform", 4, "ngIf"], ["aria-label", "Logout", 1, "flex", "items-center", "justify-center", "h-8", "w-8", "rounded-full", "bg-[#edf1f4]", "dark:bg-[#2a2a2a]", "hover:bg-[#dce4ec]", "dark:hover:bg-[#3a3a3a]", "text-[#ff6b69]", "dark:text-[#ff8785]", "mr-3", "transition-all", "duration-300", "relative", "overflow-hidden", "group", 3, "click"], [1, "absolute", "inset-0", "rounded-full", "overflow-hidden"], [1, "absolute", "inset-0", "rounded-full", "border", "border-[#ff6b69]/20", "dark:border-[#ff8785]/20", "opacity-0", "group-hover:opacity-100", "transition-opacity"], [1, "absolute", "-inset-1", "bg-gradient-to-r", "from-[#ff6b69]/0", "via-[#ff6b69]/30", "to-[#ff6b69]/0", "dark:from-[#ff8785]/0", "dark:via-[#ff8785]/30", "dark:to-[#ff8785]/0", "opacity-0", "group-hover:opacity-100", "blur-sm", "animate-shine"], [1, "absolute", "inset-0", "bg-[#ff6b69]/10", "dark:bg-[#ff8785]/10", "opacity-0", "group-hover:opacity-100", "transition-opacity", "blur-md"], [1, "fas", "fa-sign-out-alt", "relative", "z-10", "group-hover:scale-110", "transition-transform"], ["routerLink", "/profile", 1, "flex", "items-center", "bg-[#edf1f4]", "dark:bg-[#2a2a2a]", "hover:bg-[#dce4ec]", "dark:hover:bg-[#3a3a3a]", "text-[#4f5fad]", "dark:text-[#6d78c9]", "px-3", "py-2", "rounded-lg", "transition-all", "group"], [1, "sr-only"], [1, "hidden", "md:inline-block", "mr-2", "text-sm", "font-medium", "text-[#4f5fad]", "dark:text-[#6d78c9]", "group-hover:scale-105", "transition-transform"], [1, "h-8", "w-8", "rounded-full", "overflow-hidden", "border-2", "border-[#4f5fad]", "dark:border-[#6d78c9]", "flex", "items-center", "justify-center", "relative", "group-hover:border-[#3d4a85]", "dark:group-hover:border-[#4f5fad]", "transition-colors"], ["alt", "Profile", 1, "h-full", "w-full", "object-cover", 3, "src"], ["routerLink", "/notifications", "aria-label", "Notifications", 1, "flex", "items-center", "justify-center", "h-10", "px-4", "rounded-xl", "bg-white", "dark:bg-[#1e1e1e]", "border", "border-[#4f5fad]/20", "dark:border-[#6d78c9]/20", "text-[#4f5fad]", "dark:text-[#6d78c9]", "mr-2", "transition-all", "duration-300", "hover:bg-[#edf1f4]", "dark:hover:bg-[#2a2a2a]"], [1, "far", "fa-bell", "text-lg", "transition-transform", "mr-2"], [1, "flex", "items-center", "justify-center", "h-6", "min-w-6", "px-1.5", "rounded-md", "bg-gradient-to-r", "from-[#ff8c00]", "to-[#ff6b00]", "text-white", "font-bold", "text-xs", "shadow-md", "animate-pulse"], [1, "far", "fa-moon", "text-lg", "group-hover:scale-110", "transition-transform"], [1, "far", "fa-sun", "text-lg", "group-hover:scale-110", "transition-transform"], [1, "flex", "space-x-4"], ["routerLink", "/login", 1, "inline-flex", "items-center", "relative", "overflow-hidden", "group"], [1, "absolute", "inset-0", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#3d4a85]", "dark:to-[#6d78c9]", "rounded-lg", "transition-transform", "duration-300", "group-hover:scale-105"], [1, "absolute", "inset-0", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#3d4a85]", "dark:to-[#6d78c9]", "rounded-lg", "opacity-0", "group-hover:opacity-100", "blur-xl", "transition-opacity", "duration-300"], [1, "relative", "flex", "items-center", "text-white", "font-medium", "py-2", "px-4", "rounded-lg", "transition-all"], [1, "fas", "fa-sign-in-alt", "mr-2"], ["routerLink", "/signup", 1, "inline-flex", "items-center", "relative", "overflow-hidden", "group"], [1, "absolute", "inset-0", "bg-gradient-to-r", "from-[#7826b5]", "to-[#9d4edd]", "dark:from-[#7826b5]", "dark:to-[#9d4edd]", "rounded-lg", "transition-transform", "duration-300", "group-hover:scale-105"], [1, "absolute", "inset-0", "bg-gradient-to-r", "from-[#7826b5]", "to-[#9d4edd]", "dark:from-[#7826b5]", "dark:to-[#9d4edd]", "rounded-lg", "opacity-0", "group-hover:opacity-100", "blur-xl", "transition-opacity", "duration-300"], [1, "fas", "fa-user-plus", "mr-2"], ["routerLinkActive", "bg-gradient-to-r from-[#3d4a85]/10 to-[#4f5fad]/10 dark:from-[#3d4a85]/20 dark:to-[#4f5fad]/20 text-[#4f5fad] dark:text-[#6d78c9] font-medium border-r-2 border-[#4f5fad] dark:border-[#6d78c9]", 1, "flex", "items-center", "px-3", "py-2", "text-[#6d6870]", "dark:text-[#a0a0a0]", "hover:text-[#4f5fad]", "dark:hover:text-[#6d78c9]", "hover:bg-[#edf1f4]", "dark:hover:bg-[#2a2a2a]", "rounded-lg", "transition-all", "relative", "group", "overflow-hidden", 3, "routerLink", "routerLinkActiveOptions", "click"], [1, "relative", "z-10", "flex", "items-center", "w-full"], ["class", "ml-auto bg-gradient-to-r from-[#ff8c00] to-[#ff6b00] text-white text-xs rounded-full h-5 min-w-5 px-1 flex items-center justify-center shadow-md animate-pulse", 4, "ngIf"], [1, "ml-auto", "bg-gradient-to-r", "from-[#ff8c00]", "to-[#ff6b00]", "text-white", "text-xs", "rounded-full", "h-5", "min-w-5", "px-1", "flex", "items-center", "justify-center", "shadow-md", "animate-pulse"], ["routerLink", "/admin/dashboard", 1, "flex", "items-center", "px-3", "py-2", "text-[#6d6870]", "dark:text-[#a0a0a0]", "hover:text-[#7826b5]", "dark:hover:text-[#9d4edd]", "hover:bg-[#edf1f4]", "dark:hover:bg-[#2a2a2a]", "rounded-lg", "transition-all", "relative", "group", "overflow-hidden", "mt-2", 3, "click"], [1, "fas", "fa-tachometer-alt", "h-5", "w-5", "mr-3", "text-[#7826b5]", "dark:text-[#9d4edd]", "group-hover:text-[#7826b5]", "dark:group-hover:text-[#9d4edd]", "transition-colors"], ["routerLink", "/login", 1, "flex", "items-center", "px-3", "py-2", "text-[#6d6870]", "dark:text-[#a0a0a0]", "hover:text-[#4f5fad]", "dark:hover:text-[#6d78c9]", "hover:bg-[#edf1f4]", "dark:hover:bg-[#2a2a2a]", "rounded-lg", "transition-all", "relative", "group", "overflow-hidden", 3, "click"], [1, "fas", "fa-sign-in-alt", "mr-3", "text-[#4f5fad]", "dark:text-[#6d78c9]", "group-hover:scale-110", "transition-transform"], ["routerLink", "/signup", 1, "flex", "items-center", "px-3", "py-2", "text-[#6d6870]", "dark:text-[#a0a0a0]", "hover:text-[#4f5fad]", "dark:hover:text-[#6d78c9]", "hover:bg-[#edf1f4]", "dark:hover:bg-[#2a2a2a]", "rounded-lg", "transition-all", "relative", "group", "overflow-hidden", 3, "click"], [1, "fas", "fa-user-plus", "mr-3", "text-[#7826b5]", "dark:text-[#9d4edd]", "group-hover:scale-110", "transition-transform"], ["routerLink", "/profile", 1, "flex", "items-center", "px-3", "py-2", "text-[#6d6870]", "dark:text-[#a0a0a0]", "hover:text-[#4f5fad]", "dark:hover:text-[#6d78c9]", "hover:bg-[#edf1f4]", "dark:hover:bg-[#2a2a2a]", "rounded-lg", "transition-all", "relative", "group", "overflow-hidden", 3, "click"], [1, "fas", "fa-user", "mr-3", "text-[#4f5fad]", "dark:text-[#6d78c9]", "group-hover:scale-110", "transition-transform"], [1, "flex", "items-center", "px-3", "py-2", "text-[#6d6870]", "dark:text-[#a0a0a0]", "hover:text-[#ff6b69]", "dark:hover:text-[#ff8785]", "hover:bg-[#edf1f4]", "dark:hover:bg-[#2a2a2a]", "rounded-lg", "transition-all", "relative", "group", "overflow-hidden", "cursor-pointer", 3, "click"], [1, "absolute", "inset-0", "w-1", "bg-gradient-to-b", "from-[#ff6b69]", "to-[#ff8785]", "dark:from-[#ff6b69]", "dark:to-[#ff8785]", "opacity-0", "group-hover:opacity-100", "transition-opacity"], [1, "fas", "fa-sign-out-alt", "mr-3", "text-[#ff6b69]", "dark:text-[#ff8785]", "group-hover:scale-110", "transition-transform"], [1, "absolute", "inset-0", "bg-[#ff6b69]/20", "dark:bg-[#ff8785]/20", "opacity-0", "group-hover:opacity-100", "transition-opacity", "blur-md", "rounded-full"], [1, "max-w-7xl", "mx-auto", "px-4", "sm:px-6", "lg:px-8", "relative", "z-10", "mt-4"], [1, "bg-white", "dark:bg-[#1e1e1e]", "border-l-4", "border-[#ff8c00]", "dark:border-[#ff6b00]", "rounded-lg", "p-4", "mb-6", "shadow-md", "dark:shadow-[0_4px_20px_rgba(0,0,0,0.2)]", "backdrop-blur-sm"], [1, "fas", "fa-info-circle", "text-[#ff8c00]", "dark:text-[#ff6b00]", "text-lg", "mr-3"], [1, "absolute", "inset-0", "bg-[#ff8c00]/30", "dark:bg-[#ff6b00]/30", "blur-xl", "rounded-full", "transform", "scale-150", "-z-10", "animate-pulse"], [1, "ml-3", "text-sm", "font-medium", "text-[#6d6870]", "dark:text-[#a0a0a0]"]],
      template: function FrontLayoutComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpipe"](1, "async");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](2, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](3, "div", 2)(4, "div", 3)(5, "div", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](6, "div", 5)(7, "div", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](8, "div", 7)(9, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnamespaceSVG"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](10, "svg", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](11, "path", 10)(12, "path", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnamespaceHTML"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](13, "div", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](14, "span", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](15, "DevBridge");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](16, "div", 14)(17, "nav", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](18, FrontLayoutComponent_a_18_Template, 8, 2, "a", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](19, FrontLayoutComponent_a_19_Template, 8, 0, "a", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](20, FrontLayoutComponent_a_20_Template, 8, 0, "a", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](21, FrontLayoutComponent_a_21_Template, 8, 0, "a", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](22, FrontLayoutComponent_a_22_Template, 8, 0, "a", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](23, FrontLayoutComponent_a_23_Template, 8, 0, "a", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](24, "div", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](25, FrontLayoutComponent_a_25_Template, 8, 0, "a", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](26, "header", 24)(27, "div", 25)(28, "div", 26);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](29, "div", 27)(30, "div", 28);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](31, "div", 29)(32, "div", 30)(33, "button", 31);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function FrontLayoutComponent_Template_button_click_33_listener() {
            return ctx.toggleSidebar();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](34, "div", 32);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnamespaceSVG"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](35, "svg", 33);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](36, "path", 34);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnamespaceHTML"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](37, "span", 35);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](38, "Menu");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](39, "a", 36)(40, "div", 8)(41, "h1", 37);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](42, " DevBridge ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](43, "div", 38);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](44, "span", 39);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](45, "Project Management Suite");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](46, "div", 30);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](47, FrontLayoutComponent_ng_container_47_Template, 28, 14, "ng-container", 40);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](48, FrontLayoutComponent_ng_template_48_Template, 13, 0, "ng-template", null, 41, _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplateRefExtractor"]);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](50, "div", 42);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function FrontLayoutComponent_Template_div_click_50_listener() {
            return ctx.sidebarOpen && ctx.toggleSidebar();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](51, "div", 43);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](52, "div", 44);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function FrontLayoutComponent_Template_div_click_52_listener($event) {
            return $event.stopPropagation();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](53, "div", 45)(54, "div", 26);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](55, "div", 27)(56, "div", 28)(57, "div", 46)(58, "div", 47);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](59, "div", 48)(60, "div")(61, "h3", 49);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](62, " DevBridge ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](63, "p", 50);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](64, " Project Management Suite ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](65, "button", 51);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function FrontLayoutComponent_Template_button_click_65_listener() {
            return ctx.toggleSidebar();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](66, "div", 52)(67, "i", 53);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](68, "nav", 54);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](69, FrontLayoutComponent_a_69_Template, 9, 9, "a", 55);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](70, FrontLayoutComponent_a_70_Template, 8, 0, "a", 56);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](71, "div", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](72, FrontLayoutComponent_ng_container_72_Template, 17, 0, "ng-container", 57);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](73, FrontLayoutComponent_ng_container_73_Template, 17, 0, "ng-container", 57);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](74, "div", 58)(75, "main", 59)(76, "div", 26);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](77, "div", 60)(78, "div", 61);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](79, FrontLayoutComponent_div_79_Template, 8, 1, "div", 62);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](80, "div", 63);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](81, "router-outlet");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()()();
        }
        if (rf & 2) {
          const _r8 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵreference"](49);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵclassProp"]("dark", _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpipeBind1"](1, 22, ctx.isDarkMode$));
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](18);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.authService.userLoggedIn());
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.authService.userLoggedIn());
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.authService.userLoggedIn());
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.authService.userLoggedIn());
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.authService.userLoggedIn());
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.authService.userLoggedIn());
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.authService.userLoggedIn() && ((ctx.currentUser == null ? null : ctx.currentUser.role) === "admin" || (ctx.currentUser == null ? null : ctx.currentUser.role) === "teacher"));
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](22);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.authService.userLoggedIn())("ngIfElse", _r8);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵclassProp"]("hidden", !ctx.sidebarOpen);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵclassProp"]("translate-x-0", ctx.sidebarOpen)("-translate-x-full", !ctx.sidebarOpen);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](17);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngForOf", _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpureFunction7"](32, _c10, _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpureFunction0"](24, _c3), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpureFunction0"](25, _c4), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpureFunction0"](26, _c5), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpureFunction0"](27, _c6), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpureFunction0"](28, _c7), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpureFunction0"](29, _c8), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpureFunction1"](30, _c9, ctx.unreadNotificationsCount)));
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.authService.userLoggedIn() && ((ctx.currentUser == null ? null : ctx.currentUser.role) === "admin" || (ctx.currentUser == null ? null : ctx.currentUser.role) === "teacher"));
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !ctx.authService.userLoggedIn());
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.authService.userLoggedIn());
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.messageFromRedirect);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_13__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_13__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_13__.NgIf, _angular_router__WEBPACK_IMPORTED_MODULE_12__.RouterOutlet, _angular_router__WEBPACK_IMPORTED_MODULE_12__.RouterLink, _angular_router__WEBPACK_IMPORTED_MODULE_12__.RouterLinkActive, _angular_common__WEBPACK_IMPORTED_MODULE_13__.AsyncPipe],
      styles: ["@charset \"UTF-8\";\n\n\n\n\n.futuristic-layout[_ngcontent-%COMP%] {\n  background-color: var(--dark-bg);\n  color: var(--text-light);\n  min-height: 100vh;\n  position: relative;\n}\n\n\n\n.futuristic-layout[_ngcontent-%COMP%]:not(.dark) {\n  background-color: #f0f4f8;\n  color: #6d6870;\n}\n\n\n\n.dark[_ngcontent-%COMP%]   .futuristic-layout[_ngcontent-%COMP%] {\n  background-color: #121212;\n}\n\n\n\n.futuristic-layout[_ngcontent-%COMP%]:not(.dark) {\n  background-color: #f0f4f8;\n}\n\n\n\n.futuristic-header[_ngcontent-%COMP%] {\n  background-color: var(--medium-bg);\n  border-bottom: 1px solid rgba(0, 247, 255, 0.2);\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\n}\n\n\n\n.futuristic-logo[_ngcontent-%COMP%] {\n  background: linear-gradient(\n    135deg,\n    var(--accent-color),\n    var(--secondary-color)\n  );\n  -webkit-background-clip: text;\n  background-clip: text;\n  color: transparent;\n  text-shadow: 0 0 10px rgba(0, 247, 255, 0.5);\n}\n\n.futuristic-subtitle[_ngcontent-%COMP%] {\n  color: var(--text-dim);\n}\n\n\n\n.futuristic-nav-link[_ngcontent-%COMP%] {\n  color: var(--text-dim);\n  padding: 0.75rem 1rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  transition: all var(--transition-fast);\n  position: relative;\n  overflow: hidden;\n}\n\n.futuristic-nav-link[_ngcontent-%COMP%]::before {\n  content: \"\";\n  position: absolute;\n  bottom: 0;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 0;\n  height: 2px;\n  background: linear-gradient(\n    90deg,\n    var(--accent-color),\n    var(--secondary-color)\n  );\n  transition: width var(--transition-fast);\n}\n\n.futuristic-nav-link[_ngcontent-%COMP%]:hover {\n  color: var(--text-light);\n}\n\n.futuristic-nav-link[_ngcontent-%COMP%]:hover::before {\n  width: 80%;\n}\n\n.futuristic-nav-link-active[_ngcontent-%COMP%] {\n  color: var(--accent-color);\n}\n\n.futuristic-nav-link-active[_ngcontent-%COMP%]::before {\n  width: 80%;\n}\n\n\n\n.futuristic-profile-button[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  background: transparent;\n  border: none;\n  cursor: pointer;\n  padding: 0.5rem;\n  border-radius: var(--border-radius-md);\n  transition: all var(--transition-fast);\n}\n\n.futuristic-profile-button[_ngcontent-%COMP%]:hover {\n  background-color: rgba(0, 247, 255, 0.1);\n}\n\n.futuristic-username[_ngcontent-%COMP%] {\n  color: var(--text-light);\n}\n\n\n\n.futuristic-dropdown-menu[_ngcontent-%COMP%] {\n  position: absolute;\n  right: 0;\n  top: 100%;\n  margin-top: 0.5rem;\n  width: 12rem;\n  background-color: var(--medium-bg);\n  border: 1px solid rgba(0, 247, 255, 0.2);\n  border-radius: var(--border-radius-md);\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);\n  overflow: hidden;\n  z-index: 50;\n  animation: _ngcontent-%COMP%_fadeIn 0.2s ease-out;\n}\n\n.futuristic-dropdown-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  padding: 0.75rem 1rem;\n  color: var(--text-dim);\n  font-size: 0.875rem;\n  transition: all var(--transition-fast);\n  cursor: pointer;\n}\n\n.futuristic-dropdown-item[_ngcontent-%COMP%]:hover {\n  background-color: rgba(0, 247, 255, 0.1);\n  color: var(--text-light);\n}\n\n\n\n.futuristic-login-button[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  padding: 0.5rem 1rem;\n  color: var(--accent-color);\n  font-size: 0.875rem;\n  font-weight: 500;\n  border-radius: var(--border-radius-md);\n  transition: all var(--transition-fast);\n  border: 1px solid rgba(0, 247, 255, 0.3);\n}\n\n.futuristic-login-button[_ngcontent-%COMP%]:hover {\n  background-color: rgba(0, 247, 255, 0.1);\n  transform: translateY(-2px);\n  box-shadow: var(--glow-effect);\n}\n\n.futuristic-register-button[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  padding: 0.5rem 1rem;\n  background: linear-gradient(\n    135deg,\n    var(--accent-color),\n    var(--secondary-color)\n  );\n  color: var(--text-light);\n  font-size: 0.875rem;\n  font-weight: 500;\n  border-radius: var(--border-radius-md);\n  transition: all var(--transition-fast);\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);\n}\n\n.futuristic-register-button[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n  box-shadow: var(--glow-effect);\n}\n\n\n\n.futuristic-sidebar[_ngcontent-%COMP%] {\n  background-color: var(--medium-bg);\n  border-right: 1px solid rgba(0, 247, 255, 0.2);\n  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.3);\n}\n\n.futuristic-sidebar-header[_ngcontent-%COMP%] {\n  border-bottom: 1px solid rgba(0, 247, 255, 0.2);\n}\n\n.futuristic-close-button[_ngcontent-%COMP%] {\n  width: 32px;\n  height: 32px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: rgba(0, 247, 255, 0.1);\n  color: var(--accent-color);\n  border: none;\n  border-radius: 50%;\n  cursor: pointer;\n  transition: all var(--transition-fast);\n}\n\n.futuristic-close-button[_ngcontent-%COMP%]:hover {\n  background-color: rgba(0, 247, 255, 0.2);\n  transform: rotate(90deg);\n}\n\n.futuristic-sidebar-nav[_ngcontent-%COMP%] {\n  scrollbar-width: thin;\n  scrollbar-color: var(--accent-color) transparent;\n}\n\n.futuristic-sidebar-nav[_ngcontent-%COMP%]::-webkit-scrollbar {\n  width: 4px;\n}\n\n.futuristic-sidebar-nav[_ngcontent-%COMP%]::-webkit-scrollbar-track {\n  background: transparent;\n}\n\n.futuristic-sidebar-nav[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\n  background-color: var(--accent-color);\n  border-radius: 10px;\n}\n\n.futuristic-sidebar-link[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  padding: 0.75rem 1rem;\n  color: var(--text-dim);\n  font-size: 0.875rem;\n  font-weight: 500;\n  border-radius: var(--border-radius-md);\n  transition: all var(--transition-fast);\n  position: relative;\n}\n\n.futuristic-sidebar-link[_ngcontent-%COMP%]:hover {\n  background-color: rgba(0, 247, 255, 0.1);\n  color: var(--text-light);\n}\n\n.futuristic-sidebar-link-active[_ngcontent-%COMP%] {\n  background-color: rgba(0, 247, 255, 0.15);\n  color: var(--accent-color);\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);\n}\n\n.futuristic-sidebar-icon[_ngcontent-%COMP%] {\n  margin-right: 0.75rem;\n  width: 1.5rem;\n  height: 1.5rem;\n  color: var(--text-dim);\n  transition: color var(--transition-fast);\n}\n\n.futuristic-sidebar-icon-fa[_ngcontent-%COMP%] {\n  margin-right: 0.75rem;\n  width: 1.5rem;\n  font-size: 1.25rem;\n  color: var(--text-dim);\n  transition: color var(--transition-fast);\n  text-align: center;\n}\n\n.futuristic-sidebar-link[_ngcontent-%COMP%]:hover   .futuristic-sidebar-icon[_ngcontent-%COMP%], .futuristic-sidebar-link[_ngcontent-%COMP%]:hover   .futuristic-sidebar-icon-fa[_ngcontent-%COMP%], .futuristic-sidebar-link-active[_ngcontent-%COMP%]   .futuristic-sidebar-icon[_ngcontent-%COMP%], .futuristic-sidebar-link-active[_ngcontent-%COMP%]   .futuristic-sidebar-icon-fa[_ngcontent-%COMP%] {\n  color: var(--accent-color);\n}\n\n.futuristic-separator[_ngcontent-%COMP%] {\n  height: 1px;\n  background: linear-gradient(\n    to right,\n    transparent,\n    rgba(0, 247, 255, 0.2),\n    transparent\n  );\n}\n\n\n\n.futuristic-badge[_ngcontent-%COMP%] {\n  background: linear-gradient(\n    135deg,\n    var(--accent-color),\n    var(--secondary-color)\n  );\n  color: var(--text-light);\n  font-size: 0.75rem;\n  font-weight: 500;\n  border-radius: 9999px;\n  padding: 2px 8px;\n  min-width: 1.5rem;\n  text-align: center;\n  box-shadow: var(--glow-effect);\n}\n\n\n\n.futuristic-main-content[_ngcontent-%COMP%] {\n  position: relative;\n  z-index: 1;\n}\n\n\n\n.futuristic-status-message[_ngcontent-%COMP%] {\n  background-color: rgba(0, 247, 255, 0.1);\n  border-left: 4px solid var(--accent-color);\n  border-radius: var(--border-radius-md);\n  padding: 1rem;\n  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);\n}\n\n.futuristic-status-icon[_ngcontent-%COMP%] {\n  color: var(--accent-color);\n  font-size: 1.25rem;\n}\n\n.futuristic-status-text[_ngcontent-%COMP%] {\n  color: var(--text-light);\n}\n\n\n\n@keyframes _ngcontent-%COMP%_fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n\n\n.main-content-grid[_ngcontent-%COMP%] {\n  z-index: 0;\n  pointer-events: none;\n}\n\n\n\n.main-content-grid[_ngcontent-%COMP%] {\n  pointer-events: none;\n  z-index: 0;\n}\n\n\n\n\n\n\n.sidebar-nav-link[_ngcontent-%COMP%] {\n  position: relative;\n  overflow: hidden;\n}\n\n\n\n.sidebar-nav-link[_ngcontent-%COMP%]::before {\n  content: \"\";\n  position: absolute;\n  inset: 0;\n  border-radius: 0.375rem 0 0 0.375rem;\n  border: 2px solid rgba(79, 95, 173, 0.1);\n  pointer-events: none;\n}\n\n.dark[_ngcontent-%COMP%]   .sidebar-nav-link[_ngcontent-%COMP%]::before {\n  border-color: rgba(109, 120, 201, 0.1);\n}\n\n\n\n.sidebar-nav-link.active[_ngcontent-%COMP%]::after {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  width: 0.5rem;\n  background: linear-gradient(to bottom, #4f5fad, #00f7ff, #4f5fad);\n  border-radius: 0 0.375rem 0.375rem 0;\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.7);\n}\n\n.dark[_ngcontent-%COMP%]   .sidebar-nav-link.active[_ngcontent-%COMP%]::after {\n  background: linear-gradient(to bottom, #6d78c9, #00f7ff, #6d78c9);\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.7);\n}\n\n\n\n@keyframes _ngcontent-%COMP%_pulse {\n  0% {\n    opacity: 0.7;\n  }\n  50% {\n    opacity: 1;\n  }\n  100% {\n    opacity: 0.7;\n  }\n}\n\n\n\n.sidebar-nav-link.active[_ngcontent-%COMP%]::before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  width: 0.5rem;\n  background: linear-gradient(to bottom, #4f5fad, #00f7ff, #4f5fad);\n  border-radius: 0 0.375rem 0.375rem 0;\n  filter: blur(8px);\n  transform: scale(1.5);\n  opacity: 0.5;\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\n}\n\n.dark[_ngcontent-%COMP%]   .sidebar-nav-link.active[_ngcontent-%COMP%]::before {\n  background: linear-gradient(to bottom, #6d78c9, #00f7ff, #6d78c9);\n}\n\n\n\n.dashboard-link.active[_ngcontent-%COMP%]::after {\n  background: linear-gradient(to bottom, #7826b5, #9d4edd, #7826b5);\n  box-shadow: 0 0 15px rgba(157, 78, 221, 0.7);\n}\n\n.dark[_ngcontent-%COMP%]   .dashboard-link.active[_ngcontent-%COMP%]::after {\n  background: linear-gradient(to bottom, #7826b5, #9d4edd, #7826b5);\n}\n\n.dashboard-link.active[_ngcontent-%COMP%]::before {\n  background: linear-gradient(to bottom, #7826b5, #9d4edd, #7826b5);\n}\n\n/*# sourceMappingURL=data:application/json;base64,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 */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 7431:
/*!*******************************************!*\
  !*** ./src/app/layouts/layouts.module.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LayoutsModule: () => (/* binding */ LayoutsModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _admin_layout_admin_layout_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./admin-layout/admin-layout.component */ 134);
/* harmony import */ var _front_layout_front_layout_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./front-layout/front-layout.component */ 3030);
/* harmony import */ var _auth_admin_layout_auth_admin_layout_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./auth-admin-layout/auth-admin-layout.component */ 4564);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 7580);







class LayoutsModule {
  static {
    this.ɵfac = function LayoutsModule_Factory(t) {
      return new (t || LayoutsModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineNgModule"]({
      type: LayoutsModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineInjector"]({
      imports: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.CommonModule, _angular_router__WEBPACK_IMPORTED_MODULE_5__.RouterModule, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.FormsModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵsetNgModuleScope"](LayoutsModule, {
    declarations: [_admin_layout_admin_layout_component__WEBPACK_IMPORTED_MODULE_0__.AdminLayoutComponent, _front_layout_front_layout_component__WEBPACK_IMPORTED_MODULE_1__.FrontLayoutComponent, _auth_admin_layout_auth_admin_layout_component__WEBPACK_IMPORTED_MODULE_2__.AuthAdminLayoutComponent],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.CommonModule, _angular_router__WEBPACK_IMPORTED_MODULE_5__.RouterModule, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.FormsModule]
  });
})();

/***/ }),

/***/ 5293:
/*!*****************************************!*\
  !*** ./src/app/models/message.model.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CallStatus: () => (/* binding */ CallStatus),
/* harmony export */   CallType: () => (/* binding */ CallType),
/* harmony export */   MessageStatus: () => (/* binding */ MessageStatus),
/* harmony export */   MessageType: () => (/* binding */ MessageType)
/* harmony export */ });
var MessageType;
(function (MessageType) {
  MessageType["TEXT"] = "TEXT";
  MessageType["IMAGE"] = "IMAGE";
  MessageType["FILE"] = "FILE";
  MessageType["AUDIO"] = "AUDIO";
  MessageType["VIDEO"] = "VIDEO";
  MessageType["SYSTEM"] = "SYSTEM";
  MessageType["VOICE_MESSAGE"] = "VOICE_MESSAGE";
})(MessageType || (MessageType = {}));
var MessageStatus;
(function (MessageStatus) {
  MessageStatus["SENDING"] = "SENDING";
  MessageStatus["SENT"] = "SENT";
  MessageStatus["DELIVERED"] = "DELIVERED";
  MessageStatus["READ"] = "READ";
  MessageStatus["FAILED"] = "FAILED";
})(MessageStatus || (MessageStatus = {}));
// --------------------------------------------------------------------------
// Types et interfaces pour les appels
// --------------------------------------------------------------------------
/**
 * Types d'appels possibles
 */
var CallType;
(function (CallType) {
  CallType["AUDIO"] = "AUDIO";
  CallType["VIDEO"] = "VIDEO";
  CallType["VIDEO_ONLY"] = "VIDEO_ONLY";
})(CallType || (CallType = {}));
/**
 * États possibles d'un appel
 */
var CallStatus;
(function (CallStatus) {
  CallStatus["RINGING"] = "RINGING";
  CallStatus["CONNECTED"] = "CONNECTED";
  CallStatus["ENDED"] = "ENDED";
  CallStatus["MISSED"] = "MISSED";
  CallStatus["REJECTED"] = "REJECTED";
  CallStatus["FAILED"] = "FAILED";
})(CallStatus || (CallStatus = {}));

/***/ }),

/***/ 4796:
/*!******************************************!*\
  !*** ./src/app/services/auth.service.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthService: () => (/* binding */ AuthService)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/common/http */ 6443);


class AuthService {
  constructor(http) {
    this.http = http;
    this.AUTH_API = 'http://localhost:3000/api/auth';
    this.ADMIN_API = 'http://localhost:3000/api/admin';
  }
  // Auth endpoints
  signup(data) {
    return this.http.post(`${this.AUTH_API}/signup`, data);
  }
  verifyEmail(data) {
    return this.http.post(`${this.AUTH_API}/verify-email`, data);
  }
  login(data) {
    return this.http.post(`${this.AUTH_API}/login`, data);
  }
  forgotPassword(email) {
    return this.http.post(`${this.AUTH_API}/forgot-password`, {
      email
    });
  }
  resetPassword(data) {
    return this.http.post(`${this.AUTH_API}/reset-password`, data);
  }
  getProfile(token) {
    return this.http.get(`${this.AUTH_API}/profile`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
  }
  updateProfile(formData, token) {
    return this.http.put(`${this.AUTH_API}/update-profile`, formData, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
  }
  removeProfileImage(token) {
    return this.http.delete(`${this.AUTH_API}/remove-profile-image`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
  }
  changePassword(data, token) {
    return this.http.put(`${this.AUTH_API}/change-password`, data, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
  }
  // Admin endpoints
  getAllUsers(token) {
    return this.http.get(`${this.ADMIN_API}/users`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
  }
  updateUserRole(userId, role, token) {
    return this.http.put(`${this.ADMIN_API}/users/${userId}/role`, {
      role
    }, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
  }
  deleteUser(userId, token) {
    return this.http.delete(`${this.ADMIN_API}/users/${userId}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
  }
  toggleUserActivation(userId, isActive, token) {
    return this.http.put(`${this.ADMIN_API}/users/${userId}/activation`, {
      isActive
    }, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
  }
  getUserById(userId, token) {
    return this.http.get(`${this.ADMIN_API}/users/${userId}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
  }
  getCurrentUser() {
    const user = localStorage.getItem('user');
    return user ? JSON.parse(user) : null;
  }
  getUserRole() {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    return user?.role || '';
  }
  isAdmin() {
    return this.getUserRole() === 'admin';
  }
  logout() {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  }
  resendCode(email) {
    return this.http.post(`${this.AUTH_API}/resend-code`, {
      email
    });
  }
  static {
    this.ɵfac = function AuthService_Factory(t) {
      return new (t || AuthService)(_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵinject"](_angular_common_http__WEBPACK_IMPORTED_MODULE_1__.HttpClient));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineInjectable"]({
      token: AuthService,
      factory: AuthService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 4667:
/*!***********************************************!*\
  !*** ./src/app/services/authadmin.service.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthadminService: () => (/* binding */ AuthadminService)
/* harmony export */ });
/* harmony import */ var src_environments_environment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/environments/environment */ 5312);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/common/http */ 6443);
/* harmony import */ var _auth0_angular_jwt__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @auth0/angular-jwt */ 2389);




class AuthadminService {
  constructor(http, jwtHelper) {
    this.http = http;
    this.jwtHelper = jwtHelper;
  }
  // login
  login(body) {
    return this.http.post(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}auth/login`, body);
  }
  saveDataProfil(token) {
    localStorage.setItem('token', token);
  }
  getUser() {
    let token = localStorage.getItem('token');
    let decodedToken = this.jwtHelper.decodeToken(token);
    return decodedToken;
  }
  loggedIn() {
    let token = localStorage.getItem('token');
    if (!token) {
      return false;
    }
    if (this.jwtHelper.decodeToken(token).role !== 'admin') {
      return false;
    }
    if (this.jwtHelper.isTokenExpired(token)) {
      return false;
    }
    return true;
  }
  clearAuthData() {
    localStorage.removeItem('token');
  }
  static {
    this.ɵfac = function AuthadminService_Factory(t) {
      return new (t || AuthadminService)(_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵinject"](_angular_common_http__WEBPACK_IMPORTED_MODULE_2__.HttpClient), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵinject"](_auth0_angular_jwt__WEBPACK_IMPORTED_MODULE_3__.JwtHelperService));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
      token: AuthadminService,
      factory: AuthadminService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 9271:
/*!**********************************************!*\
  !*** ./src/app/services/authuser.service.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthuserService: () => (/* binding */ AuthuserService)
/* harmony export */ });
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/common/http */ 6443);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rxjs */ 5797);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rxjs */ 819);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rxjs */ 8764);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rxjs */ 1318);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rxjs */ 7919);
/* harmony import */ var src_environments_environment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/environments/environment */ 5312);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _auth0_angular_jwt__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @auth0/angular-jwt */ 2389);







class AuthuserService {
  constructor(http, router, jwtHelper) {
    this.http = http;
    this.router = router;
    this.jwtHelper = jwtHelper;
    this.currentUserSubject = new rxjs__WEBPACK_IMPORTED_MODULE_1__.BehaviorSubject(null);
    this.currentUser$ = this.currentUserSubject.asObservable();
    this.isInitialized = false;
    // Subject pour notifier les changements d'authentification
    this.authChangeSubject = new rxjs__WEBPACK_IMPORTED_MODULE_2__.Subject();
    this.authChange$ = this.authChangeSubject.asObservable();
    this.initializeCurrentUser();
  }
  // Authentification
  getUserHeaders() {
    const token = localStorage.getItem('token');
    if (!token || this.jwtHelper.isTokenExpired(token)) {
      throw new Error('Token invalide ou expiré');
    }
    return new _angular_common_http__WEBPACK_IMPORTED_MODULE_3__.HttpHeaders({
      Authorization: `Bearer ${token || ''}`,
      'Content-Type': 'application/json'
    });
  }
  getCommonParams() {
    return new _angular_common_http__WEBPACK_IMPORTED_MODULE_3__.HttpParams().set('secret', src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.secret).set('client', src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.client);
  }
  initializeCurrentUser() {
    if (this.isInitialized) return;
    const token = localStorage.getItem('token');
    if (!token || this.jwtHelper.isTokenExpired(token)) {
      this.isInitialized = true;
      return;
    }
    const decodedToken = this.jwtHelper.decodeToken(token);
    // Déterminer l'image de profil à utiliser
    let profileImage = 'assets/images/default-profile.png';
    // Vérifier d'abord profileImage
    if (decodedToken.profileImage && decodedToken.profileImage !== 'null' && decodedToken.profileImage !== 'undefined' && decodedToken.profileImage.trim() !== '') {
      profileImage = decodedToken.profileImage;
    }
    // Ensuite vérifier image si profileImage n'est pas valide
    else if (decodedToken.image && decodedToken.image !== 'null' && decodedToken.image !== 'undefined' && decodedToken.image.trim() !== '') {
      profileImage = decodedToken.image;
    }
    console.log('AuthuserService - Using profile image:', profileImage);
    const fallbackUser = {
      _id: decodedToken.id,
      username: decodedToken.username,
      fullName: decodedToken.fullName,
      email: decodedToken.email,
      role: decodedToken.role,
      image: profileImage,
      profileImage: profileImage,
      isActive: true
    };
    this.currentUserSubject.next(fallbackUser);
    this.isInitialized = true;
  }
  // Typage plus strict pour les réponses
  register(userData) {
    return this.http.post(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}users/register`, userData).pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_4__.tap)(response => {
      this.saveToken(response.token);
      this.setCurrentUser(response.user);
    }), (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.catchError)(this.handleError));
  }
  login(credentials) {
    return this.http.post(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}users/login`, credentials).pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_4__.tap)(response => {
      this.saveToken(response.token);
      this.setCurrentUser(response.user);
    }), (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.catchError)(this.handleError));
  }
  setCurrentUser(user) {
    this.currentUserSubject.next(user);
  }
  getCurrentUser() {
    return this.currentUserSubject.value;
  }
  getCurrentUserRole() {
    const currentUser = this.getCurrentUser();
    if (currentUser && currentUser.role) {
      return currentUser.role;
    }
    // Fallback: try to get role from JWT token
    const token = this.getToken();
    if (token) {
      try {
        const decodedToken = this.jwtHelper.decodeToken(token);
        return decodedToken?.role || null;
      } catch (error) {
        console.error('Error decoding token for role:', error);
        return null;
      }
    }
    return null;
  }
  saveToken(token) {
    localStorage.setItem('token', token);
    this.initializeCurrentUser();
    // Notifier du changement d'authentification
    this.authChangeSubject.next({
      type: 'login',
      token
    });
  }
  getToken() {
    return localStorage.getItem('token');
  }
  userLoggedIn() {
    const token = localStorage.getItem('token');
    if (!token) return false;
    const decodedToken = this.jwtHelper.decodeToken(token);
    return !!decodedToken?.role && !this.jwtHelper.isTokenExpired(token);
  }
  getCurrentUserId() {
    const token = localStorage.getItem('token');
    if (!token) return null;
    return this.jwtHelper.decodeToken(token)?.id || null;
  }
  // Déconnexion plus robuste
  // Modifiez la méthode logout
  logout() {
    return this.http.put(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}users/logout`, {}, {
      headers: this.getUserHeaders(),
      params: this.getCommonParams()
    }).pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_4__.tap)(() => {
      this.clearAuthData();
      this.router.navigate(['/loginuser'], {
        queryParams: {
          message: 'Vous avez été déconnecté avec succès'
        },
        replaceUrl: true
      });
    }), (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.catchError)(error => {
      this.clearAuthData();
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_6__.throwError)(() => error);
    }));
  }
  deactivateSelf() {
    if (!this.userLoggedIn()) {
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_6__.throwError)(() => new Error('User not logged in'));
    }
    return this.http.put(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}users/deactivateself`, {}, {
      headers: this.getUserHeaders(),
      params: this.getCommonParams()
    }).pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_4__.tap)(() => this.clearAuthData()));
  }
  clearAuthData() {
    localStorage.removeItem('token');
    this.currentUserSubject.next(null);
    this.isInitialized = false;
    // Notifier du changement d'authentification
    this.authChangeSubject.next({
      type: 'logout',
      token: null
    });
  }
  handleError(error) {
    let errorMessage = 'Authentication error';
    if (error.error instanceof ErrorEvent) {
      errorMessage = `Client error: ${error.error.message}`;
    } else {
      errorMessage = error.error?.message || error.message || 'Unknown authentication error';
    }
    return (0,rxjs__WEBPACK_IMPORTED_MODULE_6__.throwError)(() => new Error(errorMessage));
  }
  static {
    this.ɵfac = function AuthuserService_Factory(t) {
      return new (t || AuthuserService)(_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵinject"](_angular_common_http__WEBPACK_IMPORTED_MODULE_3__.HttpClient), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵinject"](_angular_router__WEBPACK_IMPORTED_MODULE_8__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵinject"](_auth0_angular_jwt__WEBPACK_IMPORTED_MODULE_9__.JwtHelperService));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdefineInjectable"]({
      token: AuthuserService,
      factory: AuthuserService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 8490:
/*!******************************************!*\
  !*** ./src/app/services/data.service.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DataService: () => (/* binding */ DataService)
/* harmony export */ });
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common/http */ 6443);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rxjs */ 5797);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rxjs */ 8764);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rxjs */ 1318);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rxjs */ 9452);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rxjs */ 7919);
/* harmony import */ var src_environments_environment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/environments/environment */ 5312);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _auth0_angular_jwt__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @auth0/angular-jwt */ 2389);
/* harmony import */ var _authuser_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./authuser.service */ 9271);







class DataService {
  constructor(http, jwtHelper, authService) {
    this.http = http;
    this.jwtHelper = jwtHelper;
    this.authService = authService;
    this.usersCache$ = new rxjs__WEBPACK_IMPORTED_MODULE_2__.BehaviorSubject([]);
    this.lastFetchTime = 0;
    this.CACHE_DURATION = 300000;
    this.currentUserSubject = new rxjs__WEBPACK_IMPORTED_MODULE_2__.BehaviorSubject(null);
    this.userAddedSubject = new rxjs__WEBPACK_IMPORTED_MODULE_2__.BehaviorSubject(null);
    this.userAdded$ = this.userAddedSubject.asObservable();
    this.currentUser$ = this.currentUserSubject.asObservable();
    this.initializeCurrentUser();
  }
  fetchCurrentUser() {
    return this.getProfile().pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_3__.tap)(user => this.currentUserSubject.next(user)));
  }
  getAdminHeaders() {
    const token = localStorage.getItem('token');
    if (!token || this.jwtHelper.isTokenExpired(token)) {
      throw new Error('Token invalide ou expiré');
    }
    return new _angular_common_http__WEBPACK_IMPORTED_MODULE_4__.HttpHeaders({
      Authorization: `Bearer ${token}`,
      role: 'admin',
      'Content-Type': 'application/json'
    });
  }
  getUserHeaders() {
    const token = localStorage.getItem('token');
    if (!token || this.jwtHelper.isTokenExpired(token)) {
      throw new Error('Token invalide ou expiré');
    }
    return new _angular_common_http__WEBPACK_IMPORTED_MODULE_4__.HttpHeaders({
      Authorization: `Bearer ${token || ''}`,
      'Content-Type': 'application/json'
    });
  }
  getCommonParams() {
    return new _angular_common_http__WEBPACK_IMPORTED_MODULE_4__.HttpParams().set('secret', src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.secret).set('client', src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.client);
  }
  syncCurrentUser() {
    return this.getProfile().pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_3__.tap)(user => {
      this.currentUserSubject.next(user);
      this.authService.setCurrentUser(user);
    }), (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.catchError)(error => {
      // If fetch fails, try to get from auth service
      const authUser = this.authService.getCurrentUser();
      if (authUser) {
        this.currentUserSubject.next(authUser);
        return (0,rxjs__WEBPACK_IMPORTED_MODULE_6__.of)(authUser);
      }
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_7__.throwError)(() => error);
    }));
  }
  getProfile() {
    return this.http.get(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}users/profile`, {
      headers: this.getUserHeaders(),
      params: this.getCommonParams()
    }).pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_5__.catchError)(this.handleError));
  }
  initializeCurrentUser() {
    const token = localStorage.getItem('token');
    if (token && !this.jwtHelper.isTokenExpired(token)) {
      this.syncCurrentUser().subscribe({
        error: () => {
          const decodedToken = this.jwtHelper.decodeToken(token);
          // Déterminer l'image de profil à utiliser
          let profileImage = 'assets/images/default-profile.png';
          // Vérifier d'abord profileImage
          if (decodedToken.profileImage && decodedToken.profileImage !== 'null' && decodedToken.profileImage !== 'undefined' && decodedToken.profileImage.trim() !== '') {
            profileImage = decodedToken.profileImage;
          }
          // Ensuite vérifier image si profileImage n'est pas valide
          else if (decodedToken.image && decodedToken.image !== 'null' && decodedToken.image !== 'undefined' && decodedToken.image.trim() !== '') {
            profileImage = decodedToken.image;
          }
          console.log('DataService - Using profile image:', profileImage);
          const fallbackUser = {
            _id: decodedToken.id,
            username: decodedToken.username,
            email: decodedToken.email,
            role: decodedToken.role,
            image: profileImage,
            profileImage: profileImage,
            isActive: true
          };
          this.currentUserSubject.next(fallbackUser);
          this.authService.setCurrentUser(fallbackUser);
        }
      });
    }
  }
  updateCurrentUser(userData) {
    const currentUser = this.currentUserSubject.value;
    if (currentUser) {
      this.currentUserSubject.next({
        ...currentUser,
        ...userData
      });
    }
  }
  updateSelf(userId, updateData) {
    return this.http.put(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}users/updateself/${userId}`, updateData, {
      headers: this.getUserHeaders(),
      params: this.getCommonParams()
    }).pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_3__.tap)(updatedUser => {
      this.updateUserInCache(updatedUser);
      this.updateCurrentUser(updatedUser);
    }), (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.catchError)(this.handleError));
  }
  changePassword(currentPassword, newPassword) {
    const userId = this.currentUserValue?._id;
    if (!userId) {
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_7__.throwError)(() => new Error('User not logged in'));
    }
    const passwordData = {
      currentPassword,
      newPassword
    };
    return this.http.put(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}users/updateself/${userId}`, passwordData, {
      headers: this.getUserHeaders(),
      params: this.getCommonParams()
    }).pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_3__.tap)(response => {
      if (response.token) {
        localStorage.setItem('token', response.token);
        const decodedToken = this.jwtHelper.decodeToken(response.token);
        this.updateCurrentUser(decodedToken);
      }
    }), (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.catchError)(error => {
      if (error.status === 400) {
        if (error.error?.errors?.general) {
          return (0,rxjs__WEBPACK_IMPORTED_MODULE_7__.throwError)(() => new Error(error.error.errors.general));
        }
        return (0,rxjs__WEBPACK_IMPORTED_MODULE_7__.throwError)(() => new Error(error.error?.message || 'Validation failed'));
      }
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_7__.throwError)(() => new Error('Failed to change password'));
    }));
  }
  uploadProfileImage(file) {
    const formData = new FormData();
    formData.append('image', file);
    return this.http.post(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}users/upload-profile-image`, formData, {
      headers: new _angular_common_http__WEBPACK_IMPORTED_MODULE_4__.HttpHeaders({
        Authorization: `Bearer ${localStorage.getItem('token')}`
      }),
      params: this.getCommonParams()
    }).pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_3__.tap)(response => {
      if (response.token) {
        localStorage.setItem('token', response.token);
        const decodedToken = this.jwtHelper.decodeToken(response.token);
        this.updateCurrentUser({
          image: response.imageUrl,
          ...decodedToken
        });
      }
    }), (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.catchError)(this.handleError));
  }
  removeProfileImage() {
    return this.http.delete(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}users/remove-profile-image`, {
      headers: this.getUserHeaders(),
      params: this.getCommonParams()
    }).pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_3__.tap)(response => {
      if (response.token) {
        localStorage.setItem('token', response.token);
        let decodeToken = this.jwtHelper.decodeToken(response.token);
        this.updateCurrentUser(decodeToken);
      }
    }), (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.catchError)(this.handleError));
  }
  get currentUserValue() {
    return this.currentUserSubject.value;
  }
  isAdmin() {
    return this.currentUserValue?.role === 'admin';
  }
  isCurrentUser(userId) {
    return this.currentUserValue?._id === userId;
  }
  updateUserInCache(updatedUser) {
    const updatedUsers = this.usersCache$.value.map(u => u._id === updatedUser._id ? {
      ...u,
      ...updatedUser
    } : u);
    this.usersCache$.next(updatedUsers);
  }
  refreshUserCache() {
    this.lastFetchTime = 0;
    this.getAllUsers(true).subscribe();
  }
  getAllUsers(forceRefresh = false) {
    const now = Date.now();
    const cacheValid = !forceRefresh && this.usersCache$.value.length > 0 && now - this.lastFetchTime <= this.CACHE_DURATION;
    if (cacheValid) {
      return this.usersCache$.asObservable();
    }
    this.lastFetchTime = now;
    return this.http.get(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}users/getall`, {
      headers: this.getAdminHeaders(),
      params: this.getCommonParams()
    }).pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_3__.tap)(users => this.usersCache$.next([...users])), (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.catchError)(this.handleError));
  }
  getOneUser(id) {
    return this.http.get(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}users/getone/${id}`, {
      headers: this.getAdminHeaders(),
      params: this.getCommonParams()
    }).pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_5__.catchError)(this.handleError));
  }
  addUser(userData) {
    return this.http.post(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}users/add`, userData, {
      headers: this.getAdminHeaders(),
      params: this.getCommonParams()
    }).pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_3__.tap)(newUser => {
      const currentUsers = this.usersCache$.value;
      this.usersCache$.next([...currentUsers, newUser]);
      this.userAddedSubject.next();
    }), (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.catchError)(this.handleError));
  }
  deleteUser(id) {
    return this.http.delete(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}users/delete/${id}`, {
      headers: this.getAdminHeaders(),
      params: this.getCommonParams()
    }).pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_3__.tap)(() => {
      const updatedUsers = this.usersCache$.value.filter(u => u._id !== id);
      this.usersCache$.next(updatedUsers);
    }), (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.catchError)(this.handleError));
  }
  updateUserByAdmin(id, data) {
    return this.http.put(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}users/update/${id}`, data, {
      headers: this.getAdminHeaders(),
      params: this.getCommonParams()
    }).pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_3__.tap)(updatedUser => this.updateUserInCache(updatedUser)), (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.catchError)(this.handleError));
  }
  deactivateUser(id) {
    return this.http.put(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}users/update/${id}/deactivate`, {}, {
      headers: this.getAdminHeaders(),
      params: this.getCommonParams()
    }).pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_3__.tap)(updatedUser => this.updateUserInCache(updatedUser)), (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.catchError)(this.handleError));
  }
  reactivateUser(id) {
    return this.http.put(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}users/update/${id}/reactivate`, {}, {
      headers: this.getAdminHeaders(),
      params: this.getCommonParams()
    }).pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_3__.tap)(updatedUser => this.updateUserInCache(updatedUser)), (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.catchError)(this.handleError));
  }
  updateUserRole(id, role) {
    return this.http.put(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}admin/users/${id}/role`, {
      role
    }, {
      headers: this.getAdminHeaders(),
      params: this.getCommonParams()
    }).pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_3__.tap)(updatedUser => this.updateUserInCache(updatedUser)), (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.catchError)(this.handleError));
  }
  toggleUserActivation(id, isActive) {
    return this.http.put(`${src_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.urlBackend}admin/users/${id}/activation`, {
      isActive
    }, {
      headers: this.getAdminHeaders(),
      params: this.getCommonParams()
    }).pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_3__.tap)(updatedUser => this.updateUserInCache(updatedUser)), (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.catchError)(this.handleError));
  }
  handleError(error) {
    let errorMessage = 'Une erreur est survenue';
    if (error.status === 0) {
      errorMessage = 'Erreur réseau - impossible de contacter le serveur';
    } else if (error.status >= 400 && error.status < 500) {
      errorMessage = error.error?.message || error.message;
    } else if (error.status >= 500) {
      errorMessage = 'Erreur serveur - veuillez réessayer plus tard';
    }
    console.error(`Erreur ${error.status}:`, error.error);
    return (0,rxjs__WEBPACK_IMPORTED_MODULE_7__.throwError)(() => new Error(errorMessage));
  }
  static {
    this.ɵfac = function DataService_Factory(t) {
      return new (t || DataService)(_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵinject"](_angular_common_http__WEBPACK_IMPORTED_MODULE_4__.HttpClient), _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵinject"](_auth0_angular_jwt__WEBPACK_IMPORTED_MODULE_9__.JwtHelperService), _angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵinject"](_authuser_service__WEBPACK_IMPORTED_MODULE_1__.AuthuserService));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdefineInjectable"]({
      token: DataService,
      factory: DataService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 4798:
/*!********************************************!*\
  !*** ./src/app/services/logger.service.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LoggerService: () => (/* binding */ LoggerService)
/* harmony export */ });
/* harmony import */ var _env_environment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @env/environment */ 5312);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);


class LoggerService {
  constructor() {
    // Configuration des logs
    this.enableLogs = false; // Désactiver les logs par défaut
    this.enabledComponents = [
      // Liste des composants pour lesquels les logs sont activés
      // Exemple: 'MessageService', 'MessageChat'
    ];
  }
  /**
   * Active ou désactive les logs
   */
  setLogsEnabled(enabled) {
    this.enableLogs = enabled;
  }
  /**
   * Ajoute un composant à la liste des composants pour lesquels les logs sont activés
   */
  enableComponentLogs(component) {
    if (!this.enabledComponents.includes(component)) {
      this.enabledComponents.push(component);
    }
  }
  /**
   * Supprime un composant de la liste des composants pour lesquels les logs sont activés
   */
  disableComponentLogs(component) {
    const index = this.enabledComponents.indexOf(component);
    if (index !== -1) {
      this.enabledComponents.splice(index, 1);
    }
  }
  /**
   * Supprime tous les composants de la liste des composants pour lesquels les logs sont activés
   */
  clearEnabledComponents() {
    this.enabledComponents = [];
  }
  /**
   * Log a message at the 'log' level
   */
  log(message, context) {
    if (!_env_environment__WEBPACK_IMPORTED_MODULE_0__.environment.production) {
      console.log(message, context);
    }
  }
  /**
   * Log a message at the 'debug' level
   * Supports multiple formats:
   * - debug(message)
   * - debug(message, context)
   * - debug(component, message)
   * - debug(component, message, context)
   */
  /**
   * Vérifie si les logs sont activés pour un composant donné
   */
  shouldLog(component) {
    if (_env_environment__WEBPACK_IMPORTED_MODULE_0__.environment.production) return false;
    if (!this.enableLogs) return false;
    if (!component) return true;
    if (this.enabledComponents.length === 0) return true;
    return this.enabledComponents.includes(component);
  }
  debug(messageOrComponent, contextOrMessage, context) {
    if (typeof messageOrComponent === 'string' && typeof contextOrMessage === 'string') {
      // Format: debug(component, message, context)
      if (!this.shouldLog(messageOrComponent)) return;
      if (context !== undefined) {
        console.debug(`[${messageOrComponent}] ${contextOrMessage}`, context);
      } else {
        console.debug(`[${messageOrComponent}] ${contextOrMessage}`);
      }
    } else if (typeof messageOrComponent === 'string' && contextOrMessage !== undefined) {
      // Format: debug(message, context)
      if (!this.shouldLog()) return;
      console.debug(messageOrComponent, contextOrMessage);
    } else {
      // Format: debug(message)
      if (!this.shouldLog()) return;
      console.debug(messageOrComponent);
    }
  }
  /**
   * Log a message at the 'error' level
   * Supports multiple formats:
   * - error(message)
   * - error(error)
   * - error(message, error)
   * - error(message, context)
   * - error(component, message)
   * - error(component, error)
   * - error(component, message, error)
   * - error(component, message, context)
   */
  error(messageOrComponentOrError, errorOrMessageOrContext, contextOrError) {
    // Les erreurs sont toujours affichées, même en production
    if (typeof messageOrComponentOrError === 'string' && typeof errorOrMessageOrContext === 'string') {
      // Format: error(component, message, context/error)
      // Pour les erreurs, on vérifie quand même si le composant est activé
      if (!this.shouldLog(messageOrComponentOrError) && !_env_environment__WEBPACK_IMPORTED_MODULE_0__.environment.production) return;
      if (contextOrError !== undefined) {
        console.error(`[${messageOrComponentOrError}] ${errorOrMessageOrContext}`, contextOrError);
      } else {
        console.error(`[${messageOrComponentOrError}] ${errorOrMessageOrContext}`);
      }
    } else if (typeof messageOrComponentOrError === 'string' && errorOrMessageOrContext instanceof Error) {
      // Format: error(component/message, error)
      console.error(messageOrComponentOrError, errorOrMessageOrContext, contextOrError);
    } else if (typeof messageOrComponentOrError === 'string') {
      // Format: error(message, context)
      console.error(messageOrComponentOrError, errorOrMessageOrContext);
    } else if (messageOrComponentOrError instanceof Error) {
      // Format: error(error, context)
      console.error(messageOrComponentOrError, errorOrMessageOrContext);
    } else {
      // Fallback
      console.error(messageOrComponentOrError, errorOrMessageOrContext, contextOrError);
    }
  }
  /**
   * Log a message at the 'warn' level
   * Supports multiple formats:
   * - warn(message)
   * - warn(message, context)
   * - warn(component, message)
   * - warn(component, message, context)
   */
  warn(messageOrComponent, contextOrMessage, context) {
    if (typeof messageOrComponent === 'string' && typeof contextOrMessage === 'string') {
      // Format: warn(component, message, context)
      if (!this.shouldLog(messageOrComponent)) return;
      if (context !== undefined) {
        console.warn(`[${messageOrComponent}] ${contextOrMessage}`, context);
      } else {
        console.warn(`[${messageOrComponent}] ${contextOrMessage}`);
      }
    } else if (typeof messageOrComponent === 'string' && contextOrMessage !== undefined) {
      // Format: warn(message, context)
      if (!this.shouldLog()) return;
      console.warn(messageOrComponent, contextOrMessage);
    } else {
      // Format: warn(message)
      if (!this.shouldLog()) return;
      console.warn(messageOrComponent);
    }
  }
  /**
   * Log a message at the 'info' level
   * Supports multiple formats:
   * - info(message)
   * - info(message, context)
   * - info(component, message)
   * - info(component, message, context)
   */
  info(messageOrComponent, contextOrMessage, context) {
    if (typeof messageOrComponent === 'string' && typeof contextOrMessage === 'string') {
      // Format: info(component, message, context)
      if (!this.shouldLog(messageOrComponent)) return;
      if (context !== undefined) {
        console.info(`[${messageOrComponent}] ${contextOrMessage}`, context);
      } else {
        console.info(`[${messageOrComponent}] ${contextOrMessage}`);
      }
    } else if (typeof messageOrComponent === 'string' && contextOrMessage !== undefined) {
      // Format: info(message, context)
      if (!this.shouldLog()) return;
      console.info(messageOrComponent, contextOrMessage);
    } else {
      // Format: info(message)
      if (!this.shouldLog()) return;
      console.info(messageOrComponent);
    }
  }
  static {
    this.ɵfac = function LoggerService_Factory(t) {
      return new (t || LoggerService)();
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
      token: LoggerService,
      factory: LoggerService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 4537:
/*!*********************************************!*\
  !*** ./src/app/services/message.service.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MessageService: () => (/* binding */ MessageService)
/* harmony export */ });
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rxjs */ 5797);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rxjs */ 9452);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rxjs */ 7919);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rxjs */ 1995);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! rxjs */ 3942);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! rxjs */ 9400);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rxjs/operators */ 271);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rxjs/operators */ 1318);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! rxjs/operators */ 8764);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! rxjs/operators */ 2575);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! rxjs/operators */ 1567);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! rxjs/operators */ 1817);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! rxjs/operators */ 6301);
/* harmony import */ var _environments_environment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../environments/environment */ 5312);
/* harmony import */ var _models_message_model__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../models/message.model */ 5293);
/* harmony import */ var _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../graphql/message.graphql */ 8896);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var apollo_angular__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! apollo-angular */ 7797);
/* harmony import */ var _logger_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./logger.service */ 4798);








class MessageService {
  constructor(apollo, logger, zone) {
    this.apollo = apollo;
    this.logger = logger;
    this.zone = zone;
    // État partagé
    this.activeConversation = new rxjs__WEBPACK_IMPORTED_MODULE_4__.BehaviorSubject(null);
    this.notifications = new rxjs__WEBPACK_IMPORTED_MODULE_4__.BehaviorSubject([]);
    this.notificationCache = new Map();
    this.notificationCount = new rxjs__WEBPACK_IMPORTED_MODULE_4__.BehaviorSubject(0);
    this.onlineUsers = new Map();
    this.subscriptions = [];
    this.CACHE_DURATION = 300000;
    this.lastFetchTime = 0;
    // Propriétés pour les appels
    this.activeCall = new rxjs__WEBPACK_IMPORTED_MODULE_4__.BehaviorSubject(null);
    this.incomingCall = new rxjs__WEBPACK_IMPORTED_MODULE_4__.BehaviorSubject(null);
    this.callSignals = new rxjs__WEBPACK_IMPORTED_MODULE_4__.BehaviorSubject(null);
    this.localStream = null;
    this.remoteStream = null;
    this.peerConnection = null;
    // Observables publics pour les appels
    this.activeCall$ = this.activeCall.asObservable();
    this.incomingCall$ = this.incomingCall.asObservable();
    this.callSignals$ = this.callSignals.asObservable();
    this.localStream$ = new rxjs__WEBPACK_IMPORTED_MODULE_4__.BehaviorSubject(null);
    this.remoteStream$ = new rxjs__WEBPACK_IMPORTED_MODULE_4__.BehaviorSubject(null);
    // Configuration WebRTC
    this.rtcConfig = {
      iceServers: [{
        urls: 'stun:stun.l.google.com:19302'
      }, {
        urls: 'stun:stun1.l.google.com:19302'
      }]
    };
    this.usersCache = [];
    // Pagination metadata for user list
    this.currentUserPagination = {
      totalCount: 0,
      totalPages: 0,
      currentPage: 1,
      hasNextPage: false,
      hasPreviousPage: false
    };
    // Observables publics
    this.activeConversation$ = this.activeConversation.asObservable();
    this.notifications$ = this.notifications.asObservable();
    this.notificationCount$ = this.notificationCount.asObservable();
    // Propriétés pour la gestion des sons
    this.sounds = {};
    this.isPlaying = {};
    this.muted = false;
    // --------------------------------------------------------------------------
    // Section 2: Méthodes pour les Notifications
    // --------------------------------------------------------------------------
    // Propriétés pour la pagination des notifications
    this.notificationPagination = {
      currentPage: 1,
      limit: 10,
      hasMoreNotifications: true
    };
    // --------------------------------------------------------------------------
    // Section 4: Subscriptions et Gestion Temps Réel
    // --------------------------------------------------------------------------
    // ✅ Optimized subscription with connection pooling and caching
    this.subscriptionCache = new Map();
    this.subscriptionRefCount = new Map();
    this.toSafeISOString = date => {
      if (!date) return undefined;
      return typeof date === 'string' ? date : date.toISOString();
    };
    this.loadNotificationsFromLocalStorage();
    this.initSubscriptions();
    this.startCleanupInterval();
    this.preloadSounds();
  }
  /**
   * Charge les notifications depuis le localStorage
   * @private
   */
  loadNotificationsFromLocalStorage() {
    try {
      const savedNotifications = localStorage.getItem('notifications');
      if (savedNotifications) {
        const notifications = JSON.parse(savedNotifications);
        this.notificationCache.clear();
        notifications.forEach(notification => {
          if (notification && notification.id) {
            this.notificationCache.set(notification.id, notification);
          }
        });
        this.notifications.next(Array.from(this.notificationCache.values()));
        this.updateUnreadCount();
      }
    } catch (error) {
      // Handle error silently
    }
  }
  initSubscriptions() {
    this.zone.runOutsideAngular(() => {
      this.subscribeToNewNotifications().subscribe();
      this.subscribeToNotificationsRead().subscribe();
      this.subscribeToIncomingCalls().subscribe();
      // 🔥 AJOUT: Subscription générale pour l'utilisateur
    });

    this.subscribeToUserStatus();
  }
  /**
   * S'abonne aux appels entrants
   */
  subscribeToIncomingCalls() {
    return this.apollo.subscribe({
      query: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.INCOMING_CALL_SUBSCRIPTION
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(({
      data
    }) => {
      if (!data?.incomingCall) {
        return null;
      }
      // Gérer l'appel entrant
      this.handleIncomingCall(data.incomingCall);
      return data.incomingCall;
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('Error in incoming call subscription', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_7__.of)(null);
    }));
  }
  /**
   * Gère un appel entrant
   */
  handleIncomingCall(call) {
    this.incomingCall.next(call);
    this.play('ringtone', true);
  }
  // --------------------------------------------------------------------------
  // Section: Subscriptions aux événements temps réel
  // --------------------------------------------------------------------------
  /**
   * S'abonne aux nouveaux messages
   */
  subscribeToMessages() {
    return this.apollo.subscribe({
      query: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.MESSAGE_SENT_SUBSCRIPTION
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(({
      data
    }) => {
      if (!data?.messageSent) {
        return null;
      }
      return this.normalizeMessage(data.messageSent);
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('Error in message subscription', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_7__.of)(null);
    }));
  }
  /**
   * S'abonne aux indicateurs de frappe
   */
  subscribeToTypingIndicators() {
    return this.apollo.subscribe({
      query: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.TYPING_INDICATOR_SUBSCRIPTION
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(({
      data
    }) => {
      if (!data?.typingIndicator) {
        return null;
      }
      return data.typingIndicator;
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('Error in typing indicator subscription', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_7__.of)(null);
    }));
  }
  // Méthodes de frappe déplacées vers la fin du fichier pour éviter les doublons
  /**
   * Initie un appel
   */
  initiateCall(recipientId, callType) {
    return this.apollo.mutate({
      mutation: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.INITIATE_CALL_MUTATION,
      variables: {
        recipientId,
        callType
      }
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      if (!result.data?.initiateCall) {
        throw new Error('Failed to initiate call');
      }
      return result.data.initiateCall;
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('Error initiating call', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Failed to initiate call'));
    }));
  }
  /**
   * Envoie un message avec fichier
   */
  sendMessageWithFile(senderId, receiverId, content, file) {
    return this.apollo.mutate({
      mutation: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.SEND_MESSAGE_MUTATION,
      variables: {
        senderId,
        receiverId,
        content,
        file
      }
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      if (!result.data?.sendMessage) {
        throw new Error('Failed to send message');
      }
      return this.normalizeMessage(result.data.sendMessage);
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('Error sending message with file', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Failed to send message'));
    }));
  }
  // Méthode sendMessage déplacée vers la fin du fichier pour éviter les doublons
  /**
   * S'abonne aux notifications
   */
  subscribeToNotifications() {
    return this.apollo.subscribe({
      query: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.NOTIFICATION_SUBSCRIPTION
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(({
      data
    }) => {
      if (!data?.notificationReceived) {
        return null;
      }
      return data.notificationReceived;
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('Error in notification subscription', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_7__.of)(null);
    }));
  }
  /**
   * Marque une notification comme lue
   */
  markNotificationAsRead(notificationId) {
    return this.apollo.mutate({
      mutation: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.MARK_NOTIFICATION_READ_MUTATION,
      variables: {
        notificationId
      }
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => result.data?.markNotificationAsRead || false), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('Error marking notification as read', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_7__.of)(false);
    }));
  }
  /**
   * Crée ou récupère une conversation avec un utilisateur
   */
  createOrGetConversation(userId) {
    return this.apollo.mutate({
      mutation: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.CREATE_CONVERSATION_MUTATION,
      variables: {
        userId
      }
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      if (!result.data?.createConversation) {
        throw new Error('Failed to create or get conversation');
      }
      return this.normalizeConversation(result.data.createConversation);
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('Error creating or getting conversation', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Failed to create or get conversation'));
    }));
  }
  // --------------------------------------------------------------------------
  // Section: Gestion des sons (intégré depuis SoundService)
  // --------------------------------------------------------------------------
  /**
   * Précharge les sons utilisés dans l'application
   */
  preloadSounds() {
    this.loadSound('ringtone', 'assets/sounds/ringtone.mp3');
    this.loadSound('call-end', 'assets/sounds/call-end.mp3');
    this.loadSound('call-connected', 'assets/sounds/call-connected.mp3');
    this.loadSound('notification', 'assets/sounds/notification.mp3');
  }
  /**
   * Charge un fichier audio
   * @param name Nom du son
   * @param path Chemin du fichier
   */
  loadSound(name, path) {
    try {
      const audio = new Audio(path);
      audio.load();
      this.sounds[name] = audio;
      this.isPlaying[name] = false;
      audio.addEventListener('ended', () => {
        this.isPlaying[name] = false;
      });
    } catch (error) {
      // Handle error silently
    }
  }
  /**
   * Joue un son
   * @param name Nom du son
   * @param loop Lecture en boucle
   */
  play(name, loop = false) {
    if (this.muted) {
      return;
    }
    try {
      const sound = this.sounds[name];
      if (!sound) {
        return;
      }
      sound.loop = loop;
      if (!this.isPlaying[name]) {
        sound.currentTime = 0;
        sound.play().catch(error => {
          // Handle error silently
        });
        this.isPlaying[name] = true;
      }
    } catch (error) {
      // Handle error silently
    }
  }
  /**
   * Arrête un son
   * @param name Nom du son
   */
  stop(name) {
    try {
      const sound = this.sounds[name];
      if (!sound) {
        return;
      }
      if (this.isPlaying[name]) {
        sound.pause();
        sound.currentTime = 0;
        this.isPlaying[name] = false;
      }
    } catch (error) {
      // Handle error silently
    }
  }
  /**
   * Arrête tous les sons
   */
  stopAllSounds() {
    Object.keys(this.sounds).forEach(name => {
      this.stop(name);
    });
  }
  /**
   * Active ou désactive le son
   * @param muted True pour désactiver le son, false pour l'activer
   */
  setMuted(muted) {
    this.muted = muted;
    if (muted) {
      this.stopAllSounds();
    }
  }
  /**
   * Vérifie si le son est désactivé
   * @returns True si le son est désactivé, false sinon
   */
  isMuted() {
    return this.muted;
  }
  /**
   * Joue le son de notification
   */
  playNotificationSound() {
    console.log('MessageService: Tentative de lecture du son de notification');
    if (this.muted) {
      console.log('MessageService: Son désactivé, notification ignorée');
      return;
    }
    // Créer une mélodie agréable avec l'API Web Audio
    try {
      // Créer un contexte audio
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      // 🎵 TESTEZ DIFFÉRENTS SONS - Décommentez celui que vous voulez tester !
      // SON 1: Mélodie douce (WhatsApp style) - ACTUEL
      this.playNotificationMelody1(audioContext);
      // SON 2: Mélodie montante (iPhone style) - Décommentez pour tester
      // this.playNotificationMelody2(audioContext);
      // SON 3: Mélodie descendante (Messenger style) - Décommentez pour tester
      // this.playNotificationMelody3(audioContext);
      // SON 4: Triple note (Discord style) - Décommentez pour tester
      // this.playNotificationMelody4(audioContext);
      // SON 5: Cloche douce (Slack style) - Décommentez pour tester
      // this.playNotificationMelody5(audioContext);
      console.log('MessageService: Son de notification mélodieux généré avec succès');
    } catch (error) {
      console.error('MessageService: Erreur lors de la génération du son:', error);
      // Fallback à la méthode originale en cas d'erreur
      try {
        const audio = new Audio('assets/sounds/notification.mp3');
        audio.volume = 0.7; // Volume plus doux
        audio.play().catch(err => {
          console.error('MessageService: Erreur lors de la lecture du fichier son:', err);
        });
      } catch (audioError) {
        console.error('MessageService: Exception lors de la lecture du fichier son:', audioError);
      }
    }
  }
  // 🎵 SON 1: Mélodie douce (WhatsApp style)
  playNotificationMelody1(audioContext) {
    this.playNotificationTone(audioContext, 0, 659.25, 0.15); // E5
    this.playNotificationTone(audioContext, 0.15, 523.25, 0.15); // C5
  }
  // 🎵 SON 2: Mélodie montante (iPhone style)
  playNotificationMelody2(audioContext) {
    this.playNotificationTone(audioContext, 0, 523.25, 0.12); // C5
    this.playNotificationTone(audioContext, 0.12, 659.25, 0.12); // E5
    this.playNotificationTone(audioContext, 0.24, 783.99, 0.16); // G5
  }
  // 🎵 SON 3: Mélodie descendante (Messenger style)
  playNotificationMelody3(audioContext) {
    this.playNotificationTone(audioContext, 0, 880, 0.1); // A5
    this.playNotificationTone(audioContext, 0.1, 659.25, 0.1); // E5
    this.playNotificationTone(audioContext, 0.2, 523.25, 0.15); // C5
  }
  // 🎵 SON 4: Triple note (Discord style)
  playNotificationMelody4(audioContext) {
    this.playNotificationTone(audioContext, 0, 698.46, 0.08); // F5
    this.playNotificationTone(audioContext, 0.08, 698.46, 0.08); // F5
    this.playNotificationTone(audioContext, 0.16, 880, 0.12); // A5
  }
  // 🎵 SON 5: Cloche douce (Slack style)
  playNotificationMelody5(audioContext) {
    this.playBellTone(audioContext, 0, 1046.5, 0.4); // C6 - son de cloche
  }
  /**
   * Joue une note individuelle pour la mélodie de notification
   */
  playNotificationTone(audioContext, startTime, frequency, duration) {
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    // Configurer l'oscillateur pour un son plus doux
    oscillator.type = 'sine';
    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime + startTime);
    // Configurer le volume avec une enveloppe douce
    gainNode.gain.setValueAtTime(0, audioContext.currentTime + startTime);
    gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + startTime + 0.02);
    gainNode.gain.linearRampToValueAtTime(0.2, audioContext.currentTime + startTime + duration * 0.7);
    gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + startTime + duration);
    // Connecter les nœuds
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    // Démarrer et arrêter l'oscillateur
    oscillator.start(audioContext.currentTime + startTime);
    oscillator.stop(audioContext.currentTime + startTime + duration);
  }
  /**
   * Joue un son de cloche pour les notifications
   */
  playBellTone(audioContext, startTime, frequency, duration) {
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    // Configurer l'oscillateur pour un son de cloche
    oscillator.type = 'triangle'; // Son plus doux que sine
    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime + startTime);
    // Enveloppe de cloche (attaque rapide, déclin lent)
    gainNode.gain.setValueAtTime(0, audioContext.currentTime + startTime);
    gainNode.gain.linearRampToValueAtTime(0.4, audioContext.currentTime + startTime + 0.01);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + startTime + duration);
    // Connecter les nœuds
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    // Démarrer et arrêter l'oscillateur
    oscillator.start(audioContext.currentTime + startTime);
    oscillator.stop(audioContext.currentTime + startTime + duration);
  }
  // --------------------------------------------------------------------------
  // Section 1: Méthodes pour les Messages
  // --------------------------------------------------------------------------
  /**
   * Joue un fichier audio
   * @param audioUrl URL du fichier audio à jouer
   * @returns Promise qui se résout lorsque la lecture est terminée
   */
  playAudio(audioUrl) {
    return new Promise((resolve, reject) => {
      const audio = new Audio(audioUrl);
      audio.onended = () => {
        resolve();
      };
      audio.onerror = error => {
        this.logger.error(`[MessageService] Error playing audio:`, error);
        reject(error);
      };
      audio.play().catch(error => {
        this.logger.error(`[MessageService] Error playing audio:`, error);
        reject(error);
      });
    });
  }
  /**
   * Récupère tous les messages vocaux de l'utilisateur
   * @returns Observable avec la liste des messages vocaux
   */
  getVoiceMessages() {
    this.logger.debug('[MessageService] Getting voice messages');
    return this.apollo.watchQuery({
      query: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.GET_VOICE_MESSAGES_QUERY,
      fetchPolicy: 'network-only' // Ne pas utiliser le cache pour cette requête
    }).valueChanges.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      const voiceMessages = result.data?.getVoiceMessages || [];
      this.logger.debug(`[MessageService] Retrieved ${voiceMessages.length} voice messages`);
      return voiceMessages;
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('[MessageService] Error fetching voice messages:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Failed to fetch voice messages'));
    }));
  }
  // Message methods
  getMessages(senderId, receiverId, conversationId, page = 1, limit = 25 // ✅ Increased batch size for better performance
  ) {
    return this.apollo.watchQuery({
      query: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.GET_MESSAGES_QUERY,
      variables: {
        senderId,
        receiverId,
        conversationId,
        limit,
        page
      },
      fetchPolicy: 'cache-first',
      errorPolicy: 'all' // ✅ Handle partial errors gracefully
    }).valueChanges.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      const messages = result.data?.getMessages || [];
      // ✅ Batch normalize messages for better performance
      return this.batchNormalizeMessages(messages);
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      console.error('Error fetching messages:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Failed to fetch messages'));
    }));
  }
  editMessage(messageId, newContent) {
    return this.apollo.mutate({
      mutation: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.EDIT_MESSAGE_MUTATION,
      variables: {
        messageId,
        newContent
      }
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      if (!result.data?.editMessage) {
        throw new Error('Failed to edit message');
      }
      return this.normalizeMessage(result.data.editMessage);
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('Error editing message:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Failed to edit message'));
    }));
  }
  deleteMessage(messageId) {
    return this.apollo.mutate({
      mutation: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.DELETE_MESSAGE_MUTATION,
      variables: {
        messageId
      }
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      if (!result.data?.deleteMessage) {
        throw new Error('Failed to delete message');
      }
      return this.normalizeMessage(result.data.deleteMessage);
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('Error deleting message:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Failed to delete message'));
    }));
  }
  markMessageAsRead(messageId) {
    return this.apollo.mutate({
      mutation: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.MARK_AS_READ_MUTATION,
      variables: {
        messageId
      }
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      if (!result.data?.markMessageAsRead) throw new Error('Failed to mark message as read');
      return {
        ...result.data.markMessageAsRead,
        readAt: new Date()
      };
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      console.error('Error marking message as read:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Failed to mark message as read'));
    }));
  }
  reactToMessage(messageId, emoji) {
    return this.apollo.mutate({
      mutation: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.REACT_TO_MESSAGE_MUTATION,
      variables: {
        messageId,
        emoji
      }
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      if (!result.data?.reactToMessage) throw new Error('Failed to react to message');
      return result.data.reactToMessage;
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      console.error('Error reacting to message:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Failed to react to message'));
    }));
  }
  forwardMessage(messageId, conversationIds) {
    return this.apollo.mutate({
      mutation: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.FORWARD_MESSAGE_MUTATION,
      variables: {
        messageId,
        conversationIds
      }
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      if (!result.data?.forwardMessage) throw new Error('Failed to forward message');
      return result.data.forwardMessage.map(msg => ({
        ...msg,
        timestamp: msg.timestamp ? this.normalizeDate(msg.timestamp) : new Date()
      }));
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      console.error('Error forwarding message:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Failed to forward message'));
    }));
  }
  pinMessage(messageId, conversationId) {
    return this.apollo.mutate({
      mutation: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.PIN_MESSAGE_MUTATION,
      variables: {
        messageId,
        conversationId
      }
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      if (!result.data?.pinMessage) throw new Error('Failed to pin message');
      return {
        ...result.data.pinMessage,
        pinnedAt: new Date()
      };
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      console.error('Error pinning message:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Failed to pin message'));
    }));
  }
  searchMessages(query, conversationId, filters = {}) {
    return this.apollo.watchQuery({
      query: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.SEARCH_MESSAGES_QUERY,
      variables: {
        query,
        conversationId,
        ...filters,
        dateFrom: this.toSafeISOString(filters.dateFrom),
        dateTo: this.toSafeISOString(filters.dateTo)
      },
      fetchPolicy: 'cache-first',
      errorPolicy: 'all'
    }).valueChanges.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => result.data?.searchMessages?.map(msg => ({
      ...msg,
      timestamp: this.safeDate(msg.timestamp),
      sender: this.normalizeUser(msg.sender)
    })) || []), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      console.error('Error searching messages:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Failed to search messages'));
    }));
  }
  // ✅ Batch normalization for better performance
  batchNormalizeMessages(messages) {
    if (!messages || messages.length === 0) return [];
    return messages.map(msg => {
      try {
        return this.normalizeMessage(msg);
      } catch (error) {
        console.error('Error normalizing message:', error);
        // Return minimal valid message on error
        return {
          id: msg.id || msg._id || `temp-${Date.now()}`,
          content: msg.content || '',
          type: msg.type || _models_message_model__WEBPACK_IMPORTED_MODULE_1__.MessageType.TEXT,
          timestamp: this.safeDate(msg.timestamp),
          isRead: false,
          sender: msg.sender ? this.normalizeUser(msg.sender) : {
            id: this.getCurrentUserId(),
            username: 'Unknown'
          }
        };
      }
    });
  }
  getUnreadMessages(userId) {
    return this.apollo.watchQuery({
      query: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.GET_UNREAD_MESSAGES_QUERY,
      variables: {
        userId
      },
      fetchPolicy: 'network-only'
    }).valueChanges.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => result.data?.getUnreadMessages?.map(msg => ({
      ...msg,
      timestamp: this.safeDate(msg.timestamp),
      sender: this.normalizeUser(msg.sender)
    })) || []), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      console.error('Error fetching unread messages:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Failed to fetch unread messages'));
    }));
  }
  setActiveConversation(conversationId) {
    this.activeConversation.next(conversationId);
  }
  getConversations() {
    return this.apollo.watchQuery({
      query: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.GET_CONVERSATIONS_QUERY,
      fetchPolicy: 'network-only'
    }).valueChanges.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      const conversations = result.data?.getConversations || [];
      return conversations.map(conv => this.normalizeConversation(conv));
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      console.error('Error fetching conversations:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Failed to load conversations'));
    }));
  }
  getConversation(conversationId, limit, page) {
    this.logger.info(`[MessageService] Getting conversation: ${conversationId}, limit: ${limit}, page: ${page}`);
    const variables = {
      conversationId
    };
    // Ajouter les paramètres de pagination s'ils sont fournis
    if (limit !== undefined) {
      variables.limit = limit;
    } else {
      variables.limit = 10; // Valeur par défaut
    }
    // Calculer l'offset à partir de la page si elle est fournie
    if (page !== undefined) {
      // La requête GraphQL utilise offset, donc nous devons convertir la page en offset
      const offset = (page - 1) * variables.limit;
      variables.offset = offset;
      this.logger.debug(`[MessageService] Calculated offset: ${offset} from page: ${page} and limit: ${variables.limit}`);
    } else {
      variables.offset = 0; // Valeur par défaut
    }

    this.logger.debug(`[MessageService] Final pagination parameters: limit=${variables.limit}, offset=${variables.offset}`);
    return this.apollo.watchQuery({
      query: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.GET_CONVERSATION_QUERY,
      variables: variables,
      fetchPolicy: 'network-only',
      errorPolicy: 'all'
    }).valueChanges.pipe((0,rxjs__WEBPACK_IMPORTED_MODULE_9__.retry)(2),
    // Réessayer 2 fois en cas d'erreur
    (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      this.logger.debug(`[MessageService] Conversation response received:`, result);
      const conv = result.data?.getConversation;
      if (!conv) {
        this.logger.error(`[MessageService] Conversation not found: ${conversationId}`);
        throw new Error('Conversation not found');
      }
      this.logger.debug(`[MessageService] Normalizing conversation: ${conversationId}`);
      const normalizedConversation = this.normalizeConversation(conv);
      this.logger.info(`[MessageService] Conversation loaded successfully: ${conversationId}, participants: ${normalizedConversation.participants?.length || 0}, messages: ${normalizedConversation.messages?.length || 0}`);
      return normalizedConversation;
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error(`[MessageService] Error fetching conversation:`, error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Failed to load conversation'));
    }));
  }
  createConversation(userId) {
    this.logger.info(`[MessageService] Creating conversation with user: ${userId}`);
    if (!userId) {
      this.logger.error(`[MessageService] Cannot create conversation: userId is undefined`);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('User ID is required to create a conversation'));
    }
    return this.apollo.mutate({
      mutation: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.CREATE_CONVERSATION_MUTATION,
      variables: {
        userId
      }
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      this.logger.debug(`[MessageService] Conversation creation response:`, result);
      const conversation = result.data?.createConversation;
      if (!conversation) {
        this.logger.error(`[MessageService] Failed to create conversation with user: ${userId}`);
        throw new Error('Failed to create conversation');
      }
      try {
        const normalizedConversation = this.normalizeConversation(conversation);
        this.logger.info(`[MessageService] Conversation created successfully: ${normalizedConversation.id}`);
        return normalizedConversation;
      } catch (error) {
        this.logger.error(`[MessageService] Error normalizing created conversation:`, error);
        throw new Error('Error processing created conversation');
      }
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error(`[MessageService] Error creating conversation with user ${userId}:`, error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error(`Failed to create conversation: ${error.message}`));
    }));
  }
  /**
   * Récupère une conversation existante ou en crée une nouvelle si elle n'existe pas
   * @param userId ID de l'utilisateur avec qui créer/récupérer une conversation
   * @returns Observable avec la conversation
   */
  getOrCreateConversation(userId) {
    this.logger.info(`[MessageService] Getting or creating conversation with user: ${userId}`);
    if (!userId) {
      this.logger.error(`[MessageService] Cannot get/create conversation: userId is undefined`);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('User ID is required to get/create a conversation'));
    }
    // D'abord, essayons de trouver une conversation existante entre les deux utilisateurs
    return this.getConversations().pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(conversations => {
      // Récupérer l'ID de l'utilisateur actuel
      const currentUserId = this.getCurrentUserId();
      // Chercher une conversation directe (non groupe) entre les deux utilisateurs
      const existingConversation = conversations.find(conv => {
        if (conv.isGroup) return false;
        // Vérifier si la conversation contient les deux utilisateurs
        const participantIds = conv.participants?.map(p => p.id || p._id) || [];
        return participantIds.includes(userId) && participantIds.includes(currentUserId);
      });
      if (existingConversation) {
        this.logger.info(`[MessageService] Found existing conversation: ${existingConversation.id}`);
        return existingConversation;
      }
      // Si aucune conversation n'est trouvée, en créer une nouvelle
      throw new Error('No existing conversation found');
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.info(`[MessageService] No existing conversation found, creating new one: ${error.message}`);
      return this.createConversation(userId);
    }));
  }
  getNotifications(refresh = false, page = 1, limit = 10) {
    this.logger.info('MessageService', `Fetching notifications, refresh: ${refresh}, page: ${page}, limit: ${limit}`);
    this.logger.debug('MessageService', 'Using query', {
      query: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.GET_NOTIFICATIONS_QUERY
    });
    // Si refresh est true, réinitialiser la pagination mais ne pas vider le cache
    // pour conserver les suppressions locales
    if (refresh) {
      this.logger.debug('MessageService', 'Resetting pagination due to refresh');
      this.notificationPagination.currentPage = 1;
      this.notificationPagination.hasMoreNotifications = true;
    }
    // Mettre à jour les paramètres de pagination
    this.notificationPagination.currentPage = page;
    this.notificationPagination.limit = limit;
    // Récupérer les IDs des notifications supprimées du localStorage
    const deletedNotificationIds = this.getDeletedNotificationIds();
    this.logger.debug('MessageService', `Found ${deletedNotificationIds.size} deleted notification IDs in localStorage`);
    return this.apollo.watchQuery({
      query: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.GET_NOTIFICATIONS_QUERY,
      variables: {
        page: page,
        limit: limit
      },
      fetchPolicy: refresh ? 'network-only' : 'cache-first'
    }).valueChanges.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      this.logger.debug('MessageService', 'Notifications response received');
      if (result.errors) {
        this.logger.error('MessageService', 'GraphQL errors:', result.errors);
        throw new Error(result.errors.map(e => e.message).join(', '));
      }
      const notifications = result.data?.getUserNotifications || [];
      this.logger.debug('MessageService', `Received ${notifications.length} notifications from server for page ${page}`);
      // Vérifier s'il y a plus de notifications à charger
      this.notificationPagination.hasMoreNotifications = notifications.length >= limit;
      if (notifications.length === 0) {
        this.logger.info('MessageService', 'No notifications received from server');
        this.notificationPagination.hasMoreNotifications = false;
      }
      // Filtrer les notifications supprimées
      const filteredNotifications = notifications.filter(notif => !deletedNotificationIds.has(notif.id));
      this.logger.debug('MessageService', `Filtered out ${notifications.length - filteredNotifications.length} deleted notifications`);
      // Afficher les notifications reçues pour le débogage
      filteredNotifications.forEach((notif, index) => {
        console.log(`Notification ${index + 1} (page ${page}):`, {
          id: notif.id || notif._id,
          type: notif.type,
          content: notif.content,
          isRead: notif.isRead
        });
      });
      // Vérifier si les notifications existent déjà dans le cache avant de les ajouter
      // Mettre à jour le cache avec les nouvelles notifications
      this.updateCache(filteredNotifications);
      // Récupérer toutes les notifications du cache et les TRIER
      const cachedNotifications = Array.from(this.notificationCache.values());
      // 🚀 TRI OPTIMISÉ: Les notifications les plus récentes en premier
      const sortedNotifications = this.sortNotificationsByDate(cachedNotifications);
      console.log(`📊 SORTED: ${sortedNotifications.length} notifications triées (plus récentes en premier)`);
      // Mettre à jour le BehaviorSubject avec les notifications TRIÉES
      this.notifications.next(sortedNotifications);
      // Mettre à jour le compteur de notifications non lues
      this.updateUnreadCount();
      // Sauvegarder les notifications dans le localStorage
      this.saveNotificationsToLocalStorage();
      return cachedNotifications;
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('MessageService', 'Error loading notifications:', error);
      if (error.graphQLErrors) {
        this.logger.error('MessageService', 'GraphQL errors:', error.graphQLErrors);
      }
      if (error.networkError) {
        this.logger.error('MessageService', 'Network error:', error.networkError);
      }
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Failed to load notifications'));
    }));
  }
  /**
   * Récupère les IDs des notifications supprimées du localStorage
   * @private
   * @returns Set contenant les IDs des notifications supprimées
   */
  getDeletedNotificationIds() {
    try {
      const deletedIds = new Set();
      const savedNotifications = localStorage.getItem('notifications');
      // Si aucune notification n'est sauvegardée, retourner un ensemble vide
      if (!savedNotifications) {
        return deletedIds;
      }
      // Récupérer les IDs des notifications sauvegardées
      const savedNotificationIds = new Set(JSON.parse(savedNotifications).map(n => n.id));
      // Récupérer les notifications du serveur (si disponibles dans le cache Apollo)
      const serverNotifications = this.apollo.client.readQuery({
        query: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.GET_NOTIFICATIONS_QUERY
      })?.getUserNotifications || [];
      // Pour chaque notification du serveur, vérifier si elle est dans les notifications sauvegardées
      serverNotifications.forEach(notification => {
        if (!savedNotificationIds.has(notification.id)) {
          deletedIds.add(notification.id);
        }
      });
      return deletedIds;
    } catch (error) {
      this.logger.error('MessageService', 'Erreur lors de la récupération des IDs de notifications supprimées:', error);
      return new Set();
    }
  }
  // Méthode pour vérifier s'il y a plus de notifications à charger
  hasMoreNotifications() {
    return this.notificationPagination.hasMoreNotifications;
  }
  // Méthode pour charger la page suivante de notifications
  loadMoreNotifications() {
    const nextPage = this.notificationPagination.currentPage + 1;
    return this.getNotifications(false, nextPage, this.notificationPagination.limit);
  }
  getNotificationById(id) {
    return this.notifications$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(notifications => notifications.find(n => n.id === id)), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('Error finding notification:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Failed to find notification'));
    }));
  }
  getNotificationCount() {
    return this.notifications.value?.length || 0;
  }
  getNotificationAttachments(notificationId) {
    return this.apollo.query({
      query: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.GET_NOTIFICATIONS_ATTACHAMENTS,
      variables: {
        id: notificationId
      },
      fetchPolicy: 'network-only'
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => result.data?.getNotificationAttachments || []), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('Error fetching notification attachments:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Failed to fetch attachments'));
    }));
  }
  getUnreadNotifications() {
    return this.notifications$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(notifications => notifications.filter(n => !n.isRead)));
  }
  /**
   * Supprime une notification
   * @param notificationId ID de la notification à supprimer
   * @returns Observable avec le résultat de l'opération
   */
  deleteNotification(notificationId) {
    this.logger.debug('MessageService', `Suppression de la notification ${notificationId}`);
    if (!notificationId) {
      this.logger.warn('MessageService', 'ID de notification invalide');
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('ID de notification invalide'));
    }
    // Supprimer localement d'abord pour une meilleure expérience utilisateur
    const removedCount = this.removeNotificationsFromCache([notificationId]);
    // Appeler le backend pour supprimer la notification
    return this.apollo.mutate({
      mutation: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.DELETE_NOTIFICATION_MUTATION,
      variables: {
        notificationId
      }
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      const response = result.data?.deleteNotification;
      if (!response) {
        throw new Error('Réponse de suppression invalide');
      }
      this.logger.debug('MessageService', 'Résultat de la suppression:', response);
      return response;
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => this.handleDeletionError(error, 'la suppression de la notification', {
      success: true,
      message: 'Notification supprimée localement (erreur serveur)'
    })));
  }
  /**
   * Sauvegarde les notifications dans le localStorage
   * @private
   */
  saveNotificationsToLocalStorage() {
    try {
      const notifications = Array.from(this.notificationCache.values());
      localStorage.setItem('notifications', JSON.stringify(notifications));
      this.logger.debug('MessageService', 'Notifications sauvegardées localement');
    } catch (error) {
      this.logger.error('MessageService', 'Erreur lors de la sauvegarde des notifications:', error);
    }
  }
  /**
   * Supprime toutes les notifications de l'utilisateur
   * @returns Observable avec le résultat de l'opération
   */
  deleteAllNotifications() {
    this.logger.debug('MessageService', 'Suppression de toutes les notifications');
    // Supprimer localement d'abord pour une meilleure expérience utilisateur
    const count = this.notificationCache.size;
    const allNotificationIds = Array.from(this.notificationCache.keys());
    this.removeNotificationsFromCache(allNotificationIds);
    // Appeler le backend pour supprimer toutes les notifications
    return this.apollo.mutate({
      mutation: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.DELETE_ALL_NOTIFICATIONS_MUTATION
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      const response = result.data?.deleteAllNotifications;
      if (!response) {
        throw new Error('Réponse de suppression invalide');
      }
      this.logger.debug('MessageService', 'Résultat de la suppression de toutes les notifications:', response);
      return response;
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => this.handleDeletionError(error, 'la suppression de toutes les notifications', {
      success: true,
      count,
      message: `${count} notifications supprimées localement (erreur serveur)`
    })));
  }
  /**
   * Supprime plusieurs notifications
   * @param notificationIds IDs des notifications à supprimer
   * @returns Observable avec le résultat de l'opération
   */
  deleteMultipleNotifications(notificationIds) {
    this.logger.debug('MessageService', `Suppression de ${notificationIds.length} notifications`);
    if (!notificationIds || notificationIds.length === 0) {
      this.logger.warn('MessageService', 'Aucun ID de notification fourni');
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Aucun ID de notification fourni'));
    }
    // Supprimer localement d'abord pour une meilleure expérience utilisateur
    const count = this.removeNotificationsFromCache(notificationIds);
    // Appeler le backend pour supprimer les notifications
    return this.apollo.mutate({
      mutation: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.DELETE_MULTIPLE_NOTIFICATIONS_MUTATION,
      variables: {
        notificationIds
      }
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      const response = result.data?.deleteMultipleNotifications;
      if (!response) {
        throw new Error('Réponse de suppression invalide');
      }
      this.logger.debug('MessageService', 'Résultat de la suppression multiple:', response);
      return response;
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => this.handleDeletionError(error, 'la suppression multiple de notifications', {
      success: count > 0,
      count,
      message: `${count} notifications supprimées localement (erreur serveur)`
    })));
  }
  groupNotificationsByType() {
    return this.notifications$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(notifications => {
      const groups = new Map();
      notifications.forEach(notif => {
        if (!groups.has(notif.type)) {
          groups.set(notif.type, []);
        }
        groups.get(notif.type)?.push(notif);
      });
      return groups;
    }));
  }
  markAsRead(notificationIds) {
    this.logger.debug('MessageService', `Marking notifications as read: ${notificationIds?.join(', ') || 'none'}`);
    if (!notificationIds || notificationIds.length === 0) {
      this.logger.warn('MessageService', 'No notification IDs provided');
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_7__.of)({
        success: false,
        readCount: 0,
        remainingCount: this.notificationCount.value
      });
    }
    // Vérifier que tous les IDs sont valides
    const validIds = notificationIds.filter(id => id && typeof id === 'string' && id.trim() !== '');
    if (validIds.length !== notificationIds.length) {
      this.logger.error('MessageService', 'Some notification IDs are invalid', {
        provided: notificationIds,
        valid: validIds
      });
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Some notification IDs are invalid'));
    }
    this.logger.debug('MessageService', 'Sending mutation to mark notifications as read', validIds);
    // Mettre à jour localement d'abord pour une meilleure expérience utilisateur
    this.updateNotificationStatus(validIds, true);
    // Créer une réponse optimiste
    const optimisticResponse = {
      markNotificationsAsRead: {
        success: true,
        readCount: validIds.length,
        remainingCount: Math.max(0, this.notificationCount.value - validIds.length)
      }
    };
    // Afficher des informations de débogage supplémentaires
    console.log('Sending markNotificationsAsRead mutation with variables:', {
      notificationIds: validIds
    });
    console.log('Using mutation:', _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.MARK_NOTIFICATION_READ_MUTATION);
    return this.apollo.mutate({
      mutation: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.MARK_NOTIFICATION_READ_MUTATION,
      variables: {
        notificationIds: validIds
      },
      optimisticResponse: optimisticResponse,
      errorPolicy: 'all',
      fetchPolicy: 'no-cache' // Ne pas utiliser le cache pour cette mutation
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      this.logger.debug('MessageService', 'Mutation result', result);
      console.log('Mutation result:', result);
      // Si nous avons des erreurs GraphQL, les logger mais continuer
      if (result.errors) {
        this.logger.error('MessageService', 'GraphQL errors:', result.errors);
        console.error('GraphQL errors:', result.errors);
      }
      // Utiliser la réponse du serveur ou notre réponse optimiste
      const response = result.data?.markNotificationsAsRead ?? optimisticResponse.markNotificationsAsRead;
      return response;
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('MessageService', 'Error marking notifications as read:', error);
      console.error('Error in markAsRead:', error);
      // En cas d'erreur, retourner quand même un succès simulé
      // puisque nous avons déjà mis à jour l'interface utilisateur
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_7__.of)({
        success: true,
        readCount: validIds.length,
        remainingCount: Math.max(0, this.notificationCount.value - validIds.length)
      });
    }));
  }
  // --------------------------------------------------------------------------
  // Section 3: Méthodes pour les Appels (SUPPRIMÉES - VOIR SECTION À LA FIN)
  // --------------------------------------------------------------------------
  /**
   * S'abonne aux signaux d'appel
   * @param callId ID de l'appel
   * @returns Observable avec les signaux d'appel
   */
  subscribeToCallSignals(callId) {
    return this.apollo.subscribe({
      query: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.CALL_SIGNAL_SUBSCRIPTION,
      variables: {
        callId
      }
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(({
      data
    }) => {
      if (!data?.callSignal) {
        throw new Error('No call signal received');
      }
      return data.callSignal;
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_10__.tap)(signal => {
      this.callSignals.next(signal);
      this.handleCallSignal(signal);
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('Error in call signal subscription', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Call signal subscription failed'));
    }));
  }
  /**
   * Envoie un signal d'appel
   * @param callId ID de l'appel
   * @param signalType Type de signal
   * @param signalData Données du signal
   * @returns Observable avec le résultat de l'opération
   */
  sendCallSignal(callId, signalType, signalData) {
    return this.apollo.mutate({
      mutation: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.SEND_CALL_SIGNAL_MUTATION,
      variables: {
        callId,
        signalType,
        signalData
      }
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      const success = result.data?.sendCallSignal;
      if (!success) {
        throw new Error('Failed to send call signal');
      }
      return success;
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('Error sending call signal', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Failed to send call signal'));
    }));
  }
  /**
   * Récupère l'historique des appels avec filtres
   * @param limit Nombre d'appels à récupérer
   * @param offset Décalage pour la pagination
   * @param status Filtres de statut
   * @param type Filtres de type
   * @param startDate Date de début
   * @param endDate Date de fin
   * @returns Observable avec l'historique des appels
   */
  getCallHistory(limit = 20, offset = 0, status, type, startDate, endDate) {
    return this.apollo.watchQuery({
      query: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.CALL_HISTORY_QUERY,
      variables: {
        limit,
        offset,
        status,
        type,
        startDate,
        endDate
      },
      fetchPolicy: 'network-only'
    }).valueChanges.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      const history = result.data?.callHistory || [];
      this.logger.debug(`Retrieved ${history.length} call history items`);
      return history;
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('Error fetching call history:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Failed to fetch call history'));
    }));
  }
  /**
   * Récupère les détails d'un appel spécifique
   * @param callId ID de l'appel
   * @returns Observable avec les détails de l'appel
   */
  getCallDetails(callId) {
    return this.apollo.watchQuery({
      query: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.CALL_DETAILS_QUERY,
      variables: {
        callId
      },
      fetchPolicy: 'network-only'
    }).valueChanges.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      const details = result.data?.callDetails;
      if (!details) {
        throw new Error('Call details not found');
      }
      this.logger.debug(`Retrieved call details for: ${callId}`);
      return details;
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('Error fetching call details:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Failed to fetch call details'));
    }));
  }
  /**
   * Récupère les statistiques d'appels
   * @returns Observable avec les statistiques d'appels
   */
  getCallStats() {
    return this.apollo.watchQuery({
      query: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.CALL_STATS_QUERY,
      fetchPolicy: 'network-only'
    }).valueChanges.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      const stats = result.data?.callStats;
      if (!stats) {
        throw new Error('Call stats not found');
      }
      this.logger.debug('Retrieved call stats:', stats);
      return stats;
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('Error fetching call stats:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Failed to fetch call stats'));
    }));
  }
  /**
   * Gère un signal d'appel reçu
   * @param signal Signal d'appel
   */
  handleCallSignal(signal) {
    switch (signal.type) {
      case 'ice-candidate':
        this.handleIceCandidate(signal);
        break;
      case 'answer':
        this.handleAnswer(signal);
        break;
      case 'end-call':
        this.handleEndCall(signal);
        break;
      case 'reject':
        this.handleRejectCall(signal);
        break;
      default:
        this.logger.debug(`Unhandled signal type: ${signal.type}`, signal);
    }
  }
  /**
   * Gère un candidat ICE reçu
   * @param signal Signal d'appel contenant un candidat ICE
   */
  handleIceCandidate(signal) {
    if (!this.peerConnection) {
      this.logger.error('No peer connection available for ICE candidate');
      return;
    }
    try {
      const candidate = JSON.parse(signal.data);
      this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate)).catch(error => {
        this.logger.error('Error adding ICE candidate', error);
      });
    } catch (error) {
      this.logger.error('Error parsing ICE candidate', error);
    }
  }
  /**
   * Gère une réponse SDP reçue
   * @param signal Signal d'appel contenant une réponse SDP
   */
  handleAnswer(signal) {
    if (!this.peerConnection) {
      this.logger.error('No peer connection available for answer');
      return;
    }
    try {
      const answer = JSON.parse(signal.data);
      this.peerConnection.setRemoteDescription(new RTCSessionDescription(answer)).catch(error => {
        this.logger.error('Error setting remote description', error);
      });
    } catch (error) {
      this.logger.error('Error parsing answer', error);
    }
  }
  /**
   * Gère la fin d'un appel
   * @param signal Signal d'appel indiquant la fin de l'appel
   */
  handleEndCall(signal) {
    this.stop('ringtone');
    this.cleanupCall();
    // Mettre à jour l'état de l'appel actif
    const currentCall = this.activeCall.value;
    if (currentCall && currentCall.id === signal.callId) {
      this.activeCall.next({
        ...currentCall,
        status: _models_message_model__WEBPACK_IMPORTED_MODULE_1__.CallStatus.ENDED,
        endTime: new Date().toISOString()
      });
    }
  }
  /**
   * Gère le rejet d'un appel
   * @param signal Signal d'appel indiquant le rejet de l'appel
   */
  handleRejectCall(signal) {
    this.stop('ringtone');
    this.cleanupCall();
    // Mettre à jour l'état de l'appel actif
    const currentCall = this.activeCall.value;
    if (currentCall && currentCall.id === signal.callId) {
      this.activeCall.next({
        ...currentCall,
        status: _models_message_model__WEBPACK_IMPORTED_MODULE_1__.CallStatus.REJECTED,
        endTime: new Date().toISOString()
      });
    }
  }
  /**
   * Nettoie les ressources d'appel
   */
  cleanupCall() {
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop());
      this.localStream = null;
      this.localStream$.next(null);
    }
    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }
    this.remoteStream = null;
    this.remoteStream$.next(null);
  }
  /**
   * Configure les périphériques média pour un appel
   * @param callType Type d'appel (audio, vidéo)
   * @returns Observable avec le flux média
   */
  setupMediaDevices(callType) {
    const constraints = {
      audio: true,
      video: callType !== _models_message_model__WEBPACK_IMPORTED_MODULE_1__.CallType.AUDIO ? {
        width: {
          ideal: 1280
        },
        height: {
          ideal: 720
        }
      } : false
    };
    return new rxjs__WEBPACK_IMPORTED_MODULE_11__.Observable(observer => {
      navigator.mediaDevices.getUserMedia(constraints).then(stream => {
        observer.next(stream);
        observer.complete();
      }).catch(error => {
        this.logger.error('Error accessing media devices', error);
        observer.error(new Error('Failed to access media devices'));
      });
    });
  }
  /**
   * Génère un ID d'appel unique
   * @returns ID d'appel unique
   */
  generateCallId() {
    return Date.now().toString() + Math.random().toString(36).substring(2, 9);
  }
  // --------------------------------------------------------------------------
  // Section 4: Méthodes pour les Utilisateurs/Groupes
  // --------------------------------------------------------------------------
  // User methods
  getAllUsers(forceRefresh = false, search, page = 1, limit = 10, sortBy = 'username', sortOrder = 'asc', isOnline) {
    this.logger.info('MessageService', `Getting users with params: forceRefresh=${forceRefresh}, search=${search || '(empty)'}, page=${page}, limit=${limit}, sortBy=${sortBy}, sortOrder=${sortOrder}, isOnline=${isOnline}`);
    const now = Date.now();
    const cacheValid = !forceRefresh && this.usersCache.length > 0 && now - this.lastFetchTime <= this.CACHE_DURATION && !search && page === 1 && limit >= this.usersCache.length;
    // Use cache only for first page with no filters
    if (cacheValid) {
      this.logger.debug('MessageService', `Using cached users (${this.usersCache.length} users)`);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_7__.of)([...this.usersCache]);
    }
    this.logger.debug('MessageService', `Fetching users from server with pagination, fetchPolicy=${forceRefresh ? 'network-only' : 'cache-first'}`);
    return this.apollo.watchQuery({
      query: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.GET_ALL_USER_QUERY,
      variables: {
        search,
        page,
        limit,
        sortBy,
        sortOrder,
        isOnline: isOnline !== undefined ? isOnline : null
      },
      fetchPolicy: forceRefresh ? 'network-only' : 'cache-first'
    }).valueChanges.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      this.logger.debug('MessageService', 'Users response received', result);
      if (result.errors) {
        this.logger.error('MessageService', 'GraphQL errors in getAllUsers:', result.errors);
        throw new Error(result.errors.map(e => e.message).join(', '));
      }
      if (!result.data?.getAllUsers) {
        this.logger.warn('MessageService', 'No users data received from server');
        return [];
      }
      const paginatedResponse = result.data.getAllUsers;
      // Log pagination metadata
      this.logger.debug('MessageService', 'Pagination metadata:', {
        totalCount: paginatedResponse.totalCount,
        totalPages: paginatedResponse.totalPages,
        currentPage: paginatedResponse.currentPage,
        hasNextPage: paginatedResponse.hasNextPage,
        hasPreviousPage: paginatedResponse.hasPreviousPage
      });
      // Normalize users with error handling
      const users = [];
      for (const user of paginatedResponse.users) {
        try {
          if (user) {
            users.push(this.normalizeUser(user));
          }
        } catch (error) {
          this.logger.warn('MessageService', `Error normalizing user, skipping:`, error);
        }
      }
      this.logger.info('MessageService', `Received ${users.length} users from server (page ${paginatedResponse.currentPage} of ${paginatedResponse.totalPages})`);
      // Update cache only for first page with no filters
      if (!search && page === 1 && !isOnline) {
        this.usersCache = [...users];
        this.lastFetchTime = Date.now();
        this.logger.debug('MessageService', `User cache updated with ${users.length} users`);
      }
      // Store pagination metadata in a property for component access
      this.currentUserPagination = {
        totalCount: paginatedResponse.totalCount,
        totalPages: paginatedResponse.totalPages,
        currentPage: paginatedResponse.currentPage,
        hasNextPage: paginatedResponse.hasNextPage,
        hasPreviousPage: paginatedResponse.hasPreviousPage
      };
      return users;
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('MessageService', 'Error fetching users:', error);
      if (error.graphQLErrors) {
        this.logger.error('MessageService', 'GraphQL errors:', error.graphQLErrors);
      }
      if (error.networkError) {
        this.logger.error('MessageService', 'Network error:', error.networkError);
      }
      // Return cache if available (only for first page)
      if (this.usersCache.length > 0 && page === 1 && !search && !isOnline) {
        this.logger.warn('MessageService', `Returning ${this.usersCache.length} cached users due to fetch error`);
        return (0,rxjs__WEBPACK_IMPORTED_MODULE_7__.of)([...this.usersCache]);
      }
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error(`Failed to fetch users: ${error.message || 'Unknown error'}`));
    }));
  }
  getOneUser(userId) {
    return this.apollo.watchQuery({
      query: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.GET_USER_QUERY,
      variables: {
        id: userId
      },
      fetchPolicy: 'network-only'
    }).valueChanges.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => this.normalizeUser(result.data?.getOneUser)), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('MessageService', 'Error fetching user:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Failed to fetch user'));
    }));
  }
  getCurrentUser() {
    return this.apollo.watchQuery({
      query: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.GET_CURRENT_USER_QUERY,
      fetchPolicy: 'network-only'
    }).valueChanges.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => this.normalizeUser(result.data?.getCurrentUser)), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('MessageService', 'Error fetching current user:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Failed to fetch current user'));
    }));
  }
  setUserOnline(userId) {
    return this.apollo.mutate({
      mutation: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.SET_USER_ONLINE_MUTATION,
      variables: {
        userId
      }
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      if (!result.data?.setUserOnline) throw new Error('Failed to set user online');
      return this.normalizeUser(result.data.setUserOnline);
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('MessageService', 'Error setting user online:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Failed to set user online'));
    }));
  }
  setUserOffline(userId) {
    return this.apollo.mutate({
      mutation: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.SET_USER_OFFLINE_MUTATION,
      variables: {
        userId
      }
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      if (!result.data?.setUserOffline) throw new Error('Failed to set user offline');
      return this.normalizeUser(result.data.setUserOffline);
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('MessageService', 'Error setting user offline:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Failed to set user offline'));
    }));
  }
  // --------------------------------------------------------------------------
  // Section: Gestion des Groupes
  // --------------------------------------------------------------------------
  /**
   * Crée un nouveau groupe
   */
  createGroup(name, participantIds, photo, description) {
    this.logger.debug('MessageService', `Creating group: ${name} with ${participantIds.length} participants`);
    if (!name || !participantIds || participantIds.length === 0) {
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Nom du groupe et participants requis'));
    }
    return this.apollo.mutate({
      mutation: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.CREATE_GROUP_MUTATION,
      variables: {
        name,
        participantIds,
        photo,
        description
      }
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      const group = result.data?.createGroup;
      if (!group) {
        throw new Error('Échec de la création du groupe');
      }
      this.logger.info('MessageService', `Group created successfully: ${group.id}`);
      return group;
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('MessageService', 'Error creating group:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Échec de la création du groupe'));
    }));
  }
  /**
   * Met à jour un groupe existant
   */
  updateGroup(groupId, input) {
    this.logger.debug('MessageService', `Updating group: ${groupId}`);
    if (!groupId) {
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('ID du groupe requis'));
    }
    return this.apollo.mutate({
      mutation: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.UPDATE_GROUP_MUTATION,
      variables: {
        id: groupId,
        input
      }
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      const group = result.data?.updateGroup;
      if (!group) {
        throw new Error('Échec de la mise à jour du groupe');
      }
      this.logger.info('MessageService', `Group updated successfully: ${group.id}`);
      return group;
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('MessageService', 'Error updating group:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Échec de la mise à jour du groupe'));
    }));
  }
  /**
   * Supprime un groupe
   */
  deleteGroup(groupId) {
    this.logger.debug('MessageService', `Deleting group: ${groupId}`);
    if (!groupId) {
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('ID du groupe requis'));
    }
    return this.apollo.mutate({
      mutation: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.DELETE_GROUP_MUTATION,
      variables: {
        id: groupId
      }
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      const response = result.data?.deleteGroup;
      if (!response) {
        throw new Error('Échec de la suppression du groupe');
      }
      this.logger.info('MessageService', `Group deleted successfully: ${groupId}`);
      return response;
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('MessageService', 'Error deleting group:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Échec de la suppression du groupe'));
    }));
  }
  /**
   * Quitte un groupe
   */
  leaveGroup(groupId) {
    this.logger.debug('MessageService', `Leaving group: ${groupId}`);
    if (!groupId) {
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('ID du groupe requis'));
    }
    return this.apollo.mutate({
      mutation: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.LEAVE_GROUP_MUTATION,
      variables: {
        groupId
      }
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      const response = result.data?.leaveGroup;
      if (!response) {
        throw new Error('Échec de la sortie du groupe');
      }
      this.logger.info('MessageService', `Left group successfully: ${groupId}`);
      return response;
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('MessageService', 'Error leaving group:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Échec de la sortie du groupe'));
    }));
  }
  /**
   * Récupère les informations d'un groupe
   */
  getGroup(groupId) {
    this.logger.debug('MessageService', `Getting group: ${groupId}`);
    if (!groupId) {
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('ID du groupe requis'));
    }
    return this.apollo.query({
      query: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.GET_GROUP_QUERY,
      variables: {
        id: groupId
      },
      fetchPolicy: 'network-only'
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      const group = result.data?.getGroup;
      if (!group) {
        throw new Error('Groupe non trouvé');
      }
      this.logger.info('MessageService', `Group retrieved successfully: ${groupId}`);
      return group;
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('MessageService', 'Error getting group:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Échec de la récupération du groupe'));
    }));
  }
  /**
   * Récupère les groupes d'un utilisateur
   */
  getUserGroups(userId) {
    this.logger.debug('MessageService', `Getting groups for user: ${userId}`);
    if (!userId) {
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error("ID de l'utilisateur requis"));
    }
    return this.apollo.query({
      query: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.GET_USER_GROUPS_QUERY,
      variables: {
        userId
      },
      fetchPolicy: 'network-only'
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      const groups = result.data?.getUserGroups || [];
      this.logger.info('MessageService', `Retrieved ${groups.length} groups for user: ${userId}`);
      return groups;
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('MessageService', 'Error getting user groups:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Échec de la récupération des groupes'));
    }));
  }
  subscribeToNewMessages(conversationId) {
    if (!conversationId) {
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Conversation ID is required'));
    }
    // ✅ Use cached subscription if available
    const cacheKey = `messages_${conversationId}`;
    if (this.subscriptionCache.has(cacheKey)) {
      const refCount = this.subscriptionRefCount.get(cacheKey) || 0;
      this.subscriptionRefCount.set(cacheKey, refCount + 1);
      return this.subscriptionCache.get(cacheKey);
    }
    // ✅ Quick token validation without verbose logging
    if (!this.isTokenValid()) {
      return rxjs__WEBPACK_IMPORTED_MODULE_12__.EMPTY;
    }
    // ✅ Reduced logging for better performance
    if (!_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.production) {
      console.log(`🚀 Setting up subscription: ${conversationId}`);
    }
    // ✅ Create optimized subscription with caching and shareReplay
    const sub$ = this.apollo.subscribe({
      query: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.MESSAGE_SENT_SUBSCRIPTION,
      variables: {
        conversationId
      },
      errorPolicy: 'all' // Handle partial errors gracefully
    }).pipe(
    // ✅ Debounce rapid messages to prevent UI flooding
    (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_13__.debounceTime)(10), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      const msg = result.data?.messageSent;
      if (!msg) {
        throw new Error('No message payload received');
      }
      // ✅ Reduced logging for better performance
      if (!_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.production) {
        console.log('⚡ New message via WebSocket:', msg.id);
      }
      // Vérifier que l'ID est présent
      if (!msg.id && !msg._id) {
        this.logger.warn('⚠️ Message without ID received, generating temp ID');
        msg.id = `temp-${Date.now()}`;
      }
      try {
        // NORMALISATION RAPIDE du message
        const normalizedMessage = this.normalizeMessage(msg);
        this.logger.debug('✅ INSTANT: Message normalized successfully', normalizedMessage);
        // TRAITEMENT INSTANTANÉ selon le type
        if (normalizedMessage.type === _models_message_model__WEBPACK_IMPORTED_MODULE_1__.MessageType.AUDIO || normalizedMessage.type === _models_message_model__WEBPACK_IMPORTED_MODULE_1__.MessageType.VOICE_MESSAGE || normalizedMessage.attachments && normalizedMessage.attachments.some(att => att.type === 'AUDIO')) {
          this.logger.debug('🎤 INSTANT: Voice message received in real-time');
        }
        // MISE À JOUR IMMÉDIATE de l'UI
        this.zone.run(() => {
          this.logger.debug('📡 INSTANT: Updating conversation UI immediately');
          this.updateConversationWithNewMessage(conversationId, normalizedMessage);
        });
        return normalizedMessage;
      } catch (err) {
        this.logger.error('❌ Error normalizing message:', err);
        // Créer un message minimal mais valide pour éviter les erreurs
        const minimalMessage = {
          id: msg.id || msg._id || `temp-${Date.now()}`,
          content: msg.content || '',
          type: msg.type || _models_message_model__WEBPACK_IMPORTED_MODULE_1__.MessageType.TEXT,
          timestamp: this.safeDate(msg.timestamp),
          isRead: false,
          sender: msg.sender ? this.normalizeUser(msg.sender) : {
            id: this.getCurrentUserId(),
            username: 'Unknown'
          }
        };
        this.logger.debug('🔧 FALLBACK: Created minimal message', minimalMessage);
        return minimalMessage;
      }
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      console.error('Message subscription error:', error);
      return rxjs__WEBPACK_IMPORTED_MODULE_12__.EMPTY;
    }),
    // ✅ Filter null values and deduplicate messages
    (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_14__.filter)(message => !!message), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_15__.distinctUntilChanged)((prev, curr) => prev?.id === curr?.id),
    // ✅ Cache subscription with shareReplay for performance
    (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_16__.shareReplay)({
      bufferSize: 1,
      refCount: true
    }),
    // ✅ Retry with exponential backoff
    (0,rxjs__WEBPACK_IMPORTED_MODULE_9__.retry)(3));
    // ✅ Cache the subscription for reuse
    this.subscriptionCache.set(cacheKey, sub$);
    this.subscriptionRefCount.set(cacheKey, 1);
    // ✅ Optimized subscription observer with minimal logging
    const sub = sub$.subscribe({
      next: message => {
        if (!_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.production) {
          console.log(`✅ Message received:`, message.id);
        }
        // ✅ Update conversation immediately
        this.updateConversationWithNewMessage(conversationId, message);
      },
      error: err => {
        console.error('Subscription error:', err);
        // ✅ Clean up cache on error
        this.subscriptionCache.delete(cacheKey);
        this.subscriptionRefCount.delete(cacheKey);
      },
      complete: () => {
        // ✅ Clean up cache on completion
        this.subscriptionCache.delete(cacheKey);
        this.subscriptionRefCount.delete(cacheKey);
      }
    });
    this.subscriptions.push(sub);
    return sub$;
  }
  /**
   * Met à jour une conversation avec un nouveau message INSTANTANÉMENT
   * @param conversationId ID de la conversation
   * @param message Nouveau message
   */
  updateConversationWithNewMessage(conversationId, message) {
    this.logger.debug(`⚡ INSTANT: Updating conversation ${conversationId} with new message ${message.id}`);
    // MISE À JOUR IMMÉDIATE sans attendre la requête
    this.zone.run(() => {
      // Émettre IMMÉDIATEMENT l'événement de conversation active
      this.activeConversation.next(conversationId);
      this.logger.debug('📡 INSTANT: Conversation event emitted immediately');
    });
    // Mise à jour en arrière-plan (non-bloquante)
    setTimeout(() => {
      this.getConversation(conversationId).subscribe({
        next: conversation => {
          this.logger.debug(`✅ BACKGROUND: Conversation ${conversationId} refreshed with ${conversation?.messages?.length || 0} messages`);
        },
        error: error => {
          this.logger.error(`⚠️ BACKGROUND: Error refreshing conversation ${conversationId}:`, error);
        }
      });
    }, 0); // Exécution asynchrone immédiate
  }
  /**
   * Rafraîchit les notifications du sender après envoi d'un message
   */
  refreshSenderNotifications() {
    console.log('🔄 SENDER: Refreshing notifications after message sent');
    // Recharger les notifications en arrière-plan
    this.getNotifications(true).subscribe({
      next: notifications => {
        console.log('🔄 SENDER: Notifications refreshed successfully', notifications.length);
      },
      error: error => {
        console.error('🔄 SENDER: Error refreshing notifications:', error);
      }
    });
  }
  subscribeToUserStatus() {
    // Vérifier si l'utilisateur est connecté avec un token valide
    if (!this.isTokenValid()) {
      this.logger.warn("Tentative d'abonnement au statut utilisateur avec un token invalide ou expiré");
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Invalid or expired token'));
    }
    this.logger.debug("Démarrage de l'abonnement au statut utilisateur");
    const sub$ = this.apollo.subscribe({
      query: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.USER_STATUS_SUBSCRIPTION
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_10__.tap)(result => this.logger.debug("Données reçues de l'abonnement au statut utilisateur:", result)), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      const user = result.data?.userStatusChanged;
      if (!user) {
        this.logger.error('No status payload received');
        throw new Error('No status payload received');
      }
      return this.normalizeUser(user);
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('Status subscription error:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Status subscription failed'));
    }), (0,rxjs__WEBPACK_IMPORTED_MODULE_9__.retry)(3) // Réessayer 3 fois en cas d'erreur
    );

    const sub = sub$.subscribe();
    this.subscriptions.push(sub);
    return sub$;
  }
  subscribeToConversationUpdates(conversationId) {
    const sub$ = this.apollo.subscribe({
      query: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.CONVERSATION_UPDATED_SUBSCRIPTION,
      variables: {
        conversationId
      }
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      const conv = result.data?.conversationUpdated;
      if (!conv) throw new Error('No conversation payload received');
      const normalizedConversation = {
        ...conv,
        participants: conv.participants?.map(p => this.normalizeUser(p)) || [],
        lastMessage: conv.lastMessage ? {
          ...conv.lastMessage,
          sender: this.normalizeUser(conv.lastMessage.sender),
          timestamp: this.safeDate(conv.lastMessage.timestamp),
          readAt: conv.lastMessage.readAt ? this.safeDate(conv.lastMessage.readAt) : undefined,
          // Conservez toutes les autres propriétés du message
          id: conv.lastMessage.id,
          content: conv.lastMessage.content,
          type: conv.lastMessage.type,
          isRead: conv.lastMessage.isRead
          // ... autres propriétés nécessaires
        } : null // On conserve null comme dans votre version originale
      };

      return normalizedConversation; // Assertion de type si nécessaire
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('MessageService', 'Conversation subscription error:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Conversation subscription failed'));
    }));
    const sub = sub$.subscribe();
    this.subscriptions.push(sub);
    return sub$;
  }
  subscribeToTypingIndicator(conversationId) {
    const sub$ = this.apollo.subscribe({
      query: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.TYPING_INDICATOR_SUBSCRIPTION,
      variables: {
        conversationId
      }
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => result.data?.typingIndicator), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_14__.filter)(Boolean), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('MessageService', 'Typing indicator subscription error:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Typing indicator subscription failed'));
    }));
    const sub = sub$.subscribe();
    this.subscriptions.push(sub);
    return sub$;
  }
  isTokenValid() {
    const token = localStorage.getItem('token');
    if (!token) {
      this.logger.warn('Aucun token trouvé');
      return false;
    }
    try {
      // Décoder le token JWT (format: header.payload.signature)
      const parts = token.split('.');
      if (parts.length !== 3) {
        this.logger.warn('Format de token invalide');
        return false;
      }
      // Décoder le payload (deuxième partie du token)
      const payload = JSON.parse(atob(parts[1]));
      // Vérifier l'expiration
      if (!payload.exp) {
        this.logger.warn("Token sans date d'expiration");
        return false;
      }
      const expirationDate = new Date(payload.exp * 1000);
      const now = new Date();
      if (expirationDate < now) {
        this.logger.warn('Token expiré', {
          expiration: expirationDate.toISOString(),
          now: now.toISOString()
        });
        return false;
      }
      return true;
    } catch (error) {
      this.logger.error('Erreur lors de la vérification du token:', error);
      return false;
    }
  }
  subscribeToNotificationsRead() {
    // Vérifier si l'utilisateur est connecté avec un token valide
    if (!this.isTokenValid()) {
      this.logger.warn("Tentative d'abonnement aux notifications avec un token invalide ou expiré");
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_7__.of)([]);
    }
    this.logger.debug("Démarrage de l'abonnement aux notifications lues");
    const sub$ = this.apollo.subscribe({
      query: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.NOTIFICATIONS_READ_SUBSCRIPTION
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_10__.tap)(result => this.logger.debug("Données reçues de l'abonnement aux notifications lues:", result)), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      const notificationIds = result.data?.notificationsRead || [];
      this.logger.debug('Notifications marquées comme lues:', notificationIds);
      this.updateNotificationStatus(notificationIds, true);
      return notificationIds;
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(err => {
      this.logger.error('Notifications read subscription error:', err);
      // Retourner un tableau vide au lieu de propager l'erreur
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_7__.of)([]);
    }),
    // Réessayer après un délai en cas d'erreur
    (0,rxjs__WEBPACK_IMPORTED_MODULE_9__.retry)(3) // Réessayer 3 fois en cas d'erreur
    );

    const sub = sub$.subscribe();
    this.subscriptions.push(sub);
    return sub$;
  }
  subscribeToNewNotifications() {
    // Vérifier si l'utilisateur est connecté
    const token = localStorage.getItem('token');
    if (!token) {
      this.logger.warn("Tentative d'abonnement aux notifications sans être connecté");
      return rxjs__WEBPACK_IMPORTED_MODULE_12__.EMPTY;
    }
    this.logger.debug('🚀 INSTANT NOTIFICATION: Setting up real-time subscription');
    const source$ = this.apollo.subscribe({
      query: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.NOTIFICATION_SUBSCRIPTION
    });
    const processed$ = source$.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      const notification = result.data?.notificationReceived;
      if (!notification) {
        throw new Error('No notification payload received');
      }
      this.logger.debug('⚡ INSTANT: New notification received', notification);
      const normalized = this.normalizeNotification(notification);
      // Vérification rapide du cache
      if (this.notificationCache.has(normalized.id)) {
        this.logger.debug(`🔄 Notification ${normalized.id} already in cache, skipping`);
        throw new Error('Notification already exists in cache');
      }
      // TRAITEMENT INSTANTANÉ
      this.logger.debug('📡 INSTANT: Processing notification immediately');
      // Vérifier si la notification existe déjà pour éviter les doublons
      const currentNotifications = this.notifications.value;
      const existingNotification = currentNotifications.find(n => n.id === normalized.id);
      if (existingNotification) {
        this.logger.debug('🔄 DUPLICATE: Notification already exists, skipping:', normalized.id);
        return normalized;
      }
      // Son de notification IMMÉDIAT
      this.playNotificationSound();
      // Mise à jour INSTANTANÉE du cache
      this.updateNotificationCache(normalized);
      // Émettre IMMÉDIATEMENT la notification EN PREMIER
      this.zone.run(() => {
        // 🚀 INSERTION EN PREMIER: Nouvelle notification en tête de liste
        const updatedNotifications = [normalized, ...currentNotifications];
        this.logger.debug(`⚡ INSTANT: Nouvelle notification ajoutée en PREMIER (${updatedNotifications.length} total)`);
        this.notifications.next(updatedNotifications);
        this.notificationCount.next(this.notificationCount.value + 1);
      });
      this.logger.debug('✅ INSTANT: Notification processed and emitted', normalized);
      return normalized;
    }),
    // Gestion d'erreurs optimisée
    (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(err => {
      if (err instanceof Error && err.message === 'Notification already exists in cache') {
        return rxjs__WEBPACK_IMPORTED_MODULE_12__.EMPTY;
      }
      this.logger.error('❌ Notification subscription error:', err);
      return rxjs__WEBPACK_IMPORTED_MODULE_12__.EMPTY;
    }),
    // Optimisation: traitement en temps réel
    (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_10__.tap)(notification => {
      this.logger.debug('⚡ INSTANT: Notification ready for UI update', notification);
    }));
    const sub = processed$.subscribe({
      next: notification => {
        this.logger.debug('✅ INSTANT: Notification delivered to UI', notification);
      },
      error: error => {
        this.logger.error('❌ CRITICAL: Notification subscription error', error);
      }
    });
    this.subscriptions.push(sub);
    this.logger.debug('🔗 INSTANT: Notification subscription established');
    return processed$;
  }
  // --------------------------------------------------------------------------
  // Helpers et Utilitaires
  // --------------------------------------------------------------------------
  startCleanupInterval() {
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredNotifications();
    }, 3600000);
  }
  cleanupExpiredNotifications() {
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    let expiredCount = 0;
    this.notificationCache.forEach((notification, id) => {
      const notificationDate = new Date(notification.timestamp);
      if (notificationDate < thirtyDaysAgo) {
        this.notificationCache.delete(id);
        expiredCount++;
      }
    });
    if (expiredCount > 0) {
      this.logger.debug(`Cleaned up ${expiredCount} expired notifications`);
      // 🚀 TRI OPTIMISÉ: Maintenir l'ordre après nettoyage
      const remainingNotifications = Array.from(this.notificationCache.values());
      const sortedNotifications = this.sortNotificationsByDate(remainingNotifications);
      this.notifications.next(sortedNotifications);
      this.updateUnreadCount();
    }
  }
  /**
   * Trie les notifications par date (plus récentes en premier)
   * @param notifications Array de notifications à trier
   * @returns Array de notifications triées
   */
  sortNotificationsByDate(notifications) {
    return notifications.sort((a, b) => {
      // Utiliser timestamp ou une date par défaut si manquant
      const dateA = new Date(a.timestamp || 0);
      const dateB = new Date(b.timestamp || 0);
      return dateB.getTime() - dateA.getTime(); // Ordre décroissant (plus récent en premier)
    });
  }

  getCurrentUserId() {
    return localStorage.getItem('userId') || '';
  }
  normalizeMessage(message) {
    if (!message) {
      this.logger.error('[MessageService] Cannot normalize null or undefined message');
      throw new Error('Message object is required');
    }
    try {
      // Vérification des champs obligatoires
      if (!message.id && !message._id) {
        this.logger.error('[MessageService] Message ID is missing', undefined, message);
        throw new Error('Message ID is required');
      }
      // Normaliser le sender avec gestion d'erreur
      let normalizedSender;
      try {
        normalizedSender = message.sender ? this.normalizeUser(message.sender) : undefined;
      } catch (error) {
        this.logger.warn('[MessageService] Error normalizing message sender, using default values', error);
        normalizedSender = {
          _id: message.senderId || 'unknown',
          id: message.senderId || 'unknown',
          username: 'Unknown User',
          email: '<EMAIL>',
          role: 'user',
          isActive: true
        };
      }
      // Normaliser le receiver si présent
      let normalizedReceiver;
      if (message.receiver) {
        try {
          normalizedReceiver = this.normalizeUser(message.receiver);
        } catch (error) {
          this.logger.warn('[MessageService] Error normalizing message receiver, using default values', error);
          normalizedReceiver = {
            _id: message.receiverId || 'unknown',
            id: message.receiverId || 'unknown',
            username: 'Unknown User',
            email: '<EMAIL>',
            role: 'user',
            isActive: true
          };
        }
      }
      // Normaliser les pièces jointes si présentes
      const normalizedAttachments = message.attachments?.map(att => ({
        id: att.id || att._id || `attachment-${Date.now()}`,
        url: att.url || '',
        type: att.type || 'unknown',
        name: att.name || 'attachment',
        size: att.size || 0,
        duration: att.duration || 0
      })) || [];
      // Construire le message normalisé
      const normalizedMessage = {
        ...message,
        _id: message.id || message._id,
        id: message.id || message._id,
        content: message.content || '',
        sender: normalizedSender,
        timestamp: this.normalizeDate(message.timestamp),
        readAt: message.readAt ? this.normalizeDate(message.readAt) : undefined,
        attachments: normalizedAttachments,
        metadata: message.metadata || null
      };
      // Ajouter le receiver seulement s'il existe
      if (normalizedReceiver) {
        normalizedMessage.receiver = normalizedReceiver;
      }
      this.logger.debug('[MessageService] Message normalized successfully', {
        messageId: normalizedMessage.id,
        senderId: normalizedMessage.sender?.id
      });
      return normalizedMessage;
    } catch (error) {
      this.logger.error('[MessageService] Error normalizing message:', error instanceof Error ? error : new Error(String(error)), message);
      throw new Error(`Failed to normalize message: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
  normalizeUser(user) {
    if (!user) {
      throw new Error('User object is required');
    }
    // Vérification des champs obligatoires avec valeurs par défaut
    const userId = user.id || user._id;
    if (!userId) {
      throw new Error('User ID is required');
    }
    // Utiliser des valeurs par défaut pour les champs manquants
    const username = user.username || 'Unknown User';
    const email = user.email || `user-${userId}@example.com`;
    const isActive = user.isActive !== undefined && user.isActive !== null ? user.isActive : true;
    const role = user.role || 'user';
    // Construire l'objet utilisateur normalisé
    return {
      _id: userId,
      id: userId,
      username: username,
      email: email,
      role: role,
      isActive: isActive,
      // Champs optionnels
      image: user.image ?? null,
      bio: user.bio,
      isOnline: user.isOnline || false,
      lastActive: user.lastActive ? new Date(user.lastActive) : undefined,
      createdAt: user.createdAt ? new Date(user.createdAt) : undefined,
      updatedAt: user.updatedAt ? new Date(user.updatedAt) : undefined,
      followingCount: user.followingCount,
      followersCount: user.followersCount,
      postCount: user.postCount
    };
  }
  normalizeConversation(conv) {
    if (!conv) {
      this.logger.error('[MessageService] Cannot normalize null or undefined conversation');
      throw new Error('Conversation object is required');
    }
    try {
      // Vérification des champs obligatoires
      if (!conv.id && !conv._id) {
        this.logger.error('[MessageService] Conversation ID is missing', undefined, conv);
        throw new Error('Conversation ID is required');
      }
      // Normaliser les participants avec gestion d'erreur
      const normalizedParticipants = [];
      if (conv.participants && Array.isArray(conv.participants)) {
        for (const participant of conv.participants) {
          try {
            if (participant) {
              normalizedParticipants.push(this.normalizeUser(participant));
            }
          } catch (error) {
            this.logger.warn('[MessageService] Error normalizing participant, skipping', error);
          }
        }
      } else {
        this.logger.warn('[MessageService] Conversation has no participants or invalid participants array', conv);
      }
      // Normaliser les messages avec gestion d'erreur
      const normalizedMessages = [];
      if (conv.messages && Array.isArray(conv.messages)) {
        this.logger.debug('[MessageService] Processing conversation messages', {
          count: conv.messages.length
        });
        for (const message of conv.messages) {
          try {
            if (message) {
              const normalizedMessage = this.normalizeMessage(message);
              this.logger.debug('[MessageService] Successfully normalized message', {
                messageId: normalizedMessage.id,
                content: normalizedMessage.content?.substring(0, 20),
                sender: normalizedMessage.sender?.username
              });
              normalizedMessages.push(normalizedMessage);
            }
          } catch (error) {
            this.logger.warn('[MessageService] Error normalizing message in conversation, skipping', error);
          }
        }
      } else {
        this.logger.debug('[MessageService] No messages found in conversation or invalid messages array');
      }
      // Normaliser le dernier message avec gestion d'erreur
      let normalizedLastMessage = null;
      if (conv.lastMessage) {
        try {
          normalizedLastMessage = this.normalizeMessage(conv.lastMessage);
        } catch (error) {
          this.logger.warn('[MessageService] Error normalizing last message, using null', error);
        }
      }
      // Construire la conversation normalisée
      const normalizedConversation = {
        ...conv,
        _id: conv.id || conv._id,
        id: conv.id || conv._id,
        participants: normalizedParticipants,
        messages: normalizedMessages,
        lastMessage: normalizedLastMessage,
        unreadCount: conv.unreadCount || 0,
        isGroup: !!conv.isGroup,
        createdAt: this.normalizeDate(conv.createdAt),
        updatedAt: this.normalizeDate(conv.updatedAt)
      };
      this.logger.debug('[MessageService] Conversation normalized successfully', {
        conversationId: normalizedConversation.id,
        participantCount: normalizedParticipants.length,
        messageCount: normalizedMessages.length
      });
      return normalizedConversation;
    } catch (error) {
      this.logger.error('[MessageService] Error normalizing conversation:', error instanceof Error ? error : new Error(String(error)), conv);
      throw new Error(`Failed to normalize conversation: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
  normalizeDate(date) {
    if (!date) return new Date();
    try {
      return typeof date === 'string' ? new Date(date) : date;
    } catch (error) {
      this.logger.warn(`Failed to parse date: ${date}`, error);
      return new Date();
    }
  }
  // Méthode sécurisée pour créer une date à partir d'une valeur potentiellement undefined
  safeDate(date) {
    if (!date) return new Date();
    try {
      return typeof date === 'string' ? new Date(date) : date;
    } catch (error) {
      this.logger.warn(`Failed to create safe date: ${date}`, error);
      return new Date();
    }
  }
  normalizeNotification(notification) {
    this.logger.debug('MessageService', 'Normalizing notification', notification);
    if (!notification) {
      this.logger.error('MessageService', 'Notification is null or undefined');
      throw new Error('Notification is required');
    }
    // Vérifier et normaliser l'ID
    const notificationId = notification.id || notification._id;
    if (!notificationId) {
      this.logger.error('MessageService', 'Notification ID is missing', notification);
      throw new Error('Notification ID is required');
    }
    if (!notification.timestamp) {
      this.logger.warn('MessageService', 'Notification timestamp is missing, using current time', notification);
      notification.timestamp = new Date();
    }
    try {
      const normalized = {
        ...notification,
        _id: notificationId,
        id: notificationId,
        timestamp: new Date(notification.timestamp),
        ...(notification.senderId && {
          senderId: this.normalizeSender(notification.senderId)
        }),
        ...(notification.message && {
          message: this.normalizeNotMessage(notification.message)
        })
      };
      this.logger.debug('MessageService', 'Normalized notification result', normalized);
      return normalized;
    } catch (error) {
      this.logger.error('MessageService', 'Error in normalizeNotification', error);
      throw error;
    }
  }
  normalizeSender(sender) {
    return {
      id: sender.id,
      username: sender.username,
      ...(sender.image && {
        image: sender.image
      })
    };
  }
  /**
   * Normalise un message de notification
   * @param message Message à normaliser
   * @returns Message normalisé
   */
  normalizeNotMessage(message) {
    if (!message) return null;
    return {
      id: message.id || message._id,
      content: message.content || '',
      type: message.type || 'TEXT',
      timestamp: this.safeDate(message.timestamp),
      attachments: message.attachments || [],
      ...(message.sender && {
        sender: this.normalizeSender(message.sender)
      })
    };
  }
  /**
   * Met à jour le cache de notifications avec une ou plusieurs notifications
   * @param notifications Notification(s) à ajouter au cache
   * @param skipDuplicates Si true, ignore les notifications déjà présentes dans le cache
   */
  updateCache(notifications, skipDuplicates = true) {
    const notificationArray = Array.isArray(notifications) ? notifications : [notifications];
    this.logger.debug('MessageService', `Updating notification cache with ${notificationArray.length} notifications`);
    if (notificationArray.length === 0) {
      this.logger.warn('MessageService', 'No notifications to update in cache');
      return;
    }
    // Vérifier si les notifications ont des IDs valides
    const validNotifications = notificationArray.filter(notif => notif && (notif.id || notif._id));
    if (validNotifications.length !== notificationArray.length) {
      this.logger.warn('MessageService', `Found ${notificationArray.length - validNotifications.length} notifications without valid IDs`);
    }
    let addedCount = 0;
    let skippedCount = 0;
    // Traiter chaque notification
    validNotifications.forEach((notif, index) => {
      try {
        // S'assurer que la notification a un ID
        const notifId = notif.id || notif._id;
        if (!notifId) {
          this.logger.error('MessageService', 'Notification without ID:', notif);
          return;
        }
        // Normaliser la notification
        const normalized = this.normalizeNotification(notif);
        // Vérifier si cette notification existe déjà dans le cache
        if (skipDuplicates && this.notificationCache.has(normalized.id)) {
          this.logger.debug('MessageService', `Notification ${normalized.id} already exists in cache, skipping`);
          skippedCount++;
          return;
        }
        // Ajouter au cache
        this.notificationCache.set(normalized.id, normalized);
        addedCount++;
        this.logger.debug('MessageService', `Added notification ${normalized.id} to cache`);
      } catch (error) {
        this.logger.error('MessageService', `Error processing notification ${index + 1}:`, error);
      }
    });
    this.logger.debug('MessageService', `Cache update complete: ${addedCount} added, ${skippedCount} skipped, total: ${this.notificationCache.size}`);
    // Mettre à jour les observables et sauvegarder
    this.refreshNotificationObservables();
  }
  /**
   * Met à jour les observables de notifications et sauvegarde dans le localStorage
   * OPTIMISÉ: Trie les notifications par date (plus récentes en premier)
   */
  refreshNotificationObservables() {
    const allNotifications = Array.from(this.notificationCache.values());
    // 🚀 TRI OPTIMISÉ: Les notifications les plus récentes en premier
    const sortedNotifications = this.sortNotificationsByDate(allNotifications);
    this.logger.debug(`📊 SORTED: ${sortedNotifications.length} notifications triées par date (plus récentes en premier)`);
    this.notifications.next(sortedNotifications);
    this.updateUnreadCount();
    this.saveNotificationsToLocalStorage();
  }
  /**
   * Met à jour le compteur de notifications non lues
   */
  updateUnreadCount() {
    const allNotifications = Array.from(this.notificationCache.values());
    const unreadNotifications = allNotifications.filter(n => !n.isRead);
    const count = unreadNotifications.length;
    // Forcer la mise à jour dans la zone Angular
    this.zone.run(() => {
      this.notificationCount.next(count);
      // Émettre un événement global pour forcer la mise à jour du layout
      window.dispatchEvent(new CustomEvent('notificationCountChanged', {
        detail: {
          count
        }
      }));
    });
  }
  /**
   * Met à jour le cache avec une seule notification (méthode simplifiée)
   * @param notification Notification à ajouter
   */
  updateNotificationCache(notification) {
    this.updateCache(notification, true);
  }
  /**
   * Met à jour le statut de lecture des notifications
   * @param ids IDs des notifications à mettre à jour
   * @param isRead Nouveau statut de lecture
   */
  updateNotificationStatus(ids, isRead) {
    ids.forEach(id => {
      const notif = this.notificationCache.get(id);
      if (notif) {
        this.notificationCache.set(id, {
          ...notif,
          isRead,
          readAt: isRead ? new Date().toISOString() : undefined
        });
      }
    });
    this.refreshNotificationObservables();
  }
  /**
   * Méthode générique pour supprimer des notifications du cache local
   * @param notificationIds IDs des notifications à supprimer
   * @returns Nombre de notifications supprimées
   */
  removeNotificationsFromCache(notificationIds) {
    console.log('🗑️ REMOVE FROM CACHE: Starting removal of', notificationIds.length, 'notifications');
    console.log('🗑️ REMOVE FROM CACHE: Cache size before:', this.notificationCache.size);
    let removedCount = 0;
    notificationIds.forEach(id => {
      if (this.notificationCache.has(id)) {
        console.log('🗑️ REMOVE FROM CACHE: Removing notification:', id);
        this.notificationCache.delete(id);
        removedCount++;
      } else {
        console.log('🗑️ REMOVE FROM CACHE: Notification not found in cache:', id);
      }
    });
    console.log('🗑️ REMOVE FROM CACHE: Removed', removedCount, 'notifications');
    console.log('🗑️ REMOVE FROM CACHE: Cache size after:', this.notificationCache.size);
    if (removedCount > 0) {
      console.log('🗑️ REMOVE FROM CACHE: Refreshing observables...');
      this.refreshNotificationObservables();
    }
    return removedCount;
  }
  /**
   * Méthode générique pour gérer les erreurs de suppression
   * @param error Erreur survenue
   * @param operation Nom de l'opération
   * @param fallbackResponse Réponse de fallback en cas d'erreur
   */
  handleDeletionError(error, operation, fallbackResponse) {
    this.logger.error('MessageService', `Erreur lors de ${operation}:`, error);
    return (0,rxjs__WEBPACK_IMPORTED_MODULE_7__.of)(fallbackResponse);
  }
  // Typing indicators
  startTyping(conversationId) {
    const userId = this.getCurrentUserId();
    if (!userId) {
      this.logger.warn('MessageService', 'Cannot start typing: no user ID');
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_7__.of)(false);
    }
    return this.apollo.mutate({
      mutation: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.START_TYPING_MUTATION,
      variables: {
        input: {
          conversationId,
          userId
        }
      }
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => result.data?.startTyping || false), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('MessageService', 'Error starting typing indicator', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Failed to start typing indicator'));
    }));
  }
  stopTyping(conversationId) {
    const userId = this.getCurrentUserId();
    if (!userId) {
      this.logger.warn('MessageService', 'Cannot stop typing: no user ID');
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_7__.of)(false);
    }
    return this.apollo.mutate({
      mutation: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.STOP_TYPING_MUTATION,
      variables: {
        input: {
          conversationId,
          userId
        }
      }
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => result.data?.stopTyping || false), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      this.logger.error('MessageService', 'Error stopping typing indicator', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error('Failed to stop typing indicator'));
    }));
  }
  // ========================================
  // MÉTHODE SENDMESSAGE MANQUANTE
  // ========================================
  /**
   * Envoie un message (texte, fichier, audio, etc.)
   * @param receiverId ID du destinataire
   * @param content Contenu du message (texte)
   * @param file Fichier à envoyer (optionnel)
   * @param messageType Type de message (TEXT, AUDIO, IMAGE, etc.)
   * @param conversationId ID de la conversation
   * @returns Observable avec le message envoyé
   */
  sendMessage(receiverId, content, file, messageType = 'TEXT', conversationId) {
    console.log('🚀 [MessageService] sendMessage called with:', {
      receiverId,
      content: content?.substring(0, 50),
      hasFile: !!file,
      fileName: file?.name,
      fileType: file?.type,
      fileSize: file?.size,
      messageType,
      conversationId
    });
    if (!receiverId) {
      const error = new Error('Receiver ID is required');
      console.error('❌ [MessageService] sendMessage error:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => error);
    }
    // Préparer les variables pour la mutation
    const variables = {
      receiverId,
      content: content || '',
      type: messageType
    };
    // Ajouter l'ID de conversation si fourni
    if (conversationId) {
      variables.conversationId = conversationId;
    }
    // Si un fichier est fourni, l'ajouter aux variables
    if (file) {
      variables.file = file;
      console.log('📁 [MessageService] Adding file to mutation:', {
        name: file.name,
        type: file.type,
        size: file.size
      });
    }
    console.log('📤 [MessageService] Sending mutation with variables:', variables);
    return this.apollo.mutate({
      mutation: _graphql_message_graphql__WEBPACK_IMPORTED_MODULE_2__.SEND_MESSAGE_MUTATION,
      variables,
      context: {
        useMultipart: !!file // Utiliser multipart si un fichier est présent
      }
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.map)(result => {
      console.log('✅ [MessageService] sendMessage mutation result:', result);
      if (!result.data?.sendMessage) {
        throw new Error('No message data received from server');
      }
      const message = result.data.sendMessage;
      console.log('📨 [MessageService] Message sent successfully:', {
        id: message.id,
        type: message.type,
        content: message.content?.substring(0, 50),
        hasAttachments: !!message.attachments?.length
      });
      // Normaliser le message reçu
      const normalizedMessage = this.normalizeMessage(message);
      console.log('🔧 [MessageService] Message normalized:', normalizedMessage);
      return normalizedMessage;
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.catchError)(error => {
      console.error('❌ [MessageService] sendMessage error:', error);
      this.logger.error('Error sending message:', error);
      // Fournir un message d'erreur plus spécifique
      let errorMessage = "Erreur lors de l'envoi du message";
      if (error.networkError) {
        errorMessage = 'Erreur de connexion réseau';
      } else if (error.graphQLErrors?.length > 0) {
        errorMessage = error.graphQLErrors[0].message || errorMessage;
      }
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_8__.throwError)(() => new Error(errorMessage));
    }));
  }
  // ========================================
  // MÉTHODES UTILITAIRES CONSOLIDÉES
  // ========================================
  /**
   * Formate l'heure d'un message
   */
  formatMessageTime(timestamp) {
    if (!timestamp) return 'Unknown time';
    try {
      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);
      return date.toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
    } catch (error) {
      return 'Invalid time';
    }
  }
  /**
   * Formate la dernière activité d'un utilisateur
   */
  formatLastActive(lastActive) {
    if (!lastActive) return 'Offline';
    const lastActiveDate = lastActive instanceof Date ? lastActive : new Date(lastActive);
    const now = new Date();
    const diffHours = Math.abs(now.getTime() - lastActiveDate.getTime()) / (1000 * 60 * 60);
    if (diffHours < 24) {
      return `Active ${lastActiveDate.toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit'
      })}`;
    }
    return `Active ${lastActiveDate.toLocaleDateString()}`;
  }
  /**
   * Formate la date d'un message
   */
  formatMessageDate(timestamp) {
    if (!timestamp) return 'Unknown date';
    try {
      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);
      const today = new Date();
      if (date.toDateString() === today.toDateString()) {
        return date.toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit'
        });
      }
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      if (date.toDateString() === yesterday.toDateString()) {
        return `LUN., ${date.toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit'
        })}`;
      }
      const day = date.toLocaleDateString('fr-FR', {
        weekday: 'short'
      }).toUpperCase();
      return `${day}., ${date.toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit'
      })}`;
    } catch (error) {
      return 'Invalid date';
    }
  }
  /**
   * Détermine si un en-tête de date doit être affiché
   */
  shouldShowDateHeader(messages, index) {
    if (index === 0) return true;
    try {
      const currentMsg = messages[index];
      const prevMsg = messages[index - 1];
      if (!currentMsg?.timestamp || !prevMsg?.timestamp) return true;
      const currentDate = this.getDateFromTimestamp(currentMsg.timestamp);
      const prevDate = this.getDateFromTimestamp(prevMsg.timestamp);
      return currentDate !== prevDate;
    } catch (error) {
      return false;
    }
  }
  getDateFromTimestamp(timestamp) {
    if (!timestamp) return 'unknown-date';
    try {
      return (timestamp instanceof Date ? timestamp : new Date(timestamp)).toDateString();
    } catch (error) {
      return 'invalid-date';
    }
  }
  /**
   * Obtient l'icône d'un fichier selon son type MIME
   */
  getFileIcon(mimeType) {
    if (!mimeType) return 'fa-file';
    if (mimeType.startsWith('image/')) return 'fa-image';
    if (mimeType.includes('pdf')) return 'fa-file-pdf';
    if (mimeType.includes('word') || mimeType.includes('msword')) return 'fa-file-word';
    if (mimeType.includes('excel')) return 'fa-file-excel';
    if (mimeType.includes('powerpoint')) return 'fa-file-powerpoint';
    if (mimeType.includes('audio')) return 'fa-file-audio';
    if (mimeType.includes('video')) return 'fa-file-video';
    if (mimeType.includes('zip') || mimeType.includes('compressed')) return 'fa-file-archive';
    return 'fa-file';
  }
  /**
   * Obtient le type d'un fichier selon son type MIME
   */
  getFileType(mimeType) {
    if (!mimeType) return 'File';
    const typeMap = {
      'image/': 'Image',
      'application/pdf': 'PDF',
      'application/msword': 'Word Doc',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word Doc',
      'application/vnd.ms-excel': 'Excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel',
      'application/vnd.ms-powerpoint': 'PowerPoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'PowerPoint',
      'audio/': 'Audio',
      'video/': 'Video',
      'application/zip': 'ZIP Archive',
      'application/x-rar-compressed': 'RAR Archive'
    };
    for (const [key, value] of Object.entries(typeMap)) {
      if (mimeType.includes(key)) return value;
    }
    return 'File';
  }
  /**
   * Vérifie si un message contient une image
   */
  hasImage(message) {
    if (!message || !message.attachments || message.attachments.length === 0) {
      return false;
    }
    const attachment = message.attachments[0];
    if (!attachment || !attachment.type) {
      return false;
    }
    const type = attachment.type.toString();
    return type === 'IMAGE' || type === 'image';
  }
  /**
   * Vérifie si le message est un message vocal
   */
  isVoiceMessage(message) {
    if (!message) return false;
    // Vérifier le type du message
    if (message.type === _models_message_model__WEBPACK_IMPORTED_MODULE_1__.MessageType.VOICE_MESSAGE || message.type === _models_message_model__WEBPACK_IMPORTED_MODULE_1__.MessageType.VOICE_MESSAGE) {
      return true;
    }
    // Vérifier les pièces jointes
    if (message.attachments && message.attachments.length > 0) {
      return message.attachments.some(att => {
        const type = att.type?.toString();
        return type === 'VOICE_MESSAGE' || type === 'voice_message' || message.metadata?.isVoiceMessage && (type === 'AUDIO' || type === 'audio');
      });
    }
    // Vérifier les métadonnées
    return !!message.metadata?.isVoiceMessage;
  }
  /**
   * Récupère l'URL du message vocal
   */
  getVoiceMessageUrl(message) {
    if (!message || !message.attachments || message.attachments.length === 0) {
      return '';
    }
    const voiceAttachment = message.attachments.find(att => {
      const type = att.type?.toString();
      return type === 'VOICE_MESSAGE' || type === 'voice_message' || type === 'AUDIO' || type === 'audio';
    });
    return voiceAttachment?.url || '';
  }
  /**
   * Récupère la durée du message vocal
   */
  getVoiceMessageDuration(message) {
    if (!message) return 0;
    // Essayer d'abord de récupérer la durée depuis les métadonnées
    if (message.metadata?.duration) {
      return message.metadata.duration;
    }
    // Sinon, essayer de récupérer depuis les pièces jointes
    if (message.attachments && message.attachments.length > 0) {
      const voiceAttachment = message.attachments.find(att => {
        const type = att.type?.toString();
        return type === 'VOICE_MESSAGE' || type === 'voice_message' || type === 'AUDIO' || type === 'audio';
      });
      if (voiceAttachment && voiceAttachment.duration) {
        return voiceAttachment.duration;
      }
    }
    return 0;
  }
  /**
   * Génère la hauteur des barres de la forme d'onde moderne
   */
  getVoiceBarHeight(index) {
    const pattern = [8, 12, 6, 15, 10, 18, 7, 14, 9, 16, 5, 13, 11, 17, 8, 12, 6, 15, 10, 18];
    return pattern[index % pattern.length];
  }
  /**
   * Formate la durée du message vocal en format MM:SS
   */
  formatVoiceDuration(seconds) {
    if (!seconds || seconds === 0) {
      return '0:00';
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
  /**
   * Obtient l'URL de l'image en toute sécurité
   */
  getImageUrl(message) {
    if (!message || !message.attachments || message.attachments.length === 0) {
      return '';
    }
    const attachment = message.attachments[0];
    return attachment?.url || '';
  }
  /**
   * Détermine le type d'un message
   */
  getMessageType(message) {
    if (!message) return _models_message_model__WEBPACK_IMPORTED_MODULE_1__.MessageType.TEXT;
    try {
      if (message.type) {
        const msgType = message.type.toString();
        if (msgType === 'text' || msgType === 'TEXT') {
          return _models_message_model__WEBPACK_IMPORTED_MODULE_1__.MessageType.TEXT;
        } else if (msgType === 'image' || msgType === 'IMAGE') {
          return _models_message_model__WEBPACK_IMPORTED_MODULE_1__.MessageType.IMAGE;
        } else if (msgType === 'file' || msgType === 'FILE') {
          return _models_message_model__WEBPACK_IMPORTED_MODULE_1__.MessageType.FILE;
        } else if (msgType === 'audio' || msgType === 'AUDIO') {
          return _models_message_model__WEBPACK_IMPORTED_MODULE_1__.MessageType.AUDIO;
        } else if (msgType === 'video' || msgType === 'VIDEO') {
          return _models_message_model__WEBPACK_IMPORTED_MODULE_1__.MessageType.VIDEO;
        } else if (msgType === 'system' || msgType === 'SYSTEM') {
          return _models_message_model__WEBPACK_IMPORTED_MODULE_1__.MessageType.SYSTEM;
        }
      }
      if (message.attachments?.length) {
        const attachment = message.attachments[0];
        if (attachment && attachment.type) {
          const attachmentTypeStr = attachment.type.toString();
          if (attachmentTypeStr === 'image' || attachmentTypeStr === 'IMAGE') {
            return _models_message_model__WEBPACK_IMPORTED_MODULE_1__.MessageType.IMAGE;
          } else if (attachmentTypeStr === 'file' || attachmentTypeStr === 'FILE') {
            return _models_message_model__WEBPACK_IMPORTED_MODULE_1__.MessageType.FILE;
          } else if (attachmentTypeStr === 'audio' || attachmentTypeStr === 'AUDIO') {
            return _models_message_model__WEBPACK_IMPORTED_MODULE_1__.MessageType.AUDIO;
          } else if (attachmentTypeStr === 'video' || attachmentTypeStr === 'VIDEO') {
            return _models_message_model__WEBPACK_IMPORTED_MODULE_1__.MessageType.VIDEO;
          }
        }
        return _models_message_model__WEBPACK_IMPORTED_MODULE_1__.MessageType.FILE;
      }
      return _models_message_model__WEBPACK_IMPORTED_MODULE_1__.MessageType.TEXT;
    } catch (error) {
      return _models_message_model__WEBPACK_IMPORTED_MODULE_1__.MessageType.TEXT;
    }
  }
  /**
   * Retourne la liste des emojis communs
   */
  getCommonEmojis() {
    return ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣', '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗', '🤔', '👍', '👎', '👏', '🙌', '👐', '🤲', '🤝', '🙏', '✌️', '🤞', '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '💔', '💯', '💢'];
  }
  /**
   * Obtient les classes CSS pour un message
   */
  getMessageTypeClass(message, currentUserId) {
    if (!message) {
      return 'bg-gray-100 rounded-lg px-4 py-2';
    }
    try {
      const isCurrentUser = message.sender?.id === currentUserId || message.sender?._id === currentUserId || message.senderId === currentUserId;
      const baseClass = isCurrentUser ? 'bg-blue-500 text-white rounded-2xl rounded-br-sm' : 'bg-gray-200 text-gray-800 rounded-2xl rounded-bl-sm';
      const messageType = this.getMessageType(message);
      if (message.attachments && message.attachments.length > 0) {
        const attachment = message.attachments[0];
        if (attachment && attachment.type) {
          const attachmentTypeStr = attachment.type.toString();
          if (attachmentTypeStr === 'IMAGE' || attachmentTypeStr === 'image') {
            return `p-1 max-w-xs`;
          } else if (attachmentTypeStr === 'FILE' || attachmentTypeStr === 'file') {
            return `${baseClass} p-3`;
          }
        }
      }
      // Les vérifications de type sont déjà faites avec les attachments ci-dessus
      return `${baseClass} px-4 py-3 whitespace-normal break-words min-w-[120px]`;
    } catch (error) {
      return 'bg-gray-100 rounded-lg px-4 py-2 whitespace-normal break-words';
    }
  }
  // ========================================
  // APPELS WEBRTC - DÉLÉGUÉS AU CALLSERVICE
  // ========================================
  // Note: Les méthodes d'appel ont été déplacées vers CallService
  // pour éviter la duplication de code et centraliser la logique
  // destroy
  cleanupSubscriptions() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.subscriptions = [];
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.notificationCache.clear();
    this.logger.debug('NotificationService destroyed');
  }
  ngOnDestroy() {
    this.cleanupSubscriptions();
  }
  static {
    this.ɵfac = function MessageService_Factory(t) {
      return new (t || MessageService)(_angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵinject"](apollo_angular__WEBPACK_IMPORTED_MODULE_18__.Apollo), _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵinject"](_logger_service__WEBPACK_IMPORTED_MODULE_3__.LoggerService), _angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵinject"](_angular_core__WEBPACK_IMPORTED_MODULE_17__.NgZone));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_17__["ɵɵdefineInjectable"]({
      token: MessageService,
      factory: MessageService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 487:
/*!*******************************************!*\
  !*** ./src/app/services/theme.service.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   THEMES: () => (/* binding */ THEMES),
/* harmony export */   ThemeService: () => (/* binding */ ThemeService)
/* harmony export */ });
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rxjs */ 5797);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);


const THEMES = {
  dark: {
    name: 'dark',
    displayName: 'Sombre',
    colors: {
      primary: '#3b82f6',
      secondary: '#6366f1',
      accent: '#8b5cf6',
      background: '#111827',
      surface: '#1f2937',
      text: '#ffffff',
      textSecondary: '#9ca3af',
      border: '#374151',
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444'
    },
    gradients: {
      primary: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
      secondary: 'linear-gradient(135deg, #6366f1 0%, #4f46e5 100%)',
      accent: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)'
    }
  },
  neon: {
    name: 'neon',
    displayName: 'Néon',
    colors: {
      primary: '#00ffff',
      secondary: '#ff00ff',
      accent: '#ffff00',
      background: '#0a0a0a',
      surface: '#1a1a1a',
      text: '#ffffff',
      textSecondary: '#cccccc',
      border: '#333333',
      success: '#00ff00',
      warning: '#ff8800',
      error: '#ff0040'
    },
    gradients: {
      primary: 'linear-gradient(135deg, #00ffff 0%, #0080ff 100%)',
      secondary: 'linear-gradient(135deg, #ff00ff 0%, #8000ff 100%)',
      accent: 'linear-gradient(135deg, #ffff00 0%, #ff8000 100%)'
    }
  },
  purple: {
    name: 'purple',
    displayName: 'Violet',
    colors: {
      primary: '#8b5cf6',
      secondary: '#a855f7',
      accent: '#c084fc',
      background: '#1e1b4b',
      surface: '#312e81',
      text: '#ffffff',
      textSecondary: '#c7d2fe',
      border: '#4c1d95',
      success: '#22c55e',
      warning: '#eab308',
      error: '#ef4444'
    },
    gradients: {
      primary: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
      secondary: 'linear-gradient(135deg, #a855f7 0%, #9333ea 100%)',
      accent: 'linear-gradient(135deg, #c084fc 0%, #a855f7 100%)'
    }
  },
  ocean: {
    name: 'ocean',
    displayName: 'Océan',
    colors: {
      primary: '#0ea5e9',
      secondary: '#06b6d4',
      accent: '#22d3ee',
      background: '#0c4a6e',
      surface: '#075985',
      text: '#ffffff',
      textSecondary: '#bae6fd',
      border: '#0369a1',
      success: '#059669',
      warning: '#d97706',
      error: '#dc2626'
    },
    gradients: {
      primary: 'linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%)',
      secondary: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',
      accent: 'linear-gradient(135deg, #22d3ee 0%, #06b6d4 100%)'
    }
  }
};
class ThemeService {
  constructor() {
    this.currentTheme = new rxjs__WEBPACK_IMPORTED_MODULE_0__.BehaviorSubject(THEMES['dark']);
    this.currentTheme$ = this.currentTheme.asObservable();
    this.loadThemeFromStorage();
    this.applyTheme(this.currentTheme.value);
  }
  setTheme(themeName) {
    const theme = THEMES[themeName];
    if (theme) {
      this.currentTheme.next(theme);
      this.applyTheme(theme);
      this.saveThemeToStorage(themeName);
    }
  }
  getCurrentTheme() {
    return this.currentTheme.value;
  }
  getAvailableThemes() {
    return Object.values(THEMES);
  }
  applyTheme(theme) {
    const root = document.documentElement;
    // Appliquer les couleurs CSS custom properties
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value);
    });
    // Appliquer les gradients
    Object.entries(theme.gradients).forEach(([key, value]) => {
      root.style.setProperty(`--gradient-${key}`, value);
    });
    // Ajouter la classe de thème au body
    document.body.className = document.body.className.replace(/theme-\w+/g, '');
    document.body.classList.add(`theme-${theme.name}`);
  }
  saveThemeToStorage(themeName) {
    try {
      localStorage.setItem('selectedTheme', themeName);
    } catch (error) {
      console.warn('Could not save theme to localStorage:', error);
    }
  }
  loadThemeFromStorage() {
    try {
      const savedTheme = localStorage.getItem('selectedTheme');
      if (savedTheme && THEMES[savedTheme]) {
        this.currentTheme.next(THEMES[savedTheme]);
      }
    } catch (error) {
      console.warn('Could not load theme from localStorage:', error);
    }
  }
  // Méthodes utilitaires pour les composants
  getPrimaryColor() {
    return this.currentTheme.value.colors.primary;
  }
  getSecondaryColor() {
    return this.currentTheme.value.colors.secondary;
  }
  getAccentColor() {
    return this.currentTheme.value.colors.accent;
  }
  getPrimaryGradient() {
    return this.currentTheme.value.gradients.primary;
  }
  isTheme(themeName) {
    return this.currentTheme.value.name === themeName;
  }
  // Méthode pour basculer entre les thèmes
  toggleTheme() {
    const currentTheme = this.currentTheme.value;
    if (currentTheme.name === 'dark') {
      this.setTheme('light');
    } else {
      this.setTheme('dark');
    }
  }
  // Alias pour la compatibilité
  toggleDarkMode() {
    this.toggleTheme();
  }
  static {
    this.ɵfac = function ThemeService_Factory(t) {
      return new (t || ThemeService)();
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
      token: ThemeService,
      factory: ThemeService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 9722:
/*!*************************************************!*\
  !*** ./src/app/services/user-status.service.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   UserStatusService: () => (/* binding */ UserStatusService)
/* harmony export */ });
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rxjs/operators */ 1567);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rxjs */ 3942);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _message_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./message.service */ 4537);
/* harmony import */ var _logger_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./logger.service */ 4798);





class UserStatusService {
  constructor(messageService, logger, ngZone) {
    this.messageService = messageService;
    this.logger = logger;
    this.ngZone = ngZone;
    this.onlineUsers = new Map();
    this.reconnectionAttempts = 0;
    this.maxReconnectionAttempts = 5;
    this.reconnectionDelay = 1000;
    this.initStatusSubscription();
  }
  //helper methode
  initStatusSubscription() {
    this.logger.debug('Initializing user status subscription');
    this.ngZone.runOutsideAngular(() => {
      try {
        this.statusSub?.unsubscribe(); // Unsubscribe from any existing subscription
        this.statusSub = this.messageService.subscribeToUserStatus().subscribe({
          next: user => this.handleUserStatusUpdate(user),
          error: error => this.handleSubscriptionError(error),
          complete: () => this.logger.debug('User status subscription completed')
        });
        this.logger.debug('User status subscription initialized successfully');
      } catch (error) {
        this.logger.error('Error initializing user status subscription:', error);
        // Schedule a retry after a delay
        setTimeout(() => this.initStatusSubscription(), 5000);
      }
    });
  }
  handleUserStatusUpdate(user) {
    this.ngZone.run(() => {
      const isOnline = user.isOnline ?? false;
      if (isOnline) {
        this.onlineUsers.set(user._id, user);
        this.logger.debug(`User ${user.username} is now online`, {
          userId: user._id
        });
      } else {
        this.onlineUsers.delete(user._id);
        this.logger.debug(`User ${user.username} is now offline`, {
          userId: user._id
        });
      }
      this.reconnectionAttempts = 0;
    });
  }
  handleSubscriptionError(error) {
    this.logger.error('Status subscription error', error, {
      attempt: this.reconnectionAttempts,
      maxAttempts: this.maxReconnectionAttempts
    });
    if (this.reconnectionAttempts < this.maxReconnectionAttempts) {
      this.reconnectionAttempts++;
      const delay = this.reconnectionDelay * Math.pow(2, this.reconnectionAttempts - 1);
      this.logger.debug(`Attempting reconnection in ${delay}ms`, {
        attempt: this.reconnectionAttempts,
        maxAttempts: this.maxReconnectionAttempts
      });
      setTimeout(() => {
        this.initStatusSubscription();
      }, delay);
    } else {
      this.logger.error('Max reconnection attempts reached', undefined, {
        maxAttempts: this.maxReconnectionAttempts
      });
    }
  }
  //  méthodes
  trackUserPresence(userId) {
    return new rxjs__WEBPACK_IMPORTED_MODULE_2__.Observable(observer => {
      // État initial - avec vérification que isOnline est défini
      const user = this.onlineUsers.get(userId);
      observer.next(user?.isOnline ?? false);
      // Abonnement aux changements
      const sub = this.messageService.subscribeToUserStatus().pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_3__.filter)(user => user._id === userId)).subscribe({
        next: user => observer.next(user.isOnline ?? false),
        error: err => observer.error(err)
      });
      return () => sub.unsubscribe();
    });
  }
  isUserOnline(userId) {
    const user = this.onlineUsers.get(userId);
    return user?.isOnline ?? false;
  }
  getOnlineUsers() {
    return Array.from(this.onlineUsers.values());
  }
  getUserStatus(userId) {
    const user = this.onlineUsers.get(userId);
    return {
      isOnline: user?.isOnline ?? false,
      lastSeen: user?.lastActive ? new Date(user.lastActive) : undefined
    };
  }
  ngOnDestroy() {
    this.statusSub?.unsubscribe();
    this.onlineUsers.clear();
    this.logger.debug('UserStatusService destroyed');
  }
  static {
    this.ɵfac = function UserStatusService_Factory(t) {
      return new (t || UserStatusService)(_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵinject"](_message_service__WEBPACK_IMPORTED_MODULE_0__.MessageService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵinject"](_logger_service__WEBPACK_IMPORTED_MODULE_1__.LoggerService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵinject"](_angular_core__WEBPACK_IMPORTED_MODULE_4__.NgZone));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineInjectable"]({
      token: UserStatusService,
      factory: UserStatusService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 3887:
/*!*****************************************!*\
  !*** ./src/app/shared/shared.module.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SharedModule: () => (/* binding */ SharedModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common/http */ 6443);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 7580);





// AiChatbotModule sera importé directement dans AppModule
class SharedModule {
  static {
    this.ɵfac = function SharedModule_Factory(t) {
      return new (t || SharedModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineNgModule"]({
      type: SharedModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineInjector"]({
      providers: [_angular_common__WEBPACK_IMPORTED_MODULE_1__.DatePipe],
      imports: [_angular_common__WEBPACK_IMPORTED_MODULE_1__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.ReactiveFormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormsModule, _angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterModule, _angular_common_http__WEBPACK_IMPORTED_MODULE_4__.HttpClientModule, _angular_common__WEBPACK_IMPORTED_MODULE_1__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.ReactiveFormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormsModule, _angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵsetNgModuleScope"](SharedModule, {
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_1__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.ReactiveFormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormsModule, _angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterModule, _angular_common_http__WEBPACK_IMPORTED_MODULE_4__.HttpClientModule],
    exports: [_angular_common__WEBPACK_IMPORTED_MODULE_1__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.ReactiveFormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormsModule, _angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterModule]
  });
})();

/***/ }),

/***/ 3235:
/*!**************************************************!*\
  !*** ./src/app/views/guards/guardadmin.guard.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   guardadminGuard: () => (/* binding */ guardadminGuard)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var src_app_services_authadmin_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/services/authadmin.service */ 4667);



const guardadminGuard = (route, state) => {
  const authService = (0,_angular_core__WEBPACK_IMPORTED_MODULE_1__.inject)(src_app_services_authadmin_service__WEBPACK_IMPORTED_MODULE_0__.AuthadminService);
  const router = (0,_angular_core__WEBPACK_IMPORTED_MODULE_1__.inject)(_angular_router__WEBPACK_IMPORTED_MODULE_2__.Router);
  if (authService.loggedIn() == true) {
    return true;
  } else {
    router.navigate(['/admin/login'], {
      queryParams: {
        returnUrl: state.url
      }
    });
    localStorage.removeItem('token');
    return false;
  }
};

/***/ }),

/***/ 6039:
/*!*************************************************!*\
  !*** ./src/app/views/guards/guarduser.guard.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   guarduserGuard: () => (/* binding */ guarduserGuard)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var src_app_services_authuser_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/services/authuser.service */ 9271);



const guarduserGuard = (route, state) => {
  const authService = (0,_angular_core__WEBPACK_IMPORTED_MODULE_1__.inject)(src_app_services_authuser_service__WEBPACK_IMPORTED_MODULE_0__.AuthuserService);
  const router = (0,_angular_core__WEBPACK_IMPORTED_MODULE_1__.inject)(_angular_router__WEBPACK_IMPORTED_MODULE_2__.Router);
  if (authService.userLoggedIn() == true) {
    return true;
  } else {
    router.navigate(['/loginuser'], {
      queryParams: {
        returnUrl: state.url
      }
    });
    localStorage.removeItem('token');
    return false;
  }
};

/***/ }),

/***/ 8722:
/*!***************************************************!*\
  !*** ./src/app/views/guards/noguarduser.guard.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   noguarduserGuard: () => (/* binding */ noguarduserGuard)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var src_app_services_authuser_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/services/authuser.service */ 9271);



const noguarduserGuard = (route, state) => {
  const authService = (0,_angular_core__WEBPACK_IMPORTED_MODULE_1__.inject)(src_app_services_authuser_service__WEBPACK_IMPORTED_MODULE_0__.AuthuserService);
  const router = (0,_angular_core__WEBPACK_IMPORTED_MODULE_1__.inject)(_angular_router__WEBPACK_IMPORTED_MODULE_2__.Router);
  if (authService.userLoggedIn() == false) {
    return true;
  } else {
    router.navigate(['/users']);
    return false;
  }
};

/***/ }),

/***/ 5312:
/*!*****************************************!*\
  !*** ./src/environments/environment.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   environment: () => (/* binding */ environment)
/* harmony export */ });
// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.
const environment = {
  production: false,
  secret: '2cinfo1',
  client: 'esprit',
  urlBackend: 'http://localhost:3000/api/',
  geminiApiKey: 'AIzaSyDCXc16FzaVWSJkW4RGboTZ8AD9_PTDL88'
};
/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.

/***/ }),

/***/ 4429:
/*!*********************!*\
  !*** ./src/main.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _angular_platform_browser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/platform-browser */ 436);
/* harmony import */ var _app_app_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./app/app.module */ 635);


_angular_platform_browser__WEBPACK_IMPORTED_MODULE_1__.platformBrowser().bootstrapModule(_app_app_module__WEBPACK_IMPORTED_MODULE_0__.AppModule).catch(err => console.error(err));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["vendor"], () => (__webpack_exec__(4429)));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);
//# sourceMappingURL=main.js.map