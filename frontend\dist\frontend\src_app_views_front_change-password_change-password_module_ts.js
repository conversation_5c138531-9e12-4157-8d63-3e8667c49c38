"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([["src_app_views_front_change-password_change-password_module_ts"],{

/***/ 8405:
/*!*******************************************************************************!*\
  !*** ./src/app/views/front/change-password/change-password-routing.module.ts ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ChangePasswordRoutingModule: () => (/* binding */ ChangePasswordRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _change_password_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./change-password.component */ 4585);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);




const routes = [{
  path: '',
  component: _change_password_component__WEBPACK_IMPORTED_MODULE_0__.ChangePasswordComponent
}];
class ChangePasswordRoutingModule {
  static {
    this.ɵfac = function ChangePasswordRoutingModule_Factory(t) {
      return new (t || ChangePasswordRoutingModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineNgModule"]({
      type: ChangePasswordRoutingModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjector"]({
      imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵsetNgModuleScope"](ChangePasswordRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_2__.RouterModule]
  });
})();

/***/ }),

/***/ 4585:
/*!**************************************************************************!*\
  !*** ./src/app/views/front/change-password/change-password.component.ts ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ChangePasswordComponent: () => (/* binding */ ChangePasswordComponent)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _services_auth_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../services/auth.service */ 4796);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 316);






function ChangePasswordComponent_div_37_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 37);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](1, "i", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](2, " Current password is required ");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
function ChangePasswordComponent_div_46_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 37);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](1, "i", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](2, " New password is required ");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
function ChangePasswordComponent_div_47_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 39)(1, "div", 40)(2, "div", 41);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](3, "i", 42)(4, "div", 43);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](5, "div", 44)(6, "p", 45);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"](" ", ctx_r2.error, " ");
  }
}
function ChangePasswordComponent_div_48_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 46)(1, "div", 40)(2, "div", 47);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](3, "i", 48)(4, "div", 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](5, "div", 44)(6, "p", 50);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"](" ", ctx_r3.message, " ");
  }
}
class ChangePasswordComponent {
  constructor(fb, authService, router) {
    this.fb = fb;
    this.authService = authService;
    this.router = router;
    this.message = '';
    this.error = '';
    this.form = this.fb.group({
      currentPassword: ['', _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required],
      newPassword: ['', [_angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.minLength(6)]]
    });
  }
  onSubmit() {
    if (this.form.invalid) return;
    const token = localStorage.getItem('token');
    this.authService.changePassword(this.form.value, token).subscribe({
      next: res => {
        this.message = res.message;
        this.error = '';
        this.form.reset();
        setTimeout(() => this.router.navigate(['/profile']), 1500);
      },
      error: err => {
        this.error = err.error.message || 'Failed to change password';
        this.message = '';
      }
    });
  }
  static {
    this.ɵfac = function ChangePasswordComponent_Factory(t) {
      return new (t || ChangePasswordComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormBuilder), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_services_auth_service__WEBPACK_IMPORTED_MODULE_0__.AuthService), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_3__.Router));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
      type: ChangePasswordComponent,
      selectors: [["app-change-password"]],
      decls: 60,
      vars: 6,
      consts: [[1, "container-fluid", "p-4", "md:p-6", "bg-[#edf1f4]", "dark:bg-[#121212]", "min-h-screen", "flex", "items-center", "justify-center", "relative"], [1, "absolute", "inset-0", "overflow-hidden", "pointer-events-none"], [1, "absolute", "top-[15%]", "left-[10%]", "w-64", "h-64", "rounded-full", "bg-gradient-to-br", "from-[#4f5fad]/5", "to-transparent", "dark:from-[#6d78c9]/3", "dark:to-transparent", "blur-3xl"], [1, "absolute", "bottom-[20%]", "right-[10%]", "w-80", "h-80", "rounded-full", "bg-gradient-to-tl", "from-[#4f5fad]/5", "to-transparent", "dark:from-[#6d78c9]/3", "dark:to-transparent", "blur-3xl"], [1, "absolute", "inset-0", "opacity-5", "dark:opacity-[0.03]"], [1, "h-full", "grid", "grid-cols-12"], [1, "border-r", "border-[#4f5fad]", "dark:border-[#6d78c9]"], [1, "w-full", "max-w-md", "relative", "z-10"], [1, "bg-white", "dark:bg-[#1e1e1e]", "rounded-xl", "shadow-lg", "dark:shadow-[0_8px_30px_rgba(0,0,0,0.3)]", "overflow-hidden", "backdrop-blur-sm", "border", "border-[#edf1f4]/50", "dark:border-[#2a2a2a]", "relative"], [1, "absolute", "top-0", "left-0", "right-0", "h-1", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]"], [1, "absolute", "top-0", "left-0", "right-0", "h-1", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "blur-md"], [1, "p-6", "text-center"], [1, "text-2xl", "font-bold", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "bg-clip-text", "text-transparent", "flex", "items-center", "justify-center"], [1, "fas", "fa-key", "mr-2"], [1, "text-sm", "text-[#6d6870]", "dark:text-[#a0a0a0]", "mt-2"], [1, "p-6"], [1, "space-y-5", 3, "formGroup", "ngSubmit"], [1, "group"], [1, "flex", "items-center", "text-sm", "font-medium", "text-[#4f5fad]", "dark:text-[#6d78c9]", "mb-2"], [1, "fas", "fa-lock", "mr-1.5", "text-xs"], [1, "relative"], ["type", "password", "formControlName", "currentPassword", "placeholder", "\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022", 1, "w-full", "px-4", "py-2.5", "text-sm", "rounded-lg", "border", "border-[#bdc6cc]", "dark:border-[#2a2a2a]", "bg-white", "dark:bg-[#1e1e1e]", "text-[#6d6870]", "dark:text-[#e0e0e0]", "focus:outline-none", "focus:border-[#4f5fad]", "dark:focus:border-[#6d78c9]", "focus:ring-2", "focus:ring-[#4f5fad]/20", "dark:focus:ring-[#6d78c9]/20", "transition-all"], [1, "absolute", "inset-y-0", "left-0", "pl-3", "flex", "items-center", "pointer-events-none", "opacity-0", "group-focus-within:opacity-100", "transition-opacity"], [1, "w-0.5", "h-4", "bg-gradient-to-b", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#6d78c9]", "dark:to-[#4f5fad]", "rounded-full"], ["class", "text-[#ff6b69] dark:text-[#ff8785] text-xs mt-1.5 flex items-center", 4, "ngIf"], [1, "fas", "fa-key", "mr-1.5", "text-xs"], ["type", "password", "formControlName", "newPassword", "placeholder", "\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022", 1, "w-full", "px-4", "py-2.5", "text-sm", "rounded-lg", "border", "border-[#bdc6cc]", "dark:border-[#2a2a2a]", "bg-white", "dark:bg-[#1e1e1e]", "text-[#6d6870]", "dark:text-[#e0e0e0]", "focus:outline-none", "focus:border-[#4f5fad]", "dark:focus:border-[#6d78c9]", "focus:ring-2", "focus:ring-[#4f5fad]/20", "dark:focus:ring-[#6d78c9]/20", "transition-all"], ["class", "bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-3 backdrop-blur-sm", 4, "ngIf"], ["class", "bg-[#4f5fad]/10 dark:bg-[#6d78c9]/5 border border-[#4f5fad] dark:border-[#6d78c9]/30 rounded-lg p-3 backdrop-blur-sm", 4, "ngIf"], ["type", "submit", 1, "w-full", "relative", "overflow-hidden", "group", "mt-6", 3, "disabled"], [1, "absolute", "inset-0", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#3d4a85]", "dark:to-[#6d78c9]", "rounded-lg", "transition-transform", "duration-300", "group-hover:scale-105", "disabled:opacity-50"], [1, "absolute", "inset-0", "bg-gradient-to-r", "from-[#3d4a85]", "to-[#4f5fad]", "dark:from-[#3d4a85]", "dark:to-[#6d78c9]", "rounded-lg", "opacity-0", "group-hover:opacity-100", "blur-xl", "transition-opacity", "duration-300", "disabled:opacity-0"], [1, "relative", "flex", "items-center", "justify-center", "text-white", "font-medium", "py-2.5", "px-4", "rounded-lg", "transition-all", "z-10"], [1, "fas", "fa-save", "mr-2"], [1, "text-center", "text-sm", "text-[#6d6870]", "dark:text-[#a0a0a0]", "space-y-2", "pt-4"], ["routerLink", "/profile", 1, "text-[#4f5fad]", "dark:text-[#6d78c9]", "hover:text-[#3d4a85]", "dark:hover:text-[#4f5fad]", "transition-colors", "font-medium", "flex", "items-center", "justify-center"], [1, "fas", "fa-arrow-left", "mr-1.5", "text-xs"], [1, "text-[#ff6b69]", "dark:text-[#ff8785]", "text-xs", "mt-1.5", "flex", "items-center"], [1, "fas", "fa-exclamation-circle", "mr-1"], [1, "bg-[#ff6b69]/10", "dark:bg-[#ff6b69]/5", "border", "border-[#ff6b69]", "dark:border-[#ff6b69]/30", "rounded-lg", "p-3", "backdrop-blur-sm"], [1, "flex", "items-start"], [1, "text-[#ff6b69]", "dark:text-[#ff8785]", "mr-2", "text-base", "relative"], [1, "fas", "fa-exclamation-triangle"], [1, "absolute", "inset-0", "bg-[#ff6b69]/20", "dark:bg-[#ff8785]/20", "blur-xl", "rounded-full", "transform", "scale-150", "-z-10"], [1, "flex-1"], [1, "text-xs", "text-[#ff6b69]", "dark:text-[#ff8785]"], [1, "bg-[#4f5fad]/10", "dark:bg-[#6d78c9]/5", "border", "border-[#4f5fad]", "dark:border-[#6d78c9]/30", "rounded-lg", "p-3", "backdrop-blur-sm"], [1, "text-[#4f5fad]", "dark:text-[#6d78c9]", "mr-2", "text-base", "relative"], [1, "fas", "fa-check-circle"], [1, "absolute", "inset-0", "bg-[#4f5fad]/20", "dark:bg-[#6d78c9]/20", "blur-xl", "rounded-full", "transform", "scale-150", "-z-10"], [1, "text-xs", "text-[#4f5fad]", "dark:text-[#6d78c9]"]],
      template: function ChangePasswordComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 0)(1, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](2, "div", 2)(3, "div", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](4, "div", 4)(5, "div", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](6, "div", 6)(7, "div", 6)(8, "div", 6)(9, "div", 6)(10, "div", 6)(11, "div", 6)(12, "div", 6)(13, "div", 6)(14, "div", 6)(15, "div", 6)(16, "div", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](17, "div", 7)(18, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](19, "div", 9)(20, "div", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](21, "div", 11)(22, "h1", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](23, "i", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](24, " Change Password ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](25, "p", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](26, " Update your account password ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](27, "div", 15)(28, "form", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("ngSubmit", function ChangePasswordComponent_Template_form_ngSubmit_28_listener() {
            return ctx.onSubmit();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](29, "div", 17)(30, "label", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](31, "i", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](32, " Current Password ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](33, "div", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](34, "input", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](35, "div", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](36, "div", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](37, ChangePasswordComponent_div_37_Template, 3, 0, "div", 24);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](38, "div", 17)(39, "label", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](40, "i", 25);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](41, " New Password ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](42, "div", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](43, "input", 26);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](44, "div", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](45, "div", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](46, ChangePasswordComponent_div_46_Template, 3, 0, "div", 24);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](47, ChangePasswordComponent_div_47_Template, 8, 1, "div", 27);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](48, ChangePasswordComponent_div_48_Template, 8, 1, "div", 28);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](49, "button", 29);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](50, "div", 30)(51, "div", 31);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](52, "span", 32);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](53, "i", 33);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](54, " Update Password ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](55, "div", 34)(56, "div")(57, "a", 35);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](58, "i", 36);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](59, " Back to Profile ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()()()()()()();
        }
        if (rf & 2) {
          let tmp_1_0;
          let tmp_2_0;
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](28);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("formGroup", ctx.form);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](9);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ((tmp_1_0 = ctx.form.get("currentPassword")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.form.get("currentPassword")) == null ? null : tmp_1_0.touched));
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](9);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ((tmp_2_0 = ctx.form.get("newPassword")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.form.get("newPassword")) == null ? null : tmp_2_0.touched));
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.error);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.message);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("disabled", ctx.form.invalid);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_2__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_2__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormControlName, _angular_router__WEBPACK_IMPORTED_MODULE_3__.RouterLink],
      styles: ["/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJjaGFuZ2UtcGFzc3dvcmQuY29tcG9uZW50LmNzcyJ9 */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvZnJvbnQvY2hhbmdlLXBhc3N3b3JkL2NoYW5nZS1wYXNzd29yZC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7O0FBRUEsNEtBQTRLIiwic291cmNlUm9vdCI6IiJ9 */"]
    });
  }
}

/***/ }),

/***/ 5756:
/*!***********************************************************************!*\
  !*** ./src/app/views/front/change-password/change-password.module.ts ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ChangePasswordModule: () => (/* binding */ ChangePasswordModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _change_password_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./change-password-routing.module */ 8405);
/* harmony import */ var _change_password_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./change-password.component */ 4585);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/core */ 7580);





class ChangePasswordModule {
  static {
    this.ɵfac = function ChangePasswordModule_Factory(t) {
      return new (t || ChangePasswordModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineNgModule"]({
      type: ChangePasswordModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵdefineInjector"]({
      imports: [_angular_common__WEBPACK_IMPORTED_MODULE_3__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.ReactiveFormsModule, _change_password_routing_module__WEBPACK_IMPORTED_MODULE_0__.ChangePasswordRoutingModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_2__["ɵɵsetNgModuleScope"](ChangePasswordModule, {
    declarations: [_change_password_component__WEBPACK_IMPORTED_MODULE_1__.ChangePasswordComponent],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_3__.CommonModule, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.ReactiveFormsModule, _change_password_routing_module__WEBPACK_IMPORTED_MODULE_0__.ChangePasswordRoutingModule]
  });
})();

/***/ })

}]);
//# sourceMappingURL=src_app_views_front_change-password_change-password_module_ts.js.map