{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../services/message.service\";\nimport * as i2 from \"../../../../services/auth.service\";\nimport * as i3 from \"../../../../services/toast.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nconst _c0 = [\"searchInput\"];\nfunction MessageLayoutComponent_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function MessageLayoutComponent_button_17_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.clearSearch());\n    });\n    i0.ɵɵelement(1, \"i\", 33);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageLayoutComponent_span_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.notifications.length > 9 ? \"9+\" : ctx_r2.notifications.length, \" \");\n  }\n}\nfunction MessageLayoutComponent_div_33_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵlistener(\"click\", function MessageLayoutComponent_div_33_div_1_div_3_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r14);\n      const result_r12 = restoredCtx.$implicit;\n      const ctx_r13 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r13.selectConversation(result_r12));\n    });\n    i0.ɵɵelementStart(1, \"div\", 4);\n    i0.ɵɵelement(2, \"img\", 43);\n    i0.ɵɵelementStart(3, \"div\", 44)(4, \"h4\", 45);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 46);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const result_r12 = ctx.$implicit;\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r11.getConversationAvatar(result_r12), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r11.getConversationName(result_r12));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.getConversationName(result_r12), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.getLastMessagePreview(result_r12), \" \");\n  }\n}\nfunction MessageLayoutComponent_div_33_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"div\", 40);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_33_div_1_div_3_Template, 8, 4, \"div\", 41);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" R\\u00E9sultats de recherche (\", ctx_r8.searchResults.length, \") \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.searchResults);\n  }\n}\nfunction MessageLayoutComponent_div_33_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵelement(1, \"i\", 48);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Aucun r\\u00E9sultat trouv\\u00E9\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageLayoutComponent_div_33_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵelement(1, \"div\", 53);\n    i0.ɵɵelementStart(2, \"p\", 54);\n    i0.ɵɵtext(3, \"Chargement des conversations...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageLayoutComponent_div_33_div_3_div_2_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 61);\n  }\n}\nfunction MessageLayoutComponent_div_33_div_3_div_2_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 62);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const conversation_r19 = i0.ɵɵnextContext().$implicit;\n    const ctx_r21 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r21.getUnreadCount(conversation_r19) > 99 ? \"99+\" : ctx_r21.getUnreadCount(conversation_r19), \" \");\n  }\n}\nfunction MessageLayoutComponent_div_33_div_3_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵlistener(\"click\", function MessageLayoutComponent_div_33_div_3_div_2_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r24);\n      const conversation_r19 = restoredCtx.$implicit;\n      const ctx_r23 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r23.selectConversation(conversation_r19));\n    });\n    i0.ɵɵelementStart(1, \"div\", 4)(2, \"div\", 10);\n    i0.ɵɵelement(3, \"img\", 43);\n    i0.ɵɵtemplate(4, MessageLayoutComponent_div_33_div_3_div_2_div_4_Template, 1, 0, \"div\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 44)(6, \"div\", 57)(7, \"h4\", 45);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 58);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 59)(12, \"p\", 46);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, MessageLayoutComponent_div_33_div_3_div_2_span_14_Template, 2, 1, \"span\", 60);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const conversation_r19 = ctx.$implicit;\n    const ctx_r16 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"bg-gray-700\", ctx_r16.selectedConversationId === conversation_r19.id)(\"border-l-4\", ctx_r16.selectedConversationId === conversation_r19.id)(\"border-blue-500\", ctx_r16.selectedConversationId === conversation_r19.id);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r16.getConversationAvatar(conversation_r19), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r16.getConversationName(conversation_r19));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !conversation_r19.isGroup && ctx_r16.isUserOnline(conversation_r19.participants == null ? null : conversation_r19.participants[0]));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r16.getConversationName(conversation_r19), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r16.formatLastMessageTime(conversation_r19.lastMessage == null ? null : conversation_r19.lastMessage.timestamp), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r16.getLastMessagePreview(conversation_r19), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.getUnreadCount(conversation_r19) > 0);\n  }\n}\nfunction MessageLayoutComponent_div_33_div_3_div_3_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Charger plus\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageLayoutComponent_div_33_div_3_div_3_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Chargement...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageLayoutComponent_div_33_div_3_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function MessageLayoutComponent_div_33_div_3_div_3_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r27.loadMoreConversations());\n    });\n    i0.ɵɵtemplate(2, MessageLayoutComponent_div_33_div_3_div_3_span_2_Template, 2, 0, \"span\", 38);\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_33_div_3_div_3_span_3_Template, 2, 0, \"span\", 38);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r17.isLoadingConversations);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r17.isLoadingConversations);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r17.isLoadingConversations);\n  }\n}\nfunction MessageLayoutComponent_div_33_div_3_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵelement(1, \"i\", 65);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Aucune conversation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 66);\n    i0.ɵɵtext(5, \" Commencez une nouvelle conversation dans l'onglet Contacts \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageLayoutComponent_div_33_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, MessageLayoutComponent_div_33_div_3_div_1_Template, 4, 0, \"div\", 49);\n    i0.ɵɵtemplate(2, MessageLayoutComponent_div_33_div_3_div_2_Template, 15, 13, \"div\", 50);\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_33_div_3_div_3_Template, 4, 3, \"div\", 51);\n    i0.ɵɵtemplate(4, MessageLayoutComponent_div_33_div_3_div_4_Template, 6, 0, \"div\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.isLoadingConversations && ctx_r10.conversations.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.conversations)(\"ngForTrackBy\", ctx_r10.trackByConversationId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.hasMoreConversations);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.conversations.length === 0 && !ctx_r10.isLoadingConversations);\n  }\n}\nfunction MessageLayoutComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtemplate(1, MessageLayoutComponent_div_33_div_1_Template, 4, 2, \"div\", 36);\n    i0.ɵɵtemplate(2, MessageLayoutComponent_div_33_div_2_Template, 4, 0, \"div\", 37);\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_33_div_3_Template, 5, 5, \"div\", 38);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isSearching && ctx_r3.searchResults.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isSearching && ctx_r3.searchResults.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.isSearching);\n  }\n}\nfunction MessageLayoutComponent_div_34_div_1_div_3_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 61);\n  }\n}\nfunction MessageLayoutComponent_div_34_div_1_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 10);\n    i0.ɵɵelement(2, \"img\", 43);\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_34_div_1_div_3_div_1_div_3_Template, 1, 0, \"div\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 44)(5, \"h4\", 45);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 46);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 71);\n    i0.ɵɵelement(10, \"i\", 72);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const result_r33 = i0.ɵɵnextContext().$implicit;\n    const ctx_r34 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", result_r33.image || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", result_r33.username);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r34.isUserOnline(result_r33));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", result_r33.username, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(result_r33.email);\n  }\n}\nfunction MessageLayoutComponent_div_34_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r38 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵlistener(\"click\", function MessageLayoutComponent_div_34_div_1_div_3_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r38);\n      const result_r33 = restoredCtx.$implicit;\n      const ctx_r37 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r37.isUser(result_r33) ? ctx_r37.startConversationWithUser(result_r33) : null);\n    });\n    i0.ɵɵtemplate(1, MessageLayoutComponent_div_34_div_1_div_3_div_1_Template, 11, 5, \"div\", 70);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const result_r33 = ctx.$implicit;\n    const ctx_r32 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r32.isUser(result_r33));\n  }\n}\nfunction MessageLayoutComponent_div_34_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"div\", 40);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_34_div_1_div_3_Template, 2, 1, \"div\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" R\\u00E9sultats de recherche (\", ctx_r29.searchResults.length, \") \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r29.searchResults);\n  }\n}\nfunction MessageLayoutComponent_div_34_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵelement(1, \"i\", 48);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Aucun utilisateur trouv\\u00E9\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageLayoutComponent_div_34_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵelement(1, \"div\", 53);\n    i0.ɵɵelementStart(2, \"p\", 54);\n    i0.ɵɵtext(3, \"Chargement des utilisateurs...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageLayoutComponent_div_34_div_3_div_2_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 61);\n  }\n}\nfunction MessageLayoutComponent_div_34_div_3_div_2_p_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 78);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r43 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", user_r43.role, \" \");\n  }\n}\nfunction MessageLayoutComponent_div_34_div_3_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵlistener(\"click\", function MessageLayoutComponent_div_34_div_3_div_2_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r48);\n      const user_r43 = restoredCtx.$implicit;\n      const ctx_r47 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r47.startConversationWithUser(user_r43));\n    });\n    i0.ɵɵelementStart(1, \"div\", 4)(2, \"div\", 10);\n    i0.ɵɵelement(3, \"img\", 43);\n    i0.ɵɵtemplate(4, MessageLayoutComponent_div_34_div_3_div_2_div_4_Template, 1, 0, \"div\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 44)(6, \"h4\", 45);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 46);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, MessageLayoutComponent_div_34_div_3_div_2_p_10_Template, 2, 1, \"p\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 75)(12, \"div\", 76);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 77);\n    i0.ɵɵelement(15, \"i\", 72);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const user_r43 = ctx.$implicit;\n    const ctx_r40 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", user_r43.image || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", user_r43.username);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r40.isUserOnline(user_r43));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", user_r43.username, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r43.email);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", user_r43.role);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"bg-green-600\", ctx_r40.isUserOnline(user_r43))(\"text-green-100\", ctx_r40.isUserOnline(user_r43))(\"bg-gray-600\", !ctx_r40.isUserOnline(user_r43))(\"text-gray-300\", !ctx_r40.isUserOnline(user_r43));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r40.isUserOnline(user_r43) ? \"En ligne\" : \"Hors ligne\", \" \");\n  }\n}\nfunction MessageLayoutComponent_div_34_div_3_div_3_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Charger plus\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageLayoutComponent_div_34_div_3_div_3_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Chargement...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageLayoutComponent_div_34_div_3_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r52 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function MessageLayoutComponent_div_34_div_3_div_3_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r52);\n      const ctx_r51 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r51.loadMoreUsers());\n    });\n    i0.ɵɵtemplate(2, MessageLayoutComponent_div_34_div_3_div_3_span_2_Template, 2, 0, \"span\", 38);\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_34_div_3_div_3_span_3_Template, 2, 0, \"span\", 38);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r41 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r41.isLoadingUsers);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r41.isLoadingUsers);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r41.isLoadingUsers);\n  }\n}\nfunction MessageLayoutComponent_div_34_div_3_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵelement(1, \"i\", 79);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Aucun utilisateur trouv\\u00E9\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageLayoutComponent_div_34_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, MessageLayoutComponent_div_34_div_3_div_1_Template, 4, 0, \"div\", 49);\n    i0.ɵɵtemplate(2, MessageLayoutComponent_div_34_div_3_div_2_Template, 16, 15, \"div\", 73);\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_34_div_3_div_3_Template, 4, 3, \"div\", 51);\n    i0.ɵɵtemplate(4, MessageLayoutComponent_div_34_div_3_div_4_Template, 4, 0, \"div\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r31 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r31.isLoadingUsers && ctx_r31.users.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r31.users)(\"ngForTrackBy\", ctx_r31.trackByUserId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r31.hasMoreUsers);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r31.users.length === 0 && !ctx_r31.isLoadingUsers);\n  }\n}\nfunction MessageLayoutComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵtemplate(1, MessageLayoutComponent_div_34_div_1_Template, 4, 2, \"div\", 36);\n    i0.ɵɵtemplate(2, MessageLayoutComponent_div_34_div_2_Template, 4, 0, \"div\", 37);\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_34_div_3_Template, 5, 5, \"div\", 38);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isSearching && ctx_r4.searchResults.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isSearching && ctx_r4.searchResults.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.isSearching);\n  }\n}\nfunction MessageLayoutComponent_div_35_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵelement(1, \"div\", 53);\n    i0.ɵɵelementStart(2, \"p\", 54);\n    i0.ɵɵtext(3, \"Chargement des notifications...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageLayoutComponent_div_35_div_2_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 89);\n  }\n}\nfunction MessageLayoutComponent_div_35_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r59 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 82);\n    i0.ɵɵlistener(\"click\", function MessageLayoutComponent_div_35_div_2_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r59);\n      const notification_r56 = restoredCtx.$implicit;\n      const ctx_r58 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r58.markNotificationAsRead(notification_r56));\n    });\n    i0.ɵɵelementStart(1, \"div\", 83)(2, \"div\", 84);\n    i0.ɵɵelement(3, \"i\", 85);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 44)(5, \"h4\", 45);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 86);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 87);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, MessageLayoutComponent_div_35_div_2_div_11_Template, 1, 0, \"div\", 88);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const notification_r56 = ctx.$implicit;\n    const ctx_r54 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"bg-gray-700\", !notification_r56.isRead);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"bg-blue-600\", notification_r56.type === \"NEW_MESSAGE\")(\"bg-green-600\", notification_r56.type === \"FRIEND_REQUEST\")(\"bg-yellow-600\", notification_r56.type === \"GROUP_INVITE\")(\"bg-purple-600\", notification_r56.type === \"MESSAGE_REACTION\")(\"bg-red-600\", notification_r56.type === \"SYSTEM_ALERT\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"fa-message\", notification_r56.type === \"NEW_MESSAGE\")(\"fa-user-plus\", notification_r56.type === \"FRIEND_REQUEST\")(\"fa-users\", notification_r56.type === \"GROUP_INVITE\")(\"fa-heart\", notification_r56.type === \"MESSAGE_REACTION\")(\"fa-exclamation-triangle\", notification_r56.type === \"SYSTEM_ALERT\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r54.getNotificationTitle(notification_r56), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", notification_r56.content, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r54.formatLastMessageTime(notification_r56.timestamp), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !notification_r56.isRead);\n  }\n}\nfunction MessageLayoutComponent_div_35_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵelement(1, \"i\", 90);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Aucune notification\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 66);\n    i0.ɵɵtext(5, \" Vous serez notifi\\u00E9 des nouveaux messages et \\u00E9v\\u00E9nements \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageLayoutComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 80);\n    i0.ɵɵtemplate(1, MessageLayoutComponent_div_35_div_1_Template, 4, 0, \"div\", 49);\n    i0.ɵɵtemplate(2, MessageLayoutComponent_div_35_div_2_Template, 12, 26, \"div\", 81);\n    i0.ɵɵtemplate(3, MessageLayoutComponent_div_35_div_3_Template, 6, 0, \"div\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isLoadingNotifications && ctx_r5.notifications.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.notifications)(\"ngForTrackBy\", ctx_r5.trackByNotificationId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.notifications.length === 0 && !ctx_r5.isLoadingNotifications);\n  }\n}\nexport class MessageLayoutComponent {\n  constructor(messageService, authService, toastService, route, router, cdr) {\n    this.messageService = messageService;\n    this.authService = authService;\n    this.toastService = toastService;\n    this.route = route;\n    this.router = router;\n    this.cdr = cdr;\n    // État du composant\n    this.currentUser = null;\n    this.conversations = [];\n    this.users = [];\n    this.notifications = [];\n    // Navigation et UI\n    this.activeTab = 'conversations';\n    this.selectedConversationId = null;\n    this.isMobileMenuOpen = false;\n    this.isSearching = false;\n    // Recherche\n    this.searchQuery = '';\n    this.searchResults = [];\n    // États de chargement\n    this.isLoadingConversations = false;\n    this.isLoadingUsers = false;\n    this.isLoadingNotifications = false;\n    // Pagination\n    this.conversationsPage = 1;\n    this.usersPage = 1;\n    this.hasMoreConversations = true;\n    this.hasMoreUsers = true;\n    // Subscriptions\n    this.subscriptions = [];\n    // Observables\n    this.searchQuery$ = new BehaviorSubject('');\n  }\n  ngOnInit() {\n    this.initializeComponent();\n    this.setupSubscriptions();\n    this.loadInitialData();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  // ============================================================================\n  // MÉTHODES D'INITIALISATION\n  // ============================================================================\n  initializeComponent() {\n    // Récupérer l'utilisateur actuel\n    this.currentUser = this.authService.getCurrentUser();\n    if (!this.currentUser) {\n      this.router.navigate(['/login']);\n      return;\n    }\n    // Écouter les changements de route\n    this.route.params.subscribe(params => {\n      const conversationId = params['conversationId'];\n      if (conversationId) {\n        this.selectedConversationId = conversationId;\n        this.markConversationAsSelected(conversationId);\n      }\n    });\n  }\n  setupSubscriptions() {\n    // Subscription pour les nouveaux messages\n    const messagesSub = this.messageService.subscribeToMessages().subscribe(message => {\n      if (message) {\n        this.handleNewMessage(message);\n      }\n    });\n    // Subscription pour les notifications\n    const notificationsSub = this.messageService.subscribeToNotifications().subscribe(notification => {\n      if (notification) {\n        this.handleNewNotification(notification);\n      }\n    });\n    // Subscription pour la recherche\n    const searchSub = this.searchQuery$.pipe().subscribe(query => {\n      this.performSearch(query);\n    });\n    this.subscriptions.push(messagesSub, notificationsSub, searchSub);\n  }\n  loadInitialData() {\n    this.loadConversations();\n    this.loadUsers();\n    this.loadNotifications();\n  }\n  // ============================================================================\n  // MÉTHODES DE CHARGEMENT DES DONNÉES\n  // ============================================================================\n  loadConversations(page = 1) {\n    if (this.isLoadingConversations) return;\n    this.isLoadingConversations = true;\n    this.messageService.getConversations(25, page).subscribe({\n      next: conversations => {\n        if (page === 1) {\n          this.conversations = conversations;\n        } else {\n          this.conversations.push(...conversations);\n        }\n        this.conversationsPage = page;\n        this.hasMoreConversations = conversations.length === 25;\n        this.isLoadingConversations = false;\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des conversations:', error);\n        this.isLoadingConversations = false;\n        this.toastService.showError('Erreur lors du chargement des conversations');\n      }\n    });\n  }\n  loadUsers(page = 1) {\n    if (this.isLoadingUsers) return;\n    this.isLoadingUsers = true;\n    this.messageService.getAllUsers(false, '', page, 25).subscribe({\n      next: users => {\n        if (page === 1) {\n          this.users = users;\n        } else {\n          this.users.push(...users);\n        }\n        this.usersPage = page;\n        this.hasMoreUsers = users.length === 25;\n        this.isLoadingUsers = false;\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des utilisateurs:', error);\n        this.isLoadingUsers = false;\n        this.toastService.showError('Erreur lors du chargement des utilisateurs');\n      }\n    });\n  }\n  loadNotifications() {\n    if (this.isLoadingNotifications) return;\n    this.isLoadingNotifications = true;\n    this.messageService.getNotifications().subscribe({\n      next: notifications => {\n        this.notifications = notifications;\n        this.isLoadingNotifications = false;\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.error('Erreur lors du chargement des notifications:', error);\n        this.isLoadingNotifications = false;\n        this.toastService.showError('Erreur lors du chargement des notifications');\n      }\n    });\n  }\n  // ============================================================================\n  // MÉTHODES DE GESTION DES ÉVÉNEMENTS\n  // ============================================================================\n  handleNewMessage(message) {\n    // Mettre à jour la conversation correspondante\n    const conversationIndex = this.conversations.findIndex(conv => conv.id === message.conversationId);\n    if (conversationIndex !== -1) {\n      // Mettre à jour le dernier message\n      this.conversations[conversationIndex].lastMessage = message;\n      // Déplacer la conversation en haut de la liste\n      const conversation = this.conversations.splice(conversationIndex, 1)[0];\n      this.conversations.unshift(conversation);\n      this.cdr.detectChanges();\n    }\n  }\n  handleNewNotification(notification) {\n    // Ajouter la nouvelle notification en haut de la liste\n    this.notifications.unshift(notification);\n    this.cdr.detectChanges();\n    // Afficher une notification toast si ce n'est pas l'onglet actif\n    if (this.activeTab !== 'notifications') {\n      this.toastService.showInfo('Nouvelle notification reçue');\n    }\n  }\n  markConversationAsSelected(conversationId) {\n    // Marquer la conversation comme sélectionnée visuellement\n    this.selectedConversationId = conversationId;\n    this.cdr.detectChanges();\n  }\n  // ============================================================================\n  // MÉTHODES DE NAVIGATION ET UI\n  // ============================================================================\n  switchTab(tab) {\n    this.activeTab = tab;\n    this.searchQuery = '';\n    this.searchResults = [];\n    this.isSearching = false;\n    // Charger les données si nécessaire\n    switch (tab) {\n      case 'conversations':\n        if (this.conversations.length === 0) {\n          this.loadConversations();\n        }\n        break;\n      case 'users':\n        if (this.users.length === 0) {\n          this.loadUsers();\n        }\n        break;\n      case 'notifications':\n        if (this.notifications.length === 0) {\n          this.loadNotifications();\n        }\n        break;\n    }\n  }\n  selectConversation(conversation) {\n    if (!conversation.id) return;\n    this.selectedConversationId = conversation.id;\n    this.router.navigate(['/messages', conversation.id]);\n    // Fermer le menu mobile si ouvert\n    this.isMobileMenuOpen = false;\n  }\n  startConversationWithUser(user) {\n    if (!user.id && !user._id) return;\n    const userId = user.id || user._id;\n    // Créer ou récupérer la conversation avec cet utilisateur\n    this.messageService.createOrGetConversation(userId).subscribe({\n      next: conversation => {\n        this.selectConversation(conversation);\n      },\n      error: error => {\n        console.error('Erreur lors de la création de la conversation:', error);\n        this.toastService.showError('Erreur lors de la création de la conversation');\n      }\n    });\n  }\n  toggleMobileMenu() {\n    this.isMobileMenuOpen = !this.isMobileMenuOpen;\n  }\n  // ============================================================================\n  // MÉTHODES DE RECHERCHE\n  // ============================================================================\n  onSearchInput(event) {\n    const query = event.target.value.trim();\n    this.searchQuery = query;\n    this.searchQuery$.next(query);\n  }\n  performSearch(query) {\n    if (!query) {\n      this.searchResults = [];\n      this.isSearching = false;\n      return;\n    }\n    this.isSearching = true;\n    if (this.activeTab === 'conversations') {\n      this.searchResults = this.conversations.filter(conv => conv.isGroup ? conv.groupName?.toLowerCase().includes(query.toLowerCase()) : conv.participants?.some(p => p.username?.toLowerCase().includes(query.toLowerCase())));\n    } else if (this.activeTab === 'users') {\n      this.searchResults = this.users.filter(user => user.username?.toLowerCase().includes(query.toLowerCase()) || user.email?.toLowerCase().includes(query.toLowerCase()));\n    }\n    this.cdr.detectChanges();\n  }\n  clearSearch() {\n    this.searchQuery = '';\n    this.searchResults = [];\n    this.isSearching = false;\n    this.searchQuery$.next('');\n  }\n  // ============================================================================\n  // MÉTHODES DE PAGINATION\n  // ============================================================================\n  loadMoreConversations() {\n    if (this.hasMoreConversations && !this.isLoadingConversations) {\n      this.loadConversations(this.conversationsPage + 1);\n    }\n  }\n  loadMoreUsers() {\n    if (this.hasMoreUsers && !this.isLoadingUsers) {\n      this.loadUsers(this.usersPage + 1);\n    }\n  }\n  // ============================================================================\n  // MÉTHODES UTILITAIRES POUR LE TEMPLATE\n  // ============================================================================\n  getConversationName(conversation) {\n    if (conversation.isGroup) {\n      return conversation.groupName || 'Groupe sans nom';\n    }\n    if (!this.currentUser) return 'Conversation';\n    const currentUserId = this.currentUser.id || this.currentUser._id;\n    const otherParticipant = conversation.participants?.find(p => (p.id || p._id) !== currentUserId);\n    return otherParticipant?.username || 'Utilisateur inconnu';\n  }\n  getConversationAvatar(conversation) {\n    if (conversation.isGroup) {\n      return conversation.groupPhoto || '/assets/images/default-group.png';\n    }\n    if (!this.currentUser) return '/assets/images/default-avatar.png';\n    const currentUserId = this.currentUser.id || this.currentUser._id;\n    const otherParticipant = conversation.participants?.find(p => (p.id || p._id) !== currentUserId);\n    return otherParticipant?.image || '/assets/images/default-avatar.png';\n  }\n  getLastMessagePreview(conversation) {\n    if (!conversation.lastMessage) return 'Aucun message';\n    const message = conversation.lastMessage;\n    if (message.type === 'TEXT') {\n      return message.content || '';\n    } else if (message.type === 'IMAGE') {\n      return '📷 Image';\n    } else if (message.type === 'FILE') {\n      return '📎 Fichier';\n    } else if (message.type === 'VOICE_MESSAGE') {\n      return '🎤 Message vocal';\n    } else if (message.type === 'VIDEO') {\n      return '🎥 Vidéo';\n    }\n    return 'Message';\n  }\n  formatLastMessageTime(timestamp) {\n    if (!timestamp) return '';\n    const date = new Date(timestamp);\n    const now = new Date();\n    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n    if (diffInHours < 1) {\n      return \"À l'instant\";\n    } else if (diffInHours < 24) {\n      return date.toLocaleTimeString('fr-FR', {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    } else if (diffInHours < 168) {\n      // 7 jours\n      return date.toLocaleDateString('fr-FR', {\n        weekday: 'short'\n      });\n    } else {\n      return date.toLocaleDateString('fr-FR', {\n        day: '2-digit',\n        month: '2-digit'\n      });\n    }\n  }\n  getUnreadCount(conversation) {\n    return conversation.unreadCount || 0;\n  }\n  isUserOnline(user) {\n    return user.isOnline || false;\n  }\n  trackByConversationId(index, conversation) {\n    return conversation.id || conversation._id || index.toString();\n  }\n  trackByUserId(index, user) {\n    return user.id || user._id || index.toString();\n  }\n  trackByNotificationId(index, notification) {\n    return notification.id || notification._id || index.toString();\n  }\n  markNotificationAsRead(notification) {\n    if (!notification.id || notification.isRead) return;\n    this.messageService.markNotificationAsRead(notification.id).subscribe({\n      next: () => {\n        notification.isRead = true;\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.error('Erreur lors du marquage de la notification comme lue:', error);\n        this.toastService.showError('Erreur lors du marquage de la notification');\n      }\n    });\n  }\n  // Type guards pour différencier User et Conversation dans les résultats de recherche\n  isUser(item) {\n    return 'username' in item && 'email' in item;\n  }\n  isConversation(item) {\n    return 'participants' in item || 'isGroup' in item;\n  }\n  getNotificationTitle(notification) {\n    switch (notification.type) {\n      case 'NEW_MESSAGE':\n        return 'Nouveau message';\n      case 'FRIEND_REQUEST':\n        return \"Demande d'ami\";\n      case 'GROUP_INVITE':\n        return 'Invitation de groupe';\n      case 'MESSAGE_REACTION':\n        return 'Réaction à un message';\n      case 'SYSTEM_ALERT':\n        return 'Alerte système';\n      default:\n        return 'Notification';\n    }\n  }\n  static {\n    this.ɵfac = function MessageLayoutComponent_Factory(t) {\n      return new (t || MessageLayoutComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.ToastService), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MessageLayoutComponent,\n      selectors: [[\"app-message-layout\"]],\n      viewQuery: function MessageLayoutComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.searchInput = _t.first);\n        }\n      },\n      decls: 42,\n      vars: 43,\n      consts: [[1, \"message-layout\", \"h-screen\", \"bg-gray-900\", \"text-white\", \"flex\"], [1, \"sidebar\", \"w-80\", \"bg-gray-800\", \"border-r\", \"border-gray-700\", \"flex\", \"flex-col\"], [1, \"sidebar-header\", \"p-4\", \"border-b\", \"border-gray-700\", \"bg-gray-800\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mb-4\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [1, \"w-10\", \"h-10\", \"rounded-full\", \"border-2\", \"border-blue-500\", 3, \"src\", \"alt\"], [1, \"font-semibold\", \"text-white\"], [1, \"text-sm\", \"text-green-400\"], [1, \"md:hidden\", \"p-2\", \"rounded-lg\", \"bg-gray-700\", \"hover:bg-gray-600\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"text-white\"], [1, \"relative\"], [\"type\", \"text\", \"placeholder\", \"Rechercher...\", 1, \"w-full\", \"bg-gray-700\", \"border\", \"border-gray-600\", \"rounded-lg\", \"px-4\", \"py-2\", \"pl-10\", \"text-white\", \"placeholder-gray-400\", \"focus:outline-none\", \"focus:border-blue-500\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [\"searchInput\", \"\"], [1, \"fas\", \"fa-search\", \"absolute\", \"left-3\", \"top-3\", \"text-gray-400\"], [\"class\", \"absolute right-3 top-3 text-gray-400 hover:text-white\", 3, \"click\", 4, \"ngIf\"], [1, \"tabs\", \"flex\", \"border-b\", \"border-gray-700\"], [1, \"tab\", \"flex-1\", \"py-3\", \"px-4\", \"text-center\", \"transition-all\", \"duration-200\", 3, \"click\"], [1, \"fas\", \"fa-comments\", \"mb-1\"], [1, \"text-xs\"], [1, \"fas\", \"fa-users\", \"mb-1\"], [1, \"tab\", \"flex-1\", \"py-3\", \"px-4\", \"text-center\", \"transition-all\", \"duration-200\", \"relative\", 3, \"click\"], [1, \"fas\", \"fa-bell\", \"mb-1\"], [\"class\", \"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\", 4, \"ngIf\"], [1, \"sidebar-content\", \"flex-1\", \"overflow-y-auto\"], [\"class\", \"conversations-list\", 4, \"ngIf\"], [\"class\", \"users-list\", 4, \"ngIf\"], [\"class\", \"notifications-list\", 4, \"ngIf\"], [1, \"main-content\", \"flex-1\", \"flex\", \"flex-col\"], [1, \"md:hidden\", \"p-4\", \"border-b\", \"border-gray-700\", \"bg-gray-800\"], [1, \"p-2\", \"rounded-lg\", \"bg-gray-700\", \"hover:bg-gray-600\", 3, \"click\"], [1, \"fas\", \"fa-bars\", \"text-white\"], [1, \"flex-1\"], [1, \"absolute\", \"right-3\", \"top-3\", \"text-gray-400\", \"hover:text-white\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"absolute\", \"-top-1\", \"-right-1\", \"bg-red-500\", \"text-white\", \"text-xs\", \"rounded-full\", \"w-5\", \"h-5\", \"flex\", \"items-center\", \"justify-center\"], [1, \"conversations-list\"], [\"class\", \"search-results\", 4, \"ngIf\"], [\"class\", \"p-8 text-center text-gray-400\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"search-results\"], [1, \"p-3\", \"text-sm\", \"text-gray-400\", \"border-b\", \"border-gray-700\"], [\"class\", \"conversation-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"conversation-item\", \"p-4\", \"hover:bg-gray-700\", \"cursor-pointer\", \"border-b\", \"border-gray-700\", \"transition-colors\", 3, \"click\"], [1, \"w-12\", \"h-12\", \"rounded-full\", 3, \"src\", \"alt\"], [1, \"flex-1\", \"min-w-0\"], [1, \"font-medium\", \"text-white\", \"truncate\"], [1, \"text-sm\", \"text-gray-400\", \"truncate\"], [1, \"p-8\", \"text-center\", \"text-gray-400\"], [1, \"fas\", \"fa-search\", \"text-4xl\", \"mb-4\"], [\"class\", \"p-8 text-center\", 4, \"ngIf\"], [\"class\", \"conversation-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors relative\", 3, \"bg-gray-700\", \"border-l-4\", \"border-blue-500\", \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"p-4 text-center\", 4, \"ngIf\"], [1, \"p-8\", \"text-center\"], [1, \"animate-spin\", \"rounded-full\", \"h-8\", \"w-8\", \"border-b-2\", \"border-blue-500\", \"mx-auto\"], [1, \"text-gray-400\", \"mt-2\"], [1, \"conversation-item\", \"p-4\", \"hover:bg-gray-700\", \"cursor-pointer\", \"border-b\", \"border-gray-700\", \"transition-colors\", \"relative\", 3, \"click\"], [\"class\", \"absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-gray-800\", 4, \"ngIf\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"text-xs\", \"text-gray-400\"], [1, \"flex\", \"items-center\", \"justify-between\", \"mt-1\"], [\"class\", \"bg-blue-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center\", 4, \"ngIf\"], [1, \"absolute\", \"bottom-0\", \"right-0\", \"w-3\", \"h-3\", \"bg-green-500\", \"rounded-full\", \"border-2\", \"border-gray-800\"], [1, \"bg-blue-500\", \"text-white\", \"text-xs\", \"rounded-full\", \"px-2\", \"py-1\", \"min-w-[20px]\", \"text-center\"], [1, \"p-4\", \"text-center\"], [1, \"text-blue-400\", \"hover:text-blue-300\", \"disabled:text-gray-500\", 3, \"disabled\", \"click\"], [1, \"fas\", \"fa-comments\", \"text-4xl\", \"mb-4\"], [1, \"text-sm\", \"mt-2\"], [1, \"users-list\"], [\"class\", \"user-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"user-item\", \"p-4\", \"hover:bg-gray-700\", \"cursor-pointer\", \"border-b\", \"border-gray-700\", \"transition-colors\", 3, \"click\"], [\"class\", \"flex items-center space-x-3\", 4, \"ngIf\"], [1, \"text-blue-400\"], [1, \"fas\", \"fa-comment\"], [\"class\", \"user-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"text-xs text-gray-500\", 4, \"ngIf\"], [1, \"text-right\"], [1, \"text-xs\", \"px-2\", \"py-1\", \"rounded-full\"], [1, \"text-blue-400\", \"mt-1\"], [1, \"text-xs\", \"text-gray-500\"], [1, \"fas\", \"fa-users\", \"text-4xl\", \"mb-4\"], [1, \"notifications-list\"], [\"class\", \"notification-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors\", 3, \"bg-gray-700\", \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"notification-item\", \"p-4\", \"hover:bg-gray-700\", \"cursor-pointer\", \"border-b\", \"border-gray-700\", \"transition-colors\", 3, \"click\"], [1, \"flex\", \"items-start\", \"space-x-3\"], [1, \"notification-icon\", \"p-2\", \"rounded-full\"], [1, \"text-white\", \"text-sm\"], [1, \"text-sm\", \"text-gray-400\", \"mt-1\"], [1, \"text-xs\", \"text-gray-500\", \"mt-2\"], [\"class\", \"w-2 h-2 bg-blue-500 rounded-full\", 4, \"ngIf\"], [1, \"w-2\", \"h-2\", \"bg-blue-500\", \"rounded-full\"], [1, \"fas\", \"fa-bell\", \"text-4xl\", \"mb-4\"]],\n      template: function MessageLayoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4);\n          i0.ɵɵelement(5, \"img\", 5);\n          i0.ɵɵelementStart(6, \"div\")(7, \"h3\", 6);\n          i0.ɵɵtext(8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"p\", 7);\n          i0.ɵɵtext(10, \"En ligne\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function MessageLayoutComponent_Template_button_click_11_listener() {\n            return ctx.toggleMobileMenu();\n          });\n          i0.ɵɵelement(12, \"i\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 10)(14, \"input\", 11, 12);\n          i0.ɵɵlistener(\"ngModelChange\", function MessageLayoutComponent_Template_input_ngModelChange_14_listener($event) {\n            return ctx.searchQuery = $event;\n          })(\"input\", function MessageLayoutComponent_Template_input_input_14_listener($event) {\n            return ctx.onSearchInput($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(16, \"i\", 13);\n          i0.ɵɵtemplate(17, MessageLayoutComponent_button_17_Template, 2, 0, \"button\", 14);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 15)(19, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function MessageLayoutComponent_Template_button_click_19_listener() {\n            return ctx.switchTab(\"conversations\");\n          });\n          i0.ɵɵelement(20, \"i\", 17);\n          i0.ɵɵelementStart(21, \"div\", 18);\n          i0.ɵɵtext(22, \"Discussions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function MessageLayoutComponent_Template_button_click_23_listener() {\n            return ctx.switchTab(\"users\");\n          });\n          i0.ɵɵelement(24, \"i\", 19);\n          i0.ɵɵelementStart(25, \"div\", 18);\n          i0.ɵɵtext(26, \"Contacts\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function MessageLayoutComponent_Template_button_click_27_listener() {\n            return ctx.switchTab(\"notifications\");\n          });\n          i0.ɵɵelement(28, \"i\", 21);\n          i0.ɵɵelementStart(29, \"div\", 18);\n          i0.ɵɵtext(30, \"Notifications\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(31, MessageLayoutComponent_span_31_Template, 2, 1, \"span\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 23);\n          i0.ɵɵtemplate(33, MessageLayoutComponent_div_33_Template, 4, 3, \"div\", 24);\n          i0.ɵɵtemplate(34, MessageLayoutComponent_div_34_Template, 4, 3, \"div\", 25);\n          i0.ɵɵtemplate(35, MessageLayoutComponent_div_35_Template, 4, 4, \"div\", 26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"div\", 27)(37, \"div\", 28)(38, \"button\", 29);\n          i0.ɵɵlistener(\"click\", function MessageLayoutComponent_Template_button_click_38_listener() {\n            return ctx.toggleMobileMenu();\n          });\n          i0.ɵɵelement(39, \"i\", 30);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"div\", 31);\n          i0.ɵɵelement(41, \"router-outlet\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"hidden\", !ctx.isMobileMenuOpen)(\"md:flex\", true);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"src\", (ctx.currentUser == null ? null : ctx.currentUser.image) || \"/assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", ctx.currentUser == null ? null : ctx.currentUser.username);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.currentUser == null ? null : ctx.currentUser.username, \" \");\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.searchQuery);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.activeTab === \"conversations\")(\"text-blue-400\", ctx.activeTab === \"conversations\")(\"border-b-2\", ctx.activeTab === \"conversations\")(\"border-blue-500\", ctx.activeTab === \"conversations\")(\"text-gray-400\", ctx.activeTab !== \"conversations\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"active\", ctx.activeTab === \"users\")(\"text-blue-400\", ctx.activeTab === \"users\")(\"border-b-2\", ctx.activeTab === \"users\")(\"border-blue-500\", ctx.activeTab === \"users\")(\"text-gray-400\", ctx.activeTab !== \"users\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"active\", ctx.activeTab === \"notifications\")(\"text-blue-400\", ctx.activeTab === \"notifications\")(\"border-b-2\", ctx.activeTab === \"notifications\")(\"border-blue-500\", ctx.activeTab === \"notifications\")(\"text-gray-400\", ctx.activeTab !== \"notifications\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.notifications.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"conversations\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"users\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"notifications\");\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i4.RouterOutlet, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgModel],\n      styles: [\"\\n\\n\\n\\n\\n.message-layout[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n  font-family: \\\"Inter\\\", -apple-system, BlinkMacSystemFont, sans-serif;\\n}\\n\\n\\n\\n\\n\\n\\n.sidebar[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 20rem;\\n  flex-direction: column;\\n  border-right-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\\n  background: linear-gradient(180deg, #1f2937 0%, #111827 100%);\\n  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);\\n}\\n\\n.sidebar-header[_ngcontent-%COMP%] {\\n  border-bottom-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\\n  padding: 1rem;\\n  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);\\n}\\n\\n\\n\\n\\n\\n\\n.tabs[_ngcontent-%COMP%] {\\n  display: flex;\\n  border-bottom-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\\n  background: linear-gradient(135deg, #374151 0%, #1f2937 100%);\\n}\\n\\n.tab[_ngcontent-%COMP%] {\\n  flex: 1 1 0%;\\n  cursor: pointer;\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n  text-align: center;\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n  position: relative;\\n}\\n\\n.tab[_ngcontent-%COMP%]:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n}\\n\\n.conversation-item.tab[_ngcontent-%COMP%]:hover {\\n  border-left-width: 4px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\\n  background: linear-gradient(135deg, #1e3a8a 0%, #1d4ed8 100%);\\n  box-shadow: 0 2px 10px rgba(59, 130, 246, 0.2);\\n}\\n\\n.tab.active[_ngcontent-%COMP%] {\\n  border-bottom-width: 2px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\\n  --tw-text-opacity: 1;\\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\\n  background: linear-gradient(135deg, #1e3a8a 0%, #1d4ed8 100%);\\n  box-shadow: 0 2px 10px rgba(59, 130, 246, 0.3);\\n}\\n\\n.tab[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-bottom: 0.25rem;\\n  display: block;\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\n\\n\\n\\n\\n\\n\\n.sidebar-content[_ngcontent-%COMP%] {\\n  flex: 1 1 0%;\\n  overflow-y: auto;\\n  scrollbar-width: thin;\\n  scrollbar-color: #374151 #1f2937;\\n}\\n\\n.sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n.sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #1f2937;\\n}\\n\\n.sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #374151;\\n  border-radius: 3px;\\n}\\n\\n.sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #4b5563;\\n}\\n\\n\\n\\n\\n\\n\\n.conversation-item[_ngcontent-%COMP%], .user-item[_ngcontent-%COMP%], .notification-item[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  border-bottom-width: 1px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\\n  padding: 1rem;\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 200ms;\\n}\\n\\n.conversation-item[_ngcontent-%COMP%]:hover, .user-item[_ngcontent-%COMP%]:hover, .notification-item[_ngcontent-%COMP%]:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\\n}\\n\\n.conversation-item[_ngcontent-%COMP%], .user-item[_ngcontent-%COMP%], .notification-item[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.conversation-item[_ngcontent-%COMP%]:hover, .user-item[_ngcontent-%COMP%]:hover, .notification-item[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #374151 0%, #1f2937 100%);\\n  transform: translateX(2px);\\n}\\n\\n.conversation-item.bg-gray-700[_ngcontent-%COMP%] {\\n  border-left-width: 4px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\\n  background: linear-gradient(135deg, #1e3a8a 0%, #1d4ed8 100%);\\n  box-shadow: 0 2px 10px rgba(59, 130, 246, 0.2);\\n}\\n\\n\\n\\n\\n\\n\\n.user-avatar[_ngcontent-%COMP%], .conversation-avatar[_ngcontent-%COMP%] {\\n  height: 3rem;\\n  width: 3rem;\\n  border-radius: 9999px;\\n  border-width: 2px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\\n  transition: all 0.2s ease;\\n}\\n\\n.user-avatar[_ngcontent-%COMP%]:hover, .conversation-avatar[_ngcontent-%COMP%]:hover {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\\n  box-shadow: 0 0 15px rgba(59, 130, 246, 0.4);\\n}\\n\\n.online-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 0px;\\n  right: 0px;\\n  height: 0.75rem;\\n  width: 0.75rem;\\n  border-radius: 9999px;\\n  border-width: 2px;\\n  --tw-border-opacity: 1;\\n  border-color: rgb(31 41 55 / var(--tw-border-opacity, 1));\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%,\\n  100% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0.7;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n.unread-badge[_ngcontent-%COMP%] {\\n  min-width: 20px;\\n  border-radius: 9999px;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n  text-align: center;\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\\n  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);\\n  animation: _ngcontent-%COMP%_badgePulse 2s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_badgePulse {\\n  0%,\\n  100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.05);\\n  }\\n}\\n\\n.notification-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -0.25rem;\\n  right: -0.25rem;\\n  display: flex;\\n  height: 1.25rem;\\n  width: 1.25rem;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 9999px;\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\\n  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.4);\\n  animation: _ngcontent-%COMP%_notificationPulse 1s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_notificationPulse {\\n  0%,\\n  100% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n  50% {\\n    transform: scale(1.1);\\n    opacity: 0.8;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n.main-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex: 1 1 0%;\\n  flex-direction: column;\\n  background: linear-gradient(180deg, #0f172a 0%, #111827 100%);\\n}\\n\\n\\n\\n\\n\\n\\n@media (max-width: 768px) {\\n  .sidebar[_ngcontent-%COMP%] {\\n    position: fixed;\\n    top: 0px;\\n    bottom: 0px;\\n    left: 0px;\\n    z-index: 50;\\n    width: 20rem;\\n    transform: translateX(-100%);\\n    transition: transform 0.3s ease-in-out;\\n  }\\n\\n  .sidebar.show[_ngcontent-%COMP%] {\\n    transform: translateX(0);\\n  }\\n\\n  .main-content[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "i0", "ɵɵelementStart", "ɵɵlistener", "MessageLayoutComponent_button_17_Template_button_click_0_listener", "ɵɵrestoreView", "_r7", "ctx_r6", "ɵɵnextContext", "ɵɵresetView", "clearSearch", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r2", "notifications", "length", "MessageLayoutComponent_div_33_div_1_div_3_Template_div_click_0_listener", "restoredCtx", "_r14", "result_r12", "$implicit", "ctx_r13", "selectConversation", "ɵɵproperty", "ctx_r11", "getConversationAvatar", "ɵɵsanitizeUrl", "getConversationName", "getLastMessagePreview", "ɵɵtemplate", "MessageLayoutComponent_div_33_div_1_div_3_Template", "ctx_r8", "searchResults", "ctx_r21", "getUnreadCount", "conversation_r19", "MessageLayoutComponent_div_33_div_3_div_2_Template_div_click_0_listener", "_r24", "ctx_r23", "MessageLayoutComponent_div_33_div_3_div_2_div_4_Template", "MessageLayoutComponent_div_33_div_3_div_2_span_14_Template", "ɵɵclassProp", "ctx_r16", "selectedConversationId", "id", "isGroup", "isUserOnline", "participants", "formatLastMessageTime", "lastMessage", "timestamp", "MessageLayoutComponent_div_33_div_3_div_3_Template_button_click_1_listener", "_r28", "ctx_r27", "loadMoreConversations", "MessageLayoutComponent_div_33_div_3_div_3_span_2_Template", "MessageLayoutComponent_div_33_div_3_div_3_span_3_Template", "ctx_r17", "isLoadingConversations", "MessageLayoutComponent_div_33_div_3_div_1_Template", "MessageLayoutComponent_div_33_div_3_div_2_Template", "MessageLayoutComponent_div_33_div_3_div_3_Template", "MessageLayoutComponent_div_33_div_3_div_4_Template", "ctx_r10", "conversations", "trackByConversationId", "hasMoreConversations", "MessageLayoutComponent_div_33_div_1_Template", "MessageLayoutComponent_div_33_div_2_Template", "MessageLayoutComponent_div_33_div_3_Template", "ctx_r3", "isSearching", "MessageLayoutComponent_div_34_div_1_div_3_div_1_div_3_Template", "result_r33", "image", "username", "ctx_r34", "ɵɵtextInterpolate", "email", "MessageLayoutComponent_div_34_div_1_div_3_Template_div_click_0_listener", "_r38", "ctx_r37", "isUser", "startConversationWithUser", "MessageLayoutComponent_div_34_div_1_div_3_div_1_Template", "ctx_r32", "MessageLayoutComponent_div_34_div_1_div_3_Template", "ctx_r29", "user_r43", "role", "MessageLayoutComponent_div_34_div_3_div_2_Template_div_click_0_listener", "_r48", "ctx_r47", "MessageLayoutComponent_div_34_div_3_div_2_div_4_Template", "MessageLayoutComponent_div_34_div_3_div_2_p_10_Template", "ctx_r40", "MessageLayoutComponent_div_34_div_3_div_3_Template_button_click_1_listener", "_r52", "ctx_r51", "loadMoreUsers", "MessageLayoutComponent_div_34_div_3_div_3_span_2_Template", "MessageLayoutComponent_div_34_div_3_div_3_span_3_Template", "ctx_r41", "isLoadingUsers", "MessageLayoutComponent_div_34_div_3_div_1_Template", "MessageLayoutComponent_div_34_div_3_div_2_Template", "MessageLayoutComponent_div_34_div_3_div_3_Template", "MessageLayoutComponent_div_34_div_3_div_4_Template", "ctx_r31", "users", "trackByUserId", "hasMoreUsers", "MessageLayoutComponent_div_34_div_1_Template", "MessageLayoutComponent_div_34_div_2_Template", "MessageLayoutComponent_div_34_div_3_Template", "ctx_r4", "MessageLayoutComponent_div_35_div_2_Template_div_click_0_listener", "_r59", "notification_r56", "ctx_r58", "markNotificationAsRead", "MessageLayoutComponent_div_35_div_2_div_11_Template", "isRead", "type", "ctx_r54", "getNotificationTitle", "content", "MessageLayoutComponent_div_35_div_1_Template", "MessageLayoutComponent_div_35_div_2_Template", "MessageLayoutComponent_div_35_div_3_Template", "ctx_r5", "isLoadingNotifications", "trackByNotificationId", "MessageLayoutComponent", "constructor", "messageService", "authService", "toastService", "route", "router", "cdr", "currentUser", "activeTab", "isMobileMenuOpen", "searchQuery", "conversationsPage", "usersPage", "subscriptions", "searchQuery$", "ngOnInit", "initializeComponent", "setupSubscriptions", "loadInitialData", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "getCurrentUser", "navigate", "params", "subscribe", "conversationId", "markConversationAsSelected", "messagesSub", "subscribeToMessages", "message", "handleNewMessage", "notificationsSub", "subscribeToNotifications", "notification", "handleNewNotification", "searchSub", "pipe", "query", "performSearch", "push", "loadConversations", "loadUsers", "loadNotifications", "page", "getConversations", "next", "detectChanges", "error", "console", "showError", "getAllUsers", "getNotifications", "conversationIndex", "findIndex", "conv", "conversation", "splice", "unshift", "showInfo", "switchTab", "tab", "user", "_id", "userId", "createOrGetConversation", "toggleMobileMenu", "onSearchInput", "event", "target", "value", "trim", "filter", "groupName", "toLowerCase", "includes", "some", "p", "currentUserId", "otherParticipant", "find", "groupPhoto", "date", "Date", "now", "diffInHours", "getTime", "toLocaleTimeString", "hour", "minute", "toLocaleDateString", "weekday", "day", "month", "unreadCount", "isOnline", "index", "toString", "item", "isConversation", "ɵɵdirectiveInject", "i1", "MessageService", "i2", "AuthService", "i3", "ToastService", "i4", "ActivatedRoute", "Router", "ChangeDetectorRef", "selectors", "viewQuery", "MessageLayoutComponent_Query", "rf", "ctx", "MessageLayoutComponent_Template_button_click_11_listener", "MessageLayoutComponent_Template_input_ngModelChange_14_listener", "$event", "MessageLayoutComponent_Template_input_input_14_listener", "MessageLayoutComponent_button_17_Template", "MessageLayoutComponent_Template_button_click_19_listener", "MessageLayoutComponent_Template_button_click_23_listener", "MessageLayoutComponent_Template_button_click_27_listener", "MessageLayoutComponent_span_31_Template", "MessageLayoutComponent_div_33_Template", "MessageLayoutComponent_div_34_Template", "MessageLayoutComponent_div_35_Template", "MessageLayoutComponent_Template_button_click_38_listener"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-layout\\message-layout.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-layout\\message-layout.component.html"], "sourcesContent": ["import {\n  Compo<PERSON>,\n  On<PERSON><PERSON>t,\n  On<PERSON><PERSON>roy,\n  ChangeDetectorRef,\n  ViewChild,\n  ElementRef,\n} from '@angular/core';\nimport { Subscription, BehaviorSubject } from 'rxjs';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { MessageService } from '../../../../services/message.service';\nimport { AuthService } from '../../../../services/auth.service';\nimport { ToastService } from '../../../../services/toast.service';\nimport {\n  User,\n  Conversation,\n  Message,\n  Notification,\n} from '../../../../models/message.model';\n\n@Component({\n  selector: 'app-message-layout',\n  templateUrl: './message-layout.component.html',\n  styleUrls: ['./message-layout.component.css'],\n})\nexport class MessageLayoutComponent implements OnInit, OnDestroy {\n  @ViewChild('searchInput') searchInput!: ElementRef;\n\n  // État du composant\n  currentUser: User | null = null;\n  conversations: Conversation[] = [];\n  users: User[] = [];\n  notifications: Notification[] = [];\n\n  // Navigation et UI\n  activeTab: 'conversations' | 'users' | 'notifications' = 'conversations';\n  selectedConversationId: string | null = null;\n  isMobileMenuOpen = false;\n  isSearching = false;\n\n  // Recherche\n  searchQuery = '';\n  searchResults: (Conversation | User)[] = [];\n\n  // États de chargement\n  isLoadingConversations = false;\n  isLoadingUsers = false;\n  isLoadingNotifications = false;\n\n  // Pagination\n  conversationsPage = 1;\n  usersPage = 1;\n  hasMoreConversations = true;\n  hasMoreUsers = true;\n\n  // Subscriptions\n  private subscriptions: Subscription[] = [];\n\n  // Observables\n  private searchQuery$ = new BehaviorSubject<string>('');\n\n  constructor(\n    private messageService: MessageService,\n    private authService: AuthService,\n    private toastService: ToastService,\n    private route: ActivatedRoute,\n    private router: Router,\n    private cdr: ChangeDetectorRef\n  ) {}\n\n  ngOnInit(): void {\n    this.initializeComponent();\n    this.setupSubscriptions();\n    this.loadInitialData();\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\n  }\n\n  // ============================================================================\n  // MÉTHODES D'INITIALISATION\n  // ============================================================================\n\n  private initializeComponent(): void {\n    // Récupérer l'utilisateur actuel\n    this.currentUser = this.authService.getCurrentUser();\n\n    if (!this.currentUser) {\n      this.router.navigate(['/login']);\n      return;\n    }\n\n    // Écouter les changements de route\n    this.route.params.subscribe((params) => {\n      const conversationId = params['conversationId'];\n      if (conversationId) {\n        this.selectedConversationId = conversationId;\n        this.markConversationAsSelected(conversationId);\n      }\n    });\n  }\n\n  private setupSubscriptions(): void {\n    // Subscription pour les nouveaux messages\n    const messagesSub = this.messageService\n      .subscribeToMessages()\n      .subscribe((message) => {\n        if (message) {\n          this.handleNewMessage(message);\n        }\n      });\n\n    // Subscription pour les notifications\n    const notificationsSub = this.messageService\n      .subscribeToNotifications()\n      .subscribe((notification) => {\n        if (notification) {\n          this.handleNewNotification(notification);\n        }\n      });\n\n    // Subscription pour la recherche\n    const searchSub = this.searchQuery$\n      .pipe\n      // debounceTime(300),\n      // distinctUntilChanged()\n      ()\n      .subscribe((query) => {\n        this.performSearch(query);\n      });\n\n    this.subscriptions.push(messagesSub, notificationsSub, searchSub);\n  }\n\n  private loadInitialData(): void {\n    this.loadConversations();\n    this.loadUsers();\n    this.loadNotifications();\n  }\n\n  // ============================================================================\n  // MÉTHODES DE CHARGEMENT DES DONNÉES\n  // ============================================================================\n\n  loadConversations(page: number = 1): void {\n    if (this.isLoadingConversations) return;\n\n    this.isLoadingConversations = true;\n\n    this.messageService.getConversations(25, page).subscribe({\n      next: (conversations) => {\n        if (page === 1) {\n          this.conversations = conversations;\n        } else {\n          this.conversations.push(...conversations);\n        }\n\n        this.conversationsPage = page;\n        this.hasMoreConversations = conversations.length === 25;\n        this.isLoadingConversations = false;\n        this.cdr.detectChanges();\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement des conversations:', error);\n        this.isLoadingConversations = false;\n        this.toastService.showError(\n          'Erreur lors du chargement des conversations'\n        );\n      },\n    });\n  }\n\n  loadUsers(page: number = 1): void {\n    if (this.isLoadingUsers) return;\n\n    this.isLoadingUsers = true;\n\n    this.messageService.getAllUsers(false, '', page, 25).subscribe({\n      next: (users) => {\n        if (page === 1) {\n          this.users = users;\n        } else {\n          this.users.push(...users);\n        }\n\n        this.usersPage = page;\n        this.hasMoreUsers = users.length === 25;\n        this.isLoadingUsers = false;\n        this.cdr.detectChanges();\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement des utilisateurs:', error);\n        this.isLoadingUsers = false;\n        this.toastService.showError(\n          'Erreur lors du chargement des utilisateurs'\n        );\n      },\n    });\n  }\n\n  loadNotifications(): void {\n    if (this.isLoadingNotifications) return;\n\n    this.isLoadingNotifications = true;\n\n    this.messageService.getNotifications().subscribe({\n      next: (notifications) => {\n        this.notifications = notifications;\n        this.isLoadingNotifications = false;\n        this.cdr.detectChanges();\n      },\n      error: (error) => {\n        console.error('Erreur lors du chargement des notifications:', error);\n        this.isLoadingNotifications = false;\n        this.toastService.showError(\n          'Erreur lors du chargement des notifications'\n        );\n      },\n    });\n  }\n\n  // ============================================================================\n  // MÉTHODES DE GESTION DES ÉVÉNEMENTS\n  // ============================================================================\n\n  private handleNewMessage(message: Message): void {\n    // Mettre à jour la conversation correspondante\n    const conversationIndex = this.conversations.findIndex(\n      (conv) => conv.id === message.conversationId\n    );\n\n    if (conversationIndex !== -1) {\n      // Mettre à jour le dernier message\n      this.conversations[conversationIndex].lastMessage = message;\n\n      // Déplacer la conversation en haut de la liste\n      const conversation = this.conversations.splice(conversationIndex, 1)[0];\n      this.conversations.unshift(conversation);\n\n      this.cdr.detectChanges();\n    }\n  }\n\n  private handleNewNotification(notification: Notification): void {\n    // Ajouter la nouvelle notification en haut de la liste\n    this.notifications.unshift(notification);\n    this.cdr.detectChanges();\n\n    // Afficher une notification toast si ce n'est pas l'onglet actif\n    if (this.activeTab !== 'notifications') {\n      this.toastService.showInfo('Nouvelle notification reçue');\n    }\n  }\n\n  private markConversationAsSelected(conversationId: string): void {\n    // Marquer la conversation comme sélectionnée visuellement\n    this.selectedConversationId = conversationId;\n    this.cdr.detectChanges();\n  }\n\n  // ============================================================================\n  // MÉTHODES DE NAVIGATION ET UI\n  // ============================================================================\n\n  switchTab(tab: 'conversations' | 'users' | 'notifications'): void {\n    this.activeTab = tab;\n    this.searchQuery = '';\n    this.searchResults = [];\n    this.isSearching = false;\n\n    // Charger les données si nécessaire\n    switch (tab) {\n      case 'conversations':\n        if (this.conversations.length === 0) {\n          this.loadConversations();\n        }\n        break;\n      case 'users':\n        if (this.users.length === 0) {\n          this.loadUsers();\n        }\n        break;\n      case 'notifications':\n        if (this.notifications.length === 0) {\n          this.loadNotifications();\n        }\n        break;\n    }\n  }\n\n  selectConversation(conversation: Conversation): void {\n    if (!conversation.id) return;\n\n    this.selectedConversationId = conversation.id;\n    this.router.navigate(['/messages', conversation.id]);\n\n    // Fermer le menu mobile si ouvert\n    this.isMobileMenuOpen = false;\n  }\n\n  startConversationWithUser(user: User): void {\n    if (!user.id && !user._id) return;\n\n    const userId = user.id || user._id!;\n\n    // Créer ou récupérer la conversation avec cet utilisateur\n    this.messageService.createOrGetConversation(userId).subscribe({\n      next: (conversation) => {\n        this.selectConversation(conversation);\n      },\n      error: (error) => {\n        console.error('Erreur lors de la création de la conversation:', error);\n        this.toastService.showError(\n          'Erreur lors de la création de la conversation'\n        );\n      },\n    });\n  }\n\n  toggleMobileMenu(): void {\n    this.isMobileMenuOpen = !this.isMobileMenuOpen;\n  }\n\n  // ============================================================================\n  // MÉTHODES DE RECHERCHE\n  // ============================================================================\n\n  onSearchInput(event: any): void {\n    const query = event.target.value.trim();\n    this.searchQuery = query;\n    this.searchQuery$.next(query);\n  }\n\n  private performSearch(query: string): void {\n    if (!query) {\n      this.searchResults = [];\n      this.isSearching = false;\n      return;\n    }\n\n    this.isSearching = true;\n\n    if (this.activeTab === 'conversations') {\n      this.searchResults = this.conversations.filter((conv) =>\n        conv.isGroup\n          ? conv.groupName?.toLowerCase().includes(query.toLowerCase())\n          : conv.participants?.some((p) =>\n              p.username?.toLowerCase().includes(query.toLowerCase())\n            )\n      );\n    } else if (this.activeTab === 'users') {\n      this.searchResults = this.users.filter(\n        (user) =>\n          user.username?.toLowerCase().includes(query.toLowerCase()) ||\n          user.email?.toLowerCase().includes(query.toLowerCase())\n      );\n    }\n\n    this.cdr.detectChanges();\n  }\n\n  clearSearch(): void {\n    this.searchQuery = '';\n    this.searchResults = [];\n    this.isSearching = false;\n    this.searchQuery$.next('');\n  }\n\n  // ============================================================================\n  // MÉTHODES DE PAGINATION\n  // ============================================================================\n\n  loadMoreConversations(): void {\n    if (this.hasMoreConversations && !this.isLoadingConversations) {\n      this.loadConversations(this.conversationsPage + 1);\n    }\n  }\n\n  loadMoreUsers(): void {\n    if (this.hasMoreUsers && !this.isLoadingUsers) {\n      this.loadUsers(this.usersPage + 1);\n    }\n  }\n\n  // ============================================================================\n  // MÉTHODES UTILITAIRES POUR LE TEMPLATE\n  // ============================================================================\n\n  getConversationName(conversation: Conversation): string {\n    if (conversation.isGroup) {\n      return conversation.groupName || 'Groupe sans nom';\n    }\n\n    if (!this.currentUser) return 'Conversation';\n\n    const currentUserId = this.currentUser.id || this.currentUser._id;\n    const otherParticipant = conversation.participants?.find(\n      (p) => (p.id || p._id) !== currentUserId\n    );\n\n    return otherParticipant?.username || 'Utilisateur inconnu';\n  }\n\n  getConversationAvatar(conversation: Conversation): string {\n    if (conversation.isGroup) {\n      return conversation.groupPhoto || '/assets/images/default-group.png';\n    }\n\n    if (!this.currentUser) return '/assets/images/default-avatar.png';\n\n    const currentUserId = this.currentUser.id || this.currentUser._id;\n    const otherParticipant = conversation.participants?.find(\n      (p) => (p.id || p._id) !== currentUserId\n    );\n\n    return otherParticipant?.image || '/assets/images/default-avatar.png';\n  }\n\n  getLastMessagePreview(conversation: Conversation): string {\n    if (!conversation.lastMessage) return 'Aucun message';\n\n    const message = conversation.lastMessage;\n\n    if (message.type === 'TEXT') {\n      return message.content || '';\n    } else if (message.type === 'IMAGE') {\n      return '📷 Image';\n    } else if (message.type === 'FILE') {\n      return '📎 Fichier';\n    } else if (message.type === 'VOICE_MESSAGE') {\n      return '🎤 Message vocal';\n    } else if (message.type === 'VIDEO') {\n      return '🎥 Vidéo';\n    }\n\n    return 'Message';\n  }\n\n  formatLastMessageTime(timestamp: Date | string | undefined): string {\n    if (!timestamp) return '';\n\n    const date = new Date(timestamp);\n    const now = new Date();\n    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n\n    if (diffInHours < 1) {\n      return \"À l'instant\";\n    } else if (diffInHours < 24) {\n      return date.toLocaleTimeString('fr-FR', {\n        hour: '2-digit',\n        minute: '2-digit',\n      });\n    } else if (diffInHours < 168) {\n      // 7 jours\n      return date.toLocaleDateString('fr-FR', { weekday: 'short' });\n    } else {\n      return date.toLocaleDateString('fr-FR', {\n        day: '2-digit',\n        month: '2-digit',\n      });\n    }\n  }\n\n  getUnreadCount(conversation: Conversation): number {\n    return conversation.unreadCount || 0;\n  }\n\n  isUserOnline(user: User): boolean {\n    return user.isOnline || false;\n  }\n\n  trackByConversationId(index: number, conversation: Conversation): string {\n    return conversation.id || conversation._id || index.toString();\n  }\n\n  trackByUserId(index: number, user: User): string {\n    return user.id || user._id || index.toString();\n  }\n\n  trackByNotificationId(index: number, notification: Notification): string {\n    return notification.id || notification._id || index.toString();\n  }\n\n  markNotificationAsRead(notification: Notification): void {\n    if (!notification.id || notification.isRead) return;\n\n    this.messageService.markNotificationAsRead(notification.id).subscribe({\n      next: () => {\n        notification.isRead = true;\n        this.cdr.detectChanges();\n      },\n      error: (error) => {\n        console.error(\n          'Erreur lors du marquage de la notification comme lue:',\n          error\n        );\n        this.toastService.showError(\n          'Erreur lors du marquage de la notification'\n        );\n      },\n    });\n  }\n\n  // Type guards pour différencier User et Conversation dans les résultats de recherche\n  isUser(item: User | Conversation): item is User {\n    return 'username' in item && 'email' in item;\n  }\n\n  isConversation(item: User | Conversation): item is Conversation {\n    return 'participants' in item || 'isGroup' in item;\n  }\n\n  getNotificationTitle(notification: Notification): string {\n    switch (notification.type) {\n      case 'NEW_MESSAGE':\n        return 'Nouveau message';\n      case 'FRIEND_REQUEST':\n        return \"Demande d'ami\";\n      case 'GROUP_INVITE':\n        return 'Invitation de groupe';\n      case 'MESSAGE_REACTION':\n        return 'Réaction à un message';\n      case 'SYSTEM_ALERT':\n        return 'Alerte système';\n      default:\n        return 'Notification';\n    }\n  }\n}\n", "<!-- ============================================================================\n     LAYOUT PRINCIPAL DE MESSAGERIE - STYLE WHATSAPP\n     ============================================================================ -->\n\n<div class=\"message-layout h-screen bg-gray-900 text-white flex\">\n  <!-- ========================================================================\n       SIDEBAR GAUCHE - CONVERSATIONS/UTILISATEURS/NOTIFICATIONS\n       ======================================================================== -->\n  <div\n    class=\"sidebar w-80 bg-gray-800 border-r border-gray-700 flex flex-col\"\n    [class.hidden]=\"!isMobileMenuOpen\"\n    [class.md:flex]=\"true\"\n  >\n    <!-- En-tête de la sidebar -->\n    <div class=\"sidebar-header p-4 border-b border-gray-700 bg-gray-800\">\n      <div class=\"flex items-center justify-between mb-4\">\n        <div class=\"flex items-center space-x-3\">\n          <img\n            [src]=\"currentUser?.image || '/assets/images/default-avatar.png'\"\n            [alt]=\"currentUser?.username\"\n            class=\"w-10 h-10 rounded-full border-2 border-blue-500\"\n          />\n          <div>\n            <h3 class=\"font-semibold text-white\">\n              {{ currentUser?.username }}\n            </h3>\n            <p class=\"text-sm text-green-400\">En ligne</p>\n          </div>\n        </div>\n\n        <!-- Bouton menu mobile -->\n        <button\n          class=\"md:hidden p-2 rounded-lg bg-gray-700 hover:bg-gray-600\"\n          (click)=\"toggleMobileMenu()\"\n        >\n          <i class=\"fas fa-times text-white\"></i>\n        </button>\n      </div>\n\n      <!-- Barre de recherche -->\n      <div class=\"relative\">\n        <input\n          #searchInput\n          type=\"text\"\n          [(ngModel)]=\"searchQuery\"\n          (input)=\"onSearchInput($event)\"\n          placeholder=\"Rechercher...\"\n          class=\"w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 pl-10 text-white placeholder-gray-400 focus:outline-none focus:border-blue-500\"\n        />\n        <i class=\"fas fa-search absolute left-3 top-3 text-gray-400\"></i>\n        <button\n          *ngIf=\"searchQuery\"\n          (click)=\"clearSearch()\"\n          class=\"absolute right-3 top-3 text-gray-400 hover:text-white\"\n        >\n          <i class=\"fas fa-times\"></i>\n        </button>\n      </div>\n    </div>\n\n    <!-- Onglets de navigation -->\n    <div class=\"tabs flex border-b border-gray-700\">\n      <button\n        class=\"tab flex-1 py-3 px-4 text-center transition-all duration-200\"\n        [class.active]=\"activeTab === 'conversations'\"\n        [class.text-blue-400]=\"activeTab === 'conversations'\"\n        [class.border-b-2]=\"activeTab === 'conversations'\"\n        [class.border-blue-500]=\"activeTab === 'conversations'\"\n        [class.text-gray-400]=\"activeTab !== 'conversations'\"\n        (click)=\"switchTab('conversations')\"\n      >\n        <i class=\"fas fa-comments mb-1\"></i>\n        <div class=\"text-xs\">Discussions</div>\n      </button>\n\n      <button\n        class=\"tab flex-1 py-3 px-4 text-center transition-all duration-200\"\n        [class.active]=\"activeTab === 'users'\"\n        [class.text-blue-400]=\"activeTab === 'users'\"\n        [class.border-b-2]=\"activeTab === 'users'\"\n        [class.border-blue-500]=\"activeTab === 'users'\"\n        [class.text-gray-400]=\"activeTab !== 'users'\"\n        (click)=\"switchTab('users')\"\n      >\n        <i class=\"fas fa-users mb-1\"></i>\n        <div class=\"text-xs\">Contacts</div>\n      </button>\n\n      <button\n        class=\"tab flex-1 py-3 px-4 text-center transition-all duration-200 relative\"\n        [class.active]=\"activeTab === 'notifications'\"\n        [class.text-blue-400]=\"activeTab === 'notifications'\"\n        [class.border-b-2]=\"activeTab === 'notifications'\"\n        [class.border-blue-500]=\"activeTab === 'notifications'\"\n        [class.text-gray-400]=\"activeTab !== 'notifications'\"\n        (click)=\"switchTab('notifications')\"\n      >\n        <i class=\"fas fa-bell mb-1\"></i>\n        <div class=\"text-xs\">Notifications</div>\n        <span\n          *ngIf=\"notifications.length > 0\"\n          class=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\"\n        >\n          {{ notifications.length > 9 ? \"9+\" : notifications.length }}\n        </span>\n      </button>\n    </div>\n\n    <!-- Contenu de la sidebar -->\n    <div class=\"sidebar-content flex-1 overflow-y-auto\">\n      <!-- ====================================================================\n           ONGLET CONVERSATIONS\n           ==================================================================== -->\n      <div *ngIf=\"activeTab === 'conversations'\" class=\"conversations-list\">\n        <!-- Résultats de recherche -->\n        <div\n          *ngIf=\"isSearching && searchResults.length > 0\"\n          class=\"search-results\"\n        >\n          <div class=\"p-3 text-sm text-gray-400 border-b border-gray-700\">\n            Résultats de recherche ({{ searchResults.length }})\n          </div>\n          <div\n            *ngFor=\"let result of searchResults\"\n            class=\"conversation-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors\"\n            (click)=\"selectConversation(result)\"\n          >\n            <div class=\"flex items-center space-x-3\">\n              <img\n                [src]=\"getConversationAvatar(result)\"\n                [alt]=\"getConversationName(result)\"\n                class=\"w-12 h-12 rounded-full\"\n              />\n              <div class=\"flex-1 min-w-0\">\n                <h4 class=\"font-medium text-white truncate\">\n                  {{ getConversationName(result) }}\n                </h4>\n                <p class=\"text-sm text-gray-400 truncate\">\n                  {{ getLastMessagePreview(result) }}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Message si aucun résultat -->\n        <div\n          *ngIf=\"isSearching && searchResults.length === 0\"\n          class=\"p-8 text-center text-gray-400\"\n        >\n          <i class=\"fas fa-search text-4xl mb-4\"></i>\n          <p>Aucun résultat trouvé</p>\n        </div>\n\n        <!-- Liste des conversations -->\n        <div *ngIf=\"!isSearching\">\n          <!-- Indicateur de chargement -->\n          <div\n            *ngIf=\"isLoadingConversations && conversations.length === 0\"\n            class=\"p-8 text-center\"\n          >\n            <div\n              class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto\"\n            ></div>\n            <p class=\"text-gray-400 mt-2\">Chargement des conversations...</p>\n          </div>\n\n          <!-- Conversations -->\n          <div\n            *ngFor=\"\n              let conversation of conversations;\n              trackBy: trackByConversationId\n            \"\n            class=\"conversation-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors relative\"\n            [class.bg-gray-700]=\"selectedConversationId === conversation.id\"\n            [class.border-l-4]=\"selectedConversationId === conversation.id\"\n            [class.border-blue-500]=\"selectedConversationId === conversation.id\"\n            (click)=\"selectConversation(conversation)\"\n          >\n            <div class=\"flex items-center space-x-3\">\n              <!-- Avatar avec indicateur en ligne -->\n              <div class=\"relative\">\n                <img\n                  [src]=\"getConversationAvatar(conversation)\"\n                  [alt]=\"getConversationName(conversation)\"\n                  class=\"w-12 h-12 rounded-full\"\n                />\n                <div\n                  *ngIf=\"!conversation.isGroup && isUserOnline(conversation.participants?.[0]!)\"\n                  class=\"absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-gray-800\"\n                ></div>\n              </div>\n\n              <!-- Informations de la conversation -->\n              <div class=\"flex-1 min-w-0\">\n                <div class=\"flex items-center justify-between\">\n                  <h4 class=\"font-medium text-white truncate\">\n                    {{ getConversationName(conversation) }}\n                  </h4>\n                  <span class=\"text-xs text-gray-400\">\n                    {{\n                      formatLastMessageTime(conversation.lastMessage?.timestamp)\n                    }}\n                  </span>\n                </div>\n\n                <div class=\"flex items-center justify-between mt-1\">\n                  <p class=\"text-sm text-gray-400 truncate\">\n                    {{ getLastMessagePreview(conversation) }}\n                  </p>\n\n                  <!-- Badge de messages non lus -->\n                  <span\n                    *ngIf=\"getUnreadCount(conversation) > 0\"\n                    class=\"bg-blue-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center\"\n                  >\n                    {{\n                      getUnreadCount(conversation) > 99\n                        ? \"99+\"\n                        : getUnreadCount(conversation)\n                    }}\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Bouton charger plus -->\n          <div *ngIf=\"hasMoreConversations\" class=\"p-4 text-center\">\n            <button\n              (click)=\"loadMoreConversations()\"\n              [disabled]=\"isLoadingConversations\"\n              class=\"text-blue-400 hover:text-blue-300 disabled:text-gray-500\"\n            >\n              <span *ngIf=\"!isLoadingConversations\">Charger plus</span>\n              <span *ngIf=\"isLoadingConversations\">Chargement...</span>\n            </button>\n          </div>\n\n          <!-- Message si aucune conversation -->\n          <div\n            *ngIf=\"conversations.length === 0 && !isLoadingConversations\"\n            class=\"p-8 text-center text-gray-400\"\n          >\n            <i class=\"fas fa-comments text-4xl mb-4\"></i>\n            <p>Aucune conversation</p>\n            <p class=\"text-sm mt-2\">\n              Commencez une nouvelle conversation dans l'onglet Contacts\n            </p>\n          </div>\n        </div>\n      </div>\n\n      <!-- ====================================================================\n           ONGLET UTILISATEURS/CONTACTS\n           ==================================================================== -->\n      <div *ngIf=\"activeTab === 'users'\" class=\"users-list\">\n        <!-- Résultats de recherche -->\n        <div\n          *ngIf=\"isSearching && searchResults.length > 0\"\n          class=\"search-results\"\n        >\n          <div class=\"p-3 text-sm text-gray-400 border-b border-gray-700\">\n            Résultats de recherche ({{ searchResults.length }})\n          </div>\n          <div\n            *ngFor=\"let result of searchResults\"\n            class=\"user-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors\"\n            (click)=\"isUser(result) ? startConversationWithUser(result) : null\"\n          >\n            <div class=\"flex items-center space-x-3\" *ngIf=\"isUser(result)\">\n              <div class=\"relative\">\n                <img\n                  [src]=\"result.image || '/assets/images/default-avatar.png'\"\n                  [alt]=\"result.username\"\n                  class=\"w-12 h-12 rounded-full\"\n                />\n                <div\n                  *ngIf=\"isUserOnline(result)\"\n                  class=\"absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-gray-800\"\n                ></div>\n              </div>\n              <div class=\"flex-1 min-w-0\">\n                <h4 class=\"font-medium text-white truncate\">\n                  {{ result.username }}\n                </h4>\n                <p class=\"text-sm text-gray-400 truncate\">{{ result.email }}</p>\n              </div>\n              <div class=\"text-blue-400\">\n                <i class=\"fas fa-comment\"></i>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Message si aucun résultat -->\n        <div\n          *ngIf=\"isSearching && searchResults.length === 0\"\n          class=\"p-8 text-center text-gray-400\"\n        >\n          <i class=\"fas fa-search text-4xl mb-4\"></i>\n          <p>Aucun utilisateur trouvé</p>\n        </div>\n\n        <!-- Liste des utilisateurs -->\n        <div *ngIf=\"!isSearching\">\n          <!-- Indicateur de chargement -->\n          <div\n            *ngIf=\"isLoadingUsers && users.length === 0\"\n            class=\"p-8 text-center\"\n          >\n            <div\n              class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto\"\n            ></div>\n            <p class=\"text-gray-400 mt-2\">Chargement des utilisateurs...</p>\n          </div>\n\n          <!-- Utilisateurs -->\n          <div\n            *ngFor=\"let user of users; trackBy: trackByUserId\"\n            class=\"user-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors\"\n            (click)=\"startConversationWithUser(user)\"\n          >\n            <div class=\"flex items-center space-x-3\">\n              <!-- Avatar avec indicateur en ligne -->\n              <div class=\"relative\">\n                <img\n                  [src]=\"user.image || '/assets/images/default-avatar.png'\"\n                  [alt]=\"user.username\"\n                  class=\"w-12 h-12 rounded-full\"\n                />\n                <div\n                  *ngIf=\"isUserOnline(user)\"\n                  class=\"absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-gray-800\"\n                ></div>\n              </div>\n\n              <!-- Informations de l'utilisateur -->\n              <div class=\"flex-1 min-w-0\">\n                <h4 class=\"font-medium text-white truncate\">\n                  {{ user.username }}\n                </h4>\n                <p class=\"text-sm text-gray-400 truncate\">{{ user.email }}</p>\n                <p class=\"text-xs text-gray-500\" *ngIf=\"user.role\">\n                  {{ user.role }}\n                </p>\n              </div>\n\n              <!-- Statut en ligne -->\n              <div class=\"text-right\">\n                <div\n                  class=\"text-xs px-2 py-1 rounded-full\"\n                  [class.bg-green-600]=\"isUserOnline(user)\"\n                  [class.text-green-100]=\"isUserOnline(user)\"\n                  [class.bg-gray-600]=\"!isUserOnline(user)\"\n                  [class.text-gray-300]=\"!isUserOnline(user)\"\n                >\n                  {{ isUserOnline(user) ? \"En ligne\" : \"Hors ligne\" }}\n                </div>\n                <div class=\"text-blue-400 mt-1\">\n                  <i class=\"fas fa-comment\"></i>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Bouton charger plus -->\n          <div *ngIf=\"hasMoreUsers\" class=\"p-4 text-center\">\n            <button\n              (click)=\"loadMoreUsers()\"\n              [disabled]=\"isLoadingUsers\"\n              class=\"text-blue-400 hover:text-blue-300 disabled:text-gray-500\"\n            >\n              <span *ngIf=\"!isLoadingUsers\">Charger plus</span>\n              <span *ngIf=\"isLoadingUsers\">Chargement...</span>\n            </button>\n          </div>\n\n          <!-- Message si aucun utilisateur -->\n          <div\n            *ngIf=\"users.length === 0 && !isLoadingUsers\"\n            class=\"p-8 text-center text-gray-400\"\n          >\n            <i class=\"fas fa-users text-4xl mb-4\"></i>\n            <p>Aucun utilisateur trouvé</p>\n          </div>\n        </div>\n      </div>\n\n      <!-- ====================================================================\n           ONGLET NOTIFICATIONS\n           ==================================================================== -->\n      <div *ngIf=\"activeTab === 'notifications'\" class=\"notifications-list\">\n        <!-- Indicateur de chargement -->\n        <div\n          *ngIf=\"isLoadingNotifications && notifications.length === 0\"\n          class=\"p-8 text-center\"\n        >\n          <div\n            class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto\"\n          ></div>\n          <p class=\"text-gray-400 mt-2\">Chargement des notifications...</p>\n        </div>\n\n        <!-- Notifications -->\n        <div\n          *ngFor=\"\n            let notification of notifications;\n            trackBy: trackByNotificationId\n          \"\n          class=\"notification-item p-4 hover:bg-gray-700 cursor-pointer border-b border-gray-700 transition-colors\"\n          [class.bg-gray-700]=\"!notification.isRead\"\n          (click)=\"markNotificationAsRead(notification)\"\n        >\n          <div class=\"flex items-start space-x-3\">\n            <!-- Icône de notification -->\n            <div\n              class=\"notification-icon p-2 rounded-full\"\n              [class.bg-blue-600]=\"notification.type === 'NEW_MESSAGE'\"\n              [class.bg-green-600]=\"notification.type === 'FRIEND_REQUEST'\"\n              [class.bg-yellow-600]=\"notification.type === 'GROUP_INVITE'\"\n              [class.bg-purple-600]=\"notification.type === 'MESSAGE_REACTION'\"\n              [class.bg-red-600]=\"notification.type === 'SYSTEM_ALERT'\"\n            >\n              <i\n                class=\"fas\"\n                [class.fa-message]=\"notification.type === 'NEW_MESSAGE'\"\n                [class.fa-user-plus]=\"notification.type === 'FRIEND_REQUEST'\"\n                [class.fa-users]=\"notification.type === 'GROUP_INVITE'\"\n                [class.fa-heart]=\"notification.type === 'MESSAGE_REACTION'\"\n                [class.fa-exclamation-triangle]=\"\n                  notification.type === 'SYSTEM_ALERT'\n                \"\n                class=\"text-white text-sm\"\n              ></i>\n            </div>\n\n            <!-- Contenu de la notification -->\n            <div class=\"flex-1 min-w-0\">\n              <h4 class=\"font-medium text-white truncate\">\n                {{ getNotificationTitle(notification) }}\n              </h4>\n              <p class=\"text-sm text-gray-400 mt-1\">\n                {{ notification.content }}\n              </p>\n              <p class=\"text-xs text-gray-500 mt-2\">\n                {{ formatLastMessageTime(notification.timestamp) }}\n              </p>\n            </div>\n\n            <!-- Indicateur non lu -->\n            <div\n              *ngIf=\"!notification.isRead\"\n              class=\"w-2 h-2 bg-blue-500 rounded-full\"\n            ></div>\n          </div>\n        </div>\n\n        <!-- Message si aucune notification -->\n        <div\n          *ngIf=\"notifications.length === 0 && !isLoadingNotifications\"\n          class=\"p-8 text-center text-gray-400\"\n        >\n          <i class=\"fas fa-bell text-4xl mb-4\"></i>\n          <p>Aucune notification</p>\n          <p class=\"text-sm mt-2\">\n            Vous serez notifié des nouveaux messages et événements\n          </p>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- ========================================================================\n       ZONE PRINCIPALE - CHAT OU MESSAGE DE BIENVENUE\n       ======================================================================== -->\n  <div class=\"main-content flex-1 flex flex-col\">\n    <!-- Bouton menu mobile -->\n    <div class=\"md:hidden p-4 border-b border-gray-700 bg-gray-800\">\n      <button\n        class=\"p-2 rounded-lg bg-gray-700 hover:bg-gray-600\"\n        (click)=\"toggleMobileMenu()\"\n      >\n        <i class=\"fas fa-bars text-white\"></i>\n      </button>\n    </div>\n\n    <!-- Contenu principal -->\n    <div class=\"flex-1\">\n      <router-outlet></router-outlet>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAQA,SAAuBA,eAAe,QAAQ,MAAM;;;;;;;;;;;;IC0C5CC,EAAA,CAAAC,cAAA,iBAIC;IAFCD,EAAA,CAAAE,UAAA,mBAAAC,kEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAGvBT,EAAA,CAAAU,SAAA,YAA4B;IAC9BV,EAAA,CAAAW,YAAA,EAAS;;;;;IA2CTX,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAO;;;;IADLX,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAC,MAAA,CAAAC,aAAA,CAAAC,MAAA,cAAAF,MAAA,CAAAC,aAAA,CAAAC,MAAA,MACF;;;;;;IAkBEjB,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAE,UAAA,mBAAAgB,wEAAA;MAAA,MAAAC,WAAA,GAAAnB,EAAA,CAAAI,aAAA,CAAAgB,IAAA;MAAA,MAAAC,UAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAAvB,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAe,OAAA,CAAAC,kBAAA,CAAAH,UAAA,CAA0B;IAAA,EAAC;IAEpCrB,EAAA,CAAAC,cAAA,aAAyC;IACvCD,EAAA,CAAAU,SAAA,cAIE;IACFV,EAAA,CAAAC,cAAA,cAA4B;IAExBD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAK;IACLX,EAAA,CAAAC,cAAA,YAA0C;IACxCD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAI;;;;;IAVJX,EAAA,CAAAa,SAAA,GAAqC;IAArCb,EAAA,CAAAyB,UAAA,QAAAC,OAAA,CAAAC,qBAAA,CAAAN,UAAA,GAAArB,EAAA,CAAA4B,aAAA,CAAqC,QAAAF,OAAA,CAAAG,mBAAA,CAAAR,UAAA;IAMnCrB,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAY,OAAA,CAAAG,mBAAA,CAAAR,UAAA,OACF;IAEErB,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAY,OAAA,CAAAI,qBAAA,CAAAT,UAAA,OACF;;;;;IAxBRrB,EAAA,CAAAC,cAAA,cAGC;IAEGD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAA+B,UAAA,IAAAC,kDAAA,kBAoBM;IACRhC,EAAA,CAAAW,YAAA,EAAM;;;;IAvBFX,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,mCAAAmB,MAAA,CAAAC,aAAA,CAAAjB,MAAA,OACF;IAEqBjB,EAAA,CAAAa,SAAA,GAAgB;IAAhBb,EAAA,CAAAyB,UAAA,YAAAQ,MAAA,CAAAC,aAAA,CAAgB;;;;;IAuBvClC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAU,SAAA,YAA2C;IAC3CV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAY,MAAA,sCAAqB;IAAAZ,EAAA,CAAAW,YAAA,EAAI;;;;;IAM5BX,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAU,SAAA,cAEO;IACPV,EAAA,CAAAC,cAAA,YAA8B;IAAAD,EAAA,CAAAY,MAAA,sCAA+B;IAAAZ,EAAA,CAAAW,YAAA,EAAI;;;;;IAuB7DX,EAAA,CAAAU,SAAA,cAGO;;;;;IAsBLV,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAAY,MAAA,GAKF;IAAAZ,EAAA,CAAAW,YAAA,EAAO;;;;;IALLX,EAAA,CAAAa,SAAA,GAKF;IALEb,EAAA,CAAAc,kBAAA,MAAAqB,OAAA,CAAAC,cAAA,CAAAC,gBAAA,iBAAAF,OAAA,CAAAC,cAAA,CAAAC,gBAAA,OAKF;;;;;;IArDRrC,EAAA,CAAAC,cAAA,cAUC;IADCD,EAAA,CAAAE,UAAA,mBAAAoC,wEAAA;MAAA,MAAAnB,WAAA,GAAAnB,EAAA,CAAAI,aAAA,CAAAmC,IAAA;MAAA,MAAAF,gBAAA,GAAAlB,WAAA,CAAAG,SAAA;MAAA,MAAAkB,OAAA,GAAAxC,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAgC,OAAA,CAAAhB,kBAAA,CAAAa,gBAAA,CAAgC;IAAA,EAAC;IAE1CrC,EAAA,CAAAC,cAAA,aAAyC;IAGrCD,EAAA,CAAAU,SAAA,cAIE;IACFV,EAAA,CAAA+B,UAAA,IAAAU,wDAAA,kBAGO;IACTzC,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAC,cAAA,cAA4B;IAGtBD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAK;IACLX,EAAA,CAAAC,cAAA,eAAoC;IAClCD,EAAA,CAAAY,MAAA,IAGF;IAAAZ,EAAA,CAAAW,YAAA,EAAO;IAGTX,EAAA,CAAAC,cAAA,eAAoD;IAEhDD,EAAA,CAAAY,MAAA,IACF;IAAAZ,EAAA,CAAAW,YAAA,EAAI;IAGJX,EAAA,CAAA+B,UAAA,KAAAW,0DAAA,mBASO;IACT1C,EAAA,CAAAW,YAAA,EAAM;;;;;IAhDVX,EAAA,CAAA2C,WAAA,gBAAAC,OAAA,CAAAC,sBAAA,KAAAR,gBAAA,CAAAS,EAAA,CAAgE,eAAAF,OAAA,CAAAC,sBAAA,KAAAR,gBAAA,CAAAS,EAAA,qBAAAF,OAAA,CAAAC,sBAAA,KAAAR,gBAAA,CAAAS,EAAA;IAS1D9C,EAAA,CAAAa,SAAA,GAA2C;IAA3Cb,EAAA,CAAAyB,UAAA,QAAAmB,OAAA,CAAAjB,qBAAA,CAAAU,gBAAA,GAAArC,EAAA,CAAA4B,aAAA,CAA2C,QAAAgB,OAAA,CAAAf,mBAAA,CAAAQ,gBAAA;IAK1CrC,EAAA,CAAAa,SAAA,GAA4E;IAA5Eb,EAAA,CAAAyB,UAAA,UAAAY,gBAAA,CAAAU,OAAA,IAAAH,OAAA,CAAAI,YAAA,CAAAX,gBAAA,CAAAY,YAAA,kBAAAZ,gBAAA,CAAAY,YAAA,KAA4E;IAS3EjD,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAA8B,OAAA,CAAAf,mBAAA,CAAAQ,gBAAA,OACF;IAEErC,EAAA,CAAAa,SAAA,GAGF;IAHEb,EAAA,CAAAc,kBAAA,MAAA8B,OAAA,CAAAM,qBAAA,CAAAb,gBAAA,CAAAc,WAAA,kBAAAd,gBAAA,CAAAc,WAAA,CAAAC,SAAA,OAGF;IAKEpD,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAA8B,OAAA,CAAAd,qBAAA,CAAAO,gBAAA,OACF;IAIGrC,EAAA,CAAAa,SAAA,GAAsC;IAAtCb,EAAA,CAAAyB,UAAA,SAAAmB,OAAA,CAAAR,cAAA,CAAAC,gBAAA,MAAsC;;;;;IAqB7CrC,EAAA,CAAAC,cAAA,WAAsC;IAAAD,EAAA,CAAAY,MAAA,mBAAY;IAAAZ,EAAA,CAAAW,YAAA,EAAO;;;;;IACzDX,EAAA,CAAAC,cAAA,WAAqC;IAAAD,EAAA,CAAAY,MAAA,oBAAa;IAAAZ,EAAA,CAAAW,YAAA,EAAO;;;;;;IAP7DX,EAAA,CAAAC,cAAA,cAA0D;IAEtDD,EAAA,CAAAE,UAAA,mBAAAmD,2EAAA;MAAArD,EAAA,CAAAI,aAAA,CAAAkD,IAAA;MAAA,MAAAC,OAAA,GAAAvD,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA+C,OAAA,CAAAC,qBAAA,EAAuB;IAAA,EAAC;IAIjCxD,EAAA,CAAA+B,UAAA,IAAA0B,yDAAA,mBAAyD;IACzDzD,EAAA,CAAA+B,UAAA,IAAA2B,yDAAA,mBAAyD;IAC3D1D,EAAA,CAAAW,YAAA,EAAS;;;;IALPX,EAAA,CAAAa,SAAA,GAAmC;IAAnCb,EAAA,CAAAyB,UAAA,aAAAkC,OAAA,CAAAC,sBAAA,CAAmC;IAG5B5D,EAAA,CAAAa,SAAA,GAA6B;IAA7Bb,EAAA,CAAAyB,UAAA,UAAAkC,OAAA,CAAAC,sBAAA,CAA6B;IAC7B5D,EAAA,CAAAa,SAAA,GAA4B;IAA5Bb,EAAA,CAAAyB,UAAA,SAAAkC,OAAA,CAAAC,sBAAA,CAA4B;;;;;IAKvC5D,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAU,SAAA,YAA6C;IAC7CV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAY,MAAA,0BAAmB;IAAAZ,EAAA,CAAAW,YAAA,EAAI;IAC1BX,EAAA,CAAAC,cAAA,YAAwB;IACtBD,EAAA,CAAAY,MAAA,mEACF;IAAAZ,EAAA,CAAAW,YAAA,EAAI;;;;;IA7FRX,EAAA,CAAAC,cAAA,UAA0B;IAExBD,EAAA,CAAA+B,UAAA,IAAA8B,kDAAA,kBAQM;IAGN7D,EAAA,CAAA+B,UAAA,IAAA+B,kDAAA,oBAyDM;IAGN9D,EAAA,CAAA+B,UAAA,IAAAgC,kDAAA,kBASM;IAGN/D,EAAA,CAAA+B,UAAA,IAAAiC,kDAAA,kBASM;IACRhE,EAAA,CAAAW,YAAA,EAAM;;;;IA5FDX,EAAA,CAAAa,SAAA,GAA0D;IAA1Db,EAAA,CAAAyB,UAAA,SAAAwC,OAAA,CAAAL,sBAAA,IAAAK,OAAA,CAAAC,aAAA,CAAAjD,MAAA,OAA0D;IAYzBjB,EAAA,CAAAa,SAAA,GACjB;IADiBb,EAAA,CAAAyB,UAAA,YAAAwC,OAAA,CAAAC,aAAA,CACjB,iBAAAD,OAAA,CAAAE,qBAAA;IAyDbnE,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAyB,UAAA,SAAAwC,OAAA,CAAAG,oBAAA,CAA0B;IAa7BpE,EAAA,CAAAa,SAAA,GAA2D;IAA3Db,EAAA,CAAAyB,UAAA,SAAAwC,OAAA,CAAAC,aAAA,CAAAjD,MAAA,WAAAgD,OAAA,CAAAL,sBAAA,CAA2D;;;;;IAhIlE5D,EAAA,CAAAC,cAAA,cAAsE;IAEpED,EAAA,CAAA+B,UAAA,IAAAsC,4CAAA,kBA4BM;IAGNrE,EAAA,CAAA+B,UAAA,IAAAuC,4CAAA,kBAMM;IAGNtE,EAAA,CAAA+B,UAAA,IAAAwC,4CAAA,kBA+FM;IACRvE,EAAA,CAAAW,YAAA,EAAM;;;;IAvIDX,EAAA,CAAAa,SAAA,GAA6C;IAA7Cb,EAAA,CAAAyB,UAAA,SAAA+C,MAAA,CAAAC,WAAA,IAAAD,MAAA,CAAAtC,aAAA,CAAAjB,MAAA,KAA6C;IA+B7CjB,EAAA,CAAAa,SAAA,GAA+C;IAA/Cb,EAAA,CAAAyB,UAAA,SAAA+C,MAAA,CAAAC,WAAA,IAAAD,MAAA,CAAAtC,aAAA,CAAAjB,MAAA,OAA+C;IAQ5CjB,EAAA,CAAAa,SAAA,GAAkB;IAAlBb,EAAA,CAAAyB,UAAA,UAAA+C,MAAA,CAAAC,WAAA,CAAkB;;;;;IA0HhBzE,EAAA,CAAAU,SAAA,cAGO;;;;;IAVXV,EAAA,CAAAC,cAAA,aAAgE;IAE5DD,EAAA,CAAAU,SAAA,cAIE;IACFV,EAAA,CAAA+B,UAAA,IAAA2C,8DAAA,kBAGO;IACT1E,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,cAA4B;IAExBD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAK;IACLX,EAAA,CAAAC,cAAA,YAA0C;IAAAD,EAAA,CAAAY,MAAA,GAAkB;IAAAZ,EAAA,CAAAW,YAAA,EAAI;IAElEX,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAU,SAAA,aAA8B;IAChCV,EAAA,CAAAW,YAAA,EAAM;;;;;IAjBFX,EAAA,CAAAa,SAAA,GAA2D;IAA3Db,EAAA,CAAAyB,UAAA,QAAAkD,UAAA,CAAAC,KAAA,yCAAA5E,EAAA,CAAA4B,aAAA,CAA2D,QAAA+C,UAAA,CAAAE,QAAA;IAK1D7E,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAyB,UAAA,SAAAqD,OAAA,CAAA9B,YAAA,CAAA2B,UAAA,EAA0B;IAM3B3E,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAA6D,UAAA,CAAAE,QAAA,MACF;IAC0C7E,EAAA,CAAAa,SAAA,GAAkB;IAAlBb,EAAA,CAAA+E,iBAAA,CAAAJ,UAAA,CAAAK,KAAA,CAAkB;;;;;;IArBlEhF,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAE,UAAA,mBAAA+E,wEAAA;MAAA,MAAA9D,WAAA,GAAAnB,EAAA,CAAAI,aAAA,CAAA8E,IAAA;MAAA,MAAAP,UAAA,GAAAxD,WAAA,CAAAG,SAAA;MAAA,MAAA6D,OAAA,GAAAnF,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA2E,OAAA,CAAAC,MAAA,CAAAT,UAAA,CAAc,GAAGQ,OAAA,CAAAE,yBAAA,CAAAV,UAAA,CAAiC,GAAG,IAAI;IAAA,EAAC;IAEnE3E,EAAA,CAAA+B,UAAA,IAAAuD,wDAAA,mBAqBM;IACRtF,EAAA,CAAAW,YAAA,EAAM;;;;;IAtBsCX,EAAA,CAAAa,SAAA,GAAoB;IAApBb,EAAA,CAAAyB,UAAA,SAAA8D,OAAA,CAAAH,MAAA,CAAAT,UAAA,EAAoB;;;;;IAZlE3E,EAAA,CAAAC,cAAA,cAGC;IAEGD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAA+B,UAAA,IAAAyD,kDAAA,kBA2BM;IACRxF,EAAA,CAAAW,YAAA,EAAM;;;;IA9BFX,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,mCAAA2E,OAAA,CAAAvD,aAAA,CAAAjB,MAAA,OACF;IAEqBjB,EAAA,CAAAa,SAAA,GAAgB;IAAhBb,EAAA,CAAAyB,UAAA,YAAAgE,OAAA,CAAAvD,aAAA,CAAgB;;;;;IA8BvClC,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAU,SAAA,YAA2C;IAC3CV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAY,MAAA,oCAAwB;IAAAZ,EAAA,CAAAW,YAAA,EAAI;;;;;IAM/BX,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAU,SAAA,cAEO;IACPV,EAAA,CAAAC,cAAA,YAA8B;IAAAD,EAAA,CAAAY,MAAA,qCAA8B;IAAAZ,EAAA,CAAAW,YAAA,EAAI;;;;;IAiB5DX,EAAA,CAAAU,SAAA,cAGO;;;;;IASPV,EAAA,CAAAC,cAAA,YAAmD;IACjDD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAI;;;;IADFX,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAA4E,QAAA,CAAAC,IAAA,MACF;;;;;;IA3BN3F,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAE,UAAA,mBAAA0F,wEAAA;MAAA,MAAAzE,WAAA,GAAAnB,EAAA,CAAAI,aAAA,CAAAyF,IAAA;MAAA,MAAAH,QAAA,GAAAvE,WAAA,CAAAG,SAAA;MAAA,MAAAwE,OAAA,GAAA9F,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAsF,OAAA,CAAAT,yBAAA,CAAAK,QAAA,CAA+B;IAAA,EAAC;IAEzC1F,EAAA,CAAAC,cAAA,aAAyC;IAGrCD,EAAA,CAAAU,SAAA,cAIE;IACFV,EAAA,CAAA+B,UAAA,IAAAgE,wDAAA,kBAGO;IACT/F,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAC,cAAA,cAA4B;IAExBD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAK;IACLX,EAAA,CAAAC,cAAA,YAA0C;IAAAD,EAAA,CAAAY,MAAA,GAAgB;IAAAZ,EAAA,CAAAW,YAAA,EAAI;IAC9DX,EAAA,CAAA+B,UAAA,KAAAiE,uDAAA,gBAEI;IACNhG,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAC,cAAA,eAAwB;IAQpBD,EAAA,CAAAY,MAAA,IACF;IAAAZ,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAAgC;IAC9BD,EAAA,CAAAU,SAAA,aAA8B;IAChCV,EAAA,CAAAW,YAAA,EAAM;;;;;IAlCJX,EAAA,CAAAa,SAAA,GAAyD;IAAzDb,EAAA,CAAAyB,UAAA,QAAAiE,QAAA,CAAAd,KAAA,yCAAA5E,EAAA,CAAA4B,aAAA,CAAyD,QAAA8D,QAAA,CAAAb,QAAA;IAKxD7E,EAAA,CAAAa,SAAA,GAAwB;IAAxBb,EAAA,CAAAyB,UAAA,SAAAwE,OAAA,CAAAjD,YAAA,CAAA0C,QAAA,EAAwB;IAQzB1F,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAA4E,QAAA,CAAAb,QAAA,MACF;IAC0C7E,EAAA,CAAAa,SAAA,GAAgB;IAAhBb,EAAA,CAAA+E,iBAAA,CAAAW,QAAA,CAAAV,KAAA,CAAgB;IACxBhF,EAAA,CAAAa,SAAA,GAAe;IAAfb,EAAA,CAAAyB,UAAA,SAAAiE,QAAA,CAAAC,IAAA,CAAe;IAS/C3F,EAAA,CAAAa,SAAA,GAAyC;IAAzCb,EAAA,CAAA2C,WAAA,iBAAAsD,OAAA,CAAAjD,YAAA,CAAA0C,QAAA,EAAyC,mBAAAO,OAAA,CAAAjD,YAAA,CAAA0C,QAAA,mBAAAO,OAAA,CAAAjD,YAAA,CAAA0C,QAAA,qBAAAO,OAAA,CAAAjD,YAAA,CAAA0C,QAAA;IAKzC1F,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAmF,OAAA,CAAAjD,YAAA,CAAA0C,QAAA,mCACF;;;;;IAeF1F,EAAA,CAAAC,cAAA,WAA8B;IAAAD,EAAA,CAAAY,MAAA,mBAAY;IAAAZ,EAAA,CAAAW,YAAA,EAAO;;;;;IACjDX,EAAA,CAAAC,cAAA,WAA6B;IAAAD,EAAA,CAAAY,MAAA,oBAAa;IAAAZ,EAAA,CAAAW,YAAA,EAAO;;;;;;IAPrDX,EAAA,CAAAC,cAAA,cAAkD;IAE9CD,EAAA,CAAAE,UAAA,mBAAAgG,2EAAA;MAAAlG,EAAA,CAAAI,aAAA,CAAA+F,IAAA;MAAA,MAAAC,OAAA,GAAApG,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA4F,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAIzBrG,EAAA,CAAA+B,UAAA,IAAAuE,yDAAA,mBAAiD;IACjDtG,EAAA,CAAA+B,UAAA,IAAAwE,yDAAA,mBAAiD;IACnDvG,EAAA,CAAAW,YAAA,EAAS;;;;IALPX,EAAA,CAAAa,SAAA,GAA2B;IAA3Bb,EAAA,CAAAyB,UAAA,aAAA+E,OAAA,CAAAC,cAAA,CAA2B;IAGpBzG,EAAA,CAAAa,SAAA,GAAqB;IAArBb,EAAA,CAAAyB,UAAA,UAAA+E,OAAA,CAAAC,cAAA,CAAqB;IACrBzG,EAAA,CAAAa,SAAA,GAAoB;IAApBb,EAAA,CAAAyB,UAAA,SAAA+E,OAAA,CAAAC,cAAA,CAAoB;;;;;IAK/BzG,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAU,SAAA,YAA0C;IAC1CV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAY,MAAA,oCAAwB;IAAAZ,EAAA,CAAAW,YAAA,EAAI;;;;;IA/EnCX,EAAA,CAAAC,cAAA,UAA0B;IAExBD,EAAA,CAAA+B,UAAA,IAAA2E,kDAAA,kBAQM;IAGN1G,EAAA,CAAA+B,UAAA,IAAA4E,kDAAA,oBA8CM;IAGN3G,EAAA,CAAA+B,UAAA,IAAA6E,kDAAA,kBASM;IAGN5G,EAAA,CAAA+B,UAAA,IAAA8E,kDAAA,kBAMM;IACR7G,EAAA,CAAAW,YAAA,EAAM;;;;IA9EDX,EAAA,CAAAa,SAAA,GAA0C;IAA1Cb,EAAA,CAAAyB,UAAA,SAAAqF,OAAA,CAAAL,cAAA,IAAAK,OAAA,CAAAC,KAAA,CAAA9F,MAAA,OAA0C;IAW1BjB,EAAA,CAAAa,SAAA,GAAU;IAAVb,EAAA,CAAAyB,UAAA,YAAAqF,OAAA,CAAAC,KAAA,CAAU,iBAAAD,OAAA,CAAAE,aAAA;IAgDvBhH,EAAA,CAAAa,SAAA,GAAkB;IAAlBb,EAAA,CAAAyB,UAAA,SAAAqF,OAAA,CAAAG,YAAA,CAAkB;IAarBjH,EAAA,CAAAa,SAAA,GAA2C;IAA3Cb,EAAA,CAAAyB,UAAA,SAAAqF,OAAA,CAAAC,KAAA,CAAA9F,MAAA,WAAA6F,OAAA,CAAAL,cAAA,CAA2C;;;;;IA5HlDzG,EAAA,CAAAC,cAAA,cAAsD;IAEpDD,EAAA,CAAA+B,UAAA,IAAAmF,4CAAA,kBAmCM;IAGNlH,EAAA,CAAA+B,UAAA,IAAAoF,4CAAA,kBAMM;IAGNnH,EAAA,CAAA+B,UAAA,IAAAqF,4CAAA,kBAiFM;IACRpH,EAAA,CAAAW,YAAA,EAAM;;;;IAhIDX,EAAA,CAAAa,SAAA,GAA6C;IAA7Cb,EAAA,CAAAyB,UAAA,SAAA4F,MAAA,CAAA5C,WAAA,IAAA4C,MAAA,CAAAnF,aAAA,CAAAjB,MAAA,KAA6C;IAsC7CjB,EAAA,CAAAa,SAAA,GAA+C;IAA/Cb,EAAA,CAAAyB,UAAA,SAAA4F,MAAA,CAAA5C,WAAA,IAAA4C,MAAA,CAAAnF,aAAA,CAAAjB,MAAA,OAA+C;IAQ5CjB,EAAA,CAAAa,SAAA,GAAkB;IAAlBb,EAAA,CAAAyB,UAAA,UAAA4F,MAAA,CAAA5C,WAAA,CAAkB;;;;;IAyFxBzE,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAU,SAAA,cAEO;IACPV,EAAA,CAAAC,cAAA,YAA8B;IAAAD,EAAA,CAAAY,MAAA,sCAA+B;IAAAZ,EAAA,CAAAW,YAAA,EAAI;;;;;IAkD/DX,EAAA,CAAAU,SAAA,cAGO;;;;;;IAjDXV,EAAA,CAAAC,cAAA,cAQC;IADCD,EAAA,CAAAE,UAAA,mBAAAoH,kEAAA;MAAA,MAAAnG,WAAA,GAAAnB,EAAA,CAAAI,aAAA,CAAAmH,IAAA;MAAA,MAAAC,gBAAA,GAAArG,WAAA,CAAAG,SAAA;MAAA,MAAAmG,OAAA,GAAAzH,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAiH,OAAA,CAAAC,sBAAA,CAAAF,gBAAA,CAAoC;IAAA,EAAC;IAE9CxH,EAAA,CAAAC,cAAA,cAAwC;IAUpCD,EAAA,CAAAU,SAAA,YAUK;IACPV,EAAA,CAAAW,YAAA,EAAM;IAGNX,EAAA,CAAAC,cAAA,cAA4B;IAExBD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAK;IACLX,EAAA,CAAAC,cAAA,YAAsC;IACpCD,EAAA,CAAAY,MAAA,GACF;IAAAZ,EAAA,CAAAW,YAAA,EAAI;IACJX,EAAA,CAAAC,cAAA,YAAsC;IACpCD,EAAA,CAAAY,MAAA,IACF;IAAAZ,EAAA,CAAAW,YAAA,EAAI;IAINX,EAAA,CAAA+B,UAAA,KAAA4F,mDAAA,kBAGO;IACT3H,EAAA,CAAAW,YAAA,EAAM;;;;;IA5CNX,EAAA,CAAA2C,WAAA,iBAAA6E,gBAAA,CAAAI,MAAA,CAA0C;IAOtC5H,EAAA,CAAAa,SAAA,GAAyD;IAAzDb,EAAA,CAAA2C,WAAA,gBAAA6E,gBAAA,CAAAK,IAAA,mBAAyD,iBAAAL,gBAAA,CAAAK,IAAA,wCAAAL,gBAAA,CAAAK,IAAA,sCAAAL,gBAAA,CAAAK,IAAA,uCAAAL,gBAAA,CAAAK,IAAA;IAQvD7H,EAAA,CAAAa,SAAA,GAAwD;IAAxDb,EAAA,CAAA2C,WAAA,eAAA6E,gBAAA,CAAAK,IAAA,mBAAwD,iBAAAL,gBAAA,CAAAK,IAAA,mCAAAL,gBAAA,CAAAK,IAAA,iCAAAL,gBAAA,CAAAK,IAAA,oDAAAL,gBAAA,CAAAK,IAAA;IAcxD7H,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAgH,OAAA,CAAAC,oBAAA,CAAAP,gBAAA,OACF;IAEExH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAA0G,gBAAA,CAAAQ,OAAA,MACF;IAEEhI,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAAgH,OAAA,CAAA5E,qBAAA,CAAAsE,gBAAA,CAAApE,SAAA,OACF;IAKCpD,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAyB,UAAA,UAAA+F,gBAAA,CAAAI,MAAA,CAA0B;;;;;IAOjC5H,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAU,SAAA,YAAyC;IACzCV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAY,MAAA,0BAAmB;IAAAZ,EAAA,CAAAW,YAAA,EAAI;IAC1BX,EAAA,CAAAC,cAAA,YAAwB;IACtBD,EAAA,CAAAY,MAAA,8EACF;IAAAZ,EAAA,CAAAW,YAAA,EAAI;;;;;IA3ERX,EAAA,CAAAC,cAAA,cAAsE;IAEpED,EAAA,CAAA+B,UAAA,IAAAkG,4CAAA,kBAQM;IAGNjI,EAAA,CAAA+B,UAAA,IAAAmG,4CAAA,oBAmDM;IAGNlI,EAAA,CAAA+B,UAAA,IAAAoG,4CAAA,kBASM;IACRnI,EAAA,CAAAW,YAAA,EAAM;;;;IA1EDX,EAAA,CAAAa,SAAA,GAA0D;IAA1Db,EAAA,CAAAyB,UAAA,SAAA2G,MAAA,CAAAC,sBAAA,IAAAD,MAAA,CAAApH,aAAA,CAAAC,MAAA,OAA0D;IAY3BjB,EAAA,CAAAa,SAAA,GACjB;IADiBb,EAAA,CAAAyB,UAAA,YAAA2G,MAAA,CAAApH,aAAA,CACjB,iBAAAoH,MAAA,CAAAE,qBAAA;IAoDdtI,EAAA,CAAAa,SAAA,GAA2D;IAA3Db,EAAA,CAAAyB,UAAA,SAAA2G,MAAA,CAAApH,aAAA,CAAAC,MAAA,WAAAmH,MAAA,CAAAC,sBAAA,CAA2D;;;ADnbtE,OAAM,MAAOE,sBAAsB;EAoCjCC,YACUC,cAA8B,EAC9BC,WAAwB,EACxBC,YAA0B,EAC1BC,KAAqB,EACrBC,MAAc,EACdC,GAAsB;IALtB,KAAAL,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IAvCb;IACA,KAAAC,WAAW,GAAgB,IAAI;IAC/B,KAAA7E,aAAa,GAAmB,EAAE;IAClC,KAAA6C,KAAK,GAAW,EAAE;IAClB,KAAA/F,aAAa,GAAmB,EAAE;IAElC;IACA,KAAAgI,SAAS,GAAgD,eAAe;IACxE,KAAAnG,sBAAsB,GAAkB,IAAI;IAC5C,KAAAoG,gBAAgB,GAAG,KAAK;IACxB,KAAAxE,WAAW,GAAG,KAAK;IAEnB;IACA,KAAAyE,WAAW,GAAG,EAAE;IAChB,KAAAhH,aAAa,GAA4B,EAAE;IAE3C;IACA,KAAA0B,sBAAsB,GAAG,KAAK;IAC9B,KAAA6C,cAAc,GAAG,KAAK;IACtB,KAAA4B,sBAAsB,GAAG,KAAK;IAE9B;IACA,KAAAc,iBAAiB,GAAG,CAAC;IACrB,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAhF,oBAAoB,GAAG,IAAI;IAC3B,KAAA6C,YAAY,GAAG,IAAI;IAEnB;IACQ,KAAAoC,aAAa,GAAmB,EAAE;IAE1C;IACQ,KAAAC,YAAY,GAAG,IAAIvJ,eAAe,CAAS,EAAE,CAAC;EASnD;EAEHwJ,QAAQA,CAAA;IACN,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,eAAe,EAAE;EACxB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACN,aAAa,CAACO,OAAO,CAAEC,GAAG,IAAKA,GAAG,CAACC,WAAW,EAAE,CAAC;EACxD;EAEA;EACA;EACA;EAEQN,mBAAmBA,CAAA;IACzB;IACA,IAAI,CAACT,WAAW,GAAG,IAAI,CAACL,WAAW,CAACqB,cAAc,EAAE;IAEpD,IAAI,CAAC,IAAI,CAAChB,WAAW,EAAE;MACrB,IAAI,CAACF,MAAM,CAACmB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAChC;;IAGF;IACA,IAAI,CAACpB,KAAK,CAACqB,MAAM,CAACC,SAAS,CAAED,MAAM,IAAI;MACrC,MAAME,cAAc,GAAGF,MAAM,CAAC,gBAAgB,CAAC;MAC/C,IAAIE,cAAc,EAAE;QAClB,IAAI,CAACtH,sBAAsB,GAAGsH,cAAc;QAC5C,IAAI,CAACC,0BAA0B,CAACD,cAAc,CAAC;;IAEnD,CAAC,CAAC;EACJ;EAEQV,kBAAkBA,CAAA;IACxB;IACA,MAAMY,WAAW,GAAG,IAAI,CAAC5B,cAAc,CACpC6B,mBAAmB,EAAE,CACrBJ,SAAS,CAAEK,OAAO,IAAI;MACrB,IAAIA,OAAO,EAAE;QACX,IAAI,CAACC,gBAAgB,CAACD,OAAO,CAAC;;IAElC,CAAC,CAAC;IAEJ;IACA,MAAME,gBAAgB,GAAG,IAAI,CAAChC,cAAc,CACzCiC,wBAAwB,EAAE,CAC1BR,SAAS,CAAES,YAAY,IAAI;MAC1B,IAAIA,YAAY,EAAE;QAChB,IAAI,CAACC,qBAAqB,CAACD,YAAY,CAAC;;IAE5C,CAAC,CAAC;IAEJ;IACA,MAAME,SAAS,GAAG,IAAI,CAACvB,YAAY,CAChCwB,IAAI,EAGH,CACDZ,SAAS,CAAEa,KAAK,IAAI;MACnB,IAAI,CAACC,aAAa,CAACD,KAAK,CAAC;IAC3B,CAAC,CAAC;IAEJ,IAAI,CAAC1B,aAAa,CAAC4B,IAAI,CAACZ,WAAW,EAAEI,gBAAgB,EAAEI,SAAS,CAAC;EACnE;EAEQnB,eAAeA,CAAA;IACrB,IAAI,CAACwB,iBAAiB,EAAE;IACxB,IAAI,CAACC,SAAS,EAAE;IAChB,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEA;EACA;EACA;EAEAF,iBAAiBA,CAACG,IAAA,GAAe,CAAC;IAChC,IAAI,IAAI,CAACzH,sBAAsB,EAAE;IAEjC,IAAI,CAACA,sBAAsB,GAAG,IAAI;IAElC,IAAI,CAAC6E,cAAc,CAAC6C,gBAAgB,CAAC,EAAE,EAAED,IAAI,CAAC,CAACnB,SAAS,CAAC;MACvDqB,IAAI,EAAGrH,aAAa,IAAI;QACtB,IAAImH,IAAI,KAAK,CAAC,EAAE;UACd,IAAI,CAACnH,aAAa,GAAGA,aAAa;SACnC,MAAM;UACL,IAAI,CAACA,aAAa,CAAC+G,IAAI,CAAC,GAAG/G,aAAa,CAAC;;QAG3C,IAAI,CAACiF,iBAAiB,GAAGkC,IAAI;QAC7B,IAAI,CAACjH,oBAAoB,GAAGF,aAAa,CAACjD,MAAM,KAAK,EAAE;QACvD,IAAI,CAAC2C,sBAAsB,GAAG,KAAK;QACnC,IAAI,CAACkF,GAAG,CAAC0C,aAAa,EAAE;MAC1B,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;QACpE,IAAI,CAAC7H,sBAAsB,GAAG,KAAK;QACnC,IAAI,CAAC+E,YAAY,CAACgD,SAAS,CACzB,6CAA6C,CAC9C;MACH;KACD,CAAC;EACJ;EAEAR,SAASA,CAACE,IAAA,GAAe,CAAC;IACxB,IAAI,IAAI,CAAC5E,cAAc,EAAE;IAEzB,IAAI,CAACA,cAAc,GAAG,IAAI;IAE1B,IAAI,CAACgC,cAAc,CAACmD,WAAW,CAAC,KAAK,EAAE,EAAE,EAAEP,IAAI,EAAE,EAAE,CAAC,CAACnB,SAAS,CAAC;MAC7DqB,IAAI,EAAGxE,KAAK,IAAI;QACd,IAAIsE,IAAI,KAAK,CAAC,EAAE;UACd,IAAI,CAACtE,KAAK,GAAGA,KAAK;SACnB,MAAM;UACL,IAAI,CAACA,KAAK,CAACkE,IAAI,CAAC,GAAGlE,KAAK,CAAC;;QAG3B,IAAI,CAACqC,SAAS,GAAGiC,IAAI;QACrB,IAAI,CAACpE,YAAY,GAAGF,KAAK,CAAC9F,MAAM,KAAK,EAAE;QACvC,IAAI,CAACwF,cAAc,GAAG,KAAK;QAC3B,IAAI,CAACqC,GAAG,CAAC0C,aAAa,EAAE;MAC1B,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;QACnE,IAAI,CAAChF,cAAc,GAAG,KAAK;QAC3B,IAAI,CAACkC,YAAY,CAACgD,SAAS,CACzB,4CAA4C,CAC7C;MACH;KACD,CAAC;EACJ;EAEAP,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAAC/C,sBAAsB,EAAE;IAEjC,IAAI,CAACA,sBAAsB,GAAG,IAAI;IAElC,IAAI,CAACI,cAAc,CAACoD,gBAAgB,EAAE,CAAC3B,SAAS,CAAC;MAC/CqB,IAAI,EAAGvK,aAAa,IAAI;QACtB,IAAI,CAACA,aAAa,GAAGA,aAAa;QAClC,IAAI,CAACqH,sBAAsB,GAAG,KAAK;QACnC,IAAI,CAACS,GAAG,CAAC0C,aAAa,EAAE;MAC1B,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;QACpE,IAAI,CAACpD,sBAAsB,GAAG,KAAK;QACnC,IAAI,CAACM,YAAY,CAACgD,SAAS,CACzB,6CAA6C,CAC9C;MACH;KACD,CAAC;EACJ;EAEA;EACA;EACA;EAEQnB,gBAAgBA,CAACD,OAAgB;IACvC;IACA,MAAMuB,iBAAiB,GAAG,IAAI,CAAC5H,aAAa,CAAC6H,SAAS,CACnDC,IAAI,IAAKA,IAAI,CAAClJ,EAAE,KAAKyH,OAAO,CAACJ,cAAc,CAC7C;IAED,IAAI2B,iBAAiB,KAAK,CAAC,CAAC,EAAE;MAC5B;MACA,IAAI,CAAC5H,aAAa,CAAC4H,iBAAiB,CAAC,CAAC3I,WAAW,GAAGoH,OAAO;MAE3D;MACA,MAAM0B,YAAY,GAAG,IAAI,CAAC/H,aAAa,CAACgI,MAAM,CAACJ,iBAAiB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACvE,IAAI,CAAC5H,aAAa,CAACiI,OAAO,CAACF,YAAY,CAAC;MAExC,IAAI,CAACnD,GAAG,CAAC0C,aAAa,EAAE;;EAE5B;EAEQZ,qBAAqBA,CAACD,YAA0B;IACtD;IACA,IAAI,CAAC3J,aAAa,CAACmL,OAAO,CAACxB,YAAY,CAAC;IACxC,IAAI,CAAC7B,GAAG,CAAC0C,aAAa,EAAE;IAExB;IACA,IAAI,IAAI,CAACxC,SAAS,KAAK,eAAe,EAAE;MACtC,IAAI,CAACL,YAAY,CAACyD,QAAQ,CAAC,6BAA6B,CAAC;;EAE7D;EAEQhC,0BAA0BA,CAACD,cAAsB;IACvD;IACA,IAAI,CAACtH,sBAAsB,GAAGsH,cAAc;IAC5C,IAAI,CAACrB,GAAG,CAAC0C,aAAa,EAAE;EAC1B;EAEA;EACA;EACA;EAEAa,SAASA,CAACC,GAAgD;IACxD,IAAI,CAACtD,SAAS,GAAGsD,GAAG;IACpB,IAAI,CAACpD,WAAW,GAAG,EAAE;IACrB,IAAI,CAAChH,aAAa,GAAG,EAAE;IACvB,IAAI,CAACuC,WAAW,GAAG,KAAK;IAExB;IACA,QAAQ6H,GAAG;MACT,KAAK,eAAe;QAClB,IAAI,IAAI,CAACpI,aAAa,CAACjD,MAAM,KAAK,CAAC,EAAE;UACnC,IAAI,CAACiK,iBAAiB,EAAE;;QAE1B;MACF,KAAK,OAAO;QACV,IAAI,IAAI,CAACnE,KAAK,CAAC9F,MAAM,KAAK,CAAC,EAAE;UAC3B,IAAI,CAACkK,SAAS,EAAE;;QAElB;MACF,KAAK,eAAe;QAClB,IAAI,IAAI,CAACnK,aAAa,CAACC,MAAM,KAAK,CAAC,EAAE;UACnC,IAAI,CAACmK,iBAAiB,EAAE;;QAE1B;;EAEN;EAEA5J,kBAAkBA,CAACyK,YAA0B;IAC3C,IAAI,CAACA,YAAY,CAACnJ,EAAE,EAAE;IAEtB,IAAI,CAACD,sBAAsB,GAAGoJ,YAAY,CAACnJ,EAAE;IAC7C,IAAI,CAAC+F,MAAM,CAACmB,QAAQ,CAAC,CAAC,WAAW,EAAEiC,YAAY,CAACnJ,EAAE,CAAC,CAAC;IAEpD;IACA,IAAI,CAACmG,gBAAgB,GAAG,KAAK;EAC/B;EAEA5D,yBAAyBA,CAACkH,IAAU;IAClC,IAAI,CAACA,IAAI,CAACzJ,EAAE,IAAI,CAACyJ,IAAI,CAACC,GAAG,EAAE;IAE3B,MAAMC,MAAM,GAAGF,IAAI,CAACzJ,EAAE,IAAIyJ,IAAI,CAACC,GAAI;IAEnC;IACA,IAAI,CAAC/D,cAAc,CAACiE,uBAAuB,CAACD,MAAM,CAAC,CAACvC,SAAS,CAAC;MAC5DqB,IAAI,EAAGU,YAAY,IAAI;QACrB,IAAI,CAACzK,kBAAkB,CAACyK,YAAY,CAAC;MACvC,CAAC;MACDR,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;QACtE,IAAI,CAAC9C,YAAY,CAACgD,SAAS,CACzB,+CAA+C,CAChD;MACH;KACD,CAAC;EACJ;EAEAgB,gBAAgBA,CAAA;IACd,IAAI,CAAC1D,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;EAChD;EAEA;EACA;EACA;EAEA2D,aAAaA,CAACC,KAAU;IACtB,MAAM9B,KAAK,GAAG8B,KAAK,CAACC,MAAM,CAACC,KAAK,CAACC,IAAI,EAAE;IACvC,IAAI,CAAC9D,WAAW,GAAG6B,KAAK;IACxB,IAAI,CAACzB,YAAY,CAACiC,IAAI,CAACR,KAAK,CAAC;EAC/B;EAEQC,aAAaA,CAACD,KAAa;IACjC,IAAI,CAACA,KAAK,EAAE;MACV,IAAI,CAAC7I,aAAa,GAAG,EAAE;MACvB,IAAI,CAACuC,WAAW,GAAG,KAAK;MACxB;;IAGF,IAAI,CAACA,WAAW,GAAG,IAAI;IAEvB,IAAI,IAAI,CAACuE,SAAS,KAAK,eAAe,EAAE;MACtC,IAAI,CAAC9G,aAAa,GAAG,IAAI,CAACgC,aAAa,CAAC+I,MAAM,CAAEjB,IAAI,IAClDA,IAAI,CAACjJ,OAAO,GACRiJ,IAAI,CAACkB,SAAS,EAAEC,WAAW,EAAE,CAACC,QAAQ,CAACrC,KAAK,CAACoC,WAAW,EAAE,CAAC,GAC3DnB,IAAI,CAAC/I,YAAY,EAAEoK,IAAI,CAAEC,CAAC,IACxBA,CAAC,CAACzI,QAAQ,EAAEsI,WAAW,EAAE,CAACC,QAAQ,CAACrC,KAAK,CAACoC,WAAW,EAAE,CAAC,CACxD,CACN;KACF,MAAM,IAAI,IAAI,CAACnE,SAAS,KAAK,OAAO,EAAE;MACrC,IAAI,CAAC9G,aAAa,GAAG,IAAI,CAAC6E,KAAK,CAACkG,MAAM,CACnCV,IAAI,IACHA,IAAI,CAAC1H,QAAQ,EAAEsI,WAAW,EAAE,CAACC,QAAQ,CAACrC,KAAK,CAACoC,WAAW,EAAE,CAAC,IAC1DZ,IAAI,CAACvH,KAAK,EAAEmI,WAAW,EAAE,CAACC,QAAQ,CAACrC,KAAK,CAACoC,WAAW,EAAE,CAAC,CAC1D;;IAGH,IAAI,CAACrE,GAAG,CAAC0C,aAAa,EAAE;EAC1B;EAEA/K,WAAWA,CAAA;IACT,IAAI,CAACyI,WAAW,GAAG,EAAE;IACrB,IAAI,CAAChH,aAAa,GAAG,EAAE;IACvB,IAAI,CAACuC,WAAW,GAAG,KAAK;IACxB,IAAI,CAAC6E,YAAY,CAACiC,IAAI,CAAC,EAAE,CAAC;EAC5B;EAEA;EACA;EACA;EAEA/H,qBAAqBA,CAAA;IACnB,IAAI,IAAI,CAACY,oBAAoB,IAAI,CAAC,IAAI,CAACR,sBAAsB,EAAE;MAC7D,IAAI,CAACsH,iBAAiB,CAAC,IAAI,CAAC/B,iBAAiB,GAAG,CAAC,CAAC;;EAEtD;EAEA9C,aAAaA,CAAA;IACX,IAAI,IAAI,CAACY,YAAY,IAAI,CAAC,IAAI,CAACR,cAAc,EAAE;MAC7C,IAAI,CAAC0E,SAAS,CAAC,IAAI,CAAC/B,SAAS,GAAG,CAAC,CAAC;;EAEtC;EAEA;EACA;EACA;EAEAvH,mBAAmBA,CAACoK,YAA0B;IAC5C,IAAIA,YAAY,CAAClJ,OAAO,EAAE;MACxB,OAAOkJ,YAAY,CAACiB,SAAS,IAAI,iBAAiB;;IAGpD,IAAI,CAAC,IAAI,CAACnE,WAAW,EAAE,OAAO,cAAc;IAE5C,MAAMwE,aAAa,GAAG,IAAI,CAACxE,WAAW,CAACjG,EAAE,IAAI,IAAI,CAACiG,WAAW,CAACyD,GAAG;IACjE,MAAMgB,gBAAgB,GAAGvB,YAAY,CAAChJ,YAAY,EAAEwK,IAAI,CACrDH,CAAC,IAAK,CAACA,CAAC,CAACxK,EAAE,IAAIwK,CAAC,CAACd,GAAG,MAAMe,aAAa,CACzC;IAED,OAAOC,gBAAgB,EAAE3I,QAAQ,IAAI,qBAAqB;EAC5D;EAEAlD,qBAAqBA,CAACsK,YAA0B;IAC9C,IAAIA,YAAY,CAAClJ,OAAO,EAAE;MACxB,OAAOkJ,YAAY,CAACyB,UAAU,IAAI,kCAAkC;;IAGtE,IAAI,CAAC,IAAI,CAAC3E,WAAW,EAAE,OAAO,mCAAmC;IAEjE,MAAMwE,aAAa,GAAG,IAAI,CAACxE,WAAW,CAACjG,EAAE,IAAI,IAAI,CAACiG,WAAW,CAACyD,GAAG;IACjE,MAAMgB,gBAAgB,GAAGvB,YAAY,CAAChJ,YAAY,EAAEwK,IAAI,CACrDH,CAAC,IAAK,CAACA,CAAC,CAACxK,EAAE,IAAIwK,CAAC,CAACd,GAAG,MAAMe,aAAa,CACzC;IAED,OAAOC,gBAAgB,EAAE5I,KAAK,IAAI,mCAAmC;EACvE;EAEA9C,qBAAqBA,CAACmK,YAA0B;IAC9C,IAAI,CAACA,YAAY,CAAC9I,WAAW,EAAE,OAAO,eAAe;IAErD,MAAMoH,OAAO,GAAG0B,YAAY,CAAC9I,WAAW;IAExC,IAAIoH,OAAO,CAAC1C,IAAI,KAAK,MAAM,EAAE;MAC3B,OAAO0C,OAAO,CAACvC,OAAO,IAAI,EAAE;KAC7B,MAAM,IAAIuC,OAAO,CAAC1C,IAAI,KAAK,OAAO,EAAE;MACnC,OAAO,UAAU;KAClB,MAAM,IAAI0C,OAAO,CAAC1C,IAAI,KAAK,MAAM,EAAE;MAClC,OAAO,YAAY;KACpB,MAAM,IAAI0C,OAAO,CAAC1C,IAAI,KAAK,eAAe,EAAE;MAC3C,OAAO,kBAAkB;KAC1B,MAAM,IAAI0C,OAAO,CAAC1C,IAAI,KAAK,OAAO,EAAE;MACnC,OAAO,UAAU;;IAGnB,OAAO,SAAS;EAClB;EAEA3E,qBAAqBA,CAACE,SAAoC;IACxD,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;IAEzB,MAAMuK,IAAI,GAAG,IAAIC,IAAI,CAACxK,SAAS,CAAC;IAChC,MAAMyK,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,MAAME,WAAW,GAAG,CAACD,GAAG,CAACE,OAAO,EAAE,GAAGJ,IAAI,CAACI,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAEvE,IAAID,WAAW,GAAG,CAAC,EAAE;MACnB,OAAO,aAAa;KACrB,MAAM,IAAIA,WAAW,GAAG,EAAE,EAAE;MAC3B,OAAOH,IAAI,CAACK,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;OACT,CAAC;KACH,MAAM,IAAIJ,WAAW,GAAG,GAAG,EAAE;MAC5B;MACA,OAAOH,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAO,CAAE,CAAC;KAC9D,MAAM;MACL,OAAOT,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;QACtCE,GAAG,EAAE,SAAS;QACdC,KAAK,EAAE;OACR,CAAC;;EAEN;EAEAlM,cAAcA,CAAC6J,YAA0B;IACvC,OAAOA,YAAY,CAACsC,WAAW,IAAI,CAAC;EACtC;EAEAvL,YAAYA,CAACuJ,IAAU;IACrB,OAAOA,IAAI,CAACiC,QAAQ,IAAI,KAAK;EAC/B;EAEArK,qBAAqBA,CAACsK,KAAa,EAAExC,YAA0B;IAC7D,OAAOA,YAAY,CAACnJ,EAAE,IAAImJ,YAAY,CAACO,GAAG,IAAIiC,KAAK,CAACC,QAAQ,EAAE;EAChE;EAEA1H,aAAaA,CAACyH,KAAa,EAAElC,IAAU;IACrC,OAAOA,IAAI,CAACzJ,EAAE,IAAIyJ,IAAI,CAACC,GAAG,IAAIiC,KAAK,CAACC,QAAQ,EAAE;EAChD;EAEApG,qBAAqBA,CAACmG,KAAa,EAAE9D,YAA0B;IAC7D,OAAOA,YAAY,CAAC7H,EAAE,IAAI6H,YAAY,CAAC6B,GAAG,IAAIiC,KAAK,CAACC,QAAQ,EAAE;EAChE;EAEAhH,sBAAsBA,CAACiD,YAA0B;IAC/C,IAAI,CAACA,YAAY,CAAC7H,EAAE,IAAI6H,YAAY,CAAC/C,MAAM,EAAE;IAE7C,IAAI,CAACa,cAAc,CAACf,sBAAsB,CAACiD,YAAY,CAAC7H,EAAE,CAAC,CAACoH,SAAS,CAAC;MACpEqB,IAAI,EAAEA,CAAA,KAAK;QACTZ,YAAY,CAAC/C,MAAM,GAAG,IAAI;QAC1B,IAAI,CAACkB,GAAG,CAAC0C,aAAa,EAAE;MAC1B,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CACX,uDAAuD,EACvDA,KAAK,CACN;QACD,IAAI,CAAC9C,YAAY,CAACgD,SAAS,CACzB,4CAA4C,CAC7C;MACH;KACD,CAAC;EACJ;EAEA;EACAvG,MAAMA,CAACuJ,IAAyB;IAC9B,OAAO,UAAU,IAAIA,IAAI,IAAI,OAAO,IAAIA,IAAI;EAC9C;EAEAC,cAAcA,CAACD,IAAyB;IACtC,OAAO,cAAc,IAAIA,IAAI,IAAI,SAAS,IAAIA,IAAI;EACpD;EAEA5G,oBAAoBA,CAAC4C,YAA0B;IAC7C,QAAQA,YAAY,CAAC9C,IAAI;MACvB,KAAK,aAAa;QAChB,OAAO,iBAAiB;MAC1B,KAAK,gBAAgB;QACnB,OAAO,eAAe;MACxB,KAAK,cAAc;QACjB,OAAO,sBAAsB;MAC/B,KAAK,kBAAkB;QACrB,OAAO,uBAAuB;MAChC,KAAK,cAAc;QACjB,OAAO,gBAAgB;MACzB;QACE,OAAO,cAAc;;EAE3B;;;uBAvfWU,sBAAsB,EAAAvI,EAAA,CAAA6O,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA/O,EAAA,CAAA6O,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAjP,EAAA,CAAA6O,iBAAA,CAAAK,EAAA,CAAAC,YAAA,GAAAnP,EAAA,CAAA6O,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAArP,EAAA,CAAA6O,iBAAA,CAAAO,EAAA,CAAAE,MAAA,GAAAtP,EAAA,CAAA6O,iBAAA,CAAA7O,EAAA,CAAAuP,iBAAA;IAAA;EAAA;;;YAAtBhH,sBAAsB;MAAAiH,SAAA;MAAAC,SAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;UCrBnC3P,EAAA,CAAAC,cAAA,aAAiE;UAavDD,EAAA,CAAAU,SAAA,aAIE;UACFV,EAAA,CAAAC,cAAA,UAAK;UAEDD,EAAA,CAAAY,MAAA,GACF;UAAAZ,EAAA,CAAAW,YAAA,EAAK;UACLX,EAAA,CAAAC,cAAA,WAAkC;UAAAD,EAAA,CAAAY,MAAA,gBAAQ;UAAAZ,EAAA,CAAAW,YAAA,EAAI;UAKlDX,EAAA,CAAAC,cAAA,iBAGC;UADCD,EAAA,CAAAE,UAAA,mBAAA2P,yDAAA;YAAA,OAASD,GAAA,CAAAjD,gBAAA,EAAkB;UAAA,EAAC;UAE5B3M,EAAA,CAAAU,SAAA,YAAuC;UACzCV,EAAA,CAAAW,YAAA,EAAS;UAIXX,EAAA,CAAAC,cAAA,eAAsB;UAIlBD,EAAA,CAAAE,UAAA,2BAAA4P,gEAAAC,MAAA;YAAA,OAAAH,GAAA,CAAA1G,WAAA,GAAA6G,MAAA;UAAA,EAAyB,mBAAAC,wDAAAD,MAAA;YAAA,OAChBH,GAAA,CAAAhD,aAAA,CAAAmD,MAAA,CAAqB;UAAA,EADL;UAH3B/P,EAAA,CAAAW,YAAA,EAOE;UACFX,EAAA,CAAAU,SAAA,aAAiE;UACjEV,EAAA,CAAA+B,UAAA,KAAAkO,yCAAA,qBAMS;UACXjQ,EAAA,CAAAW,YAAA,EAAM;UAIRX,EAAA,CAAAC,cAAA,eAAgD;UAQ5CD,EAAA,CAAAE,UAAA,mBAAAgQ,yDAAA;YAAA,OAASN,GAAA,CAAAvD,SAAA,CAAU,eAAe,CAAC;UAAA,EAAC;UAEpCrM,EAAA,CAAAU,SAAA,aAAoC;UACpCV,EAAA,CAAAC,cAAA,eAAqB;UAAAD,EAAA,CAAAY,MAAA,mBAAW;UAAAZ,EAAA,CAAAW,YAAA,EAAM;UAGxCX,EAAA,CAAAC,cAAA,kBAQC;UADCD,EAAA,CAAAE,UAAA,mBAAAiQ,yDAAA;YAAA,OAASP,GAAA,CAAAvD,SAAA,CAAU,OAAO,CAAC;UAAA,EAAC;UAE5BrM,EAAA,CAAAU,SAAA,aAAiC;UACjCV,EAAA,CAAAC,cAAA,eAAqB;UAAAD,EAAA,CAAAY,MAAA,gBAAQ;UAAAZ,EAAA,CAAAW,YAAA,EAAM;UAGrCX,EAAA,CAAAC,cAAA,kBAQC;UADCD,EAAA,CAAAE,UAAA,mBAAAkQ,yDAAA;YAAA,OAASR,GAAA,CAAAvD,SAAA,CAAU,eAAe,CAAC;UAAA,EAAC;UAEpCrM,EAAA,CAAAU,SAAA,aAAgC;UAChCV,EAAA,CAAAC,cAAA,eAAqB;UAAAD,EAAA,CAAAY,MAAA,qBAAa;UAAAZ,EAAA,CAAAW,YAAA,EAAM;UACxCX,EAAA,CAAA+B,UAAA,KAAAsO,uCAAA,mBAKO;UACTrQ,EAAA,CAAAW,YAAA,EAAS;UAIXX,EAAA,CAAAC,cAAA,eAAoD;UAIlDD,EAAA,CAAA+B,UAAA,KAAAuO,sCAAA,kBA0IM;UAKNtQ,EAAA,CAAA+B,UAAA,KAAAwO,sCAAA,kBAmIM;UAKNvQ,EAAA,CAAA+B,UAAA,KAAAyO,sCAAA,kBA6EM;UACRxQ,EAAA,CAAAW,YAAA,EAAM;UAMRX,EAAA,CAAAC,cAAA,eAA+C;UAKzCD,EAAA,CAAAE,UAAA,mBAAAuQ,yDAAA;YAAA,OAASb,GAAA,CAAAjD,gBAAA,EAAkB;UAAA,EAAC;UAE5B3M,EAAA,CAAAU,SAAA,aAAsC;UACxCV,EAAA,CAAAW,YAAA,EAAS;UAIXX,EAAA,CAAAC,cAAA,eAAoB;UAClBD,EAAA,CAAAU,SAAA,qBAA+B;UACjCV,EAAA,CAAAW,YAAA,EAAM;;;UAheNX,EAAA,CAAAa,SAAA,GAAkC;UAAlCb,EAAA,CAAA2C,WAAA,YAAAiN,GAAA,CAAA3G,gBAAA,CAAkC;UAQ1BjJ,EAAA,CAAAa,SAAA,GAAiE;UAAjEb,EAAA,CAAAyB,UAAA,SAAAmO,GAAA,CAAA7G,WAAA,kBAAA6G,GAAA,CAAA7G,WAAA,CAAAnE,KAAA,0CAAA5E,EAAA,CAAA4B,aAAA,CAAiE,QAAAgO,GAAA,CAAA7G,WAAA,kBAAA6G,GAAA,CAAA7G,WAAA,CAAAlE,QAAA;UAM/D7E,EAAA,CAAAa,SAAA,GACF;UADEb,EAAA,CAAAc,kBAAA,MAAA8O,GAAA,CAAA7G,WAAA,kBAAA6G,GAAA,CAAA7G,WAAA,CAAAlE,QAAA,MACF;UAmBF7E,EAAA,CAAAa,SAAA,GAAyB;UAAzBb,EAAA,CAAAyB,UAAA,YAAAmO,GAAA,CAAA1G,WAAA,CAAyB;UAOxBlJ,EAAA,CAAAa,SAAA,GAAiB;UAAjBb,EAAA,CAAAyB,UAAA,SAAAmO,GAAA,CAAA1G,WAAA,CAAiB;UAapBlJ,EAAA,CAAAa,SAAA,GAA8C;UAA9Cb,EAAA,CAAA2C,WAAA,WAAAiN,GAAA,CAAA5G,SAAA,qBAA8C,kBAAA4G,GAAA,CAAA5G,SAAA,oCAAA4G,GAAA,CAAA5G,SAAA,yCAAA4G,GAAA,CAAA5G,SAAA,uCAAA4G,GAAA,CAAA5G,SAAA;UAa9ChJ,EAAA,CAAAa,SAAA,GAAsC;UAAtCb,EAAA,CAAA2C,WAAA,WAAAiN,GAAA,CAAA5G,SAAA,aAAsC,kBAAA4G,GAAA,CAAA5G,SAAA,4BAAA4G,GAAA,CAAA5G,SAAA,iCAAA4G,GAAA,CAAA5G,SAAA,+BAAA4G,GAAA,CAAA5G,SAAA;UAatChJ,EAAA,CAAAa,SAAA,GAA8C;UAA9Cb,EAAA,CAAA2C,WAAA,WAAAiN,GAAA,CAAA5G,SAAA,qBAA8C,kBAAA4G,GAAA,CAAA5G,SAAA,oCAAA4G,GAAA,CAAA5G,SAAA,yCAAA4G,GAAA,CAAA5G,SAAA,uCAAA4G,GAAA,CAAA5G,SAAA;UAU3ChJ,EAAA,CAAAa,SAAA,GAA8B;UAA9Bb,EAAA,CAAAyB,UAAA,SAAAmO,GAAA,CAAA5O,aAAA,CAAAC,MAAA,KAA8B;UAa7BjB,EAAA,CAAAa,SAAA,GAAmC;UAAnCb,EAAA,CAAAyB,UAAA,SAAAmO,GAAA,CAAA5G,SAAA,qBAAmC;UA+InChJ,EAAA,CAAAa,SAAA,GAA2B;UAA3Bb,EAAA,CAAAyB,UAAA,SAAAmO,GAAA,CAAA5G,SAAA,aAA2B;UAwI3BhJ,EAAA,CAAAa,SAAA,GAAmC;UAAnCb,EAAA,CAAAyB,UAAA,SAAAmO,GAAA,CAAA5G,SAAA,qBAAmC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}