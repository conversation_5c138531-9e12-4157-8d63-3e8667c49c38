{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/authadmin.service\";\nimport * as i2 from \"src/app/services/authuser.service\";\nimport * as i3 from \"src/app/services/auth.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@app/services/theme.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nfunction AuthAdminLayoutComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 49)(2, \"div\", 34);\n    i0.ɵɵelement(3, \"i\", 50)(4, \"div\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 52);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.messageFromRedirect, \" \");\n  }\n}\nfunction AuthAdminLayoutComponent_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵelement(1, \"i\", 54);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r2 = i0.ɵɵreference(62);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (_r2.errors == null ? null : _r2.errors[\"required\"]) ? \"Email requis\" : \"Format email invalide\", \" \");\n  }\n}\nfunction AuthAdminLayoutComponent_div_74_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵelement(1, \"i\", 54);\n    i0.ɵɵtext(2, \" Mot de passe requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AuthAdminLayoutComponent_div_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵelement(1, \"i\", 56);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r6.messageAuthError);\n  }\n}\nexport class AuthAdminLayoutComponent {\n  constructor(authAdminService, authUserService, authService, router, route, themeService) {\n    this.authAdminService = authAdminService;\n    this.authUserService = authUserService;\n    this.authService = authService;\n    this.router = router;\n    this.route = route;\n    this.themeService = themeService;\n    this.messageAuthError = '';\n    this.messageFromRedirect = '';\n    this.destroy$ = new Subject();\n    this.checkExistingAuth();\n    this.isDarkMode$ = this.themeService.currentTheme$.pipe(map(theme => theme.name === 'dark'));\n  }\n  ngOnInit() {\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/admin/';\n    this.subscribeToQueryParams();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  checkExistingAuth() {\n    if (this.authUserService.userLoggedIn()) {\n      this.router.navigate(['/'], {\n        queryParams: {\n          message: \"Vous êtes déjà connecté en tant qu'utilisateur. Veuillez vous déconnecter d'abord.\"\n        }\n      });\n      return;\n    }\n    if (this.authAdminService.loggedIn()) {\n      this.router.navigateByUrl('/admin');\n    }\n  }\n  subscribeToQueryParams() {\n    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe(params => {\n      this.messageFromRedirect = params['message'] || '';\n      this.clearMessageAfterDelay();\n    });\n  }\n  clearMessageAfterDelay() {\n    if (this.messageFromRedirect) {\n      setTimeout(() => this.messageFromRedirect = '', 5000);\n    }\n  }\n  loginAdmin(form) {\n    if (!form.valid) {\n      this.messageAuthError = 'Veuillez remplir correctement le formulaire.';\n      return;\n    }\n    const data = form.value;\n    this.authAdminService.login(data).subscribe({\n      next: response => {\n        this.handleLoginSuccess(response);\n      },\n      error: err => {\n        this.handleLoginError(err);\n      }\n    });\n  }\n  handleLoginSuccess(response) {\n    this.authAdminService.saveDataProfil(response.token);\n    this.router.navigate([this.returnUrl]);\n  }\n  handleLoginError(err) {\n    this.messageAuthError = err.error?.message || 'Une erreur est survenue lors de la connexion';\n    // Effacer le message d'erreur après 5 secondes\n    setTimeout(() => this.messageAuthError = '', 5000);\n  }\n  static {\n    this.ɵfac = function AuthAdminLayoutComponent_Factory(t) {\n      return new (t || AuthAdminLayoutComponent)(i0.ɵɵdirectiveInject(i1.AuthadminService), i0.ɵɵdirectiveInject(i2.AuthuserService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i5.ThemeService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AuthAdminLayoutComponent,\n      selectors: [[\"app-auth-admin-layout\"]],\n      decls: 81,\n      vars: 8,\n      consts: [[\"class\", \"fixed top-4 right-4 left-4 md:left-auto md:w-96 bg-white dark:bg-[#1e1e1e] border-l-4 border-[#4f5fad] dark:border-[#6d78c9] rounded-lg p-4 shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.3)] z-50 backdrop-blur-sm\", 4, \"ngIf\"], [1, \"min-h-screen\", \"main-grid-container\", \"flex\", \"items-center\", \"justify-center\", \"p-4\", \"relative\", \"overflow-hidden\"], [1, \"background-grid\"], [1, \"absolute\", \"top-0\", \"left-0\", \"w-full\", \"h-full\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"top-[10%]\", \"left-[5%]\", \"w-64\", \"h-64\", \"rounded-full\", \"bg-gradient-to-br\", \"from-[#4f5fad]/10\", \"to-transparent\", \"dark:from-[#6d78c9]/5\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"absolute\", \"bottom-[10%]\", \"right-[5%]\", \"w-80\", \"h-80\", \"rounded-full\", \"bg-gradient-to-tl\", \"from-[#4f5fad]/10\", \"to-transparent\", \"dark:from-[#6d78c9]/5\", \"dark:to-transparent\", \"blur-3xl\"], [1, \"w-full\", \"max-w-4xl\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-2xl\", \"shadow-xl\", \"dark:shadow-[0_10px_30px_rgba(0,0,0,0.3)]\", \"overflow-hidden\", \"backdrop-blur-sm\", \"relative\", \"z-10\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\"], [1, \"flex\", \"flex-col\", \"md:flex-row\"], [1, \"md:w-1/2\", \"bg-gradient-to-br\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#2a3052]\", \"dark:to-[#4f5fad]\", \"hidden\", \"md:flex\", \"items-center\", \"justify-center\", \"p-12\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"top-0\", \"right-0\", \"w-32\", \"h-32\", \"bg-white/10\", \"rounded-full\", \"blur-3xl\", \"transform\", \"translate-x-16\", \"-translate-y-16\"], [1, \"absolute\", \"bottom-0\", \"left-0\", \"w-40\", \"h-40\", \"bg-white/10\", \"rounded-full\", \"blur-3xl\", \"transform\", \"-translate-x-20\", \"translate-y-20\"], [1, \"absolute\", \"inset-0\", \"opacity-10\"], [1, \"grid\", \"grid-cols-12\", \"h-full\"], [1, \"border-r\", \"border-white/20\"], [1, \"grid\", \"grid-rows-12\", \"w-full\", \"absolute\", \"top-0\", \"left-0\", \"h-full\"], [1, \"border-b\", \"border-white/20\"], [1, \"text-center\", \"text-white\", \"relative\", \"z-10\"], [1, \"text-3xl\", \"font-bold\", \"mb-4\", \"text-white\"], [1, \"text-white/80\"], [1, \"mt-8\", \"relative\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"none\", \"viewBox\", \"0 0 24 24\", \"stroke\", \"currentColor\", 1, \"h-24\", \"w-24\", \"mx-auto\", \"text-white\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"1.5\", \"d\", \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"], [1, \"absolute\", \"inset-0\", \"bg-white/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"md:w-1/2\", \"p-8\", \"md:p-12\", \"relative\"], [1, \"absolute\", \"top-0\", \"right-0\", \"w-20\", \"h-20\", \"bg-[#4f5fad]/5\", \"dark:bg-[#6d78c9]/5\", \"rounded-full\", \"blur-2xl\"], [1, \"absolute\", \"bottom-0\", \"left-0\", \"w-24\", \"h-24\", \"bg-[#4f5fad]/5\", \"dark:bg-[#6d78c9]/5\", \"rounded-full\", \"blur-2xl\"], [1, \"text-center\", \"mb-8\", \"relative\"], [1, \"text-2xl\", \"font-bold\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-2\"], [1, \"space-y-6\", \"relative\", \"z-10\", 3, \"ngSubmit\"], [\"f\", \"ngForm\"], [1, \"group\"], [\"for\", \"email\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-1\", \"transition-colors\"], [1, \"relative\"], [\"id\", \"email\", \"type\", \"email\", \"name\", \"email\", \"ngModel\", \"\", \"required\", \"\", \"email\", \"\", \"placeholder\", \"<EMAIL>\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"email\", \"ngModel\"], [1, \"absolute\", \"inset-y-0\", \"left-0\", \"pl-3\", \"flex\", \"items-center\", \"pointer-events-none\", \"opacity-0\", \"group-focus-within:opacity-100\", \"transition-opacity\"], [1, \"w-0.5\", \"h-4\", \"bg-gradient-to-b\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"rounded-full\"], [\"class\", \"text-[#ff6b69] dark:text-[#ff8785] text-xs mt-1 flex items-center\", 4, \"ngIf\"], [\"for\", \"password\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mb-1\", \"transition-colors\"], [\"id\", \"password\", \"type\", \"password\", \"name\", \"password\", \"ngModel\", \"\", \"required\", \"\", \"placeholder\", \"\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\", 1, \"w-full\", \"px-4\", \"py-3\", \"rounded-lg\", \"border\", \"border-[#bdc6cc]\", \"dark:border-[#2a2a2a]\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"text-[#6d6870]\", \"dark:text-[#e0e0e0]\", \"focus:border-[#4f5fad]\", \"dark:focus:border-[#6d78c9]\", \"focus:ring-2\", \"focus:ring-[#4f5fad]/20\", \"dark:focus:ring-[#6d78c9]/20\", \"transition-all\"], [\"password\", \"ngModel\"], [\"class\", \"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 text-[#ff6b69] dark:text-[#ff8785] p-3 rounded-lg text-sm flex items-start\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"w-full\", \"relative\", \"overflow-hidden\", \"group\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"transition-transform\", \"duration-300\", \"group-hover:scale-105\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#3d4a85]\", \"dark:to-[#6d78c9]\", \"rounded-lg\", \"opacity-0\", \"group-hover:opacity-100\", \"blur-xl\", \"transition-opacity\", \"duration-300\"], [1, \"relative\", \"block\", \"text-white\", \"font-bold\", \"py-3\", \"px-4\", \"rounded-lg\", \"transition-all\"], [1, \"fixed\", \"top-4\", \"right-4\", \"left-4\", \"md:left-auto\", \"md:w-96\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"border-l-4\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\", \"rounded-lg\", \"p-4\", \"shadow-md\", \"dark:shadow-[0_4px_20px_rgba(0,0,0,0.3)]\", \"z-50\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-center\"], [1, \"fas\", \"fa-info-circle\", \"text-[#4f5fad]\", \"dark:text-[#6d78c9]\", \"text-lg\", \"mr-3\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"text-sm\", \"font-medium\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"text-xs\", \"mt-1\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-exclamation-circle\", \"mr-1\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff6b69]/5\", \"border\", \"border-[#ff6b69]\", \"dark:border-[#ff6b69]/30\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"p-3\", \"rounded-lg\", \"text-sm\", \"flex\", \"items-start\"], [1, \"fas\", \"fa-exclamation-triangle\", \"mt-0.5\", \"mr-2\"]],\n      template: function AuthAdminLayoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r7 = i0.ɵɵgetCurrentView();\n          i0.ɵɵtemplate(0, AuthAdminLayoutComponent_div_0_Template, 7, 1, \"div\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1);\n          i0.ɵɵpipe(2, \"async\");\n          i0.ɵɵelement(3, \"div\", 2);\n          i0.ɵɵelementStart(4, \"div\", 3);\n          i0.ɵɵelement(5, \"div\", 4)(6, \"div\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7)(9, \"div\", 8);\n          i0.ɵɵelement(10, \"div\", 9)(11, \"div\", 10);\n          i0.ɵɵelementStart(12, \"div\", 11)(13, \"div\", 12);\n          i0.ɵɵelement(14, \"div\", 13)(15, \"div\", 13)(16, \"div\", 13)(17, \"div\", 13)(18, \"div\", 13)(19, \"div\", 13)(20, \"div\", 13)(21, \"div\", 13)(22, \"div\", 13)(23, \"div\", 13)(24, \"div\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"div\", 14);\n          i0.ɵɵelement(26, \"div\", 15)(27, \"div\", 15)(28, \"div\", 15)(29, \"div\", 15)(30, \"div\", 15)(31, \"div\", 15)(32, \"div\", 15)(33, \"div\", 15)(34, \"div\", 15)(35, \"div\", 15)(36, \"div\", 15);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"div\", 16)(38, \"h2\", 17);\n          i0.ɵɵtext(39, \" Espace Administrateur \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"p\", 18);\n          i0.ɵɵtext(41, \"Gestion compl\\u00E8te de votre plateforme\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"div\", 19);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(43, \"svg\", 20);\n          i0.ɵɵelement(44, \"path\", 21)(45, \"path\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelement(46, \"div\", 23);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(47, \"div\", 24);\n          i0.ɵɵelement(48, \"div\", 25)(49, \"div\", 26);\n          i0.ɵɵelementStart(50, \"div\", 27)(51, \"h1\", 28);\n          i0.ɵɵtext(52, \" Connexion Admin \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"p\", 29);\n          i0.ɵɵtext(54, \" Acc\\u00E9dez \\u00E0 votre tableau de bord \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(55, \"form\", 30, 31);\n          i0.ɵɵlistener(\"ngSubmit\", function AuthAdminLayoutComponent_Template_form_ngSubmit_55_listener() {\n            i0.ɵɵrestoreView(_r7);\n            const _r1 = i0.ɵɵreference(56);\n            return i0.ɵɵresetView(ctx.loginAdmin(_r1));\n          });\n          i0.ɵɵelementStart(57, \"div\", 32)(58, \"label\", 33);\n          i0.ɵɵtext(59, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"div\", 34);\n          i0.ɵɵelement(61, \"input\", 35, 36);\n          i0.ɵɵelementStart(63, \"div\", 37);\n          i0.ɵɵelement(64, \"div\", 38);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(65, AuthAdminLayoutComponent_div_65_Template, 3, 1, \"div\", 39);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"div\", 32)(67, \"label\", 40);\n          i0.ɵɵtext(68, \"Mot de passe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"div\", 34);\n          i0.ɵɵelement(70, \"input\", 41, 42);\n          i0.ɵɵelementStart(72, \"div\", 37);\n          i0.ɵɵelement(73, \"div\", 38);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(74, AuthAdminLayoutComponent_div_74_Template, 3, 0, \"div\", 39);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(75, AuthAdminLayoutComponent_div_75_Template, 4, 1, \"div\", 43);\n          i0.ɵɵelementStart(76, \"button\", 44);\n          i0.ɵɵelement(77, \"div\", 45)(78, \"div\", 46);\n          i0.ɵɵelementStart(79, \"span\", 47);\n          i0.ɵɵtext(80, \" Se connecter \");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          const _r2 = i0.ɵɵreference(62);\n          const _r4 = i0.ɵɵreference(71);\n          i0.ɵɵproperty(\"ngIf\", ctx.messageFromRedirect);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"dark\", i0.ɵɵpipeBind1(2, 6, ctx.isDarkMode$));\n          i0.ɵɵadvance(64);\n          i0.ɵɵproperty(\"ngIf\", _r2.invalid && (_r2.dirty || _r2.touched));\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", _r4.invalid && (_r4.dirty || _r4.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.messageAuthError);\n        }\n      },\n      dependencies: [i6.NgIf, i7.ɵNgNoValidate, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgControlStatusGroup, i7.RequiredValidator, i7.EmailValidator, i7.NgModel, i7.NgForm, i6.AsyncPipe],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n.notification[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 20px;\\n  right: 20px;\\n  padding: 15px 25px;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  z-index: 1000;\\n  display: flex;\\n  align-items: center;\\n  max-width: 350px;\\n  animation: _ngcontent-%COMP%_slideIn 0.3s ease-out forwards;\\n  font-family: \\\"Segoe UI\\\", Roboto, sans-serif;\\n  font-size: 14px;\\n  line-height: 1.5;\\n}\\n\\n.notification-info[_ngcontent-%COMP%] {\\n  background-color: #e6f7ff;\\n  color: #0052cc;\\n  border-left: 4px solid #1890ff;\\n}\\n\\n.notification-error[_ngcontent-%COMP%] {\\n  background-color: #fff1f0;\\n  color: #cf1322;\\n  border-left: 4px solid #ff4d4f;\\n}\\n\\n.notification-success[_ngcontent-%COMP%] {\\n  background-color: #f6ffed;\\n  color: #389e0d;\\n  border-left: 4px solid #52c41a;\\n}\\n\\n\\n\\n.notification-icon[_ngcontent-%COMP%] {\\n  margin-right: 12px;\\n  font-size: 18px;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_slideIn {\\n  from {\\n    transform: translateX(100%);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateX(0);\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_fadeOut {\\n  to {\\n    opacity: 0;\\n    transform: translateY(-20px);\\n  }\\n}\\n\\n\\n.notification-auto-hide[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeOut 0.5s ease-in 4.5s forwards;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "map", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "messageFromRedirect", "_r2", "errors", "ɵɵtextInterpolate", "ctx_r6", "messageAuthError", "AuthAdminLayoutComponent", "constructor", "authAdminService", "authUserService", "authService", "router", "route", "themeService", "destroy$", "checkExistingAuth", "isDarkMode$", "currentTheme$", "pipe", "theme", "name", "ngOnInit", "returnUrl", "snapshot", "queryParams", "subscribeToQueryParams", "ngOnDestroy", "next", "complete", "userLoggedIn", "navigate", "message", "loggedIn", "navigateByUrl", "subscribe", "params", "clearMessageAfterDelay", "setTimeout", "loginAdmin", "form", "valid", "data", "value", "login", "response", "handleLoginSuccess", "error", "err", "handleLoginError", "saveDataProfil", "token", "ɵɵdirectiveInject", "i1", "AuthadminService", "i2", "AuthuserService", "i3", "AuthService", "i4", "Router", "ActivatedRoute", "i5", "ThemeService", "selectors", "decls", "vars", "consts", "template", "AuthAdminLayoutComponent_Template", "rf", "ctx", "ɵɵtemplate", "AuthAdminLayoutComponent_div_0_Template", "ɵɵnamespaceSVG", "ɵɵnamespaceHTML", "ɵɵlistener", "AuthAdminLayoutComponent_Template_form_ngSubmit_55_listener", "ɵɵrestoreView", "_r7", "_r1", "ɵɵreference", "ɵɵresetView", "AuthAdminLayoutComponent_div_65_Template", "AuthAdminLayoutComponent_div_74_Template", "AuthAdminLayoutComponent_div_75_Template", "ɵɵproperty", "ɵɵclassProp", "ɵɵpipeBind1", "invalid", "dirty", "touched", "_r4"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\layouts\\auth-admin-layout\\auth-admin-layout.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\layouts\\auth-admin-layout\\auth-admin-layout.component.html"], "sourcesContent": ["import { HttpErrorResponse } from '@angular/common/http';\nimport { Component, OnDestroy, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { AuthadminService } from 'src/app/services/authadmin.service';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport { Observable, Subject, takeUntil } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { AuthService } from 'src/app/services/auth.service';\nimport { ThemeService } from '@app/services/theme.service';\n@Component({\n  selector: 'app-auth-admin-layout',\n  templateUrl: './auth-admin-layout.component.html',\n  styleUrls: ['./auth-admin-layout.component.css'],\n})\nexport class AuthAdminLayoutComponent implements OnInit, OnDestroy {\n  messageAuthError: string = '';\n  messageFromRedirect: string = '';\n  private destroy$ = new Subject<void>();\n  private returnUrl: string | undefined;\n  isDarkMode$: Observable<boolean>;\n\n  constructor(\n    private authAdminService: AuthadminService,\n    public authUserService: AuthuserService,\n    public authService: AuthService,\n    private router: Router,\n    private route: ActivatedRoute,\n    private themeService: ThemeService\n  ) {\n    this.checkExistingAuth();\n    this.isDarkMode$ = this.themeService.currentTheme$.pipe(\n      map((theme) => theme.name === 'dark')\n    );\n  }\n\n  ngOnInit(): void {\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/admin/';\n    this.subscribeToQueryParams();\n  }\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  private checkExistingAuth(): void {\n    if (this.authUserService.userLoggedIn()) {\n      this.router.navigate(['/'], {\n        queryParams: {\n          message:\n            \"Vous êtes déjà connecté en tant qu'utilisateur. Veuillez vous déconnecter d'abord.\",\n        },\n      });\n      return;\n    }\n\n    if (this.authAdminService.loggedIn()) {\n      this.router.navigateByUrl('/admin');\n    }\n  }\n  private subscribeToQueryParams(): void {\n    this.route.queryParams\n      .pipe(takeUntil(this.destroy$))\n      .subscribe((params) => {\n        this.messageFromRedirect = params['message'] || '';\n        this.clearMessageAfterDelay();\n      });\n  }\n  private clearMessageAfterDelay(): void {\n    if (this.messageFromRedirect) {\n      setTimeout(() => (this.messageFromRedirect = ''), 5000);\n    }\n  }\n  loginAdmin(form: any): void {\n    if (!form.valid) {\n      this.messageAuthError = 'Veuillez remplir correctement le formulaire.';\n      return;\n    }\n    const data = form.value;\n    this.authAdminService.login(data).subscribe({\n      next: (response) => {\n        this.handleLoginSuccess(response);\n      },\n      error: (err: HttpErrorResponse) => {\n        this.handleLoginError(err);\n      },\n    });\n  }\n  private handleLoginSuccess(response: any): void {\n    this.authAdminService.saveDataProfil(response.token);\n    this.router.navigate([this.returnUrl]);\n  }\n  private handleLoginError(err: HttpErrorResponse): void {\n    this.messageAuthError =\n      err.error?.message || 'Une erreur est survenue lors de la connexion';\n\n    // Effacer le message d'erreur après 5 secondes\n    setTimeout(() => (this.messageAuthError = ''), 5000);\n  }\n}\n", "<!-- Notification message -->\n<div\n  *ngIf=\"messageFromRedirect\"\n  class=\"fixed top-4 right-4 left-4 md:left-auto md:w-96 bg-white dark:bg-[#1e1e1e] border-l-4 border-[#4f5fad] dark:border-[#6d78c9] rounded-lg p-4 shadow-md dark:shadow-[0_4px_20px_rgba(0,0,0,0.3)] z-50 backdrop-blur-sm\"\n>\n  <div class=\"flex items-center\">\n    <div class=\"relative\">\n      <i\n        class=\"fas fa-info-circle text-[#4f5fad] dark:text-[#6d78c9] text-lg mr-3\"\n      ></i>\n      <!-- Glow effect -->\n      <div\n        class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\n      ></div>\n    </div>\n    <p class=\"text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0]\">\n      {{ messageFromRedirect }}\n    </p>\n  </div>\n</div>\n\n<div\n  class=\"min-h-screen main-grid-container flex items-center justify-center p-4 relative overflow-hidden\"\n  [class.dark]=\"isDarkMode$ | async\"\n>\n  <!-- Background Grid -->\n  <div class=\"background-grid\"></div>\n\n  <!-- Background decorative elements -->\n  <div\n    class=\"absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none\"\n  >\n    <!-- Decorative circles -->\n    <div\n      class=\"absolute top-[10%] left-[5%] w-64 h-64 rounded-full bg-gradient-to-br from-[#4f5fad]/10 to-transparent dark:from-[#6d78c9]/5 dark:to-transparent blur-3xl\"\n    ></div>\n    <div\n      class=\"absolute bottom-[10%] right-[5%] w-80 h-80 rounded-full bg-gradient-to-tl from-[#4f5fad]/10 to-transparent dark:from-[#6d78c9]/5 dark:to-transparent blur-3xl\"\n    ></div>\n  </div>\n\n  <div\n    class=\"w-full max-w-4xl bg-white dark:bg-[#1e1e1e] rounded-2xl shadow-xl dark:shadow-[0_10px_30px_rgba(0,0,0,0.3)] overflow-hidden backdrop-blur-sm relative z-10 border border-[#edf1f4]/50 dark:border-[#2a2a2a]\"\n  >\n    <div class=\"flex flex-col md:flex-row\">\n      <!-- Image Section -->\n      <div\n        class=\"md:w-1/2 bg-gradient-to-br from-[#3d4a85] to-[#4f5fad] dark:from-[#2a3052] dark:to-[#4f5fad] hidden md:flex items-center justify-center p-12 relative overflow-hidden\"\n      >\n        <!-- Decorative elements -->\n        <div\n          class=\"absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full blur-3xl transform translate-x-16 -translate-y-16\"\n        ></div>\n        <div\n          class=\"absolute bottom-0 left-0 w-40 h-40 bg-white/10 rounded-full blur-3xl transform -translate-x-20 translate-y-20\"\n        ></div>\n\n        <!-- Grid pattern - Harmonisé avec la grille commune -->\n        <div class=\"absolute inset-0 opacity-10\">\n          <div class=\"grid grid-cols-12 h-full\">\n            <div class=\"border-r border-white/20\"></div>\n            <div class=\"border-r border-white/20\"></div>\n            <div class=\"border-r border-white/20\"></div>\n            <div class=\"border-r border-white/20\"></div>\n            <div class=\"border-r border-white/20\"></div>\n            <div class=\"border-r border-white/20\"></div>\n            <div class=\"border-r border-white/20\"></div>\n            <div class=\"border-r border-white/20\"></div>\n            <div class=\"border-r border-white/20\"></div>\n            <div class=\"border-r border-white/20\"></div>\n            <div class=\"border-r border-white/20\"></div>\n          </div>\n          <div class=\"grid grid-rows-12 w-full absolute top-0 left-0 h-full\">\n            <div class=\"border-b border-white/20\"></div>\n            <div class=\"border-b border-white/20\"></div>\n            <div class=\"border-b border-white/20\"></div>\n            <div class=\"border-b border-white/20\"></div>\n            <div class=\"border-b border-white/20\"></div>\n            <div class=\"border-b border-white/20\"></div>\n            <div class=\"border-b border-white/20\"></div>\n            <div class=\"border-b border-white/20\"></div>\n            <div class=\"border-b border-white/20\"></div>\n            <div class=\"border-b border-white/20\"></div>\n            <div class=\"border-b border-white/20\"></div>\n          </div>\n        </div>\n\n        <div class=\"text-center text-white relative z-10\">\n          <h2 class=\"text-3xl font-bold mb-4 text-white\">\n            Espace Administrateur\n          </h2>\n          <p class=\"text-white/80\">Gestion complète de votre plateforme</p>\n          <div class=\"mt-8 relative\">\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              class=\"h-24 w-24 mx-auto text-white\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"1.5\"\n                d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n              />\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                stroke-width=\"1.5\"\n                d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n              />\n            </svg>\n            <!-- Glow effect -->\n            <div\n              class=\"absolute inset-0 bg-white/20 blur-xl rounded-full transform scale-150 -z-10\"\n            ></div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Form Section -->\n      <div class=\"md:w-1/2 p-8 md:p-12 relative\">\n        <!-- Decorative elements -->\n        <div\n          class=\"absolute top-0 right-0 w-20 h-20 bg-[#4f5fad]/5 dark:bg-[#6d78c9]/5 rounded-full blur-2xl\"\n        ></div>\n        <div\n          class=\"absolute bottom-0 left-0 w-24 h-24 bg-[#4f5fad]/5 dark:bg-[#6d78c9]/5 rounded-full blur-2xl\"\n        ></div>\n\n        <div class=\"text-center mb-8 relative\">\n          <h1\n            class=\"text-2xl font-bold bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent\"\n          >\n            Connexion Admin\n          </h1>\n          <p class=\"text-[#6d6870] dark:text-[#a0a0a0] mt-2\">\n            Accédez à votre tableau de bord\n          </p>\n        </div>\n\n        <form\n          #f=\"ngForm\"\n          (ngSubmit)=\"loginAdmin(f)\"\n          class=\"space-y-6 relative z-10\"\n        >\n          <!-- Email Input -->\n          <div class=\"group\">\n            <label\n              for=\"email\"\n              class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1 transition-colors\"\n              >Email</label\n            >\n            <div class=\"relative\">\n              <input\n                id=\"email\"\n                type=\"email\"\n                name=\"email\"\n                #email=\"ngModel\"\n                ngModel\n                required\n                email\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\n                placeholder=\"<EMAIL>\"\n              />\n              <div\n                class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none opacity-0 group-focus-within:opacity-100 transition-opacity\"\n              >\n                <div\n                  class=\"w-0.5 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full\"\n                ></div>\n              </div>\n            </div>\n            <div\n              *ngIf=\"email.invalid && (email.dirty || email.touched)\"\n              class=\"text-[#ff6b69] dark:text-[#ff8785] text-xs mt-1 flex items-center\"\n            >\n              <i class=\"fas fa-exclamation-circle mr-1\"></i>\n              {{ email.errors?.['required'] ? 'Email requis' : 'Format email invalide' }}\n            </div>\n          </div>\n\n          <!-- Password Input -->\n          <div class=\"group\">\n            <label\n              for=\"password\"\n              class=\"block text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0] mb-1 transition-colors\"\n              >Mot de passe</label\n            >\n            <div class=\"relative\">\n              <input\n                id=\"password\"\n                type=\"password\"\n                name=\"password\"\n                #password=\"ngModel\"\n                ngModel\n                required\n                class=\"w-full px-4 py-3 rounded-lg border border-[#bdc6cc] dark:border-[#2a2a2a] bg-white dark:bg-[#1e1e1e] text-[#6d6870] dark:text-[#e0e0e0] focus:border-[#4f5fad] dark:focus:border-[#6d78c9] focus:ring-2 focus:ring-[#4f5fad]/20 dark:focus:ring-[#6d78c9]/20 transition-all\"\n                placeholder=\"••••••••\"\n              />\n              <div\n                class=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none opacity-0 group-focus-within:opacity-100 transition-opacity\"\n              >\n                <div\n                  class=\"w-0.5 h-4 bg-gradient-to-b from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] rounded-full\"\n                ></div>\n              </div>\n            </div>\n            <div\n              *ngIf=\"password.invalid && (password.dirty || password.touched)\"\n              class=\"text-[#ff6b69] dark:text-[#ff8785] text-xs mt-1 flex items-center\"\n            >\n              <i class=\"fas fa-exclamation-circle mr-1\"></i>\n              Mot de passe requis\n            </div>\n          </div>\n\n          <!-- Error Message -->\n          <div\n            *ngIf=\"messageAuthError\"\n            class=\"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 text-[#ff6b69] dark:text-[#ff8785] p-3 rounded-lg text-sm flex items-start\"\n          >\n            <i class=\"fas fa-exclamation-triangle mt-0.5 mr-2\"></i>\n            <span>{{ messageAuthError }}</span>\n          </div>\n\n          <!-- Submit Button -->\n          <button type=\"submit\" class=\"w-full relative overflow-hidden group\">\n            <div\n              class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg transition-transform duration-300 group-hover:scale-105\"\n            ></div>\n            <div\n              class=\"absolute inset-0 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#3d4a85] dark:to-[#6d78c9] rounded-lg opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-300\"\n            ></div>\n            <span\n              class=\"relative block text-white font-bold py-3 px-4 rounded-lg transition-all\"\n            >\n              Se connecter\n            </span>\n          </button>\n        </form>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAKA,SAAqBA,OAAO,EAAEC,SAAS,QAAQ,MAAM;AACrD,SAASC,GAAG,QAAQ,gBAAgB;;;;;;;;;;;ICLpCC,EAAA,CAAAC,cAAA,cAGC;IAGKD,EAAA,CAAAE,SAAA,YAEK;IAKPF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,YAAkE;IAChED,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,mBAAA,MACF;;;;;IA6JQR,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,SAAA,YAA8C;IAC9CF,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,OAAAG,GAAA,CAAAC,MAAA,kBAAAD,GAAA,CAAAC,MAAA,8DACF;;;;;IA6BAV,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,SAAA,YAA8C;IAC9CF,EAAA,CAAAI,MAAA,4BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAIRH,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,SAAA,YAAuD;IACvDF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,GAAsB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IAA7BH,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAW,iBAAA,CAAAC,MAAA,CAAAC,gBAAA,CAAsB;;;ADlNxC,OAAM,MAAOC,wBAAwB;EAOnCC,YACUC,gBAAkC,EACnCC,eAAgC,EAChCC,WAAwB,EACvBC,MAAc,EACdC,KAAqB,EACrBC,YAA0B;IAL1B,KAAAL,gBAAgB,GAAhBA,gBAAgB;IACjB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,WAAW,GAAXA,WAAW;IACV,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,YAAY,GAAZA,YAAY;IAZtB,KAAAR,gBAAgB,GAAW,EAAE;IAC7B,KAAAL,mBAAmB,GAAW,EAAE;IACxB,KAAAc,QAAQ,GAAG,IAAIzB,OAAO,EAAQ;IAYpC,IAAI,CAAC0B,iBAAiB,EAAE;IACxB,IAAI,CAACC,WAAW,GAAG,IAAI,CAACH,YAAY,CAACI,aAAa,CAACC,IAAI,CACrD3B,GAAG,CAAE4B,KAAK,IAAKA,KAAK,CAACC,IAAI,KAAK,MAAM,CAAC,CACtC;EACH;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,GAAG,IAAI,CAACV,KAAK,CAACW,QAAQ,CAACC,WAAW,CAAC,WAAW,CAAC,IAAI,SAAS;IAC1E,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EACAC,WAAWA,CAAA;IACT,IAAI,CAACZ,QAAQ,CAACa,IAAI,EAAE;IACpB,IAAI,CAACb,QAAQ,CAACc,QAAQ,EAAE;EAC1B;EACQb,iBAAiBA,CAAA;IACvB,IAAI,IAAI,CAACN,eAAe,CAACoB,YAAY,EAAE,EAAE;MACvC,IAAI,CAAClB,MAAM,CAACmB,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE;QAC1BN,WAAW,EAAE;UACXO,OAAO,EACL;;OAEL,CAAC;MACF;;IAGF,IAAI,IAAI,CAACvB,gBAAgB,CAACwB,QAAQ,EAAE,EAAE;MACpC,IAAI,CAACrB,MAAM,CAACsB,aAAa,CAAC,QAAQ,CAAC;;EAEvC;EACQR,sBAAsBA,CAAA;IAC5B,IAAI,CAACb,KAAK,CAACY,WAAW,CACnBN,IAAI,CAAC5B,SAAS,CAAC,IAAI,CAACwB,QAAQ,CAAC,CAAC,CAC9BoB,SAAS,CAAEC,MAAM,IAAI;MACpB,IAAI,CAACnC,mBAAmB,GAAGmC,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE;MAClD,IAAI,CAACC,sBAAsB,EAAE;IAC/B,CAAC,CAAC;EACN;EACQA,sBAAsBA,CAAA;IAC5B,IAAI,IAAI,CAACpC,mBAAmB,EAAE;MAC5BqC,UAAU,CAAC,MAAO,IAAI,CAACrC,mBAAmB,GAAG,EAAG,EAAE,IAAI,CAAC;;EAE3D;EACAsC,UAAUA,CAACC,IAAS;IAClB,IAAI,CAACA,IAAI,CAACC,KAAK,EAAE;MACf,IAAI,CAACnC,gBAAgB,GAAG,8CAA8C;MACtE;;IAEF,MAAMoC,IAAI,GAAGF,IAAI,CAACG,KAAK;IACvB,IAAI,CAAClC,gBAAgB,CAACmC,KAAK,CAACF,IAAI,CAAC,CAACP,SAAS,CAAC;MAC1CP,IAAI,EAAGiB,QAAQ,IAAI;QACjB,IAAI,CAACC,kBAAkB,CAACD,QAAQ,CAAC;MACnC,CAAC;MACDE,KAAK,EAAGC,GAAsB,IAAI;QAChC,IAAI,CAACC,gBAAgB,CAACD,GAAG,CAAC;MAC5B;KACD,CAAC;EACJ;EACQF,kBAAkBA,CAACD,QAAa;IACtC,IAAI,CAACpC,gBAAgB,CAACyC,cAAc,CAACL,QAAQ,CAACM,KAAK,CAAC;IACpD,IAAI,CAACvC,MAAM,CAACmB,QAAQ,CAAC,CAAC,IAAI,CAACR,SAAS,CAAC,CAAC;EACxC;EACQ0B,gBAAgBA,CAACD,GAAsB;IAC7C,IAAI,CAAC1C,gBAAgB,GACnB0C,GAAG,CAACD,KAAK,EAAEf,OAAO,IAAI,8CAA8C;IAEtE;IACAM,UAAU,CAAC,MAAO,IAAI,CAAChC,gBAAgB,GAAG,EAAG,EAAE,IAAI,CAAC;EACtD;;;uBAlFWC,wBAAwB,EAAAd,EAAA,CAAA2D,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAA7D,EAAA,CAAA2D,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA/D,EAAA,CAAA2D,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAjE,EAAA,CAAA2D,iBAAA,CAAAO,EAAA,CAAAC,MAAA,GAAAnE,EAAA,CAAA2D,iBAAA,CAAAO,EAAA,CAAAE,cAAA,GAAApE,EAAA,CAAA2D,iBAAA,CAAAU,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAAxBxD,wBAAwB;MAAAyD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCbrC7E,EAAA,CAAA+E,UAAA,IAAAC,uCAAA,iBAkBM;UAENhF,EAAA,CAAAC,cAAA,aAGC;;UAECD,EAAA,CAAAE,SAAA,aAAmC;UAGnCF,EAAA,CAAAC,cAAA,aAEC;UAECD,EAAA,CAAAE,SAAA,aAEO;UAITF,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,aAEC;UAOKD,EAAA,CAAAE,SAAA,cAEO;UAMPF,EAAA,CAAAC,cAAA,eAAyC;UAErCD,EAAA,CAAAE,SAAA,eAA4C;UAW9CF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAmE;UACjED,EAAA,CAAAE,SAAA,eAA4C;UAW9CF,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,eAAkD;UAE9CD,EAAA,CAAAI,MAAA,+BACF;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAAyB;UAAAD,EAAA,CAAAI,MAAA,iDAAoC;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UACjEH,EAAA,CAAAC,cAAA,eAA2B;UACzBD,EAAA,CAAAiF,cAAA,EAMC;UANDjF,EAAA,CAAAC,cAAA,eAMC;UACCD,EAAA,CAAAE,SAAA,gBAKE;UAOJF,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAkF,eAAA,EAEC;UAFDlF,EAAA,CAAAE,SAAA,eAEO;UACTF,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAC,cAAA,eAA2C;UAEzCD,EAAA,CAAAE,SAAA,eAEO;UAKPF,EAAA,CAAAC,cAAA,eAAuC;UAInCD,EAAA,CAAAI,MAAA,yBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAAmD;UACjDD,EAAA,CAAAI,MAAA,mDACF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAGNH,EAAA,CAAAC,cAAA,oBAIC;UAFCD,EAAA,CAAAmF,UAAA,sBAAAC,4DAAA;YAAApF,EAAA,CAAAqF,aAAA,CAAAC,GAAA;YAAA,MAAAC,GAAA,GAAAvF,EAAA,CAAAwF,WAAA;YAAA,OAAYxF,EAAA,CAAAyF,WAAA,CAAAX,GAAA,CAAAhC,UAAA,CAAAyC,GAAA,CAAa;UAAA,EAAC;UAI1BvF,EAAA,CAAAC,cAAA,eAAmB;UAIdD,EAAA,CAAAI,MAAA,aAAK;UAAAJ,EAAA,CAAAG,YAAA,EACP;UACDH,EAAA,CAAAC,cAAA,eAAsB;UACpBD,EAAA,CAAAE,SAAA,qBAUE;UACFF,EAAA,CAAAC,cAAA,eAEC;UACCD,EAAA,CAAAE,SAAA,eAEO;UACTF,EAAA,CAAAG,YAAA,EAAM;UAERH,EAAA,CAAA+E,UAAA,KAAAW,wCAAA,kBAMM;UACR1F,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAAmB;UAIdD,EAAA,CAAAI,MAAA,oBAAY;UAAAJ,EAAA,CAAAG,YAAA,EACd;UACDH,EAAA,CAAAC,cAAA,eAAsB;UACpBD,EAAA,CAAAE,SAAA,qBASE;UACFF,EAAA,CAAAC,cAAA,eAEC;UACCD,EAAA,CAAAE,SAAA,eAEO;UACTF,EAAA,CAAAG,YAAA,EAAM;UAERH,EAAA,CAAA+E,UAAA,KAAAY,wCAAA,kBAMM;UACR3F,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAA+E,UAAA,KAAAa,wCAAA,kBAMM;UAGN5F,EAAA,CAAAC,cAAA,kBAAoE;UAClED,EAAA,CAAAE,SAAA,eAEO;UAIPF,EAAA,CAAAC,cAAA,gBAEC;UACCD,EAAA,CAAAI,MAAA,sBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;UA7OhBH,EAAA,CAAA6F,UAAA,SAAAf,GAAA,CAAAtE,mBAAA,CAAyB;UAqB1BR,EAAA,CAAAK,SAAA,GAAkC;UAAlCL,EAAA,CAAA8F,WAAA,SAAA9F,EAAA,CAAA+F,WAAA,OAAAjB,GAAA,CAAAtD,WAAA,EAAkC;UAwJrBxB,EAAA,CAAAK,SAAA,IAAqD;UAArDL,EAAA,CAAA6F,UAAA,SAAApF,GAAA,CAAAuF,OAAA,KAAAvF,GAAA,CAAAwF,KAAA,IAAAxF,GAAA,CAAAyF,OAAA,EAAqD;UAmCrDlG,EAAA,CAAAK,SAAA,GAA8D;UAA9DL,EAAA,CAAA6F,UAAA,SAAAM,GAAA,CAAAH,OAAA,KAAAG,GAAA,CAAAF,KAAA,IAAAE,GAAA,CAAAD,OAAA,EAA8D;UAUhElG,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAA6F,UAAA,SAAAf,GAAA,CAAAjE,gBAAA,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}