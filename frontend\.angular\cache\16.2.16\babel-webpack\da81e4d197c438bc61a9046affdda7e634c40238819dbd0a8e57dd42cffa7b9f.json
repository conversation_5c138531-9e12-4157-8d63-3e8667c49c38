{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@app/services/reunion.service\";\nimport * as i4 from \"@app/services/data.service\";\nimport * as i5 from \"@app/services/planning.service\";\nimport * as i6 from \"@app/services/toast.service\";\nimport * as i7 from \"@app/services/authuser.service\";\nimport * as i8 from \"@app/services/role.service\";\nfunction ReunionEditComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.error.message || \"Une erreur est survenue\", \" \");\n  }\n}\nfunction ReunionEditComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵelement(1, \"i\", 64);\n    i0.ɵɵtext(2, \" Le titre est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionEditComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵelement(1, \"i\", 64);\n    i0.ɵɵtext(2, \" La date est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionEditComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵelement(1, \"i\", 64);\n    i0.ɵɵtext(2, \" L'heure de d\\u00E9but est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionEditComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵelement(1, \"i\", 64);\n    i0.ɵɵtext(2, \" L'heure de fin est obligatoire \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReunionEditComponent_div_62_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵelement(1, \"i\", 55);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.currentReunionPlanning.description, \" \");\n  }\n}\nfunction ReunionEditComponent_div_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"div\", 66)(2, \"span\", 67);\n    i0.ɵɵelement(3, \"i\", 68);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 69);\n    i0.ɵɵelement(6, \"i\", 70);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"date\");\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(10, ReunionEditComponent_div_62_div_10_Template, 3, 1, \"div\", 71);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.currentReunionPlanning.titre, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", i0.ɵɵpipeBind2(8, 4, ctx_r5.currentReunionPlanning.dateDebut, \"dd/MM/yyyy\"), \" - \", i0.ɵɵpipeBind2(9, 7, ctx_r5.currentReunionPlanning.dateFin, \"dd/MM/yyyy\"), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.currentReunionPlanning.description);\n  }\n}\nfunction ReunionEditComponent_option_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 73);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const planning_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", planning_r11._id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(planning_r11.titre);\n  }\n}\nfunction ReunionEditComponent_ng_container_79_option_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 75);\n    i0.ɵɵelement(1, \"i\", 76);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r13._id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", user_r13.username, \" \");\n  }\n}\nfunction ReunionEditComponent_ng_container_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ReunionEditComponent_ng_container_79_option_1_Template, 3, 2, \"option\", 74);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.users);\n  }\n}\nfunction ReunionEditComponent_i_88_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 77);\n  }\n}\nfunction ReunionEditComponent_i_89_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 78);\n  }\n}\nexport let ReunionEditComponent = /*#__PURE__*/(() => {\n  class ReunionEditComponent {\n    constructor(fb, route, router, reunionService, userService, planningService, toastService, authService, roleService) {\n      this.fb = fb;\n      this.route = route;\n      this.router = router;\n      this.reunionService = reunionService;\n      this.userService = userService;\n      this.planningService = planningService;\n      this.toastService = toastService;\n      this.authService = authService;\n      this.roleService = roleService;\n      this.error = null;\n      this.isSubmitting = false;\n      this.users = [];\n      this.plannings = [];\n      this.currentReunionPlanning = null;\n      this.isAdmin = false;\n    }\n    ngOnInit() {\n      this.reunionId = this.route.snapshot.paramMap.get('id');\n      this.checkUserRole();\n      this.initForm();\n      this.fetchUsers();\n      this.fetchPlannings();\n      this.loadReunion();\n    }\n    checkUserRole() {\n      this.isAdmin = this.roleService.isAdmin();\n      console.log('🔍 Utilisateur admin:', this.isAdmin);\n    }\n    initForm() {\n      this.reunionForm = this.fb.group({\n        titre: ['', Validators.required],\n        description: [''],\n        date: ['', Validators.required],\n        heureDebut: ['', Validators.required],\n        heureFin: ['', Validators.required],\n        lieu: [''],\n        lienVisio: [''],\n        planning: ['', Validators.required],\n        participants: [[]]\n      });\n    }\n    fetchUsers() {\n      this.userService.getAllUsers().subscribe(users => {\n        this.users = users;\n      });\n    }\n    fetchPlannings() {\n      const userId = this.authService.getCurrentUserId();\n      if (!userId) return;\n      // Si admin, récupérer tous les plannings, sinon seulement ceux de l'utilisateur\n      const planningsObservable = this.isAdmin ? this.planningService.getAllPlanningsAdmin() : this.planningService.getPlanningsByUser(userId);\n      planningsObservable.subscribe({\n        next: response => {\n          // Adapter la réponse selon l'endpoint utilisé\n          if (this.isAdmin) {\n            this.plannings = response.data || [];\n            console.log('🔍 Tous les plannings (admin) récupérés:', this.plannings);\n          } else {\n            this.plannings = response.plannings || [];\n            console.log('🔍 Plannings utilisateur récupérés:', this.plannings);\n          }\n        },\n        error: err => {\n          console.error('❌ Erreur chargement plannings:', err);\n          this.toastService.error('Erreur', 'Impossible de récupérer les plannings');\n        }\n      });\n    }\n    loadReunion() {\n      this.reunionService.getReunionById(this.reunionId).subscribe({\n        next: reunion => {\n          // Stocker le planning actuel de la réunion\n          this.currentReunionPlanning = reunion.reunion.planning;\n          this.reunionForm.patchValue({\n            titre: reunion.reunion.titre,\n            description: reunion.reunion.description,\n            date: reunion.reunion.date?.split('T')[0],\n            heureDebut: reunion.reunion.heureDebut,\n            heureFin: reunion.reunion.heureFin,\n            lieu: reunion.reunion.lieu,\n            lienVisio: reunion.reunion.lienVisio,\n            planning: reunion.reunion.planning?.id || reunion.reunion.planning?._id,\n            participants: reunion.reunion.participants?.map(p => p._id)\n          });\n          // Désactiver le champ planning en mode édition\n          this.reunionForm.get('planning')?.disable();\n          console.log('🔍 Réunion chargée:', reunion.reunion);\n          console.log('🔍 Planning actuel:', this.currentReunionPlanning);\n        },\n        error: err => {\n          console.error('Erreur lors du chargement de la réunion:', err);\n          if (err.status === 403) {\n            this.toastService.accessDenied('accéder à cette réunion', err.status);\n          } else if (err.status === 404) {\n            this.toastService.error('Réunion introuvable', 'La réunion demandée n\\'existe pas ou a été supprimée');\n          } else {\n            const errorMessage = err.error?.message || 'Erreur lors du chargement de la réunion';\n            this.toastService.error('Erreur de chargement', errorMessage);\n          }\n        }\n      });\n    }\n    onSubmit() {\n      if (this.reunionForm.invalid) {\n        this.toastService.warning('Formulaire invalide', 'Veuillez corriger les erreurs avant de soumettre le formulaire');\n        return;\n      }\n      // Validation de la date par rapport au planning\n      if (!this.validateDateInPlanningRange()) {\n        return;\n      }\n      this.isSubmitting = true;\n      const reunion = this.reunionForm.value;\n      // Inclure le planning même s'il est désactivé\n      if (this.currentReunionPlanning) {\n        reunion.planning = this.currentReunionPlanning._id || this.currentReunionPlanning.id;\n      }\n      console.log('🔍 Données de la réunion à mettre à jour:', reunion);\n      this.reunionService.updateReunion(this.reunionId, reunion).subscribe({\n        next: () => {\n          this.isSubmitting = false;\n          this.toastService.success('Réunion mise à jour', 'La réunion a été modifiée avec succès');\n          this.router.navigate(['/reunions']);\n        },\n        error: err => {\n          this.isSubmitting = false;\n          console.error('Erreur lors de la mise à jour de la réunion:', err);\n          if (err.status === 403) {\n            this.toastService.accessDenied('modifier cette réunion', err.status);\n          } else if (err.status === 401) {\n            this.toastService.error('Non autorisé', 'Vous devez être connecté pour effectuer cette action');\n          } else {\n            const errorMessage = err.error?.message || 'Erreur lors de la mise à jour de la réunion';\n            this.toastService.error('Erreur de mise à jour', errorMessage, 8000);\n          }\n        }\n      });\n    }\n    goReunion() {\n      this.router.navigate(['/reunions']);\n    }\n    /**\n     * Valide que la date de la réunion est dans l'intervalle du planning sélectionné\n     */\n    validateDateInPlanningRange() {\n      const formValue = this.reunionForm.value;\n      const reunionDate = formValue.date;\n      const planningId = formValue.planning;\n      if (!reunionDate || !planningId) {\n        return true; // Si pas de date ou planning, laisser la validation backend gérer\n      }\n      // Trouver le planning sélectionné\n      const selectedPlanning = this.plannings.find(p => p._id === planningId);\n      if (!selectedPlanning) {\n        console.warn('⚠️ Planning non trouvé dans la liste locale, tentative de récupération depuis le serveur');\n        // Si le planning n'est pas trouvé dans la liste locale, essayer de le récupérer\n        // Cela peut arriver si l'utilisateur modifie une réunion d'un planning dont il n'est que participant\n        this.planningService.getPlanningById(planningId).subscribe({\n          next: response => {\n            const planning = response.planning;\n            if (planning) {\n              // Ajouter le planning à la liste locale pour éviter de futures requêtes\n              this.plannings.push(planning);\n              console.log('✅ Planning récupéré et ajouté à la liste locale:', planning);\n            }\n          },\n          error: err => {\n            console.error('❌ Erreur lors de la récupération du planning:', err);\n            this.toastService.error('Planning introuvable', 'Le planning sélectionné n\\'existe pas ou vous n\\'avez pas les permissions pour y accéder');\n          }\n        });\n        // Pour cette validation, on retourne true et on laisse le backend gérer\n        return true;\n      }\n      // Convertir les dates pour comparaison\n      const reunionDateObj = new Date(reunionDate);\n      const planningDateDebut = new Date(selectedPlanning.dateDebut);\n      const planningDateFin = new Date(selectedPlanning.dateFin);\n      // Comparer seulement les dates (sans les heures)\n      reunionDateObj.setHours(0, 0, 0, 0);\n      planningDateDebut.setHours(0, 0, 0, 0);\n      planningDateFin.setHours(0, 0, 0, 0);\n      if (reunionDateObj < planningDateDebut || reunionDateObj > planningDateFin) {\n        this.toastService.error('Date invalide', `La date de la réunion doit être comprise entre le ${planningDateDebut.toLocaleDateString('fr-FR')} et le ${planningDateFin.toLocaleDateString('fr-FR')} (période du planning \"${selectedPlanning.titre}\")`, 10000);\n        return false;\n      }\n      return true;\n    }\n    static {\n      this.ɵfac = function ReunionEditComponent_Factory(t) {\n        return new (t || ReunionEditComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.ReunionService), i0.ɵɵdirectiveInject(i4.DataService), i0.ɵɵdirectiveInject(i5.PlanningService), i0.ɵɵdirectiveInject(i6.ToastService), i0.ɵɵdirectiveInject(i7.AuthuserService), i0.ɵɵdirectiveInject(i8.RoleService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ReunionEditComponent,\n        selectors: [[\"app-reunion-edit\"]],\n        decls: 91,\n        vars: 13,\n        consts: [[1, \"container\", \"mx-auto\", \"px-4\", \"py-6\", \"max-w-3xl\"], [1, \"bg-gradient-to-r\", \"from-purple-600\", \"to-indigo-600\", \"rounded-t-lg\", \"p-6\", \"text-white\", \"mb-0\"], [1, \"text-2xl\", \"font-bold\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-edit\", \"mr-3\", \"text-purple-200\"], [1, \"text-purple-100\", \"mt-2\"], [1, \"bg-white\", \"rounded-b-lg\", \"shadow-lg\", \"p-6\", \"border-t-0\", 3, \"formGroup\", \"ngSubmit\"], [\"class\", \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\", 4, \"ngIf\"], [1, \"grid\", \"grid-cols-1\", \"gap-6\"], [1, \"relative\"], [\"for\", \"titre\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-purple-700\", \"mb-2\"], [1, \"fas\", \"fa-tag\", \"mr-2\", \"text-purple-500\"], [\"id\", \"titre\", \"type\", \"text\", \"formControlName\", \"titre\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-lg\", \"border-2\", \"border-purple-200\", \"shadow-sm\", \"focus:border-purple-500\", \"focus:ring-purple-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"px-4\", \"py-3\"], [\"class\", \"text-red-500 text-sm mt-2 flex items-center\", 4, \"ngIf\"], [\"for\", \"description\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-indigo-700\", \"mb-2\"], [1, \"fas\", \"fa-align-left\", \"mr-2\", \"text-indigo-500\"], [\"id\", \"description\", \"formControlName\", \"description\", \"rows\", \"3\", \"placeholder\", \"D\\u00E9crivez votre r\\u00E9union...\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-lg\", \"border-2\", \"border-indigo-200\", \"shadow-sm\", \"focus:border-indigo-500\", \"focus:ring-indigo-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"px-4\", \"py-3\"], [1, \"bg-gradient-to-r\", \"from-blue-50\", \"to-cyan-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-blue-200\"], [1, \"text-lg\", \"font-semibold\", \"text-blue-800\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-calendar-clock\", \"mr-2\", \"text-blue-600\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-3\", \"gap-6\"], [\"for\", \"date\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-blue-700\", \"mb-2\"], [1, \"fas\", \"fa-calendar\", \"mr-2\", \"text-blue-500\"], [\"id\", \"date\", \"type\", \"date\", \"formControlName\", \"date\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-lg\", \"border-2\", \"border-blue-200\", \"shadow-sm\", \"focus:border-blue-500\", \"focus:ring-blue-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"px-4\", \"py-3\"], [\"for\", \"heureDebut\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-green-700\", \"mb-2\"], [1, \"fas\", \"fa-play\", \"mr-2\", \"text-green-500\"], [\"id\", \"heureDebut\", \"type\", \"time\", \"formControlName\", \"heureDebut\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-lg\", \"border-2\", \"border-green-200\", \"shadow-sm\", \"focus:border-green-500\", \"focus:ring-green-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"px-4\", \"py-3\"], [\"for\", \"heureFin\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-red-700\", \"mb-2\"], [1, \"fas\", \"fa-stop\", \"mr-2\", \"text-red-500\"], [\"id\", \"heureFin\", \"type\", \"time\", \"formControlName\", \"heureFin\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-lg\", \"border-2\", \"border-red-200\", \"shadow-sm\", \"focus:border-red-500\", \"focus:ring-red-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"px-4\", \"py-3\"], [1, \"bg-gradient-to-r\", \"from-orange-50\", \"to-yellow-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-orange-200\"], [1, \"text-lg\", \"font-semibold\", \"text-orange-800\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-map-marker-alt\", \"mr-2\", \"text-orange-600\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-2\", \"gap-6\"], [\"for\", \"lieu\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-orange-700\", \"mb-2\"], [1, \"fas\", \"fa-map-marker-alt\", \"mr-2\", \"text-orange-500\"], [\"id\", \"lieu\", \"type\", \"text\", \"formControlName\", \"lieu\", \"placeholder\", \"Salle 101, Bureau A, Google Meet...\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-lg\", \"border-2\", \"border-orange-200\", \"shadow-sm\", \"focus:border-orange-500\", \"focus:ring-orange-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"px-4\", \"py-3\"], [\"for\", \"lienVisio\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-cyan-700\", \"mb-2\"], [1, \"fas\", \"fa-video\", \"mr-2\", \"text-cyan-500\"], [\"id\", \"lienVisio\", \"type\", \"url\", \"formControlName\", \"lienVisio\", \"placeholder\", \"https://meet.google.com/...\", 1, \"mt-1\", \"block\", \"w-full\", \"rounded-lg\", \"border-2\", \"border-cyan-200\", \"shadow-sm\", \"focus:border-cyan-500\", \"focus:ring-cyan-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"px-4\", \"py-3\"], [\"for\", \"planning\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"class\", \"mt-1 block w-full px-4 py-3 bg-gradient-to-r from-purple-50 to-indigo-50 border-2 border-purple-200 rounded-lg shadow-sm\", 4, \"ngIf\"], [\"id\", \"planning\", \"formControlName\", \"planning\", 1, \"hidden\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"text-sm\", \"text-purple-600\", \"mt-3\", \"bg-purple-50\", \"p-3\", \"rounded-lg\", \"border\", \"border-purple-200\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-lock\", \"mr-2\", \"text-purple-500\"], [1, \"font-medium\"], [1, \"bg-gradient-to-r\", \"from-emerald-50\", \"to-teal-50\", \"p-4\", \"rounded-lg\", \"border\", \"border-emerald-200\"], [1, \"text-lg\", \"font-semibold\", \"text-emerald-800\", \"mb-4\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-users\", \"mr-2\", \"text-emerald-600\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-emerald-700\", \"mb-2\"], [1, \"fas\", \"fa-user-friends\", \"mr-2\", \"text-emerald-500\"], [\"formControlName\", \"participants\", \"multiple\", \"\", 1, \"mt-1\", \"block\", \"w-full\", \"px-4\", \"py-3\", \"border-2\", \"border-emerald-200\", \"rounded-lg\", \"shadow-sm\", \"focus:ring-emerald-500\", \"focus:border-emerald-500\", \"focus:ring-2\", \"transition-all\", \"duration-200\", \"text-sm\", \"min-h-[120px]\"], [4, \"ngIf\"], [1, \"text-xs\", \"text-emerald-600\", \"mt-2\"], [1, \"fas\", \"fa-info-circle\", \"mr-1\"], [1, \"mt-8\", \"flex\", \"justify-end\", \"space-x-4\", \"bg-gray-50\", \"p-4\", \"rounded-lg\", \"border-t\", \"border-gray-200\"], [\"type\", \"button\", 1, \"px-6\", \"py-3\", \"border-2\", \"border-gray-300\", \"rounded-lg\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"hover:bg-gray-100\", \"hover:border-gray-400\", \"transition-all\", \"duration-200\", \"flex\", \"items-center\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"mr-2\"], [\"type\", \"submit\", 1, \"px-6\", \"py-3\", \"rounded-lg\", \"text-sm\", \"font-medium\", \"text-white\", \"bg-gradient-to-r\", \"from-purple-600\", \"to-indigo-600\", \"hover:from-purple-700\", \"hover:to-indigo-700\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", \"transition-all\", \"duration-200\", \"flex\", \"items-center\", \"shadow-lg\", 3, \"disabled\"], [\"class\", \"fas fa-save mr-2\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner fa-spin mr-2\", 4, \"ngIf\"], [1, \"bg-red-100\", \"border\", \"border-red-400\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\", \"mb-4\"], [1, \"text-red-500\", \"text-sm\", \"mt-2\", \"flex\", \"items-center\"], [1, \"fas\", \"fa-exclamation-circle\", \"mr-1\"], [1, \"mt-1\", \"block\", \"w-full\", \"px-4\", \"py-3\", \"bg-gradient-to-r\", \"from-purple-50\", \"to-indigo-50\", \"border-2\", \"border-purple-200\", \"rounded-lg\", \"shadow-sm\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"font-semibold\", \"text-purple-800\", \"text-lg\"], [1, \"fas\", \"fa-calendar-alt\", \"mr-2\", \"text-purple-600\"], [1, \"text-sm\", \"font-medium\", \"text-red-600\", \"bg-red-50\", \"px-2\", \"py-1\", \"rounded-full\", \"border\", \"border-red-200\"], [1, \"fas\", \"fa-clock\", \"mr-1\"], [\"class\", \"text-sm text-indigo-700 mt-2 bg-indigo-50 p-2 rounded border-l-4 border-indigo-300\", 4, \"ngIf\"], [1, \"text-sm\", \"text-indigo-700\", \"mt-2\", \"bg-indigo-50\", \"p-2\", \"rounded\", \"border-l-4\", \"border-indigo-300\"], [3, \"value\"], [\"class\", \"py-2\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"py-2\", 3, \"value\"], [1, \"fas\", \"fa-user\", \"mr-2\"], [1, \"fas\", \"fa-save\", \"mr-2\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"mr-2\"]],\n        template: function ReunionEditComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\", 2);\n            i0.ɵɵelement(3, \"i\", 3);\n            i0.ɵɵtext(4, \" Modifier la R\\u00E9union \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(5, \"p\", 4);\n            i0.ɵɵtext(6, \"Modifiez les d\\u00E9tails de votre r\\u00E9union\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"form\", 5);\n            i0.ɵɵlistener(\"ngSubmit\", function ReunionEditComponent_Template_form_ngSubmit_7_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵtemplate(8, ReunionEditComponent_div_8_Template, 2, 1, \"div\", 6);\n            i0.ɵɵelementStart(9, \"div\", 7)(10, \"div\", 8)(11, \"label\", 9);\n            i0.ɵɵelement(12, \"i\", 10);\n            i0.ɵɵtext(13, \" Titre * \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(14, \"input\", 11);\n            i0.ɵɵtemplate(15, ReunionEditComponent_div_15_Template, 3, 0, \"div\", 12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(16, \"div\", 8)(17, \"label\", 13);\n            i0.ɵɵelement(18, \"i\", 14);\n            i0.ɵɵtext(19, \" Description \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(20, \"textarea\", 15);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"div\", 16)(22, \"h3\", 17);\n            i0.ɵɵelement(23, \"i\", 18);\n            i0.ɵɵtext(24, \" Planification \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"div\", 19)(26, \"div\")(27, \"label\", 20);\n            i0.ɵɵelement(28, \"i\", 21);\n            i0.ɵɵtext(29, \" Date * \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(30, \"input\", 22);\n            i0.ɵɵtemplate(31, ReunionEditComponent_div_31_Template, 3, 0, \"div\", 12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"div\")(33, \"label\", 23);\n            i0.ɵɵelement(34, \"i\", 24);\n            i0.ɵɵtext(35, \" Heure de d\\u00E9but * \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(36, \"input\", 25);\n            i0.ɵɵtemplate(37, ReunionEditComponent_div_37_Template, 3, 0, \"div\", 12);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(38, \"div\")(39, \"label\", 26);\n            i0.ɵɵelement(40, \"i\", 27);\n            i0.ɵɵtext(41, \" Heure de fin * \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(42, \"input\", 28);\n            i0.ɵɵtemplate(43, ReunionEditComponent_div_43_Template, 3, 0, \"div\", 12);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(44, \"div\", 29)(45, \"h3\", 30);\n            i0.ɵɵelement(46, \"i\", 31);\n            i0.ɵɵtext(47, \" Localisation \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(48, \"div\", 32)(49, \"div\")(50, \"label\", 33);\n            i0.ɵɵelement(51, \"i\", 34);\n            i0.ɵɵtext(52, \" Lieu / Salle \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(53, \"input\", 35);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(54, \"div\")(55, \"label\", 36);\n            i0.ɵɵelement(56, \"i\", 37);\n            i0.ɵɵtext(57, \" Lien Visio \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(58, \"input\", 38);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(59, \"div\")(60, \"label\", 39);\n            i0.ɵɵtext(61, \"Planning *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(62, ReunionEditComponent_div_62_Template, 11, 10, \"div\", 40);\n            i0.ɵɵelementStart(63, \"select\", 41)(64, \"option\", 42);\n            i0.ɵɵtext(65, \"S\\u00E9lectionnez un planning\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(66, ReunionEditComponent_option_66_Template, 2, 2, \"option\", 43);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(67, \"div\", 44);\n            i0.ɵɵelement(68, \"i\", 45);\n            i0.ɵɵelementStart(69, \"span\", 46);\n            i0.ɵɵtext(70, \"Le planning ne peut pas \\u00EAtre modifi\\u00E9 lors de l'\\u00E9dition d'une r\\u00E9union\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(71, \"div\", 47)(72, \"h3\", 48);\n            i0.ɵɵelement(73, \"i\", 49);\n            i0.ɵɵtext(74, \" Participants \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(75, \"label\", 50);\n            i0.ɵɵelement(76, \"i\", 51);\n            i0.ɵɵtext(77, \" S\\u00E9lectionnez les participants \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(78, \"select\", 52);\n            i0.ɵɵtemplate(79, ReunionEditComponent_ng_container_79_Template, 2, 1, \"ng-container\", 53);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(80, \"p\", 54);\n            i0.ɵɵelement(81, \"i\", 55);\n            i0.ɵɵtext(82, \" Maintenez Ctrl (ou Cmd) pour s\\u00E9lectionner plusieurs participants \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(83, \"div\", 56)(84, \"button\", 57);\n            i0.ɵɵlistener(\"click\", function ReunionEditComponent_Template_button_click_84_listener() {\n              return ctx.goReunion();\n            });\n            i0.ɵɵelement(85, \"i\", 58);\n            i0.ɵɵtext(86, \" Annuler \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(87, \"button\", 59);\n            i0.ɵɵtemplate(88, ReunionEditComponent_i_88_Template, 1, 0, \"i\", 60);\n            i0.ɵɵtemplate(89, ReunionEditComponent_i_89_Template, 1, 0, \"i\", 61);\n            i0.ɵɵtext(90);\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            let tmp_2_0;\n            let tmp_3_0;\n            let tmp_4_0;\n            let tmp_5_0;\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"formGroup\", ctx.reunionForm);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.reunionForm.get(\"titre\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.reunionForm.get(\"titre\")) == null ? null : tmp_2_0.touched));\n            i0.ɵɵadvance(16);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx.reunionForm.get(\"date\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.reunionForm.get(\"date\")) == null ? null : tmp_3_0.touched));\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.reunionForm.get(\"heureDebut\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.reunionForm.get(\"heureDebut\")) == null ? null : tmp_4_0.touched));\n            i0.ɵɵadvance(6);\n            i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.reunionForm.get(\"heureFin\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.reunionForm.get(\"heureFin\")) == null ? null : tmp_5_0.touched));\n            i0.ɵɵadvance(19);\n            i0.ɵɵproperty(\"ngIf\", ctx.currentReunionPlanning);\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngForOf\", ctx.plannings);\n            i0.ɵɵadvance(13);\n            i0.ɵɵproperty(\"ngIf\", ctx.users);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"disabled\", ctx.reunionForm.invalid || ctx.isSubmitting);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isSubmitting);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isSubmitting);\n            i0.ɵɵadvance(1);\n            i0.ɵɵtextInterpolate1(\" \", ctx.isSubmitting ? \"Enregistrement...\" : \"Enregistrer les modifications\", \" \");\n          }\n        }\n      });\n    }\n  }\n  return ReunionEditComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}