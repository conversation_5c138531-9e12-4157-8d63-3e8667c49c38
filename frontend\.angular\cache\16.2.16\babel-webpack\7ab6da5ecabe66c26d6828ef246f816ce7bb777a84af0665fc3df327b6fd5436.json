{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { HttpClientModule } from '@angular/common/http';\nimport { AiChatbotComponent } from '../components/ai-chatbot/ai-chatbot.component';\nimport { HighlightPresencePipe } from './pipes/highlight-presence.pipe';\nimport { TimeAgoPipe } from './pipes/time-ago.pipe';\nexport let SharedModule = class SharedModule {};\nSharedModule = __decorate([NgModule({\n  declarations: [AiChatbotComponent, HighlightPresencePipe, TimeAgoPipe],\n  imports: [CommonModule, ReactiveFormsModule, FormsModule, RouterModule, HttpClientModule],\n  exports: [AiChatbotComponent, CommonModule, ReactiveFormsModule, FormsModule, RouterModule]\n})], SharedModule);", "map": {"version": 3, "names": ["NgModule", "CommonModule", "ReactiveFormsModule", "FormsModule", "RouterModule", "HttpClientModule", "AiChatbotComponent", "HighlightPresencePipe", "TimeAgoPipe", "SharedModule", "__decorate", "declarations", "imports", "exports"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\shared\\shared.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { HttpClientModule } from '@angular/common/http';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\n\nimport { AiChatbotComponent } from '../components/ai-chatbot/ai-chatbot.component';\nimport { HighlightPresencePipe } from './pipes/highlight-presence.pipe';\nimport { TimeAgoPipe } from './pipes/time-ago.pipe';\n\n@NgModule({\n  declarations: [AiChatbotComponent, HighlightPresencePipe, TimeAgoPipe],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    FormsModule,\n    RouterModule,\n    HttpClientModule,\n  ],\n  exports: [\n    AiChatbotComponent,\n    CommonModule,\n    ReactiveFormsModule,\n    FormsModule,\n    RouterModule,\n  ],\n})\nexport class SharedModule {}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,sBAAsB;AAGvD,SAASC,kBAAkB,QAAQ,+CAA+C;AAClF,SAASC,qBAAqB,QAAQ,iCAAiC;AACvE,SAASC,WAAW,QAAQ,uBAAuB;AAmB5C,WAAMC,YAAY,GAAlB,MAAMA,YAAY,GAAG;AAAfA,YAAY,GAAAC,UAAA,EAjBxBV,QAAQ,CAAC;EACRW,YAAY,EAAE,CAACL,kBAAkB,EAAEC,qBAAqB,EAAEC,WAAW,CAAC;EACtEI,OAAO,EAAE,CACPX,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,YAAY,EACZC,gBAAgB,CACjB;EACDQ,OAAO,EAAE,CACPP,kBAAkB,EAClBL,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,YAAY;CAEf,CAAC,C,EACWK,YAAY,CAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}