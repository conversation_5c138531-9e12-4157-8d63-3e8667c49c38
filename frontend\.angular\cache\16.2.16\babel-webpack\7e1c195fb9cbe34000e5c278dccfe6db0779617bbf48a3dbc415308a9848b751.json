{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MessagesRoutingModule } from './messages-routing.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ApolloModule } from 'apollo-angular';\nimport { MessageChatComponent } from './message-chat/message-chat.component';\nimport { MessagesListComponent } from './messages-list/messages-list.component';\nimport { UserListComponent } from './user-list/user-list.component';\nimport { MessageLayoutComponent } from './message-layout/message-layout.component';\nimport { UserStatusService } from 'src/app/services/user-status.service';\nimport { MessageService } from 'src/app/services/message.service';\nimport * as i0 from \"@angular/core\";\nexport class MessagesModule {\n  static {\n    this.ɵfac = function MessagesModule_Factory(t) {\n      return new (t || MessagesModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: MessagesModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [UserStatusService, MessageService],\n      imports: [CommonModule, MessagesRoutingModule, FormsModule, ReactiveFormsModule, ApolloModule, RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(MessagesModule, {\n    declarations: [MessageChatComponent, MessagesListComponent, UserListComponent, MessageLayoutComponent],\n    imports: [CommonModule, MessagesRoutingModule, FormsModule, ReactiveFormsModule, ApolloModule, RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "MessagesRoutingModule", "FormsModule", "ReactiveFormsModule", "ApolloModule", "MessageChatComponent", "MessagesListComponent", "UserListComponent", "MessageLayoutComponent", "UserStatusService", "MessageService", "MessagesModule", "imports", "declarations"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\messages.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n\nimport { MessagesRoutingModule } from './messages-routing.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ApolloModule } from 'apollo-angular';\nimport { MessageChatComponent } from './message-chat/message-chat.component';\nimport { MessagesListComponent } from './messages-list/messages-list.component';\nimport { UserListComponent } from './user-list/user-list.component';\nimport { MessageLayoutComponent } from './message-layout/message-layout.component';\nimport { SystemStatusComponent } from '../../../components/system-status/system-status.component';\n\nimport { UserStatusService } from 'src/app/services/user-status.service';\nimport { MessageService } from 'src/app/services/message.service';\n\n@NgModule({\n  declarations: [\n    MessageChatComponent,\n    MessagesListComponent,\n    UserListComponent,\n    MessageLayoutComponent,\n  ],\n  imports: [\n    CommonModule,\n    MessagesRoutingModule,\n    FormsModule,\n    ReactiveFormsModule,\n    ApolloModule,\n    RouterModule,\n  ],\n  providers: [UserStatusService, MessageService],\n})\nexport class MessagesModule {}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,sBAAsB,QAAQ,2CAA2C;AAGlF,SAASC,iBAAiB,QAAQ,sCAAsC;AACxE,SAASC,cAAc,QAAQ,kCAAkC;;AAmBjE,OAAM,MAAOC,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA;IAAc;EAAA;;;iBAFd,CAACF,iBAAiB,EAAEC,cAAc,CAAC;MAAAE,OAAA,GAP5Cb,YAAY,EACZE,qBAAqB,EACrBC,WAAW,EACXC,mBAAmB,EACnBC,YAAY,EACZJ,YAAY;IAAA;EAAA;;;2EAIHW,cAAc;IAAAE,YAAA,GAfvBR,oBAAoB,EACpBC,qBAAqB,EACrBC,iBAAiB,EACjBC,sBAAsB;IAAAI,OAAA,GAGtBb,YAAY,EACZE,qBAAqB,EACrBC,WAAW,EACXC,mBAAmB,EACnBC,YAAY,EACZJ,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}