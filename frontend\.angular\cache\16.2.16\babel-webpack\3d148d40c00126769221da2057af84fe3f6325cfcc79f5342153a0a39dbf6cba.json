{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReunionsRoutingModule } from './reunions-routing.module';\nimport { ReunionListComponent } from './reunion-list/reunion-list.component';\nimport { ReunionDetailComponent } from './reunion-detail/reunion-detail.component';\nimport { ReunionFormComponent } from './reunion-form/reunion-form.component';\nimport { RouterModule } from '@angular/router';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { PipesModule } from '../../../pipes/pipes.module';\nimport { ReunionEditComponent } from './reunion-edit/reunion-edit.component';\nexport let ReunionsModule = class ReunionsModule {};\nReunionsModule = __decorate([NgModule({\n  declarations: [ReunionListComponent, ReunionDetailComponent, ReunionFormComponent, ReunionEditComponent],\n  imports: [CommonModule, ReunionsRoutingModule, RouterModule, FormsModule, ReactiveFormsModule, PipesModule]\n})], ReunionsModule);", "map": {"version": 3, "names": ["NgModule", "CommonModule", "ReunionsRoutingModule", "ReunionListComponent", "ReunionDetailComponent", "ReunionFormComponent", "RouterModule", "FormsModule", "ReactiveFormsModule", "PipesModule", "ReunionEditComponent", "ReunionsModule", "__decorate", "declarations", "imports"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\admin\\reunions\\reunions.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\nimport { ReunionsRoutingModule } from './reunions-routing.module';\nimport { ReunionListComponent } from './reunion-list/reunion-list.component';\nimport { ReunionDetailComponent } from './reunion-detail/reunion-detail.component';\nimport { ReunionFormComponent } from './reunion-form/reunion-form.component';\nimport { RouterModule } from '@angular/router';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\n\nimport { PipesModule } from '../../../pipes/pipes.module';\nimport { ReunionEditComponent } from './reunion-edit/reunion-edit.component';\n\n@NgModule({\n  declarations: [\n    ReunionListComponent,\n    ReunionDetailComponent,\n    ReunionFormComponent,\n    ReunionEditComponent,\n  \n  ],\n  imports: [\n    CommonModule,\n    ReunionsRoutingModule,\n    RouterModule,\n    FormsModule,\n    ReactiveFormsModule,\n    PipesModule,\n  ],\n})\nexport class ReunionsModule {}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAEjE,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,oBAAoB,QAAQ,uCAAuC;AAmBrE,WAAMC,cAAc,GAApB,MAAMA,cAAc,GAAG;AAAjBA,cAAc,GAAAC,UAAA,EAjB1BZ,QAAQ,CAAC;EACRa,YAAY,EAAE,CACZV,oBAAoB,EACpBC,sBAAsB,EACtBC,oBAAoB,EACpBK,oBAAoB,CAErB;EACDI,OAAO,EAAE,CACPb,YAAY,EACZC,qBAAqB,EACrBI,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,WAAW;CAEd,CAAC,C,EACWE,cAAc,CAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}