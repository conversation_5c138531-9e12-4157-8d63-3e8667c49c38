<!-- ============================================================================
     COMPOSANT MESSAGE CHAT - INTERFACE WHATSAPP PROFESSIONNELLE
     ============================================================================ -->

<div class="chat-container" *ngIf="selectedConversation">
  <!-- ========================================================================
       EN-TÊTE DU CHAT
       ======================================================================== -->
  <div class="chat-header">
    <div class="user-info">
      <img
        [src]="
          selectedConversation.isGroup
            ? selectedConversation.groupPhoto
            : getRecipientAvatar()
        "
        [alt]="
          selectedConversation.isGroup
            ? selectedConversation.groupName
            : getRecipientName()
        "
        class="user-avatar"
        [class.online]="!selectedConversation.isGroup && isRecipientOnline()"
      />

      <div class="user-details">
        <h3>
          {{
            selectedConversation.isGroup
              ? selectedConversation.groupName
              : getRecipientName()
          }}
        </h3>
        <p
          class="user-status"
          [class.online]="!selectedConversation.isGroup && isRecipientOnline()"
        >
          <span *ngIf="selectedConversation.isGroup">
            {{ selectedConversation.participants?.length }} participants
          </span>
          <span *ngIf="!selectedConversation.isGroup && isRecipientOnline()"
            >En ligne</span
          >
          <span *ngIf="!selectedConversation.isGroup && !isRecipientOnline()"
            >Hors ligne</span
          >
        </p>
      </div>
    </div>

    <div class="chat-actions">
      <!-- Bouton d'appel audio -->
      <button
        class="action-btn"
        (click)="startAudioCall()"
        title="Appel audio"
        *ngIf="!selectedConversation.isGroup"
      >
        <i class="fas fa-phone"></i>
      </button>

      <!-- Bouton d'appel vidéo -->
      <button
        class="action-btn"
        (click)="startVideoCall()"
        title="Appel vidéo"
        *ngIf="!selectedConversation.isGroup"
      >
        <i class="fas fa-video"></i>
      </button>

      <!-- Menu d'options -->
      <button class="action-btn" title="Options">
        <i class="fas fa-ellipsis-v"></i>
      </button>
    </div>
  </div>

  <!-- ========================================================================
       ZONE DES MESSAGES
       ======================================================================== -->
  <div class="messages-container" #messagesContainer>
    <!-- Indicateur de chargement -->
    <div *ngIf="isLoading" class="flex justify-center py-4">
      <div
        class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"
      ></div>
    </div>

    <!-- Messages -->
    <div
      *ngFor="let message of messages; trackBy: trackByMessageId"
      class="message"
      [class.my-message]="isMyMessage(message)"
    >
      <!-- Avatar de l'expéditeur (seulement pour les messages des autres) -->
      <img
        *ngIf="!isMyMessage(message) && message.sender"
        [src]="message.sender.image || '/assets/images/default-avatar.png'"
        [alt]="message.sender.username"
        class="message-avatar"
      />

      <!-- Contenu du message -->
      <div
        class="message-content"
        [class.my-message]="isMyMessage(message)"
        [class.other-message]="!isMyMessage(message)"
      >
        <!-- Nom de l'expéditeur (pour les groupes) -->
        <div
          *ngIf="selectedConversation.isGroup && !isMyMessage(message)"
          class="text-xs text-blue-400 mb-1 font-medium"
        >
          {{ message.sender?.username }}
        </div>

        <!-- Message de réponse -->
        <div *ngIf="message.replyTo" class="reply-preview mb-2">
          <div class="text-xs text-gray-400">
            Réponse à {{ message.replyTo.sender?.username }}
          </div>
          <div class="text-sm text-gray-300 truncate">
            {{ message.replyTo.content }}
          </div>
        </div>

        <!-- Contenu selon le type de message -->
        <div [ngSwitch]="message.type">
          <!-- Message texte -->
          <div *ngSwitchCase="MessageType.TEXT" class="message-text">
            {{ message.content }}
          </div>

          <!-- Message image -->
          <div *ngSwitchCase="MessageType.IMAGE" class="message-image">
            <img
              [src]="message.attachments?.[0]?.url"
              [alt]="message.attachments?.[0]?.name"
              class="message-image"
              (click)="openImageViewer(message.attachments?.[0])"
            />
            <div *ngIf="message.content" class="message-text mt-2">
              {{ message.content }}
            </div>
          </div>

          <!-- Message fichier -->
          <div *ngSwitchCase="MessageType.FILE" class="message-file">
            <i class="file-icon fas fa-file"></i>
            <div class="file-info">
              <div class="file-name">{{ message.attachments?.[0]?.name }}</div>
              <div class="file-size">
                {{ formatFileSize(message.attachments?.[0]?.size) }}
              </div>
            </div>
            <button
              class="text-blue-400 hover:text-blue-300"
              (click)="downloadFile(message.attachments?.[0])"
            >
              <i class="fas fa-download"></i>
            </button>
          </div>

          <!-- Message vocal -->
          <div *ngSwitchCase="MessageType.VOICE_MESSAGE" class="voice-message">
            <button class="voice-play-btn" (click)="playVoiceMessage(message)">
              <i class="fas fa-play text-white text-xs"></i>
            </button>
            <div class="voice-duration">
              {{ formatDuration(message.attachments?.[0]?.duration) }}
            </div>
          </div>

          <!-- Message vidéo -->
          <div *ngSwitchCase="MessageType.VIDEO" class="message-video">
            <video
              [src]="message.attachments?.[0]?.url"
              controls
              class="max-w-xs rounded-lg"
            ></video>
            <div *ngIf="message.content" class="message-text mt-2">
              {{ message.content }}
            </div>
          </div>
        </div>

        <!-- Réactions -->
        <div
          *ngIf="message.reactions && message.reactions.length > 0"
          class="flex flex-wrap gap-1 mt-2"
        >
          <span
            *ngFor="let reaction of message.reactions"
            class="text-xs bg-gray-600 rounded-full px-2 py-1 cursor-pointer"
            (click)="reactToMessage(message, reaction.emoji)"
          >
            {{ reaction.emoji }} {{ reaction.count }}
          </span>
        </div>

        <!-- Heure et statut -->
        <div class="flex items-center justify-between mt-1">
          <span class="message-time">
            {{ formatMessageTime(message.timestamp!) }}
          </span>

          <!-- Statut du message (seulement pour mes messages) -->
          <div
            *ngIf="isMyMessage(message)"
            class="message-status"
            [class.read]="message.isRead"
            [class.pending]="message.isPending"
            [class.error]="message.isError"
          >
            <i *ngIf="message.isPending" class="fas fa-clock"></i>
            <i *ngIf="message.isError" class="fas fa-exclamation-triangle"></i>
            <i
              *ngIf="!message.isPending && !message.isError && message.isRead"
              class="fas fa-check-double"
            ></i>
            <i
              *ngIf="!message.isPending && !message.isError && !message.isRead"
              class="fas fa-check"
            ></i>
          </div>
        </div>

        <!-- Menu contextuel du message -->
        <div
          class="message-menu absolute top-0 right-0 hidden group-hover:block"
        >
          <button
            class="text-gray-400 hover:text-white p-1"
            (click)="showMessageMenu(message)"
          >
            <i class="fas fa-ellipsis-h text-xs"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Indicateur de frappe -->
    <div *ngIf="typingUsers.length > 0" class="typing-indicator">
      <div class="typing-dots">
        <div class="typing-dot"></div>
        <div class="typing-dot"></div>
        <div class="typing-dot"></div>
      </div>
      <span>{{ getTypingText() }}</span>
    </div>
  </div>

  <!-- ========================================================================
       ZONE DE SAISIE DES MESSAGES
       ======================================================================== -->
  <div class="message-input-container">
    <!-- Aperçu de réponse -->
    <div *ngIf="replyingTo" class="reply-preview">
      <div class="reply-header">
        <div>
          <div class="text-xs text-blue-400">
            Réponse à {{ replyingTo.sender?.username }}
          </div>
          <div class="reply-text">{{ replyingTo.content }}</div>
        </div>
        <button (click)="cancelReply()" class="text-gray-400 hover:text-white">
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>

    <!-- Aperçu de modification -->
    <div *ngIf="editingMessage" class="reply-preview">
      <div class="reply-header">
        <div>
          <div class="text-xs text-yellow-400">Modification du message</div>
          <div class="reply-text">{{ editingMessage.content }}</div>
        </div>
        <button
          (click)="cancelEditing()"
          class="text-gray-400 hover:text-white"
        >
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>

    <!-- Fichiers sélectionnés -->
    <div *ngIf="selectedFiles.length > 0" class="mb-3">
      <div class="flex flex-wrap gap-2">
        <div
          *ngFor="let file of selectedFiles; let i = index"
          class="flex items-center space-x-2 bg-gray-700 rounded-lg p-2"
        >
          <i class="fas fa-file text-blue-400"></i>
          <span class="text-sm text-white truncate max-w-32">{{
            file.name
          }}</span>
          <button
            (click)="removeSelectedFile(i)"
            class="text-red-400 hover:text-red-300"
          >
            <i class="fas fa-times text-xs"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Indicateur d'enregistrement vocal -->
    <div *ngIf="isRecording" class="recording-indicator mb-3">
      <i class="fas fa-microphone text-white"></i>
      <span class="recording-time">{{
        formatDuration(recordingDuration)
      }}</span>
      <button
        (click)="stopVoiceRecording()"
        class="text-white hover:text-gray-300"
      >
        <i class="fas fa-stop"></i>
      </button>
      <button
        (click)="cancelVoiceRecording()"
        class="text-white hover:text-gray-300 ml-2"
      >
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- Zone de saisie principale -->
    <div class="input-wrapper">
      <!-- Boutons d'actions à gauche -->
      <div class="input-actions">
        <!-- Bouton pièce jointe -->
        <div class="relative">
          <button
            class="input-btn"
            (click)="toggleAttachmentMenu()"
            title="Pièce jointe"
          >
            <i class="fas fa-paperclip"></i>
          </button>

          <!-- Menu des pièces jointes -->
          <div
            *ngIf="showAttachmentMenu"
            class="absolute bottom-full left-0 mb-2 bg-gray-800 rounded-lg shadow-lg p-2 space-y-1"
          >
            <button
              (click)="openFileSelector()"
              class="flex items-center space-x-2 w-full p-2 hover:bg-gray-700 rounded text-left"
            >
              <i class="fas fa-file text-blue-400"></i>
              <span class="text-white text-sm">Fichier</span>
            </button>
            <button
              (click)="openFileSelector()"
              class="flex items-center space-x-2 w-full p-2 hover:bg-gray-700 rounded text-left"
            >
              <i class="fas fa-image text-green-400"></i>
              <span class="text-white text-sm">Image</span>
            </button>
          </div>
        </div>

        <!-- Bouton emoji -->
        <button class="input-btn" (click)="toggleEmojiPicker()" title="Emoji">
          <i class="fas fa-smile"></i>
        </button>
      </div>

      <!-- Champ de saisie -->
      <textarea
        #messageInput
        [(ngModel)]="messageContent"
        (keydown)="onKeyPress($event)"
        (input)="onTyping()"
        placeholder="Tapez votre message..."
        class="message-input"
        rows="1"
        [disabled]="isRecording"
      ></textarea>

      <!-- Boutons d'actions à droite -->
      <div class="input-actions">
        <!-- Bouton enregistrement vocal (si pas de texte) -->
        <button
          *ngIf="
            !messageContent.trim() && !selectedFiles.length && !isRecording
          "
          class="input-btn"
          (mousedown)="startVoiceRecording()"
          title="Message vocal"
        >
          <i class="fas fa-microphone"></i>
        </button>

        <!-- Bouton d'envoi (si du texte ou des fichiers) -->
        <button
          *ngIf="messageContent.trim() || selectedFiles.length"
          class="send-btn"
          (click)="sendMessage()"
          [disabled]="!canSendMessage()"
          title="Envoyer"
        >
          <i class="fas fa-paper-plane text-white"></i>
        </button>
      </div>
    </div>

    <!-- Input file caché -->
    <input
      #fileInput
      type="file"
      multiple
      class="hidden"
      (change)="onFileSelected($event)"
      accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt"
    />
  </div>
</div>

<!-- ============================================================================
     MESSAGE VIDE (AUCUNE CONVERSATION SÉLECTIONNÉE)
     ============================================================================ -->
<div
  *ngIf="!selectedConversation"
  class="flex items-center justify-center h-full bg-gray-900 text-gray-400"
>
  <div class="text-center">
    <i class="fas fa-comments text-6xl mb-4 text-gray-600"></i>
    <h3 class="text-xl font-semibold mb-2">Sélectionnez une conversation</h3>
    <p>Choisissez une conversation dans la liste pour commencer à discuter</p>
  </div>
</div>
