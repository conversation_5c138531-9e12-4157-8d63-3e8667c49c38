{"ast": null, "code": "import { Subject, of, BehaviorSubject } from 'rxjs';\nimport { MessageType } from 'src/app/models/message.model';\nimport { catchError, map, takeUntil, take, debounceTime, distinctUntilChanged, filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/services/message.service\";\nimport * as i2 from \"@app/services/theme.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nconst _c0 = [\"notificationContainer\"];\nfunction NotificationListComponent_div_8_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"label\", 34)(2, \"input\", 35);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_div_3_Template_input_click_2_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r13.toggleSelectAll($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"span\", 36);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"checked\", ctx_r10.allSelected);\n  }\n}\nfunction NotificationListComponent_div_8_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.markAllAsRead());\n    });\n    i0.ɵɵelement(1, \"i\", 38);\n    i0.ɵɵtext(2, \" Tout marquer comme lu \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationListComponent_div_8_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.deleteAllNotifications());\n    });\n    i0.ɵɵelement(1, \"i\", 40);\n    i0.ɵɵtext(2, \" Tout supprimer \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationListComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.loadNotifications());\n    });\n    i0.ɵɵelement(2, \"i\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, NotificationListComponent_div_8_div_3_Template, 4, 1, \"div\", 26);\n    i0.ɵɵpipe(4, \"async\");\n    i0.ɵɵelementStart(5, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.toggleUnreadFilter());\n    });\n    i0.ɵɵelement(6, \"i\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_8_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.toggleSound());\n    });\n    i0.ɵɵelement(8, \"i\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, NotificationListComponent_div_8_button_9_Template, 3, 0, \"button\", 31);\n    i0.ɵɵpipe(10, \"async\");\n    i0.ɵɵtemplate(11, NotificationListComponent_div_8_button_11_Template, 3, 0, \"button\", 32);\n    i0.ɵɵpipe(12, \"async\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(4, 9, ctx_r0.hasNotifications()));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", ctx_r0.showOnlyUnread);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"active\", !ctx_r0.isSoundMuted);\n    i0.ɵɵpropertyInterpolate(\"title\", ctx_r0.isSoundMuted ? \"Activer le son\" : \"D\\u00E9sactiver le son\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.isSoundMuted ? \"fa-volume-mute\" : \"fa-volume-up\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(10, 11, ctx_r0.unreadCount$) || 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(12, 13, ctx_r0.hasNotifications()));\n  }\n}\nfunction NotificationListComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"span\", 42);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_9_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.markSelectedAsRead());\n    });\n    i0.ɵɵelement(4, \"i\", 43);\n    i0.ɵɵtext(5, \" Marquer comme lu \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_9_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.deleteSelectedNotifications());\n    });\n    i0.ɵɵelement(7, \"i\", 40);\n    i0.ɵɵtext(8, \" Supprimer \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_9_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r26 = i0.ɵɵnextContext();\n      ctx_r26.selectedNotifications.clear();\n      ctx_r26.showSelectionBar = false;\n      return i0.ɵɵresetView(ctx_r26.allSelected = false);\n    });\n    i0.ɵɵelement(10, \"i\", 46);\n    i0.ɵɵtext(11, \" Annuler \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.selectedNotifications.size, \" s\\u00E9lectionn\\u00E9(s)\");\n  }\n}\nfunction NotificationListComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵelement(1, \"div\", 48);\n    i0.ɵɵelementStart(2, \"p\", 49);\n    i0.ɵɵtext(3, \"Chargement des notifications...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 51);\n    i0.ɵɵelement(2, \"i\", 52);\n    i0.ɵɵelementStart(3, \"div\")(4, \"h3\", 53);\n    i0.ɵɵtext(5, \"Erreur de chargement\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 54);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_11_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.loadNotifications());\n    });\n    i0.ɵɵtext(9, \" R\\u00E9essayer \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r3.getErrorMessage());\n  }\n}\nfunction NotificationListComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"div\", 57);\n    i0.ɵɵelement(2, \"i\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 59);\n    i0.ɵɵtext(4, \"Aucune notification\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 60);\n    i0.ɵɵtext(6, \"Vous \\u00EAtes \\u00E0 jour !\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_12_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.loadNotifications());\n    });\n    i0.ɵɵtext(8, \" V\\u00E9rifier les nouvelles notifications \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const notification_r34 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", notification_r34.message == null ? null : notification_r34.message.content, \" \");\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 90);\n    i0.ɵɵelement(1, \"i\", 91);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const notification_r34 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", notification_r34.message == null ? null : notification_r34.message.attachments == null ? null : notification_r34.message.attachments.length, \" pi\\u00E8ce(s) jointe(s) \");\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 92);\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 93);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_button_24_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r45);\n      const notification_r34 = i0.ɵɵnextContext().$implicit;\n      const ctx_r43 = i0.ɵɵnextContext(2);\n      ctx_r43.getNotificationAttachments(notification_r34.id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(1, \"i\", 91);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_button_25_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 97);\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_button_25_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 98);\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_button_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r50 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 94);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_button_25_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r50);\n      const notification_r34 = i0.ɵɵnextContext().$implicit;\n      const ctx_r48 = i0.ɵɵnextContext(2);\n      ctx_r48.joinConversation(notification_r34);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵtemplate(1, NotificationListComponent_div_14_ng_container_2_button_25_i_1_Template, 1, 0, \"i\", 95);\n    i0.ɵɵtemplate(2, NotificationListComponent_div_14_ng_container_2_button_25_i_2_Template, 1, 0, \"i\", 96);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r39 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r39.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r39.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r39.loading);\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_button_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r53 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 99);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_button_28_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r53);\n      const notification_r34 = i0.ɵɵnextContext().$implicit;\n      const ctx_r51 = i0.ɵɵnextContext(2);\n      ctx_r51.markAsRead(notification_r34.id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(1, \"i\", 100);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationListComponent_div_14_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r55 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 66)(2, \"div\", 67)(3, \"label\", 34)(4, \"input\", 35);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_Template_input_click_4_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r55);\n      const notification_r34 = restoredCtx.$implicit;\n      const ctx_r54 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r54.toggleSelection(notification_r34.id, $event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"span\", 36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 68);\n    i0.ɵɵelement(7, \"img\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 70)(9, \"div\", 71)(10, \"div\", 72)(11, \"div\", 73)(12, \"span\", 74);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 75);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 76)(18, \"span\", 77);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(20, NotificationListComponent_div_14_ng_container_2_div_20_Template, 2, 1, \"div\", 78);\n    i0.ɵɵtemplate(21, NotificationListComponent_div_14_ng_container_2_div_21_Template, 3, 1, \"div\", 79);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, NotificationListComponent_div_14_ng_container_2_div_22_Template, 1, 0, \"div\", 80);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 81);\n    i0.ɵɵtemplate(24, NotificationListComponent_div_14_ng_container_2_button_24_Template, 2, 0, \"button\", 82);\n    i0.ɵɵtemplate(25, NotificationListComponent_div_14_ng_container_2_button_25_Template, 3, 3, \"button\", 83);\n    i0.ɵɵelementStart(26, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_Template_button_click_26_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r55);\n      const notification_r34 = restoredCtx.$implicit;\n      const ctx_r56 = i0.ɵɵnextContext(2);\n      ctx_r56.openNotificationDetails(notification_r34);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(27, \"i\", 85);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(28, NotificationListComponent_div_14_ng_container_2_button_28_Template, 2, 0, \"button\", 86);\n    i0.ɵɵelementStart(29, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_14_ng_container_2_Template_button_click_29_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r55);\n      const notification_r34 = restoredCtx.$implicit;\n      const ctx_r57 = i0.ɵɵnextContext(2);\n      ctx_r57.deleteNotification(notification_r34.id);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(30, \"i\", 88);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const notification_r34 = ctx.$implicit;\n    const ctx_r32 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"futuristic-notification-unread\", !notification_r34.isRead)(\"futuristic-notification-read\", notification_r34.isRead)(\"futuristic-notification-selected\", ctx_r32.isSelected(notification_r34.id));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"checked\", ctx_r32.isSelected(notification_r34.id));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", (notification_r34.senderId == null ? null : notification_r34.senderId.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate((notification_r34.senderId == null ? null : notification_r34.senderId.username) || \"Syst\\u00E8me\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(16, 17, notification_r34.timestamp, \"shortTime\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(notification_r34.content);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", notification_r34.message == null ? null : notification_r34.message.content);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", notification_r34.message == null ? null : notification_r34.message.attachments == null ? null : notification_r34.message.attachments.length);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !notification_r34.isRead);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", notification_r34.message == null ? null : notification_r34.message.attachments == null ? null : notification_r34.message.attachments.length);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", notification_r34.type === \"NEW_MESSAGE\" || notification_r34.type === \"GROUP_INVITE\" || notification_r34.type === \"MESSAGE_REACTION\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !notification_r34.isRead);\n  }\n}\nfunction NotificationListComponent_div_14_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 101);\n    i0.ɵɵelement(1, \"div\", 102);\n    i0.ɵɵelementStart(2, \"p\", 103);\n    i0.ɵɵtext(3, \" Chargement des notifications plus anciennes... \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r59 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62, 63);\n    i0.ɵɵlistener(\"scroll\", function NotificationListComponent_div_14_Template_div_scroll_0_listener() {\n      i0.ɵɵrestoreView(_r59);\n      const _r31 = i0.ɵɵreference(1);\n      const ctx_r58 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r58.onScroll(_r31));\n    });\n    i0.ɵɵtemplate(2, NotificationListComponent_div_14_ng_container_2_Template, 31, 20, \"ng-container\", 64);\n    i0.ɵɵpipe(3, \"async\");\n    i0.ɵɵtemplate(4, NotificationListComponent_div_14_div_4_Template, 4, 0, \"div\", 65);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(3, 2, ctx_r5.filteredNotifications$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.loadingMore);\n  }\n}\nfunction NotificationListComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵelement(1, \"div\", 48);\n    i0.ɵɵelementStart(2, \"p\", 49);\n    i0.ɵɵtext(3, \"Chargement des pi\\u00E8ces jointes...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"div\", 57);\n    i0.ɵɵelement(2, \"i\", 104);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 59);\n    i0.ɵɵtext(4, \"Aucune pi\\u00E8ce jointe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 60);\n    i0.ɵɵtext(6, \" Aucune pi\\u00E8ce jointe n'a \\u00E9t\\u00E9 trouv\\u00E9e pour cette notification. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_27_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r67 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 120)(1, \"img\", 121);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_27_div_1_div_1_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r67);\n      const attachment_r61 = i0.ɵɵnextContext().$implicit;\n      const ctx_r65 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r65.openAttachment(attachment_r61.url));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const attachment_r61 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", attachment_r61.url, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction NotificationListComponent_div_27_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 122);\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const attachment_r61 = i0.ɵɵnextContext().$implicit;\n    const ctx_r63 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r63.getFileIcon(attachment_r61.type));\n  }\n}\nfunction NotificationListComponent_div_27_div_1_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 123);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const attachment_r61 = i0.ɵɵnextContext().$implicit;\n    const ctx_r64 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r64.formatFileSize(attachment_r61.size));\n  }\n}\nfunction NotificationListComponent_div_27_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r72 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 107);\n    i0.ɵɵtemplate(1, NotificationListComponent_div_27_div_1_div_1_Template, 2, 1, \"div\", 108);\n    i0.ɵɵtemplate(2, NotificationListComponent_div_27_div_1_div_2_Template, 2, 2, \"div\", 109);\n    i0.ɵɵelementStart(3, \"div\", 110)(4, \"div\", 111);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 112)(7, \"span\", 113);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, NotificationListComponent_div_27_div_1_span_9_Template, 2, 1, \"span\", 114);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 115)(11, \"button\", 116);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_27_div_1_Template_button_click_11_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r72);\n      const attachment_r61 = restoredCtx.$implicit;\n      const ctx_r71 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r71.openAttachment(attachment_r61.url));\n    });\n    i0.ɵɵelement(12, \"i\", 117);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 118);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_27_div_1_Template_button_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r72);\n      const attachment_r61 = restoredCtx.$implicit;\n      const ctx_r73 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r73.downloadAttachment(attachment_r61));\n    });\n    i0.ɵɵelement(14, \"i\", 119);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const attachment_r61 = ctx.$implicit;\n    const ctx_r60 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r60.isImage(attachment_r61.type));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r60.isImage(attachment_r61.type));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", attachment_r61.name || \"Pi\\u00E8ce jointe\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r60.getFileTypeLabel(attachment_r61.type));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", attachment_r61.size);\n  }\n}\nfunction NotificationListComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105);\n    i0.ɵɵtemplate(1, NotificationListComponent_div_27_div_1_Template, 15, 5, \"div\", 106);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.currentAttachments);\n  }\n}\nfunction NotificationListComponent_div_36_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 147)(1, \"strong\");\n    i0.ɵɵtext(2, \"Message original :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r74 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r74.currentNotification.message == null ? null : ctx_r74.currentNotification.message.content, \" \");\n  }\n}\nfunction NotificationListComponent_div_36_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 137)(1, \"span\", 138);\n    i0.ɵɵtext(2, \"Lu le :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 139);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r75 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 1, ctx_r75.currentNotification.readAt, \"medium\"));\n  }\n}\nfunction NotificationListComponent_div_36_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 148)(1, \"span\", 138);\n    i0.ɵɵelement(2, \"i\", 149);\n    i0.ɵɵtext(3, \" Note : \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 150);\n    i0.ɵɵtext(5, \" Ouvrir les d\\u00E9tails ne marque pas automatiquement comme lu \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotificationListComponent_div_36_div_37_div_5_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r87 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 164)(1, \"img\", 121);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_36_div_37_div_5_div_1_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r87);\n      const attachment_r81 = i0.ɵɵnextContext().$implicit;\n      const ctx_r85 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r85.openAttachment(attachment_r81.url));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const attachment_r81 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", attachment_r81.url, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction NotificationListComponent_div_36_div_37_div_5_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 165);\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const attachment_r81 = i0.ɵɵnextContext().$implicit;\n    const ctx_r83 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r83.getFileIcon(attachment_r81.type));\n  }\n}\nfunction NotificationListComponent_div_36_div_37_div_5_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 166);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const attachment_r81 = i0.ɵɵnextContext().$implicit;\n    const ctx_r84 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r84.formatFileSize(attachment_r81.size));\n  }\n}\nfunction NotificationListComponent_div_36_div_37_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r92 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 153);\n    i0.ɵɵtemplate(1, NotificationListComponent_div_36_div_37_div_5_div_1_Template, 2, 1, \"div\", 154);\n    i0.ɵɵtemplate(2, NotificationListComponent_div_36_div_37_div_5_div_2_Template, 2, 2, \"div\", 155);\n    i0.ɵɵelementStart(3, \"div\", 156)(4, \"div\", 157);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 158)(7, \"span\", 159);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, NotificationListComponent_div_36_div_37_div_5_span_9_Template, 2, 1, \"span\", 160);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 161)(11, \"button\", 162);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_36_div_37_div_5_Template_button_click_11_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r92);\n      const attachment_r81 = restoredCtx.$implicit;\n      const ctx_r91 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r91.openAttachment(attachment_r81.url));\n    });\n    i0.ɵɵelement(12, \"i\", 117);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 163);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_36_div_37_div_5_Template_button_click_13_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r92);\n      const attachment_r81 = restoredCtx.$implicit;\n      const ctx_r93 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r93.downloadAttachment(attachment_r81));\n    });\n    i0.ɵɵelement(14, \"i\", 119);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const attachment_r81 = ctx.$implicit;\n    const ctx_r80 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r80.isImage(attachment_r81.type));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r80.isImage(attachment_r81.type));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", attachment_r81.name || \"Pi\\u00E8ce jointe\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r80.getFileTypeLabel(attachment_r81.type));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", attachment_r81.size);\n  }\n}\nfunction NotificationListComponent_div_36_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 124)(1, \"h4\", 125);\n    i0.ɵɵelement(2, \"i\", 16);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 151);\n    i0.ɵɵtemplate(5, NotificationListComponent_div_36_div_37_div_5_Template, 15, 5, \"div\", 152);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r77 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Pi\\u00E8ces jointes (\", ctx_r77.currentAttachments.length, \") \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r77.currentAttachments);\n  }\n}\nfunction NotificationListComponent_div_36_button_39_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 170);\n  }\n}\nfunction NotificationListComponent_div_36_button_39_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 171);\n  }\n}\nfunction NotificationListComponent_div_36_button_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r97 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 167);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_36_button_39_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r97);\n      const ctx_r96 = i0.ɵɵnextContext(2);\n      ctx_r96.joinConversation(ctx_r96.currentNotification);\n      return i0.ɵɵresetView(ctx_r96.closeNotificationDetailsModal());\n    });\n    i0.ɵɵtemplate(1, NotificationListComponent_div_36_button_39_i_1_Template, 1, 0, \"i\", 168);\n    i0.ɵɵtemplate(2, NotificationListComponent_div_36_button_39_i_2_Template, 1, 0, \"i\", 169);\n    i0.ɵɵtext(3, \" Rejoindre la conversation \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r78 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r78.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r78.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r78.loading);\n  }\n}\nfunction NotificationListComponent_div_36_button_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r99 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 172);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_36_button_40_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r99);\n      const ctx_r98 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r98.markAsRead(ctx_r98.currentNotification.id));\n    });\n    i0.ɵɵelement(1, \"i\", 173);\n    i0.ɵɵtext(2, \" Marquer comme lu \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationListComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r101 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 124)(2, \"h4\", 125);\n    i0.ɵɵelement(3, \"i\", 126);\n    i0.ɵɵtext(4, \" Exp\\u00E9diteur \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 127);\n    i0.ɵɵelement(6, \"img\", 128);\n    i0.ɵɵelementStart(7, \"div\", 129)(8, \"span\", 130);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 131);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 124)(14, \"h4\", 125);\n    i0.ɵɵelement(15, \"i\", 132);\n    i0.ɵɵtext(16, \" Message \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 133);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, NotificationListComponent_div_36_div_19_Template, 4, 1, \"div\", 134);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 124)(21, \"h4\", 125);\n    i0.ɵɵelement(22, \"i\", 135);\n    i0.ɵɵtext(23, \" Informations \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 136)(25, \"div\", 137)(26, \"span\", 138);\n    i0.ɵɵtext(27, \"Type :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"span\", 139);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 137)(31, \"span\", 138);\n    i0.ɵɵtext(32, \"Statut :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"span\", 139);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(35, NotificationListComponent_div_36_div_35_Template, 6, 4, \"div\", 140);\n    i0.ɵɵtemplate(36, NotificationListComponent_div_36_div_36_Template, 6, 0, \"div\", 141);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(37, NotificationListComponent_div_36_div_37_Template, 6, 2, \"div\", 142);\n    i0.ɵɵelementStart(38, \"div\", 143);\n    i0.ɵɵtemplate(39, NotificationListComponent_div_36_button_39_Template, 4, 3, \"button\", 144);\n    i0.ɵɵtemplate(40, NotificationListComponent_div_36_button_40_Template, 3, 0, \"button\", 145);\n    i0.ɵɵelementStart(41, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function NotificationListComponent_div_36_Template_button_click_41_listener() {\n      i0.ɵɵrestoreView(_r101);\n      const ctx_r100 = i0.ɵɵnextContext();\n      ctx_r100.deleteNotification(ctx_r100.currentNotification.id);\n      return i0.ɵɵresetView(ctx_r100.closeNotificationDetailsModal());\n    });\n    i0.ɵɵelement(42, \"i\", 146);\n    i0.ɵɵtext(43, \" Supprimer \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"src\", (ctx_r9.currentNotification.senderId == null ? null : ctx_r9.currentNotification.senderId.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r9.currentNotification.senderId == null ? null : ctx_r9.currentNotification.senderId.username) || \"Syst\\u00E8me\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(12, 16, ctx_r9.currentNotification.timestamp, \"medium\"), \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.currentNotification.content, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.currentNotification.message == null ? null : ctx_r9.currentNotification.message.content);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r9.currentNotification.type);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"text-green-500\", ctx_r9.currentNotification.isRead)(\"text-orange-500\", !ctx_r9.currentNotification.isRead);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.currentNotification.isRead ? \"Lu\" : \"Non lu\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.currentNotification.readAt);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.currentNotification.isRead);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.currentAttachments.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.currentNotification.type === \"NEW_MESSAGE\" || ctx_r9.currentNotification.type === \"GROUP_INVITE\" || ctx_r9.currentNotification.type === \"MESSAGE_REACTION\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.currentNotification.isRead);\n  }\n}\nexport let NotificationListComponent = /*#__PURE__*/(() => {\n  class NotificationListComponent {\n    constructor(messageService, themeService, router) {\n      this.messageService = messageService;\n      this.themeService = themeService;\n      this.router = router;\n      this.loading = true;\n      this.loadingMore = false;\n      this.hasMoreNotifications = true;\n      this.error = null;\n      this.showOnlyUnread = false;\n      this.isSoundMuted = false;\n      // Propriétés pour la sélection multiple\n      this.selectedNotifications = new Set();\n      this.allSelected = false;\n      this.showSelectionBar = false;\n      // Propriétés pour le modal des pièces jointes\n      this.showAttachmentsModal = false;\n      this.loadingAttachments = false;\n      this.currentAttachments = [];\n      // Propriétés pour le modal des détails de notification\n      this.showNotificationDetailsModal = false;\n      this.currentNotification = null;\n      this.destroy$ = new Subject();\n      this.scrollPosition$ = new BehaviorSubject(0);\n      this.notifications$ = this.messageService.notifications$;\n      this.filteredNotifications$ = this.notifications$; // Par défaut, afficher toutes les notifications\n      this.unreadCount$ = this.messageService.notificationCount$;\n      this.isDarkMode$ = this.themeService.darkMode$;\n      // Vérifier l'état du son\n      this.isSoundMuted = this.messageService.isMuted();\n    }\n    /**\n     * Rejoint une conversation ou un groupe à partir d'une notification\n     * @param notification Notification contenant les informations de la conversation ou du groupe\n     */\n    joinConversation(notification) {\n      // Marquer la notification comme lue d'abord\n      this.markAsRead(notification.id);\n      // Extraire les informations pertinentes de la notification\n      const conversationId = notification.conversationId || notification.metadata && notification.metadata['conversationId'] || (notification.relatedEntity && notification.relatedEntity.includes('conversation') ? notification.relatedEntity : null);\n      const groupId = notification.groupId || notification.metadata && notification.metadata['groupId'] || (notification.relatedEntity && notification.relatedEntity.includes('group') ? notification.relatedEntity : null);\n      // Déterminer où rediriger l'utilisateur\n      if (conversationId) {\n        this.router.navigate(['/messages/conversations/chat', conversationId]);\n      } else if (groupId) {\n        this.router.navigate(['/messages/group', groupId]);\n      } else if (notification.senderId && notification.senderId.id) {\n        this.loading = true;\n        this.messageService.getOrCreateConversation(notification.senderId.id).subscribe({\n          next: conversation => {\n            this.loading = false;\n            if (conversation && conversation.id) {\n              this.router.navigate(['/messages/conversations/chat', conversation.id]);\n            } else {\n              this.router.navigate(['/messages']);\n            }\n          },\n          error: error => {\n            this.loading = false;\n            this.error = error;\n            this.router.navigate(['/messages']);\n          }\n        });\n      } else {\n        this.router.navigate(['/messages']);\n      }\n    }\n    onScroll(target) {\n      if (!target) return;\n      const scrollPosition = target.scrollTop;\n      const scrollHeight = target.scrollHeight;\n      const clientHeight = target.clientHeight;\n      // Si on est proche du bas (à 200px du bas)\n      if (scrollHeight - scrollPosition - clientHeight < 200) {\n        this.scrollPosition$.next(scrollPosition);\n      }\n    }\n    ngOnInit() {\n      // Charger la préférence de son depuis le localStorage\n      const savedMutePreference = localStorage.getItem('notificationSoundMuted');\n      if (savedMutePreference !== null) {\n        this.isSoundMuted = savedMutePreference === 'true';\n        this.messageService.setMuted(this.isSoundMuted);\n      }\n      this.loadNotifications();\n      this.setupSubscriptions();\n      this.setupInfiniteScroll();\n      this.filterDeletedNotifications();\n    }\n    /**\n     * Filtre les notifications supprimées lors du chargement initial\n     */\n    filterDeletedNotifications() {\n      const deletedNotificationIds = this.getDeletedNotificationIds();\n      if (deletedNotificationIds.size > 0) {\n        this.notifications$.pipe(take(1)).subscribe(notifications => {\n          const filteredNotifications = notifications.filter(notification => !deletedNotificationIds.has(notification.id));\n          this.messageService.notifications.next(filteredNotifications);\n          const unreadCount = filteredNotifications.filter(n => !n.isRead).length;\n          this.messageService.notificationCount.next(unreadCount);\n          this.updateNotificationCache(filteredNotifications);\n        });\n      }\n    }\n    setupInfiniteScroll() {\n      // Configurer le chargement des anciennes notifications lors du défilement\n      this.scrollPosition$.pipe(takeUntil(this.destroy$), debounceTime(200),\n      // Attendre 200ms après le dernier événement de défilement\n      distinctUntilChanged(),\n      // Ne déclencher que si la position de défilement a changé\n      filter(() => !this.loadingMore && this.hasMoreNotifications) // Ne charger que s'il y a plus de notifications et qu'on n'est pas déjà en train de charger\n      ).subscribe(() => {\n        this.loadMoreNotifications();\n      });\n    }\n    loadNotifications() {\n      this.loading = true;\n      this.loadingMore = false;\n      this.error = null;\n      this.hasMoreNotifications = true;\n      const deletedNotificationIds = this.getDeletedNotificationIds();\n      this.messageService.getNotifications(true).pipe(takeUntil(this.destroy$), map(notifications => {\n        if (deletedNotificationIds.size > 0) {\n          return notifications.filter(notification => !deletedNotificationIds.has(notification.id));\n        }\n        return notifications;\n      })).subscribe({\n        next: notifications => {\n          this.messageService.notifications.next(notifications);\n          const unreadCount = notifications.filter(n => !n.isRead).length;\n          this.messageService.notificationCount.next(unreadCount);\n          this.loading = false;\n          this.hasMoreNotifications = this.messageService.hasMoreNotifications();\n        },\n        error: err => {\n          this.error = err;\n          this.loading = false;\n          this.hasMoreNotifications = false;\n        }\n      });\n    }\n    loadMoreNotifications() {\n      if (this.loadingMore || !this.hasMoreNotifications) return;\n      this.loadingMore = true;\n      const deletedNotificationIds = this.getDeletedNotificationIds();\n      this.messageService.loadMoreNotifications().pipe(takeUntil(this.destroy$), map(notifications => {\n        if (deletedNotificationIds.size > 0) {\n          return notifications.filter(notification => !deletedNotificationIds.has(notification.id));\n        }\n        return notifications;\n      })).subscribe({\n        next: notifications => {\n          this.notifications$.pipe(take(1)).subscribe(existingNotifications => {\n            const allNotifications = [...existingNotifications, ...notifications];\n            this.messageService.notifications.next(allNotifications);\n            const unreadCount = allNotifications.filter(n => !n.isRead).length;\n            this.messageService.notificationCount.next(unreadCount);\n            this.updateNotificationCache(allNotifications);\n          });\n          this.loadingMore = false;\n          this.hasMoreNotifications = this.messageService.hasMoreNotifications();\n        },\n        error: err => {\n          this.loadingMore = false;\n          this.hasMoreNotifications = false;\n        }\n      });\n    }\n    setupSubscriptions() {\n      this.messageService.subscribeToNewNotifications().pipe(takeUntil(this.destroy$), catchError(error => {\n        console.log('Notification stream error:', error);\n        return of(null);\n      })).subscribe();\n      this.messageService.subscribeToNotificationsRead().pipe(takeUntil(this.destroy$), catchError(error => {\n        console.log('Notifications read stream error:', error);\n        return of(null);\n      })).subscribe();\n    }\n    markAsRead(notificationId) {\n      if (!notificationId) {\n        this.error = new Error('ID de notification invalide');\n        return;\n      }\n      this.notifications$.pipe(take(1)).subscribe(notifications => {\n        const notification = notifications.find(n => n.id === notificationId);\n        if (notification) {\n          if (notification.isRead) return;\n          const updatedNotifications = notifications.map(n => n.id === notificationId ? {\n            ...n,\n            isRead: true,\n            readAt: new Date().toISOString()\n          } : n);\n          this.updateUIWithNotifications(updatedNotifications);\n          this.messageService.markAsRead([notificationId]).pipe(takeUntil(this.destroy$)).subscribe({\n            next: result => {\n              if (result && result.success) {\n                if (this.error && this.error.message.includes('mark')) {\n                  this.error = null;\n                }\n              }\n            },\n            error: err => {\n              const revertedNotifications = notifications.map(n => n.id === notificationId ? {\n                ...n,\n                isRead: false,\n                readAt: undefined\n              } : n);\n              this.messageService.notifications.next(revertedNotifications);\n              const revertedUnreadCount = revertedNotifications.filter(n => !n.isRead).length;\n              this.messageService.notificationCount.next(revertedUnreadCount);\n            }\n          });\n        } else {\n          this.loadNotifications();\n        }\n      });\n    }\n    /**\n     * Met à jour l'interface utilisateur avec les nouvelles notifications\n     * @param notifications Notifications à afficher\n     */\n    updateUIWithNotifications(notifications) {\n      // Mettre à jour l'interface utilisateur immédiatement\n      this.messageService.notifications.next(notifications);\n      // Mettre à jour le compteur de notifications non lues\n      const unreadCount = notifications.filter(n => !n.isRead).length;\n      this.messageService.notificationCount.next(unreadCount);\n      // Mettre à jour le cache de notifications dans le service\n      this.updateNotificationCache(notifications);\n    }\n    /**\n     * Met à jour le cache de notifications dans le service\n     * @param notifications Notifications à mettre à jour\n     */\n    updateNotificationCache(notifications) {\n      notifications.forEach(notification => {\n        this.messageService.updateNotificationCache?.(notification);\n      });\n    }\n    /**\n     * Réinitialise la sélection des notifications\n     */\n    resetSelection() {\n      this.selectedNotifications.clear();\n      this.allSelected = false;\n      this.showSelectionBar = false;\n    }\n    markAllAsRead() {\n      this.notifications$.pipe(take(1)).subscribe(notifications => {\n        const unreadIds = notifications.filter(n => !n.isRead).map(n => n.id);\n        if (unreadIds.length === 0) return;\n        const validIds = unreadIds.filter(id => id && typeof id === 'string' && id.trim() !== '');\n        if (validIds.length !== unreadIds.length) {\n          this.error = new Error('Invalid notification IDs');\n          return;\n        }\n        const updatedNotifications = notifications.map(n => validIds.includes(n.id) ? {\n          ...n,\n          isRead: true,\n          readAt: new Date().toISOString()\n        } : n);\n        this.updateUIWithNotifications(updatedNotifications);\n        this.messageService.markAsRead(validIds).pipe(takeUntil(this.destroy$)).subscribe({\n          next: result => {\n            if (result && result.success) {\n              if (this.error && this.error.message.includes('mark')) {\n                this.error = null;\n              }\n            }\n          },\n          error: err => {\n            // Ne pas définir d'erreur pour éviter de perturber l'interface utilisateur\n          }\n        });\n      });\n    }\n    hasNotifications() {\n      return this.notifications$.pipe(map(notifications => notifications?.length > 0));\n    }\n    hasUnreadNotifications() {\n      return this.unreadCount$.pipe(map(count => count > 0));\n    }\n    /**\n     * Active/désactive le filtre pour n'afficher que les notifications non lues\n     */\n    toggleUnreadFilter() {\n      this.showOnlyUnread = !this.showOnlyUnread;\n      if (this.showOnlyUnread) {\n        this.filteredNotifications$ = this.messageService.getUnreadNotifications();\n      } else {\n        this.filteredNotifications$ = this.notifications$;\n      }\n    }\n    /**\n     * Active/désactive le son des notifications\n     */\n    toggleSound() {\n      this.isSoundMuted = !this.isSoundMuted;\n      this.messageService.setMuted(this.isSoundMuted);\n      if (!this.isSoundMuted) {\n        setTimeout(() => {\n          this.messageService.playNotificationSound();\n          setTimeout(() => {\n            this.messageService.playNotificationSound();\n          }, 1000);\n        }, 100);\n      }\n      localStorage.setItem('notificationSoundMuted', this.isSoundMuted.toString());\n    }\n    /**\n     * Récupère les pièces jointes d'une notification et ouvre le modal\n     * @param notificationId ID de la notification\n     */\n    getNotificationAttachments(notificationId) {\n      if (!notificationId) return;\n      this.currentAttachments = [];\n      this.loadingAttachments = true;\n      this.showAttachmentsModal = true;\n      let notification;\n      this.notifications$.pipe(take(1)).subscribe(notifications => {\n        notification = notifications.find(n => n.id === notificationId);\n      });\n      if (notification && notification.message && notification.message.attachments && notification.message.attachments.length > 0) {\n        this.loadingAttachments = false;\n        this.currentAttachments = notification.message.attachments.map(attachment => ({\n          id: '',\n          url: attachment.url || '',\n          type: this.convertAttachmentTypeToMessageType(attachment.type),\n          name: attachment.name || '',\n          size: attachment.size || 0,\n          duration: 0\n        }));\n        return;\n      }\n      this.messageService.getNotificationAttachments(notificationId).pipe(takeUntil(this.destroy$)).subscribe({\n        next: attachments => {\n          this.loadingAttachments = false;\n          this.currentAttachments = attachments;\n        },\n        error: err => {\n          this.loadingAttachments = false;\n        }\n      });\n    }\n    /**\n     * Ferme le modal des pièces jointes\n     */\n    closeAttachmentsModal() {\n      this.showAttachmentsModal = false;\n    }\n    /**\n     * Ouvre le modal des détails de notification\n     * @param notification Notification à afficher\n     */\n    openNotificationDetails(notification) {\n      this.currentNotification = notification;\n      this.showNotificationDetailsModal = true;\n      if (notification.message?.attachments?.length) {\n        this.getNotificationAttachmentsForModal(notification.id);\n      }\n    }\n    /**\n     * Ferme le modal des détails de notification\n     */\n    closeNotificationDetailsModal() {\n      this.showNotificationDetailsModal = false;\n      this.currentNotification = null;\n      this.currentAttachments = [];\n    }\n    /**\n     * Récupère les pièces jointes d'une notification pour le modal de détails\n     */\n    getNotificationAttachmentsForModal(notificationId) {\n      this.currentAttachments = [];\n      if (this.currentNotification?.message?.attachments?.length) {\n        this.currentAttachments = this.currentNotification.message.attachments.map(attachment => ({\n          id: '',\n          url: attachment.url || '',\n          type: this.convertAttachmentTypeToMessageType(attachment.type),\n          name: attachment.name || '',\n          size: attachment.size || 0,\n          duration: 0\n        }));\n      }\n    }\n    /**\n     * Convertit AttachmentType en MessageType\n     */\n    convertAttachmentTypeToMessageType(type) {\n      switch (type) {\n        case 'IMAGE':\n          return MessageType.IMAGE;\n        case 'VIDEO':\n          return MessageType.VIDEO;\n        case 'AUDIO':\n          return MessageType.AUDIO;\n        case 'FILE':\n          return MessageType.FILE;\n        default:\n          return MessageType.FILE;\n      }\n    }\n    /**\n     * Vérifie si un type de fichier est une image\n     */\n    isImage(type) {\n      return type?.startsWith('image/') || false;\n    }\n    /**\n     * Obtient l'icône FontAwesome correspondant au type de fichier\n     * @param type Type MIME du fichier\n     * @returns Classe CSS de l'icône\n     */\n    getFileIcon(type) {\n      if (!type) return 'fas fa-file';\n      if (type.startsWith('image/')) return 'fas fa-file-image';\n      if (type.startsWith('video/')) return 'fas fa-file-video';\n      if (type.startsWith('audio/')) return 'fas fa-file-audio';\n      if (type.startsWith('text/')) return 'fas fa-file-alt';\n      if (type.includes('pdf')) return 'fas fa-file-pdf';\n      if (type.includes('word') || type.includes('document')) return 'fas fa-file-word';\n      if (type.includes('excel') || type.includes('sheet')) return 'fas fa-file-excel';\n      if (type.includes('powerpoint') || type.includes('presentation')) return 'fas fa-file-powerpoint';\n      if (type.includes('zip') || type.includes('compressed')) return 'fas fa-file-archive';\n      return 'fas fa-file';\n    }\n    /**\n     * Obtient le libellé du type de fichier\n     * @param type Type MIME du fichier\n     * @returns Libellé du type de fichier\n     */\n    getFileTypeLabel(type) {\n      if (!type) return 'Fichier';\n      if (type.startsWith('image/')) return 'Image';\n      if (type.startsWith('video/')) return 'Vidéo';\n      if (type.startsWith('audio/')) return 'Audio';\n      if (type.startsWith('text/')) return 'Texte';\n      if (type.includes('pdf')) return 'PDF';\n      if (type.includes('word') || type.includes('document')) return 'Document';\n      if (type.includes('excel') || type.includes('sheet')) return 'Feuille de calcul';\n      if (type.includes('powerpoint') || type.includes('presentation')) return 'Présentation';\n      if (type.includes('zip') || type.includes('compressed')) return 'Archive';\n      return 'Fichier';\n    }\n    /**\n     * Formate la taille du fichier en unités lisibles\n     * @param size Taille en octets\n     * @returns Taille formatée (ex: \"1.5 MB\")\n     */\n    formatFileSize(size) {\n      if (!size) return '';\n      const units = ['B', 'KB', 'MB', 'GB', 'TB'];\n      let i = 0;\n      let formattedSize = size;\n      while (formattedSize >= 1024 && i < units.length - 1) {\n        formattedSize /= 1024;\n        i++;\n      }\n      return `${formattedSize.toFixed(1)} ${units[i]}`;\n    }\n    /**\n     * Ouvre une pièce jointe dans un nouvel onglet\n     * @param url URL de la pièce jointe\n     */\n    openAttachment(url) {\n      if (!url) return;\n      window.open(url, '_blank');\n    }\n    /**\n     * Télécharge une pièce jointe\n     * @param attachment Pièce jointe à télécharger\n     */\n    downloadAttachment(attachment) {\n      if (!attachment?.url) return;\n      const link = document.createElement('a');\n      link.href = attachment.url;\n      link.download = attachment.name || 'attachment';\n      link.target = '_blank';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    }\n    acceptFriendRequest(notification) {\n      this.markAsRead(notification.id);\n    }\n    /**\n     * Supprime une notification et la stocke dans le localStorage\n     * @param notificationId ID de la notification à supprimer\n     */\n    deleteNotification(notificationId) {\n      if (!notificationId) {\n        this.error = new Error('ID de notification invalide');\n        return;\n      }\n      const deletedNotificationIds = this.getDeletedNotificationIds();\n      deletedNotificationIds.add(notificationId);\n      this.saveDeletedNotificationIds(deletedNotificationIds);\n      this.messageService.deleteNotification(notificationId).pipe(takeUntil(this.destroy$)).subscribe({\n        next: result => {\n          if (result && result.success) {\n            if (this.error && this.error.message.includes('suppression')) {\n              this.error = null;\n            }\n          }\n        },\n        error: err => {\n          this.error = err;\n        }\n      });\n    }\n    /**\n     * Supprime toutes les notifications et les stocke dans le localStorage\n     */\n    deleteAllNotifications() {\n      this.notifications$.pipe(take(1)).subscribe(notifications => {\n        const deletedNotificationIds = this.getDeletedNotificationIds();\n        notifications.forEach(notification => {\n          deletedNotificationIds.add(notification.id);\n        });\n        this.saveDeletedNotificationIds(deletedNotificationIds);\n        this.messageService.deleteAllNotifications().pipe(takeUntil(this.destroy$)).subscribe({\n          next: result => {\n            if (result && result.success) {\n              if (this.error && this.error.message.includes('suppression')) {\n                this.error = null;\n              }\n            }\n          },\n          error: err => {\n            this.error = err;\n          }\n        });\n      });\n    }\n    getErrorMessage() {\n      return this.error?.message || 'Unknown error occurred';\n    }\n    /**\n     * Récupère les IDs des notifications supprimées du localStorage\n     * @returns Set contenant les IDs des notifications supprimées\n     */\n    getDeletedNotificationIds() {\n      try {\n        const deletedIdsJson = localStorage.getItem('deletedNotificationIds');\n        if (deletedIdsJson) {\n          return new Set(JSON.parse(deletedIdsJson));\n        }\n        return new Set();\n      } catch (error) {\n        return new Set();\n      }\n    }\n    /**\n     * Sauvegarde les IDs des notifications supprimées dans le localStorage\n     * @param deletedIds Set contenant les IDs des notifications supprimées\n     */\n    saveDeletedNotificationIds(deletedIds) {\n      try {\n        localStorage.setItem('deletedNotificationIds', JSON.stringify(Array.from(deletedIds)));\n      } catch (error) {\n        // Ignore silently\n      }\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    /**\n     * Sélectionne ou désélectionne une notification\n     * @param notificationId ID de la notification\n     * @param event Événement de la case à cocher\n     */\n    toggleSelection(notificationId, event) {\n      event.stopPropagation(); // Empêcher la propagation de l'événement\n      if (this.selectedNotifications.has(notificationId)) {\n        this.selectedNotifications.delete(notificationId);\n      } else {\n        this.selectedNotifications.add(notificationId);\n      }\n      // Mettre à jour l'état de sélection globale\n      this.updateSelectionState();\n      // Afficher ou masquer la barre de sélection\n      this.showSelectionBar = this.selectedNotifications.size > 0;\n    }\n    /**\n     * Sélectionne ou désélectionne toutes les notifications\n     * @param event Événement de la case à cocher\n     */\n    toggleSelectAll(event) {\n      event.stopPropagation(); // Empêcher la propagation de l'événement\n      this.allSelected = !this.allSelected;\n      this.filteredNotifications$.pipe(take(1)).subscribe(notifications => {\n        if (this.allSelected) {\n          // Sélectionner toutes les notifications\n          notifications.forEach(notification => {\n            this.selectedNotifications.add(notification.id);\n          });\n        } else {\n          // Désélectionner toutes les notifications\n          this.selectedNotifications.clear();\n        }\n        // Afficher ou masquer la barre de sélection\n        this.showSelectionBar = this.selectedNotifications.size > 0;\n      });\n    }\n    /**\n     * Met à jour l'état de sélection globale\n     */\n    updateSelectionState() {\n      this.filteredNotifications$.pipe(take(1)).subscribe(notifications => {\n        this.allSelected = notifications.length > 0 && this.selectedNotifications.size === notifications.length;\n      });\n    }\n    /**\n     * Supprime les notifications sélectionnées\n     */\n    deleteSelectedNotifications() {\n      if (this.selectedNotifications.size === 0) return;\n      const selectedIds = Array.from(this.selectedNotifications);\n      this.notifications$.pipe(take(1)).subscribe(notifications => {\n        const updatedNotifications = notifications.filter(notification => !this.selectedNotifications.has(notification.id));\n        this.updateUIWithNotifications(updatedNotifications);\n        this.resetSelection();\n      });\n      this.messageService.deleteMultipleNotifications(selectedIds).pipe(takeUntil(this.destroy$)).subscribe({\n        next: result => {\n          // Success handled silently\n        },\n        error: err => {\n          // Error handled silently\n        }\n      });\n    }\n    /**\n     * Marque les notifications sélectionnées comme lues\n     */\n    markSelectedAsRead() {\n      if (this.selectedNotifications.size === 0) return;\n      const selectedIds = Array.from(this.selectedNotifications);\n      this.notifications$.pipe(take(1)).subscribe(notifications => {\n        const updatedNotifications = notifications.map(notification => this.selectedNotifications.has(notification.id) ? {\n          ...notification,\n          isRead: true,\n          readAt: new Date().toISOString()\n        } : notification);\n        this.updateUIWithNotifications(updatedNotifications);\n        this.resetSelection();\n      });\n      this.messageService.markAsRead(selectedIds).pipe(takeUntil(this.destroy$)).subscribe({\n        next: result => {\n          // Success handled silently\n        },\n        error: err => {\n          // Error handled silently\n        }\n      });\n    }\n    /**\n     * Vérifie si une notification est sélectionnée\n     * @param notificationId ID de la notification\n     * @returns true si la notification est sélectionnée, false sinon\n     */\n    isSelected(notificationId) {\n      return this.selectedNotifications.has(notificationId);\n    }\n    static {\n      this.ɵfac = function NotificationListComponent_Factory(t) {\n        return new (t || NotificationListComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.ThemeService), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: NotificationListComponent,\n        selectors: [[\"app-notification-list\"]],\n        viewQuery: function NotificationListComponent_Query(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵviewQuery(_c0, 5);\n          }\n          if (rf & 2) {\n            let _t;\n            i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.notificationContainer = _t.first);\n          }\n        },\n        hostBindings: function NotificationListComponent_HostBindings(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵlistener(\"scroll\", function NotificationListComponent_scroll_HostBindingHandler($event) {\n              return ctx.onScroll($event.target);\n            });\n          }\n        },\n        decls: 37,\n        vars: 22,\n        consts: [[1, \"futuristic-notifications-container\", \"main-grid-container\"], [1, \"background-elements\", \"background-grid\"], [1, \"futuristic-notifications-card\", \"content-card\", \"relative\", \"z-10\"], [1, \"futuristic-notifications-header\"], [1, \"futuristic-title\"], [1, \"fas\", \"fa-bell\", \"mr-2\"], [\"class\", \"flex space-x-2\", 4, \"ngIf\"], [\"class\", \"flex space-x-2 selection-actions\", 4, \"ngIf\"], [\"class\", \"futuristic-loading-container\", 4, \"ngIf\"], [\"class\", \"futuristic-error-message\", 4, \"ngIf\"], [\"class\", \"futuristic-empty-state\", 4, \"ngIf\"], [\"class\", \"futuristic-notifications-list\", 3, \"scroll\", 4, \"ngIf\"], [1, \"futuristic-modal-overlay\", 3, \"click\"], [1, \"futuristic-modal-container\", 3, \"click\"], [1, \"futuristic-modal-header\"], [1, \"futuristic-modal-title\"], [1, \"fas\", \"fa-paperclip\", \"mr-2\"], [1, \"futuristic-modal-close\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"futuristic-modal-body\"], [\"class\", \"futuristic-attachments-list\", 4, \"ngIf\"], [1, \"fas\", \"fa-info-circle\", \"mr-2\"], [\"class\", \"futuristic-modal-body\", 4, \"ngIf\"], [1, \"flex\", \"space-x-2\"], [\"title\", \"Rafra\\u00EEchir\", 1, \"futuristic-action-button\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\"], [\"class\", \"select-all-checkbox\", 4, \"ngIf\"], [\"title\", \"Filtrer les non lues\", 1, \"futuristic-action-button\", 3, \"click\"], [1, \"fas\", \"fa-filter\"], [1, \"futuristic-action-button\", 3, \"title\", \"click\"], [1, \"fas\", 3, \"ngClass\"], [\"class\", \"futuristic-primary-button\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"futuristic-danger-button\", \"title\", \"Supprimer toutes les notifications\", 3, \"click\", 4, \"ngIf\"], [1, \"select-all-checkbox\"], [1, \"futuristic-checkbox\"], [\"type\", \"checkbox\", 3, \"checked\", \"click\"], [1, \"checkmark\"], [1, \"futuristic-primary-button\", 3, \"click\"], [1, \"fas\", \"fa-check-double\", \"mr-1\"], [\"title\", \"Supprimer toutes les notifications\", 1, \"futuristic-danger-button\", 3, \"click\"], [1, \"fas\", \"fa-trash-alt\", \"mr-1\"], [1, \"flex\", \"space-x-2\", \"selection-actions\"], [1, \"selection-count\"], [1, \"fas\", \"fa-check\", \"mr-1\"], [1, \"futuristic-danger-button\", 3, \"click\"], [1, \"futuristic-cancel-button\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"mr-1\"], [1, \"futuristic-loading-container\"], [1, \"futuristic-loading-circle\"], [1, \"futuristic-loading-text\"], [1, \"futuristic-error-message\"], [1, \"flex\", \"items-center\"], [1, \"fas\", \"fa-exclamation-triangle\", \"futuristic-error-icon\"], [1, \"futuristic-error-title\"], [1, \"futuristic-error-text\"], [1, \"futuristic-retry-button\", \"ml-auto\", 3, \"click\"], [1, \"futuristic-empty-state\"], [1, \"futuristic-empty-icon\"], [1, \"fas\", \"fa-bell-slash\"], [1, \"futuristic-empty-title\"], [1, \"futuristic-empty-text\"], [1, \"futuristic-check-button\", 3, \"click\"], [1, \"futuristic-notifications-list\", 3, \"scroll\"], [\"notificationContainer\", \"\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"futuristic-loading-more\", 4, \"ngIf\"], [1, \"futuristic-notification-card\"], [1, \"notification-checkbox\"], [1, \"notification-avatar\"], [\"alt\", \"Avatar\", \"onerror\", \"this.src='assets/images/default-avatar.png'\", 3, \"src\"], [1, \"notification-main-content\"], [1, \"notification-content\"], [1, \"notification-header\"], [1, \"notification-header-top\"], [1, \"notification-sender\"], [1, \"notification-time\"], [1, \"notification-text-container\"], [1, \"notification-text\"], [\"class\", \"notification-message-preview\", 4, \"ngIf\"], [\"class\", \"notification-attachments-indicator\", 4, \"ngIf\"], [\"class\", \"unread-indicator\", 4, \"ngIf\"], [1, \"notification-actions\"], [\"class\", \"notification-action-button notification-attachment-button\", \"title\", \"Voir les pi\\u00E8ces jointes\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"notification-action-button notification-join-button\", \"title\", \"Rejoindre la conversation\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"title\", \"Voir les d\\u00E9tails (ne marque PAS comme lu automatiquement)\", 1, \"notification-action-button\", \"notification-details-button\", 3, \"click\"], [1, \"fas\", \"fa-info-circle\"], [\"class\", \"notification-action-button notification-read-button\", \"title\", \"Marquer cette notification comme lue\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"Supprimer cette notification\", 1, \"notification-action-button\", \"notification-delete-button\", 3, \"click\"], [1, \"fas\", \"fa-trash-alt\"], [1, \"notification-message-preview\"], [1, \"notification-attachments-indicator\"], [1, \"fas\", \"fa-paperclip\"], [1, \"unread-indicator\"], [\"title\", \"Voir les pi\\u00E8ces jointes\", 1, \"notification-action-button\", \"notification-attachment-button\", 3, \"click\"], [\"title\", \"Rejoindre la conversation\", 1, \"notification-action-button\", \"notification-join-button\", 3, \"disabled\", \"click\"], [\"class\", \"fas fa-comments\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner fa-spin\", 4, \"ngIf\"], [1, \"fas\", \"fa-comments\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [\"title\", \"Marquer cette notification comme lue\", 1, \"notification-action-button\", \"notification-read-button\", 3, \"click\"], [1, \"fas\", \"fa-check\"], [1, \"futuristic-loading-more\"], [1, \"futuristic-loading-circle-small\"], [1, \"futuristic-loading-text-small\"], [1, \"fas\", \"fa-file-alt\"], [1, \"futuristic-attachments-list\"], [\"class\", \"futuristic-attachment-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"futuristic-attachment-item\"], [\"class\", \"futuristic-attachment-preview\", 4, \"ngIf\"], [\"class\", \"futuristic-attachment-icon\", 4, \"ngIf\"], [1, \"futuristic-attachment-info\"], [1, \"futuristic-attachment-name\"], [1, \"futuristic-attachment-meta\"], [1, \"futuristic-attachment-type\"], [\"class\", \"futuristic-attachment-size\", 4, \"ngIf\"], [1, \"futuristic-attachment-actions\"], [\"title\", \"Ouvrir\", 1, \"futuristic-attachment-button\", 3, \"click\"], [1, \"fas\", \"fa-external-link-alt\"], [\"title\", \"T\\u00E9l\\u00E9charger\", 1, \"futuristic-attachment-button\", 3, \"click\"], [1, \"fas\", \"fa-download\"], [1, \"futuristic-attachment-preview\"], [\"alt\", \"Image\", 3, \"src\", \"click\"], [1, \"futuristic-attachment-icon\"], [1, \"futuristic-attachment-size\"], [1, \"notification-detail-section\"], [1, \"notification-detail-title\"], [1, \"fas\", \"fa-user\", \"mr-2\"], [1, \"notification-sender-info\"], [\"alt\", \"Avatar\", \"onerror\", \"this.src='assets/images/default-avatar.png'\", 1, \"notification-sender-avatar\", 3, \"src\"], [1, \"notification-sender-details\"], [1, \"notification-sender-name\"], [1, \"notification-timestamp\"], [1, \"fas\", \"fa-message\", \"mr-2\"], [1, \"notification-content-detail\"], [\"class\", \"notification-message-detail\", 4, \"ngIf\"], [1, \"fas\", \"fa-tag\", \"mr-2\"], [1, \"notification-info-grid\"], [1, \"notification-info-item\"], [1, \"notification-info-label\"], [1, \"notification-info-value\"], [\"class\", \"notification-info-item\", 4, \"ngIf\"], [\"class\", \"notification-info-item\", \"style\", \"\\n              background: rgba(255, 140, 0, 0.1);\\n              border: 1px solid rgba(255, 140, 0, 0.3);\\n            \", 4, \"ngIf\"], [\"class\", \"notification-detail-section\", 4, \"ngIf\"], [1, \"notification-detail-actions\"], [\"class\", \"futuristic-primary-button\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"futuristic-secondary-button\", 3, \"click\", 4, \"ngIf\"], [1, \"fas\", \"fa-trash-alt\", \"mr-2\"], [1, \"notification-message-detail\"], [1, \"notification-info-item\", 2, \"background\", \"rgba(255, 140, 0, 0.1)\", \"border\", \"1px solid rgba(255, 140, 0, 0.3)\"], [1, \"fas\", \"fa-info-circle\", \"mr-1\"], [1, \"notification-info-value\", 2, \"color\", \"#ff8c00\", \"font-style\", \"italic\"], [1, \"notification-attachments-grid\"], [\"class\", \"notification-attachment-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"notification-attachment-item\"], [\"class\", \"notification-attachment-preview\", 4, \"ngIf\"], [\"class\", \"notification-attachment-icon\", 4, \"ngIf\"], [1, \"notification-attachment-info\"], [1, \"notification-attachment-name\"], [1, \"notification-attachment-meta\"], [1, \"notification-attachment-type\"], [\"class\", \"notification-attachment-size\", 4, \"ngIf\"], [1, \"notification-attachment-actions\"], [\"title\", \"Ouvrir\", 1, \"notification-attachment-button\", 3, \"click\"], [\"title\", \"T\\u00E9l\\u00E9charger\", 1, \"notification-attachment-button\", 3, \"click\"], [1, \"notification-attachment-preview\"], [1, \"notification-attachment-icon\"], [1, \"notification-attachment-size\"], [1, \"futuristic-primary-button\", 3, \"disabled\", \"click\"], [\"class\", \"fas fa-comments mr-2\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner fa-spin mr-2\", 4, \"ngIf\"], [1, \"fas\", \"fa-comments\", \"mr-2\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"mr-2\"], [1, \"futuristic-secondary-button\", 3, \"click\"], [1, \"fas\", \"fa-check\", \"mr-2\"]],\n        template: function NotificationListComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵpipe(1, \"async\");\n            i0.ɵɵelement(2, \"div\", 1);\n            i0.ɵɵelementStart(3, \"div\", 2)(4, \"div\", 3)(5, \"h2\", 4);\n            i0.ɵɵelement(6, \"i\", 5);\n            i0.ɵɵtext(7, \" Notifications \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(8, NotificationListComponent_div_8_Template, 13, 15, \"div\", 6);\n            i0.ɵɵtemplate(9, NotificationListComponent_div_9_Template, 12, 1, \"div\", 7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(10, NotificationListComponent_div_10_Template, 4, 0, \"div\", 8);\n            i0.ɵɵtemplate(11, NotificationListComponent_div_11_Template, 10, 1, \"div\", 9);\n            i0.ɵɵtemplate(12, NotificationListComponent_div_12_Template, 9, 0, \"div\", 10);\n            i0.ɵɵpipe(13, \"async\");\n            i0.ɵɵtemplate(14, NotificationListComponent_div_14_Template, 5, 4, \"div\", 11);\n            i0.ɵɵpipe(15, \"async\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"div\", 12);\n            i0.ɵɵlistener(\"click\", function NotificationListComponent_Template_div_click_16_listener() {\n              return ctx.closeAttachmentsModal();\n            });\n            i0.ɵɵelementStart(17, \"div\", 13);\n            i0.ɵɵlistener(\"click\", function NotificationListComponent_Template_div_click_17_listener($event) {\n              return $event.stopPropagation();\n            });\n            i0.ɵɵelementStart(18, \"div\", 14)(19, \"h3\", 15);\n            i0.ɵɵelement(20, \"i\", 16);\n            i0.ɵɵtext(21, \" Pi\\u00E8ces jointes \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"button\", 17);\n            i0.ɵɵlistener(\"click\", function NotificationListComponent_Template_button_click_22_listener() {\n              return ctx.closeAttachmentsModal();\n            });\n            i0.ɵɵelement(23, \"i\", 18);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(24, \"div\", 19);\n            i0.ɵɵtemplate(25, NotificationListComponent_div_25_Template, 4, 0, \"div\", 8);\n            i0.ɵɵtemplate(26, NotificationListComponent_div_26_Template, 7, 0, \"div\", 10);\n            i0.ɵɵtemplate(27, NotificationListComponent_div_27_Template, 2, 1, \"div\", 20);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(28, \"div\", 12);\n            i0.ɵɵlistener(\"click\", function NotificationListComponent_Template_div_click_28_listener() {\n              return ctx.closeNotificationDetailsModal();\n            });\n            i0.ɵɵelementStart(29, \"div\", 13);\n            i0.ɵɵlistener(\"click\", function NotificationListComponent_Template_div_click_29_listener($event) {\n              return $event.stopPropagation();\n            });\n            i0.ɵɵelementStart(30, \"div\", 14)(31, \"h3\", 15);\n            i0.ɵɵelement(32, \"i\", 21);\n            i0.ɵɵtext(33, \" D\\u00E9tails de la notification \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"button\", 17);\n            i0.ɵɵlistener(\"click\", function NotificationListComponent_Template_button_click_34_listener() {\n              return ctx.closeNotificationDetailsModal();\n            });\n            i0.ɵɵelement(35, \"i\", 18);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(36, NotificationListComponent_div_36_Template, 44, 19, \"div\", 22);\n            i0.ɵɵelementEnd()();\n          }\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"dark\", i0.ɵɵpipeBind1(1, 16, ctx.isDarkMode$));\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngIf\", !ctx.showSelectionBar);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.showSelectionBar);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && !i0.ɵɵpipeBind1(13, 18, ctx.hasNotifications()));\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && i0.ɵɵpipeBind1(15, 20, ctx.hasNotifications()));\n            i0.ɵɵadvance(2);\n            i0.ɵɵstyleProp(\"display\", ctx.showAttachmentsModal ? \"flex\" : \"none\");\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", ctx.loadingAttachments);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loadingAttachments && ctx.currentAttachments.length === 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loadingAttachments && ctx.currentAttachments.length > 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵstyleProp(\"display\", ctx.showNotificationDetailsModal ? \"flex\" : \"none\");\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngIf\", ctx.currentNotification);\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i4.AsyncPipe, i4.DatePipe]\n      });\n    }\n  }\n  return NotificationListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}