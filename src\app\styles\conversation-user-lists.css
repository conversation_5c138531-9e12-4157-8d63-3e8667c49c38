/* ============================================================================
   LISTES DE CONVERSATIONS ET UTILISATEURS
   ============================================================================ */

.conversation-list,
.user-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 1rem;
  max-height: 100%;
  overflow-y: auto;
}

.conversation-item,
.user-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 0.5rem;
  background: var(--color-surface, #1f2937);
  border: 1px solid var(--color-border, #374151);
  cursor: pointer;
  transition: all 0.2s ease;
}

.conversation-item:hover,
.user-item:hover {
  background: var(--color-primary, #3b82f6);
  transform: translateX(4px);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.conversation-item.active {
  background: var(--gradient-primary, linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%));
  border-color: var(--color-primary, #3b82f6);
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.4);
}

/* Avatar */
.avatar {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--color-border, #374151);
  position: relative;
}

.avatar-container {
  position: relative;
  display: inline-block;
}

.online-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 0.75rem;
  height: 0.75rem;
  background: #10b981;
  border: 2px solid var(--color-background, #111827);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Content */
.conversation-content,
.user-content {
  flex: 1;
  min-width: 0;
}

.conversation-title,
.user-name {
  font-weight: 600;
  color: var(--color-text, #ffffff);
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-preview,
.user-email {
  font-size: 0.875rem;
  color: var(--color-text-secondary, #9ca3af);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Metadata */
.conversation-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.conversation-time {
  font-size: 0.75rem;
  color: var(--color-text-secondary, #9ca3af);
}

.unread-badge {
  background: var(--gradient-primary, linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%));
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  min-width: 1.25rem;
  text-align: center;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
  animation: badgePulse 2s infinite;
}

@keyframes badgePulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Group conversations */
.group-avatar {
  background: var(--gradient-secondary, linear-gradient(135deg, #6366f1 0%, #4f46e5 100%));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 1.25rem;
}

.group-indicator {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 1rem;
  height: 1rem;
  background: var(--color-accent, #8b5cf6);
  border: 2px solid var(--color-background, #111827);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.5rem;
  color: white;
}

/* Empty states */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: var(--color-text-secondary, #9ca3af);
}

.empty-state-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--color-text, #ffffff);
}

.empty-state-description {
  font-size: 0.875rem;
  max-width: 20rem;
}

/* Loading states */
.loading-skeleton {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 0.25rem;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.skeleton-avatar {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
}

.skeleton-text {
  height: 1rem;
  margin-bottom: 0.5rem;
}

.skeleton-text.short {
  width: 60%;
}

.skeleton-text.long {
  width: 80%;
}

/* Responsive */
@media (max-width: 768px) {
  .conversation-item,
  .user-item {
    padding: 0.5rem;
    gap: 0.5rem;
  }
  
  .avatar {
    width: 2.5rem;
    height: 2.5rem;
  }
  
  .conversation-title,
  .user-name {
    font-size: 0.875rem;
  }
  
  .conversation-preview,
  .user-email {
    font-size: 0.75rem;
  }
}
