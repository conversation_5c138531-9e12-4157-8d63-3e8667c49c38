{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class HighlightPresencePipe {\n  transform(value, searchTerm) {\n    if (!value || !searchTerm) {\n      return value;\n    }\n    const regex = new RegExp(`(${searchTerm})`, 'gi');\n    return value.replace(regex, '<mark class=\"bg-yellow-200 text-yellow-800\">$1</mark>');\n  }\n  static {\n    this.ɵfac = function HighlightPresencePipe_Factory(t) {\n      return new (t || HighlightPresencePipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"highlightPresence\",\n      type: HighlightPresencePipe,\n      pure: true\n    });\n  }\n}", "map": {"version": 3, "names": ["HighlightPresencePipe", "transform", "value", "searchTerm", "regex", "RegExp", "replace", "pure"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\shared\\pipes\\highlight-presence.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\n\n@Pipe({\n  name: 'highlightPresence'\n})\nexport class HighlightPresencePipe implements PipeTransform {\n\n  transform(value: string, searchTerm?: string): string {\n    if (!value || !searchTerm) {\n      return value;\n    }\n\n    const regex = new RegExp(`(${searchTerm})`, 'gi');\n    return value.replace(regex, '<mark class=\"bg-yellow-200 text-yellow-800\">$1</mark>');\n  }\n\n}\n"], "mappings": ";AAKA,OAAM,MAAOA,qBAAqB;EAEhCC,SAASA,CAACC,KAAa,EAAEC,UAAmB;IAC1C,IAAI,CAACD,KAAK,IAAI,CAACC,UAAU,EAAE;MACzB,OAAOD,KAAK;;IAGd,MAAME,KAAK,GAAG,IAAIC,MAAM,CAAC,IAAIF,UAAU,GAAG,EAAE,IAAI,CAAC;IACjD,OAAOD,KAAK,CAACI,OAAO,CAACF,KAAK,EAAE,uDAAuD,CAAC;EACtF;;;uBATWJ,qBAAqB;IAAA;EAAA;;;;YAArBA,qBAAqB;MAAAO,IAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}