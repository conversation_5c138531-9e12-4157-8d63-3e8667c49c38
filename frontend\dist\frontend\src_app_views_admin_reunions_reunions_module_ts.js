"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([["src_app_views_admin_reunions_reunions_module_ts"],{

/***/ 3493:
/*!*********************************************************************************!*\
  !*** ./src/app/views/admin/reunions/reunion-detail/reunion-detail.component.ts ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ReunionDetailComponent: () => (/* binding */ ReunionDetailComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _app_services_reunion_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @app/services/reunion.service */ 78);
/* harmony import */ var _angular_platform_browser__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/platform-browser */ 436);
/* harmony import */ var _app_services_toast_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @app/services/toast.service */ 8397);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _shared_pipes_highlight_presence_pipe__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../shared/pipes/highlight-presence.pipe */ 876);







function ReunionDetailComponent_div_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "div", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function ReunionDetailComponent_div_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", ctx_r1.error, " ");
  }
}
function ReunionDetailComponent_div_7_li_33_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "li", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const participant_r6 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate2"](" ", participant_r6.username, " (", participant_r6.email, ") ");
  }
}
function ReunionDetailComponent_div_7_div_44_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 20)(1, "h2", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](2, "Lieu:");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](3, "div", 27);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](4, "svg", 22);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](5, "path", 31)(6, "path", 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](7, "span", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](ctx_r4.reunion.lieu);
  }
}
function ReunionDetailComponent_div_7_div_45_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 20)(1, "h2", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](2, "Lien Visio:");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](3, "a", 33);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](4, "svg", 34);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](5, "path", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](6, " Rejoindre la r\u00E9union ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("href", ctx_r5.reunion.lienVisio, _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵsanitizeUrl"]);
  }
}
function ReunionDetailComponent_div_7_Template(rf, ctx) {
  if (rf & 1) {
    const _r8 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 10)(1, "div", 11)(2, "div")(3, "h1", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](5, "p", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](6, "highlightPresence");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](7, "div", 14)(8, "button", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function ReunionDetailComponent_div_7_Template_button_click_8_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r8);
      const ctx_r7 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r7.editReunion());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](9, "svg", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](10, "path", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](11, " Modifier ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](12, "button", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function ReunionDetailComponent_div_7_Template_button_click_12_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r8);
      const ctx_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r9.deleteReunion());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](13, "svg", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](14, "path", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](15, " Supprimer ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](16, "div", 20)(17, "div", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](18, "svg", 22);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](19, "path", 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](20, "span", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](21);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](22, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](23, "div", 25)(24, "h2", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](25, "Cr\u00E9ateur:");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](26, "div", 27)(27, "span", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](28);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](29, "div", 20)(30, "h2", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](31, "Participants:");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](32, "ul", 28);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](33, ReunionDetailComponent_div_7_li_33_Template, 2, 2, "li", 29);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](34, "div", 20)(35, "h2", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](36, "Planning:");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](37, "div", 24)(38, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](39);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](40, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](41);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](42, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](43, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](44, ReunionDetailComponent_div_7_div_44_Template, 9, 1, "div", 30);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](45, ReunionDetailComponent_div_7_div_45_Template, 7, 1, "div", 30);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](ctx_r2.reunion.titre);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("innerHTML", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind1"](6, 13, ctx_r2.reunion.description), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵsanitizeHtml"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](16);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate3"](" ", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind2"](22, 15, ctx_r2.reunion.date, "fullDate"), ", ", ctx_r2.reunion.heureDebut, " - ", ctx_r2.reunion.heureFin, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate2"]("", ctx_r2.reunion.createur == null ? null : ctx_r2.reunion.createur.username, " (", ctx_r2.reunion.createur == null ? null : ctx_r2.reunion.createur.email, ")");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngForOf", ctx_r2.reunion.participants);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](ctx_r2.reunion.planning == null ? null : ctx_r2.reunion.planning.titre);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate2"]("Du ", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind2"](42, 18, ctx_r2.reunion.planning == null ? null : ctx_r2.reunion.planning.dateDebut, "mediumDate"), " au ", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind2"](43, 21, ctx_r2.reunion.planning == null ? null : ctx_r2.reunion.planning.dateFin, "mediumDate"), "");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx_r2.reunion.lieu);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx_r2.reunion.lienVisio);
  }
}
class ReunionDetailComponent {
  constructor(route, router, reunionService, sanitizer, toastService) {
    this.route = route;
    this.router = router;
    this.reunionService = reunionService;
    this.sanitizer = sanitizer;
    this.toastService = toastService;
    this.reunion = null;
    this.loading = true;
    this.error = null;
  }
  ngOnInit() {
    this.loadReunionDetails();
  }
  loadReunionDetails() {
    const id = this.route.snapshot.paramMap.get('id');
    if (!id) {
      this.error = 'ID de réunion non fourni';
      this.loading = false;
      return;
    }
    this.reunionService.getReunionById(id).subscribe({
      next: response => {
        this.reunion = response.reunion;
        this.loading = false;
      },
      error: err => {
        this.error = err.error?.message || 'Erreur lors du chargement';
        this.loading = false;
        console.error('Erreur:', err);
      }
    });
  }
  formatDescription(description) {
    if (!description) return this.sanitizer.bypassSecurityTrustHtml('');
    // Recherche la chaîne "(presence obligatoire)" (insensible à la casse) et la remplace par une version en rouge
    const formattedText = description.replace(/\(presence obligatoire\)/gi, '<span class="text-red-600 font-semibold">(presence obligatoire)</span>');
    // Sanitize le HTML pour éviter les problèmes de sécurité
    return this.sanitizer.bypassSecurityTrustHtml(formattedText);
  }
  editReunion() {
    if (this.reunion) {
      this.router.navigate(['/reunions/edit', this.reunion._id]);
    }
  }
  /**
   * Supprime la réunion après confirmation
   */
  deleteReunion() {
    if (!this.reunion) return;
    if (confirm('Êtes-vous sûr de vouloir supprimer cette réunion ? Cette action est irréversible.')) {
      this.reunionService.deleteReunion(this.reunion._id).subscribe({
        next: response => {
          console.log('Réunion supprimée avec succès:', response);
          // Afficher le toast de succès
          this.toastService.success('Réunion supprimée', 'La réunion a été supprimée avec succès');
          // Rediriger vers la liste des réunions
          this.router.navigate(['/reunions']);
        },
        error: error => {
          console.error('Erreur lors de la suppression:', error);
          // Gestion spécifique des erreurs d'autorisation
          if (error.status === 403) {
            this.toastService.accessDenied('supprimer cette réunion', error.status);
          } else if (error.status === 401) {
            this.toastService.error('Non autorisé', 'Vous devez être connecté pour supprimer une réunion');
          } else {
            const errorMessage = error.error?.message || 'Erreur lors de la suppression de la réunion';
            this.toastService.error('Erreur de suppression', errorMessage, 8000);
          }
        }
      });
    }
  }
  static {
    this.ɵfac = function ReunionDetailComponent_Factory(t) {
      return new (t || ReunionDetailComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_4__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_4__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_app_services_reunion_service__WEBPACK_IMPORTED_MODULE_0__.ReunionService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_platform_browser__WEBPACK_IMPORTED_MODULE_5__.DomSanitizer), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_app_services_toast_service__WEBPACK_IMPORTED_MODULE_1__.ToastService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineComponent"]({
      type: ReunionDetailComponent,
      selectors: [["app-reunion-detail"]],
      decls: 8,
      vars: 3,
      consts: [[1, "container", "mx-auto", "px-4", "py-6"], [1, "mb-4", "flex", "items-center", "text-purple-600", "hover:text-purple-800", 3, "click"], ["xmlns", "http://www.w3.org/2000/svg", "viewBox", "0 0 20 20", "fill", "currentColor", 1, "h-5", "w-5", "mr-1"], ["fill-rule", "evenodd", "d", "M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z", "clip-rule", "evenodd"], ["class", "text-center py-8", 4, "ngIf"], ["class", "bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4", 4, "ngIf"], ["class", "bg-white rounded-lg shadow-md p-6", 4, "ngIf"], [1, "text-center", "py-8"], [1, "animate-spin", "rounded-full", "h-12", "w-12", "border-b-2", "border-purple-600", "mx-auto"], [1, "bg-red-100", "border", "border-red-400", "text-red-700", "px-4", "py-3", "rounded", "mb-4"], [1, "bg-white", "rounded-lg", "shadow-md", "p-6"], [1, "flex", "justify-between", "items-start", "mb-4"], [1, "text-2xl", "font-bold", "text-gray-800"], [1, "mt-1", 3, "innerHTML"], [1, "flex", "space-x-2"], [1, "px-4", "py-2", "bg-blue-500", "text-white", "rounded", "hover:bg-blue-600", "transition-colors", "flex", "items-center", 3, "click"], ["fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "h-4", "w-4", "mr-1"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"], [1, "px-4", "py-2", "bg-red-500", "text-white", "rounded", "hover:bg-red-600", "transition-colors", "flex", "items-center", 3, "click"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"], [1, "mb-6"], [1, "flex", "items-center", "mb-2"], ["fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "h-5", "w-5", "text-gray-500", "mr-2"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"], [1, "text-gray-700"], [1, "mb-4"], [1, "text-lg", "font-semibold", "mb-2", "text-gray-800"], [1, "flex", "items-center"], [1, "list-disc", "pl-5"], ["class", "text-gray-700", 4, "ngFor", "ngForOf"], ["class", "mb-6", 4, "ngIf"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M15 11a3 3 0 11-6 0 3 3 0 016 0z"], ["target", "_blank", 1, "text-blue-600", "hover:underline", "flex", "items-center", 3, "href"], ["fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "h-5", "w-5", "mr-2"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"]],
      template: function ReunionDetailComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 0)(1, "button", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function ReunionDetailComponent_Template_button_click_1_listener() {
            return ctx.router.navigate(["/reunions"]);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnamespaceSVG"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](2, "svg", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](3, "path", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](4, " Retour aux r\u00E9unions ");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](5, ReunionDetailComponent_div_5_Template, 2, 0, "div", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](6, ReunionDetailComponent_div_6_Template, 2, 1, "div", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](7, ReunionDetailComponent_div_7_Template, 46, 24, "div", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.loading);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.error);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx.loading && ctx.reunion);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_6__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_6__.NgIf, _angular_common__WEBPACK_IMPORTED_MODULE_6__.DatePipe, _shared_pipes_highlight_presence_pipe__WEBPACK_IMPORTED_MODULE_2__.HighlightPresencePipe],
      styles: ["/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJyZXVuaW9uLWRldGFpbC5jb21wb25lbnQuY3NzIn0= */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vcmV1bmlvbnMvcmV1bmlvbi1kZXRhaWwvcmV1bmlvbi1kZXRhaWwuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0FBQ0EsNEtBQTRLIiwic291cmNlUm9vdCI6IiJ9 */"]
    });
  }
}

/***/ }),

/***/ 6023:
/*!*****************************************************************************!*\
  !*** ./src/app/views/admin/reunions/reunion-edit/reunion-edit.component.ts ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ReunionEditComponent: () => (/* binding */ ReunionEditComponent)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _app_services_reunion_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @app/services/reunion.service */ 78);
/* harmony import */ var _app_services_data_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @app/services/data.service */ 8490);
/* harmony import */ var _app_services_planning_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @app/services/planning.service */ 6543);
/* harmony import */ var _app_services_toast_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @app/services/toast.service */ 8397);
/* harmony import */ var _app_services_authuser_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @app/services/authuser.service */ 9271);
/* harmony import */ var _app_services_role_service__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @app/services/role.service */ 4866);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/common */ 316);











function ReunionEditComponent_div_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 62);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", ctx_r0.error.message || "Une erreur est survenue", " ");
  }
}
function ReunionEditComponent_div_15_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 63);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](1, "i", 64);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](2, " Le titre est obligatoire ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
}
function ReunionEditComponent_div_31_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 63);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](1, "i", 64);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](2, " La date est obligatoire ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
}
function ReunionEditComponent_div_37_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 63);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](1, "i", 64);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](2, " L'heure de d\u00E9but est obligatoire ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
}
function ReunionEditComponent_div_43_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 63);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](1, "i", 64);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](2, " L'heure de fin est obligatoire ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
}
function ReunionEditComponent_div_62_div_10_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 72);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](1, "i", 55);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", ctx_r10.currentReunionPlanning.description, " ");
  }
}
function ReunionEditComponent_div_62_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 65)(1, "div", 66)(2, "span", 67);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](3, "i", 68);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](5, "span", 69);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](6, "i", 70);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpipe"](8, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpipe"](9, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](10, ReunionEditComponent_div_62_div_10_Template, 3, 1, "div", 71);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", ctx_r5.currentReunionPlanning.titre, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate2"](" ", _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpipeBind2"](8, 4, ctx_r5.currentReunionPlanning.dateDebut, "dd/MM/yyyy"), " - ", _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵpipeBind2"](9, 7, ctx_r5.currentReunionPlanning.dateFin, "dd/MM/yyyy"), " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx_r5.currentReunionPlanning.description);
  }
}
function ReunionEditComponent_option_66_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "option", 73);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const planning_r11 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("value", planning_r11._id);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate"](planning_r11.titre);
  }
}
function ReunionEditComponent_ng_container_79_option_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "option", 75);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](1, "i", 76);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const user_r13 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("value", user_r13._id);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"]("", user_r13.username, " ");
  }
}
function ReunionEditComponent_ng_container_79_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](1, ReunionEditComponent_ng_container_79_option_1_Template, 3, 2, "option", 74);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const ctx_r7 = _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngForOf", ctx_r7.users);
  }
}
function ReunionEditComponent_i_88_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "i", 77);
  }
}
function ReunionEditComponent_i_89_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](0, "i", 78);
  }
}
class ReunionEditComponent {
  constructor(fb, route, router, reunionService, userService, planningService, toastService, authService, roleService) {
    this.fb = fb;
    this.route = route;
    this.router = router;
    this.reunionService = reunionService;
    this.userService = userService;
    this.planningService = planningService;
    this.toastService = toastService;
    this.authService = authService;
    this.roleService = roleService;
    this.error = null;
    this.isSubmitting = false;
    this.users = [];
    this.plannings = [];
    this.currentReunionPlanning = null;
    this.isAdmin = false;
  }
  ngOnInit() {
    this.reunionId = this.route.snapshot.paramMap.get('id');
    this.checkUserRole();
    this.initForm();
    this.fetchUsers();
    this.fetchPlannings();
    this.loadReunion();
  }
  checkUserRole() {
    this.isAdmin = this.roleService.isAdmin();
    console.log('🔍 Utilisateur admin:', this.isAdmin);
  }
  initForm() {
    this.reunionForm = this.fb.group({
      titre: ['', _angular_forms__WEBPACK_IMPORTED_MODULE_7__.Validators.required],
      description: [''],
      date: ['', _angular_forms__WEBPACK_IMPORTED_MODULE_7__.Validators.required],
      heureDebut: ['', _angular_forms__WEBPACK_IMPORTED_MODULE_7__.Validators.required],
      heureFin: ['', _angular_forms__WEBPACK_IMPORTED_MODULE_7__.Validators.required],
      lieu: [''],
      lienVisio: [''],
      planning: ['', _angular_forms__WEBPACK_IMPORTED_MODULE_7__.Validators.required],
      participants: [[]]
    });
  }
  fetchUsers() {
    this.userService.getAllUsers().subscribe(users => {
      this.users = users;
    });
  }
  fetchPlannings() {
    const userId = this.authService.getCurrentUserId();
    if (!userId) return;
    // Si admin, récupérer tous les plannings, sinon seulement ceux de l'utilisateur
    const planningsObservable = this.isAdmin ? this.planningService.getAllPlanningsAdmin() : this.planningService.getPlanningsByUser(userId);
    planningsObservable.subscribe({
      next: response => {
        // Adapter la réponse selon l'endpoint utilisé
        if (this.isAdmin) {
          this.plannings = response.data || [];
          console.log('🔍 Tous les plannings (admin) récupérés:', this.plannings);
        } else {
          this.plannings = response.plannings || [];
          console.log('🔍 Plannings utilisateur récupérés:', this.plannings);
        }
      },
      error: err => {
        console.error('❌ Erreur chargement plannings:', err);
        this.toastService.error('Erreur', 'Impossible de récupérer les plannings');
      }
    });
  }
  loadReunion() {
    this.reunionService.getReunionById(this.reunionId).subscribe({
      next: reunion => {
        // Stocker le planning actuel de la réunion
        this.currentReunionPlanning = reunion.reunion.planning;
        this.reunionForm.patchValue({
          titre: reunion.reunion.titre,
          description: reunion.reunion.description,
          date: reunion.reunion.date?.split('T')[0],
          heureDebut: reunion.reunion.heureDebut,
          heureFin: reunion.reunion.heureFin,
          lieu: reunion.reunion.lieu,
          lienVisio: reunion.reunion.lienVisio,
          planning: reunion.reunion.planning?.id || reunion.reunion.planning?._id,
          participants: reunion.reunion.participants?.map(p => p._id)
        });
        // Désactiver le champ planning en mode édition
        this.reunionForm.get('planning')?.disable();
        console.log('🔍 Réunion chargée:', reunion.reunion);
        console.log('🔍 Planning actuel:', this.currentReunionPlanning);
      },
      error: err => {
        console.error('Erreur lors du chargement de la réunion:', err);
        if (err.status === 403) {
          this.toastService.accessDenied('accéder à cette réunion', err.status);
        } else if (err.status === 404) {
          this.toastService.error('Réunion introuvable', 'La réunion demandée n\'existe pas ou a été supprimée');
        } else {
          const errorMessage = err.error?.message || 'Erreur lors du chargement de la réunion';
          this.toastService.error('Erreur de chargement', errorMessage);
        }
      }
    });
  }
  onSubmit() {
    if (this.reunionForm.invalid) {
      this.toastService.warning('Formulaire invalide', 'Veuillez corriger les erreurs avant de soumettre le formulaire');
      return;
    }
    // Validation de la date par rapport au planning
    if (!this.validateDateInPlanningRange()) {
      return;
    }
    this.isSubmitting = true;
    const reunion = this.reunionForm.value;
    // Inclure le planning même s'il est désactivé
    if (this.currentReunionPlanning) {
      reunion.planning = this.currentReunionPlanning._id || this.currentReunionPlanning.id;
    }
    console.log('🔍 Données de la réunion à mettre à jour:', reunion);
    this.reunionService.updateReunion(this.reunionId, reunion).subscribe({
      next: () => {
        this.isSubmitting = false;
        this.toastService.success('Réunion mise à jour', 'La réunion a été modifiée avec succès');
        this.router.navigate(['/reunions']);
      },
      error: err => {
        this.isSubmitting = false;
        console.error('Erreur lors de la mise à jour de la réunion:', err);
        if (err.status === 403) {
          this.toastService.accessDenied('modifier cette réunion', err.status);
        } else if (err.status === 401) {
          this.toastService.error('Non autorisé', 'Vous devez être connecté pour effectuer cette action');
        } else {
          const errorMessage = err.error?.message || 'Erreur lors de la mise à jour de la réunion';
          this.toastService.error('Erreur de mise à jour', errorMessage, 8000);
        }
      }
    });
  }
  goReunion() {
    this.router.navigate(['/reunions']);
  }
  /**
   * Valide que la date de la réunion est dans l'intervalle du planning sélectionné
   */
  validateDateInPlanningRange() {
    const formValue = this.reunionForm.value;
    const reunionDate = formValue.date;
    const planningId = formValue.planning;
    if (!reunionDate || !planningId) {
      return true; // Si pas de date ou planning, laisser la validation backend gérer
    }
    // Trouver le planning sélectionné
    const selectedPlanning = this.plannings.find(p => p._id === planningId);
    if (!selectedPlanning) {
      console.warn('⚠️ Planning non trouvé dans la liste locale, tentative de récupération depuis le serveur');
      // Si le planning n'est pas trouvé dans la liste locale, essayer de le récupérer
      // Cela peut arriver si l'utilisateur modifie une réunion d'un planning dont il n'est que participant
      this.planningService.getPlanningById(planningId).subscribe({
        next: response => {
          const planning = response.planning;
          if (planning) {
            // Ajouter le planning à la liste locale pour éviter de futures requêtes
            this.plannings.push(planning);
            console.log('✅ Planning récupéré et ajouté à la liste locale:', planning);
          }
        },
        error: err => {
          console.error('❌ Erreur lors de la récupération du planning:', err);
          this.toastService.error('Planning introuvable', 'Le planning sélectionné n\'existe pas ou vous n\'avez pas les permissions pour y accéder');
        }
      });
      // Pour cette validation, on retourne true et on laisse le backend gérer
      return true;
    }
    // Convertir les dates pour comparaison
    const reunionDateObj = new Date(reunionDate);
    const planningDateDebut = new Date(selectedPlanning.dateDebut);
    const planningDateFin = new Date(selectedPlanning.dateFin);
    // Comparer seulement les dates (sans les heures)
    reunionDateObj.setHours(0, 0, 0, 0);
    planningDateDebut.setHours(0, 0, 0, 0);
    planningDateFin.setHours(0, 0, 0, 0);
    if (reunionDateObj < planningDateDebut || reunionDateObj > planningDateFin) {
      this.toastService.error('Date invalide', `La date de la réunion doit être comprise entre le ${planningDateDebut.toLocaleDateString('fr-FR')} et le ${planningDateFin.toLocaleDateString('fr-FR')} (période du planning "${selectedPlanning.titre}")`, 10000);
      return false;
    }
    return true;
  }
  static {
    this.ɵfac = function ReunionEditComponent_Factory(t) {
      return new (t || ReunionEditComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_forms__WEBPACK_IMPORTED_MODULE_7__.FormBuilder), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_8__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_8__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_app_services_reunion_service__WEBPACK_IMPORTED_MODULE_0__.ReunionService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_app_services_data_service__WEBPACK_IMPORTED_MODULE_1__.DataService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_app_services_planning_service__WEBPACK_IMPORTED_MODULE_2__.PlanningService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_app_services_toast_service__WEBPACK_IMPORTED_MODULE_3__.ToastService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_app_services_authuser_service__WEBPACK_IMPORTED_MODULE_4__.AuthuserService), _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdirectiveInject"](_app_services_role_service__WEBPACK_IMPORTED_MODULE_5__.RoleService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineComponent"]({
      type: ReunionEditComponent,
      selectors: [["app-reunion-edit"]],
      decls: 91,
      vars: 13,
      consts: [[1, "container", "mx-auto", "px-4", "py-6", "max-w-3xl"], [1, "bg-gradient-to-r", "from-purple-600", "to-indigo-600", "rounded-t-lg", "p-6", "text-white", "mb-0"], [1, "text-2xl", "font-bold", "flex", "items-center"], [1, "fas", "fa-edit", "mr-3", "text-purple-200"], [1, "text-purple-100", "mt-2"], [1, "bg-white", "rounded-b-lg", "shadow-lg", "p-6", "border-t-0", 3, "formGroup", "ngSubmit"], ["class", "bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4", 4, "ngIf"], [1, "grid", "grid-cols-1", "gap-6"], [1, "relative"], ["for", "titre", 1, "block", "text-sm", "font-medium", "text-purple-700", "mb-2"], [1, "fas", "fa-tag", "mr-2", "text-purple-500"], ["id", "titre", "type", "text", "formControlName", "titre", 1, "mt-1", "block", "w-full", "rounded-lg", "border-2", "border-purple-200", "shadow-sm", "focus:border-purple-500", "focus:ring-purple-500", "focus:ring-2", "transition-all", "duration-200", "px-4", "py-3"], ["class", "text-red-500 text-sm mt-2 flex items-center", 4, "ngIf"], ["for", "description", 1, "block", "text-sm", "font-medium", "text-indigo-700", "mb-2"], [1, "fas", "fa-align-left", "mr-2", "text-indigo-500"], ["id", "description", "formControlName", "description", "rows", "3", "placeholder", "D\u00E9crivez votre r\u00E9union...", 1, "mt-1", "block", "w-full", "rounded-lg", "border-2", "border-indigo-200", "shadow-sm", "focus:border-indigo-500", "focus:ring-indigo-500", "focus:ring-2", "transition-all", "duration-200", "px-4", "py-3"], [1, "bg-gradient-to-r", "from-blue-50", "to-cyan-50", "p-4", "rounded-lg", "border", "border-blue-200"], [1, "text-lg", "font-semibold", "text-blue-800", "mb-4", "flex", "items-center"], [1, "fas", "fa-calendar-clock", "mr-2", "text-blue-600"], [1, "grid", "grid-cols-1", "md:grid-cols-3", "gap-6"], ["for", "date", 1, "block", "text-sm", "font-medium", "text-blue-700", "mb-2"], [1, "fas", "fa-calendar", "mr-2", "text-blue-500"], ["id", "date", "type", "date", "formControlName", "date", 1, "mt-1", "block", "w-full", "rounded-lg", "border-2", "border-blue-200", "shadow-sm", "focus:border-blue-500", "focus:ring-blue-500", "focus:ring-2", "transition-all", "duration-200", "px-4", "py-3"], ["for", "heureDebut", 1, "block", "text-sm", "font-medium", "text-green-700", "mb-2"], [1, "fas", "fa-play", "mr-2", "text-green-500"], ["id", "heureDebut", "type", "time", "formControlName", "heureDebut", 1, "mt-1", "block", "w-full", "rounded-lg", "border-2", "border-green-200", "shadow-sm", "focus:border-green-500", "focus:ring-green-500", "focus:ring-2", "transition-all", "duration-200", "px-4", "py-3"], ["for", "heureFin", 1, "block", "text-sm", "font-medium", "text-red-700", "mb-2"], [1, "fas", "fa-stop", "mr-2", "text-red-500"], ["id", "heureFin", "type", "time", "formControlName", "heureFin", 1, "mt-1", "block", "w-full", "rounded-lg", "border-2", "border-red-200", "shadow-sm", "focus:border-red-500", "focus:ring-red-500", "focus:ring-2", "transition-all", "duration-200", "px-4", "py-3"], [1, "bg-gradient-to-r", "from-orange-50", "to-yellow-50", "p-4", "rounded-lg", "border", "border-orange-200"], [1, "text-lg", "font-semibold", "text-orange-800", "mb-4", "flex", "items-center"], [1, "fas", "fa-map-marker-alt", "mr-2", "text-orange-600"], [1, "grid", "grid-cols-1", "md:grid-cols-2", "gap-6"], ["for", "lieu", 1, "block", "text-sm", "font-medium", "text-orange-700", "mb-2"], [1, "fas", "fa-map-marker-alt", "mr-2", "text-orange-500"], ["id", "lieu", "type", "text", "formControlName", "lieu", "placeholder", "Salle 101, Bureau A, Google Meet...", 1, "mt-1", "block", "w-full", "rounded-lg", "border-2", "border-orange-200", "shadow-sm", "focus:border-orange-500", "focus:ring-orange-500", "focus:ring-2", "transition-all", "duration-200", "px-4", "py-3"], ["for", "lienVisio", 1, "block", "text-sm", "font-medium", "text-cyan-700", "mb-2"], [1, "fas", "fa-video", "mr-2", "text-cyan-500"], ["id", "lienVisio", "type", "url", "formControlName", "lienVisio", "placeholder", "https://meet.google.com/...", 1, "mt-1", "block", "w-full", "rounded-lg", "border-2", "border-cyan-200", "shadow-sm", "focus:border-cyan-500", "focus:ring-cyan-500", "focus:ring-2", "transition-all", "duration-200", "px-4", "py-3"], ["for", "planning", 1, "block", "text-sm", "font-medium", "text-gray-700"], ["class", "mt-1 block w-full px-4 py-3 bg-gradient-to-r from-purple-50 to-indigo-50 border-2 border-purple-200 rounded-lg shadow-sm", 4, "ngIf"], ["id", "planning", "formControlName", "planning", 1, "hidden"], ["value", ""], [3, "value", 4, "ngFor", "ngForOf"], [1, "text-sm", "text-purple-600", "mt-3", "bg-purple-50", "p-3", "rounded-lg", "border", "border-purple-200", "flex", "items-center"], [1, "fas", "fa-lock", "mr-2", "text-purple-500"], [1, "font-medium"], [1, "bg-gradient-to-r", "from-emerald-50", "to-teal-50", "p-4", "rounded-lg", "border", "border-emerald-200"], [1, "text-lg", "font-semibold", "text-emerald-800", "mb-4", "flex", "items-center"], [1, "fas", "fa-users", "mr-2", "text-emerald-600"], [1, "block", "text-sm", "font-medium", "text-emerald-700", "mb-2"], [1, "fas", "fa-user-friends", "mr-2", "text-emerald-500"], ["formControlName", "participants", "multiple", "", 1, "mt-1", "block", "w-full", "px-4", "py-3", "border-2", "border-emerald-200", "rounded-lg", "shadow-sm", "focus:ring-emerald-500", "focus:border-emerald-500", "focus:ring-2", "transition-all", "duration-200", "text-sm", "min-h-[120px]"], [4, "ngIf"], [1, "text-xs", "text-emerald-600", "mt-2"], [1, "fas", "fa-info-circle", "mr-1"], [1, "mt-8", "flex", "justify-end", "space-x-4", "bg-gray-50", "p-4", "rounded-lg", "border-t", "border-gray-200"], ["type", "button", 1, "px-6", "py-3", "border-2", "border-gray-300", "rounded-lg", "text-sm", "font-medium", "text-gray-700", "hover:bg-gray-100", "hover:border-gray-400", "transition-all", "duration-200", "flex", "items-center", 3, "click"], [1, "fas", "fa-times", "mr-2"], ["type", "submit", 1, "px-6", "py-3", "rounded-lg", "text-sm", "font-medium", "text-white", "bg-gradient-to-r", "from-purple-600", "to-indigo-600", "hover:from-purple-700", "hover:to-indigo-700", "disabled:opacity-50", "disabled:cursor-not-allowed", "transition-all", "duration-200", "flex", "items-center", "shadow-lg", 3, "disabled"], ["class", "fas fa-save mr-2", 4, "ngIf"], ["class", "fas fa-spinner fa-spin mr-2", 4, "ngIf"], [1, "bg-red-100", "border", "border-red-400", "text-red-700", "px-4", "py-3", "rounded", "mb-4"], [1, "text-red-500", "text-sm", "mt-2", "flex", "items-center"], [1, "fas", "fa-exclamation-circle", "mr-1"], [1, "mt-1", "block", "w-full", "px-4", "py-3", "bg-gradient-to-r", "from-purple-50", "to-indigo-50", "border-2", "border-purple-200", "rounded-lg", "shadow-sm"], [1, "flex", "items-center", "justify-between"], [1, "font-semibold", "text-purple-800", "text-lg"], [1, "fas", "fa-calendar-alt", "mr-2", "text-purple-600"], [1, "text-sm", "font-medium", "text-red-600", "bg-red-50", "px-2", "py-1", "rounded-full", "border", "border-red-200"], [1, "fas", "fa-clock", "mr-1"], ["class", "text-sm text-indigo-700 mt-2 bg-indigo-50 p-2 rounded border-l-4 border-indigo-300", 4, "ngIf"], [1, "text-sm", "text-indigo-700", "mt-2", "bg-indigo-50", "p-2", "rounded", "border-l-4", "border-indigo-300"], [3, "value"], ["class", "py-2", 3, "value", 4, "ngFor", "ngForOf"], [1, "py-2", 3, "value"], [1, "fas", "fa-user", "mr-2"], [1, "fas", "fa-save", "mr-2"], [1, "fas", "fa-spinner", "fa-spin", "mr-2"]],
      template: function ReunionEditComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "h1", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](3, "i", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](4, " Modifier la R\u00E9union ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](5, "p", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](6, "Modifiez les d\u00E9tails de votre r\u00E9union");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](7, "form", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("ngSubmit", function ReunionEditComponent_Template_form_ngSubmit_7_listener() {
            return ctx.onSubmit();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](8, ReunionEditComponent_div_8_Template, 2, 1, "div", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](9, "div", 7)(10, "div", 8)(11, "label", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](12, "i", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](13, " Titre * ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](14, "input", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](15, ReunionEditComponent_div_15_Template, 3, 0, "div", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](16, "div", 8)(17, "label", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](18, "i", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](19, " Description ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](20, "textarea", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](21, "div", 16)(22, "h3", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](23, "i", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](24, " Planification ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](25, "div", 19)(26, "div")(27, "label", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](28, "i", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](29, " Date * ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](30, "input", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](31, ReunionEditComponent_div_31_Template, 3, 0, "div", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](32, "div")(33, "label", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](34, "i", 24);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](35, " Heure de d\u00E9but * ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](36, "input", 25);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](37, ReunionEditComponent_div_37_Template, 3, 0, "div", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](38, "div")(39, "label", 26);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](40, "i", 27);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](41, " Heure de fin * ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](42, "input", 28);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](43, ReunionEditComponent_div_43_Template, 3, 0, "div", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](44, "div", 29)(45, "h3", 30);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](46, "i", 31);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](47, " Localisation ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](48, "div", 32)(49, "div")(50, "label", 33);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](51, "i", 34);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](52, " Lieu / Salle ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](53, "input", 35);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](54, "div")(55, "label", 36);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](56, "i", 37);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](57, " Lien Visio ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](58, "input", 38);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](59, "div")(60, "label", 39);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](61, "Planning *");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](62, ReunionEditComponent_div_62_Template, 11, 10, "div", 40);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](63, "select", 41)(64, "option", 42);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](65, "S\u00E9lectionnez un planning");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](66, ReunionEditComponent_option_66_Template, 2, 2, "option", 43);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](67, "div", 44);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](68, "i", 45);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](69, "span", 46);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](70, "Le planning ne peut pas \u00EAtre modifi\u00E9 lors de l'\u00E9dition d'une r\u00E9union");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](71, "div", 47)(72, "h3", 48);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](73, "i", 49);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](74, " Participants ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](75, "label", 50);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](76, "i", 51);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](77, " S\u00E9lectionnez les participants ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](78, "select", 52);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](79, ReunionEditComponent_ng_container_79_Template, 2, 1, "ng-container", 53);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](80, "p", 54);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](81, "i", 55);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](82, " Maintenez Ctrl (ou Cmd) pour s\u00E9lectionner plusieurs participants ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](83, "div", 56)(84, "button", 57);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵlistener"]("click", function ReunionEditComponent_Template_button_click_84_listener() {
            return ctx.goReunion();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelement"](85, "i", 58);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](86, " Annuler ");
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementStart"](87, "button", 59);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](88, ReunionEditComponent_i_88_Template, 1, 0, "i", 60);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtemplate"](89, ReunionEditComponent_i_89_Template, 1, 0, "i", 61);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtext"](90);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵelementEnd"]()()()();
        }
        if (rf & 2) {
          let tmp_2_0;
          let tmp_3_0;
          let tmp_4_0;
          let tmp_5_0;
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](7);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("formGroup", ctx.reunionForm);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.error);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](7);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ((tmp_2_0 = ctx.reunionForm.get("titre")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.reunionForm.get("titre")) == null ? null : tmp_2_0.touched));
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](16);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ((tmp_3_0 = ctx.reunionForm.get("date")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.reunionForm.get("date")) == null ? null : tmp_3_0.touched));
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ((tmp_4_0 = ctx.reunionForm.get("heureDebut")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.reunionForm.get("heureDebut")) == null ? null : tmp_4_0.touched));
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ((tmp_5_0 = ctx.reunionForm.get("heureFin")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.reunionForm.get("heureFin")) == null ? null : tmp_5_0.touched));
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](19);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.currentReunionPlanning);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngForOf", ctx.plannings);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](13);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.users);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](8);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("disabled", ctx.reunionForm.invalid || ctx.isSubmitting);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", !ctx.isSubmitting);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵproperty"]("ngIf", ctx.isSubmitting);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵtextInterpolate1"](" ", ctx.isSubmitting ? "Enregistrement..." : "Enregistrer les modifications", " ");
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_9__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_9__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_7__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_7__.NgSelectOption, _angular_forms__WEBPACK_IMPORTED_MODULE_7__["ɵNgSelectMultipleOption"], _angular_forms__WEBPACK_IMPORTED_MODULE_7__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_7__.SelectControlValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_7__.SelectMultipleControlValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_7__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_7__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_7__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_7__.FormControlName, _angular_common__WEBPACK_IMPORTED_MODULE_9__.DatePipe],
      styles: ["/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJyZXVuaW9uLWVkaXQuY29tcG9uZW50LmNzcyJ9 */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vcmV1bmlvbnMvcmV1bmlvbi1lZGl0L3JldW5pb24tZWRpdC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSx3S0FBd0siLCJzb3VyY2VSb290IjoiIn0= */"]
    });
  }
}

/***/ }),

/***/ 1135:
/*!*****************************************************************************!*\
  !*** ./src/app/views/admin/reunions/reunion-form/reunion-form.component.ts ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ReunionFormComponent: () => (/* binding */ ReunionFormComponent)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rxjs/operators */ 2575);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rxjs/operators */ 1817);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var src_app_services_reunion_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/services/reunion.service */ 78);
/* harmony import */ var src_app_services_planning_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/services/planning.service */ 6543);
/* harmony import */ var _app_services_data_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @app/services/data.service */ 8490);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var src_app_services_authuser_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/app/services/authuser.service */ 9271);
/* harmony import */ var src_app_services_toast_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/app/services/toast.service */ 8397);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/common */ 316);











function ReunionFormComponent_div_8_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 61);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", ctx_r0.error.message || "Une erreur est survenue", " ");
  }
}
function ReunionFormComponent_div_9_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 62);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", ctx_r1.successMessage, " ");
  }
}
function ReunionFormComponent_div_16_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 63);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](1, "i", 64);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](2, " Le titre est obligatoire ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
}
function ReunionFormComponent_div_32_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 63);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](1, "i", 64);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](2, " La date est obligatoire ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
}
function ReunionFormComponent_div_38_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 63);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](1, "i", 64);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](2, " L'heure de d\u00E9but est obligatoire ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
}
function ReunionFormComponent_div_44_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 63);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](1, "i", 64);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](2, " L'heure de fin est obligatoire ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
}
function ReunionFormComponent_span_59_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "span", 65);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](1, "svg", 66);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](2, "circle", 67)(3, "path", 68);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](4, " V\u00E9rification... ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
}
function ReunionFormComponent_div_61_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 69);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](1, "i", 70);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r7 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", ctx_r7.lienVisioError, " ");
  }
}
function ReunionFormComponent_div_62_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 71);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](1, "i", 72);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](2, " Lien disponible ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
}
function ReunionFormComponent_div_63_option_10_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "option", 81);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const planning_r17 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("value", planning_r17._id);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate"](planning_r17.titre);
  }
}
function ReunionFormComponent_div_63_div_11_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 63);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](1, "i", 64);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](2, " Le planning est obligatoire ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
}
function ReunionFormComponent_div_63_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 73)(1, "h3", 74);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](2, "i", 75);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3, " Planning ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](4, "label", 76);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](5, "i", 77);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](6, " S\u00E9lectionnez un planning * ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](7, "select", 78)(8, "option", 79);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](9, "Choisissez un planning...");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](10, ReunionFormComponent_div_63_option_10_Template, 2, 2, "option", 80);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](11, ReunionFormComponent_div_63_div_11_Template, 3, 0, "div", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
    let tmp_1_0;
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](10);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", ctx_r9.plannings);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ((tmp_1_0 = ctx_r9.reunionForm.get("planning")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r9.reunionForm.get("planning")) == null ? null : tmp_1_0.touched));
  }
}
function ReunionFormComponent_div_64_div_13_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 90);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](1, "i", 53);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r18 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", ctx_r18.selectedPlanning.description, " ");
  }
}
function ReunionFormComponent_div_64_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 82)(1, "h3", 83);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](2, "i", 84);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](3, " Planning s\u00E9lectionn\u00E9 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](4, "div", 85)(5, "span", 86);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](6, "i", 75);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](8, "span", 87);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](9, "i", 88);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](10);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipe"](11, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipe"](12, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](13, ReunionFormComponent_div_64_div_13_Template, 3, 1, "div", 89);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", ctx_r10.selectedPlanning.titre, " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate2"](" ", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipeBind2"](11, 4, ctx_r10.selectedPlanning.dateDebut, "dd/MM/yyyy"), " - ", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipeBind2"](12, 7, ctx_r10.selectedPlanning.dateFin, "dd/MM/yyyy"), " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx_r10.selectedPlanning.description);
  }
}
function ReunionFormComponent_ng_container_73_option_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "option", 92);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](1, "i", 93);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const user_r21 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("value", user_r21._id);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"]("", user_r21.username, " ");
  }
}
function ReunionFormComponent_ng_container_73_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](1, ReunionFormComponent_ng_container_73_option_1_Template, 3, 2, "option", 91);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const users_r19 = ctx.ngIf;
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngForOf", users_r19);
  }
}
function ReunionFormComponent_i_83_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](0, "i", 94);
  }
}
function ReunionFormComponent_i_84_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](0, "i", 95);
  }
}
function ReunionFormComponent_i_85_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](0, "i", 96);
  }
}
class ReunionFormComponent {
  constructor(fb, reunionService, planningService, userService, route, router, authService, toastService) {
    this.fb = fb;
    this.reunionService = reunionService;
    this.planningService = planningService;
    this.userService = userService;
    this.route = route;
    this.router = router;
    this.authService = authService;
    this.toastService = toastService;
    this.plannings = [];
    this.loading = true;
    this.isSubmitting = false;
    this.error = null;
    this.successMessage = null;
    this.isEditMode = false;
    this.currentReunionId = null;
    this.planningIdFromUrl = null;
    this.selectedPlanning = null;
    this.lienVisioError = null;
    this.isCheckingLienVisio = false;
    this.reunionForm = this.fb.group({
      titre: ['', _angular_forms__WEBPACK_IMPORTED_MODULE_6__.Validators.required],
      description: [''],
      date: ['', _angular_forms__WEBPACK_IMPORTED_MODULE_6__.Validators.required],
      heureDebut: ['', _angular_forms__WEBPACK_IMPORTED_MODULE_6__.Validators.required],
      heureFin: ['', _angular_forms__WEBPACK_IMPORTED_MODULE_6__.Validators.required],
      lieu: [''],
      lienVisio: [''],
      planning: ['', _angular_forms__WEBPACK_IMPORTED_MODULE_6__.Validators.required],
      participants: [[]]
    });
    this.users$ = this.userService.getAllUsers();
  }
  ngOnInit() {
    this.loadPlannings();
    this.checkEditMode();
    this.checkPlanningParam();
    this.setupLienVisioValidation();
  }
  checkEditMode() {
    const reunionId = this.route.snapshot.paramMap.get('id');
    if (reunionId) {
      this.isEditMode = true;
      this.currentReunionId = reunionId;
      this.loadReunion(reunionId);
    }
  }
  loadPlannings() {
    const userId = this.authService.getCurrentUserId();
    if (!userId) return;
    this.planningService.getPlanningsByUser(userId).subscribe({
      next: response => {
        this.plannings = response.plannings || [];
        console.log('🔍 Plannings chargés:', this.plannings);
        console.log('🔍 Premier planning:', this.plannings[0]);
      },
      error: err => {
        this.error = err;
        console.error('❌ Erreur chargement plannings:', err);
      }
    });
  }
  loadReunion(id) {
    this.reunionService.getReunionById(id).subscribe({
      next: reunion => {
        this.reunionForm.patchValue({
          titre: reunion.titre,
          description: reunion.description,
          dateDebut: this.formatDateForInput(reunion.dateDebut),
          dateFin: this.formatDateForInput(reunion.dateFin),
          lieu: reunion.lieu,
          lienVisio: reunion.lienVisio,
          planningId: reunion.planningId,
          participants: reunion.participants
        });
        this.loading = false;
      },
      error: err => {
        this.error = err;
        this.loading = false;
      }
    });
  }
  formatDateForInput(date) {
    return new Date(date).toISOString().slice(0, 16); // yyyy-MM-ddTHH:mm
  }

  checkPlanningParam() {
    const planningId = this.route.snapshot.queryParamMap.get('planningId');
    if (planningId) {
      this.planningIdFromUrl = planningId;
      // Si un ID de planning est fourni dans les paramètres de requête, le sélectionner automatiquement
      this.reunionForm.patchValue({
        planning: planningId
      });
      // Récupérer les détails du planning pour l'affichage
      this.planningService.getPlanningById(planningId).subscribe({
        next: response => {
          this.selectedPlanning = response.planning;
          // Ajouter le planning à la liste locale pour la validation
          if (this.selectedPlanning && !this.plannings.find(p => p._id === planningId)) {
            this.plannings.push(this.selectedPlanning);
            console.log('✅ Planning ajouté à la liste locale pour validation:', this.selectedPlanning);
          }
        },
        error: err => {
          console.error('Erreur lors de la récupération du planning:', err);
          this.toastService.error('Planning introuvable', 'Le planning sélectionné n\'existe pas ou vous n\'avez pas les permissions pour y accéder');
        }
      });
    }
  }
  onSubmit() {
    if (this.reunionForm.invalid || !this.canSubmit()) {
      this.toastService.warning('Formulaire invalide', 'Veuillez corriger les erreurs avant de soumettre le formulaire');
      return;
    }
    // Validation de la date par rapport au planning
    if (!this.validateDateInPlanningRange()) {
      return;
    }
    this.isSubmitting = true;
    this.error = null;
    this.successMessage = null;
    const formValue = this.reunionForm.value;
    const date = formValue.date; // already in yyyy-MM-dd format from input[type=date]
    const heureDebut = formValue.heureDebut; // already in HH:mm format from input[type=time]
    const heureFin = formValue.heureFin;
    const reunionData = {
      titre: formValue.titre,
      description: formValue.description,
      date: date,
      heureDebut: heureDebut,
      heureFin: heureFin,
      lieu: formValue.lieu,
      lienVisio: formValue.lienVisio,
      planning: formValue.planning,
      participants: formValue.participants || []
    };
    console.log('🔍 Données de la réunion à envoyer:', reunionData);
    console.log('🔍 Planning ID sélectionné:', formValue.planning);
    console.log('🔍 Type du planning ID:', typeof formValue.planning);
    console.log('🔍 Plannings disponibles:', this.plannings);
    this.reunionService.createReunion(reunionData).subscribe({
      next: () => {
        this.isSubmitting = false;
        this.toastService.success('Réunion créée', 'La réunion a été créée avec succès');
        // Réinitialiser le formulaire pour permettre la création d'une nouvelle réunion
        this.resetForm();
        // Redirection immédiate
        this.router.navigate(['/reunions']);
      },
      error: err => {
        this.isSubmitting = false;
        console.error('Erreur lors de la création de la réunion:', err);
        if (err.status === 403) {
          this.toastService.accessDenied('créer une réunion', err.status);
        } else if (err.status === 401) {
          this.toastService.error('Non autorisé', 'Vous devez être connecté pour créer une réunion');
        } else {
          const errorMessage = err.error?.message || 'Erreur lors de la création de la réunion';
          this.toastService.error('Erreur de création', errorMessage, 8000);
        }
      }
    });
  }
  resetForm() {
    // Reset the form to its initial state
    this.reunionForm.reset({
      titre: '',
      description: '',
      date: '',
      heureDebut: '',
      heureFin: '',
      lieu: '',
      lienVisio: '',
      planning: '',
      participants: []
    });
    // Mark the form as pristine and untouched to reset validation states
    this.reunionForm.markAsPristine();
    this.reunionForm.markAsUntouched();
  }
  goReunion() {
    this.router.navigate(['/reunions']);
  }
  /**
   * Configure la validation en temps réel du lien visio avec debounce
   */
  setupLienVisioValidation() {
    this.reunionForm.get('lienVisio')?.valueChanges.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.debounceTime)(500),
    // Attendre 500ms après la dernière saisie
    (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_8__.distinctUntilChanged)() // Ne déclencher que si la valeur a changé
    ).subscribe(value => {
      if (value && value.trim() !== '') {
        this.checkLienVisioUniqueness(value.trim());
      } else {
        this.lienVisioError = null;
      }
    });
  }
  /**
   * Vérifie l'unicité du lien visio
   * @param lienVisio Le lien à vérifier
   */
  checkLienVisioUniqueness(lienVisio) {
    if (!lienVisio || lienVisio.trim() === '') {
      this.lienVisioError = null;
      return;
    }
    this.isCheckingLienVisio = true;
    this.lienVisioError = null;
    // Utiliser la nouvelle route dédiée pour vérifier l'unicité
    this.reunionService.checkLienVisioUniqueness(lienVisio, this.currentReunionId || undefined).subscribe({
      next: response => {
        this.isCheckingLienVisio = false;
        if (response.success && !response.isUnique) {
          this.lienVisioError = `Ce lien est déjà utilisé par la réunion "${response.conflictWith?.titre}"`;
        } else {
          this.lienVisioError = null;
        }
      },
      error: error => {
        this.isCheckingLienVisio = false;
        console.error('Erreur lors de la vérification du lien visio:', error);
        this.lienVisioError = 'Erreur lors de la vérification du lien';
      }
    });
  }
  /**
   * Vérifie si le formulaire peut être soumis
   */
  canSubmit() {
    return this.reunionForm.valid && !this.lienVisioError && !this.isCheckingLienVisio;
  }
  /**
   * Valide que la date de la réunion est dans l'intervalle du planning sélectionné
   */
  validateDateInPlanningRange() {
    const formValue = this.reunionForm.value;
    const reunionDate = formValue.date;
    const planningId = formValue.planning;
    if (!reunionDate || !planningId) {
      return true; // Si pas de date ou planning, laisser la validation backend gérer
    }
    // Chercher d'abord dans la liste locale, puis dans selectedPlanning
    let selectedPlanning = this.plannings.find(p => p._id === planningId);
    if (!selectedPlanning && this.selectedPlanning && this.selectedPlanning._id === planningId) {
      selectedPlanning = this.selectedPlanning;
    }
    if (!selectedPlanning) {
      console.warn('⚠️ Planning non trouvé pour validation:', planningId);
      console.log('📋 Plannings disponibles:', this.plannings.map(p => ({
        id: p._id,
        titre: p.titre
      })));
      console.log('🎯 Selected planning:', this.selectedPlanning);
      // Ne pas bloquer si le planning n'est pas trouvé - laisser le backend valider
      return true;
    }
    // Convertir les dates pour comparaison
    const reunionDateObj = new Date(reunionDate);
    const planningDateDebut = new Date(selectedPlanning.dateDebut);
    const planningDateFin = new Date(selectedPlanning.dateFin);
    // Comparer seulement les dates (sans les heures)
    reunionDateObj.setHours(0, 0, 0, 0);
    planningDateDebut.setHours(0, 0, 0, 0);
    planningDateFin.setHours(0, 0, 0, 0);
    if (reunionDateObj < planningDateDebut || reunionDateObj > planningDateFin) {
      this.toastService.error('Date invalide', `La date de la réunion doit être comprise entre le ${planningDateDebut.toLocaleDateString('fr-FR')} et le ${planningDateFin.toLocaleDateString('fr-FR')} (période du planning "${selectedPlanning.titre}")`, 10000);
      return false;
    }
    return true;
  }
  static {
    this.ɵfac = function ReunionFormComponent_Factory(t) {
      return new (t || ReunionFormComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_forms__WEBPACK_IMPORTED_MODULE_6__.FormBuilder), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](src_app_services_reunion_service__WEBPACK_IMPORTED_MODULE_0__.ReunionService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](src_app_services_planning_service__WEBPACK_IMPORTED_MODULE_1__.PlanningService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_app_services_data_service__WEBPACK_IMPORTED_MODULE_2__.DataService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_9__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_9__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](src_app_services_authuser_service__WEBPACK_IMPORTED_MODULE_3__.AuthuserService), _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdirectiveInject"](src_app_services_toast_service__WEBPACK_IMPORTED_MODULE_4__.ToastService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineComponent"]({
      type: ReunionFormComponent,
      selectors: [["app-reunion-form"]],
      decls: 87,
      vars: 24,
      consts: [[1, "container", "mx-auto", "px-4", "py-6", "max-w-3xl"], [1, "bg-gradient-to-r", "from-purple-600", "to-indigo-600", "rounded-t-lg", "p-6", "text-white", "mb-0"], [1, "text-2xl", "font-bold", "flex", "items-center"], [1, "fas", "fa-plus-circle", "mr-3", "text-purple-200"], [1, "text-purple-100", "mt-2"], [1, "bg-white", "rounded-b-lg", "shadow-lg", "p-6", "border-t-0", 3, "formGroup", "ngSubmit"], ["class", "bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4", 4, "ngIf"], ["class", "bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4", 4, "ngIf"], [1, "grid", "grid-cols-1", "gap-6"], [1, "relative"], ["for", "titre", 1, "block", "text-sm", "font-medium", "text-purple-700", "mb-2"], [1, "fas", "fa-tag", "mr-2", "text-purple-500"], ["id", "titre", "type", "text", "formControlName", "titre", "placeholder", "Nom de votre r\u00E9union...", 1, "mt-1", "block", "w-full", "rounded-lg", "border-2", "border-purple-200", "shadow-sm", "focus:border-purple-500", "focus:ring-purple-500", "focus:ring-2", "transition-all", "duration-200", "px-4", "py-3"], ["class", "text-red-500 text-sm mt-2 flex items-center", 4, "ngIf"], ["for", "description", 1, "block", "text-sm", "font-medium", "text-indigo-700", "mb-2"], [1, "fas", "fa-align-left", "mr-2", "text-indigo-500"], ["id", "description", "formControlName", "description", "rows", "3", "placeholder", "D\u00E9crivez votre r\u00E9union...", 1, "mt-1", "block", "w-full", "rounded-lg", "border-2", "border-indigo-200", "shadow-sm", "focus:border-indigo-500", "focus:ring-indigo-500", "focus:ring-2", "transition-all", "duration-200", "px-4", "py-3"], [1, "bg-gradient-to-r", "from-blue-50", "to-cyan-50", "p-4", "rounded-lg", "border", "border-blue-200"], [1, "text-lg", "font-semibold", "text-blue-800", "mb-4", "flex", "items-center"], [1, "fas", "fa-calendar-clock", "mr-2", "text-blue-600"], [1, "grid", "grid-cols-1", "md:grid-cols-3", "gap-6"], ["for", "date", 1, "block", "text-sm", "font-medium", "text-blue-700", "mb-2"], [1, "fas", "fa-calendar", "mr-2", "text-blue-500"], ["id", "date", "type", "date", "formControlName", "date", 1, "mt-1", "block", "w-full", "rounded-lg", "border-2", "border-blue-200", "shadow-sm", "focus:border-blue-500", "focus:ring-blue-500", "focus:ring-2", "transition-all", "duration-200", "px-4", "py-3"], ["for", "heureDebut", 1, "block", "text-sm", "font-medium", "text-green-700", "mb-2"], [1, "fas", "fa-play", "mr-2", "text-green-500"], ["id", "heureDebut", "type", "time", "formControlName", "heureDebut", 1, "mt-1", "block", "w-full", "rounded-lg", "border-2", "border-green-200", "shadow-sm", "focus:border-green-500", "focus:ring-green-500", "focus:ring-2", "transition-all", "duration-200", "px-4", "py-3"], ["for", "heureFin", 1, "block", "text-sm", "font-medium", "text-red-700", "mb-2"], [1, "fas", "fa-stop", "mr-2", "text-red-500"], ["id", "heureFin", "type", "time", "formControlName", "heureFin", 1, "mt-1", "block", "w-full", "rounded-lg", "border-2", "border-red-200", "shadow-sm", "focus:border-red-500", "focus:ring-red-500", "focus:ring-2", "transition-all", "duration-200", "px-4", "py-3"], [1, "bg-gradient-to-r", "from-orange-50", "to-yellow-50", "p-4", "rounded-lg", "border", "border-orange-200"], [1, "text-lg", "font-semibold", "text-orange-800", "mb-4", "flex", "items-center"], [1, "fas", "fa-map-marker-alt", "mr-2", "text-orange-600"], [1, "grid", "grid-cols-1", "md:grid-cols-2", "gap-6"], ["for", "lieu", 1, "block", "text-sm", "font-medium", "text-orange-700", "mb-2"], [1, "fas", "fa-map-marker-alt", "mr-2", "text-orange-500"], ["id", "lieu", "type", "text", "formControlName", "lieu", "placeholder", "Salle 101, Bureau A, Google Meet...", 1, "mt-1", "block", "w-full", "rounded-lg", "border-2", "border-orange-200", "shadow-sm", "focus:border-orange-500", "focus:ring-orange-500", "focus:ring-2", "transition-all", "duration-200", "px-4", "py-3"], ["for", "lienVisio", 1, "block", "text-sm", "font-medium", "text-cyan-700", "mb-2"], [1, "fas", "fa-video", "mr-2", "text-cyan-500"], ["class", "ml-2 text-xs text-cyan-500", 4, "ngIf"], ["id", "lienVisio", "type", "url", "formControlName", "lienVisio", "placeholder", "https://meet.google.com/..."], ["class", "text-red-500 text-sm mt-2 flex items-center bg-red-50 p-2 rounded border border-red-200", 4, "ngIf"], ["class", "text-green-600 text-sm mt-2 flex items-center bg-green-50 p-2 rounded border border-green-200", 4, "ngIf"], ["class", "bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg border border-purple-200", 4, "ngIf"], ["class", "bg-gradient-to-r from-purple-50 to-indigo-50 p-4 rounded-lg border-2 border-purple-200 shadow-sm", 4, "ngIf"], [1, "bg-gradient-to-r", "from-emerald-50", "to-teal-50", "p-4", "rounded-lg", "border", "border-emerald-200"], [1, "text-lg", "font-semibold", "text-emerald-800", "mb-4", "flex", "items-center"], [1, "fas", "fa-users", "mr-2", "text-emerald-600"], [1, "block", "text-sm", "font-medium", "text-emerald-700", "mb-2"], [1, "fas", "fa-user-friends", "mr-2", "text-emerald-500"], ["formControlName", "participants", "multiple", "", 1, "mt-1", "block", "w-full", "px-4", "py-3", "border-2", "border-emerald-200", "rounded-lg", "shadow-sm", "focus:ring-emerald-500", "focus:border-emerald-500", "focus:ring-2", "transition-all", "duration-200", "text-sm", "min-h-[120px]"], [4, "ngIf"], [1, "text-xs", "text-emerald-600", "mt-2"], [1, "fas", "fa-info-circle", "mr-1"], [1, "mt-8", "flex", "justify-end", "space-x-4", "bg-gray-50", "p-4", "rounded-lg", "border-t", "border-gray-200"], ["type", "button", 1, "px-6", "py-3", "border-2", "border-gray-300", "rounded-lg", "text-sm", "font-medium", "text-gray-700", "hover:bg-gray-100", "hover:border-gray-400", "transition-all", "duration-200", "flex", "items-center", 3, "click"], [1, "fas", "fa-times", "mr-2"], ["type", "submit", 1, "px-6", "py-3", "rounded-lg", "text-sm", "font-medium", "text-white", "bg-gradient-to-r", "from-purple-600", "to-indigo-600", "hover:from-purple-700", "hover:to-indigo-700", "disabled:opacity-50", "disabled:cursor-not-allowed", "transition-all", "duration-200", "flex", "items-center", "shadow-lg", 3, "disabled"], ["class", "fas fa-save mr-2", 4, "ngIf"], ["class", "fas fa-spinner fa-spin mr-2", 4, "ngIf"], ["class", "fas fa-search mr-2", 4, "ngIf"], [1, "bg-red-100", "border", "border-red-400", "text-red-700", "px-4", "py-3", "rounded", "mb-4"], [1, "bg-green-100", "border", "border-green-400", "text-green-700", "px-4", "py-3", "rounded", "mb-4"], [1, "text-red-500", "text-sm", "mt-2", "flex", "items-center"], [1, "fas", "fa-exclamation-circle", "mr-1"], [1, "ml-2", "text-xs", "text-cyan-500"], ["fill", "none", "viewBox", "0 0 24 24", 1, "inline", "h-3", "w-3", "animate-spin"], ["cx", "12", "cy", "12", "r", "10", "stroke", "currentColor", "stroke-width", "4", 1, "opacity-25"], ["fill", "currentColor", "d", "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z", 1, "opacity-75"], [1, "text-red-500", "text-sm", "mt-2", "flex", "items-center", "bg-red-50", "p-2", "rounded", "border", "border-red-200"], [1, "fas", "fa-exclamation-triangle", "mr-2"], [1, "text-green-600", "text-sm", "mt-2", "flex", "items-center", "bg-green-50", "p-2", "rounded", "border", "border-green-200"], [1, "fas", "fa-check-circle", "mr-2"], [1, "bg-gradient-to-r", "from-purple-50", "to-pink-50", "p-4", "rounded-lg", "border", "border-purple-200"], [1, "text-lg", "font-semibold", "text-purple-800", "mb-4", "flex", "items-center"], [1, "fas", "fa-calendar-alt", "mr-2", "text-purple-600"], ["for", "planning", 1, "block", "text-sm", "font-medium", "text-purple-700", "mb-2"], [1, "fas", "fa-list", "mr-2", "text-purple-500"], ["id", "planning", "formControlName", "planning", 1, "mt-1", "block", "w-full", "rounded-lg", "border-2", "border-purple-200", "shadow-sm", "focus:border-purple-500", "focus:ring-purple-500", "focus:ring-2", "transition-all", "duration-200", "px-4", "py-3"], ["value", ""], [3, "value", 4, "ngFor", "ngForOf"], [3, "value"], [1, "bg-gradient-to-r", "from-purple-50", "to-indigo-50", "p-4", "rounded-lg", "border-2", "border-purple-200", "shadow-sm"], [1, "text-lg", "font-semibold", "text-purple-800", "mb-3", "flex", "items-center"], [1, "fas", "fa-calendar-check", "mr-2", "text-purple-600"], [1, "flex", "items-center", "justify-between"], [1, "font-semibold", "text-purple-800", "text-lg"], [1, "text-sm", "font-medium", "text-red-600", "bg-red-50", "px-2", "py-1", "rounded-full", "border", "border-red-200"], [1, "fas", "fa-clock", "mr-1"], ["class", "text-sm text-indigo-700 mt-2 bg-indigo-50 p-2 rounded border-l-4 border-indigo-300", 4, "ngIf"], [1, "text-sm", "text-indigo-700", "mt-2", "bg-indigo-50", "p-2", "rounded", "border-l-4", "border-indigo-300"], ["class", "py-2", 3, "value", 4, "ngFor", "ngForOf"], [1, "py-2", 3, "value"], [1, "fas", "fa-user", "mr-2"], [1, "fas", "fa-save", "mr-2"], [1, "fas", "fa-spinner", "fa-spin", "mr-2"], [1, "fas", "fa-search", "mr-2"]],
      template: function ReunionFormComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "h1", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](3, "i", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](5, "p", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](7, "form", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("ngSubmit", function ReunionFormComponent_Template_form_ngSubmit_7_listener() {
            return ctx.onSubmit();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](8, ReunionFormComponent_div_8_Template, 2, 1, "div", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](9, ReunionFormComponent_div_9_Template, 2, 1, "div", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](10, "div", 8)(11, "div", 9)(12, "label", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](13, "i", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](14, " Titre * ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](15, "input", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](16, ReunionFormComponent_div_16_Template, 3, 0, "div", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](17, "div", 9)(18, "label", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](19, "i", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](20, " Description ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](21, "textarea", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](22, "div", 17)(23, "h3", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](24, "i", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](25, " Planification ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](26, "div", 20)(27, "div")(28, "label", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](29, "i", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](30, " Date * ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](31, "input", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](32, ReunionFormComponent_div_32_Template, 3, 0, "div", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](33, "div")(34, "label", 24);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](35, "i", 25);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](36, " Heure de d\u00E9but * ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](37, "input", 26);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](38, ReunionFormComponent_div_38_Template, 3, 0, "div", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](39, "div")(40, "label", 27);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](41, "i", 28);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](42, " Heure de fin * ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](43, "input", 29);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](44, ReunionFormComponent_div_44_Template, 3, 0, "div", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](45, "div", 30)(46, "h3", 31);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](47, "i", 32);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](48, " Localisation ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](49, "div", 33)(50, "div")(51, "label", 34);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](52, "i", 35);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](53, " Lieu / Salle ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](54, "input", 36);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](55, "div")(56, "label", 37);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](57, "i", 38);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](58, " Lien Visio ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](59, ReunionFormComponent_span_59_Template, 5, 0, "span", 39);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](60, "input", 40);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](61, ReunionFormComponent_div_61_Template, 3, 1, "div", 41);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](62, ReunionFormComponent_div_62_Template, 3, 0, "div", 42);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](63, ReunionFormComponent_div_63_Template, 12, 2, "div", 43);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](64, ReunionFormComponent_div_64_Template, 14, 10, "div", 44);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](65, "div", 45)(66, "h3", 46);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](67, "i", 47);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](68, " Participants ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](69, "label", 48);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](70, "i", 49);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](71, " S\u00E9lectionnez les participants ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](72, "select", 50);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](73, ReunionFormComponent_ng_container_73_Template, 2, 1, "ng-container", 51);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipe"](74, "async");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](75, "p", 52);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](76, "i", 53);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](77, " Maintenez Ctrl (ou Cmd) pour s\u00E9lectionner plusieurs participants ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](78, "div", 54)(79, "button", 55);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵlistener"]("click", function ReunionFormComponent_Template_button_click_79_listener() {
            return ctx.goReunion();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelement"](80, "i", 56);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](81, " Annuler ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementStart"](82, "button", 57);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](83, ReunionFormComponent_i_83_Template, 1, 0, "i", 58);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](84, ReunionFormComponent_i_84_Template, 1, 0, "i", 59);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtemplate"](85, ReunionFormComponent_i_85_Template, 1, 0, "i", 60);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtext"](86);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵelementEnd"]()()()();
        }
        if (rf & 2) {
          let tmp_5_0;
          let tmp_6_0;
          let tmp_7_0;
          let tmp_8_0;
          let tmp_12_0;
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", ctx.isEditMode ? "Modifier la R\u00E9union" : "Nouvelle R\u00E9union", " ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", ctx.isEditMode ? "Modifiez les d\u00E9tails de votre r\u00E9union" : "Cr\u00E9ez une nouvelle r\u00E9union pour votre \u00E9quipe", " ");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("formGroup", ctx.reunionForm);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.error);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.successMessage);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](7);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ((tmp_5_0 = ctx.reunionForm.get("titre")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.reunionForm.get("titre")) == null ? null : tmp_5_0.touched));
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](16);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ((tmp_6_0 = ctx.reunionForm.get("date")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx.reunionForm.get("date")) == null ? null : tmp_6_0.touched));
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ((tmp_7_0 = ctx.reunionForm.get("heureDebut")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx.reunionForm.get("heureDebut")) == null ? null : tmp_7_0.touched));
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ((tmp_8_0 = ctx.reunionForm.get("heureFin")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx.reunionForm.get("heureFin")) == null ? null : tmp_8_0.touched));
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](15);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.isCheckingLienVisio);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵclassMap"]("mt-1 block w-full rounded-lg shadow-sm focus:ring-cyan-500 focus:ring-2 transition-all duration-200 px-4 py-3 " + (ctx.lienVisioError ? "border-2 border-red-300 focus:border-red-500" : "border-2 border-cyan-200 focus:border-cyan-500"));
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.lienVisioError);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", !ctx.lienVisioError && !ctx.isCheckingLienVisio && ((tmp_12_0 = ctx.reunionForm.get("lienVisio")) == null ? null : tmp_12_0.value) && ((tmp_12_0 = ctx.reunionForm.get("lienVisio")) == null ? null : tmp_12_0.value.trim()) !== "");
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", !ctx.planningIdFromUrl);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.planningIdFromUrl && ctx.selectedPlanning);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](9);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵpipeBind1"](74, 22, ctx.users$));
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](9);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("disabled", !ctx.canSubmit() || ctx.isSubmitting);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", !ctx.isSubmitting && !ctx.isCheckingLienVisio);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.isSubmitting);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵproperty"]("ngIf", ctx.isCheckingLienVisio);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵtextInterpolate1"](" ", ctx.isSubmitting ? "Enregistrement..." : ctx.isCheckingLienVisio ? "V\u00E9rification..." : "Cr\u00E9er la r\u00E9union", " ");
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_10__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_10__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_6__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_6__.NgSelectOption, _angular_forms__WEBPACK_IMPORTED_MODULE_6__["ɵNgSelectMultipleOption"], _angular_forms__WEBPACK_IMPORTED_MODULE_6__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.SelectControlValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.SelectMultipleControlValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_6__.FormControlName, _angular_common__WEBPACK_IMPORTED_MODULE_10__.AsyncPipe, _angular_common__WEBPACK_IMPORTED_MODULE_10__.DatePipe],
      styles: ["/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJyZXVuaW9uLWZvcm0uY29tcG9uZW50LmNzcyJ9 */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdmlld3MvYWRtaW4vcmV1bmlvbnMvcmV1bmlvbi1mb3JtL3JldW5pb24tZm9ybS5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFDQSx3S0FBd0siLCJzb3VyY2VSb290IjoiIn0= */"]
    });
  }
}

/***/ }),

/***/ 855:
/*!*****************************************************************************!*\
  !*** ./src/app/views/admin/reunions/reunion-list/reunion-list.component.ts ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ReunionListComponent: () => (/* binding */ ReunionListComponent)
/* harmony export */ });
/* harmony import */ var _angular_animations__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/animations */ 7172);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var src_app_services_reunion_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/app/services/reunion.service */ 78);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var src_app_services_authuser_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/app/services/authuser.service */ 9271);
/* harmony import */ var _angular_platform_browser__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/platform-browser */ 436);
/* harmony import */ var src_app_services_toast_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! src/app/services/toast.service */ 8397);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _shared_pipes_highlight_presence_pipe__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../shared/pipes/highlight-presence.pipe */ 876);










function ReunionListComponent_div_10_button_9_Template(rf, ctx) {
  if (rf & 1) {
    const _r10 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "button", 30);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function ReunionListComponent_div_10_button_9_Template_button_click_0_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r10);
      const ctx_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r9.clearSearch());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](1, "svg", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](2, "path", 31);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
  }
}
function ReunionListComponent_div_10_option_16_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "option", 32);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const planning_r11 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("value", planning_r11.id);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](planning_r11.titre);
  }
}
const _c0 = function (a0) {
  return {
    "animate__animated animate__fadeInDown": a0
  };
};
function ReunionListComponent_div_10_Template(rf, ctx) {
  if (rf & 1) {
    const _r13 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 15)(1, "div", 16)(2, "div", 17)(3, "div", 4)(4, "div", 18)(5, "div", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](6, "svg", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](7, "path", 7);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](8, "input", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("ngModelChange", function ReunionListComponent_div_10_Template_input_ngModelChange_8_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r13);
      const ctx_r12 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r12.searchTerm = $event);
    })("input", function ReunionListComponent_div_10_Template_input_input_8_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r13);
      const ctx_r14 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r14.searchReunions());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](9, ReunionListComponent_div_10_button_9_Template, 3, 0, "button", 22);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](10, "div", 23)(11, "div", 4)(12, "div", 18)(13, "select", 24);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("ngModelChange", function ReunionListComponent_div_10_Template_select_ngModelChange_13_listener($event) {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r13);
      const ctx_r15 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r15.selectedPlanning = $event);
    })("change", function ReunionListComponent_div_10_Template_select_change_13_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r13);
      const ctx_r16 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r16.searchReunions());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](14, "option", 25);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](15, "Tous les plannings");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](16, ReunionListComponent_div_10_option_16_Template, 2, 2, "option", 26);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](17, "div", 27);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](18, "svg", 28);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](19, "path", 29);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()()()()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngClass", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpureFunction1"](5, _c0, ctx_r0.showSearchBar));
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](8);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngModel", ctx_r0.searchTerm);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx_r0.searchTerm);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](4);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngModel", ctx_r0.selectedPlanning);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngForOf", ctx_r0.uniquePlannings);
  }
}
function ReunionListComponent_div_11_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 33);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](1, "svg", 34);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](2, "path", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](3, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](4, "Les r\u00E9unions \u00E0 ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](5, "span", 36);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](6, "pr\u00E9sence obligatoire");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](7, " sont affich\u00E9es en premier");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
  }
}
function ReunionListComponent_div_12_span_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "span", 40);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1, " Aucune r\u00E9union ne correspond \u00E0 votre recherche. ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
}
function ReunionListComponent_div_12_span_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r18 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", ctx_r18.filteredReunions.length, " r\u00E9union(s) trouv\u00E9e(s) ");
  }
}
function ReunionListComponent_div_12_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 37);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](1, ReunionListComponent_div_12_span_1_Template, 2, 0, "span", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](2, ReunionListComponent_div_12_span_2_Template, 2, 1, "span", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx_r2.filteredReunions.length === 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx_r2.filteredReunions.length > 0);
  }
}
function ReunionListComponent_div_13_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 41);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](1, "div", 42);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](2, "p", 43);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](3, "Chargement de vos r\u00E9unions...");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
  }
}
function ReunionListComponent_div_14_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 44)(1, "div", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](2, "svg", 45);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](3, "path", 46);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](4, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"]("Erreur lors du chargement des r\u00E9unions: ", ctx_r4.error.message, "");
  }
}
function ReunionListComponent_div_15_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 47)(1, "div", 48);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](2, "svg", 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](3, "path", 50);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](4, "h3", 51);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](5, "Aucune r\u00E9union pr\u00E9vue");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](6, "p", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](7, "Vous pouvez cr\u00E9er des r\u00E9unions depuis la page d\u00E9tail d'un planning.");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵclassProp"]("animated", !ctx_r5.loading);
  }
}
function ReunionListComponent_div_16_div_1_span_7_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "span", 82);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](1, " Pr\u00E9sence Obligatoire ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
}
function ReunionListComponent_div_16_div_1_div_30_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 83)(1, "div", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](2, "svg", 69);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](3, "path", 84);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](4, "strong");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](5, "Participants:\u00A0");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const reunion_r20 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"]("", reunion_r20.participants.length, " ");
  }
}
function ReunionListComponent_div_16_div_1_div_38_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 85)(1, "a", 86);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](2, "svg", 75);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](3, "path", 87);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](4, " Rejoindre la visioconf\u00E9rence ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const reunion_r20 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpropertyInterpolate"]("href", reunion_r20.lienVisio, _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵsanitizeUrl"]);
  }
}
const _c1 = function (a1) {
  return ["/reunions/reunionDetails", a1];
};
function ReunionListComponent_div_16_div_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r28 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 55)(1, "div", 56)(2, "div", 17)(3, "div", 18)(4, "h3", 57)(5, "a", 58);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](7, ReunionListComponent_div_16_div_1_span_7_Template, 2, 0, "span", 59);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](8, "p", 60);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipe"](9, "highlightPresence");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](10, "div", 61);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](11, "svg", 62);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](12, "path", 63);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](13, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](14);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipe"](15, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](16, "div", 64)(17, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](18);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipe"](19, "titlecase");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](20, "button", 65);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function ReunionListComponent_div_16_div_1_Template_button_click_20_listener($event) {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r28);
      const reunion_r20 = restoredCtx.$implicit;
      const ctx_r27 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
      ctx_r27.deleteReunion(reunion_r20._id || reunion_r20.id);
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"]($event.stopPropagation());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](21, "svg", 66);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](22, "path", 67);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](23, "div", 68);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](24, "svg", 69);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](25, "path", 70);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](26, "span")(27, "strong");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](28, "Cr\u00E9ateur:");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](29);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](30, ReunionListComponent_div_16_div_1_div_30_Template, 7, 1, "div", 71);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](31, "div", 68);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](32, "svg", 69);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](33, "path", 50);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](34, "span")(35, "strong");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](36, "Planning:");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](37);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](38, ReunionListComponent_div_16_div_1_div_38_Template, 5, 1, "div", 72);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](39, "div", 73)(40, "div", 74);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](41, "svg", 75);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](42, "path", 76)(43, "path", 77);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](44);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](45, "div", 78)(46, "a", 79);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function ReunionListComponent_div_16_div_1_Template_a_click_46_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵrestoreView"](_r28);
      const reunion_r20 = restoredCtx.$implicit;
      const ctx_r29 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵresetView"](ctx_r29.editReunion(reunion_r20._id || reunion_r20.id));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](47, "svg", 80);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](48, "path", 81);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](49, " Modifier ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const reunion_r20 = ctx.$implicit;
    const i_r21 = ctx.index;
    const ctx_r19 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵstyleProp"]("animation-delay", i_r21 * 100 + "ms");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵclassProp"]("animated", ctx_r19.animateItems)("border-l-4", ctx_r19.hasPresenceObligatoire(reunion_r20))("border-red-500", ctx_r19.hasPresenceObligatoire(reunion_r20));
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("routerLink", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpureFunction1"](30, _c1, reunion_r20._id));
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](reunion_r20.titre);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx_r19.hasPresenceObligatoire(reunion_r20));
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("innerHTML", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipeBind1"](9, 23, reunion_r20.description), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵsanitizeHtml"]);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate3"]("", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipeBind2"](15, 25, reunion_r20.date, "mediumDate"), " \u2022 ", reunion_r20.heureDebut, " - ", reunion_r20.heureFin, "");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵclassMap"]("px-3 py-1 text-xs rounded-full font-medium " + ctx_r19.getStatutClass(reunion_r20.statut));
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵpipeBind1"](19, 28, reunion_r20.statut), " ");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](11);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", reunion_r20.createur.username, "");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", reunion_r20.participants.length > 0);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", reunion_r20.planning.titre, "");
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", reunion_r20.lienVisio);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](6);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate1"](" ", reunion_r20.lieu || "Lieu non sp\u00E9cifi\u00E9", " ");
  }
}
function ReunionListComponent_div_16_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 53);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](1, ReunionListComponent_div_16_div_1_Template, 50, 32, "div", 54);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngForOf", ctx_r6.searchTerm || ctx_r6.selectedPlanning ? ctx_r6.filteredReunions : ctx_r6.reunions);
  }
}
class ReunionListComponent {
  // Propriété pour le titre de la page
  get pageTitle() {
    return this.authService.getCurrentUserRole() === 'admin' ? 'Toutes les Réunions' : 'Mes Réunions';
  }
  constructor(reunionService, router, authService, sanitizer, toastService) {
    this.reunionService = reunionService;
    this.router = router;
    this.authService = authService;
    this.sanitizer = sanitizer;
    this.toastService = toastService;
    this.reunions = [];
    this.filteredReunions = [];
    this.loading = true;
    this.animateItems = false; // Contrôle l'animation des éléments de la liste
    // Propriétés pour la recherche
    this.showSearchBar = false;
    this.searchTerm = '';
    this.selectedPlanning = '';
    this.uniquePlannings = [];
  }
  ngOnInit() {
    this.loadReunions();
    // Test du service de toast
    console.log('🧪 Test du service de toast...');
    // this.toastService.success('Test', 'Le service de toast fonctionne !');
  }

  ngAfterViewInit() {
    // Activer les animations après un court délai pour permettre le rendu initial
    setTimeout(() => {
      this.animateItems = true;
    }, 100);
  }
  /**
   * Affiche ou masque la barre de recherche
   */
  toggleSearchBar() {
    this.showSearchBar = !this.showSearchBar;
    // Si on ferme la barre de recherche, réinitialiser les filtres
    if (!this.showSearchBar) {
      this.clearSearch();
    }
  }
  /**
   * Réinitialise les critères de recherche
   */
  clearSearch() {
    this.searchTerm = '';
    this.selectedPlanning = '';
    this.searchReunions();
  }
  /**
   * Filtre les réunions selon les critères de recherche
   */
  searchReunions() {
    if (!this.searchTerm && !this.selectedPlanning) {
      // Si aucun critère de recherche, afficher toutes les réunions
      this.filteredReunions = [...this.reunions];
      return;
    }
    // Filtrer les réunions selon les critères
    this.filteredReunions = this.reunions.filter(reunion => {
      // Vérifier le titre et la description si searchTerm est défini
      const matchesSearchTerm = !this.searchTerm || reunion.titre && reunion.titre.toLowerCase().includes(this.searchTerm.toLowerCase()) || reunion.description && reunion.description.toLowerCase().includes(this.searchTerm.toLowerCase());
      // Vérifier le planning si selectedPlanning est défini
      const matchesPlanning = !this.selectedPlanning || reunion.planning && reunion.planning._id === this.selectedPlanning;
      // La réunion doit correspondre aux deux critères (si définis)
      return matchesSearchTerm && matchesPlanning;
    });
  }
  loadReunions() {
    this.loading = true;
    this.animateItems = false; // Réinitialiser l'animation
    const userId = this.authService.getCurrentUserId();
    const userRole = this.authService.getCurrentUserRole();
    if (!userId) {
      this.error = "Utilisateur non connecté";
      this.loading = false;
      return;
    }
    // Si l'utilisateur est admin, récupérer toutes les réunions
    // Sinon, récupérer seulement ses réunions
    const reunionObservable = userRole === 'admin' ? this.reunionService.getAllReunionsAdmin() : this.reunionService.getProchainesReunions(userId);
    reunionObservable.subscribe({
      next: response => {
        console.log('Réunions chargées:', response);
        // Réinitialiser les erreurs
        this.error = null;
        // Attribuer les données après un court délai pour l'animation
        setTimeout(() => {
          // Récupérer les réunions selon la structure de réponse
          let reunions = userRole === 'admin' ? response.data || response.reunions || [] : response.reunions || [];
          console.log('Réunions récupérées pour admin:', reunions);
          console.log('Structure de la première réunion:', reunions[0]);
          // Pour le test : ajouter "présence obligatoire" à certaines réunions si aucune n'en a
          reunions = this.ajouterPresenceObligatoirePourTest(reunions);
          // Trier les réunions : celles avec "Présence Obligatoire" en premier
          this.reunions = this.trierReunionsParPresenceObligatoire(reunions);
          // Initialiser les réunions filtrées avec toutes les réunions
          this.filteredReunions = [...this.reunions];
          // Extraire les plannings uniques pour le filtre
          this.extractUniquePlannings();
          this.loading = false;
          // Activer les animations après un court délai
          setTimeout(() => {
            this.animateItems = true;
          }, 100);
        }, 300); // Délai pour une meilleure expérience visuelle
      },

      error: error => {
        console.error('Erreur détaillée:', JSON.stringify(error));
        this.error = `Erreur lors du chargement des réunions: ${error.message || error.statusText || 'Erreur inconnue'}`;
        this.loading = false;
      }
    });
  }
  getStatutClass(statut) {
    switch (statut) {
      case 'planifiee':
        return 'bg-blue-100 text-blue-800';
      case 'en_cours':
        return 'bg-yellow-100 text-yellow-800';
      case 'terminee':
        return 'bg-green-100 text-green-800';
      case 'annulee':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }
  editReunion(id) {
    console.log(id);
    if (this.reunions) {
      this.router.navigate(['/reunions/modifier', id]);
    }
  }
  /**
   * Supprime une réunion après confirmation
   * @param id ID de la réunion à supprimer
   */
  deleteReunion(id) {
    console.log('🗑️ Tentative de suppression de la réunion avec ID:', id);
    if (confirm('Êtes-vous sûr de vouloir supprimer cette réunion ?')) {
      const userRole = this.authService.getCurrentUserRole();
      console.log('👤 Rôle utilisateur:', userRole);
      // Utiliser la méthode appropriée selon le rôle
      const deleteObservable = userRole === 'admin' ? this.reunionService.forceDeleteReunion(id) : this.reunionService.deleteReunion(id);
      console.log('🚀 Envoi de la requête de suppression...');
      deleteObservable.subscribe({
        next: response => {
          console.log('✅ Réunion supprimée avec succès:', response);
          this.handleSuccessfulDeletion(id);
        },
        error: error => {
          console.error('❌ Erreur lors de la suppression:', error);
          console.error('📋 Détails de l\'erreur:', {
            status: error.status,
            statusText: error.statusText,
            message: error.error?.message,
            fullError: error
          });
          // Si c'est une erreur 200 mal interprétée ou une erreur de CORS,
          // on considère que la suppression a réussi
          if (error.status === 0 || error.status === 200) {
            console.log('🔄 Erreur probablement liée à CORS ou réponse mal formatée, on considère la suppression comme réussie');
            this.handleSuccessfulDeletion(id);
            return;
          }
          // Pour les autres erreurs, on vérifie quand même si la suppression a eu lieu
          // en rechargeant la liste après un délai
          if (error.status >= 500) {
            console.log('🔄 Erreur serveur, vérification de la suppression dans 2 secondes...');
            setTimeout(() => {
              this.loadReunions();
            }, 2000);
          }
          // Gestion spécifique des erreurs d'autorisation
          if (error.status === 403) {
            this.toastService.accessDenied('supprimer cette réunion', error.status);
          } else if (error.status === 401) {
            this.toastService.error('Non autorisé', 'Vous devez être connecté pour supprimer une réunion');
          } else {
            const errorMessage = error.error?.message || 'Erreur lors de la suppression de la réunion';
            this.toastService.error('Erreur de suppression', errorMessage, 8000);
          }
        }
      });
    } else {
      console.log('❌ Suppression annulée par l\'utilisateur');
    }
  }
  handleSuccessfulDeletion(id) {
    console.log('🎯 Traitement de la suppression réussie pour l\'ID:', id);
    // Retirer la réunion de la liste locale (gérer _id et id)
    const initialCount = this.reunions.length;
    this.reunions = this.reunions.filter(reunion => reunion._id !== id && reunion.id !== id);
    this.filteredReunions = this.filteredReunions.filter(reunion => reunion._id !== id && reunion.id !== id);
    const finalCount = this.reunions.length;
    console.log(`📊 Réunions avant suppression: ${initialCount}, après: ${finalCount}`);
    // Mettre à jour la liste des plannings uniques
    this.extractUniquePlannings();
    // Afficher le toast de succès
    this.toastService.success('Réunion supprimée', 'La réunion a été supprimée avec succès');
    console.log('🎉 Toast de succès affiché et liste mise à jour');
    // Recharger complètement la liste pour s'assurer de la mise à jour
    this.loadReunions();
  }
  formatDescription(description) {
    if (!description) return this.sanitizer.bypassSecurityTrustHtml('');
    // Recherche la chaîne "(presence obligatoire)" (insensible à la casse) et la remplace par une version en rouge
    const formattedText = description.replace(/\(presence obligatoire\)/gi, '<span class="text-red-600 font-semibold">(presence obligatoire)</span>');
    // Sanitize le HTML pour éviter les problèmes de sécurité
    return this.sanitizer.bypassSecurityTrustHtml(formattedText);
  }
  /**
   * Vérifie si une réunion contient "Présence Obligatoire" dans sa description
   * @param reunion La réunion à vérifier
   * @returns true si la réunion a une présence obligatoire, false sinon
   */
  hasPresenceObligatoire(reunion) {
    if (!reunion.description) return false;
    // Recherche différentes variations de "présence obligatoire" (insensible à la casse)
    const patterns = [/presence obligatoire/i, /présence obligatoire/i, /obligatoire/i, /\(obligatoire\)/i, /\(presence obligatoire\)/i, /\(présence obligatoire\)/i];
    // Retourne true si l'une des expressions est trouvée
    return patterns.some(pattern => pattern.test(reunion.description));
  }
  /**
   * Trie les réunions en mettant celles avec "Présence Obligatoire" en premier
   * @param reunions Liste des réunions à trier
   * @returns Liste triée des réunions
   */
  trierReunionsParPresenceObligatoire(reunions) {
    if (!reunions || !reunions.length) return [];
    console.log('Avant tri - Nombre de réunions:', reunions.length);
    // Vérifier chaque réunion pour la présence obligatoire
    reunions.forEach((reunion, index) => {
      const hasPresence = this.hasPresenceObligatoire(reunion);
      console.log(`Réunion ${index + 1} - Titre: ${reunion.titre}, Description: ${reunion.description}, Présence Obligatoire: ${hasPresence}`);
    });
    // Trier les réunions : celles avec "Présence Obligatoire" en premier
    const reunionsTriees = [...reunions].sort((a, b) => {
      const aHasPresenceObligatoire = this.hasPresenceObligatoire(a);
      const bHasPresenceObligatoire = this.hasPresenceObligatoire(b);
      if (aHasPresenceObligatoire && !bHasPresenceObligatoire) {
        return -1; // a vient avant b
      }

      if (!aHasPresenceObligatoire && bHasPresenceObligatoire) {
        return 1; // b vient avant a
      }
      // Si les deux ont ou n'ont pas "Présence Obligatoire", trier par date
      return new Date(b.date).getTime() - new Date(a.date).getTime();
    });
    console.log('Après tri - Ordre des réunions:');
    reunionsTriees.forEach((reunion, index) => {
      const hasPresence = this.hasPresenceObligatoire(reunion);
      console.log(`Position ${index + 1} - Titre: ${reunion.titre}, Présence Obligatoire: ${hasPresence}`);
    });
    return reunionsTriees;
  }
  /**
   * Méthode temporaire pour ajouter "présence obligatoire" à certaines réunions pour tester le tri
   * @param reunions Liste des réunions
   * @returns Liste des réunions avec certaines marquées comme "présence obligatoire"
   */
  /**
   * Extrait les plannings uniques à partir des réunions pour le filtre
   */
  extractUniquePlannings() {
    // Map pour stocker les plannings uniques par ID
    const planningsMap = new Map();
    // Parcourir toutes les réunions
    this.reunions.forEach(reunion => {
      if (reunion.planning && reunion.planning._id) {
        // Ajouter le planning au Map s'il n'existe pas déjà
        if (!planningsMap.has(reunion.planning._id)) {
          planningsMap.set(reunion.planning._id, {
            id: reunion.planning._id,
            titre: reunion.planning.titre
          });
        }
      }
    });
    // Convertir le Map en tableau
    this.uniquePlannings = Array.from(planningsMap.values());
    // Trier les plannings par titre
    this.uniquePlannings.sort((a, b) => a.titre.localeCompare(b.titre));
  }
  /**
   * Méthode temporaire pour ajouter "présence obligatoire" à certaines réunions pour tester le tri
   */
  ajouterPresenceObligatoirePourTest(reunions) {
    if (!reunions || reunions.length === 0) return reunions;
    // Vérifier si au moins une réunion a déjà "présence obligatoire"
    const hasAnyPresenceObligatoire = reunions.some(reunion => this.hasPresenceObligatoire(reunion));
    // Si aucune réunion n'a "présence obligatoire", en ajouter à certaines pour le test
    if (!hasAnyPresenceObligatoire) {
      console.log('Aucune réunion avec présence obligatoire trouvée, ajout pour le test...');
      // Ajouter "présence obligatoire" à la première réunion si elle existe
      if (reunions.length > 0) {
        const reunion = reunions[0];
        reunion.description = reunion.description ? reunion.description + ' (présence obligatoire)' : '(présence obligatoire)';
        console.log(`Ajout de "présence obligatoire" à la réunion: ${reunion.titre}`);
      }
      // Si au moins 3 réunions, ajouter aussi à la troisième
      if (reunions.length >= 3) {
        const reunion = reunions[2];
        reunion.description = reunion.description ? reunion.description + ' (présence obligatoire)' : '(présence obligatoire)';
        console.log(`Ajout de "présence obligatoire" à la réunion: ${reunion.titre}`);
      }
    }
    return reunions;
  }
  static {
    this.ɵfac = function ReunionListComponent_Factory(t) {
      return new (t || ReunionListComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](src_app_services_reunion_service__WEBPACK_IMPORTED_MODULE_0__.ReunionService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_5__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](src_app_services_authuser_service__WEBPACK_IMPORTED_MODULE_1__.AuthuserService), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](_angular_platform_browser__WEBPACK_IMPORTED_MODULE_6__.DomSanitizer), _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdirectiveInject"](src_app_services_toast_service__WEBPACK_IMPORTED_MODULE_2__.ToastService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineComponent"]({
      type: ReunionListComponent,
      selectors: [["app-reunion-list"]],
      decls: 17,
      vars: 8,
      consts: [[1, "container", "mx-auto", "px-4", "py-6", "page-container", "page-enter"], [1, "flex", "flex-col", "mb-8"], [1, "flex", "justify-between", "items-center"], [1, "text-2xl", "font-bold", "text-gray-800", "page-title"], [1, "relative"], [1, "search-button", "px-4", "py-2", "bg-purple-200", "text-purple-800", "rounded-md", "hover:bg-purple-300", "transition-colors", "transform", "hover:scale-105", "duration-200", "flex", "items-center", "shadow-sm", "border", "border-purple-300", 3, "click"], ["fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "h-5", "w-5", "mr-2", "text-purple-600"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"], ["class", "mt-4 bg-white p-4 rounded-lg shadow-md transition-all duration-300 animate-fadeIn", 3, "ngClass", 4, "ngIf"], ["class", "mt-2 text-sm text-gray-600 flex items-center", 4, "ngIf"], ["class", "mt-2 text-sm text-gray-600", 4, "ngIf"], ["class", "text-center py-12", 4, "ngIf"], ["class", "bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md mb-6 shadow-md transform transition-all duration-500 hover:shadow-lg", 4, "ngIf"], ["class", "text-center py-12 empty-container", 3, "animated", 4, "ngIf"], ["class", "grid grid-cols-1 md:grid-cols-2 gap-6", 4, "ngIf"], [1, "mt-4", "bg-white", "p-4", "rounded-lg", "shadow-md", "transition-all", "duration-300", "animate-fadeIn", 3, "ngClass"], [1, "flex", "flex-col", "md:flex-row", "gap-4"], [1, "flex-1"], [1, "flex", "items-center"], [1, "absolute", "left-3", "top-1/2", "transform", "-translate-y-1/2", "text-purple-400"], ["fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "h-5", "w-5"], ["type", "text", "id", "searchTerm", "placeholder", "Rechercher par titre ou description", 1, "w-full", "pl-10", "pr-10", "py-3", "border", "border-gray-300", "rounded-md", "focus:ring-2", "focus:ring-purple-300", "focus:border-purple-400", "transition-all", "duration-300", 3, "ngModel", "ngModelChange", "input"], ["class", "absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-purple-600 transition-colors", 3, "click", 4, "ngIf"], [1, "md:w-1/3"], ["id", "planningFilter", 1, "w-full", "px-4", "py-3", "border", "border-gray-300", "rounded-md", "focus:ring-2", "focus:ring-purple-300", "focus:border-purple-400", "transition-all", "duration-300", "appearance-none", 3, "ngModel", "ngModelChange", "change"], ["value", ""], [3, "value", 4, "ngFor", "ngForOf"], [1, "absolute", "inset-y-0", "right-0", "flex", "items-center", "pr-3", "pointer-events-none"], ["fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "h-5", "w-5", "text-purple-500"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M19 9l-7 7-7-7"], [1, "absolute", "right-3", "top-1/2", "transform", "-translate-y-1/2", "text-gray-400", "hover:text-purple-600", "transition-colors", 3, "click"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M6 18L18 6M6 6l12 12"], [3, "value"], [1, "mt-2", "text-sm", "text-gray-600", "flex", "items-center"], ["fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "h-4", "w-4", "mr-2", "text-red-500"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M13 10V3L4 14h7v7l9-11h-7z"], [1, "font-semibold", "text-red-600"], [1, "mt-2", "text-sm", "text-gray-600"], ["class", "text-red-500", 4, "ngIf"], [4, "ngIf"], [1, "text-red-500"], [1, "text-center", "py-12"], [1, "loading-spinner", "rounded-full", "h-16", "w-16", "border-4", "border-purple-200", "border-t-purple-600", "mx-auto"], [1, "mt-4", "text-gray-600", "animate-pulse"], [1, "bg-red-100", "border-l-4", "border-red-500", "text-red-700", "p-4", "rounded-md", "mb-6", "shadow-md", "transform", "transition-all", "duration-500", "hover:shadow-lg"], ["fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "h-6", "w-6", "mr-3", "text-red-500"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"], [1, "text-center", "py-12", "empty-container"], [1, "bg-white", "rounded-lg", "shadow-md", "p-8", "max-w-md", "mx-auto"], ["fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "mx-auto", "h-16", "w-16", "text-purple-400"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"], [1, "mt-4", "text-xl", "font-medium", "text-gray-900"], [1, "mt-2", "text-gray-500"], [1, "grid", "grid-cols-1", "md:grid-cols-2", "gap-6"], ["class", "bg-white rounded-lg shadow-md p-5 hover:shadow-xl transition-all duration-300 reunion-card", 3, "animated", "border-l-4", "border-red-500", "animation-delay", 4, "ngFor", "ngForOf"], [1, "bg-white", "rounded-lg", "shadow-md", "p-5", "hover:shadow-xl", "transition-all", "duration-300", "reunion-card"], [1, "flex", "justify-between", "items-start"], [1, "text-lg", "font-semibold", "text-gray-800", "hover:text-purple-600", "transition-colors"], [1, "hover:text-purple-600", 3, "routerLink"], ["class", "ml-2 px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full font-bold animate-pulse", 4, "ngIf"], [1, "text-sm", "mt-1", 3, "innerHTML"], [1, "mt-3", "flex", "items-center", "text-sm", "text-gray-500"], ["fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "h-4", "w-4", "mr-2", "text-purple-500"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"], [1, "flex", "items-start", "space-x-2"], ["title", "Supprimer la r\u00E9union", 1, "text-red-500", "hover:text-red-700", "transition-colors", "duration-300", "p-1", "rounded-full", "hover:bg-red-50", 3, "click"], ["xmlns", "http://www.w3.org/2000/svg", "fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "h-5", "w-5"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"], [1, "mt-3", "text-sm", "text-gray-600", "flex", "items-center"], ["fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "h-4", "w-4", "mr-2", "text-gray-500"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"], ["class", "mt-3 text-sm text-gray-600", 4, "ngIf"], ["class", "mt-3 text-sm", 4, "ngIf"], [1, "mt-4", "pt-3", "border-t", "border-gray-100", "flex", "justify-between", "items-center"], [1, "flex", "items-center", "text-sm", "text-gray-500"], ["fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "h-4", "w-4", "mr-2"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M15 11a3 3 0 11-6 0 3 3 0 016 0z"], [1, "flex", "items-center", "space-x-2"], [1, "px-4", "py-2", "bg-purple-600", "text-white", "rounded-md", "hover:bg-purple-700", "transition-all", "duration-300", "transform", "hover:scale-105", "flex", "items-center", 3, "click"], ["fill", "none", "viewBox", "0 0 24 24", "stroke", "currentColor", 1, "h-4", "w-4", "mr-1"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"], [1, "ml-2", "px-2", "py-1", "text-xs", "bg-red-100", "text-red-800", "rounded-full", "font-bold", "animate-pulse"], [1, "mt-3", "text-sm", "text-gray-600"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"], [1, "mt-3", "text-sm"], ["target", "_blank", 1, "text-purple-600", "hover:text-purple-800", "flex", "items-center", "transition-colors", 3, "href"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"]],
      template: function ReunionListComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "h1", 3);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](5, "div", 4)(6, "button", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵlistener"]("click", function ReunionListComponent_Template_button_click_6_listener() {
            return ctx.toggleSearchBar();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵnamespaceSVG"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementStart"](7, "svg", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelement"](8, "path", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtext"](9, " Rechercher ");
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](10, ReunionListComponent_div_10_Template, 20, 7, "div", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](11, ReunionListComponent_div_11_Template, 8, 0, "div", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](12, ReunionListComponent_div_12_Template, 3, 2, "div", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](13, ReunionListComponent_div_13_Template, 4, 0, "div", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](14, ReunionListComponent_div_14_Template, 6, 1, "div", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](15, ReunionListComponent_div_15_Template, 8, 2, "div", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtemplate"](16, ReunionListComponent_div_16_Template, 2, 1, "div", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵtextInterpolate"](ctx.pageTitle);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](6);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.showSearchBar);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", !ctx.loading && ctx.reunions.length > 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.searchTerm || ctx.selectedPlanning);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.loading);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", ctx.error);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", !ctx.loading && ctx.reunions.length === 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵproperty"]("ngIf", !ctx.loading && ctx.reunions.length > 0);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_7__.NgClass, _angular_common__WEBPACK_IMPORTED_MODULE_7__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_7__.NgIf, _angular_router__WEBPACK_IMPORTED_MODULE_5__.RouterLink, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.NgSelectOption, _angular_forms__WEBPACK_IMPORTED_MODULE_8__["ɵNgSelectMultipleOption"], _angular_forms__WEBPACK_IMPORTED_MODULE_8__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.SelectControlValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_8__.NgModel, _angular_common__WEBPACK_IMPORTED_MODULE_7__.TitleCasePipe, _angular_common__WEBPACK_IMPORTED_MODULE_7__.DatePipe, _shared_pipes_highlight_presence_pipe__WEBPACK_IMPORTED_MODULE_3__.HighlightPresencePipe],
      styles: ["\n\n.page-container[_ngcontent-%COMP%] {\n  overflow: hidden;\n}\n\n.page-title[_ngcontent-%COMP%] {\n  position: relative;\n  display: inline-block;\n}\n\n.page-title[_ngcontent-%COMP%]::after {\n  content: '';\n  position: absolute;\n  width: 0;\n  height: 3px;\n  bottom: -5px;\n  left: 0;\n  background-color: #8b5cf6; \n\n  transition: width 0.6s ease;\n}\n\n.page-title[_ngcontent-%COMP%]:hover::after {\n  width: 100%;\n}\n\n\n\n.reunion-card[_ngcontent-%COMP%] {\n  transform: translateY(30px);\n  opacity: 0;\n  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.reunion-card.animated[_ngcontent-%COMP%] {\n  transform: translateY(0);\n  opacity: 1;\n}\n\n\n\n.empty-container[_ngcontent-%COMP%] {\n  transform: scale(0.8);\n  opacity: 0;\n  transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);\n}\n\n.empty-container.animated[_ngcontent-%COMP%] {\n  transform: scale(1);\n  opacity: 1;\n}\n\n\n\n.loading-spinner[_ngcontent-%COMP%] {\n  animation: _ngcontent-%COMP%_pulse 1.5s infinite ease-in-out;\n}\n\n@keyframes _ngcontent-%COMP%_pulse {\n  0% {\n    transform: scale(0.95);\n    box-shadow: 0 0 0 0 rgba(139, 92, 246, 0.7);\n  }\n\n  70% {\n    transform: scale(1);\n    box-shadow: 0 0 0 10px rgba(139, 92, 246, 0);\n  }\n\n  100% {\n    transform: scale(0.95);\n    box-shadow: 0 0 0 0 rgba(139, 92, 246, 0);\n  }\n}\n\n\n\n.page-enter[_ngcontent-%COMP%] {\n  animation: _ngcontent-%COMP%_fadeInUp 0.8s forwards;\n}\n\n@keyframes _ngcontent-%COMP%_fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(40px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n\n\n.animate-fadeIn[_ngcontent-%COMP%] {\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-in-out;\n}\n\n\n\n@keyframes _ngcontent-%COMP%_slideInFromRight {\n  0% {\n    transform: translateX(30px);\n    opacity: 0;\n  }\n  100% {\n    transform: translateX(0);\n    opacity: 1;\n  }\n}\n\n\n\n.flex-col[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%]:nth-child(1) {\n  animation: _ngcontent-%COMP%_slideInFromRight 0.4s ease-out forwards;\n}\n\n.flex-col[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%]:nth-child(2) {\n  animation: _ngcontent-%COMP%_slideInFromRight 0.4s ease-out 0.1s forwards;\n  opacity: 0;\n}\n\n\n\ninput[_ngcontent-%COMP%], select[_ngcontent-%COMP%] {\n  transition: all 0.3s ease;\n}\n\ninput[_ngcontent-%COMP%]:focus, select[_ngcontent-%COMP%]:focus {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 6px -1px rgba(139, 92, 246, 0.1), 0 2px 4px -1px rgba(139, 92, 246, 0.06);\n}\n\n\n\n@keyframes _ngcontent-%COMP%_gentle-pulse {\n  0% {\n    box-shadow: 0 0 0 0 rgba(167, 139, 250, 0.4);\n  }\n  70% {\n    box-shadow: 0 0 0 6px rgba(167, 139, 250, 0);\n  }\n  100% {\n    box-shadow: 0 0 0 0 rgba(167, 139, 250, 0);\n  }\n}\n\n.search-button[_ngcontent-%COMP%] {\n  animation: _ngcontent-%COMP%_gentle-pulse 2s infinite;\n}\n\n@keyframes _ngcontent-%COMP%_fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n\n\n.search-input-container[_ngcontent-%COMP%] {\n  position: relative;\n  overflow: hidden;\n  transition: all 0.3s ease;\n}\n\n.search-input-container[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n}\n\n.search-input[_ngcontent-%COMP%] {\n  position: relative;\n  z-index: 1;\n  background: transparent;\n  transition: all 0.3s ease;\n}\n\n.search-input[_ngcontent-%COMP%]:focus {\n  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.3);\n}\n\n\n\n.search-input-container[_ngcontent-%COMP%]::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    90deg,\n    transparent,\n    rgba(139, 92, 246, 0.1),\n    transparent\n  );\n  transition: all 0.6s ease;\n  z-index: 0;\n}\n\n.search-input-container[_ngcontent-%COMP%]:hover::before {\n  left: 100%;\n  transition: all 0.6s ease;\n}\n\n\n\n@keyframes _ngcontent-%COMP%_subtlePulse {\n  0% {\n    box-shadow: 0 0 0 0 rgba(139, 92, 246, 0.2);\n  }\n  50% {\n    box-shadow: 0 0 0 5px rgba(139, 92, 246, 0);\n  }\n  100% {\n    box-shadow: 0 0 0 0 rgba(139, 92, 246, 0);\n  }\n}\n\n.search-input-container[_ngcontent-%COMP%]:focus-within {\n  animation: _ngcontent-%COMP%_subtlePulse 2s infinite;\n  border-color: #8b5cf6;\n}\n\n\n\n@keyframes _ngcontent-%COMP%_rotateIcon {\n  0% {\n    transform: rotate(0deg);\n  }\n  25% {\n    transform: rotate(-10deg);\n  }\n  75% {\n    transform: rotate(10deg);\n  }\n  100% {\n    transform: rotate(0deg);\n  }\n}\n\n.search-icon[_ngcontent-%COMP%] {\n  transition: all 0.3s ease;\n}\n\n.search-input-container[_ngcontent-%COMP%]:focus-within   .search-icon[_ngcontent-%COMP%] {\n  animation: _ngcontent-%COMP%_rotateIcon 1s ease;\n  color: #8b5cf6;\n}\n\n\n\n.search-input[_ngcontent-%COMP%]::placeholder {\n  transition: all 0.3s ease;\n}\n\n.search-input[_ngcontent-%COMP%]:focus::placeholder {\n  opacity: 0.5;\n  transform: translateX(10px);\n}\n\n\n\n.floating-label[_ngcontent-%COMP%] {\n  position: absolute;\n  left: 12px;\n  top: 50%;\n  transform: translateY(-50%);\n  font-size: 14px;\n  color: #9ca3af;\n  pointer-events: none;\n  transition: all 0.3s ease;\n  z-index: 2;\n}\n\n.search-input[_ngcontent-%COMP%]:focus    ~ .floating-label[_ngcontent-%COMP%], .search-input[_ngcontent-%COMP%]:not(:placeholder-shown)    ~ .floating-label[_ngcontent-%COMP%] {\n  top: 0;\n  left: 8px;\n  font-size: 12px;\n  padding: 0 4px;\n  background-color: white;\n  color: #8b5cf6;\n  transform: translateY(-50%);\n}\n\n\n\n.search-select[_ngcontent-%COMP%] {\n  position: relative;\n  transition: all 0.3s ease;\n  background-image: linear-gradient(to right, #f9fafb 0%, white 100%);\n}\n\n.search-select[_ngcontent-%COMP%]:hover {\n  background-image: linear-gradient(to right, #f3f4f6 0%, white 100%);\n}\n\n.search-select[_ngcontent-%COMP%]:focus {\n  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.3);\n  background-image: linear-gradient(to right, #f3f4f6 0%, white 100%);\n}\n\n\n\n.staggered-item[_ngcontent-%COMP%] {\n  opacity: 0;\n  transform: translateY(20px);\n}\n\n.staggered-item.animated[_ngcontent-%COMP%] {\n  animation: _ngcontent-%COMP%_fadeInStaggered 0.5s forwards;\n}\n\n@keyframes _ngcontent-%COMP%_fadeInStaggered {\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n/*# sourceMappingURL=data:application/json;base64,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 */\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"],
      data: {
        animation: [(0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.trigger)('fadeIn', [(0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.transition)(':enter', [(0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.style)({
          opacity: 0,
          transform: 'translateY(20px)'
        }), (0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.animate)('0.4s ease-out', (0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.style)({
          opacity: 1,
          transform: 'translateY(0)'
        }))])]), (0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.trigger)('staggerList', [(0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.transition)('* => *', [(0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.query)(':enter', [(0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.style)({
          opacity: 0,
          transform: 'translateY(30px)'
        }), (0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.stagger)('100ms', [(0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.animate)('0.5s ease-out', (0,_angular_animations__WEBPACK_IMPORTED_MODULE_9__.style)({
          opacity: 1,
          transform: 'translateY(0)'
        }))])], {
          optional: true
        })])])]
      }
    });
  }
}

/***/ }),

/***/ 333:
/*!*****************************************************************!*\
  !*** ./src/app/views/admin/reunions/reunions-routing.module.ts ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ReunionsRoutingModule: () => (/* binding */ ReunionsRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _reunion_list_reunion_list_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./reunion-list/reunion-list.component */ 855);
/* harmony import */ var _reunion_form_reunion_form_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./reunion-form/reunion-form.component */ 1135);
/* harmony import */ var _reunion_detail_reunion_detail_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./reunion-detail/reunion-detail.component */ 3493);
/* harmony import */ var _reunion_edit_reunion_edit_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./reunion-edit/reunion-edit.component */ 6023);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/core */ 7580);







const routes = [{
  path: '',
  component: _reunion_list_reunion_list_component__WEBPACK_IMPORTED_MODULE_0__.ReunionListComponent
}, {
  path: 'nouvelleReunion',
  component: _reunion_form_reunion_form_component__WEBPACK_IMPORTED_MODULE_1__.ReunionFormComponent
}, {
  path: 'reunionDetails/:id',
  component: _reunion_detail_reunion_detail_component__WEBPACK_IMPORTED_MODULE_2__.ReunionDetailComponent
}, {
  path: 'modifier/:id',
  component: _reunion_edit_reunion_edit_component__WEBPACK_IMPORTED_MODULE_3__.ReunionEditComponent
}];
class ReunionsRoutingModule {
  static {
    this.ɵfac = function ReunionsRoutingModule_Factory(t) {
      return new (t || ReunionsRoutingModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineNgModule"]({
      type: ReunionsRoutingModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵdefineInjector"]({
      imports: [_angular_router__WEBPACK_IMPORTED_MODULE_5__.RouterModule.forChild(routes), _angular_router__WEBPACK_IMPORTED_MODULE_5__.RouterModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_4__["ɵɵsetNgModuleScope"](ReunionsRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_5__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_5__.RouterModule]
  });
})();

/***/ }),

/***/ 996:
/*!*********************************************************!*\
  !*** ./src/app/views/admin/reunions/reunions.module.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ReunionsModule: () => (/* binding */ ReunionsModule)
/* harmony export */ });
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _reunions_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./reunions-routing.module */ 333);
/* harmony import */ var _reunion_list_reunion_list_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./reunion-list/reunion-list.component */ 855);
/* harmony import */ var _reunion_detail_reunion_detail_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./reunion-detail/reunion-detail.component */ 3493);
/* harmony import */ var _reunion_form_reunion_form_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./reunion-form/reunion-form.component */ 1135);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _pipes_pipes_module__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../pipes/pipes.module */ 1683);
/* harmony import */ var _reunion_edit_reunion_edit_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./reunion-edit/reunion-edit.component */ 6023);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/core */ 7580);










class ReunionsModule {
  static {
    this.ɵfac = function ReunionsModule_Factory(t) {
      return new (t || ReunionsModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineNgModule"]({
      type: ReunionsModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineInjector"]({
      providers: [_angular_common__WEBPACK_IMPORTED_MODULE_7__.DatePipe],
      imports: [_angular_common__WEBPACK_IMPORTED_MODULE_7__.CommonModule, _reunions_routing_module__WEBPACK_IMPORTED_MODULE_0__.ReunionsRoutingModule, _angular_router__WEBPACK_IMPORTED_MODULE_8__.RouterModule, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.FormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.ReactiveFormsModule, _pipes_pipes_module__WEBPACK_IMPORTED_MODULE_4__.PipesModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵsetNgModuleScope"](ReunionsModule, {
    declarations: [_reunion_list_reunion_list_component__WEBPACK_IMPORTED_MODULE_1__.ReunionListComponent, _reunion_detail_reunion_detail_component__WEBPACK_IMPORTED_MODULE_2__.ReunionDetailComponent, _reunion_form_reunion_form_component__WEBPACK_IMPORTED_MODULE_3__.ReunionFormComponent, _reunion_edit_reunion_edit_component__WEBPACK_IMPORTED_MODULE_5__.ReunionEditComponent],
    imports: [_angular_common__WEBPACK_IMPORTED_MODULE_7__.CommonModule, _reunions_routing_module__WEBPACK_IMPORTED_MODULE_0__.ReunionsRoutingModule, _angular_router__WEBPACK_IMPORTED_MODULE_8__.RouterModule, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.FormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.ReactiveFormsModule, _pipes_pipes_module__WEBPACK_IMPORTED_MODULE_4__.PipesModule]
  });
})();

/***/ })

}]);
//# sourceMappingURL=src_app_views_admin_reunions_reunions_module_ts.js.map